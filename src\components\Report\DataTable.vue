<template>
  <div class="mt-4 mb-4">
    <div class="ml-3 mb-6">
      <span style="font-size: 16px; color: #333333; font-weight: 600;">ตารางแสดงข้อมูลรายจ่ายทั้งหมด</span>
    </div>
    <v-row>
      <v-col cols="12">
        <table class="display nowrap" id="example"  cellpadding="0" cellspacing="0" style="width:100%">
          <thead>
            <tr>
              <th class="fontData">
                วันที่สั่งซื้อ
              </th>
              <th class="fontData">
                เลขสั่งซื้อ
              </th>
              <th class="fontData">
                เลขใบเสนอราคา
              </th>
              <th class="fontData">
                ชื่อร้านค้า
              </th>
              <th class="fontData">
                ยอดเงิน
              </th>
              <th class="fontData">
                วันที่ชำระเงิน
              </th>
              <th class="fontData">
                สถานะ
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, index) in dataRes" :key="index">
              <td >{{item.created_at}}</td>
              <td >{{item.payment_transaction_number}}</td>
              <td >{{item.quatation_id }}</td>
              <td >{{item.name_th}}</td>
              <td >{{item.total_amount }}</td>
              <td >{{ item.paid_datetime}}</td>
              <td >{{ item.transaction_status}}</td>
            </tr>
          </tbody>
        </table>
      </v-col>
    </v-row>
  </div>
</template>
<script>
// import 'jquery/dist/jquery.min.js'
// import { jquery, jqDatatable, dataTableBTN, jszip, pdf, vfsFonts, html5, print } from '../library/dataTableNet.js'
import dataMap from '../library/TestTable.json'
import { Decode } from '@/services'
import 'datatables.net'
import 'datatables.net-dt/css/jquery.dataTables.min.css'
import 'jszip'
import 'datatables.net-buttons-dt'
import 'datatables.net-buttons-dt/css/buttons.dataTables.min.css'
import 'datatables.net-buttons/js/buttons.colVis'
import 'datatables.net-buttons/js/buttons.flash'
import 'datatables.net-buttons/js/buttons.html5'
import 'datatables.net-buttons/js/buttons.print'
import 'datatables.net-buttons/js/dataTables.buttons'
import 'datatables.net-responsive-dt'
import $ from 'jquery'
window.JSZip = require('jszip')
export default {
  data: () => ({
    loading: false,
    dataRes: [],
    dataMobile: [],
    toDay: new Date().toISOString().slice(0, 10),
    Day: `${new Date().toISOString().slice(0, 7)}-01`,
    dataMain: dataMap,
    search: '',
    headerProps: {
      sortByText: 'เรียงตาม'
    },
    isMobile: false
  }),
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
      this.init()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
    this.init()
    this.$EventBus.$on('reloadDataTable', this.reloadDataTable)
  },
  destroyed () {
    this.$EventBus.$off('reloadDataTable', this.reloadDataTable)
  },
  mounted () {
    // const e = $('#example #example_filter').clone(true).addClass('filters').appendTo('#example #example_filter')
    // console.log('EL', e)
  },
  computed: {
    // chkSession () {
    //   if (Object.values(sessionStorage.getItem('dataRes')) !== 0) {
    //     return JSON.parse(sessionStorage.getItem('dataRes'))
    //   } else {
    //     return this.dataRes
    //   }
    // }
  },
  methods: {
    onResize () {
      if (window.innerWidth < 650) {
        this.isMobile = true
      } else {
        this.isMobile = false
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}-${month}-${year}`
    },
    async init () {
      // const search = {
      //   created: {
      //     search_type: 'all',
      //     start_date: '',
      //     end_date: ''
      //   },
      //   paid: {
      //     search_type: 'all',
      //     start_date: '',
      //     end_date: ''
      //   },
      //   company: {
      //     search_type: 'all',
      //     search_keyword: ''
      //   }
      // }
      var CompanyData = await JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      const search = await {
        company_id: CompanyData.id,
        search: {
          created: {
            search_type: 'all',
            start_date: '',
            end_date: ''
          },
          paid: {
            search_type: 'all',
            start_date: '',
            end_date: ''
          },
          company: {
            search_type: 'all',
            search_keyword: ''
          }
        }
      }
      await this.$store.dispatch('actionsReport', search)
      const response = await this.$store.state.ModuleShop.stateReport
      this.dataRes = await response.data.reports.map(x => {
        return {
          created_at: x.created_at !== '' ? this.formatDate(x.created_at.slice(0, 10)) : '',
          name_th: x.name_th,
          paid_datetime: x.paid_datetime !== '' ? this.formatDate(x.paid_datetime.slice(0, 10)) : '',
          payment_transaction_number: x.payment_transaction_number,
          quatation_id: x.quatation_id === '' ? '-' : x.quatation_id,
          total_amount: Number(x.total_amount).toFixed(2),
          transaction_status: x.transaction_status
        }
      })
      // this.dataMobile = await response.data
      // await window.sessionStorage.setItem('dataRes', JSON.stringify(this.dataRes))
      await setTimeout(function () {
        $('#example').DataTable({
          dom: 'Bfrtip',
          destroy: true,
          responsive: {
            details: {
              display: $.fn.dataTable.Responsive.display.childRowImmediate
            }
          },
          // responsive: true,
          language: {
            Search: '',
            searchPlaceholder: 'ค้นหา เลขสั่งซื้อ ชื่อร้านค้า',
            emptyTable: 'ไม่มีข้อมูลรายจ่ายของบริษัทในตาราง',
            zeroRecords: 'ไม่พบข้อมูลรายจ่ายของบริษัทในตาราง'
          },
          order: [
            [1, 'desc']
          ],
          oLanguage: {
            sSearch: '',
            searchPlaceholder: ''
          },
          // columnDefs: [
          //   {
          //     targets: [4],
          //     visible: false,
          //     searchable: false
          //   }
          // ],
          columnDefs: [
            {
              targets: 2,
              createdCell: function (cell, cellData) {
                $(cell).addClass('center')
              }
            }
          ],
          columns: [
            { data: 'created_at' },
            { data: 'payment_transaction_number' },
            { data: 'quatation_id' },
            { data: 'name_th' },
            { data: 'total_amount' },
            { data: 'paid_datetime' },
            { data: 'transaction_status' }
          ],
          buttons: [
            // {
            //   extend: 'csv',
            //   text: 'csv',
            //   charset: 'utf-8',
            //   extension: '.csv',
            //   filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            //   bom: true
            // },
            'copy',
            {
              extend: 'excel',
              text: ' Excel',
              exportOptions: {
                columns: [':visible'],
                format: {
                  body: function (data, row, column, node) {
                    var cellData
                    cellData = data.indexOf('<') < 0 ? data : $(data).text()
                    return column === 1 ? '\u200C' + cellData : cellData
                  }
                }
              }
            },
            'print'
          ]
        }
        )
      },
      0
      )
      // console.log('SesstionRes', this.dataRes)
      // console.log('Sesstion', JSON.parse(sessionStorage.getItem('dataRes')))
    },
    async reloadDataTable (reloadDataTable) {
      var CompanyData = await JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      const search = await {
        company_id: CompanyData.id,
        search: {
          created: {
            search_type: 'date',
            start_date: reloadDataTable.start,
            end_date: reloadDataTable.end
          },
          paid: {
            search_type: 'all',
            start_date: reloadDataTable.start,
            end_date: reloadDataTable.end
          },
          company: {
            search_type: 'all',
            search_keyword: 'ร้าน'
          }
        }
      }
      await this.$store.dispatch('actionsReport', search)
      const { data: { reports = [] } = {} } = await this.$store.state.ModuleShop.stateReport
      this.dataRes = await reports.map(x => {
        return {
          created_at: x.created_at !== '' ? this.formatDate(x.created_at.slice(0, 10)) : '',
          name_th: x.name_th,
          paid_datetime: x.paid_datetime !== '' ? this.formatDate(x.paid_datetime.slice(0, 10)) : '',
          payment_transaction_number: x.payment_transaction_number,
          quatation_id: x.quatation_id === '' ? '-' : x.quatation_id,
          total_amount: Number(x.total_amount).toFixed(2),
          transaction_status: x.transaction_status
        }
      })
      // this.dataRes = await [{ created_at: '2022-06-21 12:06:54', payment_transaction_number: '220621000000000010', total_amount: '2165', paid_datetime: '22025-05-10 00:00:00', name_th: null, quatation_id: '' }]
      var table = await $('#example').DataTable({
        dom: 'Bfrtip',
        destroy: true,
        responsive: {
          details: {
            display: $.fn.dataTable.Responsive.display.childRowImmediate
          }
        },
        // responsive: true,
        language: {
          Search: '',
          searchPlaceholder: 'ค้นหาจากเลขสั่งซื้อหรือชื่อร้านค้า',
          emptyTable: 'ไม่มีข้อมูลรายจ่ายของบริษัทในตาราง',
          zeroRecords: 'ไม่พบข้อมูลรายจ่ายของบริษัทในตาราง'
        },
        oLanguage: {
          sSearch: '',
          searchPlaceholder: ''
        },
        // columnDefs: [
        //   {
        //     targets: [5],
        //     visible: false,
        //     searchable: false
        //   }
        // ],
        columnDefs: [
          {
            targets: 2,
            createdCell: function (cell, cellData) {
              $(cell).addClass('center')
            }
          }
        ],
        columns: [
          { data: 'created_at' },
          {
            data: 'payment_transaction_number',
            render: function (data, type, row) {
              return type === 'export' ? data.toString() : data.toString()
            }
          },
          { data: 'quatation_id' },
          { data: 'name_th' },
          { data: 'total_amount' },
          { data: 'paid_datetime' },
          { data: 'transaction_status' }
        ],
        buttons: [
          // {
          //   extend: 'csv',
          //   text: 'csv',
          //   charset: 'utf-8',
          //   extension: '.csv',
          //   filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
          //   bom: true
          // },
          'copyHtml5',
          {
            extend: 'excel',
            text: ' Excel',
            exportOptions: {
              columns: [':visible'],
              format: {
                body: function (data, row, column, node) {
                  var cellData
                  cellData = data.indexOf('<') < 0 ? data : $(data).text()
                  return column === 1 ? '\u200C' + cellData : cellData
                }
              }
            }
          },
          'print'
        ]
      })
      await table.clear().rows.add(this.dataRes).draw()
      await table.columns.adjust().responsive.recalc()
    }
  }
}
</script>
<style scoped="scss">
.center {
  text-align: center;
}
@media only screen and (max-width: 650px) {
  .table-striped {
    display: none;
  }
  ::v-deep .dataTables_paginate.paging_simple_numbers {
    display: none;
  }
  ::v-deep #example_filter.dataTables_filter {
    display: none;
  }
}

@media only screen and (max-width: 800px) {
  ::v-deep .mobile {
    display: none;
  }
  ::v-deep .dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    width: 175px !important;
    background-color: transparent;
    margin-left: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABmJLR0QA/wD/AP+gvaeTAAABs0lEQVQ4ja3TTWsTURjF8f+ZTMUwLV10VSgVVBhDt4KrutB2aYVgBqSC4LQ7v4IbBf0KlmYErYJMJpZ+h4KLrgOm1IVvq+IqaqPNzOMiddFwk2L1LO9z+XEuPBf+UzRsEG4lE6XDfDYv9KPN5EeiKP8rKMySy5g9BBYB/+h436CuM3rSvhl3XJB3DGkky5htA188z7vy81up7BtTSPdl3LBf9rbSXJse2eioyTYobtfiV4MXZ9K0HNDZBBtv1+J5JHM3MnsEbLgQgM9RdCDfX0bMhVl9yfm0cCuZABZk9tSF/Mm76t2vgoag6oRKh/ks4He/+61RUL84LUPnnVBP/gHA2UkvOAmSFMis64R28/EPwL4VvesnNsKuFdKOEyKKckkJhT2YSdPyMKSSri8CV8eMZ24IsDEeS3gBnc1Lb55PuZBCeg2WtqKVvcH5sc2uNNemi7zUQMyZlIqiJVNgaAFsHiwF1ZDF7VurL4dC/WqmMKsvCapmuoDoStopFZa0opW9MFu/gykZxIZ+2lEJsyTCbMPQ6m4tfnFqqI/1m/V63sX3t+99Oq0DQKVZP/dPgCu/AanyqVoXnFHTAAAAAElFTkSuQmCC) !important;
    background-position: 10px 10px !important;
    outline: transparent;
    border: 1px solid #cacaca;
    border-radius: 6px;
  }
}
::v-deep #example_info {
  display: none;
}
::v-deep .dataTables_filter {
  margin-bottom: 1em;
}
::v-deep .dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    width: 600px;
    background-color: transparent;
    margin-left: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABmJLR0QA/wD/AP+gvaeTAAABs0lEQVQ4ja3TTWsTURjF8f+ZTMUwLV10VSgVVBhDt4KrutB2aYVgBqSC4LQ7v4IbBf0KlmYErYJMJpZ+h4KLrgOm1IVvq+IqaqPNzOMiddFwk2L1LO9z+XEuPBf+UzRsEG4lE6XDfDYv9KPN5EeiKP8rKMySy5g9BBYB/+h436CuM3rSvhl3XJB3DGkky5htA188z7vy81up7BtTSPdl3LBf9rbSXJse2eioyTYobtfiV4MXZ9K0HNDZBBtv1+J5JHM3MnsEbLgQgM9RdCDfX0bMhVl9yfm0cCuZABZk9tSF/Mm76t2vgoag6oRKh/ks4He/+61RUL84LUPnnVBP/gHA2UkvOAmSFMis64R28/EPwL4VvesnNsKuFdKOEyKKckkJhT2YSdPyMKSSri8CV8eMZ24IsDEeS3gBnc1Lb55PuZBCeg2WtqKVvcH5sc2uNNemi7zUQMyZlIqiJVNgaAFsHiwF1ZDF7VurL4dC/WqmMKsvCapmuoDoStopFZa0opW9MFu/gykZxIZ+2lEJsyTCbMPQ6m4tfnFqqI/1m/V63sX3t+99Oq0DQKVZP/dPgCu/AanyqVoXnFHTAAAAAElFTkSuQmCC) !important;
    background-position: 570px 10px !important;
    outline: transparent;
    border: 1px solid #cacaca;
    border-radius: 6px;
  }
  ::v-deep .input {
    display: inline-block;
    white-space: nowrap;
  }
  ::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;
}
::v-deep #example {
  padding-top: 12px;
}
::v-deep .sorting {
  color: #e6f5f3;
  background-color: #e6f5f3;
}
::v-deep table.dataTable tbody td.sorting_1 {
  text-align: center;
}
::v-deep #example > thead > tr > th {
  background-color: #e6f5f3;
  color: #27AB9C;
  font-size: 12px;
}
::v-deep table.dataTable th, table.dataTable td  {
  border-bottom: 1px solid #e7e7e7;
}
::v-deep div.dt-buttons {
  float: right;
 }
 ::v-deep .dataTables_wrapper .dataTables_filter  {
  float: left;
 }
  .fontData {
    font-size: 14px;
  }
  .tableLong {
    width: 200px;
  }
  .container {
    margin-right: 25px;
  }
  .sec {
    width: 30vw !important;
  }
  .dot {
    display: inline-block;
    width: 80px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
.hiDe {
  display: none;
}
.dot2 {
    display: inline-block;
    width: 120px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  top: 50%;
  left: 5px;
  height: 1em;
  width: 1em;
  margin-top: -9px;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control {
  padding-left: 27px;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control:before {
  left: 4px;
  height: 14px;
  width: 14px;
  border-radius: 14px;
  line-height: 14px;
  text-indent: 3px;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control,
table.dataTable.dtr-column > tbody > tr > th.dtr-control,
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  top: 50%;
  left: 50%;
  height: 0.8em;
  width: 0.8em;
  margin-top: -0.5em;
  margin-left: -0.5em;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable > tbody > tr.child {
  padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}
table.dataTable > tbody > tr.child ul.dtr-details {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
  padding-top: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
  border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}
div.dtr-modal {
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 50%;
  overflow: auto;
  margin: auto;
  z-index: 102;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: 0.5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 1em;
}
div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  border: 1px solid #eaeaea;
  background-color: #f9f9f9;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}
div.dtr-modal div.dtr-modal-close:hover {
  background-color: #eaeaea;
}
div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.6);
}
</style>
