<template>
  <v-card width="100%" :height="isMobile ? '100%' : '0%'" elevation="0">
    <v-card-text class="px-1">
    <!-- <div class="ml-3 mt-2">
      <span style="font-size: 16px; color: #333333; font-weight: 600;" ></span>สินค้าขายดี Top 10และผู้ซื้อ Top 10
    </div> -->
    <v-row :class="IpadSize ? 'mx-0' : 'mx-1'">
      <v-col cols="12" md="6" sm="6" class="mb-3 pr-0">
        <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;" class="mb-10">
          <v-card-title >
            <v-avatar :size="40">
              <v-img
               src="@/assets/icon_image/store.png"
               alt="Product"
               contain
              ></v-img>
            </v-avatar>
            <!-- <div class="mx-3"></div> -->
            <v-row dense>
              <v-col :cols="$store.state.ModuleAdminManage.dashboardListTop.order.length !== 0 ? 10 : 12" align="start" class="pt-2 pl-1" :class="$store.state.ModuleAdminManage.dashboardListTop.order.length !== 0 ? 'mt-2' : 'mt-0'">
                <span :style="isMobile ? 'font-size: 14px; color: #333333; font-weight: 700; white-space: normal;' : IpadSize ? 'font-size: 14px; color: #333333; font-weight: 700; white-space: normal;' : 'font-size: 16px; color: #333333; font-weight: 700; white-space: normal;'">Top 10 จำนวนการสั่งซื้อ
                  <!-- <div class="mx-8"> -->
                    <!-- <v-btn fab small color="teal" class="ml-0" icon @click="dialogDataProduct">
                      <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
                    </v-btn> -->
                  <!-- </div> -->
                </span>
              </v-col>
              <v-col cols="2" align="end" class="mt-2" v-if="$store.state.ModuleAdminManage.dashboardListTop.order.length !== 0">
                <v-btn fab small color="teal" class="ml-0" icon @click="openDialogShowAll('order', $store.state.ModuleAdminManage.dashboardListTop.order)">
                  <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
                </v-btn>
              </v-col>
              <!-- <v-btn v-if="isMobile" color="teal" fab text class="ml-0" @click="dialogDataProduct">
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
              <v-btn v-else outlined color="teal" class="ml-0" @click="dialogDataProduct">
                ดูทั้งหมด
                <v-icon>mdi-chevron-right</v-icon>
              </v-btn> -->
            </v-row>
          </v-card-title>
          <!-- <hr/> -->
          <v-row v-for="(item, index) in $store.state.ModuleAdminManage.dashboardListTop.order.filter((el, index) => index < 5)" :key="index" class="ml-1 mr-1">
            <v-col cols="12" md="9" sm="9" v-if="!isMobile && !IpadSize">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <span v-snip="1" v-bind="attrs" v-on="on">{{item.seller_name}}</span>
                </template>
                <span>{{item.seller_name}}</span>
              </v-tooltip>
              <v-progress-linear
               :value="item.percent"
               :color="index <= 2 ? '#50B0F7' : index === 3  ? '#50B0F7' : '#50B0F7'"
               height="27"
              >
                <div>{{item.total_order}}&nbsp;&nbsp;order</div>
              </v-progress-linear>
            </v-col>
            <v-col cols="12" md="3" sm="3" v-if="!isMobile && !IpadSize">
              <span style="font-size: 16px; padding-top: 21px;" class="d-flex flex-no-wrap justify-center">{{item.percent}}%</span>
            </v-col>
            <v-col cols="12" class="pb-0" v-if="isMobile || IpadSize">
              <v-row dense>
                <v-col cols="9">
                  <span style="width: 130px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{item.seller_name}}</span>
                </v-col>
                <v-col cols="3" align="end">
                  <span style="font-size: 16px;" class="d-flex flex-no-wrap justify-center">{{item.percent}}%</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-0" v-if="isMobile || IpadSize">
              <v-progress-linear
               :value="item.percent"
               :color="index <= 2 ? '#50B0F7' : index === 3  ? '#50B0F7' : '#50B0F7'"
               height="27"
              >
                <div>{{item.total_order}}&nbsp;&nbsp;order</div>
              </v-progress-linear>
            </v-col>
          </v-row>
          <!-- <v-data-table
              :headers="headers"
              :items="desserts"
              hide-default-footer
              class="elevation-1"
            >
              <template v-slot:item.fat="{ item, index }">
                <v-row>
                  <v-col cols="3">
                    1561
                  </v-col>
                  <v-col cols="5">
                <v-progress-linear
                v-model="item.fat"
                :color="index <= 3 ? '#43AA46' : index <= 6 ? '#FD891C' : '#F33030'"
                height="15"
              ></v-progress-linear>
              </v-col>
              <v-col cols="4">
              {{ item.fat }}%
              </v-col>
              </v-row>
              </template>
          </v-data-table> -->
        </v-card>
      </v-col>
      <v-col cols="12" md="6" sm="6" class=" mb-3" :class="IpadSize ? 'pr-2' : 'pr-0'">
        <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;" class="mb-3 pr-0">
          <v-card-title>
            <v-avatar size="40">
              <v-img
                src="@/assets/icon_image/bank.png"
                alt="Product"
                contain
              ></v-img>
            </v-avatar>
            <div class="mx-3"></div>
            <!-- <div v-if="isMobile">
              <span :style="isMobile ? 'font-size: 16px; color: #333333; font-weight: 700;' : 'font-size: 18px; color: #333333; font-weight: 700;'">Top 10 มูลค่าการสั่งซื้อ</span> &nbsp;
            </div> -->
            <div>
              <v-row dense>
                <v-col :cols="$store.state.ModuleAdminManage.dashboardListTop.values.length !== 0 ? 11 : 12" align="start" class="pt-2 pl-1" :class="$store.state.ModuleAdminManage.dashboardListTop.values.length !== 0 ? 'mt-2' : 'mt-0'">
                  <span :style="isMobile ? 'font-size: 14px; color: #333333; font-weight: 700; white-space: normal;' : IpadSize ? 'font-size: 14px; color: #333333; font-weight: 700; white-space: normal;' : 'font-size: 16px; color: #333333; font-weight: 700; white-space: normal;'">Top 10 มูลค่าการสั่งซื้อ</span> &nbsp;
                </v-col>
                <v-col cols="1" align="end" class="mt-2" :class="IpadSize ? 'pl-0' : ''" v-if="$store.state.ModuleAdminManage.dashboardListTop.values.length !== 0">
                  <v-btn fab small color="teal" class="ml-0" icon @click="openDialogShowAll('values', $store.state.ModuleAdminManage.dashboardListTop.values)">
                    <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
                  </v-btn>
                </v-col>
                <!-- <v-btn v-if="isMobile" color="teal" fab text class="ml-0" @click="dialogDataProduct">
                  <v-icon>mdi-dots-vertical</v-icon>
                </v-btn>
                <v-btn v-else outlined color="teal" class="ml-0" @click="dialogDataProduct">
                  ดูทั้งหมด
                  <v-icon>mdi-chevron-right</v-icon>
                </v-btn> -->
              </v-row>
              <!-- <span style="font-size: 16px; color: #333333; font-weight: 700;">Top 10 มูลค่าการสั่งซื้อ</span> &nbsp; -->
            </div>
            <div class="mx-8"></div>
            <!--  <v-btn v-if="isMobile" @click="dialogDataUser" fab text class="ml-0">
              <v-icon>mdi-dots-vertical</v-icon>
            </v-btn>
            <v-btn v-else @click="dialogDataUser" outlined color="teal" class="ml-0">
              ดูทั้งหมด
              <v-icon>mdi-chevron-right</v-icon>
            </v-btn> -->
          </v-card-title>
          <!-- <hr/> -->
          <v-row v-for="(item, index) in $store.state.ModuleAdminManage.dashboardListTop.values.filter((el, index) => index < 5)" :key="index" class="ml-1 mr-1">
            <v-col cols="12" md="9" sm="9" v-if="!isMobile && !IpadSize">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <span v-bind="attrs" v-on="on">{{item.seller_name}}</span>
                </template>
                <span>{{item.seller_name}}</span>
              </v-tooltip>
              <v-progress-linear
              :value="item.percent"
              :color="index <= 2 ? '#50B0F7' : index === 3  ? '#50B0F7' : '#50B0F7'"
              height="27"
              style="text-align: right"
            >
              <div>{{item.total_price}}&nbsp;&nbsp;THB</div>
            </v-progress-linear>
            </v-col>
            <v-col cols="12" md="3" sm="3" v-if="!isMobile && !IpadSize">
              <span style="font-size: 16px; padding-top: 21px;" class="d-flex flex-no-wrap justify-center">{{item.percent}}%</span>
            </v-col>
            <v-col cols="12" class="pb-0" v-if="isMobile || IpadSize">
              <v-row dense>
                <v-col cols="9">
                  <span style="width: 130px; display: inline-block; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">{{item.seller_name}}</span>
                </v-col>
                <v-col cols="3" align="end">
                  <span style="font-size: 16px;" class="d-flex flex-no-wrap justify-center">{{item.percent}}%</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-0" v-if="isMobile || IpadSize">
              <v-progress-linear
                :value="item.percent"
                :color="index <= 2 ? '#50B0F7' : index === 3  ? '#50B0F7' : '#50B0F7'"
                height="27"
                style="text-align: right"
              >
                <div>{{item.total_price}}&nbsp;&nbsp;THB</div>
              </v-progress-linear>
            </v-col>
          </v-row>
          <!-- <v-data-table
            :headers="headers"
            :items="desserts"
            hide-default-footer
            class="elevation-1"
          >
            <template v-slot:item.fat="{ item, index }">
              <v-row>
                <v-col cols="3">
                  1561
                </v-col>
                <v-col cols="5">
              <v-progress-linear
              v-model="item.fat"
              :color="index <= 3 ? '#43AA46' : index <= 6 ? '#FD891C' : '#F33030'"
              height="15"
            ></v-progress-linear>
            </v-col>
            <v-col cols="4">
            {{ item.fat }}%
            </v-col>
            </v-row>
            </template>
          </v-data-table> -->
        </v-card>
      </v-col>
    </v-row>
    <!-- dialog For show All data Top 10 -->
    <v-dialog v-model="dialogAllTopTen" persistent width="500">
      <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around" v-if="typeSelectDialog === 'order'">
              <v-toolbar-title><span style="color: #27AB9C;"><b>Top 10 จำนวนการสั่งซื้อ</b></span></v-toolbar-title>
            </v-col>
            <v-col class="d-flex justify-space-around" v-else>
              <v-toolbar-title><span style="color: #27AB9C;"><b>Top 10 มูลค่าการสั่งซื้อ</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogAllTopTen = !dialogAllTopTen" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="2" md="2">
              <v-avatar size="40" style="background: #F3F5F9; border-radius: 999px;" v-if="typeSelectDialog === 'order'">
                <v-img src="@/assets/icon_image/store.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
              </v-avatar>
              <v-avatar size="40" style="background: #F3F5F9; border-radius: 999px;" v-else>
                <v-img src="@/assets/icon_image/bank.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="10" md="6"  class="mt-3">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" v-if="typeSelectDialog === 'order'">Top 10 จำนวนการสั่งซื้อ</span>
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;" v-else>Top 10 มูลค่าการสั่งซื้อ</span>
            </v-col>
          </v-row>
          <v-row dense class="my-4">
            <v-img src="@/assets/LineDash.png" contain width="100%"></v-img>
          </v-row>
          <v-row dense>
            <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
              <v-card-text class="py-4 px-0">
                <v-row v-for="(item, index) in dataSelectDialog" :key="index" class="ml-1 mr-1">
                  <v-col cols="12" md="9" v-if="!isMobile">
                    {{item.seller_name}}
                    <v-progress-linear
                    :value="item.percent"
                    :color="index <= 2 ? '#50B0F7' : index === 3  ? '#50B0F7' : '#50B0F7'"
                    height="15"
                  ></v-progress-linear>
                  </v-col>
                  <v-col cols="12" md="3" v-if="!isMobile">
                    <span style="font-size: 16px; padding-top: 21px;" class="d-flex flex-no-wrap justify-center">{{item.percent}}%</span>
                  </v-col>
                  <v-col cols="12" class="pb-0" v-if="isMobile">
                    <v-row dense>
                      <v-col cols="9">
                        {{item.seller_name}}
                      </v-col>
                      <v-col cols="3" align="end">
                        <span style="font-size: 16px;" class="d-flex flex-no-wrap justify-center">{{item.percent}}%</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="pt-0" v-if="isMobile">
                    <v-progress-linear
                      :value="item.percent"
                      :color="index <= 2 ? '#50B0F7' : index === 3  ? '#50B0F7' : '#50B0F7'"
                      height="27"
                      style="text-align: right"
                    >
                      <div>{{item.total_price}}&nbsp;&nbsp;THB</div>
                    </v-progress-linear>
                  </v-col>
                </v-row>
                <!-- <v-list style="background: #FAFAFA;" disabled>
                  <v-list-item-group v-for="(item, i) in itemUserExtbuyer" :key="i">
                    <v-list-item>
                      <div style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333;" class="pr-4">{{ i + 1 }}</div>
                      <v-list-item-avatar size="64">
                        <v-img src="@/assets/extbuyer.png" contain max-height="40px" max-width="40px"></v-img>
                      </v-list-item-avatar>
                      <v-list-item-content class="mx-1 ml-4">
                        <v-list-item-title>
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.buyer_name }}</span><br/>
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span>
                        </v-list-item-title>
                      </v-list-item-content>
                      <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ Number(item.sum_net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                    </v-list-item>
                  </v-list-item-group>
                </v-list> -->
              </v-card-text>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
   </v-card-text>
  </v-card>
</template>
<script>
export default {
  data () {
    return {
      dialogAllTopTen: false,
      typeSelectDialog: '',
      dataSelectDialog: [],
      headers: [
        {
          text: 'ลำดับ',
          align: 'start',
          value: 'name'
        },
        { text: 'ชื่อร้าน', value: 'calories' },
        { text: 'จำนวนรายการการสั่งซื้อ', value: 'fat' }
      ],
      desserts: [
        {
          name: '1',
          calories: 'ร้านค้า A0001',
          fat: 20
        },
        {
          name: '2',
          calories: 'ร้านค้า A0002',
          fat: 37
        },
        {
          name: '3',
          calories: 'ร้านค้า A0003',
          fat: 62
        },
        {
          name: '4',
          calories: 'ร้านค้า A0004',
          fat: 50
        },
        {
          name: '5',
          calories: 'ร้านค้า A0005',
          fat: 56
        }
      ]
    }
  },
  computed: {
    isMobile () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    openDialogShowAll (status, data) {
      this.typeSelectDialog = ''
      this.dataSelectDialog = []
      this.typeSelectDialog = status
      this.dataSelectDialog = data
      this.dialogAllTopTen = !this.dialogAllTopTen
    }
  }
}
</script>
<style lang="css" scoped>
::v-deep .v-progress-linear {
 border-radius: 8px;
}
</style>
