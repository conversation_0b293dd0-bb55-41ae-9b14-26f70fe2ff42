<template>
  <v-container>
    <v-row v-resize="onResize" >
      <div v-if="!isMobile">
        <v-row>
          <v-col cols="12" md="3" sm="6">
            <div class="h-card">
              <div class="h-img-b ml-3 pt-1 pl-2" style="background: linear-gradient(60deg,#26c6da,#00acc1);">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="70"
                >mdi-bank-transfer</v-icon>
              </div>
              <div class="mt-10 pt-0 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
                เตรียมการโอนเงิน
              </div>
              <div class="ml-5">
                เตรียมการโอนเงิน {{countWaitingSum}} รายการ
              </div>
              <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-weight: 100;font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryWaitingSum).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <!-- <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span> -->
              </div>
            </div>
          </v-col>
          <v-col cols="12" md="3" sm="6">
            <div class="h-card" >
              <div class="h-img-b ml-3 pt-3 pl-3" style="background: linear-gradient(60deg,#66bb6a,#43a047);;">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="50"
                >mdi-bank-check</v-icon>
              </div>
              <div class="mt-10 pt-0 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
                โอนเงินสำเร็จ&nbsp;&nbsp;&nbsp;<span style="font-size: 13px; line-height: 40px;">(ย้อนหลัง 7 วัน)</span>
              </div>
              <div class="ml-5">
                โอนเงินสำเร็จ {{countTransferedSum}} รายการ
              </div>
              <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-weight: 100;font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryTransferredSum).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <!-- <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span> -->
              </div>
            </div>
          </v-col>
          <v-col cols="12" md="3" sm="6">
            <div class="h-card " >
              <div class="h-img-b ml-3 pt-2 pl-2" style="background: linear-gradient(60deg,#ffa726,#fb8c00);">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="65"
                >mdi-cash</v-icon>
              </div>
              <div class="mt-10 pt-0 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
                เดือนนี้&nbsp;&nbsp;&nbsp;<span style="font-size: 13px; line-height: 40px;">({{date}})</span>
              </div>
              <div class="ml-5">
                เดือนนี้ {{countMonthSum}} รายการ
              </div>
              <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-weight: 100;font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryMonthSum).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <!-- <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span> -->
              </div>
            </div>
          </v-col>
          <v-col cols="12" md="3" sm="6">
            <div class="h-card" >
              <div class="h-img-b ml-3 pt-2 pl-3" style="background: linear-gradient(60deg,#ab47bc,#8e24aa);">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="55"
                >mdi-cash-multiple</v-icon>
              </div>
              <div class="mt-10 pt-0 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
                รวม
              </div>
              <div class="ml-5">
                รวม {{countAllSum}} รายการ
              </div>
              <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-weight: 100;font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryTotal).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
              </div>
            </div>
          </v-col>
        </v-row>
      </div>
      <v-col v-else cols="12" align="center">
        <!-- <div class="h-card">
          <div class="h-img-b mt-10 pt-6">
            <v-img
              class="mx-auto py-auto"
              lazy-src="@/assets/icons/shopping-basket 1.png"
              max-height="48"
              max-width="48"
              src="@/assets/icons/shopping-basket 1.png"
            ></v-img>
          </div>
          <div class="mt-10" style="font-weight: 700; font-size: 16px; line-height: 24px;">
            ถอนเงิน
          </div>
          <div class="">
            รายการถอนเงิน {{ListData}} รายการ
          </div>
          <div class="mt-1 fontsize-28" style="font-weight: 700; font-size: 0.9vw; line-height: 40px;">
            {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
            <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
          </div>
        </div> -->
        <v-row dense class="mt-4">
          <v-col cols="6" md="3" class="pl-0">
            <div class="h-card-mobile">
              <div class="h-img-b-mobile ml-3 pt-1 pl-2" style="background: linear-gradient(60deg,#26c6da,#00acc1);">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="30"
                >mdi-bank-transfer</v-icon>
              </div>
              <div class="mt-10" style="font-weight: 700; font-size: 14px; line-height: 24px;">
                เตรียมการโอนเงิน
              </div>
              <div>
                เตรียมการโอนเงิน {{countWaitingSum}} รายการ
              </div>
              <div class="mt-1 pt-1" :style="isMobile ? 'font-weight: 100;font-size: 13px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryWaitingSum).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <!-- <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span> -->
              </div>
            </div>
          </v-col>
          <v-col cols="6" md="3" class="pr-0">
            <div class="h-card-mobile" >
              <div class="h-img-b-mobile ml-3 pt-1" style="background: linear-gradient(60deg,#66bb6a,#43a047);;">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="28"
                >mdi-bank-check</v-icon>
              </div>
              <div class="mt-9 pt-0" style="font-weight: 700; font-size: 14px; line-height: 18px;">
                โอนเงินสำเร็จ&nbsp;&nbsp;&nbsp;<br /><span style="font-size: 13px; line-height: 8px;">(ย้อนหลัง 7 วัน)</span>
              </div>
              <div >
                โอนเงินสำเร็จ {{countTransferedSum}} รายการ
              </div>
              <div class="mt-1 pt-3 " :style="isMobile ? 'font-weight: 100;font-size: 13px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryTransferredSum).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <!-- <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span> -->
              </div>
            </div>
          </v-col>
          <v-col cols="6" md="3" class="mt-10 pl-0">
            <div class="h-card-mobile" >
              <div class="h-img-b-mobile ml-3 pt-1" style="background: linear-gradient(60deg,#ffa726,#fb8c00);">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="28"
                >mdi-cash</v-icon>
              </div>
              <div class="mt-9 pt-0" style="font-weight: 700; font-size: 14px; line-height: 24px;">
                เดือนนี้&nbsp;&nbsp;&nbsp;<span style="font-size: 13px; line-height: 40px;">({{date}})</span>
              </div>
              <div class="">
                เดือนนี้ {{countMonthSum}} รายการ
              </div>
              <div class="mt-1 pt-3" :style="isMobile ? 'font-weight: 100;font-size: 13px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryMonthSum).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <!-- <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span> -->
              </div>
            </div>
          </v-col>
          <v-col cols="6" md="3" class="mt-10 pr-0">
            <div class="h-card-mobile" >
              <div class="h-img-b-mobile ml-3 pt-1" style="background: linear-gradient(60deg,#ab47bc,#8e24aa);">
                <!-- <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1.png"
                ></v-img> -->
                <v-icon
                color="white"
                size="28"
                >mdi-cash-multiple</v-icon>
              </div>
              <div class="mt-10 pt-0" style="font-weight: 700; font-size: 14px; line-height: 24px;">
                รวม
              </div>
              <div class="pt-3">
                รวม {{countAllSum}} รายการ
              </div>
              <div class="mt-1 pt-3" :style="isMobile ? 'font-weight: 100;font-size: 13px; line-height: 40px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
                {{ Number(summaryTotal).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
              </div>
            </div>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <div v-if="loading">
      <v-list-item>
        <v-list-item-title ><v-row justify="center" ><v-col cols="3"></v-col> <v-col>
          <ldsFacebook />
        </v-col><v-col cols="3"></v-col></v-row></v-list-item-title>
      </v-list-item>
    </div>
    <div v-else>
    <!--  <v-row class="justify-start ml-2 mb-0">
        <v-col>
          <v-tabs>
            <v-tab>ทั้งหมด</v-tab>
            <v-tab>รออนุมัติ</v-tab>
            <v-tab>อนุมัติ</v-tab>
            <v-tab>ไม่อนุมัติ</v-tab>
          </v-tabs>
        </v-col>
      </v-row> -->
      <!-- <v-row class="pb-0 mb-0 mt-10">
        <v-col cols="12" md="6" class="mb-0 pb-0 pl-3 ml-3">
          <v-text-field
            v-model="search"
            dense
            outlined
            :class="isMobile ? 'mx-5' : 'mb-0 pb-0'"
            style="border-radius: 6px;"
            placeholder="รหัสการสั่งซื้อ, เลขที่ทำรายการชำระเงิน"
          ><v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon></v-text-field>
        </v-col>
        <v-col cols="12"  md="3" class="pb-0 mb-0" align="right">
          <div class="mx-10 px-10 pb-0 mb-0"></div>
          <section v-if="Object.values(itemsData).length !== 0" >
            <xlsx-workbook>
              <xlsx-sheet
                :collection="sheet.data"
                v-for="sheet in sheets"
                :key="sheet.name"
                :sheet-name="sheet.name"
                class="pb-0 mb-0"
              />
              <xlsx-download>
                <v-btn
                  dark
                  color="#27AB9C"
                  class="mx-0 pl-0 pb-0 mb-0"
                  outlined
                >
                  <v-icon
                    style="font-size: 25px;"
                    large
                    color="darken-2"
                  >
                    mdi-upload-outline
                </v-icon><span style="font-size: 13px;">Export</span></v-btn>
              </xlsx-download>
            </xlsx-workbook>
          </section>
        </v-col>
        <v-col cols="12" md="1" :align="isMobile ? 'center' : 'right'" class="px-3 mx-0">
          <v-btn
           dark
           color="#27AB9C"
           @click="ref2"
          >
            ถอนเงิน
          </v-btn>
        </v-col>
      </v-row -->
      <v-row v-resize="onResize" class="mt-10">
        <v-col cols="12" class="px-0">
          <v-card width="100%" height="100%">
            <v-card-text>
              <v-row dense>
                <v-col cols="12" md="6"  :class="MobileSize ? '' : 'mt-6'">
                  <div>
                    <p style="font-size: 18px; font-weight: bold; color: #333333;">รายละเอียดรายได้ของฉัน</p>
                  </div>
                </v-col>
                <v-col cols="12" md="6" :class="MobileSize ? '' : 'mt-5 pr-8'" align="right">
                  <v-text-field
                    v-model="search"
                    dense
                    outlined
                    :class="MobileSize ? '' : 'mb-0 pb-0'"
                    style="border-radius: 6px;"
                    placeholder="ค้นหาจากรหัสการสั่งซื้อหรือเลขที่ทำรายการชำระเงิน"
                  ><v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon></v-text-field>
                </v-col>
              </v-row>
              <v-row v-if="showButton === true">
                <v-col cols="12" md="12" :align="MobileSize ? 'right' : 'right'" :class="MobileSize ? 'mb-4' : 'pr-8 pb-4'">
                  <v-btn
                    dark
                    color="#27AB9C"
                    @click="refRevenue"
                    :disabled="disableButton"
                  >
                    ถอนเงิน
                  </v-btn>
                </v-col>
              </v-row>
              <v-row dense>
                <v-tabs
                  fixed-tabs
                  v-model="tab"
                  :right="MobileSize ? true : false"
                  :show-arrows="MobileSize ? true : false"
                  :class="MobileSize ? 'pl-0' : ''"
                >
                  <v-tab class="px-2">เตรียมการโอนเงิน</v-tab>
                  <v-tab class="px-2">โอนเงินแล้ว</v-tab>
                  <v-tab class="px-2">เตรียมการถอนเงินเครดิตเทอม</v-tab>
                  <v-tab class="px-2">โอนเงินแล้วแบบเครดิตเทอม</v-tab>
                </v-tabs>
                <v-tabs-items v-model="tab">
                  <!-- tab 1 -->
                  <v-tab-item>
                    <v-card
                      color="basil"
                      :width="MobileSize ? '100%' : '1000'"
                      height="100%"
                    >
                      <v-card-text class="px-0">
                        <!-- <v-row>
                          <v-col cols="12" md="12" :align="isMobile ? 'center' : 'right'" class="pr-8 pb-4">
                            <v-btn
                              dark
                              color="#27AB9C"
                              @click="ref"
                              :disabled="disabledBtn"
                            >
                              ถอนเงิน
                            </v-btn>
                          </v-col>
                        </v-row> -->
                        <v-data-table
                         :headers="dataMain['headers-2.2']"
                         :items="listWaiting"
                         :search="search"
                         :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                         color="#D4F1E4"
                         id="DataMain"
                         style="width: 100%;"
                         :hide-default-header="MobileSize ? false : true"
                         class="my-1"
                        >
                          <template v-slot:header="{ props: { headers } }" v-if="!MobileSize">
                            <thead>
                              <tr>
                                <th v-for="h in headers" :class="h.class" :key="h.code">
                                  <span>{{h.text}}</span>
                                </th>
                              </tr>
                            </thead>
                          </template>
                          <template v-slot:no-data>
                            <div class="py-10" style="display: block; justify-content: center; width: 100%;">
                              <v-list-item :class="MobileSize ? 'pl-8' : ''">
                                <v-list-item-title >
                                  <v-row justify="center">
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'" ></v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลเตรียมการโอนเงิน</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <template
                            v-slot:[`item.index`]="{ index: number }" >{{number +1}}
                          </template>
                          <template
                            v-slot:[`item.order_id`]="{ item: { order_id } = {} }">{{ order_id }}
                          </template>
                          <template
                            v-slot:[`item.transaction_date`]="{ item: { transaction_date } = {} }">{{new Date(transaction_date).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}
                          </template>
                          <template
                            v-slot:[`item.receive_amount`]="{ item: { receive_amount } = {} }">{{ formatPrice(receive_amount) }}
                          </template>
                          <template
                            v-slot:[`item.payment_reference_id`]="{ item: {  payment_reference_id } = {} }">{{ payment_reference_id }}
                          </template>
                          <template
                            v-slot:[`item.response_message`]="{ item: { response_message } = {} }">
                            <v-chip
                              :style="response_message === 'Success' ? 'color: #42B971': 'color: #fcf0da'"
                              class="ma-2"
                              :color="response_message === 'Success' ? '#F0F9EE': '#e9a016'"
                              >
                              {{response_message === 'Success' ? 'ถอนเงินสำเร็จ' :  'รอการถอนเงิน'}}
                            </v-chip>
                          </template>
                          <template
                            v-slot:[`item.final_receive_amount`]="{ item: {  final_receive_amount } = {} }">{{ formatPrice(final_receive_amount) }}
                          </template>
                          <template
                            v-slot:[`item.detail`]="{ item }">
                            <div
                            v-if="item"
                            style="color: #27AB9C"
                            >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
                          </template>
                          <v-alert slot="no-results" :value="true">
                            <div class="py-10">
                              <v-list-item>
                                <v-list-item-title >
                                  <v-row justify="center" align="center">
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'"></v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลเตรียมการโอนเงิน</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                            <!-- <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" />
                            การค้นหาของคุณ "{{ search }}" ไม่พบผลลัพธ์ -->
                          </v-alert>
                        </v-data-table>
                      </v-card-text>
                    </v-card>
                  </v-tab-item>
                  <!--tab2-->
                  <v-tab-item>
                    <v-card
                      color="basil"
                      :width="MobileSize ? '100%' : '1000'"
                      height="100%"
                    >
                      <v-card-text>
                        <v-row>
                          <v-col cols="12" md="10">
                            <v-row dense>
                              <v-col cols="5" md="4" :class="MobileSize ? 'pb-0' : ''">
                                <v-menu
                                  ref="menu1"
                                  v-model="menu1"
                                  :close-on-content-click="false"
                                  transition="scale-transition"
                                  offset-y
                                  max-width="390px"
                                  min-width="auto"
                                  style="z-index: 12 !important"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="StartDatesText"
                                      label="เริ่มต้น"
                                      hint=""
                                      persistent-hint
                                      prepend-icon="mdi-calendar"
                                      v-bind="attrs"
                                      v-on="on"
                                    >
                                    </v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="StartDates"
                                    no-title
                                    @input="menu1 = false"
                                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                                    @change="selectStartDate(StartDates)"
                                  >
                                    <!-- <v-btn text color="primary" @click="menu1 = false">ยกเลิก</v-btn>
                                    <v-btn text color="primary" @click="selectStartDate(StartDates), menu1 = false">เลือก</v-btn> -->
                                  </v-date-picker>
                                </v-menu>
                              </v-col>
                              <!-- <v-col cols="12" md="4">
                                  <v-select
                                  v-model="weekday"
                                  :items="weekdays"
                                  item-value="id"
                                  item-text="name"
                                  return-object
                                  dense
                                  outlined
                                  hide-details
                                  @change="filterBywm"
                                  label="เลือก"
                                  class="ma-2"
                                ></v-select>
                              </v-col> -->
                              <v-col cols="1" md="1" class="pt-6 pl-8">
                                <p> - </p>
                              </v-col>
                              <v-col cols="5" md="4" :class="MobileSize ? 'pb-0' : ''">
                                <v-menu
                                  ref="menu3"
                                  v-model="menu3"
                                  :close-on-content-click="false"
                                  transition="scale-transition"
                                  offset-y
                                  max-width="390px"
                                  min-width="auto"
                                  style="z-index: 12 !important"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="EndDatesText"
                                      label="สิ้นสุด"
                                      hint=""
                                      persistent-hint
                                      prepend-icon="mdi-calendar"
                                      v-bind="attrs"
                                      v-on="on"
                                      :disabled="StartDatesText === '' ? true : false"
                                    >
                                    </v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="EndDates"
                                    no-title
                                    @input="menu3 = false"
                                    @change="selectDate(EndDates)"
                                    :min="StartDates"
                                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                                  >
                                    <!-- <v-btn text color="primary" @click="menu3 = false">ยกเลิก</v-btn>
                                    <v-btn text color="primary" @click="selectDate(EndDates), menu3 = false">เลือก</v-btn> -->
                                  </v-date-picker>
                                </v-menu>
                              </v-col>
                              <!-- <v-col cols="12" md="4">
                                  <v-select
                                  v-model="weekday"
                                  :items="weekdays"
                                  item-value="id"
                                  item-text="name"
                                  return-object
                                  dense
                                  outlined
                                  hide-details
                                  @change="filterBywm"
                                  label="เลือก"
                                  class="ma-2"
                                ></v-select>
                              </v-col> -->
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="2" :class="MobileSize ? 'mb-2' : ''">
                            <section v-if="Object.values(listTransfered).length !== 0" >
                              <xlsx-workbook>
                                <xlsx-sheet
                                  :collection="sheet.data"
                                  v-for="sheet in sheets"
                                  :key="sheet.name"
                                  :sheet-name="sheet.name"
                                  class="pb-0 mb-0"
                                />
                                <xlsx-download>
                                  <v-btn
                                  dark
                                  color="#27AB9C"
                                  :block="MobileSize ? true : false"
                                  class="mx-0 pl-0 pb-0 mb-0"
                                  outlined
                                  ><v-icon
                                  style="font-size: 25px;"
                                  large
                                  color="darken-2"
                                  >
                                    mdi-upload-outline
                                  </v-icon><span style="font-size: 13px;">Export</span></v-btn>
                                </xlsx-download>
                              </xlsx-workbook>
                            </section>
                          </v-col>
                        </v-row>
                        <v-data-table
                          :headers="dataMain['headers-2']"
                          :items="listTransfered"
                          :search="search"
                          color="#D4F1E4"
                          id="DataMain"
                          max-width="390px"
                          min-width="auto"
                          :hide-default-header="MobileSize ? false : true"
                          class="my-1"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        >
                          <template v-slot:header="{ props: { headers } }" v-if="!MobileSize">
                            <thead>
                              <tr>
                                <th v-for="h in headers" :class="h.class" :key="h.code">
                                  <span>{{h.text}}</span>
                                </th>
                              </tr>
                            </thead>
                          </template>
                          <template v-slot:no-data>
                            <div class="py-10">
                              <v-list-item>
                                <v-list-item-title >
                                  <v-row justify="center" >
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'"> </v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลการโอนเงิน</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                              </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <template
                            v-slot:[`item.index`]="{ index: number }" >{{number +1}}
                          </template>
                          <template
                            v-slot:[`item.order_id`]="{ item: { order_id } = {} }">{{ order_id }}
                          </template>
                          <template
                            v-slot:[`item.transfer_date`]="{ item: { transfer_date } = {} }">{{new Date(transfer_date).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}
                          </template>
                          <template
                            v-slot:[`item.receive_amount`]="{ item: {  receive_amount } = {} }">{{ formatPrice(receive_amount) }}
                          </template>
                          <template
                            v-slot:[`item.payment_reference_id`]="{ item: {  payment_reference_id } = {} }">{{ payment_reference_id }}
                          </template>
                          <template
                            v-slot:[`item.response_message`]="{ item: { response_message } = {} }">
                            <v-chip
                              :style="response_message === 'Success' ? 'color: #42B971': 'color: #fcf0da'"
                              class="ma-2"
                              :color="response_message === 'Success' ? '#F0F9EE': '#e9a016'"
                            >
                              {{response_message === 'Success' ? 'ถอนเงินสำเร็จ' :  'รอการถอนเงิน'}}
                            </v-chip>
                          </template>
                          <template
                            v-slot:[`item.final_receive_amount`]="{ item: {  final_receive_amount } = {} }">{{ formatPrice(final_receive_amount) }}
                          </template>
                          <template
                          v-slot:[`item.detail`]="{ item }">
                            <div
                            v-if="item"
                            style="color: #27AB9C"
                            >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
                          </template>
                          <v-alert slot="no-results" :value="true" >
                            <div class="py-10">
                              <v-list-item>
                                <v-list-item-title >
                                  <v-row justify="center" >
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'"> </v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลการโอนเงิน</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                              </v-list-item-title>
                              </v-list-item>
                            </div>
                          <!--  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" />
                            การค้นหาของคุณ "{{ search }}" ไม่พบผลลัพธ์ -->
                          </v-alert>
                        </v-data-table>
                      </v-card-text>
                    </v-card>
                  </v-tab-item>
                  <!-- tab 3 -->
                  <v-tab-item>
                    <v-card
                      color="basil"
                      :width="MobileSize ? '100%' : '1000'"
                      height="100%"
                    >
                      <v-card-text>
                        <!-- <v-row>
                          <v-col cols="12" md="12" :align="isMobile ? 'center' : 'right'" class="pr-8 pb-4">
                            <v-btn
                              dark
                              color="#27AB9C"
                              @click="refCreditTerm"
                              :disabled="disabledBtn2"
                            >
                              ถอนเงิน
                            </v-btn>
                          </v-col>
                        </v-row> -->
                        <v-data-table
                        :headers="dataMain['headers-2.2']"
                        :items="listWaitingCreditTerm"
                        :search="search"
                        color="#D4F1E4"
                        id="DataMain"
                        max-width="390px"
                        min-width="auto"
                        :hide-default-header="MobileSize ? false : true"
                        class="my-1"
                        :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        >
                          <template v-slot:header="{ props: { headers } }" v-if="!MobileSize">
                            <thead>
                              <tr style="width: 100%;">
                                <th v-for="h in headers" :class="h.class" :key="h.code">
                                  <span>{{h.text}}</span>
                                </th>
                              </tr>
                            </thead>
                          </template>
                          <template v-slot:no-data>
                            <div class="py-10">
                              <v-list-item :class="MobileSize ? 'px-0' : ''">
                                <v-list-item-title >
                                  <v-row justify="center">
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'" ></v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลเตรียมการถอนเงินเครดิตเทอม</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <template
                            v-slot:[`item.index`]="{ index: number }" >{{number +1}}
                          </template>
                          <template
                            v-slot:[`item.order_id`]="{ item: { order_id } = {} }">{{ order_id }}
                          </template>
                          <template
                            v-slot:[`item.transaction_date`]="{ item: { transaction_date } = {} }">{{new Date(transaction_date).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}
                          </template>
                          <template
                            v-slot:[`item.receive_amount`]="{ item: { receive_amount } = {} }">{{ formatPrice(receive_amount) }}
                          </template>
                          <template
                            v-slot:[`item.payment_reference_id`]="{ item: {  payment_reference_id } = {} }">{{ payment_reference_id }}
                          </template>
                          <template
                            v-slot:[`item.response_message`]="{ item: { response_message } = {} }">
                            <v-chip
                              :style="response_message === 'Success' ? 'color: #42B971': 'color: #fcf0da'"
                              class="ma-2"
                              :color="response_message === 'Success' ? '#F0F9EE': '#e9a016'"
                              >
                              {{response_message === 'Success' ? 'ถอนเงินสำเร็จ' :  'รอการถอนเงิน'}}
                            </v-chip>
                          </template>
                          <template
                            v-slot:[`item.final_receive_amount`]="{ item: {  final_receive_amount } = {} }">{{ formatPrice(final_receive_amount) }}
                          </template>
                          <template
                            v-slot:[`item.detail`]="{ item }">
                            <div
                            v-if="item"
                            style="color: #27AB9C"
                            >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
                          </template>
                          <v-alert slot="no-results" :value="true">
                            <div class="py-10">
                              <v-list-item>
                                <v-list-item-title >
                                  <v-row justify="center" >
                                    <!-- <v-col cols="2"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'" ></v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลเตรียมการถอนเงินแบบเครดิตเทอม</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                          </v-alert>
                        </v-data-table>
                      </v-card-text>
                    </v-card>
                  </v-tab-item>
                  <!-- tab 4 -->
                  <v-tab-item>
                    <v-card
                      color="basil"
                      :width="MobileSize ? '100%' : '1000'"
                      height="100%"
                    >
                      <v-card-text>
                        <v-row>
                          <v-col cols="12" md="10">
                            <v-row>
                              <v-col cols="5" md="4" :class="MobileSize ? 'pb-0' : ''">
                                <v-menu
                                  ref="menu2"
                                  v-model="menu2"
                                  :close-on-content-click="false"
                                  transition="scale-transition"
                                  offset-y
                                  max-width="390px"
                                  min-width="auto"
                                  style="z-index: 12 !important"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="StartDateCreditText"
                                      label="เริ่มต้น"
                                      hint=""
                                      persistent-hint
                                      prepend-icon="mdi-calendar"
                                      v-bind="attrs"
                                      v-on="on"
                                    >
                                    </v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="StartDateCredit"
                                    no-title
                                    @input="menu2 = false"
                                    @change="StartDateCreditTerm(StartDateCredit)"
                                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                                  >
                                    <!-- <v-btn text color="primary" @click="menu2 = false">ยกเลิก</v-btn>
                                    <v-btn text color="primary" @click="StartDateCreditTerm(StartDateCredit), menu2 = false">เลือก</v-btn> -->
                                  </v-date-picker>
                                </v-menu>
                              </v-col>
                              <!-- <v-col cols="12" md="4">
                                  <v-select
                                  v-model="weekday"
                                  :items="weekdays"
                                  item-value="id"
                                  item-text="name"
                                  return-object
                                  dense
                                  outlined
                                  hide-details
                                  @change="filterBywm"
                                  label="เลือก"
                                  class="ma-2"
                                ></v-select>
                              </v-col> -->
                              <v-col cols="1" md="1" class="pt-8 pl-8">
                                <p> - </p>
                              </v-col>
                              <v-col cols="5" md="4" :class="MobileSize ? 'pb-0' : ''">
                                <v-menu
                                  ref="menu4"
                                  v-model="menu4"
                                  :close-on-content-click="false"
                                  transition="scale-transition"
                                  offset-y
                                  max-width="390px"
                                  min-width="auto"
                                  style="z-index: 12 !important"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="EndDateCreditText"
                                      label="สิ้นสุด"
                                      hint=""
                                      persistent-hint
                                      prepend-icon="mdi-calendar"
                                      v-bind="attrs"
                                      v-on="on"
                                      :disabled="StartDateCreditText === '' ? true : false"
                                    >
                                    </v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="EndDateCredit"
                                    no-title
                                    @change="selectDateCreditTerm(EndDateCredit)"
                                    @input="menu4 = false"
                                    :min="StartDateCredit"
                                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                                  >
                                    <!-- <v-btn text color="primary" @click="menu4 = false">ยกเลิก</v-btn>
                                    <v-btn text color="primary" @click="selectDateCreditTerm(EndDateCredit), menu4 = false">เลือก</v-btn> -->
                                  </v-date-picker>
                                </v-menu>
                              </v-col>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="2" :class="MobileSize ? 'mb-2' : ''">
                            <section v-if="Object.values(listTransferedCreditTerm).length !== 0" >
                              <xlsx-workbook>
                                <xlsx-sheet
                                  :collection="sheet.data"
                                  v-for="sheet in sheetsCreditTerm"
                                  :key="sheet.name"
                                  :sheet-name="sheet.name"
                                  class="pb-0 mb-0"
                                />
                                <xlsx-download>
                                  <v-btn
                                  dark
                                  :block="MobileSize ? true : false"
                                  color="#27AB9C"
                                  class="mx-0 pl-0 pb-0 mb-0"
                                  outlined
                                  ><v-icon
                                  style="font-size: 25px;"
                                  large
                                  color="darken-2"
                                  >
                                    mdi-upload-outline
                                  </v-icon><span style="font-size: 13px;">Export</span></v-btn>
                                </xlsx-download>
                              </xlsx-workbook>
                            </section>
                          </v-col>
                        </v-row>
                        <v-row></v-row>
                        <v-data-table
                          :headers="dataMain['headers-2']"
                          :items="listTransferedCreditTerm"
                          :search="search"
                          color="#D4F1E4"
                          id="DataMain"
                          max-width="390px"
                          min-width="auto"
                          :hide-default-header="MobileSize ? false : true"
                          class="my-1"
                          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        >
                          <template v-slot:header="{ props: { headers } }" v-if="!MobileSize">
                            <thead>
                              <tr>
                                <th v-for="h in headers" :class="h.class" :key="h.code">
                                  <span>{{h.text}}</span>
                                </th>
                              </tr>
                            </thead>
                          </template>
                          <template v-slot:no-data>
                            <div class="py-10">
                              <v-list-item>
                                <v-list-item-title >
                                  <v-row justify="center" >
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'" > </v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'" >คุณยังไม่มีข้อมูลการโอนเงินแบบเครดิตเทอม</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                              </v-list-item-title>
                              </v-list-item>
                            </div>
                          </template>
                          <template
                            v-slot:[`item.index`]="{ index: number }" >{{number +1}}
                          </template>
                          <template
                            v-slot:[`item.order_id`]="{ item: { order_id } = {} }">{{ order_id }}
                          </template>
                          <template
                            v-slot:[`item.transfer_date`]="{ item: { transfer_date } = {} }">{{new Date(transfer_date).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}
                          </template>
                          <template
                            v-slot:[`item.receive_amount`]="{ item: {  receive_amount } = {} }">{{ formatPrice(receive_amount) }}
                          </template>
                          <template
                            v-slot:[`item.payment_reference_id`]="{ item: {  payment_reference_id } = {} }">{{ payment_reference_id }}
                          </template>
                          <template
                            v-slot:[`item.response_message`]="{ item: { response_message } = {} }">
                            <v-chip
                              :style="response_message === 'Success' ? 'color: #42B971': 'color: #fcf0da'"
                              class="ma-2"
                              :color="response_message === 'Success' ? '#F0F9EE': '#e9a016'"
                            >
                              {{response_message === 'Success' ? 'ถอนเงินสำเร็จ' :  'รอการถอนเงิน'}}
                            </v-chip>
                          </template>
                          <template
                            v-slot:[`item.final_receive_amount`]="{ item: {  final_receive_amount } = {} }">{{ formatPrice(final_receive_amount) }}
                          </template>
                          <template
                          v-slot:[`item.detail`]="{ item }">
                            <div
                            v-if="item"
                            style="color: #27AB9C"
                            >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
                          </template>
                          <v-alert slot="no-results" :value="true" >
                            <div class="py-10">
                              <v-list-item>
                                <v-list-item-title>
                                  <v-row justify="center">
                                    <!-- <v-col cols="3"></v-col> -->
                                    <v-col cols="12" align="center">
                                      <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" :max-width="MobileSize ? '200' : '400'" ></v-img>
                                      <h2 style="padding-top: 20px; color: #27AB9C;">
                                        <span :style="MobileSize ? 'font-weight: bold; font-size: 14px; line-height: 32px;' : 'font-weight: bold; font-size: 24px; line-height: 32px;'">คุณยังไม่มีข้อมูลการโอนเงินแบบเครดิตเทอม</span><br/>
                                      </h2>
                                    </v-col>
                                    <!-- <v-col cols="3"></v-col> -->
                                  </v-row>
                                </v-list-item-title>
                              </v-list-item>
                            </div>
                            <!-- <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" />
                            การค้นหาของคุณ "{{ search }}" ไม่พบผลลัพธ์ -->
                          </v-alert>
                        </v-data-table>
                      </v-card-text>
                    </v-card>
                  </v-tab-item>
                </v-tabs-items>
              </v-row>
            </v-card-text>
          </v-card>
          <!-- <v-data-table
             :headers="dataMain['headers-2']"
             :items="itemsData"
             :search="search"
             color="#D4F1E4"
             id="DataMain"
             hide-default-header
             class="my-1 mx-3"
            >
            <template v-slot:header="{ props: { headers } }">
              <thead>
                <tr>
                  <th v-for="h in headers" :class="h.class" :key="h.code">
                    <span>{{h.text}}</span>
                  </th>
                </tr>
              </thead>
            </template>
            <template
              v-slot:[`item.index`]="{ index: number }" >{{number +1}}
            </template>
            <template
              v-slot:[`item.orderIDRef`]="{ item: { orderIDRef } = {} }">{{ orderIDRef }}
            </template>
              <template
              v-slot:[`item.date`]="{ item: { date } = {} }">{{new Date(parseDate(date)).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" })}}
            </template>
              <template
              v-slot:[`item.total_amount`]="{ item: {  total_amount } = {} }">{{ Number(total_amount).toLocaleString() }}
            </template>
            <template
              v-slot:[`item.payment_transaction_number`]="{ item: {  payment_transaction_number } = {} }">{{ payment_transaction_number }}
            </template>
            <template
              v-slot:[`item.transaction_status`]="{ item: { transaction_status } = {} }">
              <v-chip
              :style="transaction_status === 'Success' ? 'color: #42B971': 'color: #fcf0da'"
              class="ma-2"
              :color="transaction_status === 'Success' ? '#F0F9EE': '#e9a016'"
              >
              {{transaction_status === 'Success' ? 'ถอนเงินสำเร็จ' :  'รอการถอนเงิน'}}
            </v-chip>
            </template>
            <template
              v-slot:[`item.income`]="{ item: {  income } = {} }">{{ income.toLocaleString() }}
            </template>
              <template
              v-slot:[`item.detail`]="{ item }">
              <div
              v-if="item"
              style="color: #27AB9C"
              >รายละเอียด <v-icon style="color: #27AB9C">mdi-chevron-right</v-icon></div>
            </template>
          </v-data-table> -->
        </v-col>
      </v-row>
      <ListRevenue />
    </div>
  </v-container>
</template>
<script>
import dataMap from '../library/TestTable.json'
// import lds from '../loading/lds-facebook'
// import eventBus from '@/components/eventBus'
import { XlsxWorkbook, XlsxSheet, XlsxDownload } from 'vue-xlsx'

export default {
  components: {
    XlsxDownload,
    XlsxSheet,
    XlsxWorkbook,
    ListRevenue: () => import('./dialogRevenue'),
    ldsFacebook: () => import('@/components/loading/lds-facebook.vue')
  },
  data () {
    return {
      dataMain: dataMap,
      itemsData: [],
      itemsData2: [],
      itemsData3: [],
      dateFormatted: [],
      dates: [],
      StartDates: '',
      EndDates: '',
      StartDatesText: '',
      EndDatesText: '',
      StartDateCreditText: '',
      EndDateCreditText: '',
      StartDateCredit: '',
      EndDateCredit: '',
      search: '',
      index: 1,
      counterList: '',
      ListData: '',
      sheets: [],
      sheetsCreditTerm: [],
      loading: false,
      percentage: '',
      isMobile: false,
      tab: null,
      menu1: false,
      weekday: [1],
      weekdays: [
        {
          id: 1, name: 'ทั้งหมด'
        },
        {
          id: 2, name: 'สัปดาห์'
        },
        {
          id: 3, name: 'ปี'
        }
      ],
      items: [
        'Appetizers', 'Entrees', 'Deserts', 'Cocktails'
      ],
      text: 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.',
      listTransfered: [],
      listWaiting: [],
      listRefund: [],
      listProblem: [],
      summaryMonth: '',
      summaryTotal: '',
      summaryTransferred: '',
      summaryWaiting: '',
      countAll: '',
      countMonth: '',
      countTransfered: '',
      countWaiting: '',
      menu2: false,
      menu3: false,
      menu4: false,
      dates1: [],
      listTransferedCreditTerm: [],
      listWaitingCreditTerm: [],
      listRefundCreditTerm: [],
      summaryMonthSum: '',
      summaryTotalSum: '',
      summaryTransferredSum: '',
      summaryWaitingSum: '',
      countAllSum: '',
      countMonthSum: '',
      countTransferedSum: '',
      countWaitingSum: '',
      disabledBtn: false,
      disabledBtn2: false,
      date: '-',
      showButton: false,
      disableButton: false
    }
  },
  created () {
    // this.$EventBus.$on('filterDate', this.filterDate)
    // this.$EventBus.$on('ref', this.ref)
    var d = new Date().toLocaleDateString('th-TH', {
      month: 'long'
    })
    this.date = d
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.$EventBus.$emit('changeNav')
    this.init()
  },
  computed: {
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    dateRangeText () {
      return this.dates.join(' - ')
    },
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/revenueMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/revenue' }).catch(() => {})
      }
    },
    menu1 (val) {
      if (val === true) {
        this.EndDatesText = ''
        this.EndDates = ''
      }
    },
    menu2 (val) {
      if (val === true) {
        this.EndDateCreditText = ''
        this.EndDateCredit = ''
      }
    },
    dateStart (val) {
      this.dateFormatted = this.formatDate(this.dateStart)
    },
    dates (val) {
      // if (Object.keys(val)[1] === '1') {
      //   this.menu1 = false
      // }
      // console.log('ERR', val)
    }
  },
  destroyed () {
    // this.$EventBus.$off('filterDate')
    // this.$EventBus.$off('ref')
  },
  methods: {
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    formatPrice (value) {
      // console.log(value)
      const val = (value / 1).toFixed(2).replace(',', '.')
      return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
    },
    async init () {
      this.$store.commit('openLoader')
      const data2 = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        start_date: '',
        end_date: ''
      }
      await this.$store.dispatch('actionDetailMerchantFromPayment', data2)
      const response = await this.$store.state.ModuleShop.stateDetailMerchantFromPayment
      this.listTransfered = await response.data.list_transfered
      this.listWaiting = await response.data.list_waiting
      this.listRefund = await response.data.list_refund
      this.listProblem = await response.data.list_problem
      this.percentage = await response.data.percentageNewOld
      this.counterList = await response.data.summary_waiting
      this.summaryMonth = await response.data.summary_month
      this.summaryTotal = await response.data.summary_total
      this.summaryTransferred = await response.data.summary_transferred
      this.summaryWaiting = await response.data.summary_waiting
      this.countAll = await response.data.count_all
      this.countMonth = await response.data.count_month
      this.countTransfered = await response.data.count_transfered
      this.countWaiting = await response.data.count_waiting
      this.ListData = await Object.values(response.data.list_waiting).length
      this.disabledBtn = await response.data.count_data_to_refshare
      this.sheets = [{ name: 'รายการสำเร็จ', data: [...this.mapExcel(this.listTransfered)] }, { name: 'สินค้าที่ถูกตีกลับ', data: [...this.mapExcelSheetTwo(this.listRefund)] }, { name: 'สินค้าที่มีปัญหา', data: [...this.mapExcelSheetThree(this.listProblem)] }]
      await this.getDetailCreditTerm()
    },
    async getDetailCreditTerm () {
      const data3 = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        start_date: '',
        end_date: ''
      }
      await this.$store.dispatch('actionDetailMerchantCrterm', data3)
      const response1 = await this.$store.state.ModuleShop.stateDetailMerchantCrtermFromPayment
      // console.log(response1.data)
      this.listTransferedCreditTerm = await response1.data.list_transfered
      this.listWaitingCreditTerm = await response1.data.list_waiting
      this.listRefundCreditTerm = await response1.data.list_refund
      this.summaryMonthSum = parseFloat(response1.data.summary_month) + parseFloat(this.summaryMonth)
      this.summaryTotalSum = parseFloat(response1.data.summary_total) + parseFloat(this.summaryTotalSum)
      this.summaryTransferredSum = parseFloat(response1.data.summary_transferred) + parseFloat(this.summaryTransferred)
      this.summaryWaitingSum = parseFloat(response1.data.summary_waiting) + parseFloat(this.summaryWaiting)
      this.countAllSum = parseFloat(response1.data.count_all) + parseFloat(this.countAll)
      this.countMonthSum = parseFloat(response1.data.count_month) + parseFloat(this.countMonth)
      this.countTransferedSum = parseFloat(response1.data.count_transfered) + parseFloat(this.countTransfered)
      this.countWaitingSum = parseFloat(response1.data.count_waiting) + parseFloat(this.countWaiting)
      this.ListData = await Object.values(response1.data.list_waiting).length
      // console.log('count_data_to_refshare', response1.data.count_data_to_refshare)
      this.disabledBtn2 = await response1.data.count_data_to_refshare
      this.sheetsCreditTerm = [{ name: 'รายการสำเร็จแบบเครดิตเทอม', data: [...this.mapExcel(this.listTransferedCreditTerm)] }, { name: 'สินค้าที่ถูกตีกลับ', data: [...this.mapExcelSheetTwo(this.listRefundCreditTerm)] }]
      this.checkDisableButton(this.disabledBtn, this.disabledBtn2)
      this.$store.commit('closeLoader')
    },
    checkDisableButton (valbtn, valbtn1) {
      if (valbtn.length !== 0 && valbtn1 !== 0) {
        this.showButton = true
        this.disableButton = false
      } else if (valbtn.length !== 0 && valbtn1 === 0) {
        this.showButton = true
        this.disableButton = false
      } else if (valbtn.length === 0 && valbtn1 !== 0) {
        this.showButton = true
        this.disableButton = false
      } else {
        this.showButton = false
        this.disableButton = true
      }
    },
    selectStartDate (val) {
      this.$refs.menu1.save(val)
      this.StartDatesText = this.formatDateChange(this.StartDates)
    },
    StartDateCreditTerm (val) {
      this.$refs.menu2.save(val)
      this.StartDateCreditText = this.formatDateChange(this.StartDateCredit)
    },
    async selectDate (val) {
      // const date = this.dates.sort((a, b) => {
      //   return new Date(b) - new Date(a)
      // })
      this.$refs.menu3.save(val)
      this.EndDatesText = this.formatDateChange(this.EndDates)
      const data2 = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        start_date: this.StartDates.replace(/-/g, ''),
        end_date: this.EndDates.replace(/-/g, '')
      }
      // const data2 = {
      //   seller_shop_id: localStorage.getItem('shopSellerID'),
      //   start_date: '20220601',
      //   end_date: '20220614'
      // }
      // console.log('DATA2', data2)
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionDetailMerchantFromPayment', data2)
      const response = await this.$store.state.ModuleShop.stateDetailMerchantFromPayment
      // console.log('SelectData', response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listTransfered = await response.data.list_transfered
        this.listWaiting = await response.data.list_waiting
        this.percentage = await response.data.percentageNewOld
        this.counterList = await response.data.summary_waiting
        this.ListData = await Object.values(response.data.list_waiting).length
        this.sheets = await [{ name: 'รายการสำเร็จ', data: this.mapExcel(this.listTransfered) }]
      }
    },
    async selectDateCreditTerm (val) {
      this.$refs.menu4.save(val)
      this.EndDateCreditText = this.formatDateChange(this.EndDateCredit)
      // const date = this.dates1.sort((a, b) => {
      //   return new Date(b) - new Date(a)
      // })
      const data3 = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        start_date: this.StartDateCredit.replace(/-/g, ''),
        end_date: this.EndDateCredit.replace(/-/g, '')
      }
      // const data2 = {
      //   seller_shop_id: localStorage.getItem('shopSellerID'),
      //   start_date: '20220601',
      //   end_date: '20220614'
      // }
      // console.log('DATA2', data2)
      await this.$store.dispatch('actionDetailMerchantCrterm', data3)
      const response = await this.$store.state.ModuleShop.stateDetailMerchantCrtermFromPayment
      // console.log('SelectData', response)
      this.listTransferedCreditTerm = await response.data.list_transfered
      this.listWaitingCreditTermg = await response.data.list_waiting
      this.percentage = await response.data.percentageNewOld
      this.counterList = await response.data.summary_waiting
      this.ListData = await Object.values(response.data.list_waiting).length
      this.sheetsCreditTerm = await [{ name: 'รายการสำเร็จแบบเครดิตเทอม', data: this.mapExcel(this.listTransferedCreditTerm) }]
    },
    async ref2 () {
      this.$EventBus.$emit('dialogRevenue')
    },
    async refRevenue () {
      this.loading = await true
      const data = await {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionsNewRefMerchant', data)
      this.itemsData2 = await this.$store.state.ModuleShop.stateNewRefMerchant
      if (this.itemsData2.message === 'Withdraw success') {
        await this.succesStatus()
        await this.$swal.fire({ icon: 'success', text: 'ถอนเงินสำเร็จ', timer: 1800, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'Withdraw credit success and Withdraw normal not success') {
        this.succesStatus()
        this.$swal.fire({ icon: 'success', text: 'ถอนเงินสำเร็จ', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'Withdraw credit not success and Withdraw normal success') {
        this.succesStatus()
        this.$swal.fire({ icon: 'success', text: 'ถอนเงินสำเร็จ', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'Withdraw not success') {
        // this.succesStatus()
        this.$swal.fire({ icon: 'error', text: 'ถอนเงินไม่สำเร็จ', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'recipient not match') {
        // this.succesStatus()
        this.$swal.fire({ icon: 'error', text: 'ไม่พบผู้รับนี้ในระบบ', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'Amount is null') {
        // this.succesStatus()
        this.$swal.fire({ icon: 'error', text: 'ไม่มีรายการโอนเงิน', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'An error has occurred. Please try again in an hour or two.') {
        // this.succesStatus()
        this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else if (this.itemsData2.message === 'Server payment is error') {
        // this.succesStatus()
        this.$swal.fire({ icon: 'error', text: 'ระบบปิดปรับปรุงชั่วคราว กรุณาติดต่อเจ้าหน้าที่', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      } else {
        // this.succesStatus()
        this.$swal.fire({ icon: 'error', text: `${this.itemsData2.result}`, timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
        this.loading = await false
      }
    },
    // async ref () {
    //   this.loading = await true
    //   const data = await {
    //     seller_shop_id: localStorage.getItem('shopSellerID')
    //   }
    //   await this.$store.dispatch('actionsNewRefMerchant', data)
    //   this.itemsData2 = await this.$store.state.ModuleShop.stateNewRefMerchant
    //   if (this.itemsData2.message === 'Refshare success') {
    //     await this.succesStatus()
    //     await this.$swal.fire({ icon: 'success', text: 'ถอนเงิน สำเร็จ', timer: 1800, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Not Found Data') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'ไม่พบข้อมูลรายการถอนเงิน', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Refshare not success') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'ถอนเงินไม่สำเร็จ', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Fail to sent data refshare to payment') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'การส่งข้อมูลมีการผิดพลาด', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Some data Real Shipping Cost Has Problem') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'บางรายการมีปัญหาเกี่ยวกับค่าขนส่ง', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: `${this.itemsData2.result}`, timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   }
    // },
    // async refCreditTerm () {
    //   this.loading = await true
    //   const data = await {
    //     seller_shop_id: localStorage.getItem('shopSellerID')
    //   }
    //   await this.$store.dispatch('actionRefShareCreditTerm', data)
    //   this.itemsData2 = await this.$store.state.ModuleShop.stateRefShareCreditTermToMerchant
    //   if (this.itemsData2.message === 'Refshare credit term success') {
    //     await this.succesStatus()
    //     await this.$swal.fire({ icon: 'success', text: 'ถอนเงิน สำเร็จ', timer: 1800, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Not Found Data') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'ไม่พบข้อมูลรายการถอนเงิน', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Fail to sent data refshare to payment') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'การส่งข้อมูลมีการผิดพลาด', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else if (this.itemsData2.message === 'Refshare credit term not success') {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: 'ถอนเงินไม่สำเร็จ', timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   } else {
    //     this.succesStatus()
    //     this.$swal.fire({ icon: 'error', text: `${this.itemsData2.result}`, timer: 2500, showCloseButton: false, showCancelButton: false, showConfirmButton: false })
    //     this.loading = await false
    //   }
    // },
    succesStatus () {
      // console.log('in function')
      this.init()
      this.getDetailCreditTerm()
      this.$EventBus.$emit('appendData')
    },
    async filterData (data) {
      // console.log(data)
      const data2 = await {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        choose_type: data.name,
        choose_date: ''
      }
      // console.log(data)
      await this.$store.dispatch('actionsListRefMerchant', data2)
      const { data: { incomeShop = '', listRefShare, percentageNewOld = '' } = {} } = await this.$store.state.ModuleShop.stateListRefMerchant
      this.percentage = await percentageNewOld
      this.counterList = await incomeShop
      this.itemsData = await listRefShare
      this.ListData = await listRefShare.length
      this.sheets = await [{ name: 'SheetOne', data: this.itemsData }]
    },
    commaSeparateNumber (val) {
      while (/(\d + )(\d{3})/.test(val.toString())) {
        val = val.toString().replace(/(\d + )(\d{3})/, '$1' + ',' + '$2')
      }
      return val
    },
    parseDate (date) {
      // console.log('DATE', date)
      if (!date) return null
      const [day, month, year] = date.split('-')
      return `${year}-${month}-${day}`
    },
    mapExcel (val) {
      const Excel = val.map(x => {
        return {
          'เลขที่ทำรายการชำระเงิน ': x.order_id,
          'ราคาสุทธิที่ผู้ซื้อชำระผ่านระบบ ': x.receive_amount,
          'สถานะการชำระเงิน ': x.response_message,
          'ยอดคงเหลือหลังหักค่าธรรมเนียมรับชำระเงิน ': x.final_amount,
          'เลขอ้างอิงการรับชำระ ': x.payment_reference_id,
          'ราคาขนส่งรวม(ราคาประมาณค่า) ': x.total_shipping,
          'ราคาขนส่ง(รวมราคาจริง) ': x.extra_shipping,
          'วันที่ทำธุรกรรม ': x.transaction_date,
          'เวลาทำธุรกรรม ': x.transaction_time,
          'ค่าธรรมเนียมรับชำระเงิน ': x.fee,
          'ค่าธรรมเนียมรับชำระเงินพร้อมภาษี 7 % ': x.fee_vat,
          'ค่าธรรมเนียม GP ': x.gp,
          'รายได้สุทธิหลังหักค่าธรรมเนียมรับชำระเงิน GP และ ขนส่ง ': x.final_receive_amount,
          'ประเภทขนส่ง ': x.service_type,
          'การจัดส่งสำเร็จ ': x.shipping_status,
          'การตีกลับสินค้า ': x.shipping_return_status
        }
      })
      // console.log('excel:', Excel)
      return Excel
    },
    mapExcelSheetTwo (val) {
      var Excel = []
      if (val.length !== 0) {
        Excel = val.map(x => {
          return {
            'เลขที่ทำรายการชำระเงิน ': x.payment_transaction_number,
            'ราคาสินค้ารวมภาษี ': x.total_price_vat,
            'ราคาขนส่งรวม(ราคาประมาณค่า) ': x.total_shipping,
            'ราคาสุทธิรวมภาษีและขนส่ง ': x.net_price,
            'เลขอ้างอิงการรับชำระ ': x.orderIDRef,
            'สถานะขนส่ง ': x.delivery_type,
            'ประเภทขนส่ง ': x.service_type,
            'รายการ SKU  ': x.product_list,
            'ราคาขนส่งรวม (ราคาจริง) ': x.extra_shipping,
            'วันที่ทำธุรกรรม ': x.date,
            'เวลาทำธุรกรรม ': x.time
          }
        })
      } else {
        Excel = [{}]
      }
      return Excel
    },
    mapExcelSheetThree (val) {
      var Excel1 = []
      if (val.length !== 0) {
        Excel1 = val.map(x => {
          return {
            'เลขที่ทำรายการชำระเงิน ': x.payment_transaction_number,
            'ราคาสินค้ารวมภาษี ': x.total_price_vat,
            'ราคาขนส่งรวม (ราคาประมาณค่า) ': x.total_shipping,
            'ราคาสุทธิรวมกับภาษีและขนส่ง ': x.net_price,
            'เลขอ้างอิงการรับชำระ ': x.orderIDRef,
            'สถานะขนส่ง ': x.delivery_type,
            'ประเภทขนส่ง ': x.service_type,
            'รายการ SKU ': x.product_list,
            'ราคาขนส่ง (ราคาจริง) ': x.extra_shipping,
            'วันที่ทำธุรกรรม ': x.date,
            'เวลาทำธุรกรรม ': x.time
          }
        })
      } else {
        Excel1 = [{}]
      }
      return Excel1
    },
    onResize () {
      if (window.innerWidth < 950) {
        this.isMobile = true
      } else {
        this.isMobile = false
      }
    },
    formatDateChange (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}-${month}-${year}`
    }
  }
}
</script>
<!-- <style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.55rem;
}
</style> -->
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 75px;
          z-index: 10;
          background: white;
        }
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 64px;
        }
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.header-style {
  background: #D4F1E4 !important;
}
.status-1 {
  color: #E9A016;
}

.h-card{
  width: 232px;
  height: 200px;
  background: #fff;
  border-radius: 3px;
  position: relative;
    display: flex;
    flex-direction: column;
    min-width: 0;
    word-wrap: break-word;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 54%);
}
.h-card-mobile{
  width: 100%;
  height: 200px;
  background: #fff;
  border-radius: 3px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  box-shadow: 0 1px 4px 0 rgb(0 0 0 / 54%);
}
.h-img-b {
 width: 80px;
 height: 78px;
 background: #ffffff;
 border-radius: 3px;
 margin: -32px 0px 0px -20px;
 padding: -32px 0px 0px -20px;
 box-shadow: 0 1px 4px 0 rgb(0 0 0 / 84%);
}
.h-img-b-mobile {
 width: 40px;
 height: 38px;
 background: #ffffff;
 border-radius: 3px;
 margin: -32px 0px 0px -20px;
 padding: -32px 0px 0px -20px;
 box-shadow: 0 1px 4px 0 rgb(0 0 0 / 84%);
}

</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
