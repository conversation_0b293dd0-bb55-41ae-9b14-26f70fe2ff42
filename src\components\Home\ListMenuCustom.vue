<template>
  <v-container :class="IpadProSize || IpadSize ? 'pt-0 py-2' : MobileSize ? 'pa-0' : 'pa-0'" style="display: flex; justify-content: center;">
    <v-card v-if="isLoadIcon === false" style="max-width: 1400px !important;" :width="IpadProSize || IpadSize ? '100%' : '92%'" height="100%" elevation="0" color="transparent" :class="IpadProSize || IpadSize ? 'pt-0' : MobileSize ? 'pa-0' : ''">
      <v-card-text :class="IpadProSize || IpadSize ? 'pt-0' : MobileSize ? 'px-2 py-0' : ''">
        <v-row v-if="!MobileSize" dense justify="center" class="d-flex justify-space-between" :class="IpadProSize || IpadSize ? 'pt-0' : ''">
          <v-col md="auto" sm="3" align="center" v-for="(item, index) in iconRow1" :key="'row1-' + index">
            <v-card width="100%" height="100%" elevation="0" color="transparent" :style="item.href === null ? 'cursor: default;' : 'cursor: pointer;'" @click="item.href ? linkToPage(item.href) : ''" >
              <v-card-text class="px-0">
                <div :style="`background-color: ${item.bg_color};`" style="width: 79px; height: 80px; border-radius: 32px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 12px;">
                  <v-img :src="item.path" max-height="54" max-width="54" width="54" height="54" contain></v-img>
                </div>
                <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ lang === 'th' ? item.name : lang === 'en' && (item.name_eng === null || item.name_eng === '' || item.name_eng === '-') ? item.name : item.name_eng }}</span>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <v-row v-if="!MobileSize" dense justify="center" class="d-flex justify-space-between" :class="IpadProSize || IpadSize ? 'pt-0' : ''">
          <v-col md="auto" sm="3" align="center" v-for="(item, index) in iconRow2" :key="'row2-' + index">
            <v-card width="100%" height="100%" elevation="0" color="transparent" :style="item.href === null ? 'cursor: default;' : 'cursor: pointer;'" @click="item.href ? linkToPage(item.href) : ''" >
              <v-card-text class="px-0">
                <div :style="`background-color: ${item.bg_color};`" style="width: 79px; height: 80px; border-radius: 32px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 12px;">
                  <v-img :src="item.path" max-height="54" max-width="54" width="54" height="54" contain></v-img>
                </div>
                <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ lang === 'th' ? item.name : lang === 'en' && (item.name_eng === null || item.name_eng === '' || item.name_eng === '-') ? item.name : item.name_eng }}</span>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <v-row v-if="MobileSize" dense justify="center" class="d-flex justify-space-between" :class="IpadProSize || IpadSize ? 'pt-0' : ''">
          <v-col md="auto" sm="3" align="center" v-for="(item, index) in iconRow1" :key="'row1-' + index">
            <v-card width="100%" height="100%" elevation="0" color="transparent" :style="item.href === null ? 'cursor: default;' : 'cursor: pointer;'" @click="item.href ? linkToPage(item.href) : ''" >
              <v-card-text class="px-0" style="margin-bottom: -10px;">
                <div :style="`background-color: ${item.bg_color};`" style="width: 36px; height: 36px; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 12px;">
                  <v-img :src="item.path" max-height="24" max-width="24" width="24" height="24" contain></v-img>
                </div>
                <span style="font-size: 12px; font-weight: 400; color: #333333; line-height: 1;">{{ lang === 'th' ? item.name : lang === 'en' && (item.name_eng === null || item.name_eng === '' || item.name_eng === '-') ? item.name : item.name_eng }}</span>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        <v-row v-if="MobileSize" dense justify="center" class="d-flex justify-space-between" :class="IpadProSize || IpadSize ? 'pt-0' : ''">
          <v-col md="auto" sm="3" align="center" v-for="(item, index) in iconRow2" :key="'row2-' + index">
            <v-card width="100%" height="100%" elevation="0" color="transparent" :style="item.href === null ? 'cursor: default;' : 'cursor: pointer;'" @click="item.href ? linkToPage(item.href) : ''" >
              <v-card-text class="px-0" style="margin: -5px 0;">
                <div :style="`background-color: ${item.bg_color};`" style="width: 36px; height: 36px; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 12px;">
                  <v-img :src="item.path" max-height="24" max-width="24" width="24" height="24" contain></v-img>
                </div>
                <span style="font-size: 12px; font-weight: 400; color: #333333; line-height: 1;">{{ lang === 'th' ? item.name : lang === 'en' && (item.name_eng === null || item.name_eng === '' || item.name_eng === '-') ? item.name : item.name_eng }}</span>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
          <!-- <v-row dense justify="center" class="d-flex justify-space-between" :class="IpadProSize || IpadSize ? 'pt-0' : ''" v-if="iconRow2.length">
            <v-col md="auto" sm="3" align="center" v-for="(item, index) in iconRow2" :key="'row2-' + index">
              <v-card width="100%" height="100%" elevation="0" color="transparent" :style="item.href === null ? 'cursor: default;' : 'cursor: pointer;'" @click="item.href ? linkToPage(item.href) : ''" >
                <v-card-text class="px-0">
                  <div :style="`background-color: ${item.bg_color};`" style="width: 79px; height: 80px; border-radius: 32px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 12px;">
                    <v-img :src="item.path" max-height="54" max-width="54" width="54" height="54" contain></v-img>
                  </div>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ lang === 'th' ? item.name : lang === 'en' && (item.name_eng === null || item.name_eng === '' || item.name_eng === '-') ? item.name : item.name_eng }}</span>
                </v-card-text>
              </v-card>
            </v-col>
        </v-row> -->
      </v-card-text>
    </v-card>
    <div v-if="isLoadIcon === true" style="width: 100%; max-width: 1400px; display: flex; justify-content: space-between;">
      <div v-for="item in 8" :key="item"  style="width: 80px; height: 80px;">
        <v-skeleton-loader type="image" height="80"></v-skeleton-loader>
      </div>
    </div>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      items: [],
      itemsIcon: [
        {
          name: 'คูปองส่วนลด',
          path: require('@/assets/ImageINET-Marketplace/menu/discount.png'),
          href: 'https://nexgencommerce.one.th/DetailServiceCoupon',
          index_in_type: 0,
          bg_color: '#f7f7f7'
        },
        {
          name: 'Flash Sale',
          path: require('@/assets/ImageINET-Marketplace/menu/flash_sale.png'),
          href: 'https://nexgencommerce.one.th/ListProduct/flash_sale?page=1',
          index_in_type: 1,
          bg_color: '#f7f7f7'
        },
        {
          name: 'สินค้า OTOP',
          path: require('@/assets/ImageINET-Marketplace/menu/otop.png'),
          href: 'https://nexgencommerce.one.th/GroupShoppage/OTOP-3',
          index_in_type: 2,
          bg_color: '#f7f7f7'
        },
        {
          name: 'สินค้าแม่และเด็ก',
          path: require('@/assets/ImageINET-Marketplace/menu/icon_แม่และเด็ก.png'),
          href: 'https://nexgencommerce.one.th/GroupShoppage/แม่และเด็ก-6',
          index_in_type: 3,
          bg_color: '#f7f7f7'
        },
        {
          name: 'ร้านค้าทั้งหมด',
          path: require('@/assets/ImageINET-Marketplace/menu/shop_1.png'),
          href: 'https://nexgencommerce.one.th/allShop?page=1',
          index_in_type: 4,
          bg_color: '#f7f7f7'
        },
        {
          name: 'ซื้ออีกครั้ง',
          path: require('@/assets/ImageINET-Marketplace/menu/cart.png'),
          href: 'https://nexgencommerce.one.th/pobuyerProfileRecord',
          index_in_type: 5,
          bg_color: '#f7f7f7'
        },
        {
          name: 'สินค้าขายดี',
          path: require('@/assets/ImageINET-Marketplace/menu/crown.png'),
          href: 'https://nexgencommerce.one.th/ListProduct/best_seller_landing?page=1',
          index_in_type: 6,
          bg_color: '#f7f7f7'
        },
        {
          name: 'หมวดหมู่',
          path: require('@/assets/ImageINET-Marketplace/menu/category.png'),
          href: 'https://nexgencommerce.one.th/AllProductCategory?page=1',
          type: 'custom_icon',
          index_in_type: 7,
          bg_color: '#f7f7f7'
        }
      ],
      isLoadIcon: false,
      lang: 'th'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    MacBookSize () {
      const { lg } = this.$vuetify.breakpoint
      return !!lg
    },
    iconsPerRow () {
      if (this.MobileSize) return 4
      if (this.IpadSize) return 4
      if (this.IpadProSize) return 8
      if (this.MacBookSize) return 8
      return 8
    },
    iconRow1 () {
      // return this.items.slice(0, this.iconsPerRow)
      if (this.items.length !== 0) {
        return this.items.slice(0, this.iconsPerRow)
      } else {
        return this.items
      }
    },
    iconRow2 () {
      // return this.items.slice(this.iconsPerRow, this.iconsPerRow * 2)
      if (this.items.length !== 0) {
        return this.items.slice(this.iconsPerRow, this.iconsPerRow * 2)
      } else {
        return this.items
      }
    }
    // iconRow2 () {
    //   return this.items.length > this.iconsPerRow
    //     ? this.items.slice(this.iconsPerRow)
    //     : []
    // }
  },
  async created () {
    this.isLoadIcon = true
    await this.getDataIcon()
    this.lang = localStorage.getItem('lang')
    // this.calculateNumIcon()
  },
  methods: {
    async linkToPage (link) {
      if (link !== null) {
        const lowerLink = link.toLowerCase()
        if (lowerLink.includes('pobuyerprofilerecord') || lowerLink.includes('pobuyerprofilerecordmobile')) {
          if (localStorage.getItem('oneData') === null) {
            await this.$swal.fire({
              icon: 'warning',
              text: 'กรุณาเข้าสู่ระบบ',
              showConfirmButton: false,
              timer: 1500
            })
            if (this.MobileSize) {
              sessionStorage.setItem('pathRedirect', '/pobuyerProfileRecordMobile')
            } else {
              sessionStorage.setItem('pathRedirect', '/pobuyerProfileRecord')
            }
            this.$router.push({ path: '/Login' }).catch(() => {})
          } else {
            window.open(link, '_self')
          }
        } else {
          window.open(link, '_self')
        }
      }
    },
    async getDataIcon () {
      const data = null
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsgetDataIcon', data)
      const response = this.$store.state.ModuleAdminManage.stategetDataIcon
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        if (response.data.custom_icon.length !== 0) {
          this.items = response.data.custom_icon
        } else {
          this.items = []
        }
        // console.log('this.items', this.items)
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
      this.$EventBus.$emit('changeReadyCarousel')
      this.isLoadIcon = false
    }
  }
}
</script>

<style>

</style>
