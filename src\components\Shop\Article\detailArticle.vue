<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <!-- <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายละเอียดบทความ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายละเอียดบทความ</v-card-title> -->

            <v-row class="pa-6">
        <v-col class="pa-0">
          <div class="pb-6">
            <v-card elevation="0" style="background-color: #FAFAFA;">
              <div class="pa-4">
                <v-row class="pb-4">
                  <v-col cols="9" class="pb-0">
                    <span style="font-size: 18px; font-weight: 400; color: #333333;">ชื่อกลุ่มบทความ : <span style="color: #2A70C3; font-weight: 700;">{{ groupCate.name }}</span></span>
                  </v-col>
                  <v-col align="end" class="pb-0">
                    <v-btn @click="editGroup(groupCate.id)" style="width: 32px; height: 32px; border: 1px solid #85BEEF;" elevation="0" icon><v-icon style="font-size: 18px; color: #85BEEF;">mdi-pencil</v-icon></v-btn>
                    <!-- <v-btn class="ml-2" @click="detailGroup(item.id)" style="width: 32px; height: 32px; border: 1px solid #85BEEF;" elevation="0" icon><v-icon style="font-size: 18px; color: #85BEEF;">mdi-eye</v-icon></v-btn> -->
                      <v-btn class="ml-2" @click="confirmDelete(groupCate.id)" style="width: 32px; height: 32px; border: 1px solid #85BEEF;" elevation="0" icon><v-icon style="font-size: 18px; color: #85BEEF;">mdi-delete-outline</v-icon></v-btn>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col class="pt-0 pb-6">
                    <span style="font-size: 18px; font-weight: 400; color: #333333;">จำนวนบทความ :<span class="pl-2" style="font-weight: 700; color: #2A70C3;">{{ groupCate.count_news }} บทความ </span></span>
                    <span class="pl-9" style="font-size: 18px; font-weight: 400; color: #333333;">สร้างเมื่อ :<span class="pl-2" style="font-weight: 700; color: #2A70C3;">{{ dateTai(groupCate.created_at) }} </span></span>
                    <span class="pl-9" style="font-size: 18px; font-weight: 400; color: #333333;">แก้ไขล่าสุดเมื่อ :
                      <span  class="pl-2" style="font-weight: 700; color: #2A70C3;">{{ dateCal(groupCate.updated_at) }} </span>
                    </span>
                  </v-col>
                </v-row>
            </div>
            </v-card>
          </div>
          <div>
                  <div class="pb-6">
                    <v-row class="pb-6">
                      <v-col cols="4">
                        <v-text-field v-model="textSearch1" @input="shertName" style="font-size: 14px; background-color: white;" placeholder="ค้นหาชื่อบทความ" outlined dense hide-details ></v-text-field>
                      </v-col>
                    </v-row>
                    <v-row v-if="paginated.length === 0" justify="center" align="center" class="pb-6">
                        <v-card elevation="0">
                            <v-img style="height: 238px; width: 372px; margin-bottom: 42px;" src=""></v-img>
                            <div style="text-align: center;">
                                <span style="font-size: 20px; font-weight: 400; color: #C4C4C4;">คุณยังไม่มีบทความที่ต้องการแสดง</span><br>
                            </div>
                        </v-card>
                    </v-row>
                    <v-row v-else class="px-1 pb-6">
                      <v-col v-for="(item, index) in paginated" :key="index" cols="4" style="filter: drop-shadow(0px 2px 4px rgba(96, 97, 112, 0.16));">
                        <v-card style="height: 180px !important; border-top-right-radius: 20px; border-top-left-radius: 20px; border-bottom-left-radius: 0%; border-bottom-right-radius: 0%; box-shadow: none;">
                          <v-img v-if="item.header_image && item.header_type === 'image'" :src="item.header_image" style="height: 180px !important;" height="180px" contain>
                          </v-img>
                          <v-card v-else-if="item.header_image && item.header_type === 'video'" elevation="0">
                            <youtube v-if="item.header_image.includes('/www.youtube.com/') || item.header_image.includes('youtu')" :video-id="getID(item.header_image)" style="width: 100%; height: 180px; "></youtube>
                            <!-- <iframe style="border-top-left-radius: 20px;border-top-right-radius: 20px;" v-if="item.header_image.includes('/www.youtube.com/') || item.header_image.includes('youtu')" sandbox="allow-same-origin allow-scripts allow-popups allow-forms" width="100%" height="180px"  :src="`http://www.youtube.com/embed/${youtube_parser(item.header_image)}`" frameborder="0" allowfullscreen></iframe> -->
                              <iframe v-else-if="item.header_image.includes('facebook')" :src="`https://www.facebook.com/plugins/post.php?href=${item.header_image}`" style="border:none; overflow:hidden; width: 100%; height: 180px; opacity:100%; border-top-left-radius: 20px;border-top-right-radius: 20px;" controls contain allow="web-share"></iframe>
                              <video v-else style="width: 100%; height: 180px; border-top-left-radius: 20px;border-top-right-radius: 20px;" controls contain>
                                <source :src="item.header_image.includes('/drive.google.com/')  ? ` https://drive.google.com/uc?export=download&id=${getIdFromUrlDrive(item.header_image)}`: item.header_image" id="video_here">
                              </video>
                          </v-card>
                          <v-img v-else style="height: 180px !important;" src=""></v-img>
                        </v-card>
                        <v-card elevation="0">
                          <v-col>
                            <v-row class="px-4 pt-4 mb-1" style=" font-size: 16px; font-weight: 700; line-height: 24px;  color: #2A70C3; height: 52px;">
                              <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; ">
                                {{ item.news_header }}
                              </span>
                            </v-row>
                            <v-row class="px-4 mt-0" style="font-size: 14px; line-height: 24px; color: #636363; height: 42px;">
                              <span style="display: -webkit-box; max-width: 500px; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; text-overflow: ellipsis;">
                                {{ item.short_description }}
                              </span>
                            </v-row>
                            <v-row class="px-4 mt-3" style="font-size: 12px; line-height: 24px; font-weight: 500; color: #C4C4C4;">
                              <v-col class="px-0" cols="6">
                                <span>
                                  <v-icon class="pr-1" style="font-size: 18px; color: #C4C4C4;">mdi-eye</v-icon>
                                  View {{ item.count }}
                                </span>
                              </v-col>
                              <v-col class="px-0" cols="6" align="end">
                                <span >
                                  {{ dateCal(item.updated_at) }}
                                </span>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-card>
                        </v-col>
                    </v-row>
                    <v-pagination v-if="paginated.length !== 0" color="#2A70C3" v-model="pageNumber" :length="pageMax" :total-visible="10" @change="pageChange()"></v-pagination>
                  </div>
                  <div class="pb-6">
                    <v-row justify="end" class="pr-3">
                        <v-btn class="px-7" @click="bacKto" color="#2A70C3" dark outlined rounded  style="width: 146px;">
                          <span>ย้อนกลับ</span>
                        </v-btn>
                    </v-row>
                  </div>
              <v-dialog v-model="dialogConfirm" width="400" persistent>
                <v-card style="background: #FFFFFF; border-radius: 4px;" height="100%">
                  <v-toolbar align="center" color="#DBECFA" dark dense elevation="0">
                    <span class="flex text-center" style="font-weight: 700; font-size: 16px; line-height: 24px; color: #2A70C3;">เพิ่มกลุ่มบทความ</span>
                    <v-btn icon dark @click="dialogConfirm = false">
                      <v-icon color="#2A70C3">mdi-close</v-icon>
                    </v-btn>
                  </v-toolbar>
                  <v-card-text>
                    <v-row justify="center" style="text-align: center" class="mb-8 mt-8">
                      <v-col cols="12" class="pb-0"><span style="font-weight: 500; font-size: 16px; color: #333333; line-height: 24px;">คุณได้เพิ่มกลุ่มบทความใหม่</span></v-col>
                      <v-col cols="12" class="py-0"><span style="font-weight: 500; font-size: 16px; color: #333333; line-height: 24px;">ต้องการดำเนินการต่อใช่ หรือไม่</span></v-col>
                    </v-row>
                    <v-row no-gutters justify="center" class="mt-6">
                      <v-btn outlined  dense rounded dark color="#2A70C3" class="mr-4 pl-8 pr-8" @click="dialogConfirm = false">ยกเลิก</v-btn>
                      <v-btn color="#2A70C3" dense rounded class="pl-9 pr-9" style="color:#FFFFFF;" @click="createdToCate()">ตกลง</v-btn>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-dialog>
              <v-dialog v-model="dialogSuccess" width="400" persistent>
                <v-card style="background: #FFFFFF; border-radius: 4px;" height="100%">
                  <v-toolbar align="center" color="#DBECFA" dark dense elevation="0">
                    <span class="flex text-center" style="font-weight: 700; font-size: 16px; line-height: 24px; color: #2A70C3;">เพิ่มกลุ่มบทความ</span>
                    <v-btn icon dark @click="dialogSuccess = false">
                      <v-icon color="#2A70C3">mdi-close</v-icon>
                    </v-btn>
                  </v-toolbar>
                  <v-card-text>
                    <v-row class="pt-10">
                      <v-col cols="12" align="center" align-self="center">
                        <v-img src="" loading="lazy" max-width="139px"  max-height="164px" contain></v-img>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="12" align="center" align-self="center">
                        <span class="py-0" style="font-weight: 500; font-size: 18px; color: #2A70C3; line-height: 26px;">เสร็จสิ้น</span>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-dialog>
          </div>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="dialogConfirmDelete" width="464" persistent>
      <v-card style="background: #FFFFFF; border-radius: 12px;" height="100%">
        <v-toolbar align="center" color="#DBECFA" dark dense elevation="0">
          <span class="flex text-center" style="font-weight: 700; font-size: 18px; line-height: 24px; color: #2A70C3;">
            ลบกลุ่มบทความ
          </span>
          <v-btn icon dark @click="dialogConfirmDelete = false">
            <v-icon color="#2A70C3">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense justify="center">
            <v-col cols="12" align="center" class="mt-8 mb-8">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="">คุณได้ทำการลบกลุ่มบทความ</span><br/>
              <span style="font-weight: 600; font-size: 16px; line-height: 24px;" class="pt-4">คุณต้องการที่จะลบกลุ่มบทความ ใช่ หรือ ไม่ ?</span>
            </v-col>
          </v-row>
        </v-card-text>

        <v-card-actions>
          <v-row class="mb-2" justify="center" dense>
            <v-btn outlined height="48" width="110" dense rounded dark color="#2A70C3" class="mr-4" @click="dialogConfirmDelete = false">ยกเลิก</v-btn>
            <v-btn height="48" width="110" color="#2A70C3" dark dense rounded class="" @click="deleteGroup(delId)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  name: 'YourComponent',
  data () {
    return {
      dialogConfirm: false,
      dialogSuccess: false,
      textSearch1: '',
      textSearch2: '',
      groupNewsId: '',
      pageMax: null,
      dragging: -1,
      current: 1,
      pageSize: 9,
      rule01: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล']
      },
      delId: '',
      groupCate: [],
      dataNewsPrime: [],
      dataNewsDelta: [],
      dataNewsDelta2: [],
      dataNews3: [],
      dialogConfirmDelete: false

    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      var pageSize = 9
      return (this.current - 1) * pageSize
    },
    indexEnd () {
      var pageSize = 9
      return this.indexStart + pageSize
    },
    paginated () {
      if (this.textSearch1 === '') {
        return this.dataNewsDelta.slice(this.indexStart, this.indexEnd)
      } else {
        return this.dataNewsDelta.filter(e => {
          return e.news_header.toLowerCase().includes(this.textSearch1.toLowerCase())
        })
      }
    }
  },
  methods: {
    confirmDelete (id) {
      this.delId = id
      this.dialogConfirmDelete = true
    },
    dateCal (date) {
      var date1 = new Date(date)
      var date2 = new Date()
      var DiffInTime = date2.getTime() - date1.getTime()
      console.log('dd', date1, date2, DiffInTime)
      var DiffInDays = DiffInTime / (1000 * 3600 * 24)
      var DiffMin = DiffInDays < 1 ? (date2.getTime() - date1.getTime()) / (1000 * 60) : 0
      var DiffHours = DiffMin > 70 ? DiffMin / 60 : 0
      return (DiffInDays > 1 ? `${Math.floor(DiffInDays)} วันที่ผ่านมา` : DiffMin > 70 ? `${Math.floor(DiffHours)} ชั่วโมงที่ผ่านมา` : `${Math.floor(DiffMin)} นาทีที่ผ่านมา`)
    },
    editGroup (id) {
      // alert(id)
      this.$router.push({ path: `/editCategoryNews/${id}` }).catch(() => {})
    },
    bacKto () {
      this.$router.push({ path: '/manageCategoryNews' }).catch(() => {})
    },
    shertName () {
      this.pageMax = (this.paginated.length % this.pageSize) === 0 ? Math.floor((this.paginated.length / this.pageSize)) : Math.floor((this.paginated.length / this.pageSize)) + 1
    },
    dateTai (date) {
      if (date) {
        console.log(typeof date)
        var date2 = (new Date(date.substring(0, 10)))
        const result = date2.toLocaleDateString('th-TH', {
          year: 'numeric',
          month: 'long',
          day: '2-digit'
        })
        return result
      }
    },
    youtube_parser (url) {
      console.log('URL', url)
      if (url.length !== 0 || url !== undefined) {
        let ID = ''
        url = url !== -1 ? url.replace(/(>|<)/gi, '').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/) : ''
        if (url[2] !== undefined || url !== '') {
          ID = url[2].split(/[^0-9a-z_-]/i)
          ID = ID[0]
        } else {
          ID = false
        }
        return ID
      }
    },
    getID (link) {
      return this.$youtube.getIdFromUrl(link)
    },
    getIdFromUrlDrive (url) {
      return url.match(/[-\w]{25,}/)
    }
  }
}
</script>

<style scoped>
.background_color {
background-color: #FFFFFF;
}
.v-text-field--outlined >>> fieldset {
/* padding-left: 16px; */
border-color: #E6E6E6;
}
.vlt-wrapper {
border-top-left-radius: 24px !important;
border-top-right-radius: 24px !important;
}
</style>

<style scoped>
.v-text-field.v-text-field--enclosed:not(.v-text-field--rounded) > .v-input__control > .v-input__slot {
padding-left: 16px !important;
}
.active {
font-weight: 700 !important;
}
.v-slide-group__content {
height: 32px;
}
.vlt-preview::before{
z-index: 0;
}
.vlt-preview .ly-text{
z-index: 0;
}
.lazyY .vlt-preview img{
border-top-left-radius: 20px !important;
border-top-right-radius: 20px !important;
}
.lazyY .vlt-preview {
border-top-left-radius: 20px !important;
border-top-right-radius: 20px !important;
}
</style>
