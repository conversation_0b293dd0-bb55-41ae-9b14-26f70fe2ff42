// import ColorThief from 'color-thief'
import analyze from 'rgbaster'
export const ColorConverter = {
  async textColor (img) {
    const result = await analyze(img, { scale: 0.6 })
    // await console.log(`The dominant color is ${result[0].color} with ${result[0].count} occurrence(s)`) 186 or 128
    var arr = await result[0].color.split(',')
    const r = await parseInt(arr[0].substr(4)) * 255
    const g = await parseInt(arr[1]) * 255
    const b = await parseInt(arr[2].split(')')[0]) * 255
    const yiq = await (r * 299 + g * 587 + b * 114) / 1000
    return (yiq >= 128) ? 'black' : 'white'
  }
}
