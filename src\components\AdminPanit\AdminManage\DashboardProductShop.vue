<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
    <div v-if="rowShop">
      <v-row v-if="!MobileSize">
        <v-col cols="8">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;">ร้านค้าที่มีสินค้าในระบบ</v-card-title>
        </v-col>
        <!-- <v-col cols="4" style="display: flex; justify-content: end; align-items: center;">
          <v-btn color="#38b2a4" class="mr-4" rounded @click="exportExcel()" :disabled="selectExportShopId.lenght === 0">
            <span class="white--text">EXPORT</span>
          </v-btn>
        </v-col> -->
      </v-row>
      <v-row v-else>
        <v-col cols="12" class="d-flex align-center">
            <v-card-title style="font-weight: 700;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> ร้านค้าที่มีสินค้าในระบบ</v-card-title>
            <v-spacer></v-spacer>
            <!-- <v-btn color="#38b2a4" rounded @click="exportExcel()" :disabled="selectExportShopId.lenght === 0">
              <span class="white--text">EXPORT</span>
            </v-btn> -->
        </v-col>
        <!-- <v-col cols="12" style="display: flex; justify-content: center; align-items: center; margin-top: -20px;">
            <v-btn color="#38b2a4" width="90%" rounded @click="exportExcel()">
            <span class="white--text">EXPORT</span>
            </v-btn>
        </v-col> -->
      </v-row>
    </div>
    <div v-if="rowProduct">
      <v-row v-if="!MobileSize">
        <v-col cols="8">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPageShop()">mdi-chevron-left</v-icon> สินค้าภายในร้านค้า</v-card-title>
        </v-col>
    </v-row>
    <!-- <v-row v-else>
    <v-col cols="12">
        <v-card-title style="font-weight: 700;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPageShop()">mdi-chevron-left</v-icon> สินค้าภายในร้านค้า</v-card-title>
    </v-col>
    </v-row> -->
    </div>
    <!-- ค้นหา -->
    <v-row dense v-if="rowShop" class="ml-2" :style="MobileSize ? '' : 'margin-top: -1vw;'">
        <v-col v-if="!MobileSize && !IpadSize" cols="6" class="pl-0 pr-3 mb-4 pt-3">
        <v-text-field v-model="searchShop" placeholder="ค้นหารายการร้านค้า" outlined rounded dense hide-details>
        <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
        </v-col>
        <v-col v-else cols="12" style="display: flex;" class="mt-1">
          <div style="width: 100%; padding-right: 12px;">
            <v-text-field v-model="searchShop" placeholder="ค้นหารายการร้านค้า" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </div>
        </v-col>
    </v-row>
    <v-row dense v-if="rowProduct" class="ml-2" :style="MobileSize ? '' : 'margin-top: -1vw;'">
      <!-- <v-card-title v-if="!MobileSize" style="font-weight: 500; font-size: 16px; margin-bottom: -15px; margin-top: -2vw;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPageShop()">mdi-chevron-left</v-icon> ย้อนกลับ</v-card-title> -->
      <v-col v-if="!MobileSize && !IpadSize" cols="6" class="pl-0 pr-3 mb-4 pt-3">
        <v-text-field v-model="searchProduct" placeholder="ค้นหารายการสินค้า" outlined rounded dense hide-details>
        <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col v-else cols="12" style="display: flex;" class="ml-2" :style="MobileSize ? 'margin-top: -6vw;' : ''">
        <div style="width: 90%;">
          <v-text-field v-model="searchProduct" placeholder="ค้นหารายการสินค้า" outlined rounded dense hide-details>
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
        </div>
      </v-col>
    </v-row>
    <v-row v-if="rowShop" style="display: inherit;">
      <v-col v-if="!MobileSize && !IpadSize" style="display: flex; align-items: center; margin-top: -1vw; margin-left: .5vw;">
        <v-card style="background-color: #2bc08f; width: 300px; display: flex; justify-content: center; align-items: center; border-radius: 20px;">
          <v-card-title style="font-size: 16px; margin: -10px 0; gap: 10px; color: white;" :class="IpadProSize ? 'px-0' : ''">ร้านค้าทั้งหมดที่มีสินค้าในระบบ</v-card-title>
          <v-btn style="background-color: white !important; font-weight: bolder; display: flex; align-items: center;" disabled rounded small><span style="font-size: 14px; color: black;">{{ shopCount }}</span></v-btn>
        </v-card>
        <v-spacer></v-spacer>
        <v-btn class="mr-1" color="#38b2a4" rounded @click="clearSelectedCheckBox()" >
          <span class="white--text">ล้างค่าการเลือก</span>
        </v-btn>
        <v-btn class="mr-1" color="#38b2a4" rounded @click="exportExcel()" :disabled="selected.length === 0">
          <span class="white--text">EXPORT(ที่เลือก)</span>
        </v-btn>
        <v-btn color="#38b2a4" rounded @click="exportExcelAll()">
          <span class="white--text">EXPORT(ทั้งหมด)</span>
        </v-btn>
      </v-col>
      <div elevation="0" class="px-3" v-else>
        <v-col class="ml-0" style="display: flex; align-items: center;">
          <v-card style="background-color: #2bc08f; width: 100%; display: flex; justify-content: center; align-items: center; border-radius: 20px;">
            <v-card-title style="font-size: 14px; margin: -10px 0; gap: 10px; color: white;">ร้านค้าทั้งหมดที่มีสินค้าในระบบ</v-card-title>
            <v-btn style="background-color: white !important; font-weight: bolder; display: flex; align-items: center;" disabled rounded small><span style="font-size: 12px; color: black;">{{ shopCount }}</span></v-btn>
          </v-card>
        </v-col>
        <v-col cols="12">
          <v-btn class="mr-5" color="#38b2a4" block rounded @click="clearSelectedCheckBox()" >
            <span class="white--text">ล้างค่าการเลือก</span>
          </v-btn>
        </v-col>
        <v-col cols="12">
          <v-btn class="mr-5" color="#38b2a4" block rounded @click="exportExcel()" :disabled="selected.length === 0">
            <span class="white--text">EXPORT(ที่เลือก)</span>
          </v-btn>
        </v-col>
        <v-col cols="12">
          <v-btn color="#38b2a4" block rounded @click="exportExcelAll()">
            <span class="white--text">EXPORT(ทั้งหมด)</span>
          </v-btn>
        </v-col>
      </div>
    </v-row>
    <v-row v-if="rowProduct">
        <v-col v-if="!MobileSize && !IpadSize" style="display: flex; align-items: center; margin-top: -1vw; margin-left: .5vw;">
    <v-card style="background-color: #2bc08f; width: 300px; display: flex; justify-content: center; align-items: center; border-radius: 20px;">
        <v-card-title style="font-size: 16px; margin: -10px 0; gap: 10px; color: white;">สินค้าทั้งหมดในร้านค้า</v-card-title>
    <v-btn style="background-color: white !important; font-weight: bolder; display: flex; align-items: center;" disabled rounded small><span style="font-size: 14px; color: black;">{{ productCount }}</span></v-btn>
    </v-card>
    </v-col>
    <v-col cols="12" class="ml-2" v-else style="display: flex; align-items: center;">
    <v-card style="background-color: #2bc08f; width: 280px; display: flex; justify-content: center; align-items: center; border-radius: 20px;">
        <v-card-title style="font-size: 14px; margin: -10px 0; gap: 10px; color: white;">สินค้าทั้งหมดในร้านค้า</v-card-title>
    <v-btn style="background-color: white !important; font-weight: bolder; display: flex; align-items: center;" disabled rounded small><span style="font-size: 12px; color: black;">{{ productCount }}</span></v-btn>
    </v-card>
    </v-col>
    </v-row>
    <!-- ตาราง Shop -->
    <v-row dense v-if="rowShop" class="ml-2 mr-2">
        <v-col cols="12" class="mb-2">
            <v-data-table
            v-model="selected"
            :headers="headerShop"
            :items="showData"
            style="width:100%; text-align: center;"
            height="100%"
            :page.sync="page"
            no-results-text="ไม่พบรายการร้านค้า"
            no-data-text="ไม่พบรายการร้านค้า"
            :server-items-length="maxPagesShop"
            :update:items-per-page="itemsPerPage"
            class="elevation-1 mt-4 shop-table"
            :footer-props="{ 'items-per-page-options': [5, 10, 15, 50, 100], 'items-per-page-text': 'จำนวนแถว' }"
            :options.sync="optionShop"
            :items-per-page="optionShop.itemsPerPage"
            @update:options="updateOptionsShop"
            show-select
            item-key="shopId"
            :hide-default-footer="searchShop !== ''"
            >
            <template v-slot:[`header.data-table-select`]="{ on , props }">
              <v-simple-checkbox
                v-model="checkAll"
                v-bind="props"
                v-on="on"
              ></v-simple-checkbox>
            </template>
            <template v-slot:[`item.shopName`]="{ item }">
                <span v-if="item.shopName" style="white-space: nowrap;">{{ item.shopName }}</span>
                <span v-else>-</span>
            </template>
            <template v-slot:[`item.taxId`]="{ item }">
                <span v-if="item.taxId" style="white-space: nowrap;">{{ item.taxId }}</span>
                <span v-else>-</span>
            </template>
            <template v-slot:[`item.address`]="{ item }">
                <div v-if="MobileSize">
                  <span v-if="item.address" style="white-space: nowrap;">
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-bind="attrs" v-on="on"> {{ substring(item.address) }}</span>
                      </template>
                      <span>{{ item.address }}</span>
                    </v-tooltip>
                  </span>
                  <span v-else>-</span>
                </div>
                <span v-else>
                  <span v-if="item.address" style="white-space: nowrap;">
                    {{item.address}}
                  </span>
                  <span v-else>-</span>
                </span>
            </template>
            <template v-slot:[`item.shopStatus`]="{ item }">
                <v-chip v-if="item.shopStatus === 'active'" :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
                <v-chip v-else-if="item.shopStatus === 'inactive'" :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f5224a">ปิดการใช้งาน</v-chip>
                <span v-else>-</span>
            </template>
            <template v-slot:[`item.details`]="{ item }">
            <v-btn v-if="item" color="primary" text small @click="detailProductShop(item.productCount, item.shopId)">
                <span style="font-size: 14px !important;">สินค้าภายในร้านค้า</span>
            </v-btn>
            </template>
            </v-data-table>
        </v-col>
    </v-row>
    <!-- ตาราง Product -->
    <v-row dense v-if="rowProduct"  class="ml-2 mr-2">
    <v-col cols="12" class="mb-2">
        <v-data-table
        :headers="headerProduct"
        :search="searchProduct"
        :items="this.showDetail"
        style="width:100%; text-align: center;"
        height="100%"
        :page.sync="page"
        :server-items-length="maxPagesProduct"
        no-results-text="ไม่พบรายการสินค้า"
        no-data-text="ไม่พบรายการสินค้า"
        :update:items-per-page="itemsPerPage"
        class="elevation-1 mt-4 product-table"
        :footer-props="{ 'items-per-page-options': [5, 10, 15, 50], 'items-per-page-text': 'จำนวนแถว' }"
        :options.sync="optionProduct"
        :items-per-page="optionProduct.itemsPerPage"
        @update:options="updateOptionsProduct"
        :hide-default-footer="searchProduct !== ''"
        >
        <template v-slot:[`item.product_id_main`]="{ item }">
            <span v-if="item.product_id_main" style="white-space: nowrap;">{{ item.product_id_main }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.name`]="{ item }">
            <span v-if="item.name" style="white-space: nowrap;">{{ item.name }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.sku`]="{ item }">
            <span v-if="item.sku" style="white-space: nowrap;">{{ item.sku }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.new_sku`]="{ item }">
            <span v-if="item.new_sku" style="white-space: nowrap;">{{ item.new_sku }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.attribute_1_key`]="{ item }">
            <span v-if="item.attribute_1_key" style="white-space: nowrap;">{{ item.attribute_1_key }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.attribute_2_key`]="{ item }">
            <span v-if="item.attribute_2_key" style="white-space: nowrap;">{{ item.attribute_2_key }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.attribute_priority_1`]="{ item }">
            <span v-if="item.attribute_priority_1" style="white-space: nowrap;">{{ item.attribute_priority_1 }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.attribute_priority_2`]="{ item }">
            <span v-if="item.attribute_priority_2" style="white-space: nowrap;">{{ item.attribute_priority_2 }}</span>
            <span v-else>-</span>
        </template>
        <template v-slot:[`item.status`]="{ item }">
            <v-chip v-if="item.status === 'active'" :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">กำลังใช้งาน</v-chip>
            <v-chip v-else-if="item.status === 'inactive'" :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#f7c5ad" text-color="#f5224a">ปิดการใช้งาน</v-chip>
            <span v-else>-</span>
        </template>
        </v-data-table>
    </v-col>
    </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      searchShop: '',
      searchProduct: '',
      shopCount: '',
      productCount: '',
      rowShop: true,
      rowProduct: false,
      showData: [],
      showDetail: [],
      checkAll: false,
      selected: [],
      disabledCount: 0,
      optionShop: {
        page: 1,
        itemsPerPage: 10
      },
      optionProduct: {
        page: 1,
        itemsPerPage: 10
      },
      maxPagesShop: null,
      maxPagesProduct: null,
      shopID: '',
      headerShop: [
        { text: 'Shop ID', value: 'shopId', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'shopName', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขประจำตัวผู้เสียภาษี', value: 'taxId', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ที่อยู่', value: 'address', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะร้านค้า', value: 'shopStatus', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สินค้า', value: 'details', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', fixed: true, right: true }
      ],
      headerProduct: [
        { text: 'Product ID', value: 'product_id_main', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รหัส SKU', value: 'sku', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รหัส SKU ย่อย', value: 'new_sku', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะสินค้า', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ลักษณะตัวเลือก 1', value: 'attribute_1_key', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ลักษณะตัวเลือก 2', value: 'attribute_2_key', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ค่าตัวเลือก 1', value: 'attribute_priority_1', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ค่าตัวเลือก 2', value: 'attribute_priority_2', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      selectExportShopId: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DashboardProductShopMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'DashboardProductShop')
        this.$router.push({ path: '/DashboardProductShop' }).catch(() => {})
      }
    },
    selected (val) {
      if (val.length !== 0) {
        this.selectExportShopId = val.map(e => {
          return e.shopId
        })
      } else {
        this.selectExportShopId = []
      }
    },
    searchShop (newval) {
      this.getListShop(newval)
    },
    searchProduct (newval) {
      this.searchProduct = newval
      this.detailProductShop(this.productCount, this.shopID)
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.getListShop()
  },
  methods: {
    substring (data) {
      return data.length > 20 ? data.substring(0, 20) + '...' : data
    },
    async updateOptionsShop (options) {
      this.options = options
      await this.getListShop()
    },
    async updateOptionsProduct (options) {
      this.optionProduct = options
      await this.detailProductShop(this.productCount, this.shopID)
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    backtoPageShop () {
      this.getListShop()
      this.rowShop = true
      this.rowProduct = false
      this.optionProduct.page = 1
      this.optionShop.page = 1
      this.optionShop.itemsPerPage = 10
      this.optionProduct.itemsPerPage = 10
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async detailProductShop (productCount, shopID) {
      // console.log(shopID, 7777)
      this.$store.commit('openLoader')
      this.rowShop = false
      this.rowProduct = true
      this.productCount = productCount
      this.shopID = shopID
      this.maxPagesProduct = this.productCount
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        shopId: shopID,
        limit: this.optionProduct.itemsPerPage,
        pages: this.searchProduct !== '' ? 1 : this.optionProduct.page,
        search: this.searchProduct
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}product/detail_product`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: data
      }).then(response => {
        this.showDetail = response.data.data || []
        // this.shopCount = response.data.shopCount
      })
      this.$store.commit('closeLoader')
    },
    async exportExcel () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        shopId: this.selectExportShopId
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}product/export/product_detail`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob',
        data: data
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'ProductShop.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
      this.$store.commit('closeLoader')
    },
    async exportExcelAll () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}order/export`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'ProductShop.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
      this.$store.commit('closeLoader')
    },
    async getListShop (valSearch) {
      this.$store.commit('openLoader')
      var data = {
        limit: this.optionShop.itemsPerPage,
        pages: this.searchShop !== '' ? 1 : this.optionShop.page,
        search: this.searchShop
      }
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}product/list_shop`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: data
      }).then(response => {
        this.showData = response.data.data
        this.shopCount = response.data.shopCount
        this.maxPagesShop = response.data.shopCount
      })
      this.$store.commit('closeLoader')
    },
    async clearSelectedCheckBox () {
      this.selected = []
      this.selectExportShopId = []
      this.checkAll = false
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .shop-table table {
    thead {
      tr th:nth-child(1) {
        background: #E6F5F3 !important;
        border-style: none !important;
      }
    }
    tbody {
      tr {
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
          th {
            white-space: nowrap;
          }
          th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
          }
      }
    }
    thead {
      tr {
          th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          }
      }
    }
  }
::v-deep .product-table table {
  thead {
  tr {
      th {
        white-space: nowrap;
      }
  }
  }
}

</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
font-size: 0.62rem;
}
</style>
