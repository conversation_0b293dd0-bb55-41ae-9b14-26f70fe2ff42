<template>
    <div>
        <v-dialog v-model="modalShowPosition" width="590px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card class="rounded-lg">
            <v-toolbar dark dense elevation="0" color="#BDE7D9">
            <v-row>
                <v-col class="d-flex justify-space-around" v-if="!MobileSize">
                <v-toolbar-title><span style="color: #27AB9C;"><b>รายละเอียดข้อมูลผู้ใช้งาน</b></span>
                </v-toolbar-title>
                </v-col>
                <v-col class="d-flex justify-space-around" v-else>
                <v-toolbar-title><span style="color: #27AB9C; font-size: 16px;"><b>รายละเอียดข้อมูลผู้ใช้งาน</b></span></v-toolbar-title>
                </v-col>
            </v-row>
            </v-toolbar>
            <v-container grid-list-xs>
            <v-card-text>
                <v-row style="border: 1px solid #E6E6E6;" v-if="!MobileSize">
                <v-col cols="12" md="3" sm="3">
                    <v-img v-if="sendData.img_path !== ''" :lazy-src="sendData.img_path" max-height="150" max-width="250" :src="sendData.img_path"></v-img>
                </v-col>
                <v-col cols="12" md="8" sm="8">
                    <v-row no-gutters class="mt-3 pl-4">
                    <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 14px;">
                        ชื่อ-สกุล :
                        </p>
                    </v-col>
                    <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 14px; color: #333333;">
                        {{ sendData.name }}
                        </p>
                    </v-col>
                    <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                        อีเมล :
                        </p>
                    </v-col>
                    <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                        {{ sendData.email }}
                        </p>
                    </v-col>
                    <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                        เบอร์โทรศัพท์ :
                        </p>
                    </v-col>
                    <v-col cols="8">
                        <p style="font-weight: 700; font-size: 14px; line-height: 8px; color: #333333;">
                        {{ sendData.phone }}
                        </p>
                    </v-col>
                    <v-col cols="12" v-for="(position, index) in sendData.positions" :key="index">
                        <v-row>
                        <v-col cols="4">
                            <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                            ตำแหน่ง :
                            </p>
                        </v-col>
                        <v-col cols="8" class="pt-2">
                            <p
                            style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333; cursor: pointer; margin: 0;"
                            >
                            {{ position.position_name }}
                            </p>
                        </v-col>
                        </v-row>
                    </v-col>
                    </v-row>
                </v-col>
                </v-row>
                <v-row style="border: 1px solid #E6E6E6;" v-else>
                <v-col cols="3" md="3">
                    <v-img v-if="sendData.img_path !== ''" :lazy-src="sendData.img_path" max-height="88" max-width="100" :src="sendData.img_path"></v-img>
                </v-col>
                <v-col cols="9" class="pl-0 pt-0">
                    <v-row no-gutters class="mt-3">
                    <v-col cols="4">
                        <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                        ชื่อ-สกุล :
                        </p>
                    </v-col>
                    <v-col cols="8" class="pl-0">
                        <v-row dense no-gutters>
                        <v-col cols="10">
                            <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                            {{ sendData.name }}
                            </p>
                        </v-col>
                        </v-row>
                    </v-col>
                    <v-col cols="3">
                        <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                        อีเมล :
                        </p>
                    </v-col>
                    <v-col cols="9">
                        <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                        {{ sendData.email }}
                        </p>
                    </v-col>
                    <v-col cols="6" class="pr-0">
                        <p style="font-weight: 400; font-size: 14px; line-height: 16px;">
                        เบอร์โทรศัพท์ :
                        </p>
                    </v-col>
                    <v-col cols="6" class="pl-0">
                        <p style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333;">
                        {{ sendData.phone }}
                        </p>
                    </v-col>
                    <v-col cols="12" v-for="(position, index) in sendData.positions" :key="index">
                        <v-row>
                        <v-col cols="4">
                            <p style="font-weight: 400; font-size: 14px; line-height: 8px;">
                            ตำแหน่ง :
                            </p>
                        </v-col>
                        <v-col cols="8" class="pt-2">
                            <p
                            style="font-weight: 700; font-size: 14px; line-height: 16px; color: #333333; cursor: pointer; margin: 0;"
                            >
                            {{ position.position_name }}
                            </p>
                        </v-col>
                        </v-row>
                    </v-col>
                    </v-row>
                </v-col>
                </v-row>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn class="px-5" dark color="#27AB9C" @click="modalShowPosition = !modalShowPosition">ย้อนกลับ</v-btn>
            </v-card-actions>
            </v-container>
        </v-card>
        </v-dialog>
    </div>
</template>

<script>
export default {
  data () {
    return {
      lazy: false,
      modalShowPosition: false,
      sendData: [],
      name: '',
      values: [],
      positionsOld: []
    }
  },
  watch: {
    sendData (newValue) {
      this.values = newValue.positions || []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    getData (data, taxID) {
      this.sendData = data
      this.positionsOld = data.positions
      this.userID = data.user_id
      this.taxID = taxID
      this.modalShowPosition = !this.modalShowPosition
    }
  }
}
</script>

<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
