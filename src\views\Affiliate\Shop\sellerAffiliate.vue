<template>
  <v-container grid-list-xs>
    <v-row dense>
      <v-col cols="12">
        <AffiliateConsent :title="title" :detail="detail" v-if="isSeller === '0'"/>
        <ManageProduct v-if="isSeller === '1'"/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  components: {
    AffiliateConsent: () => import('@/components/Shop/Affiliate/AffiliateConsent'),
    ManageProduct: () => import('@/components/Shop/Affiliate/ManageProduct')
  },
  data () {
    return {
      checkLogin: false,
      isSeller: '',
      title: '',
      detail: '',
      shopSellerID: ''
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    // this.$tours['myTour.ShopPage'].start()
    this.$EventBus.$on('GetConsent', this.getConsent)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('GetConsent')
    })
  },
  created () {
    this.$EventBus.$emit('getPath')
    this.$EventBus.$emit('changeNav')
    this.shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
    if (localStorage.getItem('oneData') !== null) {
      this.checkLogin = true
      this.getConsent()
    } else {
      this.checkLogin = false
    }
  },
  methods: {
    async getConsent () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopSellerID
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      this.title = res.data.title
      this.detail = res.data.consent_text
      this.isSeller = res.isSeller
      this.$store.commit('closeLoader')
    }
  }
}
</script>
