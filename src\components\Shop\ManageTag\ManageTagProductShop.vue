<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">จัดการแท็กสินค้า</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToSellerMenu()">mdi-chevron-left</v-icon> จัดการแท็กสินค้า
      </v-card-title>

      <v-col cols="12">
        <v-row>
          <v-col :cols="MobileSize ? 12 : 4">
            <v-col cols="12">
              <span style="font-size: 18px;"><b>หมวดหมู่แท็กสินค้า</b></span>
            </v-col>
            <v-col cols="12" v-if="this.listShopTag.length === 0">
              <v-card
                elevation="0"
                style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
              >
                <v-col cols="12" class="pa-2">
                  <v-list dense nav>
                    <v-list-item>
                      <v-list-item-content>
                        <v-list-item-title class="d-flex justify-center align-center">ไม่มีหมวดหมู่แท็กสินค้า</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                </v-col>
              </v-card>
            </v-col>
            <v-col cols="12" v-else>
              <v-card
                elevation="0"
                style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
              >
                <v-col cols="12" class="pa-2">
                  <v-list dense nav>
                    <v-list-item v-if="selectProductTag.length > 0" @click="clearFilter()">
                      <v-list-item-content style="text-align: end;">
                        <v-list-item-title style="cursor: pointer; color: #27AB9C; font-weight: bold;">
                          ล้างตัวกรองแท็ก
                        </v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                    <v-list-group
                      v-for="category in listShopTag"
                      v-model="openCategories[category.tag_category_id]"
                      :key="category.tag_category_id"
                      no-action
                    >
                    <template v-slot:activator>
                      <v-list-item-content>
                        <v-list-item-title class="d-flex align-center justify-space-between">
                          <div>
                            <v-icon color="#27AB9C">mdi-circle-small</v-icon>
                            {{ category.tag_category_name }}
                          </div>
                          <v-chip
                            small
                            :style="{
                              backgroundColor: category.tag_category_status === 'active' ? '#F0F9EE' : '#FBE5E4',
                              color: category.tag_category_status === 'active' ? '#1AB759' : '#F5222D'
                            }"
                          >
                            {{ category.tag_category_status === 'active' ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }}
                          </v-chip>
                        </v-list-item-title>
                      </v-list-item-content>
                    </template>

                      <v-list-item
                        v-for="tag in category.tag_list"
                        :key="tag.tag_id"
                        @click="toggleTag(tag)"
                        :style="{
                          cursor: 'pointer',
                          backgroundColor: selectProductTag === tag.tag_name ? '#E0F7F4' : '',
                          color: selectProductTag === tag.tag_name ? '#27AB9C' : ''
                        }"
                      >
                        <v-list-item-content>
                          <v-list-item-title :style="{ fontWeight: selectProductTag === tag.tag_name ? 'bold' : 'normal' }">
                            {{ tag.tag_name }}
                          </v-list-item-title>
                        </v-list-item-content>
                      </v-list-item>
                    </v-list-group>
                  </v-list>
                </v-col>
              </v-card>
            </v-col>
          </v-col>
          <v-col :cols="MobileSize ? 12 : 8">
            <v-col cols="12">
              <span style="font-size: 18px;"><b>รายการสินค้า</b></span>
            </v-col>
            <v-col cols="12">
              <v-row style="align-items: center;">
                <v-col :cols="MobileSize ? 12 : 6">
                  <v-text-field v-model="search" @keyup="searchData(search)" placeholder="ค้นหาชื่อสินค้าหรือรหัสสินค้า" outlined rounded dense hide-details style="border-radius: 8px;">
                    <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                  </v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 4 : 2">
                  <span style="font-size: 16px; line-height: 24px; color: #333333; font-weight: 400;">
                    สถานะสินค้า :
                  </span>
                </v-col>
                <v-col :cols="MobileSize ? 8 : 4">
                  <v-select
                    class="setCustomSelect"
                    v-model="stateListProductStatus"
                    :items="['ทั้งหมด','เปิดใช้งาน', 'ปิดใช้งาน']"
                    placeholder="ทั้งหมด"
                    @change="selectListProductStatus()"
                    append-icon="mdi-chevron-down"
                    dense
                    outlined
                    hide-details
                    style="border-radius: 8px; font-size: 14px;"
                  />
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12">
              <v-row>
                <v-col :cols="MobileSize ? 12 : 6" style="display: flex; align-items: center;">
                  <span style="font-size: 16px;">รายการสินค้าทั้งหมด {{ this.productTotal }} รายการ</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : 6" style="text-align: end;">
                  <v-btn rounded color="#27AB9C" style="color: white; font-size: 16px;" @click="productTagSetting()">เพิ่มแท็กสินค้า</v-btn>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" v-if="this.filteredProducts.length === 0">
              <v-card
                elevation="0"
                style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
              >
              <v-container>
                  <v-col cols="12">
                    <div class="d-flex flex-column align-center justify-center">
                      <v-avatar size="200">
                        <v-img src="@/assets/NoProducts.png" width="300" height="300" contain></v-img>
                      </v-avatar>
                      <span style="font-size: 18px;">ไม่มีรายการสินค้า</span>
                    </div>
                  </v-col>
                </v-container>
              </v-card>
            </v-col>
            <v-col cols="12" v-else>
              <v-row>
                <v-col cols="12" sm="6" md="4" lg="4" v-for="product in filteredProducts" :key="product.product_id">
                  <v-card
                    elevation="0"
                    height="100%"
                    style="display: flex; flex-direction: column; cursor: pointer; border-radius: 5px; filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.04)) drop-shadow(0px 2px 4px rgba(96, 97, 112, 0.16));"
                  >
                    <v-btn
                      icon
                      small
                      color="#27AB9C"
                      style="position: absolute; top: 10px; right: 10px; z-index: 1;"
                      @click="editTagProduct(product)"
                      v-if="product.tag_category_list.some(category =>
                        category.tag_list.some(tag => tag.is_selected === 'yes')
                      )"
                    >
                      <v-icon>mdi-pencil</v-icon>
                    </v-btn>

                    <div style="display: flex; justify-content: center; align-items: center; padding: 10px;">
                      <v-img
                        v-if="product.product_image && product.product_image !== '-' && product.product_image !== null"
                        :src="product.product_image"
                        height="100px"
                        width="100px"
                        class="mr-2"
                        contain
                      ></v-img>
                      <img
                        v-else
                        src="@/assets/NoImage.png"
                        style="max-width: 100px; max-height: 100px;"
                        class="mr-2"
                      />
                    </div>

                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-card-title
                          v-bind="attrs"
                          v-on="on"
                          style="
                            font-size: 16px;
                            color: #333333;
                            white-space: nowrap;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            width: 100%;
                            display: block;
                          "
                        >
                         <span><b>{{ product.product_name }}</b></span>
                        </v-card-title>
                      </template>
                      <span>{{ product.product_name }}</span>
                    </v-tooltip>

                    <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-card-subtitle
                          v-bind="attrs"
                          v-on="on"
                          style="
                              font-size: 14px;
                              color: #333333;
                              white-space: nowrap;
                              overflow: hidden;
                              text-overflow: ellipsis;
                              width: 100%;
                              display: block;
                              padding-top: 0px;
                            "
                        >
                          รหัสสินค้า : {{ product.product_sku }}
                        </v-card-subtitle>
                      </template>
                      <span>รหัสสินค้า : {{ product.product_sku }}</span>
                    </v-tooltip>

                    <v-card-text>
                      <div
                        style="display: flex; overflow-x: auto; gap: 8px; white-space: nowrap; padding-bottom: 4px;"
                      >
                        <v-chip
                          v-for="tag in product.tag_category_list.flatMap(c => c.tag_list).filter(t => t.is_selected === 'yes')"
                          :key="tag.tag_id"
                          small
                          color="#27AB9C"
                          style="color: white; flex-shrink: 0;"
                        >
                          {{ tag.tag_name }}
                        </v-chip>
                      </div>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
            <v-col>
              <v-row no-gutters justify="center" class="py-6" v-if="pageMax > 0">
                <v-pagination
                  color="#27AB9C"
                  v-model="pageNumber"
                  :length="pageMax"
                  :total-visible="8">
                </v-pagination>
              </v-row>
            </v-col>
          </v-col>
        </v-row>
      </v-col>
    </v-card>

    <v-dialog v-model="dialogTagSetting" persistent max-width="750">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogTagSetting = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>เพิ่มแท็กสินค้า</b></span>
        </v-card-title>
        <br>

        <v-card-text>
          <v-col cols="12">
            <span style="font-size: 16px;"><b>เลือกแท็กสินค้า</b></span>
          </v-col>
          <v-col cols="12">
            <v-row>
              <v-col cols="6">
                <v-select
                  v-model="selectedCategoryId"
                  :items="listShopTag"
                  item-value="tag_category_id"
                  item-text="tag_category_name"
                  label="เลือกหมวดหมู่แท็ก"
                  outlined
                  hide-details
                  dense
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="6">
                <v-select
                  v-if="selectedCategory"
                  v-model="selectedTagIds"
                  :items="selectedCategory.tag_list"
                  item-value="tag_id"
                  item-text="tag_name"
                  label="เลือกแท็ก"
                  hide-details
                  multiple
                  outlined
                  dense
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-chip-group column style="max-height: 100px; overflow-y: auto; display: flex; flex-wrap: wrap;">
              <v-chip
                  v-for="(tag, index) in selectedTagObjects"
                  :key="tag.tag_id"
                  close
                  @click:close="removeAddTag(index)"
                >
                {{ tag.tag_name }}
              </v-chip>
            </v-chip-group>
          </v-col>
          <v-col cols="12">
            <span style="font-size: 16px;"><b>เลือกสินค้าที่ต้องการ</b></span>
          </v-col>
          <v-col cols="12">
            <v-text-field v-model="searchAddTag" @keyup="searchDataAddTag(searchAddTag)" placeholder="ค้นหาชื่อสินค้าหรือรหัสสินค้า" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" v-if="this.listProductTagDialog.length === 0">
            <v-card
              elevation="0"
              style="background-color: #f9f9f9; max-height: 500px; overflow-y: auto;"
            >
            <v-container>
                <v-col cols="12">
                  <div class="d-flex flex-column align-center justify-center">
                    <v-avatar size="200">
                      <v-img src="@/assets/NoProducts.png" width="300" height="300" contain></v-img>
                    </v-avatar>
                    <span style="font-size: 18px;">ไม่มีรายการสินค้า</span>
                  </div>
                </v-col>
              </v-container>
            </v-card>
          </v-col>
          <v-col cols="12" class="pa-0" v-else>
            <v-row no-gutters>
              <v-col
                v-for="product in listProductTagDialog"
                :key="product.product_id"
                cols="12" sm="6" md="4" lg="4"
                class="mb-4 pa-2"
              >
                <v-card>
                  <div style="display: flex; justify-content: center; align-items: center; padding: 10px;">
                    <v-img
                      v-if="product.product_image && product.product_image !== '-' && product.product_image !== null"
                      :src="product.product_image"
                      height="100px"
                      width="100px"
                      class="mr-2"
                      contain
                    ></v-img>
                    <img
                      v-else
                      src="@/assets/NoImage.png"
                      style="max-width: 100px; max-height: 100px;"
                      class="mr-2"
                    />
                  </div>

                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-card-title
                        v-bind="attrs"
                        v-on="on"
                        style="
                          font-size: 16px;
                          color: #333333;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          width: 100%;
                          display: block;
                        "
                        >
                        <b>{{ product.product_name }}</b>
                      </v-card-title>
                    </template>
                    <span>{{ product.product_name }}</span>
                  </v-tooltip>

                  <v-tooltip bottom>
                    <template v-slot:activator="{ on, attrs }">
                      <v-card-subtitle
                        v-bind="attrs"
                        v-on="on"
                        style="
                          font-size: 14px;
                          color: #333333;
                          white-space: nowrap;
                          overflow: hidden;
                          text-overflow: ellipsis;
                          width: 100%;
                          display: block;
                          padding-top: 0px;
                        "
                      >
                        รหัสสินค้า : {{ product.product_sku }}
                      </v-card-subtitle>
                    </template>
                    <span>รหัสสินค้า : {{ product.product_sku }}</span>
                  </v-tooltip>

                  <v-card-actions>
                    <v-checkbox
                      :value="product.product_id"
                      v-model="selectedProductIds"
                      hide-details
                      outline
                      label="เลือก"
                      class="pt-0 mr-0"
                    />
                  </v-card-actions>

                </v-card>
              </v-col>
            </v-row>
          </v-col>
        </v-card-text>

        <v-col cols="12">
          <v-row no-gutters justify="center" v-if="pageMaxDialog > 0">
            <v-pagination
              color="#27AB9C"
              v-model="pageNumberDialog"
              :length="pageMaxDialog"
              :total-visible="9">
            </v-pagination>
          </v-row>
        </v-col>

        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 150px; font-size: 16px;" @click="dialogTagSetting = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 150px; color: white; font-size: 16px;" :disabled="disabledSubmit" @click="submitTags()">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="showTagEditDialog" persistent max-width="500px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="showTagEditDialog = false" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center backgroundHead" style="border-radius: 35px 35px 0 0; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>แก้ไขแท็กสินค้า</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-col cols="12">
            <span style="font-size: 16px;"><b>เลือกแท็กสินค้า</b></span>
          </v-col>
          <v-col cols="12">
            <v-row>
              <v-col cols="12">
                <v-select
                  v-model="selectedCategoryIdEdit"
                  :items="listShopTag"
                  item-value="tag_category_id"
                  item-text="tag_category_name"
                  label="เลือกหมวดหมู่แท็ก"
                  outlined
                  hide-details
                  dense
                  clearable
                ></v-select>
              </v-col>
              <v-col cols="12">
                <v-select
                  v-if="selectedCategoryEdit"
                  v-model="selectedTagIdsEdit"
                  :items="selectedCategoryEdit.tag_list"
                  item-value="tag_id"
                  item-text="tag_name"
                  label="เลือกแท็ก"
                  hide-details
                  multiple
                  outlined
                  dense
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-chip-group column style="max-height: 100px; overflow-y: auto; display: flex; flex-wrap: wrap;">
              <v-chip
                v-for="(tag, index) in selectedTagObjectsEdit"
                :key="tag.tag_id"
                close
                @click:close="removeAddTagEdit(index)"
              >
                {{ tag.tag_name }}
              </v-chip>
            </v-chip-group>
          </v-col>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 150px; font-size: 16px;" @click="showTagEditDialog = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 150px; color: white; font-size: 16px;" @click="saveTagChanges()">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      selectedTags: [],
      openCategories: {},
      listShopTag: [],
      listProductTag: [],
      listProductTagDialog: [],
      search: '',
      searchAddTag: '',
      current: 1,
      itemsPerPage: 9,
      currentDialog: 1,
      pageMax: 0,
      pageMaxDialog: 0,
      productTotal: 0,
      dialogTagSetting: false,
      selectedTagIds: [],
      selectedProductIds: [],
      selectedCategoryId: null,
      selectedTagIdsEdit: [],
      selectedCategoryIdEdit: null,
      selectedProduct: null,
      showTagEditDialog: false,
      stateListProductStatus: '',
      selectProductTag: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
        this.ListProductTag(newPage, this.search)
      }
    },
    pageNumberDialog: {
      get () {
        return this.currentDialog || 1
      },
      set (newPage) {
        this.currentDialog = newPage
        window.scrollTo(0, 0)
        this.ListProductTagDialog(newPage, this.search)
      }
    },
    filteredProducts () {
      return this.listProductTag.filter(product => {
        const matchSearch =
          this.search === '' ||
          product.product_name.toLowerCase().includes(this.search.toLowerCase()) ||
          product.product_sku.toLowerCase().includes(this.search.toLowerCase())

        const tagSearch =
          this.selectProductTag === '' ||
          product.tag_category_list.some(category =>
            category.tag_list.some(tag => tag.tag_name === this.selectProductTag)
          )

        const matchTags = this.selectedTags.length === 0 || this.selectedTags.every(tagId =>
          product.tag_category_list.some(category =>
            category.tag_list.some(tag => tag.tag_id === tagId && tag.is_selected === 'yes')
          )
        )
        return matchSearch && matchTags && tagSearch
      })
    },
    selectedCategory () {
      return this.listShopTag.find(cat => cat.tag_category_id === this.selectedCategoryId)
    },
    selectedTagObjects () {
      let allTags = []
      this.listShopTag.forEach(category => {
        allTags = allTags.concat(category.tag_list)
      })
      return allTags.filter(tag => this.selectedTagIds.includes(tag.tag_id))
    },
    disabledSubmit () {
      return this.selectedTagIds.length === 0 || this.selectedProductIds.length === 0
    },
    selectedCategoryEdit () {
      return this.listShopTag.find(cat => cat.tag_category_id === this.selectedCategoryIdEdit)
    },
    selectedTagObjectsEdit () {
      let allTags = []
      this.listShopTag.forEach(category => {
        allTags = allTags.concat(category.tag_list)
      })
      return allTags.filter(tag => this.selectedTagIdsEdit.includes(tag.tag_id))
    },
    disabledSubmitEdit () {
      return this.selectedTagIdsEdit.length === 0
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageTagProductShopMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ManageTagProductShop' }).catch(() => { })
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
    await this.ListShopTag()
    await this.ListProductTag(this.pageNumber, this.search)
  },
  methods: {
    backToSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    toggleTag (tag) {
      if (this.selectProductTag === tag.tag_name) {
        console.log('1')
        this.selectProductTag = ''
      } else {
        console.log('2')
        this.selectProductTag = tag.tag_name
      }
      this.ListProductTag(this.pageNumber, this.search)
    },
    clearFilter () {
      this.selectProductTag = ''

      this.listShopTag.forEach(category => {
        this.openCategories[category.tag_category_id] = false
      })
      this.ListProductTag(this.pageNumber, this.search)
    },
    async ListShopTag () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('actionListShopTag', data)
      var responseData = await this.$store.state.ModuleShop.stateListShopTag
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listShopTag = responseData.data
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    searchData (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.ListProductTag(1, val)
      }, 500)
    },
    selectListProductStatus () {
      if (this.stateListProductStatus === 'ทั้งหมด' || this.stateListProductStatus === '') {
        this.listProductStatus = 'all'
      } else if (this.stateListProductStatus === 'เปิดใช้งาน') {
        this.listProductStatus = 'active'
      } else if (this.stateListProductStatus === 'ปิดใช้งาน') {
        this.listProductStatus = 'inactive'
      }
      this.ListProductTag(this.pageNumber, this.search)
    },
    async ListProductTag (page = 1, textSearch = '') {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        page: page,
        per_page: this.itemsPerPage,
        filter_product: textSearch,
        filter_product_status: this.listProductStatus === 'all' ? null : this.listProductStatus,
        filter_tagging: this.selectProductTag === '' ? null : this.selectProductTag
      }
      await this.$store.dispatch('actionListProductTag', data)
      var responseData = await this.$store.state.ModuleShop.stateListProductTag
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listProductTag = responseData.data.product_list
        this.pageMax = responseData.data.pagination.total_page
        this.productTotal = responseData.data.pagination.total
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    searchDataAddTag (val) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.ListProductTagDialog(1, val)
      }, 500)
    },
    async ListProductTagDialog (page = 1, textSearch = '') {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        page: page,
        per_page: this.itemsPerPage,
        filter_product: textSearch
      }
      await this.$store.dispatch('actionSelectListProductTag', data)
      var responseData = await this.$store.state.ModuleShop.stateSelectListProductTag
      if (responseData.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.listProductTagDialog = responseData.data.product_list
        this.pageMaxDialog = responseData.data.pagination.total_page
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    removeAddTag (index) {
      const tagIdToRemove = this.selectedTagObjects[index].tag_id
      this.selectedTagIds = this.selectedTagIds.filter(id => id !== tagIdToRemove)
    },
    async productTagSetting () {
      this.dialogTagSetting = true
      this.pageNumberDialog = 1
      this.selectedTagIds = []
      this.selectedProductIds = []
      this.selectedCategoryId = null
      this.listProductTagDialog = []
      await this.ListProductTagDialog(this.pageNumberDialog, this.search)
    },
    async submitTags () {
      var data = {
        seller_shop_id: this.shopID,
        product_id: this.selectedProductIds,
        seller_shop_tag_ids: this.selectedTagIds
      }
      await this.$store.dispatch('actionCreateProductTag', data)
      var responseData = await this.$store.state.ModuleShop.stateCreateProductTag
      if (responseData.result === 'SUCCESS') {
        this.$swal.fire({
          icon: 'success',
          text: 'เพิ่มแท็กสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.dialogTagSetting = false
        this.ListProductTag(this.pageNumber, this.search)
      } else {
        this.$store.commit('closeLoader')
        this.dialogTagSetting = false
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    },
    removeAddTagEdit (index) {
      const tagIdToRemoveEdit = this.selectedTagObjectsEdit[index].tag_id
      this.selectedTagIdsEdit = this.selectedTagIdsEdit.filter(id => id !== tagIdToRemoveEdit)
    },
    editTagProduct (product) {
      this.selectedProduct = product

      // ดึง tag_id ของแท็กที่ is_selected === 'yes' จากทุกหมวด
      const selectedTagIds = []

      product.tag_category_list.forEach(category => {
        category.tag_list.forEach(tag => {
          if (tag.is_selected === 'yes') {
            selectedTagIds.push(tag.tag_id)
          }
        })
      })

      this.selectedTagIdsEdit = selectedTagIds

      // ดึง category แรกที่มีแท็กที่ถูกเลือกมาใช้เป็น selectedCategoryIdEdit
      const firstSelectedTagId = selectedTagIds[0]
      const foundCategory = this.listShopTag.find(category =>
        category.tag_list.some(tag => tag.tag_id === firstSelectedTagId)
      )
      this.selectedCategoryIdEdit = foundCategory.tag_category_id || null

      this.showTagEditDialog = true
    },
    async saveTagChanges () {
      var data = {
        seller_shop_id: this.shopID,
        product_id: this.selectedProduct.product_id,
        seller_shop_tag_ids: this.selectedTagIdsEdit
      }
      await this.$store.dispatch('actionUpdateProductTag', data)
      var responseData = await this.$store.state.ModuleShop.stateUpdateProductTag
      if (responseData.result === 'SUCCESS') {
        this.$swal.fire({
          icon: 'success',
          text: 'แก้ไขแท็กสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
        this.showTagEditDialog = false
        this.ListProductTag(this.pageNumber, this.search)
      } else {
        this.$store.commit('closeLoader')
        this.showTagEditDialog = false
        this.$swal.fire({
          icon: 'error',
          text: responseData.message,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2000
        })
      }
    }
  }
}
</script>

<style scoped>

::v-deep(.v-list-item__action:last-of-type),
::v-deep(.v-list-item__avatar:last-of-type),
::v-deep(.v-list-item__icon:last-of-type) {
  margin-left: 0 !important;
}

</style>
