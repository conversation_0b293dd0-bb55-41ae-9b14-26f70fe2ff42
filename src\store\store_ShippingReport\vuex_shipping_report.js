import AxiosShop from './axios_shipping_report'

const ModuleShippingReport = {
  state: {
    stateSummaryOrderByShopID: [],
    stateAllOrderByShopID: [],
    stateCallRider: []
  },
  mutations: {
    mutationsSummaryOrderByShopID (state, data) {
      state.stateSummaryOrderByShopID = data
    },
    mutationsAllOrderByShopID (state, data) {
      state.stateAllOrderByShopID = data
    },
    mutationsCallRider (state, data) {
      state.stateCallRider = data
    }
  },
  actions: {
    async actionSummaryOrderByShopID (context, access) {
      const responseData = await AxiosShop.SummaryOrderByShopID(access)
      await context.commit('mutationsSummaryOrderByShopID', responseData)
    },
    async actionAllOrderByShopID (context, access) {
      const responseData = await AxiosShop.AllOrderByShopID(access)
      await context.commit('mutationsAllOrderByShopID', responseData)
    },
    async actionCallRider (context, access) {
      const responseData = await AxiosShop.CallRider(access)
      await context.commit('mutationsCallRider', responseData)
    }
  }
}
export default ModuleShippingReport
