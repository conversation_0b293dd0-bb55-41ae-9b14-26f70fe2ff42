<template>
  <div
    :style="colorCard === 'blue' ? 'filter: drop-shadow(0rem .6rem 0rem #0073AA);' : colorCard === 'green' ? 'filter: drop-shadow(0rem .6rem 0rem #27AB9C);' : 'filter: drop-shadow(0rem .6rem 0rem #5C54A3);'">
    <v-hover v-slot="{ hover }" class="circle rounded-sm" open-delay="200">
      <v-card width="340" :color="colorMain" :elevation="hover ? 24 : 10" :class="{ 'on-hover': hover }">
        <v-card height="154" :class="MobileSize ? 'ml-4  mr-4 rounded-sm' : 'ml-6  mr-6 rounded-sm'">
          <v-row no-gutters>
            <v-col cols="3" md="3">
              <v-container bg fill-height grid-list-md text-xs-center>
                <v-layout row wrap align-center>
                  <v-avatar v-if="items.image === '' || items.image === null" class="border-scroll rounded-lg" tile
                    :size="IpadProSize ? 50 : MobileSize ? 40 : 60" color="#27AB9C">
                    <v-img contain src="https://devinet-eprocurement.one.th/static/img/panit_logo.png">
                    </v-img>
                  </v-avatar>
                  <v-avatar v-else class="border-scroll rounded-lg" tile :size="IpadProSize ? 50 : MobileSize ? 40 : 60">
                    <v-img contain :src="items.image">
                    </v-img>
                  </v-avatar>
                </v-layout>
              </v-container>
            </v-col>
            <v-col cols="9" md="9" :style="{'background-color': colorMainIn}" style="height: 154px">
              <v-list-item three-line :class="MobileSize ? 'px-1' : ''">
                <v-list-item-content>
                  <v-list-item-subtitle class="d-flex justify-center" :class="IpadSize ? 'px-1' : ''">
                    <v-card class="rounded-lg shadow" :color="colorheardercoupon">
                      <v-row no-gutters :class="MobileSize ? 'pa-1' : 'pa-2'">
                        <v-col cols="1">
                          <v-icon class="mr-1" :size="MobileSize ? 14 : ''" color="#FFFFFF">mdi-tag-multiple</v-icon>
                        </v-col>
                        <v-col cols="10" align="center">
                          <span v-if="items.shop_name === '' || items.shop_name === null" class="ml-2"
                            style="color: #FFFFFF; font-weight: 600;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'">
                            คูปองส่วนลด ร้านค้าพาณิชย์
                          </span>
                          <span v-else class="ml-2" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'" style="color: #FFFFFF; font-weight: 600;">
                            คูปองส่วนลด {{ items.shop_name|truncate(35, '...') }}
                          </span>
                        </v-col>
                        <v-col cols="1">
                          <v-img width="19px" height="19px" contain :src="require('@/assets/icons/wow.png')" />
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-list-item-subtitle>
                  <v-list-item-subtitle class="ml-2 mt-2 ">
                    <span style="color: #333333; font-size: 12px; font-weight: 600;">
                      {{ items.name|truncate(27, '...') }}
                    </span>
                  </v-list-item-subtitle>
                  <v-list-item-subtitle class=" ml-2">
                    <span style="color: #636363; font-size: 10px; font-weight: 400;">
                      {{ items.description|truncate(27, '...') }}
                    </span>
                  </v-list-item-subtitle>
                  <v-list-item-subtitle v-if="IpadProSize">
                    <v-icon x-small :color="colortext">mdi-clock-outline</v-icon>
                    <span v-if="items.couponDate.useEndDate === null" style="font-size: 8px; font-weight: 400;"
                      :style="{'color': colortext}">
                      ระยะเวลาใช้คูปอง {{ new
                      Date(items.couponDate.useStartDate).toLocaleDateString('th-TH',
                      { timeZone: 'UTC',
                      year:
                      'numeric', month:'numeric', day: 'numeric' })}} - ไม่มีกำหนด
                    </span>
                    <span v-else style="font-size: 8px; font-weight: 400;" :style="{'color': colortext}">
                      ระยะเวลาใช้คูปอง {{ new
                      Date(items.couponDate.useStartDate).toLocaleDateString('th-TH',
                      { timeZone: 'UTC',
                      year:
                      'numeric', month:'numeric', day: 'numeric' })}} - {{ new
                      Date(items.couponDate.useEndDate).toLocaleDateString('th-TH', { timeZone: 'UTC', year:
                      'numeric', month:'numeric', day: 'numeric' })}}
                    </span>
                  </v-list-item-subtitle>
                  <v-list-item-subtitle v-else>
                    <v-icon x-small :color="colortext">mdi-clock-outline</v-icon>
                    <span v-if="items.couponDate.useEndDate === null" style="font-size: 9px; font-weight: 400;"
                      :style="{'color': colortext}">
                      ระยะเวลาใช้คูปอง {{ new
                      Date(items.couponDate.useStartDate).toLocaleDateString('th-TH',
                      { timeZone: 'UTC',
                      year:
                      'numeric', month:'numeric', day: 'numeric' })}} - ไม่มีกำหนด
                    </span>
                    <span v-else style="font-size: 9px; font-weight: 400;" :style="{'color': colortext}">
                      ระยะเวลาใช้คูปอง {{ new
                      Date(items.couponDate.useStartDate).toLocaleDateString('th-TH',
                      { timeZone: 'UTC',
                      year:
                      'numeric', month:'numeric', day: 'numeric' })}} - {{ new
                      Date(items.couponDate.useEndDate).toLocaleDateString('th-TH', { timeZone: 'UTC', year:
                      'numeric', month:'numeric', day: 'numeric' })}}
                    </span>
                  </v-list-item-subtitle>
                  <v-list-item-subtitle v-if="keep" class="d-flex justify-center">
                    <v-btn v-if="items.status === 'not_collected' " @click="keepCoupons(items)" height="24"
                      class="white--text" dense :color="colorBtn">
                      เก็บคูปอง
                    </v-btn>
                    <v-btn v-else dense height="24" outlined disabled color="#27AB9C">
                      เก็บแล้ว
                    </v-btn>
                  </v-list-item-subtitle>
                </v-list-item-content>
              </v-list-item>
            </v-col>
          </v-row>
        </v-card>
      </v-card>
    </v-hover>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  props: ['items', 'keep', 'colorCard'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      pageMax: 1,
      pageNumber: 1,
      CountCoupons: 0,
      shop_name: '',
      Points: 0,
      response: [],
      companyId: '',
      id_company: '',
      colorMain: '#73C8F0',
      colorMainIn: '#AFE6FF',
      colorshadow: '#0073AA',
      colortext: '#1B5DD6',
      colorheardercoupon: '#0091C8',
      colorBtn: '#0073AA'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  created () {
    if (localStorage.getItem('SetRowCompany') !== null) {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      this.companyId = companyId.company.company_id
    } else {
      this.companyId = -1
    }
    if (this.colorCard === 'blue') {
      this.colorMain = '#73C8F0'
      this.colorMainIn = '#AFE6FF'
      this.colortext = '#1B5DD6'
      this.colorBtn = '#0073AA'
      this.colorheardercoupon = '#0091C8'
    } else if (this.colorCard === 'green') {
      this.colorMain = '#5BC3A2'
      this.colorMainIn = '#D8EFE4'
      this.colortext = '#27AB9C'
      this.colorBtn = '#27AB9C'
      this.colorheardercoupon = '#5BC3A2'
    } else if (this.colorCard === 'purpler') {
      this.colorMain = '#A78CCC'
      this.colorMainIn = '#E3D7D9'
      this.colortext = '#5C54A3'
      this.colorBtn = '#5C54A3'
      this.colorheardercoupon = '#5C54A3'
    }
  },
  methods: {
    async keepCoupons (item) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.id_company = this.companyId
      if (this.id_company === undefined) {
        this.id_company = -1
      } else {
        this.id_company = this.companyId
      }
      var data = {
        role_user: dataRole.role,
        company_id: this.id_company,
        coupons_id: item.couponId
      }
      await this.$store.dispatch('actionskeepCouponInShop', data)
      var response = await this.$store.state.ModuleMyCouponsPoints.stateKeepCoupon
      if (response.code === 200) {
        this.$EventBus.$emit('KeepCouponPageAll')
        this.$EventBus.$emit('KeepCouponPageHome')
        this.$EventBus.$emit('AllCouponInShop')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .shadow{
   filter: drop-shadow(0rem .6rem 0rem #D2F0FF);
  }
  .circle {
    -webkit-mask-image: radial-gradient(circle at 8px, transparent 8px, blue 8.5px);
    -webkit-mask-position: -8px;
    -webkit-mask-size: 100% 28px;
  }
  .border-scroll {
    border: 1px solid #EBEBEB;
  }
</style>
