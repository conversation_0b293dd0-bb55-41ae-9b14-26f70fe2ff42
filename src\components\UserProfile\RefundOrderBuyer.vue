<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>การคืนเงิน/คืนสินค้า</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 18px; line-height: 22px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>การคืนเงิน/คืนสินค้า</v-card-title>
      <v-row style="margin: 0 1vw; 0 1vw; font-size: large;">
        <v-col cols="12" class="d-flex align-baseline" style="gap: 1vw;">
          <span>เลขที่การสั่งซื้อ :</span>
          <span style="color: #a0a0a0;">{{ orderNumber }}</span>
        </v-col>
        <v-col :cols="MobileSize ? 12 : ''">
          <span>รายการสั่งซื้อ</span>
          <v-data-table
            :headers="headers"
            :items="dataTable"
            :single-select="singleSelect"
            show-select
            v-model="selected"
          >
            <template v-slot:[`item.actionRefund`]="{ item }">
              <v-btn v-if="selected && selected.includes(item)" rounded color="#27AB9C" style="color: #fff;" small @click="goDetailPO(item)">
                <span>คืนเงิน/คืนสินค้า</span>
              </v-btn>
            </template>
          </v-data-table>
        </v-col>
        <v-col cols="6">
          <span :style="MobileSize ? 'font-size: small;' : ''">อีเมลในการติดต่อ <span style="color: red;">*</span></span>
          <v-text-field
            v-model="email"
            outlined
            placeholder="เพิ่มอีเมลในการติดต่อ"
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="6">
          <span :style="MobileSize ? 'font-size: small;' : ''">เบอร์โทรศัพท์ในการติดต่อ <span style="color: red;">*</span></span>
          <v-text-field
            v-model="phoneNumber"
            outlined
            placeholder="เพิ่มเบอร์โทรศัพท์ในการติดต่อ"
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="6" :style="MobileSize ? 'margin-top: -12vw;' : 'margin-top: -2vw;'">
          <span :style="MobileSize ? 'font-size: small;' : ''">บัญชีธนาคาร <span style="color: red;">*</span></span>
          <v-select
            :items="listAccount"
            placeholder="เพิ่มบัญชีธนาคาร"
            outlined
            item-text="name"
            item-value="code"
            dense
            v-model="selectBank"
            @change="handleChange"
          ></v-select>
        </v-col>
        <v-col cols="6" :style="MobileSize ? 'margin-top: -12vw;' : 'margin-top: -2vw;'">
          <span :style="MobileSize ? 'font-size: small;' : ''">เลขบัญชี <span style="color: red;">*</span></span>
          <v-text-field
            v-model="accountNumber"
            outlined
            placeholder="เพิ่มเลขบัญชี"
            dense
            :maxLength="checkLengthValid"
          ></v-text-field>
        </v-col>
        <v-col cols="6" :style="MobileSize ? 'margin-top: -12vw;' : 'margin-top: -2vw;'">
          <span :style="MobileSize ? 'font-size: small;' : ''">ชื่อบัญชี <span style="color: red;">*</span></span>
          <v-text-field
            v-model="accountName"
            outlined
            placeholder="เพิ่มชื่อบัญชี"
            dense
          ></v-text-field>
        </v-col>
        <v-col cols="12" class="d-flex justify-end" style="gap: 1vw;">
          <v-btn color="#27AB9C" large style="color: #fff;">ยืนยัน</v-btn>
          <v-btn color="#bfbfbf" large>ยกเลิก</v-btn>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      orderNumber: 'AHHH-***********',
      email: '',
      phoneNumber: '',
      accountBank: '',
      accountNumber: '',
      accountName: '',
      headers: [
        // { text: '', value: 'selectProduct', width: '170', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัส SKU', value: 'id', width: '170', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียดสินค้า', value: 'name', width: '170', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'pricePer', width: '120', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำวนวน', value: 'amount', width: '120', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'summary', sortable: false, width: '120', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาคืน', value: 'refund', sortable: false, width: '120', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actionRefund', sortable: false, width: '180', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
      ],
      dataTable: [
        { id: 1, name: 'John Doe', pricePer: 100, amount: 3, summary: 300, refund: 300, detail: 'details' },
        { id: 2, name: 'John Doe', pricePer: 100, amount: 3, summary: 300, refund: 300, detail: 'details' }
      ],
      singleSelect: false,
      selected: [],
      listAccount: [],
      selectBank: ''
    }
  },
  created () {
    this.getAccountBank()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkLengthValid () {
      if (this.selectBank === '030') {
        return 15
      } else if (this.selectBank === '033' || this.selectBank === '034') {
        return 12
      } else {
        return 10
      }
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/refundOrderBuyerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/refundOrderBuyer' }).catch(() => {})
      }
    }
  },
  methods: {
    handleChange () {
      this.accountNumber = ''
    },
    async getAccountBank () {
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      if (response.code === 200) {
        this.listAccount = response.data
        // this.listAccount = response.data.map((item) => ({
        //   text: item.name
        // }))
        // console.log('see', this.listAccount)
      }
    },
    backtoPage () {
      var orderNumber = localStorage.getItem('orderNumber')
      if (this.MobileSize === false) {
        this.$router.push({ path: `/pobuyerdetail?orderNumber=${orderNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/pobuyerdetailMobile?orderNumber=${orderNumber}` }).catch(() => {})
      }
    }
  }
}
</script>

<style>

</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(8) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(8) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
