<template>
  <div>
    <!-- start No Notificatin -->
    <a-popover v-if="itemNotification.length === 0" v-model="visible" placement="bottom" trigger="click">
      <template slot="title">
        {{ $t('Notification.notification') }}
      </template>
      <template slot="content">
        <a-card :bordered="false" class="cardNotification">
          <a-row type="flex" justify="center">
            <a-col :span="24">
              <a-row type="flex" justify="center">
                <a-col :span="20" style="padding: 10px 0">
                  <a-row type="flex" justify="center">
                    <v-icon size="50" color="#27AB9C">mdi-bell-ring</v-icon><br />
                  </a-row>
                </a-col>
                <h3>{{ $t('Notification.noNotification') }}</h3>
                <span>{{ $t('Notification.textNoNoti') }}</span>
              </a-row>
            </a-col>
          </a-row>
        </a-card>
      </template>
      <!-- <v-btn icon top bordered overlap>
        <v-badge class="mt-1" :content="count" :value="count" color="red" top bordered overlap>
          <v-icon  color="#FFFFFF">mdi-bell-ring</v-icon>
        </v-badge>
      </v-btn> -->
      <!-- ปาล์มมาแก้ ให้กระดิ่งโชว์ตลอดเวลา *ปรับ pagespeed -->
      <v-btn  icon top bordered overlap>
        <v-badge class="mt-1" :content="count" :value="count" transition=false color="red" top bordered overlap>
          <!-- <v-icon color="#FFFFFF">mdi-bell-ring-outline</v-icon> -->
          <v-img :src="require('@/assets/Bell.png')" contain :max-height="MobileSize ? '24px' : '32px'" :max-width="MobileSize ? '24px' : '32px'"></v-img>
        </v-badge>
      </v-btn>
    </a-popover>
    <!-- End No Notification -->

    <!-- start Notification -->
    <!-- <a-popover  v-else v-model="visibleNoti"  placement="bottom"  trigger="click" @visibleChange="UpdateNoti(itemNotification)"> -->
    <a-popover v-else v-model="visibleNoti" :placement="MobileSize ? 'bottomRight' :'bottom'" trigger="click">
      <v-btn  icon top bordered overlap>
        <v-badge class="mt-1" :content="count" :value="count" transition=false color="red" top bordered overlap>
          <!-- <v-icon color="#FFFFFF">mdi-bell-ring-outline</v-icon> -->
          <v-img :src="require('@/assets/Bell.png')" contain :max-height="MobileSize ? '24px' : '32px'" :max-width="MobileSize ? '24px' : '32px'"></v-img>
        </v-badge>
      </v-btn>
      <template slot="title">
        <!-- <a-space align="start">การแจ้งเตือนทั้งหมด</a-space>
      <a-space align="end">{{ count }}</a-space> -->
        <v-row dense>
          <v-col cols="6" class="pt-2">{{ $t('Notification.AllNotifications') }}</v-col>
          <v-col cols="6" style="text-align: right;">
            <v-chip color="#FCEAE9" small text-color="#D1392B">{{ count }}</v-chip>
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="12" class="pt-2" style="text-align: right;">
            <v-btn text small class="mr-0 pr-0" style="text-decoration: underline;" @click="CheckReadAll(itemNotification)" :disabled='itemNotification.length === 0 ? true : false'>
              {{ $t('Notification.ReadAll') }} ({{ count }})
            </v-btn>
          </v-col>
        </v-row>
      </template>
      <template class="wrapper" slot="content">
        <v-list class="listNotification" :style="MobileSize ? 'max-width: 300px; width: 300px;' : 'max-width: 300px; width: 300px;'">
          <template v-for="(item, index) in itemNotification">
            <v-subheader v-if="item.header" :key="item.header" class="pt-0"
              style="height: 30px; background-color: #F2F2F2;">
              <v-row dense>
                <v-col cols="6" class="pt-2">{{ item.header }}</v-col>
                <v-col cols="6" style="text-align: right;"><span style=" color: #27AB9C;">{{ item.header === $t('Notification.New') ?
                countCurrentDate : countAnotherDate }}</span></v-col>
              </v-row>
            </v-subheader>
            <v-list-item style="cursor: pointer;" v-else :key="item.title"
              :style="{'background-color': item.state === '0' ? '#D8EFE4' : '#FFFFFF'}" @click="UpdateNoti(item)">
              <v-list-item-avatar>
                <v-btn icon x-large outlined class="px-8 py-8 imagesNotification"
                  v-if="item.image_url === null || item.image_url === ''">
                  <v-img :src="require('@/assets/ImageINET-Marketplace/Notification/Icon/document.png')"
                    max-height="24px" max-width="24px" contain></v-img>
                </v-btn>
                <v-btn icon x-large outlined class="px-8 py-8 imagesNotification"
                  v-else>
                  <v-img :src="item.image_url" max-height="24px" max-width="24px" contain></v-img>
                </v-btn>
                <!-- <v-avatar color="red">   <span class="text-h5">CJ</span></v-avatar> -->
              </v-list-item-avatar>
              <v-list-item-content style="padding-left: 20px">
                <v-tooltip bottom style="z-index: 1031;">
                  <template v-slot:activator="{ on, attrs }">
                    <v-list-item-title style="font-size: 14px;" v-bind="attrs" v-on="on">{{ item.text_to_display }}</v-list-item-title>
                  </template>
                  <span style="">{{ item.text_to_display }}</span>
                </v-tooltip>
                <v-list-item-subtitle v-html="item.subtitle"></v-list-item-subtitle>
                <v-list-item-subtitle style="font-size: 10px;">
                  <v-icon color="#27AB9C">mdi-calendar-clock</v-icon> {{ new Date(item.updated_at).toLocaleDateString('th-TH', { timeZone: "UTC", year: 'numeric', month:
                  'short', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}
                </v-list-item-subtitle>
              </v-list-item-content>
            </v-list-item>
            <v-divider v-if="index < itemNotification.length - 1" :key="index"></v-divider>
          </template>
        </v-list>
      </template>
    </a-popover>
    <!-- End Notification -->
  </div>
</template>

<script>
// import { Decode, Encode } from '@/services'
import { Decode, Encode } from '@/services'
import { Popover, Col, Row, Card } from 'ant-design-vue'
export default {
  components: {
    'a-popover': Popover,
    'a-row': Row,
    'a-col': Col,
    'a-card': Card
  },
  data () {
    return {
      checkAdminShop: '',
      visible: false,
      visibleNoti: false,
      count: 0,
      countCurrentDate: 0,
      countAnotherDate: 0,
      selectedItem: 1,
      itemNotification: [],
      ondata: [],
      dateTime: '',
      dataPush: [],
      dataToResponse: [],
      notificationEntity: [],
      dataToSendAll: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  async created () {
    this.$EventBus.$on('OpenNotification', this.OpenNotification)
    this.$EventBus.$on('Open_No_Notification', this.Open_No_Notification)
    // Get Date Time
    if (localStorage.getItem('oneData') !== null) {
      await this.getItemNoti()
    }
  },
  mounted () {
    this.$EventBus.$on('getItemNoti', this.getItemNoti)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getItemNoti')
    })
  },
  methods: {
    async getItemNoti () {
      if (localStorage.getItem('oneData') !== null) {
        this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      }
      this.itemNotification = []
      this.dateTime = ''
      var date = new Date()
      this.dateTime = date.getFullYear().toString().padStart(4, '0') + '-' + (date.getMonth() + 1).toString().padStart(2, '0') + '-' + date.getDate().toString().padStart(2, '0') + ' ' + date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0') + ':' + date.getSeconds().toString().padStart(2, '0')
      var data = {
        entity: 'user',
        entityID: this.onedata.user.user_id,
        state: '',
        toDatetime: this.dateTime,
        type: this.MobileSize ? 'mobile' : 'web'
      }
      await this.$store.dispatch('actionsListNotification', data)
      var responseListNoti = await this.$store.state.ModuleNotification.stateListNotification
      if (responseListNoti.result === 'SUCCESS') {
        // count จำนวนที่ยังไม่ได้อ่านทั้งหมด
        this.count = responseListNoti.data.filter(function (element) {
          return element.state === '0'
        }).length
        // count จำนวนที่ยังไม่ได้อ่านของปัจจุบัน
        this.countCurrentDate = responseListNoti.data.filter(function (element) {
          return new Date(element.updated_at).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'short', day: 'numeric' }) === date.toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'short', day: 'numeric' }) && element.state === '0'
        }).length
        // count จำนวนที่ยังไม่ได้อ่านของเมื่อวาน
        this.countAnotherDate = responseListNoti.data.filter(function (element) {
          return new Date(element.updated_at).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'short', day: 'numeric' }) !== date.toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'short', day: 'numeric' }) && element.state === '0'
        }).length
        // find หา noti ของเมื่อวาน
        // var yesterday = new Date(new Date().setDate(new Date().getDate() - 1)).toLocaleDateString('th-TH', { year: 'numeric', month: 'short', day: 'numeric' })
        var firstIndexYesterday = responseListNoti.data.findIndex(item => new Date(item.updated_at).toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'short', day: 'numeric' }) !== date.toLocaleDateString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'short', day: 'numeric' }))
        if (firstIndexYesterday > -1) {
          responseListNoti.data.splice(firstIndexYesterday, 0, { header: this.$t('Notification.Before') })
        }
        responseListNoti.data.unshift({
          header: this.$t('Notification.New')
        })
        this.itemNotification = responseListNoti.data
      } else {
        this.count = 0
        this.itemNotification = []
      }
    },
    async UpdateNoti (val) {
      var responsePosition = []
      var datarole = []
      if (val.notification_of_what_json.length !== 0) {
        if (val.notification_of_what_json[0].notification_for === 'user') {
          const companyID = val.notification_of_what_json[0].company_id
          localStorage.setItem('company_id', val.notification_of_what_json[0].company_id)
          var getDataComPany = {
            company_id: companyID
          }
          await this.$store.dispatch('actionsDetailCompany', getDataComPany)
          // await this.$store.dispatch('actionsAuthorityUser')
          var responseCompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
          if (this.$store.getters.getDataAuthorityUser.length !== 0) {
            responsePosition = await this.$store.getters.getDataAuthorityUser
          } else {
            await this.$store.dispatch('actionsAuthorityUser')
            responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
          }
          responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
          var listCompany = await responsePosition.data.list_company
          for (let i = 0; i < listCompany.length; i++) {
            if (responseCompany.data.id.toString() === listCompany[i].company_id.toString()) {
              localStorage.setItem('list_Company_detail', Encode.encode(listCompany[i]))
            }
          }
          datarole = {
            role: 'purchaser'
          }
          localStorage.setItem('roleUser', JSON.stringify(datarole))
          localStorage.setItem('CompanyData', Encode.encode(responseCompany.data))
        } else if (val.notification_of_what_json[0].notification_for === 'seller') {
          const sellerShopID = val.notification_of_what_json[0].seller_shop_id
          localStorage.setItem('shopSellerID', sellerShopID)
          if (this.$store.getters.getDataAuthorityUser.length !== 0) {
            responsePosition = await this.$store.getters.getDataAuthorityUser
          } else {
            await this.$store.dispatch('actionsAuthorityUser')
            responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
          }
          // responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
          var listSeller = await responsePosition.data.list_shop_detail
          for (let i = 0; i < listSeller.length; i++) {
            if (sellerShopID === listSeller[i].seller_shop_id) {
              var ShopToFilter = listSeller[i]
              localStorage.setItem('list_shop_detail', Encode.encode(listSeller[i]))
            }
          }
          var dataShop = {
            id: ShopToFilter.seller_shop_id,
            name: ShopToFilter.shop_name !== '' ? ShopToFilter.shop_name : ShopToFilter.shop_name_th
          }
          this.checkAdminShop = 'adminShop'
          localStorage.setItem('checkAdminShop', this.checkAdminShop)
          localStorage.setItem('shopDetail', JSON.stringify(dataShop))
        } else {
          datarole = {
            role: 'ext_buyer'
          }
          localStorage.setItem('roleUser', JSON.stringify(datarole))
        }
      }
      if (this.visibleNoti === true) {
        if (val.length !== 0) {
          this.notificationEntity = []
          var data = {
            notificationEntity: [{
              id: val.id,
              state: 1
            }]
          }
          await this.$store.dispatch('actionsUpdateNotification', data)
          var response = await this.$store.state.ModuleNotification.stateUpdateNotification
          if (response.message === 'Successfully! state of notification to entity updated.') {
            if (response.total_success !== 0) {
              await this.getItemNoti()
              window.location.assign(val.link)
            }
          }
        }
      } else {
      }
    },
    async CheckReadAll () {
      await this.$store.dispatch('actionsReadAllNotification')
      const response = await this.$store.state.ModuleNotification.stateReadAllNotification
      if (response.message === 'Approve All Notifications Success') {
        await this.getItemNoti()
      }
      // var dateTime = ''
      // var dataToSend = ''
      // var date = new Date()
      // dateTime = date.getFullYear().toString().padStart(4, '0') + '-' + (date.getMonth() + 1).toString().padStart(2, '0') + '-' + date.getDate().toString().padStart(2, '0') + ' ' + date.getHours().toString().padStart(2, '0') + ':' + date.getMinutes().toString().padStart(2, '0') + ':' + date.getSeconds().toString().padStart(2, '0')
      // var data = {
      //   entity: 'user',
      //   entityID: this.onedata.user.user_id,
      //   state: '',
      //   toDatetime: dateTime,
      //   type: this.MobileSize ? 'mobile' : 'web'
      // }
      // await this.$store.dispatch('actionsListNotification', data)
      // this.dataToResponse = await this.$store.state.ModuleNotification.stateListNotification
      // if (this.dataToResponse.result === 'SUCCESS') {
      //   dataToSend = this.dataToResponse.data.filter(function (element) {
      //     return element.state === '0'
      //   })
      //   var data1 = {
      //     notificationEntity: []
      //   }
      //   for (var i = 0; i < dataToSend.length; i++) {
      //     data1.notificationEntity.push({
      //       id: dataToSend[i].id,
      //       state: 1
      //     })
      //   }
      //   await this.$store.dispatch('actionsUpdateNotification', data1)
      //   var response = await this.$store.state.ModuleNotification.stateUpdateNotification
      //   if (response.message === 'Successfully! state of notification to entity updated.') {
      //     if (response.total_success !== 0) {
      //       await this.getItemNoti()
      //     }
      //   }
      // }
    },
    OpenNotification () {
      this.visibleNoti = false
    },
    Open_No_Notification () {
      this.visible = false
    },
    GotoDetail (val) {
      // this.count = 0
      this.visibleNoti = false
      window.location.assign(val)
    }
  }

}
</script>

<style lang="scss" scoped>
  ::v-deep .v-list-item__avatar {
    align-self: center !important;
    justify-content: center !important;
  }
</style>

<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>
