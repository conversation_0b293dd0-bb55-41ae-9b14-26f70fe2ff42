<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0" class="ma-2" style="overflow: hidden; border-radius: 8px;">
      <v-row class="mx-0" v-if="!MobileSize">
        <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">รายงานการสั่งซื้อ</v-card-title>
      </v-row>
      <v-row v-else>
        <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#1AB759" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายงานการสั่งซื้อ</v-card-title>
      </v-row>
      <v-col cols="12" class="py-0">
        <!-- <a-tabs @change="SelectDetailOrder">
          <a-tab-pane :key="0"><span slot="tab">รายการสั่งซื้อที่ยังไม่จัดส่ง <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countPendingList }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="1"><span slot="tab">รายการสั่งซื้อที่จัดส่งแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countSuccessList }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="2"><span slot="tab">รายการสั่งซื้อที่ยังไม่ชำระเงิน <a-tag color="#FFA500" style="border-radius: 8px;">{{ countNotPendingList }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="3"><span slot="tab">รายการสั่งซื้อที่ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countCencelList }}</a-tag></span></a-tab-pane>
        </a-tabs> -->
      </v-col>
      <!-- <v-row v-if="disableTable === false" dense>
        <v-col cols="12" md="12" sm="12" xs="12" align="end" :class="!MobileSize ? 'pt-1 pr-4' : 'pl-2 pr-2 mb-3'">
          <v-btn :block="MobileSize" :class="MobileSize ? 'my-2 white--text' : 'mr-4 white--text'" style="border-radius: 4px;" @click="linkToETax()" color="#27AB9C">เข้าระบบ e-Tax</v-btn>
          <v-btn :block="MobileSize" style="border-radius: 4px;" color="#27AB9C" outlined @click="GotoETaxCredential()">e-Tax Credential</v-btn>
        </v-col>
      </v-row> -->
      <v-overlay :value="overlay">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-row justify="center" align-content="center" v-if="disableTable === false">
        <v-col cols="12" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายงานการสั่งซื้อที่{{ StateStatus === 0 ? 'ยังไม่ดำเนินการ' : StateStatus === 1 ? 'ดำเนินการแล้ว' : 'ยกเลิก' }}</b></h2>
          </v-col>
      </v-row>
      <!-- </v-card> -->
      <v-row no-gutters justify="start" v-if="disableTable === true">
        <v-col cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-3 pt-6' : 'pl-2 pr-2 mb-3'">
          <v-text-field color="#27AB9C" style="border-radius: 8px;" v-model="search" dense hide-details outlined placeholder="ค้นหาข้อมูล" append-icon="mdi-magnify"></v-text-field>
        </v-col>
        <!-- <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-2 pt-0' : 'pl-2 pr-2 mb-3'">
          <v-row dense>
            <v-col cols="6">
              <span style="font-size: 16px; line-height: 24px; color: #333333;">Pay Type</span>
              <v-select v-model="PayTypeSelect" :items="payTypeItem" item-text="text" item-value="value" outlined dense></v-select>
            </v-col>
            <v-col cols="6">
              <span style="font-size: 16px; line-height: 24px; color: #333333;">ใบกำกับภาษี</span>
              <v-select v-model="InvoiceSelect" :items="invoiceItem" item-text="text" item-value="value" outlined dense></v-select>
            </v-col>
          </v-row>
        </v-col> -->
        <v-col v-if="disableTable === true" cols="12" md="12" sm="12"  :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'pl-2 pr-2 mb-3'">
          <v-row dense>
            <v-col cols="12" md="4" sm="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333;">สถานะ</span>
              <v-select v-model="selected" :items="item_selected" style="border-radius: 8px;" menu-props="offset-y" placeholder="ทั้งหมด" outlined dense @change="getOrder()" class="setCustomSelect"></v-select>
            </v-col>
            <v-col cols="12" md="4" sm="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333;">วันเริ่มต้นสัญญา</span>
              <v-dialog
                ref="modalStartDate"
                v-model="modalStartDate"
                :return-value.sync="startdate"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field readonly v-model="contractStartDate" style="border-radius: 8px;" v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                </template>
                <v-date-picker
                  v-model="startdate"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueStartDate(startdate)"
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModaltStartDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.modalStartDate.save(startdate)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12" md="4" sm="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333;">วันสิ้นสุดสัญญา</span>
              <v-dialog
                ref="modalEndDate"
                v-model="modalEndDate"
                :return-value.sync="enddate"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field :disabled="searchContractStartDate !== '' ? false : true" style="border-radius: 8px;" readonly v-model="contractEndDate" v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                </template>
                <v-date-picker
                  v-model="enddate"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueContractEndDate(enddate)"
                  :min="searchContractStartDate"
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModalEndDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.modalEndDate.save(enddate), getOrder()"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
          <v-col cols="12" md="12" sm="12" align="end">
            <v-btn @click="downloadExcel()" color="#27AB9C" outlined rounded dark><v-icon small class="mr-1">mdi-download-circle-outline</v-icon>ดาวน์โหลด Excel</v-btn>
          </v-col>
        </v-col>
        <v-col cols="12" >
          <v-card outlined class="small-card mx-4 my-5" min-height="436">
            <v-data-table
              :headers="headers"
              :items="DataTable"
              style="width:100%;"
              height="100%"
              :page.sync="page"
              :search="search"
              :items-per-page="10"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              @pagination="countOrdar"
              no-results-text="ไม่พบชื่อผู้ซื้อหรือรหัสการสั่งซื้อที่ค้นหา"
              no-data-text="ไม่มีรายการสินค้าในตาราง"
            >
              <template v-slot:[`item.order_number`]="{ item }">
                {{item.order_number}}
              </template>
              <template v-slot:[`item.vender_number`]="{ item }">
                {{item.vender_number}}
              </template>
              <template v-slot:[`item.vender_name`]="{ item }">
                {{item.vender_name}}
              </template>
              <template v-slot:[`item.cust_no`]="{ item }">
                {{item.cust_no}}
              </template>
              <template v-slot:[`item.customer_name`]="{ item }">
                {{item.customer_name}}
              </template>
              <template v-slot:[`item.item_no`]="{ item }">
                {{item.item_no}}
              </template>
              <template v-slot:[`item.actual_cost`]="{ item }">
                {{Number(item.actual_cost).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
              </template>
              <template v-slot:[`item.eng_cost`]="{ item }">
                {{Number(item.eng_cost).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
              </template>
              <template v-slot:[`item.revenue`]="{ item }">
                {{Number(item.revenue).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
              </template>
              <template v-slot:[`item.margin`]="{ item }">
                {{Number(item.margin).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
              </template>
              <template v-slot:[`item.start_date`]="{ item }">
                {{new Date(item.start_date).toLocaleDateString("th-TH", { year: "numeric", month: "2-digit", day: "2-digit" })}}
              </template>
              <template v-slot:[`item.end_date`]="{ item }">
                {{new Date(item.end_date).toLocaleDateString("th-TH", { year: "numeric", month: "2-digit", day: "2-digit" })}}
              </template>
              <template v-slot:[`item.revenue_month`]="{ item }">
                {{item.revenue_month}}
              </template>
              <template v-slot:[`item.qt_no`]="{ item }">
                {{item.qt_no}}
              </template>
              <template v-slot:[`item.so_no`]="{ item }">
                {{item.so_no}}
              </template>
              <template v-slot:[`item.pr_no`]="{ item }">
                {{item.pr_no}}
              </template>
              <template v-slot:[`item.po_no`]="{ item }">
                {{item.po_no}}
              </template>
              <template v-slot:[`item.status`]="{ item }">
                {{item.status}}
              </template>
              <template v-slot:[`item.remark`]="{ item }">
                {{item.remark}}
              </template>
              <template v-slot:[`item.change_startdate`]="{ item }">
                {{new Date(item.change_startdate).toLocaleDateString("th-TH", { year: "numeric", month: "2-digit", day: "2-digit" })}}
              </template>
              <template v-slot:[`item.change_endtdate`]="{ item }">
                {{new Date(item.change_endtdate).toLocaleDateString("th-TH", { year: "numeric", month: "2-digit", day: "2-digit" })}}
              </template>
              <template v-slot:[`item.ref_qt`]="{ item }">
                {{item.ref_qt}}
              </template>
              <template v-slot:[`item.ref_so`]="{ item }">
                {{item.ref_so}}
              </template>
              <template v-slot:[`item.ref_pr`]="{ item }">
                {{item.ref_pr}}
              </template>
              <template v-slot:[`item.ref_po`]="{ item }">
                {{item.ref_po}}
              </template>
              <!-- <template v-slot:[`item.order_number`]="{ item }">
                  <a @click="orderDetail(item)">{{item.order_number}}</a>
                </template> -->
              <!-- <template v-slot:[`item.received`]>
                  <v-row justify="center" >
                    <v-checkbox v-model="received"></v-checkbox>
                  </v-row>
                </template> -->
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import axios from 'axios'
export default {
  data () {
    return {
      seller_shop_id: '',
      modalBuyDate: false,
      modalAcceptDate: false,
      modalStartDate: false,
      modalEndDate: false,
      // date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      // date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      startdate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      enddate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchBuyDate: '',
      link: '',
      buyDate: '',
      searchAcceptDate: '',
      acceptDate: '',
      searchContractStartDate: '',
      contractStartDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      PayTypeSelect: '',
      InvoiceSelect: '',
      statusSelect: '',
      acceptSelect: '',
      orderList: [],
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: false,
      name: 'ImageItem',
      payTypeItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'Onetime', value: 'Onetime' },
        { text: 'Recurring', value: 'Recurring' }
      ],
      invoiceItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ขอใบกำกับภาษี', value: 'y' },
        { text: 'ไม่ขอใบกำกับภาษี', value: 'n' }
      ],
      statusItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'New Service', value: 'new_service' },
        { text: 'Change', value: 'change' },
        { text: 'Renew', value: 'renew' },
        { text: 'Change&Renew', value: 'change&renew' },
        { text: 'Terminate', value: 'terminate' }
      ],
      actionsItem: [
        { text: 'รายละเอียด', value: 'detail' }
        // { text: 'Change', value: 'change' },
        // { text: 'Renew', value: 'renew' },
        // { text: 'Change&Renew', value: 'both' },
        // { text: 'Terminate', value: 'terminate' }
      ],
      AcceptItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'เขียว', value: 'green' },
        { text: 'เหลือง', value: 'yellow' },
        { text: 'แดง', value: 'red' }
      ],
      headers: [
        {
          text: 'Order No.',
          value: 'order_number',
          align: 'start',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail',
          width: '150'
        },
        {
          text: 'Vender Name',
          value: 'vender_name',
          align: 'start',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail',
          width: '180'
        },
        {
          text: 'Customer Name',
          value: 'customer_name',
          align: 'start',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail',
          width: '180'
        },
        {
          text: 'Ref. QT',
          value: 'ref_qt',
          align: 'start',
          sortable: false,
          filterable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail',
          width: '150'
        },
        {
          text: 'Ref. SO',
          value: 'ref_so',
          width: '150',
          sortable: false,
          align: 'start',
          filterable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Ref. PR',
          value: 'ref_pr',
          width: '150',
          sortable: false,
          align: 'start',
          filterable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Ref. PO',
          value: 'ref_po',
          align: 'start',
          sortable: false,
          filterable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail',
          width: '150'
        }
      ],
      seller_sent_status: '',
      send_items: [
        { text: 'จัดส่งแล้ว', value: 'sent' },
        { text: 'ยังไม่จัดส่ง', value: 'not_sent' },
        { text: 'ยกเลิก', value: 'cancel' }
      ],
      send_items_notPaid: [
        { text: 'ยังไม่จัดส่ง', value: 'not_sent' },
        { text: 'ยกเลิก', value: 'cancel' }
      ],
      status_items: [
        { text: 'ดำเนินการแล้ว', value: 'not_sent' },
        { text: 'ยังไม่ดำเนินการ', value: 'cancel' }
      ],
      DataTable: [],
      customClick: record => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: '',
      page: 1,
      countPendingList: 0,
      countSuccessList: 0,
      countCencelList: 0,
      countNotPendingList: 0,
      OrderNameList: [
        { key: 0, name: 'รายการสั่งซื้อที่ยังไม่ดำเนินการ' },
        { key: 1, name: 'รายการสั่งซื้อที่ดำเนินการแล้ว' },
        { key: 2, name: 'รายการสั่งซื้อที่ยกเลิก' }
      ],
      selected: '',
      item_selected: [{ text: 'ทั้งหมด', value: '' },
        { text: 'Complete', value: 'complete' },
        { text: 'Change', value: 'change' },
        { text: 'Renew', value: 'renew' },
        { text: 'Terminate', value: 'terminate' }],
      roleUser: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }

    // GetSellerShop () {
    //   var orderList = []
    //   orderList = this.$store.state.ModuleOrder.stateOrderListSeller.data
    //   return orderList
    // }
  },
  watch: {
    DataTable (val) {
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ReportsellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Reportseller' }).catch(() => {})
      }
    }
  },
  async created () {
    this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
    this.getOrder()
    // await this.$store.dispatch('actionListOrderSeller', shopData)
    // this.orderList = await this.$store.state.ModuleOrder.stateOrderListSeller.data
    // this.ProcurementData = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('ProcurementData'))))
    // this.getDataTable()
    // if (this.StateStatus === 0) {
    //   this.DataTable = this.orderList.data_incomplete
    // } else if (this.StateStatus === 1) {
    //   this.DataTable = this.orderList.not_paid
    // } else if (this.StateStatus === 2) {
    //   this.DataTable = this.orderList.success
    // } else if (this.StateStatus === 3) {
    //   this.DataTable = this.orderList.cancel
    // } else if (this.StateStatus === 4) {
    //   this.DataTable = this.orderList.fail
    // }
  },
  methods: {
    // gotoActions (item, select) {
    //   if (select === 'detail') {
    //     var data = {
    //       order_number: item.order_number,
    //       payment_transaction_number: item.transaction_number
    //     }
    //     localStorage.setItem('orderNumberSeller', Encode.encode(data))
    //     var OrderNumber = item.order_number
    //     var transactionNumber = item.transaction_number
    //     if (this.MobileSize) {
    //       this.$router.push({ path: `/posellerDetailMobile?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}` }).catch(() => {})
    //     } else {
    //       this.$router.push({ path: `/POSellerDetail?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}` }).catch(() => {})
    //     }
    //   } else if (select === 'change') {
    //   } else if (select === 'renew') {
    //   } else if (select === 'both') {
    //   } else if (select === 'terminate') {
    //   }
    // },
    setValueBuyDate (val) {
      this.searchBuyDate = val
      // console.log(this.searchDateNotFormat)
      this.buyDate = this.formatDateToShow(val)
    },
    setValueAcceptDate (val) {
      this.searchAcceptDate = val
      // console.log(this.searchDateNotFormat)
      this.acceptDate = this.formatDateToShow(val)
    },
    setValueStartDate (val) {
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
    },
    setValueContractEndDate (val) {
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
    },
    // closeModalBuyDate () {
    //   this.modalBuyDate = false
    //   this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    //   this.searchBuyDate = ''
    //   this.buyDate = ''
    // },
    // closeModalAcceptDate () {
    //   this.modalAcceptDate = false
    //   this.date1 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    //   this.searchAcceptDate = ''
    //   this.acceptDate = ''
    // },
    closeModaltStartDate () {
      this.modalStartDate = false
      this.startdate = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
    },
    closeModalEndDate () {
      this.modalEndDate = false
      this.enddate = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.searchContractEndDate = ''
      this.contractEndDate = ''
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    // async GetInvoicePDF (val) {
    //   var data = {
    //     transactionCode: val.transaction_code
    //   }
    //   await this.$store.dispatch('ActionsGetETaxPDF', data)
    //   var response = await this.$store.state.ModuleCart.stateGetETaxPDF
    //   if (response.result === 'OK') {
    //     if (response.etaxResponse.status === 'OK') {
    //       window.open(`${response.etaxResponse.pdfURL}`)
    //     }
    //   }
    // },
    linkToETax () {
      window.open('https://devinet-etax.one.th/portal/login', '_blank')
    },
    GotoETaxCredential () {
      if (this.MobileSize) {
        this.$router.push({ path: '/EtaxCredentailMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/EtaxCredentail' }).catch(() => {})
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    // async UpdateStatusSeller (val) {
    //   const update = {
    //     order_number: val.order_number,
    //     seller_sent_status: val.seller_sent_status
    //   }
    //   await this.$store.dispatch('actionUpdateStatusSeller', update)
    //   this.$swal.fire({
    //     icon: 'success',
    //     title: 'บันทึกการส่งสินค้าสำเร็จ',
    //     showConfirmButton: false,
    //     timer: 1500
    //   })
    //   this.GetSellerShop()
    // },
    async orderDetail (val) {
      var data = {
        order_number: val.order_number,
        payment_transaction_number: val.transaction_number,
        num_credit_term: val.num_of_credit_term
      }
      localStorage.setItem('orderNumberSeller', Encode.encode(data))
      var OrderNumber = val.order_number
      var transactionNumber = val.transaction_number
      var termNumber = val.num_of_credit_term
      if (this.MobileSize) {
        this.$router.push({ path: `/posellerDetailMobile?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}&termNumber=${termNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/POSellerDetail?orderNumber=${OrderNumber}&tranNumber=${transactionNumber}&termNumber=${termNumber}` }).catch(() => {})
      }
      // await this.$store.dispatch('actionOrderDetailSeller', data)
      // await this.$store.state.ModuleOrder.stateOrderDetailSeller.data
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      // }
    },
    async orderTransactionnumber (val) {
      window.open(`${val.pdf_for_seller}`)
    },
    // async GetSellerShop () {
    //   this.DataTable = []
    //   this.countPendingList = 0
    //   this.countSuccessList = 0
    //   this.countCencelList = 0
    //   this.countNotPendingList = 0
    //   const shopId = localStorage.getItem('shopSellerID')
    //   var data = {
    //     seller_shop_id: shopId
    //   }
    //   await this.$store.dispatch('actionListOrderSeller', data)
    //   var responseData = await this.$store.state.ModuleOrder.stateOrderListSeller
    //   if (responseData.code !== 401) {
    //     this.orderList = responseData.data
    //     this.countPendingList = this.orderList.pending.length
    //     this.countSuccessList = this.orderList.success.length
    //     this.countCencelList = this.orderList.cancel.length
    //     this.countNotPendingList = this.orderList.not_paid.length
    //     if (this.StateStatus === 0) {
    //       this.DataTable = this.orderList.pending
    //     } else if (this.StateStatus === 1) {
    //       this.DataTable = this.orderList.success
    //     } else if (this.StateStatus === 2) {
    //       this.DataTable = this.orderList.not_paid
    //     } else if (this.StateStatus === 3) {
    //       this.DataTable = this.orderList.cancel
    //     }
    //     if (this.orderList.length === 0) {
    //       this.disableTable = false
    //     } else {
    //       this.disableTable = true
    //     }
    //   } else {
    //     this.$store.commit('closeLoader')
    //     localStorage.removeItem('roleUser')
    //     localStorage.removeItem('roleUserApprove')
    //     localStorage.removeItem('oneData')
    //     localStorage.removeItem('orderNumber')
    //     localStorage.removeItem('orderNumberSeller')
    //     this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
    //     window.location.assign('/')
    //   }
    // },
    // SelectDetailOrder (item) {
    //   this.page = 1
    //   this.StateStatus = item
    //   if (this.StateStatus === 0) {
    //     this.DataTable = this.orderList.pending
    //   } else if (this.StateStatus === 1) {
    //     this.DataTable = this.orderList.success
    //   } else if (this.StateStatus === 2) {
    //     this.DataTable = this.orderList.not_paid
    //   } else if (this.StateStatus === 3) {
    //     this.DataTable = this.orderList.cancel
    //   }
    //   if (this.DataTable.length === 0) {
    //     this.disableTable = false
    //   } else {
    //     this.disableTable = true
    //   }
    // },
    // async getDataTable () {
    //   this.overlay = true
    //   const data = {
    //     procurement_org_id: this.ProcurementData.procurement_org_id
    //   }
    //   await this.$store.dispatch('actionListOrderProcurement', data)
    //   var response = await this.$store.state.ModuleCart.stateListOrderProcurement
    //   if (response.result === 'SUCCESS') {
    //     this.responseData = response.data
    //     this.overlay = false
    //   }
    // },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateDetailOrder
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem(
          'MyOrderDetail',
          Encode.encode(JSON.stringify(response.data))
        )
        this.$router.push('/myorderdetail')
      }
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    getColor (item) {
      if (item === 'Pending') return '#FCF0DA'
      else if (item === 'Not Paid') return '#FCF0DA'
      else if (item === 'Success') return '#F0F9EE'
      else if (item === 'Approve') return '#F0F9EE'
      else if (item === 'Fail') return '#F7D9D9'
      else if (item === 'Credit Term') return '#E5EFFF'
      else return '#F7D9D9'
    },
    getTextColor (item) {
      if (item === 'Pending') return '#E9A016'
      else if (item === 'Not Paid') return '#E9A016'
      else if (item === 'Success') return '#1AB759'
      else if (item === 'Approve') return '#1AB759'
      else if (item === 'Fail') return '#D1392B'
      else if (item === 'Credit Term') return '#1B5DD6'
      else return '#D1392B'
    },
    getStatus (item) {
      if (item === 'Pending') return 'รออนุมัติ'
      else if (item === 'Not Paid') return 'ยังไม่ชำระเงิน'
      else if (item === 'Success') return 'ชำระเงินสำเร็จ'
      else if (item === 'Approve') return 'วางบิล'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else if (item === 'Credit Term') return 'ชำระเงินแบบเครดิตเทอม'
      else return 'ยกเลิกคำสั่งซื้อ'
    },
    async downloadExcel () {
      await axios({
        // url: 'http://localhost:3000/paperlessToUPS/downloadFilexlsx',
        url: `${process.env.VUE_APP_BACK_END2}exports/reports/order?keyword=${this.search}&start_date=${this.searchContractStartDate}&end_date=${this.searchContractEndDate}&status=${this.selected}`,
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'result.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async getOrder () {
      this.$store.commit('openLoader')
      const data = {
        keyword: this.search,
        status: this.selected,
        start_date: this.searchContractStartDate,
        end_date: this.searchContractEndDate,
        seller_shop_id: this.seller_shop_id.id
      }
      await this.$store.dispatch('actionReportOrderb2b', data)
      const response = await this.$store.state.ModuleOrder.stateOrderb2bSeller
      if (response.code === 200) {
        this.DataTable = response.data.reports
        if (this.DataTable.length > 0) {
          this.disableTable = true
        }
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          text: `${response.message}`,
          showConfirmButton: false,
          timer: 1500
        })
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
      font-size: 0.62rem;
 }
</style>
