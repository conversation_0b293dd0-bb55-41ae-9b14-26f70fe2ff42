<template>
  <v-dialog v-model="dialogAwaitChangeQT" width="424" persistent>
    <v-card style="background: #FFFFFF; border-radius: 24px;">
      <v-img
        height="240px"
        :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
      >
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click.stop="closeDialogAwait()"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
      </v-img>
      <v-container>
        <v-card-text style="text-align: center;">
          <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
          <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคา</span><br/>
          <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
        </v-card-text>
        <v-card-text>
          <v-row dense justify="center">
            <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click.stop="closeDialogAwait()">ยกเลิก</v-btn>
            <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="editDescription()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-container>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: ['value'],
  computed: {
    dialogAwaitChangeQT: {
      get () {
        return this.value
      },
      set (value) {
        this.$emit('input', value)
      }
    }
  }
}
</script>
