<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการ Stock สินค้าทั้งหมด</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายการ Stock สินค้าทั้งหมด</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-row class="align-baseline">
            <v-col :cols="MobileSize ? 12 : 5" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
              <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าของระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col class="text-end" :cols="MobileSize ? 4 : 3">
              <span style="font-size: 16px; align-items: center; color: #333333; font-weight: 600;">เลือกร้านค้า: </span>
            </v-col>
            <v-col :cols="MobileSize ? 8 : 4">
              <v-autocomplete
                v-model="selectedShop"
                :items="adminShopListByID"
                item-text="seller_shop_name"
                item-value="seller_shop_id"
                style="font-size: 14px; font-weight: 500;"
                outlined
                rounded
                dense
                class="pl-0 pr-3 mb-4 pt-2"
                hide-details
                :search-input.sync="searchShop"
                @change="onSelect"
              ></v-autocomplete>
            </v-col>
          </v-row>
          <v-col cols="12" md="12">
            <v-row dense class="px-0">
              <v-col cols="12" md="5" sm="12" align="start" :class="MobileSize ? 'pl-2' : 'pt-6'">
                <span
                  :class="MobileSize ? '' : ''"
                  style="line-height: 24px; align-items: center; color: #333333; font-weight: 600;"
                  :style="MobileSize ? 'font-size: 16px;' : 'font-size: 20px;'"
                  >รายการ Stock สินค้าทั้งหมด {{ adminShopListStock.length }} รายการ</span
                >
              </v-col>
              <v-row class="mt-2 px-4 mb-4" v-if="MobileSize">
                <v-btn v-if="MobileSize && selectedShop !== ''" @click="ImportExcel" data-v-step="3" class="white--text" rounded width="150" height="40" color="#27AB9C" >
                  <v-icon left size="24">mdi-file-plus-outline</v-icon>Import File
                </v-btn>
                <input @click="event => event.target.value = null" @change="UploadExcel($event)" id="importExcel"
                style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
                <v-spacer class="mx-auto"></v-spacer>
                <v-btn v-if="MobileSize" @click="downloadExcel()" rounded width="150" height="40" outlined color="#27AB9C" >
                  <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/file-export-solid.png')" max-width="16" max-height="16"></v-img>Export File
                </v-btn>
              </v-row>
              <v-col cols="12" md="7" sm="12" align="end" class="pt-3" v-if="!MobileSize">
                <v-btn v-if="selectedShop !== ''" @click="ImportExcel" data-v-step="3" class="white--text mr-2" rounded color="#27AB9C" width="160" height="40">
                  <v-icon left size="24">mdi-file-plus-outline</v-icon>Import File
                </v-btn>
                <input @click="event => event.target.value = null" @change="UploadExcel($event)" id="importExcel"
                style="display: none;" type="file" accept=".csv, application/vnd.openxmlformats-officedocument.spreadsheetml.sheet, application/vnd.ms-excel"  />
                <v-btn @click="downloadExcel()" rounded width="160" height="40" outlined color="#27AB9C" >
                  <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/file-export-solid.png')" max-width="16" max-height="16"></v-img>Export File
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12" v-if="ShowTable === true && adminShopListStock.length !== 0">
                <v-data-table
                :headers="headers"
                :items="adminShopListStock"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อร้านค้าของระบบ"
                no-data-text="ไม่มีชื่อร้านค้าของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <!-- <template v-slot:[`item.errorMessage`]="{ item }">
                    <v-btn v-if="item.status === 'ER'" color="#27AB9C" outlined @click="OpenDialogShowData('error', item.errorMessage)">ดู Error Message</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.inputCSV`]="{ item }">
                    <v-btn color="#27AB9C" @click="OpenDialogShowData('csv', item.inputCSV)" class="white--text">ดูข้อมูล CSV</v-btn>
                  </template> -->
                  <!-- <template v-slot:[`item.indexOfProduct`]="{ item, index }">
                    {{ index + 1 }}
                  </template> -->
                  <template v-slot:[`item.name`]="{ item }">
                      <span style="font-size: 14px; max-width: 180px;" class="d-inline-block text-truncate"> {{ item.name }}</span>
                  </template>
                  <template v-slot:[`item.sku`]="{ item }">
                      <span style="font-size: 14px; "> {{ item.sku }}</span>
                  </template>
                  <template v-slot:[`item.productdetails`]="{ item }">
                    <div v-if="item.have_attribute === 'yes'">
                      <p v-if="item.p_att_detail[0].attribute_priority_1 !== '' && item.p_att_detail[0].attribute_priority_1 !== null" class="mb-0"
                        style="font-size: 14px;">{{ item.p_att_detail[0].attribute_1_key}}: <span class="mb-0"
                        style="font-size: 14px; font-weight: 600;">{{ item.p_att_detail[0].attribute_priority_1 }}</span></p>
                    </div>
                    <div v-if="item.have_attribute === 'yes'">
                      <p v-if="item.p_att_detail[0].attribute_priority_2 !== '' && item.p_att_detail[0].attribute_priority_2 !== null" class="mb-0"
                        style="font-size: 14px;">{{ item.p_att_detail[0].attribute_2_key}}: <span class="mb-0"
                        style="font-size: 14px; font-weight: 600;">{{ item.p_att_detail[0].attribute_priority_2 }}</span></p>
                    </div>
                  </template>
                  <template v-slot:[`item.real_price`]="{ item }">
                      <b style="font-size: 14px;"> {{ Number(parseInt(item.real_price)).toLocaleString(undefined,{ minimumFractionDigits: 2 }) }}</b>
                  </template>
                  <template v-slot:[`item.actual_stock`]="{ item }">
                      <b style="font-size: 14px;"> {{ Number(parseInt(item.actual_stock)) }}</b>
                  </template>
                  <template v-slot:[`item.effective_stock`]="{ item }">
                      <b style="font-size: 14px;"> {{ Number(parseInt(item.effective_stock)) }}</b>
                  </template>
                </v-data-table>
              </v-col>
              <v-col cols="12" v-if="adminShopListStock.length === 0" align="center">
                <div class="my-5">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-stock.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <div v-if="IpadSize">
                  <h3 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>ร้านค้านี้ไม่มีสินค้าในระบบ</b></h3>
                </div>
                <div v-else>
                  <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>ร้านค้านี้ไม่มีสินค้าในระบบ</b></h2>
                </div>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
        <v-card>
            <v-card-title class="text-h5 grey lighten-2">
              {{ dataHeader }}
            </v-card-title>

            <v-card-text class="pt-4">
              <pre v-html="dataToShow"></pre>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                text
                @click="modalShowData = false"
              >
                ปิด
              </v-btn>
            </v-card-actions>
        </v-card>
      </v-dialog> -->
    </v-card>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
import axios from 'axios'
export default {
  data () {
    return {
      searchShop: '',
      ShopName: '',
      ShowTable: true,
      selectedShop: '',
      statusSelect: '',
      search: '',
      modalShowData: false,
      adminShopListByID: '',
      adminShopList: [],
      adminShopListStock: [],
      dataToShow: '',
      dataHeader: '',
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      headers: [
        // { text: 'ลำดับ', value: 'indexOfProduct', sortable: false, class: 'backgroundTable fontTable--text', width: '10%', align: 'center' },
        { text: 'ชื่อร้าน', value: 'seller_shop_name', sortable: false, class: 'backgroundTable fontTable--text', width: '200', align: 'start' },
        { text: 'ชื่อสินค้า', value: 'name', sortable: false, class: 'backgroundTable fontTable--text', width: '250', align: 'start' },
        { text: 'รหัส SKU', value: 'sku', sortable: false, class: 'backgroundTable fontTable--text', width: '180', align: 'start' },
        { text: 'รายละเอียดสินค้า', value: 'productdetails', sortable: false, class: 'backgroundTable fontTable--text', width: '250', align: 'start' },
        { text: 'ราคา', value: 'real_price', sortable: true, class: 'backgroundTable fontTable--text', width: '100', align: 'start' },
        { text: 'จำนวนสินค้าพร้อมขาย', value: 'actual_stock', sortable: true, class: 'backgroundTable fontTable--text', width: '200', align: 'center' },
        { text: 'จำนวนสินค้าคงคลัง', value: 'effective_stock', sortable: true, class: 'backgroundTable fontTable--text', width: '200', align: 'center' }
        // { text: 'สถานะสินค้า', value: '', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/stockAdminMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'stockAdmin')
        this.$router.push({ path: '/stockAdmin' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.selectedShop = 3
      this.getStockData()
      // this.getShopDataAdmin()
    //   this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  methods: {
    async onSelect (value) {
      if (!value) return
      this.searchShop = ''
      await this.getStockData()
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getStockData () {
      // console.log(this.selectedShop)
      this.$store.commit('openLoader')
      // var data1 = []
      var data = {
        seller_shop_id: this.selectedShop
      }
      // console.log(data)
      await this.$store.dispatch('actionsStockProduct', data)
      var response = await this.$store.state.ModuleAdminManage.stateStockProduct
      // console.log('list ====>', response)
      if (response.message === 'GetDashboardStock API') {
        this.$store.commit('closeLoader')
        this.adminShopListStock = [...response.result.product_detail]
        this.adminShopList = [...response.result.seller_detail]
        // data1.push({ seller_shop_id: '', seller_shop_name: 'ทั้งหมด' })
        // this.adminShopList.forEach(element => {
        //   data1.push({
        //     seller_shop_id: element.seller_shop_id,
        //     seller_shop_name: element.seller_shop_name
        //   })
        // })
        // this.adminShopListByID = data1
        this.adminShopListByID = await this.adminShopList
        // console.log(data1)
        this.ShowTable = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', html: '<h4>ร้านค้านี้ไม่มีสินค้าในระบบ</h4>' })
        // this.ShowTable = false
        // this.adminShopListStock = []
      }
    },
    async downloadExcel () {
      this.$store.commit('openLoader')
      this.adminShopListByID.forEach(element => {
        if (element.seller_shop_id === this.selectedShop) {
          this.ShopName = element.seller_shop_name
        }
      })
      // console.log(this.ShopName)
      var data = {
        seller_name: this.ShopName,
        seller_shop_id: this.selectedShop
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END2}exports/stock/excel`,
        data: data,
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        // console.log(response)
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.target = '_blank'
        fileLink.setAttribute('download', `รายการสต๊อกสินค้าร้าน(${this.ShopName}).xlsx`)
        document.body.appendChild(fileLink)
        fileLink.click()
      })
      this.$store.commit('closeLoader')
    },
    ImportExcel () {
      document.getElementById('importExcel').click()
    },
    async UploadExcel (e) {
      this.$store.commit('openLoader')
      this.adminShopListByID.forEach(element => {
        if (element.seller_shop_id === this.selectedShop) {
          this.ShopName = element.seller_shop_name
        }
      })
      var files = e.target.files
      var f = files[0]
      // console.log(f)
      var data = new FormData()
      data.append('seller_shop_id', this.selectedShop)
      data.append('update_stock', f)
      data.append('seller_name', this.ShopName)
      await this.$store.dispatch('actionsImportStock', data)
      var response = await this.$store.state.ModuleAdminManage.stateImportStock
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: 'อัพเดทสต๊อกสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.getStockData()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', html: `${response.data.errors[0].message}<br>${response.data.errors[0].value}`, showConfirmButton: false, timer: 5000 })
      }
    }
  }
}
</script>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
