<template>
  <div cols="12" class="pb-10">
    <!-- <v-col> -->
      <v-breadcrumbs :items="itemsPage" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
          <template v-slot:divider>
            <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
          </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
              <span style="z-index:1;" :style="{ color: item.disabled === true ? '#27AB9C' : '#636363', 'font-size': '16px' }">{{ item.text
              }}</span>
            </v-breadcrumbs-item>
          </template>
      </v-breadcrumbs>
    <!-- </v-col> -->
    <v-col cols="12" md="12" :class="MobileSize || IpadSize || IpadProSize? 'pa-0' : ''" style="background-color: #FAFFFF;">
      <v-container :class="MobileSize || IpadSize || IpadProSize? 'pa-0': ''">
        <v-card
          style="background-color: #FAFFFF;"
          max-height="100%"
          max-width="100%"
          elevation="0"
          :class="IpadSize? 'mt-6': 'mt-1'"
        >
          <v-row no-gutters>
            <v-col cols="12" md="12" sm="12" :class="MobileSize? 'pt-0':'mb-0'">
              <v-row
                dense
                :class="
                  MobileSize ? 'mt-0' : IpadProSize || IpadSize ? 'mt-5' : 'mt-0'
                "
              >
                <v-col
                  cols="12"
                  md="12"
                  :class="IpadSize ? 'pl-8 pt-4':IpadProSize? 'pl-0 pt-0':MobileSize? 'pt-0 mt-4':'pt-4 pl-14'"
                >
                      <v-row dense no-gutters justify="start">
                      <v-col cols="12" md="4" sm="4" xs="12">
                        <v-text-field
                        v-model="search"
                        dense
                        outlined
                        :placeholder="this.$t('AllShopsPage.SearchShops')"
                        >
                          <v-icon slot="append" color="#3EC6B6">mdi-magnify </v-icon>
                        </v-text-field>
                        <span style="
                        color: #9A9A9A;
                        font-size: 16px;
                        line-height: 22.4px;
                        font-weight: 400;" >{{ $t('AllShopsPage.AllShop') }} {{shopCount}} {{ $t('AllShopsPage.Shop') }}</span>
                      </v-col>
                      <!-- <v-col cols="12" md="8" sm="4" xs="12">
                        <div style="float: right; display: inline-flex; height: 44px;" >
                      <span class="pl-2 mt-3 mr-3" :style="IpadSize ? 'font-weight: bold; font-size: 15px':MobileSize ? 'font-weight: bold; font-size: 14px' : 'font-weight: 500; font-size: 16px'">คะแนน :</span>
                      <v-select
                            v-model="search3"
                            :items="filterName"
                            item-text="text"
                            item-value="id"
                            placeholder="ทั้งหมด"
                            dense
                            :style="MobileSize ? 'width: 90px !important;' : 'width: 120px !important;'"
                            outlined
                            class="mt-1"
                            :rules="Rules.selectType"
                          ></v-select>
                    </div>
                      </v-col> -->
                    </v-row>
                  <!-- <div :class="MobileSize? 'py-4': 'pt-3'">
                    <span :style="IpadSize ? 'font-size: 13px;':MobileSize ? 'font-size: 14px' : 'font-size: 18px;'">{{dataShop.shop_description}}</span>
                  </div> -->
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
        <a-row
        v-if="ProductSearch.length !== 0"
        style="width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;"
        >
        <v-row  :justify="MobileSize || IpadSize ? 'center' : 'start'" :class="MobileSize || IpadSize ? 'pl-3' : 'pl-12 ml-7'">
          <div v-for="(item, index) in ProductSearch" :key="index" align="start" :class="MobileSize || IpadSize ? 'pr-2 pt-3' : 'pr-4 pt-8'">
        <v-hover v-slot="{ hover }">
          <v-card outlined class="rounded-lg px-2 custom-card pb-2"  height="100%" width="188px" :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;" :href="item.path" @click="gotoShopDetail(item)">
            <v-row dense align="center" justify="center">
              <v-col cols="12" md="12" class="px-4  mt-3 mb-4 pb-0 " >
                <v-avatar v-if="item.shop_logo !== ''"  rounded size="140">
                <v-img  v-lazyload :src="item.shop_logo"  height="100%"
                        width="100%"
                        contain style="width: 100%;"/>
                </v-avatar>
                <v-avatar v-else rounded size="140">
                <v-img :src="require('@/assets/ImageINET-Marketplace/Shop/Store.png')" />
                </v-avatar>
              </v-col>
                <v-col cols="12" md="12" class="px-0 pb-0 pb-0" >
            <v-row no-gutters justify="center" style="margin-left:  -5px;">
              <v-tooltip top class="mt-n4">
                <template v-slot:activator="{ on, attrs }">
                  <v-col cols="10" md="10" v-bind="attrs" v-on="on">
                    <p class="mb-0 text-truncate d-inline-block " style="max-width: 125px; font-size: 16px; font-weight: 700;">{{ item.shop_name }}</p>
                  </v-col>
                </template>
                <span>{{ item.shop_name }}</span>
              </v-tooltip>
            </v-row>
          </v-col>
      <!-- <v-row no-gutters justify="center" style="margin-top:  -10px;">
              <v-col cols="2" md="2" class="mb-3 mt-1 pr-0 mr-0 pl-2">
                <v-avatar rounded size="16">
                <v-img  src="@/assets/favourites1.png" />
                </v-avatar>
              </v-col>
              <v-col cols="10" md="10">
                <p class="mt-2 ml-0 pl-0 mb-0 text-truncate d-inline-block " style="max-width: 125px; font-size: 12px; font-weight: 400;">คะแนนร้านค้า : 5.0 / 5.0</p>
              </v-col>
            </v-row> -->
            </v-row>
          </v-card>
        </v-hover>
      </div>
      </v-row>
        </a-row>
        <a-row
          v-else
          :class="MobileSize? 'pt-16 mt-16':''"
          type="flex"
          justify="center"
          style="margin-top: 10%; margin-bottom: 10%"
        >
          <template>
            <a-empty
            v-if="search === '' && search3 === ''"
              :image="require('@/assets/NoGroupShop.png')"
              :image-style="{ height: '150px', marginBottom: '40px' }"
            >
              <h1 slot="description"
              style="
                color: #636363;
                font-size: 18px;
                line-height: 25.2px;
                font-weight: 600;
              ">{{ $t('AllShopsPage.NoShop') }}
              </h1>
              <br />
              <h3 slot="description" style="
                color: #9A9A9A;
                font-size: 16px;
                line-height: 22.4px;
                font-weight: 400;
              ">{{ $t('AllShopsPage.ShopNotFound') }}</h3>
              <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
            </a-empty>
            <a-empty
            v-if="search !== '' || search3 !== ''"
              :image="require('@/assets/searchGroupShop.png')"
              :image-style="{ height: '150px', marginBottom: '40px' }"
            >
              <h1 slot="description"
              style="
                color: #636363;
                font-size: 18px;
                line-height: 25.2px;
                font-weight: 600;
              ">{{ $t('AllShopsPage.ShopNotFound') }}
              </h1>
              <br />
              <h3 slot="description" style="
                color: #9A9A9A;
                font-size: 16px;
                line-height: 22.4px;
                font-weight: 400;
              ">{{ $t('AllShopsPage.ShopSearchEmpty') }}</h3>
              <!-- <h3 slot="description" style="font-weight: bold;">กรุณาลองใหม่อีกครั้ง</h3> -->
            </a-empty>
          </template>
        </a-row>
      </v-container>
    </v-col>
    <v-row v-if="ProductSearch.length !== 0">
      <v-col cols="12" md="12" sm="12" class="mt-10 mb-0">
        <v-pagination
        color="#3EC6B6"
        v-model="pageNumber"
        :length="pageMax"
        :total-visible="MobileSize ? 5 : 7"
        :value="pageNumber"
        class="paginationStyle"
        @input="pageChange($event)"
        ></v-pagination>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import { Decode } from '@/services'
import { Row, Empty } from 'ant-design-vue'
const productResult = []
for (let i = 0; i < 48; i++) {
  productResult.push({
    product_id: i,
    name: `Data Title product ${i}`,
    price: ` ${(i + 1) * 100}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  metaInfo () {
    return {
      title: this.textSearch + ' | Nex Gen Commerce',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        { vmid: 'description', name: 'description', content: this.textSearch + ' | Nex Gen Commerce' },
        { property: 'og:site_name', vmid: 'og:site_name', content: 'https://nexgencommerce.one.th/' },
        { property: 'og:title', vmid: 'og:title', content: this.textSearch + ' | Nex Gen Commerce' },
        { property: 'og:description', vmid: 'og:description', content: this.textSearch + '| Nex Gen Commerce' },
        { property: 'og:type', vmid: 'og:type', content: 'website' },
        { property: 'og:url', vmid: 'og:url', content: 'https://nexgencommerce.one.th/' }
        // { property: 'og:image', name: 'image', content: this.primaryImage },
        // { property: 'og:image:width', content: '640' },
        // { property: 'og:image:height', content: '480' }
      ]
    }
  },
  components: {
    'a-row': Row,
    'a-empty': Empty
  },
  data () {
    return {
      itemsPage: [
        {
          text: this.$t('AllShopsPage.Home'),
          disabled: false,
          href: '/'
        },
        {
          text: this.$t('AllShopsPage.AllShops'),
          disabled: true,
          href: 'allShop'
        }
      ],
      search: '',
      search2: null,
      search3: '',
      filterName: [
        { id: 0, text: 'ค่าเริ่มต้น' },
        { id: 1, text: '1 คะแนน' },
        { id: 2, text: '2 คะแนน' },
        { id: 3, text: '3 คะแนน' },
        { id: 4, text: '4 คะแนน' },
        { id: 5, text: '5 คะแนน' }
      ],
      Rules: {
        Email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ]
      },
      productResult,
      textSearch: '',
      productSearch: [],
      shopSearch: [],
      overlay2: true,
      pageMax: null,
      current: 1,
      pageNumber: 1,
      pageSize: 5,
      pathShopDetail: '',
      path: process.env.VUE_APP_DOMAIN,
      productCount: null,
      shopCount: null,
      PathImage: process.env.VUE_APP_IMAGE,
      // shopSearchShow: [],
      selectPrice: '',
      shopID: '',
      ADC_DESC: '',
      NoDataInSearch: false,
      items: [
        {
          text: this.$t('AllShopsPage.Home'),
          disabled: false,
          href: '/'
        },
        {
          text: this.$t('AllShopsPage.SearchResults'),
          disabled: true,
          href: ''
        }
      ],
      itemPrice: [
        { text: 'ทั้งหมด', value: '-' },
        { text: 'ราคา: จากน้อยไปมาก', value: 'lowToHigh' },
        { text: 'ราคา: จากมากไปน้อย', value: 'HighToLow' }
      ],
      toggle_exclusive: 0,
      showSkeletonLoader: false,
      productSearchAll: [],
      valueSearch: ''
    }
  },
  async created () {
    this.oneData = []
    this.showSkeletonLoader = false
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    // this.$EventBus.$emit('searchdata')
    // this.$EventBus.$emit('getPath')
    if (this.$route.query.page !== undefined) {
      this.pageNumber = parseInt(this.$route.query.page)
    }
    // await this.getResulAlltShop()
    await this.getResultShop()
  },
  watch: {
    async $route (to, from) {
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page
      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          this.pageNumber = parseInt(this.$route.query.page)
          this.pageChange(this.pageNumber)
          this.getResultShop()
        }
      }
    },
    search (val) {
      this.valueSearch = val
      this.pageNumber = 1
      this.pageChange(this.pageNumber)
      this.getResultShop()
      // this.valueSearch = newVal || this.valueSearch

      // setTimeout(() => {
      //   this.getResultShop(this.valueSearch)
      // }, 200)
    }
    // productSearch (val) {
    //   console.log(val, 'eiei')
    // }
  },
  computed: {
    // pageNumber: {
    //   get () {
    //     return this.current || 1
    //   },
    //   set (newPage) {
    //     // You could alternatively call your API here if you have serverside pagination
    //     this.current = newPage
    //     this.getResultShop()
    //     window.scrollTo(0, 0)
    //   }
    // },
    ProductSearch () {
      var x
      if (this.search !== '') {
        x = this.productSearch.filter(e => {
          return e.shop_name.toLowerCase().includes(this.search.toLowerCase())
        })
      } else if (this.search3 !== '' && this.search3 !== 0) {
        x = this.productSearch.filter(e => {
          return parseInt(e.shop_rate_score) === parseInt(this.search3)
        })
      } else {
        return this.productSearch
      }
      return x
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    // paginated () {
    //   return this.productSearch.slice(this.indexStart, this.indexEnd)
    // },
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  methods: {
    async getResultShop () {
      // this.valueSearch = search
      // console.log(this.valueSearch, 'seee this.valueSearch')
      this.$store.commit('openLoader')
      var dataAllShop
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId = ''
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        }
        dataAllShop = {
          role_user: dataRole.role,
          company_id: companyId !== '' ? companyId.company.company_id : '-1',
          page: this.pageNumber,
          offset: '48',
          search: this.valueSearch
        }
      } else {
        dataAllShop = {
          role_user: 'ext_buyer',
          company_id: '-1',
          page: this.pageNumber,
          offset: '48',
          search: this.valueSearch
        }
      }
      await this.$store.dispatch('actionListAllShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateListAllShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.overlay2 = false
        this.productSearch = response.data.list_shop
        // console.log('show productSearch', this.productSearch)
        this.productSearch.forEach(element => {
          if (element.shop_id !== undefined) {
            this.pathShopDetail = this.path + 'shoppage/' + encodeURIComponent(element.shop_name.replace(/\s/g, '-') + '-' + element.shop_id)
            element.path = this.pathShopDetail
          }
        })
        this.pageMax = parseInt(response.data.total_page)
        this.shopCount = response.data.total_shop
        this.showSkeletonLoader = true
        window.scrollTo(0, 0)
      } else {
        this.$store.commit('closeLoader')
        this.overlay2 = false
        this.productSearch = []
        this.NoDataInSearch = true
        this.showSkeletonLoader = true
      }
    },
    async getResulAlltShop () {
      this.$store.commit('openLoader')
      var dataAllShop
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId = ''
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        }
        dataAllShop = {
          role_user: dataRole.role,
          company_id: companyId !== '' ? companyId.company.company_id : '-1',
          page: 'all'
          // offset: '3'
        }
      } else {
        dataAllShop = {
          role_user: 'ext_buyer',
          company_id: '-1',
          page: 'all'
        }
      }
      await this.$store.dispatch('actionListAllShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateListAllShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.productSearchAll = response.data.list_shop
        window.scrollTo(0, 0)
      } else {
        this.$store.commit('closeLoader')
        this.productSearchAll = []
      }
    },
    gotoShopDetail (val) {
      const shopCleaned = encodeURIComponent(val.shop_name.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${val.shop_id}` }).catch(() => {})
    },
    pageChange (val) {
      // window.scrollTo(0, 0)
      this.page = val
      this.$router.push(`/allShop?page=${val}`).catch(() => {})
    }
    // async pageChange () {
    //   this.showSkeletonLoader = false
    //   var dataAllShop
    //   if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
    //     var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //     var companyId = ''
    //     if (localStorage.getItem('SetRowCompany') !== null) {
    //       companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //     }
    //     dataAllShop = {
    //       role_user: dataRole.role,
    //       company_id: companyId !== '' ? companyId.company.company_id : '-1',
    //       page: this.pageNumber
    //     }
    //   } else {
    //     dataAllShop = {
    //       role_user: 'ext_buyer',
    //       company_id: '-1',
    //       page: this.pageNumber
    //     }
    //   }
    //   await this.$store.dispatch('actionListAllShop', dataAllShop)
    //   var response = await this.$store.state.ModuleShop.stateListAllShop
    //   // console.log('Result Search ======>', response)
    //   if (response.code === 200) {
    //     this.overlay2 = false
    //     this.productSearch = response.data.list_shop
    //     this.pageMax = parseInt(response.data.total_page)
    //     this.shopSearchShow = response.data.list_shop
    //     this.shopCount = response.data.total_shop
    //     window.scrollTo(0, 0)
    //     this.showSkeletonLoader = true
    //   } else {
    //     this.overlay2 = false
    //     this.productSearch = []
    //     this.shopSearchShow = []
    //     this.NoDataInSearch = true
    //     this.showSkeletonLoader = true
    //   }
    // }
  }
}
</script>

<style scoped>
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #27AB9C !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 12px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 24px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 24px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    /* margin-bottom: 24px; */
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
</style>
