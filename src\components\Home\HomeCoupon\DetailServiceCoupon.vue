<template>
  <div>
    <!-- <v-col> -->
    <v-breadcrumbs :items="itemsPage" class="breadcrumbsPadding" :class="MobileSize ? 'mb-12' : 'mb-10'" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          <span style="z-index:1;" :style="{ color: item.disabled === true ? '#27AB9C' : '#636363', 'font-size': '16px' }">{{ item.text}}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container grid-list-xs>
      <div v-if="!MobileSize">
        <div :class="IpadSize ? 'mx-4 mt-3' : 'mt-5'">
          <v-img
            src="@/assets/ConponNGC/serviceCoupon/bannerCoupon1.png"
            width="2000"
            height="204"
            contain
            :style="IpadSize ? 'margin-top: -11vw;' : IpadProSize ? 'margin-top: -6vw;' : 'margin-top: -3vw;'"
            v-if="$i18n.locale === 'th'"
          ></v-img>
          <v-img
            src="@/assets/ConponNGC/serviceCoupon/bannerCouponEN.png"
            width="2000"
            height="204"
            contain
            :style="IpadSize ? 'margin-top: -11vw;' : IpadProSize ? 'margin-top: -6vw;' : 'margin-top: -3vw;'"
            v-else
          ></v-img>
        </div>
        <div>
          <div v-if="detailPlatform.length !== 0" :class="!IpadSize && !IpadProSize ? 'mx-7' : ''" :style="IpadSize ? 'margin-top: -3vw;' : IpadProSize ? '' : 'margin-top: 1.5vw;'">
            <v-card outlined style="border: 0;" class="mb-5 pb-5" :style="IpadProSize || IpadSize ? 'border-radius: 2vw;' : 'border-radius: .5vw;'">
              <v-row class="pa-3 d-flex">
                <v-col cols="12" class="d-flex align-center">
                  <img class="mr-2" src="@/assets/ConponNGC/serviceCoupon/iconCoupon1.jpg" width="26" height="26">
                  <span style="font-size: large;"><b>{{ $t('Coupon.GeneralDiscount') }}</b></span>
                  <v-spacer></v-spacer>
                  <div class="d-flex align-center">
                    <v-btn icon @click="prevPagePlatfrom" :disabled="currentPagePlatfrom === 0">
                      <v-icon large color="#27AB9C">mdi-arrow-left-drop-circle</v-icon>
                    </v-btn>
                    <v-btn icon @click="nextPagePlatfrom" :disabled="disableBtn">
                      <v-icon large color="#27AB9C">mdi-arrow-right-drop-circle</v-icon>
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
              <v-row class="pa-5" :style="IpadSize ? 'margin-top: -4vw;' : 'margin-top: -2vw;'">
                <v-col :cols="IpadSize ? 6 : IpadProSize ? 4 : 3" v-for="(item, index) in paginatedPlatefrom" :key="index" class="platfrom">
                  <div
                    :style="IpadSize ? '' : IpadProSize ? '' : ''"
                    class="coupon-container"
                  >
                    <img
                      :src="item.coupon_type === 'free_shipping'
                        ? require('../../../assets/ConponNGC/serviceCoupon/BgCoupon_free.png')
                        : require('../../../assets/ConponNGC/serviceCoupon/BgCoupon_dis.png')"
                      class="coupon-image"
                      alt="coupon"
                    />

                    <div class="coupon-content" :style="IpadSize ? 'top: 9px; left: 22px;' : 'top: 0; left: 0;'">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span style="color: #269AFD; white-space: nowrap;" :style="IpadSize ? 'font-size: x-large;' : 'font-size: large;'" v-bind="attrs" v-on="on"><b>{{ substring(item.coupon_name) }}</b></span>
                        </template>
                        <span>{{ item.coupon_name }}</span>
                      </v-tooltip><br>
                      <span :style="IpadSize ? 'font-size: large' : ''">{{ $t('Coupon.MinSpend') }} {{ item.spend_minimum }} .- </span>
                      <span v-if="item.discount_maximum !== -1" :style="IpadSize ? 'font-size: large' : ''"> {{ $t('Coupon.MaxSpend') }} {{ item.discount_maximum }} .-</span><br>
                      <div class="d-flex">
                        <div class="mr-2">
                          <span style="color: #636363;" v-if="item.use_enddate !== ''" :style="IpadSize ? 'font-size: large' : ''">{{ $t('Coupon.ValidUntil') }} {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                          <span style="color: #636363;" v-else :style="IpadSize ? 'font-size: large' : ''">{{ $t('Coupon.NoExpiry') }}</span><br>
                        </div>
                        <div>
                          <span style="color: #269afd; text-decoration: underline; cursor: pointer;" @click="openDialogCondition(item)">{{ $t('Coupon.ViewConditions') }}</span>
                        </div>
                      </div>
                      <div class="mt-2" style="display: grid;">
                        <v-btn v-if="item.is_collected === 'N'" color="#FEC81C" rounded style="color: #fff;" :style="IpadSize ? 'width: 34vw; text-transform: none;' : 'text-transform: none;'" @click="collectCouponOne(item.id)"><b>{{ $t('Coupon.Claim') }}</b></v-btn>
                        <v-btn v-else disabled color="#F56E22" style="color:white;" :style="IpadSize ? 'width: 34vw; text-transform: none;' : 'text-transform: none;'" rounded>{{ $t('Coupon.Claimed') }}</v-btn>
                      </div>
                    </div>
                  </div>
                </v-col>
              </v-row>
              <div class="mt-1 mb-2 mx-5" style="display: grid;">
                <v-btn :disabled="collectStatus === 'Y'" rounded :style="collectStatus === 'Y' ? '' : 'background-image: linear-gradient(to left, #ffaf32, #ff5349); color: #fff;'" @click="collectCouponAll()">
                  <v-icon medium class="mr-2">mdi-tag-outline</v-icon>
                  <span style="text-transform: none;">
                    <b>{{ $t('Coupon.ClaimAll') }}</b>
                  </span>
                </v-btn>
              </div>
              <!-- <div>
                <div class="d-flex align-center">
                  <v-container class="d-flex flex-wrap align-end" style="margin: auto;" :style="IpadProSize ? 'margin-left: 3.5vw; width: 90vw;' : IpadSize ? 'margin-left: 7.5vw;' : 'width: 80vw; margin-left: 4.5vw;'">
                    <v-row v-for="(item, index) in paginatedPlatefrom" :key="index" class="platfrom">
                      <v-col :class="item.coupon_type === 'free_shipping' ? 'couponIMGDeskFree' : 'couponIMGDeskDis'" style="margin-bottom: 1.5vw;" :style="IpadSize ? 'width: 40vw;' : IpadProSize ? 'width: 29vw;' : 'width: 20vw;'">
                        <v-col cols="12" md="12" class="pt-0" :class="IpadSize? 'pl-0': IpadProSize? 'pl-0': 'pl-0'" style="max-width: 320px !important;">
                          <v-row no-gutters>
                            <v-col class="pr-12 pl-5 pt-1">
                              <v-row>
                                <v-col cols="12" align="start">
                                  <v-tooltip top>
                                    <template v-slot:activator="{ on, attrs }">
                                      <span style="color: #269AFD; font-size: large; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substring(item.coupon_name) }}</b></span>
                                    </template>
                                    <span>{{ item.coupon_name }}</span>
                                  </v-tooltip><br>
                                  <span>ขั้นต่ำ {{item.spend_minimum}} .- </span>
                                  <span v-if="item.discount_maximum !== -1"> ไม่เกิน {{item.discount_maximum}} .-</span><br>
                                  <span style="color: #636363;" v-if="item.use_enddate !== ''">ใช้ได้ถึงวันที่ {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                                  <span style="color: #636363;" v-else>ไม่มีวันหมดอายุใช้งาน</span><br>
                                  <div class="mt-2" style="display: grid;">
                                    <v-btn v-if="item.is_collected === 'N'" color="#FEC81C" rounded style="color: #fff;" @click="collectCouponOne(item.id)"><b>เก็บ</b></v-btn>
                                    <v-btn v-else disabled color="#F56E22" style="color:white;" rounded>เก็บแล้ว</v-btn>
                                  </div>
                                </v-col>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-col>
                    </v-row>
                  </v-container>
                </div>
                <div class="mt-1 mb-2 mx-10" style="display: grid;">
                  <v-btn :disabled="collectStatus === 'Y'" rounded :style="collectStatus === 'Y' ? '' : 'background-image: linear-gradient(to left, #ffaf32, #ff5349); color: #fff;'" @click="collectCouponAll()">
                    <v-icon medium class="mr-2">mdi-tag-outline</v-icon>
                    <b>เก็บทั้งหมด</b>
                  </v-btn>
                </div>
              </div> -->
            </v-card>
          </div>
          <div v-else class="mx-4 mt-5">
            <v-card class="mb-5 pb-5" :style="IpadProSize || IpadSize ? 'border-radius: 2vw;' : 'border-radius: .5vw;'">
              <v-row class="pa-3 d-flex">
                <v-col cols="12" class="d-flex align-center">
                  <img class="mr-2" src="@/assets/ConponNGC/serviceCoupon/iconCoupon1.jpg" width="26" height="26">
                  <span style="font-size: large;"><b>{{ $t('Coupon.GeneralDiscount') }}</b></span>
                </v-col>
                <v-card-text class="d-flex align-center flex-column">
                  <v-img
                    src="@/assets/ConponNGC/serviceCoupon/imgNoCoupon_platfrom.png"
                    width="173"
                    height="204"
                    contain
                  ></v-img>
                  <span class="mt-2" style="color: #9A9A9A;" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">{{ $t('Coupon.NoGeneralDiscount') }}</span>
                </v-card-text>
              </v-row>
            </v-card>
          </div>
        </div>
        <div>
          <div v-if="detailShop.length !== 0" :class="!IpadSize && !IpadProSize ? 'mx-7' : ''" :style="IpadProSize ? 'margin-top: 3vw;' : 'margin-top: 1vw;'">
            <v-card outlined style="border: 0;" class="mb-5 pb-5" :style="IpadProSize || IpadSize ? 'border-radius: 2vw;' : 'border-radius: .5vw;'">
              <v-row class="pa-3">
                <v-col cols="12" class="d-flex align-center">
                  <img class="mr-2" src="@/assets/ConponNGC/serviceCoupon/iconCoupon2.png" width="26" height="26">
                  <span style="font-size: large;"><b>{{ $t('Coupon.StoreSpecific') }}</b></span>
                  <v-spacer></v-spacer>
                  <div class="d-flex align-center">
                    <v-btn icon @click="prevPageShop" :disabled="currentPageShop === 0">
                      <v-icon large color="#27AB9C">mdi-arrow-left-drop-circle</v-icon>
                    </v-btn>
                    <v-btn icon @click="nextPageShop" :disabled="(currentPageShop + 1) * 4 >= detailShop.length">
                      <v-icon large color="#27AB9C">mdi-arrow-right-drop-circle</v-icon>
                    </v-btn>
                  </div>
                </v-col>
              </v-row>
              <v-row class="pa-5" :style="IpadSize ? 'margin-top: -4vw;' : 'margin-top: -2vw;'">
                <v-col :cols="IpadSize ? 12 : 6" class="d-flex justify-center" style="gap: 1.5vw" v-for="(item, index) in paginatedShop" :key="index">
                  <v-card class="d-flex align-center pa-5" outlined style="border: solid #F7F7F7; background-color: #FAFAFA;" :style="IpadSize ? 'gap: 3vw; height: 20vw; width: 80vw; border-radius: 1vw;' : IpadProSize ? 'gap: 3vw; height: 15vw; width: 45vw;' : 'gap: 2vw; border-radius: .5vw; width: 45vw;;'">
                    <div :class="IpadSize ? 'ml-3' : ''">
                      <v-avatar @click="gotoShopDetail(item.shop_name, item.seller_shop_id)" :size="MobileSize ? 90 : 100" tile v-if="item.shop_logo !== ''" width="80"><img :src="item.shop_logo" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 1vw;"></v-avatar>
                      <v-avatar @click="gotoShopDetail(item.shop_name, item.seller_shop_id)" :size="MobileSize ? 90 : 100" tile v-else width="80"><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/coupon_image/empty_coupon.png" alt=""></v-avatar>
                    </div>
                    <div :style="IpadSize ? 'width: 43vw;' : IpadProSize ? '' : 'width: 20vw;'">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span style="color: #F15A24; font-size: large; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substringCouponShop(item.coupon_name) }}</b></span>
                        </template>
                        <span>{{ item.coupon_name }}</span>
                      </v-tooltip><br>
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <span v-bind="attrs" v-on="on">{{ $t('Coupon.ForStore') }} {{ substring(item.shop_name) }} {{ $t('Coupon.Only') }}</span>
                        </template>
                        <span>{{ $t('Coupon.ForStore') }} {{ item.shop_name }} {{ $t('Coupon.Only') }}</span>
                      </v-tooltip><br>
                      <span style="color: #636363;">{{ $t('Coupon.MinSpend') }} {{item.spend_minimum}} .- </span>
                      <span style="color: #636363;" v-if="item.discount_maximum !== -1"> {{ $t('Coupon.MaxSpend') }} {{item.discount_maximum}} .-</span><br>
                      <span style="color: #636363;">
                        <span>{{ $t('Coupon.ValidFrom') }} {{ new Date(item.use_startdate).toLocaleDateString("th-TH", { day: "numeric", month: "long" }) }} </span>
                        <span v-if="item.use_enddate !== ''">- {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                      </span>
                    </div>
                    <div class="ma-auto">
                      <v-btn v-if="item.is_collected === 'N'" color="#ff9200" rounded style="color: #fff; text-transform: none;" @click="collectCouponOne(item.id)"><b>{{ $t('Coupon.Claim') }}</b></v-btn>
                      <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" rounded>{{ $t('Coupon.Claimed') }}</v-btn>
                    </div>
                  </v-card>
                </v-col>
              </v-row>
            </v-card>
          </div>
          <div v-else class="mx-4 mt-5">
            <v-card class="mb-5 pb-5" :style="IpadProSize || IpadSize ? 'border-radius: 2vw;' : 'border-radius: .5vw;'">
              <v-row class="pa-3 d-flex">
                <v-col cols="12" class="d-flex align-center">
                  <img class="mr-2" src="@/assets/ConponNGC/serviceCoupon/iconCoupon2.png" width="26" height="26">
                  <span style="font-size: large;"><b>{{ $t('Coupon.StoreSpecific') }}</b></span>
                </v-col>
                <v-card-text class="d-flex align-center flex-column">
                  <v-img
                    src="@/assets/ConponNGC/serviceCoupon/imgNoCoupon_platfrom.png"
                    width="173"
                    height="204"
                    contain
                  ></v-img>
                  <span class="mt-2" style="color: #9A9A9A;" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">{{ $t('Coupon.NoStoreSpecific') }}</span>
                </v-card-text>
              </v-row>
            </v-card>
          </div>
        </div>
      </div>
      <div v-else style="margin-top: -15vw;">
        <v-row>
          <v-col>
            <v-card outlined style="border-radius: 2vw; border: 0;">
              <div class="mx-3 py-2">
                <div>
                  <div class="d-flex align-center">
                    <span><b>{{ $t('Coupon.GeneralDiscount') }}</b></span>
                    <v-spacer></v-spacer>
                    <div v-if="detailPlatform !== 0">
                      <v-btn icon @click="prevPagePlatfrom" :disabled="currentPagePlatfrom === 0">
                        <v-icon color="#27AB9C">mdi-arrow-left-drop-circle</v-icon>
                      </v-btn>
                      <v-btn icon @click="nextPagePlatfrom" :disabled="(currentPagePlatfrom + 1) * 8 >= detailPlatform.length">
                        <v-icon color="#27AB9C">mdi-arrow-right-drop-circle</v-icon>
                      </v-btn>
                    </div>
                  </div>
                  <div v-if="detailPlatform !== 0">
                    <v-container>
                      <v-row>
                        <v-col v-for="(item, index) in paginatedPlatefrom" :key="index" cols="6" md="3">
                          <v-card width="200" class="pa-2 d-inline-block text-truncate" color="#eff3ff" style="border-radius: 3vw;">
                            <v-tooltip top>
                              <template v-slot:activator="{ on, attrs }">
                                <span style="color: #269AFD; font-size: medium; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substring(item.coupon_name) }}</b></span>
                              </template>
                              <span>{{ item.coupon_name }}</span>
                            </v-tooltip><br>
                            <span class="mr-2" style="font-size: x-small;">{{ $t('Coupon.MinSpend') }} {{ item.spend_minimum }} .-</span>
                            <span v-if="item.discount_maximum !== -1" style="font-size: x-small;">{{ $t('Coupon.MaxSpend') }} {{ item.discount_maximum }} .-</span><br>
                            <span style="color: #636363; font-size: x-small;" v-if="item.use_enddate !== ''">
                              {{ $t('Coupon.ValidUntil') }} {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}
                            </span>
                            <span style="color: #636363; font-size: x-small;" v-else>{{ $t('Coupon.NoExpiry') }}</span><br>
                            <span style="color: #269afd; text-decoration: underline; cursor: pointer; font-size: x-small;" @click="openDialogCondition(item)">{{ $t('Coupon.ViewConditions') }}</span>
                            <div class="mt-2" style="display: grid;">
                              <v-btn v-if="item.is_collected === 'N'" color="#FEC81C" rounded style="color: #fff; text-transform: none;" @click="collectCouponOne(item.id)"><b>{{ $t('Coupon.Claim') }}</b></v-btn>
                              <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" rounded>{{ $t('Coupon.Claimed') }}</v-btn>
                            </div>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-container>
                    <div class="mt-3 mb-2" style="display: grid;">
                      <v-btn :disabled="collectStatus === 'Y'" rounded :style="collectStatus === 'Y' ? '' : 'background-image: linear-gradient(to left, #ffaf32, #ff5349); color: #fff;'" @click="collectCouponAll()">
                        <v-icon medium class="mr-2">mdi-tag-outline</v-icon>
                        <span style="text-transform: none;">
                          <b>{{ $t('Coupon.ClaimAll') }}</b>
                        </span>
                      </v-btn>
                    </div>
                  </div>
                  <div v-else class="d-flex flex-column align-center">
                    <v-img
                      src="@/assets/ConponNGC/serviceCoupon/imgNoCoupon_platfrom.png"
                      width="100"
                      height="200"
                      contain
                    ></v-img>
                    <span style="color: #9A9A9A; font-size: medium; margin-top: -10vw;">{{ $t('Coupon.NoGeneralDiscount') }}</span>
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
          <v-col>
            <v-img
              src="@/assets/ConponNGC/serviceCoupon/banner.png"
              width="1500"
              height="204"
              contain
              style="margin-top: -20vw;"
            ></v-img>
          </v-col>
          <v-col style="margin-top: -20vw;">
            <v-card outlined style="border-radius: 2vw; border: 0;" class="pb-3">
              <div class="mx-3 py-2">
                <div>
                  <div class="d-flex align-center">
                    <span><b>{{ $t('Coupon.StoreSpecific') }}</b></span>
                    <v-spacer></v-spacer>
                    <div v-if="detailShop.length !== 0">
                      <v-btn icon @click="prevPageShop" :disabled="currentPageShop === 0">
                        <v-icon color="#27AB9C">mdi-arrow-left-drop-circle</v-icon>
                      </v-btn>
                      <v-btn icon @click="nextPageShop" :disabled="(currentPageShop + 1) * 4 >= detailShop.length">
                        <v-icon color="#27AB9C">mdi-arrow-right-drop-circle</v-icon>
                      </v-btn>
                    </div>
                  </div>
                  <div v-if="detailShop.length !== 0">
                    <v-container>
                      <v-row>
                        <v-col style="display: contents;" v-for="(item, index) in paginatedShop" :key="index" cols="12">
                          <v-card outlined class="pa-3 d-flex align-center justify-center mt-2" style="gap: 12px; height: 40vw; border-radius: 3vw; width: 87vw; border: solid #F7F7F7; background-color: #FAFAFA;">
                            <div>
                              <v-avatar @click="gotoShopDetail(item.shop_name, item.seller_shop_id)" size="70" tile v-if="item.shop_logo !== ''" width="80"><img :src="item.shop_logo" alt="" style="width: 100%; height: 100%; object-fit: contain; border-radius: 3vw;"></v-avatar>
                              <v-avatar @click="gotoShopDetail(item.shop_name, item.seller_shop_id)" size="70" tile v-else width="80"><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/coupon_image/empty_coupon.png" alt=""></v-avatar>
                            </div>
                            <div style="width: 32vw;">
                              <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                  <span style="color: #F15A24; font-size: medium; white-space: nowrap;" v-bind="attrs" v-on="on"><b>{{ substring(item.coupon_name) }}</b></span>
                                </template>
                                <span>{{ item.coupon_name }}</span>
                              </v-tooltip><br>
                              <v-tooltip top>
                                <template v-slot:activator="{ on, attrs }">
                                  <span style="font-size: small;" v-bind="attrs" v-on="on">{{ $t('Coupon.ForStore') }} {{ substring(item.shop_name) }} {{ $t('Coupon.Only') }}</span>
                                </template>
                                <span>{{ $t('Coupon.ForStore') }} {{ item.shop_name }} {{ $t('Coupon.Only') }}</span>
                              </v-tooltip><br>
                              <span style="color: #636363; font-size: x-small;">{{ $t('Coupon.MinSpend') }} {{item.spend_minimum}} .- </span>
                              <span style="color: #636363; font-size: x-small;" v-if="item.discount_maximum !== -1"> {{ $t('Coupon.MaxSpend') }} {{item.discount_maximum}} .-</span><br>
                              <span style="color: #636363; font-size: x-small;">
                                <span>{{ $t('Coupon.ValidOn') }} {{ new Date(item.use_startdate).toLocaleDateString("th-TH", { day: "numeric", month: "long" }) }} </span>
                                <span v-if="item.use_enddate !== ''">- {{ new Date(item.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                              </span>
                            </div>
                            <div>
                              <v-btn v-if="item.is_collected === 'N'" color="#ff9200" small rounded style="color: #fff; text-transform: none;" @click="collectCouponOne(item.id)"><b>{{ $t('Coupon.Claim') }}</b></v-btn>
                              <v-btn v-else disabled color="#F56E22" style="color:white; text-transform: none;" small rounded>{{ $t('Coupon.Claimed') }}</v-btn>
                            </div>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-container>
                  </div>
                  <div v-else class="d-flex flex-column align-center">
                    <v-img
                      src="@/assets/ConponNGC/serviceCoupon/imgNoCoupon_platfrom.png"
                      width="100"
                      height="200"
                      contain
                    ></v-img>
                    <span style="color: #9A9A9A; font-size: medium; margin-top: -10vw;">{{ $t('Coupon.NoStoreSpecific') }}</span>
                  </div>
                </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-container>
    <v-dialog v-model="dialogConditionCoupon" width="500px" content-class="elevation-0">
      <v-card style="border-radius: 24px;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: large; font-weight: 700; color: #FFFFFF;">{{ $t('Coupon.Conditions') }}
          </span>
          <v-btn icon dark @click="dialogConditionCoupon = false">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <div
              :style="IpadSize ? '' : IpadProSize ? '' : ''"
              class="coupon-container mt-3 d-flex justify-center d-inline-block text-truncate"
            >
              <img
                :src="item_show.coupon_type === 'free_shipping'
                  ? require('../../../assets/ConponNGC/serviceCoupon/BgCoupon_free.png')
                  : require('../../../assets/ConponNGC/serviceCoupon/BgCoupon_dis.png')"
                class="coupon-image-show"
                alt="coupon"
              />

              <div class="coupon-content-show" :style="MobileSize ? 'top: 20%; left: 10%' : 'top: 15%; left: 25%'">
                <v-tooltip top>
                  <template v-slot:activator="{ on, attrs }">
                    <span style="color: #269AFD; white-space: nowrap;" :style="IpadSize ? 'font-size: large;' : 'font-size: large;'" v-bind="attrs" v-on="on"><b>{{ substringShow(item_show.coupon_name) }}</b></span>
                  </template>
                  <span>{{ item_show.coupon_name }}</span>
                </v-tooltip><br>
                <span :style="IpadSize ? 'font-size: large' : ''">{{ $t('Coupon.MinSpend') }} {{ item_show.spend_minimum }} .- </span>
                <span v-if="item_show.discount_maximum !== -1" :style="IpadSize ? 'font-size: large' : ''"> {{ $t('Coupon.MaxSpend') }} {{ item_show.discount_maximum }} .-</span><br>
                <span style="color: #636363;" v-if="item_show.use_enddate !== ''" :style="IpadSize ? 'font-size: large' : ''">{{ $t('Coupon.ValidUntil') }} {{ new Date(item_show.use_enddate).toLocaleDateString("th-TH", { day: "numeric", month: "long", year: "2-digit" }) }}</span>
                <span style="color: #636363;" v-else :style="IpadSize ? 'font-size: large' : ''">{{ $t('Coupon.NoExpiry') }}</span><br>
              </div>
          </div>
        </v-card-text>
        <v-card-text class="mt-2" style="font-size: medium;">
          <span><b>{{ $t('Coupon.PromotionType') }}</b></span><br>
          <span>{{ item_show.coupon_type === 'free_shipping' ? this.$t('Coupon.ShippingDiscount') : this.$t('Coupon.ProductDiscount') }}</span>
          <v-divider class="mb-2"></v-divider>
          <span><b>{{ $t('Coupon.UsageRight') }}</b></span><br>
          <span>{{ $t('Coupon.Usable') }} {{ item_show.quota }} {{ $t('Coupon.Rights') }}</span>
          <v-divider class="mb-2"></v-divider>
          <span><b>{{ $t('Coupon.MaxDiscount') }} </b></span><br>
          <span> {{ item_show.discount_type === 'baht' ? this.$t('Coupon.MaxReduce') + ' ' + item_show.discount_amount + this.$t('Coupon.Currency') : item_show.discount_type === 'percent' && item_show.discount_maximum !== -1 ? this.$t('Coupon.MaxReduce') + item_show.discount_maximum + this.$t('Coupon.Currency') : this.$t('Coupon.Unlimited') }}</span>
          <v-divider class="mb-2"></v-divider>
          <span v-if="ShopName === ''"><b>{{ $t('Coupon.ParticipatingStores') }}</b></span><br v-if="ShopName === ''">
          <span v-if="ShopName !== ''"><b>{{ $t('Coupon.AllStores') }} <b>{{ shopList.length }}</b> {{ $t('Coupon.Shop') }}</b></span><br v-if="ShopName !== ''">
          <span v-if="ShopName === ''">{{ $t('Coupon.ValidEverywhere') }}</span>
          <span v-else-if="ShopName !== ''">
            <div v-for="(shop, index) in displayedShops" :key="index" style="font-size: 14px;">
              • {{ shop }}
            </div>
            <v-btn text small color="primary" @click="toggleShowAll" v-if="shopList.length > limit">
              {{ showAll ? this.$t('Coupon.ShowLess') : this.$t('Coupon.ShowMore') }}
            </v-btn>
          </span>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import VueHorizontalList from 'vue-horizontal-list'
export default {
  // components: {
  //   VueHorizontalList
  // },
  data () {
    return {
      itemsPage: [
        {
          text: this.$t('Headers.Home'),
          disabled: false,
          href: '/'
        },
        {
          text: this.$t('Coupon.Coupon'),
          disabled: true,
          href: 'allShop'
        }
      ],
      detail: [],
      detailPlatform: [],
      detailShop: [],
      roleUser: '',
      collect: '',
      coupon_id: null,
      optionsCard: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 3 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 3 },
          { start: 1200, end: 1300, size: 4 },
          { size: 4 }
        ],
        list: {
          windowed: 1200,
          padding: 0
        },
        item: {
          class: '',
          padding: 0
        },
        position: {
          start: 0
        }
      },
      optionsCardShop: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 2 },
          { start: 992, end: 1200, size: 2 },
          { start: 1200, end: 1300, size: 3 },
          { size: 3 }
        ],
        list: {
          windowed: 1200,
          padding: 0
        },
        item: {
          class: '',
          padding: 0
        },
        position: {
          start: 0
        }
      },
      typeCoupon: '',
      statusCollect: '',
      collectStatus: '',
      currentPagePlatfrom: 0,
      currentPageShop: 0,
      dialogConditionCoupon: false,
      item_show: {},
      ShopName: '',
      showAll: false,
      limit: 10
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    paginatedPlatefrom () {
      const start = this.IpadSize || this.IpadProSize ? this.currentPagePlatfrom * 6 : this.currentPagePlatfrom * 8
      return this.IpadSize || this.IpadProSize ? this.detailPlatform.slice(start, start + 6) : this.detailPlatform.slice(start, start + 8)
    },
    paginatedShop () {
      const start = this.currentPageShop * 4
      return this.detailShop.slice(start, start + 4)
    },
    disableBtn () {
      const itemsPerPage = this.IpadSize || this.IpadProSize ? 6 : 8
      return (this.currentPagePlatfrom + 1) * itemsPerPage >= this.detailPlatform.length
    },
    shopList () {
      return this.ShopName.split(',').map(name => name.trim())
    },
    displayedShops () {
      return this.showAll ? this.shopList : this.shopList.slice(0, this.limit)
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    if (localStorage.getItem('roleUser') !== null) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    } else {
      this.roleUser = {
        role: 'ext_buyer'
      }
    }
    await this.listCouponPlatfrom()
    await this.listCouponShop()
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DetailServiceCouponMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/DetailServiceCoupon' }).catch(() => {})
      }
    }
  },
  methods: {
    toggleShowAll () {
      this.showAll = !this.showAll
    },
    openDialogCondition (item) {
      this.dialogConditionCoupon = true
      this.showAll = false
      this.item_show = item
      if (item.shop_name !== '') {
        this.ShopName = item.shop_name
      } else {
        this.ShopName = ''
      }
    },
    gotoShopDetail (shopName, shopId) {
      var name = shopName.replace(/\s+/g, '-')
      this.$router.push({ path: `/shoppage/${name}-${shopId}?page=1` }).catch(() => {})
    },
    nextPagePlatfrom () {
      if (this.IpadSize || this.IpadProSize) {
        if ((this.currentPagePlatfrom + 1) * 6 < this.detailPlatform.length) {
          this.currentPagePlatfrom++
        }
      } else {
        if ((this.currentPagePlatfrom + 1) * 8 < this.detailPlatform.length) {
          this.currentPagePlatfrom++
        }
      }
    },
    prevPagePlatfrom () {
      if (this.currentPagePlatfrom > 0) {
        this.currentPagePlatfrom--
      }
    },
    nextPageShop () {
      if ((this.currentPageShop + 1) * 4 < this.detailShop.length) {
        this.currentPageShop++
      }
    },
    prevPageShop () {
      if (this.currentPageShop > 0) {
        this.currentPageShop--
      }
    },
    async listCouponPlatfrom () {
      this.typeCoupon = 'platform'
      await this.detailCoupon(this.typeCoupon)
      this.detailPlatform = this.detail
      this.collectStatus = this.statusCollect
    },
    async listCouponShop () {
      this.typeCoupon = 'all_shop'
      await this.detailCoupon(this.typeCoupon)
      this.detailShop = this.detail
    },
    async detailCoupon (typeCoupon) {
      this.$store.commit('openLoader')
      var data = {
        role_user: this.roleUser.role,
        company_id: -1,
        customer_id: -1,
        type: typeCoupon,
        shop_id: -1,
        orderBy: 'uncollected_first',
        is_collected: 'all',
        limit: '',
        page: ''
      }
      await this.$store.dispatch('actionsDetailServiceCoupon', data)
      var res = this.$store.state.ModuleHompage.stateDetailServiceCoupon
      if (res.code === 200) {
        this.detail = res.data.coupon
        this.statusCollect = res.data.is_collect_all
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    collectCouponOne (idCoupon) {
      this.collect = ''
      this.collectCoupon(this.collect, idCoupon)
    },
    collectCouponAll () {
      this.collect = 'platform'
      this.coupon_id = -1
      this.collectCoupon(this.collect, this.coupon_id)
    },
    async collectCoupon (collect, idCoupon) {
      if (localStorage.getItem('oneData') === null) {
        this.$router.push({ path: '/Login' }).catch(() => {})
      }
      this.$store.commit('openLoader')
      var data = {
        collect_all: collect,
        coupon_id: idCoupon,
        role_user: this.roleUser.role,
        company_id: -1,
        customer_id: -1
      }
      await this.$store.dispatch('actionCollectCoupon', data)
      var res = this.$store.state.ModuleShop.stateCollectCoupon
      if (res.code === 200) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
          icon: 'success',
          text: 'เก็บคูปองสำเร็จ'
        })
        this.listCouponPlatfrom()
        this.listCouponShop()
        this.$store.commit('closeLoader')
      } else if (res.message === 'This user is Unauthorized') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาลงทะเบียน หรือเข้าสู่ระบบ'
        })
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    substring (data) {
      if (this.MobileSize || this.IpadSize || this.IpadProSize) {
        return data.length > 15 ? data.substring(0, 15) + '...' : data
      } else {
        return data.length > 20 ? data.substring(0, 20) + '...' : data
      }
    },
    substringCouponShop (data) {
      if (this.MobileSize || this.IpadSize || this.IpadProSize) {
        return data.length > 20 ? data.substring(0, 20) + '...' : data
      } else {
        return data.length > 40 ? data.substring(0, 40) + '...' : data
      }
    },
    substringShow (data) {
      if (this.MobileSize || this.IpadSize || this.IpadProSize) {
        return data.length > 20 ? data.substring(0, 20) + '...' : data
      } else {
        return data.length > 25 ? data.substring(0, 25) + '...' : data
      }
    }
  }
}
</script>

<style scoped>
/* .backIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/background.png');
  background-size: cover;
  padding: 1%;
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  background-size: cover;
  padding: 1%;
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  background-size: contain;
} */
 .couponIMGMobile {
  background-image: url('../../../assets/ConponNGC/serviceCoupon/bgCouponMobile.png');
  background-size: contain;
 }
 .coupon-container {
  position: relative;
  overflow: hidden;
}

.coupon-image {
  width: 100%;
  height: auto;
  display: block;
}

.coupon-image-show {
  /* width: 70%; */
  height: auto;
  display: block;
}

.coupon-content {
  position: absolute;
  /* top: 0;
  left: 0; */
  padding: 1vw;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}

.coupon-content-show {
  position: absolute;
  /* top: 0;
  left: 0; */
  padding: 1vw;
  height: 100%;
  box-sizing: border-box;
}
/* .couponIMGDeskDis{
  background-image: url('../../../assets/ConponNGC/serviceCoupon/BgCoupon_dis.png');
  padding: 1%;
  height: auto;
}
.couponIMGDeskFree{
  background-image: url('../../../assets/ConponNGC/serviceCoupon/BgCoupon_free.png');
  padding: 1%;
  height: auto;
} */
.couponAll {
  cursor: pointer;
}
.couponAll:hover {
  transform: scale(1.02) !important;
}
.progress-gradient {
width: 100%;
height: 100%;
border-radius: 48px;
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.vProgressLinearDesk {
  max-width: 4.5vw;
}
.vProgressLinearIped {
  max-width: 8vw;
}
.vProgressLinearMobile {
  max-width: 23vw;
}

@media only screen and (min-width: 1200px) {
  .row.platfrom {
    flex: 0 !important;
    margin: -5px !important;
  }
}

.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #27AB9C !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
</style>
