{"data": [{"title": "ชื่อสินค้า", "content": ["การตั้งชื่อสินค้า ควรเริ่มจากชื่อยี่ห้อสินค้า ประเภทสินค้า รุ่น สี ขนาด", "ชื่อสินค้าควรตั้งให้ตรงความจริง เเละไม่ควรคัดลอกมาจากผู้อื่น"]}, {"title": "รหัส sku", "content": ["การกำหนดรหัส sku ควรเริ่มจากการกำหนดรหัสของร้าน-รหัสสินค้า ยกตัวอย่างเช่น newshop-0001"]}, {"title": "รายละเอียดสินค้า", "content": ["รายละเอียดสินค้าควรประกอบด้วย ขนาดความกว้าง ความยาว ความสูง วิธีการใช้งานสินค้า เเละการรับประกันสินค้า (หากมี)"]}, {"title": "ราคาสินค้า", "content": ["ราคาสินค้า ควรเป็นราคาที่รวมภาษีมูลค่าเพิ่มเเล้ว ซึ่งควรตั้งราคาขายปลีกให้คงที่ เพื่อสร้างประสบการณ์ที่ดีให้เเก่ผู้ซื้อ"]}, {"title": "จำนวนสินค้าคงคลัง", "content": ["เเนะนำให้ท่านตรวจสอบสินค้าคงคลัง (stock) เเละระบุจำนวนให้ตรงตามจำนวนสินค้าพร้อมขาย", "หากสินค้าของท่านหมด ให้กรอกจำนวนสินค้าคงคลังเป็น 0"]}, {"title": "น้ำหนักของสินค้า", "content": ["ควรใส่น้ำหนักที่รวมหีบห่อพร้อมส่งอย่างถูกต้อง (ระบบจะคำนวนค่าส่งอัตโนมัติตามที่ระบุ)"]}, {"title": "ขนาดของพัสดุ", "content": ["กรุณาใส่ขนาดของพัสดุที่ไม่ใช่ขนาดของสินค้าเพื่อให้การจัดส่งหรือการคำนวนมีความเเม่นยำ", "เเนะนำให้ตรวจสอบขนาดให้ตรงตามข้อจำกัดของเเต่ละบริษัทขนส่งด้วย"]}]}