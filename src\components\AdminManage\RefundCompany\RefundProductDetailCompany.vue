<template lang="html">
  <div :class="MobileSize ? 'mt-3' : ''">
    <!-- Mobile -->
    <v-container v-if="MobileSize || IpadSize" grid-list-xl>
      <v-card outlined>
      <v-card-title v-if="MobileSize" class="px-0"><v-icon color="#27AB9C" class="mr-2" @click="backtoRefundList()">mdi-chevron-left</v-icon> รายการคืนสินค้าของฉัน</v-card-title>
      <v-row justify="center" class="my-4">
        <h2 style="font-weight: 700; font-size: 20px;"> รายละเอียดการคืนสินค้า</h2>
      </v-row>
      <v-row no-gutters class="mx-4">
        <v-col :cols="IpadSize || MobileSize ? 12 : 8" :class="IpadSize || MobileSize ? 'pb-0' : ''">
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-2' : 'mb-2 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.payment_transaction_number }}</span>
            <span  v-if="!MobileSize"> | </span>
            <v-chip class="ma-2" :color="getColor(items.status_refund)" small :text-color="getTextColor(items.status_refund)">
              {{ getStatus(items.status_refund) }}
            </v-chip>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-4' : 'mb-4 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.user_name }}</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.buy_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ชำระเงิน : </span>
            <span v-if="items.paid_datetime !== null" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.paid_datetime === 'ชำระเงินแบบเครดิตเทอม' ? items.paid_datetime : new Date(items.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
            <span v-else :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">-</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ยกเลิก : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.waiting_time).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ยกเลิกโดย : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">เหตุผลในการคืน : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.reason }}</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">จำนวนเงินที่คืน : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ Number(items.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">การคืนสินค้า : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ getStatusResult(items.reject_detail[0].status_refund) }}</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">เหตุผลในการไม่อนุมัติ : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.reject_detail[0].seller_comment }}</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ดำเนินการโดย : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ขาย</span>
          </div>
          <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ไม่อนุมัติ : </span>
            <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.reject_detail[0].reject_time).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
          </div>
        </v-col>
        <v-col :cols="MobileSize || IpadSize ? 12 : 4">
          <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ส่ง : {{ items.sent_date === null ? '-' : new Date(items.sent_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</v-col>
          <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" :style="{ 'color': items.buyer_received_status === 'refund' ? '#E9A016' : items.buyer_received_status === 'received' ? '#1AB759' : items.buyer_received_status === 'not_received' ? '#D1392B' : '#333333' }"><span style="color: #333333;">สถานะ :</span> {{ items.buyer_received_status === 'refund' ? 'รออนุมัติ' : items.buyer_received_status === 'received' ? 'ผู้ซื้อได้รับสินค้าเรียบร้อยแล้ว' : items.buyer_received_status === 'not_received' ? 'ผู้ซื้อยังไม่ได้รับสินค้า' : '-' }}</v-col>
          <v-col cols="12" md="12" class="mb-3 pa-0 ma-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่รับ : {{ items.received_date === null ? '-' : new Date(items.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</v-col>
        </v-col>
        <v-col cols="12">
          <v-card outlined>
            <v-container grid-list-lg>
              <v-row no-gutters>
                <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6">
                  <v-row no-gutters>
                    <v-col cols="12">
                      <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</p>
                    </v-col>
                    <v-col cols="12" class="mb-4 ml-1">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.user_address }}</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6" v-if="items.order_no !== ''">
                  <v-row justify="center" :class="IpadSize || IpadProSize || MobileSize ? '':'ml-8'">
                    <v-col cols="12" >
                      <div :class="IpadSize || IpadProSize || MobileSize ? '' : 'ml-6'">
                        <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Standard Delivery - {{items.service_type === 'normal' ? 'ส่งแบบปกติ' : items.service_type === 'chilled' ? 'ส่งแบบควบคุมอุณหภูมิ' : items.service_type === 'frozen' ? 'ส่งแบบแช่แข็ง' : 'ส่งของขนาดใหญ่' }} {{items.business_type}} Express</span><br/>
                        <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Tracking Number :  <b>{{items.order_no}}</b></span>
                      </div>
                      <v-btn class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" text color="#27AB9C" @click="GoToMobily(items.url_tracking)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain></v-img> ติดตามสถานะขนส่ง</v-btn>
                    </v-col>
                  </v-row>
              </v-col>
              <!-- shipping withoutFlash -->
              <v-col cols="12" md="6" v-else>
                <!-- Wait Review -->
                <v-card outlined>
                  <v-row no-gutters>
                    <v-col cols="12" md="12" class="pl-3 pt-3">
                      <h3 style="font-weight: 700;"><span style="font-size: 20px; color: #E9A016;">&bull;</span> ผู้ขายกำลังดำเนินการจัดส่งสินค้า</h3>
                    </v-col>
                    <v-col cols="12" md="12" align="center" class="mb-8">
                      <v-img src="@/assets/ImageINET-Marketplace/PoSeller/Tracking-UPS.svg" max-height="300px" max-width="300px" height="100%" width="100%" contain aspect-ratio="3"></v-img>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
              </v-row>
            </v-container>
            <v-col cols="12">
              <v-divider></v-divider>
            </v-col>
            <v-container grid-list-xs>
              <v-col cols="12" class="pt-5 pl-0">
                <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'"><b>รายการสั่งซื้อสินค้า</b></p>
                <v-row justify="start" class="my-0">
                  <v-avatar size="26" class="ml-3">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Store.png"></v-img>
                  </v-avatar>
                  <span style="font-size: 12px; font-weight: 600;" class="pl-1">
                    {{ items.name_th }}
                  </span>
                </v-row>
              </v-col>
              <v-col cols="12" class="pt-5 pl-3 mb-2">
                <v-row justify="start">
                  <span style="font-size: 16px; font-weight: 700;">
                    {{ items.product_list.length }} รายการสินค้า
                  </span>
                </v-row>
              </v-col>
              <a-table :data-source="items.product_list" :rowKey="record => record.sku" :columns="headersMobile">
                <template slot="productdetails" slot-scope="text, record">
                  <v-row>
                    <v-col cols="3" md="4" class="pr-0 mt-2 py-1">
                      <v-img :src="`${record.product_image}`" class="imageshowMobile" v-if="record.product_image !== ''"/>
                      <v-img src="@/assets/NoImage.png" class="imageshowMobile" v-else/>
                    </v-col>
                    <v-col cols="9" md="8">
                      <span class="mb-0 DetailsProductFrontMobile">รหัสสินค้า : {{record.sku}}<br/>{{record.product_name}}</span><br>
                      <div class="mb-0" v-if="record.have_attribute === 'yes'">
                        <span class="mb-0 DetailsProductFrontMobile" v-if="record.product_attribute_detail.attribute_priority_1  !== null" >{{record.key_1_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_1}} </span></span>
                        <span class="ml-3 mb-0 DetailsProductFrontMobile"  v-if="record.product_attribute_detail.attribute_priority_2 !== null ">{{record.key_2_value}}: <span style="font-weight: 700;"> {{record.product_attribute_detail.attribute_priority_2}} </span> </span>
                      </div>
                      <span class="mb-0 DetailsProductFrontMobile">จำนวน : <span style="font-weight: 700;">{{ record.quantity }}</span></span>
                      <span class="mb-0 ml-3 DetailsProductFrontMobile">ราคา : <span style="font-weight: 700;">{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span><br>
                      <span class="pt-1 mb-0 DetailsProductFrontMobile">ราคารวม : <span style="font-weight: 700;">{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></span>
                    </v-col>
                  </v-row>
                </template>
              </a-table>
            </v-container>
            <v-container grid-list-xs>
              <!-- สรุปรายการสั่งซื้อ ipad -->
              <v-row v-if="IpadSize">
                <v-col cols="12" md="10">
                  <v-row dense>
                    <v-col cols="9" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="3" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="9" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ส่วนลด :</span>
                    </v-col>
                    <v-col cols="3" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="9" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ค่าจัดส่ง :</span>
                    </v-col>
                    <v-col cols="3" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="9" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">ราคารวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="3" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="9" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">รวม VAT 7% :</span>
                    </v-col>
                    <v-col cols="3" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="9" class="text-right">
                      <span class="subheader fontSizeTotalPriceMobile">ราคารวมทั้งหมด :</span>
                    </v-col>
                    <v-col cols="3" class="text-right">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTotalPriceMobile': 'fontSizeTotalPrice'">{{ Number(items.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <!-- สรุปรายการสั่งซื้อ mobile -->
              <v-row v-else>
                <v-col cols="12">
                  <OrderSummary :items="items"></OrderSummary>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="12" md="12" class="mt-1 mb-4">
        </v-col>
      </v-row>
      </v-card>
    </v-container>
  <!-- Desktop -->
  <v-container v-else grid-list-xl>
    <v-row justify="center" class="my-4">
      <h2 style="font-weight: 700; font-size: 24px;">รายละเอียดการคืนสินค้า</h2>
    </v-row>
    <v-row no-gutters class="mx-4">
      <v-col :cols="IpadSize || MobileSize ? 12 : 8">
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-1' : 'mb-1 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">รหัสการสั่งซื้อ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.payment_transaction_number }}</span>
          <span  v-if="!MobileSize"> | </span>
          <v-chip class="ma-2" :color="getColor(items.status_refund)" small :text-color="getTextColor(items.status_refund)">
            {{ getStatus(items.status_refund) }}
          </v-chip>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.user_name }}</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่สั่งซื้อ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.buy_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ชำระเงิน : </span>
          <span v-if="items.paid_datetime !== null" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.paid_datetime === 'ชำระเงินแบบเครดิตเทอม' ? items.paid_datetime : new Date(items.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
          <span v-else :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">-</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ยกเลิก : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.waiting_time).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ยกเลิกโดย : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ซื้อ</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">เหตุผลในการคืน : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.reason }}</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">จำนวนเงินที่คืน : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ Number(items.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">การคืนสินค้า : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ getStatusResult(items.reject_detail[0].status_refund) }}</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">เหตุผลในการไม่อนุมัติ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.reject_detail[0].seller_comment }}</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ดำเนินการโดย : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">ผู้ขาย</span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'" v-if="items.status_refund === 'reject'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ไม่อนุมัติ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ new Date(items.reject_detail[0].reject_time).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
        </div>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? 12 : 4" class="mt-1">
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่ส่ง : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">
            {{ items.sent_date === null ? '-' : new Date(items.sent_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}
          </span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">สถานะ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" :style="{ 'color': items.buyer_received_status === 'refund' ? '#E9A016' : items.buyer_received_status === 'received' ? '#1AB759' : items.buyer_received_status === 'not_received' ? '#D1392B' : '#333333' }">
            {{ items.buyer_received_status === 'refund' ? 'รออนุมัติ' : items.buyer_received_status === 'received' ? 'ผู้ซื้อได้รับสินค้าเรียบร้อยแล้ว' : items.buyer_received_status === 'not_received' ? 'ผู้ซื้อยังไม่ได้รับสินค้า' : '-' }}
          </span>
        </div>
        <div :class="IpadSize || IpadProSize || MobileSize ? 'mb-3' : 'mb-3 ml-2'">
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">วันที่รับ : </span>
          <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'" :style="{ 'color': items.buyer_received_status === 'refund' ? '#E9A016' : items.buyer_received_status === 'received' ? '#1AB759' : items.buyer_received_status === 'not_received' ? '#D1392B' : '#333333' }">
            {{ items.received_date === null ? '-' : new Date(items.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}
          </span>
        </div>
      </v-col>
      <v-col cols="12" class="mt-8">
        <v-card outlined>
          <v-container grid-list-lg>
            <v-row no-gutters>
              <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6">
                <v-row no-gutters>
                  <v-col cols="12">
                    <p :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'" style="font-weight: 700;">ที่อยู่ในการจัดส่งสินค้า</p>
                  </v-col>
                  <v-col cols="12" class="mb-4 ml-1">
                    <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile': 'fontSizeDetail'">{{ items.user_address }}</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col :cols="IpadSize || IpadProSize || MobileSize ? 12 : 6" v-if="items.order_no !== ''">
                <!-- Wait Review -->
                <v-row justify="center" :class="IpadSize || IpadProSize || MobileSize ? '':'ml-8'">
                  <v-col cols="12" >
                    <div :class="IpadSize || IpadProSize || MobileSize ? '' : 'ml-6'">
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Standard Delivery - {{items.service_type === 'normal' ? 'ส่งแบบปกติ' : items.service_type === 'chilled' ? 'ส่งแบบควบคุมอุณหภูมิ' : items.service_type === 'frozen' ? 'ส่งแบบแช่แข็ง' : 'ส่งของขนาดใหญ่' }} {{items.business_type}} Express</span><br/>
                      <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile':'fontSizeDetail'">Tracking Number :  <b>{{items.order_no}}</b></span>
                    </div>
                    <v-btn class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" text color="#27AB9C" @click="GoToMobily(items.url_tracking)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain class="pr-2"></v-img> ติดตามสถานะขนส่ง</v-btn>
                  </v-col>
                </v-row>
            </v-col>
            <v-col cols="12" md="6" v-else>
              <!-- Wait Review -->
              <v-row no-gutters class="mb-5">
                <v-col cols="12" md="12" align="right">
                  <v-btn v-if="checkAcceptProduct === 'waiting_accept'" rounded small class="white--text px-5" color="#27AB9C" @click="acceptProduct()"><b class="buttonFontSize">ฉันตรวจสอบและได้รับสินค้าแล้ว</b></v-btn>
                  <v-btn v-if="checkAcceptProduct === 'waiting_review'" rounded small class="white--text px-5" color="#FF8200" @click="openModalReviewProduct()"><b class="buttonFontSize">ได้รับสินค้าแล้ว</b></v-btn>
                </v-col>
              </v-row>
              <!-- Wait Review -->
              <v-card outlined>
                <v-row no-gutters>
                  <v-col cols="12" md="12" class="pl-3 pt-3">
                    <h3 style="font-weight: 700;"><span style="font-size: 20px; color: #E9A016;">&bull;</span> ผู้ขายกำลังดำเนินการจัดส่งสินค้า</h3>
                  </v-col>
                  <v-col cols="12" md="12" align="center" class="mb-8">
                    <v-img src="@/assets/ImageINET-Marketplace/PoSeller/Tracking-UPS.svg" max-height="300px" max-width="300px" height="100%" width="100%" contain aspect-ratio="3"></v-img>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
            </v-row>
          </v-container>
          <v-col cols="12">
            <v-divider></v-divider>
          </v-col>
          <v-container grid-list-xs>
            <v-col cols="12" class="pl-3 pt-5">
              <h2><b>รายการสั่งซื้อสินค้า</b></h2>
            </v-col>
            <v-col cols="12" class="mr-4 mb-2">
              <v-row justify="start">
                <v-avatar size="28" class="ml-2">
                  <v-img src="@/assets/ImageINET-Marketplace/Shop/Store.png"></v-img>
                </v-avatar>
                <span style="font-size: 16px; font-weight: 600;" class="pl-1">
                  {{ items.name_th }}
                </span>
              </v-row>
            </v-col>
            <v-col cols="12" class="mr-4 mb-2 ml-2">
              <v-row justify="start">
                <span class="pt-1 pl-1" style="font-size: 16px; font-weight: 700;">
                  {{ items.product_list.length }} รายการสินค้า
                </span>
              </v-row>
            </v-col>
            <a-table :data-source="items.product_list" :rowKey="record => record.sku" :columns="headers" :showHeader="false">
              <template slot="productdetails" slot-scope="text, record">
                <v-row>
                  <v-col cols="12" md="4" class="pr-0 py-1">
                    <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
                    <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
                  </v-col>
                  <v-col cols="12" md="8">
                    <p class="mb-0 DetailsProductFront">รหัสสินค้า :{{ record.sku }}<br/>{{ record.product_name }}</p>
                    <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
                    <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span>
                  </v-col>
                </v-row>
              </template>
              <template slot="quantity" slot-scope="text, record">
                <v-col cols="12">
                  <span>จำนวน {{ record.quantity }} ชิ้น</span>
                </v-col>
              </template>
              <template slot="net_price" slot-scope="text, record">
                <span style="font-weight: 700; font-size: 16px; line-height: 26px; color: #000000;">{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
            </a-table>
          </v-container>
          <v-container grid-list-xs>
            <v-row>
              <v-col cols="12" sm="9" md="10">
                <v-row dense>
                  <v-col cols="12" class="text-right">
                    <span class="fontSizeTotalPrice">ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="fontSizeTotalPrice">ส่วนลด :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="fontSizeTotalPrice">ค่าจัดส่ง :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="fontSizeTotalPrice">ราคารวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="fontSizeTotalPrice">รวม VAT 7% :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="subheader fontSizeTotalPrice">ราคารวมทั้งหมด :</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" sm="3" md="2">
                <v-row dense>
                  <v-col cols="12" class="text-left">
                    <span class="fontSizeTotalPrice">{{ Number(items.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span class="fontSizeTotalPrice">{{ Number(items.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span class="fontSizeTotalPrice">{{ Number(items.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span class="fontSizeTotalPrice">{{ Number(items.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span class="fontSizeTotalPrice">{{ Number(items.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span class="fontSizeTotalPrice">{{ Number(items.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" md="12" class="mt-1 mb-4">
      </v-col>
    </v-row>
  </v-container>
  </div>
</template>

<script>
import { Encode, Decode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
    // ModalReviewProduct: () => import('@/components/UPS/UserProfile/ModalReview/ReviewProduct')
  },
  data () {
    return {
      items: [],
      referenceId: {},
      statusStepper: 1,
      bankName: '',
      dataRole: '',
      trackingStatus: '',
      trackingText: '',
      flashTrackingNo: '',
      flashTrackingData: {},
      receivedDate: '',
      sentDate: '',
      sentTime: '',
      step: 0,
      refID: '',
      flashMCHID: process.env.VUE_APP_FLASH,
      checkAcceptProduct: '',
      mockupTracking: {},
      // mockupTracking: {
      //   pno: 'TH04021UAUS35B',
      //   returnedPno: null,
      //   customaryPno: null,
      //   state: 5,
      //   stateText: 'เซ็นรับแล้ว',
      //   stateChangeAt: **********,
      //   routes: [
      //     {
      //       routedAt: **********,
      //       routeAction: 'DELIVERY_CONFIRM',
      //       message: 'นำส่งสำเร็จ เซ็นรับโดย【เปีย(เจ้าของสินค้า)】 ขอบคุณที่ใช้บริการ Flash Express',
      //       state: 5
      //     },
      //     {
      //       routedAt: 1631180270,
      //       routeAction: 'PHONE',
      //       message: 'โทรศัพท์ติอต่อผู้รับ',
      //       state: 3
      //     },
      //     {
      //       routedAt: 1631173853,
      //       routeAction: 'DELIVERY_TICKET_CREATION_SCAN',
      //       message: 'พัสดุของท่านอยู่ระหว่างการนำส่ง',
      //       state: 3
      //     },
      //     {
      //       routedAt: 1631162232,
      //       routeAction: 'ARRIVAL_WAREHOUSE_SCAN',
      //       message: 'พัสดุถึงสาขา【KLL_SP-คลองหลวง】',
      //       state: 2
      //     },
      //     {
      //       routedAt: 1631153227,
      //       routeAction: 'SHIPMENT_WAREHOUSE_SCAN',
      //       message: 'ส่งต่อพัสดุจากสาขา【 16 Central_HUB-วังน้อย】 ไปยังสาขา【 KLL_SP-คลองหลวง】',
      //       state: 2
      //     },
      //     {
      //       routedAt: 1631152219,
      //       routeAction: 'ARRIVAL_WAREHOUSE_SCAN',
      //       message: 'พัสดุถึงสาขา【16 Central_HUB-วังน้อย】',
      //       state: 2
      //     },
      //     {
      //       routedAt: 1631117040,
      //       routeAction: 'SHIPMENT_WAREHOUSE_SCAN',
      //       message: 'ส่งต่อพัสดุจากสาขา【 09 NE2_HUB-ขอนแก่น】 ไปยังสาขา【 16 Central_HUB-วังน้อย】',
      //       state: 2
      //     },
      //     {
      //       routedAt: 1631112159,
      //       routeAction: 'ARRIVAL_WAREHOUSE_SCAN',
      //       message: 'พัสดุถึงสาขา【09 NE2_HUB-ขอนแก่น】',
      //       state: 2
      //     },
      //     {
      //       routedAt: 1631098284,
      //       routeAction: 'SHIPMENT_WAREHOUSE_SCAN',
      //       message: 'ส่งต่อพัสดุจากสาขา【 GMS_SP-กมลาไสย】 ไปยังสาขา【 09 NE2_HUB-ขอนแก่น】',
      //       state: 2
      //     },
      //     {
      //       routedAt: 1631075474,
      //       routeAction: 'RECEIVE_WAREHOUSE_SCAN',
      //       message: 'รับพัสดุเข้าสาขา【GMS_SP-กมลาไสย】',
      //       state: 1
      //     },
      //     {
      //       routedAt: 1631030964,
      //       routeAction: 'RECEIVED',
      //       message: 'เจ้าหน้าที่สาขา【GMS_SP-กมลาไสย】รับพัสดุเรียบร้อย',
      //       state: 1
      //     }
      //   ]
      // },
      flashRoutes: [],
      dateCreateOrderStep1: '',
      dateCreateOrderStep2: '',
      dateCreateOrderStep3: '',
      dateCreateOrderStep4: ''
    }
  },
  async created () {
    // this.$EventBus.$on('getDetailPOBuyer', this.SwitchRole)
    // this.$EventBus.$on('SentGetReview', this.getItemProduct)
    this.$EventBus.$emit('changeNavCompany')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.referenceId = JSON.parse(Decode.decode(localStorage.getItem('orderRefundNumber')))
      this.refID = this.$route.query.orderNumber
      this.getItemProduct()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  // beforeDestroy () {
  //   this.$EventBus.$off('SentGetReview')
  // },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        },
        // {
        //   title: 'ราคาต่อชิ้น',
        //   dataIndex: 'price',
        //   scopedSlots: { customRender: 'price' },
        //   key: 'price',
        //   align: 'center',
        //   width: '20%'
        // },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          align: 'center',
          width: '20%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        }
      ]
      return headersMobile
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/refundDetailCompanyMobile?orderNumber=${this.refID}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/refundDetailCompany?orderNumber=${this.refID}` }).catch(() => {})
      }
    }
  },
  methods: {
    backtoRefundList () {
      if (this.MobileSize) {
        this.$router.push({ path: '/refundCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/refundCompany' }).catch(() => {})
      }
    },
    GoToMobily (Track) {
      window.open(Track)
    },
    async getItemProduct () {
      this.$store.commit('openLoader')
      var data = {
        reference_id: this.refID,
        // role_user: this.dataRole.role
        role_user: 'purchaser'
      }
      await this.$store.dispatch('actionsDetailRefundPurchaser', data)
      var res = await this.$store.state.ModuleAdminManage.stateDetailRefundPurchaser
      // console.log('res in RefundDetailOrder', res)
      if (res.message === 'Get detail refund purchaser success' && res.result === 'SUCCESS') {
        this.items = res.data[0]
        // console.log(this.items)
        this.$store.commit('closeLoader')
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
        if (this.MobileSize === false) {
          this.$router.push({ path: `/refundDetailCompany?orderNumber=${this.refID}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/refundDetailCompany?orderNumber=${this.refID}` }).catch(() => {})
        }
      }
    },
    async CheckAcceptProduct () {
      var data = {
        payment_transaction_number: this.items.payment_transaction
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      this.checkAcceptProduct = res.data.status
      // console.log('checkAcceptProduct', this.checkAcceptProduct)
      if (this.checkAcceptProduct === 'waiting_review') {
        this.openModalReviewProduct()
      }
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.getItemProduct()
    },
    async acceptProduct () {
      var data = {
        payment_transaction_number: this.items.payment_transaction,
        status: 'accepted'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      // console.log('acceptProduct', res)
      if (res.message === 'Update status success.') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว'
        })
        this.getItemProduct()
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    async GoToPayment () {
      const PaymentID = {
        payment_transaction_number: this.items.payment_transaction
      }
      // console.log('payment_transaction_number', PaymentID)
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      // console.log('respose paymenttttttttt', response)
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    getColor (item) {
      if (item === 'waiting') return '#FCF0DA'
      else if (item === 'approve') return '#ECF8EA'
      else return '#FBE5E4'
    },
    getTextColor (item) {
      if (item === 'waiting') return '#E9A016'
      else if (item === 'approve') return '#1AB759'
      else return '#D1392B'
    },
    getStatus (item) {
      if (item === 'waiting') return 'รออนุมัติ'
      else if (item === 'approve') return 'อนุมัติ'
      else return 'ไม่อนุมัติ'
    },
    getStatusResult (item) {
      if (item === 'waiting') return 'รออนุมัติ'
      else if (item === 'approve') return 'อนุมัติ'
      else return 'ไม่อนุมัติ'
    }
  }
}
</script>

<style lang="css" scoped>
/* .v-application .mb-12 {
    margin-bottom: 12px !important;
} */
::v-deep .ant-table-pagination {
  display: none;
}
.imageshow {
  width: 120px;
  height: 120px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.bgShippingUPS {
  background-color: #F3F5F7;
}
.fontActive {
  color: #27AB9C;
}
.fontInactive {
  color: #A6A6A6;
}
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 18px;
}
.fontSizeTotalPriceMobile {
  font-size: 16px;
}
.fontSizeAddressDetail {
  font-size: 16px;
}
.buttonFontSize {
  font-size: 14px;
}
.captionSku {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
.fontSizeDetail {
  font-size: 16px;
}
.fontSizeDetailMobile {
  font-size: 14px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
</style>
