<template>
  <div>
    <v-container>
      <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
        <h2 v-if="!MobileSize" class="ml-4" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"><B>ติดตามสถานะสินค้า</B></h2>
        <v-row cols="12" md="12" class="pa-3" v-if="MobileSize">
          <v-icon v-if="MobileSize || IpadSize" @click="Cancle()" color="#27AB9C" class="mb-0 ml-2">mdi-chevron-left
          </v-icon>
          <span style="font-weight: bold; font-size: 16px; line-height: 40px;" class="ml-2">ติดตามสถานะสินค้า</span>
        </v-row>
        <a-tabs @change="getOrderReturn">
          <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countorder
          }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="1"><span slot="tab">จัดส่งสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{
              countsuccess
          }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="2"><span slot="tab">กำลังจัดส่ง <a-tag color="#FAD02C" style="border-radius: 8px;">{{
              countwait
          }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="3"><span slot="tab">ถูกตีกลับ/ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{
              countreturn
          }}</a-tag></span></a-tab-pane>
        </a-tabs>
        <v-container>
          <v-text-field style="width:400px" v-model="search" append-icon="mdi-magnify" label="ค้นหารหัสการสั่งซื้อ"
            v-if="show" outlined dense rounded hide-details></v-text-field><br />
          <v-data-table v-if="show" v-model="selected" :headers="headers" :items="data" :search="search"
            item-key="referance_id" :footer-props="{'items-per-page-text':'จำนวนแถว'}" color="blue" class="elevation-1" no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา" no-data-text="ไม่มีรายการสั่งซื้อในตาราง">
            <!-- <template v-slot:[`item.reference_id`]="{ item }">
          <span><a :href="item.url_barcode_picture" target="_blank">{{item.reference_id}}</a></span>
        </template> -->
            <!-- <template v-slot:[`item.order_no`]="{ item }">
          <span><a :href="item.url_tracking" target="_blank">{{item.order_no}}</a></span>
        </template> -->
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 1">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#E9A016">สร้างใหม่</v-chip>
                <!-- <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เรียกพนักรับพัสดุ</v-chip> -->
              </span>
              <span v-else-if="item.status === 5">
                <v-chip class="ma-2" color="#E9EAFE" text-color="#111FFA">เรียกพนักงานรับพัสดุ</v-chip>
              </span>
              <span v-else-if="item.status === 6">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พนักงานรับพัสดุแล้ว</v-chip>
              </span>
              <span v-else-if="item.status === 7">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">อยู่ระหว่างการขนส่ง</v-chip>
              </span>
              <span v-else-if="item.status === 8">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พัสดุถึงสาขาปลายทาง</v-chip>
              </span>
              <span v-else-if="item.status === 9">
                <v-chip v-if="item.delivery_type === 'DELIVERY'" class="ma-2" color="#F0F9EE" text-color="#1AB759">
                  พัสดุถึงผู้รับแล้ว</v-chip>
                <v-chip v-if="item.delivery_type === 'DELIVERY_RETURN'" class="ma-2" color="#F7D9D9"
                  text-color="#D1392B">คืนพัสดุกลับไปต้นทาง</v-chip>
              </span>
              <span v-else-if="item.status === 11">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">พัสดุตีกลับสาขาปลายทาง</v-chip>
              </span>
              <span v-else-if="item.status === 12">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">นำส่งไม่สำเร็จ</v-chip>
              </span>
              <span v-else-if="item.status === 13">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เคลมและไม่มีการส่งคืนสินค้า</v-chip>
              </span>
              <span v-else-if="item.status === 14">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">คืนพัสดุกลับไปต้นทาง</v-chip>
              </span>
              <span v-else-if="item.status === 15">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">น้ำหนักและขนาดเกินที่กำหนด</v-chip>
              </span>
              <span v-else-if="item.status === 16">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เคลมสินค้า</v-chip>
              </span>
              <span v-else-if="item.status === -1">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">คืนพัสดุขณะขนส่ง</v-chip>
              </span>
              <span v-else-if="item.status === 0">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิกแล้ว</v-chip>
              </span>
            </template>
            <template v-slot:[`item.updated_at`]="{ item }">
              {{ new Date(item.updated_at).toLocaleDateString('th-TH', {
                  timeZone: "UTC", year: 'numeric', month:
                    'long', day: 'numeric'
                })
              }}
            </template>
            <template v-slot:[`item.callcuriers`]="{ item }">
              <!-- <a  color="#27AB9C" @click="OpenTacking(item.url_tracking)"><B>ติดตามสถานะสินค้า</B></a> -->
              <!-- <v-btn class="my-3 px-0" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeDetailMobile' : 'fontSizeDetail ml-6'" text color="#27AB9C"  @click="OpenTacking(item)" style="color: #27AB9C; text-decoration: underline;"><v-img src="@/assets/icons/Vector.png" contain></v-img> ติดตามสถานะขนส่ง</v-btn> -->
              <v-row>
                <!-- <v-btn
                x-small
                outlined
                style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                class="pt-4 pb-4"
                :href="item.url_barcode_picture" target="_blank"
              >
                <v-icon color="#27AB9C" size="22"  >mdi-barcode-scan</v-icon>
            </v-btn> -->
                <v-btn x-small outlined
                  style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                  class="pt-4 pb-4 ml-2" @click="OpenTacking(item)">
                  <v-img width="25" src="@/assets/icons/Vector.png" contain></v-img>
                </v-btn>
              </v-row>
            </template>
            <!-- <template v-slot:[`item.barcode`]="{ item }">
          <a :href="item.url_barcode_picture" target="_blank"><v-icon color="#27AB9C" >mdi-barcode-scan</v-icon></a>
        </template> -->
          </v-data-table>
          <!-- <v-row justify="center" align-content="center" v-if="disableTable === true">
            <v-col cols="12" md="12" align="center" style="min-height: 636px;">
              <div style="padding-top: 90px;">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                  height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #1AB759;">
                <span
                  style="font-weight: bold; font-size: 24px; line-height: 32px;">ยังไม่มีรายการสถานะสินค้า</span><br />
              </h2>
            </v-col>
          </v-row> -->
          <v-row justify="center" align-content="center" v-if="disableTable === true">
            <v-col cols="12" align="center">
              <div class="my-5">
                <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 v-if="keyCheckHead === 0" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการติดตามสถานะสินค้า</b></h2>
              <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการติดตามสถานะสินค้าที่{{ keyCheckHead === 1 ? 'จัดส่งสำเร็จ' : keyCheckHead === 2 ? 'กำลังจัดส่ง' : keyCheckHead === 3 ? 'ถูกตีกลับ/ยกเลิก' : 'กำลังดำเนินการ' }}</b></h2>
            </v-col>
          </v-row>

          <!-- <div><span style="color:red">หมายเหตุ</span> : จะสามารถเรียกคูเรียร์/พนักงานเข้ารับได้ทีละ 1 งานเท่านั้น โดย 1 งานมีกี่คำสั่งซื้อก็ได้ หากต้องการความช่วยเหลือ ให้ติดต่อทาง Flash โดยตรง</div> -->
          <!-- <div v-if="show" class="mt-1"><span style="color:red">หมายเหตุ</span> : หากต้องการที่จะพิมพ์บาร์โค้ด(Barcode) สามารถกดที่ "สัญญาลักษณ์บาร์โค้ด(Icon Barcode)"</div> -->
        </v-container>
      </v-card>
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data: () => ({
    quantity: 0,
    res: [],
    respons: [],
    oneData: [],
    selected: [],
    data: [],
    SelectOrder: [],
    Message: '',
    DataCurier: '',
    search: '',
    dialog: false,
    dialog_Delete: false,
    dialogAllselect: false,
    disabledCount: 0,
    keyCheckHead: 0,
    countorder: 0,
    countreturn: 0,
    countsuccess: 0,
    countwait: 0,
    disableTable: false,
    show: true,
    token: '**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
    desserts: [
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300300',
        staffInfoPhone: '*********',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '0',
        updateAt: 'กำลังไปรับพัสดุ'
      },
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300181',
        staffInfoPhone: '*********',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '1',
        updateAt: 'กำลังจัดส่ง'
      },
      {
        staffInfoName: 'Frozen Yogurt',
        ticketPickupId: '6030300202',
        staffInfoPhone: '*********',
        work: 24,
        cancel: '-',
        iron: '1%',
        state: '0',
        updateAt: 'กำลังไปรับพัสดุ'
      }
    ],
    headers: [
      // { text: 'บาร์โค้ด', value: 'barcode', sortable: false, width: '78', align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'รหัสการสั่งซื้อ', value: 'reference_id', width: '140', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      // { text: 'งานรับล่าสุด', value: 'staffInfoId', width: '130', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      //   { text: 'ชื่อ-สกุล', value: 'buyer_name', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
      { text: 'รหัสการติดตาม', value: 'order_no', width: '140', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'สถานะ', value: 'status', sortable: false, filterable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      // { text: 'เหตุผลที่ยกเลิก', value: 'cancelReasonText', width: '150', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'อัปเดตล่าสุด', value: 'updated_at', filterable: false, sortable: false, width: '180', align: 'center', class: 'backgroundTable fontTable--text' },
      { text: 'จัดการ', value: 'callcuriers', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' }
    ],
    Rules: {
      curiernumber: [
        v => (/^[1-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้นและไม่ติดลบ'
      ]
    }
  }),
  created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => { })
    } else {
      this.getList()
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/tackingbuyerMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/tackingbuyer' }).catch(() => { })
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    Cancle () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => { })
    },
    OpenTacking (val) {
      window.open(val.url_tracking)
      // window.open('https://mobilysttech-poc.inet.co.th/staging/tracking3pl/tracking3pl?mode=readonly&track=' + val.order_no + '&type=D')
    },
    async getOrderReturn (item) {
      if (item === 1) {
        this.keyCheckHead = item
        this.data = []
        var Data = {
          token: this.oneData.user.access_token,
          status: 'delivery success'
        }
        await this.$store.dispatch('actionGetTackingAllBuyer', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
        this.data = this.res.data
        this.countsuccess = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 0) {
        this.keyCheckHead = item
        this.data = []
        Data = {
          token: this.oneData.user.access_token,
          status: 'all'
        }
        await this.$store.dispatch('actionGetTackingAllBuyer', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
        this.data = this.res.data
        this.countorder = this.data.length
        // console.log('GetTackingAll', this.res)
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 2) {
        this.keyCheckHead = item
        this.data = []
        Data = {
          token: this.oneData.user.access_token,
          status: 'delivery in progress'
        }
        await this.$store.dispatch('actionGetTackingAllBuyer', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
        this.data = this.res.data
        this.countwait = this.data.length
        // console.log('GetTackingAll', this.res)
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      } else if (item === 3) {
        this.keyCheckHead = item
        this.data = []
        Data = {
          token: this.oneData.user.access_token,
          status: 'delivery return'
        }
        await this.$store.dispatch('actionGetTackingAllBuyer', Data)
        this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
        this.data = this.res.data
        this.countreturn = this.data.length
        if (this.data.length === 0 || this.data === undefined) {
          this.disableTable = true
          this.show = false
        } else {
          this.disableTable = false
          this.show = true
        }
      }
    },
    async getList () {
      this.data = []
      // ยิงเพื่อจะนับว่ามีข้อมูลอยู่เท่าไหร่   ในหัวข้อ "ทั้งหมด"
      var Data = {
        token: this.oneData.user.access_token,
        status: 'all'
      }
      await this.$store.dispatch('actionGetTackingAllBuyer', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
      this.data = this.res.data
      this.countorder = this.data.length

      // ยิงเพื่อจะนับว่ามีข้อมูลอยู่เท่าไหร่   ในหัวข้อ "จัดส่งสำเร็จแล้ว"
      Data = {
        token: this.oneData.user.access_token,
        status: 'delivery success'
      }
      await this.$store.dispatch('actionGetTackingAllBuyer', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
      this.countsuccess = this.res.data.length

      // ยิงเพื่อจะนับว่ามีข้อมูลอยู่เท่าไหร่   ในหัวข้อ "กำลังจัดส่ง"
      Data = {
        token: this.oneData.user.access_token,
        status: 'delivery in progress'
      }
      await this.$store.dispatch('actionGetTackingAllBuyer', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
      this.countwait = this.res.data.length

      // ยิงเพื่อจะนับว่ามีข้อมูลอยู่เท่าไหร่   ในหัวข้อ "ถูกตีกลับ/ยกเลิก"
      Data = {
        token: this.oneData.user.access_token,
        status: 'delivery return'
      }
      await this.$store.dispatch('actionGetTackingAllBuyer', Data)
      this.res = await this.$store.state.ModuleTacking.stateAllOrderBuyer
      this.countreturn = this.res.data.length

      if (this.data.length === 0 || this.data === undefined) {
        this.disableTable = true
        this.show = false
      } else {
        this.disableTable = false
        this.show = true
      }
    }
  }
}
</script>

<style lang="scss">
.elevation-1 th:first-of-type {
  background-color: #E6F5F3;
}
</style>
