import AxiosAdminPanit from '../store_admin_panit/axios_admin_panit'

const ModuleAdminPanit = {
  state: {
    // Transaction
    stateTransaction: [],
    // top10sellers
    stateTop10Sellers: [],
    // top10buyers
    stateTop10Buyer: [],
    // top10purchasers
    stateTop10Purchaser: [],
    // List Order Approve
    stateListOrderApprove: [],
    // Iframe Order Approve
    stateIframeOrderApprove: [],
    // Detail Order Approve
    stateDetailOrderApprove: [],
    // List Purchaser Approve
    stateListPurchaserApproveOrder: [],
    stateListGSellerShop: [],
    stateListSelectNewGSellerShop: [],
    stateAddGAdminSellerShop: [],
    stateDeleteGSellerShop: [],
    stateGetAllSumDocAdmin: [],
    stateGetAllSumTrendAdmin: [],
    stateGetAllSumBarQTAdmin: [],
    stateGetAllSumBarSOAdmin: [],
    stateGetAllSumBarPRAdmin: [],
    stateGetAllSumBarPOAdmin: [],
    stateGetAllSumTopFiveQTAdmin: [],
    stateMediaUpload: [],
    stateMediaDetail: [],
    stateGetPopUp: [],
    stateGetPopUpHomepage: [],
    stateUpdatePopUp: [],
    stateServicePartner: [],
    stateUpdateServicePartner: [],
    stateAdminAddUserManual: [],
    stateAdminDeleteUserManual: [],
    stateAdminUserManualDetail: [],
    stateAdminAddLinkSeminar: [],
    stateAdminLinkSeminar: [],
    stateAdminDeleteLinkSeminar: []
  },
  mutations: {
    mutationsTransaction (state, data) {
      state.stateTransaction = data
    },
    mutationsTop10Sellers (state, data) {
      state.stateTop10Sellers = data
    },
    mutationsTop10Buyer (state, data) {
      state.stateTop10Buyer = data
    },
    mutationsTop10Purchaser (state, data) {
      state.stateTop10Purchaser = data
    },
    mutationsListOrderApprove (state, data) {
      state.stateListOrderApprove = data
    },
    mutationsIframeOrderApprove (state, data) {
      state.stateIframeOrderApprove = data
    },
    mutationsDetailOrderApprove (state, data) {
      state.stateDetailOrderApprove = data
    },
    mutationsListPurchaserApproveOrder (state, data) {
      state.stateListPurchaserApproveOrder = data
    },
    mutationsListGSellerShop (state, data) {
      state.stateListGSellerShop = data
    },
    mutationsListSelectNewGSellerShop (state, data) {
      state.stateListSelectNewGSellerShop = data
    },
    mutationsAddGAdminSellerShop (state, data) {
      state.stateAddGAdminSellerShop = data
    },
    mutationsDeleteGSellerShop (state, data) {
      state.stateDeleteGSellerShop = data
    },
    mutationsGetAllSumDocAdmin (state, data) {
      state.stateGetAllSumDocAdmin = data
    },
    mutationsGetAllSumTrendAdmin (state, data) {
      state.stateGetAllSumTrendAdmin = data
    },
    mutationsGetAllSumBarQTAdmin (state, data) {
      state.stateGetAllSumBarQTAdmin = data
    },
    mutationsGetAllSumBarSOAdmin (state, data) {
      state.stateGetAllSumBarSOAdmin = data
    },
    mutationsGetAllSumBarPRAdmin (state, data) {
      state.stateGetAllSumBarPRAdmin = data
    },
    mutationsGetAllSumBarPOAdmin (state, data) {
      state.stateGetAllSumBarPOAdmin = data
    },
    mutationsMediaUpload (state, data) {
      state.stateMediaUpload = data
    },
    mutationsMediaDetail (state, data) {
      state.stateMediaDetail = data
    },
    mutationsGetPopUp (state, data) {
      state.stateGetPopUp = data
    },
    mutationsGetPopUpHomepage (state, data) {
      state.stateGetPopUpHomepage = data
    },
    mutationsUpdatePopUp (state, data) {
      state.stateUpdatePopUp = data
    },
    mutationsServicePartner (state, data) {
      state.stateServicePartner = data
    },
    mutationsUpdateServicePartner (state, data) {
      state.stateUpdateServicePartner = data
    },
    mutationsAdminAddUserManual (state, data) {
      state.stateAdminAddUserManual = data
    },
    mutationsAdminDeleteUserManual (state, data) {
      state.stateAdminDeleteUserManual = data
    },
    mutationsAdminUserManualDetail (state, data) {
      state.stateAdminUserManualDetail = data
    },
    mutationsAdminAddLinkSeminar (state, data) {
      state.stateAdminAddLinkSeminar = data
    },
    mutationsAdminLinkSeminar (state, data) {
      state.stateAdminLinkSeminar = data
    },
    mutationsAdminDeleteLinkSeminar (state, data) {
      state.stateAdminDeleteLinkSeminar = data
    }
  },
  actions: {
    async actionsTransaction (context, access) {
      var response = await AxiosAdminPanit.transaction(access)
      await context.commit('mutationsTransaction', response)
    },
    async actionsTop10Sellers (context, access) {
      var response = await AxiosAdminPanit.Top10Sellers(access)
      await context.commit('mutationsTop10Sellers', response)
    },
    async actionsTop10Buyer (context, access) {
      var response = await AxiosAdminPanit.Top10Buyer(access)
      await context.commit('mutationsTop10Buyer', response)
    },
    async actionsTop10Purchaser (context, access) {
      var response = await AxiosAdminPanit.Top10Purchaser(access)
      await context.commit('mutationsTop10Purchaser', response)
    },
    async actionsListOrderApprove (context, access) {
      var response = await AxiosAdminPanit.ListOrderApprove(access)
      await context.commit('mutationsListOrderApprove', response)
    },
    async actionsIframeOrderApprove (context, access) {
      var response = await AxiosAdminPanit.IframeOrderApprove(access)
      await context.commit('mutationsIframeOrderApprove', response)
    },
    async actionsDetailOrderApprove (context, access) {
      var response = await AxiosAdminPanit.DetailOrderApprove(access)
      await context.commit('mutationsDetailOrderApprove', response)
    },
    async actionsListPurchaserApproveOrder (context, access) {
      var response = await AxiosAdminPanit.ListPurchaserApproveOrder(access)
      await context.commit('mutationsListPurchaserApproveOrder', response)
    },
    async actionsListGSellerShop (context) {
      var response = await AxiosAdminPanit.ListGSellerShop()
      await context.commit('mutationsListGSellerShop', response)
    },
    async actionsListSelectNewGSellerShop (context) {
      var response = await AxiosAdminPanit.ListSelectNewGSellerShop()
      await context.commit('mutationsListSelectNewGSellerShop', response)
    },
    async actionsAddGAdminSellerShop (context, access) {
      var response = await AxiosAdminPanit.AddGAdminSellerShop(access)
      await context.commit('mutationsAddGAdminSellerShop', response)
    },
    async actionsDeleteGSellerShop (context, access) {
      var response = await AxiosAdminPanit.DeleteGSellerShop(access)
      await context.commit('mutationsDeleteGSellerShop', response)
    },
    async actionsGetAllSumDocAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumDocAdmin(access)
      await context.commit('mutationsGetAllSumDocAdmin', response)
    },
    async actionsGetAllSumTrendAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumTrendAdmin(access)
      await context.commit('mutationsGetAllSumTrendAdmin', response)
    },
    async actionsGetAllSumBarQTAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumBarQTAdmin(access)
      await context.commit('mutationsGetAllSumBarQTAdmin', response)
    },
    async actionsGetAllSumBarSOAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumBarSOAdmin(access)
      await context.commit('mutationsGetAllSumBarSOAdmin', response)
    },
    async actionsGetAllSumBarPRAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumBarPRAdmin(access)
      await context.commit('mutationsGetAllSumBarPRAdmin', response)
    },
    async actionsGetAllSumBarPOAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumBarPOAdmin(access)
      await context.commit('mutationsGetAllSumBarPOAdmin', response)
    },
    async actionsGetAllSumTopFiveQTAdmin (context, access) {
      var response = await AxiosAdminPanit.GetAllSumTopFiveQTAdmin(access)
      await context.commit('mutationsGetAllSumTopFiveQTAdmin', response)
    },
    async actionsMediaUpload (context, access) {
      var response = await AxiosAdminPanit.MediaUpload(access)
      await context.commit('mutationsMediaUpload', response)
    },
    async actionsMediaDetail (context, access) {
      var response = await AxiosAdminPanit.MediaDetail(access)
      await context.commit('mutationsMediaDetail', response)
    },
    async actionsGetPopUp (context, access) {
      var response = await AxiosAdminPanit.GetPopUp(access)
      await context.commit('mutationsGetPopUp', response)
    },
    async actionsGetPopUpHomepage (context, access) {
      var response = await AxiosAdminPanit.GetPopUpHomepage(access)
      await context.commit('mutationsGetPopUpHomepage', response)
    },
    async actionsUpdatePopUp (context, access) {
      var response = await AxiosAdminPanit.UpdatePopUp(access)
      await context.commit('mutationsUpdatePopUp', response)
    },
    async actionsServicePartner (context, access) {
      var response = await AxiosAdminPanit.ServicePartner(access)
      await context.commit('mutationsServicePartner', response)
    },
    async actionsUpdateServicePartner (context, access) {
      var response = await AxiosAdminPanit.UpdateServicePartner(access)
      await context.commit('mutationsUpdateServicePartner', response)
    },
    async actionsAdminAddUserManual (context, access) {
      var response = await AxiosAdminPanit.AdminAddUserManual(access)
      await context.commit('mutationsAdminAddUserManual', response)
    },
    async actionsAdminDeleteUserManual (context, access) {
      var response = await AxiosAdminPanit.AdminDeleteUserManual(access)
      await context.commit('mutationsAdminDeleteUserManual', response)
    },
    async actionsAdminUserManualDetail (context, access) {
      var response = await AxiosAdminPanit.AdminUserManualDetail(access)
      await context.commit('mutationsAdminUserManualDetail', response)
    },
    async actionsAdminAddLinkSeminar (context, access) {
      var response = await AxiosAdminPanit.AdminAddLinkSeminar(access)
      await context.commit('mutationsAdminAddLinkSeminar', response)
    },
    async actionsAdminLinkSeminar (context, access) {
      var response = await AxiosAdminPanit.AdminLinkSeminar(access)
      await context.commit('mutationsAdminLinkSeminar', response)
    },
    async actionsAdminDeleteLinkSeminar (context, access) {
      var response = await AxiosAdminPanit.AdminDeleteLinkSeminar(access)
      await context.commit('mutationsAdminDeleteLinkSeminar', response)
    }
  }
}

export default ModuleAdminPanit
