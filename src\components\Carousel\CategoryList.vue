<template>
  <v-container>
    <v-row class="ml-1 mb-9 mt-1">
      <h2 class="headder">หมวดหมู่</h2>
    </v-row>
    <v-row class="ml-1 mr-1" justify="center">
      <!-- <v-card height="100%" width="100%" elevation="0" tile>
        <v-card-title> -->
        <div>
          <v-row align-content="center" justify="center">
            <!-- <v-hover v-slot="{ hover }"> -->
              <v-card v-for="(item, index) in categoryList" :key="index" style="cursor: pointer" tile outlined height="119px" @click="gotoCategoryPage(item)">
                <v-card-title class="pr-4">
                  <v-row justify="center" dense>
                    <v-avatar
                    tile
                    size="60"
                    >
                      <!-- <v-row justify="center"> -->
                        <v-img
                          :src="item.category_image"
                          height="73"
                          width="92"
                        >
                        </v-img>
                      <!-- </v-row> -->
                    </v-avatar>
                  </v-row>
                </v-card-title>
                <v-card-text>
                  <span>{{ item.category_name }}</span>
                </v-card-text>
              </v-card>
            <!-- </v-hover> -->
          </v-row>
        </div>
        <!-- </v-card-title>
      </v-card> -->
    </v-row>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
const categoryList = []
for (let i = 1; i < 13; i++) {
  categoryList.push({
    category_ID: i,
    category_name: `category ${i}`,
    category_image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  data () {
    return {
      categoryList
    }
  },
  methods: {
    gotoCategoryPage (item) {
      localStorage.setItem('categoryList', Encode.encode(item))
      this.$router.push({ path: '/CategoryPage' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.container {
  max-width: 1250px;
}
.v-card:hover {
  transform:scale(1.02) ;
}
.headder {
  font-size: 20px;
  font-weight: bold;
  font-style: normal;
}
</style>
