<template>
  <v-container>
    <!-- Start List Sales -->
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="border-radius: 8px;">
      <v-card-text class="px-0">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-0' : 'px-0 py-0'">
          <v-row class="mx-0" v-if="!MobileSize">
            <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">รายชื่อฝ่ายขาย</v-card-title>
          </v-row>
          <v-row v-else>
            <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายชื่อ Sales</v-card-title>
          </v-row>
        </v-col>
        <v-col cols="12" :style="IpadSize? 'max-width: 100%; overflow: hidden;' : 'max-width: 100%;'">
          <!-- Search Sales Name and ID -->
          <v-row dense justify="start">
            <v-col cols="12" md="4" sm="6">
              <v-text-field v-model="searchSales" outlined style="border-radius: 8px;" dense placeholder="ค้นหาจากรายชื่อฝ่ายขายและรหัสฝ่ายขาย" hide-details></v-text-field>
            </v-col>
          </v-row>
          <v-row dense justify="start" class="my-3">
            <v-col cols="12" md="6" sm="6" align="start" class="pt-4">
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;">รายชื่อฝ่ายขายทั้งหมด {{ showCountOrder }} คน</span>
            </v-col>
            <v-col cols="12" md="6" sm="6" align="end" v-if="checkAdminSale">
              <v-btn color="primary" rounded height="40" @click="openDialogAddSale()">เพิ่มข้อมูลฝ่ายขาย</v-btn>
            </v-col>
          </v-row>
          <!-- Table list Sales -->
          <v-row dense justify="center">
            <v-card width="100%" elevation="0" outlined>
              <v-data-table
               :headers="headerListSales"
               :items="itemSales"
               :items-per-page="10"
               :search="searchSales"
               :footer-props="{'items-per-page-text': 'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
               no-results-text="ไม่พบรายชื่อฝ่ายขายหรือรหัสฝ่ายขายในตาราง"
               no-data-text="ไม่มีรายชื่อในตาราง"
               @pagination="countSales"
               :style="IpadSize ? 'max-width: 100%; overflow: hidden !important;' : ''"
              >
                <template v-slot:[`item.preview`]="{ item }">
                  <v-btn :disabled="item.status === 'inActive'" @click="gotoCustomerOfSales(item)" outlined text color="primary">ดูรายชื่อลูกค้า</v-btn>
                </template>
                <template v-slot:[`item.details`]="{ item }">
                  <!-- <v-btn @click="openSalesDetail(item)" text color="primary">รายละเอียด</v-btn> -->
                  <v-row dense class="d-flex justify-center">
                    <!-- Detail Button -->
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          color="#757D8A"
                          icon
                          outlined
                          height="40"
                          width="40"
                          class="mr-2"
                          v-bind="attrs"
                          v-on="on"
                          style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          @click="openSalesDetail(item)"
                        >
                          <v-icon color="#757D8A" size="24">mdi-file-document-outline</v-icon>
                        </v-btn>
                      </template>
                      <span>รายละเอียด</span>
                    </v-tooltip>
                    <!-- Edit Button -->
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          color="primary"
                          icon
                          outlined
                          height="40"
                          width="40"
                          class="mr-2"
                          v-bind="attrs"
                          v-on="on"
                          v-if="item.id === ownSaleID || checkAdminSale"
                          style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          @click="SaleDataDialog(item, 'edit')"
                        >
                          <v-icon color="primary" size="24">mdi-pencil</v-icon>
                        </v-btn>
                      </template>
                      <span>แก้ไขข้อมูล</span>
                    </v-tooltip>
                    <!-- Delete Button -->
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          color="#F5222D"
                          icon
                          outlined
                          height="40"
                          width="40"
                          v-bind="attrs"
                          v-if="checkAdminSale"
                          v-on="on"
                          style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          @click="SaleDataDialog(item, 'delete')"
                        >
                          <v-icon color="#F5222D" size="24">mdi-delete</v-icon>
                        </v-btn>
                      </template>
                      <span>ลบข้อมูล</span>
                    </v-tooltip>
                  </v-row>
                </template>
              </v-data-table>
            </v-card>
          </v-row>
        </v-col>
      </v-card-text>
    </v-card>
    <!-- Dialog Modal Detail Sale -->
    <v-dialog v-model="dialogDetailSales" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายละเอียดฝ่ายขาย</b></span>
              </v-col>
              <v-btn fab small @click="dialogDetailSales = !dialogDetailSales" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;"></v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" class="mr-0 d-flex">
                    <v-row dense class="mr-auto">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดข้อมูลฝ่ายขาย</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดข้อมูลฝ่ายขาย</p>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="4" sm="4" align="center">
                    <v-img :src="salesDetail.img_path" max-height="120" max-width="120" contain></v-img>
                  </v-col>
                  <v-col cols="12" md="8" sm="8" align="start">
                    <span style="color: #333333;"><b>ชื่อ - นามสกุล : </b> {{ salesDetail.sale_name }} </span><br/>
                    <span style="color: #333333;"><b>Sale Code : </b> {{ salesDetail.sale_code }} </span><br/>
                    <span style="color: #333333;"><b>เบอร์โทรศัพท์ : </b> {{ salesDetail.phone }} </span><br/>
                    <span style="color: #333333;"><b>E-mail : </b> {{ salesDetail.email }} </span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            </div>
            <v-card-actions>
              <v-row justify="center" dense style="height: 88px; background: #F5FCFB; " :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="dialogDetailSales = !dialogDetailSales">
                  ปิด
                </v-btn>
              </v-row>
            </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Dialog Modal Add Sale -->
    <v-dialog v-model="dialogAddSale" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มข้อมูลฝ่ายขาย</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogAddSale()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;"></v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" class="mr-0 d-flex">
                    <v-row dense class="mr-auto">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายชื่อพนักงานทั้งหมด</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายชื่อพนักงานทั้งหมด</p>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" align="canter">
                    <v-text-field v-model="searchEmployee" placeholder="ค้นหาพนักงาน" outlined dense hide-details></v-text-field>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" align="center" v-for="(item, index) in filteredUserInShop" :key="index">
                    <v-card width="100%" height="100%" elevation="4" style="border: 1px solid #F3F5F7; border-radius: 8px; cursor: pointer;" outlined @click="SaleDataDialog(item, 'add')">
                      <v-card-text>
                        <v-row dense>
                          <v-col cols="12" md="2" sm="4" style="text-align: center;">
                            <v-img :src="item.img_path" max-height="60" max-width="60" v-if="item.img_path !== null && item.img_path !== ''"></v-img>
                            <v-img src="@/assets/NoImage.png" max-height="60" max-width="60" v-else></v-img>
                          </v-col>
                          <v-col cols="12" md="10" sm="8" align="start">
                            <span style="color: #333333;"><b>ชื่อ - นามสกุล : </b> {{ item.sale_name }} </span><br/>
                            <span style="color: #333333;"><b>รหัสพนักงาน : </b> {{ item.employee_one_id }} </span><br/>
                            <span style="color: #333333;"><b>เบอร์โทรศัพท์ : </b> {{ item.phone }} </span><br/>
                            <span style="color: #333333;"><b>E-mail : </b> {{ item.email }} </span>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            </div>
            <v-card-actions>
              <v-row justify="center" dense style="height: 88px; background: #F5FCFB; " :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="closeDialogAddSale()">
                  ปิด
                </v-btn>
              </v-row>
            </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Dialog Input Sale Data -->
    <v-dialog v-model="dialogInputSaleData" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;" class="pt-0">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ actionOfButton === 'add' ? 'เพิ่มข้อมูลฝ่ายขาย' : 'แก้ไขข้อมูลฝ่ายขาย' }}</b></span>
                </v-col>
                <v-btn fab small @click="closeDialogInputSale()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;"></v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0">
                      <v-row dense>
                        <v-col cols="12" md="8" sm="8" align="start">
                          <v-row dense>
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                            <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">{{ actionOfButton === 'add' ? 'เพิ่มข้อมูลฝ่ายขาย' : 'แก้ไขข้อมูลฝ่ายขาย' }}</p>
                            <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>{{ actionOfButton === 'add' ? 'เพิ่มข้อมูลฝ่ายขาย' : 'แก้ไขข้อมูลฝ่ายขาย' }}</p>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" align="end" v-if="actionOfButton === 'edit' && checkAdminSale">
                          <v-switch v-model="statusSale" inset :label="`สถานะ : ${statusSale === 'Active' ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }`" :false-value="'inActive'" :true-value="'Active'"></v-switch>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row dense class="mt-4">
                    <v-col cols="12" md="12" sm="12" align="center">
                      <v-form ref="formAddSale" :lazy-validation="lazy">
                        <v-card width="100%" height="100%" elevation="0">
                          <v-card-text>
                            <v-row dense>
                              <v-col cols="12" md="12" sm="12" align="center">
                                <v-img :src="itemForSale.img_path" max-height="100" max-width="100" v-if="itemForSale.img_path !== null && itemForSale.img_path !== ''"></v-img>
                                <v-img src="@/assets/NoImage.png" max-height="100" max-width="100" v-else></v-img>
                              </v-col>
                              <v-col cols="12" md="12" sm="12" align="start">
                                <span style="color: #333333;"><b>ชื่อ - นามสกุล : </b></span><span style="color: red;"> *</span>
                                <v-text-field v-model="itemForSale.sale_name" placeholder="กรอกชื่อ - นามสกุล" outlined dense :rules="rules.name" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏-\s]/g, '')"></v-text-field>
                              </v-col>
                              <v-col cols="12" md="12" sm="12" align="start">
                                <span style="color: #333333;"><b>รหัสฝ่ายขาย : </b></span><span style="color: red;"> *</span>
                                <v-text-field v-model="itemForSale.sale_code" placeholder="กรอกรหัสฝ่ายขาย" outlined dense :rules="rules.code"></v-text-field>
                              </v-col>
                            </v-row>
                            <v-row dense v-if="checkAdminSale">
                              <v-col cols="12" md="8" sm="8" align="start">
                                <span style="color: #333333;"><b>สิทธิ์ในการเพิ่ม แก้ไขและลบข้อมูลลูกค้า</b></span><span style="color: red;"> *</span>
                              </v-col>
                              <v-col cols="12" md="4" sm="4" align="end" style="displaty: contents;">
                                <v-switch v-model="statusEditCustomer" inset :label="`สถานะ : ${statusEditCustomer === 'Y' ? 'เปิดใช้งาน' : 'ปิดใช้งาน' }`" :false-value="'N'" :true-value="'Y'" class="mt-0"></v-switch>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-form>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row justify="center" dense style="height: 88px; background: #F5FCFB; " :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="closeDialogInputSale()">
                  ปิด
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded width="125" height="40" dense class="white--text my-auto" color="#27AB9C" @click="OpenDialogAwaitSale()">
                  บันทึก
                </v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
    </v-dialog>
    <!-- Await Add/Edit Product -->
    <v-dialog v-model="dialogAwaitSale" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitSale = !dialogAwaitSale"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ actionOfButton === 'add' ? 'เพิ่มข้อมูล' : 'แก้ไขข้อมูล' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ actionOfButton === 'add' ? 'คุณต้องการเพิ่ม ' + `${itemForSale.sale_name}` + ' เป็น Sale' : 'คุณได้ทำการแก้ไขข้อมูล' }}</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitSale = !dialogAwaitSale">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="AddSale()" v-if="actionOfButton === 'add'">ตกลง</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="EditSale()" v-else>ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Await Delete Product -->
    <v-dialog v-model="dialogAwaitDeleteSale" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/DeleteProduct.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitDeleteSale = !dialogAwaitDeleteSale"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบฝ่ายขาย</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบฝ่ายขาย</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitDeleteSale = !dialogAwaitDeleteSale">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="DeleteSale(itemForSale)">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Add/Edit/Delete Product -->
    <v-dialog v-model="dialogSuccessSale" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ actionOfButton === 'add' ? 'บันทึกเสร็จสิ้น' : actionOfButton === 'delete' ? 'ลบเสร็จสิ้น' : 'แก้ไขเสร็จสิ้น' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ actionOfButton === 'add' ? 'คุณได้ทำการเพิ่มฝ่ายขายเรียบร้อย' : actionOfButton === 'delete' ? 'คุณได้ทำการลบฝ่ายขายเรียบร้อย' : 'คุณได้ทำการแก้ไขข้อมูลเรียบร้อย' }}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      headerListSales: [
        { text: 'ชื่อ - นามสกุล', value: 'sale_name', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส Sale', value: 'sale_code', filterable: true, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายชื่อลูกค้า', value: 'preview', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'details', filterable: false, width: '160', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      itemSales: [],
      searchSales: '',
      salesDetail: [],
      dialogDetailSales: false,
      showCountOrder: 0,
      dialogAddSale: false,
      dialogEditSale: false,
      dialogDeleteSale: false,
      listUserInShop: [],
      searchEmployee: '',
      itemForSale: [],
      dialogAwaitSale: false,
      actionOfButton: '',
      dialogSuccessSale: false,
      dialogAwaitDeleteSale: false,
      dialogInputSaleData: false,
      saleName: '',
      saleCode: '',
      saleTel: '',
      saleEmail: '',
      lazy: false,
      rules: {
        code: [
          v => !!v || 'กรุณากรอกรหัสฝ่ายขาย',
          v => /^[0-9a-zA-Z-]/.test(v) || 'กรุณากรอกรหัสฝ่ายขายให้ถูกต้อง'
        ],
        name: [
          v => !!v || 'กรุณากรอกชื่อ - นามสกุล',
          v => /^[a-zA-Zก-๏]/.test(v) || 'กรุณากรอกชื่อ - นามสกุลให้ถูกต้อง'
        ]
      },
      statusSale: 'inActive',
      checkAdminSale: false,
      dataDetail: [],
      ownSaleID: '',
      statusEditCustomer: 'N'
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    if (localStorage.getItem('list_shop_detail') !== null) {
      this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      this.ownSaleID = this.dataDetail.sale_id
      if (this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        this.checkAdminSale = true
      } else {
        this.checkAdminSale = false
      }
    }
    await this.getListSales()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredUserInShop () {
      return this.listUserInShop.filter(userData => {
        if (this.searchEmployee !== '') {
          return userData.sale_name.toLowerCase().includes(this.searchEmployee.toLowerCase()) || userData.employee_one_id.toLowerCase().includes(this.searchEmployee.toLowerCase())
        } else {
          return userData
        }
      })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listSalesMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listSales' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async getListSales () {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID'))
      }
      await this.$store.dispatch('actionsListSales', data)
      const response = await this.$store.state.ModuleSaleOrder.stateListSales
      // console.log('response', response)
      if (response.message === 'Get list sale data successfully.') {
        this.itemSales = response.data.all
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
      }
    },
    openSalesDetail (salesDetail) {
      this.salesDetail = []
      this.salesDetail = salesDetail
      this.dialogDetailSales = true
    },
    gotoCustomerOfSales (item) {
      localStorage.setItem('Detail_sales', Encode.encode(item))
      if (this.MobileSize) {
        this.$router.push({ path: `/listCustomerSalesMobile?sale_code=${item.sale_code}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/listCustomerSales?sale_code=${item.sale_code}` }).catch(() => {})
      }
    },
    getMessage (msg) {
      if (msg === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
        // return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'Data missing. Please check your parameter and try again.') {
        return 'ข้อมูลขาดหาย โปรดตรวจสอบและลองอีกครั้ง'
      } else if (msg === 'Shop not found.') {
        return 'ไม่พบร้านค้า'
      } else if (msg === 'Your data not found in this shop.') {
        return 'ไม่พบข้อมูลของคุณในร้านค้านี้'
      } else if (msg === 'Sale userID not found.') {
        return 'ไม่พบ UserID ของฝ่ายขายในระบบนี้'
      } else if (msg === 'This Sales is already exist.') {
        return 'มีฝ่ายขายคนนี้ในร้านอยู่แล้ว'
      } else if (msg === 'Cannot create sales data.') {
        return 'ไม่สามารถสร้างข้อมูลฝ่ายขายได้'
      } else if (msg === 'Has customer in active status. Please select new sales to the customer first.') {
        return 'มีลูกค้าที่ active อยู่ไม่สามารถปิดใช้งานได้ กรุณาเลือกฝ่ายขายใหม่ให้กับลูกค้าก่อน'
      } else if (msg === 'Has customer in active status. Please inActive the customer status or select new sales to the customer first.') {
        return 'มีลูกค้าที่ active อยู่ไม่สามารถปิดใช้งานได้ ต้องทำการปิดสถานะการใช้งานของลูกค้าหรือเลือกฝ่ายขายใหม่ให้กับลูกค้าก่อน'
      } else if (msg === 'Status is Active or inActive only.') {
        return 'สถานะคือ Active หรือ inActive เท่านั้น'
      } else if (msg === 'Unable to delete in your infomation.') {
        return 'ท่านไม่สามารถลบข้อมูลของท่านเองได้'
      } else {
        return 'An error has occurred. Please try again in an hour or two.'
      }
    },
    countSales (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async openDialogAddSale () {
      this.$store.commit('openLoader')
      this.listUserInShop = []
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID'))
      }
      await this.$store.dispatch('actionsGetAllUserShop', data)
      const response = await this.$store.state.ModuleSaleOrder.stateGetAllUserShop
      if (response.message === 'Get all user in shop success.') {
        this.listUserInShop = response.data.list_sale
        this.dialogAddSale = true
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
      }
    },
    SaleDataDialog (data, action) {
      // console.log('data for action ====>', data)
      this.itemForSale = []
      if (action === 'add') {
        this.actionOfButton = action
        this.itemForSale = data
        this.dialogAddSale = false
        this.dialogInputSaleData = true
      } else if (action === 'delete') {
        this.actionOfButton = action
        this.itemForSale = data
        this.dialogAwaitDeleteSale = true
      } else if (action === 'edit') {
        this.actionOfButton = action
        this.itemForSale = data
        this.statusSale = data.status
        this.statusEditCustomer = data.manage_customer
        this.dialogInputSaleData = true
      }
    },
    async AddSale () {
      if (this.itemForSale.employee_one_id === null) {
        this.itemForSale.employee_one_id = this.itemForSale.sale_code
      }
      this.dialogInputSaleData = false
      this.dialogAwaitSale = false
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        sale_user_id: this.itemForSale.sale_user_id,
        sale_name: this.itemForSale.sale_name,
        sale_code: this.itemForSale.employee_one_id,
        manage_customer: this.statusEditCustomer
      }
      await this.$store.dispatch('actionsCreateSaleShop', data)
      const response = await this.$store.state.ModuleSaleOrder.stateCreateSaleShop
      if (response.message === 'Create sales data success.') {
        this.dialogSuccessSale = true
        this.$EventBus.$emit('getUserDetailMP')
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
      }
    },
    async closeModalSuccess () {
      // await this.$EventBus.$emit('AuthorityUser')
      await this.$store.dispatch('actionsAuthorityUser')
      var responseShop = await this.$store.state.ModuleUser.stateAuthorityUser
      var ListSeller = responseShop.data.list_shop_detail
      for (let i = 0; i < ListSeller.length; i++) {
        if (parseInt(localStorage.getItem('shopSellerID')) === ListSeller[i].seller_shop_id) {
          // console.log('ListSeller', ListSeller)
          localStorage.removeItem('list_shop_detail')
          localStorage.setItem('list_shop_detail', Encode.encode(ListSeller[i]))
        }
      }
      await this.getListSales()
      this.dialogSuccessSale = false
    },
    async DeleteSale () {
      this.dialogAwaitDeleteSale = false
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        sale_id: this.itemForSale.id
      }
      await this.$store.dispatch('actionsDeleteSaleShop', data)
      const response = await this.$store.state.ModuleSaleOrder.stateDeleteSaleShop
      if (response.message === 'Delete sales data success.') {
        this.dialogSuccessSale = true
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 3000 })
      }
    },
    closeDialogAddSale () {
      this.dialogAddSale = false
      this.searchEmployee = ''
    },
    OpenDialogAwaitSale () {
      if (this.$refs.formAddSale.validate(true)) {
        this.dialogAwaitSale = true
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    closeDialogInputSale () {
      this.dialogInputSaleData = false
      this.itemForSale = []
    },
    async EditSale () {
      this.dialogInputSaleData = false
      this.dialogAwaitSale = false
      const data = {
        seller_shop_id: parseInt(localStorage.getItem('shopSellerID')),
        sale_id: this.itemForSale.id,
        sale_name: this.itemForSale.sale_name,
        sale_code: this.itemForSale.sale_code,
        status: this.statusSale,
        manage_customer: this.statusEditCustomer
      }
      await this.$store.dispatch('actionsEditSaleShop', data)
      const response = await this.$store.state.ModuleSaleOrder.stateEditSaleShop
      if (response.message === 'Update sales data success.') {
        this.dialogSuccessSale = true
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: this.getMessage(response.message), showConfirmButton: false, timer: 2500 })
      }
    }
  }
}
</script>

<style scoped>

</style>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
