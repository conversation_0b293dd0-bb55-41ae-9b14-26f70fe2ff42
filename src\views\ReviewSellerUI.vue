<template>
  <div>
    <ReviewSeller />
  </div>
</template>
<script>
// import dataMap from '../library/TestTable.json'

export default {
  components: {
    ReviewSeller: () => import('@/components/Shop/Review/ReviewSeller/ReviewSeller.vue')
    // ldsFacebook: () => import('@/components/loading/lds-facebook.vue')
  },
  data () {
    return {
      dataMain: []
    }
  },
  created () {
    this.init()
  },
  computed: {
  },
  watch: {
  },
  destroyed () {
  },
  methods: {
    async init () {
      const data2 = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        start_date: '',
        end_date: ''
      }
      await this.$store.dispatch('actionDetailMerchantFromPayment', data2)
      const response = await this.$store.state.ModuleShop.stateDetailMerchantFromPayment
      this.listTransfered = await response.data.list_transfered
      this.listWaiting = await response.data.list_waiting
      this.listRefund = await response.data.list_refund
      this.percentage = await response.data.percentageNewOld
      this.counterList = await response.data.summary_waiting
      this.ListData = await Object.values(response.data.list_waiting).length
      this.sheets = [{ name: 'รายการสำเร็จ', data: [...this.mapExcel(this.listTransfered)] }, { name: 'สินค้าที่ถูกตีกลับ', data: [...this.mapExcelSheetTwo(this.listRefund)] }]
      // console.log('respon', this.listTransfered, this.listWaiting)
    }
  }
}
</script>
