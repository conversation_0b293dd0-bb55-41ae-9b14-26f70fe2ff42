<template>
  <v-container :style="MobileSize ? 'background-color: white' : ''">
    <v-row>
      <v-col>
        <v-btn @click="backPages" icon v-if="MobileSize" color="#27AB9C">
          <v-icon>mdi-chevron-left</v-icon>
        </v-btn>
        <span style="font-weight: bold; align-content: center;" :style="IpadSize ? 'font-size: 18px;' : MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">จัดการหมวดหมู่สินค้า</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="6">
        <v-btn @click="backCategories" rounded elevation="0" color="white" v-if="categoryStack.length !== 0">
          <v-icon>mdi-chevron-left</v-icon>
          <span>ย้อนกลับ</span>
        </v-btn>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn color="#38b2a4" rounded @click="onOpenDialog">
          <v-icon color="white">mdi-plus</v-icon>
          <span class="white--text">เพิ่มหมวดหมู่สินค้า</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-breadcrumbs
          :items="breadcrumbItems"
          divider="/"
          class="pa-0 pl-3"
        >
          <template v-slot:item="{ item }">
            <span v-if="item.text === 'Default Category'" :style="isLastItem(item) ? 'font-size: 16px; font-weight: bold; color: #27AB9C;' : 'font-size: 16px; font-weight: bold;'">หมวดหมู่หลัก</span>
            <span v-else-if="item.text !== 'Default Category' && isLastItem(item)" style="font-size: 16px; font-weight: bold; color: #27AB9C;">{{ item.text }}</span>
            <span v-else style="font-size: 16px; font-weight: bold;">{{ item.text }}</span>
          </template>
          <template v-slot:divider>
            <span style="font-size: 16px; font-weight: bold;">/</span>
          </template>
        </v-breadcrumbs>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <v-card>
          <v-data-table
            :headers="categoriesDataTableHeaders"
            :items="categoriesDataTable"
          >
            <template v-slot:[`item.index`]="{index}">
                <span>{{index + 1}}</span>
            </template>
            <template v-slot:[`item.children`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                    :disabled="item.children === undefined"
                    outlined
                    color="#27AB9C"
                    @click="onOpenchildrenCategories(item)"
                    class="pt-4 pb-4"
                >
                    <span>ดูหมวดหมู่ย่อย</span>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog
      v-model="openDialog"
      max-width="500px"
      class="setOverflow"
      persistent
    >
      <v-card width="100%" height="100%">
        <v-card-title style="background-color: #bde7d9;">
          <v-row>
            <v-col class="d-flex justify-center pa-3" :cols="MobileSize ? 10 : 11">
              <span style="color: rgb(39, 171, 156); font-size: 20px; font-weight: bold;" :class="MobileSize ? 'ml-10' : ''">ตั้งชื่อหมวดหมู่</span>
            </v-col>
            <v-col :cols="MobileSize ? 2 : 1">
              <v-btn icon @click="onCloseDialog">
                <v-icon color="rgb(39, 171, 156)">mdi-close</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card-text>
            <v-row class="mt-3">
              <v-col>
                <span style="font-size: 18px; font-weight: bold;">เลือกหมวดหมู่สินค้า</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <!-- <a-cascader
                  v-model="selectedCategory"
                  :options="categoriesData"
                  size="large"
                  change-on-select
                  :show-search="true"
                  placeholder="กรุณาเลือกหมวดหมู่สินค้า"
                  notFoundContent="ไม่พบหมวดหมู่ที่ค้นหา"
                  style="font-size: 0.9em !important; width: 100%;"
                  @popupVisibleChange="handlePopupVisibleChange"
                  @change="substring"
                /> -->
              <treeselect
              style="z-index: 2;"
              v-model="selectedCategory"
              :options="categoriesData"
              placeholder="เลือกหมวดหมู่"
              ></treeselect>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <span style="font-size: 18px; font-weight: bold;">ชื่อหมวดหมู่สินค้า</span>
              </v-col>
            </v-row>
            <v-row>
            <v-col>
              <v-text-field
                placeholder="ระบุชื่อหมวดหมู่"
                solo
                hide-details
                v-model="categoriesName"
                dense
                style="border: #d9d9d9;"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row>
            <v-col cols="6" class="d-flex justify-center">
              <v-btn color="#38b2a4" rounded @click="onCloseDialog">
                <span class="white--text">ยกเลิก</span>
              </v-btn>
            </v-col>
            <v-col cols="6" class="d-flex justify-center">
              <v-btn color="#38b2a4" rounded @click="submitCreate" disabled v-if="selectedCategory === undefined">
                <span class="white--text">ยืนยัน</span>
              </v-btn>
              <v-btn color="#38b2a4" rounded @click="submitCreate" v-else>
                <span class="white--text">ยืนยัน</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: {
    Treeselect
  },
  data () {
    return {
      normalizer (node) {
        return {
          id: node.id,
          label: node.label,
          children: node.children
        }
      },
      categoriesData: [],
      selectedCategory: null,
      openDialog: false,
      categoriesName: '',
      categoriesRequestBody: {
        idHeadCategory: '',
        newCategory: ''
      },
      categoriesDataTable: [],
      categoriesDataTableHeaders: [
        { text: 'ลำดับ', value: 'index', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสหมวดหมู่', value: 'id', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อหมวดหมู่', value: 'label', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนผลิตภัณฑ์', value: 'product_count', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'หมวดหมู่ย่อย', value: 'children', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      currentCategoryId: '',
      categoryStack: [],
      breadcrumbItems: [
        { text: 'Default Category' }
      ]
    }
  },
  async created () {
    await this.$EventBus.$emit('changeNavAdmin')
    await this.getCategoriesData()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageCategoryMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'ManageCategory')
        this.$router.push({ path: '/ManageCategory' }).catch(() => {})
      }
    },
    selectedCategory (val) {
      console.log(val)
    },
    categoriesDataTable (val) {
      // console.log(val, 'data table')
    },
    categoryStack (val) {
      // console.log(val)
    },
    categoriesData (val) {
      // console.log(val, 'data')
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // selectedCategory (val) {
    //   console.log(val)
    // },
    // categoriesDataTable (val) {
    //   console.log(val)
    // }
  },
  methods: {
    async getCategoriesData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('GetCagegory')
      var ResponseListCategory = await this.$store.state.ModuleManageShop.Category.data.data
      this.categoriesData = ResponseListCategory[0].children
      this.categoriesDataTable = ResponseListCategory[0].children
      this.categoryStack = []
      this.$store.commit('closeLoader')
    },
    async onOpenDialog () {
      this.openDialog = true
    },
    async onCloseDialog () {
      this.selectedCategory = ''
      this.categoriesName = ''
      this.openDialog = false
    },
    async submitCreate () {
      this.$store.commit('openLoader')
      if (this.selectedCategory === undefined) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกหมวดหมู่'
        })
        this.$store.commit('closeLoader')
      }
      this.categoriesRequestBody.idHeadCategory = this.selectedCategory
      // for (var i = 0; i < this.selectedCategory.length; i++) {
      //   if (i + 1 === this.selectedCategory.length) {
      //     this.categoriesRequestBody.idHeadCategory = this.selectedCategory[i]
      //   }
      // }
      // this.categoriesName
      this.categoriesRequestBody.newCategory = this.categoriesName
      this.openDialog = false
      await this.$store.dispatch('ActionCreateCategories', this.categoriesRequestBody)
      var response = await this.$store.state.ModuleManageShop.newCategories
      if (response.data.code === 400) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'error',
          title: 'สร้างหมวดหมู่ไม่สำเร็จกรุณากรอกข้อมูล'
        })
      } else if (response.data.code === 409) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ชื่อหมวดหมู่ซํ้า'
        })
      } else if (response.data.code === 200) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'success',
          title: 'สร้างหมวดหมู่สำเร็จ'
        })
      }
      await this.getCategoriesData()
      this.$store.commit('closeLoader')
      this.breadcrumbItems = [
        { text: 'Default Category' }
      ]
      this.selectedCategory = ''
      this.categoriesName = ''
    },
    async onOpenchildrenCategories (val) {
      // console.log(val.children)
      this.categoryStack.push(this.categoriesDataTable)
      this.breadcrumbItems.push({ text: `${val.label}` })
      this.currentCategoryId = val.value
      this.categoriesDataTable = val.children
      window.scrollTo(0, 0)
    },
    async backCategories () {
      if (this.categoryStack.length > 0) {
        this.categoriesDataTable = this.categoryStack.pop()
        this.breadcrumbItems.pop()
      }
    },
    async filterFunction (inputValue, path) {
      return path.some(option => option.label.toLowerCase().includes(inputValue.toLowerCase()))
    },
    isLastItem (item) {
      return this.breadcrumbItems.indexOf(item) === this.breadcrumbItems.length - 1
    },
    async backPages () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
  >>> .v-dialog {
    overflow-y: hidden;
  }
</style>

<style>
@media (max-width: 2560px) and (min-width: 768px) {
  /* Media Query สำหรับ iPad Pro (1024px) */
  .vue-treeselect--open-below:not(.vue-treeselect--append-to-body) .vue-treeselect__menu-container {
    max-width: 452px;
    top: auto;
    left: auto;
    position: fixed;
  }
}
@media (max-width: 500px) and (min-width: 275px) {
  .vue-treeselect--open-below:not(.vue-treeselect--append-to-body) .vue-treeselect__menu-container {
    top: auto;
    position: fixed;
  }
}
.ant-cascader-menus {
    position: fixed !important;
    z-index: 1050;
    font-size: 14px;
    white-space: nowrap;
    background: #fff;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.v-text-field.v-text-field--solo:not(.v-text-field--solo-flat)>.v-input__control>.v-input__slot {
  border: 1px solid #d9d9d9 !important;
}
</style>
