<template>
  <v-container :class="MobileSize ? 'background_color_Mobile' : 'background_color'" grid-list-xs rounded>
    <v-row dense :class="MobileSize ? 'pt-3 pb-3' : 'pt-3 pb-3'">
      <v-col cols="12">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; line-height: 32px; font-weight: bold;'">
          <v-icon v-if="MobileSize" @click="cancle()" color="#27AB9C" size="30">mdi-chevron-left</v-icon>
          แนบรูปยืนยันเข้ารับพัสดุ
        </span>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'pt-1 pb-3' : 'pt-1 pb-3'">
      <v-col cols="12">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 16px; line-height: 24px;' : 'font-size: 20px; line-height: 30px; font-weight: bold;'">คำสั่งซื้อที่ยังไม่ดำเนินการ</span>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'py-1' : 'py-1 pl-3'" v-if="dataOrder.length !== 0">
      <v-col cols="12" md="6" sm="6">
        <v-checkbox v-model="checkAllOrder" label="เลือกรายการทั้งหมด" color="#27AB9C" @click="SelectAll(dataOrder, checkAllOrder)" hide-details></v-checkbox>
      </v-col>
      <v-col cols="12" md="6" sm="6" :align="IpadSize ? 'end': 'end'" class="mt-2">
        <v-btn :block="MobileSize ? true : false" @click="ShowDialog()" :height="!MobileSize && !IpadProSize && !IpadSize ? '40' : '40'" rounded color="#27AB9C" class="white--text">แนบรูปยืนยันเข้ารับพัสดุ</v-btn>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? '' : 'pl-3'" v-if="dataOrder.length !== 0">
      <v-col cols="12" v-for="(items, index) in dataOrder" :key="index" class="mb-4" :class="MobileSize ? 'px-0' : ''">
        <v-card width="100%" height="100%" outlined style="border-radius: 8px;">
          <v-card-text :class="MobileSize ? 'px-2' : ''">
            <v-col cols="12" align="end" v-if="MobileSize">
              <v-row justify="end">
                <v-col cols="6" class="pa-1 mt-2">
                  <v-btn block outlined color="#27AB9C" height="36" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                    <v-icon small class="mr-2">mdi-plus-circle-outline</v-icon>แนบรูปยืนยันเข้ารับพัสดุ
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
            <v-row dense>
              <v-col :cols="!MobileSize && !IpadProSize && !IpadSize ? '6' : MobileSize ? '12' : '6'" align="start" class="pt-3 my-checkbox">
                <v-checkbox v-model="items.isSelect" class="mt-0" :label="'รายการสั่งซื้อสินค้า' + ' ' + `${ items.product_list.length }` + ' ' + 'รายการ'" color="#27AB9C" hide-details></v-checkbox>
              </v-col>
              <v-col cols="6" align="end" v-if="!MobileSize && !IpadProSize && !IpadSize" style="display: inline-block; margin: auto;">
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">แนบรูปยืนยันเข้ารับพัสดุ</div>
                </v-btn>
              </v-col>
              <v-col cols="6" align="end" v-if="IpadProSize && !IpadSize && !MobileSize" style="display: inline-block; margin: auto;">
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">แนบรูปยืนยันเข้ารับพัสดุ</div>
                </v-btn>
              </v-col>
              <v-col cols="6" align="end" v-if="!IpadProSize && IpadSize && !MobileSize" style="display: inline-block; margin: auto;">
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">แนบรูปยืนยันเข้ารับพัสดุ</div>
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense class="pt-2">
              <v-col cols="12">
                <!-- <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">หมายเหตุจากขนส่ง : <b style="color: #FAAD14;">{{ items.shipping_remark === '' ? '-' : items.shipping_remark }}</b></p> -->
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">รหัสคำสั่งซื้อ : <b>{{ items.order_number }}</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">วันที่สั่งซื้อ : <b>{{ new Date(items.paid_datetime).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric", hour: "numeric", minute: "numeric" }) }} น.</b></p>
                <p style="font-weight: 700; font-size: 18px; line-height: 24px; color: #000000;"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;"><b>{{ items.shipping_data.dst_data.dst_name }}</b> <br v-if="MobileSize"/> {{ items.shipping_data.dst_data.dst_address }}</p><br>
                <ul>
                  <li>
                    <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><b>Standard Delivery</b> : <br v-if="MobileSize"/><b>{{ items.shipping_data.tpl_name }}</b></span><br>
                  </li>
                </ul>
              </v-col>
            </v-row>
            <v-row v-if="items.media_details.length !== 0">
              <v-col v-for="(items, index2) in items.media_details" :key="index2" cols="12" md="4" sm="4">
                  <v-card outlined class="pa-1" width="146" height="146">
                    <v-img :src="items.media_path" width="130" height="130" contain>
                    </v-img>
                  </v-card>
              </v-col>
            </v-row>
            <!-- <v-row dense class="pt-2">
              <v-col cols="12">
                <v-data-table
                 :headers="MobileSize ? dataTableheaderMobile : dataTableheader"
                 :hide-default-header="MobileSize ? true : false"
                 :items="items.expand_table === false ? items.product_list_not_expand : items.product_list"
                 :items-per-page="50"
                 hide-default-footer
                 style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));"
                >
                  <template v-slot:[`item.product_detailMobile`]="{ item }">
                    <v-row dense>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                        <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <v-row dense class="py-4">
                      <v-col cols="2" class="">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                  <template v-slot:[`item.total_price`]="{ item }">
                    {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                </v-data-table>
                <v-data-table
                 :headers="MobileSize ? dataTableheaderPromotionMobile : dataTableheaderPromotion"
                 :items="items.product_promotion"
                 v-if="items.product_promotion.length !== 0"
                 hide-default-footer
                 class="headerPromotion"
                 style="background: #F5FBFF; filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));"
                >
                  <template v-slot:[`item.product_detailMobile`]="{ item, index }">
                    <v-row dense justify="start">
                      <v-col cols="12" class="pt-4" align="start" v-if="index === 0">
                        <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #27AB9C;" >สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                        <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.product_detail`]="{ item, index }">
                    <v-row dense>
                      <v-col cols="12" class="pt-4"  v-if="index === 0">
                        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="2" class="">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="start">
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    <v-row dense>
                      <v-col cols="12">
                        {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.quantity`]="{ item }">
                    <v-row dense>
                      <v-col cols="12">
                        {{ item.quantity }}
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.net_price`]="{ item }">
                    <v-row dense>
                      <v-col cols="12">
                        {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </v-col>
                    </v-row>
                  </template>
                </v-data-table>
                <v-row dense justify="center" class="mt-0" v-if="items.product_list.length > 1">
                  <v-col cols="12" align="center">
                    <v-btn text block @click="ShowExpand(dataOrder, items.prepare_order_id, !items.expand_table)">
                      <span :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'" style="font-weight: 400; line-height: 22px; text-decoration-line: underline; color: #27AB9C;" v-if="items.expand_table === false">ดูเพิ่มเติม</span><v-icon color="#27AB9C" v-if="items.expand_table === false">mdi-chevron-down</v-icon>
                      <span :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'" style="font-weight: 400; line-height: 22px; text-decoration-line: underline; color: #27AB9C;" v-if="items.expand_table === true">ย่อขนาด</span><v-icon color="#27AB9C" v-if="items.expand_table === true">mdi-chevron-up</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>
            </v-row> -->
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense v-else>
      <v-col cols="12">
        <v-card width="100%" :height="MobileSize ? '20vh' : '10vh'" outlined style="border-radius: 8px; border-color: #27AB9C;">
          <v-card-text class="text-center">
            <v-row justify="center" style="text-align: center; margin: auto;">
              <span style="font-size: 1rem; font-weight: 700;" :class="MobileSize ? 'pt-6' : IpadSize ? 'pt-6' : 'pt-2'">ไม่มีคำสั่งซื้อที่ยังไม่ดำเนินการ</span>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog v-model="dialogUploadPhotoShipping" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แนบรูปยืนยันเข้ารับพัสดุ
          </span>
           <v-btn icon dark @click="closeDialogUploadPhotoShipping()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <!-- อัปโหลดรูปภาพ -->
          <v-row>
            <v-col cols="12">
              <span>รหัสคำสั่งซื้อ: </span>
            </v-col>
            <v-col v-for="(item, index) in orderSelect" :key="index" cols="12">
              <span>{{ item.order_number }}</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-card
              class="mt-3"
              elevation="0"
              @click="onPickFileByOrder()"
              @drop.prevent="DropImageByOrder($event)"
              :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
              >
              <v-file-input
                  v-model="DataImage"
                  :items="DataImage"
                  accept="image/jpeg, image/jpg, image/png"
                  @change="onFileSelectedByOrder(DataImage)"
                  id="file_input"
                  multiple
                  :clearable="false"
                  style="display:none"
              ></v-file-input>
              <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                  <v-col cols="12" md="12" align="center">
                      <v-img
                      src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                      width="280.34"
                      height="154.87"
                      contain
                      v-if="orderImagePath.length === 0"
                      ></v-img>
                      <v-row v-if="orderImagePath.length !== 0">
                      <v-col v-for="(items, index2) in orderImagePath" :key="index2" cols="12" md="4" sm="4">
                          <v-card outlined class="pa-1" width="146" height="146">
                          <v-img :src="items" width="130" height="130" contain>
                              <v-btn icon x-small style="float: right; background-color: #ff5252;">
                              <v-icon x-small color="white" dark @click.prevent.stop="RemoveImageMultiByOrder(index2)">mdi-close</v-icon>
                              </v-btn>
                          </v-img>
                          </v-card>
                      </v-col>
                      </v-row>
                  </v-col>
                  <v-col cols="12" md="12" style="text-align: center;" v-if="orderImagePath.length === 0">
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                  </v-col>
                  </v-row>
              </v-col>
              </v-card>
              <v-row v-if="DataImage.length !== 0">
                <v-col v-for="(item, index) in DataImage" :key="index">
                  <canvas :id="`canvas${index}`" :ref="`canvas${index}`" style="display: none;"></canvas>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogUploadPhotoShipping()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmUploadIMG()">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      theRedI: true,
      dataOrder: [],
      listShow: [],
      DataImage: [],
      orderImagePath: [],
      base64ImageList: [],
      shopID: localStorage.getItem('shopSellerID'),
      checkAllOrder: false,
      orderSelectAll: [],
      dialogUploadPhotoShipping: false,
      orderSelect: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/UpLoadPickUpOrderIMGMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/UpLoadPickUpOrderIMG' }).catch(() => {})
      }
    }
  },
  async created () {
    // this.$EventBus.$emit('SelectPath')
    // this.$EventBus.$emit('checkAuthUser')
    await this.GetOrderToUploadIMG()
  },
  methods: {
    onPickFileByOrder () {
      document.getElementById('file_input').click()
    },
    async onFileSelectedByOrder (files) {
      // console.log('Ready to upload:', files, files.length)
      this.base64ImageList = []
      this.DataImage = files
      // await this.EditWaterMark()
      var files2 = await this.EditWaterMark(files)
      // console.log(this.base64ImageList, this.base64ImageList.length, 'this.base64ImageList')
      if (files2 && files2.length > 0) {
        const file = files2
        // console.log(file, 'file[i]')
        var base64ImageList = []
        for (let i = 0; i < file.length; i++) {
          const element = file[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
            const base64 = await new Promise((resolve, reject) => {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = async () => {
                resolve(reader.result.split(',')[1])
              }
              reader.onerror = reject
            })

            base64ImageList.push(base64)
          }
        }
        const Image = {
          image: base64ImageList,
          type: 'shipment',
          seller_shop_id: this.shopID
        }

        this.$store.commit('openLoader')

        await this.$store.dispatch('actionsUploadToS3', Image)
        const response = this.$store.state.ModuleShop.stateUploadToS3
        if (response.message === 'List Success.') {
          this.$store.commit('closeLoader')
          var imagePath = response.data.list_path.map(e => e.path)
          for (var i = 0; i < imagePath.length; i++) {
            if (this.orderImagePath.length < 3) {
              this.orderImagePath.push(imagePath[i])
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'ใส่ไม่ได้เกิน 3 ภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
          this.theRedI = true
        }
      }
    },
    RemoveImageMultiByOrder (index2) {
      this.orderImagePath.splice(index2, 1)
    },
    async EditWaterMark (files) {
      // console.log(files, 'files')

      const processedFiles = await Promise.all(
        Array.from(files).map((file, index) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader()

            reader.onload = (e) => {
              const img = new Image()

              img.onload = () => {
                const canvas = this.$refs[`canvas${index}`][0]
                if (!canvas) {
                  console.error(`ไม่พบ canvas${index}`)
                  return reject(new Error(`Canvas ${index} not found`))
                }

                const ctx = canvas.getContext('2d')
                canvas.width = img.width
                canvas.height = img.height

                ctx.drawImage(img, 0, 0)

                // Add timestamp
                const timestamp = new Date().toLocaleString()
                const fontSize = Math.floor(canvas.width * 0.035)
                ctx.font = `${fontSize}px Arial`
                ctx.fillStyle = 'white'
                ctx.textAlign = 'right'
                ctx.textBaseline = 'bottom'

                const padding = 10
                ctx.strokeStyle = 'black'
                ctx.lineWidth = 3
                ctx.strokeText(timestamp, canvas.width - padding, canvas.height - padding)
                ctx.fillText(timestamp, canvas.width - padding, canvas.height - padding)

                canvas.toBlob((blob) => {
                  if (!blob) return reject(new Error('Blob is null'))

                  const newFile = new File([blob], 'timestamped_' + file.name, { type: blob.type })
                  resolve(newFile)
                }, file.type)
              }

              img.onerror = (e) => reject(new Error('Image load failed'))
              img.src = e.target.result
            }

            reader.onerror = (e) => reject(new Error('FileReader failed'))
            reader.readAsDataURL(file)
          })
        })
      )

      this.base64ImageList = processedFiles
      return processedFiles
    },
    async GetOrderToUploadIMG () {
      // console.log('come')
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID,
        count: 10,
        courier_code: '',
        start_date: '',
        end_date: '',
        search_keyword: '',
        page: 1,
        status: 'all'
      }
      await this.$store.dispatch('actionsGetWithoutImages', data)
      var res = await this.$store.state.ModuleDashboardTransport.stateGetWithoutImages
      // console.log(res, 'res')
      if (res.message === 'Get orders list success') {
        this.$store.commit('closeLoader')
        this.dataOrder = res.data.data.map(e => {
          return {
            ...e,
            isSelect: false
          }
        })
      }
    },
    async getCourierType () {
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierTypeII', data)
      const response = await this.$store.state.ModuleManageShop.GetCourierTypeII
      const dataListCourier = []
      var listCourier = response.data.data
      for (let i = 0; i < listCourier.length; i++) {
        dataListCourier.push({
          courier_name: listCourier[i].name === '' ? 'ขนส่งทั้งหมด' : listCourier[i].name,
          courier_code: listCourier[i].type_id
        })
      }
      this.listCourier = await dataListCourier
    },
    SelectAll (data, check) {
      // console.log(data, check)
      if (check === true) {
        this.dataOrder = []
        for (var i = 0; i < data.length; i++) {
          data[i].isSelect = true
        }
        this.dataOrder = data
      } else {
        this.dataOrder = []
        for (var j = 0; j < data.length; j++) {
          data[j].isSelect = false
        }
        this.dataOrder = data
      }
    },
    ShowDialog (items) {
      // console.log(items, 45)
      if (items) {
        this.orderSelect.push({ order_number: items.order_number })
      } else {
        this.orderSelect = this.dataOrder.filter(e => e.isSelect === true)
      }
      // console.log(items, 'items')
      this.dialogUploadPhotoShipping = true
    },
    closeDialogUploadPhotoShipping () {
      this.dialogUploadPhotoShipping = false
      this.orderImagePath = []
      this.DataImage = []
      this.orderSelect = []
    },
    async confirmUploadIMG () {
      // this.orderSelect = this.dataOrder.filter(e => e.isSelect === true)
      var data = {
        seller_shop_id: this.shopID,
        mappings: [
          {
            order_numbers: this.orderSelect.map(e => e.order_number),
            images: this.orderImagePath
          }
        ]
      }
      await this.$store.dispatch('actionsUploadProofOfDelivery', data)
      const response = this.$store.state.ModuleDashboardTransport.stateUploadProofOfDelivery
      if (response.message === 'Save images success.') {
        this.$swal.fire({
          icon: 'success',
          text: 'อัปโหลดรูปภาพสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        this.$swal.fire({
          icon: 'error',
          text: response.message,
          showConfirmButton: false,
          timer: 1500
        })
      }
      this.orderSelect = []
      this.dialogUploadPhotoShipping = false
      await this.GetOrderToUploadIMG()
    }
  }
}
</script>

<style>

</style>
