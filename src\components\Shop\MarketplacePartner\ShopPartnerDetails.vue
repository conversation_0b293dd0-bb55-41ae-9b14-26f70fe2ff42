<template>
  <v-container class="pa-4">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-col cols="12" class="pt-6">
        <span v-if="MobileSize" class="f-left" style="color: #333333; font-weight: 700; font-size: 24px;">
          <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ข้อมูลร้านค้า Partner
        </span>
        <span v-else class="f-left" style="color: #333333; font-weight: 700; font-size: 24px;">
          <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ข้อมูลร้านค้า Partner
        </span>
      </v-col>

      <v-container>
        <v-card elevation="0">
          <v-row no-gutters>
            <v-col cols="12" md="12" sm="12" :style="IpadSize? 'position: relative; height: 340px;': MobileSize ? 'position: relative; height: 210px;' :IpadProSize?'position: relative; height: 460px;': 'position: relative; height: 519px;'">
              <v-carousel
                style="position: absolute; border-radius: 12px;"
                :height=" IpadSize ? '220': IpadProSize? '290': MobileSize && showDataServicePartner.partner_media && showDataServicePartner.partner_media.length !== 0 ? '110': MobileSize && showDataServicePartner.partner_media && showDataServicePartner.partner_media.length === 0 ? '120':'380'"
                hide-delimiters
              >
                <v-carousel-item v-for="(image, index) in filteredBanners" :key="index">
                  <v-img
                    :src="image.media_path"
                    height="100%"
                    width="100%"
                    alt="Software Marketplace"
                  ></v-img>
                </v-carousel-item>
                <template v-if="filteredBanners.length === 0">
                  <v-carousel-item>
                    <v-img
                      src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                      height="100%"
                      width="100%"
                      contain
                    ></v-img>
                  </v-carousel-item>
                </template>
              </v-carousel>
              <v-col :style="IpadSize ? 'position: absolute; margin-top: 148px;' :IpadProSize ? 'position: absolute; margin-top: 150px;': MobileSize ? 'position: absolute; margin-top: 19%;': 'position: absolute; margin-top: 235px;'" cols="12" md="12" sm="12" class="pr-0">
                <v-row no-gutters>
                  <v-col dense no-gutters :cols="MobileSize? 5 : 5" :md="IpadProSize ? 5 : 4" :class="IpadSize ? 'pa-2' : MobileSize ? '': 'pa-2 pl-0'" style="text-align: center;">
                    <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-if="showDataServicePartner && showDataServicePartner.partner_media && showDataServicePartner.partner_media.find(image => image.image_type === 'main')">
                      <v-img
                        :src="showDataServicePartner.partner_media.find(image => image.image_type === 'main').media_path"
                        contain
                        style="border: 5px solid #ffffff"
                      ></v-img>
                    </v-avatar>
                    <v-avatar color="#FFFFFF" :size="IpadSize ? '125': MobileSize ? '90' : '244'" v-else>
                      <v-img
                        src="@/assets/NoImage.png"
                        style="border: 10px solid #ffffff"
                      ></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col :cols="MobileSize ? 7 : 7" :md="IpadProSize ? 7 : 8" :style="MobileSize ? 'padding-top: 35px' :IpadSize ? 'padding-top: 80px' : 'padding-top: 150px'">
                    <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 24px;' "><b>{{ showDataServicePartner && showDataServicePartner.partner_name }}</b></span><br>
                    <span><b>{{ showDataServicePartner && showDataServicePartner.business_name_th }}</b></span><br>
                    <span :class="IpadSize || IpadProSize ? 'inline-flex' : 'inline-flex-desktop'">
                      <!-- <v-img @click="facebookUrl()"
                        :class="IpadSize || IpadProSize ? 'classFBLineSizeIpad' : 'classFBLineSizeDesk'"
                        src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                      ></v-img> -->
                      <!-- <v-img @click="lineUrl()"
                        :class="IpadSize || IpadProSize ? 'classFBLineSizeIpad' : 'classFBLineSizeDesk'"
                        src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                      ></v-img> -->
                      <a :href="`${facebookURL}`" target="_blank" :class="facebookURL === null ? 'disabled-link' : ''">
                        <v-btn
                        color="#3b5998"
                        fab
                        x-small
                        dark
                        style="margin-top: -2px; box-shadow: none;">
                        <v-img :class="IpadSize || IpadProSize? 'classFBLineSizeIpad':'classFBLineSizeDesk'"
                          src="@/assets/ImageINET-Marketplace/Shop/facebooklogo.png"
                        ></v-img>
                        </v-btn>
                      </a>
                      <a :href="`${lineURL}`" target="_blank" :class="lineURL === '' ? 'disabled-link' : ''">
                        <v-btn
                        color="#39cd00"
                        fab
                        x-small
                        dark
                        style="margin-top: -2px; box-shadow: none;">
                          <v-img :class="IpadSize || IpadProSize? 'classFBLineSizeIpad':'classFBLineSizeDesk'"
                          src="@/assets/ImageINET-Marketplace/Shop/linelogo.png"
                        ></v-img>
                      </v-btn>
                      </a>
                      <span>| {{ showDataServicePartner && showDataServicePartner.phone_no }}</span>
                    </span>
                  </v-col>
                  <v-col class="pt-3" style="text-align: end;" v-if="partnerName && showDataServicePartner.partner_name && partnerName.includes(showDataServicePartner.partner_name.toLowerCase()) && this.getAvaliablePackage === 'normal'">
                    <v-btn rounded dark color="#27AB9C" @click="selectServiceZort()"><b>เป็นผู้ใช้บริการ ZORT</b></v-btn>
                  </v-col>
                </v-row>
              </v-col>
            </v-col>
          </v-row>
        </v-card>
        <br>
        <br>
        <v-row>
          <v-col cols="12" class="d-flex align-center">
            <img
              src="@/assets/Marketplace_partner/Capa_1.png"
              alt="เกี่ยวกับ Partner"
              width="25px"
              class="mr-2"
            />
            <span><b>เกี่ยวกับ Partner</b></span>
          </v-col>
          <v-col cols="12">
            <span>{{ showDataServicePartner && showDataServicePartner.partner_detail }}</span>
          </v-col>
          <v-col cols="12">
            <span>เว็บไซต์ : {{ showDataServicePartner && showDataServicePartner.path_website }}</span>
          </v-col>
          <v-col cols="12" class="d-inline-flex align-center">
            <div style="display: flex; overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px; max-width: 100%;">
              <span style="position: sticky; left: 0; background-color: white; flex-shrink: 0; z-index: 1; display: flex; align-items: center;">
                ประเภทบริการ :
              </span>
              <v-chip-group class="d-inline-flex" style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                <v-chip
                  v-if="showDataServicePartner && showDataServicePartner.type_partner && showDataServicePartner.type_partner.includes('ERP')"
                  label
                  class="ml-2"
                  style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"><b>ERP</b>
                  <!-- style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;" v-bind:style="{ padding: MobileSize ? '3px 6px' : '', fontSize: MobileSize ? '10px' : '' }"><b>ERP</b> -->
                </v-chip>
                <v-chip
                  v-if="showDataServicePartner && showDataServicePartner.type_partner && showDataServicePartner.type_partner.includes('Web Development')"
                  label
                  class="ml-2"
                  style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"><b>Web Development</b>
                </v-chip>
                <v-chip
                  v-if="showDataServicePartner && showDataServicePartner.type_partner && showDataServicePartner.type_partner.includes('POS')"
                  label
                  class="ml-2"
                  style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"><b>POS</b>
                </v-chip>
                <v-chip
                  v-if="showDataServicePartner && showDataServicePartner.type_partner && showDataServicePartner.type_partner.includes('OMS')"
                  label
                  class="ml-2"
                  style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"><b>OMS</b>
                </v-chip>
                <v-chip
                  v-if="showDataServicePartner && showDataServicePartner.type_partner && showDataServicePartner.type_partner.includes('Marketing')"
                  label
                  class="ml-2"
                  style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"><b>Marketing</b>
                </v-chip>
              </v-chip-group>
            </div>
          </v-col>
          <v-col cols="12" v-if="getAvaliableDocument !== '-' || getAvaliableLink !== '-'" >
            <v-row>
              <v-col cols="auto">
                <img
                  src="@/assets/Marketplace_partner/Frame.png"
                  width="25px"
                  class="mr-2"
                />
                <span>ข้อมูลการใช้งาน : </span>
                <template v-if="getAvaliableDocument !== '-'">
                  <v-icon color="#27AB9C" small class="pr-1">mdi mdi-download</v-icon>
                  <span
                    style="cursor: pointer; color: #27AB9C; text-decoration: underline;"
                    @click="openLink(getAvaliableDocument)"
                  >
                    ดาวน์โหลดคู่มือการใช้งาน
                  </span>
                  <span v-if="getAvaliableLink !== '-'" style="margin: 0 8px;">|</span>
                </template>
                <template v-if="getAvaliableLink !== '-'">
                  <v-icon class="pr-1" small color="#FAAD14">mdi mdi-account-group</v-icon>
                  <span
                    style="cursor: pointer; color: #FAAD14; text-decoration: underline;"
                    @click="openLinkSeminar(getAvaliableLink)"
                  >
                    เข้าร่วมสัมมนา
                  </span>
                </template>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12">
            <v-row style="margin-left: 0px; margin-right: 0px">
              <h1 style="font-size:24px; font-weight: 700; color:#27AB9C;">สินค้า</h1>
              <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px;"></v-spacer>
            </v-row>
          </v-col>
          <v-col cols="12" class="text-center" style="font-size: 18px;">
            <!-- <span><b>{{ showDataServicePartner && showDataServicePartner.partner_name }}</b></span> -->
            <span><b>{{ showDataServicePartner && showDataServicePartner.service_name }}</b></span>
          </v-col>
          <v-col cols="12" class="text-center">
            <div class="scrollable-content-service-detail showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="showDataServicePartner && showDataServicePartner.service_detail"></div>
          </v-col>
          <v-container class="text-center" style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
            <v-row style="flex-wrap: nowrap; display: inline-flex;">
              <v-col
                v-for="(item, index) in showpackages"
                :key="index"
              >
                <v-hover v-slot="{ hover }">
                  <v-card
                    :class="{
                      'hovered-card': hover,
                      'active-card': !hover && item.available_status === 'active' && item.package_status === 'approve',
                      'noactive-card': hover && !(item.available_status === 'active' && item.package_status === 'approve'),
                      'active-card-pending': !hover && (item.available_status === 'pending' || item.available_status === 'inactive' && item.package_status === 'approve'),
                      'noactive-card-pending': hover && (item.available_status === 'pending' || item.available_status === 'inactive' && item.package_status === 'approve'),
                      'active-card-zort-wating': !hover && (item.available_status === 'zort_waiting' || item.available_status === 'waiting'),
                      'noactive-card-zort-wating': hover && (item.available_status === 'zort_waiting' || item.available_status === 'waiting'),
                      'active-card-cancel': !hover && (item.available_status === 'cancel'),
                      'noactive-card-cancel': hover && (item.available_status === 'cancel')
                    }"
                    class="pa-4"
                    width="320px"
                    style="border-radius: 10px;"
                  >
                    <v-col class="text-left">
                      <span>Package</span><br>
                      <span :style="hover ? 'font-size: 18px;' : 'color:#2F8BDC; font-size: 18px;'"><b>{{ item.package_name }}</b></span>
                    </v-col>
                    <v-col class="text-left">
                        <span style="font-size: 24px;">
                          <b>฿{{ Number(item.package_price).toLocaleString() }}</b>
                        </span>
                        <span>
                          /<template v-if="item.payment_type === 'Monthly'">เดือน</template>
                          <template v-else-if="item.payment_type === 'Yearly'">ปี</template>
                          <template v-else-if="item.payment_type === 'Percent'">เปอร์เซ็นต์</template>
                          <template v-else>{{ item.payment_type }}</template>
                        </span>
                      </v-col>
                    <v-col class="text-left">
                        <span><b>รายละเอียด Package :</b></span><br>
                    </v-col>
                    <v-col class="text-left">
                      <template v-if="item.package_name === 'Custom'">
                        <!-- <div class="scrollable-content-first fixed-height" ref="termsContent" @scroll="handleScroll" v-html="item.package_detail || 'ไม่มีข้อมูลรายละเอียด'"></div> -->
                        <div class="scrollable-content-first fixed-height" ref="termsContent" @scroll="handleScroll" v-html="item.package_detail || 'กำหนดรายละเอียดการใช้งานได้เอง'"></div>
                      </template>
                      <template v-else>
                        <div ref="termsContent" @scroll="handleScroll" class="scrollable-content-first fixed-height">
                          <div v-if="item.package_functions.length !== 0">
                            <div v-for="(func, index) in item.package_functions" :key="index">
                              <v-icon class="mr-2" :style="hover ? 'color: #ffffff;' : 'color: #52c41a;'">mdi-check-circle</v-icon>
                              <span>{{ func.name }} - </span>
                              <span>{{ func.limit === -1 ? 'ไม่จำกัด' : func.limit }} </span>
                              <span>{{ func.unit }}</span>
                            </div>
                          </div>
                          <div v-else>
                            <span>-</span>
                          </div>
                          <!-- <ul>
                            <li v-for="(func, index) in item.package_functions" :key="index">
                              {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit || '-')}}
                              {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit + ' ' + func.unit || '-') }}
                              {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit + (func.unit ? ' ' + func.unit : ' -')) }}
                            </li>
                          </ul> -->
                        </div>
                      </template>
                    </v-col>
                    <!-- <v-col class="text-left">
                      <span><b>รายละเอียด Package/คำอธิบายเพิ่มเติม :</b></span><br>
                    </v-col>
                    <v-col class="text-left">
                      <div class="scrollable-content-first" ref="termsContent" @scroll="handleScroll" v-html="item.package_detail"></div>
                    </v-col> -->
                    <v-col class="text-center">
                      <span :style="hover ? 'cursor: pointer; text-decoration: underline;' : 'color:#FAAD14; cursor: pointer; text-decoration: underline;'" @click="openDialogDetails(index)">
                        ดูรายละเอียดเพิ่มเติม  <v-icon small :style="hover ? 'color: white;' : 'color:#FAAD14;'">mdi-arrow-right</v-icon>
                      </span>
                    </v-col>
                    <v-col class="text-center">
                      <v-btn
                        depressed
                        :class="[hover ? 'hovered-btn' : 'nohovered-btn',
                        item.available_status === 'active' && item.package_status === 'approve' ? 'active-btn' : '',
                        item.available_status === 'pending' || item.available_status === 'inactive' && item.package_status === 'approve' ? 'hovered-btn-status' : '',
                        item.available_status === 'zort_waiting' ? 'hovered-btn-status' && 'zort-btn' : '',
                        item.available_status === 'inactive' && item.package_status === 'approve' ? 'pending-btn' : '',
                        item.package_status !== 'approve' ? 'hovered-btn-status' : '',
                        item.available_status === 'pending' ? 'pending-btn' : '',
                        item.available_status === 'waiting' ? 'hovered-btn-status' && 'zort-btn' : '',
                        item.available_status === 'cancel' ? 'hovered-btn-status' && 'cancel-btn' : ''
                        ]"
                        :disabled="( !(item.available_status === 'active' && item.package_status === 'approve') && (item.package_status !== 'approve') || getAvaliablePackage !== 'normal' )"
                        class="w-100"
                        @click="opendialogPartnerDetail(index)"
                      >
                        <b>{{ statusLabel(item) }}</b>
                      </v-btn>
                    </v-col>
                    <v-col>
                      <a v-if="item.available_status === 'pending'" @click="GotoERPPartner()" style="text-decoration: none; color: inherit;">
                        <span><b>หากต้องการเชื่อมต่อ Partner <br>(โปรดคลิ๊กที่นี่)</b></span>
                      </a>
                      <a v-else-if="item.available_status === 'inactive' && item.package_status === 'approve'" @click="GotoERPPartner()" style="text-decoration: none; color: inherit;">
                        <span><b>หากต้องการเปิดการใช้งาน Partner <br>(โปรดคลิ๊กที่นี่)</b></span>
                      </a>
                      <a v-else-if="item.available_status === 'cancel'" @click="GotoPaymentPartner()" style="text-decoration: none; color: inherit;">
                        <span><b>โปรดทำการชำระเงินยอดค้างชำระ <br>(คลิ๊กที่นี่)</b></span>
                      </a>
                    </v-col>
                  </v-card>
                </v-hover>
              </v-col>
            </v-row>
          </v-container>
        </v-row>
      </v-container>
    </v-card>

    <v-dialog v-model="dialogPartnerDetail" max-width="600px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogPartnerDetail = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #27AB9C; color: white;">
          <span><b>รายละเอียด Package</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-card class="elevation-0" style="background-color: #f5f5f5;">
            <v-col>
              <span><b>ชื่อ Package : </b>{{ selectPartnerDetailIndex.package_name }}</span>
            </v-col>
            <v-col>
              <span><b>ประเภทการชำระเงิน : </b>{{ selectPartnerDetailIndex.payment_type === 'Monthly' ? 'รายเดือน' : selectPartnerDetailIndex.payment_type === 'Yearly' ? 'รายปี' : '' }}</span>
            </v-col>
            <v-col>
              <span><b>ราคาสินค้า : </b>{{ Number(selectPartnerDetailIndex.package_price).toLocaleString() }}</span>
            </v-col>
          </v-card>
          <br>
          <v-card class="elevation-0" style="background-color: #f5f5f5;" :hidden="selectPartnerDetailIndex.package_name === 'Custom'">
            <v-col>
              <span><b>รายละเอียด Package</b></span>
            </v-col>
            <v-col>
              <div ref="termsContent" @scroll="handleScroll">
                  <div v-for="(func, index) in selectPartnerDetailIndex.package_functions" :key="index">
                    <v-icon class="mr-2" color="#52c41a">mdi-check-circle</v-icon>
                    <span>{{ func.name }} - </span>
                    <span>{{ func.limit === -1 ? 'ไม่จำกัด' : func.limit }} </span>
                    <span>{{ func.unit }}</span>
                  </div>
                <!-- <ul>
                  <li v-for="(func, index) in selectPartnerDetailIndex.package_functions" :key="index">
                    {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit || '-')}}
                    {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit + ' ' + func.unit || '-') }}
                    {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit + (func.unit ? ' ' + func.unit : ' -')) }}
                  </li>
                </ul> -->
              </div>
            </v-col>
          </v-card>
          <br>
          <v-card class="elevation-0" style="background-color: #f5f5f5;" v-if="selectPartnerDetailIndex.package_name === 'Custom'">
            <v-col>
              <span><b>รายละเอียด Package/คำอธิบายเพิ่มเติม :</b></span>
            </v-col>
            <v-col>
              <div class="scrollable-content-details showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="selectPartnerDetailIndex.package_detail || 'กำหนดรายละเอียดการใช้งานได้เอง'"></div>
            </v-col>
          </v-card>
          <v-card class="elevation-0" style="background-color: #f5f5f5;" v-else>
            <v-col>
              <span><b>รายละเอียด Package/คำอธิบายเพิ่มเติม :</b></span>
            </v-col>
            <v-col>
              <div class="scrollable-content-details showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="selectPartnerDetailIndex.package_detail"></div>
            </v-col>
          </v-card>
          <br>
          <v-card class="elevation-0" style="background-color: #f5f5f5;">
            <v-col>
              <span><b>เบอร์โทรศัพท์ : </b>{{ selectPartnerDetailIndex.phone }}</span>
            </v-col>
            <v-col>
              <span><b>อีเมล : </b>{{ selectPartnerDetailIndex.email }}</span>
            </v-col>
          </v-card>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded dark color="#27AB9C" class="ma-2" style="width: 150px;" @click="opendialogPartnerCondition()">สนใจใช้งาน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogPartnerCondition" max-width="800px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogPartnerCondition = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #27AB9C; color: white;">
          <span><b>เงื่อนไขการให้บริการ</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-col class="d-flex align-center">
            <img
              src="@/assets/Marketplace_partner/imageCondition.png"
              alt="เกี่ยวกับ Partner"
              width="25px"
              class="mr-2"
            />
            <span><b>รายละเอียดเงื่อนไขการให้บริการ</b></span>
          </v-col>
          <v-col>
            <v-card class="elevation-0" style="background-color: #f5f5f5;">
              <v-col>
                <div class="scrollable-content showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="selectPartnerConditionIndex.contract_detail"></div>
              </v-col>
            </v-card>
            <v-checkbox
              v-model="checkCondition"
              :disabled="!isScrollComplete"
              label="ยอมรับเงื่อนไขการให้บริการทั้งหมด"
            ></v-checkbox>
          </v-col>
        </v-card-text>
        <v-card-actions style="display: flex; justify-content: space-between; background: #F5FCFB;">
          <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogPartnerCondition = false">ยกเลิก</v-btn>
          <v-btn rounded class="ma-2" color="#27AB9C" style="width: 100px; color: white;" :disabled="!checkCondition" @click="openDialogConfirm()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogPartnerConditionZort" max-width="800px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogPartnerConditionZort = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #27AB9C; color: white;">
          <span><b>เงื่อนไขการให้บริการ</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-col class="d-flex align-center">
            <img
              src="@/assets/Marketplace_partner/imageCondition.png"
              alt="เกี่ยวกับ Partner"
              width="25px"
              class="mr-2"
            />
            <span><b>รายละเอียดเงื่อนไขการให้บริการ</b></span>
          </v-col>
          <v-col>
            <v-card class="elevation-0" style="background-color: #f5f5f5;">
              <v-col>
                <div class="scrollable-content showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="selectPartnerConditionIndex.contract_detail"></div>
              </v-col>
            </v-card>
            <v-checkbox
              v-model="checkCondition"
              :disabled="!isScrollComplete"
              label="ยอมรับเงื่อนไขการให้บริการทั้งหมด"
            ></v-checkbox>
          </v-col>
        </v-card-text>
        <v-card-actions style="display: flex; justify-content: space-between; background: #F5FCFB;">
          <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogPartnerConditionZort = false">ยกเลิก</v-btn>
          <v-btn rounded class="ma-2" color="#27AB9C" style="width: 100px; color: white;" :disabled="!checkCondition" @click="openDialogConfirmZort()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogDetails" max-width="650px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDetails = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #27AB9C; color: white;">
          <span><b>รายละเอียด Package</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-card class="elevation-0" style="background-color: #f5f5f5;">
            <v-col>
              <span><b>ชื่อ Package : </b>{{ selectPartnerDetailIndex.package_name }}</span>
            </v-col>
            <v-col>
              <span><b>ประเภทการชำระเงิน : </b>{{ selectPartnerDetailIndex.payment_type === 'Monthly' ? 'รายเดือน' : selectPartnerDetailIndex.payment_type === 'Yearly' ? 'รายปี' : '' }}</span>
            </v-col>
            <v-col>
              <span><b>ราคาสินค้า : </b>{{  Number(selectPartnerDetailIndex.package_price).toLocaleString() }}</span>
            </v-col>
          </v-card>
          <br>
          <v-card class="elevation-0" style="background-color: #f5f5f5;" :hidden="selectPartnerDetailIndex.package_name === 'Custom'">
            <v-col>
              <span><b>รายละเอียด Package</b></span>
            </v-col>
            <v-col>
              <div ref="termsContent" @scroll="handleScroll">
                <div v-for="(func, index) in selectPartnerDetailIndex.package_functions" :key="index">
                  <v-icon class="mr-2" color="#52c41a">mdi-check-circle</v-icon>
                  <span>{{ func.name }} - </span>
                  <span>{{ func.limit === -1 ? 'ไม่จำกัด' : func.limit }} </span>
                  <span>{{ func.unit }}</span>
                </div>
                <!-- <ul>
                  <li v-for="(func, index) in selectPartnerDetailIndex.package_functions" :key="index">
                    {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit || '-')}}
                    {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit + ' ' + func.unit || '-') }}
                    {{ func.name || '-' }} : {{ func.limit === -1 ? 'ไม่จำกัด' : (func.limit + (func.unit ? ' ' + func.unit : ' -')) }}
                  </li>
                </ul> -->
              </div>
            </v-col>
          </v-card>
          <br :hidden="selectPartnerDetailIndex.package_name === 'Custom'">
          <v-card class="elevation-0" style="background-color: #f5f5f5;" v-if="selectPartnerDetailIndex.package_name === 'Custom'">
            <v-col>
              <span><b>รายละเอียด Package/คำอธิบายเพิ่มเติม :</b></span>
            </v-col>
            <v-col>
              <div class="scrollable-content-details showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="selectPartnerDetailIndex.package_detail || 'กำหนดรายละเอียดการใช้งานได้เอง'"></div>
            </v-col>
          </v-card>
          <v-card class="elevation-0" style="background-color: #f5f5f5;" v-else>
            <v-col>
              <span><b>รายละเอียด Package/คำอธิบายเพิ่มเติม :</b></span>
            </v-col>
            <v-col>
              <div class="scrollable-content-details showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="selectPartnerDetailIndex.package_detail"></div>
            </v-col>
          </v-card>
          <br>
          <v-card class="elevation-0" style="background-color: #f5f5f5;">
            <v-col>
              <span><b>เบอร์โทรศัพท์ : </b>{{ selectPartnerDetailIndex.phone }}</span>
            </v-col>
            <v-col>
              <span><b>อีเมล : </b>{{ selectPartnerDetailIndex.email }}</span>
            </v-col>
          </v-card>
        </v-card-text>
        <v-card-actions style="justify-content: end;">
          <v-btn rounded class="ma-2" color="#27AB9C" style="width: 100px; color: white;" @click="dialogDetails = false">ปิด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogzortWaiting" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogzortWaiting = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            src="@/assets/Marketplace_partner/Group1.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text>
          <div class="text-center mb-4">
            <span><b>ส่งข้อมูลของคุณเรียบร้อยแล้ว</b></span><br>
            <span>
              บริษัทจะดำเนินการติดต่อกลับไปยัง ข้อมูลที่คุณได้ให้ไว้
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="dialogzortWaiting = false">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogSelectZort" max-width="800px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="cancelSelectService()" style="position: absolute; top: 25px; right: 25px;">
          <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #27AB9C; color: white; padding-top: 25px; padding-bottom: 25px;">
          <span><b>เลือก Package</b></span>
        </v-card-title>
        <br>
        <v-card-text>
          <v-col cols="12" class="text-center">
            <span style="color: black; font-size: 18px;"><b>ลูกค้าใช้ Package</b></span>
          </v-col>
          <v-col cols="12">
            <v-container class="text-center" style="overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px;">
              <v-row style="flex-wrap: nowrap; display: inline-flex;">
                <v-col
                  v-for="(item, index) in showpackages"
                  :key="index"
                >
                  <v-hover v-slot="{ hover }">
                    <v-card
                      :class="{
                        'hovered-card': hover,
                        'active-card': !hover && item.available_status === 'active' && item.package_status === 'approve',
                        'noactive-card': hover && !(item.available_status === 'active' && item.package_status === 'approve'),
                        'active-card-pending': !hover && (item.available_status === 'pending' || item.available_status === 'inactive' && item.package_status === 'approve'),
                        'noactive-card-pending': hover && (item.available_status === 'pending' || item.available_status === 'inactive' && item.package_status === 'approve'),
                        'active-card-zort-wating': !hover && (item.available_status === 'zort_waiting' || item.available_status === 'waiting' || item.select === true),
                        'noactive-card-zort-wating': hover && (item.available_status === 'zort_waiting' || item.available_status === 'waiting' || item.select === true),
                        'active-card-cancel': !hover && (item.available_status === 'cancel'),
                        'noactive-card-cancel': hover && (item.available_status === 'cancel')
                      }"
                      class="pa-4"
                      width="320px"
                      style="border-radius: 10px;"
                    >
                      <v-col class="text-left">
                        <span>Package</span><br>
                        <span :style="hover ? 'font-size: 18px;' : 'color:#2F8BDC; font-size: 18px;'"><b>{{ item.package_name }}</b></span>
                      </v-col>
                      <v-col class="text-left">
                          <span style="font-size: 24px;">
                            <b>฿{{ Number(item.package_price).toLocaleString() }}</b>
                          </span>
                          <span>
                            /<template v-if="item.payment_type === 'Monthly'">เดือน</template>
                            <template v-else-if="item.payment_type === 'Yearly'">ปี</template>
                            <template v-else-if="item.payment_type === 'Percent'">เปอร์เซ็นต์</template>
                            <template v-else>{{ item.payment_type }}</template>
                          </span>
                        </v-col>
                      <v-col class="text-left">
                          <span><b>รายละเอียด Package :</b></span><br>
                      </v-col>
                      <v-col class="text-left">
                        <template v-if="item.package_name === 'Custom'">
                          <!-- <div class="scrollable-content-first fixed-height" ref="termsContent" @scroll="handleScroll" v-html="item.package_detail || 'ไม่มีข้อมูลรายละเอียด'"></div> -->
                          <div class="scrollable-content-first fixed-height" ref="termsContent" @scroll="handleScroll" v-html="item.package_detail || 'กำหนดรายละเอียดการใช้งานได้เอง'"></div>
                        </template>
                        <template v-else>
                          <div ref="termsContent" @scroll="handleScroll" class="scrollable-content-first fixed-height">
                            <div v-if="item.package_functions.length !== 0">
                              <div v-for="(func, index) in item.package_functions" :key="index">
                                <v-icon class="mr-2" :style="hover ? 'color: #ffffff;' : 'color: #52c41a;'">mdi-check-circle</v-icon>
                                <span>{{ func.name }} - </span>
                                <span>{{ func.limit === -1 ? 'ไม่จำกัด' : func.limit }} </span>
                                <span>{{ func.unit }}</span>
                              </div>
                            </div>
                            <div v-else>
                              <span>-</span>
                            </div>
                          </div>
                        </template>
                      </v-col>
                      <v-col class="text-center">
                        <span :style="hover ? 'cursor: pointer; text-decoration: underline;' : 'color:#FAAD14; cursor: pointer; text-decoration: underline;'" @click="openDialogDetails(index)">
                          ดูรายละเอียดเพิ่มเติม  <v-icon small :style="hover ? 'color: white;' : 'color:#FAAD14;'">mdi-arrow-right</v-icon>
                        </span>
                      </v-col>
                      <v-col class="text-center">
                        <v-btn
                          depressed
                          :class="[hover ? 'hovered-btn' : 'nohovered-btn',
                          item.available_status === 'active' && item.package_status === 'approve' ? 'active-btn' : '',
                          item.available_status === 'pending' || item.available_status === 'inactive' && item.package_status === 'approve' ? 'hovered-btn-status' : '',
                          item.available_status === 'zort_waiting' ? 'hovered-btn-status' && 'zort-btn' : '',
                          item.available_status === 'inactive' && item.package_status === 'approve' ? 'pending-btn' : '',
                          item.package_status !== 'approve' ? 'hovered-btn-status' : '',
                          item.available_status === 'pending' ? 'pending-btn' : '',
                          item.available_status === 'waiting' ? 'hovered-btn-status' && 'zort-btn' : '',
                          item.available_status === 'cancel' ? 'hovered-btn-status' && 'cancel-btn' : ''
                          ]"
                          style="width: 200px;"
                          :disabled="( !(item.available_status === 'active' && item.package_status === 'approve') && (item.package_status !== 'approve') || getAvaliablePackage !== 'normal' )"
                          @click="selectService(index)"
                        >
                          <b>{{ statusLabelZort(item) }}</b>
                        </v-btn>
                      </v-col>
                      <v-col>
                        <a v-if="item.available_status === 'pending'" @click="GotoERPPartner()" style="text-decoration: none; color: inherit;">
                          <span><b>หากต้องการเชื่อมต่อ Partner <br>(โปรดคลิ๊กที่นี่)</b></span>
                        </a>
                        <a v-else-if="item.available_status === 'inactive' && item.package_status === 'approve'" @click="GotoERPPartner()" style="text-decoration: none; color: inherit;">
                          <span><b>หากต้องการเปิดการใช้งาน Partner <br>(โปรดคลิ๊กที่นี่)</b></span>
                        </a>
                        <a v-else-if="item.available_status === 'cancel'" @click="GotoPaymentPartner()" style="text-decoration: none; color: inherit;">
                          <span><b>โปรดทำการชำระเงินยอดค้างชำระ <br>(คลิ๊กที่นี่)</b></span>
                        </a>
                      </v-col>
                    </v-card>
                  </v-hover>
                </v-col>
              </v-row>
            </v-container>
          </v-col>
        </v-card-text>
        <v-card-actions class="justify-center pa-5 pt-0">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="cancelSelectService()">ย้อนกลับ</v-btn>
          <v-btn :disabled="showDisable"  rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="opendialogPartnerConditionZort()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogConfirmPackage" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirmPackage = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>ยืนยันการเข้าร่วมบริการ</b></span><br><br>
            <span>
              คุณต้องการทำรายการนี้ ใช่ หรือไม่
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogConfirmPackage = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="gotoConfirmPackagePartner()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogConfirmPackageZort" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirmPackageZort = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>ยืนยันการเข้าร่วมบริการ</b></span><br><br>
            <span>
              คุณต้องการทำรายการนี้ ใช่ หรือไม่
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogConfirmPackageZort = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="confirmLinkOldPartnerData()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogAfterConfirm" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogAfterConfirm = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group1.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>ยืนยันการเข้าร่วมบริการเสร็จสิ้น</b></span><br><br>
            <span>
              คุณได้ทำการขอเข้าร่วมใช้บริการเรียบร้อย<br>กรุณารอรับเพื่อเชื่อมต่อบริการทางอีเมลของคุณ
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 170px;" @click="gotoOrderPage()">ไปยังหน้ารายการสั่งซื้อ</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 110px; color: white;" @click="gotoMainPage()">ไปยังหน้าหลัก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogSuspended" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogSuspended = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5; height: 200px;">
          <v-img
            src="@/assets/Marketplace_partner/Suspend.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 18px;"><b>บริการของคุณถูกระงับการใช้งาน</b></span><br><br>
            <span>บริการของคุณถูกระงับ เนื่องจากเลยกำหนดชำระเงิน กรุณาชำระเงินเพื่อใช้งานต่อ</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center pb-5">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogSuspended = false">ปิด</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="GotoPaymentPartner()">ชำระเงิน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
// import { Encode } from '@/services'
import { Decode } from '@/services'
export default {
  data () {
    return {
      dialogConfirmPackage: false,
      dialogPartnerDetail: false,
      dialogPartnerCondition: false,
      dialogSuspended: false,
      dialogAfterConfirm: false,
      dialogSelectZort: false,
      dialogPartnerConditionZort: false,
      dialogConfirmPackageZort: false,
      checkCondition: false,
      dialogzortWaiting: false,
      isScrollComplete: false,
      dataServicePartner: [],
      showDataServicePartner: [],
      showpackages: [],
      showpackagesDisable: [],
      selectPartnerDetailIndex: [],
      selectPartnerConditionIndex: [],
      facebookURL: '',
      lineURL: '',
      getAvaliablePackage: '',
      getAvaliableDocument: '',
      getAvaliableLink: '',
      dialogDetails: '',
      onechatToken: '',
      suspendedPartner: '',
      selectPartnerZort: [],
      partnerName: [
        'ZORT',
        'zort',
        'ซอร์ท',
        'ซอร์ท จำกัด',
        'บริษัท ZORT',
        'บริษัท ZORT จำกัด',
        'บริษัท Zort',
        'บริษัท Zort จำกัด',
        'บริษัท zort',
        'บริษัท zort จำกัด',
        'บริษัท ซอร์ท',
        'บริษัท ซอร์ท จำกัด',
        'บริษัทซอร์ท',
        'ซอร์ทเอาท์',
        'บริษัท ซอร์ทเอาท์',
        'บริษัท ซอร์ทเอาท์ จำกัด',
        'zortout',
        'zortout จำกัด',
        'บริษัท zortout',
        'บริษัท zortout จำกัด',
        'ZORTOUT'
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredBanners () {
      return Array.isArray(this.showDataServicePartner.partner_media)
        ? this.showDataServicePartner.partner_media.filter(image => image.image_type === 'banner')
        : []
    },
    statusLabelZort () {
      return (item) => {
        if (item.available_status === 'active' && item.package_status === 'approve') {
          return 'กำลังใช้งาน'
        } else if (item.available_status === 'pending') {
          return 'ไม่มีการเชื่อมต่อ'
        } else if (item.available_status === 'waiting') {
          return 'รอการติดต่อกลับ'
        } else if (item.available_status === 'cancel') {
          return 'ระงับการใช้งาน'
        } else if (item.available_status === 'inactive' && item.package_status === 'approve') {
          return 'ปิดการใช้งาน'
        } else if (item.available_status === 'zort_waiting') {
          return 'รออนุมัติการใช้งาน'
        } else if (item.package_status === 'reject' || item.package_status === 'pending') {
          return 'ไม่พร้อมให้บริการ'
        } else if (item.available_status === '-' && item.package_status === 'approve') {
          return 'เลือก'
        }
      }
    },
    showDisable () {
      if (!this.selectPartnerDetailIndex || Array.isArray(this.selectPartnerDetailIndex)) {
        return true
      }
      return this.selectPartnerDetailIndex.select === false
    },
    statusLabel () {
      return (item) => {
        if (item.available_status === 'active' && item.package_status === 'approve') {
          return 'กำลังใช้งาน'
        } else if (item.available_status === 'pending') {
          return 'ไม่มีการเชื่อมต่อ'
        } else if (item.available_status === 'waiting') {
          return 'รอการติดต่อกลับ'
        } else if (item.available_status === 'cancel') {
          return 'ระงับการใช้งาน'
        } else if (item.available_status === 'inactive' && item.package_status === 'approve') {
          return 'ปิดการใช้งาน'
        } else if (item.available_status === 'zort_waiting') {
          return 'รออนุมัติการใช้งาน'
        } else if (item.package_status === 'reject' || item.package_status === 'pending') {
          return 'ไม่พร้อมให้บริการ'
        } else if (item.available_status === '-' && item.package_status === 'approve') {
          return 'สนใจใช้งาน'
        }
      }
    }
  },
  watch: {
    MobileSize (val) {
      var PartnerCode = localStorage.getItem('SelectPartnerCode')
      if (val === true) {
        this.$router.push({ path: `/ShopPartnerDetailsMobile?SelectPartnerCode=${PartnerCode}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ShopPartnerDetails?SelectPartnerCode=${PartnerCode}` }).catch(() => {})
      }
    }
  },
  async created () {
    window.scrollTo(0, 0)
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user !== undefined) {
        this.isLogin = true
        if (oneData.user.shared_token !== undefined && oneData.user.shared_token !== null && oneData.user.shared_token !== '') {
          this.onechatToken = oneData.user.shared_token
        } else {
          this.onechatToken = ''
        }
      } else {
        localStorage.removeItem('oneData')
      }
    }
    this.$EventBus.$emit('changeNav')
    this.SelectPartnerCode = this.$route.query.SelectPartnerCode
    localStorage.setItem('SelectPartnerCode', this.SelectPartnerCode)
    await this.getdataServicePartner()
    if (this.suspendedPartner === 'yes') {
      this.dialogSuspended = true
    } else if (this.suspendedPartner === 'no') {
      this.dialogSuspended = false
    }
  },
  mounted () {
    this.checkContentHeight()
  },
  methods: {
    backtoPage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/ShopJoinPartnerDetailsMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ShopJoinPartnerDetails' }).catch(() => {})
      }
    },
    facebookUrl () {
      this.facebookURL = this.showDataServicePartner.facebook
      window.open(this.facebookURL, '_blank')
    },
    lineUrl () {
      this.lineURL = this.showDataServicePartner.line === null ? '' : 'https://line.me/ti/p/~' + this.showDataServicePartner.line
      window.open(this.lineURL, '_blank')
    },
    async getdataServicePartner () {
      this.$store.commit('openLoader')
      const shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopSellerID
      }

      await this.$store.dispatch('actionGetListDetailPartnerShop', data)
      var response = await this.$store.state.ModuleShop.stateGetListDetailPartnerShop

      if (response.message === 'list partner success.') {
        this.$store.commit('closeLoader')
        this.dataServicePartner = Object.values(response.data).filter(partnerCode => partnerCode.partner_code === this.SelectPartnerCode)
        this.showDataServicePartner = this.dataServicePartner[0]
        this.lineURL = this.showDataServicePartner.line === null ? '' : 'https://line.me/ti/p/~' + this.showDataServicePartner.line
        this.facebookURL = this.showDataServicePartner.facebook
        this.getAvaliablePackage = this.showDataServicePartner.avaliable_package
        this.getAvaliableDocument = this.showDataServicePartner.avaliable_document
        this.getAvaliableLink = this.showDataServicePartner.avaliable_link
        this.showpackages = await this.showDataServicePartner.packages
        this.suspendedPartner = this.showDataServicePartner.suspended_partner
      }
    },
    async openDialogDetails (index) {
      this.dialogDetails = true
      this.selectPartnerDetailIndex = await this.showpackages[index]
      // console.log(this.selectPartnerDetailIndex)
    },
    async opendialogPartnerDetail (index) {
      this.dialogPartnerDetail = true
      this.selectPartnerDetailIndex = await this.showpackages[index]
      // console.log(this.selectPartnerDetailIndex)
      // console.log(this.selectPartnerDetailIndex.package_code)
      localStorage.setItem('SelectPackage', this.selectPartnerDetailIndex.package_code)
    },
    selectServiceZort () {
      if (this.selectPartnerDetailIndex) {
        this.selectPartnerDetailIndex.select = false
      }

      this.selectPartnerDetailIndex = { select: false }
      this.dialogSelectZort = true
    },
    async selectService (index) {
      // console.log(this.selectPartnerDetailIndex)
      if (this.selectPartnerDetailIndex) {
        this.selectPartnerDetailIndex.select = false
      }

      this.selectPartnerDetailIndex = await this.showpackages[index]
      this.selectPartnerDetailIndex.select = true
      localStorage.setItem('SelectPackage', this.selectPartnerDetailIndex.package_code)
      // console.log(this.selectPartnerDetailIndex)
    },
    cancelSelectService () {
      // console.log(this.selectPartnerDetailIndex)
      if (this.selectPartnerDetailIndex) {
        this.selectPartnerDetailIndex.select = false
      }

      this.selectPartnerDetailIndex = { select: false }
      this.dialogSelectZort = false
    },
    openDialogConfirmZort () {
      this.dialogConfirmPackageZort = true
      this.dialogPartnerConditionZort = false
    },
    async confirmLinkOldPartnerData () {
      this.dialogConfirmPackageZort = false
      this.$store.commit('openLoader')
      const ShopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        package_code: this.selectPartnerDetailIndex.package_code,
        partner_code: this.showDataServicePartner.partner_code,
        seller_shop_id: ShopID,
        service_id: this.showDataServicePartner.service_id,
        status: 'zort_waiting',
        type: 'old_customer'
      }

      await this.$store.dispatch('actionOldPartner', data)
      var response = await this.$store.state.ModuleShop.stateOldPartner

      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dialogAfterConfirm = true
        this.dialogPartnerConditionZort = false
        await this.getdataServicePartner()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    async opendialogPartnerCondition () {
      this.dialogPartnerDetail = false
      this.dialogPartnerCondition = true
      this.checkCondition = false
      this.dialogSelectZort = false
      this.$nextTick(() => {
        this.checkContentHeight()
      })
      this.selectPartnerConditionIndex = this.selectPartnerDetailIndex
    },
    async opendialogPartnerConditionZort () {
      this.dialogPartnerConditionZort = true
      this.checkCondition = false
      this.dialogSelectZort = false
      this.$nextTick(() => {
        this.checkContentHeight()
      })
      this.selectPartnerConditionIndex = this.selectPartnerDetailIndex
    },
    checkContentHeight () {
      this.$nextTick(() => {
        const content = this.$refs.termsContent
        if (content) {
          if (content.scrollHeight <= content.clientHeight) {
            this.isScrollComplete = true
          } else {
            this.isScrollComplete = false
          }
        }
      })
    },
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    },
    openDialogConfirm () {
      this.dialogConfirmPackage = true
      this.dialogPartnerCondition = false
    },
    async gotoConfirmPackagePartner () {
      this.dialogConfirmPackage = false
      this.$store.commit('openLoader')
      const partnerName = this.showDataServicePartner.partner_name.toLowerCase()

      const searchTerms = [
        'zort',
        'ซอร์ท',
        'ซอร์ท จำกัด',
        'บริษัท ZORT',
        'บริษัท ZORT จำกัด',
        'บริษัท Zort',
        'บริษัท Zort จำกัด',
        'บริษัท zort',
        'บริษัท zort จำกัด',
        'บริษัท ซอร์ท',
        'บริษัท ซอร์ท จำกัด',
        'บริษัทซอร์ท',
        'ซอร์ทเอาท์',
        'บริษัท ซอร์ทเอาท์',
        'บริษัท ซอร์ทเอาท์ จำกัด',
        'zortout',
        'zortout จำกัด',
        'บริษัท zortout',
        'บริษัท zortout จำกัด',
        'ZORTOUT'
      ].map(term => term.toLowerCase())

      if (searchTerms.some(term => partnerName.includes(term))) {
        const shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))

        var dataZort = {
          service_id: this.showDataServicePartner.service_id,
          partner_code: this.showDataServicePartner.partner_code,
          seller_shop_id: shopSellerID,
          package_code: this.selectPartnerConditionIndex.package_code,
          status: 'zort_waiting'
        }

        await this.$store.dispatch('actionUpdateStatusAfterBuyPartner', dataZort)
        var responseZort = await this.$store.state.ModuleShop.stateUpdateStatusAfterBuyPartner

        if (responseZort.message === 'add interested package success.') {
          this.$store.commit('closeLoader')
          this.dialogAfterConfirm = true
          this.dialogPartnerCondition = false
          await this.getdataServicePartner()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2500,
            icon: 'error',
            text: `${responseZort.message}`
          })
        }
      } else {
        const shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
        var dataNormal = {
          service_id: this.showDataServicePartner.service_id,
          partner_code: this.showDataServicePartner.partner_code,
          seller_shop_id: shopSellerID,
          package_code: this.selectPartnerConditionIndex.package_code,
          status: 'waiting'
        }

        await this.$store.dispatch('actionUpdateStatusAfterBuyPartner', dataNormal)
        var responseNormal = await this.$store.state.ModuleShop.stateUpdateStatusAfterBuyPartner

        if (responseNormal.message === 'add interested package success.') {
          this.$store.commit('closeLoader')
          this.dialogAfterConfirm = true
          this.dialogPartnerCondition = false
          await this.getdataServicePartner()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2500,
            icon: 'error',
            text: `${responseNormal.message}`
          })
        }
      }
    },
    GotoERPPartner () {
      if (this.MobileSize) {
        this.$router.push({ path: '/ERPPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ERPPartner' }).catch(() => {})
      }
    },
    GotoPaymentPartner () {
      if (this.MobileSize) {
        this.$router.push({ path: '/paymentPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentPartner' }).catch(() => {})
      }
    },
    gotoOrderPage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/orderListPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderListPartner' }).catch(() => {})
      }
    },
    gotoMainPage () {
      this.dialogAfterConfirm = false
      this.getdataServicePartner()
      // var PartnerCode = localStorage.getItem('SelectPartnerCode')
      // if (this.MobileSize) {
      //   this.$router.push({ path: `/ShopPartnerDetailsMobile?SelectPartnerCode=${PartnerCode}` }).catch(() => {})
      // } else {
      //   this.$router.push({ path: `/ShopPartnerDetails?SelectPartnerCode=${PartnerCode}` }).catch(() => {})
      // }
    },
    openLink (link) {
      window.open(link, '_blank')
    },
    openLinkSeminar (link) {
      // var sharetoken = this.onechatToken
      // window.open(link + '?share_token=' + sharetoken, '_blank')
      window.open(link, '_blank')
    }
  }
}
</script>

<style scoped>

.inline-flex {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.inline-flex-desktop {
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.classFBLineSizeIpad {
  height: 24px;
  width: 24px;
  max-width: 24px;
}

.classFBLineSizeDesk {
height: 24px;
width: 24px;
max-width: 24px;
}

.scrolling-row {
  display: flex;
  overflow-x: auto;
  white-space: nowrap;
}

.v-btn.hovered-btn {
  background-color: white !important;
  color: #47a9ff !important;
  border-radius: 20px;
}

.v-btn.hovered-btn-status {
  background-color: white !important;
  color: white !important;
  border-radius: 20px;
}

.v-btn.nohovered-btn {
  background-color: #47a9ff !important;
  color: white !important;
  border-radius: 20px;
}

.scrollable-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
}

.scrollable-content-first {
  max-height: 150px;
  overflow-y: auto;
  /* padding: 10px; */
}

.fixed-height {
  min-height: 150px;
  overflow-y: auto;
}

.scrollable-content-details {
  max-height: 230px;
  overflow-y: auto;
  /* padding: 10px; */
}

.scrollable-content-service-detail {
  max-height: 100px;
  overflow-y: auto;
  /* padding: 10px; */
}

/* .package-detail {
  display: -webkit-box;
  -webkit-line-clamp: 5;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
} */

.hovered-card {
  background-color: #4ac521 !important;
  color: white;
  transition: background-color 0.3s ease;
}

.theme--light.v-btn.v-btn--disabled {
  color: rgb(255, 255, 255) !important;
  cursor: not-allowed !important;
}

.theme--light.v-btn.v-btn--disabled.active-btn {
  background-color: white !important;
  color: #4ac521 !important;
}

.noactive-card {
  background-color: #47a9ff!important;
}

.active-card {
  border: 2px solid #4ac521;
}

.active-card-pending {
  border: 2px solid #ffb05c;
}

.noactive-card-pending {
  background-color: #ffb05c !important;
  color: white !important;
}

.v-btn.pending-btn {
  border: 2px solid #ffb05c;
  background-color: white !important;
  color: #ffb05c !important;
}

.theme--light.v-btn.v-btn--disabled.pending-btn {
  background-color: white !important;
  color: #ffb05c !important;
}

/* .active-card-zort-wating {
  border: 2px solid #ffc756;
}

.noactive-card-zort-wating {
  background-color: #ffc756 !important;
  color: white !important;
}

.v-btn.zort-btn {
  border: 2px solid #ffc756;
  background-color: white !important;
  color: #ffc756 !important;
} */

.active-card-zort-wating {
  border: 2px solid #3c6cc6;
}

.noactive-card-zort-wating {
  background-color: #3c6cc6 !important;
  color: white !important;
}

.v-btn.zort-btn {
  border: 2px solid #3c6cc6;
  background-color: white !important;
  color: #3c6cc6 !important;
}

.theme--light.v-btn.v-btn--disabled.zort-btn {
  background-color: white !important;
  color: #3c6cc6 !important;
}

.active-card-cancel {
  border: 2px solid #8b8b8b;
}

.noactive-card-cancel {
  background-color: #8b8b8b !important;
  color: white !important;
}

.v-btn.cancel-btn {
  border: 2px solid #8b8b8b;
  background-color: white !important;
  color: #8b8b8b !important;
}

.theme--light.v-btn.v-btn--disabled.cancel-btn {
  background-color: white !important;
  color: #8b8b8b !important;
}

.v-btn.active-btn {
  border: 2px solid #4ac521;
  background-color: white !important;
  color: #4ac521 !important;
}

.disabled-link {
  pointer-events: none;
  opacity: 0.5;
  text-decoration: none;
}
</style>

<style>

/* .scrollable-content-details figure.image img {
  max-width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
} */

</style>
