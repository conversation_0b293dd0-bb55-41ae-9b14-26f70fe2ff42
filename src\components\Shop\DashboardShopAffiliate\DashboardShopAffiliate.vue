<template>
  <v-container :style="MobileSize ? 'background-color: white' : ''">
    <v-dialog
      v-model="dialog"
      content-class="elevation-0"
      persistent
    >
      <v-card
        max-width="450px"
        style="border-radius: 24px"
      >
        <v-card-title>
          <v-row no-gutters>
              <v-col class="d-flex justify-center mt-4 mb-5">
                <span style="font-size: 16px; font-weight: bold;" class="text-center ml-12">ตัวกรอง</span>
              </v-col>
              <v-btn icon dark @click="dialog = false" class="mr-4 mt-2">
                <v-icon color="#333">mdi-close</v-icon>
              </v-btn>
            </v-row>
        </v-card-title>
        <v-card-text>
          <v-row>
            <v-col class="d-flex justify-center" >
              <v-card max-width="380px" style="background-color: #f3f5f7; border-radius: 12px;" class="pa-1" flat>
                <v-row align="center" v-if="selectedDropdown === 'รายปี'">
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" color="white" @click="setFilter('รายปี')" elevation="0">
                      <span class="text-16-color">รายปี</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายเดือน')" elevation="0">
                      <span class="text-16">รายเดือน</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายวัน')" elevation="0">
                      <span class="text-16">รายวัน</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row align="center" v-if="selectedDropdown === 'รายเดือน'">
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายปี')" elevation="0">
                      <span class="text-16">รายปี</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" color="white" @click="setFilter('รายเดือน')" elevation="0">
                      <span class="text-16-color">รายเดือน</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายวัน')" elevation="0">
                      <span class="text-16">รายวัน</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row align="center" v-if="selectedDropdown === 'รายวัน'">
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายปี')" elevation="0">
                      <span class="text-16">รายปี</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" @click="setFilter('รายเดือน')" elevation="0">
                      <span class="text-16">รายเดือน</span>
                    </v-btn>
                  </v-col>
                  <v-col cols="4">
                    <v-btn style="width: 80px; border-radius: 10px;" color="white" @click="setFilter('รายวัน')" elevation="0">
                      <span class="text-16-color">รายวัน</span>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row v-if="selectedDropdown === 'รายปี'">
            <v-col cols="2" align="center" class="d-flex align-center">
              <span>ปี:</span>
            </v-col>
            <v-col cols="10">
              <v-select
                outlined
                placeholder="เลือกปี"
                v-model="selectedYear"
                rounded
                dense
                hide-details
                :items="years"
              >
                <template slot="selection">
                  {{selectedYear + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
            </v-col>
          </v-row>
          <v-row v-if="selectedDropdown === 'รายเดือน'">
            <v-col cols="2" align="center" class="d-flex align-center">
              <span>ปี:</span>
            </v-col>
            <v-col cols="10">
              <v-select
                outlined
                placeholder="เลือกปี"
                v-model="selectedYear"
                rounded
                dense
                hide-details
                :items="years"
              >
                <template slot="selection">
                  {{selectedYear + 543}}
                </template>
                <template slot="item" slot-scope="data">
                  {{data.item + 543}}
                </template>
              </v-select>
            </v-col>
            <v-col cols="2" align="center" class="d-flex align-center">
              <span>เดือน:</span>
            </v-col>
            <v-col cols="10">
              <v-select
                outlined
                placeholder="เลือกเดือน"
                v-model="selectedMonth"
                rounded
                dense
                hide-details
                :items="monthsMobileSelector"
                item-text="text"
                item-value="value"
              >
              </v-select>
            </v-col>
          </v-row>
          <v-row v-if="selectedDropdown === 'รายวัน'">
            <v-col cols="2" align="center" class="d-flex align-center px-0 justify-center">
              <span style="font-size: 16px;">วันที่:</span>
            </v-col>
            <v-col cols="10">
              <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
                <template v-slot:activator = "{ on, attrs }">
                  <v-text-field
                    v-bind="attrs"
                    v-on="on"
                    v-model="daterange"
                    placeholder="วว/ดด/ปปปป"
                    dense
                    rounded
                    readonly
                    hide-details
                    class="d-inline-block ml-2 custom-text-field"
                    style="border: 1px solid #EBEBEB; border-radius: 8px;"
                  >
                    <v-spacer></v-spacer>
                    <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  style="font-size:29px !important; height: 480px !important"
                  v-model="dates"
                  scrollable
                  reactive
                  locale="Th-th"
                  range
                  no-title
                  full-width
                  :min="minDate"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-row>
                    <v-col align="end">
                      <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                      <v-btn text color="primary" @click="saveDatesMobile(dates)">ตกลง</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
          <v-row style="position: buttom;">
            <v-col cols="6" align="center" >
              <v-btn class="elevation-0" rounded color="white" @click="clearFilterDialog()">
                <span class="text-16-color">ล้างค่า</span>
              </v-btn>
            </v-col>
            <v-col cols="6" align="center">
              <v-btn class="elevation-0" block rounded color="#27ab9c" @click="submitFilterDialog()">
                <span class="white--text">ยืนยัน</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-row>
      <v-col cols="12" align="end">
        <v-btn rounded color="primary" height="40" @click="exportTransaction()">Export Transaction</v-btn>
      </v-col>
      <v-col cols="8" sm="4">
        <v-row>
          <v-col :class="MobileSize ? 'd-flex align-center' : ''">
            <v-btn icon @click="backtoPage()" v-if="MobileSize">
              <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            <span style="font-weight: 700;" class="my-5" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">แดชบอร์ด affiliate</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="4" sm="8" v-if="MobileSize" class="mt-6">
        <v-btn
          rounded
          outlined
          color="#27ab9c"
          @click="dialog = true"
          dense
        >
          <v-icon color="#27ab9c">mdi-filter-outline</v-icon>
          <span>ตัวกรอง</span>
        </v-btn>
        <v-row>
          <v-col class="mt-5" v-if="MobileSize && mobileTitlesSelected === 'รายการสินค้าที่มีการ affiliate'">
            <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('productlist_seller')">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="white--text ml-1">Export</span>
            </v-btn>
          </v-col>
          <v-col class="mt-5" v-if="MobileSize && mobileTitlesSelected === 'ตารางรายงานการสั่งซื้อ'">
            <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('report_seller')">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="white--text ml-1">Export</span>
            </v-btn>
          </v-col>
          <v-col class="mt-5" v-if="MobileSize && mobileTitlesSelected === 'ตารางแสดงข้อมูลผู้ใช้งานลิงก์ affiliate'">
            <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('userlist_seller')">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="white--text ml-1">Export</span>
            </v-btn>
          </v-col>
          <v-col class="mt-5" v-if="MobileSize && mobileTitlesSelected === 'ลิงค์สินค้าที่ถูกกดมากที่สุด'">
            <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('topten_seller')">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="white--text ml-1">Export</span>
            </v-btn>
          </v-col>
          <v-col class="mt-5" v-if="MobileSize && mobileTitlesSelected === 'รายการสินค้าที่ถูกขายมากที่สุด 10 อันดับ'">
            <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('toptensolds_seller')">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="white--text ml-1">Export</span>
            </v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="4" sm="8" v-else>
        <v-row>
          <v-col v-if="IpadSize || IpadProSize" cols="12" align="end" :class="MobileSize ? 'd-flex justify-start' : ''">
            <v-row>
              <v-col>
                <span style="font-size: 16px;">แสดงผล:</span>
                <v-menu offset-y>
                <template v-slot:activator= "{ on, attrs }">
                  <v-btn v-bind="attrs" v-on="on" color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">
                      {{ selectedDropdown || 'รายปี' }}
                    </span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
                </template>
                  <v-list>
                    <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
                    <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
                    <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
            <v-row v-if="showYearDropdown">
              <v-col>
                <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
                <v-menu v-if="showYearDropdown" offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                      <span style="font-size: 16px; font-weight: 400; color: #333333">
                        {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                      </span>
                      <v-spacer></v-spacer>
                      <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
            <v-row v-if="showMonthDropdown">
              <v-col>
                <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
                <v-menu v-if="showMonthDropdown" offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                      <span style="font-size: 16px; font-weight: 400; color: #333333">
                        {{ monthName === null ? 'เลือกเดือน' : monthName }}
                      </span>
                      <v-spacer></v-spacer>
                      <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelected(month.value)">{{ month.text }}</v-list-item>
                  </v-list>
                </v-menu>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" v-if="showDatePicker">
                <v-row>
                  <v-col cols="3" md="6" class="d-flex align-center justify-end">
                    <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
                  </v-col>
                  <v-col cols="9" md="6">
                    <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
                      <template v-slot:activator = "{ on, attrs }">
                        <v-text-field
                          v-bind="attrs"
                          v-on="on"
                          v-model="daterange"
                          placeholder="วว/ดด/ปปปป"
                          dense
                          rounded
                          readonly
                          hide-details
                          class="d-inline-block ml-2 custom-text-field"
                          style="border: 1px solid #EBEBEB; border-radius: 8px;"
                        >
                          <v-spacer></v-spacer>
                          <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        style="font-size:29px !important; height: 480px !important"
                        v-model="dates"
                        scrollable
                        reactive
                        locale="Th-th"
                        range
                        no-title
                        full-width
                        :min="minDate"
                        :max="
                          new Date(
                            Date.now() - new Date().getTimezoneOffset() * 60000
                          )
                            .toISOString()
                            .substr(0, 10)
                        "
                      >
                      <v-row>
                        <v-col align="end">
                          <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                          <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                        </v-col>
                      </v-row>
                      </v-date-picker>
                    </v-dialog>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <v-col v-else cols="12" align="end" :class="MobileSize ? 'd-flex justify-start' : ''">
            <span style="font-size: 16px;">แสดงผล:</span>
            <v-menu offset-y>
            <template v-slot:activator= "{ on, attrs }">
              <v-btn v-bind="attrs" v-on="on" color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px">
                <span style="font-size: 16px; font-weight: 400; color: #333333">
                  {{ selectedDropdown || 'รายปี' }}
                </span>
                <v-spacer></v-spacer>
                <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
            </template>
              <v-list>
                <v-list-item @click="onDropdownSelected('รายปี')">รายปี</v-list-item>
                <v-list-item @click="onDropdownSelected('รายเดือน')">รายเดือน</v-list-item>
                <v-list-item @click="onDropdownSelected('รายวัน')">รายวัน</v-list-item>
              </v-list>
            </v-menu>
            <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">ปี :</span>
            <v-menu v-if="showYearDropdown" offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">
                    {{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}
                  </span>
                  <v-spacer></v-spacer>
                  <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year)">{{ year + 543 }}</v-list-item>
              </v-list>
            </v-menu>
            <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">เดือน :</span>
            <v-menu v-if="showMonthDropdown" offset-y>
              <template v-slot:activator="{ on, attrs }">
                <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">
                    {{ monthName === null ? 'เลือกเดือน' : monthName }}
                  </span>
                  <v-spacer></v-spacer>
                  <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelected(month.value)">{{ month.text }}</v-list-item>
              </v-list>
            </v-menu>
            <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">วันที่ :</span>
            <v-dialog v-if="showDatePicker" ref="modalDateSelect" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
              <template v-slot:activator = "{ on, attrs }">
                <v-text-field
                  v-bind="attrs"
                  v-on="on"
                  v-model="daterange"
                  placeholder="วว/ดด/ปปปป"
                  dense
                  rounded
                  readonly
                  hide-details
                  class="d-inline-block ml-2 custom-text-field"
                  style="border: 1px solid #EBEBEB; border-radius: 8px;"
                >
                  <v-spacer></v-spacer>
                  <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                style="font-size:29px !important; height: 480px !important"
                v-model="dates"
                scrollable
                reactive
                locale="Th-th"
                range
                no-title
                full-width
                :min="minDate"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
              <v-row>
                <v-col align="end">
                  <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                  <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                </v-col>
              </v-row>
              </v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === true">
      <v-col>
        <v-select
          v-model="mobileTitlesSelected"
          :items="mobileTitles"
          hide-details
          outlined
          @change="changeMenu()"
          label="เลือกเมนู"
        >
          <template slot="selection">
            <span style="font-weight: 600; font-size: 18px;">{{mobileTitlesSelected}}</span>
          </template>
        </v-select>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize">
    </v-row>
    <v-row v-else>
      <v-col cols="12">
        <v-avatar rounded size="24">
          <v-img contain :src="passiveIncomeIconPath"></v-img>
        </v-avatar>
        <span style="font-size: 16px; color: #27ab9c; font-weight: 700">ข้อมูลการขายสินค้าผ่านลิงค์ affiliate</span>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && mobileTitlesSelected === 'ข้อมูลการขายสินค้าผ่านลิงค์ affiliate'">
      <v-col cols="12">
        <v-row>
          <v-col class="d-flex justify-center">
            <v-tabs class="d-flex justify-center" @change="setChartsTypeMobile">
              <v-tab>
                <span>ค่าคอมมิชชัน</span>
              </v-tab>
              <v-tab>
                <span>ยอดขาย</span>
              </v-tab>
              <v-tab>
                <span>จำนวนที่ขายได้</span>
              </v-tab>
            </v-tabs>
          </v-col>
        </v-row>
        <v-card>
          <v-card-text v-if="selectedDropdown === 'รายปี'">
            <apexchart width="100%" height="400" type="line" :options="chartOptions" :series="series"></apexchart>
          </v-card-text>
          <v-card-text  v-if="selectedDropdown === 'รายเดือน'">
            <apexchart width="100%" height="400" type="line" :options="chartOptionsMonth" :series="series"></apexchart>
          </v-card-text>
          <v-card-text  v-if="selectedDropdown === 'รายวัน'">
            <apexchart width="100%" height="400" type="line" :options="chartOptionsDay" :series="series"></apexchart>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false && IpadSize">
      <v-col cols="12">
        <v-card elevation="0" v-if="chartSelected === 'commission'">
          <v-btn @click="onChartSelected('commission')" style="background-color: white;">
            คอมมิชชัน
          </v-btn>
          <v-btn @click="onChartSelected('price')">
            ข้อมูลการขาย
          </v-btn>
          <v-btn @click="onChartSelected('piece')">
            ข้อมูลจำนวนสินค้าที่ขายได้
          </v-btn>
        </v-card>
        <v-card elevation="0" v-if="chartSelected === 'price'">
          <v-btn @click="onChartSelected('commission')">
            คอมมิชชัน
          </v-btn>
          <v-btn @click="onChartSelected('price')" style="background-color: white;">
            ข้อมูลการขาย
          </v-btn>
          <v-btn @click="onChartSelected('piece')">
            ข้อมูลจำนวนสินค้าที่ขายได้
          </v-btn>
        </v-card>
        <v-card elevation="0" v-if="chartSelected === 'piece'">
          <v-btn @click="onChartSelected('commission')">
            คอมมิชชัน
          </v-btn>
          <v-btn @click="onChartSelected('price')">
            ข้อมูลการขาย
          </v-btn>
          <v-btn @click="onChartSelected('piece')" style="background-color: white;">
            ข้อมูลจำนวนสินค้าที่ขายได้
          </v-btn>
        </v-card>
        <v-card>
          <v-card-title>
              <v-row>
                <v-col cols="12">
                  <v-avatar rounded size="20" class="mr-2">
                    <v-img contain :src="statisticsIconPath"></v-img>
                  </v-avatar>
                  <span style="font-size: 18px">กราฟแสดงข้อมูลสินค้าที่ถูกขายผ่านลิงค์ affiliate</span>
                </v-col>
              </v-row>
          </v-card-title>
          <v-card-text v-if="selectedDropdown === 'รายปี'">
            <apexchart width="100%" height="400" type="line" :options="chartOptions" :series="series"></apexchart>
          </v-card-text>
          <v-card-text  v-if="selectedDropdown === 'รายเดือน'">
            <apexchart width="100%" height="400" type="line" :options="chartOptionsMonth" :series="series"></apexchart>
          </v-card-text>
          <v-card-text  v-if="selectedDropdown === 'รายวัน'">
            <apexchart width="100%" height="400" type="line" :options="chartOptionsDay" :series="series"></apexchart>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false && IpadSize === false">
      <v-col cols="12">
        <v-card elevation="0" v-if="chartSelected === 'commission'">
          <v-btn @click="onChartSelected('commission')" style="background-color: white;">
            คอมมิชชัน
          </v-btn>
          <v-btn @click="onChartSelected('price')">
            ข้อมูลการขาย
          </v-btn>
          <v-btn @click="onChartSelected('piece')">
            ข้อมูลจำนวนสินค้าที่ขายได้
          </v-btn>
        </v-card>
        <v-card elevation="0" v-if="chartSelected === 'price'">
          <v-btn @click="onChartSelected('commission')">
            คอมมิชชัน
          </v-btn>
          <v-btn @click="onChartSelected('price')" style="background-color: white;">
            ข้อมูลการขาย
          </v-btn>
          <v-btn @click="onChartSelected('piece')">
            ข้อมูลจำนวนสินค้าที่ขายได้
          </v-btn>
        </v-card>
        <v-card elevation="0" v-if="chartSelected === 'piece'">
          <v-btn @click="onChartSelected('commission')">
            คอมมิชชัน
          </v-btn>
          <v-btn @click="onChartSelected('price')">
            ข้อมูลการขาย
          </v-btn>
          <v-btn @click="onChartSelected('piece')" style="background-color: white;">
            ข้อมูลจำนวนสินค้าที่ขายได้
          </v-btn>
        </v-card>
        <v-card>
          <v-card-title>
            <v-row>
              <v-col cols="6">
                <v-avatar rounded size="20" class="mr-2">
                  <v-img contain :src="statisticsIconPath"></v-img>
                </v-avatar>
                <span style="font-size: 18px">กราฟแสดงข้อมูลสินค้าที่ถูกขายผ่านลิงค์ affiliate</span>
              </v-col>
              <v-col cols="6">
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text v-if="selectedDropdown === 'รายปี'">
            <apexchart width="100%" height="400" type="line" :options="chartOptions" :series="series"></apexchart>
          </v-card-text>
          <v-card-text  v-if="selectedDropdown === 'รายเดือน'">
            <apexchart width="100%" height="400" type="line" :options="chartOptionsMonth" :series="series"></apexchart>
          </v-card-text>
          <v-card-text  v-if="selectedDropdown === 'รายวัน'">
            <apexchart width="100%" height="400" type="line" :options="chartOptionsDay" :series="series"></apexchart>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false">
      <v-col cols="6">
        <v-card>
          <v-row>
            <v-col align="center">
              <v-avatar rounded size="80">
                <v-img contain :src="commissionicon"></v-img>
              </v-avatar>
            </v-col>
          </v-row>
          <v-row>
            <v-col align="center">
              <span style="font-size: 48px; color: #186deb; font-weight: bold;">{{total_commission_paid}}</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col align="center">
              <v-row>
                <v-col class="pa-0">
                  <span style="font-size: 18px;">ค่าคอมมิชชันทั้งหมด</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pa-0 mb-5">
                  <span style="font-size: 18px;">(บาท)</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <v-col cols="6">
        <v-card>
          <v-row>
            <v-col align="center">
              <v-avatar rounded size="80">
                <v-img contain :src="moneyIconPath"></v-img>
              </v-avatar>
            </v-col>
          </v-row>
          <v-row>
            <v-col align="center">
              <span style="font-size: 48px; color: #186deb; font-weight: bold;">{{total_price}}</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col align="center">
              <v-row>
                <v-col class="pa-0">
                  <span style="font-size: 18px;">ยอดขายทั้งหมด</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pa-0 mb-3">
                  <span style="font-size: 18px;">(บาท)</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <v-col cols="12" class="mt-3">
        <v-card>
          <v-row>
            <v-col align="center">
              <v-avatar rounded size="80">
                <v-img contain :src="boxIconPath"></v-img>
              </v-avatar>
            </v-col>
          </v-row>
          <v-row>
            <v-col align="center">
              <span style="font-size: 48px; color: #186deb; font-weight: bold;">{{total_quantity}}</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col align="center">
              <v-row>
                <v-col class="pa-0">
                  <span style="font-size: 18px;">จำนวนสินค้าที่ทั้งหมดขายได้</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pa-0 mb-3">
                  <span style="font-size: 18px;">(ชิ้น)</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <!-- ข้อมูลการขาย -->
    <v-row></v-row>
    <v-row v-if="MobileSize && mobileTitlesSelected === 'รายการสินค้าที่มีการ affiliate'">
      <v-col cols="12" class="d-flex align-center">
      <v-text-field
        outlined
        placeholder="ค้นหาสินค้า"
        dense
        hide-details
        v-model="productSearchTable"
        @change="usePaginationProduct('filter')"
      ></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-data-table
            :headers="productHeadersTable"
            :items="productTable.productLists"
            :items-per-page="productTable.item_end"
            hide-default-footer
            style="width:100%; white-space: nowrap;"
            height="290px"
            @click:row="showProductsDetails"
          >
            <template v-slot:[`item.createdAt`]="{ item }">
              {{new Date(item.createdAt * 1000).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.name`]="{ item }">
              <v-row>
                <v-col>
                  <span style="white-space: nowrap;">{{ item.name }}</span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  x-small
                  outlined
                  @click="openProductDialog(item.affiliate)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
          <v-row no-gutters>
            <v-col cols="12" class="d-flex justify-end">
              <v-divider></v-divider>
              <v-card flat style="border-top: 5px solid rgb(0 0 0 / 12%); border-radius: 0px; border-width: thin 0 0 0">
                <v-col cols="12" class="d-flex justify-end align-center pa-0">
                  <span style="font-size: 12px; margin-right: 5px">Rows per page:</span>
                    <v-col cols="2" class="py-0">
                      <v-select
                        class="pt-0 my-5"
                        v-model="rowPerPagesProductsTable"
                        :items="pageTableItemsSizeProduct"
                        @change="usePaginationProduct('pageSize')"
                        hide-details
                      >
                        <template v-slot:[`body.options`]="{item}">
                          {{item}}
                        </template>
                        <template v-slot:selection="{item}">
                          <span style="font-size: 12px;">{{item}}</span>
                        </template>
                      </v-select>
                    </v-col>
                  <span style="font-size: 12px; margin-left: 20px;">{{productTable.current_page}} of {{productTable.max_pages}}</span>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationProduct('previous')" disabled v-if="productTable.current_page === 1"><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationProduct('previous')" v-else><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon @click="usePaginationProduct('next')" disabled v-if="productTable.current_page === productTable.max_pages"><v-icon>mdi-chevron-right</v-icon></v-btn>
                  <v-btn icon @click="usePaginationProduct('next')" v-else><v-icon>mdi-chevron-right</v-icon></v-btn>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && mobileTitlesSelected === 'ตารางรายงานการสั่งซื้อ'">
      <v-col cols="12" class="d-flex align-center">
      <v-text-field
        outlined
        placeholder="ค้นหารายการคำสั่งซื้อ"
        dense
        hide-details
        v-model="sellerReportSearchTable"
        @change="usePaginationSellerReport('filter')"
      ></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-data-table
            :headers="sellerReporttableheaders"
            :items="sellerReportTable.response"
            :items-per-page="usersTable.max_items"
            style="width:100%;"
            height="100%"
            hide-default-footer
            @click:row="showSellerReportDetails"
          >
            <template
              v-slot:[`item.order_row.date`]="{ item }"
            >
              {{ new Date(item.order_row.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  x-small
                  outlined
                  @click="openSellerReportDialog(item.affiliate)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
          <v-row no-gutters>
            <v-col cols="12" class="d-flex justify-end">
              <v-divider></v-divider>
              <v-card flat style="border-top: 5px solid rgb(0 0 0 / 12%); border-radius: 0px; border-width: thin 0 0 0">
                <v-col cols="12" class="d-flex justify-end align-center pa-0">
                  <span style="font-size: 12px; margin-right: 5px">Rows per page:</span>
                    <v-col cols="2" class="py-0">
                      <v-select
                        class="pt-0 my-5"
                        v-model="rowPerPagesSellerReportTable"
                        :items="pageTableItemsSizeProduct"
                        @change="usePaginationSellerReport('pageSize')"
                        hide-details
                      >
                        <template v-slot:[`body.options`]="{item}">
                          {{item}}
                        </template>
                        <template v-slot:selection="{item}">
                          <span style="font-size: 12px;">{{item}}</span>
                        </template>
                      </v-select>
                    </v-col>
                  <span style="font-size: 12px; margin-left: 20px;">{{sellerReportTable.current_page}} of {{sellerReportTable.max_pages}}</span>
                  <v-btn icon class="mr-3 ml-5" @click="sellerReportSearchTable('previous')" disabled v-if="sellerReportTable.current_page === 1"><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationSellerReport('previous')" v-else><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon @click="usePaginationSellerReport('next')" disabled v-if="sellerReportTable.current_page === sellerReportTable.max_pages"><v-icon>mdi-chevron-right</v-icon></v-btn>
                  <v-btn icon @click="usePaginationSellerReport('next')" v-else><v-icon>mdi-chevron-right</v-icon></v-btn>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row class="mt-8" v-if="MobileSize === false">
      <v-col cols="9" class="d-flex align-center">
        <v-avatar rounded size="24">
          <v-img contain :src="dataModelIconPath"></v-img>
        </v-avatar>
        <span style="font-size: 16px; color: #27ab9c; font-weight: 700; margin-left: 8px;">รายการสินค้าที่มีการ affiliate</span>
        <v-text-field
          outlined
          rounded
          placeholder="ค้นหาสินค้า"
          dense
          hide-details
          class="ml-5"
          v-model="productSearchTable"
          @change="usePaginationProduct('filter')"
        ></v-text-field>
      </v-col>
      <v-col cols="3" class="d-flex justify-end align-center">
        <v-btn style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('productlist_seller')">
          <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
          <span class="white--text ml-1">Export</span>
        </v-btn>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-data-table
            :headers="productHeadersTable"
            :items="productTable.productLists"
            :items-per-page="productTable.item_end"
            hide-default-footer
            style="width:100%;  white-space: nowrap;"
            height="100%"
            class="TableProductAffiliate"
            @click:row="showProductsDetails"
          >
            <template v-slot:[`item.createdAt`]="{ item }">
              {{new Date(item.createdAt * 1000).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.name`]="{ item }">
              <v-row>
                <v-col>
                  <span style="white-space: nowrap;">{{ item.name }}</span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.commission_rate`]="{ item }">
              <v-row>
                <v-col v-if="item.commission_type === 'percent'">
                  <span style="white-space: nowrap;">{{ item.commission_rate }} %</span>
                </v-col>
                <v-col v-else>
                  <span style="white-space: nowrap;">{{ item.commission_rate }} บาท</span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  x-small
                  outlined
                  @click="openProductDialog(item.affiliate)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
          <v-row no-gutters>
            <v-col cols="12" class="d-flex justify-end">
              <v-divider></v-divider>
              <v-card flat style="border-top: 5px solid rgb(0 0 0 / 12%); border-radius: 0px; border-width: thin 0 0 0">
                <v-col cols="12" class="d-flex justify-end align-center pa-0">
                  <span style="font-size: 12px; margin-right: 5px">Rows per page:</span>
                    <v-col cols="2" class="py-0">
                      <v-select
                        class="pt-0 my-5"
                        v-model="rowPerPagesProductsTable"
                        :items="pageTableItemsSizeProduct"
                        @change="usePaginationProduct('pageSize')"
                        hide-details
                      >
                        <template v-slot:[`body.options`]="{item}">
                          {{item}}
                        </template>
                        <template v-slot:selection="{item}">
                          <span style="font-size: 12px;">{{item}}</span>
                        </template>
                      </v-select>
                    </v-col>
                  <span style="font-size: 12px; margin-left: 20px;">{{productTable.current_page}} of {{productTable.max_pages}}</span>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationProduct('previous')" disabled v-if="productTable.current_page === 1"><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationProduct('previous')" v-else><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon @click="usePaginationProduct('next')" disabled v-if="productTable.current_page === productTable.max_pages"><v-icon>mdi-chevron-right</v-icon></v-btn>
                  <v-btn icon @click="usePaginationProduct('next')" v-else><v-icon>mdi-chevron-right</v-icon></v-btn>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && mobileTitlesSelected === 'ตารางแสดงข้อมูลผู้ใช้งานลิงก์ affiliate'">
      <v-col cols="12" align="center">
        <v-text-field
          placeholder="ค้นหาสินค้า"
          hide-details
          dense
          outlined
          v-model="userSearchTable"
          @change="usePaginationUsers('filter')"
        ></v-text-field>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-data-table
            :headers="userstableheaders"
            :items="usersTable.usersList"
            :items-per-page="usersTable.max_items"
            style="width:100%;"
            height="290px"
            hide-default-footer
            @click:row="showUsersDetails"
          >
            <template
              v-slot:[`item.createdAt`]="{ item }"
            >
              {{ new Date(item.createdAt * 1000).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.useractions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  dense
                  outlined
                  @click="openUsersDialog(item.affiliate)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`footer.page-text`]="items">
              แสดง {{ items.pageStart + 1}} {{ items.pageStop }} {{ items.itemsLength }}
            </template>
          </v-data-table>
          <v-row no-gutters>
            <v-col cols="12" class="d-flex justify-end">
              <v-divider></v-divider>
              <v-card flat style="border-top: 5px solid rgb(0 0 0 / 12%); border-radius: 0px; border-width: thin 0 0 0">
                <v-col cols="12" class="d-flex justify-end align-center pa-0">
                  <span style="font-size: 12px; margin-right: 5px">Rows per page:</span>
                    <v-col cols="2" class="py-0">
                      <v-select
                        class="pt-0 my-5"
                        v-model="rowPerPagesUsersTable"
                        :items="pageTableItemsSizeProduct"
                        @change="usePaginationUsers('pageSize')"
                        hide-details
                      >
                        <template v-slot:[`body.options`]="{item}">
                          {{item}}
                        </template>
                        <template v-slot:selection="{item}">
                          <span style="font-size: 12px;">{{item}}</span>
                        </template>
                      </v-select>
                    </v-col>
                  <span style="font-size: 12px; margin-left: 20px;">{{usersTable.current_page}} of {{usersTable.max_pages}}</span>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationUsers('previous')" disabled v-if="usersTable.current_page === 1"><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationUsers('previous')" v-else><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon @click="usePaginationUsers('next')" disabled v-if="usersTable.current_page === usersTable.max_pages"><v-icon>mdi-chevron-right</v-icon></v-btn>
                  <v-btn icon @click="usePaginationUsers('next')" v-else><v-icon>mdi-chevron-right</v-icon></v-btn>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false">
      <v-col cols="9">
        <v-row>
          <v-col cols="7" class="d-flex align-center">
            <span style="font-size: 16px; color: #27ab9c; font-weight: 700; margin-left: 8px;">ตารางแสดงข้อมูลผู้ใช้งานลิงก์ affiliate</span>
          </v-col>
          <v-col cols="5">
            <v-text-field
              placeholder="ค้นหาชื่อผู้ใช้"
              v-model="userSearchTable"
              @change="usePaginationUsers('filter')"
              hide-details
              dense
              outlined
              rounded
            ></v-text-field>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="3" align="end" class="d-flex align-center justify-end">
        <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('userlist_seller')">
          <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
          <span class="white--text">Export</span>
        </v-btn>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-data-table
            :headers="userstableheaders"
            :items="usersTable.usersList"
            :items-per-page="usersTable.max_items"
            style="width:100%;"
            height="100%"
            hide-default-footer
            @click:row="showUsersDetails"
          >
            <template
              v-slot:[`item.createdAt`]="{ item }"
            >
              {{ new Date(item.createdAt * 1000).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.useractions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  x-small
                  outlined
                  @click="openUsersDialog(item.affiliate)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
          <v-row no-gutters>
            <v-col cols="12" class="d-flex justify-end">
              <v-divider></v-divider>
              <v-card flat style="border-top: 5px solid rgb(0 0 0 / 12%); border-radius: 0px; border-width: thin 0 0 0">
                <v-col cols="12" class="d-flex justify-end align-center pa-0">
                  <span style="font-size: 12px; margin-right: 5px">Rows per page:</span>
                    <v-col cols="2" class="py-0">
                      <v-select
                        class="pt-0 my-5"
                        v-model="rowPerPagesUsersTable"
                        :items="pageTableItemsSizeProduct"
                        @change="usePaginationUsers('pageSize')"
                        hide-details
                      >
                        <template v-slot:[`body.options`]="{item}">
                          {{item}}
                        </template>
                        <template v-slot:selection="{item}">
                          <span style="font-size: 12px;">{{item}}</span>
                        </template>
                      </v-select>
                    </v-col>
                  <span style="font-size: 12px; margin-left: 20px;">{{usersTable.current_page}} of {{usersTable.max_pages}}</span>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationUsers('previous')" disabled v-if="usersTable.current_page === 1"><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationUsers('previous')" v-else><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon @click="usePaginationUsers('next')" disabled v-if="usersTable.current_page === usersTable.max_pages"><v-icon>mdi-chevron-right</v-icon></v-btn>
                  <v-btn icon @click="usePaginationUsers('next')" v-else><v-icon>mdi-chevron-right</v-icon></v-btn>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false">
      <v-col cols="9">
        <v-row>
          <v-col cols="7" class="d-flex align-center">
            <span style="font-size: 16px; color: #27ab9c; font-weight: 700; margin-left: 8px;">ตารางรายงานการสั่งซื้อ</span>
          </v-col>
          <v-col cols="5">
            <v-text-field
              placeholder="ค้นหารหัสสินค้า"
              v-model="sellerReportSearchTable"
              @change="usePaginationSellerReport('filter')"
              hide-details
              dense
              outlined
              rounded
            ></v-text-field>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="3" align="end" class="d-flex align-center justify-end">
        <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('report_seller')">
          <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
          <span class="white--text">Export</span>
        </v-btn>
      </v-col>
      <v-col cols="12">
        <v-card>
          <v-data-table
            :headers="sellerReporttableheaders"
            :items="sellerReportTable.response"
            :items-per-page="usersTable.max_items"
            style="width:100%;"
            height="100%"
            hide-default-footer
            @click:row="showSellerReportDetails"
          >
            <template
              v-slot:[`item.order_row.date`]="{ item }"
            >
              {{ new Date(item.order_row.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.actions`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  x-small
                  outlined
                  @click="openSellerReportDialog(item.affiliate)"
                  style="border: none; width:100%;"
                  height="100%"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
          <v-row no-gutters>
            <v-col cols="12" class="d-flex justify-end">
              <v-divider></v-divider>
              <v-card flat style="border-top: 5px solid rgb(0 0 0 / 12%); border-radius: 0px; border-width: thin 0 0 0">
                <v-col cols="12" class="d-flex justify-end align-center pa-0">
                  <span style="font-size: 12px; margin-right: 5px">Rows per page:</span>
                    <v-col cols="2" class="py-0">
                      <v-select
                        class="pt-0 my-5"
                        v-model="rowPerPagesSellerReportTable"
                        :items="pageTableItemsSizeProduct"
                        @change="usePaginationSellerReport('pageSize')"
                        hide-details
                      >
                        <template v-slot:[`body.options`]="{item}">
                          {{item}}
                        </template>
                        <template v-slot:selection="{item}">
                          <span style="font-size: 12px;">{{item}}</span>
                        </template>
                      </v-select>
                    </v-col>
                  <span style="font-size: 12px; margin-left: 20px;">{{sellerReportTable.current_page}} of {{sellerReportTable.max_pages}}</span>
                  <v-btn icon class="mr-3 ml-5" @click="sellerReportSearchTable('previous')" disabled v-if="sellerReportTable.current_page === 1"><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon class="mr-3 ml-5" @click="usePaginationSellerReport('previous')" v-else><v-icon>mdi-chevron-left</v-icon></v-btn>
                  <v-btn icon @click="usePaginationSellerReport('next')" disabled v-if="sellerReportTable.current_page === sellerReportTable.max_pages"><v-icon>mdi-chevron-right</v-icon></v-btn>
                  <v-btn icon @click="usePaginationSellerReport('next')" v-else><v-icon>mdi-chevron-right</v-icon></v-btn>
                </v-col>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false">
      <v-col cols="9">
        <v-avatar rounded size="24">
          <v-img contain :src="uniqueIconPath"></v-img>
        </v-avatar>
        <span style="font-size: 16px; color: #27ab9c; font-weight: 700; margin-left: 8px;">ลิงค์สินค้าที่ถูกกดมากที่สุด 10 อันดับ</span>
      </v-col>
      <v-col cols="3" class="d-flex justify-end">
        <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('topten_seller')">
          <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
          <span class="white--text ml-1">Export</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-card flat v-if="MobileSize && mobileTitlesSelected === 'ลิงค์สินค้าที่ถูกกดมากที่สุด'">
      <v-row>
        <v-col cols="12">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestShared.slice(0, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="55px">
                <v-row class="pa-1" no-gutters>
                  <v-col cols="2">
                    <v-avatar  v-if="index === 0 ">
                      <v-img contain :src="goldMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          1
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 1 ">
                      <v-img contain :src="silverMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px; color: #e37f22">
                          2
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 2">
                      <v-img contain :src="bronzeMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          3
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 1}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="6">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">การกด {{item.total_click}} ครั้ง</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card flat v-if="MobileSize && mobileTitlesSelected === 'รายการสินค้าที่ถูกขายมากที่สุด 10 อันดับ'">
      <v-row>
        <v-col cols="12">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestSold.slice(0, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="55px">
                <v-row class="pa-1" no-gutters>
                  <v-col cols="2">
                    <v-avatar  v-if="index === 0 ">
                      <v-img contain :src="goldMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          1
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 1 ">
                      <v-img contain :src="silverMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px; color: #e37f22">
                          2
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 2">
                      <v-img contain :src="bronzeMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          3
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 1}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="6" class="d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">ขายได้ {{item.quantity}} ชิ้น</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card flat v-if="MobileSize === false && IpadSize">
      <v-row>
        <v-col cols="12">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestShared.slice(0, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="60px">
                <v-row class="pa-1" no-gutters>
                  <v-col cols="2">
                    <v-avatar  v-if="index === 0 ">
                      <v-img contain :src="goldMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          1
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 1 ">
                      <v-img contain :src="silverMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px; color: #e37f22">
                          2
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 2">
                      <v-img contain :src="bronzeMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          3
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 1}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-2 pr-2 d-flex align-center">
                    <v-avatar tile size="50">
                      <v-img :src="item.media_path ? item.media_path : require('@/assets/NoImage.png')"/>
                    </v-avatar>
                  </v-col>
                  <v-col cols="5" class="pl-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="3" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">กดลิงก์ {{item.total_click}} ครั้ง</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card flat v-if="MobileSize === false && IpadSize === false">
      <v-row>
        <v-col cols="6">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestShared.slice(0, 5)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="60px">
                <v-row class="pa-1" no-gutters>
                  <v-col cols="2">
                    <v-avatar  v-if="index === 0 ">
                      <v-img contain :src="goldMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          1
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 1 ">
                      <v-img contain :src="silverMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px; color: #e37f22">
                          2
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 2">
                      <v-img contain :src="bronzeMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          3
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 1}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-2 pr-2 d-flex align-center">
                    <v-avatar tile size="50">
                      <v-img :src="item.media_path ? item.media_path : require('@/assets/NoImage.png')"/>
                    </v-avatar>
                  </v-col>
                  <v-col cols="5" class="pl-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="3" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">กดลิงก์ {{item.total_click}} ครั้ง</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="6">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestShared.slice(5, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="60px">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold "
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 6}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="pl-2 pr-2 d-flex align-center">
                    <v-avatar tile size="50">
                      <v-img :src="item.media_path ? item.media_path : require('@/assets/NoImage.png')"/>
                    </v-avatar>
                  </v-col>
                  <v-col cols="5" class="pr-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="3" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">กดลิงก์ {{item.total_click}} ครั้ง</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-row v-if="MobileSize === false" class="mt-5">
      <v-col cols="9">
        <v-avatar rounded size="24">
          <v-img contain :src="uniqueIconPath"></v-img>
        </v-avatar>
        <span style="font-size: 16px; color: #27ab9c; font-weight: 700; margin-left: 8px;">รายการสินค้าที่ถูกขายมากที่สุด 10 อันดับ</span>
      </v-col>
      <v-col cols="3" class="d-flex justify-end">
        <v-btn height="32px" style="border-radius: 40px; background: #27AB9C" @click="getReportExcel('toptensolds_seller')">
          <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
          <span class="white--text ml-1">Export</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-card flat v-if="MobileSize === false && IpadSize">
      <v-row v-if="bestSold.length !== 0">
        <v-col cols="12" class="mt-3">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestSold.slice(0, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="55px">
                <v-row class="pa-1" no-gutters>
                  <v-col cols="2">
                    <v-avatar  v-if="index === 0 ">
                      <v-img contain :src="goldMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          1
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 1 ">
                      <v-img contain :src="silverMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px; color: #e37f22">
                          2
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 2">
                      <v-img contain :src="bronzeMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          3
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 1}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="6" class="pl-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">ขายได้ {{item.quantity}} ชิ้น</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- <v-col cols="6" class="mt-3">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestSold.slice(5, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="55px">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold "
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 6}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="6" class="pr-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">ขายได้ {{item.quantity}} ชิ้น</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col> -->
      </v-row>
      <v-row v-else-if="bestSold.length === 0">
        <v-col>
          <span style="color: rgb(196, 196, 196); font-size: 18px; margin-left: 12px;">ไม่มีสินค้าที่ขายได้ในช่วงเวลานี้</span>
        </v-col>
      </v-row>
    </v-card>
    <v-card flat v-if="MobileSize === false && IpadSize === false">
      <v-row v-if="bestSold.length !== 0">
        <v-col cols="6" class="mt-3">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestSold.slice(0, 5)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="55px">
                <v-row class="pa-1" no-gutters>
                  <v-col cols="2">
                    <v-avatar  v-if="index === 0 ">
                      <v-img contain :src="goldMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          1
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 1 ">
                      <v-img contain :src="silverMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px; color: #e37f22">
                          2
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else-if="index === 2">
                      <v-img contain :src="bronzeMedalIconPath"></v-img>
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          3
                        </font>
                      </span>
                    </v-avatar>
                    <v-avatar v-else color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold"
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 1}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="6" class="pl-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">ขายได้ {{item.quantity}} ชิ้น</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="6" class="mt-3">
          <v-row>
            <v-col cols="12" v-for="(item, index) in bestSold.slice(5, 10)" :key="index" >
              <v-card style="border: 1px solid #E6FCD6;" elevation="0" height="55px">
                <v-row class="pa-1">
                  <v-col cols="2">
                    <v-avatar color="#f9fafd">
                      <span
                        class="display-1 font-weight-bold "
                        style="position: absolute; top: 45%; left: 50%; transform: translate(-50%, -50%); font-size: 10px;"
                      >
                        <font style="font-size: 20px;">
                          {{index + 6}}
                        </font>
                      </span>
                    </v-avatar>
                  </v-col>
                  <v-col cols="6" class="pr-2 d-flex align-center">
                    <span>{{item.name}}</span>
                  </v-col>
                  <v-col cols="4" class="d-flex justify-end">
                    <v-chip style="margin-top: 7px" color="#27AB9C0D">
                      <span style="color: #4ba597;">ขายได้ {{item.quantity}} ชิ้น</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row v-else-if="bestSold.length === 0">
        <v-col>
          <span style="color: rgb(196, 196, 196); font-size: 18px; margin-left: 12px;">ไม่มีสินค้าที่ขายได้ในช่วงเวลานี้</span>
        </v-col>
      </v-row>
    </v-card>
    <!-- dialog -->
    <v-dialog
      v-model="productOpen"
      max-width="600px"
      persistent
      scrollable
    >
      <v-card>
        <v-card-title style="background-color: #bde7d9;">
          <v-row>
            <v-col class="d-flex justify-center pa-3" :cols="MobileSize ? 10 : 11">
              <span style="color: rgb(39, 171, 156); font-size: 20px; font-weight: bold;" :class="MobileSize ? 'ml-10' : ''">รายละเอียดของสินค้า</span>
            </v-col>
            <v-col :cols="MobileSize ? 2 : 1">
              <v-btn icon @click="productOpen = false">
                <v-icon color="rgb(39, 171, 156)">mdi-close</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card-text>
          <v-card v-for="(item, index) in detailDialogProductData" :key="index" class="mt-5">
            <v-row class="mt-5 pa-10">
              <v-col cols="12" sm="4">
                <v-row>
                  <v-col v-if="item.color_image_path != null">
                    <v-img :src="item.color_image_path"/>
                  </v-col>
                  <v-col v-else>
                    <v-img src="@/assets/NoImage.png"/>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" sm="8" v-if="MobileSize">
                <v-row>
                  <v-col class="d-flex align-center" cols="12" sm="6">
                    <span style="max-width: 100vw; font-size: 18px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis; color: #27AB9C;">{{item.name}}</span>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col class="d-flex align-center" cols="12">
                    <span style="font-weight: bold;" v-if="item.attribute_priority_1 !== '' || item.attribute_priority_2 !== ''">ชนิดสินค้า {{ item.attribute_priority_1 }} {{ item.attribute_priority_2 !== '' ? ', ' + item.attribute_priority_2 : '' }}</span>
                    <span v-else>-</span>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="6">
                    <span style="font-weight: bold;">ราคาสินค้า: </span>
                  </v-col>
                  <v-col cols="6">
                    <span>{{item.product_price}} บาท</span>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="6">
                    <span style="font-weight: bold;">ค่าคอมมิชชัน: </span>
                  </v-col>
                  <v-col cols="6">
                    <span>{{item.commission_paid}} บาท</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" sm="8" v-if="MobileSize === false">
                <v-row>
                  <v-col class="d-flex align-center" cols="12" sm="12">
                    <span style="max-width: 100vw; font-size: 18px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box;overflow: hidden; text-overflow: ellipsis; color: #27AB9C;">{{item.name}}</span>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col class="d-flex align-center" cols="6">
                    <span>ชนิดสินค้า</span>
                  </v-col>
                  <v-col class="d-flex align-center" cols="6">
                    <span v-if="item.attribute_priority_1 !== '' || item.attribute_priority_2 !== ''">{{ item.attribute_priority_1 }} {{ item.attribute_priority_2 !== '' ? ', ' + item.attribute_priority_2 : '' }}</span>
                    <span v-else>-</span>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="6">
                    <span>ราคาสินค้า</span>
                  </v-col>
                  <v-col cols="3">
                    <span>{{item.product_price}}</span>
                  </v-col>
                  <v-col cols="3">
                    <span>บาท</span>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="6">
                    <span>ค่าคอมมิชชันของสินค้า</span>
                  </v-col>
                  <v-col cols="3">
                    <span>{{item.commission_paid}}</span>
                  </v-col>
                  <v-col cols="3">
                    <span>บาท</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="usersOpen"
      max-width="600px"
      persistent
    >
      <v-card>
        <v-card-title style="background-color: #bde7d9;">
          <v-row>
            <v-col class="d-flex justify-center" :cols="MobileSize ? 10 : 11">
              <span style="color: rgb(39, 171, 156); font-size: 20px; font-weight: bold;" :class="MobileSize ? 'ml-6' : ''">รายละเอียดของผู้แชร์ลิงก์</span>
            </v-col>
            <v-col :cols="MobileSize ? 2 : 1">
              <v-btn icon @click="usersOpen = false">
                <v-icon color="rgb(39, 171, 156)">mdi-close</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card-text>
          <v-row class="mt-5">
            <v-col cols="12">
              <v-row>
                <v-col cols="4">
                  <span style="font-weight: bold;">ชื่อผู้ใช้</span>
                </v-col>
                <v-col cols="8">
                  <span>{{detailDialogData.name}}</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6" sm="4">
                  <span style="font-weight: bold;">จำนวนการคลิก</span>
                </v-col>
                <v-col cols="3" sm="4">
                  <span>{{detailDialogData.total_click_all}}</span>
                </v-col>
                <v-col cols="3" sm="4">
                  <span>ครั้ง</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6" sm="4">
                  <span style="font-weight: bold;">จำนวนการขาย</span>
                </v-col>
                <v-col cols="3" sm="4">
                  <span>{{detailDialogData.total_sales}}</span>
                </v-col>
                <v-col cols="3" sm="4">
                  <span>ชิ้น</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6" sm="4">
                  <span style="font-weight: bold;">ค่าคอมมิชชันที่ได้</span>
                </v-col>
                <v-col cols="3" sm="4">
                  <span>{{detailDialogData.commission_received}}</span>
                </v-col>
                <v-col cols="3" sm="4">
                  <span>บาท</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="sellerReportDialog"
      max-width="600px"
      persistent
    >
      <v-card>
        <v-card-title style="background-color: #bde7d9;">
          <v-row>
            <v-col class="d-flex justify-center" :cols="MobileSize ? 10 : 11">
              <span style="color: rgb(39, 171, 156); font-size: 20px; font-weight: bold;" :class="MobileSize ? 'ml-6' : ''">รายละเอียดของรายการสั่งซื้อสินค้า</span>
            </v-col>
            <v-col :cols="MobileSize ? 2 : 1">
              <v-btn icon @click="sellerReportDialog = false">
                <v-icon color="rgb(39, 171, 156)">mdi-close</v-icon>
              </v-btn>
            </v-col>
          </v-row>
        </v-card-title>
        <v-card-text>
          <v-row class="mt-5">
            <v-col cols="12">
              <v-card v-for="(item, index) in detailDialogSellerReportData" :key="index" class="mt-5">
                <v-row>
                  <v-col cols="12" sm="5">
                    <v-row>
                      <v-col v-if="item.color_image_path != null">
                        <v-img :src="item.color_image_path"/>
                      </v-col>
                      <v-col v-else>
                        <v-img src="@/assets/NoImage.png"/>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" sm="7" v-if="MobileSize === false">
                    <v-row>
                      <v-col class="d-flex align-center py-1 mt-3" cols="6" sm="12">
                        <span style="font-size: 16px;">ชื่อสินค้า: <span style="color: rgb(39, 171, 156);">{{item.name}}</span></span><br>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">ชนิดสินค้า: <span style="color: rgb(39, 171, 156);">{{item.attribute_priority_1}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">ลักษณะสินค้า: <span style="color: rgb(39, 171, 156);">{{item.attribute_priority_2}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">ราคาสินค้า: <span style="color: rgb(39, 171, 156);">{{item.product_price}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">จำนวน: <span style="color: rgb(39, 171, 156);">{{item.quantitiy}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">เปอร์เซ็นค่าคอมมิชชัน(%): <span style="color: rgb(39, 171, 156);">{{item.commission_rate}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">ค่าคอมมิชชัน(บาท): <span style="color: rgb(39, 171, 156);">{{item.commission_received}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">หักภาษี(ภาษี): <span style="color: rgb(39, 171, 156);">{{item.influencer_tax}}</span></span>
                      </v-col>
                      <v-col cols="6" sm="12" class="py-1">
                        <span style="font-size: 16px;">ค่าคอมมิชชันที่ได้(บาท): <span style="color: rgb(39, 171, 156);">{{item.influencer_payment}}</span></span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" sm="7" v-if="MobileSize">
                    <v-row class="mx-5">
                      <v-col class="d-flex align-center py-1 mt-3" cols="12" sm="12">
                        <span style="font-size: 16px;">ชื่อสินค้า: <span style="color: rgb(39, 171, 156);">{{item.name}}</span></span><br>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">ชนิดสินค้า: <span style="color: rgb(39, 171, 156);">{{item.attribute_priority_1}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">ลักษณะสินค้า: <span style="color: rgb(39, 171, 156);">{{item.attribute_priority_2}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">ราคาสินค้า: <span style="color: rgb(39, 171, 156);">{{item.product_price}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">จำนวน: <span style="color: rgb(39, 171, 156);">{{item.quantitiy}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">เปอร์เซ็นค่าคอมมิชชัน(%): <span style="color: rgb(39, 171, 156);">{{item.commission_rate}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">ค่าคอมมิชชัน(บาท): <span style="color: rgb(39, 171, 156);">{{item.commission_received}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1">
                        <span style="font-size: 16px;">หักภาษี(ภาษี): <span style="color: rgb(39, 171, 156);">{{item.influencer_tax}}</span></span>
                      </v-col>
                      <v-col cols="12" sm="12" class="py-1 mb-5">
                        <span style="font-size: 16px;">ค่าคอมมิชชันที่ได้(บาท): <span style="color: rgb(39, 171, 156);">{{item.influencer_payment}}</span></span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import VueApexCharts from 'vue-apexcharts'
import axios from 'axios'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      filterType: '',
      headers: [
        { text: 'รายการแชร์ที่', value: 'item' },
        { text: 'รหัสผู้แชร์', value: 'user_id' },
        { text: 'ชื่อผู้แชร์', value: 'username' },
        { text: 'จำนวนการกดลิงค์', value: 'times' },
        { text: 'วันที่', value: 'datetime' }
      ],
      affiliateTableheaders: [
        { text: 'รายการแชร์ที่', value: 'affiliate' },
        { text: 'รหัสสินค้า', value: 'affiliate' },
        { text: 'ชื่อสินค้า', width: '130', value: 'productname' },
        { text: 'จำนวนลิงค์ที่แชร์ (ครั้ง)', value: 'cancle' },
        { text: 'จำนวนการกดลิงก์ (ครั้ง)', value: 'clickstime' },
        { text: 'ค่าคอมมิชชัน (บาท)', value: 'commition' },
        { text: 'ยอดขายทั้งหมด (บาท)', value: 'price' },
        { text: 'จำนวนยอดขายทั้งหมด (ชิ้น)', value: 'totalprice' },
        { text: 'รายละเอียด', value: 'actions', align: 'center' }
      ],
      tabledata: [
        { item: '1', username: 'Voke', user_id: '1234', times: 1, datetime: new Date('2015-03-25') }
      ],
      series: [{
        name: 'จำนวน',
        data: []
      }],
      bestShared: [
        { username: 'Bank', times: 50 },
        { username: 'Bank', times: 45 },
        { username: 'Bank', times: 32 },
        { username: 'Bank', times: 12 },
        { username: 'Bank', times: 12 },
        { username: 'Bank', times: 10 },
        { username: 'Bank', times: 10 },
        { username: 'Bank', times: 5 },
        { username: 'Bank', times: 5 },
        { username: 'Bank', times: 5 }
      ],
      bestSold: [],
      salesData: [
        { title: 'จำนวนการคลิกลิงค์ทั้งหมด', value: 50, type: 'ครั้ง' },
        { title: 'จำนวนการซื้อสินค้าผ่านลิงค์', value: 50, type: 'ครั้ง' },
        { title: 'จำนวนยอดขายสินค้าทั้งหมด', value: 50, type: 'ชิ้น' },
        { title: 'จำนวนสินค้าที่ถูกยกเลิก', value: 50, type: 'ชิ้น' },
        { title: 'จำนวนเงินที่ได้', value: 50, type: 'บาท' },
        { title: 'จำนวนค่าคอมมิชชั่นสำหรับขายสินค้า', value: 50, type: 'บาท' }
      ],
      usertableheaders: [
        { text: 'ลำดับที่', value: 'id' },
        { text: 'ชื่อผู้ใช้', value: 'username' },
        { text: 'จำนวนการคลิก (ครั้ง)', value: 'affiliatelink_create' },
        { text: 'จำนวนที่ขายได้ (บาท)', value: 'total_success' },
        { text: 'ค่าคอมมิสชันที่ได้ (บาท)', value: 'commition' },
        { text: 'รายละเอียด', value: 'useractions', align: 'center' }
      ],
      detailsMockupProductTable: [
        { text: 'จำนวนลิงก์ที่ถูกสร้างจากสินค้า', value: 80, type: 'ลิ้งค์' },
        { text: 'จำนวนการกดลิงก์', value: 500, type: 'ครั้ง' },
        { text: 'จำนวนยอดขายสินค้า', value: 50, type: 'ชิ้น' },
        { text: 'คิดเป็นค่าคอมมิสชันจำนวน', value: 5000, type: 'บาท' },
        { text: 'ทำยอดขายได้', value: 48000, type: 'บาท' }
      ],
      detailsMockupUsersTable: [
        { text: 'จำนวนลิงก์ที่ถูกสร้างจากสินค้า', value: 80, type: 'ลิ้งค์' },
        { text: 'จำนวนการกดลิงก์', value: 500, type: 'ครั้ง' },
        { text: 'จำนวนยอดขายสินค้า', value: 50, type: 'ชิ้น' },
        { text: 'คิดเป็นค่าคอมมิสชันจำนวน', value: 5000, type: 'บาท' },
        { text: 'ทำยอดขายได้', value: 48000, type: 'บาท' }
      ],
      testpagination: [
        { text: 'id', value: 'id' },
        { text: 'จำนวน', value: 'amount' }
      ],
      testpaginationdata: [],
      userdata: [],
      selectedDropdown: 'รายปี',
      selectedYear: new Date().getFullYear(),
      selectedMonth: null,
      monthName: null,
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31', // Set your maximum date here
      startDays: null,
      endDate: null,
      showYearDropdown: true,
      showMonthDropdown: false,
      showDatePicker: false,
      modalDateSelect: false,
      productOpen: false,
      usersOpen: false,
      dates: [],
      datePicker: [
        { start_date: null },
        { end_date: null }
      ],
      page: 1,
      pageCount: 20,
      pageTableItemsSizeProduct: [5, 10, 15],
      pageTableItemsProduct: [5, 10, 15],
      paginationParams: {
        page: 1,
        pageSize: 5
      },
      dialog: false,
      mobileFilterDialog: false,
      mobileTitles: [
        'ข้อมูลการขายสินค้าผ่านลิงค์ affiliate',
        'รายการสินค้าที่มีการ affiliate',
        'ตารางแสดงข้อมูลผู้ใช้งานลิงก์ affiliate',
        'ตารางรายงานการสั่งซื้อ',
        'ลิงค์สินค้าที่ถูกกดมากที่สุด',
        'รายการสินค้าที่ถูกขายมากที่สุด 10 อันดับ'
      ],
      // Products Table data
      productTableBody: {
        start: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        filter: 'year',
        role_user: 'admin',
        seller_shop_id: localStorage.getItem('shopSellerID'),
        pages: 1,
        count: 5,
        search: ''
      },
      productHeadersTable: [
        { text: 'รหัสสินค้า', value: 'product_code' },
        { text: 'ชื่อสินค้า', width: '200', value: 'name' },
        { text: 'ราคาสินค้า (บาท)', width: '150', align: 'center', value: 'product_range' },
        { text: 'จำนวนลิงค์ที่แชร์ (ครั้ง)', align: 'center', value: 'link_created' },
        { text: 'จำนวนการกดลิงก์ (ครั้ง)', align: 'center', value: 'total_click' },
        { text: 'จำนวนยอดขายทั้งหมด (ชิ้น)', align: 'center', value: 'total_quantity' },
        { text: 'ยอดขายทั้งหมด (บาท)', align: 'center', value: 'sales_success' },
        { text: 'เปอร์เซ็นค่าคอมมิชชัน', align: 'center', value: 'commission_rate' },
        { text: 'รายละเอียด', value: 'actions', align: 'center' }
      ],
      productTable: [],
      rowPerPagesProductsTable: 5,
      productSearchTable: '',
      detailDialogProductData: [],
      // Users Table data
      usersTableBody: {
        start: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        filter: 'month',
        role_user: 'ext_buyer',
        seller_shop_id: localStorage.getItem('shopSellerID'),
        pages: 1,
        count: 5,
        search: ''
      },
      usersTable: [],
      userstableheaders: [
        { text: 'ชื่อผู้ใช้', value: 'name' },
        { text: 'จำนวนการคลิก (ครั้ง)', align: 'center', value: 'total_click_all' },
        { text: 'จำนวนที่ขายได้ (บาท)', align: 'center', value: 'total_sales' },
        { text: 'ค่าคอมมิสชันที่ได้ (บาท)', align: 'center', value: 'commission_received' },
        { text: 'รายละเอียด', value: 'useractions', align: 'center' }
      ],
      rowPerPagesUsersTable: 5,
      userSearchTable: '',
      pageTableItemsSizeUsers: [5, 10, 15],
      mobileTitlesSelected: 'ข้อมูลการขายสินค้าผ่านลิงค์ affiliate',
      detailDialogData: {},
      // seller report
      sellerReportTableBody: {
        start: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        filter: 'month',
        role_user: 'ext_buyer',
        seller_shop_id: localStorage.getItem('shopSellerID'),
        pages: 1,
        count: 5,
        search: ''
      },
      sellerReporttableheaders: [
        { text: 'วันที่สั่งซื้อสินค้า', value: 'order_row.date' },
        { text: 'รหัสการสั่งซื้อสินค้า', align: 'center', value: 'order_row.order_number' },
        { text: 'ค่าคอมมิสชันทั้งหมด (บาท)', align: 'center', value: 'order_row.total_commission' },
        { text: 'รายละเอียด', value: 'actions', align: 'center' }
      ],
      sellerReportTable: [],
      rowPerPagesSellerReportTable: 5,
      sellerReportSearchTable: '',
      sellerReportDialog: false,
      detailDialogSellerReportData: [],
      // chart data
      chartDataBody: {
        start: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        filter: 'year',
        role_user: 'admin',
        seller_shop_id: localStorage.getItem('shopSellerID')
      },
      chartsData: [{
        name: 'จำนวน',
        data: [10, 15, 50, 54, 55, 10]
      }],
      tableStatus: false,
      pageEnd: null,
      years: [2019, 2020, 2021, 2023, 2024],
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      monthsMobileSelector: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      dateToDate: [],
      monthDate: [],
      exportExcelBody: {
        start: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        role_user: 'ext_buyer',
        exportdashboard: '',
        seller_shop_id: localStorage.getItem('shopSellerID'),
        count: '-1',
        pages: '1',
        search: '',
        transaction_status: 'Success'
      },
      total_price: '',
      total_quantity: 0,
      total_commission_paid: '',
      chartSelected: 'commission',
      topTenSoldBody: {
        start: this.dateFormat(new Date(new Date(new Date().getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        end: this.dateFormat(new Date(new Date(new Date().getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd'),
        role_user: 'ext_buyer',
        filter: 'year',
        seller_shop_id: localStorage.getItem('shopSellerID')
      },
      chartsTabData: [
        { key: 0, text: 'commission' },
        { key: 1, text: 'price' },
        { key: 2, text: 'piece' }
      ],
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      boxIconPath: require('@/assets/icons/SellerDashboard/box 1.png'),
      dataModelIconPath: require('@/assets/icons/SellerDashboard/data-model 1.png'),
      uniqueIconPath: require('@/assets/icons/SellerDashboard/unique 1.png'),
      goldMedalIconPath: require('@/assets/icons/SellerDashboard/gold-medal.png'),
      silverMedalIconPath: require('@/assets/icons/SellerDashboard/silver-medal.png'),
      bronzeMedalIconPath: require('@/assets/icons/SellerDashboard/bronze-medal.png'),
      moneyIconPath: require('@/assets/icons/SellerDashboard/money (1) 1.png'),
      commissionicon: require('@/assets/icons/commission.png'),
      totalprice: require('@/assets/icons/totalPrice.png')
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.generateYears()
    this.getUsersTable()
    this.getProductsTable()
    this.getUsersTable()
    this.getTopTenProduct()
    this.getChartsAffiliateDashboard()
    this.getTopTenSoldProduct()
    this.getSellerReportTable()
    // this.formatCurrentDate()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    daterange () {
      if (this.dates.length > 1) {
        var startDate = new Date(this.dates[0]).toLocaleDateString('th-TH')
        var endDate = new Date(this.dates[1]).toLocaleDateString('th-TH')
        var dateLenght = startDate + ' - ' + endDate
        return dateLenght
      } else if (this.dates.length === 1) {
        var oneDay = new Date(this.dates[0]).toLocaleDateString('th-TH')
        return oneDay
      }
      return this.dates.join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    chartOptionsDay () {
      return {
        chart: {
          id: 'basic-line',
          toolbar: false,
          type: 'line'
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          categories: this.dateToDate.length === 0 ? ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'] : this.dateToDate
          // categories: this.dateToDate
        }
      }
    },
    chartOptionsMonth () {
      return {
        chart: {
          id: 'basic-line',
          toolbar: false,
          type: 'line'
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          categories: this.series.length === 0 ? ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'] : this.monthDate
        }
      }
    },
    chartOptions () {
      return {
        chart: {
          id: 'basic-line',
          toolbar: false
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          categories: ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        tooltip: {
          y: {
            formatter: function (value) {
              return value % 1 === 0 ? value.toFixed(0) : value.toFixed(2)
            }
          }
        }
      }
    }
  },
  methods: {
    async exportTransaction () {
      this.$store.commit('openLoader')
      var shopID = localStorage.getItem('shopSellerID')
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_transaction_shop/${shopID}`,
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'export_transaction_shop.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
        // console.log('fileLink ')
      })
    },
    async onYearSelected (year) {
      this.monthName = null
      this.selectedYear = year
      this.chartDataBody.filter = 'year'
      var start = this.selectedYear + '-01-01'
      var end = this.selectedYear + '-12-31'
      this.chartDataBody.start = start
      this.chartDataBody.end = end
      if (this.selectedDropdown === 'รายปี') {
        await this.getChartsAffiliateDashboard()
        await this.usePaginationUsers('dates')
        await this.usePaginationProduct('dates')
        await this.usePaginationSellerReport('dates')
        await this.setDateFilter()
        await this.getTopTenSoldProduct()
      }
      this.selectedMonth = null
    },
    async onYearSelectedMobile () {
      this.selectedMonth = null
      this.chartDataBody.filter = 'year'
      var start = this.selectedYear + '-01-01'
      var end = this.selectedYear + '-12-31'
      this.chartDataBody.start = start
      this.chartDataBody.end = end
      if (this.selectedDropdown === 'รายปี') {
        await this.getChartsAffiliateDashboard()
        await this.usePaginationUsers('dates')
        await this.usePaginationProduct('dates')
        await this.usePaginationSellerReport('dates')
        await this.setDateFilter()
        await this.getTopTenSoldProduct()
      }
      this.selectedMonth = null
    },
    async setFilter (filter) {
      this.selectedDropdown = filter
      if (filter === 'รายปี') {
        this.chartDataBody.filter = 'year'
        var start = this.selectedYear + '-01-01'
        var end = this.selectedYear + '-12-31'
        this.chartDataBody.start = start
        this.chartDataBody.end = end
      } else if (filter === 'รายเดือน') {
        var datenow = new Date()
        var year = datenow.getFullYear()
        this.selectedYear = year
        var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
        this.selectedMonth = month
        this.chartDataBody.filter = 'month'
        const thaiMonths = [
          'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
          'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
        ]
        const thaiDate = `${thaiMonths[month - 1]}`
        this.monthName = thaiDate
        start = this.selectedYear + '-' + month + '-01'
        end = this.selectedYear + '-' + month + '-31'
        this.chartDataBody.start = start
        this.chartDataBody.end = end
      } else if (filter === 'รายวัน') {
        this.showDatePicker = true
        this.showYearDropdown = false
        this.showMonthDropdown = false
        this.chartDataBody.filter = 'day'
        this.chartDataBody.start = null
        this.chartDataBody.end = null
        await this.formatCurrentDate()
      }
    },
    async onDropdownSelected (filter) {
      this.series = []
      this.selectedDropdown = filter
      this.selectedMonth = null
      this.monthName = null
      this.dates = []
      this.selectedYear = null
      if (this.selectedDropdown === 'รายปี') {
        this.showYearDropdown = true
        this.showDatePicker = false
        this.showMonthDropdown = false
        this.chartDataBody.filter = 'year'
        var start = this.selectedYear + '-01-01'
        var end = this.selectedYear + '-12-31'
        this.chartDataBody.start = start
        this.chartDataBody.end = end
        var datenowYear = new Date()
        var currentYear = datenowYear.getFullYear()
        await this.onYearSelected(currentYear)
        // this.getChartsAffiliateDashboard()
        // selectedYear
      } else if (this.selectedDropdown === 'รายเดือน') {
        this.showYearDropdown = true
        this.showMonthDropdown = true
        this.showDatePicker = false
        this.chartDataBody.filter = 'month'
        var datenow = new Date()
        var year = datenow.getFullYear()
        var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
        this.selectedYear = year
        this.selectedMonth = month
        const thaiMonths = [
          'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
          'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
        ]
        const thaiDate = `${thaiMonths[month - 1]}`
        this.monthName = thaiDate
        start = this.selectedYear + '-' + month + '-01'
        end = this.selectedYear + '-' + month + '-31'
        this.chartDataBody.start = start
        this.chartDataBody.end = end
        await this.onMonthSelected(month)
        // this.getChartsAffiliateDashboard()
      } else if (this.selectedDropdown === 'รายวัน') {
        this.showDatePicker = true
        this.showYearDropdown = false
        this.showMonthDropdown = false
        this.chartDataBody.filter = 'day'
        this.chartDataBody.start = null
        this.chartDataBody.end = null
        await this.formatCurrentDate()
        await this.saveDates(this.dates)
      }
    },
    async onMonthSelected (month) {
      this.$store.commit('openLoader')
      this.series = []
      for (var i = 0; i < this.monthsMobileSelector.length; i++) {
        if (month === this.monthsMobileSelector[i]) {
          month = (i + 1).toString().padStart(2, '0')
        }
      }
      const thaiMonths = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const thaiDate = `${thaiMonths[month - 1]}`
      this.monthName = thaiDate
      this.selectedMonth = month
      this.chartDataBody.filter = 'month'
      var start = this.selectedYear + '-' + this.selectedMonth + '-01'
      var end = this.selectedYear + '-' + this.selectedMonth + '-31'
      this.chartDataBody.start = start
      this.chartDataBody.end = end
      this.usePaginationUsers('dates')
      this.usePaginationProduct('dates')
      this.usePaginationSellerReport('dates')
      this.setDateFilter()
      this.getTopTenSoldProduct()
      await this.$store.dispatch('actionChartsAffiliateDashboard', this.chartDataBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.dashboardAffiliateChart
      if (response.code === 500 && this.selectedMonth) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกปี'
        })
        this.$store.commit('closeLoader')
      } else if (response.code === 500 && this.selectedYear) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกเดือน'
        })
        this.$store.commit('closeLoader')
      } else if (response.code === 500 && !this.selectedYear && !this.selectedMonth) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกปีและเดือน'
        })
        this.$store.commit('closeLoader')
      } else if (response.ok === 'y') {
        this.dialog = false
      }
      this.chartOptionsMonth.xaxis.categories = []
      if (response.data.ok === 'y') {
        this.$store.commit('openLoader')
        var chartsdata = response.data.query_result.response
        this.total_commission_paid = response.data.query_result.total_commission_paid
        this.total_price = response.data.query_result.total_price
        this.total_quantity = response.data.query_result.total_quantity
        var newSeries = []
        this.monthDate = []
        this.chartOptionsDay.xaxis.categories = []
        for (i = 0; i < chartsdata.length; i++) {
          if (this.chartSelected === 'commission') {
            newSeries.push(parseFloat(chartsdata[i].revenue.commission_paid).toFixed(2))
            this.series = [{ name: 'ค่าคอมมิสชัน', data: newSeries }]
          } else if (this.chartSelected === 'price') {
            newSeries.push(parseFloat(chartsdata[i].revenue.price).toFixed(2))
            this.series = [{ name: 'ยอดขาย', data: newSeries }]
          } else if (this.chartSelected === 'piece') {
            newSeries.push(parseFloat(chartsdata[i].revenue.quantity).toFixed(2))
            this.series = [{ name: 'จำนวนที่ขายได้', data: newSeries }]
          }
          // newSeries.push(chartsdata[i].revenue.commission_paid)
          // this.series = [{ name: 'ค่าคอมมิสชัน', data: newSeries }]
          this.convertToThaiDate(chartsdata[i].date)
          this.$store.commit('closeLoader')
        }
      }
    },
    closeDateSelect () {
      this.modalDateSelect = false
      this.dates = []
      this.chartDataBody.start = null
      this.chartDataBody.end = null
    },
    closeSelectFilter () {
      this.mobileFilterDialog = false
    },
    async saveDates (val) {
      if (this.dates.length === 1) {
        this.chartDataBody.start = val[0]
        this.chartDataBody.end = val[0]
        this.usePaginationUsers('dates')
        this.usePaginationProduct('dates')
        this.usePaginationSellerReport('dates')
        this.setDateFilter()
        this.getTopTenSoldProduct()
      } else {
        this.$refs.modalDateSelect.save(val)
        await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.chartDataBody.start = val[0]
        this.chartDataBody.end = val[1]
        this.usePaginationUsers('dates')
        this.usePaginationProduct('dates')
        this.usePaginationSellerReport('dates')
        this.setDateFilter()
        this.getTopTenSoldProduct()
      }
      this.modalDateSelect = false
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionChartsAffiliateDashboard', this.chartDataBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.dashboardAffiliateChart
      if (response.data.ok === 'y') {
        var chartsdata = response.data.query_result.response
        this.total_commission_paid = response.data.query_result.total_commission_paid
        this.total_price = response.data.query_result.total_price
        this.total_quantity = response.data.query_result.total_quantity
        var newSeries = []
        this.dateToDate = []
        this.chartOptionsDay.xaxis.categories = []
        for (var i = 0; i < chartsdata.length; i++) {
          if (this.chartSelected === 'commission') {
            newSeries.push(parseFloat(chartsdata[i].revenue.commission_paid).toFixed(2))
            this.series = [{ name: 'ค่าคอมมิสชัน', data: newSeries }]
          } else if (this.chartSelected === 'price') {
            newSeries.push(parseFloat(chartsdata[i].revenue.price).toFixed(2))
            this.series = [{ name: 'ยอดขาย', data: newSeries }]
          } else if (this.chartSelected === 'piece') {
            newSeries.push(parseFloat(chartsdata[i].revenue.quantity).toFixed(2))
            this.series = [{ name: 'จำนวนที่ขายได้', data: newSeries }]
          }
          this.convertToThaiDate(chartsdata[i].date)
        }
        this.$store.commit('closeLoader')
      }
    },
    async saveDatesMobile (val) {
      if (this.dates.length === 1 || this.chartDataBody.start) {
        this.chartDataBody.start = val[0]
        this.chartDataBody.end = val[0]
        this.getTopTenSoldProduct()
        this.modalDateSelect = false
      } else if (this.dates.length === 0) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกวันที่'
        })
        this.dates = []
        this.chartDataBody.start = null
        this.chartDataBody.end = null
        this.series = []
        this.chartOptionsDay.xaxis.categories = []
      } else {
        this.$refs.modalDateSelect.save(val)
        await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.chartDataBody.start = val[0]
        this.chartDataBody.end = val[1]
      }
    },
    async changeMenu () {
      if (this.selectedDropdown === 'รายวัน') {
        this.getchartsDaysMobile()
      } else if (this.selectedDropdown === 'รายเดือน') {
        if (this.selectedMonth !== null || this.selectedYear !== null) {
          this.chartOptionsMonth.xaxis.categories = []
          this.series = []
          // this.monthDate = []
          await this.onMonthSelected(this.selectedMonth)
        } else {
          this.chartOptionsMonth.xaxis.categories = []
          this.series = []
          await this.onMonthSelected(this.selectedMonth)
        }
      } else if (this.selectedDropdown === 'รายปี') {
        this.onYearSelectedMobile()
      }
    },
    async getchartsDaysMobile () {
      this.$store.commit('openLoader')
      if (this.chartDataBody.start === null) {
        this.$store.commit('closeLoader')
      }
      await this.$store.dispatch('actionChartsAffiliateDashboard', this.chartDataBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.dashboardAffiliateChart
      if (response.data.ok === 'y') {
        var chartsdata = response.data.query_result.response
        this.total_commission_paid = response.data.query_result.total_commission_paid
        this.total_price = response.data.query_result.total_price
        this.total_quantity = response.data.query_result.total_quantity
        var newSeries = []
        this.dateToDate = []
        this.chartOptionsDay.xaxis.categories = []
        for (var i = 0; i < chartsdata.length; i++) {
          if (this.chartSelected === 'commission') {
            newSeries.push(parseFloat(chartsdata[i].revenue.commission_paid).toFixed(2))
            this.series = [{ name: 'ค่าคอมมิสชัน', data: newSeries }]
          } else if (this.chartSelected === 'price') {
            newSeries.push(parseFloat(chartsdata[i].revenue.price).toFixed(2))
            this.series = [{ name: 'ยอดขาย', data: newSeries }]
          } else if (this.chartSelected === 'piece') {
            newSeries.push(parseFloat(chartsdata[i].revenue.quantity).toFixed(2))
            this.series = [{ name: 'จำนวนที่ขายได้', data: newSeries }]
          }
          this.convertToThaiDate(chartsdata[i].date)
        }
        this.$store.commit('closeLoader')
      }
    },
    async convertToThaiDate (dateString) {
      const [year, month, day] = dateString.split('-').map(Number)
      const thaiYear = year + 543
      const thaiMonths = [
        'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
        'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
      ]
      const thaiDate = `${day} ${thaiMonths[month - 1]} ${thaiYear}`
      this.dateToDate.push(thaiDate)
      this.monthDate.push(thaiDate)
    },
    async getTransactions () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionDashboardAffiliateGraph', this.selectedYear)
      await this.$store.state.ModuleDashboardAffiliateShop.transactions
      this.$store.commit('closeLoader')
    },
    async openProductDialog (productId) {
      this.productOpen = true
    },
    async openUsersDialog (userId) {
      this.usersOpen = true
    },
    async openSellerReportDialog (userId) {
      this.sellerReportDialog = true
    },
    async usePaginationProduct (option) {
      this.$store.commit('openLoader')
      if (option === 'next') {
        this.productTableBody.pages = this.productTable.current_page + 1
      } else if (option === 'previous') {
        this.productTableBody.pages = this.productTable.current_page - 1
      } else if (option === 'pageSize') {
        this.productTableBody.pages = 1
        this.productTableBody.count = this.rowPerPagesProductsTable
      } else if (option === 'filter') {
        this.productTableBody.pages = 1
        this.productTableBody.search = this.productSearchTable
      } else if (option === 'dates') {
        this.productTableBody.start = this.chartDataBody.start
        this.productTableBody.end = this.chartDataBody.end
      }
      await this.$store.dispatch('actionPaginationsProductsTable', this.productTableBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.paginationProductsTable
      this.productTable = response.data.query_result
      this.$store.commit('closeLoader')
    },
    async usePaginationUsers (option) {
      this.$store.commit('openLoader')
      if (option === 'next') {
        this.usersTableBody.pages = this.usersTable.current_page + 1
      } else if (option === 'previous') {
        this.usersTableBody.pages = this.usersTable.current_page - 1
      } else if (option === 'pageSize') {
        this.usersTableBody.pages = 1
        this.usersTableBody.count = this.rowPerPagesUsersTable
      } else if (option === 'filter') {
        this.usersTableBody.pages = 1
        this.usersTableBody.search = this.userSearchTable
      } else if (option === 'dates') {
        this.usersTableBody.start = this.chartDataBody.start
        this.usersTableBody.end = this.chartDataBody.end
      }
      await this.$store.dispatch('actionPaginationsUsersTable', this.usersTableBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.paginationUsersTable
      this.usersTable = response.data.query_result
      this.$store.commit('closeLoader')
    },
    async usePaginationSellerReport (option) {
      if (option === 'next') {
        this.sellerReportTableBody.pages = this.sellerReportTable.current_page + 1
      } else if (option === 'previous') {
        this.sellerReportTableBody.pages = this.sellerReportTable.current_page - 1
      } else if (option === 'pageSize') {
        this.sellerReportTable.pages = 1
        this.sellerReportTableBody.count = this.rowPerPagesSellerReportTable
      } else if (option === 'filter') {
        this.sellerReportTableBody.pages = 1
        this.sellerReportTableBody.search = this.sellerReportSearchTable
      } else if (option === 'dates') {
        this.sellerReportTableBody.start = this.chartDataBody.start
        this.sellerReportTableBody.end = this.chartDataBody.end
      }
      this.getSellerReportTable()
    },
    async submitFilterDialog () {
      this.dialog = false
      this.mobileTitlesSelected = 'ข้อมูลการขายสินค้าผ่านลิงค์ affiliate'
      if (this.selectedDropdown === 'รายปี') {
        this.chartOptions.xaxis.categories = []
        this.onYearSelectedMobile()
      } else if (this.selectedDropdown === 'รายเดือน') {
        this.chartOptionsMonth = []
        this.monthDate = []
        await this.onMonthSelected(this.selectedMonth)
      } else if (this.selectedDropdown === 'รายวัน') {
        if (this.chartDataBody.start !== null) {
          this.chartOptionsDay.xaxis.categories = []
          await this.getchartsDaysMobile()
          this.usePaginationUsers('dates')
          this.usePaginationProduct('dates')
          this.usePaginationSellerReport('dates')
          this.setDateFilter()
          this.getTopTenSoldProduct()
          this.dialog = false
        } else if (this.chartDataBody.start === null || this.dates.length === 0) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกวันที่'
          })
          this.chartOptionsDay.xaxis.categories = []
          this.dates = []
          this.chartDataBody.start = null
          this.chartDataBody.end = null
          this.series = []
        }
      }
    },
    async clearFilterDialog () {
      this.selectedMonth = null
      this.selectedYear = null
      this.dates = []
      this.chartDataBody.start = null
      this.chartDataBody.end = null
      // this.topTenSoldBody.start = null
      // this.topTenSoldBody.end = null
    },
    backtoPage () {
      this.$router.push({ path: '/sellerMobile' }).catch(() => {})
    },
    async getProductsTable () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionPaginationsProductsTable', this.productTableBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.paginationProductsTable
      this.productTable = response.data.query_result
      this.$store.commit('closeLoader')
    },
    async getUsersTable () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionPaginationsUsersTable', this.usersTableBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.paginationUsersTable
      this.usersTable = response.data.query_result
      this.$store.commit('closeLoader')
    },
    async getSellerReportTable () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionSellerReport', this.sellerReportTableBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.sellerReport
      this.sellerReportTable = response.data.query_result
      this.$store.commit('closeLoader')
    },
    async getTopTenProduct () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionTopTenMostSoldPropuct', this.topTenSoldBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.topTenProduct
      this.bestShared = response.data.query_result.topclicked
      this.$store.commit('closeLoader')
    },
    async getTopTenSoldProduct () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionToptenSoldProduct', this.topTenSoldBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.topTenSoldsProduct
      this.bestSold = response.data.query_result.topclicked
      this.$store.commit('closeLoader')
    },
    async getChartsAffiliateDashboard () {
      this.$store.commit('openLoader')
      this.series = []
      await this.$store.dispatch('actionChartsAffiliateDashboard', this.chartDataBody)
      var response = await this.$store.state.ModuleDashboardAffiliateShop.dashboardAffiliateChart
      if (response.code === 500) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'กรุณาเลือกปี'
        })
        this.$store.commit('closeLoader')
      }
      if (response.data.ok === 'y') {
        this.chartsData = response.data.query_result.response
        this.total_commission_paid = response.data.query_result.total_commission_paid
        this.total_price = response.data.query_result.total_price
        this.total_quantity = response.data.query_result.total_quantity
        var newSeries = []
        this.dateToDate = []
        for (let i = 0; i < this.chartsData.length; i++) {
          if (this.chartSelected === 'commission') {
            newSeries.push(parseFloat(this.chartsData[i].revenue.commission_paid).toFixed(2))
          } else if (this.chartSelected === 'price') {
            newSeries.push(parseFloat(this.chartsData[i].revenue.price).toFixed(2))
          } else if (this.chartSelected === 'piece') {
            newSeries.push(this.chartsData[i].revenue.quantity)
          }
          // this.convertToThaiDate(chartsdata[i].date)
          // newSeries.push(parseFloat(this.chartsData[i].revenue.commission_paid).toFixed(2))
        }
        if (this.chartSelected === 'commission') {
          this.series = [{ name: 'ค่าคอมมิสชัน', data: newSeries }]
        } else if (this.chartSelected === 'price') {
          this.series = [{ name: 'ยอดขาย', data: newSeries }]
        } else if (this.chartSelected === 'piece') {
          this.series = [{ name: 'จำนวนชิ้น', data: newSeries }]
        }
        // this.series = [{ name: 'ค่าคอมมิสชัน', data: newSeries }]
        this.$store.commit('closeLoader')
      } else {
        if (response.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.series = []
        }
        this.$store.commit('closeLoader')
      }
    },
    async showUsersDetails (val, index) {
      this.detailDialogData = this.usersTable.usersList[index.index]
    },
    async showProductsDetails (val, index) {
      this.detailDialogProductData = this.productTable.productLists[index.index].attribute_list
    },
    async showSellerReportDetails (val, index) {
      this.detailDialogSellerReportData = this.sellerReportTable.response[index.index].order_details
    },
    dateFormat (inputDate, format) {
      const date = new Date(inputDate)
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      format = format.replace('MM', month.toString().padStart(2, '0'))
      if (format.indexOf('yyyy') > -1) {
        format = format.replace('yyyy', year.toString())
      } else if (format.indexOf('yy') > -1) {
        format = format.replace('yy', year.toString().substr(2, 2))
      }
      format = format.replace('dd', day.toString().padStart(2, '0'))
      return format
    },
    async getReportExcel (val) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.exportExcelBody.exportdashboard = val
      this.exportExcelBody.start = this.chartDataBody.start
      this.exportExcelBody.end = this.chartDataBody.end
      if (val === 'productlist_seller') {
        this.exportExcelBody.search = this.productTableBody.search
      } else if (val === 'userlist_seller') {
        this.exportExcelBody.search = this.usersTableBody.search
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}affiliate_dashboard/exportAffiliateDashboard`,
        data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        if (val === 'productlist_seller') {
          fileLink.setAttribute('download', 'productlist.xlsx')
        } else if (val === 'userlist_seller') {
          fileLink.setAttribute('download', 'userslist.xlsx')
        } else if (val === 'topten_seller') {
          fileLink.setAttribute('download', 'ToptenClicklist.xlsx')
        } else if (val === 'toptensolds_seller') {
          fileLink.setAttribute('download', 'ToptenSoldslist.xlsx')
        } else if (val === 'report_seller') {
          fileLink.setAttribute('download', 'reportsellerlist.xlsx')
        }
        // fileLink.setAttribute('download', 'report.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async generateYears () {
      this.years = []
      const currentYear = new Date().getFullYear()
      const years = []
      for (let year = 2022; year <= currentYear; year++) {
        years.push(year)
      }
      this.years = years
    },
    async setDateFilter () {
      this.topTenSoldBody.start = this.chartDataBody.start
      this.topTenSoldBody.end = this.chartDataBody.end
      this.getTopTenSoldProduct()
      this.getTopTenProduct()
    },
    async onChartSelected (choice) {
      if (choice === 'commission') {
        this.chartSelected = 'commission'
        if (this.selectedYear && this.selectedMonth && this.selectedDropdown === 'รายเดือน') {
          await this.getChartsAffiliateDashboard()
        } else if (this.selectedYear && this.selectedDropdown === 'รายปี') {
          await this.getChartsAffiliateDashboard()
        } else if (this.chartDataBody.start && this.selectedDropdown === 'รายวัน') {
          var dateValue = []
          dateValue.push(this.chartDataBody.start)
          dateValue.push(this.chartDataBody.end)
          this.saveDates(dateValue)
        }
      } else if (choice === 'price') {
        this.chartSelected = 'price'
        if (this.selectedYear && this.selectedMonth && this.selectedDropdown === 'รายเดือน') {
          await this.getChartsAffiliateDashboard()
        } else if (this.selectedYear && this.selectedDropdown === 'รายปี') {
          await this.getChartsAffiliateDashboard()
        } else if (this.chartDataBody.start && this.selectedDropdown === 'รายวัน') {
          dateValue = []
          dateValue.push(this.chartDataBody.start)
          dateValue.push(this.chartDataBody.end)
          this.saveDates(dateValue)
        }
      } else if (choice === 'piece') {
        this.chartSelected = 'piece'
        if (this.selectedYear && this.selectedMonth && this.selectedDropdown === 'รายเดือน') {
          await this.getChartsAffiliateDashboard()
        } else if (this.selectedYear && this.selectedDropdown === 'รายปี') {
          await this.getChartsAffiliateDashboard()
        } else if (this.chartDataBody.start && this.selectedDropdown === 'รายวัน') {
          dateValue = []
          dateValue.push(this.chartDataBody.start)
          dateValue.push(this.chartDataBody.end)
          this.saveDates(dateValue)
        }
      }
    },
    async setChartsTypeMobile (value) {
      this.onChartSelected(this.chartsTabData[value].text)
    },
    async formatCurrentDate () {
      var datenow = new Date()
      var year = datenow.getFullYear()
      var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
      var day = datenow.getDate().toString().padStart(2, '0')
      var formattedDate = `${year}-${month}-${day}`
      this.dates = [formattedDate]
      this.chartDataBody.start = formattedDate
      this.chartDataBody.end = formattedDate
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DashboardShopAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/DashboardShopAffiliate' }).catch(() => {})
      }
    }
  }
}
</script>

<!-- <style>
  .text-12 {
    font-size: 12px;
  }

  .text-16 {
    font-size: 16px;
    color: #e0e0e0;
  }

  .text-16-color {
    font-size: 16px;
    color: #27ab9c;
  }

  .setboxShadow {
    border-radius: 4px;
    margin: 24px;
    overflow-y: auto;
    pointer-events: auto;
    transition: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    width: 100%;
    z-index: inherit;
    box-shadow: 0px !important;
  }
</style> -->

<style lang="scss" scoped>
::v-deep table {
  tbody {
    tr {
      td:nth-last-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
      }
    }
  }
  thead {
    tr {
      th:nth-last-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
      }
    }
  }
  thead {
    tr {
      th:nth-last-child(1) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
      }
    }
  }
}
</style>

<style scoped>
  >>> .v-dialog {
    overflow-y: hidden !important;
  }
  .text-12 {
    font-size: 12px;
  }

  .text-16 {
    font-size: 16px;
    color: #e0e0e0;
  }

  .text-16-color {
    font-size: 16px;
    color: #27ab9c;
  }

  .setboxShadow {
    border-radius: 4px;
    margin: 24px;
    overflow-y: auto;
    pointer-events: auto;
    transition: 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    width: 100%;
    z-index: inherit;
    box-shadow: 0px !important;
  }
</style>
