<template>
  <v-container :class="MobileSize ? 'mt-3' : 'pa-0'">
    <v-img
      src="@/assets/bannerOTOP.png"
      contain
      v-if="!MobileSize && !IpadSize"
    ></v-img>
    <v-card width="100%" height="100%" style="background: #FFFFFF; !important; margin-top: -.8vw !important;" :style="MobileSize ? 'z-index: 99' : ''" elevation="0" class="mx-0 my-0 pa-2">
      <v-row v-if="!MobileSize">
        <v-col class="d-flex" cols="12">
          <v-dialog
            ref="dialogStartDate"
            v-model="dialogStartDate"
            width="290px"
          >
            <template v-slot:activator="{ on, attrs }">
              <!-- <v-select
                v-model="sentStartDate"
                v-bind="attrs"
                placeholder="วันที่"
                outlined
                dense
                v-on="on"
                class="customSelect mr-1"
                hide-details
                rounded
              >
              </v-select> -->
              <v-text-field
                v-model="sentStartDate"
                v-bind="attrs"
                v-on="on"
                hide-details
                outlined
                dense
                rounded
                placeholder="วันที่"
                class="customSelect mr-1"
                readonly
              >
              </v-text-field>
            </template>
            <v-card>
              <v-card-title>
                <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันที่</span>
                <v-spacer></v-spacer>
                <v-btn text @click="cancelSelectDate" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
              </v-card-title>
              <v-date-picker
                v-model="date"
                range
                locale="TH-th"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
              >
                <v-btn
                  text
                  color="primary"
                  @click="cancelPickDate"
                >
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="clearSelectDate"
                >
                  ล้างค่า
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  :disabled="date.length === 0"
                  @click="changeformatdate"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-card>
          </v-dialog>
          <v-autocomplete
            v-model="selectsector"
            :items="sector"
            dense
            outlined
            placeholder="ภาค"
            class="customSelect mr-1"
            no-data-text="ภาคที่ค้นหา"
            rounded
            hide-details
            item-text="name"
            item-value="id"
          ></v-autocomplete>
          <v-autocomplete
            v-model="selectProvince"
            :items="province"
            dense
            outlined
            placeholder="จังหวัด"
            class="customSelect mr-1"
            no-data-text="ไม่พบจังหวัดที่ค้นหา"
            rounded
            hide-details
            item-text="name_th"
            item-value="id"
          ></v-autocomplete>
          <v-autocomplete
            v-model="selectedProductType"
            :items="productType"
            dense
            outlined
            placeholder="ประเภทสินค้า"
            class="customSelect mr-1"
            no-data-text="ไม่พบประเภทสินค้าที่ค้นหา"
            rounded
            hide-details
            item-text="type"
            item-value="id"
          ></v-autocomplete>
        </v-col>
        <v-col class="d-flex" cols="12">
          <v-row class="pa-0">
            <v-col cols="12" class="d-flex">
              <v-autocomplete
                v-model="selectedShop"
                :items="shopList"
                dense
                outlined
                placeholder="ร้านค้า"
                class="customSelect mr-1"
                no-data-text="ไม่พบร้านค้าที่ค้นหา"
                rounded
                hide-details
                item-text="shop_name_th"
                item-value="id"
              ></v-autocomplete>
              <v-select
                v-model="selectedOTOPType"
                :items="OTOPTypeItem"
                placeholder="ประเภท OTOP"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                rounded
              >
              </v-select>
              <v-btn color="#9dd4ea" class="mx-2" style="width: 100px;" @click="confirmFilter" rounded>ค้นหา</v-btn>
              <v-btn color="#c6c6c6" style="width: 100px;" rounded @click="clearFilter">ล้างค่า</v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col class="d-flex">
          <v-icon v-if="MobileSize" @click="backtoSellerMenu()" color="#27AB9C" size="30">mdi-chevron-left</v-icon>
          <v-spacer></v-spacer>
          <v-btn color="primary" rounded outlined @click="openFilterDialog">ตัวกรอง</v-btn>
        </v-col>
        <v-dialog
          v-model="filterMobileDialog"
        >
          <v-card>
            <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
              <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
                ตัวกรอง
              </span>
              <v-btn icon dark @click="closeFilterDialog">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text class="mt-5">
              <v-row>
                <v-col cols="12" class="pa-3">
                  <v-dialog
                    ref="dialogStartDate"
                    v-model="dialogStartDate"
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="sentStartDate"
                        v-bind="attrs"
                        v-on="on"
                        hide-details
                        outlined
                        dense
                        placeholder="วันที่"
                        class="customSelect mr-1"
                      >
                      </v-text-field>
                    </template>
                    <v-card>
                    <v-card-title>
                      <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันที่</span>
                      <v-spacer></v-spacer>
                      <v-btn text @click="cancelSelectDate" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                    </v-card-title>
                    <v-date-picker
                        v-model="date"
                        range
                        locale="TH-th"
                        no-title
                        :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      >
                        <v-btn
                          text
                          color="primary"
                          @click="cancelPickDate"
                        >
                          ยกเลิก
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                          text
                          color="primary"
                          @click="clearSelectDate"
                        >
                          ล้างค่า
                        </v-btn>
                        <v-btn
                          text
                          color="primary"
                          :disabled="date.length === 0"
                          @click="changeformatdate"
                        >
                          ตกลง
                        </v-btn>
                      </v-date-picker>
                    </v-card>
                  </v-dialog>
                </v-col>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="selectsector"
                    :items="sector"
                    dense
                    outlined
                    placeholder="ภาค"
                    class="customSelect mr-1"
                    no-data-text="ภาคที่ค้นหา"
                    hide-details
                    item-text="name"
                    item-value="id"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="selectProvince"
                    :items="province"
                    dense
                    outlined
                    placeholder="จังหวัด"
                    class="customSelect mr-1"
                    no-data-text="ไม่พบจังหวัดที่ค้นหา"
                    hide-details
                    item-text="name_th"
                    item-value="id"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="selectedProductType"
                    :items="productType"
                    dense
                    outlined
                    placeholder="ประเภทสินค้า"
                    class="customSelect mr-1"
                    no-data-text="ไม่พบประเภทสินค้าที่ค้นหา"
                    hide-details
                    item-text="type"
                    item-value="id"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-autocomplete
                    v-model="selectedShop"
                    :items="shopList"
                    dense
                    outlined
                    placeholder="ร้านค้า"
                    class="customSelect mr-1"
                    no-data-text="ไม่พบร้านค้าที่ค้นหา"
                    hide-details
                    item-text="shop_name_th"
                    item-value="id"
                  ></v-autocomplete>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="selectedOTOPType"
                    :items="OTOPTypeItem"
                    placeholder="ประเภท OTOP"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                  >
                  </v-select>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="6">
                  <v-btn block color="#2faea0" outlined @click="clearFilter">ล้างค่า</v-btn>
                </v-col>
                <v-col cols="6">
                  <v-btn block color="#2faea0" class="white--text" @click="confirmFilter">ค้นหา</v-btn>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-dialog>
      </v-row>
      <v-row class="d-flex ma-1 mt-4" v-if="!MobileSize && !IpadProSize && !IpadSize">
        <v-col cols="6" class="pa-0" style="box-shadow: rgba(99, 99, 99, 0.2) 0px 0px 7px -1.5px; border-radius: 7px;">
          <div
            class="coupon-container"
          >
            <img
              :src="require('@/assets/bgOTOP.png')"
              class="coupon-image"
              :style="MobileSize ? '' : 'height: 15vw;'"
            >
            <div class="coupon-content" style="top: 0; left: 0; padding: 2vw;">
              <div class="d-flex pa-1" style="font-size: x-large">
                <span><b>ยอดสะสมรวม (บาท)</b></span>
                <v-spacer></v-spacer>
                <span><b>{{ totalSales }}</b></span>
              </div>
              <v-divider style="border: .5px dashed #a0a0a0 !important;"></v-divider>
              <div class="d-flex align-center" style="gap: 1vw">
                <img src="@/assets/ICON/iconOTOP1.png" alt="" height="100" width="100">
                <div class="d-flex flex-column align-center">
                  <span><b>รับหน้าร้าน</b></span>
                  <span style="color: #64BD34; font-size: x-large;"><b>{{ totalReceive }}</b></span>
                  <span><b>หน้าร้านสะสม (บาท)</b></span>
                  <span style="font-size: x-small;"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedReceive }}</span></b></span>
                </div>
                <v-divider style="border: .5px dashed #a0a0a0 !important;" vertical></v-divider>
                <div class="d-flex flex-column align-center">
                  <span><b>ซื้อออนไลน์</b></span>
                  <span style="color: #64BD34; font-size: x-large"><b>{{ totalOnline }}</b></span>
                  <span><b>ออนไลน์สะสม (บาท)</b></span>
                  <span style="font-size: x-small;"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedOnline }}</span></b></span>
                </div>
              </div>
            </div>
          </div>
          <v-card class="mx-5 pa-2" style="margin-top: -1vw; !important;">
            <div class="d-flex align-center pa-3">
              <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
              <div>
                <span style="font-size: large;"><b>Ranking Top 10</b></span><br>
                <span>จัดอันดับจังหวัดที่มียอดขายสูงที่สุด</span>
              </div>
            </div>
            <div>
              <v-data-table
                :headers="headers"
                :items="rankingProvince"
                :items-per-page="10"
                class="elevation-1"
                item-key="i"
                no-data-text="ไม่มีรายการอันดับยอดขายของจังหวัด"
                no-results-text="ไม่พบรายการอันดับยอดขายของจังหวัด"
                style="border: 0 !important;"
                hide-default-footer
                >
                  <template v-slot:[`item.indexOfUser`]="{ item }">
                    <img v-if="item.indexOfUser === 1" src="@/assets/ICON/iconFirst.png" alt="">
                    <img v-else-if="item.indexOfUser === 2" src="@/assets/ICON/iconSecond.png" alt="">
                    <img v-else-if="item.indexOfUser === 3" src="@/assets/ICON/iconThird.png" alt="">
                    <span v-else>{{ item.indexOfUser }}</span>
                  </template>
                  <template v-slot:[`item.total_revenue_all`]="{ item }">
                    <span>{{ item.total_revenue_all.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
                  <template v-slot:[`item.total_revenue_front`]="{ item }">
                    <span>{{ item.total_revenue_front.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
                  <template v-slot:[`item.total_revenue_online`]="{ item }">
                    <span>{{ item.total_revenue_online.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
              </v-data-table>
            </div>
          </v-card>
        </v-col>
        <v-col cols="6" style="box-shadow: rgba(99, 99, 99, 0.2) 0px 0px 7px -1.5px; border-radius: 7px;">
          <v-card class="pa-3" outlined style="border: 0">
            <div>
              <div class="d-flex align-center pa-3">
                <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
                <span style="font-size: large;"><b>ยอดขายประเภทสินค้า 5 ประเภท (ร้อยละ)</b></span>
              </div>
              <div class="d-flex justify-center">
                <apexchart
                  type="donut"
                  :series="dataDonut.series"
                  :options="dataDonut.chartOptions"
                  height="280" width="280"
                >
                </apexchart>
              </div>
              <div style="font-size: smaller" class="d-flex justify-center">
                <span><v-icon small color="#c39bd3">mdi-circle</v-icon> {{ listTypeCategory[0] }}</span>
                <span><v-icon small color="#ec7063">mdi-circle</v-icon> {{ listTypeCategory[1] }}</span>
                <span><v-icon small color="#f8c471">mdi-circle</v-icon> {{ listTypeCategory[2] }}</span>
                <span><v-icon small color="#58d68d">mdi-circle</v-icon> {{ listTypeCategory[3] }}</span>
                <span><v-icon small color="#85c1e9">mdi-circle</v-icon> {{ listTypeCategory[4] }}</span>
              </div>
              <v-divider style="border: .5px dashed #a0a0a0 !important;" class="my-4"></v-divider>
              <div>
                <span><b>รายละเอียดยอดขายประเภทสินค้า 5 ประเภท</b></span>
                <div
                v-for="(item, index) in dataRank"
                :key="index"
                class="mb-4 mt-4"
                >
                <div class="d-flex">
                    <span class="mr-3"><b>{{ index+1 }}</b></span>
                    <span><b>{{ item.title }}</b></span>
                    <v-spacer></v-spacer>
                    <span><b>{{ item.price }} บาท</b></span>
                </div>
                <v-spacer></v-spacer>
                <div>
                    <div class="d-flex justify-space-between">
                        <div>หน้าร้าน : {{ item.rateOff }}%</div>
                        <div>Online : {{ item.rateOn }}%</div>
                    </div>
                    <div class="progress-wrapper" style="height: 20px; background-color: #e0e0e0; border-radius: 4px; overflow: hidden;">
                        <div
                        :style="{
                            width: item.rateOff + '%',
                            backgroundColor: '#6c8ef5',
                            height: '100%',
                            display: 'inline-block'
                        }"
                        ></div>
                        <div
                        :style="{
                            width: item.rateOn + '%',
                            backgroundColor: '#b6f3b0',
                            height: '100%',
                            display: 'inline-block'
                        }"
                        ></div>
                    </div>
                </div>
                </div>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="6" class="mt-4" style="box-shadow: rgba(99, 99, 99, 0.2) 0px 0px 7px -1.5px; border-radius: 7px;">
          <v-card class="pb-2" outlined style="border: 0">
            <div class="d-flex align-center pa-3">
              <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
              <div>
                <span style="font-size: large;"><b>ช่วงเวลาซื้อขาย</b></span><br>
                <span>ขนาดของวงกลมสะท้อนถึงยอดขายที่มากน้อย</span>
              </div>
            </div>
            <div class="d-flex justify-center" style="gap: .5vw;">
              <div>
                <div class="d-flex flex-column align-center">
                  <img src="@/assets/ICON/iconTimeReceive.png" width="120" height="120">
                  <span><b>รับหน้าร้าน</b></span>
                </div>
                <div>
                  <v-data-table
                    :headers="headersReceive"
                    :items="rankingTimeReceive"
                    :items-per-page="10"
                    class="elevation-1"
                    item-key="i"
                    no-data-text="ไม่มีรายการอันดับยอดขายของจังหวัด"
                    no-results-text="ไม่พบรายการอันดับยอดขายของจังหวัด"
                    style="border: 0 !important;"
                    hide-default-footer
                  >
                    <template v-slot:[`item.time`]="{ item }">
                      <span v-if="item.time === 'other'">ช่วงเวลาอื่น ๆ</span>
                      <span v-else>{{ item.time }}</span>
                    </template>
                    <template v-slot:[`item.revenue`]="{ item }">
                      <span>{{ item.revenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                  </v-data-table>
                </div>
              </div>
              <div>
                <div class="d-flex flex-column align-center">
                  <img src="@/assets/ICON/iconTimeOnline.png" width="120" height="120">
                  <span><b>ซื้อออนไลน์</b></span>
                </div>
                <div>
                  <v-data-table
                    :headers="headersReceive"
                    :items="rankingTimeOnline"
                    :items-per-page="10"
                    class="elevation-1"
                    item-key="i"
                    no-data-text="ไม่มีรายการอันดับยอดขายของจังหวัด"
                    no-results-text="ไม่พบรายการอันดับยอดขายของจังหวัด"
                    style="border: 0 !important;"
                    hide-default-footer
                  >
                    <template v-slot:[`item.time`]="{ item }">
                      <span v-if="item.time === 'other'">ช่วงเวลาอื่น ๆ</span>
                      <span v-else>{{ item.time }}</span>
                    </template>
                    <template v-slot:[`item.revenue`]="{ item }">
                      <span>{{ item.revenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                  </v-data-table>
                </div>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="6" class="mt-4" style="box-shadow: rgba(99, 99, 99, 0.2) 0px 0px 7px -1.5px; border-radius: 7px;">
          <v-card outlined style="border: 0">
            <div class="d-flex align-center pa-3">
              <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
              <div>
                <span style="font-size: large;"><b>Peak Time avg.</b></span><br>
                <span>แสดงช่วงเวลาใน 1 วันที่มีการซื้อสูงสุด</span>
              </div>
            </div>
            <div>
              <apexchart
                type="line"
                :series="dataLine.series"
                :options="dataLine.chartOptions"
                height="325" width="520"
              >
              </apexchart>
            </div>
           </v-card>
        </v-col>
        <!-- <v-col cols="6" class="d-flex flex-column">
          <v-card
            :style="{
              backgroundImage: 'url(@/assets/bgOTOP.png)',
              backgroundSize: 'cover',
              backgroundRepeat: 'no-repeat',
              backgroundPosition: 'center',
            }"
          >
            <v-col cols="12">
              <div class="d-flex" :style="MobileSize ? '' : 'font-size: x-large'">
                <span><b>ยอดสะสมรวม (บาท)</b></span>
                <v-spacer></v-spacer>
                <span><b>{{ totalSales }}</b></span>
              </div>
              <v-divider></v-divider>
              <div class="d-flex align-center" style="gap: 1vw">
                <img src="@/assets/ICON/iconOTOP1.png" alt="" height="160" width="160">
                    <div class="d-flex flex-column align-center">
                    <span><b>รับหน้าร้าน</b></span>
                    <span style="color: #64BD34;" :style="MobileSize ? '' : 'font-size: x-large'"><b>{{ totalReceive }}</b></span>
                    <span><b>หน้าร้านสะสม (บาท)</b></span>
                    <span style="font-size: x-small;"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedReceive }}</span></b></span>
                    </div>
                    <v-divider vertical></v-divider>
                    <div class="d-flex flex-column align-center">
                    <span><b>ซื้อออนไลน์</b></span>
                    <span style="color: #64BD34;" :style="MobileSize ? '' : 'font-size: x-large'"><b>{{ totalOnline }}</b></span>
                    <span><b>ออนไลน์สะสม (บาท)</b></span>
                    <span style="font-size: x-small;"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedOnline }}</span></b></span>
                  </div>
                </div>
            </v-col>
          </v-card>
          <v-col cols="12">
            <span>5555</span>
          </v-col>
        </v-col> -->
      </v-row>
      <v-row v-else>
        <v-col cols="12" class="pa-0">
          <div
            :style="IpadSize ? '' : IpadProSize ? '' : ''"
            class="coupon-container"
          >
            <img
              :src="require('@/assets/bgOTOP.png')"
              class="coupon-image"
              :style="MobileSize ? '' : ''"
            >
            <div class="coupon-content" style="top: 0; left: 0; padding: 4vw;">
              <div class="d-flex pa-1" :style="MobileSize ? '' : IpadSize ? 'font-size: large' : 'font-size: x-large'">
                <span><b>ยอดสะสมรวม (บาท)</b></span>
                <v-spacer></v-spacer>
                <span><b>{{ totalSales }}</b></span>
              </div>
              <v-divider style="border: .5px dashed #a0a0a0 !important;"></v-divider>
              <div class="d-flex align-center" :style="IpadProSize ? 'gap: 3vw' : 'gap: 1vw'">
                <img src="@/assets/ICON/iconOTOP1.png" alt="" :height="MobileSize ? 50 : IpadSize ? 80 : 150" :width="MobileSize ? 50 : IpadSize ? 80 : 150">
                <div class="d-flex flex-column align-center">
                  <span :style="MobileSize ? 'font-size: small' : IpadProSize ? 'font-size: large' : ''"><b>รับหน้าร้าน</b></span>
                  <span style="color: #64BD34;" :style="MobileSize ? 'font-size: small' : IpadProSize ? 'font-size: xx-large' : 'font-size: large'"><b>{{ totalReceive }}</b></span>
                  <span :style="MobileSize || IpadSize ? 'font-size: x-small' : 'font-size: medium'"><b>หน้าร้านสะสม (บาท)</b></span>
                  <span :style="MobileSize || IpadSize ? 'font-size: x-small; white-space: nowrap;' : 'font-size: small'"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedReceive }}</span></b></span>
                </div>
                <v-divider style="border: .5px dashed #a0a0a0 !important;" vertical></v-divider>
                <div class="d-flex flex-column align-center">
                  <span :style="MobileSize ? 'font-size: small' : IpadProSize ? 'font-size: large' : ''"><b>ซื้อออนไลน์</b></span>
                  <span style="color: #64BD34;" :style="MobileSize ? 'font-size: small' : IpadProSize ? 'font-size: xx-large' : 'font-size: large'"><b>{{ totalOnline }}</b></span>
                  <span :style="MobileSize || IpadSize ? 'font-size: x-small' : 'font-size: medium'"><b>ออนไลน์สะสม (บาท)</b></span>
                  <span :style="MobileSize || IpadSize ? 'font-size: x-small; white-space: nowrap;' : 'font-size: small'"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedOnline }}</span></b></span>
                </div>
              </div>
            </div>
          </div>
          <v-card class="mx-3">
            <div class="d-flex align-center pa-3">
              <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
              <div>
                <span style="font-size: medium;"><b>Ranking Top 10</b></span><br>
                <span>จัดอันดับจังหวัดที่มียอดขายสูงที่สุด</span>
              </div>
            </div>
            <div>
              <v-data-table
                :headers="headers"
                :items="rankingProvince"
                :items-per-page="5"
                class="elevation-1"
                item-key="i"
                no-data-text="ไม่มีรายการอันดับยอดขายของจังหวัด"
                no-results-text="ไม่พบรายการอันดับยอดขายของจังหวัด"
                style="border: 0 !important;"
                >
                  <template v-slot:[`item.indexOfUser`]="{ item }">
                    <img v-if="item.indexOfUser === 1" src="@/assets/ICON/iconFirst.png" alt="">
                    <img v-else-if="item.indexOfUser === 2" src="@/assets/ICON/iconSecond.png" alt="">
                    <img v-else-if="item.indexOfUser === 3" src="@/assets/ICON/iconThird.png" alt="">
                    <span v-else>{{ item.indexOfUser }}</span>
                  </template>
                  <template v-slot:[`item.total_revenue_all`]="{ item }">
                    <span>{{ item.total_revenue_all.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
                  <template v-slot:[`item.total_revenue_front`]="{ item }">
                    <span>{{ item.total_revenue_front.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
                  <template v-slot:[`item.total_revenue_online`]="{ item }">
                    <span>{{ item.total_revenue_online.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                  </template>
              </v-data-table>
            </div>
          </v-card>
        </v-col>
        <v-col cols="12">
          <v-card class="pa-3">
            <div>
              <div class="d-flex align-center pa-3">
                <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
                <span style="font-size: large;"><b>ยอดขายประเภทสินค้า 5 ประเภท (ร้อยละ)</b></span>
              </div>
              <div class="d-flex justify-center">
                <apexchart
                  type="donut"
                  :series="dataDonut.series"
                  :options="dataDonut.chartOptions"
                  height="280" width="280"
                >
                </apexchart>
              </div>
              <div style="font-size: medium" class="d-flex justify-center" v-if="IpadProSize">
                <span><v-icon small color="#c39bd3">mdi-circle</v-icon> {{ listTypeCategory[0] }}</span>
                <span><v-icon small color="#ec7063">mdi-circle</v-icon> {{ listTypeCategory[1] }}</span>
                <span><v-icon small color="#f8c471">mdi-circle</v-icon> {{ listTypeCategory[2] }}</span>
                <span><v-icon small color="#58d68d">mdi-circle</v-icon> {{ listTypeCategory[3] }}</span>
                <span><v-icon small color="#85c1e9">mdi-circle</v-icon> {{ listTypeCategory[4] }}</span>
              </div>
              <div v-else class="d-flex flex-column align-center">
                <div>
                  <span><v-icon small color="#c39bd3">mdi-circle</v-icon> {{ listTypeCategory[0] }}</span>
                  <span><v-icon small color="#ec7063">mdi-circle</v-icon> {{ listTypeCategory[1] }}</span>
                  <span><v-icon small color="#f8c471">mdi-circle</v-icon> {{ listTypeCategory[2] }}</span>
                </div>
                <div>
                  <span><v-icon small color="#58d68d">mdi-circle</v-icon> {{ listTypeCategory[3] }}</span>
                  <span><v-icon small color="#85c1e9">mdi-circle</v-icon> {{ listTypeCategory[4] }}</span>
                </div>
              </div>
              <v-divider style="border: .5px dashed #a0a0a0 !important;" class="my-4"></v-divider>
              <div>
                <span><b>รายละเอียดยอดขายประเภทสินค้า 5 ประเภท</b></span>
                <div
                v-for="(item, index) in dataRank"
                :key="index"
                class="mb-4 mt-4"
                >
                <div class="d-flex">
                    <span class="mr-3"><b>{{ index+1 }}</b></span>
                    <span><b>{{ item.title }}</b></span>
                    <v-spacer></v-spacer>
                    <span><b>{{ item.price }} บาท</b></span>
                </div>
                <v-spacer></v-spacer>
                <div>
                    <div class="d-flex justify-space-between">
                        <div>หน้าร้าน : {{ item.rateOff }}%</div>
                        <div>Online : {{ item.rateOn }}%</div>
                    </div>
                    <div class="progress-wrapper" style="height: 20px; background-color: #e0e0e0; border-radius: 4px; overflow: hidden;">
                        <div
                        :style="{
                            width: item.rateOff + '%',
                            backgroundColor: '#6c8ef5',
                            height: '100%',
                            display: 'inline-block'
                        }"
                        ></div>
                        <div
                        :style="{
                            width: item.rateOn + '%',
                            backgroundColor: '#b6f3b0',
                            height: '100%',
                            display: 'inline-block'
                        }"
                        ></div>
                    </div>
                </div>
                </div>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="12">
          <v-card class="pb-2">
            <div class="d-flex align-center pa-3">
              <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
              <div>
                <span style="font-size: large;"><b>ช่วงเวลาซื้อขาย</b></span><br>
                <span>ขนาดของวงกลมสะท้อนถึงยอดขายที่มากน้อย</span>
              </div>
            </div>
            <div :class="MobileSize || IpadSize ? 'd-flex flex-column pa-1' : 'd-flex justify-center'" style="gap: .5vw;">
              <div :style="IpadProSize ? 'width: 32vw;' : ''">
                <div class="d-flex flex-column align-center">
                  <img src="@/assets/ICON/iconTimeReceive.png" width="120" height="120">
                  <span><b>รับหน้าร้าน</b></span>
                </div>
                <div>
                  <v-data-table
                    :headers="headersReceive"
                    :items="rankingTimeReceive"
                    :items-per-page="10"
                    class="elevation-1"
                    item-key="i"
                    no-data-text="ไม่มีรายการรับหน้าร้าน"
                    no-results-text="ไม่มีรายการรับหน้าร้าน"
                    style="border: 0 !important;"
                    hide-default-footer
                  >
                    <template v-slot:[`item.time`]="{ item }">
                      <span v-if="item.time === 'other'">ช่วงเวลาอื่น ๆ</span>
                      <span v-else>{{ item.time }}</span>
                    </template>
                    <template v-slot:[`item.revenue`]="{ item }">
                      <span>{{ item.revenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                  </v-data-table>
                </div>
              </div>
              <div :style="IpadProSize ? 'width: 32vw;' : ''">
                <div class="d-flex flex-column align-center">
                  <img src="@/assets/ICON/iconTimeOnline.png" width="120" height="120">
                  <span><b>ซื้อออนไลน์</b></span>
                </div>
                <div>
                  <v-data-table
                    :headers="headersReceive"
                    :items="rankingTimeOnline"
                    :items-per-page="10"
                    class="elevation-1"
                    item-key="i"
                    no-data-text="ไม่มีรายการซื้อออนไลน์"
                    no-results-text="ไม่มีรายการซื้อออนไลน์"
                    style="border: 0 !important;"
                    hide-default-footer
                  >
                    <template v-slot:[`item.time`]="{ item }">
                      <span v-if="item.time === 'other'">ช่วงเวลาอื่น ๆ</span>
                      <span v-else>{{ item.time }}</span>
                    </template>
                    <template v-slot:[`item.revenue`]="{ item }">
                      <span>{{ item.revenue.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                  </v-data-table>
                </div>
              </div>
            </div>
          </v-card>
        </v-col>
        <v-col cols="12">
          <v-card>
            <div class="d-flex align-center pa-3">
              <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
              <div>
                <span style="font-size: large;"><b>Peak Time avg.</b></span><br>
                <span>แสดงช่วงเวลาใน 1 วันที่มีการซื้อสูงสุด</span>
              </div>
            </div>
            <div>
              <apexchart
                type="line"
                :series="dataLine.series"
                :options="dataLine.chartOptions"
                :height="MobileSize ? '300' : ''" :width="MobileSize ? '330' : IpadProSize ? '710' : ''"
              >
              </apexchart>
            </div>
           </v-card>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import { Decode } from '@/services'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      totalSales: '960,000',
      dateFilter: '',
      dialogStartDate: false,
      sentStartDate: '',
      date: ['2024-06-07', '2024-06-15'],
      filterMobileDialog: false,
      sector: ['ภาคเหนือ', 'ภาคตะวันออกเฉียงเหนือ', 'ภาคตะวันตก', 'ภาคกลาง', 'ภาคตะวันออก', 'ภาคใต้'],
      province: [
        'กรุงเทพมหานคร', 'อำนาจเจริญ', 'อ่างทอง', 'บึงกาฬ', 'บุรีรัมย์', 'ฉะเชิงเทรา', 'ชัยนาท', 'ชัยภูมิ', 'จันทบุรี', 'เชียงใหม่',
        'เชียงราย', 'ชลบุรี', 'ชุมพร', 'กาฬสินธุ์', 'กำแพงเพชร', 'กาญจนบุรี', 'ขอนแก่น', 'กระบี่', 'ลำปาง', 'ลำพูน',
        'เลย', 'ลพบุรี', 'แม่ฮ่องสอน', 'มหาสารคาม', 'มุกดาหาร', 'นครนายก', 'นครปฐม', 'นครพนม', 'นครราชสีมา', 'นครสวรรค์',
        'นครศรีธรรมราช', 'น่าน', 'นราธิวาส', 'หนองบัวลำภู', 'หนองคาย', 'นนทบุรี', 'ปทุมธานี', 'ปัตตานี', 'พังงา', 'พัทลุง',
        'พะเยา', 'เพชรบูรณ์', 'เพชรบุรี', 'พิจิตร', 'พิษณุโลก', 'พระนครศรีอยุธยา', 'แพร่', 'ภูเก็ต', 'ปราจีนบุรี', 'ประจวบคีรีขันธ์',
        'ระนอง', 'ราชบุรี', 'ระยอง', 'ร้อยเอ็ด', 'สระแก้ว', 'สกลนคร', 'สมุทรปราการ', 'สมุทรสาคร', 'สมุทรสงคราม', 'สระบุรี',
        'สตูล', 'สิงห์บุรี', 'ศรีสะเกษ', 'สงขลา', 'สุโขทัย', 'สุพรรณบุรี', 'สุราษฎร์ธานี', 'สุรินทร์', 'ตาก', 'ตรัง',
        'ตราด', 'อุบลราชธานี', 'อุดรธานี', 'อุทัยธานี', 'อุตรดิตถ์', 'ยะลา', 'ยโสธร'
      ],
      selectProvince: '',
      selectsector: '',
      productType: [
        'ทดสอบ1',
        'ทดสอบ2',
        'ทดสอบ3'
      ],
      selectedProductType: '',
      shopList: [],
      selectedShop: '',
      dataFilter: [],
      totalReceive: '230,000',
      increasedReceive: '',
      totalOnline: '730,000',
      increasedOnline: '',
      headers: [
        { text: 'ลำดับ', align: 'center', sortable: false, value: 'indexOfUser', class: 'backgroundTable fontTable--text' },
        { text: 'จังหวัด', align: 'center', sortable: false, value: 'province', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดขาย', align: 'center', sortable: false, value: 'total_revenue_all', class: 'backgroundTable fontTable--text' },
        { text: 'หน้าร้าน', align: 'center', sortable: false, value: 'total_revenue_front', class: 'backgroundTable fontTable--text' },
        { text: 'ออนไลน์', align: 'center', sortable: false, value: 'total_revenue_online', class: 'backgroundTable fontTable--text' }
      ],
      headersReceive: [
        { text: 'ช่วงเวลา', align: 'center', sortable: false, value: 'time', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดขายสะสม', align: 'center', sortable: false, value: 'revenue', class: 'backgroundTable fontTable--text' }
      ],
      rankingProvince: [],
      dataRank: [
        { title: '', price: '', offline: '', online: '', rateOff: '', rateOn: '' }
      ],
      rankingTimeReceive: [],
      rankingTimeOnline: [],
      listSummary: [],
      listTypeCategory: [],
      revenue: '',
      startDate: '',
      endDate: '',
      OTOPTypeItem: [
        'OTOP',
        'OTOP Midyear'
      ],
      selectedOTOPType: 'OTOP'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dataDonut () {
      return {
        series: [],
        chartOptions: {
          chart: {
            type: 'donut',
            fontFamily: 'Noto Sans Thai, sans-serif',
            fontWeight: 900,
            fontSize: '24px'
          },
          tooltip: {
            y: {
              formatter: function (val, opts) {
                const total = opts.globals.seriesTotals.reduce((a, b) => a + b, 0)
                const percent = ((val / total) * 100)
                return `(${percent.toFixed(2)}%)`
              },
              title: {
                formatter: function (seriesName) {
                  return seriesName + ':'
                }
              }
            }
          },
          plotOptions: {
            pie: {
              donut: {
                size: '50%',
                labels: {
                  show: false
                }
              }
            }
          },
          dataLabels: {
            enabled: true,
            formatter: (val, opts) => {
              return opts.w.config.series[opts.seriesIndex] + '%'
            },
            style: {
              fontFamily: 'Noto Sans Thai, sans-serif',
              fontSize: '12px'
            }
          },
          labels: this.listTypeCategory,
          colors: ['#c39bd3', '#ec7063', '#f8c471', '#58d68d', '#85c1e9'],
          legend: {
            show: false,
            fontSize: '8px'
          },
          responsive: [{
            options: {
              chart: {
                width: 200
              },
              legend: {
                position: 'bottom'
              }
            }
          }]
        }
      }
    },
    dataLine () {
      return {
        series: [],
        chartOptions: {
          chart: {
            height: 350,
            type: 'line',
            zoom: {
              enabled: false
            },
            toolbar: {
              show: false
            }
          },
          colors: ['#D00000', '#004F7C'],
          dataLabels: {
            enabled: false
          },
          stroke: {
            curve: 'straight',
            width: 2
          },
          title: {
            // text: 'Product Trends by Month',
            align: 'left'
          },
          grid: {
            row: {
              colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
              opacity: 0.5
            }
          },
          xaxis: {
            categories: ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '24:00', '02:00']
          }
          // yaxis: {
          //   title: {
          //     text: 'ยอดจำหน่ายรวม',
          //     fontFamily: 'Noto Sans Thai, sans-serif'
          //   }
          // }
        }
      }
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardOTOPMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardOTOP' }).catch(() => {})
      }
    }
  },
  async created () {
    await this.getItemData()
    await this.getListDetailOTOP()
  },
  methods: {
    cancelPickDate () {
      this.date = []
      this.dialogStartDate = false
    },
    changeformatdate () {
      // console.log(this.date, '484')
      var date1 = this.date[0]
      var date2 = this.date[1] !== undefined ? this.date[1] : this.date[0]
      // var dateStart = ''
      // var dateEnd = ''

      // dateStart = date1.split('-')
      // dateEnd = date2.split('-')
      // console.log(date1, 4555)
      if (date1 > date2) {
        this.dataFilter = [date2, date1]
        this.sentStartDate = this.formatDateToThai(date2) + ' - ' + this.formatDateToThai(date1)
        this.endDate = date1
        this.startDate = date2
      } else if (date1 < date2) {
        this.dataFilter = [date1, date2]
        this.sentStartDate = this.formatDateToThai(date1) + ' - ' + this.formatDateToThai(date2)
        this.endDate = date2
        this.startDate = date1
      } else {
        this.sentStartDate = this.formatDateToThai(date1)
        this.endDate = date1
        this.startDate = date1
      }
      // console.log(this.sentStartDate, 'this.sentStartDate')
      this.dialogStartDate = false
    },
    cancelSelectDate () {
      this.sentStartDate = ''
      this.date = []
      // this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.dialogStartDate = false
    },
    clearSelectDate () {
      this.sentStartDate = ''
      this.date = []
      // this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    },
    closeFilterDialog () {
      this.filterMobileDialog = false
    },
    openFilterDialog () {
      this.filterMobileDialog = true
    },
    clearFilter () {
      this.sentStartDate = ''
      this.selectsector = ''
      this.date = []
      this.selectProvince = ''
      this.selectedShop = ''
      this.selectedProductType = ''
      this.startDate = ''
      this.endDate = ''
      this.getListDetailOTOP()
    },
    confirmFilter () {
      this.filterMobileDialog = false
      this.getListDetailOTOP()
    },
    formatDateToThai (dateStr) {
      const [year, month, day] = dateStr.split('-')
      const buddhistYear = parseInt(year) + 543
      return `${day}/${month}/${buddhistYear}`
    },
    async getItemData () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/filters`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.shopList = response.data.data.shops
          this.sector = response.data.data.regions
          this.province = response.data.data.provinces
          this.productType = response.data.data.types
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.changeformatdate()
      this.$store.commit('closeLoader')
    },
    async getListDetailOTOP () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // await this.axios({
      //   url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/filters`,
      //   // data: this.exportExcelBody,
      //   headers: { Authorization: `Bearer ${oneData.user.access_token}` },
      //   method: 'GET'
      // }).then((response) => {
      //   this.shopList = response.data.data.shops
      //   this.sector = response.data.data.regions
      //   this.province = response.data.data.provinces
      //   this.productType = response.data.data.types
      // })
      await this.axios({
        // url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/summary`,
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/summary?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.listSummary = response.data.data
          this.totalReceive = this.listSummary[0].total_otop_revenue_vat.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.totalOnline = this.listSummary[1].total_otop_revenue_vat.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.increasedReceive = this.listSummary[0].difference.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.increasedOnline = this.listSummary[1].difference.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          var total = this.listSummary[0].total_otop_revenue_vat + this.listSummary[1].total_otop_revenue_vat
          this.totalSales = total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/top-provinces?limit=${10}&${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.rankingProvince = response.data.data
          for (var i = 0; i < this.rankingProvince.length; i++) {
            this.rankingProvince[i].indexOfUser = i + 1
          }
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/top-categories?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          var res = response.data.data
          this.listTypeCategory = res.percentage.map(item => item.type)
          this.dataDonut.series = res.percentage.map(item => item.percentRatio)
          this.dataRank = res.grouped.map(item => {
            return {
              title: item.type,
              price: item.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
              offline: item.front,
              online: item.online,
              rateOff: item.ratio.front,
              rateOn: item.ratio.online
            }
          })
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/time-summary?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/time-summary?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' ? `shop=${this.selectedShop}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          var res = response.data.data
          this.rankingTimeReceive = res.times[0].time_slots
          this.rankingTimeOnline = res.times[1].time_slots
          this.dataLine.series = res.peakTimes.map(item => {
            return {
              name: item.shipping_type === 'front' ? 'หน้าร้าน' : 'online',
              data: item.time_slots.map(item => item.quantity)
            }
          })
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      this.$store.commit('closeLoader')
    },
    backtoSellerMenu () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    }
  }
}
</script>

<style>

</style>
<style scoped>
.coupon-image {
  width: 100%;
  display: block;
  border: 0;
}
.coupon-container {
  position: relative;
  overflow: hidden;
}
.coupon-content {
  position: absolute;
  /* top: 0;
  left: 0; */
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
::v-deep td {
  font-size: smaller !important;
}
</style>
