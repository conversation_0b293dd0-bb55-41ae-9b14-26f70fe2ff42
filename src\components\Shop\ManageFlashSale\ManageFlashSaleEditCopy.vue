<template>
  <v-container grid-list-xs rounded :class="MobileSize ? 'background_color_Mobile' : 'background_color'">
    <v-row justify="start" class="pt-3 pb-3 px-3" v-if="MobileSize">
      <span class="pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
        <v-icon style="color: #2A70C3;" @click="back()">mdi-chevron-left</v-icon> แก้ไขแฟลชเซลล์
      </span>
    </v-row>
    <v-row justify="space-between" class="pt-3 px-3" v-else>
      <span class="pl-4 pt-3 mb-4" :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; font-weight: 700;'">
        แก้ไขแฟลชเซลล์
      </span>
    </v-row>
    <v-row>
      <!-- {{ selectedProduct }} -->
    </v-row>
    <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3 mt-6" >
      <v-col cols="12">
        <span>สินค้าที่เข้าร่วม <span style="color:#F5222D">*</span></span>
          <treeselect
            v-model="selectedProduct"
            :multiple="true"
            :options="dataItem"
            placeholder="เลือกสินค้าที่เข้าร่วม..."
            :normalizer="normalizer"
            valueFormat="object"
            :disable-branch-nodes="true"
          />
      </v-col>
    </v-row>
    <v-row justify="end" class="px-5 mt-4">
      <v-btn width="110" height="40" outlined rounded color="#2A70C3" @click="closeeditFlash()" class="mr-2">ยกเลิก</v-btn>
      <v-btn width="110" height="40" rounded color="#2A70C3" class="white--text" @click="editFlash()">บันทึก</v-btn>
    </v-row>
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64" color="#2A70C3"></v-progress-circular>
    </v-overlay>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
import { msgErr, statusErr } from '@/enum/GetError'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: {
    Treeselect
  },
  data () {
    return {
      overlay: false,
      selectedProduct: [],
      dataItem: [],
      normalizer (node) {
        return {
          id: node.id,
          label: node.name,
          children: node.sub_category
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    }
  },
  mounted () {
  },
  async created () {
    this.$EventBus.$emit('SelectPath')
    window.addEventListener('storage', function (event) {
      if (event.key === 'authNSG' && !event.newValue) {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    })
    if (localStorage.getItem('authNSG') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.getDataForTree()
    this.getDetailFlash()
  },
  methods: {
    scrollToTopPage () {
      window.scrollTo(0, 0)
    },
    async getDataForTree () {
      await this.$store.dispatch('actionGetForSelectFlashSaleSeller')
      var res = await this.$store.state.NSGManageFlashSale.stateGetForSelectFlashSaleSeller
      console.log(res)
      if (res.result === 'SUCCESS') {
        this.dataItem = res.data
      } else {
        if (res.result === 'FAILED') {
          var [msg, iconMsg] = msgErr(res.message)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        } else {
          [msg, iconMsg] = statusErr(res.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    async getDetailFlash () {
      var data = {
        flashsale_id: this.$route.query.flashSaleID
      }
      await this.$store.dispatch('actionDetailFlashSaleSeller', data)
      var res = await this.$store.state.NSGManageFlashSale.stateDetailFlashSaleSeller
      if (res.result === 'SUCCESS') {
        this.getData = res.data
        this.selectedProduct = this.getData.product_list
        for (var i = 0; i < this.selectedProduct.length; i++) {
          this.selectedProduct[i].name = this.selectedProduct[i].product_name
          delete this.selectedProduct[i].product_name
        }
        console.log(this.selectedProduct)
      }
    },
    async createFlash () {
      this.itemFlashSalefinalPhase = this.itemFlashSalePhase2.map(e => {
        return {
          product_attribute_id: e.attribute_id,
          product_id: e.id,
          flashsale_price: e.flashprice + '.00'
        }
      })
      console.log(this.FlashsTest[0], '5555555556')
      console.log(this.itemFlashSalefinalPhase, '5555555556')
      var data = {
        seller_shop_id: '1',
        flashsale_id: this.$route.query.flashSaleID,
        image_path: '',
        start_date: '',
        end_date: '',
        countdown_date: '',
        product_list: this.itemFlashSalefinalPhase
      }
      console.log(data, '5555555556')
      await this.$store.dispatch('actionCreateFlashSaleSaleSeller', data)
      const res = await this.$store.state.NSGManageFlashSale.stateCreateFlashSaleSeller
      if (res.result === 'SUCCESS') {
        this.dialogSuccess = true
        setTimeout(() => {
          this.dialogSuccess = false
        }, 2000)
      } else {
        if (res.result === 'FAILED') {
          if (res.message === 'Have flashsale in this start_date') {
            this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในวันที่เริ่มต้นนี้แล้ว', showConfirmButton: false, timer: 2000 })
          } else if (res.message === 'Have flashsale in this end_date') {
            this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในวันที่สิ้นสุดนี้แล้ว', showConfirmButton: false, timer: 2000 })
          } else if (res.message === 'Have flashsale in this start_date and end_date') {
            this.$swal.fire({ icon: 'error', text: 'มีแฟลชเซลล์ในช่วงเวลานี้แล้ว', showConfirmButton: false, timer: 2000 })
          } else if (res.message === 'end_date not over start_date') {
            this.$swal.fire({ icon: 'error', text: 'วันที่สิ้นสุดต้องไม่น้อยกว่าวันที่เริ่มต้น', showConfirmButton: false, timer: 2000 })
          } else if (res.message === 'countdown_date not over start_date') {
            this.$swal.fire({ icon: 'error', text: 'ตัวนับเวลาถอยหลังต้องไม่น้อยกว่าวันที่เริ่มต้น', showConfirmButton: false, timer: 2000 })
          }
        } else {
          var [msg, iconMsg] = statusErr(res.code)
          this.$swal.fire({ icon: iconMsg, text: msg, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    back () {
      this.$EventBus.$emit('openNavBar')
    }
  }
}
</script>
<style scoped>
.vSelectLineHeight /deep/ .v-select__selection--comma {
  line-height: 25px !important;
}
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.v-data-table /deep/
.v-data-table__wrapper .v-data-table__mobile-table-row {
  margin-bottom: 20px;
  border: 1px solid #ededed;
  display: block;
  border-radius: 8px;
}
@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.6rem;
    padding: 0 0 0 8px;
  }
}
@media only screen and (min-width: 768px) {
  /* For desktop: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    /* font-size: 14px; */
    padding: 0 0 0 8px;
  }
}
.v-data-table__empty-wrapper {
    text-align: center;
    place-self: center;
}
</style>
