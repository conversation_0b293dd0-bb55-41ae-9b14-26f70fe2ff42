<template>
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <AddUserModal ref="AddUserModal" />
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="pb-0" v-if="!MobileSize">จัดการผู้ใช้งานภายในนิติบุคคล</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;" class="pb-0" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> จัดการผู้ใช้งานภายในนิติบุคคล</v-card-title>

      <v-divider class="my-4"></v-divider>

      <v-card-text class="px-2">
        <v-row dense style="padding-bottom: 10px;">
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" dense hide-details outlined placeholder="ค้นหาจากชื่อ-นามสกุลหรืออีเมลผู้ใช้งาน" style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" sm="12" xs="12" align="end" class="py-0" v-if="!IpadSize && managePosition === 'yes'">
            <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
              @click="addDetailUser()" dark>
              <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล
            </v-btn>
          </v-col>
        </v-row>

        <v-row dense no-gutters class="mb-2">
          <v-col cols="12" class="py-0 mb-0">
            <a-tabs v-model="activeTab" @change="SelectDetailUser" :show-arrows="IpadSize || IpadProSize">
              <a-tab-pane :key="0">
                <span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countUserAll }}</a-tag></span>
              </a-tab-pane>
              <a-tab-pane :key="1">
                <span slot="tab">ใช้งาน <a-tag color="#1AB759" style="border-radius: 8px;">{{ countUserActive }}</a-tag></span>
              </a-tab-pane>
              <a-tab-pane :key="2">
                <span slot="tab">ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countUserInActive }}</a-tag></span>
              </a-tab-pane>
            </a-tabs>
          </v-col>
        </v-row>

        <div v-if="userDataList.length > 0">
          <h4 v-if="!MobileSize" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">
              {{ pageHeaders }} {{ showCountStatusUser }} รายการ
          </h4>

          <v-data-table
          :headers="headerManageUser"
          :items="userDataList"
          :search="search"
          no-results-text="ไม่พบรายชื่อผู้ใช้งานภายในนิติบุคคล"
          no-data-text="ไม่มีรายชื่อผู้ใช้งานภายในนิติบุคคล"
          @pagination="countStatusUser"
          :items-per-page="10"
          class="elevation-1 mt-4"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
        >
          <template v-slot:[`item.indexOfUser`]="{ index }">
            <span>{{ index + 1 }}</span>
          </template>
          <template v-slot:[`item.name`]="{ item }">
            <span>
              {{ item.name }}
            </span>
          </template>
          <template v-slot:[`item.active_status`]="{ item }">
            <span v-if="item.active_status === 'active'">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ใช้งานได้</v-chip>
            </span>
            <span v-else-if="item.active_status === 'inactive'">
              <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
            </span>
          </template>
          <template v-slot:[`item.action`]="{ item }">
              <template>
                <v-btn
                  color="#27AB9C"
                  dark
                  @click="showDetailUser(item)"
                >
                  รายละเอียดผู้ใช้งาน
                </v-btn>
              </template>
          </template>
        </v-data-table>
        </div>
      </v-card-text>
      <v-container v-if="userDataList.length === 0">
              <v-row justify="center" align-content="center" >
                <v-col cols="12" md="12" align="center" style="min-height: 636px;">
                  <div style="padding-top: 90px;">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                      height="100%" width="100%" contain aspect-ratio="2"></v-img>
                  </div>
                  <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                    <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการ{{ emptyPageText }}</span><br />
                  </h2>
                </v-col>
              </v-row>
      </v-container>
      <DetailUserModal ref="DetailUserModal" />
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    AddUserModal: () => import(/* webpackPrefetch: true */ '@/components/Business/dialogAddUser.vue'),
    DetailUserModal: () => import(/* webpackPrefetch: true */ '@/components/Business/dialogDetailUser.vue')
  },
  data () {
    return {
      managePosition: '',
      status: '',
      activeTab: 0,
      taxID: '',
      detailBusiness: '',
      userDataList: [],
      userID: [],
      lazy: false,
      countUserAll: 0,
      countUserActive: 0,
      countUserInActive: 0,
      disableTable: false,
      search: '',
      showCountStatusUser: '',
      itemsPerPage: 10,
      pageHeaders: '',
      emptyPageText: '',
      headerManageUser: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-นามสกุล', value: 'name', align: 'center', sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'อีเมล', value: 'email', align: 'center', sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'active_status', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', align: 'center', filterable: false, sortable: false, width: '200px', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ]
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavBusiness')
    this.$EventBus.$on('getBusinessTaxID', (status) => {
      // console.log('Status received:', status)
      this.getBusinessTaxID(status)
    })
    this.$EventBus.$on('SelectDetailUser', (activeTab) => {
      // console.log('ActiveTab received:', activeTab)
      this.SelectDetailUser(activeTab)
    })
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getBusinessTaxID')
      this.$EventBus.$off('SelectDetailUser')
    })
    // window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageUserMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageUser' }).catch(() => {})
      }
    }
  },
  mounted () {
    this.SelectDetailUser(this.activeTab)
  },
  methods: {
    addDetailUser () {
      this.$refs.AddUserModal.open(this.userID)
    },
    showDetailUser (item) {
      const data = item
      this.$refs.DetailUserModal.open(data, this.taxID, this.status, this.activeTab)
    },
    backtoUserMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
      }
    },
    async getBusinessTaxID (status) {
      // console.log('Status', status)
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      const taxID = await this.$store.state.ModuleUser.stateAuthorityUser
      if (taxID.code === 200) {
        this.detailBusiness = taxID.data
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = taxID.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxID = ownerBusiness[0].owner_tax_id
          this.listPositionTable(this.taxID)
          const data = {
            status: status,
            tax_id: this.taxID
          }
          await this.$store.dispatch('actionsManageUser', data)
          const detailUser = await this.$store.state.ModuleBusiness.stateManageUser
          if (detailUser.result === 'SUCCESS') {
            // console.log(detailUser.data)
            this.userID = detailUser.data.map(user => user.user_id)
            // console.log(this.userID)
            this.$EventBus.$emit('changeNavBusiness')
            this.userDataList = detailUser.data
            // console.log('0', this.userDataList)
            if (this.userDataList.length !== 0) {
              this.userDataList.forEach(element => {
                element.name = element.first_name_th + ' ' + element.last_name_th
              })
              for (var i = 0; i < this.userDataList.length; i++) {
                this.userDataList[i].indexOfUser = i + 1
              }
            }
            this.countUserAll = this.userDataList.length
            this.countUserActive = detailUser.data.filter(user => user.active_status === 'active').length
            this.countUserInActive = detailUser.data.filter(user => user.active_status === 'inactive').length
          }
        }
        this.$store.commit('closeLoader')
      } else {
        // this.array_business = response.data.array_business
        this.disableTable = false
        this.$swal.fire({
          icon: 'error',
          text: 'ยังไม่อนุมัติ',
          showConfirmButton: false
        })
        this.$store.commit('closeLoader')
      }
    },
    // this.detailBusiness = taxID.data
    // this.taxID = taxID.data.array_business[0].owner_tax_id

    // const data = {
    //   status: status,
    //   tax_id: this.taxID
    // }

    // await this.$store.dispatch('actionsManageUser', data)
    // const detailUser = await this.$store.state.ModuleBusiness.stateManageUser
    // if (detailUser.result === 'SUCCESS') {
    //   // console.log(detailUser.data)
    //   this.$EventBus.$emit('changeNav')
    //   this.userDataList = detailUser.data
    //   // console.log('0', this.userDataList)
    //   if (this.userDataList.length !== 0) {
    //     this.userDataList.forEach(element => {
    //       element.name = element.first_name_th + ' ' + element.last_name_th
    //     })
    //     for (var i = 0; i < this.userDataList.length; i++) {
    //       this.userDataList[i].indexOfUser = i + 1
    //     }
    //   }
    //   this.countUserAll = this.userDataList.length
    //   this.countUserActive = detailUser.data.filter(user => user.status === 'active').length
    //   this.countUserInActive = detailUser.data.filter(user => user.status === 'inactive').length
    //   this.$store.commit('closeLoader')
    // }
    async listPositionTable (id) {
      var data = {
        tax_id: id
      }
      await this.$store.dispatch('actionsListPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListPositions
      if (response.code === 200) {
        this.managePosition = response.data.manage_position
      }
    },
    async SelectDetailUser (item) {
      // console.log('item', item)
      this.activeTab = item
      if (item === 0) {
        this.pageHeaders = 'รายการผู้ใช้งานในนิติบุคคลทั้งหมด'
        this.emptyPageText = 'ทั้งหมด'
        this.status = ''
      } else if (item === 1) {
        this.pageHeaders = 'รายการกำลังใช้งานทั้งหมด'
        this.emptyPageText = 'ใช้งาน'
        this.status = 'active'
      } else if (item === 2) {
        this.pageHeaders = 'รายการยกเลิกทั้งหมด'
        this.emptyPageText = 'ยกเลิก'
        this.status = 'inactive'
      }
      await this.getBusinessTaxID(this.status)
      if (this.status) {
        this.userDataList = this.userDataList.filter(user => user.active_status === this.status)
      } else {
        this.userDataList = this.userDataList.filter(user => user.active_status === 'active' || user.active_status === 'inactive')
      }
      // console.log('Filtered userDataList:', this.userDataList)
    },
    // async SelectDetailUser (item) {
    //   let status = ''
    //   if (item === 0) {
    //     this.pageHeaders = 'รายการผู้ใช้งานในนิติบุคคลทั้งหมด'
    //     this.emptyPageText = 'ทั้งหมด'
    //     status = ''
    //   } else if (item === 1) {
    //     this.pageHeaders = 'รายการกำลังใช้งานทั้งหมด'
    //     this.emptyPageText = 'ใช้งาน'
    //     status = 'active'
    //   } else if (item === 2) {
    //     this.pageHeaders = 'รายการยกเลิกทั้งหมด'
    //     this.emptyPageText = 'ยกเลิก'
    //     status = 'inactive'
    //   }

    //   await this.getBusinessTaxID(status)

    //   this.userDataList = this.userDataList.filter((user) => {
    //     if (status === '') return true
    //     return user.status === status
    //   })
    // },
    countStatusUser (pagination) {
      this.showCountStatusUser = pagination.itemsLength
    }
  }
}
</script>

<style scoped>
.custom-btn-style {
  width: 50px;
  height: 50px;
  border: 1px solid #F2F2F2;
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
}
.text-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.text-break {
  white-space: normal;
  display: inline-block;
  word-break: break-word;
  max-width: 600px;
}
/* .v-data-table /deep/ .v-data-table-header-mobile__wrapper {
  display: flex;
  justify-content: end;
} */
/* .v-data-table /deep/ .v-simple-checkbox {
  padding-left: 25px;
} */
/* .v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
} */
/* .whiteNumber {
  color: #FFF;
}
.v-data-table /deep/ .v-data-table__wrapper .v-data-table__mobile-row {
  border-bottom: 0px !important;
  padding-left: 4px;
  padding-right: 4px;
} */
</style>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  ::v-deep .elevation-1 tr th:first-of-type, td:first-of-type {
    background-color: #E6F5F3;
    border-style: none !important;
  }
</style>
