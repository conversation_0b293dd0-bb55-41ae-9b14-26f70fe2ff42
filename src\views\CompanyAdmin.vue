<template>
  <v-app>
    <v-app-bar color="green darken-3" dark app elevation="0" clipped-left>
      <v-app-bar-nav-icon @click="drawer = !drawer"></v-app-bar-nav-icon>
      <v-spacer></v-spacer>
      <v-btn icon small>
        <v-icon>mdi-dots-vertical</v-icon>
      </v-btn>
    </v-app-bar>
    <v-navigation-drawer v-model="drawer" app clipped>
      <v-sheet color="grey lighten-4" class="pa-4">
        <v-avatar color="indigo">
          <v-icon dark>
            mdi-account-circle
          </v-icon>
        </v-avatar>
        <div><EMAIL></div>
      </v-sheet>
      <v-divider></v-divider>
      <v-list dense rounded>
        <v-list-item-group v-model="selectedItem" color="success">
          <v-list-item v-for="[icon, text, link] in links" :key="icon" link @click="goPage(link)">
            <v-list-item-icon>
              <v-icon>{{ icon }}</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title>{{ text }}</v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list-item-group>
      </v-list>
    </v-navigation-drawer>
    <v-main>
      <router-view/>
    </v-main>
  </v-app>
</template>

<script>
export default {
  data: () => ({
    drawer: true,
    group: null,
    selectedItem: null,
    cards: ['Today', 'Yesterday'],
    links: [
      ['mdi-city-variant-outline', 'รายละเอียด', '/detail'],
      ['mdi-animation-outline', 'ฝ่ายและแผนก', '/departmentanddivision'],
      ['mdi-account-group-outline', 'ผู้ใช้งาน', '/user'],
      ['mdi-ballot-outline', 'แค็ตตาล็อก', '/hidecategory'],
      ['mdi-order-bool-ascending-variant', 'รายการ', '']
    ]
  }),
  methods: {
    goPage (link) {
      this.$router.push(link).catch(() => {})
    }
  }
}
</script>
