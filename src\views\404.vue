<template>
<div>
  <AppBar/>
  <div class="container-fluid crm-page-wrapper error-404">
    <center v-if="status === 1">
      <v-img contain width="400" height="100%" :src="items[0].url"></v-img>
    </center>
    <v-div v-if="status === 2">
      <center >
        <v-img contain width="400" height="100%" :src="items[2].url"></v-img>
      </center>
      <h1>404 not found</h1>
      <p>ไม่พบ URL ของหน้าเว็บไซต์นี้</p>
    </v-div>
  </div>
  <FooTer/>
</div>
</template>
<script>
export default {
  components: {
    AppBar: () => import('@/components/Home/AppBarUI'),
    FooTer: () => import('@/components/Home/FooterUI')
  },
  data () {
    return {
      status: 1,
      items: [
        {
          url: require('@/assets/ErrorAndRenovation/Frame654img.png')
        },
        {
          url: require('@/assets/ErrorAndRenovation/Frame654ss.png')
        },
        {
          url: require('@/assets/ErrorAndRenovation/error1.jpg')
        },
        {
          url: require('@/assets/ErrorAndRenovation/error2.png')
        }
      ]
    }
  }
}
</script>
