<template>
  <div>
  <!-- <v-container grid-list-xs> -->
    <!-- <v-form> -->
      <v-card width="100%" height="100%" class="mb-4" v-if="userAddress.length !== 0" elevation="0">
        <v-card-title style="font-weight: 700;">บัญชีธนาคารและบัตรเครดิต</v-card-title>
        <!-- <v-col cols="12" class="mt-0 pt-0"><v-divider></v-divider></v-col> -->
        <v-row dense class="mt-2 mb-6 mr-4">
          <v-col cols="12" md="12" class="px-6">
            <v-row justify="end">
              <v-btn color="#27AB9C" dark><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
            </v-row>
          </v-col>
        </v-row>
        <v-col cols="12" md="6" sm="12" xs="12" v-if="dataCreate !== ''">
          <v-card elevation="0" outlined :style="radioGroup === 'default' ? 'border-color: #27AB9C' : 'border-color: #C4C4C4'">
            <v-row>
              <v-col cols="12" md="12">
                <v-row>
                  <v-row class="pa-5">
                    <!-- action -->
                    <v-col cols="12" md="12" class="pl-5 pb-0">
                      <v-row justify="end" class="mt-1 mr-1">
                        <v-btn color="#F2F2F2" fab x-small elevation="0"><v-icon color="#A1A1A1">mdi-pencil</v-icon></v-btn>
                        <v-btn color="#F2F2F2" fab x-small class="ml-2" elevation="0"><v-icon color="#A1A1A1">mdi-delete-outline</v-icon></v-btn>
                      </v-row>
                    </v-col>
                    <!-- ชื่อ-นามสกุล -->
                    <v-col cols="12" md="12" class="pl-5 pb-0">
                      <span style="font-weight: 700; font-size: 16px;">{{ userdetail.first_name_th }} {{ userdetail.last_name_th }}</span>
                    </v-col>
                    <!-- หมายเลขโทรศัพท์ -->
                    <v-col cols="12" md="12" class="pl-5 pb-0">
                      <span style="color: #333333; font-weight: 400; font-size: 14px;">{{ userdetail.phone }}</span>
                    </v-col>
                    <!-- ที่อยู่ -->
                    <v-col cols="12" md="12" class="pl-5 pb-0">
                      <span style="color: #333333; font-weight: 400; font-size: 14px;">{{ userAddress.detail }} {{ userAddress.sub_district }} {{ userAddress.district }} {{ userAddress.province }} {{ userAddress.zip_code }}</span>
                    </v-col>
                    <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                    <v-col cols="12" md="12" class="pl-5 py-0">
                      <v-radio-group v-model="radioGroup">
                        <v-radio
                          color="#27AB9C"
                          label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                          value="default"
                          style="color: #333333"
                        ></v-radio>
                      </v-radio-group>
                    </v-col>
                  </v-row>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" md="12" v-else>
          <v-row justify="center" align-content="center">
            <v-col cols="12" md="12" sm="12" class="mt-4">
              <v-row justify="center">
                <v-img src="@/assets/ImageINET-Marketplace/ICONProfile/OBJECTS.png" width="448px" height="251.86px" contain></v-img>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6" class="mt-8">
              <v-row justify="center" align-content="center">
                <span style="font-weight: 700; font-size: 24px; color: #27AB9C;">ยังไม่มีบัญชีธนาคารและบัตรเครดิต</span>
                <span style="font-weight: 700; font-size: 24px; color: #27AB9C;">“กดเพิ่มเพื่อข้อมูลที่สมบูรณ์ของคุณ”</span>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
      </v-card>
    <!-- </v-form> -->
  <!-- </v-container> -->
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dataCreate: '',
      userdetail: [],
      userAddress: [],
      userShop: [],
      oneUserType: '',
      BindAccountdialog: false,
      usernameOne: '',
      passwordOne: '',
      lazy: false,
      radioGroup: 'default',
      Rules: {
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้',
          v => /^[A-Za-z0-9]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรและตัวเลขเท่านั้น'
        ],
        password: [
          v => !!v || 'กรุณากรอกรหัสผ่าน'
        ]
      }
    }
  },
  async created () {
    this.dataCreate = ''
    this.$EventBus.$emit('changeNav')
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.oneUserType = onedata.user.type_user
    // console.log('type user', this.oneUserType)
    var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    var data = {
      role_user: dataRole.role
    }
    await this.$store.dispatch('actionsUserDetailPage', data)
    const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
    // console.log('userdetail data', userdetail)
    this.userdetail = userdetail.data[0]
    if (this.userdetail.address_data !== undefined) {
      this.userAddress = this.userdetail.address_data[0]
    } else {
      this.userAddress = []
    }
    this.userShop = this.userdetail.shop_data[0]
    // console.log('userdetail data', userdetail, this.userAddress, this.userShop)
  },
  methods: {
    BindAccount () {
      this.$swal.fire({
        text: `คุณต้องการผูกบัญชี ${this.userdetail.email} กับ One ID จริงหรือไม่?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#3085d6',
        cancelButtonColor: '#d33',
        confirmButtonText: 'ยอมรับ',
        cancelButtonText: 'ไม่ยอมรับ'
      }).then(async (result) => {
        if (result.isConfirmed) {
          this.BindAccountdialog = true
          // const { value: password } = await this.$swal.fire({
          //   // text: 'กรุณาใส่รหัสผ่านเพื่อยืนยันผู้ใช้บัญชีนี้',
          //   showCancelButton: true,
          //   confirmButtonColor: '#3085d6',
          //   cancelButtonColor: '#d33',
          //   confirmButtonText: 'ตกลง',
          //   cancelButtonText: 'ยกเลิก',
          //   input: 'password',
          //   inputLabel: 'ใส่รหัสผ่านเพื่อยืนยันผู้ใช้บัญชีนี้',
          //   inputPlaceholder: 'กรุณาใส่รหัสผ่าน',
          //   inputValidator: (value) => {
          //     if (!value) {
          //       return 'กรุณาใส่รหัสผ่าน'
          //     }
          //   }
          // })
          // if (password) {
          //   var data = {
          //     username: this.userdetail.email,
          //     password: password
          //   }
          //   // console.log(data)
          //   await this.$store.dispatch('actionBindAccount', data)
          //   var response = await this.$store.state.ModuleUser.stateBindAccount
          //   console.log(response)
          //   if (response.result === 'SUCCESS') {
          //     this.$swal.fire({ title: 'ผูกบัญชีสำเร็จ!', text: 'กรุณา Login บัญชีใหม่เพื่อใช้งาน', icon: 'success', timer: 3000, showConfirmButton: false })
          //   } else {
          //     this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
          //   }
          // }
        }
      })
    }
  }
}
</script>
