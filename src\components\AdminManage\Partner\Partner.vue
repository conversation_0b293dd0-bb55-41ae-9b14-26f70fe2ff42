<template>
  <v-container>
    <v-row align="center">
      <!-- รายชื่อคู่ค้าองค์กร -->
      <v-col cols="12">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense justify="center">
            <v-col cols="12" md="12" sm="12" xs="12">
              <v-card-title style="font-weight: bold;">รายชื่อร้านค้าที่เป็นคู่ค้าของบริษัท {{ companyName }}</v-card-title>
            </v-col>
          </v-row>
          <v-col cols="12" class="py-0 px-4">
            <a-tabs @change="SelectedMenuPartner">
              <a-tab-pane v-for="item in PartnerMenu" :key="item.key" :tab="item.name"></a-tab-pane>
            </a-tabs>
          </v-col>
          <!-- <TableUser :props="ListData" :type="stateStatus === 0 ? 'userList' : 'userRequest'"/> -->
          <TablePartner :props="stateStatus === 0 ? this.PartnerData : this.RequsetPartnerData" :type="stateStatus === 0 ? 'PartnerList' : 'PartnerRequest'"/>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    TablePartner: () => import(/* webpackPrefetch: true */ '@/components/AdminManage/Partner/PartnerTable')
  },
  data () {
    return {
      companyName: '',
      PartnerMenu: [
        { key: 0, name: 'อนุมัติ' },
        { key: 1, name: 'กำลังส่งคำขอ' }
      ],
      stateStatus: 0,
      PartnerData: [
        {
          id: 1,
          name_th: 'ร้านค้าเทสวันศูนย์หนึ่ง',
          name_en: 'Testone 01 Shop',
          status: true
        }
      ],
      RequsetPartnerData: [
        {
          id: 1,
          name_th: 'ร้านค้าเทสวันศูนย์สอง',
          name_en: 'Testone 02 Shop',
          request_date: '2022-03-21T04:46:36.000000Z',
          update_date: '2022-03-23T04:46:36.000000Z',
          status: 'request'
        }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'รายชื่อคู่ค้าองค์กร')
    this.$EventBus.$emit('changeNavAdminManage')
    this.getListPartner()
  },
  methods: {
    getListPartner () {
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('companyData')))
      this.companyName = companyData.name_th
    },
    SelectedMenuPartner (item) {
      this.stateStatus = item
    }
  }
}
</script>
