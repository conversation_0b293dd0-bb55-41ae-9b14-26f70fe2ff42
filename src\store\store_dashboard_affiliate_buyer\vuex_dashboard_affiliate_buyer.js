import DashboardAffiliateBuyer from './axios_dashboard_affiliate_buyer'

const ModuleDashboardAffiliateBuyer = {
  state: {
    stateDataTable: [],
    stateClickReportList: [],
    stateTopTen: [],
    stateShoplist: [],
    stateGraph: [],
    stateOrderReport: [],
    stateTopSold: [],
    stateCommissionTable: []
  },
  mutations: {
    mutationDataTable (state, data) {
      state.stateDataTable = data
    },
    mutationClickReportList (state, data) {
      state.stateClickReportList = data
    },
    mutationTopTen (state, data) {
      state.stateTopTen = data
    },
    mutationShoplist (state, data) {
      state.stateShoplist = data
    },
    mutationGraph (state, data) {
      state.stateGraph = data
    },
    mutationOrderReport (state, data) {
      state.stateOrderReport = data
    },
    mutationTopSold (state, data) {
      state.stateTopSold = data
    },
    mutationCommissionTable (state, data) {
      state.stateCommissionTable = data
    }
  },
  actions: {
    async actionClickReportList (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetReportClick(access)
      await context.commit('mutationClickReportList', responseData)
    },
    async actionDataTable (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetDataTable(access)
      await context.commit('mutationDataTable', responseData)
    },
    async actionTopTen (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetTopTen(access)
      await context.commit('mutationTopTen', responseData)
      // console.log('this top 10 vuex', responseData)
    },
    async actionShopList (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetShopList(access)
      await context.commit('mutationShoplist', responseData)
      // console.log('this shoplist vuex', responseData)
    },
    async actionGraph (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetGraph(access)
      await context.commit('mutationGraph', responseData)
      // console.log('this graph vuex', responseData)
    },
    async actionOrderReport (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetOrderReport(access)
      await context.commit('mutationOrderReport', responseData)
      // console.log('this order report vuex', responseData)
    },
    async actionTopSold (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetTopSold(access)
      await context.commit('mutationTopSold', responseData)
    },
    async actionCommissionTable (context, access) {
      var responseData = await DashboardAffiliateBuyer.GetCommissionTable(access)
      await context.commit('mutationCommissionTable', responseData)
    }
  }
}
export default ModuleDashboardAffiliateBuyer
