<template>
 <div>
  <div class="ml-3 mt-2">
    <span style="font-size: 16px; color: #333333; font-weight: 600;" ></span>สินค้าขายดี Top 10และผู้ซื้อ Top 10
  </div>
<v-row v-resize="onResize">
  <v-col cols="12" md="6">
<v-card :width="isMobile ? '370px' : '470px' " :class="isMobile ? 'ml-3 mb-3' : 'ml-6 mt-4' ">
  <v-card-title >
      <v-avatar size="40">
             <img
              src="../../assets/icons/nullproduct.png"
              alt="Product"
             >
      </v-avatar>
      <div class="mx-3"></div>
          <span style="font-size: 16px; color: #333333; font-weight: 700;">สินค้าขายดี</span> &nbsp;&nbsp;Top 10
          <div class="mx-8"></div>
          <v-btn v-if="isMobile" color="teal" fab text class="ml-0" @click="dialogDataProduct">
            <v-icon>mdi-dots-vertical</v-icon>
          </v-btn>
          <v-btn v-else outlined color="teal" class="ml-0" @click="dialogDataProduct">
            ดูทั้งหมด
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
  </v-card-title>
  <hr/>
<v-list>
      <v-list-item-group
        v-for="(item, i) in items"
        :key="i"
      >

        <v-list-item
          v-if="i <= 4"
        >
         <v-list-item-avatar size="60">
             <img v-if="i === 0"
              src="../../assets/icons/level_1.png"
              alt="Product"
             >
             <img v-else-if="i === 1"
              src="../../assets/icons/level_2.png"
              alt="Product"
             >
             <img v-else-if="i === 2"
              src="../../assets/icons/level_3.png"
              alt="Product"
             >
             <img v-else-if="i === 3"
              src="../../assets/icons/level_4.png"
              alt="Product"
             >
             <img v-else-if="i === 4"
              src="../../assets/icons/level_5.png"
              alt="Product"
             >
          </v-list-item-avatar>
        <v-list-item-avatar size="90">
           <img v-if="item.product_image"
              :src="item.product_image"
              alt="Product"
             >
             <img v-else
              src="../../assets/icons/nullproduct.png"
              alt="Product"
             >
          </v-list-item-avatar>
        <v-list-item-content class="mx-1 pr-12">
            <v-list-item-title v-text="item.product_name"></v-list-item-title>
          </v-list-item-content>
          <div style="font-size: 16px;color: #1AB759;">{{item.total_sold_prize}}&nbsp;&nbsp;ชิ้น</div>
        </v-list-item>
      </v-list-item-group>
    </v-list>
</v-card>
</v-col>
<v-col cols="12" md="6">
<v-card :width="isMobile ? '370px' : '470px' " :class="isMobile ? 'ml-3 mb-3' : 'mt-4' ">
  <v-card-title>
      <v-avatar size="40">
             <img
              src="../../assets/icons/checklist 1.png"
              alt="Product"
             >
      </v-avatar>
      <div class="mx-3"></div>
      <div v-if="isMobile">
          <span style="font-size: 18px; color: #333333; font-weight: 700;">ผู้ซื้อประจำ</span> &nbsp;&nbsp;Top 10
          </div>
      <div v-else >
           <span style="font-size: 16px; color: #333333; font-weight: 700;">ผู้ซื้อประจำ</span> &nbsp;&nbsp;Top 10
          </div>
          <div class="mx-8"></div>
          <v-btn v-if="isMobile" @click="dialogDataUser" fab text class="ml-0">
            <v-icon>mdi-dots-vertical</v-icon>
          </v-btn>
          <v-btn v-else @click="dialogDataUser" outlined color="teal" class="ml-0">
            ดูทั้งหมด
            <v-icon>mdi-chevron-right</v-icon>
          </v-btn>
  </v-card-title>
  <hr/>
<v-list>
      <v-list-item-group
          v-for="(item, i) in items2"
          :key="item.user_id"
      >
        <v-list-item
          v-if="i <= 4"
        >
        <v-list-item-avatar size="60">
             <img v-if="i === 0"
              src="../../assets/icons/level_1.png"
              alt="Product"
             >
             <img v-else-if="i === 1"
              src="../../assets/icons/level_2.png"
              alt="Product"
             >
             <img v-else-if="i === 2"
              src="../../assets/icons/level_3.png"
              alt="Product"
             >
             <img v-else-if="i === 3"
              src="../../assets/icons/level_4.png"
              alt="Product"
             >
             <img v-else-if="i === 4"
              src="../../assets/icons/level_5.png"
              alt="Product"
             >
          </v-list-item-avatar>
          <v-list-item-avatar size="90">
            <img
              src="../../assets/icons/checklist 1.png"
              alt="Product"
             >
          </v-list-item-avatar>
          <v-list-item-icon>
            <v-icon v-text="item.icon"></v-icon>
          </v-list-item-icon>
          <v-list-item-content>
            <v-list-item-title class="text-center" v-text="item.name"></v-list-item-title>
            <span class="text-center" style="font-size: 10px;color: #e7e7e7;">(Partner)</span>
          </v-list-item-content>
          <v-list-item-content style="font-size: 16px;color: #1AB759;" class="text-center dot">
            <v-list-item-title v-text="toFixData(item.price)"></v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list-item-group>
    </v-list>
</v-card>
</v-col>
</v-row>
<BestListProduct />
<BestListUser />
 </div>
</template>
<script>
import eventBus from '@/components/eventBus'
export default {
  name: 'ApexChart',
  components: {
    BestListProduct: () => import(/* webpackPrefetch: true */ '@/components/Modal/bestListProduct'),
    BestListUser: () => import(/* webpackPrefetch: true */ '@/components/Modal/bestListUser')
  },
  data () {
    return {
      isMobile: false,
      imageNull: '../../assets/icons/nullproduct.png',
      items: [
        {
          icon: 'mdi-trophy-outline',
          text: 'Inbox'
        },
        {
          icon: 'mdi-trophy-outline',
          text: 'Star'
        },
        {
          icon: 'mdi-trophy-outline',
          text: 'Send'
        },
        {
          icon: 'mdi-trophy-outline',
          text: 'Drafts'
        }
      ],
      items2: [
        {
          icon: 'mdi-trophy-outline',
          text: 'Inbox'
        },
        {
          icon: 'mdi-trophy-outline',
          text: 'Star'
        },
        {
          icon: 'mdi-trophy-outline',
          text: 'Send'
        },
        {
          icon: 'mdi-trophy-outline',
          text: 'Drafts'
        }
      ],
      series: [],
      series2: [{
        name: 'Inflation',
        data: [2.3, 3.1, 4.0, 10.1, 4.0, 3.6, 3.2, 2.3, 1.4, 0.8]
      }],
      chartOptions: {},
      chartOptions2: {
        chart: {
          height: 350,
          type: 'bar'
        },
        plotOptions: {
          bar: {
            borderRadius: 10,
            dataLabels: {
              position: 'top' // top, center, bottom
            }
          }
        },
        dataLabels: {
          enabled: true,
          formatter: function (val) {
            return val + ' ชิ้น'
          },
          offsetY: -20,
          style: {
            fontSize: '12px',
            colors: ['#304758']
          }
        },
        xaxis: {
          categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
          position: 'top',
          axisBorder: {
            show: false
          },
          axisTicks: {
            show: false
          },
          crosshairs: {
            fill: {
              type: 'gradient',
              gradient: {
                colorFrom: '#D8E3F0',
                colorTo: '#BED1E6',
                stops: [0, 100],
                opacityFrom: 0.4,
                opacityTo: 0.5
              }
            },
            tooltip: {
              enabled: true
            }
          },
          yaxis: {
            axisBorder: {
              show: false
            },
            axisTicks: {
              show: false
            },
            labels: {
              show: false,
              formatter: function (val) {
                return val + '%'
              }
            }
          },
          title: {
            text: 'Monthly Inflation in Argentina, 2002',
            floating: true,
            offsetY: 330,
            align: 'center',
            style: {
              color: '#444'
            }
          }
        }
      }
    }
  },
  created () {
    this.UserMostBuy()
    this.initRank()
    eventBus.$on('appendData', this.appendData)
  },
  destroyed () {
    this.$EventBus.$off('appendData')
  },
  mounted () {
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    dataSum (e) {
      // console.log('EdataSum', e)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // Frequency () {
    //   if (this.$store.state.ModuleShop.stateSetCompo1) {
    //     return ''
    //   } else {
    //     return FrequencyUI
    //   }
    // }
    // seriesSum () {
    //   if (Object.values(this.series).length !== 0) {
    //     return this.series
    //   } else {
    //     return this.series2
    //   }
    // },
    // setchartOptions () {
    //   if (Object.values(this.chartOptions).length !== 0) {
    //     return this.chartOptions
    //   } else {
    //     return this.chartOptions2
    //   }
    // }
  },
  methods: {
    toFixData (data) {
      return data.toFixed(2)
    },
    async dialogDataProduct () {
      this.$EventBus.$emit('dialogDataProduct', this.items)
    },
    async dialogDataUser () {
      this.$EventBus.$emit('dialogDataUser', this.items2)
    },
    // async initChart () {
    //   await this.$store.dispatch('actionsProductBestSeller')
    //   var data = await this.$store.state.ModuleShop.stateProductBestSeller
    //   this.series = await [{
    //     name: '',
    //     data: data.map(e => { return e.total_sold_prize })
    //   }]
    //   this.chartOptions = await {
    //     chart: {
    //       height: 350,
    //       type: 'bar'
    //     },
    //     plotOptions: {
    //       bar: {
    //         borderRadius: 10,
    //         dataLabels: {
    //           position: 'top' // top, center, bottom
    //         }
    //       }
    //     },
    //     dataLabels: {
    //       enabled: true,
    //       formatter: function (val) {
    //         return val + ' ชิ้น'
    //       },
    //       offsetY: -20,
    //       style: {
    //         fontSize: '12px',
    //         colors: ['#304758']
    //       }
    //     },
    //     xaxis: {
    //       categories: data.map(e => { return e.product_name }),
    //       position: 'top',
    //       axisBorder: {
    //         show: false
    //       },
    //       axisTicks: {
    //         show: false
    //       },
    //       crosshairs: {
    //         fill: {
    //           type: 'gradient',
    //           gradient: {
    //             colorFrom: '#D8E3F0',
    //             colorTo: '#BED1E6',
    //             stops: [0, 100],
    //             opacityFrom: 0.4,
    //             opacityTo: 0.5
    //           }
    //         },
    //         tooltip: {
    //           enabled: true
    //         }
    //       },
    //       yaxis: {
    //         axisBorder: {
    //           show: false
    //         },
    //         axisTicks: {
    //           show: false
    //         },
    //         labels: {
    //           show: false,
    //           formatter: function (val) {
    //             return val + '%'
    //           }
    //         }
    //       },
    //       title: {
    //         text: 'Monthly Inflation in Argentina, 2002',
    //         floating: true,
    //         offsetY: 330,
    //         align: 'center',
    //         style: {
    //           color: '#444'
    //         }
    //       }
    //     }
    //   }
    //   console.log('ProductBestSeller', data)
    // },
    async initRank () {
      await this.$store.dispatch('actionsProductBestSeller')
      this.items = await this.$store.state.ModuleShop.stateProductBestSeller
    },
    async UserMostBuy () {
      await this.$store.dispatch('actionsUserMostBuyProduct')
      var userBuy = await this.$store.state.ModuleShop.stateUserMostBuyProduct
      this.items2 = userBuy.sort((a, b) => { return b.price - a.price })
    },
    async appendData () {
      // console.log('stateTimeline', this.$store.state.ModuleShop.stateTimeline)
      this.series = await [{ data: this.$store.state.ModuleShop.stateTimeline }]
      // console.log('series2', this.series2)
      // console.log('series', this.series)
    },
    getValues (e) {
      const as = e.map(x => { return { ...x } })
      return as
    },
    onResize () {
      if (window.innerWidth < 650) {
        this.isMobile = true
      } else {
        this.isMobile = false
      }
    }
  }
}
</script>
<style scoped>
::v-deep .apexcharts-xaxis-label {
display: inline-block;
    width: 80px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
.hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px dotted #ccc;
    margin: 1em 0;
    padding: 0;
}
.dot {
  display: inline-block;
    width: 80px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
</style>
