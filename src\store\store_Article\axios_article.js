import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async GetProductManageArticle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_product_article`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetProductManageArticleV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_product_article_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCategoryShopList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/categoryShopList`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddArticleV2Video (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/add_article_v2_video`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddArticleV2Image (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/add_article_v2_image`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListArticle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_article_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListArticleWithSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_article_with_seller_shop_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadFileToS3 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/upload_to_s3`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailArticle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_article`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditArticle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_article_with_seller_shop_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteArticle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_article`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeStatus (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_stutus_article`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListProductAritcle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_detail_product_in_article`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListProductAritcleV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_detail_product_in_article_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
