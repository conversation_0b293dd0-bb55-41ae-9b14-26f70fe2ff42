<template>
  <v-card width="100%" height="100%" elevation="0">
    <v-card-text class="px-1">
      <v-row dense>
        <v-col cols="12" md="4" :class="MobileSize ? 'pb-0' : ''">
          <v-row dense>
            <v-col cols="12" md="3" class="pt-3">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกร้านค้า :</span>
            </v-col>
            <v-col cols="12" md="8">
              <v-autocomplete v-model="shopID" :items="shopList" dense outlined item-text="shop_name" item-value="shop_id" placeholder="เลือกร้านค้า" @change="selectShop($event)"  style="z-index: 11 !important;"></v-autocomplete>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="8" :class="MobileSize ? 'mb-4' : ''">
          <v-row dense :justify="dataSelect !== 3 && dataSelect !== 4 ? 'center' : 'end'">
            <v-col cols="12" md="2" class="pt-3" v-if="dataSelect !== 3 && dataSelect !== 4">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">วันที่เริ่ม - สิ้นสุด</span>
            </v-col>
            <v-col cols="12" md="2" class="pt-3" v-else-if="dataSelect === 3">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกเดือน</span>
            </v-col>
            <v-col cols="12" md="2" class="pt-3" v-else-if="dataSelect === 4">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกปี</span>
            </v-col>
            <v-col cols="12" :md="dataSelect !== 3 && dataSelect !== 4 ? 7 : 4">
              <v-row dense v-if="dataSelect !== 3 && dataSelect !== 4">
                <!-- เลือกวันเริ่มต้น -->
                <v-col cols="5" md="5" class="px-0">
                  <v-dialog
                    ref="dialog"
                    v-model="modal"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="dateStart"
                        v-bind="attrs"
                        v-on="on"
                        readonly
                        outlined
                        :disabled="dataSelect === 5 ? true : false"
                        dense
                        placeholder="DD/MM/YYYY"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="pickerDateStart"
                      v-model="dateFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      reactive
                      no-title
                      @input="modal = false"
                      @change="getSelectDate(dateFormatted)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="getSelectDate(dateFormatted)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <v-col cols="2" md="1" :class="MobileSize ? 'pt-3 pl-6' : 'pt-3 pl-3'">-</v-col>
                <!-- เลือกวันสิ้นสุด -->
                <v-col cols="5" md="5" class="px-0">
                  <v-dialog
                    ref="dialog1"
                    v-model="modal1"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="dateEnd"
                        v-bind="attrs"
                        v-on="on"
                        :readonly="dataSelect === 2 ? true : false"
                        dense
                        :disabled="dataSelect === 5 ? true : dataSelect === 2 ? true : false"
                        placeholder="DD/MM/YYYY"
                        outlined
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="dateFormatted2"
                      ref="pickerDateEnd"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      @input="modal1 = false"
                      @change="getSelectDateEnd(dateFormatted2)"
                      :readonly="dataSelect === 2 ? true : false"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      :min="dateFormatted"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal1 = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        :disabled="dataSelect === 2 ? true : false"
                        color="primary"
                        @click="getSelectDateEnd(dateFormatted2)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
              <v-row dense v-else-if="dataSelect === 3" justify="center" class="pr-4">
                <!-- เลือกเดือน -->
                <v-col cols="12" class="px-0">
                  <v-dialog
                    ref="dialogMonth"
                    v-model="modalMonth"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="month"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        dense
                        placeholder="MMMM"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="pickerMonth"
                      v-model="MonthFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      reactive
                      @input="modalMonth = false"
                      @change="SaveMonth(MonthFormatted)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      min="1950-01-01"
                      @click:month="SaveMonth(MonthFormatted)"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modalMonth = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="SaveMonth(MonthFormatted)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
              <v-row dense v-else-if="dataSelect === 4" justify="center" class="pr-4">
                <!-- เลือกปี -->
                <v-col cols="12" class="px-0">
                  <v-dialog
                    ref="dialogYear"
                    v-model="modalYear"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="year"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        dense
                        placeholder="YYYY"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="picker"
                      v-model="YearFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      reactive
                      @input="modalYear = false"
                      @change="SaveYear(YearFormatted)"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      min="1950-01-01"
                      @click:year="SaveYear(YearFormatted)"
                    >
                      <!-- <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modalYear = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="SaveYear(YearFormatted)"
                      >
                        ตกลง
                      </v-btn> -->
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" class="px-0">
              <v-select
               v-model="selectFilter"
               :items="selectFilteritem"
               @change="getFilter($event)"
               item-value="id"
               item-text="name"
               dense
               outlined
               hide-details
               placeholder="เลือก"
               style="z-index: 11 !important;"
              >
              </v-select>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <!-- กราฟ -->
      <v-row dense style="z-index: -1;">
        <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;">
          <v-card-text>
            <div id='chart' class="mt-5">
              <apexchart type='line' height='400' :options='chartOptions' :series='series'></apexchart>
            </div>
          </v-card-text>
        </v-card>
      </v-row>
      <!-- ตารางข้อมูล -->
      <v-row dense class="mt-4">
        <v-col cols="12" md="12">
          <table class="display nowrap" id="example" cellpadding="0" cellspacing="0" style="width:100%">
            <thead>
              <tr>
                <th class="fontData">
                  รหัสร้านค้า
                </th>
                <th class="fontData">
                  ชื่อร้านค้า
                </th>
                <th class="fontData">
                  เลขที่ทำรายการชำระเงิน
                </th>
                <th class="fontData">
                  รหัสผู้ซื้อ
                </th>
                <th class="fontData">
                  ผู้ซื้อ
                </th>
                <th class="fontData">
                  วันที่ชำระเงิน
                </th>
                <th class="fontData">
                  มูลค่า
                </th>
                <th class="fontData">
                  สถานะการชำระเงิน
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in dataRes" :key="item.payment_transaction_number">
                <td >{{item.shop_id}}</td>
                <td class="dot">{{item.shop_name}}</td>
                <td >{{item.payment_transaction_number}}</td>
                <td >{{item.user_id}}</td>
                <td class="dot2">{{item.user_name }}</td>
                <!-- <td >{{ new Date(item.updated_at).toLocaleString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' }) }}</td> -->
                <td >{{ formatDateToShow(item.updated_at.substr(0, 10)) }}</td>
                <td >{{ Number(item.net_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}</td>
                <td >{{ item.transaction_status }}</td>
              </tr>
            </tbody>
          </table>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import 'datatables.net'
import 'datatables.net-dt/css/jquery.dataTables.min.css'
import 'jszip'
import 'datatables.net-buttons-dt'
import 'datatables.net-buttons-dt/css/buttons.dataTables.min.css'
import 'datatables.net-buttons/js/buttons.colVis'
import 'datatables.net-buttons/js/buttons.flash'
import 'datatables.net-buttons/js/buttons.html5'
import 'datatables.net-buttons/js/buttons.print'
import 'datatables.net-buttons/js/dataTables.buttons'
import 'datatables.net-responsive-dt'
import $ from 'jquery'
window.JSZip = require('jszip')
export default {
  name: 'ApexChart',
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      headerProps: {
        sortByText: 'เรียงตาม'
      },
      month: '',
      shopID: '',
      activePicker: null,
      modalMonth: false,
      modalYear: false,
      selectFilter: 0,
      dataSelect: 0,
      dataRes: [],
      shopList: [],
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      modal: false,
      modal1: false,
      series: [],
      dateGraph: [],
      dateStart: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateEnd: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      startDate: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      endDate: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      year: '',
      dateSelectFunctionOne: '',
      dateSelectFunctionTwo: '',
      FirstDateSelect: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      EndDateSelect: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      typeFilterForShop: '',
      MonthFormatted: null,
      YearFormatted: null,
      dateFormatted: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted2: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      selectFilteritem: [
        { id: 1, name: 'ไม่ระบุ' },
        { id: 2, name: 'รายวัน' },
        { id: 3, name: 'รายเดือน' },
        { id: 4, name: 'รายปี' },
        { id: 5, name: 'ทั้งหมด' }
      ]
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.setIconData()
  },
  updated () {
    $('#help').remove()
    this.setIconData()
    window.onresize = this.responsiveWidth ? 1193 : 1667
    // window.onload = (e) => {
    //   this.setIconData()
    // }
  },
  watch: {
    modalYear (val) {
      // console.log(val)
      val && this.$nextTick(() => (this.activePicker = 'YEAR'))
    },
    modalMonth (val) {
      val && this.$nextTick(() => (this.activePicker = 'MONTH'))
    },
    dialog (val) {
      val && this.$nextTick(() => (this.activePicker = 'DATE'))
    }
  },
  computed: {
    responsiveWidth () {
      return window.innerWidth < 1667
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    chartOptions () {
      return {
        chart: {
          height: 400,
          type: 'line',
          zoom: {
            enabled: true
          },
          toolbar: {
            show: true,
            tools: {
              zoom: true,
              zoomin: true,
              zoomout: true,
              download: this.IpadSize ? Boolean(false) : Boolean(true),
              selection: this.IpadSize ? Boolean(false) : Boolean(true),
              pan: this.IpadSize ? Boolean(false) : Boolean(true),
              reset: this.IpadSize ? Boolean(false) : Boolean(true)
            }
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          width: [2, 2, 2, 2],
          curve: 'straight',
          dashArray: [0, 0, 0, 0]
        },
        title: {
          text: 'กราฟข้อมูลการขายสินค้าทั้งหมดของร้าน',
          align: 'left',
          margin: this.MobileSize ? 60 : this.IpadSize ? 45 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        legend: {
          // tooltipHoverFormatter: function (val, opts) {
          //   return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
          // },
          position: 'top',
          horizontalAlign: 'left',
          offsetY: -5
        },
        markers: {
          size: 4,
          hover: {
            sizeOffset: 6
          }
        },
        yaxis: [
          {
            labels: {
              formatter: function (val) {
                return val.toFixed(0)
              }
            }
          }
        ],
        xaxis: {
          categories: this.dateGraph
          // labels: {
          //   formatter: function (val) {
          //     return val.toFixed(0)
          //   }
          // }
        },
        tooltip: {
          y: [
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            }
          ]
        },
        grid: {
          borderColor: '#f1f1f1'
        },
        colors: ['#27AB9C', '#1AB759', '#F5222D', '#1B5DD6', '#f50']
      }
    }
  },
  created () {
    var typefilter = ''
    this.$EventBus.$emit('CheckShopTopTen', this.shopID)
    this.$EventBus.$emit('getDataTop10buyers', this.startDate, this.endDate, typefilter, this.shopID)
    this.$EventBus.$emit('getDataTop10purchasers', this.startDate, this.endDate, typefilter, this.shopID)
    this.$EventBus.$emit('getDataShopTopTen', this.startDate, this.endDate, typefilter, this.shopID)
    this.getDataTransaction(this.startDate, this.endDate, typefilter, this.shopID)
  },
  methods: {
    setIconData () {
      var img = require('@/assets/icon_image/infoTransaction.png')
      // console.log('example_filter')
      // $('#example_filter').append('<i id="help" class="fa fa-exclamation-circle fa-lg stroke-transparent ml-2" style="color: #27AB9C;"></i>')
      $('#example_filter').append('<svg id="help" xmlns="http://www.w3.org/2000/svg" width="25" height="40" fill="currentColor" style="color: #27AB9C;" class="bi bi-exclamation-circle ml-2 pt-5" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/><path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/></svg>')
      $(`<div class="hide" style="position: absolute; z-index: 1; margin-left: 20vw" ><img src="${img}" width="182px" height="206px"/></div>`).appendTo('#example_filter')
      $('.hide').hide()
      // $('.hide').attr('src', require('./info.jpg'))
      // $('#help').hover(function () { $('.hide').show() }, function () { $('.hide').hide() })
      $(document).on('mouseenter', '#help', function (event) {
        $('.hide').show()
      }).on('mouseleave', '#help', function () {
        $('.hide').hide()
      })
      // $(#help).on({
      //   mouseenter: function () {
      //     $('.hide').show()
      //   },
      //   mouseleave: function () {
      //     $('.hide').hide()
      //   }
      // })
      // $('#help').hover(function () { console.log('hover!!!!') })
    },
    async selectShop (val) {
      // console.log(val)
      await this.$EventBus.$emit('CheckShopTopTen', val)
      await this.$EventBus.$emit('getDataTop10buyers', this.FirstDateSelect, this.EndDateSelect, this.typeFilterForShop, val)
      await this.$EventBus.$emit('getDataTop10purchasers', this.FirstDateSelect, this.EndDateSelect, this.typeFilterForShop, val)
      await this.$EventBus.$emit('getDataShopTopTen', this.FirstDateSelect, this.EndDateSelect, this.typeFilterForShop, val)
      await this.getDataTransactionReload(this.FirstDateSelect, this.EndDateSelect, this.typeFilterForShop, val)
    },
    async getDataTransaction (startDate, endDate, type, shopID) {
      // this.$store.commit('openLoader')
      this.dataRes = []
      this.series = []
      this.dateGraph = []
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      await this.$store.dispatch('actionsTransaction', data)
      var response = await this.$store.state.ModuleAdminPanit.stateTransaction
      // console.log('actionsTransaction', response)
      if (response.result === 'OK') {
        this.dataRes = await response.data.transection
        this.series = await response.data.series
        this.shopList = await response.shopList
        this.shopList.push({
          shop_id: '',
          shop_name: 'ทั้งหมด'
        })
        var dataDateGraph = await response.data.date
        // console.log('dataDateGraph', typeof dataDateGraph)
        for (var i = 0; i < dataDateGraph.length; i++) {
          // console.log(new Date(dataDateGraph[i]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          this.dateGraph.push(new Date(dataDateGraph[i]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        }
        // console.log(this.dataRes)
      }
      var imgSuccess = require('./sucess.png')
      var imgCancle = require('./Cancle.png')
      var imgNot = require('./not.png')
      var imgNotpaid = require('./notpaid.png')
      await $('#example').DataTable({
        dom: 'Bfrtip',
        destroy: true,
        responsive: {
          details: {
            display: $.fn.dataTable.Responsive.display.childRowImmediate
          }
        },
        // responsive: true,
        language: {
          Search: '',
          searchPlaceholder: 'ค้นหา',
          emptyTable: 'ไม่มีข้อมูลจำนวน Transaction ในตาราง',
          zeroRecords: 'ไม่พบข้อมูล Transaction ในตาราง'
        },
        order: [
          [5, 'desc']
        ],
        oLanguage: {
          sSearch: ''
        },
        columnDefs: [
          {
            targets: -1,
            render: function (data, type, row, meta) {
              if (data === 'Success') {
                return '<img src="' + imgSuccess + '" height="30" width="120"/>'
              } else if (data === 'Fail') {
                return '<img src="' + imgNot + '" height="30" width="120"/>'
              } else if (data === 'Cancel') {
                return '<img src="' + imgCancle + '" height="30" width="70"/>'
              } else {
                return '<img src="' + imgNotpaid + '" height="30" width="120"/>'
              }
            }
          }
        ],
        columns: [
          { data: 'shop_id' },
          { data: 'shop_name' },
          { data: 'payment_transaction_number' },
          { data: 'user_id' },
          { data: 'user_name' },
          { data: 'updated_at' },
          { data: 'net_price' },
          { data: 'transaction_status' }
        ],
        buttons: [
          {
            extend: 'csv',
            text: '<i class="fa fa-file"></i> Csv',
            charset: 'utf-8',
            extension: '.csv',
            filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            bom: true
          },
          {
            extend: 'copy',
            text: '<i class="fa fa-copy"></i> Copy',
            titleAttr: 'Copy'
          },
          {
            extend: 'excel',
            text: ' <i class="fa fa-file-excel"></i> Excel',
            exportOptions: {
              columns: [0, 1, 2, 3, 4, 5, 6, 7],
              format: {
                body: function (data, row, column, node) {
                  var cellData
                  cellData = data.toString().indexOf('<') < 0 ? data : $(data).text()
                  var transaction
                  if (column === 7) {
                    // console.log(data.split('.png')[0])
                    if (data.split('.png')[0] === '<img src="/img/notpaid.0ecee66c') {
                      transaction = 'ยังไม่ชำระเงิน'
                    } else if (data.split('.png')[0] === '<img src="/img/sucess.ca1f6e42') {
                      transaction = 'ชำระเงินสำเร็จ'
                    } else if (data.split('.png')[0] === '<img src="/img/not.16575473') {
                      transaction = 'ชำระเงินไม่สำเร็จ'
                    } else {
                      transaction = 'ยกเลิก'
                    }
                  }
                  return column === 7 ? transaction : cellData
                }
              }
            }
          },
          {
            extend: 'print',
            text: '<i class="fa fa-print"></i> Print',
            titleAttr: 'Print'
          }
        ]
      })
      this.$store.commit('closeLoader')
    },
    async getDataTransactionReload (startDate, endDate, type, shopID) {
      this.$store.commit('openLoader')
      this.dataRes = []
      this.series = []
      this.dateGraph = []
      var dataDateFilterReload = ''
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      await this.$store.dispatch('actionsTransaction', data)
      var response = await this.$store.state.ModuleAdminPanit.stateTransaction
      // console.log('actionsTransaction', response)
      if (response.result === 'OK') {
        this.series = await response.data.series
        if (type === 'year') {
          dataDateFilterReload = await response.data.date
          for (var k = 0; k < dataDateFilterReload.length; k++) {
            this.dateGraph.push(new Date(dataDateFilterReload[k]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long' }))
          }
          // this.dateGraph = await response.data.date
        } else if (type === 'all') {
          dataDateFilterReload = await response.data.date
          for (var j = 0; j < dataDateFilterReload.length; j++) {
            this.dateGraph.push(new Date(dataDateFilterReload[j]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long' }))
          }
          // this.dateGraph = await response.data.date
        } else {
          dataDateFilterReload = await response.data.date
          for (var i = 0; i < dataDateFilterReload.length; i++) {
            this.dateGraph.push(new Date(dataDateFilterReload[i]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          }
        }
        this.dataRes = await response.data.transection.map(x => {
          return {
            shop_id: x.shop_id,
            shop_name: x.shop_name,
            payment_transaction_number: x.payment_transaction_number,
            user_id: x.user_id,
            user_name: x.user_name,
            // updated_at: x.updated_at !== '' ? new Date(x.updated_at).toLocaleString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' }) : '',
            updated_at: x.updated_at !== '' ? this.formatDateToShow(x.updated_at.substr(0, 10)) : '',
            // updated_at: `<span>${new Date(x.updated_at).toLocaleString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' })}</span>`,
            net_price: parseFloat(x.net_price).toFixed(2),
            transaction_status: x.transaction_status
          }
        })
        // console.log(this.dataRes)
      }
      var imgSuccess = require('./sucess.png')
      var imgCancle = require('./Cancle.png')
      var imgNot = require('./not.png')
      var imgNotpaid = require('./notpaid.png')
      await $('#help').remove()
      var table = await $('#example').DataTable({
        dom: 'Bfrtip',
        destroy: true,
        responsive: {
          details: {
            display: $.fn.dataTable.Responsive.display.childRowImmediate
          }
        },
        // responsive: true,
        language: {
          Search: '',
          searchPlaceholder: 'ค้นหา',
          emptyTable: 'ไม่มีข้อมูลจำนวน Transaction ในตาราง',
          zeroRecords: 'ไม่พบข้อมูล Transaction ในตาราง'
        },
        order: [
          [5, 'desc']
        ],
        oLanguage: {
          sSearch: ''
        },
        columnDefs: [
          {
            targets: 7,
            render: function (data, type, row, meta) {
              if (data === 'Success') {
                return '<img src="' + imgSuccess + '" height="30" width="120"/>'
              } else if (data === 'Fail') {
                return '<img src="' + imgNot + '" height="30" width="120"/>'
              } else if (data === 'Cancel') {
                return '<img src="' + imgCancle + '" height="30" width="70"/>'
              } else {
                return '<img src="' + imgNotpaid + '" height="30" width="120"/>'
              }
            }
          },
          {
            targets: 1,
            render: function (data) {
              return '<div style="white-space: nowrap; display: inline-block; width: 100px; overflow: hidden; text-overflow: ellipsis;">' + data + '</div>'
              // $(cell).addClass('dot2')
              // $(cell).css({ display: 'inline-block', width: '100px', whiteSpace: 'nowrap', overflow: 'hidden !important', textOverflow: 'ellipsis' })
              // $('<style>{ display: inline-block; width: 200px; white-space: nowrap; overflow: hidden !important; text-overflow: ellipsis; }</style>').appendTo('dot2')
            }
          },
          // {
          //  targets: 4,
          //  createdCell: function (cell, cellData) {
          // $(cell).addClass('dot2')
          // $(cell).css({ display: 'inline-block', width: '200px', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' })
          // $('<style>{ display: inline-block; width: 200px; white-space: nowrap; overflow: hidden !important; text-overflow: ellipsis; }</style>').appendTo('dot2')
          //   }
          // },
          {
            targets: 4,
            render: function (data) {
              return '<div style="white-space: nowrap; display: inline-block; width: 200px; overflow: hidden; text-overflow: ellipsis;">' + data + '</div>'
            }
          }
        ],
        columns: [
          { data: 'shop_id' },
          { data: 'shop_name' },
          { data: 'payment_transaction_number' },
          { data: 'user_id' },
          { data: 'user_name' },
          { data: 'updated_at' },
          { data: 'net_price' },
          { data: 'transaction_status' }
        ],
        buttons: [
          {
            extend: 'csv',
            text: '<i class="fa fa-file"></i> Csv',
            charset: 'utf-8',
            extension: '.csv',
            filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            bom: true
          },
          {
            extend: 'copy',
            text: '<i class="fa fa-copy"></i> Copy',
            titleAttr: 'Copy'
          },
          {
            extend: 'excel',
            text: ' <i class="fa fa-file-excel"></i> Excel',
            exportOptions: {
              columns: [0, 1, 2, 3, 4, 5, 6, 7],
              format: {
                body: function (data, row, column, node) {
                  var cellData
                  cellData = data.toString().indexOf('<') < 0 ? data : $(data).text()
                  var transaction
                  if (column === 7) {
                    // console.log(data.split('.png')[0])
                    if (data.split('.png')[0] === '<img src="/img/notpaid.0ecee66c') {
                      transaction = 'ยังไม่ชำระเงิน'
                    } else if (data.split('.png')[0] === '<img src="/img/sucess.ca1f6e42') {
                      transaction = 'ชำระเงินสำเร็จ'
                    } else if (data.split('.png')[0] === '<img src="/img/not.16575473') {
                      transaction = 'ชำระเงินไม่สำเร็จ'
                    } else {
                      transaction = 'ยกเลิก'
                    }
                  }
                  return column === 7 ? transaction : cellData
                }
              }
            }
          },
          {
            extend: 'print',
            text: '<i class="fa fa-print"></i> Print',
            titleAttr: 'Print'
          }
        ]
      })
      await table.clear().rows.add(this.dataRes).draw()
      await table.columns.adjust().responsive.recalc()
      await this.setIconData()
      await this.$store.commit('closeLoader')
    },
    SaveMonth (val) {
      // console.log(val)
      this.$refs.dialogMonth.save(val)
      this.$refs.pickerMonth.activePicker = 'MONTH'
      // var getMonth = new Date(this.MonthFormatted)
      // const month = ['January', 'February', 'March', 'April', 'May', 'June', 'July', 'August', 'September', 'October', 'November', 'December']
      var monthCurrent = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 7)
      // console.log(monthCurrent)
      this.month = new Date(this.MonthFormatted).toISOString().substr(0, 7)
      // console.log(this.month)
      var SelectMonth = new Date(this.MonthFormatted)
      var firstDateMonth = this.dateFormat(new Date(new Date(SelectMonth.getFullYear(), SelectMonth.getMonth(), 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var lastDateMonth = ''
      if (monthCurrent !== this.month) {
        lastDateMonth = this.dateFormat(new Date(new Date(SelectMonth.getFullYear(), SelectMonth.getMonth() + 1, 0) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      } else {
        lastDateMonth = this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd')
      }
      // console.log(lastDateMonth)
      this.FirstDateSelect = firstDateMonth
      this.EndDateSelect = lastDateMonth
      var typefilter = ''
      this.typeFilterForShop = ''
      // console.log(firstDateMonth, lastDateMonth)
      this.$EventBus.$emit('getDataTop10buyers', firstDateMonth, lastDateMonth, typefilter, this.shopID)
      this.$EventBus.$emit('getDataTop10purchasers', firstDateMonth, lastDateMonth, typefilter, this.shopID)
      this.$EventBus.$emit('getDataShopTopTen', firstDateMonth, lastDateMonth, typefilter, this.shopID)
      this.getDataTransactionReload(firstDateMonth, lastDateMonth, typefilter, this.shopID)
      this.modalMonth = false
    },
    SaveYear (val) {
      this.$refs.dialogYear.save(val)
      this.$refs.picker.activePicker = 'YEAR'
      this.year = new Date(this.YearFormatted).toISOString().substr(0, 4)
      var currentDate = new Date(this.YearFormatted)
      var firstDateYear = this.dateFormat(new Date(new Date(currentDate.getFullYear(), 0, 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var LastDateYear = this.dateFormat(new Date(new Date(currentDate.getFullYear(), 11, 31) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      this.FirstDateSelect = firstDateYear
      this.EndDateSelect = LastDateYear
      var typefilter = 'year'
      this.typeFilterForShop = 'year'
      // console.log(firstDateYear, LastDateYear)
      this.$EventBus.$emit('getDataTop10buyers', firstDateYear, LastDateYear, typefilter, this.shopID)
      this.$EventBus.$emit('getDataTop10purchasers', firstDateYear, LastDateYear, typefilter, this.shopID)
      this.$EventBus.$emit('getDataShopTopTen', firstDateYear, LastDateYear, typefilter, this.shopID)
      this.getDataTransactionReload(firstDateYear, LastDateYear, typefilter, this.shopID)
      this.modalYear = false
    },
    getSelectDateEnd (val) {
      this.$refs.dialog1.save(val)
      this.dateSelectFunctionTwo = ''
      this.dateSelectFunctionTwo = new Date(this.dateFormatted2)
      this.dateEnd = this.formatDate(new Date(this.dateSelectFunctionTwo.setDate(this.dateSelectFunctionTwo.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      var startSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var endDate = this.dateFormat(new Date(this.dateSelectFunctionTwo.setDate(this.dateSelectFunctionTwo.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      this.FirstDateSelect = startSelect
      this.EndDateSelect = endDate
      var typefilter = ''
      this.typeFilterForShop = ''
      this.$EventBus.$emit('getDataTop10buyers', startSelect, endDate, typefilter, this.shopID)
      this.$EventBus.$emit('getDataTop10purchasers', startSelect, endDate, typefilter, this.shopID)
      this.$EventBus.$emit('getDataShopTopTen', startSelect, endDate, typefilter, this.shopID)
      this.getDataTransactionReload(startSelect, endDate, typefilter, this.shopID)
      this.modal1 = false
    },
    async getSelectDate (val) {
      this.$refs.dialog.save(val)
      this.$refs.pickerDateStart.activePicker = 'DATE'
      this.dateStart = ''
      this.dateEnd = ''
      this.dateSelectFunctionOne = ''
      if (this.dataSelect === 1) {
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      } else if (this.dataSelect === 2) {
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        this.dateFormatted2 = new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.dateEnd = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        var startSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
        var endSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
        this.FirstDateSelect = startSelect
        this.EndDateSelect = endSelect
        var typefilter = ''
        this.typeFilterForShop = ''
        await this.$EventBus.$emit('getDataTop10buyers', startSelect, endSelect, typefilter, this.shopID)
        await this.$EventBus.$emit('getDataTop10purchasers', startSelect, endSelect, typefilter, this.shopID)
        await this.$EventBus.$emit('getDataShopTopTen', startSelect, endSelect, typefilter, this.shopID)
        await this.getDataTransactionReload(startSelect, endSelect, typefilter, this.shopID)
      } else if (this.dataSelect === 3) {
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      } else if (this.dataSelect === 4) {
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      } else if (this.dataSelect === 5) {
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      } else if (this.dataSelect === 0) {
        // console.log('เข้าเงื่อนไข')
        this.dateSelectFunctionOne = new Date(this.dateFormatted)
        this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      }
      this.modal = false
    },
    dateFormat (inputDate, format) {
      // parse the input date
      const date = new Date(inputDate)
      // console.log(date)
      // extract the parts of the date
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      // replace the month
      format = format.replace('MM', month.toString().padStart(2, '0'))
      // replace the year
      if (format.indexOf('yyyy') > -1) {
        format = format.replace('yyyy', year.toString())
      } else if (format.indexOf('yy') > -1) {
        format = format.replace('yy', year.toString().substr(2, 2))
      }
      // replace the day
      format = format.replace('dd', day.toString().padStart(2, '0'))
      return format
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    async getFilter (valSelect) {
      // console.log(valSelect)
      this.setIconData()
      this.dateStart = ''
      this.dateEnd = ''
      this.dateFormatted = ''
      this.dateFormatted2 = ''
      // var date = new Date()
      if (valSelect === 1) {
        this.dataSelect = valSelect
        this.month = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.year = ''
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        // this.dateFormatted = ''
        // console.log(this.dateStart)
        // console.log(this.formatDate(new Date(date.setDate(date.getDate() + 3) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)))
        // if (this.dateStart !== '') {
        //   this.dateFormatted2 = new Date(date.setDate(date.getDate(this.dateStart) + 3) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        // }
        await $('#help').remove()
      } else if (valSelect === 2) {
        this.dataSelect = valSelect
        this.dateFormatted = ''
        this.dateFormatted2 = ''
        this.month = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.year = ''
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        await $('#help').remove()
      } else if (valSelect === 3) {
        this.dataSelect = valSelect
        this.month = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted2 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        await $('#help').remove()
      } else if (valSelect === 4) {
        this.dataSelect = valSelect
        this.year = ''
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.dateFormatted2 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        await $('#help').remove()
      } else if (valSelect === 5) {
        this.dataSelect = valSelect
        this.MonthFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.YearFormatted = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var startSelect = ''
        var endSelect = ''
        this.FirstDateSelect = startSelect
        this.EndDateSelect = endSelect
        var typefilter = 'all'
        this.typeFilterForShop = ''
        await this.$EventBus.$emit('getDataTop10buyers', startSelect, endSelect, typefilter, this.shopID)
        await this.$EventBus.$emit('getDataTop10purchasers', startSelect, endSelect, typefilter, this.shopID)
        await this.$EventBus.$emit('getDataShopTopTen', startSelect, endSelect, typefilter, this.shopID)
        await this.getDataTransactionReload(startSelect, endSelect, typefilter, this.shopID)
      }
    },
    getStatus (item) {
      if (item === 'Success') return 'ชำระเงินสำเร็จ'
      else if (item === 'Not Paid') return 'ยังไม่ชำระเงิน'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else return 'ยกเลิก'
    },
    getColorText (item) {
      if (item === 'Success') return '#1AB759'
      else if (item === 'Not Paid') return '#1B5DD6'
      else if (item === 'Fail') return '#E9A016'
      else return '#F5222D'
    },
    getColorBack (item) {
      if (item === 'Success') return 'rgba(236, 248, 234, 0.8)'
      else if (item === 'Not Paid') return 'rgba(245, 249, 255, 0.8)'
      else if (item === 'Fail') return '#FCF0DA'
      else return 'rgba(251, 229, 228, 0.8)'
    }
  }
}
</script>

<style scoped="scss">
@media only screen and (max-width: 650px) {
  .table-striped {
    display: none;
  }
  ::v-deep .dataTables_paginate.paging_simple_numbers {
    display: none;
  }
  ::v-deep #example_filter.dataTables_filter {
    display: none;
  }
}
@media only screen and (min-width: 750px) {
  ::v-deep .mobile {
    display: none;
  }
}
::v-deep .notpaid {
  background-image: url('notpaid.png');
  background-position: center center;
  background-size: 70%;
  z-index: 1;
  background-repeat: no-repeat;
  color: #ffffff00;

}

::v-deep .successA {
  background-image: url('sucess.png');
  background-position: center center;
  background-size: 70%;
  z-index: 1;
  background-repeat: no-repeat;
  color: #ffffff00;
}

::v-deep .fail {
  background-image: url('not.png');
  background-position: center center;
  background-size: 70%;
  z-index: 1;
  background-repeat: no-repeat;
  color: #ffffff00;
}

::v-deep .cancelA {
  background-image: url('Cancle.png');
  background-position: center center;
  background-size: 40%;
  z-index: 1;
  background-repeat: no-repeat;
  color: #ffffff00;
}

::v-deep #example_info {
  display: none;
}
::v-deep .dataTables_filter {
  margin-bottom: 1em;
}
::v-deep .dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    width: 331px;
    background-color: transparent;
    margin-left: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABmJLR0QA/wD/AP+gvaeTAAABs0lEQVQ4ja3TTWsTURjF8f+ZTMUwLV10VSgVVBhDt4KrutB2aYVgBqSC4LQ7v4IbBf0KlmYErYJMJpZ+h4KLrgOm1IVvq+IqaqPNzOMiddFwk2L1LO9z+XEuPBf+UzRsEG4lE6XDfDYv9KPN5EeiKP8rKMySy5g9BBYB/+h436CuM3rSvhl3XJB3DGkky5htA188z7vy81up7BtTSPdl3LBf9rbSXJse2eioyTYobtfiV4MXZ9K0HNDZBBtv1+J5JHM3MnsEbLgQgM9RdCDfX0bMhVl9yfm0cCuZABZk9tSF/Mm76t2vgoag6oRKh/ks4He/+61RUL84LUPnnVBP/gHA2UkvOAmSFMis64R28/EPwL4VvesnNsKuFdKOEyKKckkJhT2YSdPyMKSSri8CV8eMZ24IsDEeS3gBnc1Lb55PuZBCeg2WtqKVvcH5sc2uNNemi7zUQMyZlIqiJVNgaAFsHiwF1ZDF7VurL4dC/WqmMKsvCapmuoDoStopFZa0opW9MFu/gykZxIZ+2lEJsyTCbMPQ6m4tfnFqqI/1m/V63sX3t+99Oq0DQKVZP/dPgCu/AanyqVoXnFHTAAAAAElFTkSuQmCC) !important;
    background-position: 570px 10px !important;
    outline: transparent;
    border: 1px solid #cacaca;
    border-radius: 6px;
  }
  ::v-deep .input {
    display: inline-block;
    white-space: nowrap;
  }
  ::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
}
::v-deep #example {
  padding-top: 12px;
}
::v-deep .sorting {
  color: #e6f5f3;
  background-color: #e6f5f3;
}
::v-deep table.dataTable tbody td.sorting_1 {
  text-align: center;
}
::v-deep #example > thead > tr > th {
  background-color: #e6f5f3;
  color: #27AB9C;
  font-size: 12px;
}
::v-deep table.dataTable th, table.dataTable td  {
  /* border-bottom: 1px solid #e7e7e7; */
}
::v-deep div.dt-buttons {
  float: right;
 }
 ::v-deep .dataTables_wrapper .dataTables_filter  {
  float: left;
 }
  .fontData {
    font-size: 14px;
  }
  .tableLong {
    width: 200px;
  }
  .container {
    margin-right: 25px;
  }
  .sec {
    width: 30vw !important;
  }
  .dot {
    display: inline-block;
    width: 100px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
  }
.hiDe {
  display: none;
}
.dot2 {
    display: inline-block;
    width: 200px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  top: 50%;
  left: 5px;
  height: 1em;
  width: 1em;
  margin-top: -9px;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control {
  padding-left: 27px;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control:before {
  left: 4px;
  height: 14px;
  width: 14px;
  border-radius: 14px;
  line-height: 14px;
  text-indent: 3px;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control,
table.dataTable.dtr-column > tbody > tr > th.dtr-control,
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  top: 50%;
  left: 50%;
  height: 0.8em;
  width: 0.8em;
  margin-top: -0.5em;
  margin-left: -0.5em;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable > tbody > tr.child {
  padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}
table.dataTable > tbody > tr.child ul.dtr-details {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li {
  /* border-bottom: 1px solid #efefef; */
  padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
  padding-top: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
  border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}
div.dtr-modal {
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 50%;
  overflow: auto;
  margin: auto;
  z-index: 102;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: 0.5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 1em;
}
div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  border: 1px solid #eaeaea;
  background-color: #f9f9f9;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}
div.dtr-modal div.dtr-modal-close:hover {
  background-color: #eaeaea;
}
div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.6);
}
</style>
