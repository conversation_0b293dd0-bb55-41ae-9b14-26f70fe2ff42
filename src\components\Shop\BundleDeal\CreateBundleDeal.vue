<template>
  <v-container :class="MobileSize ? 'background_productMobile my-4' : 'background_product pa-6'" style="background-color: #FFFFFF">
      <v-row>
        <v-col>
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">สร้าง Bundle Deal</span>
        </v-col>
      </v-row>
    <v-form ref="FormManageDiscount">
      <v-row dense class="mt-4">
        <v-col cols="12"><span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">ข้อมูลเบื้องต้น</span></v-col>
        <v-col cols="12" md="12">
          <span class="detail1">ชื่อโปรโมชั่น <span style="color:#F5222D">*</span></span>
          <v-text-field v-model="bundleName" :rules="Rules.empty" placeholder="ระบุชื่อโปรโมชั่น" @keypress="CheckSpacebar($event)" outlined dense></v-text-field>
        </v-col>
        <v-col cols="12" class="pt-0">
          <span class="detail1">รายละเอียดโปรโมชั่น</span>
          <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="bundleDescription" @ready="onReady"></ckeditor>
          <span class="rule">* รายละเอียดโปรโมชั่น วิธีใช้งานและสิทธิประโยชน์สำหรับสมาชิกอย่างละเอียด</span>
        </v-col>
      </v-row>
      <div class="mt-4">
        <v-row dense class="mt-3">
          <v-col cols="12" md="12">
            <span class="detail1">จำนวนที่ใช้ได้ต่อคน <span style="color:#F5222D">*</span></span>
            <v-text-field v-model="amonutCap" :maxLength="7" placeholder="จำนวนคูปองที่ใช้ได้ต่อคน" @input="checkZero('usecap')" :rules="Rules.amonutCap" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
          </v-col>
        </v-row>
      </div>

      <div class="mt-4">
        <v-row class="">
          <v-col cols="12">
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">กำหนดระยะเวลาโปรโมชั่น</span>
          </v-col>
        </v-row>
        <div>
          <span class="detail1">ระยะเวลาใช้งานโปรโมชั่น<span style="color:#F5222D">*</span></span>
          <v-row dense style="align-items: center;">
            <v-col cols="2" v-if="!MobileSize"><span class="detail1">วันที่เริ่ม - สิ้นสุด</span></v-col>
            <v-col cols="4" v-if="MobileSize"><span class="detail1">วันที่เริ่ม</span></v-col>
            <!-- use start -->
            <div>
            <v-dialog
              ref="dialogStartDate2"
              v-model="dialogStartDate2"
              :return-value.sync="date21"
              width="290px"
              persistent
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentStartDate2"
                  v-bind="attrs"
                  placeholder="วว/ดด/ปป"
                  outlined
                  readonly
                  dense
                  v-on="on"
                  :disabled="disableddate21"
                  :rules="Rules.empty"
                  @click="date22 = '', time22 = '', sentEndDate2 = ''"
                >
                  <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="date21"
                scrollable
                reactive
                locale="TH-th"
                :min="date11 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                @change="time21 = ''"
              >
                <v-row dense>
                  <v-col cols="12" class="pt-0">
                    <v-col cols="12" class="pt-0">
                      <a-time-picker
                        v-model="time21"
                        :bordered="false"
                        style="width: 100%;"
                        format="HH:mm:ss น."
                        valueFormat="HH:mm:ss"
                        size="large"
                        placeholder="00.00.00 น."
                        :disabled="date21 === ''"
                        :placement="'topLeft'"
                        :disabledHours="disabledHoursUse"
                        :disabledMinutes="disabledMinutesUse"
                        :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                      />
                    </v-col>
                  </v-col>
                  <v-col cols="12" align="end">
                    <v-btn text color="primary" @click="dialogStartDate2 = false, date21 === '' ? time21 = '' : ''" > ยกเลิก </v-btn>
                    <v-btn text color="primary" :disabled="date21 == '' || time21 == ''" @click="setValueDate(date21, 'date21'), $refs.dialogStartDate2.save(date21), date22 = '', sentEndDate2 = '', time22 = ''"> บันทึก</v-btn>
                  </v-col>
                </v-row>
              </v-date-picker>
            </v-dialog>
            </div>
            <span class="detail1 mx-4 mb-10" v-if="!noEndDateUse && !MobileSize"> - </span>
            <v-col cols="4" v-if="!noEndDateUse && MobileSize"><span class="detail1">วันที่สิ้นสุด</span></v-col>
            <!-- use end -->
            <div v-if="!noEndDateUse">
            <v-dialog
              ref="dialogEndDate2"
              v-model="dialogEndDate2"
              :return-value.sync="date22"
              width="290px"
              persistent
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentEndDate2"
                  v-bind="attrs"
                  placeholder="วว/ดด/ปป"
                  outlined
                  readonly
                  dense
                  v-on="on"
                  :disabled="sentStartDate2 === '' || disableddate22"
                  :rules="noEndDateUse ? [] : Rules.datesMustNotBeSame2"
                >
                  <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="date22"
                scrollable
                reactive
                locale="TH-th"
                :min="date21 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                @change="time22 = ''"
              >
              <v-row dense>
                  <v-col cols="12" class="pt-0">
                    <v-col cols="12" class="pt-0">
                      <a-time-picker
                        v-model="time22"
                        :bordered="false"
                        style="width: 100%;"
                        format="HH:mm:ss น."
                        valueFormat="HH:mm:ss"
                        size="large"
                        placeholder="00.00.00 น."
                        :disabled="date22 === ''"
                        :placement="'topLeft'"
                        :disabledHours="disabledHoursRangeUse"
                        :disabledMinutes="disabledMinutesRangeUse"
                        :disabledSeconds="disabledSecondsRangeUse"
                        :defaultOpenValue="defaultDate"
                        :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                      />
                    </v-col>
                  </v-col>
                  <v-col cols="12" align="end">
                    <v-btn text color="primary" @click="dialogEndDate2 = false, date22 === '' ? time22 = '' : ''" > ยกเลิก </v-btn>
                    <v-btn text color="primary" :disabled="date22 == '' || time22 == ''" @click="setValueDate(date22, 'date22'), $refs.dialogEndDate2.save(date22)"> บันทึก</v-btn>
                  </v-col>
                </v-row>
              </v-date-picker>
            </v-dialog>
            </div>
          </v-row>
        </div>
      </div>
      <div class="mt-6">
        <span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">การใช้โปรโมชัน</span>
        <v-row dense>
          <v-col cols="12">
          <span class="detail1">ประเภทโปรโมชัน <span style="color:#F5222D">* (เลือกได้ 1 รูปแบบ)</span></span>
            <v-radio-group v-model="discountType" dense  class="detail1 ma-0">
              <v-col>
                <v-row class="d-flex align-center">
                  <v-radio label="ส่วนลดเป็นจำนวนเงิน" value="baht"  @click="discountAmountPercent = '', discount_maximum = null, discountMaximumType = 'haveCapNot'"></v-radio>
                  <v-text-field class="textDiscount ml-2" v-if="discountType === 'baht'" :rules="discountType === 'baht' ? Rules.minimumRule : []" v-model="discountAmount" @input="checkZero(discountType)" suffix="บาท" style="max-width: 250px;" outlined dense placeholder="ระบุส่วนลดที่ต้องการ" :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-row>
              </v-col>
              <v-col class="pa-0">
                <v-row no-gutters>
                  <v-radio label="ส่วนลดเป็นเปอร์เซ็นต์ (%)" value="percent" @click="discountAmount = ''"></v-radio>
                  <v-text-field class="textDiscount ml-2" v-if="discountType === 'percent'" :rules="discountType === 'percent' ? Rules.maxMore : []" :key="component" v-model="discountAmountPercent" @input="checkZero(discountType)" suffix="%" style="max-width: 200px;" placeholder="ระบุส่วนลดที่ต้องการ" outlined dense :maxLength="2" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-row>
              </v-col>
              <v-row align="center" v-if="discountType === 'percent'" no-gutters class="pl-6">
                <v-radio-group v-model="discountMaximumType" dense v-if="discountType === 'percent'" class="detail1 ma-0">
                  <v-col :class="!MobileSize ? 'pa-0 d-flex textDiscount' : 'pa-0'" style="margin-top: -1vw;">
                    <v-radio label="จำกัดจำนวนเงิน" value="haveCap"></v-radio>
                    <v-text-field v-if="discountMaximumType === 'haveCap'" :rules="discountMaximumType === 'haveCap' ? Rules.maxMore : []" v-model="discount_maximum" placeholder="ระบุจำนวนเงิน" suffix="บาท" class="ml-2" style="max-width: 200px;" outlined dense :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col class="pa-0">
                    <v-radio label="ไม่จำกัดจำนวนเงิน" @click="discount_maximum = null" value="haveCapNot"></v-radio>
                  </v-col>
                </v-radio-group>
              </v-row>
            </v-radio-group>
          </v-col>
        </v-row>
      </div>
      <v-row :justify="MobileSize ? 'center' : 'end'" :align="MobileSize ? 'center' : 'end'" dense class="mt-10 mb-4">
        <v-col cols="12" md="12">
          <v-row :justify="MobileSize ? 'center' : 'end'" class="pr-3">
            <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="canCel()">ยกเลิก</v-btn>
            <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="createCouponPlatform()" :disabled="isLoading">ยืนยัน</v-btn>
          </v-row>
        </v-col>
      </v-row>
    </v-form>
  </v-container>
</template>

<script>
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
import { TimePicker } from 'ant-design-vue'
import dayjs from 'dayjs'
export default {
  components: {
    'a-time-picker': TimePicker
    // 'a-time-range-picker': TimePicker.RangePicker
  },
  data () {
    return {
      sentStartDate2: '',
      sentEndDate2: '',
      disableddate21: false,
      disableddate22: false,
      isLoading: false,
      discountType: '',
      noEndDateUse: false,
      dialogStartDate2: false,
      dialogEndDate2: false,
      time11: '',
      time12: '',
      time21: '',
      time22: '',
      date11: '',
      date12: '',
      date21: '',
      date22: '',
      amonutCap: '',
      bundleDescription: '',
      defaultDate: dayjs('00:00:00', 'HH:mm:ss'),
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'blockquote',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      },
      bundleName: '',
      Rules: {
        amonutCap: [v => !!v || 'กรุณากรอกข้อมูล',
          v => (parseInt(v) <= parseInt(this.amonutUse)) || 'จำนวนคูปองที่ใช้ได้ต่อคนไม่ควรมากกว่าจำนวนคูปองสูงสุด'
        ],
        fourand15: [v => v.length >= 4 || 'กรุณากรอกข้อมูล'],
        datesMustNotBeSame1: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        datesMustNotBeSame2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate2 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        StartDate1MustNotBeSameStartDate2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'ระยะเวลาใช้งานและระยะเวลาเก็บ เวาเชอร์ไม่ควรเป็นวัน/เวลาเดียวกัน'
        ],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        maxMore: [v => !!v || 'กรุณากรอกข้อมูล', v => v > 0 || 'กรุณากรอกราคาที่มากกว่า 0', v => !/^0[0-9]+$/.test(v) || 'ห้ามกรอกเลข 0 ตัวแรก'],
        minimumRule: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) < parseInt(this.spendMinimum) || 'กรุณากรอกราคาให้ไม่เกินค่าใช้จ่ายขั้นต่ำ', v => Number(v) > 0 || 'กรุณากรอกจำนวนตัวเลขที่มากกว่า 0', v => !/^0[0-9]+$/.test(v) || 'ห้ามกรอกเลข 0 ตัวแรก'],
        spendminimumRule: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) > 0 || 'กรุณากรอกค่าใช้จ่ายขั้นต่ำให้มากกว่า 0'],
        duplicateCoupon: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) > 0 || 'กรุณากรอกจำนวนคูปองซํ้าให้มากกว่า 0']
      },
      seller_shop_id: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    // this.getData()
  },
  mounted () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  methods: {
    disabledMinutesUse (hour) {
      const now = new Date()
      const selectedDate = new Date(this.date21)
      if (
        selectedDate.toDateString() !== now.toDateString()
      ) {
        return []
      }
      // ถ้าเลือกชั่วโมงตรงกับชั่วโมงปัจจุบัน ให้ disable นาทีที่ผ่านมาแล้ว
      if (hour === now.getHours()) {
        const currentMinute = now.getMinutes()
        return Array.from({ length: currentMinute }, (v, k) => k)
      }
      return []
    },
    disabledHoursUse () {
      const now = new Date()
      const selectedDate = new Date(this.date21)
      // ถ้า date21 ไม่ใช่วันเดียวกับวันนี้ ให้ไม่ disable อะไรเลย
      if (
        selectedDate.toDateString() !== now.toDateString()
      ) {
        return []
      }
      // ถ้าเป็นวันปัจจุบัน ให้ disable ชั่วโมงก่อนเวลาปัจจุบัน
      const currentHour = now.getHours()
      return Array.from({ length: currentHour }, (v, k) => k)
    },
    onReady (editor) {
      editor.execute('heading', { value: 'heading2' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    canCel () {
      this.$router.push({ path: '/bundleDeal' }).catch(() => {})
    },
    backtoMenu () {
      this.$router.push({ path: '/bundleDeal' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.title1 {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.subTitle1 {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.detail1 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.rule {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #C4C4C4;
}
</style>

<style scoped>
::v-deep .affix {
  width: 442.66px;
  position: fixed;
}
::v-deep .affix .v-Card {
  overflow-x: hidden;
  height: 80vh; /* ความสูงเมื่อ affix ทำงาน */
  overflow-y: auto;
  transition: height 0.3s ease;
}
::v-deep .v-expansion-panel-content__wrap {
  padding: 5px 24px 0px;
  flex: 1 1 auto;
  max-width: 100%;
}
.disChipClickCoupon {
  color: #989898 !important;
  border-color: #989898 !important;
}
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  height: 50%;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
</style>

<style>
.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}

.ant-table-thead>tr>th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}

.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  bottom: 40px;
}

.ant-time-picker-panel {
  width: 243px;
}

@media (max-width: 767px) {
  .ant-time-picker-panel {
    width: 243px;
  }
}
.ant-time-picker-panel-select:nth-child(1),
.ant-time-picker-panel-select:nth-child(2),
.ant-time-picker-panel-select:nth-child(3) {
  width: 33.33% !important;
}
.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
  padding: 0 0 0 0px;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}

::v-deep .textDiscount .v-text-field__details {
  display: contents !important;
}
</style>
