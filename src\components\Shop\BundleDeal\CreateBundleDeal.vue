<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0 pa-3">
      <v-row>
        <v-col>
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">สร้าง Bundle Deal</span>
        </v-col>
      </v-row>
      <v-form ref="FormManageBundleDeal">
        <v-row dense class="mt-4">
          <v-col cols="12"><span :style="MobileSize ? 'font-size: 18px;' : 'font-size: medium;'"><b>⚫️ ข้อมูลเบื้องต้น</b></span></v-col>
          <v-col cols="12" md="12">
            <span class="detail1">ชื่อโปรโมชัน <span style="color:#F5222D">*</span></span>
            <v-text-field v-model="bundleName" :rules="Rules.empty" placeholder="ระบุชื่อโปรโมชัน" outlined dense></v-text-field>
          </v-col>
          <!-- <v-col cols="12" class="pt-0">
            <span class="detail1">รายละเอียดโปรโมชั่น</span>
            <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="bundleDescription" @ready="onReady"></ckeditor>
            <span class="rule">* รายละเอียดโปรโมชั่น วิธีใช้งานและสิทธิประโยชน์สำหรับสมาชิกอย่างละเอียด</span>
          </v-col> -->
        </v-row>
        <div>
          <v-row dense class="mt-3">
            <v-col cols="12" md="12">
              <span class="detail1">จำนวนที่ใช้ได้ต่อคน <span style="color:#F5222D">*</span></span>
              <v-text-field v-model="amonutBundle" :rules="Rules.empty" :maxLength="7" placeholder="จำนวนที่ใช้ได้ต่อคน" outlined dense oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
            </v-col>
          </v-row>
        </div>

        <div class="mt-4">
          <v-row class="">
            <v-col cols="12"><span :style="MobileSize ? 'font-size: 18px;' : 'font-size: medium;'"><b>⚫️ กำหนดระยะเวลาโปรโมชัน</b></span></v-col>
            <!-- <v-col cols="12">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">กำหนดระยะเวลาโปรโมชั่น</span>
            </v-col> -->
          </v-row>
          <div>
            <span class="detail1">ระยะเวลาใช้งานโปรโมชัน<span style="color:#F5222D">*</span></span>
            <v-row dense style="align-items: center;">
              <v-col cols="2" v-if="!MobileSize"><span class="detail1">วันที่เริ่ม - สิ้นสุด</span></v-col>
              <v-col cols="4" v-if="MobileSize"><span class="detail1">วันที่เริ่ม</span></v-col>
              <!-- use start -->
              <div>
              <v-dialog
                ref="dialogStartDate2"
                v-model="dialogStartDate2"
                :return-value.sync="date21"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentStartDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :disabled="disableddate21"
                    :rules="Rules.empty"
                    @click="date22 = '', time22 = '', sentEndDate2 = ''"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date21"
                  scrollable
                  reactive
                  locale="TH-th"
                  :min="date11 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time21 = ''"
                >
                  <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                          v-model="time21"
                          :bordered="false"
                          style="width: 100%;"
                          format="HH:mm:ss น."
                          valueFormat="HH:mm:ss"
                          size="large"
                          placeholder="00.00.00 น."
                          :disabled="date21 === ''"
                          :placement="'topLeft'"
                          :disabledHours="disabledHoursUse"
                          :disabledMinutes="disabledMinutesUse"
                          :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                        />
                      </v-col>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogStartDate2 = false, date21 === '' ? time21 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date21 == '' || time21 == ''" @click="setValueDate(date21, 'date21'), $refs.dialogStartDate2.save(date21), date22 = '', sentEndDate2 = '', time22 = ''"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
              <span class="detail1 mx-4 mb-10" v-if="!noEndDateUse && !MobileSize"> - </span>
              <v-col cols="4" v-if="!noEndDateUse && MobileSize"><span class="detail1">วันที่สิ้นสุด</span></v-col>
              <!-- use end -->
              <div v-if="!noEndDateUse">
              <v-dialog
                ref="dialogEndDate2"
                v-model="dialogEndDate2"
                :return-value.sync="date22"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentEndDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                    :disabled="sentStartDate2 === '' || disableddate22"
                    :rules="noEndDateUse ? [] : Rules.datesMustNotBeSame2"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date22"
                  scrollable
                  reactive
                  locale="TH-th"
                  :min="date21 || (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  @change="time22 = ''"
                >
                <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                            v-model="time22"
                            :bordered="false"
                            style="width: 100%;"
                            format="HH:mm:ss น."
                            valueFormat="HH:mm:ss"
                            size="large"
                            placeholder="00.00.00 น."
                            :placement="'topLeft'"
                            :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                          />
                      </v-col>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogEndDate2 = false, date22 === '' ? time22 = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" :disabled="date22 == '' || time22 == ''" @click="setValueDate(date22, 'date22'), $refs.dialogEndDate2.save(date22)"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
              </div>
            </v-row>
          </div>
        </div>
        <div class="mt-6">
          <!-- <span class="title1" :style="MobileSize ? 'font-size: 18px;' : ''">การใช้โปรโมชัน</span> -->
          <v-row dense>
            <v-col cols="12"><span :style="MobileSize ? 'font-size: 18px;' : 'font-size: medium;'"><b>⚫️ การใช้โปรโมชัน</b></span></v-col>
            <v-col cols="12">
              <span class="detail1">ประเภทโปรโมชัน <span style="color:#F5222D">* (เลือกได้ 1 รูปแบบ)</span></span>
              <v-radio-group @change="clsDataSelect" v-model="discountType" dense  class="detail1 ma-0">
                <v-col>
                  <v-row class="flex-column my-2">
                    <v-radio label="ส่วนลดเป็นจำนวนเงิน" value="baht"  @click="discountAmountPercent = '', discount_maximum = null, discountMaximumType = 'haveCapNot'"></v-radio>
                    <div v-if="discountType === 'baht'">
                      <div class="d-flex align-baseline" v-for="(items, index) in dataDiscount" :key="index">
                        <span class="mr-2">{{ index + 1 }}.</span>
                        <div class="d-flex align-baseline mr-3">
                          <span>ซื้อ</span>
                          <v-text-field class="textDiscount ml-2" :rules="Rules.maxMore" v-model="items.discountPcs" style="max-width: 250px;" outlined dense placeholder="ระบุจำนวนชิ้น" :maxLength="4" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                          <span class="ml-2">ชิ้น</span>
                        </div>
                        <div class="d-flex align-baseline">
                          <span>ลด</span>
                          <v-text-field class="textDiscount ml-2" :rules="Rules.checkDiscount" v-model="items.discountAmount" suffix="บาท" style="max-width: 250px;" outlined dense placeholder="ระบุส่วนลดที่ต้องการ" :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </div>
                        <v-btn v-if="dataDiscount.length !== 1" class="ml-2" outlined fab small color="red" @click="removeItem(index)"><v-icon>mdi-delete-outline</v-icon></v-btn>
                        <v-btn v-if="index === dataDiscount.length - 1 && dataDiscount.length <= 4" :disabled="items.discountPcs === '' || items.discountAmount === ''" class="ml-2" outlined color="#27AB9C" @click="addItemFunc(items.discountPcs)">+ ส่วนลด</v-btn>
                      </div>
                    </div>
                    <!-- <v-btn class="ml-2" outlined color="#27AB9C" @click="addItemFunc">+ ส่วนลด</v-btn> -->
                  </v-row>
                </v-col>
                <v-col class="pa-0" :style="discountType === 'baht' ? 'margin-top: -40px' : 'margin-top: -10px'">
                  <v-row no-gutters class="flex-column my-2">
                    <v-radio label="ส่วนลดเป็นเปอร์เซ็นต์ (%)" value="percent" @click="discountAmount = ''"></v-radio>
                    <div v-if="discountType === 'percent'">
                      <div  class="d-flex align-baseline" v-for="(items, index) in dataPercent" :key="index">
                        <span class="mr-2">{{ index + 1 }}.</span>
                        <div class="d-flex align-baseline mr-3">
                          <span>ซื้อ</span>
                          <v-text-field class="textDiscount ml-2" :rules="Rules.maxMore" v-model="items.discountPcsPercent" style="max-width: 250px;" outlined dense placeholder="ระบุจำนวนชิ้น" :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                          <span class="ml-2">ชิ้น</span>
                        </div>
                        <div class="d-flex align-baseline">
                          <span>ลด</span>
                          <v-text-field class="textDiscount ml-2" :rules="Rules.maxMore" @input="checkZeroDiscount(discountType, index)" v-model="items.discountAmountPercent" suffix="%" style="max-width: 250px;" outlined dense placeholder="ระบุส่วนลดที่ต้องการ" :maxLength="3" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </div>
                        <v-btn v-if="dataPercent.length !== 1" class="ml-2" outlined fab small color="red" @click="removeItemPercent(index)"><v-icon>mdi-delete-outline</v-icon></v-btn>
                        <v-btn v-if="index === dataPercent.length - 1 && dataPercent.length <= 4" :disabled="items.discountPcsPercent === '' || items.discountAmountPercent === ''" class="ml-2" outlined color="#27AB9C" @click="addItemPercent(items.discountPcsPercent)">+ ส่วนลด</v-btn>
                      </div>
                    </div>
                    <!-- <v-text-field class="textDiscount ml-2" v-if="discountType === 'percent'" :rules="discountType === 'percent' ? Rules.maxMore : []" :key="component" v-model="discountAmountPercent" suffix="%" style="max-width: 200px;" placeholder="ระบุส่วนลดที่ต้องการ" outlined dense :maxLength="2" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  </v-row>
                </v-col>
                <v-col class="pa-0" :style="discountType === 'percent' ? 'margin-top: -30px' : ''">
                  <v-row no-gutters class="flex-column my-2">
                    <v-radio label="ดีลพิเศษ" value="special" @click="discountAmount = ''"></v-radio>
                    <div v-if="discountType === 'special'">
                      <div  class="d-flex align-baseline" v-for="(items, index) in dataSpecial" :key="index">
                        <span class="mr-2">{{ index + 1 }}.</span>
                        <div class="d-flex align-baseline mr-3">
                          <span>ซื้อ</span>
                          <v-text-field class="textDiscount ml-2" :rules="Rules.maxMore" v-model="items.discountPcsSpecial" style="max-width: 250px;" outlined dense placeholder="ระบุจำนวนชิ้น" :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                          <span class="ml-2">ชิ้น</span>
                        </div>
                        <div class="d-flex align-baseline">
                          <span>เพียง</span>
                          <v-text-field class="textDiscount ml-2" :rules="Rules.checkDiscount" v-model="items.discountAmountSpecial" suffix="บาท" style="max-width: 250px;" outlined dense placeholder="ระบุส่วนลดที่ต้องการ" :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </div>
                        <v-btn v-if="dataSpecial.length !== 1" class="ml-2" outlined fab small color="red" @click="removeItemSpecial(index)"><v-icon>mdi-delete-outline</v-icon></v-btn>
                        <v-btn v-if="index === dataSpecial.length - 1 && dataSpecial.length <= 4" :disabled="items.discountPcsSpecial === '' || items.discountAmountSpecial === ''" class="ml-2" outlined color="#27AB9C" @click="addItemSpecial(items.discountPcsSpecial)">+ ส่วนลด</v-btn>
                      </div>
                    </div>
                    <!-- <v-text-field class="textDiscount ml-2" v-if="discountType === 'percent'" :rules="discountType === 'percent' ? Rules.maxMore : []" :key="component" v-model="discountAmountPercent" suffix="%" style="max-width: 200px;" placeholder="ระบุส่วนลดที่ต้องการ" outlined dense :maxLength="2" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  </v-row>
                </v-col>
                <!-- <v-row align="center" v-if="discountType === 'percent'" no-gutters class="pl-6">
                  <v-radio-group v-model="discountMaximumType" dense v-if="discountType === 'percent'" class="detail1 ma-0">
                    <v-col :class="!MobileSize ? 'pa-0 d-flex textDiscount' : 'pa-0'" style="margin-top: -1vw;">
                      <v-radio label="จำกัดจำนวนเงิน" value="haveCap"></v-radio>
                      <v-text-field v-if="discountMaximumType === 'haveCap'" :rules="discountMaximumType === 'haveCap' ? Rules.maxMore : []" v-model="discount_maximum" placeholder="ระบุจำนวนเงิน" suffix="บาท" class="ml-2" style="max-width: 200px;" outlined dense :maxLength="7" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col class="pa-0">
                      <v-radio label="ไม่จำกัดจำนวนเงิน" @click="discount_maximum = null" value="haveCapNot"></v-radio>
                    </v-col>
                  </v-radio-group>
                </v-row> -->
              </v-radio-group>
            </v-col>
          </v-row>
        </div>
        <v-row dense>
          <v-col cols="12"><span :style="MobileSize ? 'font-size: 18px;' : 'font-size: medium;'"><b>⚫️ สินค้าในโปรโมชัน</b></span></v-col>
          <v-col cols="12" class="d-flex align-center">
            <span class="detail1">เพิ่มสินค้าลงใน Bundle Deal <span style="color:#F5222D">* </span></span>
            <v-btn color="#27AB9C" outlined class="d-flex justify-center align-center ml-2" @click="openDialogSelectProduct">
              <v-icon>mdi-plus</v-icon>
              <span>เพิ่มสินค้า</span>
            </v-btn>
          </v-col>
          <v-col>
            <v-data-table
            :headers="headersShow"
            :items="Selected"
            :search="search"
            style="white-space: nowrap; width: 100%; font-weight: normal"
            height="100%"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-1"
            v-if="Selected.length !== 0"
            >
              <template v-slot:[`item.product`]="{ item }">
                <v-row class="my-4 mx-4 flex-nowrap" style="width: fit-content;">
                  <v-img max-height="93" max-width="48" width="93" height="48" src="@/assets/ImageINET-Marketplace/ICONShop/NoImageProduct.png" v-if="item.product_image.length === 0"></v-img>
                  <v-img max-height="89" max-width="48" width="100%" height="100%" :src="`${item.product_image}`" v-else></v-img>
                  <div class="d-flex flex-column ml-3">
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-if="!IpadSize" v-bind="attrs" v-on="on"> {{ item.product_name | truncate(30, '...') }}</span>
                        <span v-else v-bind="attrs" v-on="on"> {{ item.product_name }}</span>
                      </template>
                      <span>{{ item.product_name }}</span>
                    </v-tooltip>
                    <span>sku: {{ item.sku }}</span>
                  </div>
                </v-row>
              </template>
              <template v-slot:[`item.action`]="{ index }">
                <v-btn class="ml-2" small outlined fab color="red" @click="removeItemShow(index)"><v-icon>mdi-delete-outline</v-icon></v-btn>
              </template>
              <template v-slot:[`item.actual_stock`]="{ item }" v-if="!MobileSize">
                <span>{{ Number(item.actual_stock).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
              </template>
              <template v-slot:[`item.product_price`]="{ item }" v-if="!MobileSize">
                <span>{{ Number(item.product_price).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
              </template>
            </v-data-table>
            <!-- <div v-for="(items, index) in Selected" :key="index">
              <v-card width="80px" height="80px" class="d-flex justify-center align-center">
                <v-img max-height="93" max-width="48" width="93" height="48" src="@/assets/ImageINET-Marketplace/ICONShop/NoImageProduct.png" v-if="items.product_image.length === 0"></v-img>
                <v-img max-height="89" max-width="48" width="100%" height="100%" :src="`${items.product_image}`" v-else></v-img>
              </v-card>
            </div> -->
          </v-col>
        </v-row>
        <v-row :justify="MobileSize ? 'center' : 'end'" :align="MobileSize ? 'center' : 'end'" dense class="mt-10 mb-4">
          <v-col cols="12" md="12">
            <v-row :justify="MobileSize ? 'center' : 'end'" class="pr-3">
              <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="canCel()">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="createBundleDeal()" :disabled="isLoading">ยืนยัน</v-btn>
            </v-row>
          </v-col>
        </v-row>
      </v-form>
    </v-card>
    <v-dialog v-model="dialogProduct" width="750px" content-class="elevation-0">
      <v-card style="border-radius: 22px;" class="pb-2">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: large; font-weight: 700; color: #FFFFFF;">รายการสินค้า
          </span>
          <v-btn icon dark @click="closeDialogProduct">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="d-flex justify-center mt-2" :class="MobileSize ? 'flex-column' : ''">
          <!-- <span class="mr-1" style="white-space: nowrap;"><b>รายการ: </b></span> -->
          <!-- <span> {{ allOrders }}</span> -->
          <!-- <v-data-table
            :headers="headers"
            :items="DataTable.list_product"
            :search="search"
            style="white-space: nowrap; width: 100%; font-weight: normal"
            height="100%"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-1"
            >
          </v-data-table> -->
          <v-data-table
           :headers="!MobileSize ? headers : headersMobile"
           v-model="Selected"
           :items="DataTable.list_product"
           :items-per-page="10"
           :search="search"
           :footer-props="{'items-per-page-text': 'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
           @pagination="countProduct"
           show-select
           item-key="product_id"
           @toggle-select-all="selectAllToggle"
           :hide-default-footer="(DataTable.list_product !== undefined && DataTable.list_product.length !== 0) ? false : true"
           :hide-default-header="MobileSize ? true : false"
           no-results-text="ไม่พบรายการสินค้าที่ค้นหา"
           no-data-text="ไม่มีรายการสินค้าในตาราง"
           style="white-space: nowrap;"
           :style="IpadSize ? 'max-width: 100%; overflow: hidden !important;' : MobileSize ? 'max-width: 100%; overflow: hidden !important;' : ''"
           :update:items-per-page="getItemPerPage"
           :class="MobileSize ? 'px-0' : 'elevation-1'"
          >
            <template v-slot:[`header.data-table-select`]="{ on , props }">
              <v-simple-checkbox
                v-model="allSelected"
                :indeterminate="checkboxIndeterminate"
                :ripple="false"
                v-bind="props"
                v-on="on"
                color="#27AB9C"
              ></v-simple-checkbox>
            </template>
            <template v-slot:[`item.data-table-select`]="{ isSelected, select, item }">
              <v-simple-checkbox :ripple="false" :value="isSelected" @input="select($event)" v-if="!MobileSize" color="#27AB9C"></v-simple-checkbox>
              <v-card elevation="0" class="d-flex pb-0" v-if="MobileSize" style="max-width: 100%; border-top: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 8px 8px 0px 0px;">
                <v-card-text class="d-flex pb-0">
                  <v-simple-checkbox :ripple="false" :value="isSelected" @input="select($event)" class="pt-1 mr-auto" color="#27AB9C"></v-simple-checkbox>
                  <v-spacer></v-spacer>
                  <div class="d-flex align-center">
                    <span class="mr-3">เปิด-ปิด</span>
                    <v-switch :loading="isLoading[item.product_id]" :disabled="isLoading[item.product_id]" v-model="item.product_status" @change="actionChange(item.product_id)" inset true-value="active" false-value="inactive"></v-switch>
                  </div>
                </v-card-text>
              </v-card>
            </template>
            <template v-slot:[`item.product`]="{ item }">
              <v-row class="my-4 mx-4 flex-nowrap">
                <v-img max-height="93" max-width="48" width="93" height="48" src="@/assets/ImageINET-Marketplace/ICONShop/NoImageProduct.png" v-if="item.product_image.length === 0"></v-img>
                <v-img max-height="89" max-width="48" width="100%" height="100%" :src="`${item.product_image}`" v-else></v-img>
                <div class="d-flex flex-column ml-3">
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-if="!IpadSize" v-bind="attrs" v-on="on"> {{ item.product_name | truncate(30, '...') }}</span>
                      <span v-else v-bind="attrs" v-on="on"> {{ item.product_name }}</span>
                    </template>
                    <span>{{ item.product_name }}</span>
                  </v-tooltip>
                  <span>sku: {{ item.sku }}</span>
                </div>
              </v-row>
            </template>
            <template v-slot:[`item.actual_stock`]="{ item }" v-if="!MobileSize">
              <span>{{ Number(item.actual_stock).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
            </template>
            <template v-slot:[`item.product_price`]="{ item }" v-if="!MobileSize">
              <span>{{ Number(item.product_price).toLocaleString(undefined, {minimumFractionDigits: 0}) }}</span>
            </template>
          </v-data-table>
        </v-card-text>
        <v-card-actions>
          <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="closeDialogProduct()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn outlined  dense rounded dark color="#27AB9C" class="mr-4 pl-8 pr-8" @click="clsProduct()">ล้างตัวเลือก</v-btn>
          <v-btn color="#27AB9C" dark dense rounded class="pl-9 pr-9" @click="dialogProduct = false" :disabled="isLoading">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
import { TimePicker } from 'ant-design-vue'
import dayjs from 'dayjs'
import { Decode } from '@/services'
export default {
  components: {
    'a-time-picker': TimePicker
    // 'a-time-range-picker': TimePicker.RangePicker
  },
  data () {
    return {
      sentStartDate2: '',
      sentEndDate2: '',
      disableddate21: false,
      disableddate22: false,
      isLoading: false,
      discountType: '',
      noEndDateUse: false,
      dialogStartDate2: false,
      dialogEndDate2: false,
      time11: '',
      time12: '',
      time21: '',
      time22: '',
      date11: '',
      date12: '',
      date21: '',
      date22: '',
      amonutBundle: '',
      bundleDescription: '',
      defaultDate: dayjs('00:00:00', 'HH:mm:ss'),
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'blockquote',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      },
      bundleName: '',
      Rules: {
        amonutBundle: [v => !!v || 'กรุณากรอกข้อมูล'
          // v => (parseInt(v) <= parseInt(this.amonutUse)) || 'จำนวนคูปองที่ใช้ได้ต่อคนไม่ควรมากกว่าจำนวนคูปองสูงสุด'
        ],
        fourand15: [v => v.length >= 4 || 'กรุณากรอกข้อมูล'],
        datesMustNotBeSame1: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        datesMustNotBeSame2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate2 !== v) || 'วันเริ่มต้นและวันที่สิ้นสุดไม่ควรเป็นวันเดียวกัน'
        ],
        StartDate1MustNotBeSameStartDate2: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => (this.sentStartDate1 !== v) || 'ระยะเวลาใช้งานและระยะเวลาเก็บ เวาเชอร์ไม่ควรเป็นวัน/เวลาเดียวกัน'
        ],
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        maxMore: [v => !!v || 'กรุณากรอกข้อมูล', v => v > 0 || 'กรุณากรอกข้อมูลที่มากกว่า 0', v => !/^0[0-9]+$/.test(v) || 'ห้ามกรอกเลข 0 ตัวแรก'],
        checkDiscount: [v => !!v || 'กรุณากรอกข้อมูล', v => v > 0 || 'กรุณากรอกข้อมูลที่มากกว่า 0', v => !/^0[0-9]+$/.test(v) || 'ห้ามกรอกเลข 0 ตัวแรก', v => v <= 1000000 || 'ราคาสูงสุดห้ามเกิน 1,000,000'],
        minimumRule: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) < parseInt(this.spendMinimum) || 'กรุณากรอกราคาให้ไม่เกินค่าใช้จ่ายขั้นต่ำ', v => Number(v) > 0 || 'กรุณากรอกจำนวนตัวเลขที่มากกว่า 0', v => !/^0[0-9]+$/.test(v) || 'ห้ามกรอกเลข 0 ตัวแรก'],
        spendminimumRule: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) > 0 || 'กรุณากรอกค่าใช้จ่ายขั้นต่ำให้มากกว่า 0'],
        duplicateCoupon: [v => !!v || 'กรุณากรอกข้อมูล', v => parseInt(v) > 0 || 'กรุณากรอกจำนวนคูปองซํ้าให้มากกว่า 0']
      },
      seller_shop_id: '',
      dataDiscount: [
        {
          discountPcs: '',
          discountAmount: ''
        }
      ],
      dataPercent: [
        {
          discountPcsPercent: '',
          discountAmountPercent: ''
        }
      ],
      dataSpecial: [
        {
          discountPcsSpecial: '',
          discountAmountSpecial: ''
        }
      ],
      dialogProduct: false,
      onedata: [],
      DataTable: [],
      headers: [
        { text: 'สินค้า', value: 'product', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สินค้าพร้อมขาย', value: 'actual_stock', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'product_price', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersShow: [
        { text: 'สินค้า', value: 'product', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สินค้าพร้อมขาย', value: 'actual_stock', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'product_price', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ขนส่ง', value: 'shipping', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', filterable: false, sortable: false, width: '150', align: 'left', class: 'backgroundTable fontTable--text fontSizeDetail' }

      ],
      search: '',
      showCountOrder: 0,
      Selected: [],
      allSelected: false,
      checkboxIndeterminate: false
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.current_role_user.super_admin === false && this.onedata.user.current_role_user.super_admin_platform === false && this.onedata.user.current_role_user.admin_platform === false) {
        this.$router.push({ path: '/' }).catch(() => {})
      }
      // console.log(this.onedata)
    }
    this.getCountInTable()
    // this.getData()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/createBundleDealMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createBundleDeal' }).catch(() => {})
      }
    },
    Selected (val) {
      if (val.length !== this.DataTable.list_product.length) {
        this.allSelected = false
      } else {
        this.allSelected = true
      }
    },
    allSelected (val) {
      // console.log('allSelected', val)
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  methods: {
    removeItemShow (index) {
      this.Selected.splice(index, 1)
    },
    selectAllToggle () {
      if (this.Selected.length !== this.DataTable.list_product.length && this.allSelected) {
        this.Selected = []
        const self = this
        if (this.search !== '' || this.selectType !== 'all' || this.selectStatus !== 'all' || this.selectStatusProduct !== 'all') {
          this.DataTable.list_product.forEach(item => {
            self.Selected.push(item)
          })
        } else {
          this.DataTable.list_product.forEach(item => {
            self.Selected.push(item)
          })
        }
        if (this.Selected.length > 0 && (this.DataTable.list_product.length === this.Selected.length)) {
          this.allSelected = true
          this.checkboxIndeterminate = false
        } else if (this.Selected.length > 0) {
          this.checkboxIndeterminate = true
        }
      } else {
        this.Selected = []
        this.allSelected = false
        this.checkboxIndeterminate = false
      }
    },
    getItemPerPage (val) {
      // console.log(val)
      this.itemsPerPage = val
    },
    countProduct (pagination) {
      // console.log(pagination)
      window.scrollTo(0, 0)
      this.showCountOrder = pagination.itemsLength
      // for (let i = 0; i < this.itemsTab.length; i++) {
      //   if (i === 0) {
      //     this.itemsTab[i].value = pagination.itemsLength
      //   }
      // }
    },
    clsDataSelect () {
      if (this.discountType === 'baht') {
        this.dataPercent = [{ discountPcsPercent: '', discountAmountPercent: '' }]
        this.dataSpecial = [{ discountPcsSpecial: '', discountAmountSpecial: '' }]
      } else if (this.discountType === 'percent') {
        this.dataDiscount = [{ discountPcs: '', discountAmount: '' }]
        this.dataSpecial = [{ discountPcsSpecial: '', discountAmountSpecial: '' }]
      } else {
        this.dataDiscount = [{ discountPcs: '', discountAmount: '' }]
        this.dataPercent = [{ discountPcsPercent: '', discountAmountPercent: '' }]
      }
    },
    async createBundleDeal () {
      this.$store.commit('openLoader')
      if (this.$refs.FormManageBundleDeal.validate(true)) {
        if (this.Selected.length === 0) {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'กรุณาเพิ่มสินค้าในโปรโมชัน', showConfirmButton: false, timer: 2500 })
          this.$store.commit('closeLoader')
        } else {
          // this.checkNextStep = true
          window.scrollTo(0, 0)
          setTimeout(() => {
            this.$store.commit('closeLoader')
          }, 1000)
        }
        var data = {
          bundleName: this.bundleName,
          bundleDescription: this.bundleDescription,
          amonutBundle: this.amonutBundle,
          startDate: this.sentStartDate2,
          endDate: this.sentEndDate2,
          typeDis: this.discountType,
          listDis: this.discountType === 'baht' ? this.dataDiscount : this.discountType === 'percent' ? this.dataPercent : this.dataSpecial,
          listProduct: this.Selected
        }
        console.log(data)
        // console.log('เข้ามั้ย')
        this.$store.commit('closeLoader')
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
        setTimeout(() => {
          this.$store.commit('closeLoader')
        }, 1000)
      }
    },
    checkZeroDiscount (type, index) {
      if (type === 'percent') {
        if (parseInt(this.dataPercent[index].discountAmountPercent) > 100) {
          this.dataPercent[index].discountAmountPercent = '100'
          // this.forceRerender()
        }
      }
    },
    addItemFunc (pcs) {
      this.dataDiscount.push({
        discountPcs: Number(pcs) + 1,
        discountAmount: ''
      })
    },
    removeItem (funcIndex) {
      this.dataDiscount.splice(funcIndex, 1)
    },
    addItemPercent (pcs) {
      this.dataPercent.push({
        discountPcsPercent: Number(pcs) + 1,
        discountAmountPercent: ''
      })
    },
    removeItemPercent (funcIndex) {
      this.dataPercent.splice(funcIndex, 1)
    },
    addItemSpecial (pcs) {
      this.dataSpecial.push({
        discountPcsSpecial: Number(pcs) + 1,
        discountAmountSpecial: ''
      })
    },
    removeItemSpecial (funcIndex) {
      this.dataSpecial.splice(funcIndex, 1)
    },
    setValueDate (dateDay, proof) {
      if (!dateDay) return null
      const [year, month, day] = dateDay.split('-')
      const yearChange = parseInt(year) + 543
      if (proof === 'date11') {
        this.sentStartDate1 = `${day}/${month}/${yearChange}` + ' ' + this.time11
      } else if (proof === 'date12') {
        this.sentEndDate1 = `${day}/${month}/${yearChange}` + ' ' + this.time12
      } else if (proof === 'date21') {
        this.sentStartDate2 = `${day}/${month}/${yearChange}` + ' ' + this.time21
      } else if (proof === 'date22') {
        this.sentEndDate2 = `${day}/${month}/${yearChange}` + ' ' + this.time22
      }
    },
    disabledMinutesUse (hour) {
      const now = new Date()
      const selectedDate = new Date(this.date21)
      if (
        selectedDate.toDateString() !== now.toDateString()
      ) {
        return []
      }
      // ถ้าเลือกชั่วโมงตรงกับชั่วโมงปัจจุบัน ให้ disable นาทีที่ผ่านมาแล้ว
      if (hour === now.getHours()) {
        const currentMinute = now.getMinutes()
        return Array.from({ length: currentMinute }, (v, k) => k)
      }
      return []
    },
    disabledHoursUse () {
      const now = new Date()
      const selectedDate = new Date(this.date21)
      // ถ้า date21 ไม่ใช่วันเดียวกับวันนี้ ให้ไม่ disable อะไรเลย
      if (
        selectedDate.toDateString() !== now.toDateString()
      ) {
        return []
      }
      // ถ้าเป็นวันปัจจุบัน ให้ disable ชั่วโมงก่อนเวลาปัจจุบัน
      const currentHour = now.getHours()
      return Array.from({ length: currentHour }, (v, k) => k)
    },
    onReady (editor) {
      editor.execute('heading', { value: 'heading2' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    canCel () {
      if (this.MobileSize) {
        this.$router.push({ path: '/bundleDealMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/bundleDeal' }).catch(() => {})
      }
    },
    backtoMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/bundleDealMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/bundleDeal' }).catch(() => {})
      }
    },
    openDialogSelectProduct () {
      this.dialogProduct = true
    },
    closeDialogProduct () {
      this.dialogProduct = false
      // this.Selected = []
      // this.allSelected = false
    },
    clsProduct () {
      this.Selected = []
      this.allSelected = false
    },
    async getCountInTable () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID'))
      }
      const auth = {
        headers: { Authorization: `Bearer ${this.onedata.user.access_token}` }
      }
      // await this.$store.dispatch('GetProductBySellerID', data)
      var dataResponse = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/product/list_product`, data, auth)
      // console.log('dataResponse ====>', dataResponse)
      if (dataResponse.data.result === 'SUCCESS') {
        this.DataTable = await dataResponse.data.data
        this.$store.commit('closeLoader')
        // console.log(this.DataTable, '****')
      } else {
        if (dataResponse.data.message === 'This user is not in your shop') {
          window.location.assign('/')
        } else {
          window.location.assign('/')
        }
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
.title1 {
  font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;
}
.subTitle1 {
  font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;
}
.detail1 {
  font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;
}
.rule {
  font-weight: 400; font-size: 12px; line-height: 16px; color: #C4C4C4;
}
</style>

<style scoped>
::v-deep .affix {
  width: 442.66px;
  position: fixed;
}
::v-deep .affix .v-Card {
  overflow-x: hidden;
  height: 80vh; /* ความสูงเมื่อ affix ทำงาน */
  overflow-y: auto;
  transition: height 0.3s ease;
}
::v-deep .v-expansion-panel-content__wrap {
  padding: 5px 24px 0px;
  flex: 1 1 auto;
  max-width: 100%;
}
.disChipClickCoupon {
  color: #989898 !important;
  border-color: #989898 !important;
}
.custom-scroll::-webkit-scrollbar {
  width: 10px;
  height: 50%;
  -webkit-overflow-scrolling: touch;
  -webkit-appearance: none;
}

.custom-scroll::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px grey;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background: #27AB9C;
  border-radius: 10px;
  margin-top: 10px;
  margin-bottom: 10px;
  -webkit-overflow-scrolling: touch;
}

.custom-scroll::-webkit-scrollbar-thumb:hover {
  background: #23998C;
  -webkit-overflow-scrolling: touch;
}
</style>

<style>
.ant-table-thead>tr>th,
.ant-table-tbody>tr>td {
  padding: 10px 10px;
  overflow-wrap: break-word;
}

.ant-table-thead>tr>th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}

.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input {
  border-radius: 8px;
  border-color: rgba(0, 0, 0, 0.42);
  height: 40px;
  padding: 6px 11px;
  font-size: 16px;
}

.ant-time-picker-large .ant-time-picker-input:hover {
  border-color: rgba(0, 0, 0, 0.87);
}

.ant-time-picker-panel-inner {
  bottom: 40px;
}

.ant-time-picker-panel {
  width: 243px;
}

@media (max-width: 767px) {
  .ant-time-picker-panel {
    width: 243px;
  }
}
.ant-time-picker-panel-select:nth-child(1),
.ant-time-picker-panel-select:nth-child(2),
.ant-time-picker-panel-select:nth-child(3) {
  width: 33.33% !important;
}
.ant-time-picker-panel-select ul {
  width: auto;
}

li.ant-time-picker-panel-select-option-selected {
  color: #27AB9C;
  font-weight: 600;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-select li {
  text-align: center;
  padding: 0 0 0 0px;
}

li.ant-time-picker-panel-select-option-selected:hover {
  color: #27AB9C;
  background: rgba(39, 171, 156, 0.10);
}

.ant-time-picker-panel-narrow .ant-time-picker-panel-input-wrap {
  display: none;
}

.ant-time-picker-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.ant-time-picker-panel-input:focus {
  border-color: #27AB9C;
  border-width: 2px !important;
  box-shadow: none;
}

.anticon svg {
  font-size: larger;
  color: #27AB9C;
  display: inline-block;
}

::v-deep .textDiscount .v-text-field__details {
  display: contents !important;
}
</style>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  ::v-deep .elevation-1 tr th:first-of-type, td:first-of-type {
    background-color: #E6F5F3;
    border-style: none !important;
  }
</style>
