<template>
  <v-container class="pl-3 pr-3 pt-4 pb-4">
  <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0 mb-2">
    <v-card-title v-if="!MobileSize" style="font-size: x-large; font-weight: 600;">รายการสั่งซื้อสินค้า</v-card-title>
    <v-card-title v-else style="font-size: large; font-weight: 600;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> รายการสั่งซื้อสินค้า</v-card-title>
    <v-row class="ml-2 mr-2">
      <v-col :cols="MobileSize || IpadSize ? 12 : 7">
        <v-text-field v-model="search" dense hide-details outlined placeholder="ค้นหาข้อมูลจากชื่อ Service Partner ที่เชื่อมบริการ" @keyup="checkSearch" class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'">
        <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? 12 : 5">
        <div style="display: flex; align-items: center;">
          <span :style="MobileSize || IpadSize ? 'font-size: small; min-width: 74px;' : 'font-size: medium; min-width: 90px;'">สถานะสั่งซื้อ :</span>
          <v-select outlined dense v-model="dataOrderList" @change="handleSelectChangeOrderList" :items="dataSelectOrderList" item-text="text" item-value="value" placeholder="เลือกประเภทการชำระเงิน" style="line-height: 1.5;" hide-details class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'"></v-select>
        </div>
      </v-col>
    </v-row>
    <v-row class="ml-2 mr-2">
      <v-col :cols="MobileSize || IpadSize ? 12 : 4">
        <div style="display: flex; align-items: center;">
          <span :style="MobileSize || IpadSize ? 'font-size: small; min-width: 74px;' : 'font-size: medium; min-width: 90px;'">Pay type :</span>
          <v-select outlined dense v-model="dataPayType" @change="handleSelectChangePayType" :items="dataSelectPayType" item-text="text" item-value="value" placeholder="เลือกสถานะรายการ" hide-details class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'"></v-select>
        </div>
      </v-col>
      <v-col  :cols="MobileSize ? 12 : IpadSize ? 6 : 4">
        <div style="display: flex; align-items: center;">
          <span :style="MobileSize ? 'font-size: small; min-width: 74px;' : IpadSize ? 'font-size: small; min-width: 64px;' : 'font-size: medium; min-width: 90px;'">วันที่สั่งซื้อ :</span>
          <v-dialog ref="dialogCreatedDate" v-model="modalSCreatedDate" :return-value.sync="date" persistent width="290px">
            <template v-slot:activator="{ on, attrs }">
              <v-text-field readonly
                v-model="createdDateToshow"
                v-bind="attrs"
                v-on="on"
                outlined
                dense
                hide-details
                placeholder="วว/ดด/ปปปป"
                class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'"
                >
                <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              v-model="date"
              color = "#27AB9C"
              scrollable
              reactive
              locale="Th-th"
              @change="setValueCreatedDate(date)"
              :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
            >
              <v-spacer></v-spacer>
              <v-btn text color="primary" @click="closeModalCreatedDate($refs)">ยกเลิก</v-btn>
              <v-btn text color="primary" @click=" $refs.dialogCreatedDate.save(date); getOrderList(); options.page = 1">ตกลง</v-btn>
            </v-date-picker>
          </v-dialog>
          <!-- <v-text-field v-model="dateOrder" dense hide-details outlined  placeholder="วว/ดด/ปปปป" class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'">
          <v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon>
          </v-text-field> -->
        </div>
      </v-col>
      <v-col  :cols="MobileSize ? 12 : IpadSize ? 6 : 4">
        <div style="display: flex; align-items: center;">
          <span :style="MobileSize ? 'font-size: small; min-width: 74px;' : IpadSize ? 'font-size: small; min-width: 64px;' : 'font-size: medium; min-width: 90px;'">วันที่อนุมัติ :</span>
          <v-dialog ref="dialogApproveDate" v-model="modalApproveDate" :return-value.sync="date" persistent width="290px">
            <template v-slot:activator="{ on, attrs }">
              <v-text-field readonly
                v-model="approveDateToshow"
                v-bind="attrs"
                v-on="on"
                outlined
                dense
                hide-details
                placeholder="วว/ดด/ปปปป"
                class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'"
                >
                <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              v-model="date"
              color = "#27AB9C"
              scrollable
              reactive
              locale="Th-th"
              @change="setValueapproveDate(date)"
              :min="createdDate"
              :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
            >
              <v-spacer></v-spacer>
              <v-btn text color="primary" @click="closeModalApproveDate($refs)">ยกเลิก</v-btn>
              <v-btn text color="primary" @click="$refs.dialogApproveDate.save(date); getOrderList(); options.page = 1">ตกลง</v-btn>
            </v-date-picker>
          </v-dialog>
          <!-- <v-text-field v-model="dateApprove" dense hide-details outlined  placeholder="วว/ดด/ปปปป" class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'">
          <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
          </v-text-field> -->
        </div>
      </v-col>
    </v-row>
    <v-row class="ml-2 mr-2">
      <v-col  :cols="MobileSize || IpadSize ? 12 : 8">
        <div style="display: flex; align-items: center;">
          <span :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'">วันที่รอบบริการ :</span>
          <v-dialog ref="dialogServiceDateContract" v-model="modalServiceDateContract" :return-value.sync="serviceDate" persistent width="290px">
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="serviceDateShow"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    hide-details
                    class="ml-2"
                    :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'"
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                    >
                    <v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon>
                  </v-text-field>
                </template>
              <v-date-picker color="#27AB9C" v-model="serviceDate" scrollable range reactive locale="Th-th">
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="closeModalServiceDateContract($refs)">ยกเลิก</v-btn>
                  <v-btn text color="primary" @click=" $refs.dialogServiceDateContract.save(serviceDate); getOrderList(); options.page = 1">ตกลง</v-btn>
                </v-date-picker>
              </v-dialog>
          <!-- <v-text-field v-model="dateService" dense hide-details outlined  placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป" class="ml-2" :style="MobileSize || IpadSize ? 'font-size: small;' : 'font-size: medium;'">
          <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
          </v-text-field> -->
        </div>
      </v-col>
    </v-row>
    <v-row class="ml-2 mr-2 mt-4">
      <v-col v-if="!MobileSize && !IpadSize" cols="9" style="display: flex; align-items: flex-end;">
        <span :style="MobileSize  ? 'font-size: small;' : 'font-size: medium;'">รายการสั่งซื้อทั้งหมด {{ countOrderList }} รายการ</span>
      </v-col>
      <v-col :cols="MobileSize || IpadSize ? 12 : 3" style="display: flex; justify-content: end;">
        <v-btn v-if="!MobileSize && !IpadSize" rounded outlined color="primary" height="40" class="white--text mr-2" @click="exportExcel()"  style="font-size: small;">
          <v-img :src="require('@/assets/icons/export_excel.png')" max-width="18" class="mr-1"></v-img>
          Export File
        </v-btn>
        <v-btn v-else rounded outlined color="primary" height="34" class="white--text mr-1" @click="exportExcel()" style="font-size: x-small;">
          <!-- <v-img :src="require('@/assets/icons/export_excel.png')" max-width="14" class="mr-1"></v-img> -->
          Export
        </v-btn>
        <v-btn v-if="!MobileSize && !IpadSize" @click="reSetSearch()" class="white--text" rounded color="#27AB9C" height="40" style="font-size: small;">
          <v-icon small>mdi-restart</v-icon>ล้างค่า
        </v-btn>
        <v-btn v-else @click="reSetSearch()" class="white--text" rounded color="#27AB9C" height="34" style="font-size: x-small;">
          ล้างค่า
        </v-btn>

      </v-col>
      <v-col v-if="MobileSize || IpadSize" cols="12" style="display: flex; align-items: flex-end;">
        <span :style="MobileSize  ? 'font-size: small;' : 'font-size: medium;'">รายการสั่งซื้อทั้งหมด {{ countOrderList }} รายการ</span>
      </v-col>
    </v-row>
    <v-row class="ml-2 mr-2">
      <v-col cols="12">
        <v-data-table
          :headers="headers"
          :items="itemOrderList"
          :search="search"
          class="elevation-1 mt-3"
          :footer-props="{ 'items-per-page-options': [5, 10, 15, 25], 'items-per-page-text': 'จำนวนแถว' }"
          height="100%"
          no-results-text="ไม่พบรายการ"
          no-data-text="ไม่พบรายการ"
          style="width: 100%; white-space: nowap; text-align: center;"
          :options.sync="options"
          :items-per-page="options.itemsPerPage"
          :server-items-length="maxPage"
          @update:options="updateOptions"
        >
          <template v-slot:[`item.created_at`]="{ item }">
            <!-- <span v-if="item.created_at" color="#27AB9C" >{{item.created_at}} {{new Date(item.created_at).toLocaleDateString('th-TH', { day: 'numeric', month: 'long', year: 'numeric' })}}</span> -->
            <span v-if="item.created_at" color="#27AB9C" > {{item.created_at}} </span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.purchase_order_number`]="{ item }">
            <span v-if="item.purchase_order_number !== '-'" color="#27AB9C" style="white-space: nowrap;"> {{ item.purchase_order_number }}</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.approve_date`]="{ item }">
            <!-- <span v-if="item.approve_date !== '-'" color="#27AB9C" > {{ new Date(item.approve_date).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span> -->
            <span v-if="item.approve_date !== '-'" color="#27AB9C" > {{ item.approve_date }}</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.transaction_text`]="{ item }">
            <span v-if="item.transaction_text === 'ชำระเงินสำเร็จ'" style="color: #52c41a;"><v-icon size="x-small" color="#52c41a">mdi-circle</v-icon> ชำระเงินแล้ว</span>
            <span v-else-if="item.transaction_text === 'สั่งซื้อสำเร็จ'" style="color: #47b8ff;"><v-icon size="x-small" color="#47b8ff">mdi-circle</v-icon> สั่งซื้อสำเร็จ</span>
            <!-- <span v-else-if="item.transaction_text === 'รอชำระเงิน'" style="color: #faad14;"><v-icon size="x-small" color="#faad14">mdi-circle</v-icon> รอชำระเงิน</span>
            <span v-else-if="item.transaction_text === 'ยกเลิก'"><v-icon size="x-small">mdi-circle</v-icon> ยกเลิกคำสั่งซื้อ</span>
            <span v-else-if="item.transaction_text === 'กำลังจัดส่ง'" style="color: #FAD02C;"><v-icon size="x-small" color="#FAD02C">mdi-circle</v-icon> กำลังจัดส่ง</span> -->
            <span v-else-if="item.transaction_text === 'รอดำเนินการ'" style="color: #434bf7;"><v-icon size="x-small" color="#434bf7">mdi-circle</v-icon> รอดำเนินการ</span>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.pay_type`]="{ item }">
            <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
            <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
            <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.QT_order_path`]="{ item }">
            <v-btn text color="success" v-if="item.QT_order_path !== '-'" @click="getQTOrder(item.QT_order_path)"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.order_number }}</span></v-btn>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.receipt_transaction_code`]="{ item }">
            <v-btn style="margin-left: -10px;" text color="success" v-if="item.receipt_transaction_code !== '-' && item.bill_order_number !== '-'" @click="getQTOrderInvoice(item.receipt_transaction_code)"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.bill_order_number }}</span></v-btn>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.etax_number`]="{ item }">
            <v-btn style="margin-left: -10px;" text color="success" v-if="item.etax_number !== '-' && item.bill_order_number !== '-'" @click="GetETaxPDF(item.etax_number)"><span style="text-decoration: underline; color: #1b5dd6;">{{ item.bill_order_number }}</span></v-btn>
            <span v-else>-</span>
          </template>
          <template v-slot:[`item.manage`]="{ item }">
            <!-- <v-btn v-if="!MobileSize" plain><v-icon color="#27AB9C">mdi-dots-vertical</v-icon></v-btn> -->
            <v-btn :disabled="(item.transaction_text !== 'ชำระเงินสำเร็จ' && item.transaction_text !== 'สั่งซื้อสำเร็จ') || (item.purchase_order_number === '-' && item.bill_order_number === '-')" color="#27AB9C" text @click="goDetailOrder(item.purchase_order_number, item.bill_order_number)">
              <v-icon>mdi-file-document-outline</v-icon>
              <span>รายละเอียด</span>
            </v-btn>
            <!-- <v-btn color="primary" rounded small text style="font-weight: 500; font-size: small; text-decoration: underline" @click="goDetailOrder(item.order_number)">รายละเอียด</v-btn> -->
          </template>
          <template v-slot:[`item.service_date`]="{ item }">
            <span style="white-space: nowrap;" v-if="item.start_date_contract && item.end_date_contract !== '-'" color="#27AB9C" > {{ formatDateService(item.start_date_contract) + ' - ' + formatDateService(item.end_date_contract) }}</span>
            <span v-else>-</span>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
  </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      search: '',
      dataOrderList: '',
      dataPayType: '',
      itemsPerPage: 10,
      itemOrderList: [],
      countOrderList: 0,
      headers: [
        { text: 'วันที่ทำรายการ', value: 'created_at', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'purchase_order_number', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'Pay Type', value: 'pay_type', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะสั่งซื้อ', value: 'transaction_text', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'name_th', sortable: false, align: 'start', width: '160', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่อนุมัติ', value: 'approve_date', sortable: false, align: 'start', width: '160', class: 'backgroundTable fontTable--text' },
        { text: 'ใบเสนอราคา', value: 'QT_order_path', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'ใบแจ้งหนี้', value: 'receipt_transaction_code', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่รอบบริการ', value: 'service_date', sortable: false, align: 'start', width: '160', class: 'backgroundTable fontTable--text' },
        { text: 'ใบกำกับภาษี', value: 'etax_number', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manage', sortable: false, align: 'center', width: '40', class: 'backgroundTable fontTable--text' }
      ],
      ServiceID: '',
      options: {
        page: 1,
        itemsPerPage: 10
      },
      modalSCreatedDate: false,
      createdDate: '',
      createdDateToshow: '',
      modalApproveDate: false,
      approveDate: '',
      approveDateToshow: '',
      modalServiceDateContract: false,
      serviceDateContract: '',
      serviceDateShow: '',
      startDateToSend: '',
      endDateToSend: '',
      startDateToShow: '',
      endDateToShow: '',
      serviceDate: new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10),
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dataSelectOrderList: [
        { text: 'ทั้งหมด', value: '' },
        // { text: 'ยกเลิก', value: 'ยกเลิก' },
        // { text: 'กำลังจัดส่ง', value: 'กำลังจัดส่ง' },
        { text: 'ชำระเงินสำเร็จ', value: 'ชำระเงินสำเร็จ' },
        { text: 'สั่งซื้อสำเร็จ', value: 'สั่งซื้อสำเร็จ' },
        // { text: 'รอชำระเงิน', value: 'รอชำระเงิน' }
        { text: 'รอดำเนินการ', value: 'รอดำเนินการ' }
      ],
      dataSelectPayType: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'recurring', value: 'recurring' },
        { text: 'onetime', value: 'onetime' },
        { text: 'general', value: 'general' }
      ],
      taxId: '',
      checkUpdate: true,
      maxPage: 0
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/orderListPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderListPartner' }).catch(() => {})
      }
    },
    serviceDate (val) {
      this.startDateToSend = val[0] !== undefined ? this.formatDate(val[0]) : ''
      this.endDateToSend = val[1] !== undefined ? this.formatDate(val[1]) : ''
      this.startDateToShow = val[0] !== undefined ? this.formatDateToShow(val[0]) : ''
      this.endDateToShow = val[1] !== undefined ? this.formatDateToShow(val[1]) : ''
      if (this.startDateToShow !== '' && this.endDateToShow !== '') {
        this.serviceDateShow = this.startDateToShow + ' - ' + this.endDateToShow
      } else {
        this.serviceDateShow = ''
      }
    }
  },
  async created () {
    // this.$store.commit('openLoader')
    await this.getServiceID()
    await this.getOrderList()
    // this.$store.commit('closeLoader')
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    backToMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    setValueCreatedDate (val) {
      this.createdDate = this.formatDate(val)
      this.createdDateToshow = this.formatDateToShow(val)
      this.approveDate = ''
    },
    async closeModalCreatedDate ($refs) {
      this.modalSCreatedDate = false
      $refs.dialogCreatedDate.save('')
      this.date = new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)
      this.createdDateToshow = ''
      this.createdDate = ''
      this.options.page = 1
      await this.getOrderList()
    },
    setValueapproveDate (val) {
      this.approveDate = this.formatDate(val)
      this.approveDateToshow = this.formatDateToShow(val)
    },
    async closeModalApproveDate ($refs) {
      this.modalApproveDate = false
      $refs.dialogApproveDate.save('')
      this.date = new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)
      this.approveDateToshow = ''
      this.approveDate = ''
      this.options.page = 1
      await this.getOrderList()
    },
    setValueServiceDateContract (val) {
      this.serviceDateContract = this.formatDate(val)
      this.serviceDateShow = this.formatDateToShow(val)
    },
    async setValueServiceDate (val) {
      this.$refs.dialogServiceDateContract.save(val)
      var Range = await val.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
      this.serviceDate = Range
      if (!this.MobileSize) {
        this.options.page = 1
        await this.getOrderList()
      }
    },
    SortDate (serviceDate) {
      return serviceDate.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
    },
    async closeModalServiceDateContract () {
      this.modalServiceDateContract = false
      this.$refs.dialogServiceDateContract.save([])
      this.serviceDateShow = ''
      this.startDateToSend = ''
      this.endDateToSend = ''
      this.serviceDate = []
      this.serviceDateContract = ''
      this.options.page = 1
      await this.getOrderList()
      if (!this.MobileSize) {
        this.options.page = 1
        await this.getOrderList()
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year)
      // return `${yearChange}-${month}-${day}`
      return `${day}/${month}/${yearChange}`
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year)
      return `${yearChange}-${month}-${day}`
      // return `${day}/${month}/${yearChange}`
    },
    checkSearch () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        this.options.page = 1
        await this.getOrderList()
      }, 1000)
    },
    async reSetSearch () {
      this.dataOrderList = ''
      this.dataPayType = ''
      this.createdDate = ''
      this.approveDate = ''
      this.startDateToSend = ''
      this.endDateToSend = ''
      this.serviceDateContract = ''
      this.search = ''
      this.$refs.dialogServiceDateContract.save([])
      this.modalServiceDateContract = false
      this.modalSCreatedDate = false
      this.modalApproveDate = false
      this.serviceDate = []
      this.createdDateToshow = ''
      this.approveDateToshow = ''
      this.serviceDateShow = ''
      this.options.page = 1
      await this.getOrderList()
    },
    async handleSelectChangeOrderList (value) {
      // console.log('handleSelectChangeOrderList', value)
      this.dataOrderList = value
      this.options.page = 1
      await this.getOrderList()
    },
    async handleSelectChangePayType (value) {
      // console.log('handleSelectChangePayType', value)
      this.dataPayType = value
      this.options.page = 1
      await this.getOrderList()
    },
    async getServiceID () {
      this.$store.commit('openLoader')
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopID
      }
      // console.log(localStorage.getItem('shopSellerID'))
      await this.$store.dispatch('actionGetServiceID', data)
      var respons = await this.$store.state.ModuleOrder.stateGetServiceID
      if (respons.code === 200) {
        if (respons.data.length !== 0) {
          this.$store.commit('openLoader')
          this.detailServiceID = respons.data[0]
          // console.log('service', this.detailServiceID)
          this.serviceID = this.detailServiceID.service_id
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
      this.$store.commit('closeLoader')
    },
    async getOrderList () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        transaction_text: this.dataOrderList,
        pay_type: this.dataPayType,
        created_date: this.createdDate,
        approve_date: this.approveDate,
        start_date_contract: this.startDateToSend,
        end_date_contract: this.endDateToSend,
        search: this.search !== undefined ? this.search : '',
        limit: this.options.itemsPerPage,
        pages: this.options.page
      }
      await this.$store.dispatch('actionOrderListERP', data)
      var response = await this.$store.state.ModuleOrder.stateOrderListERP
      if (response.code === 200) {
        this.itemOrderList = response.data
        this.countOrderList = response.orderCount
        this.maxPage = response.orderCount
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    getQTOrder (item) {
      window.open(item, '_blank')
    },
    async getQTOrderInvoice (item) {
      const data = {
        transactionCode: item
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}partner/etax/document_invoice`,
          method: 'POST',
          data: data
        }).then((response) => {
          window.open(response.data.etaxResponse.pdfURL, '_blank')
        })
      } catch (error) {
        // console.log('error', error)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async GetETaxPDF (val) {
      const data = {
        transactionCode: val
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}partner/etax/decument_receipt`,
          method: 'POST',
          data: data
        }).then((response) => {
          window.open(response.data.etaxResponse.pdfURL, '_blank')
        })
      } catch (error) {
        // console.log('error', error)
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async updateOptions (options) {
      this.options = options
      await this.getOrderList()
    },
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}exports/orders/erpV2`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          responseType: 'blob',
          data: data
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          const date = new Date().getDate().toString().padStart(2, '0') + '_' + (new Date().getMonth() + 1).toString().padStart(2, '0') + '_' + new Date().getFullYear()
          const time = new Date().getHours().toString().padStart(2, '0') + '_' + new Date().getMinutes().toString().padStart(2, '0')
          fileLink.setAttribute('download', 'orderlist_shop_' + date + '_' + time + '.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      } catch (error) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    goDetailOrder (order, bill) {
      // console.log('val', val)
      if ((order !== undefined) && (bill !== undefined)) {
        if (this.MobileSize) {
          this.$router.push(`/DetailOrderProductShopMobile?orderNumber=${order}&billNumber=${bill}`).catch(() => {})
          // this.$router.push({ path: '/DetailOrderProductShopMobile' }).catch(() => { })
        } else {
          this.$router.push(`/DetailOrderProductShop?orderNumber=${order}&billNumber=${bill}`).catch(() => {})
          // this.$router.push({ path: '/DetailOrderProductShop' }).catch(() => { })
        }
      }
    },
    formatDateService (dateString) {
      const [year, month, day] = dateString.split('-')
      return `${day}/${month}/${year}`
    }
  }
}
</script>

<style scoped>
  ::v-deep .theme--light.v-data-table > .v-data-table__wrapper > table > tbody > tr:not(:last-child) > td:not(.v-data-table__mobile-row) {
    white-space: nowrap !important;
  }
</style>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(11) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
          text-align: center !important;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(11) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          text-align: center !important;
        }
      }
    }
  }
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
