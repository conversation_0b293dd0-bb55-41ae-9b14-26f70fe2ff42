<template>
  <div class="pa-6" style="min-height: 500px;">
    <div v-if="!connected">
      <div style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333; display: flex;"><v-spacer></v-spacer><v-btn color="primary" @click="createdRoomModalOpen">สร้างห้องใหม่</v-btn></div>
      <!-- <v-row justify="center" class="">
        <v-col cols="4" align="center" align-self="center">
          <v-col cols="10" align="start">
            <span>ห้องไลฟ์</span>
            <v-text-field
              v-model="shopName"
              dense
              hide-details
              outlined
              placeholder="กรอกชื่อห้องไลฟ์"
            ></v-text-field>
          </v-col>
          <v-col cols="10" align="start">
            <span>หัวข้อการไลฟ์</span>
            <v-text-field
              v-model="liveTitle"
              dense
              hide-details
              outlined
              placeholder="กรอกชื่อหัวข้อการไลฟ์"
            ></v-text-field>
          </v-col>
          <v-btn color="primary" @click="createRoome" :disabled="roomName == '' ||  participantName == ''">Create Room</v-btn>
        </v-col>
      </v-row> -->
      <v-row>
        <v-col>
          <DafaultVideoComponent
            v-if="localTrack"
          />
        </v-col>
      </v-row>
      <v-dialog
        v-model="createdRoomModal"
        width="50%"
        :style="MobileSize ? 'z-index: 16000004' : ''"
      >
        <v-card style="border-radius: 5px">
          <div style="font-weight: bold; font-size: 24px; line-height: 32px; color: #ffffff; background-color: #38b2a4;" class="pa-5">สร้างห้องไลฟ์</div>
            <v-col align="center" class="justify-center">
              <v-col cols="10" align="start">
                <span>ห้องไลฟ์</span>
                <v-text-field
                v-model="shopName"
                dense
                hide-details
                outlined
                placeholder="กรอกชื่อห้องไลฟ์"
                ></v-text-field>
              </v-col>
              <v-col cols="10" align="start">
                <span>หัวข้อการไลฟ์</span>
                <v-text-field
                v-model="liveTitle"
                dense
                hide-details
                outlined
                placeholder="กรอกชื่อหัวข้อการไลฟ์"
                ></v-text-field>
              </v-col>
              <v-col>
                <v-btn color="primary" @click="createRoome" :disabled="roomName == '' ||  participantName == ''">Create Room</v-btn>
              </v-col>
          </v-col>
        </v-card>
      </v-dialog>
    </div>

    <div v-else>
      <VideoComponent
        v-if="localTrack"
        :track="localTrack"
        :participantIdentity="`Host ${shopName}`"
        :local="true"
        :message="message"
        :token="token"
        :room="room"
        @leaveRoom="leaveRoom"
        @pauseVideo="pauseVideoLive"
        :liveTitle="liveTitle"
      />
    </div>

    <Dialog
      :modal = dialog
      :status="status"
      :title="title"
      :message="body"
      :message2="body2"
      @close="dialog = false"
    />
  </div>
</template>

<script>
import VideoComponent from './component/VideoComponentShop.vue'
import DafaultVideoComponent from './component/DefaultVideoComponent.vue'
import Dialog from './component/DialogStatus.vue'
// import { io } from 'socket.io-client'
import {
  Room,
  createLocalAudioTrack
} from 'livekit-client'
// const socket = io('http://**********:3000')
export default {
  components: {
    VideoComponent,
    DafaultVideoComponent,
    Dialog
  },
  data () {
    return {
      shopName: '',
      shopID: '',

      roomName: '',
      participantName: '',

      room: null,
      token: null,
      localTrack: null,

      connected: false,
      // message: 'Hello everyone' + Math.floor(Math.random() * 100),
      message: '',

      dialog: false,
      status: '',
      title: '',
      body: '',
      body2: '',

      // LIVEKIT_URL: 'wss://helloworld-nt1b7zmh.livekit.cloud',
      LIVEKIT_URL: 'wss://meet-lab.one.th',
      liveTitle: '',
      createdRoomModal: false,
      audioTrack: null
      // socket: io('http://**********:3000')
      // socket: io('https://devinet-eprocurement.one.th', {
      //   path: '/api/backend_2/socket.io',
      //   transports: ['websocket']
      // })
      // socket: io(process.env.VUE_APP_BACK_END2.substring(0, 35), {
      //   path: '/api/backend_2/socket.io'
      // })
    }
  },
  created () {
    if (localStorage.getItem('shopDetail') !== '' || localStorage.getItem('shopDetail') !== null || localStorage.getItem('shopDetail') !== undefined) {
      this.getAllRoom()
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.shopID = shopDetail.id
      this.shopName = shopDetail.name
      this.roomName = 'Host' + this.shopID
      this.participantName = this.roomName
    } else {
      this.dialog = true
      this.status = 'failed'
      this.title = 'ไม่สามารถระบุร้านค้าได้'
      this.body = 'กรุณาเข้าสู่ระบบใหม่'
      this.body2 = 'หากยังไม่สามารถให้บริการ กรุณาติดต่อเจ้าหน้าที่'
    }
    this.defaultRoom()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async mounted () {
    if (this.room) {
      await this.room.disconnect()
    }
    await this.useActionDeleteRoom(this.roomName, true)
  },
  async beforeDestroy () {
    if (this.room) {
      await this.room.disconnect()
      await this.useActionDeleteRoom(this.roomName, true)
      await this.$swal.fire({
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        icon: 'warning',
        title: 'กำลังปิดห้องไฟล์เมื่อออกจากหน้า live stream'
      })
    }
    // window.removeEventListener('beforeunload', yourHandler)
    // socket.off('new_live')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/hostMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/host' }.catch(() => {}))
      }
    }
  },
  methods: {
    async createRoome () {
      var accessCamera = await this.checkPermission()
      if (accessCamera) {
        return null
      }
      this.$store.commit('openLoader')
      try {
        // socket.emit('new_live', 'Hello from Vue!')
        var tokenResponse = await this.getToken(this.roomName, this.participantName, this.liveTitle)
        // console.log(tokenResponse)
        if (tokenResponse.code === 200) {
          this.room = new Room()
          this.token = tokenResponse.token
          await this.room.connect(this.LIVEKIT_URL, this.token)
          await this.room.localParticipant.enableCameraAndMicrophone()
          // const videoPublication = this.room.localParticipant.videoTrackPublications.values().next().value
          const videoPublication = this.room.localParticipant.videoTrackPublications.values().next().value
          const audioPublication = this.room.localParticipant.audioTrackPublications.values().next().value
          this.localTrack = videoPublication ? videoPublication.videoTrack : undefined
          this.connected = true
          // const audioPublication = this.room.localParticipant.audioTrackPublications.values().next().value
          // console.log(audioPublication, 'audioPublication')
          if (!audioPublication) {
            // console.warn('🚨 ไม่มีเสียง, กำลังเพิ่ม Audio Track...')
            const audioTrack = await createLocalAudioTrack()
            await this.room.localParticipant.publishTrack(audioTrack)
          }
          this.audioTrack = await createLocalAudioTrack()
          this.localTrack = await videoPublication ? videoPublication.videoTrack : undefined
          // console.log(this.localTrack, 'local track')

          // setTimeout(() => {
          //   this.socket.emit('new_live')
          // }, 5000)
          // if (audioPublication && audioPublication.track && audioPublication.track.kind === 'audio') {
          //   console.log('🎤 มี Audio Track แล้ว:', audioPublication.track)
          // } else {
          //   console.error('🚨 ไม่พบ Audio Track, ต้องสร้างใหม่')
          // }
          // this.localTrack = audioPublication
          // console.log(this.localTrack, 456)
          window.addEventListener('beforeunload', function (e) {
            if (this.room) {
              this.room.disconnect()
            }
            e.preventDefault()
            e.returnValue = ''
          })
        } else {
          this.dialog = true
          this.status = 'failed'
          this.title = 'สร้างห้องไม่สำเร็จ'
          this.body =
          tokenResponse.message.includes('format') ? 'ข้อมูลร้านค้าไม่ถูกต้อง'
            : tokenResponse.message.includes('ไม่พบ') ? 'ไม่พบข้อมูลร้านค้า'
              : tokenResponse.message
          this.body2 = 'กรุณาตรวจสอบข้อมูลใหม่'
        }
      } catch (error) {
        console.error('There was an error connecting to the room:', error.message)
        // await this.leaveRoom()
      }
      this.$store.commit('closeLoader')
    },
    async getToken (roomName, participantName, title) {
      console.log(process.env.VUE_APP_LIVE_STREAM, 'env')
      const response = await fetch(`${process.env.VUE_APP_LIVE_STREAM}token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roomName, participantName, title })
      })

      var res = await response.json()
      if (res.message.includes('กำลังไลฟ์อยู่')) {
        await this.leaveRoom()
        await this.createRoome()
      } else {
        return await res
      }
    },

    async leaveRoom () {
      this.connected = false
      this.createdRoomModal = false
      if (this.room) {
        this.$store.commit('openLoader')
        await this.room.disconnect()
        await this.deleteRoom(this.roomName, true)
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'success',
          title: 'ปิดห้องไลฟ์แล้ว'
        })
        this.liveTitle = ''
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('openLoader')
        await this.deleteRoom(this.roomName, false)
        this.$store.commit('closeLoader')
      }

      this.room = []
      //  this.localTrack = undefined
      this.localTrack = {}
    },

    async deleteRoom (roomName, roomConnect) {
      console.log(process.env.VUE_APP_LIVE_STREAM, 'env')
      const response = await fetch(`${process.env.VUE_APP_LIVE_STREAM}deleteRoom`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          roomName: roomName,
          roomConnect: roomConnect
        })
      })
      // setTimeout(() => {
      //   this.socket.emit('new_live')
      // }, 5000)
      if (response) {
      }
    },

    async roomOnEverything () {
      this.room = new Room()
      // this.room.on(
      //   RoomEvent.TrackSubscribed,
      //   (_track, publication, participant) => {
      //     this.remoteTracksMap.set(publication.trackSid, {
      //       trackPublication: publication,
      //       participantIdentity: participant.identity
      //     })
      //     this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      //   }
      // )

      // this.room.on(RoomEvent.ParticipantConnected, (participant) => {
      //   console.log('ParticipantDisconnected', participant)
      // })

      // this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      //   console.log('live ParticipantDisconnected')
      //   console.log('first', participant)
      // })

      // this.room.on(RoomEvent.TrackUnsubscribed, (_track, publication) => {
      //   // this.remoteTracksMap.delete(publication.trackSid)
      //   this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      // })
    },
    defaultRoom () {
      this.localTrack = {}
    },
    createdRoomModalOpen () {
      this.createdRoomModal = true
    },
    async getAllRoom () {
      // await this.$store.dispatch('actionGetAllRoom')
      // var response = await this.$store.state.ModuleShop.stateGetAllRoom
      // console.log(response, 'response')
    },
    async useActionDeleteRoom (roomName, roomConnect) {
      this.$store.commit('openLoader')
      var data = {
        roomName: roomName,
        roomConnect: roomConnect
      }
      await this.$store.dispatch('actionDeleteRoom', data)

      // var response = await this.$store.state.ModuleShop.stateDeleteRoom
      this.$store.commit('closeLoader')
      // setTimeout(() => {
      //   console.log(this.socket)
      //   this.socket.emit('new_live')
      // }, 5000)
    },
    async pauseVideoLive () {
      // alert('ทำได้แหละ')
      // console.log(this.room.localParticipant.videoTrackPublications, 456)
      if (this.room.localParticipant.videoTrackPublications) {
        if (this.room.localParticipant.videoTrackPublications.length > 0) {
          this.room.localParticipant.videoTrackPublications.forEach((publication) => {
            // console.log(publication, 'vv')
            if (publication.track) {
              publication.track.setEnabled(false)
              // console.log('สำเร็จ')
            }
          })
        }
      } else {
        console.error('❌ ไม่มี videoTracks หรือ room ยังไม่ connect')
      }
    },
    async checkPermission () {
      await navigator.permissions.query({ name: 'camera' }).then((permissionStatus) => {
        // console.log('Camera permission state is:', permissionStatus.state)

        if (permissionStatus.state === 'granted') {
          // console.log('ผู้ใช้อนุญาต')
          // กล้องได้รับอนุญาตแล้ว
        } else if (permissionStatus.state === 'prompt') {
          // console.log('ยังไม่ได้ขอ permission')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            title: 'คุณยังไม่อนุมัติสิทธ์การเข้าถึงกล้อง'
          })
          return true
          // ยังไม่ได้ขอ permission
        } else if (permissionStatus.state === 'denied') {
          // console.log('ผู้ใช้ไม่อนุญาต')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            title: 'คุณยังไม่อนุมัติสิทธ์การเข้าถึงกล้อง'
          })
          return true
          // ผู้ใช้ไม่อนุญาต
        }

        // ถ้าอยากติดตามการเปลี่ยนแปลง permission:
        permissionStatus.onchange = () => {
          console.log('Camera permission changed to:', permissionStatus.state)
        }
      }).catch((err) => {
        console.error('Permissions API not supported or error occurred:', err)
      })
    }
  }
}
</script>

<style>

</style>
