<template>
  <v-app-bar
     app
     height="100px"
     color="#FFFFFF"
     style="z-index: 12 !important; position: fixed;"
     :style="{'max-width': MobileSize ? '100vw' : '100%'}"
    >
    <v-col cols="12" v-if="MobileSize">
      <v-toolbar-title class="d-flex justify-space-between">
        <!-- logo -->
        <router-link v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :to="{ pathShop }">
          <v-img :class="IpadProSize ? 'pr-4' : ''" v-lazyload :src="require('@/assets/ngc_logo_1.png')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
        />
        </router-link>
        <router-link v-else :to="{ path: pathHome }">
          <v-img v-lazyload :class="IpadProSize ? 'pr-4' : ''" :src="require('@/assets/ngc_logo_1.png')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
            @mousedown.right="goHomeRightClick()" />
        </router-link>
        <v-tooltip location="bottom">
          <template #activator="{ on, attrs }">
            <v-card
              class="d-flex align-center"
              outlined
              style="border: 0;"
              v-bind="attrs"
              v-on="on"
            >
              <img
                src="@/assets/ICON/iconRegister.png"
                alt=""
                width="40"
                height="40"
              />
            </v-card>
          </template>
          <span>ข้อมูลสำหรับผู้เข้าใช้งานระบบ Nex Gen Commerce</span>
        </v-tooltip>
        <v-tooltip location="bottom">
          <template #activator="{ on, attrs }">
            <v-card
              class="d-flex align-center"
              outlined
              style="border: 0;"
              v-bind="attrs"
              v-on="on"
            >
              <img
                src="@/assets/ICON/iconForm.png"
                alt=""
                width="40"
                height="40"
                style="filter: grayscale(100%);"
              />
            </v-card>
          </template>
          <span>ข้อมูลการลงทะเบียน</span>
        </v-tooltip>
        <v-tooltip location="bottom">
          <template #activator="{ on, attrs }">
            <v-card
              class="d-flex align-center"
              outlined
              style="border: 0;"
              v-bind="attrs"
              v-on="on"
            >
              <img
                src="@/assets/ICON/iconRegisConfirm.png"
                alt=""
                width="40"
                height="40"
                style="filter: grayscale(100%);"
              />
            </v-card>
          </template>
          <span>ยืนยันข้อมูลการลงทะเบียน</span>
        </v-tooltip>
      </v-toolbar-title>
    </v-col>
    <v-col cols="12" v-else>
      <v-toolbar-title class="d-flex px-10" :class="!IpadSize && !IpadProSize ? '' : 'justify-space-between'" :style="!IpadSize && !IpadProSize ? 'gap: 12vw;' : ''">
        <!-- logo -->
        <router-link v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :to="{ pathShop }">
          <v-img :class="IpadProSize ? 'pr-4' : ''" v-lazyload :src="require('@/assets/ngc_logo_1.png')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
        />
        </router-link>
        <router-link v-else :to="{ path: pathHome }">
          <v-img v-lazyload :class="IpadProSize ? 'pr-4' : ''" :src="require('@/assets/ngc_logo_1.png')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
            @mousedown.right="goHomeRightClick()" />
        </router-link>
        <div>
          <v-card class="d-flex flex-column align-center" outlined style="border: 0;">
            <img src="@/assets/ICON/iconRegister.png" alt="" width="40" height="40" :style="step === 1 ? '' : 'filter: grayscale(100%);'">
            <span style="font-size: medium;">ข้อมูลสำหรับผู้เข้าใช้งานระบบ Nex Gen Commerce</span>
          </v-card>
        </div>
        <div>
          <v-card class="d-flex flex-column align-center" outlined style="border: 0;">
            <img src="@/assets/ICON/iconForm.png" alt="" width="40" height="40" :style="step === 2 ? '' : 'filter: grayscale(100%);'">
            <span style="font-size: medium;">ข้อมูลการลงทะเบียน</span>
          </v-card>
        </div>
        <div>
          <v-card class="d-flex flex-column align-center" outlined style="border: 0;">
            <img src="@/assets/ICON/iconRegisConfirm.png" alt="" width="40" height="40" :style="step === 3 ? '' : 'filter: grayscale(100%);'">
            <span style="font-size: medium;">ยืนยันข้อมูลการลงทะเบียน</span>
          </v-card>
        </div>
      </v-toolbar-title>
    </v-col>
  </v-app-bar>
</template>

<script>
export default {
  props: ['step'],
  data () {
    return {
      dataRole: 'ext_buyer',
      pathHome: '/'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    goHome () {
      var val
      var data
      if (this.MobileSize) {
        this.$EventBus.$emit('GetLink')
      }
      localStorage.removeItem('pathAdmin')
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        this.$EventBus.$emit('resetAdminShop')
        if (this.onedata.user !== undefined) {
          if (localStorage.getItem('roleUserApprove') !== null) {
            // console.log('เข้าเงื่อนไขขขขขขขขข')
            val = JSON.parse(localStorage.getItem('roleUser'))
            data = {
              role: val.role === 'purchaser' ? 'purchaser' : 'ext_buyer'
            }
            val.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
            localStorage.setItem('roleUser', JSON.stringify(data))
            this.$EventBus.$emit('LinkPage', val.role)
            this.searchtext = ''
            this.selectCategory = ''
            this.$router.push({ path: '/' }).catch(() => {})
          } else if (this.dataRole === 'sale_order' || this.dataRole === 'sale_order_no_JV') {
            this.searchtext = ''
            this.selectCategory = ''
            this.$router.push({ path: this.pathShop }).catch(() => {})
          } else {
            this.searchtext = ''
            this.selectCategory = ''
            this.$router.push({ path: '/' }).catch(() => {})
          }
        } else {
          localStorage.removeItem('oneData')
          this.$router.push({ path: '/' }).catch(() => {})
        }
      } else {
        this.searchtext = ''
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
  }
}
</script>

<style>

</style>
<style scoped>
.backgroundAppBar {
  background: linear-gradient(90deg, #2D95FF -4%, #54ECB5 103.33%);
  /* border-bottom: 1px solid #F5F5F5; */
  box-shadow: 0px 0px 1px 0px #C3CDD517;
  box-shadow: 0px 0px 1px 0px #C3CDD50D;
  box-shadow: 0px 0px 1px 0px #C3CDD503;
  box-shadow: 1px 0px 1px 0px #C3CDD500;
}
</style>
