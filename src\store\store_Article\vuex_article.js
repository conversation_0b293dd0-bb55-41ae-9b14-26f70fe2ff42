import AxiosArticle from './axios_article.js'

const ModuleArticle = {
  state: {
    stateListProductManageArticle: [],
    stateListProductManageArticleV2: [],
    stateGetCategoryShopList: [],
    stateAddArticleV2Video: [],
    stateAddArticleV2Image: [],
    stateListArticle: [],
    stateListArticleWithSellerShop: [],
    stateUploadFileToS3: [],
    stateDetailArticle: [],
    stateEditArticle: [],
    stateDeleteArticle: [],
    stateChangeStatus: [],
    stateListProductAritcle: [],
    stateListProductAritcleV2: []
  },
  mutations: {
    mutationListProductManageArticle (state, data) {
      state.stateListProductManageArticle = data
    },
    mutationListProductManageArticleV2 (state, data) {
      state.stateListProductManageArticleV2 = data
    },
    mutationGetCategoryShopList (state, data) {
      state.stateGetCategoryShopList = data
    },
    mutationAddArticleV2Videot (state, data) {
      state.stateAddArticleV2Video = data
    },
    mutationAddArticleV2Image (state, data) {
      state.stateAddArticleV2Image = data
    },
    mutationListArticle (state, data) {
      state.stateListArticle = data
    },
    mutationListArticleWithSellerShop (state, data) {
      state.stateListArticleWithSellerShop = data
    },
    mutationUploadFileToS3 (state, data) {
      state.stateUploadFileToS3 = data
    },
    mutationDetailArticle (state, data) {
      state.stateDetailArticle = data
    },
    mutationEditArticle (state, data) {
      state.stateEditArticle = data
    },
    mutationDeleteArticle (state, data) {
      state.stateDeleteArticle = data
    },
    mutationChangeStatus (state, data) {
      state.stateChangeStatus = data
    },
    mutationListProductAritcle (state, data) {
      state.stateListProductAritcle = data
    },
    mutationListProductAritcleV2 (state, data) {
      state.stateListProductAritcleV2 = data
    }
  },
  actions: {
    async actionsListProductManageArticle (context, access) {
      const response = await AxiosArticle.GetProductManageArticle(access)
      await context.commit('mutationListProductManageArticle', response)
    },
    async actionsListProductManageArticleV2 (context, access) {
      const response = await AxiosArticle.GetProductManageArticleV2(access)
      await context.commit('mutationListProductManageArticleV2', response)
    },
    async actionsGetCategoryShopList (context, access) {
      var responseData = await AxiosArticle.GetCategoryShopList(access)
      await context.commit('mutationGetCategoryShopList', responseData)
    },
    async actionsAddArticleV2Video (context, access) {
      var responseData = await AxiosArticle.AddArticleV2Video(access)
      await context.commit('mutationAddArticleV2Videot', responseData)
    },
    async actionsAddArticleV2Image (context, access) {
      var responseData = await AxiosArticle.AddArticleV2Image(access)
      await context.commit('mutationAddArticleV2Image', responseData)
    },
    async actionsListArticle (context, access) {
      var responseData = await AxiosArticle.ListArticle(access)
      await context.commit('mutationListArticle', responseData)
    },
    async actionsListArticleWithSellerShop (context, access) {
      var responseData = await AxiosArticle.ListArticleWithSellerShop(access)
      await context.commit('mutationListArticleWithSellerShop', responseData)
    },
    async actionsUploadFileToS3 (context, access) {
      var responseData = await AxiosArticle.UploadFileToS3(access)
      await context.commit('mutationUploadFileToS3', responseData)
    },
    async actionsDetailArticle (context, access) {
      var responseData = await AxiosArticle.DetailArticle(access)
      await context.commit('mutationDetailArticle', responseData)
    },
    async actionsEditArticle (context, access) {
      var responseData = await AxiosArticle.EditArticle(access)
      await context.commit('mutationEditArticle', responseData)
    },
    async actionsDeleteArticle (context, access) {
      var responseData = await AxiosArticle.DeleteArticle(access)
      await context.commit('mutationDeleteArticle', responseData)
    },
    async actionsChangeStatus (context, access) {
      var responseData = await AxiosArticle.ChangeStatus(access)
      await context.commit('mutationChangeStatus', responseData)
    },
    async actionsListProductAritcle (context, access) {
      var responseData = await AxiosArticle.ListProductAritcle(access)
      await context.commit('mutationListProductAritcle', responseData)
    },
    async actionsListProductAritcleV2 (context, access) {
      var responseData = await AxiosArticle.ListProductAritcleV2(access)
      await context.commit('mutationListProductAritcleV2', responseData)
    }
  }
}

export default ModuleArticle
