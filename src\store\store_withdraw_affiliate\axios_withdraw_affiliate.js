import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async getTransactionUserAffiliate (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/affiliate/transaction_affiliate_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getDashboardAffiliate (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/affiliate/dashboard_affiliate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getTransferAffiliateClick (data) {
    const auth = await GetToken(data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/transferAffiliateClick`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
