<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
  <!-- <v-container grid-list-xs> -->
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ใบเสนอราคา</v-card-title>
      <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#1AB759" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> ใบเสนอราคา</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="px-2 py-0">
          <a-tabs @change="SelectDetailOrder">
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)" style="border-radius: 40px;">{{ countPOAll }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">สร้างรายการสั่งซื้อแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8" style="border-radius: 40px;">{{ countPOSuccess }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8" style="border-radius: 40px;">{{ countPOActive }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">รออนุมัติ/รอตรวจสอบเอกสาร <v-chip small text-color="#FF710B" color="#FBECE1" style="border-radius: 40px;">{{ countPOWaitingApprove }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="6"><span slot="tab">รออนุมัติฝั่งผู้ซื้อ <v-chip small text-color="#FF710B" color="#FBECE1" style="border-radius: 40px;">{{ countPOWaitingApproveCompany }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="7"><span slot="tab">รอผู้ขายอนุมัติ <v-chip small text-color="#FF710B" color="#FBECE1" style="border-radius: 40px;">{{ countPOWaitingShopApprove }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">ปฏิเสธ <v-chip small text-color="#F5222D" color="rgba(245, 34, 45, 0.10)" style="border-radius: 40px;">{{ countPOReject }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="5"><span slot="tab">ยกเลิก <v-chip small text-color="#636363" color="#E6E6E6" style="border-radius: 40px;">{{ countPOCancel }}</v-chip></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col cols="12" md="7" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'px-3 mb-3'">
          <v-text-field v-if="countPOAll !== 0" v-model="search" dense hide-details outlined placeholder="ค้นหาจากชื่อร้านค้าหรือหมายเลขใบเสนอราคา" style="border-radius: 8px;">
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col :class="MobileSize ? 'px-3' : IpadSize ? 'px-3' : ''" cols="12" md="5" sm="12">
          <v-row dense>
            <v-col cols="3" md="3" sm="2" class="mt-2" :class="IpadSize ? 'pl-0' : 'pl-0'">
              <span style="font-size: 16px;" class="pt-5">
                Pay Type :
              </span>
            </v-col>
            <v-col cols="9" md="8" sm="10" :class="MobileSize ? '' : IpadSize ? 'mb-2' : 'ml-2 mr-4'">
              <v-select
                outlined
                dense
                v-model="statePayType"
                style="border-radius: 8px;"
                :items="['ทั้งหมด','recurring','onetime','general']"
                @change="selectType()"
                placeholder="ทั้งหมด"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="7" sm="12" class="" :class="!MobileSize ? 'pl-4 pr-3 mb-0 mt-1' : 'px-3 mb-3 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 0">รายการใบเสนอราคาทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่สร้างรายการสั่งซื้อแล้วทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 2">รายการใบเสนอราคาที่อนุมัติแล้วทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 3">รายการใบเสนอราคาที่รออนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 4">รายการใบเสนอราคาที่ปฏิเสธทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 5">รายการใบเสนอราคาที่ยกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 6">รายการใบเสนอราคาที่รออนุมัติฝั่งผู้ซื้อทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 7">รายการใบเสนอราคาที่รอผู้ขายอนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
        </v-col>
        <v-col v-if="disableTable === false" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-3' : 'px-3 mb-3'">
        </v-col>
        <v-col v-if="disableTable === false" cols="12" md="7" sm="12" class="" :class="!MobileSize ? 'pl-4 pr-3 mb-0 mt-1' : 'px-3 mb-3 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 0">รายการใบเสนอราคาทั้งหมด {{ countPOAll }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 1">รายการใบเสนอราคาที่สร้างรายการสั่งซื้อแล้วทั้งหมด {{ countPOSuccess }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 2">รายการใบเสนอราคาที่อนุมัติแล้วทั้งหมด {{ countPOActive }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 3">รายการใบเสนอราคาที่รออนุมัติทั้งหมด {{ countPOWaitingApprove }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 4">รายการใบเสนอราคาที่ปฏิเสธทั้งหมด {{ countPOReject }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 5">รายการใบเสนอราคาที่ยกเลิกทั้งหมด {{ countPOCancel }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 6">รายการใบเสนอราคาที่รออนุมัติฝั่งผู้ซื้อทั้งหมด {{ countPOWaitingApproveCompany }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 7">รายการใบเสนอราคาที่รอผู้ขายอนุมัติทั้งหมด {{ countPOWaitingShopApprove }} รายการ</span>
        </v-col>
        <v-col v-if="disableTable === false"  cols="12" md="5" align="end">
          <!-- <v-row  class="d-flex justify-end" no-gutters>
            <v-col cols="3" class="mt-2">
              <span style="font-size: 16px;" class="pt-5">
                Pay Type :
              </span>
            </v-col>
            <v-col cols="5" class="ml-2 mr-4">
              <v-select
                outlined
                dense
                v-model="statePayType"
                :items="['ทั้งหมด','recurring','onetime']"
                @change="selectType()"
                placeholder="ทั้งหมด"
                hide-details
              ></v-select>
            </v-col>
          </v-row> -->
        </v-col>
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-4 my-5" min-height="436">
            <v-data-table
              :headers="isJV === 'yes' ? headersJV : headers"
              :items="DataTable"
              :search="search"
              :page.sync="page"
              style="width:100%;"
              height="100%"
              @pagination="countOrdar"
              :items-per-page="10"
              class=""
              no-results-text="ไม่พบชื่อบริษัทหรือหมายเลขใบเสนอราคาที่ค้นหา"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.qu_status`]="{ item }">
                <span v-if="item.qu_status === 'success'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0FEE8" text-color="#52C41A">สร้างรายการสั่งซื้อแล้ว</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#F0FEE8" text-color="#52C41A">อนุมัติแล้ว</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'cancel'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" text-color="#636363" color="#E6E6E6">ยกเลิก</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'waiting'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FEF6E6" text-color="#E9A016">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'check_doc'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รอตรวจสอบเอกสาร</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'waiting_approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รออนุมัติฝั่งผู้ซื้อ</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'waiting_shop_approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" color="#FBECE1" text-color="#FF710B">รอผู้ขายอนุมัติ</v-chip>
                </span>
                <span v-else-if="item.qu_status === 'reject'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">ปฏิเสธ</v-chip>
                </span>
                <span v-else>
                  <v-chip :class="!MobileSize ? 'ma-2' : 'mb-2'" :color="item.status_approve_detail === '-' || item.status_approve_detail === 'รออนุมัติ' ? '#FCF0DA' : '#F7D9D9'" :text-color="item.status_approve_detail === '-' || item.status_approve_detail === 'รออนุมัติ' ?'#FAAD14' : '#F5222D'">{{ item.status_approve_detail === '-' ? 'รออนุมัติ' : item.status_approve_detail }}</v-chip>
                </span>
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{item.created_at}}
              </template>
              <template v-slot:[`item.pay_type`]="{ item }">
                <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                {{ item.payment_transaction_number === null ? '-' : item.payment_transaction_number}}
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <!-- <v-btn width="24" height="24" icon style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"  outlined small @click="goPDFQU(item)" :disabled="item.qu_status === 'check_doc' && item.is_order_JV === 'yes'">
                  <v-icon color="#27AB9C" size="18">mdi-file-document-outline</v-icon>
                </v-btn>
                <v-btn text rounded color="#27AB9C" small @click="goDetailQU(item)">
                  <b style="text-decoration: underline;">รายละเอียด</b>
                </v-btn> -->
                <v-btn text rounded color="#27AB9C" small @click="goDetailQU(item)">
                  <v-icon class="pr-1" color="27AB9C" small>mdi-file-document-outline</v-icon>
                  <b>รายละเอียด</b><v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0"><b>คุณยังไม่มีรายการใบเสนอราคา</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1"><b>คุณยังไม่มีรายการใบเสนอราคาที่สร้างรายการสั่งซื้อแล้ว</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2"><b>คุณยังไม่มีรายการใบเสนอราคาที่อนุมัติแล้ว</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 3"><b>คุณยังไม่มีรายการใบเสนอราคาที่รออนุมัติ</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 4"><b>คุณยังไม่มีรายการใบเสนอราคาที่ปฏิเสธ</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 5"><b>คุณยังไม่มีรายการใบเสนอราคาที่ยกเลิก</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 6"><b>คุณยังไม่มีรายการใบเสนอราคาที่รออนุมัติฝั่งผู้ซื้อ</b></h2>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 7"><b>คุณยังไม่มีรายการใบเสนอราคาที่รอผู้ขายอนุมัติ</b></h2>
        </v-col>
      </v-row>
    </v-card>
    <!-- dialog check doc -->
    <v-dialog v-model="dialogCheckDoc" persistent :width="MobileSize ? '100%' : '80%'">
      <v-card class=".rounded-lg" elevation="0">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px; font-weight: bold;">
            <font color="#27AB9C">รายการสั่งซื้อ</font>
          </span>
          <v-btn icon dark @click="ClosedialogCheckDoc()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text :class="MobileSize ? 'px-3' : ''">
          <v-row class="mt-1">
            <!-- start เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
            <!-- <v-col cols="12">
              <v-card style="padding-bottom: 15px">
                <v-col cols="12" class="pl-5 pt-5 pb-0">
                  <p :style="MobileSize ? 'font-size: 24px;' : 'font-size: 24px;'">
                    <b>ที่อยู่ในการจัดส่งใบกำกับภาษี</b>
                  </p>
                </v-col>
                <v-row dense no-gutters class="ml-4 pt-0">
                  <v-col cols="12">
                    <v-radio-group v-model="taxRoles" row style="margin-top: -10px">
                      <v-radio color="#27AB9C" label="ผู้ใช้ทั่วไป" value="Personal" v-if="role.role !== 'purchaser'">
                      </v-radio>
                      <v-radio color="#27AB9C" label="นิติบุคคล" value="Business"></v-radio>
                      <v-radio color="#27AB9C" label="ไม่รับใบกำกับภาษี" value="No"></v-radio>
                    </v-radio-group>
                  </v-col>
                </v-row>
                <v-col v-if="taxAddress === '' && taxRoles !== 'No'" cols="12" align="left" class="pt-2 pl-5">
                  <v-btn outlined icon small color="#A1A1A1" @click="openModalTaxAddress()">
                    <v-icon color="#A1A1A1" small>mdi-plus</v-icon>
                  </v-btn>
                  <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี</b></span>
                </v-col>
                <v-col v-else-if="taxAddress !== '' && taxRoles !== 'No'" cols="12" align="left" class="pt-0 pl-5">
                  {{ companyName }} {{ companyTaxID }} {{ taxAddress }}
                </v-col>
              </v-card>
            </v-col> -->
            <!--end เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
            <!-- start ส่วนแสดงข้อมูลสินค้า -->
            <!-- Desktop -->
            <v-col cols="12" v-if="!MobileSize">
              <v-card>
                <v-col cols="12" class="pl-5 pt-5">
                  <v-row dense>
                    <p style="font-size: 24px"><b>รายการสั่งซื้อสินค้า</b></p>
                    <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 8px; margin-left: 10px;"></v-spacer>
                  </v-row>
                </v-col>
                <v-container grid-list-xs>
                  <a-table bordered v-for="(item,index) in itemsCart.choose_list" :key="index"
                    :data-source="item.product_list" :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id" :columns="headersTable" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col justify="center">
                          <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30"
                            height="30"></v-img>
                          <b class="ml-3" style="line-height: 30px;">{{item.product_list[0].seller_shop_name}}</b>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="12" md="4" class="pr-0">
                          <v-img :src="`${record.product_image}`" contain
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''" @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                            v-else @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0 captionSku">รหัสสินค้า: {{record.sku}}<br />{{record.product_name}}</p>
                          <p class="mb-0 captionSku">ราคา: {{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="revenue_default" slot-scope="text, record">
                      <v-col cols="12">
                        <span>{{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </template>
                    <template slot="total_revenue_default" slot-scope="text, record">
                      <span>{{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                    <template slot="item_code_pr" slot-scope="text, record">
                      <v-row>
                        <v-col>
                          <v-autocomplete
                            v-model="record.item_code_pr_buyer"
                            :items="itemCodePrList"
                            outlined
                            dense
                            style="height: 50px;"
                            item-text="material_name"
                            item-value="material_code"
                            @change="updateSelectPr()"
                            no-data-text="ไม่พบ Item Code PR"
                          ></v-autocomplete>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                </v-container>
              </v-card>
            </v-col>
            <!-- Mobile Ipad -->
            <v-col cols="12" v-else>
              <v-card>
                <v-col cols="12" class="pl-5 pt-5">
                  <p :style="MobileSize ? 'font-size: 18px' : 'font-size: 20px'"><b>รายการสั่งซื้อสินค้า</b></p>
                </v-col>
                <v-container>
                  <a-table bordered v-for="(item,index) in itemsCart.choose_list" :key="index"
                    :data-source="item.product_list"
                    :rowKey="record => record.have_attribute === 'no' ? record.sku : record.product_attribute_detail.product_attribute_id"
                    :columns="headersMobile"
                    :showHeader="false" :pagination="false">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col justify="center">
                          <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30"
                            height="30"></v-img>
                          <b class="ml-3" style="line-height: 30px;" >{{item.seller_shop_name}}</b>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <v-row>
                        <v-col cols="4" md="4" class="pr-0">
                          <v-img :src="`${record.product_image}`" contain
                            :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'"
                            v-else />
                        </v-col>
                        <v-col cols="8" md="8">
                          <p class="mb-0 captionSku">รหัสสินค้า: {{record.sku}}<br />{{record.product_name}}</p>
                          <p class="mb-0 captionSku">ราคา: {{ Number(record.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                          <p class="mb-0 captionSku">จำนวน: {{ record.quantity }}</p>
                          <p class="mb-0 captionSku">ราคารวม: {{ Number(record.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                            <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span>
                          </p>
                          <p class="mb-0 captionSku">Item Code PR:</p>
                          <v-autocomplete
                            class="mt-1"
                            v-model="record.item_code_pr_buyer"
                            :items="itemCodePrList"
                            outlined
                            dense
                            style="height: 50px;"
                            :style="MobileSize ? 'width: 100%;' : 'width: 60%;'"
                            item-text="material_code"
                            item-value="material_code"
                            @change="updateSelectPr()"
                            no-data-text="ไม่พบ Item Code PR"
                          ></v-autocomplete>
                        </v-col>
                      </v-row>
                    </template>
                  </a-table>
                </v-container>
              </v-card>
            </v-col>
            <!-- end ส่วนแสดงข้อมูลสินค้า -->
            <!-- ส่วนกรอกข้อมูลรายละเอียดรายการสั่งซื้อ -->
            <v-col cols="12" md="12" class="mt-6">
              <v-card>
                <v-card-text class="pa-1">
                  <v-col cols="12" class="pl-5 pt-5">
                    <v-row dense>
                      <p :style="MobileSize ? 'font-size: 18px' : 'font-size: 24px'" style="color: #333333;"><b>รายละเอียดรายการสั่งซื้อ</b></p>
                      <v-spacer style="border-top: 2px solid #EBEBEB; margin-top: 8px; margin-left: 10px;"></v-spacer>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-form ref="orderListDetails" :lazy-validation="lazy">
                      <v-row dense>
                        <v-col cols="12" md="4" sm="6">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่เริ่มสัญญา <span style="color: red;">*</span></span>
                          <v-dialog
                            ref="dialogContractStartDate"
                            v-model="modalContractStartDate"
                            persistent
                            width="290px"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-text-field readonly v-model="contractStartDate" v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                            </template>
                            <v-date-picker
                              color="#27AB9C"
                              v-model="date"
                              scrollable
                              reactive
                              locale="Th-th"
                            >
                              <v-spacer></v-spacer>
                              <v-btn
                                text
                                color="primary"
                                @click="closeModalContractStartDate()"
                              >
                                ยกเลิก
                              </v-btn>
                              <v-btn
                                text
                                color="primary"
                                @click="setValueContractStartDate(date)"
                              >
                                ตกลง
                              </v-btn>
                            </v-date-picker>
                          </v-dialog>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่สิ้นสุดสัญญา <span style="color: red;">*</span></span>
                          <v-dialog
                            ref="dialogContractEndDate"
                            v-model="modalContractEndDate"
                            persistent
                            width="290px"
                          >
                            <template v-slot:activator="{ on, attrs }">
                              <v-text-field :disabled="searchContractStartDate !== '' ? false : true" readonly v-model="contractEndDate" v-bind="attrs" v-on="on" outlined dense placeholder="วว/ดด/ปปปป"><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
                            </template>
                            <v-date-picker
                              color="#27AB9C"
                              v-model="date1"
                              scrollable
                              reactive
                              locale="Th-th"
                              :min="setMinDateContractEndDate"
                            >
                              <v-spacer></v-spacer>
                              <v-btn
                                text
                                color="primary"
                                @click="closeModalContractEndDate()"
                              >
                                ยกเลิก
                              </v-btn>
                              <v-btn
                                text
                                color="primary"
                                @click="setValueContractEndDate(date1)"
                              >
                                ตกลง
                              </v-btn>
                            </v-date-picker>
                          </v-dialog>
                        </v-col>
                        <v-col cols="12" md="4" sm="6" :class="!MobileSize && !IpadProSize && !IpadSize ? 'pl-4 m-auto' : 'mb-3'">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">Pay Type : {{ payType }} </span>
                        </v-col>
                        <!-- ชื่อผู้ซื้อ -->
                        <v-col cols="12" md="12" sm="12" class="mt-2">
                          <span style="font-size: 16px;font-weight: 600;">ชื่อผู้ซื้อ</span>
                        </v-col>
                        <v-col cols="12" md="5" sm="8">
                          <span>ชื่อ-สกุล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Name" v-model="buyer_name" placeholder="ระบุชื่อ-สกุล" outlined dense @keydown="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3" sm="2">
                          <span>เบอร์โทร</span><span style="color: red;"> *</span>
                          <v-text-field maxlength="10" :rules="Rules.Phone" v-model="buyer_phone" placeholder="ระบุเบอร์โทร" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="CheckSpacebarName($event)"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="2">
                          <span>อีเมล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Email" v-model="buyer_email" placeholder="ระบุอีเมลล์" outlined dense ></v-text-field>
                        </v-col>
                        <!-- หัวหน้าผู้ขอซื้อ -->
                        <v-col cols="12" md="12" sm="12" class="mt-2">
                          <span style="font-size: 16px;font-weight: 600;">หัวหน้าผู้ขอซื้อ</span>
                        </v-col>
                        <v-col cols="12" md="5" sm="8">
                          <span>ชื่อ-สกุล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Name" v-model="Name_Buyer" placeholder="ระบุชื่อ-สกุล" outlined dense @keypress="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3" sm="2">
                          <span>เบอร์โทร</span><span style="color: red;"> *</span>
                          <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Buyer" placeholder="ระบุเบอร์โทร" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="CheckSpacebarName($event)"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="2">
                          <span>ตำแหน่ง</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Position" v-model="Position_Buyer" placeholder="ระบุตำแหน่ง" outlined dense @keypress="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="5" sm="2">
                          <span>อีเมล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Email" v-model="Email_Buyer" placeholder="ระบุอีเมล" outlined dense  ></v-text-field>
                        </v-col>
                        <!-- คณะผู้ตรวจรับ 1 -->
                        <v-col cols="12" md="12" sm="12" class="mt-2">
                          <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 1</span>
                        </v-col>
                        <v-col cols="12" md="5" sm="8">
                          <span>ชื่อ-สกุล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Name" v-model="Name_Audit1" placeholder="ระบุชื่อ-สกุล" outlined dense @keypress="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3" sm="2">
                          <span>เบอร์โทร</span><span style="color: red;"> *</span>
                          <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Audit1" placeholder="ระบุเบอร์โทร" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="CheckSpacebarName($event)"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="2">
                          <span>ตำแหน่ง</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Position" v-model="Position_Audit1" placeholder="ระบุตำแหน่ง" outlined dense @keypress="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="5" sm="2" >
                          <span>อีเมล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Email" v-model="Email_Audit1" placeholder="ระบุอีเมล" outlined dense  ></v-text-field>
                        </v-col>
                        <!-- คณะผู้ตรวจรับ 2 -->
                        <v-col cols="12" md="12" sm="12" class="mt-2">
                          <span style="font-size: 16px;font-weight: 600;">คณะผู้ตรวจรับ 2</span>
                        </v-col>
                        <v-col cols="12" md="5" sm="8">
                          <span>ชื่อ-สกุล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Name" v-model="Name_Audit2" placeholder="ระบุชื่อ-สกุล" outlined dense @keypress="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="3" sm="2">
                          <span>เบอร์โทร</span><span style="color: red;"> *</span>
                          <v-text-field maxlength="10" :rules="Rules.Phone" v-model="Phone_Audit2" placeholder="ระบุเบอร์โทร" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="CheckSpacebarName($event)"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="2">
                          <span>ตำแหน่ง</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Position" v-model="Position_Audit2" placeholder="ระบุตำแหน่ง" outlined dense @keypress="CheckSpacebarName($event)" oninput="this.value = this.value.replace(/[^ก-๏a-zA-Z0-9\s]/g, '').replace(/^\s/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="5" sm="2">
                          <span>อีเมล</span><span style="color: red;"> *</span>
                          <v-text-field :rules="Rules.Email" v-model="Email_Audit2" placeholder="ระบุอีเมล" outlined dense ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="12" sm="12">
                          <v-checkbox v-model="contractSet" :readonly="itemsCart.net_price > 50000 ? true : false" label="ต้องการระบุสัญญาบริการ"></v-checkbox>
                        </v-col>
                        <v-col cols="12">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">หมายเหตุ</span>
                          <v-textarea v-model="reason" outlined placeholder="ระบุหมายเหตุ"></v-textarea>
                        </v-col>
                        <v-col cols="12" md="12" sm="12">
                          <span style="line-height: 24px; font-size: 16px; color: #333333; font-weight: 600px;">ข้อมูลสำหรับการออก Purchase Requisition และ Purchase Order</span>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">งบประมาณ<span style="color: red;"> *</span></span>
                          <v-select v-model="selectBudget" :items="itemBudget" item-text="text" item-value="value" outlined dense></v-select>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">ตัดงบ<span style="color: red;"> *</span></span>
                          <v-select v-model="selectCutBudget" :items="itemCutBudget" item-text="text" item-value="value" outlined dense></v-select>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="line-height: 24px; font-size: 16px; color: #333333;">ประเภทเอกสาร<span style="color: red;"> *</span></span>
                          <v-select v-model="selectTypeDoc" :items="itemTypeDoc" item-text="name" item-value="name" outlined dense></v-select>
                        </v-col>
                      </v-row>
                    </v-form>
                  </v-col>
                </v-card-text>
              </v-card>
            </v-col>
            <!-- end -->
            <!-- start ส่วนสรุปรายการสั่งซื้อ -->
            <v-col cols="12">
              <v-card :class="MobileSize || IpadSize || IpadProSize ? 'mt-3' : ''">
                <v-container grid-list-xs>
                  <v-row>
                    <v-col cols="12" class="my-2">
                      <v-row dense>
                        <v-icon color="#27AB9C" class="pr-2">mdi-file-document-outline</v-icon>
                        <span :style="MobileSize ? 'font-size: 18px;' : IpadSize ? 'font-size: 20px;' : 'font-size: 20px;'"><b>สรุปรายการสั่งซื้อสินค้า</b></span>
                      </v-row>
                    </v-col>
                    <v-col cols="8" class="py-0">
                      <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
                    </v-col>
                    <v-col cols="4" align="right" class="py-0">
                      <span><b>{{ itemsCart.total_price_no_vat ? Number(itemsCart.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
                    </v-col>
                    <v-col cols="12">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col cols="8">
                      <span>ส่วนลด</span>
                    </v-col>
                    <v-col cols="4" align="right">
                      <span><b>{{ itemsCart.total_discount ? Number(itemsCart.total_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
                    </v-col>
                    <v-col cols="8">
                      <span>ภาษีมูลค่าเพิ่ม</span>
                    </v-col>
                    <v-col cols="4" align="right">
                      <span><b>{{ itemsCart.total_vat ? Number(itemsCart.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }}</b></span>
                    </v-col>
                    <v-col cols="8">
                      <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
                    </v-col>
                    <v-col cols="4" align="right">
                      <span><b>{{ itemsCart.total_price_vat ? Number(itemsCart.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00'}}</b></span>
                    </v-col>
                    <v-col cols="12" class="">
                      <v-row dense class="pl-1 d-flex">
                        <span class="totalPriceFont mr-auto"><b>ราคารวมทั้งหมด</b></span>
                        <span class="totalPriceFont ml-auto pt-0 pr-1"><b>{{ itemsCart.net_price ? Number(itemsCart.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) : '0.00' }} <span v-if="choose_list === 'recurring'" class="ml-2">บาท/เดือน</span></b></span>
                      </v-row>
                    </v-col>
                    <v-col cols="12" align="right" class="mt-2 mb-2">
                      <!-- ชำระเงิน -->
                      <v-btn class="white--text" block color="#27AB9C" @click="confirmCreateOrderMobile('payment')"
                        :disabled="checkSelectPr || contractStartDate === '' || contractEndDate === '' || selectBudget === '' || selectCutBudget === '' || selectTypeDoc === ''">
                        <span style="font-size: 16px; font-style: normal; font-weight: bold;">ยืนยันการขอซื้อ</span>
                      </v-btn>
                      <!-- ชำระเงินสด -->
                      <v-btn v-if="selectTypeAddress === 'Shop' && checkOwnShop === 'Y'" class="mt-4" outlined block color="#27AB9C" @click="confirmCreateOrderMobile('cashPayment')"
                        :disabled="(taxRoles === 'Personal' || taxRoles === 'Business') && taxAddress === '' ? true : disableButtonPay ? true : false">
                        <span style="font-size: 16px; font-style: normal; font-weight: bold;">ชำระเงินสด</span>
                      </v-btn>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
            </v-col>
            <!-- end -->
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  <!-- </v-container> -->
  <ModalTaxInvoice ref="ModalTaxAddress" :taxType="taxRoles" :frompage="CartPage" :ShippingType="selectTypeAddress"/>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import { Tabs, Table } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-table': Table,
    ModalTaxInvoice: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalAddress/TaxInvoiceAddress')
  },
  data () {
    return {
      isJV: '',
      orderList: [],
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: false,
      CartPage: 'POPage',
      companyData: [],
      dataRole: '',
      pay_type: 'all',
      statePayType: '',
      page: 1,
      keyCheckHead: 0,
      headers: [
        { text: 'หมายเลขใบเสนอราคา', value: 'qt_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ผู้ซื้อ', value: 'buyer_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ร้านค้า', value: 'shop_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '270' },
        { text: 'Pay Type', value: 'pay_type', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'วันที่สร้างรายการ', value: 'created_at', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'วันที่เริ่มสัญญา', value: 'start_date_contract', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'วันที่สิ้นสุดสัญญา', value: 'end_date_contract', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'สถานะ', value: 'qu_status', sortable: false, filterable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' }
      ],
      headersJV: [
        { text: 'หมายเลขใบเสนอราคา', value: 'qt_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ผู้ซื้อ', value: 'buyer_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ส่งคำขอโดย', value: 'created_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '230' },
        { text: 'ร้านค้า', value: 'shop_name', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '270' },
        { text: 'Pay Type', value: 'pay_type', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '100' },
        { text: 'วันที่สร้างรายการ', value: 'created_at', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'วันที่เริ่มสัญญา', value: 'start_date_contract', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'วันที่สิ้นสุดสัญญา', value: 'end_date_contract', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '180' },
        { text: 'สถานะ', value: 'qu_status', sortable: false, filterable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '200' }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: '',
      countPOAll: 0,
      countOrderPending: 0,
      countPOSuccess: 0,
      countPOActive: 0,
      countPOWaitingApproveCompany: 0,
      countPOWaitingShopApprove: 0,
      countPOWaitingApprove: 0,
      countPOCancel: 0,
      countPOReject: 0,
      countOrderApprove: 0,
      dialogCheckDoc: false,
      Rules: {
        Name: [
          v => !!v || 'กรุณากรอกชื่อ-สกุล'
        ],
        Phone: [
          v => !!v || 'กรุณากรอกเบอร์โทร',
          v => /^[0-9]{10}$/.test(v) || 'กรุณากรอกเบอร์โทรให้ถูกต้อง'
        ],
        Position: [
          v => !!v || 'กรุณากรอกตำแหน่ง'
        ],
        Email: [
          v => !!v || 'กรุณากรอก Email',
          v => /^\w+([.-]?\w+)@[a-zA-Z]+([.-]?[a-zA-Z]+)(.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอก Email ให้ถูกต้อง'
        ]
      },
      payType: '',
      lazy: false,
      cartData: '',
      itemsCart: [],
      Address: '',
      Fullname: '',
      EditAddressDetail: '',
      titleAddress: '',
      address_data: '',
      taxAddress: '',
      companyName: '',
      companyTaxID: '',
      checkAdminQU: true,
      pplURL: '',
      pplToken: '',
      // page: '',
      taxinvoiceAddress: [],
      taxinvoiceAddressNew: [],
      googleItem: [],
      taxRoles: 'No',
      selectTypeAddress: 'PO',
      oneDataSpecial: '',
      modalPayment: false,
      responseSentDataPPL: '',
      SelectCouponOrPoint: true,
      dialog_Cancel_Coupon: false,
      checkOwnShop: 'N',
      discountBaht: '',
      discountCode: '',
      disableButtonPay: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      minDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchContractStartDate: '',
      setMinDateContractEndDate: '',
      contractStartDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      ActiveDiscount: false,
      selectDiscount: '',
      discount: '',
      contractSet: false,
      reason: '',
      selectBudget: '',
      choose_list: '',
      selectTypeDoc: '',
      tax_id: '',
      itemTypeDoc: [],
      selectedPr: '',
      itemCodePrList: [],
      itemBudget: [
        { text: 'งบดำเนินการ', value: 'operating_budget' },
        { text: 'งบลงทุน', value: 'investment_budget' },
        { text: 'งบรายจ่ายประจำ', value: 'regular_expenditure_budget' }
      ],
      selectCutBudget: '',
      itemCutBudget: [
        { text: 'ต้นทุนขาย (COGS)', value: 'COGS' },
        { text: 'ค่าใช้จ่ายและบริการ (SG&A)', value: 'SG&A' },
        { text: 'ต้นทุนวิจัยและพัฒนา (R&D)', value: 'R&D' }
      ],
      Name_Buyer: '',
      Phone_Buyer: '',
      Position_Buyer: '',
      Email_Buyer: '',
      Name_Audit1: '',
      Phone_Audit1: '',
      Position_Audit1: '',
      Email_Audit1: '',
      Name_Audit2: '',
      Phone_Audit2: '',
      Position_Audit2: '',
      Email_Audit2: '',
      json_personal: [],
      purchasing_cheif: [],
      inspectors_one: [],
      inspectors_two: [],
      name: '',
      position: '',
      phone: '',
      email: '',
      buyer_name: '',
      created_name: '',
      buyer_phone: '',
      buyer_email: '',
      modalContractStartDate: false,
      modalContractEndDate: false,
      orderNumber: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headersTable () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        },
        // {
        //   title: 'ราคาต่อชิ้น',
        //   dataIndex: 'revenue_default',
        //   scopedSlots: { customRender: 'revenue_default' },
        //   key: 'revenue_default',
        //   align: 'center',
        //   width: '20%'
        // },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'total_revenue_default',
          scopedSlots: { customRender: 'total_revenue_default' },
          key: 'total_revenue_default',
          align: 'center',
          width: '20%'
        },
        {
          title: 'Item Code PR',
          dataIndex: 'item_code_pr',
          scopedSlots: { customRender: 'item_code_pr' },
          key: 'item_code_pr',
          align: 'center',
          width: '20%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headersMobile
    },
    checkSelectPr () {
      const checkPr = this.itemsCart.length === 0 ? true : this.itemsCart.choose_list[0].product_list.some(v => v.item_code_pr_buyer === null)
      return checkPr
    }
    // DatatableFilter () {
    //   if (this.search === '') {
    //     return this.DataTable
    //   } else {
    //     return this.DataTable.filter(element => {
    //       console.log(element.qu_number.toLowerCase())
    //       return element.qu_number.toLowerCase().includes(this.search.toLowerCase()) || element.shop_name.toLowerCase().includes(this.search.toLowerCase())
    //     })
    //   }
    // }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/QUCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/QUCompany' }).catch(() => {})
      }
    },
    DataTable (val) {
      // console.log('DataTable', val)
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        // this.DataTable = this.orderList.data.all
        this.DataTable = this.orderList.data.all.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        // this.DataTable = this.orderList.data.success
        this.DataTable = this.orderList.data.success.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        // this.DataTable = this.orderList.data.active
        this.DataTable = this.orderList.data.approve.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        // this.DataTable = this.orderList.data.waiting_approve
        this.DataTable = this.orderList.data.waiting.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 4) {
        // this.DataTable = this.orderList.data.edited
        this.DataTable = this.orderList.data.reject.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 4
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 5) {
        // this.DataTable = this.orderList.data.reject
        this.DataTable = this.orderList.data.cancel.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 5
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 6) {
        // this.DataTable = this.orderList.data.reject
        this.DataTable = this.orderList.data.waiting_approve.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 6
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 7) {
        // this.DataTable = this.orderList.data.reject
        this.DataTable = this.orderList.data.waiting_shop_approve.map(x => {
          return {
            is_order_JV: x.is_order_JV,
            qu_number: x.order_number,
            shop_name: x.shop_name,
            qt_number: x.QT_number,
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            qu_status: x.status,
            buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
            created_name: x.created_name !== '' ? x.created_name : '-',
            // status_approve: x.status_approve,
            // status_approve_detail: x.status_approve_detail,
            // payment_transaction_number: x.payment_transaction_number,
            pdf_path_qu: x.QT_path,
            start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            // pdf_path_cs: x.pdf_cs_path,
            pay_type: x.pay_type
            // ref_cs_document_id: x.ref_cs_document_id,
            // ref_qt_document_id: x.ref_qt_document_id,
            // pdf_for_buyer: x.pdf_for_buyer,
            // order_id: x.order_document_id,
            // id: x.id,
            // company_id: x.company_id,
            // seller_shop_id: x.seller_shop_id,
            // buyer_name: x.buyer_name,
            // payment_method: x.payment_method,
            // com_perm_id: x.com_perm_id
          }
        })
        this.keyCheckHead = 7
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  async created () {
    // console.log('StateStatus', this.StateStatus)
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    this.$EventBus.$on('ListDataTable', this.ListDataTable)
    this.$EventBus.$on('checkAddressTaxInvoicePO', this.checkAddressTaxInvoicePO)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      // console.log(this.companyData)
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.role = JSON.parse(localStorage.getItem('roleUser'))
      this.pplToken = onedata.user.access_token
      this.ListDataTable()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    async checkAddressTaxInvoicePO () {
      this.companyName = ''
      this.companyTaxID = ''
      this.taxAddress = ''
      var dataTaxAddress = JSON.parse(Decode.decode(localStorage.getItem('TaxinvoiceAddress')))
      // console.log(dataTaxAddress)
      this.companyName = dataTaxAddress.name
      this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี :' + ' ' + dataTaxAddress.tax_id
      this.taxAddress = dataTaxAddress.address + ' ' + 'แขวง/ตำบล' + ' ' + dataTaxAddress.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + dataTaxAddress.district + ' ' + 'จังหวัด' + ' ' + dataTaxAddress.province + ' ' + dataTaxAddress.postal_code
    },
    openModalTaxAddress () {
      this.$refs.ModalTaxAddress.open()
    },
    ClosedialogCheckDoc () {
      this.Name_Buyer = ''
      this.Phone_Buyer = ''
      this.Position_Buyer = ''
      this.Email_Buyer = ''
      this.Name_Audit1 = ''
      this.Phone_Audit1 = ''
      this.Position_Audit1 = ''
      this.Email_Audit1 = ''
      this.Name_Audit2 = ''
      this.Phone_Audit2 = ''
      this.Position_Audit2 = ''
      this.Email_Audit2 = ''
      this.json_personal = []
      this.purchasing_cheif = []
      this.inspectors_one = []
      this.inspectors_two = []
      this.name = ''
      this.position = ''
      this.phone = ''
      this.email = ''
      this.buyer_name = ''
      this.created_name = ''
      this.buyer_phone = ''
      this.buyer_email = ''
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.date1 = ''
      this.contractEndDate = ''
      this.searchContractEndDate = ''
      this.selectBudget = ''
      this.selectCutBudget = ''
      this.selectTypeDoc = ''
      this.reason = ''
      this.$refs.orderListDetails.resetValidation()
      this.dialogCheckDoc = false
    },
    async openTypeDoc () {
      this.tax_id = localStorage.getItem('tax_id')
      var body = {
        tax_id: this.tax_id
      }
      // console.log('body', body)
      await this.$store.dispatch('actionsGetDocumentType', body)
      var res = await this.$store.state.ModuleCart.stateGetDocumentType
      if (res.message === 'Show document type success') {
        // console.log('res', res)
        this.itemTypeDoc = res.data
      } else {
        this.itemTypeDoc = []
      }
    },
    async getItemCodePr () {
      this.tax_id = localStorage.getItem('tax_id')
      await this.$store.dispatch('actionsListItemCodePr', this.tax_id)
      var res = await this.$store.state.ModuleCart.stateListItemCodePr
      if (res.message === 'Show section success') {
        this.itemCodePrList = res.data
      }
    },
    CheckSpacebarName (e) {
      // console.log(e)
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '' || e.target.value === ' ')) {
        e.preventDefault()
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    setValueContractStartDate (val) {
      this.$refs.dialogContractStartDate.save(val)
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      // clear end date กรณีที่ start date เปลี่ยนแปลง
      var setMin = new Date(val)
      this.date1 = new Date(setMin.setDate(setMin.getDate() + 1)).toISOString().substr(0, 10)
      this.setMinDateContractEndDate = this.date1
      // console.log('date1', this.date1)
      this.contractEndDate = ''
      this.searchContractEndDate = ''
    },
    setValueContractEndDate (val) {
      this.$refs.dialogContractEndDate.save(val)
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
    },
    closeModalContractStartDate () {
      this.modalContractStartDate = false
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.date1 = ''
    },
    closeModalContractEndDate () {
      this.modalContractEndDate = false
      // this.date1 = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      // this.searchContractEndDate = ''
      this.date1 = this.setMinDateContractEndDate
      this.contractEndDate = ''
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    selectType () {
      if (this.statePayType === 'ทั้งหมด' || this.statePayType === '') {
        this.pay_type = 'all'
      } else if (this.statePayType === 'recurring') {
        this.pay_type = 'recurring'
      } else if (this.statePayType === 'onetime') {
        this.pay_type = 'onetime'
      } else if (this.statePayType === 'general') {
        this.pay_type = 'general'
      }
      this.ListDataTable()
    },
    async ListDataTable () {
      this.$store.commit('openLoader')
      this.countPOAll = 0
      this.countPOReject = 0
      this.countPOSuccess = 0
      this.countPOActive = 0
      this.countPOWaitingApprove = 0
      this.countPOWaitingApproveCompany = 0
      this.countPOWaitingShopApprove = 0
      this.countPOCancel = 0
      var data = {
        company_id: this.companyData.company.company_id,
        com_perm_id: this.companyData.position.com_perm_id,
        pay_type: this.pay_type === 'all' ? null : this.pay_type
      }
      // console.log('data before send =========>', data)
      await this.$store.dispatch('actionsListQTBuyer', data)
      this.orderList = await this.$store.state.ModuleAdminManage.stateListQTBuyer
      // console.log('Data =====>', this.orderList)
      if (this.orderList.result === 'Success') {
        if (this.orderList.message === 'List Order Document Success.') {
          this.countPOAll = this.orderList.data.all.length
          this.countPOReject = this.orderList.data.reject.length
          this.countPOSuccess = this.orderList.data.success.length
          this.countPOActive = this.orderList.data.approve.length
          this.countPOWaitingApprove = this.orderList.data.waiting.length
          this.countPOWaitingApproveCompany = this.orderList.data.waiting_approve.length
          this.countPOWaitingShopApprove = this.orderList.data.waiting_shop_approve.length
          this.countPOCancel = this.orderList.data.cancel.length
          this.isJV = this.orderList.data.is_JV
          // console.log(this.countPOAll, this.countOrderPending, this.countPOSuccess, this.countPOWaitingApprove, this.countPOActive, this.countPOCancel)
          if (this.StateStatus === 0) {
            // this.DataTable = this.orderList.data.all
            this.DataTable = this.orderList.data.all.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                qt_number: x.QT_number,
                shop_name: x.shop_name,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            // this.DataTable = this.orderList.data.success
            this.DataTable = this.orderList.data.success.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            // this.DataTable = this.orderList.data.active
            this.DataTable = this.orderList.data.approve.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 3) {
            // this.DataTable = this.orderList.data.waiting_approve
            this.DataTable = this.orderList.data.waiting.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 4) {
            // this.DataTable = this.orderList.data.edited
            this.DataTable = this.orderList.data.reject.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 5) {
            // this.DataTable = this.orderList.data.reject
            this.DataTable = this.orderList.data.cancel.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 6) {
            // this.DataTable = this.orderList.data.reject
            this.DataTable = this.orderList.data.waiting_approve.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 7) {
            // this.DataTable = this.orderList.data.reject
            this.DataTable = this.orderList.data.waiting_shop_approve.map(x => {
              return {
                is_order_JV: x.is_order_JV,
                qu_number: x.order_number,
                shop_name: x.shop_name,
                qt_number: x.QT_number,
                created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                qu_status: x.status,
                buyer_name: x.buyer_name !== '' ? x.buyer_name : '-',
                created_name: x.created_name !== '' ? x.created_name : '-',
                // status_approve: x.status_approve,
                // status_approve_detail: x.status_approve_detail,
                // payment_transaction_number: x.payment_transaction_number,
                pdf_path_qu: x.QT_path,
                start_date_contract: x.start_date_contract === null ? '-' : new Date(x.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                end_date_contract: x.end_date_contract === null ? '-' : new Date(x.end_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
                // pdf_path_cs: x.pdf_cs_path,
                pay_type: x.pay_type
                // ref_cs_document_id: x.ref_cs_document_id,
                // ref_qt_document_id: x.ref_qt_document_id,
                // pdf_for_buyer: x.pdf_for_buyer,
                // order_id: x.order_document_id,
                // id: x.id,
                // company_id: x.company_id,
                // seller_shop_id: x.seller_shop_id,
                // buyer_name: x.buyer_name,
                // payment_method: x.payment_method,
                // com_perm_id: x.com_perm_id
              }
            })
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: 'ผู้ใช้งานนี้ไม่มีสิทธิ์การเข้าถึงใบเสนอราคาบริษัท',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
        if (this.orderList.message === 'This user is Unauthorized' || this.orderList.message === 'This user is unauthorized.' || this.orderList.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    async updateSelectPr () {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = []
      var glAccount = []
      this.itemsCart.choose_list[0].product_list.forEach(element => {
        glAccount = this.itemCodePrList.filter(item => item.material_code === element.item_code_pr_buyer)
        data.push({
          product_id: element.product_id,
          quantity: element.quantity,
          price: element.revenue_vat,
          item_code_pr_buyer: element.item_code_pr_buyer !== undefined || element.item_code_pr_buyer !== null ? element.item_code_pr_buyer : null,
          type_budget_gl_account: glAccount.length !== 0 ? glAccount[0].gl_account : null
        })
      })
      onedata.cartData.product_to_calculate = data
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async UpdateStatusBuyer (item) {
      // console.log(item)
      if (item.seller_sent_status === 'not_sent') {
        this.$swal.fire({
          icon: 'warning',
          text: 'สินค้ากำลังจัดส่ง',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      } else {
        const update = {
          order_number: item.order_number,
          buyer_received_status: item.buyer_received_status
        }
        await this.$store.dispatch('actionUpdateStatusBuyer', update)
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      }
    },
    async GoToPayment (item) {
      const PaymentID = {
        payment_transaction_number: item.payment_transaction_number
      }
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    async orderDetail (val) {
      // console.log('path to not tell =====>', val.pdf_for_buyer)
      window.open(`${val.pdf_for_buyer}`)
    },
    SelectDetailOrder (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
      this.page = 1
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.ListDataTable()
    },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateOrderDetailData
      // console.log('response detail order', response)
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem('MyOrderDetail', Encode.encode(JSON.stringify(response.data)))
        this.$router.push('/myorderdetail')
      }
    },
    goPDFQU (item) {
      window.open(`${item.pdf_path_qu}`)
    },
    goPDFCS (item) {
      window.open(`${item.pdf_path_cs}`)
    },
    async goDetailQU (item) {
      // item.qu_status !== 'check_doc'
      this.orderNumber = ''
      if (item.qu_status !== 'check_doc') {
        var ordernumber = item.qu_number
        // var id = this.companyData.company.company_id
        localStorage.setItem('detailItemQU', Encode.encode(item))
        if (this.MobileSize === false) {
          this.$router.push({ path: `/QUCompanyDetail?ordernumber=${ordernumber}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/QUCompanyDetailMobile?ordernumber=${ordernumber}` }).catch(() => {})
        }
      } else {
        if (item.is_order_JV === 'no') {
          var ordernumber2 = item.qu_number
          // var id = this.companyData.company.company_id
          localStorage.setItem('detailItemQU', Encode.encode(item))
          if (this.MobileSize === false) {
            this.$router.push({ path: `/QUCompanyDetail?ordernumber=${ordernumber2}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/QUCompanyDetailMobile?ordernumber=${ordernumber2}` }).catch(() => {})
          }
        } else {
          this.orderNumber = item.qu_number
          this.$store.commit('openLoader')
          if (localStorage.getItem('TaxinvoiceAddress') !== null) {
            this.checkAddressTaxInvoicePO()
          }
          this.Name_Buyer = ''
          this.Phone_Buyer = ''
          this.Position_Buyer = ''
          this.Email_Buyer = ''
          this.Name_Audit1 = ''
          this.Phone_Audit1 = ''
          this.Position_Audit1 = ''
          this.Email_Audit1 = ''
          this.Name_Audit2 = ''
          this.Phone_Audit2 = ''
          this.Position_Audit2 = ''
          this.Email_Audit2 = ''
          this.json_personal = []
          this.purchasing_cheif = []
          this.inspectors_one = []
          this.inspectors_two = []
          this.name = ''
          this.position = ''
          this.phone = ''
          this.email = ''
          this.buyer_name = ''
          this.created_name = ''
          this.buyer_phone = ''
          this.buyer_email = ''
          this.searchContractStartDate = ''
          this.contractStartDate = ''
          this.contractEndDate = ''
          this.date1 = ''
          this.contractEndDate = ''
          this.searchContractEndDate = ''
          this.selectBudget = ''
          this.selectCutBudget = ''
          this.selectTypeDoc = ''
          this.reason = ''
          await this.getItemCodePr()
          await this.openTypeDoc()
          await this.getDetailCart(item)
          await this.GetPersonal()
          // this.dialogCheckDoc = true
        }
      }
    },
    async GetPersonal () {
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      // console.log('companyId--------->', companyId.company.company_id)
      const data = {
        company_id: companyId.company.company_id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var companyData = await this.$store.state.ModuleAdminManage.stateDetailCompany
      this.Name_Buyer = companyData.data.json_personal[0].purchasing_chief[0].name
      this.Phone_Buyer = companyData.data.json_personal[0].purchasing_chief[0].phone
      this.Position_Buyer = companyData.data.json_personal[0].purchasing_chief[0].position
      this.Email_Buyer = companyData.data.json_personal[0].purchasing_chief[0].email
      this.Name_Audit1 = companyData.data.json_personal[0].inspectors_one[0].name
      this.Phone_Audit1 = companyData.data.json_personal[0].inspectors_one[0].phone
      this.Position_Audit1 = companyData.data.json_personal[0].inspectors_one[0].position
      this.Email_Audit1 = companyData.data.json_personal[0].inspectors_one[0].email
      this.Name_Audit2 = companyData.data.json_personal[0].inspectors_two[0].name
      this.Phone_Audit2 = companyData.data.json_personal[0].inspectors_two[0].phone
      this.Position_Audit2 = companyData.data.json_personal[0].inspectors_two[0].position
      this.Email_Audit2 = companyData.data.json_personal[0].inspectors_two[0].email
    },
    async getDetailCart (item) {
      var data = ''
      data = {
        order_number: item.qu_number,
        company_id: this.companyData.company.company_id,
        com_perm_id: this.companyData.position.com_perm_id,
        product_to_calculate: []
      }
      await this.$store.dispatch('actionsDetailCartInCompony', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCartInCompony
      // console.log('response ======>', response)
      if (response.message === 'Success.') {
        this.itemsCart = await response.data
        this.payType = this.itemsCart.choose_list[0].pay_type
        if (this.itemsCart.total_price_no_vat >= 50000) {
          this.contractSet = true
        }
        var responseData = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
        var user = responseData.data[0]
        this.buyer_name = user.first_name_th + ' ' + user.last_name_th
        this.buyer_phone = user.phone
        this.buyer_email = user.email
        this.dialogCheckDoc = true
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async confirmCreateOrderMobile (paymentType) {
      if (this.$refs.orderListDetails.validate(true)) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (dataRole.role === 'purchaser') {
          this.$swal.fire({
            icon: 'warning',
            html: '<p>* ผู้ซื้อสามารถดูใบสั่งซื้อได้จาก <b><u>ใบเสนอราคา</u></b></p>',
            title: '<h5>ยืนยันการสั่งซื้อหรือไม่</h5>',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          // cancelButtonColor: '#d33'
          }).then((result) => {
            if (result.isConfirmed) {
              this.CreateOrder(paymentType)
            // this.googleSentData()
            } else if (result.isDismissed) {
              // this.getCart()
            }
          }).catch(() => {
          })
        } else if (dataRole.role === 'ext_buyer') {
          this.$swal.fire({
            icon: 'warning',
            showCancelButton: true,
            title: '<h5>ยืนยันการสั่งซื้อหรือไม่</h5>',
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          // cancelButtonColor: '#aaa'
          }).then((result) => {
            if (result.isConfirmed) {
              this.CreateOrder(paymentType)
            // this.googleSentData()
            } else if (result.isDismissed) {
              // this.getCart()
            }
          }).catch(() => {
          })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async CreateOrder (paymentTypeData) {
      this.$store.commit('openLoader')
      var msg = ''
      const paymentType = paymentTypeData
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      // var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var res
      var data
      if (paymentType === 'cashPayment') {
        data = {
          order_number: this.orderNumber,
          company_id: this.companyData.company.company_id,
          com_perm_id: this.companyData.position.com_perm_id,
          role_user: dataRole.role,
          // invoice_id: this.taxinvoiceAddress.data.length > 0 ? this.taxinvoiceAddress.data[0].id : '',ของเก่าที่ยังมี invoice_id
          invoice_id: '',
          type_shipping: 'online',
          start_date_contract: this.searchContractStartDate,
          end_date_contract: this.searchContractEndDate,
          use_discount: 'N',
          discount_percent: '0',
          discount_amount: this.itemsCart.total_discount,
          contract_service: this.contractSet === true ? 'Y' : 'N',
          remark: this.reason,
          type_budget: this.selectBudget,
          budget_cut: this.selectCutBudget,
          document_type_name: this.selectTypeDoc,
          json_personal: [{
            purchasing_chief: [
              {
                name: this.Name_Buyer,
                phone: this.Phone_Buyer,
                email: this.Position_Buyer,
                position: this.Email_Buyer
              }
            ],
            inspectors_one: [
              {
                name: this.Name_Audit1,
                phone: this.Phone_Audit1,
                email: this.Position_Audit1,
                position: this.Email_Audit1
              }
            ],
            inspectors_two: [
              {
                name: this.Name_Audit2,
                phone: this.Phone_Audit2,
                email: this.Position_Audit2,
                position: this.Email_Audit2
              }
            ]
          }
          ],
          json_buyer: [
            {
              buyer_name: this.buyer_name,
              buyer_phone: this.buyer_phone,
              buyer_email: this.buyer_email
            }
          ]
          // credit_term: paymentType === 'creditTerm' ? 'yes' : 'no',
          // company_id: onedata.cartData.company_id ? onedata.cartData.company_id : -1,
          // company_position: onedata.cartData.company_position ? onedata.cartData.company_position : -1,
          // com_perm_id: onedata.cartData.com_perm_id ? onedata.cartData.com_perm_id : -1,
          // coupon: onedata.cartData.coupon.length === 0 ? [] : haveCoupon,
          // payment_method: 'Cash'
        }
        // console.log('data-------->', data)
        await this.$store.dispatch('actionsCompanyApproveOrder', data)
        res = await this.$store.state.ModuleAdminManage.stateCompanyApproveOrder
      } else {
        data = {
          order_number: this.orderNumber,
          company_id: this.companyData.company.company_id,
          com_perm_id: this.companyData.position.com_perm_id,
          role_user: dataRole.role,
          // invoice_id: this.taxinvoiceAddress.data.length > 0 ? this.taxinvoiceAddress.data[0].id : '',ของเก่าที่ยังมี invoice_id
          invoice_id: '',
          type_shipping: 'online',
          start_date_contract: this.searchContractStartDate,
          end_date_contract: this.searchContractEndDate,
          use_discount: 'N',
          discount_percent: '0',
          discount_amount: this.itemsCart.total_discount,
          contract_service: this.contractSet === true ? 'Y' : 'N',
          remark: this.reason,
          type_budget: this.selectBudget,
          budget_cut: this.selectCutBudget,
          document_type_name: this.selectTypeDoc,
          json_personal: [{
            purchasing_chief: [
              {
                name: this.Name_Buyer,
                phone: this.Phone_Buyer,
                email: this.Position_Buyer,
                position: this.Email_Buyer
              }
            ],
            inspectors_one: [
              {
                name: this.Name_Audit1,
                phone: this.Phone_Audit1,
                email: this.Position_Audit1,
                position: this.Email_Audit1
              }
            ],
            inspectors_two: [
              {
                name: this.Name_Audit2,
                phone: this.Phone_Audit2,
                email: this.Position_Audit2,
                position: this.Email_Audit2
              }
            ]
          }
          ],
          json_buyer: [
            {
              buyer_name: this.buyer_name,
              buyer_phone: this.buyer_phone,
              buyer_email: this.buyer_email
            }
          ]
        }
        await this.$store.dispatch('actionsCompanyApproveOrder', data)
        res = await this.$store.state.ModuleAdminManage.stateCompanyApproveOrder
      // }
      }
      // console.log('res =====>', res)
      if (res.result === 'Success') {
        if (res.message === 'Success.') {
          if (res.data.can_pay === 'yes') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'success',
              text: 'สั่งสินค้าเรียบร้อย'
            })
          } else {
          //   if (dataRole.role === 'purchaser') {
          //     const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          //     var id2 = companyId.company.company_id
          //     const data2 = {
          //       company_id: id2
          //     }
          //     await this.$store.dispatch('actionsDetailCompany', data2)
          //     await this.$store.dispatch('actionsAuthorityUser')
          //     var responseCompany2 = await this.$store.state.ModuleAdminManage.stateDetailCompany
          //     var responsePosition2 = await this.$store.state.ModuleUser.stateAuthorityUser
          //     var listCompany2 = responsePosition2.data.list_company
          //     for (let i = 0; i < listCompany2.length; i++) {
          //       if (responseCompany2.data.id === listCompany2[i].company_id) {
          //         localStorage.removeItem('list_Company_detail')
          //         localStorage.setItem('list_Company_detail', Encode.encode(listCompany2[i]))
          //       }
          //     }
          //     localStorage.setItem('CompanyData', Encode.encode(responseCompany2.data))
          //     this.$EventBus.$emit('getItemNoti')
          //     if (this.MobileSize) {
          //       this.$router.push({ path: `/orderDetailCompanyMobile?orderNumber=${res.data.order_number}` }).catch(() => {})
          //     } else {
          //       this.$router.push({ path: `/orderDetailCompany?orderNumber=${res.data.order_number}` }).catch(() => {})
          //     }
          //   }
          // }
            this.dialogCheckDoc = false
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'success',
              text: 'สั่งสินค้าเรียบร้อย'
            })
            this.$EventBus.$emit('getItemNoti')
            await this.ListDataTable()
          }
        } else if (res.message === 'This user is purchaser & credit term . create order success.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: 'สั่งสินค้าเรียบร้อย'
          })
          if (localStorage.getItem('SetRowCompany') !== null) {
            const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            var id = companyId.company.company_id
            const data = {
              company_id: id
            }
            await this.$store.dispatch('actionsDetailCompany', data)
            await this.$store.dispatch('actionsAuthorityUser')
            var responseCompany = await this.$store.state.ModuleAdminManage.stateDetailCompany
            var responsePosition = await this.$store.state.ModuleUser.stateAuthorityUser
            var listCompany = responsePosition.data.list_company
            for (let i = 0; i < listCompany.length; i++) {
              if (responseCompany.data.id === listCompany[i].company_id) {
                localStorage.removeItem('list_Company_detail')
                localStorage.setItem('list_Company_detail', Encode.encode(listCompany[i]))
              }
            }
            localStorage.setItem('CompanyData', Encode.encode(responseCompany.data))
            this.$EventBus.$emit('getItemNoti')
            if (this.MobileSize) {
              this.$router.push({ path: '/companyListCreditOrderMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/companyListCreditOrder' }).catch(() => {})
            }
          }
        } else if (res.message === 'This user is have approver . create order success.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: 'สั่งสินค้าเรียบร้อย'
          })
          if (localStorage.getItem('SetRowCompany') !== null) {
            const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
            var Companyid = companyId.company.company_id
            const data = {
              company_id: Companyid
            }
            await this.$store.dispatch('actionsDetailCompany', data)
            await this.$store.dispatch('actionsAuthorityUser')
            var responseCompanyApprove = await this.$store.state.ModuleAdminManage.stateDetailCompany
            var responsePositionApprove = await this.$store.state.ModuleUser.stateAuthorityUser
            var listCompanyAprrove = responsePositionApprove.data.list_company
            for (let i = 0; i < listCompanyAprrove.length; i++) {
              if (responseCompanyApprove.data.id === listCompanyAprrove[i].company_id) {
                localStorage.removeItem('list_Company_detail')
                localStorage.setItem('list_Company_detail', Encode.encode(listCompanyAprrove[i]))
              }
            }
            localStorage.setItem('CompanyData', Encode.encode(responseCompanyApprove.data))
            this.$EventBus.$emit('getItemNoti')
            if (this.MobileSize) {
              this.$router.push({ path: '/listApproveMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/listApprove' }).catch(() => {})
            }
          }
        } else {
          this.dialogCheckDoc = false
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: 'สั่งสินค้าเรียบร้อย'
          })
          await this.ListDataTable()
          // window.location.assign('/')
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      } else if (res.result === 'FAILED') {
        if (res.message === 'Credit is not enough for buy.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: 'วงเงินของบริษัทไม่เพียงพอ'
          })
        } else if (res.message === 'Not enough product in stock.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: `ไม่สามารถซื้อสินค้า ${res.data[0].product} ได้เนื่องจากจำนวนสินค้ามีไม่เพียงพอ`
          })
        } else if (res.message === 'Type_budget not found.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ไม่พบประเภทการควบคุมวงเงิน'
          })
        } else if (res.message === 'No approver in this company.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ไม่มีผู้อนุมัติในบริษัทนี้'
          })
        } else if (res.message === 'Approver limit exceeded.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: 'วงเงินผู้อนุมัติไม่เพียงพอ'
          })
        } else if (res.message === 'Credit not enough for buy.') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: 'วงเงินฝ่าย/แผนกไม่เพียงพอ'
          })
        } else if (res.message === 'This user can not create order becase this user is not in some department') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ผู้ซื้อไม่มีแผนก กรุณาติดต่อเจ้าหน้าที่'
          })
        } else if (res.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 3000,
          //   timerProgressBar: true,
          //   icon: 'error',
          //   text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง'
          // })
          // window.location.assign('/')
        } else {
          // error msg form OrderPurchaserCheck
          this.$store.commit('closeLoader')
          msg = this.getErrorMsg(res.message)
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'error',
            text: msg
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          text: res.message
        })
      }
    }
  }
}
</script>

<style>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
</style>

<style lang="scss" scoped>
::v-deep table {
  tbody tr td:last-child,
  thead tr th:last-child {
    position: sticky !important;
    position: -webkit-sticky !important;
    right: 0;
    z-index: 10;
    background: white;
  }
  thead tr th:last-child {
    z-index: 11;
  }
}
</style>

<style scoped>
.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}
.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-family: 'Prompt' !important; */
  /* font-weight: 500; */
}
.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}
.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}
.m-auto {
margin: auto;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
::v-deep .ant-table-pagination {
  display: none;
}
::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}
::v-deep .ant-table-thead > tr > th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-tbody > tr > td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-thead > tr > th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-body > table {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table-bordered .ant-table-tbody > tr > td {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #e8e8e8;
  margin-bottom: 6px;
  border-radius: 8px;
}
</style>
