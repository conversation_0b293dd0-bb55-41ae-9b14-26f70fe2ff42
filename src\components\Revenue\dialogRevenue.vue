<template>
  <div class="text-center">
    <v-dialog v-model="ListRevenue" width="471" persistent scrollable>
      <v-card style="overflow: hidden">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>ถอนเงิน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="ListRevenue = !ListRevenue" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
            <v-card-title class="text-center">
          คุณได้ทำรายการถอนเงิน
คุณต้องการทำรายการนี้ ใช่หรือไม่
      </v-card-title>
            <v-spacer></v-spacer>
            <v-card-actions class="justify-center">
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="ConfirmData()">ตกลง</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      itemsListProduct: [],
      ListRevenue: false
    }
  },
  watch: {
  },
  created () {
    this.$EventBus.$on('dialogRevenue', this.dialogRevenue)
  },
  destroyed () {
    this.$EventBus.$off('dialogRevenue')
  },
  mounted () {
  },
  methods: {
    async dialogRevenue (data) {
      // console.log('dialogRevenue', data)
      this.ListRevenue = await !this.ListRevenue
    },
    async ConfirmData () {
      this.ListRevenue = await !this.ListRevenue
      await this.$EventBus.$emit('ref')
    },
    async cancel () {
      this.ListRevenue = await !this.ListRevenue
    }
  }
}
</script>
<style scoped>
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
.inner-right {
    height: 832px;
    max-height: 832px;
    overflow-y: scroll;
}
#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
