<template>
  <v-container grid-list-xs>
    <v-overlay :value="overlay2">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-row dense align-content="center" justify="center">
      <v-col cols="12" md="12" xs="12">
        <v-row class="mt-2">
          <v-spacer class="spacerStyleProductInDetailPage"></v-spacer>
          <v-chip
           class="ma-2"
           color="#BDE7D9"
           label
          >
            <h2 class="pt-3 fontHeaderListProduct">{{header}}</h2>
          </v-chip>
          <v-spacer class="spacerStyleProductInDetailPage"></v-spacer>
        </v-row>
      </v-col>
    </v-row>
    <v-row justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize">
      <v-col cols="12" md="2" sm="3" xs="4" v-for="(item, index) in paginated" :key="index">
        <CardProducts :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && !MobileSize && IpadSize">
      <v-col cols="12" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index">
        <CardProducts :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row justify="start" class="pt-12"  v-if="AllProduct.length !== 0 && MobileSize && !IpadSize">
      <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index">
        <CardProductsResponsive :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row justify="center" class="my-6">
      <v-pagination
       color="#27AB9C"
       v-model="pageNumber"
       :length="pageMax"
       :total-visible="7"
       @change="pageChange()"
      ></v-pagination>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
const FakeData = []
for (let i = 0; i < 48; i++) {
  FakeData.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      header: '',
      FakeData,
      overlay2: false,
      productCount: null,
      AllProduct: [],
      pageMax: null,
      current: 1,
      pageSize: 48,
      shopID: null,
      typeProduct: ''
    }
  },
  created () {
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    this.$EventBus.$emit('getPath')
    this.typeProduct = this.$router.currentRoute.params.data
    // console.log('this.typeProduct44', this.typeProduct)
    // this.pageNumber = parseInt(this.$route.query.pageNumber)
    if (this.typeProduct === 'new_product') {
      this.header = 'สินค้ามาใหม่'
      this.$EventBus.$on('getAllNewProduct', this.getAllNewProduct)
      this.getAllNewProduct()
    } else if (this.typeProduct === 'best_seller') {
      this.header = 'สินค้าขายดี'
      this.$EventBus.$on('getAllBestSeller', this.getAllBestSeller)
      this.getAllBestSeller()
    } else if (this.typeProduct === 'recommended_product') {
      this.header = 'สินค้าแนะนำ'
      this.$EventBus.$on('getAllSameProductShop', this.getAllSameProductShop)
      this.shopID = localStorage.getItem('shopID')
      this.getAllSameProductShop()
    } else if (this.typeProduct === 'same_shop') {
      this.header = 'สินค้าจากร้านเดียวกัน'
      this.$EventBus.$on('getAllProductShop', this.getAllProductShop)
      this.getAllProductShop()
    } else if (this.typeProduct === 'all_product') {
      this.header = 'สินค้าทั้งหมด'
      this.$EventBus.$on('getAllProductShop', this.getAllProductShop)
      this.getAllProductShop()
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  // computed: {
  //   pageNumber: {
  //     get () {
  //       return parseInt(this.$route.query.pageNumber) || 1
  //     },
  //     set (newPage) {
  //       this.$router.push(`/ListProductUI/${this.header}?pageNumber=${newPage}`).catch(() => {})
  //     }
  //   }
  // },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.AllProduct.slice(this.indexStart, this.indexEnd)
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async getAllNewProduct () {
      var data
      var idshop = localStorage.getItem('shopSellerID')
      data = {
        role_user: 'ext_buyer',
        seller_shop_id: idshop,
        product_type: 'new'
        // begin_search_type: 'component',
        // role_user: dataRole.role,
        // seller_shop_id: idshop,
        // begin_search_details: {
        //   custom_user_ID: '1',
        //   what_component: this.typeProduct,
        //   component_id: ''
        // },
        // user_detail: {
        //   company_id: companyID
        // }
      }
      // await this.$store.dispatch('actionMoreNewProductHome', data)
      // var response = await this.$store.state.ModuleHompage.stateMoreNewProductHome
      await this.$store.dispatch('actionsProductNew', data)
      var response = await this.$store.state.ModuleProductNode.stateProductNew
      // console.log('response all New product=======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = response.query_result
          this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.data.length
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
      }
    },
    async getAllBestSeller () {
      var idshop = localStorage.getItem('shopSellerID')
      var data
      data = {
        role_user: 'ext_buyer',
        seller_shop_id: idshop,
        product_type: 'best-seller'
        // begin_search_type: 'component',
        // seller_shop_id: idshop,
        // role_user: dataRole.role,
        // begin_search_details: {
        //   custom_user_ID: '1',
        //   what_component: this.typeProduct,
        //   component_id: ''
        // },
        // user_detail: {
        //   company_id: companyID
        // }
      }
      await this.$store.dispatch('actionsProductBestSellerHome', data)
      var response = await this.$store.state.ModuleProductNode.stateProductBestSeller
      // console.log('response all Best Seller =======>', response)
      if (response.ok === 'y') {
        if (response.query_result !== 'No products ready to sell.') {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = response.query_result
          this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
      }
    },
    async getAllSameProductShop () {
      var idshop = localStorage.getItem('shopSellerID')
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var companyID
      // if (localStorage.getItem('SetRowCompany') !== null) {
      //   var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      //   companyID = companyDataSet.company.company_id
      // } else {
      //   companyID = '-1'
      // }
      var data
      data = {
        role_user: 'ext_buyer',
        seller_shop_id: idshop,
        product_type: 'recommend'
        // begin_search_type: 'component',
        // seller_shop_id: idshop,
        // role_user: dataRole.role,
        // begin_search_details: {
        //   custom_user_ID: '1',
        //   what_component: this.typeProduct,
        //   component_id: ''
        // },
        // user_detail: {
        //   company_id: companyID
        // }
      }
      await this.$store.dispatch('actionsProductRecomment', data)
      var response = await this.$store.state.ModuleProductNode.stateProductRecomment
      // console.log('response all Best Seller =======>', response)
      if (response.ok === 'y') {
        this.overlay2 = false
        this.AllProduct = []
        this.AllProduct = response.query_result
        // this.AllProduct = response.data.list_new_products
        this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
        // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
        this.productCount = response.query_result.length
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
      }
    },
    async getAllProductShop () {
      var data
      var idshop = localStorage.getItem('shopSellerID')
      if (localStorage.getItem('roleUser') !== null) {
        // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        // var shopID = localStorage.getItem('shopID')
        data = {
          role_user: 'ext_buyer',
          seller_shop_id: idshop,
          product_type: 'all'
        }
      } else {
        if (this.oneData.length !== 0) {
          data = {
            role_user: 'ext_buyer',
            seller_shop_id: idshop,
            product_type: 'all'
          }
        } else {
          data = {
            role_user: 'ext_buyer',
            seller_shop_id: idshop,
            product_type: 'all'
          }
        }
      }
      // console.log(data)
      await this.$store.dispatch('actionsAllProduct', data)
      var response = await this.$store.state.ModuleProductNode.stateAllProduct
      // console.log('response Shop Detail Page =======>', response)
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = response.query_result
          // this.AllProduct = response.data.list_new_products
          this.pageMax = parseInt(this.AllProduct.length / 48) === 0 ? 1 : Math.ceil(this.AllProduct.length / 48)
          // console.log('pageMax ===>', this.pageMax, this.AllProduct.length)
          this.productCount = response.query_result.length
        }
      } else {
        this.overlay2 = false
        this.AllProduct = []
        this.productCount = 0
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
      }
    }
  }
}
</script>
