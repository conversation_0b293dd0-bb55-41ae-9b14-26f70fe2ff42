<template lang="html">
  <v-container>
    <a-card>
      <a-row>
        <a-col :span='24' class="mb-2">
          <a-row type="flex" justify="start">
            <v-icon x-large class="mb-2">mdi-store-outline</v-icon><span class="pl-5 pt-1 headline">สต๊อกสินค้าของฉัน</span>
          </a-row>
        </a-col>
        <a-col :span='24'>
            <TableInventory :props='DataTable' />
          </a-col>
        </a-row>
    </a-card>
  </v-container>
</template>

<script>
import { Card, Col, Row } from 'ant-design-vue'
export default {
  components: {
    'a-card': Card,
    'a-row': Row,
    'a-col': Col,
    TableInventory: () => import('@/components/Inventory/MyInventory/TableInventory')
  },
  data () {
    return {
      DataTable: []
    }
  },
  created () {
    this.GetInventoryData()
    this.$EventBus.$emit('changeNav')
  },
  methods: {
    async GetInventoryData () {
      await this.$store.dispatch('actionsGetShopData')
      const shopData = await this.$store.state.ModuleShop.stateShopData.data[0]
      // console.log('shop id', shopData.seller_shop_id.toString())
      var data = {
        seller_shop_id: shopData.seller_shop_id.toString()
      }
      await this.$store.dispatch('actionsGetInventoryData', data)
      var response = await this.$store.state.ModuleInventory.stateGetInventoryData
      // console.log('response inventory data====>', response)
      // this.$EventBus.$emit('send_shop', response.data[0])
      if (response.result === 'SUCCESS') {
        this.DataTable = response.data
      } else {
        this.DataTable = []
      }
    }
  }
}
</script>

<style lang="css" scoped>
</style>
