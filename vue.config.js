const webpack = require('webpack')
const CompressionPlugin = require('compression-webpack-plugin')
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin
const zlib = require('zlib')
module.exports = {
  css: {
    loaderOptions: {
      sass: {
        sassOptions: {
          quietDeps: true
        }
      }
    }
  },
  devServer: {
    disableHostCheck: true
  },
  transpileDependencies: [
    'vuetify'
  ],
  pluginOptions: {
    compression: {
      brotli: {
        filename: '[file].br[query]',
        algorithm: 'brotliCompress',
        include: /\.(js|css|html|svg|json)(\?.*)?$/i,
        compressionOptions: {
          params: {
            [zlib.constants.BROTLI_PARAM_QUALITY]: 11
          }
        },
        minRatio: 0.8
      },
      gzip: {
        filename: '[file].gz[query]',
        algorithm: 'gzip',
        include: /\.(js|css|html|svg|json)(\?.*)?$/i,
        minRatio: 0.8
      }
    }
  },
  configureWebpack: {
    plugins: [new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/)],
    performance: {
      hints: false,
      maxAssetSize: 1048576,
      maxEntrypointSize: 1048576
    },
    optimization: {
      runtimeChunk: 'single'
      // splitChunks: {
      //   chunks: 'all',
      //   maxInitialRequests: Infinity,
      //   minSize: 1,
      //   // maxSize: 250000,
      //   cacheGroups: {
      //     vendor: {
      //       test: /[\\/]node_modules[\\/]/,
      //       name (module) {
      //         const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
      //         return `npm.${packageName.replace('@', '')}`
      //       }
      //     }
      //   }
      // }
    }
  },
  chainWebpack: (config) => {
    /*
       Disable (or customize) prefetch, see:
       https://cli.vuejs.org/guide/html-and-static-assets.html#prefetch
    */
    // config.plugins.delete('html')
    config.plugins.delete('preload')
    config.plugins.delete('prefetch')
    config.plugins.delete('split-vendor')
    config.plugins.delete('split-vendor-async')
    config.plugins.delete('split-manifest')
    config.plugins.delete('inline-manifest')
    // config.plugin('CompressionPlugin').use(CompressionPlugin, [
    //   {
    //     algorithm: 'brotliCompress',
    //     test: /\.(js|css|html|svg)$/,
    //     compressionOptions: {
    //       level: 11,
    //     },
    //     filename: '[path][base].br',
    //   },
    // ])
    /*
       Configure preload to load all chunks
       NOTE: use `allChunks` instead of `all` (deprecated)
    */
    // config.plugin('preload').tap((options) => {
    //   options[0].include = 'allChunks'
    //   return options
    // })
  }
}

// const webpack = require("webpack")
// const PreloadWebpackPlugin = require('@vue/preload-webpack-plugin')
// const HtmlWebpackPlugin = require('html-webpack-plugin')

// module.exports = {
//   pwa: {
//     workboxPluginMode: 'GenerateSW',
//     workboxOptions: {
//         navigateFallback: '/index.html',
//         cleanupOutdatedCaches: true,
//         importWorkboxFrom: 'local',
//         importScripts: ['/inject-sw.js']
//     }
//   },
//   transpileDependencies: [
//     'vuetify'
//   ],
//   configureWebpack: {
//     plugins: [
//       new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/),
//       new HtmlWebpackPlugin({
//         title: 'Panit | สินค้า e-commerce หลากหลาย',
//         // Load a custom template (lodash by default)
//         template: './public/index.html'
//       }),
//       new PreloadWebpackPlugin({
//         rel: 'preload',
//         as(entry) {
//           if (/\.css$/.test(entry)) return 'style'
//           if (/\.woff$/.test(entry)) return 'font'
//           if (/\.png$/.test(entry)) return 'image'
//           return 'script'
//         }
//       })
//     ],
//     performance: {
//       hints: false
//     },
//     optimization: {
//       runtimeChunk: 'single',
//       splitChunks: {
//         chunks: 'all',
//         maxInitialRequests: Infinity,
//         minSize: 0,
//         maxSize: 250000,
//         cacheGroups: {
//           default: false,
//           vendor: {
//             test: /[\\/]node_modules[\\/]/,
//             chunks: 'all',
//             name(module) {
//               const packageName = module.context.match(/[\\/]node_modules[\\/](.*?)([\\/]|$)/)[1]
//               return `npm.${packageName.replace('@', '')}`
//             }
//           },
//           styles: {
//             test: /\.css$/,
//             name: 'styles',
//             chunks: 'all',
//             enforce: true
//           }
//         }
//       }
//     }
//   },
//   chainWebpack: (config) => {
//     config.plugins.delete('prefetch')

//     config.plugin('preload').tap((options) => {
//       options[0].include = 'allChunks'
//       return options
//     })
//   }
// }
