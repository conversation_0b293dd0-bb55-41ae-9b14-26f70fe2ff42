<template lang="html">
  <div>
    <v-row dense :class="MobileSize ? 'px-2' : ''">
      <v-col cols="12" md="7">
        <v-text-field
         v-model="search"
         rounded
         dense
         outlined
         placeholder="ค้นหาจากรหัสสต๊อกสินค้า สินค้าพร้อมขายหรือสินค้าพร้อมขายและพรีออเดอร์"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" class="pb-3">
        <v-card outlined width="100%">
          <v-data-table
            :headers="headers"
            :items="props"
            @page-count="pageCount = $event"
            :page.sync="page"
            :items-per-page="itemsPerPage"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            class="elevation-0"
            style="width: 100%;"
            :search="search"
            no-data-text="ไม่มีสต๊อกสินค้า"
            no-results-text="ไม่พบรหัสสต๊อกสินค้าหรือสินค้าพร้อมขายและสินค้าพร้อมขายและพรีออเดอร์ที่ค้นหา"
          >
            <template v-slot:[`item.actual_stock`]="{ item }">
              <span>{{ Number(item.actual_stock).toLocaleString(undefined) }}</span>
            </template>
            <template v-slot:[`item.effective_stock`]="{ item }">
              <span>{{ Number(item.effective_stock).toLocaleString(undefined) }}</span>
            </template>
          </v-data-table>
        </v-card>
        <!-- <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div> -->
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  props: ['props'],
  data () {
    return {
      pageCount: 5,
      page: 1,
      itemsPerPage: 10,
      search: '',
      headers: [
        { text: 'รหัสสต๊อกสินค้า', value: 'inventory_code', sortable: false, align: 'left', class: 'backgroundTable fontTable--text' },
        { text: 'สินค้าพร้อมขาย', value: 'actual_stock', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สินค้าพร้อมขายและพรีออเดอร์', value: 'effective_stock', sortable: false, class: 'backgroundTable fontTable--text' }
        // { text: 'edit', value: 'edit', sortable: false, align: 'center' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>

<style lang="css" scoped>
.v-data-table::v-deep th {
  font-size: 12px !important;
}

</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
