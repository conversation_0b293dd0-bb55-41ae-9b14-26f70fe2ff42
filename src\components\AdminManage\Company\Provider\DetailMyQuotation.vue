<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card
      width="100%"
      height="100%"
      elevation="0"
      :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']"
    >
      <v-card-title>
        <v-row dense>
          <v-col cols="12" md="6">
            <span
              style="
                font-weight: bold;
                font-size: 18px;
                line-height: 32px;
                color: #333333;
              "
              ><v-icon
                color="#27AB9C"
                class="mr-2"
                @click="backtoListPoCompany()"
                >mdi-chevron-left</v-icon
              >ใบเสนอราคา</span
            >
          </v-col>
          <v-col
            cols="12"
            md="6"
            align="end"
          >
            <v-btn
              color="#27AB9C"
              class="mr-0"
              width="125"
              height="40"
              rounded
              style="color: #ffffff"
              @click="GoToOrderCompany()"
            >
              ดูรายการสั่งซื้อ</v-btn
            >
          </v-col>
        </v-row>
      </v-card-title>
      <v-card-text>
        <v-row class="pt-3 px-2" dense style="background: #F9FAFD; border-radius: 8px 8px 0px 0px;">
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่สร้างรายการ : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
                new Date(DetailQU.created_at).toLocaleDateString('th-TH', {
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric'
                })
              }}</b></span> -->
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">เลขที่ใบเสนอราคา : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.QT_number }}</b></span> -->
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเลขใบรีเควส : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><v-chip :text-color="textcolorChip(DetailQU.status)" :color="colorChip(DetailQU.status)">{{
                returnStringStatus(DetailQU.status)
              }}</v-chip></span> -->
          </v-col>
        </v-row>
        <v-row class="px-2" dense style="background: #F9FAFD; border-radius: 0px 0px 8px 8px;">
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">ร้านค้า : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
                new Date(DetailQU.updated_at).toLocaleDateString('th-TH', {
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric'
                })
              }}</b></span> -->
          </v-col>
          <v-col cols="12" md="4"  :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">สถานะใบเสนอราคา : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.buyer_name }}</b></span> -->
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเหตุ : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
              DetailQU.remark === null ? '-' : DetailQU.remark
              }}</b></span> -->
          </v-col>
          <v-col cols="12" md="12" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">TMTID : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
              DetailQU.remark === null ? '-' : DetailQU.remark
              }}</b></span> -->
          </v-col>
          <v-col cols="12" md="12" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">ชื่อยา : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
              DetailQU.remark === null ? '-' : DetailQU.remark
              }}</b></span> -->
          </v-col>
          <v-col cols="12" md="12" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">ข้อมูลสินค้า : </span>
            <!-- <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{
              DetailQU.remark === null ? '-' : DetailQU.remark
              }}</b></span> -->
          </v-col>
          <!-- <v-col cols="12" md="4" v-if="DetailQU.qu_status === 'reject'" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">เหตุผลในการปฏิเสธ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.reason }}</b></span>
          </v-col> -->
        </v-row>
        <v-row dense>
          <!-- ใส่ iframe -->
          <v-card
            width="100%"
            height="100%"
            outlined
            style="background: #c4c4c4; border-radius: 8px"
            class="mt-4"
          >
            <v-card-text :class="MobileSize ? 'pa-0' : ''">
              <!-- <iframe v-if="DetailQU.QT_path !== '-'"  :src="DetailQU.QT_path" width="100%" :height="MobileSize ? '500' : IpadSize ? '600' : '1200'"></iframe> -->
            </v-card-text>
          </v-card>
        </v-row>
      </v-card-text>
    </v-card>
    <!-- Await Confirm QT -->
    <v-dialog v-model="dialogAwaitConfirmQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalAwaitConfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยืนยันคำขอซื้อ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeModalAwaitConfirm()">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="confirmQT('approve')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Confirm OT -->
    <v-dialog v-model="dialogSuccessConfirmQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยืนยันคำขอซื้อเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยืนยันคำขอซื้อเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
// import VuePdf from 'vue-pdf'
// import { Encode, Decode } from '@/services'
// import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
export default {
  data () {
    return {
    }
  },
  created () {
  },
  watch: {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    textcolorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return '#FF710B'
      } else if (quStatus === 'waiting_dwf') {
        return '#FF710B'
      } else if (quStatus === 'waiting_approve') {
        return '#FF710B'
      } else if (quStatus === 'check_doc') {
        return '#FF710B'
      } else if (quStatus === 'waiting_shop_approve') {
        return '#FF710B'
      } else if (quStatus === 'approve') {
        return '#52C41A'
      } else if (quStatus === 'reject') {
        return '#F5222D'
      } else if (quStatus === 'success') {
        return '#52C41A'
      } else {
        return '#636363'
      }
    },
    colorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return '#FEF6E6'
      } else if (quStatus === 'waiting_dwf') {
        return '#FBECE1'
      } else if (quStatus === 'waiting_approve') {
        return '#FBECE1'
      } else if (quStatus === 'check_doc') {
        return '#FBECE1'
      } else if (quStatus === 'waiting_shop_approve') {
        return '#FBECE1'
      } else if (quStatus === 'approve') {
        return '#F0FEE8'
      } else if (quStatus === 'reject') {
        return 'rgba(245, 34, 45, 0.10)'
      } else if (quStatus === 'success') {
        return '#F0FEE8'
      } else {
        return '#E6E6E6'
      }
    }
  }
}
</script>

<style>
.annotationLayer {
    position: inherit !important ;
}
</style>
<style lang="css">
 .vue-pdf-embed canvas {
  display: initial;
  position: inherit!important;
}
</style>
