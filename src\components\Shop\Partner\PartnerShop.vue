<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="pt-6" v-if="!MobileSize">รายชื่อคู่ค้า</v-card-title>
      <v-card-title style="font-weight: 600; font-size: 20px; line-height: 22px; color: #333333;" class="pt-6" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายชื่อคู่ค้า</v-card-title>
      <v-card-text>
        <v-row no-gutters>
          <v-col cols="12" class="py-0 pr-2">
            <a-tabs @change="getPartner" class="changeBorderbottom">
              <a-tab-pane :key="0"><span slot="tab" style="font-size: 18px;">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)">{{ countall }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab" style="font-size: 18px;">อนุมัติ <v-chip small text-color="#52C41A" color="#F0FEE8">{{ countActive }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="2"><span slot="tab" style="font-size: 18px;">รออนุมัติ <v-chip small text-color="#FAAD14" color="#FEF6E6">{{ countPending }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="3"><span slot="tab" style="font-size: 18px;">ปฏิเสธ <v-chip small text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">{{ countcancel }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="4"><span slot="tab" style="font-size: 18px;">ยกเลิก <v-chip small text-color="#636363" color="#E6E6E6">{{ countInActive }}</v-chip></span></a-tab-pane>
            </a-tabs>
          </v-col>
          <v-col cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3 pt-3' : ' mb-3 pt-3'" v-if="disableTable === true">
            <v-row dense>
              <v-col cols="12" md="8" sm="12" :class="!MobileSize && !IpadSize ? 'pr-6' : ''">
                <v-text-field style="border-radius: 8px;" v-model="search" placeholder="ค้นหาข้อมูลจากชื่อบริทคู่ค้าและ Vendor No." outlined dense hide-details>
                  <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <v-row dense :class="MobileSize ? 'pt-2' : 'pt-1'">
                  <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pt-2">วันที่ยื่นคำขอ : </span>
                  <v-dialog
                    ref="dialog"
                    v-model="modal"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field v-model="DateSearch" v-on="on" v-bind="attrs" style="border-radius: 8px;" class="pl-2" :style="(!IpadSize && !MobileSize) && widthScreen > 1280 ? 'max-width: 230px' : (!IpadSize && !MobileSize) && widthScreen <= 1280 ? 'max-width: 180px' : 'max-width: 100%'" placeholder="วว/ดด/ปปปป" outlined dense readonly hide-details>
                        <template v-slot:append>
                          <v-icon>mdi-calendar-month</v-icon>
                        </template>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="date"
                      scrollable
                      locale="th-TH"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="closeFilterDate()"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="ChangeFilterDate(date)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <!-- <v-col cols="12" md="6" sm="6" align="end" class="pt-0" v-if="MobileSize">
            <v-btn color="#27AB9C" dark class="mr-2" block  @click="openDialog()"><v-icon>mdi-plus</v-icon> เพิ่มคู่ค้า</v-btn>
          </v-col> -->
          <v-col cols="12" class="pt-2 pb-2">
            <v-row dense class="py-0">
              <v-col cols="12" md="6" sm="6" :class="MobileSize ? 'd-flex pt-2' : 'pt-2'">
                <span :class="MobileSize ? 'mr-auto pt-2' : ''">
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="StateStatus === 0">รายการบริษัททั้งหมด {{ this.showCountOrder }} รายการ</span>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 1">รายการบริษัทที่อนุมัติทั้งหมด {{ this.showCountOrder }} รายการ</span>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 2">รายการบริษัทที่รออนุมัติทั้งหมด {{ this.showCountOrder }} รายการ</span>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 3">รายการบริษัทที่ปฏิเสธทั้งหมด {{ this.showCountOrder }} รายการ</span>
                  <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="StateStatus === 4">รายการบริษัทที่ยกเลิกทั้งหมด {{ this.showCountOrder }} รายการ</span>
                </span>
                <v-btn color="#27AB9C" rounded dark v-if="MobileSize" class="ml-auto" @click="openDialog()"><v-icon>mdi-plus</v-icon> เพิ่มคู่ค้า</v-btn>
              </v-col>
              <v-col cols="12" md="6" sm="6" align="end" class="pt-0" v-if="!MobileSize">
                <v-btn color="#27AB9C" height="40" width="124" rounded class="white--text mr-2"  @click="openDialog()"><v-icon>mdi-plus</v-icon> เพิ่มคู่ค้า</v-btn>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" sm="12" v-if="disableTable === true">
            <v-data-table
            :headers="headers"
            :items="filterDateData"
            :search="search"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            style="width:100%;"
            height="100%"
            :page.sync="page"
            @pagination="countPartner"
            no-results-text="ไม่พบรายชื่อบริษัทผู้ซื้อ"
            no-data-text="ไม่มีรายชื่อบริษัทผู้ซื้อในตาราง"
            :update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 mt-4"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}
                <!-- {{ new Date(item.created_at)}} -->
              </template>
              <template v-slot:[`item.setting`]="{ item }">
                <!-- {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }} -->
                {{ item.setting_partner_id }}
              </template>
              <template v-slot:[`item.vender_no`]="{ item }">
                <!-- {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }} -->
                {{ item.vender_no !== '' ? item.vender_no : '-' }}
              </template>
              <template v-slot:[`item.status`]="{ item }">
                <span v-if="item.status === 'active'">
                  <v-chip class="ma-2" text-color="#52C41A" color="#F0FEE8">อนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'request'">
                  <v-chip class="ma-2" text-color="#FAAD14" color="#FEF6E6">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status === 'reject'">
                  <v-chip class="ma-2" text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">ปฏิเสธ</v-chip>
                </span>
                <span v-else-if="item.status === 'inactive'">
                  <v-chip class="ma-2" text-color="#636363" color="#E6E6E6">ยกเลิก</v-chip>
                </span>
              </template>
              <template v-slot:[`item.manages`]="{ item }">
                <v-row>
                  <v-btn x-small @click="EditPartner(item, item.status)"
                    style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                    :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                    class="pt-4 pb-4">
                    <v-icon color="#27AB9C">mdi-file-document</v-icon>
                  </v-btn>
                  <span class="ma-2"
                    style="font-weight: 400; font-size: 12px; line-height: 22px; color:  #27AB9C; cursor: pointer"
                    @click="EditPartner(item, item.status)"><b>รายละเอียด</b> <v-icon size="15" color="#27AB9C">
                      mdi-chevron-right</v-icon></span>
                  <!-- <v-btn outlined color="#27AB9C" @click="EditPartner(item)">
                    รายละเอียด
                  </v-btn> -->
                </v-row>
              </template>
            </v-data-table>
          </v-col>
          <v-col cols="12" v-else>
            <v-row justify="center" align-content="center">
              <v-col cols="12" md="12" align="center">
                <div class="my-5">
                  <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อคู่ค้าในร้านค้า</span><br/>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 1">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อคู่ค้าในร้านค้าที่อนุมัติ</span><br/>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 2">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อคู่ค้าในร้านค้าที่รออนุมัติ</span><br/>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 3">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อคู่ค้าในร้านค้าที่ปฏิเสธ</span><br/>
                </h2>
                <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 4">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายชื่อคู่ค้าในร้านค้าที่ยกเลิก</span><br/>
                </h2>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- กำหนดสิทธิ์คู่ค้า ตอนสร้าง Partner Complete -->
      <v-dialog v-model="ModalSearchCompany" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="CloseModalSearch()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 10px 20px 10px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0">
                      <v-row dense no-gutters v-if="showformSetting === false">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/IconAddUser.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">ค้นหาบริษัทคู่ค้าที่ต้องการเพิ่ม</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>ค้นหาบริษัทคู่ค้าที่ต้องการเพิ่ม</p>
                      </v-row>
                      <v-row dense no-gutters v-if="showformSetting === true">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/settingPartner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">กำหนดสิทธิ์คู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>กำหนดสิทธิ์คู่ค้า</p>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="py-5 mx-2" v-if="showformSetting === true">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col v-if="showformSetting === false" cols="12" md="12" sm="12" :class="dataSearch.length !== 0 && searchcompany !== '' && showformSetting === false ? 'pt-0' : dataSearch.length !== 0 && searchcompany !== '' && showformSetting === true ? 'py-5' : 'py-5'">
                      <v-row dense>
                        <v-col cols="12" md="12" sm="12">
                          <!-- @keyup.enter="searchdata()" -->
                          <v-text-field v-model="searchcompany" hide-details outlined placeholder="ค้นหาได้จากรหัสบริษัท หรือชื่อบริษัท" dense @keyup="searchdata()" oninput="this.value = this.value.replace(/^\s/g, '')"><v-icon slot="append" color="#CCCCCC">mdi-magnify</v-icon></v-text-field>
                        </v-col>
                      </v-row>
                    </v-col>
                    <!-- กรณีหาเจอและเลือก -->
                    <v-col cols="12" md="12" sm="12" v-if="dataSearch.length !== 0 && searchcompany !== ''" :style="showformSetting === true ? '' : 'max-height: 388px; overflow-y: auto;'" class="custom-scrollbar mb-4">
                      <div v-for="(item, index) in dataSearch" :key="index" v-show="showformSetting === false">
                        <v-row dense>
                          <v-col cols="12" md="12" sm="12" xs="12" :class="MobileSize ? 'px-2' : 'px-5'">
                            <v-card width="100%" height="100%" elevation="4" style="border: 1px solid #F3F5F7; border-radius: 8px; cursor: pointer;" outlined @click="checkShopOwn(item)">
                              <v-card-text :class="MobileSize ? 'px-4 py-2' : ''">
                                <v-col cols="12" md="12" sm="12" xs="12" class="py-0 px-0">
                                  <v-row :no-gutters="MobileSize ? false : true" dense>
                                    <v-col cols="2" md="2" sm="2" :style="MobileSize ? 'margin: auto;' : ''" :class="MobileSize ? '' : 'ml-3'">
                                      <v-avatar
                                        rounded
                                        :width="IpadProSize ? '80px' : IpadSize ? '70px' : MobileSize ? '60px' : '80px'"
                                        :height="IpadProSize ? '80px' : IpadSize ? '70px' : MobileSize ? '60px' : '80px'"
                                      >
                                        <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain></v-img>
                                      </v-avatar>
                                    </v-col>
                                    <v-col cols="9" md="9" sm="12" :class="MobileSize ? 'pt-1 pl-3' : 'pt-1 ml-3'">
                                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ item.company_name_th }}</span><br/>
                                      <span style="font-weight: 600; font-size: 12px; line-height: 16px; color: #989898;">{{ item.company_name_en }}</span><br/>
                                      <v-row no-gutters>
                                        <!-- <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;" class="pr-4"><v-icon color="#27AB9C">mdi-email-outline</v-icon> WWW.Eggs.net</span> -->
                                        <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;" class="pr-4"><v-icon color="#27AB9C">mdi-phone-outline</v-icon> {{ item.company_phone }}</span>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </v-row>
                      </div>
                      <div v-show="showformSetting">
                        <v-form ref="FormSetting" :lazy-validation="lazy">
                          <v-row dense class="mx-2">
                            <v-col cols="12" class="pb-4">
                              <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                            </v-col>
                            <v-col cols="12">
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataform.company_id }}</span></span><br/>
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataform.company_name_th }}</span></span><br/>
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาอังกฤษ :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataform.company_name_en }}</span></span><br/>
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataform.company_phone }}</span></span>
                            </v-col>
                          </v-row>
                          <v-row dense class="py-5">
                            <v-col cols="12" class="mx-2">
                              <v-divider></v-divider>
                            </v-col>
                          </v-row>
                          <v-row dense class="mt-2 mx-2">
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดผู้ซื้อ</span>
                            </v-col>
                          </v-row>
                          <v-row dense class="mt-2 mx-2">
                            <!-- รหัสผู้ซื้อ -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Customer No.</span>
                              <v-text-field v-model="customerCode" :maxLength="20" dense outlined placeholder="หมายเลขหรือตัวอักษรไม่เกิน 20 ตัวอักษร"></v-text-field>
                            </v-col>
                            <!-- Customer No. -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Customer Name</span>
                              <v-text-field v-model="customerNo" dense outlined placeholder="Customer Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-()\s]/g, '')"></v-text-field>
                              <!-- <v-autocomplete v-model="customerNo" :items="listCustomer" item-text="CustomerName" item-value="CustomerName" dense outlined placeholder="Customer Name" :rules="Rules.customerNo"></v-autocomplete> -->
                            </v-col>
                            <!-- Vendor No. -->
                            <!-- <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Vendor No.</span>
                              <v-text-field v-model="venderNo" dense outlined placeholder="Vendor No."></v-text-field>
                            </v-col> -->
                            <!-- Vendor Name. -->
                            <!-- <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Vendor Name</span>
                              <v-text-field v-if="isJV === false" v-model="descriptionPR" dense outlined placeholder="ระบุ Vendor Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-\s]/g, '')"></v-text-field>
                              <v-autocomplete v-else v-model="descriptionPR" :items="listVendor" item-text="vendor_name_th" item-value="vendor_name_th" label="Vendor Name" solo dense no-data-text="ไม่มีข้อมูล Vendor Name"></v-autocomplete>
                            </v-col> -->
                            <!-- กลุ่มลูกค้า -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มลูกค้า <span style="color: red;"> *</span></span>
                              <v-select v-model="tierID" :items="itemSelectTier" placeholder="เลือกกลุ่มลูกค้า" dense outlined item-text="tier_name" item-value="tier_id" :rules="Rules.empty"></v-select>
                            </v-col>
                            <!-- business_type -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ประเภทธุรกิจ <span style="color: red;"> *</span></span>
                              <v-select v-model="business_type" :items="type" placeholder="เลือกประเภทธุรกิจ" disabled dense outlined ></v-select>
                            </v-col>
                            <!-- Email -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">E-mail <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (ใช้ในกรณีแจ้งเตือนการวางขายสินค้าให้กับคู่ค้า)</span></span>
                              <v-text-field v-model="email" type="email" dense outlined placeholder="ระบุ E-mail" :maxLength="50" :rules="Rules.email"></v-text-field>
                            </v-col>
                            <!-- เครดิตวงเงิน -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">เครดิตวงเงิน <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (วงเงินไม่เกิน 2 ล้านบาท)</span></span>
                              <v-text-field v-model="credit" dense outlined placeholder="ระบุเครดิตวงเงิน" :maxLength="10" :rules="Rules.credit" ></v-text-field>
                            </v-col>
                            <!-- ชำระงวด -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ชำระงวด <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (1-24 งวด)</span></span>
                              <v-text-field v-model="numOfCreditTerm" dense outlined placeholder="จำนวนงวดชำระเงิน" :maxLength="2" :rules="Rules.numCredit"></v-text-field>
                            </v-col>
                            <!-- เครดิตเทอม -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">เครดิตเทอม <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (1-90 วัน)</span></span>
                              <v-text-field v-model="creditTrem" dense outlined placeholder="วันเวลาชำระเงิน จำนวนวัน" :maxLength="2" :rules="Rules.CreditTerm" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <!-- รูปแบบการนับเครดิตเทอม -->
                            <v-col cols="12">
                              <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รูปแบบการนับเครดิตเทอม <span style="color: red;"> *</span></span>
                              <v-select v-model="type_credit_term" :items="itemOfCreditTerm" item-text="name" item-value="value" placeholder="เลือกรูปแบบการนับเครดิตเทอม" dense outlined></v-select>
                            </v-col>
                          </v-row>
                        </v-form>
                      </div>
                    </v-col>
                    <!-- กรณีหาไม่เจอ -->
                    <v-col cols="12" md="12" sm="12" v-else-if="dataSearch.length === 0 && searchcompany !== ''">
                      <v-row justify="center" v-if="searchcompany !== '' && checkEnter === true" style="height: 300px;">
                        <p class="pt-6" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #CCCCCC;">ไม่พบรหัสบริษัทหรือชื่อบริษัทที่คุณค้นหา</p>
                      </v-row>
                    </v-col>
                    <!-- กรณียังไม่ได้หา -->
                    <v-col cols="12" md="12" sm="12" v-else>
                      <v-card elevation="0" height="350" width="100%">
                        <v-card-text style="text-align: center;">
                          <v-row dense>
                            <v-col cols="12" align="center" class="margin: auto;">
                              <v-img src="@/assets/ImageINET-Marketplace/Shop/search.png" height="100%" width="100%" max-height="120" max-width="116"></v-img>
                              <p style="font-size: 16px; color: #CCCCCC; font-weight: 400;" class="pt-4">ระบุรหัสบริษัทหรือชื่อบริษัท ในช่องค้นหา</p>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions v-if="dataSearch.length !== 0 && showformSetting">
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C" @click="CloseModalSearch()">ยกเลิก</v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded width="125" height="40" class="white--text my-auto" color="#27AB9C" @click="ModalConfirmAddPartner = !ModalConfirmAddPartner" :disabled="dataSearch.length !== 0 && showformSetting ? false : true">บันทึก</v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- รายละเอียดการยื่นขอเป็นคู่ค้า Complete -->
      <v-dialog v-model="ModalDetailPartner" width="1106" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : IpadProSize ? 'width: 100%' : 'width: 1106px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>คำขอเป็นคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="ModalDetailPartner = !ModalDetailPartner" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '1106px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0 d-flex" v-if="!MobileSize">
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                      </v-row>
                      <span class="ml-auto" style="text-align: end;">
                        <v-row dense justify="end">
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะ :</span>
                          <v-chip class="ma-2" :color="getColor(detailPartner.status)" small :text-color="getTextColor(detailPartner.status)">{{ getStatus(detailPartner.status) }}</v-chip>
                        </v-row>
                        <v-row dense justify="end">
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">วันที่อนุมัติ : {{ new Date(detailPartner.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                        </v-row>
                      </span>
                    </v-col>
                    <v-col cols="12" v-else>
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                      </v-row>
                      <v-row dense justify="start">
                        <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะ :</span>
                        <v-chip class="ma-2" :color="getColor(detailPartner.status)" small :text-color="getTextColor(detailPartner.status)">{{ getStatus(detailPartner.status) }}</v-chip>
                      </v-row>
                      <v-row dense justify="start">
                        <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">วันที่อนุมัติ : {{ new Date(detailPartner.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="py-5">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col cols="12" class="pb-4">
                      <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="12" md="6" sm="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <b>{{ detailPartner.company_code }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย : <b>{{ detailPartner.company_name_th }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาอังกฤษ : <b>{{ detailPartner.company_name_en }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{ detailPartner.company_phone }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">วันที่ยื่นคำขอ : <b>{{ new Date(detailPartner.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ประเภทธุรกิจ : <b>JV</b></p>
                        </v-col>
                        <v-col cols="12" md="6" sm="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Customer Name : <b>{{ detailPartner.customer_no }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Vendor No : <b>{{ detailPartner.vender_no }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มคู่ค้า : <b>{{ detailPartner.tier_name === '' ? ' - ' : detailPartner.tier_name }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">E-mail : <b>{{ detailPartner.email }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-if="detailPartner.reason !== null && detailPartner.status !== 'active'">เหตุผล : <b>{{ detailPartner.reason }}</b></p>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="py-5" v-if="detailPartner.status === 'active'">
                      <v-card width="100%" :height="MobileSize ? '100%' : '195'" elevation="0" outlined :class="MobileSize ? 'setBackgroundMobile' : 'setBackground'" style="border-radius: 8px;">
                        <v-card-text class="px-0">
                          <span :class="MobileSize ? '' : 'pt-1'" :style="MobileSize ? 'font-size: 12px; height: 25px;' : 'font-size: 16px; height: 36px;'" style="text-align: center; color: #FFFFFF; font-weight: 600; border-radius: 0px 20px 20px 0px; background: #1B5DD6; width: 128px; display: block;">
                            <v-icon color="white" :size="MobileSize ? '16' : ''">mdi-gift-outline</v-icon>
                            <span :class="MobileSize ? 'pl-1' : 'pl-1 pt-2'">สิทธิพิเศษ</span>
                          </span>
                        </v-card-text>
                        <v-card-text class="pt-0">
                          <v-row dense>
                            <v-col cols="12" md="4" sm="6">
                              <!-- <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">สิทธิพิเศษสำหรับผู้ซื้อ</p> -->
                              <p style="font-weight: 400; font-size: 14px; color: #333333;" :style="MobileSize ? 'line-height: 18px; margin-bottom: 12px;' : 'line-height: 22px;'">กลุ่มคู่ค้า : <br v-if="MobileSize"/><b>{{ detailPartner.tier_name }}</b></p>
                              <p style="font-weight: 400; font-size: 14px; color: #333333;" :style="MobileSize ? 'line-height: 18px; margin-bottom: 12px;' : 'line-height: 22px;'">เครดิตวงเงิน : <br v-if="MobileSize"/><b>{{ detailPartner.credit }} บาท</b></p>
                              <p style="font-weight: 400; font-size: 14px; color: #333333;" :style="MobileSize ? 'line-height: 18px; margin-bottom: 12px;' : 'line-height: 22px;'">เครดิตเทอม : <br v-if="MobileSize"/><b>{{ detailPartner.credit_term }} วัน</b></p>
                            </v-col>
                            <v-col cols="12" md="4" sm="6">
                              <!-- <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">สิทธิพิเศษสำหรับผู้ซื้อ</p> -->
                              <p style="font-weight: 400; font-size: 14px; color: #333333;" :style="MobileSize ? 'line-height: 18px; margin-bottom: 12px;' : 'line-height: 22px;'">ชำระงวด : <br v-if="MobileSize"/><b>{{ detailPartner.num_of_credit_term }} งวด</b></p>
                              <p style="font-weight: 400; font-size: 14px; color: #333333;" :style="MobileSize ? 'line-height: 18px; margin-bottom: 12px;' : 'line-height: 22px;'">รูปแบบการนับเครดิตเทอม : <br v-if="MobileSize"/><b>{{ detailPartner.type_credit_term === 'create_order' ? 'นับจากการยืนยันคำสั่งซื้อ' : 'นับจากวันยอมรับสินค้า' }}</b></p>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                    <v-col cols="12" md="12" v-if="detailPartner.status === 'active' && listDocument === null || listDocument === 0">
                      <v-row dense class="pb-4">
                        <v-col cols="12">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดเอกสาร</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" >
                          <v-card width="100%" style="border-radius: 8px;" height="60" outlined elevation="0" dense >
                            <v-card-text >
                              <v-row dense no-gutters>
                                <v-col cols="12">
                                  <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A" v-if="!MobileSize">ไม่มีเอกสารในการยื่นคำขอเป็นคู่ค้า</p>
                                  <p style="font-weight: 400; font-size: 16px; line-height: 22px; color: #9A9A9A" v-else>ไม่มีเอกสารในการยื่นคำขอเป็นคู่ค้า</p>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12" v-else>
                      <v-row dense class="pb-4">
                        <v-col cols="12">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดเอกสาร</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" v-for="(item, index) in detailPartner.document_list" :key="index">
                          <v-card width="100%" :height="MobileSize ? '100%' : '60'" style="border-radius: 8px;" outlined elevation="0" dense class="mb-4">
                            <v-card-text class="pa-4">
                              <v-row dense class="d-flex">
                                <v-col cols="12" md="2" sm="2" class="ml-auto">
                                  <v-row dense class="pt-1">
                                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Document/PDFNew.png" max-height="28" max-width="22"></v-img>
                                    <span class="pl-3 pt-1" style="font-size: 16px; font-weight: 400; color: #333333;">เอกสารบริษัท</span>
                                  </v-row>
                                </v-col>
                                <v-col cols="12" md="10" sm="10" :class="MobileSize ? 'ml-auto mt-2' : 'mr-auto'" :align="MobileSize ? 'start' : 'end'">
                                  <v-chip color="rgba(27, 93, 214, 0.10)" text-color="#1B5DD6" style="text-decoration: underline;"  @click="GotoPDF(item)">
                                    <span style="font-weight: 400; font-size: 14px; line-height: 24px;" v-if="!MobileSize">{{ item.name_document }}</span>
                                    <span style="font-weight: 400; font-size: 14px; line-height: 22px;" v-else>{{ item.name_document }}</span>
                                  </v-chip>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions v-if="detailPartner.status === 'active'">
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 10px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="157" height="40" dense outlined color="#27AB9C" class="my-auto" @click="NotApproveWhenAccept(detailPartner)">ยกเลิกการเป็นคู่ค้า</v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded dense color="#27AB9C" :width="MobileSize ? '120' : '154'" height="40" class="my-auto white--text" @click="EditParterAfter(detailPartner)">
                  <v-icon class="pr-2" small>mdi-pencil-outline</v-icon>
                  แก้ไข
                </v-btn>
              </v-row>
            </v-card-actions>
            <v-card-actions v-else-if="detailPartner.status === 'inactive'">
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-spacer></v-spacer>
                <!-- <v-btn dense dark outlined color="#27AB9C" class="pl-4 pr-4 mt-2" @click="NotApproveWhenAccept(detailPartner)">ยกเลิกคู่ค้า</v-btn> -->
                <v-btn dense color="#27AB9C" width="154" height="40" class="my-auto white--text" @click="ConfirmActivePartner(detailPartner)">เปิดใช้งานคู่ค้า</v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- รายละเอียดอนุมัติขอเป็นคู่ค้า Complete -->
      <v-dialog v-model="ModalApprovePartner" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : IpadProSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>คำขอเป็นคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="ModalApprovePartner = !ModalApprovePartner" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0 d-flex">
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                      </v-row>
                      <span class="ml-auto" style="text-align: end;">
                        <v-row dense justify="end">
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะ :</span>
                          <v-chip class="ma-2" :color="getColor(detailPartner.status)" small :text-color="getTextColor(detailPartner.status)">{{ getStatus(detailPartner.status) }}</v-chip>
                        </v-row>
                      </span>
                    </v-col>
                    <v-col cols="12" class="py-5">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col cols="12" class="pb-4">
                      <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="12" md="4" sm="12">
                        </v-col>
                        <v-col cols="12" md="8" sm="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <b>{{ detailPartner.company_code }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย : <b>{{ detailPartner.company_name_th }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาอังกฤษ : <b>{{ detailPartner.company_name_en }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{ detailPartner.company_phone }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">วันที่ยื่นคำขอ : <b>{{ new Date(detailPartner.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มคู่ค้า : <b>{{ detailPartner.tier_name === '' ? ' - ' : detailPartner.tier_name }}</b></p>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="py-5">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col cols="12" md="12" v-if="listDocument === null || listDocument === 0">
                      <v-row dense class="pb-4">
                        <v-col cols="12">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดเอกสาร</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" >
                          <v-card width="100%" height="60" style="border-radius: 8px;" outlined elevation="0" dense >
                            <v-card-text>
                              <v-row dense no-gutters>
                                <v-col cols="12">
                                  <p style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333; display:flex; justify-content:center;" v-if="!MobileSize">ไม่มีเอกสารในการยื่นคำขอเป็นคู่ค้า</p>
                                  <p style="font-weight: 700; font-size: 16px; line-height: 22px; color: #333333; display:flex; justify-content:center;" v-else>ไม่มีเอกสารในการยื่นคำขอเป็นคู่ค้า</p>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12" v-else>
                      <v-row dense>
                        <v-col cols="12">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดเอกสาร</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" v-for="(item, index) in detailPartner.document_list" :key="index">
                          <v-card width="100%" :height="MobileSize ? '100%' : '60'" style="border-radius: 8px;"  outlined elevation="0" dense class="mb-4" @click="GotoPDF(item)">
                            <v-card-text class="pa-4">
                              <v-row dense class="d-flex">
                                <v-col class="ml-auto">
                                  <v-row dense class="pt-1">
                                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Document/PDFNew.png" max-height="28" max-width="22"></v-img>
                                    <span class="pl-3 pt-1" style="font-size: 16px; font-weight: 400; color: #333333;">เอกสารบริษัท</span>
                                  </v-row>
                                </v-col>
                                <v-col :class="MobileSize ? 'ml-auto mt-2' : 'mr-auto'" :align="MobileSize ? 'start' : 'end'">
                                  <v-chip color="rgba(27, 93, 214, 0.10)" text-color="#1B5DD6" style="text-decoration: underline;"  @click="GotoPDF(item)">
                                    <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;" v-if="!MobileSize">{{ item.name_document }}</span>
                                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-else>{{ item.name_document }}</span>
                                  </v-chip>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row dense style="height: 88px; background: #F5FCFB; " :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="NotApprove()">
                  ปฏิเสธ
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded dense color="#27AB9C" width="125" height="40" class="my-auto white--text" @click="Approve()">
                  อนุมัติ
                </v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- รายละเอียดไม่อนุมัติขอเป็นคู่ค้า Complete -->
      <v-dialog v-model="ModalNotApprove" width="750" persistent :style="MobileSize ? 'z-index: 16000004' : ''">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>คำขอเป็นคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="CancelNotApprove()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 20px 20px 20px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" class="pb-4">
                      <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="12" md="4" sm="12">
                        </v-col>
                        <v-col cols="12" md="8" sm="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <b>{{ detailPartner.company_code }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย : <b>{{ detailPartner.company_name_th }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาอังกฤษ : <b>{{ detailPartner.company_name_en }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{ detailPartner.company_phone }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">วันที่ยื่นคำขอ : <b>{{ new Date(detailPartner.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มคู่ค้า : <b>{{ detailPartner.tier_name === '' ? ' - ' : detailPartner.tier_name }}</b></p>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12">
                      <span class="pt-5 pb-4" style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">เหตุผลการไม่อนุมัติคำขอเป็นคู่ค้า</span>
                      <v-textarea
                        outlined
                        style="border-radius: 8px;"
                        placeholder="กรอกเหตุผลการไม่อนุมัติคำขอเป็นคู่ค้า"
                        v-model="reason"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="CancelNotApprove()">
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded dense color="#27AB9C" width="125" height="40" class="my-auto white--text" :disabled="reason !== '' ? false : true" @click="CheckNotApprove()">
                  บันทึก
                </v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- รายละเอียดไม่อนุมัติขอเป็นคู่ค้าเมื่ออนุมัติแล้ว Complete -->
      <v-dialog v-model="ModalNotApproveAfter" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>การยกเลิกคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="CancelNotApproveAfter()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 20px 20px 20px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12">
                      <v-row dense no-gutters justify="center">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/DocNotApprove.png" contain max-height="175" max-width="160"></v-img>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12">
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เหตุผลการยกเลิกการคู่ค้า <span style="color:red"> *</span></span>
                      <v-textarea :rules="Rules.reason" @keypress="CheckSpacebar($event)"
                        outlined
                        style="border-radius: 8px;"
                        placeholder="เหตุผลการยกเลิกการคู่ค้า"
                        v-model="reason"
                      ></v-textarea>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C" @click="CancelNotApproveAfter()">
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded width="125" height="40" class="white--text my-auto" color="#27AB9C" :disabled="reason !== '' ? false : true" @click="CheckNotApproveAfter()">
                  บันทึก
                </v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- ยืนยันไม่อนุมัติการขอเป็นคู่ค้า Complete -->
      <v-dialog v-model="ModalConfirmNotApprove" width="424"  :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalConfirmNotApprove = !ModalConfirmNotApprove"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ไม่อนุมัติคำขอเป็นคู่ค้า</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmNotApprove = !ModalConfirmNotApprove">ยกเลิก</v-btn>
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ConfirmNotApprove()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- ยืนยันไม่อนุมัติการขอเป็นคู่ค้า Complete -->
      <v-dialog v-model="ModalConfirmNotApproveAfter" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalConfirmNotApproveAfter = !ModalConfirmNotApproveAfter"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>คุณยกเลิกขอเป็นคู่ค้า</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmNotApproveAfter = !ModalConfirmNotApproveAfter">ยกเลิก</v-btn>
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ConfirmNotApproveAfter()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- ยืนยันเพิ่มคู่ค้า Complete -->
      <v-dialog v-model="ModalConfirmAddPartner" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalConfirmAddPartner = !ModalConfirmAddPartner"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เพิ่มคู่ค้า</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmAddPartner = !ModalConfirmAddPartner">ยกเลิก</v-btn>
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" :disabled="disableBeforeAPI" @click="ActivePartner('Add')">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- ยืนยันเพิ่มคู่ค้าสำเร็จ Complete -->
      <v-dialog v-model="ModalOpenCreatePartnerSuccess" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalOpenCreatePartnerSuccess = !ModalOpenCreatePartnerSuccess"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เพิ่มคู่ค้าเรียบร้อย</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการเพิ่มคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModalOpenCreatePartnerSuccess = !ModalOpenCreatePartnerSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- ยืนยันเปิดการเป็นคู่ค้าอีกรอบ Complete -->
      <v-dialog v-model="ModalConfirmOpenPartner" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalConfirmOpenPartner = !ModalConfirmOpenPartner"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เปิดใช้งานขอเป็นคู่ค้า</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmOpenPartner = !ModalConfirmOpenPartner">ยกเลิก</v-btn>
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" :disabled="disableBeforeAPI" @click="ActivePartner('AddAgain')">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- ยืนยันเปิดการเป็นคู่ค้าอีกรอบสำเร็จ Complete -->
      <v-dialog v-model="ModalOpenPartnerSuccess" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalOpenPartnerSuccess = !ModalOpenPartnerSuccess"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เปิดใช้งานขอเป็นคู่ค้าเรียบร้อย</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการเปิดใช้งานขอเป็นคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModalOpenPartnerSuccess = !ModalOpenPartnerSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- สำเร็จ Complete -->
      <v-dialog v-model="ModalSuccess" width="373" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalSuccess = !ModalSuccess"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการปฏิเสธคำขอคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModalSuccess = !ModalSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- สำเร็จ Complete-->
      <v-dialog v-model="ModalSuccessAfter" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalSuccessAfter = !ModalSuccessAfter"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกการเป็นคู่ค้าเรียบร้อย</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยกเลิกการเป็นคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModalSuccessAfter = !ModalSuccessAfter">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- กำหนดสิทธิ์ผู้ซื้อ ตอนสร้าง Partner Complete -->
      <v-dialog v-model="ModalSettingPartnerCompany" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="CloseModalSettingPartnerCompany()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 20px 20px 20px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0">
                      <v-row dense no-gutters>
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/settingPartner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">กำหนดสิทธิ์คู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>กำหนดสิทธิ์คู่ค้า</p>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row class="py-5">
                    <v-col cols="12" class="mx-2">
                      <v-divider></v-divider>
                    </v-col>
                  </v-row>
                  <v-row dense class="mx-2">
                    <v-col cols="12">
                      <v-form ref="formSettingApprove" :lazy-validation="lazy">
                        <v-row dense class="mx-2">
                          <v-col cols="12" class="pb-4">
                            <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                          </v-col>
                          <v-col cols="12">
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ detailPartner.company_id }}</span></span><br/>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ detailPartner.company_name_th }}</span></span><br/>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ detailPartner.company_name_en }}</span></span><br/>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ detailPartner.company_phone }}</span></span>
                          </v-col>
                        </v-row>
                        <v-row dense class="py-5">
                          <v-col cols="12" class="mx-2">
                            <v-divider></v-divider>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-2 mx-2">
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดผู้ซื้อ</span>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-2 mx-2">
                          <!-- รหัสผู้ซื้อ -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Customer No.</span>
                            <v-text-field v-model="customerCode" :maxLength="20" dense outlined placeholder="หมายเลขหรือตัวอักษรไม่เกิน 20 ตัวอักษร"></v-text-field>
                            <!-- <v-text-field v-model="customerCode" disabled dense outlined placeholder="Customer No." :rules="Rules.code"></v-text-field> -->
                          </v-col>
                          <!-- Customer No. -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Customer Name</span>
                            <v-text-field v-model="customerNo" dense outlined placeholder="Customer Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-()\s]/g, '')"></v-text-field>
                              <!-- <v-autocomplete v-model="customerNo" :items="listCustomer" item-text="CustomerName" item-value="CustomerName" dense outlined placeholder="Customer Name" :rules="Rules.customerNo"></v-autocomplete> -->
                          </v-col>
                          <!-- Vendor No. -->
                          <!-- <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Vendor No.</span>
                            <v-text-field v-model="venderNo" dense outlined placeholder="Vendor No." :maxLength="100"></v-text-field>
                          </v-col> -->
                          <!-- Vendor Name. -->
                          <!-- <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Vendor Name </span>
                            <v-text-field v-if="isJV === false" v-model="descriptionPR" dense outlined placeholder="ระบุ Vendor Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-\s]/g, '')"></v-text-field>
                            <v-autocomplete v-else v-model="descriptionPR" :items="listVendor" item-text="vendor_name_th" item-value="vendor_name_th" label="เลือก Vendor Name" solo dense no-data-text="ไม่มีข้อมูล Vendor Name."></v-autocomplete>
                          </v-col> -->
                          <!-- กลุ่มลูกค้า -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มลูกค้า <span style="color: red;"> *</span></span>
                            <v-select v-model="tierID" :items="detailListTier" placeholder="เลือกกลุ่มลูกค้า" dense outlined item-text="tier_name" item-value="tier_id" :rules="Rules.empty"></v-select>
                          </v-col>
                          <!-- business_type -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ประเภทธุรกิจ <span style="color: red;"> *</span></span>
                            <v-select v-model="business_type" :items="type" placeholder="เลือกประเภทธุรกิจ" disabled dense outlined ></v-select>
                          </v-col>
                          <!-- Email -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">E-mail <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (ใช้ในกรณีแจ้งเตือนการวางขายสินค้าให้กับคู่ค้า)</span></span>
                            <v-text-field v-model="email" type="email" dense outlined placeholder="ระบุ E-mail" :maxLength="50" :rules="Rules.email"></v-text-field>
                          </v-col>
                          <!-- เครดิตวงเงิน -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">เครดิตวงเงิน <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (วงเงินไม่เกิน 2 ล้านบาท)</span></span>
                            <v-text-field v-model="credit" dense outlined placeholder="วงเงินไม่เกิน 2 ล้านบาท" :maxLength="10" :rules="Rules.credit" ></v-text-field>
                          </v-col>
                          <!-- ชำระงวด -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ชำระงวด <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (1-24 งวด)</span></span>
                            <v-text-field v-model="numOfCreditTerm" dense outlined placeholder="จำนวนงวดชำระเงิน" :maxLength="2" :rules="Rules.numCredit"></v-text-field>
                          </v-col>
                          <!-- เครดิตเทอม -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">เครดิตเทอม <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (1-90 วัน)</span></span>
                            <v-text-field v-model="creditTrem" dense outlined placeholder="วันเวลาชำระเงิน จำนวนวัน" :maxLength="2" :rules="Rules.CreditTerm" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                          </v-col>
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รูปแบบการนับเครดิตเทอม <span style="color: red;"> *</span></span>
                            <!-- <v-text-field v-model="creditTrem" dense outlined placeholder="วันเวลาชำระเงิน จำนวนวัน" :rules="Rules.CreditTerm" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                            <v-select v-model="type_credit_term" :items="itemOfCreditTerm" item-text="name" item-value="value" placeholder="เลือกรูปแบบการนับเครดิตเทอม" dense outlined></v-select>
                          </v-col>
                        </v-row>
                      </v-form>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="CloseModalSettingPartnerCompany()">
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded dense color="#27AB9C" width="125" height="40" class="my-auto white--text" @click="ConfrimApprove()">
                  บันทึก
                </v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- กำหนดสิทธิ์ผู้ซื้อ ตอนแก้ไข Partner เลือกรายละเอียด Complete-->
      <v-dialog v-model="ModalSettingPartnerCompanyAfter" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>แก้ไขข้อมูลคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="ModalSettingPartnerCompanyAfter = !ModalSettingPartnerCompanyAfter" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;"  :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0">
                      <v-row dense no-gutters>
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/settingPartner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">กำหนดสิทธิ์คู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>กำหนดสิทธิ์คู่ค้า</p>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="py-5 mx-2">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col cols="12" class="mb-4">
                      <v-form ref="formSettingApproveAfter" :lazy-validation="lazy">
                        <v-row dense class="mx-2">
                          <v-col cols="12" class="pb-4">
                              <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                            </v-col>
                          <v-col cols="12">
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataForAccept.company_id }}</span></span><br/>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataForAccept.company_name_th }}</span></span><br/>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาอังกฤษ :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataForAccept.company_name_en }}</span></span><br/>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ :  <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{ dataForAccept.company_phone }}</span></span>
                          </v-col>
                        </v-row>
                        <v-row dense class="py-5">
                          <v-col cols="12" class="mx-2">
                            <v-divider></v-divider>
                          </v-col>
                        </v-row>
                        <v-row dense class="mx-2">
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดผู้ซื้อ</span>
                          </v-col>
                        </v-row>
                        <v-row dense class="mt-2 mx-2">
                          <!-- รหัสผู้ซื้อ -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Customer No.</span>
                            <v-text-field v-model="dataForAccept.customer_id" :maxLength="20" dense outlined placeholder="หมายเลขหรือตัวอักษรไม่เกิน 20 ตัวอักษร"></v-text-field>
                            <!-- <v-text-field v-model="customerCode" disabled dense outlined placeholder="Customer No." :rules="Rules.code"></v-text-field> -->
                          </v-col>
                          <!-- Customer No. ใช้คีย์เดิมของ venderNo -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Customer Name</span>
                            <v-text-field v-model="customerNo" dense outlined placeholder="Customer Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-()\s]/g, '')"></v-text-field>
                            <!-- <v-autocomplete v-model="customerNo" :items="listCustomer" item-text="CustomerName" item-value="CustomerCode" dense outlined placeholder="Customer Name" :rules="Rules.customerNo"></v-autocomplete> -->
                          </v-col>
                          <!-- Vendor No. -->
                          <!-- <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Vendor No.</span>
                            <v-text-field v-model="venderNo" dense outlined placeholder="Vendor No."></v-text-field>
                          </v-col> -->
                          <!-- Vendor Name. -->
                          <!-- <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">Vendor Name</span>
                            <v-text-field v-if="isJV === false" v-model="descriptionPR" dense outlined placeholder="ระบุ Vendor Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-\s]/g, '')"></v-text-field>
                            <v-autocomplete v-else v-model="descriptionPR" :items="listVendor" item-text="vendor_name_th" item-value="vendor_name_th" label="Vendor Name" solo dense no-data-text="ไม่มีข้อมูล Vendor Name."></v-autocomplete>
                          </v-col> -->
                          <!-- กลุ่มลูกค้า -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มลูกค้า <span style="color: red;"> *</span></span>
                            <v-select v-model="tierID" :items="detailListTier" placeholder="เลือกกลุ่มลูกค้า" dense outlined item-text="tier_name" item-value="tier_id" :rules="Rules.empty"></v-select>
                          </v-col>
                          <!-- business_type -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ประเภทธุรกิจ <span style="color: red;"> *</span></span>
                            <v-select v-model="business_type" :items="type" placeholder="เลือกประเภทธุรกิจ" disabled dense outlined ></v-select>
                          </v-col>
                          <!-- Email -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">E-mail <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (ใช้ในกรณีแจ้งเตือนการวางขายสินค้าให้กับคู่ค้า)</span></span>
                            <v-text-field v-model="email" type="email" dense outlined placeholder="ระบุ E-mail" :maxLength="50" :rules="Rules.email"></v-text-field>
                          </v-col>
                          <!-- เครดิตวงเงิน -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">เครดิตวงเงิน <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (วงเงินไม่เกิน 2 ล้านบาท)</span></span>
                            <v-text-field v-model="dataForAccept.credit" dense outlined placeholder="วงเงินไม่เกิน 2 ล้านบาท" :maxLength="10" :rules="Rules.credit" ></v-text-field>
                          </v-col>
                          <!-- ชำระงวด -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">ชำระงวด <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (1-24 งวด)</span></span>
                            <v-text-field v-model="dataForAccept.num_of_credit_term" dense outlined placeholder="จำนวนงวดชำระเงิน" :maxLength="2" :rules="Rules.numCredit" ></v-text-field>
                          </v-col>
                          <!-- เครดิตเทอม -->
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">เครดิตเทอม <span style="color: red;"> *</span><span style="color: #9E9E9E; font-size: 12px;"> (1-90 วัน)</span></span>
                            <v-text-field v-model="dataForAccept.credit_term" dense outlined placeholder="วันเวลาชำระเงิน จำนวนวัน" :maxLength="2" :rules="Rules.CreditTerm" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                          </v-col>
                          <v-col cols="12">
                            <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">รูปแบบการนับเครดิตเทอม <span style="color: red;"> *</span></span>
                            <!-- <v-text-field v-model="creditTrem" dense outlined placeholder="วันเวลาชำระเงิน จำนวนวัน" :rules="Rules.CreditTerm" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                            <v-select v-model="dataForAccept.type_credit_term" :items="itemOfCreditTerm" item-text="name" item-value="value" placeholder="เลือกรูปแบบการนับเครดิตเทอม" dense outlined></v-select>
                          </v-col>
                        </v-row>
                      </v-form>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
                <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C"  @click="ModalSettingPartnerCompanyAfter = !ModalSettingPartnerCompanyAfter">
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded width="125" height="40" class="white--text my-auto" color="#27AB9C" @click="ModalConfirmSettingPartnerCompanyAfter = !ModalConfirmSettingPartnerCompanyAfter">
                  บันทึก
                </v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- ยืนยันแก้ไข Partner Complete -->
      <v-dialog v-model="ModalConfirmSettingPartnerCompanyAfter" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalConfirmSettingPartnerCompanyAfter = !ModalConfirmSettingPartnerCompanyAfter"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขข้อมูลคู่ค้า</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmSettingPartnerCompanyAfter = !ModalConfirmSettingPartnerCompanyAfter">ยกเลิก</v-btn>
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ConfrimApproveAfter()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- แก้ไข Partner สำเร็จ Complete -->
      <v-dialog v-model="ModalSettingPartnerSuccess" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModalSettingPartnerSuccess = !ModalSettingPartnerSuccess"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>คุณได้ทำการแก้ไขข้อมูลคู่ค้าเรียบร้อย</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModalSettingPartnerSuccess = !ModalSettingPartnerSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- เพิ่ม Partner สำเร็จ Complete -->
      <v-dialog v-model="ModelAddPartnerSuccess" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModelAddPartnerSuccess = !ModelAddPartnerSuccess"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เพิ่มคู่ค้าเรียบร้อย</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการเพิ่มคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModelAddPartnerSuccess = !ModelAddPartnerSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- ยกเลิก Partner สำเร็จ Complete -->
      <v-dialog v-model="ModelCanclePartnerSuccess" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="ModelCanclePartnerSuccess = !ModelCanclePartnerSuccess"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกการเป็นคู่ค้าเรียบร้อย</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยกเลิกการเป็นคู่ค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="ModelCanclePartnerSuccess = !ModelCanclePartnerSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- รายละเอียดหลังการยื่นขอเป็นคู่ค้าสำเร็จ Complete -->
      <v-dialog v-model="ModalDetailAfterCreateSuccess" width="1106" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pb-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 1106px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>คำขอเป็นคู่ค้า</b></span>
                </v-col>
                <v-btn fab small @click="ModalDetailAfterCreateSuccess = !ModalDetailAfterCreateSuccess" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '1106px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" md="12" class="mr-0 d-flex">
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                        <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเป็นคู่ค้า</p>
                      </v-row>
                      <span class="ml-auto" style="text-align: end;">
                        <v-row dense justify="end">
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">สถานะ :</span>
                          <v-chip class="ma-2" :color="getColor(detailAfterSuccess.status)" small :text-color="getTextColor(detailAfterSuccess.status)">{{ getStatus(detailAfterSuccess.status) }}</v-chip>
                        </v-row>
                        <v-row dense justify="end">
                          <span class="mt-2 pr-2" style="font-weight: 400; color: #333333; font-size: 16px;">วันที่อนุมัติ : {{ new Date(detailAfterSuccess.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                        </v-row>
                      </span>
                    </v-col>
                    <v-col cols="12" class="py-5">
                      <v-divider></v-divider>
                    </v-col>
                    <v-col cols="12" class="pb-4">
                      <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบริษัท</span>
                    </v-col>
                    <v-col cols="12">
                      <v-row dense>
                        <v-col cols="12" md="6" sm="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสบริษัท : <b>{{ detailAfterSuccess.company_code }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาไทย : <b>{{ detailAfterSuccess.company_name_th }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อภาษาอังกฤษ : <b>{{ detailAfterSuccess.company_name_en }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{ detailAfterSuccess.company_phone }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">วันที่ยื่นคำขอ : <b>{{ new Date(detailAfterSuccess.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ประเภทธุรกิจ : <b>JV</b></p>
                        </v-col>
                        <v-col cols="12" md="6" sm="12">
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Customer Name : <b>{{ detailAfterSuccess.customer_no }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Vendor No : <b>{{ detailAfterSuccess.vender_no }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มคู่ค้า : <b>{{ detailAfterSuccess.tier_name === '' ? ' - ' : detailAfterSuccess.tier_name }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">E-mail : <b>{{ detailAfterSuccess.email }}</b></p>
                          <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" v-if="detailAfterSuccess.reason !== null && detailAfterSuccess.status !== 'active'">เหตุผล : <b>{{ detailAfterSuccess.reason }}</b></p>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" class="py-5" v-if="detailAfterSuccess.status === 'active'">
                      <v-card width="100%" height="195" elevation="0" outlined class="setBackground">
                        <v-card-text class="px-0">
                          <span class="pt-1" style="text-align: center; color: #FFFFFF; font-size: 16px; font-weight: 600; border-radius: 0px 20px 20px 0px; background: #1B5DD6; width: 128px; height: 36px; display: block;">
                            <v-icon color="white">mdi-gift-outline</v-icon>
                            <span class="pl-1 pt-2">สิทธิพิเศษ</span>
                          </span>
                        </v-card-text>
                        <v-card-text class="pt-0">
                          <v-row dense>
                            <v-col cols="12" md="4" sm="6">
                              <!-- <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">สิทธิพิเศษสำหรับผู้ซื้อ</p> -->
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">กลุ่มคู่ค้า : <b>{{ detailAfterSuccess.tier_name }}</b></p>
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เครดิตวงเงิน : <b>{{ detailAfterSuccess.credit }} บาท</b></p>
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เครดิตเทอม : <b>{{ detailAfterSuccess.credit_term }} วัน</b></p>
                            </v-col>
                            <v-col cols="12" md="4" sm="6">
                              <!-- <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">สิทธิพิเศษสำหรับผู้ซื้อ</p> -->
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชำระงวด : <b>{{ detailAfterSuccess.num_of_credit_term }} งวด</b></p>
                              <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รูปแบบการนับเครดิตเทอม : <b>{{ detailAfterSuccess.type_credit_term === 'create_order' ? 'นับจากการยืนยันคำสั่งซื้อ' : 'นับจากวันยอมรับสินค้า' }}</b></p>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                    <v-col cols="12" md="12" v-if="detailAfterSuccess.document_list !== null || listDocument !== 0">
                      <v-row dense class="pb-4">
                        <v-col cols="12">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดเอกสาร</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" v-for="(item, index) in detailAfterSuccess.document_list" :key="index">
                          <v-card width="100%" :height="MobileSize ? '100%' : '60'" style="border-radius: 8px;" outlined elevation="0" dense class="mb-4">
                            <v-card-text class="pa-4">
                              <v-row dense class="d-flex">
                                <v-col cols="12" md="2" sm="2" class="ml-auto">
                                  <v-row dense class="pt-1">
                                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Document/PDFNew.png" max-height="28" max-width="22"></v-img>
                                    <span class="pl-3 pt-1" style="font-size: 16px; font-weight: 400; color: #333333;">เอกสารบริษัท</span>
                                  </v-row>
                                </v-col>
                                <v-col cols="12" md="10" sm="10" :class="MobileSize ? 'ml-auto mt-2' : 'mr-auto'" :align="MobileSize ? 'start' : 'end'">
                                  <v-chip color="rgba(27, 93, 214, 0.10)" text-color="#1B5DD6" style="text-decoration: underline;"  @click="GotoPDF(item)">
                                    <p style="font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;" v-if="!MobileSize">{{ item.name_document }}</p>
                                    <p style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;" v-else>{{ item.name_document }}</p>
                                  </v-chip>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" v-else>
                      <v-row dense class="pb-4">
                        <v-col cols="12">
                          <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดเอกสาร</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" >
                          <v-card width="100%" style="border-radius: 8px;" height="60" outlined elevation="0" dense >
                            <v-card-text >
                              <v-row dense no-gutters>
                                <v-col cols="12">
                                  <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A" v-if="!MobileSize">ไม่มีเอกสารในการยื่นคำขอเป็นคู่ค้า</p>
                                  <p style="font-weight: 400; font-size: 16px; line-height: 22px; color: #9A9A9A" v-else>ไม่มีเอกสารในการยื่นคำขอเป็นคู่ค้า</p>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
            <v-card-actions>
              <v-row justify="center" dense style="height: 88px; background: #F5FCFB; padding: 0px 44px;">
                <v-btn rounded width="154" height="40" dense color="#27AB9C" class="my-auto white--text" @click="ModalDetailAfterCreateSuccess = !ModalDetailAfterCreateSuccess">ปิด</v-btn>
              </v-row>
            </v-card-actions>
          </v-card-text>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      disableBeforeAPI: false,
      isJV: false,
      DateSearch: '',
      DateToSearch: '',
      modal: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      listCustomer: [],
      getCompanyCode: '',
      timer: '',
      email: '',
      business_type: 'JV',
      type: ['JV'],
      lazy: false,
      partnerList: [],
      selected: null,
      search: '',
      dialog: false,
      disableTable: true,
      countall: 0,
      countActive: 0,
      showCountOrder: 0,
      countfailed: 0,
      countPending: 0,
      countInActive: 0,
      StateStatus: 0,
      quantity: 0,
      pageCount: 5,
      page: 1,
      itemsPerPage: 10,
      onedata: [],
      searchcompany: '',
      shopDetail: '',
      shopDetail2: '',
      listVendor: [],
      descriptionPR: '',
      showformSetting: false,
      itemSelectTier: [],
      customerCode: '',
      tierID: '',
      credit: '',
      reason: '',
      numOfCreditTerm: '',
      venderNo: '',
      customerNo: '',
      venderName: '',
      // venderCode: '',
      creditTrem: '',
      listDocument: 0,
      headers: [
        { text: 'บริษัท', value: 'company_name_th', width: '250', sortable: false, class: 'backgroundTable fontTable--text' },
        // { text: 'Vendor No.', value: 'vender_no', width: '180', sortable: false, class: 'backgroundTable fontTable--text' },
        // { text: 'ประเภทธุรกิจ', filterable: false, value: '', width: '180', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ยื่นคำขอ', filterable: false, value: 'created_at', width: '150', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', filterable: false, value: 'status', width: '150', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', filterable: false, value: 'manages', width: '150', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      DataTable: [],
      ModalSearchCompany: false,
      ModalDetailPartner: false,
      ModalApprovePartner: false,
      ModalConfirmOpenPartner: false,
      ModalConfirmAddPartner: false,
      ModalOpenPartnerSuccess: false,
      ModalOpenCreatePartnerSuccess: false,
      ModalNotApprove: false,
      ModalConfirmNotApprove: false,
      ModalConfirmNotApproveAfter: false,
      ModalSuccessAfter: false,
      ModalSettingPartnerCompanyAfter: false,
      ModalConfirmSettingPartnerCompanyAfter: false,
      ModalSettingPartnerSuccess: false,
      ModelAddPartnerSuccess: false,
      ModelCanclePartnerSuccess: false,
      ModalNotApproveAfter: false,
      ModalSuccess: false,
      dataForAccept: [],
      ModalSettingPartnerCompany: false,
      ModalDetailAfterCreateSuccess: false,
      dataSearch: [],
      detailPartner: [],
      detailListTier: [],
      detailConfirmActivePartner: [],
      detailAfterSuccess: [],
      checkEnter: false,
      time: '',
      type_credit_term: 'create_order',
      titlSuccess: '',
      msgSuccess: '',
      itemOfCreditTerm: [
        { name: 'นับจากการยืนยันคำสั่งซื้อ', value: 'create_order' },
        { name: 'นับจากวันยอมรับสินค้า', value: 'approve_product' }
      ],
      dataform: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        code: [
          v => !!v || 'กรุณากรอกรหัสผู้ซื้อ',
          v => /^[0-9a-zA-Z]/.test(v) || 'กรุณากรอกรหัสผู้ซื้อให้ถูกต้อง'
        ],
        credit: [
          v => !!v || 'กรุณากรอกเครดิตวงเงิน',
          // v => parseInt(v) !== 0 & v !== '' || 'กรุณากรอกเครดิตวงเงิน 1-2,000,000 บาท'
          v => /^[1-9]/.test(v) || 'กรุณากรอกเครดิตวงเงิน 1-2,000,000 บาท',
          v => (v >= 1 && v <= 2000000) || 'จำนวนเครดิตวงเงินต้องอยู่ระหว่าง 1-2,000,000 บาท'
        ],
        numCredit: [
          v => !!v || 'กรุณากรอกชำระงวด',
          v => /^[1-9][0-9]*$/.test(v) || 'กรุณากรอกจำนวนงวด 1-24 งวด',
          v => (v >= 1 && v <= 24) || 'จำนวนงวดต้องอยู่ระหว่าง 1-24'
        ],
        CreditTerm: [
          v => !!v || 'กรุณากรอกเครดิตเทอม',
          v => /^[1-9][0-9]*$/.test(v) || 'กรุณากรอกจำนวนเครดิตเทอม 1-90 วัน',
          v => (v >= 1 && v <= 90) || 'จำนวนเครดิตเทอมต้องอยู่ระหว่าง 1-90 วัน'
        ],
        email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /^[^@]+@\w+(\.\w+)+\w$/.test(v) || 'กรุณากรอก Email ให้ถูกต้อง'
        ],
        venderNo: [
          v => !!v || 'กรุณากรอก Vendor No.',
          v => /^[0-9a-zA-Z]/.test(v) || 'กรุณากรอกรหัสผู้ขายให้ถูกต้อง'
        ],
        customerNo: [
          v => !!v || 'กรุณากรอก Customer Name',
          v => /^[0-9a-zA-Zก-๏-()]/.test(v) || 'กรุณากรอก Customer Name ให้ถูกต้อง'
        ],
        reason: [
          v => !!v || 'กรุณากรอกเหตุผล'
        ]
        // ,
        // venderName: [
        //   v => !!v || 'กรุณากรอก Vendor Name'
        // ]
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('checkpath')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.type_user === 'general_user') {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    this.getListPartner()
    this.getDetailShop()
    // this.getListVendor()
    // setTimeout(() => {
    //   this.ModalSettingPartnerSuccess = false
    // }, 2000)
    // this.getApi()
  },
  computed: {
    filterDateData () {
      if (this.DateToSearch !== '') {
        return this.DataTable.filter(element => {
          return element.created_at.substr(0, 10).includes(this.DateToSearch)
        })
      } else {
        return this.DataTable
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    widthScreen () {
      const widthToDisplay = window.innerWidth
      return widthToDisplay
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/partnerSellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/partnerSeller' }).catch(() => {})
      }
    },
    // descriptionPR (val) {
    //   // console.log('val---->', val)
    //   this.venderName = val
    //   var data = this.listVendor.find(item => item.vendor_name_th === this.venderName)
    //   // console.log('data', data)
    //   if (data !== undefined) {
    //     this.venderNo = data.vendor_code
    //   } else {
    //     this.$swal.fire({ icon: 'warning', text: 'Vendor Name ไม่พบในระบบ PR กรุณาเลือกใหม่อีกครั้ง', showConfirmButton: false, timer: 2500, toast: true })
    //     this.descriptionPR = ''
    //     this.venderNo = ''
    //   }
    // },
    // customerNo (val) {
    //   var customerName = val
    //   var data = this.listCustomer.find(item => item.CustomerName === customerName)
    //   // console.log('data', data)
    //   if (data !== undefined) {
    //     this.customerCode = data.CustomerCode
    //   } else {
    //     this.$swal.fire({ icon: 'warning', text: 'Customer Name ไม่พบในระบบ กรุณาเลือกใหม่อีกครั้ง', showConfirmButton: false, timer: 2500, toast: true })
    //     this.customerCode = ''
    //   }
    // },
    searchcompany (val) {
      if (val === '') {
        this.checkEnter = false
      }
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.partnerList.all !== undefined ? this.partnerList.all : []
        // this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.partnerList.active !== undefined ? this.partnerList.active : []
        // this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.partnerList.pending_request !== undefined ? this.partnerList.pending_request : []
        // this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.DataTable = this.partnerList.cancel !== undefined ? this.partnerList.cancel : []
        // this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      } else if (val === 4) {
        this.DataTable = this.partnerList.inactive !== undefined ? this.partnerList.inactive : []
        // this.keyCheckHead = 4
        if (this.DataTable.length === 0) {
          this.disableTable = false
          this.showCountOrder = 0
        } else {
          this.disableTable = true
        }
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    ChangeFilterDate (val) {
      this.$refs.dialog.save(val)
      this.DateSearch = this.formatDateToShow(val)
      this.DateToSearch = val
    },
    closeFilterDate () {
      this.modal = false
      this.DateSearch = ''
      this.DateToSearch = ''
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    openDialog () {
      this.email = ''
      this.tierID = ''
      this.creditTrem = ''
      this.numOfCreditTerm = ''
      this.credit = ''
      // this.venderName = ''
      this.ModalSearchCompany = !this.ModalSearchCompany
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async getListVendor (item) {
      // console.log('item', item)
      await this.$store.dispatch('actionsListVendorPR', item)
      var response = await this.$store.state.ModulePartner.stateListVendorPR
      if (response.message === 'Show item&vendor success') {
        if (response.data.vendor.length !== 0 && response.data.item.length !== 0) {
          this.listVendor = response.data.vendor
          // console.log('listVendor---------->', response.data.item[0].company_code)
          this.getCompanyCode = response.data.item[0].company_code
          // console.log(this.getCompanyCode)
          await this.getCustomerName()
        }
      }
    },
    async getListPartner () {
      this.countall = 0
      this.countActive = 0
      this.countcancel = 0
      this.countPending = 0
      this.countInActive = 0
      var data = {
        seller_shop_id: this.shopDetail.id
      }
      await this.$store.dispatch('actionsListPartner', data)
      var response = await this.$store.state.ModulePartner.stateListPartner
      // console.log('getListPartner------------->', response)
      // console.log(response.data)
      if (response.result === 'SUCCESS') {
        this.partnerList = response.data
        this.countall = this.partnerList.total_all
        this.countActive = this.partnerList.total_active
        this.countPending = this.partnerList.total_pending_request
        this.countcancel = this.partnerList.total_cancel
        this.countInActive = this.partnerList.total_inactive
        // console.log('partnerList----->', this.partnerList.all)
        if (this.StateStatus === 0) {
          this.DataTable = this.partnerList.all
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 1) {
          this.DataTable = this.partnerList.active
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 2) {
          this.DataTable = this.partnerList.pending_request
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 3) {
          this.DataTable = this.partnerList.cancel
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 4) {
          this.DataTable = this.partnerList.inactive
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
        // console.log('this.DataTable', this.DataTable)
        // for (const item of this.DataTable) {
        //   this.time = new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })
        //   console.log('this.time', this.time)
        // }
      }
    },
    getPartner (item) {
      this.StateStatus = item
      this.page = 1
    },
    countPartner (pagination) {
      this.showCountOrder = pagination.itemsLength
      // console.log('showCountOrder', this.showCountOrder)
      // if (this.pagination === '') {
      //   this.showCountOrder = 0
      // } else {
      //   this.showCountOrder = pagination.itemsLength
      // }
    },
    async getDetailShop () {
      var idshop = localStorage.getItem('shopSellerID')
      if (idshop === null) {
        this.$router.push({ path: '/' })
      }
      var data = {
        seller_shop_id: idshop,
        role: 'seller'
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        // console.log('-------->', response.data[0])
        this.isJV = response.data[0].is_JV
        this.shopDetail2 = response.data[0].tax_id
      }
      await this.getListVendor(this.shopDetail2)
    },
    async searchdata () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.dataSearch = []
      this.itemSelectTier = []
      var data = {
        seller_shop_id: this.shopDetail.id,
        search_company: this.searchcompany
      }
      this.timer = setTimeout(async () => {
        await this.$store.dispatch('actionsSearchCompany', data)
        var response = this.$store.state.ModulePartner.stateSearchCompany
        // console.log(response.data)
        if (response.result === 'SUCCESS') {
          // console.log([response.data.search_company])
          this.dataSearch = response.data.search_company
          this.itemSelectTier = response.data.tier_list
          this.showformSetting = false
          this.checkEnter = false
        } else {
          if (response.data.length === 0) {
            this.dataSearch = []
            this.showformSetting = false
            this.checkEnter = true
          }
        }
      }, 500)
    },
    // async checkCredit (val) {
    //   if (parseFloat(val) > parseFloat('2000000.00')) {
    //     await this.$swal.fire({ icon: 'warning', text: 'วงเงินไม่เกิน 2 ล้านบาท', showConfirmButton: false, timer: 2500, toast: true })
    //     this.credit = '2000000.00'
    //   }
    // },
    // async checkValueCredit (val) {
    //   if (parseFloat(val) > parseFloat('2000000.00')) {
    //     await this.$swal.fire({ icon: 'warning', text: 'วงเงินไม่เกิน 2 ล้านบาท', showConfirmButton: false, timer: 2500, toast: true })
    //     this.credit = '2000000.00'
    //   }
    // },
    // async checkNumCredit (val) {
    //   if (parseInt(val) > parseInt('24')) {
    //     await this.$swal.fire({ icon: 'warning', text: 'จำนวนงวดไม่เกิน 24 งวด', showConfirmButton: false, timer: 2500, toast: true })
    //     this.numOfCreditTerm = '24'
    //   }
    // },
    // async checkValueNumCredit (val) {
    //   if (parseInt(val) > parseInt('24')) {
    //     await this.$swal.fire({ icon: 'warning', text: 'จำนวนงวดไม่เกิน 24 งวด', showConfirmButton: false, timer: 2500, toast: true })
    //     this.numOfCreditTerm = '24'
    //   }
    // },
    // async checkNumCreditTerm (val) {
    //   if (parseInt(val) > parseInt('90')) {
    //     await this.$swal.fire({ icon: 'warning', text: 'จำนวนเครดิตเทอมไม่เกิน 90 วัน', showConfirmButton: false, timer: 2500, toast: true })
    //     this.creditTrem = '90'
    //   }
    // },
    // async checkValueNumCreditTerm (val) {
    //   if (parseInt(val) > parseInt('90')) {
    //     await this.$swal.fire({ icon: 'warning', text: 'จำนวนเครดิตเทอมไม่เกิน 90 วัน', showConfirmButton: false, timer: 2500, toast: true })
    //     this.creditTrem = '90'
    //   }
    // },
    checkShopOwn (item) {
      // if (item.own_business === 'no') {
      this.changeform(item)
      // } else {
      //   this.$swal.fire({ icon: 'error', text: 'ไม่สามารถเพิ่มบริษัทตัวเองเป็นคู่ค้าได้', showConfirmButton: false, timer: 1500 })
      // }
    },
    changeform (val) {
      // console.log('changeformVal------>', val)
      var detailPartner = this.DataTable.find(item => item.company_id === val.company_id)
      var venderNo = ''
      var customerNo = ''
      var data = this.partnerList.all.find(item => item.company_id === val.company_id)
      // console.log('this.customerCode', data)
      if (data === undefined) {
        this.venderNo = venderNo
        this.customerNo = customerNo
        this.dataform = val
        this.customerCode = val.company_code
        this.showformSetting = true
      } else if (data.status === 'inactive') {
        this.EditPartner(data, data.status)
      } else if (data.status === 'reject') {
        this.EditParterAfter(detailPartner, val)
        // console.log('reject', detailPartner)
        // this.ModalDetailPartner = !this.ModalDetailPartner
      } else if (data.status === 'active') {
        this.$swal.fire(
          {
            icon: 'warning',
            html: '<h3>บริษัทนี้ได้เป็นคู่ค้ากันแล้ว</h3>',
            showConfirmButton: false,
            timer: 1500,
            willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' }
          })
      } else if (data.status === 'request') {
        this.$swal.fire(
          {
            icon: 'warning',
            html: '<h3>บริษัทนี้กำลังขอคู่ค้า</h3>',
            showConfirmButton: false,
            timer: 1500,
            willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' }
          })
      }
      // {
      //   console.log('เป็นคู่ค้า', data)
      //   this.$swal.fire(
      //     {
      //       icon: 'warning',
      //       html: '<h3>บริษัทนี้ได้เป็นคู่ค้ากันแล้ว</h3>',
      //       showConfirmButton: false,
      //       timer: 1500
      //     })
      // }
    },
    async CreateUserCredit () {
      if (this.$refs.FormSetting.validate(true)) {
        var data = {
          seller_shop_id: this.shopDetail.id,
          business_id: this.dataform.business_id,
          email: this.email,
          vender_no: this.venderNo,
          customer_no: this.customerNo,
          // vender_name: this.venderName,
          company_id: this.dataform.company_id,
          customer_id: this.customerCode,
          tier_id: typeof this.tierID === 'object' ? this.tierID.tier_id : this.tierID,
          business_type: this.business_type,
          credit: this.credit,
          credit_term: parseInt(this.creditTrem),
          num_of_credit_term: this.numOfCreditTerm,
          type_credit_term: this.type_credit_term
        }
        // console.log(data)
        await this.$store.dispatch('actionsCreatePartner', data)
        var response = await this.$store.state.ModulePartner.stateCreatePartner
        if (response.result === 'SUCCESS') {
          this.$swal.fire({ icon: 'success', text: 'สร้างคู่ค้าสำเร็จ', showConfirmButton: false, timer: 2500, toast: true, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
          this.ModalSearchCompany = !this.ModalSearchCompany
          this.ModalCreatePartnerSuccess = !this.ModalCreatePartnerSuccess
          this.showformSetting = false
          this.searchcompany = ''
          this.dataSearch = []
          this.getListPartner()
        } else if (response.message === 'This company is already partner.') {
          this.$swal.fire({ icon: 'error', text: 'คุณเป็นคู่ค้ากับบริษัทนี้แล้ว', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        } else if (response.message === 'Please re-enable the partner company.') {
          this.$swal.fire({ icon: 'error', text: 'กรุณากดปุ่ม เปิดใช้งานร้านค้า ในรายละเอียดคู่ค้า เพื่อทำการเปิดคู่ค้าใหม่อีกครั้ง', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        } else if (response.message === 'Please wait for the request from the partner company again.') {
          this.$swal.fire({ icon: 'error', text: 'โปรดรอคำขอจากบริษัทอีกครั้ง', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        } else if (response.message === 'There is a request in the request list. Please check the list for approve.') {
          this.$swal.fire({ icon: 'error', text: 'มีคำขออยู่ในรายการคำขอแล้ว โปรดตรวจสอบรายชื่อเพื่ออนุมัติคู่ค้า', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        } else {
          this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    AddParnner () {
      this.ModalSearchCompany = !this.ModalSearchCompany
      this.ModalConfirmAddPartner = !this.ModalConfirmAddPartner
    },
    CloseModalSearch () {
      this.ModalSearchCompany = !this.ModalSearchCompany
      this.showformSetting = false
      // this.venderNo = ''
      this.descriptionPR = ''
      this.customerNo = ''
      this.searchcompany = ''
      this.dataSearch = []
    },
    CloseModalSettingPartnerCompany () {
      this.venderNo = ''
      this.customerNo = ''
      this.ModalSettingPartnerCompany = false
    },
    // async GetAllDetailPartner () {},
    async EditPartner (item, status) {
      this.detailPartner = []
      if (status === 'active' || status === 'inactive' || status === 'reject') {
        var dataDetail = {
          seller_shop_id: this.shopDetail.id.toString(),
          company_id: item.company_id.toString()
        }
        await this.$store.dispatch('actionsDetailPartner', dataDetail)
        var response = await this.$store.state.ModulePartner.stateDetailPartner
        if (response.result === 'SUCCESS') {
          this.detailPartner = await response.data
          this.listDocument = this.detailPartner.document_list === null ? 0 : this.detailPartner.document_list.length
          this.ModalDetailPartner = !this.ModalDetailPartner
        } else {
          this.detailPartner = []
        }
      } else if (status === 'request') {
        var dataDetailEdit = {
          seller_shop_id: this.shopDetail.id.toString(),
          setting_partner_id: item.setting_partner_id.toString()
        }
        await this.$store.dispatch('actionsDetailSettingPartner', dataDetailEdit)
        var responseEdit = await this.$store.state.ModulePartner.stateDetailSettingPartner
        // console.log('open dialog', responseEdit)
        if (responseEdit.result === 'SUCCESS') {
          this.detailPartner = responseEdit.data.partner_detail[0]
          this.detailListTier = responseEdit.data.tier_list
          this.listDocument = this.detailPartner.document_list === null ? 0 : this.detailPartner.document_list.length
          this.tierID = this.detailPartner.tier_id
          this.ModalApprovePartner = !this.ModalApprovePartner
        } else {
          this.detailPartner = []
        }
      }
    },
    NotApprove () {
      this.ModalApprovePartner = !this.ModalApprovePartner
      this.ModalNotApprove = !this.ModalNotApprove
    },
    async EditParterAfter (val, item) {
      var detailPartner = this.DataTable.find(item => item.company_id === val.company_id)
      var status = val.status
      var value = val
      if (status === 'reject') {
        this.dataForAccept = item
        await this.getListTier(value)
        this.dataForAccept.customer_id = this.dataForAccept.company_code
        this.venderNo = detailPartner.vender_no
        this.customerNo = detailPartner.customer_no
        this.email = this.dataForAccept.email
        this.ModalSettingPartnerCompanyAfter = !this.ModalSettingPartnerCompanyAfter
      } else {
        this.dataForAccept = value
        await this.getListTier(value)
        this.tierID = this.dataForAccept.tier_id
        this.descriptionPR = this.dataForAccept.vender_name
        this.customerNo = this.dataForAccept.customer_no
        this.email = this.dataForAccept.email
        this.ModalDetailPartner = !this.ModalDetailPartner
        this.ModalSettingPartnerCompanyAfter = !this.ModalSettingPartnerCompanyAfter
      }
    },
    async getCustomerName () {
      var data = {
        username: 'cusb2b',
        password: '0wpch7Tc8zXJIDY'
      }
      await this.$store.dispatch('actionsGetTokenCustomerName', data)
      var response = await this.$store.state.ModulePartner.stateGetTokenCustomerName
      if (response.message === 'success') {
        // console.log(response.access_token)
        const auth = {
          headers: { Authorization: `Bearer ${response.access_token}` }
        }
        var response1 = await this.axios.get(`http://***************:5012/api-bi/get_data?CompanyCode=${this.getCompanyCode}`, auth)
        if (response1.status === 'true') {
          // console.log(response1.data)
          this.listCustomer = response1.data
        }
      }
    },
    async getListTier (item) {
      var dataDetailEdit = {
        seller_shop_id: this.shopDetail.id.toString(),
        setting_partner_id: item.setting_partner_id.toString()
      }
      await this.$store.dispatch('actionsDetailSettingPartner', dataDetailEdit)
      var responseEdit = await this.$store.state.ModulePartner.stateDetailSettingPartner
      // console.log(responseEdit)
      if (responseEdit.result === 'SUCCESS') {
        this.detailPartner = responseEdit.data.partner_detail[0]
        this.detailListTier = responseEdit.data.tier_list
      } else {
        this.detailPartner = []
      }
    },
    CancelNotApprove () {
      this.reason = ''
      this.ModalNotApprove = !this.ModalNotApprove
    },
    CancelNotApproveAfter () {
      this.reason = ''
      this.ModalNotApproveAfter = !this.ModalNotApproveAfter
    },
    NotApproveWhenAccept (val) {
      this.reason = ''
      this.dataForAccept = val
      this.ModalDetailPartner = !this.ModalDetailPartner
      this.ModalNotApproveAfter = !this.ModalNotApproveAfter
    },
    CheckNotApprove () {
      this.ModalNotApprove = !this.ModalNotApprove
      this.ModalConfirmNotApprove = !this.ModalConfirmNotApprove
    },
    CheckNotApproveAfter () {
      this.ModalNotApproveAfter = !this.ModalNotApproveAfter
      this.ModalConfirmNotApproveAfter = !this.ModalConfirmNotApproveAfter
    },
    async ConfirmNotApprove () {
      this.ModalConfirmNotApprove = !this.ModalConfirmNotApprove
      var data = {
        seller_shop_id: this.shopDetail.id,
        company_id: this.detailPartner.company_id,
        business_id: this.detailPartner.business_id,
        customer_id: this.detailPartner.customer_id,
        tier_id: this.detailPartner.tier_id,
        credit: this.detailPartner.credit,
        credit_term: this.detailPartner.credit_term,
        num_of_credit_term: this.detailPartner.num_of_credit_term,
        type_credit_term: this.detailPartner.type_credit_term,
        status: 'reject',
        reason: this.reason
      }
      // console.log(data)
      await this.$store.dispatch('actionsSettingPartner', data)
      var responseSetting = await this.$store.state.ModulePartner.stateSettingPartner
      if (responseSetting.result === 'SUCCESS') {
        this.ModalSuccess = !this.ModalSuccess
        setTimeout(async () => {
          this.ModalSuccess = false
        }, 2000)
        this.getListPartner()
      } else {
        this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
      }
    },
    async ConfirmNotApproveAfter () {
      this.ModalConfirmNotApproveAfter = !this.ModalConfirmNotApproveAfter
      // console.log(this.dataForAccept)
      var data = {
        seller_shop_id: this.shopDetail.id,
        company_id: this.dataForAccept.company_id,
        business_id: this.dataForAccept.business_id,
        customer_id: this.dataForAccept.customer_id,
        vender_no: this.dataForAccept.venderNo,
        customer_no: this.dataForAccept.customerNo,
        // vender_name: this.dataForAccept.venderName,
        email: this.dataForAccept.email,
        tier_id: this.dataForAccept.tier_id,
        credit: this.dataForAccept.credit,
        credit_term: this.dataForAccept.credit_term,
        num_of_credit_term: this.dataForAccept.num_of_credit_term,
        type_credit_term: this.dataForAccept.type_credit_term,
        status: 'inactive',
        reason: this.reason
      }
      // console.log(data)
      await this.$store.dispatch('actionsSettingPartner', data)
      var responseSetting = await this.$store.state.ModulePartner.stateSettingPartner
      if (responseSetting.result === 'SUCCESS') {
        this.ModelCanclePartnerSuccess = true
        setTimeout(() => {
          this.ModelCanclePartnerSuccess = false
        }, 2000)
        this.getListPartner()
      } else {
        this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
      }
    },
    Approve () {
      this.ModalApprovePartner = !this.ModalApprovePartner
      this.customerCode = this.detailPartner.customer_id
      this.email = this.detailPartner.email
      // this.isJV = this.detailPartner.is_JV === 'yes'
      this.ModalSettingPartnerCompany = !this.ModalSettingPartnerCompany
      // console.log('detailpartner----------->', this.detailPartner)
    },
    async ConfrimApprove () {
      if (this.$refs.formSettingApprove.validate(true)) {
        var data = {
          seller_shop_id: this.shopDetail.id,
          company_id: this.detailPartner.company_id,
          vender_no: this.venderNo,
          customer_no: this.customerNo,
          // vender_name: this.venderName,
          business_id: this.detailPartner.business_id,
          customer_id: this.customerCode,
          tier_id: typeof this.tierID === 'object' ? this.tierID.tier_id : this.tierID,
          business_type: this.business_type,
          email: this.email,
          credit: this.credit,
          credit_term: this.creditTrem,
          num_of_credit_term: this.numOfCreditTerm,
          type_credit_term: this.type_credit_term,
          status: 'active',
          reason: ''
        }
        // console.log(data)
        await this.$store.dispatch('actionsSettingPartner', data)
        var responseSetting = await this.$store.state.ModulePartner.stateSettingPartner
        // console.log(responseSetting)
        if (responseSetting.result === 'SUCCESS') {
          this.ModalSettingPartnerCompany = !this.ModalSettingPartnerCompany
          this.detailAfterSuccess = responseSetting.data[0]
          this.ModalDetailAfterCreateSuccess = !this.ModalDetailAfterCreateSuccess
          this.$EventBus.$emit('getItemNoti')
          this.getListPartner()
        } else {
          this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    // 12123
    ConfirmActivePartner (val) {
      this.detailConfirmActivePartner = val
      this.ModalDetailPartner = !this.ModalDetailPartner
      this.ModalConfirmOpenPartner = !this.ModalConfirmOpenPartner
    },
    async ActivePartner (actionKey) {
      // console.log(val)
      this.$store.commit('openLoader')
      this.disableBeforeAPI = true
      var data = ''
      if (actionKey === 'Add') {
        data = {
          seller_shop_id: this.shopDetail.id,
          company_id: this.dataform.company_id,
          business_id: this.dataform.business_id,
          customer_id: this.customerCode,
          email: this.email,
          business_type: this.business_type,
          vender_name: this.venderName,
          vender_no: this.venderNo,
          customer_no: this.customerNo,
          tier_id: this.tierID,
          credit: this.credit,
          credit_term: parseInt(this.creditTrem),
          num_of_credit_term: this.numOfCreditTerm,
          type_credit_term: this.type_credit_term
          // status: 'active',
          // reason: ''
        }
        // console.log('dataAdd------->', data)
        await this.$store.dispatch('actionsCreatePartner', data)
        var response = await this.$store.state.ModulePartner.stateCreatePartner
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'success', text: 'สร้างคู่ค้าสำเร็จ', showConfirmButton: false, timer: 2500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
          this.disableBeforeAPI = false
          this.ModalSearchCompany = !this.ModalSearchCompany
          this.ModalCreatePartnerSuccess = !this.ModalCreatePartnerSuccess
          this.ModalConfirmAddPartner = false
          this.showformSetting = false
          this.searchcompany = ''
          this.dataSearch = []
          this.getListPartner()
        } else {
          this.$store.commit('closeLoader')
          this.disableBeforeAPI = false
          this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        }
      } else {
        data = {
          seller_shop_id: this.shopDetail.id,
          company_id: this.detailConfirmActivePartner.company_id,
          business_id: this.detailConfirmActivePartner.business_id,
          customer_id: this.detailConfirmActivePartner.customer_id,
          tier_id: this.detailConfirmActivePartner.tier_id,
          credit: this.detailConfirmActivePartner.credit,
          credit_term: this.detailConfirmActivePartner.credit_term,
          num_of_credit_term: this.detailConfirmActivePartner.num_of_credit_term,
          type_credit_term: this.detailConfirmActivePartner.type_credit_term,
          status: 'active',
          reason: ''
        }
        // console.log(data)
        await this.$store.dispatch('actionsSettingPartner', data)
        var responseSetting = await this.$store.state.ModulePartner.stateSettingPartner
        if (responseSetting.result === 'SUCCESS') {
          if (actionKey === 'AddAgain') {
            this.$store.commit('closeLoader')
            this.disableBeforeAPI = false
            this.ModalConfirmOpenPartner = !this.ModalConfirmOpenPartner
            this.$swal.fire({ icon: 'success', text: 'เปิดใช้งานคู่ค้า ', showConfirmButton: false, timer: 2500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
            this.getListPartner()
            // this.ModelAddPartnerSuccess = true
            // setTimeout(() => {
            //   this.ModelAddPartnerSuccess = false
            // }, 2000)
          } else {
            this.$store.commit('closeLoader')
            this.disableBeforeAPI = false
            this.ModalConfirmAddPartner = !this.ModalConfirmAddPartner
            this.ModalSearchCompany = !this.ModalSearchCompany
            this.ModelAddPartnerSuccess = true
            setTimeout(() => {
              this.ModelAddPartnerSuccess = false
            }, 2000)
          }
          this.$EventBus.$emit('getItemNoti')
          this.getListPartner()
        } else {
          this.$store.commit('closeLoader')
          this.disableBeforeAPI = false
          this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        }
      }
    },
    async ConfrimApproveAfter () {
      // this.$store.commit('openLoader')
      if (this.$refs.formSettingApproveAfter.validate(true)) {
        var data = {
          seller_shop_id: this.shopDetail.id,
          company_id: this.dataForAccept.company_id,
          business_id: this.dataForAccept.business_id,
          customer_id: this.dataForAccept.customer_id,
          email: this.email,
          business_type: this.business_type,
          vender_no: this.venderNo,
          vender_name: this.venderName,
          customer_no: this.customerNo,
          tier_id: typeof this.tierID === 'object' ? this.tierID.tier_id : this.tierID,
          credit: this.dataForAccept.credit,
          credit_term: this.dataForAccept.credit_term,
          num_of_credit_term: this.dataForAccept.num_of_credit_term,
          type_credit_term: this.dataForAccept.type_credit_term,
          status: 'active',
          reason: ''
        }
        // console.log('ConfrimApproveAfter------->', data)
        await this.$store.dispatch('actionsSettingPartner', data)
        var responseSetting = await this.$store.state.ModulePartner.stateSettingPartner
        // console.log('responseSetting', responseSetting)
        if (responseSetting.result === 'SUCCESS') {
          // this.$store.commit('closeLoader')
          this.ModalConfirmSettingPartnerCompanyAfter = !this.ModalConfirmSettingPartnerCompanyAfter
          this.ModalSettingPartnerCompanyAfter = !this.ModalSettingPartnerCompanyAfter
          this.ModalSettingPartnerSuccess = true
          setTimeout(() => {
            this.ModalSettingPartnerSuccess = false
          }, 2000)
          // this.$swal.fire({ icon: 'success', text: 'แก้ไขข้อมูลคู่ค้าสำเร็จ', showConfirmButton: false, timer: 3500 })
          this.detailAfterSuccess = responseSetting.data[0]
          this.$EventBus.$emit('getItemNoti')
          this.getListPartner()
        } else {
          this.$swal.fire({ icon: 'error', text: 'กรุณาตรวจสอบข้อมูลใหม่อีกรอบ', showConfirmButton: false, timer: 3500, willOpen: () => { document.querySelector('.swal2-container').style.zIndex = '16000010' } })
        }
      } else {
        this.$store.commit('closeLoader')
        this.ModalConfirmSettingPartnerCompanyAfter = !this.ModalConfirmSettingPartnerCompanyAfter
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    GotoPDF (val) {
      window.open(`${val.file_url}`)
    },
    getColor (item) {
      if (item === 'active') return '#F0F9EE'
      else if (item === 'request') return '#FCF0DA'
      else return '#F7D9D9'
    },
    getTextColor (item) {
      if (item === 'active') return '#1AB759'
      else if (item === 'request') return '#FAAD14'
      else return '#F5222D'
    },
    getStatus (item) {
      if (item === 'active') return 'อนุมัติ'
      else if (item === 'request') return 'รออนุมัติ'
      else if (item === 'reject') return 'ปฏิเสธ'
      else return 'ยกเลิก'
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: rgb(255, 255, 255);
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: rgb(255, 255, 255);
        }
      }
    }
  }
</style>
<style scoped>
/* custom scrollbar */
::-webkit-scrollbar {
  width: 16px;
}

::-webkit-scrollbar-track {
  background-color: #F3F5F7;
  border-radius: 29px;
}

::-webkit-scrollbar-thumb {
  background-color: #CCCCCC;
  border-radius: 20px;
  border: 6px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: #a8bbbf;
}
.changeBorderbottom /deep/ .ant-tabs-bar {
  margin: 0 0 16px 0;
  border-bottom: 2px solid #DAF1E9 !important;
  outline: none;
  transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.setBackground {
  background-image: url('../../../assets/ImageINET-Marketplace/Shop/BackgroundNew.png');
  object-fit: contain;
  width: 100%;
}
.setBackgroundMobile {
  background: url('../../../assets/ImageINET-Marketplace/Shop/BackgroundNewMobile.png') no-repeat;
  /* max-height: 100%;*/
  background-size: contain;
}
</style>
<style>
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
}
</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
