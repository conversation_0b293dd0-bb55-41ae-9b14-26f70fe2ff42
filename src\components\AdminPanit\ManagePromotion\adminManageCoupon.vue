<template>
  <v-container :class="MobileSize ? 'background_productMobile mt-3' : 'background_product pa-6'" style="background-color: #FFFFFF">
    <v-row>
      <v-col>
        <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoPage()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">จัดการคูปอง</span>
      </v-col>
      <v-col v-if="!MobileSize && !IpadSize" class="d-flex justify-end">
        <v-btn class="mr-2" style="border: 1px solid #27AB9C" outlined rounded @click="OpenDialogTemplateCoupon"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">เพิ่ม template</span></v-btn>
        <v-btn style="border: 1px solid #27AB9C" outlined rounded @click="dialogCoupon = true"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">เพิ่มคูปอง</span></v-btn>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize || IpadSize">
      <v-col cols="6">
        <v-btn class="mr-2" style="border: 1px solid #27AB9C; width: 100%;" outlined rounded @click="OpenDialogTemplateCoupon"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">เพิ่ม template</span></v-btn>
      </v-col>
      <v-col cols="6">
        <v-btn style="border: 1px solid #27AB9C; width: 100%;" outlined rounded @click="dialogCoupon = true"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">เพิ่มคูปอง</span></v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="12" md="6" sm="6">
        <v-text-field v-model="search" placeholder="ค้นหารายการโปรโมชัน" outlined dense hide-details @click="current = 1">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" md="4" sm="4">
        <v-select class="setCustomSelect vSelectLineHeight" v-model="selectType" :items="type" item-text="title" item-value="value" outlined dense hide-details @click="current = 1"></v-select>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <span class="pb-0" style="font-weight: 600; font-size:16px; line-height: 24px;">Template Coupon Shop</span>
      </v-col>
    </v-row>
    <v-card-title v-if="dataTemplateCoupon.length === 0" class="d-flex justify-center my-2" style="color: #333; font-size: large; font-weight: 600;">ไม่มี Template คูปองร้านค้า</v-card-title>
    <v-row v-if="dataTemplateCoupon.length !== 0" class="mb-2">
      <v-col cols="12" :style="MobileSize ? 'display: grid !important; justify-content: center;' : ''">
        <div :style="{ display: 'grid', gap: '16px', gridTemplateColumns: MobileSize ? 'repeat(2, 1fr)' : IpadSize ? 'repeat(2, 1fr)' : IpadProSize ? 'repeat(3, 1fr)' : PCSize ? 'repeat(5, 1fr)' : 'repeat(4, 1fr)' }">
          <div v-for="(item, index) in dataTemplateCoupon" :key="index">
            <v-card :class="MobileSize || IpadSize || IpadProSize ? 'ml-1' : ''" elevation="0" style="position: relative; overflow: hidden;">
              <img
                :src="require('../../../assets/ConponNGC/serviceCoupon/BgCoupon_dis.png')"
                alt="Coupon Background"
                style="width: 200px; height: auto; object-fit: cover; display: block;"
                :style="MobileSize ? 'width: 170px;' : IpadSize || IpadProSize ? 'width: 200px;' : PCSize ? 'width: 230px;' : 'width: 200px;'"
              />
              <div
              style="position: absolute; box-sizing: border-box; display: flex; flex-direction: column; justify-content: space-between; top: 10px; left: 10px; color: #269AFD;"
              :style="MobileSize ? 'width: 150px;' : IpadSize || IpadProSize ? 'width: 180px;' : PCSize ? 'width: 210px;' : 'width: 180px;'"
              >
                <div class="d-flex ml-1">
                  <div>
                    <span style="color: #269AFD; white-space: nowrap;" :style="MobileSize || IpadSize ? 'font-size: 14px;' : IpadProSize ? 'font-size: 16px;' : PCSize ? 'font-size: 18px;' : 'font-size: 16px;'"><b>{{ item.coupon_type === 'discount' ? 'โค้ดส่วนลด' : item.coupon_type === 'free_shipping' ? 'ค่าส่งฟรี' : item.coupon_type === 'free_product' ? '1 แถม 1' : '' }}</b></span><br>
                    <span v-if="item.coupon_type !== 'free_product'" :style="MobileSize || IpadSize ? 'font-size: 11px;' : IpadProSize ? 'font-size: 12px;' : PCSize ? 'font-size: 14px;' : 'font-size: 12px;'">ลด {{ item.discount_type === 'baht' ? item.discount_amount + ' บาท' : item.discount_type === 'percent' ? item.discount_amount + ' %' : '' }} </span>
                    <br v-else>
                  </div>
                  <div class="mt-n2 ml-auto mr-1">
                    <v-img :width="MobileSize ? '40' : IpadSize || IpadProSize ? '50' : '60'" :height="MobileSize ? '40' : IpadSize || IpadProSize ? '50' : '60'" contain :src="item.coupon_type === 'discount' ? require('../../../assets/ConponNGC/serviceCoupon/discount.png') : item.coupon_type === 'free_shipping' ? require('../../../assets/ConponNGC/serviceCoupon/shipping.png') : item.coupon_type === 'free_product' ? require('../../../assets/ConponNGC/serviceCoupon/free.png') : '' " />
                  </div>
                  <v-btn x-small icon @click="dialogConfirmDelete = true; TemplateCouponID = item.id;" class="mt-n2 mr-n2"><v-icon  small color="red">mdi-delete</v-icon></v-btn>
                </div>
                <div :class="MobileSize || IpadSize || IpadProSize ? 'mt-1' : ''" class="d-flex justify-center align-end">
                  <v-btn
                  disabled color="#F56E22" style="color: white;" :height="MobileSize ? '20' : IpadSize ? '24' : IpadProSize ? '26' : PCSize ? '32' : '28'" rounded
                  :style="MobileSize || IpadSize ? 'font-size: 11px;' : IpadProSize ? 'font-size: 12px;' : PCSize ? 'font-size: 14px;' : 'font-size: 12px;'"
                  >ใช้ template</v-btn>
                </div>
              </div>
            </v-card>
          </div>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <span class="pb-0" style="font-weight: 600; font-size:16px; line-height: 24px;">โปรโมชันทั้งหมด {{ sumCoupon }} รายการ</span>
      </v-col>
    </v-row>
    <v-row>
      <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" v-for="(items, index) in paginated" :key="index">
        <v-card outlined style="border-radius: 12px; box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px;" width="100%" height="100%">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
              <v-row dense class="pl-2" style="align-items: center;">
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">เปิด-ปิดใช้งานคูปอง</span>
                <v-switch hide-details color="#52C41A" @change="changeStatusCoupon(items.id)" v-model="items.show_status" true-value="active" false-value="inactive" dense inset class="pl-2"></v-switch>
              </v-row>
            <v-col>
              <span v-if="items.coupon_type !== '' && items.coupon_type === 'discount'" style="font-weight: 700; font-size: small; line-height: 22px; color: #333333;">ส่วนลด</span>
              <span v-else-if="items.coupon_type !== '' && items.coupon_type === 'free_shipping'" style="white-space: nowrap; font-weight: 700; font-size: small; line-height: 22px; color: #333333;">ฟรีค่าส่ง</span>
              <span v-else>-</span>
            </v-col>
            <v-menu bottom left :offset-y="true">
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#27AB9C" icon v-bind="attrs" v-on="on" >
                <v-icon>mdi-dots-vertical</v-icon>
              </v-btn>
            </template>
            <!-- List menu promotions -->
            <v-list style="border-radius: 1vw;">
              <v-list-item
                v-for="(item, i) in actionChoice"
                :key="i"
                @click="item.value === 'edit' ? gateOpen(items) : openDialogdel(items)"
                :disabled="item.value === 'delete' && items.can_delete === 'no'"
              >
                <v-list-item-icon class="mr-1">
                  <v-icon v-if="item.value === 'delete' && items.can_delete === 'no'" color='#a0a0a0'>{{ item.icon }}</v-icon>
                  <v-icon v-else color='#27AB9C'>{{ item.icon }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title>{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list>
          </v-menu>
          </v-app-bar>
          <v-divider style="margin-top: -.5vw;"></v-divider>
          <v-card-text :style="MobileSize ? 'margin-top: -1vw' : 'margin-bottom: 1vw; margin-top: .5vw;'">
            <v-row>
              <v-col cols="5" class="d-flex justify-center align-center">
                <v-avatar :size="MobileSize ? 90 : 100" tile v-if="items.coupon_image !== null" width="80"><img :src="items.coupon_image" alt="" style="width: 100%; height: 100%; object-fit: contain;"></v-avatar>
                <v-avatar :size="MobileSize ? 90 : 100" tile v-else width="80"><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/coupon_image/empty_coupon.png" alt=""></v-avatar>
              </v-col>
              <v-col cols="7" class="d-flex justify-center flex-column">
                <v-card-title class="px-1 pb-2 d-inline-block text-truncate">
                  <span :style="MobileSize ? 'font-weight: 700; font-size: medium;' : 'font-weight: 700; font-size: medium; line-height: 28px;'">
                    <v-tooltip top>
                      <template v-slot:activator="{ on, attrs }">
                        <span v-if="!IpadSize" v-bind="attrs" v-on="on"> {{ items.coupon_name }}</span>
                        <span v-else v-bind="attrs" v-on="on"> {{ items.coupon_name }}</span>
                      </template>
                      <span>{{ items.coupon_name }}</span>
                    </v-tooltip>
                  </span>
                </v-card-title>
                <v-card-subtitle class="px-1 pt-3 pb-0 d-inline-block text-truncate">
                  <span style=" font-size: small; line-height: 24px;">โค้ดคูปอง:</span>
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-if="!IpadSize" v-bind="attrs" v-on="on"> {{ items.coupon_code }}</span>
                      <span v-else v-bind="attrs" v-on="on"> {{ items.coupon_code }}</span>
                    </template>
                    <span>{{ items.coupon_code }}</span>
                  </v-tooltip>
                </v-card-subtitle>
                <v-card-subtitle class="px-1 pt-1 pb-0"><span><span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333; white-space: nowrap;">( {{ items.user_cap }} ครั้ง</span> / ผู้ใช้ )</span></v-card-subtitle>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text class="py-2" style="background-color: #d1edea;">
            <v-row>
              <v-col style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <span>ใช้ไปแล้ว</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ items.use_count }}</span>
              </v-col>
              <v-divider class="my-3" vertical style="border: 1px solid #FFFFFF; height: 50px;" />
              <v-col style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
                <span>คงเหลือ</span>
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ items.quota - items.use_count }}</span>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-if="paginated.length > 0">
      <v-pagination color="#27AB9C" v-model="pageNumber" :length="checkPagination()" :total-visible="10" ></v-pagination>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-else>
      <span>ไม่พบโปรโมชัน</span>
    </v-row>
    <v-dialog v-model="dialogCoupon" width="500px" round>
      <v-card style="max-width: 500px; border-radius: 1vw;">
        <v-toolbar height="50px" style="background-color: #27AB9C;">
          <v-row dense class="ma-2">
            <v-col style="display: flex; align-items: center; justify-content: center;">
              <span style="font-size: 20px; font-weight: 700; color: #fff">เพิ่มคูปอง</span>
            </v-col>
          </v-row>
        </v-toolbar>
        <v-col>
          <v-row class="pa-3">
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" :width="MobileSize ? 140 : 185" elevation="0" @click="openPath('discount')">
                <v-col>
                  <v-row>
                    <v-img v-if="!MobileSize" src="@/assets/coupon_image/discount.png" height="140" width="125"></v-img>
                    <img v-else src="@/assets/coupon_image/discount.png" width="100">
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      ส่วนลด
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
            <v-col cols="6" style="display: flex; align-items: center; justify-content: center;">
              <v-btn height="185" :width="MobileSize ? 140 : 185" elevation="0" @click="openPath('freeshippng')">
                <v-col>
                  <v-row>
                    <v-img v-if="!MobileSize" src="@/assets/coupon_image/shipping.png" height="140" width="125"></v-img>
                    <img v-else src="@/assets/coupon_image/shipping.png" width="100">
                  </v-row>
                  <v-row style="justify-content: center;">
                    <span class="pt-2">
                      ฟรีค่าจัดส่ง
                    </span>
                  </v-row>
                </v-col>
              </v-btn>
            </v-col>
          </v-row>
        </v-col>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDelCoupon" width="424">
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 22px; font-weight: 700;">ลบโปรโมชัน</span>
          </v-row>
          <v-btn fab small @click="closeDialogDel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <div class="d-flex flex-column align-center" style="gap: 1.5vw;">
              <v-img src="@/assets/ConponNGC/serviceCoupon/deleteCoupon.png" height="140" width="250"></v-img>
              <span style="font-size: medium; font-weight: 600;">ต้องการลบโปรโมชัน " {{ itemForDelName }} " หรือไม่</span>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-row>
              <v-col style=" display: flex; justify-content: center; align-items: center;">
                <v-btn rounded color="#27AB9C" height="40" width="120" outlined @click="closeDialogDel()" elevation="0">ยกเลิก</v-btn>
              </v-col>
              <v-col style=" display: flex; justify-content: center; align-items: center;">
                <v-btn rounded color="#27AB9C" height="40" width="120" style="color: #FFFFFF" @click="deleteCoupon()" elevation="0">ยืนยัน</v-btn>
              </v-col>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Dialog ลบ Template -->
    <v-dialog v-model="dialogConfirmDelete" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการทำรายการ
          </span>
           <v-btn icon dark @click="dialogConfirmDelete = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: medium; font-weight: 600; line-height: 3;">คุณต้องการลบ Template คูปองนี้</span><br>
              <span style="font-size: medium; font-weight: 600;">ใช่หรือไม่</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogConfirmDelete = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click=" deleteTemplateCoupon(TemplateCouponID)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <TemplateCoupon ref="TemplateCoupon" />
  </v-container>
</template>

<script>
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: {
    TemplateCoupon: () => import(/* webpackPrefetch: true */ '@/components/Shop/Voucher/TemplateCoupon')
  },
  data () {
    return {
      seller_shop_id: '',
      pageMax: null,
      current: 1,
      pageSize: 6,
      search: '',
      countPromotion: 0,
      dialogCoupon: false,
      type:
      [
        { title: 'ทั้งหมด', value: 'all' },
        { title: 'โค้ดส่วนลด', value: 'discount' },
        { title: 'ฟรีค่าจัดส่ง', value: 'free_shipping' }
      ],
      actionChoice: [
        { title: 'แก้ไขโปรโมชัน', value: 'edit', icon: 'mdi-pencil', link: '' },
        { title: 'ลบโปรโมชัน', value: 'delete', icon: 'mdi-delete', link: '' }
      ],
      selectType: 'all',
      detailCoupon: [],
      itemForDel: null,
      itemForDelName: null,
      dialogDelCoupon: false,
      dataTemplateCoupon: [],
      dialogConfirmDelete: false,
      TemplateCouponID: null
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value !== null) {
        if (value.length > limit) {
          value = value.substring(0, (limit - 4)) + '...'
        }
        return value
      }
    }
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.search === '' && this.selectType === 'all') {
        return this.detailCoupon.slice(this.indexStart, this.indexEnd)
      } else {
        var items = this.detailCoupon.filter(e => {
          if (this.search !== '' && this.selectType === 'all') {
            return (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
          } else if (this.search === '' && this.selectType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
          } else if (this.search !== '' && this.selectType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
          }
        })
        return items.slice(this.indexStart, this.indexEnd)
      }
    },
    sumCoupon () {
      if (this.search === '' && this.selectType === 'all') {
        return this.detailCoupon.length
      } else {
        var items = this.detailCoupon.filter(e => {
          if (this.search !== '' && this.selectType === 'all') {
            return (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
          } else if (this.search === '' && this.selectType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
          } else if (this.search !== '' && this.selectType !== 'all') {
            return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
          }
        })
        return items.length
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminManageCouponMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/adminManageCoupon' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('CheckShop')
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    this.getListCoupon()
    this.getDataTemplateCoupon()
    this.$EventBus.$on('getDataTemplateCoupon', this.getDataTemplateCoupon)
  },
  methods: {
    closeDialogDel () {
      this.itemForDel = null
      this.itemForDelName = null
      this.dialogDelCoupon = false
    },
    openDialogdel (item) {
      this.itemForDel = item
      this.itemForDelName = item.coupon_name
      this.dialogDelCoupon = true
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    gateOpen (item) {
      if (item.coupon_type === 'discount') {
        if (this.MobileSize) {
          this.$router.push({ path: `/editDiscountCouponMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/editDiscountCoupon?status=edit&id=${item.id}` }).catch(() => {})
        }
      } else if (item.coupon_type === 'free_shipping') {
        if (this.MobileSize) {
          this.$router.push({ path: `/editFreeShippingCouponMobile?status=edit&id=${item.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/editFreeShippingCoupon?status=edit&id=${item.id}` }).catch(() => {})
        }
      }
    },
    openPath (path) {
      if (path === 'discount') {
        if (this.MobileSize) {
          this.$router.push({ path: '/discountCouponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/discountCoupon' }).catch(() => {})
        }
      } else if (path === 'freeshippng') {
        if (this.MobileSize) {
          this.$router.push({ path: '/freeShippingCouponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/freeShippingCoupon' }).catch(() => {})
        }
      }
    },
    async getListCoupon () {
      this.$store.commit('openLoader')
      var data = {
        status: null
      }
      await this.$store.dispatch('actionsGetListCoupon', data)
      const respons = await this.$store.state.ModuleAdminManage.stateGetListCoupon
      if (respons.code === 200) {
        this.detailCoupon = respons.data.coupon
        this.pageMax = parseInt(this.detailCoupon.length / 6) === 0 ? 1 : Math.ceil(this.detailCoupon.length / 6)
        this.$store.commit('closeLoader')
      } else {
        this.detailCoupon = []
        this.$store.commit('closeLoader')
      }
    },
    async changeStatusCoupon (id) {
      this.$store.commit('openLoader')
      var data = { coupon_id: id }
      await this.$store.dispatch('actionsChangeStatusCoupon', data)
      var respons = this.$store.state.ModuleAdminManage.stateChangeStatusCoupon
      if (respons.code === 200) {
        this.getListCoupon()
        this.$store.commit('closeLoader')
      }
    },
    async deleteCoupon () {
      var data = {
        coupon_id: this.itemForDel.id
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsDeleteCoupon', data)
      var respons = this.$store.state.ModuleAdminManage.stateDeleteCoupon
      if (respons.code === 200) {
        this.dialogDelCoupon = false
        this.$store.commit('closeLoader')
        this.getListCoupon()
      }
    },
    checkPagination () {
      var max = null
      var items = this.detailCoupon.filter(e => {
        if (this.search !== '' && this.selectType === 'all') {
          return (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
        } else if (this.search === '' && this.selectType !== 'all') {
          return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
        } else if (this.search !== '' && this.selectType !== 'all') {
          return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
        } else {
          return this.detailCoupon
        }
      })
      this.countPromotion = items.length
      max = Math.ceil(items.length / this.pageSize)
      this.pageNumber = this.pageNumber > max ? max : this.pageNumber
      return max
    },
    async getDataTemplateCoupon () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetDataTemplateCoupon')
      var res = this.$store.state.ModuleAdminManage.stateGetDataTemplateCoupon
      // console.log('res', res)
      this.$store.commit('closeLoader')
      if (res.code === 200) {
        this.dataTemplateCoupon = res.data
      }
    },
    OpenDialogTemplateCoupon () {
      this.$refs.TemplateCoupon.open('created')
    },
    async deleteTemplateCoupon (id) {
      var data = {
        id: id
      }
      await this.$store.dispatch('actionsDeleteDataTemplateCoupon', data)
      var res = this.$store.state.ModuleAdminManage.stateDeleteDataTemplateCoupon
      this.$store.commit('closeLoader')
      if (res.code === 200) {
        this.getDataTemplateCoupon()
        this.TemplateCouponID = null
        this.dialogConfirmDelete = false
        this.$swal.fire({ showConfirmButton: false, timer: 1500, text: 'ลบ Template สำเร็จ', timerProgressBar: true, icon: 'success' })
      } else {
        this.TemplateCouponID = null
        this.dialogConfirmDelete = false
        this.$swal.fire({ showConfirmButton: false, timer: 1500, text: 'กรุณาลองใหม่อีกครั้ง', timerProgressBar: true, icon: 'error' })
      }
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
/* style btn */
.format-btn {
  border-radius: 1.5vw;
  color: #fff;
  font-size: medium;
}
@media screen and (min-width: 320px) and (max-width: 915px) {
  .v-image__image--contain {
    width: 27vw;
    margin-left: 3vw;
  }
}
</style>
