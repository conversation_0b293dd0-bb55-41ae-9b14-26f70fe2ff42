<template>
  <div>
    <v-row no-gutters>
      <v-col cols="12" class="py-0 pr-2">
        <a-tabs @change="changeReviewProduct">
          <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countall }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="1"><span slot="tab">ยังไม่อนุมัติ <a-tag color="#FAAD14" style="border-radius: 8px;">{{ countPending }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countApprove }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="3"><span slot="tab">ซ่อน <a-tag color="#F5222D" style="border-radius: 8px;">{{ countHide }}</a-tag></span></a-tab-pane>
        </a-tabs>
      </v-col>
      <v-col cols="12" md="6">
        <v-text-field
         v-model="search"
         dense
         outlined
         placeholder="ค้นหาจากชื่อสินค้า"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" class="pl-0">
        <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="keyCheckHead === 0">รายการทั้งหมด {{ countall === 0 ? 0 : showCountOrder }} รายการ</span>
        <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 1">รายการยังไม่อนุมัติทั้งหมด {{ countPending === 0 ? 0 : showCountOrder }} รายการ</span>
        <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 2">รายการอนุมัติทั้งหมด {{ countApprove === 0 ? 0 : showCountOrder }} รายการ</span>
        <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 3">รายการซ่อนทั้งหมด {{ countHide === 0 ? 0 : showCountOrder }} รายการ</span>
      </v-col>
    </v-row>
    <v-row v-if="disableTable === true">
      <v-col cols="12" md="12">
        <v-card outlined class="mb-4">
          <v-data-table
           :headers="keyCheckHead == 0 ? headers : keyCheckHead == 1 ? headersPending : keyCheckHead == 2 ? headersApprove : headersHidden"
           :items="DataTable"
           style="width:100%;"
           height="100%"
           @pagination="countReview"
           :search="search"
           no-results-text="ไม่พบรายการสินค้าที่ค้นหา"
           no-data-text="ไม่มีรายการสินค้าในตาราง"
           :page.sync="page"
           class=""
           :items-per-page="10"
           :footer-props="{'items-per-page-text':'จำนวนแถว'}"
           :update:items-per-page="getItemPerPage"
          >
            <template v-slot:[`item.product_image`]="{ item }">
              <v-row justify="center" class="my-4 mx-4">
                <v-img width="100" height="100" src="@/assets/NoImage.png" v-if="item.product_image === ''" contain></v-img>
                <v-img width="100" height="100" :src="`${item.product_image}`" v-else contain></v-img>
              </v-row>
            </template>
            <template v-slot:[`item.product_name`]="{ item }" v-if="MobileSize">
              <v-row justify="end" class="my-4 mx-0">
                <span v-snip="1" style="width: 200px;">{{ item.product_name }}</span>
              </v-row>
            </template>
            <template v-slot:[`item.last_comment`]="{ item }">
              <v-row justify="start" class="py-2">
                <span :style="{'padding-right': MobileSize ? '10px' : '' }">{{item.comment.comment|truncate(35, '...')}}</span>
              </v-row>
              <v-row justify="start"  class="py-2" v-if="!MobileSize">
                <span class="">
                  <v-rating
                    color="#FB9300"
                    background-color="#C4C4C4"
                    empty-icon="$ratingFull"
                    dense
                    half-increments
                    hover
                    size="17"
                    :value="item.comment.stars"
                    readonly
                  ></v-rating>
                </span>
                <span class="pl-2 " style="margin-bottom:5px;"  v-if="!MobileSize">{{item.comment.stars + "/5"}} </span>
                <span class="pl-2"  v-if="!MobileSize">{{item.comment.total_comment_product}} ความคิดเห็น </span>
              </v-row>
              <v-row justify="start" class="pt-2"  v-if="!MobileSize">
                <span ><v-icon color="#A1A1A1" small>mdi-account</v-icon> {{item.comment.created_by}}</span>
              </v-row>
            </template>
            <template v-slot:[`item.company_name_th`]="{ item }">
              <span>{{ item.company_name_th !== null ? item.company_name_th : '-' }}</span>
            </template>
            <template v-slot:[`item.status_comment`]="{ item }">
              <span v-if="item.status_comment === 'inactive'">
                <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#FAAD14">ยังไม่อนุมัติ</v-chip>
              </span>
              <span v-else-if="item.status_comment === 'active'">
                <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
              </span>
              <span v-else>
                <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#E9A016">ซ่อน</v-chip>
              </span>
            </template>
            <template v-slot:[`item.date`]="{ item }">
              <span>{{new Date(item.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })}} </span>
            </template>
            <template v-slot:[`item.edit`]="{ item }">
              <v-row dense v-if="!MobileSize">
                <v-btn
                  x-small
                  outlined
                  style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px; max-width: 32px; max-height: 32px;"
                  @click="Edit(item)"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#27AB9C" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
              <v-row dense v-else>
                <v-btn
                  text
                  style="color: #27AB9C;"
                  @click="Edit(item)"
                  class="pt-4 pb-4 px-0"
                >
                  ดูรายละเอียด
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.view_comment`]="{ item }">
              <v-row dense v-if="!MobileSize">
                <v-btn
                  x-small
                  outlined
                  style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px; max-width: 32px; max-height: 32px;"
                  @click="ViewDialogApprove(item)"
                  class="pt-4 pb-4"
                >
                  <v-icon color="#27AB9C" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
              <v-row dense v-else>
                <v-btn
                  text
                  style="color: #27AB9C;"
                  @click="ViewDialogApprove(item)"
                  class="pt-4 pb-4 px-0"
                >
                  ดูรายละเอียด
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.hidden_comment`]="{ item }">
              <v-row dense v-if="!MobileSize">
                <v-icon color="red" @click="confirmChangeStatus('hidden', item.product_feedback_id)">mdi-eye-off</v-icon>
              </v-row>
              <v-row dense v-else>
                <v-btn
                  text
                  style="color: red;"
                  @click="confirmChangeStatus('hidden', item.product_feedback_id)"
                  class="pt-4 pb-4 px-0"
                >
                  ซ่อน
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.show_comment`]="{ item }">
              <v-row dense v-if="!MobileSize">
                <v-icon color="#27AB9C" @click="confirmChangeStatus('show', item.product_feedback_id)">mdi-eye</v-icon>
              </v-row>
              <v-row dense v-else>
                <v-btn
                  text
                  style="color: #27AB9C;"
                  @click="confirmChangeStatus('show', item.product_feedback_id)"
                  class="pt-4 pb-4 px-0"
                >
                  แสดง
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-else justify="center" align-content="center">
      <v-col cols="12" align="center">
        <div class="my-5">
          <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
        </div>
        <h2 v-if="StateStatus === 0" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการความคิดเห็น</b></h2>
        <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการความคิดเห็นที่{{ StateStatus === 1 ? 'ยังไม่อนุมัติ' : StateStatus === 2 ? 'อนุมัติแล้ว' : 'ซ่อน'}}</b></h2>
      </v-col>
    </v-row>
    <v-dialog
     v-model="dialogcomment"
     :width="MobileSize ? '100%' : '992px'"
     height="100%"
     persistent
     :style="MobileSize ? 'z-index: 16000004' : ''"
    >
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar elevation="0">
          <v-container>
            <v-row justify="start" align-content="center" class="mt-2" :class="MobileSize ? 'px-0' : ''">
              <v-col cols="1">
                  <v-img src="@/assets/ImageINET-Marketplace/Shop/ReviewDetail/chat.png" contain max-width="40" max-height="40"  />
              </v-col>
              <v-col cols="10" class="mt-2" v-if="!MobileSize">
                <span  style="color: #27AB9C; font-size: 20px; font-weight: Bold;">รายการประเมินความพึงพอใจสินค้าทั้งหมด</span>
              </v-col>
              <v-col cols="10" class="mt-2" v-else>
                <span  style="color: #27AB9C; font-size: 18px; font-weight: Bold;">รายการประเมินความพึงพอใจสินค้าทั้งหมด</span>
              </v-col>
            </v-row>
          </v-container>
          <v-btn fab small @click="CloseDialog()" icon class="mr-2 mt-6"><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs :class="MobileSize ? 'px-4' : 'px-8'">
          <v-divider></v-divider>
        </v-container>
        <v-container grid-list-xs :class="MobileSize ? 'px-4' : 'px-8'">
          <v-card outlined class="mb-3" v-for="(item, index) in dataAllReview" :key="index">
            <v-card-text>
              <!-- Web, Ipad -->
              <v-row dense v-if="!MobileSize">
                <v-col cols="1" sm="2">
                  <v-avatar size="60" style="border: 1px solid #FFFFFF;">
                    <v-img :src="item.user_image" v-if="item.user_image !== '' && item.user_image !== undefined" width="60" height="60" contain></v-img>
                    <v-icon style="width: 38px; height: 38px;" size="38" color="#333333" large v-else>mdi-account-circle-outline</v-icon>
                  </v-avatar>
                </v-col>
                <v-col cols="11" sm="10">
                  <v-row dense>
                    <v-col cols="7">
                      <span style="font-weight: 400; font-size: 18px; line-height: 26px; color: #333333;">{{ item.created_by }} <span v-if="item.company_name_th !== null">, <b>{{ item.company_name_th }}</b></span></span><br/>
                      <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363; margin-top: 7px;">{{ item.product_attribute }}</p>
                      <v-row dense>
                        <v-rating color="#FFCB49" background-color="#C4C4C4" empty-icon="$ratingFull" dense half-increments hover size="20" :value="item.stars" readonly></v-rating>
                        <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #333333;" class="ml-2 mt-1">{{ item.stars }}/5</span>
                      </v-row>
                    </v-col>
                    <v-col cols="5" align="end">
                      <span v-if="item.status_comment === 'inactive'">
                        <v-chip small class="mb-2" color="#FCF0DA" text-color="#FAAD14">ยังไม่อนุมัติ</v-chip><br/>
                      </span>
                      <span v-else-if="item.status_comment === 'active'">
                        <v-chip small class="mb-2" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip><br/>
                      </span>
                      <span v-else>
                        <v-chip small class="mb-2" color="#FCF0DA" text-color="#E9A016">ซ่อน</v-chip><br/>
                      </span>
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; #636363">ประเมินความพึงพอใจเมื่อวันที่ {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                    </v-col>
                  </v-row>
                  <v-row dense class="mt-4 pl-1">
                    <v-col cols="12">
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">{{ item.comment }}</span>
                    </v-col>
                  </v-row>
                  <v-row dense v-if="item.image_upload.length !== 0 || item.video_upload.length !== 0" class="mt-6">
                    <div v-if="item.image_upload.length !== 0" style="display: contents;">
                      <v-col cols="12" md="2" sm="4" xs="6" v-for="(items, index) in item.image_upload" :key="index">
                        <v-card width="100" height="100" outlined>
                          <v-card-text>
                            <v-img :src="items.image_path" contain max-height="84" max-width="84"></v-img>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </div>
                    <div v-if="item.video_upload.length !== 0" style="display: flex;">
                      <v-col cols="12" v-for="(items, index1) in item.video_upload" :key="index1">
                        <v-card width="100px" height="100px" outlined style="text-align: center;">
                          <v-card-text class="pa-0">
                            <video width="95" height="95" muted controls>
                              <source :src="items.image_path" type="video/mp4">
                            </video>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </div>
                  </v-row>
                  <v-row dense class="mt-6 d-flex align-center">
                    <span><v-icon color="#A1A1A1">mdi-calendar-month</v-icon>แก้ไขล่าสุดเมื่อ {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}</span>
                    <span><v-icon color="#A1A1A1" class="ml-4">mdi-clock-time-four-outline</v-icon> {{ item.time }} </span>
                    <v-spacer></v-spacer>
                    <v-btn v-if="item.reply_comment_review.length === 0" color="#27ab9c" class="white--text" @click="openModalReplyComment(item)"><span>ตอบกลับความคิดเห็น</span></v-btn>
                  </v-row>
                  <v-row v-if="item.reply_comment_review.length !== 0">
                    <v-col cols="12" class="d-flex align-center">
                      <v-avatar size="38" style="border: 1px solid #FFFFFF;">
                        <v-img v-lazyload :src="item.reply_comment_review.user_image" v-if="item.reply_comment_review.user_image !== undefined" width="38" height="38" contain></v-img>
                        <v-icon style="width: 38px; height: 38px;" size="38" color="#333333" large v-else>mdi-account-circle-outline</v-icon>
                      </v-avatar>
                      <span class="pl-3" style="font-size: 16px; font-weight: bold;">ข้อความตอบกลับ</span>
                      <v-spacer></v-spacer>
                      <v-btn v-if="item.reply_comment_review[0].have_edit === '0'" color="#27ab9c" @click="openModalReplyCommentEdit(item)" class="white--text">แก้ไขการตอบกลับ</v-btn>
                    </v-col>
                    <v-col cols="12">
                      <span class="pl-13" style="font-size: 16px;">{{item.reply_comment_review[0].reply_comment}}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
              <!-- Mobile -->
              <v-row dense v-else>
                <v-col cols="12" align="end">
                  <span v-if="item.status_comment === 'inactive'">
                    <v-chip small class="mb-2" color="#FCF0DA" text-color="#FAAD14">ยังไม่อนุมัติ</v-chip><br/>
                  </span>
                  <span v-else-if="item.status_comment === 'active'">
                    <v-chip small class="mb-2" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip><br/>
                  </span>
                  <span v-else>
                    <v-chip small class="mb-2" color="#FCF0DA" text-color="#E9A016">ซ่อน</v-chip><br/>
                  </span>
                  <!-- <span  style="font-weight: 400; font-size: 14px; line-height: 22px; #636363">ประเมินความพึงพอใจเมื่อวันที่ {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span> -->
                </v-col>
                <v-col cols="12">
                  <v-row dense>
                    <v-col cols="2">
                      <v-avatar size="40" style="border: 1px solid #FFFFFF;">
                        <v-img :src="item.user_image" v-if="item.user_image !== '' && item.user_image !== undefined" width="40" height="40" contain></v-img>
                        <v-icon style="width: 38px; height: 38px;" size="38" color="#333333" large v-else>mdi-account-circle-outline</v-icon>
                      </v-avatar>
                    </v-col>
                    <v-col cols="10">
                      <span style="font-weight: 400; font-size: 16px; line-height: 26px; color: #333333;">{{ item.created_by }}<span v-if="item.company_name_th !== '' && item.company_name_th !== null">,</span> <b>{{ item.company_name_th }}</b></span><br/>
                      <span style="font-weight: 400; font-size: 10px; line-height: 14px; #636363">{{ item.product_attribute }}</span>
                      <v-row dense class="pt-1">
                        <v-rating color="#FFCB49" background-color="#C4C4C4" empty-icon="$ratingFull" dense half-increments hover size="20" :value="item.stars" readonly></v-rating>
                        <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #333333;" class="ml-2 mt-1">{{ item.stars }}/5</span>
                      </v-row>
                      <v-row dense class="pt-1">
                        <span  style="font-weight: 400; font-size: 10px; line-height: 14px; #636363">ประเมินความพึงพอใจเมื่อวันที่ {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                      </v-row>
                      <v-row dense class="mt-4 pl-1">
                        <v-col cols="12">
                          <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #000000;">{{ item.comment }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense v-if="item.image_upload.length !== 0 || item.video_upload.length !== 0" class="mt-6">
                        <div v-if="item.image_upload.length !== 0" style="display: contents;" class="pa-1">
                          <v-col cols="6" md="2" sm="4" v-for="(items, index) in item.image_upload" :key="index" class="pa-1">
                            <v-card width="100" height="100" outlined>
                              <v-card-text>
                                <v-img :src="items.image_path" contain max-height="84" max-width="84"></v-img>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </div>
                        <div v-if="item.video_upload.length !== 0" style="display: flex;">
                          <v-col cols="12" v-for="(items, index1) in item.video_upload" :key="index1" class="pa-1">
                            <v-card width="100" height="100" outlined style="text-align: center;">
                              <v-card-text class="pa-0">
                                <video width="95" height="95" muted controls>
                                  <source :src="items.image_path" type="video/mp4">
                                </video>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </div>
                      </v-row>
                      <v-row dense class="mt-6">
                        <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #333333;"><v-icon color="#A1A1A1">mdi-calendar-month</v-icon> แก้ไขล่าสุดเมื่อ {{ new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}</span><br/>
                        <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #333333;"><v-icon color="#A1A1A1" class="ml-0">mdi-clock-time-four-outline</v-icon> {{ item.time }} </span>
                        <v-spacer></v-spacer>
                        <v-btn color="#27ab9c" class="white--text" @click="openModalReplyComment(item)"><span>ตอบกลับความคิดเห็น</span></v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-row v-if="item.reply_comment_review.length !== 0">
                    <v-col>
                      <span>ข้อความตอบกลับ</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
            <!-- {{dataAllReview}} -->
            <!-- <v-layout row>
              <v-flex md1>
                <v-row justify="center" class="ma-3 mt-5">
                  <v-avatar size="60" >
                    <v-img  v-if="!item.user_image" src="@/assets/noprofile.png" class="pointPic" contain></v-img>
                    <v-img  v-else :src="item.user_image" />
                  </v-avatar>
                  <v-avatar size="60" >
                    <v-img src="@/assets/noprofile.png" class="pointPic" contain></v-img>
                  </v-avatar>
                </v-row>
              </v-flex>
              <v-flex md10>
                <div class="mt-2 pa-0">
                  <v-card-title>{{ dataAllReview.created_by }}</v-card-title>
                  <v-row class="ml-3">
                    <div class="ml-3">
                    <v-rating
                      color="#FB9300"
                      background-color="#C4C4C4"
                      empty-icon="$ratingFull"
                      dense
                      half-increments
                      hover
                      size="17"
                      :value="dataAllReview.stars"
                      readonly
                    ></v-rating>
                    <span class="ml-2">{{dataAllReview.stars + "/5"}} </span>
                  </div>
                  </v-row>
                  <v-card-subtitle>
                    {{ dataAllReview.comment }}
                  </v-card-subtitle>
                  <v-row>
                    <v-col cols="12">
                      <v-row dense class="pa-4">
                        <v-card class="mr-2"  v-if="item.video_upload.length !== 0" @click="ShowPicReview(item.video_upload[0].image_path, 'video', index)" width="120" height="120">
                          <v-icon class="video-icon" style="position: absolute;">mdi-video-box</v-icon>
                            <v-btn icon x-small style="float: right; background-color: #ff5252;">
                            <v-icon x-small color="white" dark @click="RemoveImageComment(item.video_upload[0].id,item.id)">mdi-close</v-icon>
                          </v-btn>
                          <video autoplay loop muted playsinline width="118" height="85"  poster><source :src="item.video_upload.length === 0 ? null:item.video_upload[0].image_path" type="video/mp4"></video>
                         </v-card>
                        <v-card v-for="(items, index2) in dataAllReview.comment_image"
                          :key="index2" outlined class="mr-2" width="120" height="120"
                          >
                          <v-btn icon x-small style="float: right; background-color: #ff5252;">
                            <v-icon x-small color="white" dark @click="RemoveImageComment(items.id,item.id)">mdi-close</v-icon>
                          </v-btn>
                          <v-img
                            :src="items.image_path"
                            :lazy-src="items.image_path"
                            width="118"
                            height="85"
                            style="cursor: pointer;"
                            contain
                            @click="ShowPicReview(items.image_path, 'image', index)">
                          </v-img>
                        </v-card>
                      </v-row>
                      <v-row justify="end" class="mr-16" v-if="showReview !== '' && SelectPicReview === index">
                        <v-btn fab small @click="closeShowReview()" icon><v-icon color="#00B500">mdi-close</v-icon></v-btn>
                      </v-row>
                      <v-row dense class="mt-2 mb-4" justify="start" v-if="showReview === 'image' && SelectPicReview === index">
                        <v-img :src="listImageReview" max-height="50%" max-width="50%" contain></v-img>
                      </v-row>
                      <v-row dense class="mt-2 mb-4" v-else-if="showReview === 'video' && SelectPicReview === index">
                        <video width="50%" height="50%" controls  loop autoplay><source :src="listImageReview" type="video/mp4"></video>
                      </v-row>
                    </v-col>
                  </v-row>
                  <v-card-subtitle class="py-0 mb-4">
                    <span  class="mr-5" style="cursor: pointer;" @click="replyComment(dataAllReview.id,dataAllReview.id)"><v-icon >mdi-chat </v-icon> {{'ตอบกลับ'}}</span>
                    <span  class="mr-3">
                    <v-icon >mdi-calendar-month</v-icon>
                    {{setDateTh(item.date)}}
                    </span>
                    <span>
                      <v-icon >mdi-clock-time-four-outline</v-icon>
                     {{item.time}} time
                    </span>
                  </v-card-subtitle>
                  <v-row>
                    <v-row v-if="index === indexreplyComment || Object.values(dataAllReview.reply_detail).length !== 0">
                    <v-col :cols="IpadSize? 2:1" >
                      <v-avatar size="40" :class="IpadSize ? 'ml-5':'ml-2' " >
                        <v-img  v-if="img_path === null" src="@/assets/noprofile.png" class="pointPic" contain></v-img>
                        <v-img v-else  :src="img_path" class="rounded-lg"  ></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col :cols="IpadSize? 2:1">
                      <v-avatar size="40" :class="IpadSize ? 'ml-7':'ml-2' " >
                        <v-img  v-if="img_path === null" src="@/assets/noprofile.png" class="pointPic" contain></v-img>
                        <v-img v-else  :src="img_path" class="rounded-lg"  ></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col :cols="IpadSize ? 10:11" class="pb-0">
                        <v-col :cols="IpadSize ? 10:11" v-if="Object.values(dataAllReview.reply_detail).length === 0" class="pb-0">
                          <v-textarea
                          class="pb-0"
                          v-model="add_replyComment"
                          outlined
                          value=""
                          placeholder="เขียนความคิดเห็นของคุณ"
                        ></v-textarea>
                          <v-row v-if=" index === indexreplyComment  && item.reply_comment_review.length === 0" class="ma-0" justify="end">
                             <v-row class="ma-0" justify="end">
                              <v-btn dense rounded dark color="#00B500" @click="sendCreateReplyComment(item)" class="mb-6 mr-3 pl-9 pr-9" >โพสต์ความคิดเห็น</v-btn>
                          </v-row>
                      </v-col>
                      <v-col :cols="IpadSize ? 10:11" v-for="(itemsReply, index2) in item.reply_comment_review" :key="index2"  class="pb-0">
                      <v-col :cols="IpadSize ? 10:11" v-for="(itemsReply, index2) in item.reply_comment_review" :key="index2"  class="pb-0">
                           <v-textarea
                          v-model="itemsReply.reply_comment"
                          class="pb-0"
                          outlined
                          :disabled="[(item.reply_comment_review !== ''? index === indexreditReply ? disabledReply : true :false )][0]"
                          value=""> </v-textarea>
                          <v-card-subtitle class="pt-0 mb-2">
                            <span  v-if="item.reply_comment_review !== '' && item.status_expired_edit" class="mr-5" style="cursor: pointer;" @click="editReply(index,itemsReply.reply_comment)"><v-icon >mdi-pencil </v-icon></span>
                            <span  v-if="item.reply_comment_review !== ''" class="mr-5" style="cursor: pointer;" @click="deleteReply(itemsReply)"><v-icon >mdi-delete-outline </v-icon></span>
                            <span  class="mr-3">
                            <v-icon >mdi-calendar-month</v-icon>
                              {{
                              new Date(itemsReply.updated_at).toLocaleDateString("th-TH", {
                                year: "numeric",
                                month: "long",
                                day: "numeric"
                              })
                            }}
                            </span>
                            <span>
                            <v-icon>mdi-clock-time-four-outline</v-icon>
                              {{new Date(itemsReply.updated_at).toLocaleDateString('th-TH', {
                              hour: 'numeric',
                              minute: 'numeric',
                              second: 'numeric'
                            }).substring(30,10)}}
                          </span>
                          </v-card-subtitle>
                           <v-row v-if=" index === indexreditReply || index === indexreplyComment " class="ma-0" justify="end">
                              <v-btn  dense rounded dark color="#00B500" @click="sendEditReplyComment(item,itemsReply.reply_comment,itemsReply.id)" class="mb-6 mr-3 pl-9 pr-9" >โพสต์ความคิดเห็น</v-btn>
                          </v-row>
                    </v-col>
                  </v-row>
                </div>
              </v-flex>
              <v-flex md1>
                <v-row justify="center" class="mt-2 mr-1">
                  <v-btn color="#F2F2F2" fab x-small elevation="0" @click="Deletecomments(dataAllReview)"
                    ><v-icon color="#A1A1A1"
                      >mdi-delete-outline</v-icon
                    ></v-btn>
                </v-row>
              </v-flex>
            </v-layout> -->
          </v-card>
          <div class="text-center py-4">
            <v-pagination  color="#27AB9C" v-model="page" @input="getDataReviewpage(props.total_comment)" :total-visible="7" :length="pageCount"></v-pagination>
          </div>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog อนุมัติ Review  -->
    <v-dialog v-model="dialogApprove" width="730px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
      <v-card width="100%" height="100%" max-height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span v-if="MobileSize"  class="flex text-center ml-5" style="font-size:18px">
            <font color="#27AB9C">รายละเอียดการให้คะแนน</font>
          </span>
          <span v-else class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">รายละเอียดการให้คะแนน</font>
          </span>
          <v-btn icon dark @click="dialogApprove = !dialogApprove">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row no-gutters>
            <v-col cols="12">
              <v-card outlined class="mt-8">
                <v-container grid-list-xs>
                  <v-row no-gutters>
                    <v-col cols="4" md="2" sm="2" class="text-center" v-if="productListReview.length !== 0">
                      <v-img :src="productListReview[0].product_image" class="imageshow" v-if="productListReview[0].product_image !== ''"/>
                      <v-img :src="require('@/assets/NoImage.png')" class="imageshow" v-else/>
                    </v-col>
                    <v-col cols="7" md="7" sm="7" class="text-left" v-if="productListReview.length !== 0">
                      <!-- <p class="mb-0 captionSku">รหัสสินค้า: {{productList[0].sku}}<br/>{{productList[0].product_name}}</p> -->
                      <p class="mb-0" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ productListReview[0].product_name }}</p>
                      <span v-for="(item, index) in productListReview" :key="index">
                        <span v-if="item.attribute_detail !== ''" class="mb-0 captionSku"><b>{{ item.attribute_detail }}</b></span><span v-if="index !== productListReview.length - 1">, </span>
                      </span>
                      <!-- <span v-if="productListReview.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 captionSku">{{productList[0].key_2_value}}: <b>{{productList[0].product_attribute_detail.attribute_priority_2}}</b></span> -->
                      <!-- <span class="pl-2 mb-0 " style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Color : <b>Black</b> Size : <b>36</b></span> -->
                    </v-col>
                    <v-col cols="12" md="3" class="text-center">
                      <!-- <span class="captionSku">จำนวน {{ productList[0].quantity }} ชิ้น</span> -->
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;" v-if="!MobileSize">จำนวน {{ DetailApproveReview.total_product }} ชิ้น</span>
                      <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" class="ml-0" v-else>จำนวน {{ DetailApproveReview.total_product }} ชิ้น</span>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
            </v-col>
            <v-col cols="12" class="mt-2">
              <v-card outlined color="#F5F8FF" class="mt-2">
                <v-container grid-list-xs>
                  <v-row no-gutters>
                    <v-col cols="12" md="12" class="text-center">
                      <v-rating v-model="DetailApproveReview.stars" readonly size="28" background-color="#FFCB49" color="#FFCB49"></v-rating>
                    </v-col>
                  </v-row>
                </v-container>
              </v-card>
            </v-col>
            <v-col cols="12" class="mt-2">
              <v-row class="mt-6">
                <v-col cols="12" md="12" class="text-left pb-0">
                  <span :style="{'font-size': MobileSize ? '14px' :'18px'}" style="color: #333333; font-weight: 700;">เขียนประเมินความพึงพอใจ</span>
                </v-col>
                <v-col cols="12" md="12" class="pb-0">
                  <v-textarea v-model="DetailApproveReview.comment" outlined maxlength="100" disabled></v-textarea>
                </v-col>
              </v-row>
            </v-col>
            <!-- รูปภาพ -->
            <v-col cols="12" v-if="checkImage">
              <v-row class="pt-2">
                <v-col cols="12" md="12" class="text-left py-0">
                  <span :style="{'font-size': MobileSize ? '14px' :'18px'}" style="color: #333333; font-weight: 700;">รูปภาพ</span>
                </v-col>
                <v-col cols="6" md="3" sm="3" v-for="(item, index) in DetailApproveReview.image_upload" :key="index" class="mt-2">
                  <v-card outlined class="pa-2" width="100%" height="100%" align="center" elevation="1" v-if="!MobileSize">
                    <v-card-text class="pa-0">
                      <v-img :src="item.image_path" contain width="130" height="130" :lazy-src="item.image"></v-img>
                    </v-card-text>
                  </v-card>
                  <v-card outlined width="100" height="100" align="center" elevation="1" v-else>
                    <v-card-text class="pa-0">
                      <v-img :src="item.image_path" contain width="93" height="93" :lazy-src="item.image"></v-img>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
            <!-- วิดีโอ -->
            <v-col cols="12" v-if="checkVideo">
              <v-row class="pt-8">
                <v-col cols="12" md="12" class="text-left py-0">
                  <span :style="{'font-size': MobileSize ? '14px' :'18px'}" style="color: #333333; font-weight: 700;">วิดีโอ</span>
                </v-col>
                <v-col cols="4" md="3" sm="3" v-for="(items, index1) in DetailApproveReview.video_upload" :key="index1" class="mr-2 mt-2">
                  <v-card outlined width="146" height="146" align="center" elevation="1" v-if="!MobileSize">
                    <v-card-text class="pa-0">
                      <video width="141" height="141" muted controls>
                        <source :src="items.image_path" type="video/mp4">
                      </video>
                    </v-card-text>
                  </v-card>
                  <v-card outlined width="100" height="100" align="center" elevation="1" v-else>
                    <v-card-text class="pa-0">
                      <video width="95" height="95" muted controls>
                        <source :src="items.image_path" type="video/mp4">
                      </video>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col>
            <!-- <v-col cols="12" class="mt-6">
              <v-row dense class="mt-6">
                <v-col cols="12" md="12" lg="12" class="text-left mb-2">
                  <span style="font-size: 18px;">เขียนประเมินความพึงพอใจ</span>
                </v-col>
                <v-col cols="12" md="12" lg="12">
                  <v-textarea v-model="DetailApproveReview.comment" outlined maxlength="100" disabled></v-textarea>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12">
              <v-row dense>
                <v-col cols="12" md="12" lg="12" class="text-left mb-2">
                  <span style="font-size: 18px;">รูปภาพ</span>
                </v-col>
                <v-col cols="12" md="3" lg="3" v-for="(item, index) in DetailApproveReview.image_upload" :key="index" class="mr-2 mt-2">
                  <v-card width="170" height="170" elevation="1" >
                    <v-card-text>
                      <v-img :src="item.image_path" contain width="100%" height="100%" :lazy-src="item.image"></v-img>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-col> -->
            <v-col v-if="MobileSize" cols="12" class="mt-4">
              <p style="font-weight: 500; font-size: 12px; line-height: 26px; color: #333333;"><b>วันที่อัปเดตล่าสุด :</b> {{ new Date(DetailApproveReview.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</p>
              <p style="font-weight: 500; font-size: 12px; line-height: 26px; color: #333333;"><b>บริษัท :</b> {{ DetailApproveReview.company_name_th !== null ? DetailApproveReview.company_name_th : '-' }}</p>
              <p style="font-weight: 500; font-size: 12px; line-height: 26px; color: #333333;"><b>ชื่อผู้ใช้ :</b> {{ DetailApproveReview.created_by }}</p>
            </v-col>
            <v-col v-else cols="12" class="mt-4">
              <p style="font-weight: 500; font-size: 16px; line-height: 26px; color: #333333;"><b>วันที่อัปเดตล่าสุด :</b> {{ new Date(DetailApproveReview.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</p>
              <p style="font-weight: 500; font-size: 16px; line-height: 26px; color: #333333;"><b>บริษัท :</b> {{ DetailApproveReview.company_name_th !== null ? DetailApproveReview.company_name_th : '-' }}</p>
              <p style="font-weight: 500; font-size: 16px; line-height: 26px; color: #333333;"><b>ชื่อผู้ใช้ :</b> {{ DetailApproveReview.created_by }}</p>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions class="pb-6">
          <v-spacer></v-spacer>
          <v-btn outlined color="#F5222D" class="px-10" @click="confirmChangeStatus('hidden', DetailApproveReview.product_feedback_id)">ซ่อน</v-btn>
          <v-btn color="#27AB9C" class="white--text px-8" @click="confirmChangeStatus('active', DetailApproveReview.product_feedback_id)">อนุมัติ</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- hidden Review -->
    <v-dialog v-model="dialogComformHidden" width="464px" height="100%" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">ซ่อนความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="dialogComformHidden = !dialogComformHidden">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" align="center">
            <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">คุณได้ทำการซ่อนความคิดเห็น</p>
            <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">คุณต้องการที่จะซ่อนความคิดเห็นนี้ ใช่ หรือ ไม่ ?</p>
          </v-col>
        </v-card-text>
        <v-card-actions>
          <v-col cols="12" align="center">
            <v-btn outlined color="#27AB9C" class="px-8 mr-2" @click="dialogComformHidden = !dialogComformHidden">ยกเลิก</v-btn>
            <v-btn color="#27AB9C" class="white--text px-8" @click="ChangeStatueReview('hidden')">ตกลง</v-btn>
          </v-col>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Active Review -->
    <v-dialog v-model="dialogComfirmActive" width="464px" height="100%" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">อนุมัติความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="dialogComfirmActive = !dialogComfirmActive">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" align="center">
            <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">คุณได้ทำการอนุมัติความคิดเห็น</p>
            <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">คุณต้องการที่จะอนุมัติความคิดเห็นนี้ ใช่ หรือ ไม่ ?</p>
          </v-col>
        </v-card-text>
        <v-card-actions>
          <v-col cols="12" align="center">
            <v-btn outlined color="#27AB9C" class="px-8 mr-2" @click="dialogComfirmActive = !dialogComfirmActive">ยกเลิก</v-btn>
            <v-btn color="#27AB9C" class="white--text px-8" @click="ChangeStatueReview('active')">ตกลง</v-btn>
          </v-col>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Active Review -->
    <v-dialog v-model="dialogComfirmShow" width="464px" height="100%" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card width="100%" height="100%" class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">แสดงความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="dialogComfirmShow = !dialogComfirmShow">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" align="center">
            <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">คุณได้ทำการแสดงความคิดเห็น</p>
            <p style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;">คุณต้องการที่จะแสดงความคิดเห็นนี้ ใช่ หรือ ไม่ ?</p>
          </v-col>
        </v-card-text>
        <v-card-actions>
          <v-col cols="12" align="center">
            <v-btn outlined color="#27AB9C" class="px-8 mr-2" @click="dialogComfirmShow = !dialogComfirmShow">ยกเลิก</v-btn>
            <v-btn color="#27AB9C" class="white--text px-8" @click="ChangeStatueReview('show')">ตกลง</v-btn>
          </v-col>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Success Hidden Review -->
    <v-dialog v-model="dialogSuccessHidden" width="400" :style="MobileSize ? 'z-index: 16000004' : ''">
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">ซ่อนความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="dialogSuccessHidden = !dialogSuccessHidden">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br /><br />
        <v-card-text>
          <v-img src="@/assets/Create_Store/Vector.png" width="70" height="70"></v-img>
          <br />
          <h3 style="font-weight: 600; font-size: 20px; line-height: 22px; color: #27AB9C;">สำเร็จ</h3>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Success Active Review -->
    <v-dialog v-model="dialogSuccessActive" width="400"  :style="MobileSize ? 'z-index: 16000004' : ''">
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">อนุมัติความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="dialogSuccessActive = !dialogSuccessActive">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br /><br />
        <v-card-text>
          <v-img src="@/assets/Create_Store/Vector.png" width="70" height="70"></v-img>
          <br />
          <h3 style="font-weight: 600; font-size: 20px; line-height: 22px; color: #27AB9C;">สำเร็จ</h3>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Success Show Review -->
    <v-dialog v-model="dialogSuccessShow" width="400"  :style="MobileSize ? 'z-index: 16000004' : ''">
      <v-card align="center" class="rounded-lg">
        <v-toolbar color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">แสดงความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="dialogSuccessShow = !dialogSuccessShow">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <br /><br />
        <v-card-text>
          <v-img src="@/assets/Create_Store/Vector.png" width="70" height="70"></v-img>
          <br />
          <h3 style="font-weight: 600; font-size: 20px; line-height: 22px; color: #27AB9C;">สำเร็จ</h3>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="replyCommentModal"
      max-width="600px"
      style="overflow: hidden;"
    >
      <v-card>
        <v-toolbar color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">แสดงข้อความตอบกลับความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="replyCommentModal = !replyCommentModal">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-row dense>
          <v-col cols="12" class="mt-5">
            <span style="font-size: 18px; ">กรอกข้อความตอบกลับความคิดเห็น</span>
          </v-col>
          <v-col cols="12" class="pa-3">
            <v-textarea
              v-model="replyCommentText"
              outlined
            ></v-textarea>
          </v-col>
          <v-col cols="12" class="d-flex justify-end pr-3 pb-5">
            <v-btn color="#27AB9C" class="white--text" @click="confirmReplyComment()">ตกลง</v-btn>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="replyCommentModalEdit"
      max-width="600px"
      style="overflow: hidden;"
    >
      <v-card>
        <v-toolbar color="#BDE7D9" dark dense>
          <span class="flex text-center ml-5" style="font-size:20px">
            <font color="#27AB9C">แก้ไขข้อความตอบกลับความคิดเห็น</font>
          </span>
          <v-btn icon dark @click="replyCommentModalEdit = !replyCommentModalEdit">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-row dense>
          <v-col cols="12" class="mt-5">
            <span style="font-size: 18px; ">กรอกข้อความตอบกลับความคิดเห็น</span>
          </v-col>
          <v-col cols="12" class="pa-3">
            <v-textarea
              v-model="replyCommentText"
              outlined
            ></v-textarea>
          </v-col>
          <v-col cols="12" class="d-flex justify-end pr-3 pb-5">
            <v-btn color="#27AB9C" class="white--text" @click="confirmReplyComment(true)">ตกลง</v-btn>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
  </div>
</template>
<script>
import { Tabs, Tag } from 'ant-design-vue'
const FakeImage = []
for (let i = 0; i < 3; i++) {
  FakeImage.push({
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  props: ['props'],
  filters: {
    truncate: function (value, limit) {
      if (value !== null) {
        if (value.length > limit) {
          value = value.substring(0, (limit - 4)) + '...'
        }
        return value
      }
    }
  },
  data () {
    return {
      comment: 'ชอบสินค้าตัวนี้มากเลย ',
      rating: 4,
      countall: 0,
      countPending: 0,
      countApprove: 0,
      countHide: 0,
      pageCount: 5,
      StateStatus: 0,
      showCountOrder: 0,
      page: 1,
      ShopID: 16,
      dialogApprove: false,
      dialogComfirmActive: false,
      dialogComfirmShow: false,
      dialogComformHidden: false,
      dialogSuccessHidden: false,
      dialogSuccessActive: false,
      dialogSuccessShow: false,
      itemsPerPage: 5,
      openDialog: false,
      selectedProduct: [],
      dialogcomment: false,
      reply: false,
      disabledReply: true,
      indexreplyComment: '',
      indexreditReply: '',
      listImage: [],
      listVideo: [],
      reply_comment: '',
      add_replyComment: '',
      showReview: null,
      listImageReview: [],
      SelectPicReview: null,
      product_id: '',
      userdetail: '',
      img_path: '',
      search: '',
      keyCheckHead: 0,
      dateThai: '',
      headers: [
        { text: 'รูปภาพสินค้า', filterable: false, value: 'product_image', sortable: false, align: 'start', width: '132', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, align: 'start', width: '170', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัส SKU', filterable: false, value: 'product_sku', sortable: false, align: 'start', width: '140', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ความคิดเห็น', filterable: false, value: 'last_comment', sortable: false, align: 'start', width: '260', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วัน/เวลา', filterable: false, value: 'date', sortable: false, align: 'start', width: '130', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', filterable: false, value: 'edit', sortable: false, align: 'start', width: '50', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersPending: [
        { text: 'รูปภาพสินค้า', filterable: false, value: 'product_image', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'บริษัท', filterable: false, value: 'company_name_th', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อผู้ใช้', filterable: false, value: 'created_by', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', filterable: false, value: 'status_comment', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', filterable: false, value: 'view_comment', sortable: false, align: 'start', width: '50', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersApprove: [
        { text: 'รูปภาพสินค้า', filterable: false, value: 'product_image', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'บริษัท', filterable: false, value: 'company_name_th', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อผู้ใช้', filterable: false, value: 'created_by', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', filterable: false, value: 'status_comment', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'hidden_comment', sortable: false, align: 'start', width: '50', class: 'backgroundTable fontTable--text' }
      ],
      headersHidden: [
        { text: 'รูปภาพสินค้า', filterable: false, value: 'product_image', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'บริษัท', filterable: false, value: 'company_name_th', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อผู้ใช้', filterable: false, value: 'created_by', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', filterable: false, value: 'status_comment', sortable: false, align: 'start', width: '100', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'show_comment', sortable: false, align: 'start', width: '50', class: 'backgroundTable fontTable--text' }
      ],
      dataAllReview: [],
      DataTable: [],
      ListReviewData: [],
      disableTable: false,
      FakeImage,
      DetailApproveReview: [],
      productListReview: [],
      feedbackID: '',
      checkImage: false,
      checkVideo: false
    }
  },
  computed: {
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  created () {
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.getReviewProductSeller()
  },
  watch: {
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.ListReviewData.all_product !== undefined ? this.ListReviewData.all_product : []
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataTable = this.ListReviewData.inactive !== undefined ? this.ListReviewData.inactive : []
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataTable = this.ListReviewData.active !== undefined ? this.ListReviewData.active : []
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.DataTable = this.ListReviewData.hidden !== undefined ? this.ListReviewData.hidden : []
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    changeReviewProduct (item) {
      this.StateStatus = item
      this.page = 1
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
    },
    countReview (pagination) {
      this.showCountOrder = pagination.itemsLength
      // console.log('pagination', pagination)
      // console.log('aaaa', this.showCountOrder)
    },
    async getReviewProductSeller () {
      this.$store.commit('openLoader')
      this.countall = 0
      this.countPending = 0
      this.countApprove = 0
      this.countHide = 0
      const shopSellerID = localStorage.getItem('shopSellerID').toString()
      const dataAll = await {
        seller_shop_id: shopSellerID,
        stars: '0',
        page: '1'
      }
      await this.$store.dispatch('actionGetallProductReviewSeller', dataAll)
      var responseDeleteProduct = await this.$store.state.ModuleOrder.stateGetallProductReviewSeller
      // console.log('responseDeleteProduct', responseDeleteProduct.data)
      if (responseDeleteProduct.code !== 401) {
        if (responseDeleteProduct.data.length !== 0) {
          this.ListReviewData = responseDeleteProduct.data
          // console.log('this.data+1', this.ListReviewData)
          this.$store.commit('closeLoader')
          this.countall = this.ListReviewData.total_all_product
          this.countPending = this.ListReviewData.total_inactive
          this.countApprove = this.ListReviewData.total_active
          this.countHide = this.ListReviewData.total_hidden
          if (this.StateStatus === 0) {
            this.DataTable = this.ListReviewData.all_product
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 1) {
            this.DataTable = this.ListReviewData.inactive
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 2) {
            this.DataTable = this.ListReviewData.active
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          } else if (this.StateStatus === 3) {
            this.DataTable = this.ListReviewData.hidden
            if (this.DataTable.length === 0) {
              this.disableTable = false
            } else {
              this.disableTable = true
            }
          }
        } else {
          this.DataTable = []
          this.$store.commit('closeLoader')
        }
      } else {
        this.$store.commit('closeLoader')
        // localStorage.removeItem('roleUser')
        // localStorage.removeItem('roleUserApprove')
        // localStorage.removeItem('oneData')
        // localStorage.removeItem('orderNumber')
        // localStorage.removeItem('orderNumberSeller')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
        this.$EventBus.$emit('refreshToken')
      }
    },
    async Edit (val) {
      this.dialogcomment = true
      this.indexreplyComment = ''
      this.indexreditReply = ''
      this.showReview = ''
      this.listImageReview = ''
      this.SelectPicReview = ''
      this.product_id = val.product_id
      const shopSellerID = localStorage.getItem('shopSellerID').toString()
      var data = {
        seller_shop_id: shopSellerID,
        product_id: val.product_id,
        page: this.page,
        stars: 0
      }
      await this.$store.dispatch('actionsDetailProductReviewSeller', data)
      var responseDetailProduct = await this.$store.state.ModuleOrder.stateDetailProductReviewSeller
      // console.log('Detail ===================>', responseDetailProduct.data.data)
      // this.dataAllReview = responseDeleteProduct.data.data
      if (responseDetailProduct.result === 'SUCCESS') {
        if (responseDetailProduct.data.length !== 0) {
          this.dataAllReview = responseDetailProduct.data.data
          if (responseDetailProduct.data.total_comment % 10 === 0) {
            this.pageCount = responseDetailProduct.data.total_comment / 10
          } else {
            this.pageCount = Math.floor(responseDetailProduct.data.total_comment / 10) + 1
          }
        } else {
          this.dataAllReview = []
          this.pageCount = 0
        }
        // console.log(responseDeleteProduct.data.total_comment, 'this.dataAllReview')
      }
    },
    async ViewDialogApprove (item) {
      // console.log(item)
      this.DetailApproveReview = []
      const shopSellerID = localStorage.getItem('shopSellerID').toString()
      var data = {
        seller_shop_id: shopSellerID,
        product_feedback_id: item.product_feedback_id
      }
      // console.log(data)
      await this.$store.dispatch('actionsDetailFeedbackReview', data)
      var responseFeedbackData = await this.$store.state.ModuleOrder.stateDetailFeedbackReview
      // console.log(responseFeedbackData.data[0])
      if (responseFeedbackData.result === 'SUCCESS') {
        this.DetailApproveReview = responseFeedbackData.data[0]
        this.checkImage = this.DetailApproveReview.image_upload.length !== 0
        this.checkVideo = this.DetailApproveReview.video_upload.length !== 0
        this.productListReview = this.DetailApproveReview.product_list
        this.dialogApprove = true
      } else {
        this.DetailApproveReview = []
        this.$swal.fire({ icon: 'warning', text: 'ไม่พบการประเมินความพึงพอใจสินค้าชิ้นนี้', showConfirmButton: false, timer: 1500 })
      }
    },
    confirmChangeStatus (val, feedbackId) {
      this.feedbackID = ''
      if (val === 'active') {
        this.feedbackID = feedbackId
        this.dialogApprove = false
        this.dialogComfirmActive = true
      } else if (val === 'show') {
        this.feedbackID = feedbackId
        this.dialogApprove = false
        this.dialogComfirmShow = true
      } else {
        this.feedbackID = feedbackId
        this.dialogApprove = false
        this.dialogComformHidden = true
      }
    },
    async ChangeStatueReview (val) {
      if (val === 'active') {
        this.dialogComfirmActive = false
      } else if (val === 'show') {
        this.dialogComfirmShow = false
      } else {
        this.dialogComformHidden = false
      }
      const shopSellerID = localStorage.getItem('shopSellerID').toString()
      var data = {
        seller_shop_id: shopSellerID,
        product_feedback_id: this.feedbackID,
        status: val === 'show' || val === 'active' ? 'active' : val
      }
      await this.$store.dispatch('actionsChangeStatusReview', data)
      var response = await this.$store.state.ModuleOrder.stateChangeStatusReview
      if (response.result === 'SUCCESS') {
        if (val === 'active') {
          this.dialogSuccessActive = true
        } else if (val === 'show') {
          this.dialogSuccessShow = true
        } else {
          this.dialogSuccessHidden = true
        }
        await this.getReviewProductSeller()
      } else {
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
      }
    },
    replyComment (index, id) {
      // console.log('replyComment')
      this.add_replyComment = ''
      this.indexreplyComment = index
    },
    CloseDialog () {
      this.dialogcomment = false
    },
    setDateTh (date) {
      var monthNamesThai = ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
      var day = date.split('-')[0] + ' ' + monthNamesThai[Number(date.split('-')[1]) - 1] + '  ' + (Number(date.split('-')[2]) + 543)
      return day
    },
    editReply (index, type) {
      this.disabledReply = !this.disabledReply
      // console.log(type, 'type')
      this.checkedit = type
      this.indexreditReply = index
    },
    async RemoveImageComment (id, idcomment) {
      var dataDelete = {
        feedback_id: idcomment,
        status: '',
        media_id: id
      }
      // console.log(dataDelete, 'dataDelete')
      await this.$store.dispatch('actionUPSManageReviewDetailsUpdateSeller', dataDelete)
      var responseDeletemedia = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsUpdateSeller
      // console.log(responseDeletemedia, 'responseDeletemedia')
      if (responseDeletemedia.result === 'SUCCESS') {
        // await this.$swal.fire({ icon: 'success', text: 'ลบสำเร็จ', showConfirmButton: false, timer: 2000 })
        var datalist = {
          product_id: this.product_id,
          page: this.page,
          stars: 0
        }
        await this.$store.dispatch('actionUPSManageReviewDetailsSeller', datalist)
        var responseReviewProduct = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsSeller
        this.dataAllReview = responseReviewProduct.data.data
      } else {
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
      }
    },
    async Deletecomments (item) {
      // console.log(item, 'item')
      var dataUpdate = {
        feedback_id: item.id,
        status: 'inactive'

      }
      await this.$store.dispatch('actionUPSManageReviewDetailsUpdateSeller', dataUpdate)
      var responseDeleteComments = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsUpdateSeller
      // console.log(responseDeleteComments, 'responseDeleteComments')
      if (responseDeleteComments.result === 'SUCCESS') {
        await this.$swal.fire({ icon: 'success', text: 'ลบคอมเมนท์สำเร็จ', showConfirmButton: false, timer: 2000 })
        var datalist = {
          product_id: this.product_id,
          page: this.page,
          stars: 0
        }
        await this.$store.dispatch('actionUPSManageReviewDetailsSeller', datalist)
        var responseReviewProduct = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsSeller
        this.dataAllReview = responseReviewProduct.data.data
        if (responseReviewProduct.data.length === 0) {
          this.dialogcomment = false
        }
        var data = {
          stars: 0
        }
        await this.$store.dispatch('actionUPSManageReviewSeller', data)
        var responseListProductReview = await this.$store.state.UPSModuleOrder.stateUPSManageReviewSeller
        this.props = responseListProductReview.data.data
      } else {
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
      }
    },
    async sendCreateReplyComment (item) {
      var addReply = {
        feedback_id: item.id,
        reply_comment: this.add_replyComment
      }
      // console.log(addReply, 'addReply')
      await this.$store.dispatch('actionCreateReplyComment', addReply)
      var responseReplyComments = await this.$store.state.ModuleOrder.stateCreateReplyComment
      // console.log(responseReplyComments, 'responseDeleteComments')
      if (responseReplyComments.result === 'SUCCESS') {
        this.disabledReply = !this.disabledReply
        this.add_replyComment = ''
        var datalist = {
          product_id: this.product_id,
          page: this.page,
          stars: 0
        }
        await this.$store.dispatch('actionUPSManageReviewDetailsSeller', datalist)
        var responseReviewProduct = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsSeller
        this.dataAllReview = responseReviewProduct.data.data
      } else {
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
        this.add_replyComment = ''
      }
    },
    async sendEditReplyComment (item, itemsReply, id) {
      // console.log(item, 'item')
      // console.log(itemsReply, 'itemsReply')
      var editRyply = {
        reply_id: id,
        reply_edit: itemsReply
      }
      // console.log(editRyply, 'editRyply')
      await this.$store.dispatch('actionEditReplyComment', editRyply)
      var responseReplyEditComments = await this.$store.state.ModuleOrder.stateEditReplyComment
      // console.log(responseReplyEditComments, 'responseDeleteComments')
      if (responseReplyEditComments.result === 'SUCCESS') {
        this.add_replyComment = ''
        this.disabledReply = !this.disabledReply
        var datalist = {
          product_id: this.product_id,
          page: this.page,
          stars: 0
        }
        await this.$store.dispatch('actionUPSManageReviewDetailsSeller', datalist)
        var responseReviewProduct = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsSeller
        this.dataAllReview = responseReviewProduct.data.data
      } else {
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
        this.add_replyComment = ''
      }
    },
    async deleteReply (item) {
      var deletereply = {
        reply_id: item.id
      }
      await this.$store.dispatch('actionUPSManageReviewReplyDeleteSeller', deletereply)
      var responseDeleteReplyComments = await this.$store.state.UPSModuleOrder.stateUPSManageReviewReplyDeleteSeller
      // console.log(responseDeleteReplyComments, 'responseDeleteReplyComments')
      if (responseDeleteReplyComments.result === 'SUCCESS') {
        var datalist = {
          product_id: this.product_id,
          page: this.page,
          stars: 0
        }
        await this.$store.dispatch('actionUPSManageReviewDetailsSeller', datalist)
        var responseReviewProduct = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsSeller
        this.dataAllReview = responseReviewProduct.data.data
      }
    },
    async getDataReviewpage (page) {
      // console.log(page, 'page')
      this.dataAllReview = []
      var data = {
        product_id: this.product_id,
        page: this.page,
        stars: 0
      }
      await this.$store.dispatch('actionUPSManageReviewDetailsSeller', data)
      var responseDeleteProduct = await this.$store.state.UPSModuleOrder.stateUPSManageReviewDetailsSeller
      // console.log(responseDeleteProduct, 'responseDeleteProduct')
      if (responseDeleteProduct.message === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
      } else {
        this.dataAllReview = responseDeleteProduct.data.data
        if (responseDeleteProduct.data.total_comment % 10 === 0) {
          this.pageCount = responseDeleteProduct.data.total_comment / 10
        } else {
          this.pageCount = Math.floor(responseDeleteProduct.data.total_comment / 10) + 1
        }
        // console.log(responseDeleteProduct.data.total_comment, 'this.dataAllReview')
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var dataUser = {
          role_user: dataRole.role
        }
        await this.$store.dispatch('actionsUPSUserDetailPage', dataUser)
        const userdetail = await this.$store.state.UPSModuleUser.stateUPSUserDetailPage
        this.userdetail = userdetail.data[0]
        this.img_path = this.userdetail.img_path
      }
    },
    closeShowReview () {
      this.showReview = ''
      this.listImageReview = ''
      this.SelectPicReview = ''
    },
    ShowPicReview (val, type, indexFix) {
      this.showReview = type
      this.listImageReview = val
      this.SelectPicReview = indexFix
    },
    async openModalReplyComment (val) {
      this.replyCommentModal = true
      this.replyData = val
      // console.log(val.product_feedback_id, 'val')
    },
    async openModalReplyCommentEdit (val) {
      this.replyCommentModalEdit = true
      this.replyData = val
      this.replyCommentText = val.reply_comment_review[0].reply_comment
      // console.log(val.product_feedback_id, 'val')
    },
    async confirmReplyComment (isEdit) {
      // console.log(this.replyData, 'this.replyData')
      var data = {
        id: isEdit ? String(this.replyData.reply_comment_review[0].id) : '',
        seller_shop_id: localStorage.getItem('shopSellerID'),
        feedback_id: this.replyData.product_feedback_id,
        reply: this.replyCommentText
      }
      await this.$store.dispatch('actionReplyComment', data)
      var responseFeedbackData = await this.$store.state.ModuleOrder.stateReplyComment
      // console.log(responseFeedbackData, 'responseFeedbackData')
      if (responseFeedbackData.code === 200) {
        this.$swal.fire({ icon: 'success', text: 'ดำเนินการสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.replyCommentModal = false
        this.replyCommentModalEdit = false
        this.dialogcomment = false
        this.replyCommentText = ''
      } else {
        this.$swal.fire({ icon: 'warning', text: responseFeedbackData.message, showConfirmButton: false, timer: 1500 })
      }
      // this.replyCommentModal = false
      // this.replyCommentModalEdit = false
      // await this.getReviewProductSeller()
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
<style scoped>
.pointPic {
  opacity: 0.5;
}
.pointPic:hover {
  opacity: 1.0;
}
.imageshow {
  width: 80px;
  height: 80px;
  cursor: pointer;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
.captionSku {
  font-size: 12px;
}
.labelInputSize {
  font-size: 16px;
}
.bgRating {
  background-color: red;
}
</style>
