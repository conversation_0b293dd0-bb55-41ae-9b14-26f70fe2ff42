<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="pb-0" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>รายละเอียดของ{{ this.detailCompany.name_th }}</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 17px; line-height: 32px; color: #333333;" class="pb-0" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> รายละเอียดของ{{ this.detailCompany.name_th }}</v-card-title>

      <v-card-text class="px-0 pa-4">
        <v-row dense>
          <v-col cols="12" md="6" sm="12">
            <v-row no-gutters dense>
              <v-col cols="3" md="2" sm="2" class="ml-3">
                <v-avatar
                  style="border: 1px solid #EBEBEB;"
                  rounded
                  size="60"
                  :width="IpadProSize ? '80px' : IpadSize ? '70px' :'80px'"
                  :height="IpadProSize ? '80px' : IpadSize ? '70px' : '80px'"
                >
                  <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png" contain v-if="this.detailCompany.img_path === null"></v-img>
                  <v-img :src="this.detailCompany.img_path + '?nochace=' + time" max-width="50" max-height="50" contain v-else></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="8" md="9" sm="9" class="pt-1 ml-3">
                <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ this.detailCompany.name_th }}</span><br/>
                <span style="font-weight: 600; font-size: 12px; line-height: 16px; color: #989898;">{{ this.detailCompany.name_en }}</span><br/>
                <v-row no-gutters>
                  <!-- <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;" class="pr-4"><v-icon color="#27AB9C">mdi-email-outline</v-icon> WWW.Eggs.net</span> -->
                  <span style="font-weight: 400; font-size: 12px; line-height: 16px; color: #989898;" class="pr-4"><v-icon color="#27AB9C">mdi-phone-outline</v-icon> {{ this.detailCompany.tel !== null ? this.detailCompany.tel : '-'  }}</span>
                </v-row>
              </v-col>
            </v-row>
          </v-col>
          <!-- <v-col cols="12" md="6" align="end" class="pr-4" v-if="!IpadSize">
            <v-btn color="#27AB9C" dark rounded @click="editCompany()" outlined ><v-icon color="#27AB9C" class="pr-2">mdi-pencil</v-icon> แก้ไขข้อมูล</v-btn>
          </v-col> -->
        </v-row>
        <v-row dense class="mx-2">
          <v-col cols="12" md="7" sm="12" class="mb-4">
            <v-card elevation="0" width="100%" height="100%" outlined style="border: 1px solid #EBEBEB; box-sizing: border-box; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
              <v-card-text>
                <v-row dense>
                  <v-col cols="2" md="2" sm="2" class="ml-1">
                    <v-avatar
                      style="background: #F3F5F9;"
                      :width="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                      :height="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                    >
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/office.png" contain width="40" height="40"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="9" md="9" sm="12" :class="MobileSize ? 'pl-1 mb-2' : 'pt-3 ml-0'">
                    <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ this.detailCompany.name_th }}</span><br/>
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Company Detail)</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4 mb-4">
                  <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                </v-row>
                <v-row dense>
                  <v-card elevation="0" width="100%" height="100%" style="background: #FAFAFA; border-radius: 8px;">
                    <v-card-text>
                      <span class="textCompany">ชื่อบริษัท (ภาษาไทย) :  <b style="color: #333333;">{{ this.detailCompany.name_th }}</b></span><br/>
                      <span class="textCompany">ชื่อบริษัท (ภาษาอังกฤษ) : <b style="color: #333333;">{{ this.detailCompany.name_en }}</b></span><br/>
                      <span class="textCompany">รหัสบริษัท  : <b style="color: #333333;">{{ this.detailCompany.code }}</b></span><br/>
                      <span class="textCompany">รหัสประจำตัวผู้เสียภาษี  : <b style="color: #333333;">{{ this.detailCompany.tax_id }}</b></span><br/>
                      <span class="textCompany">หมายเลขโทรศัพท์บริษัท  : <b style="color: #333333;">{{ this.detailCompany.phone !== null ? this.detailCompany.phone : '-' }}</b></span><br/>
                      <span class="textCompany">หมายเลขโทรศัพท์มือถือ  : <b style="color: #333333;">{{ this.detailCompany.tel !== null ? this.detailCompany.tel : '-' }}</b></span><br/>
                      <span class="textCompany">หมายเลขแฟกซ์  : <b style="color: #333333;">{{ this.detailCompany.fax !== null ? this.detailCompany.fax : '-' }}</b></span>
                    </v-card-text>
                  </v-card>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
          <v-col cols="12" md="5" sm="12" class="mb-4">
            <v-card elevation="0" width="100%" height="100%" outlined style="border: 1px solid #EBEBEB; box-sizing: border-box; box-shadow: inset 0px 1px 2px rgba(62, 69, 239, 0.3); border-radius: 8px;">
              <v-card-text>
                <v-row no-gutters dense>
                  <v-col cols="2" md="2" sm="2" class="ml-1">
                    <v-avatar
                      style="background: #F3F5F9;"
                      :width="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                      :height="IpadProSize ? '60px' : IpadSize ? '40px' : MobileSize ? '50px' : '60px'"
                    >
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/map.png" contain width="40" height="40"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="9" md="9" sm="12" :class="MobileSize ? 'pl-1 mb-3' : 'pt-3 pl-3 mb-3 ml-1'">
                    <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #333333;">{{ this.detailCompany.name_th }}</span><br/>
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #636363;">(Address)</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-2 mb-2">
                  <v-divider style="border: 1px dashed #80E1F9;"></v-divider>
                </v-row>
                <v-card elevation="0" width="100%" height="182" style="background: #FAFAFA; border-radius: 8px;" class="mt-4">
                  <v-card-text>
                    <span class="textCompany">ที่อยู่ :  <b style="color: #333333;">{{ this.detailAddress.detail }} {{ this.detailAddress.sub_district }} {{ this.detailAddress.district }} {{ this.detailAddress.province }} {{ this.detailAddress.zip_code }}</b></span>
                  </v-card-text>
                </v-card>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      companyList: [],
      detailCompany: [],
      detailAddress: [],
      name_th: '',
      time: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.companyID = Number(this.$route.query.companyID)
    this.getBusinessTaxIDtoCompany()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/detailCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    backtoUserMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
      }
    },
    getTime () {
      this.time = (new Date()).getTime() + Math.round(Math.random())
    },
    async getBusinessTaxIDtoCompany () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      const taxID = await this.$store.state.ModuleUser.stateAuthorityUser
      var bizid = localStorage.getItem('business_id')
      var ownerBusiness = taxID.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
      // this.taxID = ownerBusiness[0].owner_tax_id
      if (ownerBusiness.length === 0) {
        this.$swal.fire({
          icon: 'error',
          text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
          showConfirmButton: false,
          timer: 2500
        })
        if (!this.MobileSize) {
          this.$router.push('/detailbusinesssid')
        } else {
          this.$router.push('/detailbusinesssidMobile')
        }
      } else {
        this.taxID = ownerBusiness[0].owner_tax_id
        const data = {
          tax_id: this.taxID
        }

        await this.$store.dispatch('actionsManageCompany', data)
        const response = await this.$store.state.ModuleBusiness.stateManageCompany
        // console.log('Companies', response)
        if (response.result === 'SUCCESS') {
          this.$EventBus.$emit('changeNav')
          this.companyList = response.data.companies
          this.companyList = [...response.data.companies.filter(company => company.company_id === this.companyID)]
          this.countCompany = response.data.total_company
          // console.log('Companies', this.companyList)
          const detailcompany = this.companyList[0]
          this.detailCompany = detailcompany
          // console.log('detail', this.detailCompany)
          this.detailAddress = detailcompany.address[0]
          // console.log('address', this.detailAddress)
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>
