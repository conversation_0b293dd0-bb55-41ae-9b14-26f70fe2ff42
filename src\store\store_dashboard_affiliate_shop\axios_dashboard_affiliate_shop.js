import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async DashboardAffiliateGraph (year) {
    try {
      var response = await axios.get(`http://192.168.75.54:3001/api/transactions?year=${year}`)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DashboardAffiliateGraphbymonth (year, month) {
    try {
      var response = await axios.get(`http://192.168.75.54:3001/api/transactions?year=${year}?month=${month}`)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DashboardAffiliateGraphbyday (year, month, day) {
    try {
      var response = await axios.get(`http://192.168.75.54:3001/api/transactions?year=${year}?month=${month}?$day=${day}`)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateTable () {
    try {
      var response = await axios.get('https://666be6d749dbc5d7145bafb2.mockapi.io/api/affiliate/table/dashboardaffiliate')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateUserTable () {
    try {
      var response = await axios.get('https://666be6d749dbc5d7145bafb2.mockapi.io/api/affiliate/table/userTable')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PaginationTable (data) {
    try {
      var response = await axios.get(`http://192.168.72.92:3001/api/transactions/record?page=${data.page}&pageSize=${data.pageSize}`)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async PaginationTableProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/productsAffiliate`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async PaginationTableUsers (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/usersAffiliated`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async TopTenMostSoldPropuct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/sellerTopClicked`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async SummaryChart (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/summarySellerGraph`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async excelDownload (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/exportAffiliateDashboard`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async SellerTopSolds (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/sellerTopSolds`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  },
  async SellerReport (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/sellerReport`, data, auth)
      return response
    } catch (error) {
      return error.response.data
    }
  }
}
