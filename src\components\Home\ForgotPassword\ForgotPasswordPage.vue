<template>
  <!-- Form Forgot Password -->
  <v-container>
    <v-row dense justify="center">
      <v-col cols="12" md="12" class="my-9" align="center">
        <v-card :width="!MobileSize ? '480px': '100%'" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text :class="MobileSize ? 'px-2' : ''">
            <v-card-title class="pb-0">
              <v-btn v-if="changeForm === false" fab small elevation="0" color="#EBF1F9" @click="backtoLogin()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn>
              <v-btn v-else fab small elevation="0" color="#EBF1F9" @click="backtoResetPasswordOTP()"><v-icon color="#269AFD">mdi-chevron-left</v-icon></v-btn>
            </v-card-title>
            <v-row dense class="px-4 mt-4">
              <v-col cols="12">
                <v-tabs v-model="selectTab" @change="handleTabChange" hide-slider centered grow class="tabsStyle" background-color="#F6F6F6" active-class="activeTabs" ref="tabs">
                  <v-tab
                    v-for="(item, index) in tabItem"
                    :key="index"
                    class="mx-1"
                    ref="tab"
                  >
                    {{ item.text }}
                  </v-tab>
                  <div class="glider" :style="gliderStyle"></div>
                </v-tabs>
                <v-tabs-items v-model="selectTab">
                  <!-- email -->
                  <v-tab-item>
                    <v-form ref="formEmail" v-model="isFormValidEmail" :lazy-validation="lazyEmail">
                      <v-card elevation="0" width="100%" height="100%" class="mt-4">
                        <v-card-text :class="MobileSize ? 'px-0' : ''">
                          <v-row dense>
                            <v-col cols="12" align="center">
                              <img loading="lazy" src="@/assets/ForgotPasswordEmail.jpg" width="126px" height="115px" alt="ForgotPasswordEmail">
                            </v-col>
                            <v-col cols="12" align="center" class="mt-4">
                              <span style="font-weight: 700; color: #333333; font-size: 16px;">{{ $t('ForgotPassword.headers') }}</span>
                            </v-col>
                            <v-col cols="12" align="center" class="mt-2">
                              <span style="color: #A1A1A1; font-size: 14px;">{{ $t('ForgotPassword.describe') }}</span>
                            </v-col>
                            <!-- ชื่อผู้ใช้งาน -->
                            <v-col cols="12" class="mt-2" align="start">
                              <span style="color: #333333; font-size: 16px;">{{ $t('register.Username') }}</span>
                              <v-text-field v-model="username" dense outlined :placeholder="$t('register.EnterUsername')" @paste="onPaste" @keypress="checkCopyPaste($event)" @keydown="noSpace" :rules="Rules.name" oninput="this.value = this.value.replace(/[^A-Za-z0-9]/g, '').replace(/\s/g, '').toLowerCase()"></v-text-field>
                            </v-col>
                            <!-- E-mail -->
                            <v-col cols="12" align="start" >
                              <span style="color: #333333; font-size: 16px;">E-mail</span>
                              <v-text-field v-model="email" dense outlined :placeholder="$t('register.EnterEmail')" :rules="Rules.email"></v-text-field>
                            </v-col>
                          </v-row>
                        </v-card-text>
                        <v-card-actions :class="MobileSize ? 'px-0 pt-0' : 'px-4 pt-0'">
                          <!-- <v-btn block rounded height="50px" color="primary" :disabled="username === '' || email === '' ? true : false" @click="resetPasswordEmail()">ส่ง</v-btn> -->
                          <v-btn block rounded height="50px" color="primary" :disabled="!isFormValidEmail" @click="resetPasswordEmail()">{{ $t('ForgotPassword.send') }}</v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-form>
                  </v-tab-item>
                  <!-- otp -->
                  <v-tab-item>
                    <v-form ref="formOTP" v-model="isFormValidOTP" :lazy-validation="lazyOTP" v-if="changeForm === false">
                      <v-card elevation="0" width="100%" height="100%" class="mt-4">
                        <v-card-text :class="MobileSize ? 'px-2' : ''">
                          <v-row dense>
                            <v-col cols="12" align="center">
                              <img loading="lazy" src="@/assets/ForgotPasswordOTP.jpg" width="174px" height="110px" alt="ForgotPasswordOTP">
                            </v-col>
                            <v-col cols="12" align="center" class="mt-4">
                              <span style="font-weight: 700; color: #333333; font-size: 16px;">{{ $t('ForgotPassword.headers') }}</span>
                            </v-col>
                            <v-col cols="12" align="center" class="mt-2">
                              <span style="color: #A1A1A1; font-size: 14px;">{{ $t('ForgotPassword.describeOTP') }}</span>
                            </v-col>
                            <!-- ชื่อผู้ใช้งาน -->
                            <v-col cols="12" class="mt-2" align="start">
                              <span style="color: #333333; font-size: 16px;">{{ $t('register.Username') }}</span>
                              <v-text-field v-model="usernameOTP" dense outlined :placeholder="$t('register.EnterUsername')" @paste="onPaste" @keypress="checkCopyPaste($event)" @keydown="noSpace" :rules="Rules.name" oninput="this.value = this.value.replace(/[^A-Za-z0-9]/g, '').replace(/\s/g, '').toLowerCase()"></v-text-field>
                            </v-col>
                            <!-- เบอร์โทรศัพท์ -->
                            <v-col cols="12" align="start" >
                              <span style="color: #333333; font-size: 16px;">{{ $t('register.Phone') }}</span>
                              <v-text-field v-model="tel" dense outlined :placeholder="$t('register.EnterPhone')" @keydown="noSpace" :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                            </v-col>
                            <!-- รหัสผ่านใหม่ -->
                            <v-col cols="12" align="start" >
                              <span style="color: #333333; font-size: 16px;">{{ $t('ForgotPassword.newPassword') }}</span><span style="color: red; font-size: 16px;">*</span>
                              <v-text-field v-model="password" dense outlined :placeholder="$t('ForgotPassword.enterNewPassword')" @keydown="noSpace" :rules="Rules.password" :append-icon="show1 ? 'mdi-eye' : 'mdi-eye-off'" :type="show1 ? 'text' : 'password'" @click:append="show1 = !show1"></v-text-field>
                            </v-col>
                            <!-- ยืนยันรหัสผ่านใหม่ -->
                            <v-col cols="12" align="start" >
                              <span style="color: #333333; font-size: 16px;">{{ $t('ForgotPassword.confirmNewPassword') }}</span><span style="color: red; font-size: 16px;">*</span>
                              <v-text-field v-model="confirmpassword" dense outlined :placeholder="$t('ForgotPassword.EnterConfirmPassword')" @keydown="noSpace" :append-icon="show2 ? 'mdi-eye' : 'mdi-eye-off'" :type="show2 ? 'text' : 'password'" @click:append="show2 = !show2" :rules="[v => confirmPasswordRules(v, password)]"></v-text-field>
                            </v-col>
                          </v-row>
                        </v-card-text>
                        <v-card-actions :class="MobileSize ? 'px-0 pt-0' : 'px-4 pt-0'">
                          <!-- <v-btn block rounded height="50px" color="primary" :disabled="usernameOTP === '' || tel === '' || password === '' || (password !== confirmpassword) ? true : false" @click="sentOTP()">ส่ง OTP</v-btn> -->
                          <v-btn block rounded height="50px" color="primary" :disabled="!isFormValidOTP" @click="sentOTP()">{{ $t('ForgotPassword.sendOTP') }}</v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-form>
                    <v-form ref="formOTPNumber" :lazy-validation="lazyOTPNumber" v-else>
                      <v-card elevation="0" width="100%" height="100%" class="mt-4">
                        <v-card-text :class="MobileSize ? 'px-2' : ''">
                          <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                            <img loading="lazy" src="@/assets/ForgotPasswordOTP.jpg" width="174px" height="110px" alt="ForgotPasswordOTP">
                          </v-row>
                          <v-row dense justify="center" align-content="center" class="mt-4 mb-4">
                            <span style="font-weight: 700; color: #333333; font-size: 16px;">{{ $t('register.EnterOTP') }}</span>
                          </v-row>
                          <v-row v-if="showError" dense justify="center" align-content="center" class="my-0">
                            <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">{{ $t('register.WrongOTP') }}</span>
                          </v-row>
                          <v-row no-gutters dense :class="MobileSize ? 'mx-0' : 'mx-4'" justify="center">
                            <v-col cols="12" md="12" sm="12">
                              <v-otp-input
                                ref="otpInput"
                                color="#269AFD"
                                v-model="otp"
                                :error="showError"
                                :length="length"
                                oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                              ></v-otp-input>
                            </v-col>
                            <v-row dense justify="center" align-content="center" class="mt-6 mb-4">
                              <span style="font-weight: normal; font-size: 16px; line-height: 32px; color: #000000;">{{ $t('register.SendOTPPhone') }} <span style="color: #269AFD;">{{maskPhoneNumber(tel)}}</span></span>
                            </v-row>
                            <!-- <v-col cols="12" md="12" sm="12" class="mb-4">
                              <span style="font-weight: normal; font-size: 16px; color: #000000;">รหัสอ้างอิง: <b>{{ refCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn><br />
                            </v-col> -->
                            <v-col cols="12" md="12" sm="12">
                              <span style="font-weight: normal; font-size: 16px; color: #000000;">{{ $t('register.OTPrequestAgain') }} <span style="color: #269AFD;">{{ displayCounter }}</span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon color="#269AFD">mdi-refresh</v-icon></v-btn></span>
                            </v-col>
                          </v-row>
                        </v-card-text>
                        <v-card-actions :class="MobileSize ? 'px-0 pt-0' : 'px-4 pt-0'">
                          <v-btn block rounded height="50px" color="primary" @click="checkOTP()" :disabled="!isActive">{{ $t('ForgotPassword.sendOTP') }}</v-btn>
                        </v-card-actions>
                      </v-card>
                    </v-form>
                  </v-tab-item>
                </v-tabs-items>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-dialog v-model="DialogConfirmChangeTab" persistent width="420px" style="border-radius: 24px;">
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 24px;">
        <v-card-text class="px-2 py-2">
          <v-row dense>
            <v-col cols="12" align="end">
              <v-btn icon @click="cancelChangeTab()">
                <v-icon color="primary">mdi-close</v-icon>
              </v-btn>
            </v-col>
            <v-col cols="12" align="center">
              <v-row dense>
                <v-col cols="12" align="center">
                  <v-icon size="108" color="#ffcd3f">mdi-information</v-icon>
                </v-col>
                <v-col cols="12" align="center">
                  <span style="font-weight: 700; color: #333333; font-size: 18px;">{{ $t('register.LeavePage') }}</span>
                </v-col>
                <v-col cols="12" align="center">
                  <span style="color: #333333; font-size: 16px;">{{ $t('register.ExitPage') }}</span>
                </v-col>
                <v-col cols="12" align="center" class="px-4 py-4">
                  <v-row dense class="d-flex">
                    <v-col class="mr-auto">
                      <v-btn block rounded height="40px" outlined color="primary" @click="cancelChangeTab()">{{ $t('register.Cancel') }}</v-btn>
                    </v-col>
                    <v-col class="ml-auto">
                      <v-btn block rounded height="40px" color="primary" @click="confirmChangeTab()">{{ $t('register.Confirm') }}</v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogMessage" persistent width="420px" style="border-radius: 24px;">
      <v-card elevation="0" width="100%" height="100%" style="border-radius: 24px;">
        <v-card-text class="px-2 py-2">
          <v-row dense>
            <v-col cols="12" align="end">
              <v-btn icon @click="closeModal()">
                <v-icon color="primary">mdi-close</v-icon>
              </v-btn>
            </v-col>
            <v-col cols="12" align="center" v-if="responseCode === 'success'">
              <v-row dense>
                <v-col cols="12" align="center">
                  <v-icon size="108" color="primary">mdi-check-circle</v-icon>
                </v-col>
                <v-col cols="12" align="center">
                  <span style="font-weight: 700; color: #333333; font-size: 18px;">{{ typeResetPassword === 'email' ? $t('ForgotPassword.resetEmailSuccess') : $t('ForgotPassword.resetOTPSuccess') }}</span>
                </v-col>
                <v-col cols="12" align="center" v-if="typeResetPassword === 'email'">
                  <span style="color: #9A9A9A; font-size: 16px;">{{ $t('ForgotPassword.detailReset') }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" align="center" v-else>
              <v-row dense>
                <v-col cols="12" align="center">
                  <v-icon size="108" color="red">mdi-close-circle</v-icon>
                </v-col>
                <v-col cols="12" align="center">
                  <span v-if="$i18n.locale === 'th'" style="font-weight: 700; color: #333333; font-size: 18px;">{{ messageFail }}</span>
                  <span v-if="$i18n.locale === 'en'" style="font-weight: 700; color: #333333; font-size: 18px;">No matching username or email was found.</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      selectTab: 0,
      pendingTab: null,
      tabItem: [
        { text: 'E-mail', href: 'email' },
        { text: `${this.$t('ForgotPassword.PhoneType')}`, href: 'otp' }
      ],
      gliderPosition: {
        width: '0px',
        left: '0px'
      },
      DialogConfirmChangeTab: false,
      lazyEmail: false,
      lazyOTP: false,
      lazyOTPNumber: false,
      showError: false,
      displayCounter: '00:00',
      counter: 0,
      disableRefreshOTP: true,
      dataOTP: [],
      refCode: '',
      otpCode: '',
      mobile: '',
      length: 6,
      otp: '',
      username: '',
      usernameOTP: '',
      email: '',
      tel: '',
      password: '',
      confirmpassword: '',
      show1: false,
      show2: false,
      changeForm: false,
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        name: [
          v => !!v || `${this.$t('register.ValidateUsername1')}`,
          v => /^[A-Za-z0-9\s]+$/.test(v) || `${this.$t('register.ValidateUsername2')}`,
          v => v.length >= 6 || `${this.$t('register.ValidateUsername3')}`
        ],
        password: [
          v => !!v || `${this.$t('register.ValidatePassword1')}`,
          v => /[A-Za-z0-9!@#$%^&*(),.?":{}|<>_]/.test(v) || `${this.$t('register.ValidatePassword2')}`,
          v => /(?=.*?[A-Za-z]).+/.test(v) || `${this.$t('register.ValidatePassword3')}`,
          v => /(?=.*\d).+/.test(v) || `${this.$t('register.ValidatePassword4')}`,
          v => v.length >= 8 || `${this.$t('register.ValidatePassword5')}`
        ],
        email: [
          v => !!v || `${this.$t('register.ValidateEmail1')}`,
          v => /.+@.+\..+/.test(v) || `${this.$t('register.ValidateEmail2')}`,
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || `${this.$t('register.ValidateEmail3')}`,
          v => /^\S*$/.test(v) || `${this.$t('register.ValidateEmail4')}`
        ],
        tel: [
          v => !!v || `${this.$t('OTPPage.ValidateOTP1')}`,
          v => v.length === 10 || v === '' || `${this.$t('OTPPage.ValidateOTP2')}`
        ]
      },
      DialogMessage: false,
      responseCode: '',
      typeResetPassword: '',
      messageFail: '',
      isFormValidEmail: false,
      isFormValidOTP: false
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.updateGlider()
  },
  computed: {
    isActive () {
      return this.otp.length === this.length
    },
    passwordConfirmationRule () {
      return () =>
        this.password === this.confirmpassword || 'กรุณาใส่รหัสผ่านให้ตรงกัน'
    },
    gliderStyle () {
      // กำหนดสไตล์ให้ glider ตามตำแหน่งและขนาดที่คำนวณได้
      return {
        width: this.gliderPosition.width,
        left: this.gliderPosition.left,
        transition: 'all 0.3s ease'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    confirmPasswordRules (confirmPass, Pass) {
      if (confirmPass === '') {
        return `${this.$t('register.ValidatePassword1')}`
      } else if (confirmPass !== Pass) {
        return `${this.$t('register.ValidateConfirmPassword1')}`
      }
    },
    handleTabChange (newTab) {
      if (newTab === 1) {
        if (this.username !== '' && this.email !== '') {
          this.pendingTab = newTab
          this.DialogConfirmChangeTab = true
        } else {
          this.pendingTab = newTab
          this.confirmChangeTab()
        }
      } else {
        if (this.usernameOTP !== '' || this.tel !== '' || this.password !== '' || this.confirmpassword !== '') {
          this.pendingTab = newTab
          this.DialogConfirmChangeTab = true
        } else {
          this.pendingTab = newTab
          this.confirmChangeTab()
        }
      }
    },
    backtoLogin () {
      this.$router.push({ path: '/Login' }).catch(() => {})
    },
    maskPhoneNumber (phoneNumber) {
      return phoneNumber.substring(0, 2) + 'xxxxxx' + phoneNumber.substring(8)
    },
    onPaste (evt) {
      if (evt.which === 32) {
        evt.preventDefault()
      }
    },
    checkCopyPaste (e) {
      if (e.which === 32) {
        e.preventDefault()
      }
    },
    noSpace (e) {
      if (e.which === 32) {
        e.preventDefault()
      }
    },
    updateGlider () {
      const tabs = this.$refs.tab
      if (tabs && tabs[this.selectTab]) {
        const currentTab = tabs[this.selectTab].$el || tabs[this.selectTab]
        this.gliderPosition = {
          width: `${currentTab.offsetWidth}px`,
          left: `${currentTab.offsetLeft}px`
        }
      }
    },
    cancelChangeTab () {
      this.selectTab = this.pendingTab === 1 ? 0 : 1
      this.DialogConfirmChangeTab = false
    },
    confirmChangeTab () {
      this.selectTab = this.pendingTab
      if (this.selectTab === 0) {
        if (this.changeForm === false) {
          this.usernameOTP = ''
          this.tel = ''
          this.password = ''
          this.confirmpassword = ''
          this.$refs.formOTP.resetValidation()
        } else {
          this.usernameOTP = ''
          this.tel = ''
          this.password = ''
          this.confirmpassword = ''
          this.changeForm = false
        }
      } else {
        this.username = ''
        this.email = ''
        this.$refs.formEmail.resetValidation()
      }
      this.updateGlider()
      this.DialogConfirmChangeTab = false
    },
    backtoResetPasswordOTP () {
      this.otp = ''
      this.showError = false
      this.counter = 0
      this.changeForm = false
    },
    closeModal () {
      this.DialogMessage = false
      if (this.responseCode === 'success') {
        this.$router.push({ path: '/Login' }).catch(() => {})
      }
    },
    async resetPasswordEmail () {
      this.responseCode = ''
      this.typeResetPassword = 'email'
      this.messageFail = ''
      this.$store.commit('openLoader')
      const dataEmail = {
        username: this.username,
        email: this.email
      }
      await this.$store.dispatch('actionsResetPasswordByEmail', dataEmail)
      const response = await this.$store.state.ModuleHompage.stateResetPasswordByEmail
      if (response.message === 'คำขอดำเนินการสำเร็จ กรุณาตรวจสอบอีเมลของท่าน') {
        this.$store.commit('closeLoader')
        this.responseCode = 'success'
        this.DialogMessage = true
      } else {
        this.$store.commit('closeLoader')
        this.responseCode = 'fail'
        this.messageFail = response.message
        this.DialogMessage = true
      }
    },
    async sentOTP () {
      this.typeResetPassword = 'OTP'
      this.messageFail = ''
      this.responseCode = ''
      this.$store.commit('openLoader')
      const dataSentOTP = {
        username: this.usernameOTP,
        tel_no: this.tel
      }
      await this.$store.dispatch('actionsSendOTPResetPassword', dataSentOTP)
      const responseSentOTP = await this.$store.state.ModuleHompage.stateSendOTPResetPassword
      if (responseSentOTP.message === 'ส่ง OTP สำเร็จ') {
        this.$store.commit('closeLoader')
        this.changeForm = true
        window.scrollTo(0, 0)
        this.counter = 0
        this.disableRefreshOTP = true
        this.countdownCheck(300)
      } else {
        this.$store.commit('closeLoader')
        this.responseCode = 'fail'
        this.messageFail = responseSentOTP.message
        this.DialogMessage = true
      }
    },
    countdownCheck (second) {
      this.counter = second
      const interval = setInterval(() => {
        var minutes = Math.floor(this.counter / 60)
        var seconds = this.counter % 60
        seconds = seconds < 10 ? `0${seconds}` : seconds
        this.displayCounter = `${minutes}:${seconds}`
        this.counter--
        if (this.counter < 0) {
          this.disableRefreshOTP = false
          clearInterval(interval)
        }
      }, 1000)
    },
    async RefreshOTP () {
      this.typeResetPassword = 'OTP'
      this.messageFail = ''
      this.responseCode = ''
      this.$store.commit('openLoader')
      const dataSentOTP = {
        username: this.usernameOTP,
        tel_no: this.tel
      }
      await this.$store.dispatch('actionsSendOTPResetPassword', dataSentOTP)
      const responseSentOTP = await this.$store.state.ModuleHompage.stateSendOTPResetPassword
      if (responseSentOTP.message === 'ส่ง OTP สำเร็จ') {
        this.$store.commit('closeLoader')
        this.counter = 0
        this.disableRefreshOTP = true
        this.countdownCheck(300)
      } else {
        this.$store.commit('closeLoader')
        this.responseCode = 'fail'
        this.messageFail = responseSentOTP.message
        this.DialogMessage = true
      }
    },
    async checkOTP () {
      this.typeResetPassword = 'OTP'
      this.messageFail = ''
      this.responseCode = ''
      this.$store.commit('openLoader')
      const dataCheckOTP = {
        otp: this.otp,
        new_password: this.password,
        confirm_new_password: this.confirmpassword
      }
      await this.$store.dispatch('actionsResetPasswordByOTP', dataCheckOTP)
      const responseCheckOTP = await this.$store.state.ModuleHompage.stateResetPasswordByOTP
      if (responseCheckOTP.message === 'ดำเนินการเปลี่ยนรหัสผ่านสำเร็จ') {
        this.$store.commit('closeLoader')
        this.responseCode = 'success'
        this.DialogMessage = true
      } else {
        this.$store.commit('closeLoader')
        this.showError = true
        // this.responseCode = 'fail'
        // this.messageFail = responseCheckOTP.message
        // this.DialogMessage = true
      }
    }
  }
}
</script>

<style scoped>
  .tabsStyle {
    border-radius: 999px;
    position: relative;
    overflow: hidden;
  }
  .activeTabs {
    background-color: #FFFFFF;
    border-radius: 999px;
    margin: 2px;
  }
  .v-tab {
    z-index: 2; /* ให้แท็บอยู่ด้านบน */
  }
  .glider {
    position: absolute;
    top: 2px;
    bottom: 2px;
    background-color: #ffffff; /* สีขาวที่ครอบแท็บ */
    border-radius: 999px; /* เพิ่มความโค้งให้กลมกลืน */
    box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.1); /* เงาเพื่อความสวยงาม */
    z-index: 1; /* อยู่ด้านล่างของแท็บ */
    transition: all 0.3s ease;
  }
</style>
