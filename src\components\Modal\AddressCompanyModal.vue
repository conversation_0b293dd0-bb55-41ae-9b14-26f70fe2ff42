<template>
  <div>
    <!-- Dialog Add/Edit Address Detail -->
    <v-dialog v-model="AddressCompanyDialog" width="918" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
          <v-row style="height: 120px;">
              <v-col style="text-align: center;" :class="MobileSize ? 'pt-6' : 'pt-6'">
                <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{title}}</b></span>
              </v-col>
              <v-btn fab small @click="cancel()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;"></v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-container class="pa-0">
              <!-- Address Desktop, IpadPro, Ipad -->
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                  <v-card-text v-if="!MobileSize" >
                    <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="30" height="30"></v-img>
                    <span style="font-size: 20px; font-weight: 700;">ข้อมูลผู้รับสินค้า</span>
                    <v-form ref="FormCompanyAddress" :lazy-validation="lazy">
                      <v-row no-gutters>
                        <v-col cols="12" class="mt-6">
                          <span>ชื่อบริษัท<span style="color: red;">*</span></span>
                          <v-text-field class="input_text" disabled placeholder="ระบุชื่อ" outlined dense v-model="name_th" :rules="Rules.name_th" counter="100" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="6" class="mb-6 pr-4">
                          <span>หมายเลขโทรศัพท์บริษัท<span style="color: red;">*</span></span>
                          <v-text-field class="input_text" placeholder="หมายเลขโทรศัพท์บริษัท" outlined dense v-model="phone" :rules="Rules.tel" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="6" class="mb-6">
                          <span>หมายเลขโทรศัพท์มือถือ</span>
                          <v-text-field class="input_text" placeholder="หมายเลขโทรศัพท์มือถือ" outlined dense v-model="tel" maxlength="10" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                          <span style="font-size: 20px; font-weight: 700;">ข้อมูลที่อยู่บริษัท</span>
                        </v-col>
                        <v-col cols="4" class="pr-5 mt-6">
                          <span>เลขที่<span style="color: red;">*</span></span>
                          <v-text-field class="input_text" placeholder="ระบุเลขที่อยู่" outlined dense v-model="house_no" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,-/]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4" class="pr-5 mt-6">
                          <span>ห้องเลขที่</span>
                          <v-text-field class="input_text" placeholder="ระบุเลขห้อง" outlined dense v-model="room_no" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4 mt-6">
                          <span>ชั้นที่</span>
                          <v-text-field class="input_text" placeholder="ระบุชั้น" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12"></v-col>
                        <v-col cols="4" class="pr-5">
                          <span>อาคาร</span>
                          <v-text-field class="input_text" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" outlined dense v-model="building_name" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4" class="pr-5">
                          <span>หมู่บ้าน</span>
                          <v-text-field class="input_text" placeholder="ชื่อหมู่บ้าน" outlined dense v-model="moo_ban" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4">
                          <span>หมู่ที่</span>
                          <v-text-field class="input_text" placeholder="ระบุหมู่" outlined dense v-model="moo_no" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4" class="pr-5">
                          <span>ตรอก/ซอย</span>
                          <v-text-field class="input_text" placeholder="ระบุตรอก,ซอย" outlined dense v-model="soi" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4" class="pr-5">
                          <span>แยก</span>
                          <v-text-field class="input_text" placeholder="ระบุแยก" outlined dense v-model="yaek" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="4">
                          <span>ถนน</span>
                          <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/, '').replace(/[^a-zA-Zก-๏0-9\s().-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="6" class="pr-5">
                          <span>แขวง/ตำบล<span style="color: red;">*</span></span>
                          <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                          <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="6">
                          <span>เขต/อำเภอ<span style="color: red;">*</span></span>
                          <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                          <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="6" class="pr-5">
                          <span>จังหวัด<span style="color: red;">*</span></span>
                          <addressinput-province label="" v-model="province" placeholder="ระบุจังหวัด" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                          <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="6">
                          <span>รหัสไปรษณีย์<span style="color: red;">*</span></span>
                          <addressinput-zipcode label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                          <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                      </v-row>
                    </v-form>
                  </v-card-text>
                  <!-- Address Mobile -->
                  <v-card-text v-if="MobileSize" class="pa-0">
                    <v-form ref="FormCompanyAddress" :lazy-validation="lazy">
                      <v-row no-gutters>
                        <v-col cols="12" class="mb-4">
                          <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="24" height="24"></v-img>
                          <span style="font-size: 18px; font-weight: 700;">ข้อมูลผู้รับสินค้า</span>
                        </v-col>
                        <v-col cols="12">
                          <span>ชื่อบริษัท<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุชื่อ" disabled outlined dense v-model="name_th" :rules="Rules.name_th" counter="100" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>หมายเลขโทรศัพท์บริษัท<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="หมายเลขโทรศัพท์บริษัท" outlined dense v-model="phone" :rules="Rules.tel" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>หมายเลขโทรศัพท์มือถือ</span>
                        </v-col>
                        <v-col cols="12" class="mb-4">
                          <v-text-field class="input_text" placeholder="หมายเลขโทรศัพท์มือถือ" outlined dense v-model="tel" maxlength="10" oninput="this.value = this.value.replace(/^[\s]/, '').replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" class="mb-4">
                          <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                          <span style="font-size: 18px; font-weight: 700;">ข้อมูลที่อยู่บริษัท</span>
                        </v-col>
                        <v-col cols="12">
                          <span>เลขที่<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุเลขที่อยู่" outlined dense v-model="house_no" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,-/]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" >
                          <span>ห้องเลขที่</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุเลขห้อง" outlined dense v-model="room_no" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>ชั้นที่</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุชั้น" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12"></v-col>
                        <v-col cols="12">
                          <span>อาคาร</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" outlined dense v-model="building_name" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>หมู่บ้าน</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ชื่อหมู่บ้าน" outlined dense v-model="moo_ban" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>หมู่ที่</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุหมู่" outlined dense v-model="moo_no" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>ตรอก/ซอย</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุตรอก,ซอย" outlined dense v-model="soi" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>แยก</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ระบุแยก" outlined dense v-model="yaek" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>ถนน</span>
                        </v-col>
                        <v-col cols="12">
                          <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/, '').replace(/[^a-zA-Zก-๏0-9\s().-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12">
                          <span>แขวง/ตำบล<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                          <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="12">
                          <span>เขต/อำเภอ<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                          <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="12">
                          <span>จังหวัด<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <addressinput-province label="" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                          <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="12">
                          <span>รหัสไปรษณีย์<span style="color: red;">*</span></span>
                        </v-col>
                        <v-col cols="12">
                          <addressinput-zipcode label="" v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                          <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                      </v-row>
                    </v-form>
                  </v-card-text>
                </div>
              </v-card>
            </v-container>
          </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="px-12" style="height: 88px; background-color: #F5FCFB;">
          <v-btn class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">บันทึก</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="px-4" style="height: 88px; background-color: #F5FCFB;">
          <v-btn class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Await Confirm -->
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? 'เพิ่มที่อยู่จัดส่ง' : 'แก้ไขที่อยู่จัดส่ง' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text v-if="!MobileSize">
            <v-row dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' && page === 'CompanyDetail' ? createAddresss() : title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' && page === 'checkout' ? createAddresss() : updateAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
          <v-card-text v-if="MobileSize">
            <v-row dense justify="between">
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' && page === 'CompanyDetail' ? createAddresss() : title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' && page === 'checkout' ? createAddresss() : updateAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Add/Edit Address -->
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogSuccess = !dialogSuccess"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? 'เพิ่มที่อยู่จัดส่งเสร็จสิ้น' : 'แก้ไขที่อยู่จัดส่งเสร็จสิ้น' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? 'คุณได้ทำการเพิ่มที่อยู่จัดส่งเรียบร้อย' : 'คุณได้ทำการแก้ไขที่อยู่จัดส่งเรียบร้อย' }}</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogSuccess = !dialogSuccess">ตกลง</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogSuccess = !dialogSuccess">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  props: ['EditAddressDetail', 'title', 'page'],
  data () {
    return {
      dialogSuccess: false,
      dialogAwaitConfirm: false,
      company_id: '',
      id: '',
      AddressCompanyDialog: false,
      lazy: false,
      name_th: '',
      phone: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      house_no: '',
      room_no: '',
      floor: '',
      building_name: '',
      moo_ban: '',
      moo_no: '',
      soi: '',
      tel: '',
      yaek: '',
      street: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        name_th: [
          v => !!v || 'กรุณากรอกชื่อผู้รับ',
          v => v.length <= 100 || 'กรอกได้ไม่เกิน 100 ตัวอักษร'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ',
          v => v.length <= 20 || 'กรอกได้ไม่เกิน 20 ตัวอักษร'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.charAt(0) === '0' || 'เบอร์โทรศัพท์ควรขึ้นต้นด้วยเลข 0'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่',
          v => (/^[-0-9,/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9,/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        maxText: [
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร'
        ]
        // room_no: [
        //   v => !!v || 'กรุณาระบุห้องเลขที่'
        // ],
        // floor: [
        //   v => !!v || 'กรุณาระบุชั้นที่'
        // ],
        // building_name: [
        //   v => !!v || 'กรุณาระบุอาคาร'
        // ],
        // moo_ban: [
        //   v => !!v || 'กรุณาระบุหมู่บ้าน'
        // ],
        // soi: [
        //   v => !!v || 'กรุณาระบุตรอก/ซอย'
        // ],
        // yaek: [
        //   v => !!v || 'กรุณาระบุแยก'
        // ],
        // street: [
        //   v => !!v || 'กรุณาระบุถนน'
        // ]
      }
    }
  },
  watch: {
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  mounted () {
    this.$EventBus.$on('EditModalCompanyAddress', this.getAddressCompanyData)
  },
  beforeDestroy () {
    this.$EventBus.$off('EditModalCompanyAddress')
  },
  methods: {
    cancel () {
      this.$refs.FormCompanyAddress.resetValidation()
      this.AddressCompanyDialog = false
      if (this.page === 'checkout') {
        this.$EventBus.$emit('closeEditModalAddress')
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    async getAddressCompanyData () {
      var addressData = ''
      this.data = await JSON.parse(Decode.decode(localStorage.getItem('AddressCompanyDetail')))
      addressData = this.data
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // var data = {
      //   role_user: dataRole.role
      // }
      // await this.$store.dispatch('GetUserAddress', data)
      if (localStorage.getItem('AddressCompanyDetail') !== null) {
        // console.log('addressData', addressData)
        if (this.title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า') {
          this.company_id = addressData.company_id
          this.house_no = ''
          this.moo_ban = ''
          this.building_name = ''
          this.street = ''
          this.name_th = addressData.name_th
          this.soi = ''
          this.room_no = ''
          this.floor = ''
          this.moo_no = ''
          this.yaek = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
          this.phone = ''
          this.tel = ''
          this.zipcode = ''
          this.id = addressData.id
          this.AddressCompanyDialog = true
        } else {
          this.name_th = addressData.name_th
          this.house_no = addressData.house_no === '' ? '-' : addressData.house_no
          this.moo_ban = addressData.moo_ban === '' ? '-' : addressData.moo_ban
          this.building_name = addressData.building_name === '' ? '-' : addressData.building_name
          this.street = addressData.street === '' ? '-' : addressData.street
          this.soi = addressData.soi === '' ? '-' : addressData.soi
          this.room_no = addressData.room_no === '' ? '-' : addressData.room_no
          this.floor = addressData.floor === '' ? '-' : addressData.floor
          this.moo_no = addressData.moo_no === '' ? '-' : addressData.moo_no
          this.yaek = addressData.yaek === '' ? '-' : addressData.yaek
          this.subdistrict = addressData.sub_district
          this.district = addressData.district
          this.province = addressData.province
          this.phone = addressData.phone
          this.zipcode = addressData.zipcode
          this.id = addressData.id
          this.tel = addressData.tel
          this.company_id = addressData.company_id
          this.default_address = addressData.default_address
          this.AddressCompanyDialog = true
        }
      }
      this.$store.commit('closeLoader')
    },
    async createAddresss () {
      this.$store.commit('openLoader')
      if (this.page === 'checkout') {
        this.setDefaultAdress()
      }
      if (this.$refs.FormCompanyAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkAddress()
            if (check.length !== 0) {
              var house = 'เลขที่ ' + this.house_no
              var room = this.room_no !== '' ? 'ห้อง ' + this.room_no : ''
              var floor = this.floor !== '' ? 'ชั้นที่ ' + this.floor : ''
              var building = this.building_name !== '' ? 'อาคาร ' + this.building_name : ''
              var mooBan = this.moo_ban !== '' ? 'หมู่บ้าน ' + this.moo_ban : ''
              var mooNo = this.moo_no !== '' ? 'หมู่ที่ ' + this.moo_no : ''
              var soi = this.soi !== '' ? 'ซอย ' + this.soi : ''
              var yaek = this.yaek !== '' ? 'แยก ' + this.yaek : ''
              var street = this.street !== '' ? 'ถนน ' + this.street : ''
              var detail = house + ' ' + room + ' ' + floor + ' ' + building + ' ' + mooBan + ' ' + mooNo + ' ' + soi + ' ' + yaek + ' ' + street
              var addData = {
                company_id: this.company_id,
                detail: detail,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building_name: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                soi: this.soi,
                yaek: this.yaek,
                street: this.street,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                zip_code: this.zipcode,
                phone: this.phone,
                tel: this.tel,
                status: 'Y'
              }
              await this.$store.dispatch('actionsAddAddressCompany', addData)
              const userAddAddress = await this.$store.state.ModuleAdminManage.stateAddAddressCompany
              if (userAddAddress.ok === 'y') {
                this.dialog = false
                this.AddressCompanyDialog = false
                this.$EventBus.$emit('EditAddressCompanyComplete', addData)
                localStorage.removeItem('AddressCompanyDetail')
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
              } else {
                if (userAddAddress.message === 'This user is Unauthorized' || userAddAddress.message === 'This user is unauthorized.' || userAddAddress.message === 'กรุณากรอก token ให้ถูกต้อง') {
                  this.$store.commit('closeLoader')
                  this.$EventBus.$emit('refreshToken')
                } else {
                  this.$store.commit('closeLoader')
                  this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
                }
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    async updateAddress () {
      this.$store.commit('openLoader')
      if (this.$refs.FormCompanyAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkAddress()
            if (check.length !== 0) {
              var house = 'เลขที่ ' + this.house_no
              var room = this.room_no !== '' ? 'ห้อง ' + this.room_no : ''
              var floor = this.floor !== '' ? 'ชั้นที่ ' + this.floor : ''
              var building = this.building_name !== '' ? 'อาคาร ' + this.building_name : ''
              var mooBan = this.moo_ban !== '' ? 'หมู่บ้าน ' + this.moo_ban : ''
              var mooNo = this.moo_no !== '' ? 'หมู่ที่ ' + this.moo_no : ''
              var soi = this.soi !== '' ? 'ซอย ' + this.soi : ''
              var yaek = this.yaek !== '' ? 'แยก ' + this.yaek : ''
              var street = this.street !== '' ? 'ถนน ' + this.street : ''
              var detail = house + ' ' + room + ' ' + floor + ' ' + building + ' ' + mooBan + ' ' + mooNo + ' ' + soi + ' ' + yaek + ' ' + street
              var data = {
                company_id: this.company_id,
                detail: detail,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building_name: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                soi: this.soi,
                yaek: this.yaek,
                street: this.street,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                zip_code: this.zipcode,
                tel: this.tel,
                phone: this.phone,
                id: this.id
              }
              await this.$store.dispatch('actionsUpdateAddressCompany', data)
              const userAddress = await this.$store.state.ModuleAdminManage.stateUpdateAddressCompany
              if (userAddress.ok === 'y') {
                // this.$swal.fire({ icon: 'success', title: 'แก้ไขที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
                // this.$EventBus.$emit('SentGetCart', this.SentGetCart)
                this.dialog = false
                this.AddressCompanyDialog = false
                this.$EventBus.$emit('EditAddressCompanyComplete', data)
                localStorage.removeItem('AddressCompanyDetail')
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
              } else {
                if (userAddress.message === 'This user is Unauthorized' || userAddress.message === 'This user is unauthorized.' || userAddress.message === 'กรุณากรอก token ให้ถูกต้อง') {
                  this.$store.commit('closeLoader')
                  this.$EventBus.$emit('refreshToken')
                } else {
                  this.$store.commit('closeLoader')
                  this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
                }
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipcode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    async getCompanyAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var data = {
        company_id: companyId.company.company_id
      }
      await this.$store.dispatch('actionsGetCompanyAddress', data)
      var res = await this.$store.state.ModuleCart.stateGetCompanyAddress
      if (res.ok === 'y') {
        // console.log(res.query_result)
        this.comAddress = res.query_result
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
      }
    },
    async setDefaultAdress () {
      const defaultAdress = this.defaultAdress === null ? this.defaultAdress === 'N' : this.defaultAdress
      const data = {
        id: this.data.id,
        default_address: defaultAdress
      }
      await this.$store.dispatch('actionDefaultUserAddress', data)
      const res = await this.$store.state.ModuleUser.stateSetDefaultUserAddress
      if (res.message === 'Update default address success') {
        // console.log('set default adress success')
        // this.$swal.fire({ icon: 'success', title: 'ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว', showConfirmButton: false, timer: 1500 })
        // this.getAddress()
      } else {
        // console.log('set default adress fail')
        // this.$swal.fire({ icon: 'error', title: 'ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.th-address-autocomplete {
  width: 100% !important;
  max-height: 200px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}
</style>
