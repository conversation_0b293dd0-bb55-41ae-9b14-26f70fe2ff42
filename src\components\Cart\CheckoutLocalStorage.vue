<template>
  <v-container>
    <v-row dense>
      <v-overlay :value="overlay">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <v-col cols="12" md="12">
        <v-card outlined class="elevation-0">
          <v-container grid-list-xs,sm,md,lg,xl>
            <v-row dense >
              <span class="black--text subtitle-1 font-weight-bold">ที่อยู่ในการจัดส่ง</span>
              <!-- <v-spacer></v-spacer>
              <v-btn text color="primary">เพิ่มที่อยู่ใหม่</v-btn>
              <v-btn text color="primary">จัดการที่อยู่</v-btn> -->
              <v-container grid-list-xs>
                <v-row>
                  <v-col cols="12" md="12" class="text-left">
                    <span class="black--text subtitle-3">{{Address}}</span>
                    <!-- <span class="black--text subtitle-3">นายอภิชาติ คมสัน 0918258185</span>&nbsp;<a text class="pt-0" color="primary">เปลี่ยน</a> -->
                  </v-col>
                </v-row>
              </v-container>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" md="12">
        <v-card outlined class="elevation-0">
          <v-container grid-list-xs>
            <a-table bordered v-for="(item,index) in itemsCart" :key="index" :data-source="item.product_list" :rowKey="record => record.sku" :columns="headers">
              <template slot="title">
                <v-row>
                  <v-col cols="6" class="text-left">
                     <p>{{item.shop_name}}</p>
                  </v-col>
                </v-row>
              </template>
              <template slot="productdetails" slot-scope="text, record">
                <v-row>
                  <v-col cols="12" md="4" class="pr-0 py-1">
                    <v-img :src="record.product_image.url" class="imageshow"/>
                  </v-col>
                  <v-col cols="12" md="8">
                    <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                  </v-col>
                </v-row>
              </template>
              <template slot="price" slot-scope="text, record">
                <v-col cols="12">
                  <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </v-col>
              </template>
              <template slot="net_price" slot-scope="text, record">
                <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
              <template slot="footer">
                <v-row>
                  <!-- <pre>{{item}}</pre> -->
                  <v-col cols="12" md="10" class="text-right">
                    ราคารวมร้านค้า
                  </v-col>
                  <v-col cols="12" md="2" class="text-left">
                    {{ Number(item.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </v-col>
                </v-row>
              </template>
            </a-table>
          </v-container>
        </v-card>
      </v-col>
      <v-col cols="12" md="12">
        <v-card outlined class="elevation-0">
          <v-container grid-list-xs>
            <v-row>
              <v-col cols="12" md="10">
                <v-row dense>
                  <v-col cols="12" class="text-right">
                    <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ราคารวมภาษีมูลค่าเพิ่ม :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span>ค่าจัดส่ง :</span>
                  </v-col>
                  <v-col cols="12" class="text-right">
                    <span class="subheader">ราคารวมทั้งหมด :</span>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="2">
                <v-row dense>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(TotalPriceNoVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(TotalVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(TotalPriceVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(TotalShipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                  <v-col cols="12" class="text-left">
                    <span>{{ Number(TotalNetPrice).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
    <v-row>
      <v-col cols="6" class="text-left pt-5">
        <v-btn color="blue-grey" outlined @click="backstep()">
          ย้อนกลับ
        </v-btn>
      </v-col>
      <v-col cols="6" class="text-right pt-5">
        <v-btn color="primary" outlined @click="confirmCreateOrder()">
          ยืนยันการสั่งซื้อ
        </v-btn>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data: () => ({
    overlay: false,
    cartData: '',
    itemsCart: [],
    DataGetCart: {},
    Address: 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่',
    AddressData: {},
    Fullname: '',
    TotalPriceNoVat: 0,
    TotalVat: 0,
    TotalPriceVat: 0,
    TotalShipping: 0,
    TotalNetPrice: 0
  }),
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '40%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          width: '20%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          key: 'price',
          scopedSlots: { customRender: 'price' },
          width: '20%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          key: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          width: '20%'
        }
      ]
      return headers
    }
  },
  watch: {
  },
  mounted () {
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$on('SentGetAddressLocal')
    })
  },
  async created () {
    this.$EventBus.$on('SentGetAddressLocal', (data) => { this.getAddress(data) })
    var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
    // console.log('cartdata created', cartData)
    if (cartData.product_to_cal.length === 0 || cartData.shop_to_cal.length === 0) {
      this.$router.push('/shoppingcart')
    } else {
      this.getCart()
    }
  },
  methods: {
    async getCart () {
      var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
      var data = {
        shop_to_cal: cartData.shop_to_cal,
        product_to_cal: cartData.product_to_cal,
        shop_list: cartData.shop_list,
        address_data: [this.AddressData]
      }
      await this.$store.dispatch('ActionLocalstorageGetCart', data)
      var res = await this.$store.state.ModuleCart.stateLocalstorageGetCart
      // console.log('res get cart', res.data)
      if (res.message === 'Get localstorage get cart success') {
        this.DataGetCart = res.data
        this.overlay = false
        this.itemsCart = res.data.choose_list
        this.TotalPriceNoVat = res.data.total_price_no_vat
        this.TotalVat = res.data.total_vat
        this.TotalPriceVat = res.data.total_price_vat
        this.TotalShipping = res.data.total_shipping
        this.TotalNetPrice = res.data.total_net_price
      }
    },
    async getCartUpdate (cartData) {
      var data = {
        shop_to_cal: cartData.shop_to_cal,
        product_to_cal: cartData.product_to_cal,
        shop_list: cartData.shop_list,
        address_data: [this.AddressData]
      }
      await this.$store.dispatch('ActionLocalstorageGetCart', data)
      var res = await this.$store.state.ModuleCart.stateLocalstorageGetCart
      // console.log('res get cart', res.data)
      if (res.message === 'Get localstorage get cart success') {
        this.DataGetCart = res.data
        this.overlay = false
        this.itemsCart = res.data.choose_list
        this.TotalPriceNoVat = res.data.total_price_no_vat
        this.TotalVat = res.data.total_vat
        this.TotalPriceVat = res.data.total_price_vat
        this.TotalShipping = res.data.total_shipping
        this.TotalNetPrice = res.data.total_net_price
      }
    },
    backstep () {
      this.$router.push('/shoppingcart')
    },
    getAddress (val) {
      this.AddressData = val
      this.Address = val.first_name + ' ' + val.address_detail + ' ' + val.house_no + ' ' + val.sub_district + ' ' + val.district + ' ' + val.province + ' ' + val.zipcode + ' ' + val.phone
      this.getCart()
    },
    confirmCreateOrder () {
      // const ToastDelete = this.$swal.mixin({
      //   toast: true,
      //   showCancelButton: true,
      //   confirmButtonText: 'ยืนยัน',
      //   cancelButtonText: 'ยกเลิก',
      //   cancelButtonColor: '#d33'
      // })
      this.$swal.fire({
        toast: true,
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        cancelButtonColor: '#d33',
        icon: 'warning',
        title: 'ยืนยันการสั่งซื้อหรือไม่'
      }).then((result) => {
        if (result.isConfirmed) {
          this.CreateOrder()
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    async CreateOrder () {
      this.overlay = true
      await this.$store.dispatch('ActionLocalstorageCreateOrder', this.DataGetCart)
      var res = await this.$store.state.ModuleCart.stateLocalstorageCreateOrder
      // console.log('response create order', res)
      this.overlay = false
      if (res.message === 'Create Order success') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'สั่งสินค้าเรียบร้อย'
        })
        var setData = {
          product_to_cal: res.data.product_to_cal,
          shop_to_cal: res.data.shop_to_cal,
          address_data: {},
          shop_list: res.data.shop_list
        }
        await localStorage.setItem('_cartData', Encode.encode(setData))
        var dataPayment = {
          payment_transaction_number: res.data.payment_transaction_number
        }
        // console.log('BeforePayment', res.data)
        await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
        var resRedirect = this.$store.state.ModuleCart.stateGetPaymentPage
        this.overlay = false
        this.$EventBus.$emit('getCartPopOver')
        localStorage.setItem('PaymentData', Encode.encode(resRedirect))
        this.$router.push('/RedirectPaymentPage')
      } else if (res.message === 'Not enough product in stock') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: `สินค้า ${res.data[0].product} สามารถซื้อได้ ${res.data[0].quantity} ชิ้นเท่านั้น`
        })
        this.Cancel()
      } else if (res.message === 'Update New Price') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          title: 'ระบบมีการอัปเดตสินค้าใหม่'
        })
        this.getCartUpdate(res.data)
      }
    },
    Cancel () {
      this.$router.push({ path: '/shoppingcart' })
    }
  }
}
</script>

<style scoped>
.imageshow {
    width: 50px;
    height: 50px;
}
</style>
