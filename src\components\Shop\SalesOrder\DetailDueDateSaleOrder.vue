<template>
  <div>
    <v-card>
      <v-card-title style="font-weight: bold; font-size: 28px; line-height: 32px; color: #333333;" >
        <v-icon color="#27AB9C" class="mr-2" @click="backToOrderSales()">mdi-chevron-left</v-icon>
        รายละเอียดวันที่ครบกำหนดชำระ
      </v-card-title>
      <v-card-text>
        <v-container>
          <v-data-table
            :headers="headersAll"
            :items="DataTable"
            :search="search"
            :page.sync="page"
            style="width: 100%"
            height="100%"
            @pagination="countOrdar"
            :items-per-page="10"
            class=""
            no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
            :footer-props="{ 'items-per-page-text': 'จำนวนแถว' }"
          >
            <template v-slot:[`item.order_number`]="{ item }">
              <span>{{ item.order_number }}</span>
            </template>
            <template v-slot:[`item.id`]="{ index }">
              <span>{{ index + 1 }}</span>
            </template>
            <template v-slot:[`item.payment_credit_term_number`]="{ item }">
              <span>{{ item.payment_credit_term_number }}</span>
            </template>
            <template v-slot:[`item.credit_term`]="{ item }">
              <span>{{ item.credit_term }}</span>
            </template>
            <template v-slot:[`item.invoice_start_date`]="{ item }">
              <span>{{new Date(item.invoice_start_date).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</span>
            </template>
            <template v-slot:[`item.invoice_end_date`]="{ item }">
              <span>{{new Date(item.invoice_end_date).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</span>
            </template>
            <template v-slot:[`item.invoice_due_date`]="{ item }">
              <span>{{new Date(item.invoice_due_date).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</span>
            </template>
            <template v-slot:[`item.total_amount`]="{ item }">
              <span>{{ item.total_amount }}</span>
            </template>
            <template v-slot:[`item.invoice_paper`]="{ item }">
              <v-btn
                x-small
                @click="GetETaxPDF(item)"
                style="
                  border: 1px solid #f2f2f2;
                  box-sizing: border-box;
                  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                  border-radius: 4px;
                "
                :style="
                  IpadProSize
                    ? 'max-width: 24px; max-height: 24px;'
                    : IpadSize
                    ? 'max-width: 16px; max-height: 16px;'
                    : 'max-width: 32px; max-height: 32px;'
                "
                class="pt-4 pb-4"
              >
                <v-icon color="#27AB9C">mdi-file-document</v-icon>
              </v-btn>
            </template>
            <template v-slot:[`item.paid_datetime`]="{ item }">
              <span v-if="item.paid_datetime !== null">{{new Date(item.paid_datetime).toLocaleDateString("th-TH", { timeZone: 'UTC', year: "numeric", month: "long", day: "numeric" })}}</span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.payment`]="{ item }">
              <!-- <v-btn :disabled="item.can_pay === 'no'"  @click="getQrCodePayment(item.payment_credit_term_number)" color="#27AB9C" >
                <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                <span class="pt-1 pl-2" style="color:#ffff;">ชำระเงิน</span>
              </v-btn> -->
              <v-btn :disabled="item.can_pay === 'no'"  @click="getQrCodePayment(item.payment_credit_term_number)" :color="item.is_late === 'no' ? '#27AB9C' : 'red'" >
                <v-img class="sizeIMGMobile" src="@/assets/payment.png"></v-img>
                <span class="pt-1 pl-2" style="color:#ffff;">ชำระเงิน</span>
              </v-btn>
            </template>
          </v-data-table>
        </v-container>
      </v-card-text>
    </v-card>
    <v-dialog v-model="DialogQR" persistent :width="MobileSize ? '100%' : '640'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
            style="position: absolute; height: 120px; ">
            <v-row style="height: 120px; ">
              <v-col style="text-align: center;" class="pt-4">
                <span style="margin-left: 47px"
                  :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>สแกน QR Code
                    ชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                  color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '640px'"
              style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
              style="background: #FFFFFF; border-radius: 20px;">
              <div style="text-align: center;">
                <!-- <v-img height="280" width="280" style="margin-inline: auto;" :src="Image"></v-img>
                <v-btn @click="saveQRCode()" color="#27AB9C" rounded width="125" height="40" class="white--text my-8">บันทึกรูปภาพ</v-btn> -->
                <v-col class="py-0">
                  <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="Image"/>
                </v-col>
                <v-col class="py-0">
                  <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                </v-col>
                <div>
                  <v-col>
                    <span style="font-size: 20px; font-weight: 700;">ยอดชำระเงินจำนวน : {{ Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
                      บาท</span>
                  </v-col>
                  <v-col>
                    <span style="font-size: 14px; font-weight: 400;">รหัสอ้างอิง {{Ref1}}</span>
                  </v-col>
                  <v-col class="py-0">
                    <span style="font-size: 14px; font-weight: 600; color: #A1A1A1;">สามารถชำระเงินได้ตามขั้นตอนนี้
                      (กรณีชำระเงินผ่านมือถือ)</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">2. เปิดแอปธนาคารของท่าน
                      และเลือกเมนูสแกน QR Code</span>
                  </v-col>
                  <v-col class="text-left py-0">
                    <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                      Code</span>
                  </v-col>
                </div>
              </div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      TypeOS: '',
      MobileSize: false,
      disableTable: false,
      TransactionNumber: '',
      payment_transaction: '',
      netPrice: '',
      Ref1: '',
      Ref2: '',
      imageBase64: '',
      Image: '',
      DialogQR: false,
      DataTable: [],
      headersAll: [
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'order_number',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'งวด',
          value: 'id',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '125px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ใบแจ้งหนี้',
          value: 'payment_credit_term_number',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เครดิตเทอม',
          value: 'credit_term',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '125px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันเริ่มต้น',
          value: 'invoice_start_date',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันสิ้นสุด',
          value: 'invoice_end_date',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันครบกำหนด',
          value: 'invoice_due_date',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จำนวนเงิน',
          value: 'total_amount',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบแจ้งหนี้',
          value: 'invoice_paper',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '125px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่ชำระเงิน',
          value: 'paid_datetime',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ชำระเงิน',
          value: 'payment',
          align: 'center',
          filterable: false,
          sortable: false,
          width: '125px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ]
    }
  },
  mounted () {
    this.checkMobileSize()
    window.addEventListener('resize', this.checkMobileSize)
  },
  created () {
    window.scrollTo(0, 0)
    localStorage.setItem('sale_order', 'saleorder')
    this.TypeOS = this.detectOS()
    this.getDetailCreditTerm()
  },
  methods: {
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    closeDialogQR () {
      this.DialogQR = false
    },
    async showIMG (ImageQR) {
      // console.log('ImageQR', ImageQR)
      this.Image = ImageQR
      // console.log('ImageQR', ImageQR)
      this.DialogQR = true
      this.$store.commit('closeLoader')
      var data
      data = {
        payment_transaction_number: this.payment_transaction
      }
      var value = data.payment_transaction_number
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'การชำระเงินไม่เสร็จสมบูรณ์'
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    async getDetailCreditTerm () {
      this.$store.commit('openLoader')
      this.TransactionNumber = this.$route.query.Id
      var data = {
        payment_transaction_number: this.TransactionNumber
      }
      await this.$store.dispatch('actionsGetDetailCreditTerm', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetDetailCreditTerm
      if (res.message === 'Get Order Credit Term Success') {
        this.$store.commit('closeLoader')
        this.DataTable = await res.data
        if (this.DataTable.length > 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            icon: 'error',
            text: `${res.message}`,
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    async getQrCodePayment (val) {
      this.$store.commit('openLoader')
      this.payment_transaction = val
      var data = {
        payment_transaction_number: val
      }
      await this.$store.dispatch('actionsGetQrCodePayment', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetQrCodePayment
      if (res.result === 'SUCCESS') {
        this.netPrice = res.data.net_price
        this.Ref1 = res.data.ref1
        this.Ref2 = res.data.ref2
        this.imageBase64 = 'data:image/png;base64,' + res.data.img_base
        this.ImageQR = ''
        this.ImageQR = await res.data.img_base64
        setTimeout(() => {
          this.showIMG(this.ImageQR)
        }, 1000)
      } else if (res.result === 'FAILED') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ERROR ไม่สามารถชำระเงินได้'
        })
        this.DialogQR = false
        this.dialogConfirm = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.DialogQR = false
        this.dialogConfirm = false
        // }
        // this.$store.commit('closeLoader')
      }
      // console.log('res', res)
    },
    checkMobileSize () {
      if (window.innerWidth < 768) {
        this.MobileSize = true
      } else {
        this.MobileSize = false
      }
    },
    backToOrderSales () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/orderSales' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderSalesMobile' }).catch(() => {})
      }
    }
  }
}
</script>
