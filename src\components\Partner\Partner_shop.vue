<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">ร้านค้าคู่ค้า</v-card-title>
      <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> ร้านค้าคู่ค้า</v-card-title>
      <v-row no-gutters class="px-2 pt-0 py-6">
        <v-col cols="12" class="py-0">
          <a-tabs @change="getOrderReturn" class="changeBorderbottom">
            <!-- <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countall }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countsuccess }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countwaiting }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">ปฏิเสธ <a-tag color="#F5222D" style="border-radius: 8px;">{{ countreject }}</a-tag></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#E6E6E6" style="border-radius: 8px;">{{ countfailed }}</a-tag></span></a-tab-pane> -->
            <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)">{{ countall }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <v-chip small text-color="#FAAD14" color="#FEF6E6">{{ countwaiting }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <v-chip small text-color="#52C41A" color="#F0FEE8">{{ countsuccess }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="3"><span slot="tab">ปฏิเสธ <v-chip small text-color="#F5222D" color="rgba(245, 34, 45, 0.10)">{{ countreject }}</v-chip></span></a-tab-pane>
            <a-tab-pane :key="4"><span slot="tab">ยกเลิก <v-chip small text-color="#636363" color="#E6E6E6">{{ countfailed }}</v-chip></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col cols="12" v-if="disableTable === false">
          <v-row dense>
            <v-col cols="12" md="4" sm="12" class="pt-4">
              <span v-if="select === 0" style="font-weight: 400; font-size: 16px; line-height: 22px; color:  #333333;">
                รายชื่อร้านค้าคู่ค้าทั้งหมด {{ showCountOrder }} รายการ</span>
              <span v-else-if="select === 2" style="font-weight: 400; font-size: 16px; line-height: 22px; color:  #333333;">
                รายชื่อร้านค้าคู่ค้าที่อนุมัติแล้ว {{ showCountOrder }} รายการ</span>
              <span v-else-if="select === 1" style="font-weight: 400; font-size: 16px; line-height: 22px; color:  #333333;">
                รายชื่อร้านค้าคู่ค้าที่รออนุมัติ {{ showCountOrder }} รายการ</span>
              <span v-else-if="select === 3" style="font-weight: 400; font-size: 16px; line-height: 22px; color:  #333333;">
                รายชื่อร้านค้าคู่ค้าที่ปฏิเสธ {{ showCountOrder }} รายการ</span>
              <span v-else-if="select === 4" style="font-weight: 400; font-size: 16px; line-height: 22px; color:  #333333;">
                รายชื่อร้านค้าคู่ค้าที่ยกเลิก {{ showCountOrder }} รายการ</span>
            </v-col>
            <v-col cols="12" md="4" sm="12">
              <v-text-field v-model="search" style="border-radius: 8px;" append-icon="mdi-magnify"
              placeholder="ค้นหาจากชื่อร้านค้า" outlined dense hide-details
              >
            </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="12" class="pt-2">
              <v-row dense>
                <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pt-2">วันที่ยื่นคำขอ :</span>
                <v-dialog
                  ref="dialog"
                  v-model="modal"
                  :return-value.sync="date"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field v-model="DateSearch" v-on="on" v-bind="attrs" readonly dense outlined class="pl-4" hide-details style="border-radius: 8px;" :style="!IpadSize && !MobileSize ? 'max-width: 230px' : 'max-width: 100%'" placeholder="วว/ดด/ปปปป">
                      <template v-slot:append>
                        <v-icon>mdi-calendar-month</v-icon>
                      </template>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="date"
                    scrollable
                    locale="th-TH"
                  >
                    <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="closeFilterDate()"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="ChangeFilterDate(date)"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" class="py-2">
          <v-data-table v-if="disableTable === false" @pagination="countOrdar" v-model="selected" :headers="headers" :items="filterDateData" :search="search" color="blue"
            class="elevation-1 mt-4" no-data-text="ไม่มีรายชื่อร้านค้าที่เป็นคู่ค้าองค์กรในตาราง" no-results-text="ไม่พบรายชื่อร้านค้าคู่ค้าที่รออนุมัติในตาราง" :footer-props="{'items-per-page-text':'จำนวนแถว'}">
            <template v-slot:[`item.shop_name_th`]="{ item }">
              <span><a :href="item.shop_url" color="#E9A016"><U>{{ item.shop_name_th }}</U></a></span>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'request'">
                <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
              </span>
              <span v-else-if="item.status === 'active'">
                <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">อนุมัติแล้ว</v-chip>
              </span>
              <span v-else-if="item.status === 'reject'">
                <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#FBE5E4" text-color="#F5222D">ปฏิเสธ</v-chip>
              </span>
              <span v-else-if="item.status === 'inactive'">
                <v-chip :class="!MobileSize? 'ma-2' : 'ma-0'" color="#FBE5E4" text-color="#F5222D">ยกเลิก</v-chip>
              </span>
            </template>
            <template v-slot:[`item.manages`]="{ item }">
              <!-- <v-row> -->
              <v-btn x-small @click="EditPartner(item)"
                style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                class="pt-4 pb-4">
                <v-icon color="#27AB9C">mdi-file-document</v-icon>
              </v-btn>
              <v-btn text rounded color="#27AB9C" small @click="EditPartner(item)">
                  <b>รายละเอียด</b>
                  <v-icon color="#27AB9C">mdi-chevron-right</v-icon>
                </v-btn>
                <!-- <v-btn outlined color="#27AB9C" @click="EditPartner(item)">
                  รายละเอียด
                </v-btn> -->
              <!-- </v-row> -->
            </template>
          </v-data-table>
          <v-row>
            <v-col cols="12" v-if="disableTable === true" align="center">
              <div class="my-5">
                <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="select === 0">
                <b>คุณยังไม่มีรายการร้านค้าคู่ค้า</b>
              </h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="select === 1">
                <b>คุณยังไม่มีรายการร้านค้าคู่ค้าที่อนุมัติแล้ว</b>
              </h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="select === 2">
                <b>คุณยังไม่มีรายการร้านค้าคู่ค้าที่รออนุมัติ</b>
              </h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="select === 3">
                <b>คุณยังไม่มีรายการร้านค้าคู่ค้าที่ปฏิเสธ</b>
              </h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="select === 4">
                <b>คุณยังไม่มีรายการร้านค้าคู่ค้าที่ยกเลิก</b>
              </h2>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <!-- Dialog แสดงรายละเอียดใหม่ -->
      <v-dialog v-model="dialog" :width="MobileSize ? '100%' : IpadSize ? '100%' : '760'" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 760px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ checkstatus !== 6 && checkstatus !== 1 ? 'ร้านค้าคู่ค้า' : 'ยื่นคำขอเป็นคู่ค้า' }}</b></span>
                </v-col>
                <v-btn fab small @click="dialog = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '760px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; padding: 40px 48px 10px 48px; border-radius: 20px 20px 0px 0px;">
                <v-card-text class="pa-0">
                  <v-row dense>
                    <v-col cols="12" align="end" v-if="checkstatus !== 6 && checkstatus !== 1">
                      <v-btn small color="#27AB9C" outlined @click="EditVenderDetail(companyIDDetail, sellerShopIDDetail)">เพิ่ม/แก้ไข</v-btn>
                      <p style="color: red; font-size: 10px;">* กรณีที่มีการเพิ่มเติม Vendor</p>
                    </v-col>
                    <v-col cols="12" class="d-flex">
                      <v-row dense class="mr-auto">
                        <v-img src="@/assets/Create_Store/partnerCompany.png" max-height="62" max-width="62"></v-img>
                        <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 16px;"> รายละเอียดการขอเป็นคู่ค้า </span>
                      </v-row>
                      <span class="ml-auto" style="text-align: end;">
                        <span style="font-weight: 400; color: #333333; font-size: 16px;">สถานะ :
                          <span v-if="checkstatus === 1">
                            <v-chip color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
                          </span>
                          <span v-else-if="checkstatus === 5">
                            <v-chip color="#F0F9EE" text-color="#1AB759">อนุมัติแล้ว</v-chip>
                          </span>
                          <span v-else-if="checkstatus === 6">
                            <v-chip color="#FBE5E4" text-color="#F5222D">ปฏิเสธ</v-chip>
                          </span>
                          <span v-else-if="checkstatus === 0">
                            <v-chip color="#FBE5E4" text-color="#F5222D">ยกเลิก</v-chip>
                          </span>
                        </span><br/>
                        <span class="mt-2" style="font-weight: 400; font-size: 16px; color: #333333;">วันที่อัปเดต : {{ new Date(dateUp).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                      </span>
                    </v-col>
                    <v-col cols="12" class="mt-5 px-4 py-3" style="border-radius: 8px; background: #FAFAFA;" v-if="reason !== null && reason !== ''">
                      <span style="font-weight: 400; font-size: 16px; color: #333333;">หมายเหตุ : <b>{{ reason }}</b></span>
                    </v-col>
                  </v-row>
                  <v-row dense class="py-4">
                    <v-col cols="12">
                      <v-divider></v-divider>
                    </v-col>
                  </v-row>
                  <v-row dense>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">รายละเอียดคู่ค้า</span>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                    </v-col>
                    <v-col cols="12" md="8" sm="8">
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">ร้านค้า :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ name }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">วันที่ยื่นคำขอ :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ new Date(dateUp).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' }) }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">กลุ่มคู่ค้า :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ businessType }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">รูปแบบการนับเครดิตเทอม :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ type_credit_term }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">ผู้ดูแลลูกค้า (sales) :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">-</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">E-mail ผู้ดูแลลูกค้า :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ email }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">Vendor Code :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ venderCode }}</span>
                        </v-col>
                      </v-row>
                      <v-row dense>
                        <v-col cols="5">
                          <span class="fontStyleTitle">Vendor Name :</span>
                        </v-col>
                        <v-col cols="7">
                          <span class="fontStyleData">{{ venderName }}</span>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col v-if="document_list !== null" cols="12" md="12">
                      <v-card v-for="(item,index) in document_list" :key="index" class="mt-6 rounded-lg">
                        <v-row class="pa-2">
                          <v-col cols="3" md="2" xs="12" sm="3">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="55px" height="55px" contain>
                            </v-img>
                          </v-col>
                          <v-col cols="9" md="7" xs="12" sm="8">
                            <v-layout row wrap align-center :style="MobileSize ? 'margin-top:6px' : 'margin-top:12px'">
                              <v-flex>
                                <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{
                                item.name_document}}</span><br />
                              </v-flex>
                            </v-layout>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                  <v-row dense class="py-4">
                    <v-col cols="12">
                      <v-divider></v-divider>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions v-if="checkstatus === 6 || checkstatus === 1 " align="right">
            <v-container>
              <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="cancelModal()">
                ย้อนกลับ
              </v-btn>
              <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="success()">
                ยกเลิกคำขอ
              </v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- dialog  ขื้นเตือนว่าจะบันทึกหรือไม่-->
      <v-dialog v-model="dialogSuccess" width="400" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card align="center" class="rounded-lg">
          <v-toolbar color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ยกเลิกการยื่นคำขอเป็นคู่ค้า</font>
            </span>
            <v-btn icon dark @click="dialogSuccess = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <br /><br />
          <v-card-text>
            <span>
              คุณได้ทำการยกเลิกคำขออนุมัติคู่ค้า<br />
              คุณต้องการทำรายการนี้ ใช่หรือไม่
            </span>
          </v-card-text>
          <v-card-actions>
            <v-container>
              <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">ยกเลิก</v-btn>
              <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="postDetailShop()">ตกลง</v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <!-- dialog  ขึ้นมาตอนสำเร็จ -->
      <v-dialog v-model="save_success" width="400" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card align="center" class="rounded-lg">
          <v-toolbar color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ยื่นคำขอเป็นคู่ค้า</font>
            </span>
            <v-btn icon dark @click="save_success = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <br /><br />
          <v-card-text>
            <v-img src="@/assets/Create_Store/Vector.png" width="70" height="70"></v-img>
            <br />
            <h3 style="font-weight: 600; font-size: 20px; line-height: 22px; color: #27AB9C;">สำเร็จ</h3>
          </v-card-text>
        </v-card>
      </v-dialog>
      <!-- dialog  ยกเลิกคำขอสำเร็จ -->
      <v-dialog v-model="cancel_success" width="400" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card align="center" class="rounded-lg">
          <v-toolbar color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ยกเลิกการยื่นคำขอ</font>
            </span>
            <v-btn icon dark @click="cancel_success = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <br /><br />
          <v-card-text>
            <v-img src="@/assets/Create_Store/Vector.png" width="70" height="70"></v-img>
            <br />
            <h3 style="font-weight: 600; font-size: 20px; line-height: 22px; color: #27AB9C;">สำเร็จ</h3>
          </v-card-text>
        </v-card>
      </v-dialog>

      <!-- dialog  แก้ไข partner ในตอนที่ถูกปฏิเสธ -->
      <v-dialog v-model="dialogEdit" width="732px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
        <v-card>
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">แก้ไขคำขอเป็นคู่ค้า</font>
            </span>
            <v-btn icon dark @click="dialogEdit = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card-text>
            <v-row class="mt-5">
              <v-col cols="12" md="2" class="mr-0">
                <v-avatar rounded size="72">
                  <v-img src="@/assets/ImageINET-Marketplace/Shop/Store2.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12" md="10">
                <v-row dense no-gutters justify="start">
                  <v-col cols="12" md="12" sm="12" xs="12">
                    <p class="mt-5" style="font-weight: bold; font-size: 20px; text-transform: uppercase;">{{name}}</p>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <div class="pt-5" v-if="this.response_doc_array.length !== 0">
              <span style="font-weight: 400; font-size: 16px; line-height: 22px; color: #333333;">เลือกกลุ่มลูกค้า</span>
              <v-select v-model="selectgroup" @change="onChange" :items="this.doc" return-object item-text="tier_name"
                outlined dense placeholder="ระบุกลุ่มลูกค้า"></v-select>
            </div>
            <div class="pt-5" v-else style="font-weight: 400; font-size: 16px; line-height: 24px; text-align: center;">
              <span>ไม่มีเอกสารในการยื่นขอเป็นคู่ค้า</span>
              <!-- <span style="font-weight: 400; font-size: 16px; line-height: 22px; color: red;"> กรุณาเพิ่มรายการเอกสารขอเป็นคู่ค้า</span> -->
            </div>
            <div v-for="(item, index) in  datapartner " :key="index">
              <v-card class="mt-4 rounded-lg">
                <v-row class="pa-2">
                  <v-col cols="12" md="2" xs="12" sm="12">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="55px" height="55px" contain>
                    </v-img>
                  </v-col>
                  <v-col cols="12" md="7" xs="12" sm="12">
                    <v-layout row wrap align-center style="margin-top:10px">
                      <v-flex>
                        <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">{{
                        item.name_document
                        }}</span>
                      </v-flex>
                    </v-layout>
                  </v-col>
                  <v-col cols="12" md="3" xs="12" sm="12">
                    <v-btn v-if="!item.InputFile && datapartner[index].status === false" dense dark outlined
                      color="#27AB9C" @click="show_inputimagse(index, item)" style="height:35px;margin-top:5px">
                      <v-icon small color="#27AB9C">mdi-plus</v-icon>อัปโหลดไฟล์
                    </v-btn>
                    <v-row v-if="item.InputFile && datapartner[index].status === false"
                      class="d-flex justify-center mt-2 pr-4">
                      <v-btn icon @click="show_inputimagse(index, item)">
                        <v-icon color="green">mdi-check-circle</v-icon>
                      </v-btn>
                    </v-row>
                    <v-row v-if="datapartner[index].status === true" class="d-flex justify-end mt-2 pr-4">
                      <v-btn icon>
                        <v-icon @click="openToPDF(index)">mdi-eye</v-icon>
                      </v-btn>
                      <v-btn @click="delete_file(index)" icon>
                        <v-icon>mdi-delete</v-icon>
                      </v-btn>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card>
              <v-card @click="selectIndex(index)" class="mt-10 rounded-lg" v-show="item.InputFile">
                <v-container>
                  <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                    @click="onPickFile()">
                    <v-card-text>
                      <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                        <v-file-input v-model="DataFile" :items="DataFile" accept=".pdf" @change="UploadFile($event)"
                          id="file_input" multiple :clearable="false" style="display:none">
                        </v-file-input>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="280.34"
                              height="154.87" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-6">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="6" style="text-align: center;">
                              <span
                                style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br />
                              <span
                                style="font-size: 16px; line-height: 24px; font-weight: 40;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .PDF
                                ขนาดไม่เกิน
                                5 mb)</span><br />
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-container>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions>
            <v-container style="display: flex; justify-content: flex-end">
              <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">
                ยกเลิก
              </v-btn>
              <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="SaveEditPartner()">
                บันทึก
              </v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialog_pdf" width="1000px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent scrollable>
        <v-card>
          <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
            <span class="flex text-center ml-5" style="font-size:20px">
              <font color="#27AB9C">ตัวอย่างเอกสาร</font>
            </span>
            <v-btn icon dark @click="dialog_pdf = false">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <object width="1000" height="700" :data="pdftofile" type="application/pdf"></object>
        </v-card>
      </v-dialog>
      <!-- Dialog update vender -->
      <v-dialog v-model="dialogUpdateVender" width="484px" :style="MobileSize ? 'z-index: 16000004; border-radius: 8px;' : 'border-radius: 8px;'" persistent scrollable>
        <v-form ref="formUpdateVender" :lazy-validation="lazy">
          <v-card style="border-radius: 8px;">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
              <span class="flex text-center ml-5" style="font-size:20px">
                <font color="#27AB9C">เพิ่ม / แก้ไขข้อมูล Vendor</font>
              </span>
              <v-btn icon dark @click="dialogUpdateVender = false">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card-text>
              <v-row dense>
                <v-col cols="12">
                  <span class="fontStyleTitle">Vendor Code :</span>
                  <v-text-field v-model="textVenderCode" dense outlined placeholder="ระบุ Vendor Code" disabled></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="fontStyleTitle">Vendor Name :</span>
                  <!-- <v-text-field v-model="textVenderName" dense outlined placeholder="ระบุ Vendor Name" :maxLength="100" oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏-\s]/g, '')"></v-text-field> -->
                  <v-autocomplete v-model="textVenderName" :items="listVendor" item-text="vendor_name_th" item-value="vendor_name_th" label="Vendor Name" solo dense no-data-text="ไม่มีข้อมูล Vendor Name"></v-autocomplete>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-container style="display: flex; justify-content: flex-end">
                <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="dialogUpdateVender = false">
                  ยกเลิก
                </v-btn>
                <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="UpdateVender()">
                  บันทึก
                </v-btn>
              </v-container>
            </v-card-actions>
          </v-card>
        </v-form>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      lazy: false,
      DateSearch: '',
      DateToSearch: '',
      selected: null,
      modal: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      shopImg: '',
      businessType: '',
      dialog_pdf: false,
      pdftofile: '',
      email: '',
      name: '',
      dateUp: '',
      dateCreate: '',
      reason: null,
      document_list: null,
      response: [],
      tier_name: '-',
      type_credit_term: '-',
      showCountOrder: 0,
      select: 0,
      Detail: {
        product_file: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      i: 0,
      res: [
        {
          data: [
            {
              id: 5,
              business_id: null,
              seller_shop_id: null,
              company_id: null,
              customer_id: null,
              tier_id: null,
              credit: null,
              credit_term: null,
              num_of_credit_term: null,
              document_list: null,
              reason: null,
              status: null,
              created_by: null,
              updated_by: null,
              created_at: null,
              updated_at: null,
              shop_name_th: null,
              shop_name_en: null
            }
          ]
        }
      ],
      datapartner: [
        // {
        //   name_document: 'เอกสารสำเนาหนังสือรับรองบริษัท/ หจก',
        //   InputFile: false,
        //   status: false
        // },
        // {
        //   name_document: 'เอกสาร สำเนาใบทะเบียนภาษีมูลค่าเพิ่ม ภ.พ.20',
        //   InputFile: false,
        //   status: false
        // }
      ],
      search: '',
      doc: [],
      selectgroup: [],
      dialog: false,
      response_doc: '',
      response_doc_tier_id: '-',
      response_doc_array: 0,
      countall: 0,
      countsuccess: 0,
      countfailed: 0,
      countreject: 0,
      countwaiting: 0,
      quantity: 0,
      checkstatus: 5,
      dialogEdit: false,
      dialogSuccess: false,
      save_success: false,
      cancel_success: false,
      headers: [
        { text: 'ร้านค้า', value: 'shop_name_th', width: '350', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่ยื่นคำขอ', value: 'created_at', width: '200', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'manages', filterable: false, width: '200', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      data: [
        {
          name: 'ทองหล่อเจริญพาณิชย์',
          datetime: '24 พฤษภาคม 2564',
          status: 5
        }
      ],
      disableTable: false,
      venderCode: '',
      venderName: '',
      companyIDDetail: '',
      sellerShopIDDetail: '',
      textVenderCode: '',
      textVenderName: '',
      dialogUpdateVender: false,
      itemtoEdit: [],
      listVendor: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    },
    filterDateData () {
      if (this.DateToSearch !== '') {
        return this.data.filter(element => {
          return element.createdForSearch.substr(0, 10).includes(this.DateToSearch)
        })
      } else {
        return this.data
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/PartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Partner' }).catch(() => {})
      }
    },
    textVenderName (val) {
      // console.log('val---->', val)
      this.textVenderName = val
      var data = this.listVendor.find(item => item.vendor_name_th === this.textVenderName)
      // console.log('data', data)
      if (data !== undefined) {
        this.textVenderCode = data.vendor_code
      } else {
        this.$swal.fire({ icon: 'warning', text: 'Vendor Name ไม่พบในระบบ PR กรุณาเลือกใหม่อีกครั้ง', showConfirmButton: false, timer: 2500, toast: true })
        this.textVenderName = ''
        this.textVenderCode = ''
      }
    }
  },
  async created () {
    // this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' })
    } else {
      this.$EventBus.$emit('changeNavCompany')
      this.getListVendor()
      this.getListDate()
    }
  },
  methods: {
    async getListVendor () {
      // console.log('item', item)
      var companyData
      if (localStorage.getItem('CompanyData') !== null) {
        companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      }
      var item = companyData.tax_id
      await this.$store.dispatch('actionsListVenderPR', item)
      var response = await this.$store.state.ModulePartner.stateListVenderPR
      if (response.message === 'Show item&vendor success') {
        if (response.data.vendor.length !== 0 && response.data.item.length !== 0) {
          this.listVendor = response.data.vendor
        }
      }
    },
    ChangeFilterDate (val) {
      this.$refs.dialog.save(val)
      this.DateSearch = this.formatDateToShow(val)
      this.DateToSearch = val
    },
    closeFilterDate () {
      this.modal = false
      this.DateSearch = ''
      this.DateToSearch = ''
      this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    onChange () {
      this.datapartner = this.selectgroup.document
      // console.log('testao1', this.datapartner)
    },
    delete_file (index) {
      this.Detail.product_file[index] = ''
      // if (this.selectgroup.length !== 0) {
      //   this.selectgroup.document[index].status = false
      //   this.selectgroup.document[index].InputFile = false
      // } else {
      this.datapartner[index].status = false
      this.datapartner[index].InputFile = false
      // }
    },
    openToPDF (index) {
      this.dialog_pdf = true
      this.pdftofile = 'data:application/pdf;base64,' + this.Detail.product_file[index].file
    },
    selectIndex (index) {
      this.i = index
    },
    UploadFile () {
      // if (this.selectgroup.length !== 0) {
      //   this.selectgroup.document[this.i].status = true
      //   this.selectgroup.document[this.i].InputFile = false
      // } else {
      this.datapartner[this.i].status = true
      this.datapartner[this.i].InputFile = false
      // }
      for (let i = 0; i < this.DataFile.length; i++) {
        const element = this.DataFile[i]
        const imageSize = element.size / 1024 / 1024
        if (imageSize < 2) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            this.Detail.product_file[this.i] = {
              name_document: this.DataFile[i].name,
              file: resultReader.split(',')[1]

            }
          }
        } else {
          this.Detail.product_file = []
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่ไฟล์ไม่เกิน 5 mb',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }

      // console.log('test', this.Detail.product_file)
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    show_inputimagse (index, item) {
      if (this.datapartner[index].InputFile === false) {
        this.datapartner[index].InputFile = true
      } else if (this.datapartner[index].InputFile === true) {
        this.datapartner[index].InputFile = false
      }
    },
    async getOrderReturn (item) {
      if (item === 0) {
        this.select = 0
        this.getListDate()
      } else if (item === 2) {
        this.data = []
        this.select = 2
        var companyData
        var Data
        if (localStorage.getItem('CompanyData') !== null) {
          companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
          Data = {
            company_id: companyData.id
          }
        } else {
          this.$router.push({ path: '/' })
          // if (localStorage.getItem('company_id') === null) {
          //   this.$router.push({ path: '/' })
          // } else {
          //   Data = {
          //     company_id: localStorage.getItem('company_id')
          //   }
          // }
        }
        await this.$store.dispatch('actionsListPartnerBuyer', Data)
        this.response = await this.$store.state.ModuleShop.stateGetListPartner
        // console.log('test  list Pratner', this.response)
        this.quantity = this.response.data.total_active
        if (this.quantity !== 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
        this.data = await this.response.data.active.map(x => {
          return {
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            createdForSearch: x.created_at,
            seller_shop_id: x.seller_shop_id,
            setting_partner_id: x.setting_partner_id,
            shop_name_en: x.shop_name_en,
            shop_name_th: x.shop_name_th,
            shop_status: x.shop_status,
            shop_url: x.shop_url,
            status: x.status,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
          }
        })
        this.countall = this.response.data.total_all
        this.countsuccess = this.response.data.total_active
        this.countwaiting = this.response.data.total_pending_request
        this.countfailed = this.response.data.total_inactive
        this.countreject = this.response.data.total_reject
      } else if (item === 1) {
        this.data = []
        this.select = 1
        if (localStorage.getItem('CompanyData') !== null) {
          companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
          Data = {
            company_id: companyData.id
          }
        } else {
          this.$router.push({ path: '/' })
          // if (localStorage.getItem('company_id') === null) {
          //   this.$router.push({ path: '/' })
          // } else {
          //   Data = {
          //     company_id: localStorage.getItem('company_id')
          //   }
          // }
        }
        await this.$store.dispatch('actionsListPartnerBuyer', Data)
        this.response = await this.$store.state.ModuleShop.stateGetListPartner
        // console.log('test  list Pratner', this.response)
        this.quantity = this.response.data.total_pending_request
        if (this.quantity !== 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
        this.data = await this.response.data.pending_request.map(x => {
          return {
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            createdForSearch: x.created_at,
            seller_shop_id: x.seller_shop_id,
            setting_partner_id: x.setting_partner_id,
            shop_name_en: x.shop_name_en,
            shop_name_th: x.shop_name_th,
            shop_status: x.shop_status,
            shop_url: x.shop_url,
            status: x.status,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
          }
        })
        this.countall = this.response.data.total_all
        this.countsuccess = this.response.data.total_active
        this.countwaiting = this.response.data.total_pending_request
        this.countfailed = this.response.data.total_inactive
        this.countreject = this.response.data.total_reject
      } else if (item === 3) {
        this.data = []
        this.select = 3
        if (localStorage.getItem('CompanyData') !== null) {
          companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
          Data = {
            company_id: companyData.id
          }
        } else {
          this.$router.push({ path: '/' })
          // if (localStorage.getItem('company_id') === null) {
          //   this.$router.push({ path: '/' })
          // } else {
          //   Data = {
          //     company_id: localStorage.getItem('company_id')
          //   }
          // }
        }
        await this.$store.dispatch('actionsListPartnerBuyer', Data)
        this.response = await this.$store.state.ModuleShop.stateGetListPartner
        // console.log('test  list Pratner', this.response)
        this.quantity = this.response.data.total_reject
        if (this.quantity !== 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
        this.data = await this.response.data.reject.map(x => {
          return {
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            createdForSearch: x.created_at,
            seller_shop_id: x.seller_shop_id,
            setting_partner_id: x.setting_partner_id,
            shop_name_en: x.shop_name_en,
            shop_name_th: x.shop_name_th,
            shop_status: x.shop_status,
            shop_url: x.shop_url,
            status: x.status,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
          }
        })
        this.countall = this.response.data.total_all
        this.countsuccess = this.response.data.total_active
        this.countwaiting = this.response.data.total_pending_request
        this.countfailed = this.response.data.total_inactive
        this.countreject = this.response.data.total_reject
      } else if (item === 4) {
        this.data = []
        this.select = 4
        if (localStorage.getItem('CompanyData') !== null) {
          companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
          Data = {
            company_id: companyData.id
          }
        } else {
          this.$router.push({ path: '/' })
          // if (localStorage.getItem('company_id') === null) {
          //   this.$router.push({ path: '/' })
          // } else {
          //   this.$router.push({ path: '/' })
          //   Data = {
          //     company_id: localStorage.getItem('company_id')
          //   }
          // }
        }
        await this.$store.dispatch('actionsListPartnerBuyer', Data)
        this.response = await this.$store.state.ModuleShop.stateGetListPartner
        // console.log('test  list Pratner', this.response)
        this.quantity = this.response.data.total_inactive
        if (this.quantity !== 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
        this.data = await this.response.data.inactive.map(x => {
          return {
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            createdForSearch: x.created_at,
            seller_shop_id: x.seller_shop_id,
            setting_partner_id: x.setting_partner_id,
            shop_name_en: x.shop_name_en,
            shop_name_th: x.shop_name_th,
            shop_status: x.shop_status,
            shop_url: x.shop_url,
            status: x.status,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
          }
        })
        this.countall = this.response.data.total_all
        this.countsuccess = this.response.data.total_active
        this.countwaiting = this.response.data.total_pending_request
        this.countfailed = this.response.data.total_inactive
        this.countreject = this.response.data.total_reject
      }
    },
    async getListDate () {
      // console.log('test', JSON.parse(Decode.decode(localStorage.getItem('companyData'))).id)
      this.data = []
      var companyData
      var Data
      if (localStorage.getItem('CompanyData') !== null) {
        companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
        Data = {
          company_id: companyData.id
        }
      } else {
        this.$router.push({ path: '/' })
        // if (localStorage.getItem('company_id') === null) {
        //   this.$router.push({ path: '/' })
        // } else {
        //   Data = {
        //     company_id: localStorage.getItem('company_id')
        //   }
        // }
      }
      await this.$store.dispatch('actionsListPartnerBuyer', Data)
      this.response = await this.$store.state.ModuleShop.stateGetListPartner
      // console.log('test  list Pratner', this.response.data.all)
      if (this.response.message === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
      } else {
        this.quantity = this.response.data.total_all
        if (this.quantity !== 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
        this.data = await this.response.data.all.map(x => {
          return {
            created_at: new Date(x.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }),
            createdForSearch: x.created_at,
            seller_shop_id: x.seller_shop_id,
            setting_partner_id: x.setting_partner_id,
            shop_name_en: x.shop_name_en,
            shop_name_th: x.shop_name_th,
            shop_status: x.shop_status,
            shop_url: x.shop_url,
            status: x.status,
            updated_at: new Date(x.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
          }
        })
        this.countall = this.response.data.total_all
        this.countsuccess = this.response.data.total_active
        this.countwaiting = this.response.data.total_pending_request
        this.countfailed = this.response.data.total_inactive
        this.countreject = this.response.data.total_reject
      }
    },
    async EditPartner (item) {
      this.itemtoEdit = item
      this.$store.commit('openLoader')
      // this.checkstatus = item.status
      this.res = []
      this.document_list = []
      this.checkstatus = 5
      this.name = ''
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.dialog = true
      var D = {
        company_id: companyData.id,
        seller_shop_id: item.seller_shop_id
      }
      await this.$store.dispatch('actionsDetailSellerpartner', D)
      this.res = await this.$store.state.ModuleShop.stateDetailSellerPartner
      // console.log('seller_shop_id', this.res.data[0].seller_shop_id)
      if (this.res.code === 200) {
        this.$store.commit('closeLoader')
        if (this.res.data[0].status === 'active') {
          this.checkstatus = 5
        } else if (this.res.data[0].status === 'inactive') {
          this.checkstatus = 0
        } else if (this.res.data[0].status === 'reject') {
          this.checkstatus = 6
        } else if (this.res.data[0].status === 'pending_request' || this.res.data[0].status === 'request') {
          this.checkstatus = 1
        }
        this.name = this.res.data[0].shop_name_th
        this.email = this.res.data[0].email
        this.businessType = this.res.data[0].business_type
        this.dateCreate = this.res.data[0].created_at
        this.dateUp = this.res.data[0].updated_at
        this.reason = this.res.data[0].reason
        this.document_list = this.res.data[0].document_list
        this.type_credit_term = this.res.data[0].type_credit_term
        this.tier_name = this.res.data[0].tier_name === '' || this.res.data[0].tier_name === undefined ? ' - ' : this.res.data[0].tier_name
        this.venderCode = this.res.data[0].vendor_code_seller === '' ? '-' : this.res.data[0].vendor_code_seller
        this.venderName = this.res.data[0].vendor_name_seller === '' ? '-' : this.res.data[0].vendor_name_seller
        this.companyIDDetail = this.res.data[0].company_id
        this.sellerShopIDDetail = this.res.data[0].seller_shop_id
      } else {
        this.$store.commit('closeLoader')
        if (this.res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
        }
      }
      // console.log('tong test', this.res)
    },
    async EditModal () {
      // console.log('EditModal', this.res)
      this.dialog = false
      this.dialogSuccess = false
      this.dialogEdit = true
      this.response_doc = []
      var DATA = {
        seller_shop_id: this.res.data[0].seller_shop_id
        // seller_shop_id: localStorage.getItem('shopID')
      }
      await this.$store.dispatch('actionsDetailDocumentShop', DATA)
      this.response_doc = await this.$store.state.ModuleShop.stateDetailDocumentShop.data
      this.response_doc_tier_id = this.response_doc.document_list[0].tier_id
      this.response_doc_array = this.response_doc.document_list[0].document
      this.doc = this.response_doc.document_list
      // this.datapartner = this.selectgroup.document
      // console.log('testao', this.response_doc)
      // var a = {
      //   seller_shop_id: '289',
      //   company_id: '19',
      //   status: '',
      //   document_list: []
      // }
      // await this.$store.dispatch('actionsEditPartner', a)
      // var r = await this.$store.state.ModuleShop.stateEditPartner
      // console.log(r)
    },
    async SaveEditPartner () {
      this.dialogEdit = false
      var a = {
        seller_shop_id: this.res.data[0].seller_shop_id,
        company_id: this.res.data[0].company_id,
        status: 'request',
        document_list: this.Detail.product_file
      }
      // console.log('testsss', a)
      await this.$store.dispatch('actionsEditPartner', a)
      var r = await this.$store.state.ModuleShop.stateEditPartner
      if (r.code === 200) {
        this.save_success = true
        setTimeout(() => {
          this.save_success = false
          this.getListDate()
        }, 900)
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
    },
    closeModal () {
      this.dialog = false
      this.dialogSuccess = false
      this.dialogEdit = false
    },
    success () {
      this.dialog = false
      this.dialogSuccess = true
    },
    async cancelModal () {
      this.dialog = false
      this.dialogSuccess = false
      // var a = {
      //   seller_shop_id: this.res.data[0].seller_shop_id,
      //   company_id: this.res.data[0].company_id,
      //   status: 'cancel',
      //   document_list: this.res.data[0].document_list
      // }
      // await this.$store.dispatch('actionsEditPartner', a)
      // var r = await this.$store.state.ModuleShop.stateEditPartner
      // if (r.code === 200) {
      //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ยื่นคำขอเป็นคู่ค้าสำเร็จ</h3>' })
      // } else {
      //   this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      // }
    },
    async postDetailShop () {
      this.dialogSuccess = false
      var a = {
        seller_shop_id: this.res.data[0].seller_shop_id,
        company_id: this.res.data[0].company_id,
        status: 'inactive',
        document_list: this.res.data[0].document_list
      }
      await this.$store.dispatch('actionsEditPartner', a)
      var r = await this.$store.state.ModuleShop.stateEditPartner
      if (r.code === 200) {
        this.cancel_success = true
        setTimeout(() => {
          this.cancel_success = false
          this.getListDate()
        }, 900)
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>เกิดข้อผิดพลาด</h3>' })
      }
    },
    async EditVenderDetail (companyID, shopID) {
      const data = {
        company_id: companyID,
        seller_id: shopID
      }
      await this.$store.dispatch('actionsGetVender', data)
      const response = await this.$store.state.ModuleAdminManage.stateGetVender
      if (response.message === 'Get Data Success') {
        this.textVenderCode = response.data[0].vendor_code_seller
        this.textVenderName = response.data[0].vendor_name_seller
        this.dialogUpdateVender = true
      } else {
        this.$stroe.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', text: response.message })
      }
    },
    async UpdateVender () {
      this.$store.commit('openLoader')
      const data = {
        company_id: this.companyIDDetail,
        seller_id: this.sellerShopIDDetail,
        vendor_code: this.textVenderCode,
        vendor_name: this.textVenderName
      }
      await this.$store.dispatch('actionsUpdateVender', data)
      const response = await this.$store.state.ModuleAdminManage.stateUpdateVender
      // console.log(response)
      if (response.message === 'Update vendor Success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>อัพเดตข้อมูล Vender สำเร็จ</h3>' })
        this.dialogUpdateVender = false
        await this.EditPartner(this.itemtoEdit)
      } else {
        this.$stroe.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', text: response.message })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.changeBorderbottom /deep/ .ant-tabs-bar {
  margin: 0 0 16px 0;
  border-bottom: 2px solid #DAF1E9 !important;
  outline: none;
  transition: padding 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.fontStyleData {
  font-size: 16px;
  color: #333333;
  font-weight: 700;
}
.fontStyleTitle {
  font-size: 16px;
  color: #333333;
  font-weight: 400;
}
</style>
