<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ร้านค้าในระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ร้านค้าเปิด-ปิดในระบบ</v-card-title>
      <!-- table shop all -->
      <v-row>
        <v-col cols="12">
          <v-col cols="12" md="6" sm="12">
            <v-text-field v-model="search" placeholder="ค้นหาจากรายชื่อร้านค้าในระบบ" outlined rounded dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
            <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อร้านค้าในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายชื่อร้านค้าในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
          </v-col>
          <v-col cols="12">
            <v-data-table
            :headers="headers"
            :search="search"
            :items="adminShopList"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการร้านค้าในระบบ"
            no-data-text="ไม่พบรายการร้านค้าในระบบ"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.indexOfUser`]="{ index }">
                  {{ index + 1 }}
                </template>
              <template v-slot:[`item.action`]="{ item }">
                <v-btn color="#27AB9C" text @click="changePage(item.id, item.shop_name)" >
                  <v-icon>mdi-account </v-icon><br>
                  <span> ดูผู้ใช้งานร้านค้า</span>
                </v-btn>
              </template>
              <template v-slot:[`item.tax_id`]="{ item }">
                <span v-if="item.tax_id" color="#27AB9C" > {{ item.tax_id}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.name_th`]="{ item }">
                <span v-if="item.name_th" color="#27AB9C" > {{ item.name_th}}</span>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.shop_name`]="{ item }">
                <span v-if="item.shop_name" color="#27AB9C" > {{ item.shop_name}}</span>
                <span v-else>-</span>
              </template>
            </v-data-table>
          </v-col>
        </v-col>
      </v-row>
  </v-card>
</v-container>
</template>

<script>
export default {
  data () {
    return {
      showStatusShop: {},
      adminShopList: [],
      btn_manage: true,
      search: '',
      showCountRequest: '',
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขผู้ใช้ภาษี', value: 'tax_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_th', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'shop_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageUserShopMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'manageUserShop')
        this.$router.push({ path: '/manageUserShop' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetShopDataAdmin')
      var response = await this.$store.state.ModuleAdminManage.stateGetShopDataAdmin
      if (response.message === 'List Seller Shop Success.') {
        this.$store.commit('closeLoader')
        this.adminShopList = [...response.data]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    changePage (id, shopName) {
      if (!this.MobileSize) {
        this.$router.push({ path: `/manageUserList?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/manageUserListMobile?seller_shop_id=${id}&shop_name=${shopName}` }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
  .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
    white-space: nowrap !important;
    text-align: center !important;
  }
  .v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
    text-align: center !important;
  }
</style>
