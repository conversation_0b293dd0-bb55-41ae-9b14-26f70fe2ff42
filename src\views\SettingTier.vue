<template>
    <settingTier/>
</template>
<script>
export default {
  components: {
    settingTier: () => import('@/components/Shop/Tier/SettingTierTable')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SettingTierMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/SettingTier' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>
