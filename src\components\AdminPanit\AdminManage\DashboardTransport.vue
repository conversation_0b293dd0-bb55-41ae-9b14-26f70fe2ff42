<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <div class="d-flex align-center">
        <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">Operation Dashboard</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>Operation Dashboard</v-card-title>
        <v-spacer></v-spacer>
        <v-menu
          v-model="menu"
          offset-y
          :close-on-content-click="false"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn v-if="!MobileSize" v-bind="attrs" v-on="on" :style="!IpadProSize && !IpadSize ? 'padding: 35px;' : 'padding: 35px; width: 26.5vw;'" color="#f4fcff">
              <div style="display: flex; gap: 1vw;">
                <v-icon v-if="!IpadSize" color="#2d9cdb" style="background-color: #d6eefa; padding: 5px; border-radius: .5vw;">mdi-calendar</v-icon>
                <div style="display: flex; flex-direction: column; align-items: flex-start;">
                  <span>Filter Period</span>
                  <span style="font-size: 12px">{{ displayDateFilterText }}</span>
                </div>
                <v-icon class="mdi-rotate-180 mb-2" color="#b9bbbd">mdi-apple-keyboard-control</v-icon>
              </div>
            </v-btn>
            <v-btn v-else v-bind="attrs" v-on="on" style="padding: .5vw;" color="#f4fcff">
              <div style="display: flex; gap: 1vw;">
                <v-icon color="#2d9cdb">mdi-filter</v-icon>
                <div style="display: flex; flex-direction: column; align-items: flex-start;">
                  <span>ตัวกรอง</span>
                  <!-- <span style="font-size: 12px">{{ displayDateFilterText }}</span> -->
                </div>
              </div>
            </v-btn>
          </template>

          <v-card>
            <v-card-text>
              <v-radio-group v-model="selectedFilterType" row>
                <v-radio label="รายปี" value="yearly"></v-radio>
                <v-radio label="รายเดือน" value="monthly"></v-radio>
                <v-radio label="รายวัน" value="daily"></v-radio>
              </v-radio-group>

              <template v-if="selectedFilterType === 'yearly'">
                <v-select
                  v-model="selectedYear"
                  :items="years"
                  label="ปี"
                  dense
                  outlined
                >
                  <template slot="selection">
                    {{selectedYear + 543}}
                  </template>
                  <template slot="item" slot-scope="data">
                    {{data.item + 543}}
                  </template>
                </v-select>
              </template>

              <template v-if="selectedFilterType === 'monthly'">
                <v-select
                  v-model="selectedYearMonth"
                  :items="years"
                  label="ปี"
                  dense
                  outlined
                >
                  <template slot="selection">
                    {{selectedYearMonth + 543}}
                  </template>
                  <template slot="item" slot-scope="data">
                    {{data.item + 543}}
                  </template>
                </v-select>
                <v-select
                  v-model="selectedMonth"
                  :items="months"
                  label="เดือน"
                  dense
                  outlined
                ></v-select>
              </template>

              <template v-if="selectedFilterType === 'daily'">
                <v-date-picker
                  v-model="selectedDays"
                  range
                  locale="th"
                  :show-current="true"
                  @change="handleDateChange"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                ></v-date-picker>
              </template>
            </v-card-text>

            <v-card-actions style="display: flex; justify-content: center;">
              <v-btn color="primary" @click="applyDateFilter">ยืนยัน</v-btn>
              <v-btn text @click="menu = false">ยกเลิก</v-btn>
            </v-card-actions>
          </v-card>
        </v-menu>
      </div>
      <div v-if="MobileSize" style="margin: 5vw;">
        <v-select
          v-model="mobileMenuSelect"
          :items="mobileMenu"
          label="เลือกเมนู"
          dense
          outlined
          @change="handleMenuChange"
        ></v-select>
      </div>
      <!-- Mobilesize -->
      <v-row v-if="MobileSize" style="margin: .5vw;">
        <!-- dashboard all -->
        <v-row v-if="mobileMenuSelect === 'แดชบอร์ด Order ทั้งหมด'" style="margin: .5vw;">
          <v-col cols="12" class="boxDashboard" style="margin-top: -10vw;">
            <div class="topicDashboard">
              <span style="font-size: medium;">จำนวนร้านค้าที่มี Order</span>
              <v-chip color="#fffcdc">
                <span style="color: #3c87be; font-size: small;">{{ shopAll }} ร้านค้า</span>
              </v-chip>
            </div>
            <div id="app" v-if="dataDonut.series.length > 0">
              <div class="d-flex justify-center">
                <apexchart
                  type="donut"
                  :series="dataDonut.series"
                  :options="dataDonut.chartOptions"
                  height="350" width="320"
                  >
                </apexchart>
              </div>
              <v-row>
                <v-col cols="6" class="d-flex justify-center align-center" style="flex-direction: column; font-size: small">
                  <v-icon color="#42a5f5">mdi-circle</v-icon>
                  <span>Order คงค้าง</span>
                </v-col>
                <v-col cols="6" class="d-flex justify-center align-center" style="flex-direction: column; font-size: small">
                  <v-icon color="#c48a8a">mdi-circle</v-icon>
                  <span>Order ตีกลับ</span>
                </v-col>
                <v-col cols="6" class="d-flex justify-center align-center" style="flex-direction: column; font-size: small">
                  <v-icon color="#40c89b">mdi-circle</v-icon>
                  <span>Order สำเร็จ</span>
                </v-col>
                <v-col cols="6" class="d-flex justify-center align-center" style="flex-direction: column; font-size: small">
                  <v-icon color="#6ab8de">mdi-circle</v-icon>
                  <span>Order ยกเลิก</span>
                </v-col>
                <v-col cols="6" class="d-flex justify-center align-center" style="flex-direction: column; font-size: small">
                  <v-icon color="#f19b18">mdi-circle</v-icon>
                  <span>Order สูญหาย</span>
                </v-col>
                <v-col cols="6" class="d-flex justify-center align-center" style="flex-direction: column; font-size: small">
                  <v-icon color="#7bd750">mdi-circle</v-icon>
                  <span>Order ตีกลับสำเร็จ</span>
                </v-col>
                <!-- <v-col class="d-flex justify-center" style="flex-direction: column; font-size: x-small">
                  <v-icon color="#6ab8de">mdi-circle</v-icon>
                  <span> จำนวน order ยกเลิก</span>
                </v-col> -->
              </v-row>
            </div>
          </v-col>
          <!-- dashboard order transport -->
          <v-col cols="12">
            <v-row style="padding: 1vw; display: flex; gap: 2vw; justify-content: center;">
              <!-- total order -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/totalOrder.jpg" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order จัดส่งสะสม</span>
                  <span class="amount">{{ totalOrder }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentTotal >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffTotal }}</span>
                    <span>({{ percentTotal }}%)</span>
                  </div>
                </div>
              </v-col>
              <!-- pending order -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/pendingOrder.jpg" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order คงค้าง</span>
                  <span class="amount">{{ pendingOrder }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentPending >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffPending }}</span>
                    <span>({{ percentPending }}%)</span>
                  </div>
                </div>
              </v-col>
              <!-- return order -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/boune_back.png" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order ตีกลับ</span>
                  <span class="amount">{{ returnOrder }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentCancel >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffReturn }}</span>
                    <span>({{ percentReturn }}%)</span>
                  </div>
                </div>
              </v-col>
              <!-- success order -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/successOrder.jpg" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order จัดส่งสำเร็จ</span>
                  <span class="amount">{{ successOrder }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentSuccess >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffSuccess }}</span>
                    <span>({{ percentSuccess }}%)</span>
                  </div>
                </div>
              </v-col>
              <!-- cancel order -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/cancelOrder.jpg" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order ยกเลิก</span>
                  <span class="amount">{{ cancelOrder }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentCancel >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffCancel }}</span>
                    <span>({{ percentCancel }}%)</span>
                  </div>
                </div>
              </v-col>
              <!-- lost order -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/lostOrder.png" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order สูญหาย</span>
                  <span class="amount">{{ lostOrder }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentCancel >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffLost }}</span>
                    <span>({{ percentLost }}%)</span>
                  </div>
                </div>
              </v-col>
              <!-- return order success -->
              <v-col cols="12" class="styleItemMobile">
                <img src="@/assets/ImageINET-Marketplace/ICONShop/returnSuccess.png" alt="Product Image" class="styleImgMobile">
                <div class="groupData">
                  <span class="boxTopic">จำนวน Order ตีกลับสำเร็จ</span>
                  <span class="amount">{{ returnOrderSuccess }} รายการ</span>
                  <div class="percent">
                    <v-icon v-if="percentCancel >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
                    <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
                    <span class="mr-1">{{ diffReturnSuccess }}</span>
                    <span>({{ percentReturnSuccess }}%)</span>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <!-- dashboard order success -->
        <v-row v-else-if="mobileMenuSelect === 'ข้อมูล Order สำเร็จสะสม'" style="margin: .5vw;">
          <v-col cols="12" class="styleBox" style="margin-top: -10vw; margin-bottom: 2vw;">
            <!-- Quantity Oreder -->
            <div class="formatItem" cols="6">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/quantityOrder.jpg" alt="Product Image">
              <div class="groupData">
                <span>Order สำเร็จสะสมทั้งหมด</span>
                <span class="sizeWord">{{ successOrderAll }} รายการ</span>
                <div style="color: #36b48a">
                  <v-icon v-if="percentSuccessAll >= 0" color="#36b48a" size="20">mdi-arrow-up</v-icon>
                  <v-icon v-else color="#36b48a" size="20">mdi-arrow-down</v-icon>
                  <span class="mr-1">{{ diffSuccessAll }}</span>
                  <span>({{ percentSuccessAll }}%)</span>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12" class="styleBox" style="margin-bottom: 2vw;">
            <!-- Date Transport -->
            <div class="formatItem" cols="6">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/dateTransport.jpg" alt="Product Image">
              <div class="groupData">
                <span>Average จำนวนวันจัดส่งสำเร็จ</span>
                <span class="sizeWord">{{ dateOrderSuccess }} วัน</span>
                <div style="color: #f1545d">
                  <v-icon v-if="percentAverage >= 0" color="#f1545d" size="20">mdi-arrow-up</v-icon>
                  <v-icon v-else color="#f1545d" size="20">mdi-arrow-down</v-icon>
                  <span class="mr-1">{{ diffAverage }}</span>
                  <span>({{ percentAverage }}%)</span>
                </div>
              </div>
            </div>
          </v-col>
          <!-- total 1-3 day -->
          <v-col cols="12" class="styleformatboxMobile">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/iconCarG.jpg" alt="Product Image" class="styleImgMobile">
              <span class="fontSizetitle">Order สำเร็จภายใน 1-3 วัน</span>
            </div>
            <v-divider class="formatDividerMobile"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord">{{ OrderSuccess_3_day }} รายการ</span>
              <div style="color: #34ac84">
                <v-icon v-if="percent_3_days >= 0" color="#34ac84" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#34ac84" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffNormal }}</span>
                <span>({{ percent_3_days }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- total 4-6 day -->
          <v-col cols="12" class="styleformatboxMobile">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/iconCarY.jpg" alt="Product Image" class="styleImgMobile">
              <span class="fontSizetitle">Order สำเร็จภายใน 4-6 วัน</span>
            </div>
            <v-divider class="formatDividerMobile"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord">{{ OrderSuccess_6_day }} รายการ</span>
              <div style="color: #ffd166">
                <v-icon v-if="percent_6_days >= 0" color="#ffd166" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#ffd166" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffMonitor }}</span>
                <span>({{ percent_6_days }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- total 7 day -->
          <v-col cols="12" class="styleformatboxMobile">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/iconCarR.jpg" alt="Product Image" class="styleImgMobile">
              <span class="fontSizetitle">Order สำเร็จมากกว่า 7 วัน</span>
            </div>
            <v-divider class="formatDividerMobile"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord">{{ OrderSuccess_7_day }} รายการ</span>
              <div style="color: #d56062">
                <v-icon v-if="percent_7_days >= 0" color="#d56062" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#d56062" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffAlarm }}</span>
                <span>({{ percent_7_days }}%)</span>
              </div>
            </div>
          </v-col>
          <v-col cols="12">
            <span class="topicDashboard">สถานะ Order ขนส่งสำเร็จ</span>
            <div id="chart_in_progress_order" v-if="dataBar.series.length > 0">
              <!-- <apexchart type="bar" height="350" width="1200" :options="dataBar.chartOptions" :series="dataBar.series"></apexchart> -->
              <apexchart type="bar" height="350" width="320" :options="dataBar.chartOptions" :series="dataBar.series"></apexchart>
            </div>
          </v-col>
          <v-col cols="12" class="boxDashboard">
            <span class="topicDashboard">จำนวนร้านค้าที่มี Order สะสมทั้งหมด</span>
            <v-data-table
              :headers="headersSuccess"
              :items="showOrder"
              :search="search"
              style="width:100%; text-align: center !important;"
              :page.sync="page"
              @pagination="countRequest"
              no-results-text="ไม่พบรายการการ order ร้านค้า"
              no-data-text="ไม่พบรายการการ order ร้านค้า"
              :update:items-per-page="itemsPerPage"
              :items-per-page="5"
              class="elevation-1 mt-4"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              >
            </v-data-table>
          </v-col>
        </v-row>
        <!-- dashboard order pending -->
        <v-row v-else-if="mobileMenuSelect === 'ข้อมูล Order คงค้าง'" style="margin: .5vw;">
          <v-col cols="12" class="styleBox" style="margin-top: -10vw; margin-bottom: 2vw;">
            <!-- Quantity pending -->
            <div class="formatItem" cols="6">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/pending.jpg" alt="Product Image">
              <div class="groupData">
                <span>Order คงค้างทั้งหมด</span>
                <span class="sizeWord">{{ pendingOrder }} รายการ</span>
                <div style="color: #36b48a">
                  <v-icon v-if="percentPendingAll >= 0" color="#36b48a" size="20">mdi-arrow-up</v-icon>
                  <v-icon v-else color="#36b48a" size="20">mdi-arrow-down</v-icon>
                  <span class="mr-1">{{ diffPendingAll }}</span>
                  <span>({{ percentPendingAll }}%)</span>
                </div>
              </div>
            </div>
          </v-col>
          <v-col cols="12" class="styleBox" style="margin-bottom: 2vw;">
            <!-- Merchant Transport -->
            <div class="formatItem" cols="6">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/Merchant.jpg" alt="Product Image">
              <div class="groupData">
                <span>Merchant คงค้างทั้งหมด</span>
                <span class="sizeWord">{{ MerchantOrder }} ร้านค้า</span>
                <div style="color: #f1545d">
                  <v-icon v-if="percentMerchant >= 0" color="#f1545d" size="20">mdi-arrow-up</v-icon>
                  <v-icon v-else color="#f1545d" size="20">mdi-arrow-down</v-icon>
                  <span class="mr-1">{{ diffMerchant }}</span>
                  <span>({{ percentMerchant }}%)</span>
                </div>
              </div>
            </div>
          </v-col>
          <!-- Order Normal -->
          <v-col cols="12" class="styleformatboxMobile">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/normalOrder.jpg" alt="Product Image" class="styleImgMobile">
              <span class="fontSizetitle">Order Normal</span>
            </div>
            <v-divider class="formatDividerMobile"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord">{{ orderNormal }} รายการ</span>
              <div style="color: #34ac84">
                <v-icon v-if="percentNormal >= 0" color="#34ac84" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#34ac84" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffOrderNormal }}</span>
                <span>({{ percentNormal }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- Order Monitor -->
          <v-col cols="12" class="styleformatboxMobile">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/orderMonitor.jpg" alt="Product Image" class="styleImgMobile">
              <span class="fontSizetitle">Order Monitor</span>
            </div>
            <v-divider class="formatDividerMobile"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord">{{ orderMonitor }} รายการ</span>
              <div style="color: #ffd166">
                <v-icon v-if="percentMonitor >= 0" color="#ffd166" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#ffd166" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffOrderMonitor }}</span>
                <span>({{ percentMonitor }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- Order Alarm -->
          <v-col cols="12" class="styleformatboxMobile">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/orderAlarm.jpg" alt="Product Image" class="styleImgMobile">
              <span class="fontSizetitle">Order Alarm</span>
            </div>
            <v-divider class="formatDividerMobile"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord">{{ orderAlarm }} รายการ</span>
              <div style="color: #d56062">
                <v-icon v-if="percentAlarm >= 0" color="#d56062" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#d56062" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffOrderAlarm }}</span>
                <span>({{ percentAlarm }}%)</span>
              </div>
            </div>
          </v-col>
          <v-col cols="12">
            <span class="topicDashboard">สถานะ Order คงค้างทั้งหมด</span>
            <div id="chart_in_progress_order" v-if="dataBarPending.series.length > 0">
              <apexchart type="bar" height="350" width="320" :options="dataBarPending.chartOptions" :series="dataBarPending.series"></apexchart>
            </div>
          </v-col>
          <v-col cols="12" class="boxDashboard">
            <span class="topicDashboard">จำนวน Order คงค้างจำแนกตาม Stage</span>
            <v-data-table
              :headers="headersPending"
              :items="stageOrderAll"
              :search="search"
              style="width:100%; text-align: center !important; white-space: nowrap"
              :page.sync="page"
              @pagination="countRequest"
              no-results-text="ไม่พบรายการการ order ร้านค้า"
              no-data-text="ไม่พบรายการการ order ร้านค้า"
              :update:items-per-page="itemsPerPage"
              :footer-props="{ 'items-per-page-options': [7, 10, 15, 50, 100], 'items-per-page-text': 'จำนวนแถว' }"
              :items-per-page="7"
              class="elevation-1 mt-4"
              >
            </v-data-table>
          </v-col>
        </v-row>
      </v-row>
      <!-- desktop -->
      <!-- dashboard all -->
      <v-row v-else style="margin: .5vw;">
        <v-col cols="12" class="d-flex justify-center flex-column boxDashboard">
          <div class="topicDashboard">
            <span>จำนวนร้านค้าที่มี Order</span>
            <v-chip color="#fffcdc">
              <span style="color: #3c87be; font-size: medium;">{{ shopAll }} ร้านค้า</span>
            </v-chip>
          </div>
          <!-- char donut -->
          <div :class="IpadProSize || IpadSize ? 'd-flex justify-end flex-column' : 'd-flex justify-end'">
            <div class="ma-auto" id="app" v-if="dataDonut.series.length > 0">
              <apexchart
                type="donut"
                :series="dataDonut.series"
                :options="dataDonut.chartOptions"
                :height="IpadSize ? '300' : IpadProSize ? '' : '450'" :width="IpadSize ? '440' : IpadProSize ? '380' : '800'"
                >
              </apexchart>
            </div>
            <div :class="IpadProSize || IpadSize ? 'd-flex flex-wrap' : 'd-flex flex-column'" :style="IpadSize || IpadProSize ? '' : 'margin-left: -8vw;'">
              <v-col :class="IpadProSize || IpadSize ? 'd-flex flex-column align-center' : 'margin-top: -2vw;'">
                <v-icon color="#42a5f5">mdi-circle</v-icon>
                <span class="ma-auto mx-3" style="flex-direction: column; white-space: nowrap;">จำนวน Order คงค้าง</span>
              </v-col>
              <v-col :class="IpadProSize || IpadSize ? 'd-flex flex-column align-center' : 'margin-top: -2vw;'">
                <v-icon color="#c48a8a">mdi-circle</v-icon>
                <span class="ma-auto mx-3" style="flex-direction: column; white-space: nowrap;">จำนวน Order ตีกลับ</span>
              </v-col>
              <v-col :class="IpadProSize || IpadSize ? 'd-flex flex-column align-center' : ''">
                <v-icon color="#40c89b">mdi-circle</v-icon>
                <span class="ma-auto mx-3" style="font-size: x-samll; white-space: nowrap;">จำนวน Order สำเร็จ</span>
              </v-col>
              <v-col :class="IpadProSize || IpadSize ? 'd-flex flex-column align-center' : 'margin-top: -2vw;'">
                <v-icon color="#6ab8de">mdi-circle</v-icon>
                <span class="ma-auto mx-3" style="flex-direction: column; white-space: nowrap;">จำนวน Order ยกเลิก</span>
              </v-col>
              <v-col :class="IpadProSize || IpadSize ? 'd-flex flex-column align-center' : 'margin-top: -2vw;'">
                <v-icon color="#f19b18">mdi-circle</v-icon>
                <span class="ma-auto mx-3" style="flex-direction: column; white-space: nowrap;">จำนวน Order สูญหาย</span>
              </v-col>
              <v-col :class="IpadProSize || IpadSize ? 'd-flex flex-column align-center' : 'margin-top: -2vw;'">
                <v-icon color="#7bd750">mdi-circle</v-icon>
                <span class="ma-auto mx-3" style="flex-direction: column; white-space: nowrap;">จำนวน Order ตีกลับสำเร็จ</span>
              </v-col>
            </div>
          </div>
        </v-col>
        <v-col class="boxDashboard mt-5 d-flex pa-5" :style="IpadProSize || IpadSize ? 'flex-wrap: wrap; justify-content: center;' : ''">
          <!-- total order -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/totalOrder.jpg" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order จัดส่งสะสม</span>
            <span class="amount">{{ totalOrder }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentTotal >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffTotal }}</span>
              <span>({{ percentTotal }}%)</span>
            </div>
          </div>
          <!-- pending order -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/pendingOrder.jpg" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order คงค้าง</span>
            <span class="amount">{{ pendingOrder }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentPending >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffPending }}</span>
              <span>({{ percentPending }}%)</span>
            </div>
          </div>
          <!-- return product order -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/boune_back.png" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order ตีกลับ</span>
            <span class="amount">{{ returnOrder }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentPending >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffReturn }}</span>
              <span>({{ percentReturn }}%)</span>
            </div>
          </div>
          <!-- success order -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/successOrder.jpg" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order จัดส่งสำเร็จ</span>
            <span class="amount">{{ successOrder }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentSuccess >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffSuccess }}</span>
              <span>({{ percentSuccess }}%)</span>
            </div>
          </div>
          <!-- cancel order -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/cancelOrder.jpg" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order ยกเลิก</span>
            <span class="amount">{{ cancelOrder }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentPending >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffCancel }}</span>
              <span>({{ percentCancel }}%)</span>
            </div>
          </div>
          <!-- lost order -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/lostOrder.png" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order สูญหาย</span>
            <span class="amount">{{ lostOrder }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentPending >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffLost }}</span>
              <span>({{ percentLost }}%)</span>
            </div>
          </div>
          <!-- return product order success -->
          <div class="boxDashboard ma-1 d-flex flex-column align-center pa-2" style="width: 12vw;">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/returnSuccess.png" alt="Product Image" class="styleImg">
            <span class="boxTopic" :style="IpadSize || IpadProSize ? 'font-size: 13px; white-space: nowrap;' : 'font-size: 16px;'">Order ตีกลับสำเร็จ</span>
            <span class="amount">{{ returnOrderSuccess }} รายการ</span>
            <div class="percent" style="white-space: nowrap;">
              <v-icon v-if="percentPending >= 0" color="#2a4597" size="20">mdi-arrow-up</v-icon>
              <v-icon v-else color="#2a4597" size="20">mdi-arrow-down</v-icon>
              <span class="mr-1">{{ diffReturnSuccess }}</span>
              <span>({{ percentReturnSuccess }}%)</span>
            </div>
          </div>
        </v-col>
        <v-col cols="12" class="styleBox" style="margin-top: 1.5vw;">
          <!-- Quantity Oreder -->
          <div class="formatItem" cols="6">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/quantityOrder.jpg" alt="Product Image">
            <div class="formatWord">
              <span>Order สำเร็จสะสมทั้งหมด</span>
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ successOrderAll }} รายการ</span>
              <div style="color: #36b48a">
                <v-icon v-if="percentSuccessAll >= 0" color="#36b48a" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#36b48a" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffSuccessAll }}</span>
                <span>({{ percentSuccessAll }}%)</span>
              </div>
            </div>
          </div>
          <!-- Date Transport -->
          <div class="formatItem" cols="6">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/dateTransport.jpg" alt="Product Image">
            <div class="formatWord">
              <span>Average จำนวนวันจัดส่งสำเร็จ</span>
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ dateOrderSuccess }} วัน</span>
              <div style="color: #f1545d">
                <v-icon v-if="percentAverage >= 0" color="#f1545d" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#f1545d" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffAverage }}</span>
                <span>({{ percentAverage }}%)</span>
              </div>
            </div>
          </div>
        </v-col>
        <!-- dashboard order success -->
        <v-col cols="12" class="d-flex mt-3 boxDashboard" style="gap: .5vw; justify-content: center; padding: 1vw">
          <!-- total 1-3 day -->
          <v-col cols="4" class="styleItem">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/iconCarG.jpg" alt="Product Image" class="styleImg">
              <span class="fontSizetitle" :style="IpadSize ? 'font-size: 11px;' : 'font-size: 18px;'">Order สำเร็จภายใน 1-3 วัน</span>
            </div>
            <v-divider class="formatDivider"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ OrderSuccess_3_day }} รายการ</span>
              <div style="color: #34ac84">
                <v-icon v-if="percent_3_days >= 0" color="#34ac84" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#34ac84" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffNormal }}</span>
                <span>({{ percent_3_days }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- total 4-6 day -->
          <v-col  cols="4" class="styleItem">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/iconCarY.jpg" alt="Product Image" class="styleImg">
              <span class="fontSizetitle" :style="IpadSize ? 'font-size: 11px;' : 'font-size: 18px;'">Order สำเร็จภายใน 4-6 วัน</span>
            </div>
            <v-divider class="formatDivider"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ OrderSuccess_6_day }} รายการ</span>
              <div style="color: #ffd166">
                <v-icon v-if="percent_6_days >= 0" color="#ffd166" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#ffd166" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffMonitor }}</span>
                <span>({{ percent_6_days }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- total 7 day -->
          <v-col cols="4" class="styleItem">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/iconCarR.jpg" alt="Product Image" class="styleImg">
              <span class="fontSizetitle" :style="IpadSize ? 'font-size: 11px;' : 'font-size: 18px;'">Order สำเร็จมากกว่า 7 วัน</span>
            </div>
            <v-divider class="formatDivider"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ OrderSuccess_7_day }} รายการ</span>
              <div style="color: #d56062">
                <v-icon v-if="percent_7_days >= 0" color="#d56062" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#d56062" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffAlarm }}</span>
                <span>({{ percent_7_days }}%)</span>
              </div>
            </div>
          </v-col>
        </v-col>
        <v-col class="formatWord align-center boxDashboard mt-4" :cols="IpadSize || IpadProSize ? 12 :4">
          <span class="topicDashboard">สถานะ Order ขนส่งสำเร็จ</span>
          <div id="chart_in_progress_order" v-if="dataBar.series.length > 0">
            <apexchart type="bar" height="350" :width="IpadSize ? '500' : '400'" :options="dataBar.chartOptions" :series="dataBar.series"></apexchart>
          </div>
        </v-col>
        <v-col :cols="IpadSize || IpadProSize ? 12 : 8" class="boxDashboard mt-4">
          <span class="topicDashboard">จำนวนร้านค้าที่มี Order สะสมทั้งหมด</span>
          <v-data-table
            :headers="headersSuccess"
            :items="showOrder"
            hide-default-footer
            :height="340"
            :items-per-page="-1"
            fixed-header
          >
          </v-data-table>
        </v-col>
        <!-- dashboard order pending -->
        <v-col cols="12" class="styleBox" style="margin-top: 1.5vw;">
          <!-- Quantity pending -->
          <div class="formatItem" cols="6">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/pending.jpg" alt="Product Image">
            <div class="formatWord">
              <span>Order คงค้างทั้งหมด</span>
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ pendingOrder }} รายการ</span>
              <div style="color: #36b48a">
                <v-icon v-if="percentPendingAll >= 0" color="#36b48a" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#36b48a" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffPendingAll }}</span>
                <span>({{ percentPendingAll }}%)</span>
              </div>
            </div>
          </div>
          <!-- Merchant Transport -->
          <div class="formatItem" cols="6">
            <img src="@/assets/ImageINET-Marketplace/ICONShop/Merchant.jpg" alt="Product Image">
            <div class="formatWord">
              <span>Merchant คงค้างทั้งหมด</span>
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ MerchantOrder }} ร้านค้า</span>
              <div style="color: #f1545d">
                <v-icon v-if="percentMerchant >= 0" color="#f1545d" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#f1545d" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffMerchant }}</span>
                <span>({{ percentMerchant }}%)</span>
              </div>
            </div>
          </div>
        </v-col>
        <v-col cols="12" class="d-flex mt-4 boxDashboard" style="gap: .5vw; justify-content: center; padding: 1vw">
          <!-- Order Normal -->
          <v-col cols="4" class="styleItem">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/normalOrder.jpg" alt="Product Image" class="styleImg">
              <span class="fontSizetitle" :style="IpadSize ? 'font-size: 11px;' : 'font-size: 18px;'">Order Normal</span>
            </div>
            <v-divider class="formatDivider"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ orderNormal }} รายการ</span>
              <div style="color: #34ac84">
                <v-icon v-if="percentNormal >= 0" color="#34ac84" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#34ac84" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffOrderNormal }}</span>
                <span>({{ percentNormal }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- Order Monitor -->
          <v-col  cols="4" class="styleItem">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/orderMonitor.jpg" alt="Product Image" class="styleImg">
              <span class="fontSizetitle" :style="IpadSize ? 'font-size: 11px;' : 'font-size: 18px;'">Order Monitor</span>
            </div>
            <v-divider class="formatDivider"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ orderMonitor }} รายการ</span>
              <div style="color: #ffd166">
                <v-icon v-if="percentMonitor >= 0" color="#ffd166" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#ffd166" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffOrderMonitor }}</span>
                <span>({{ percentMonitor }}%)</span>
              </div>
            </div>
          </v-col>
          <!-- Order Alarm -->
          <v-col cols="4" class="styleItem">
            <div class="formatWord align-center">
              <img src="@/assets/ImageINET-Marketplace/ICONShop/orderAlarm.jpg" alt="Product Image" class="styleImg">
              <span class="fontSizetitle" :style="IpadSize ? 'font-size: 11px;' : 'font-size: 18px;'">Order Alarm</span>
            </div>
            <v-divider class="formatDivider"></v-divider>
            <div class="formatWord align-center">
              <span class="sizeWord" :style="IpadSize ? 'font-size: 15px;' : 'font-size: 18px;'">{{ orderAlarm }} รายการ</span>
              <div style="color: #d56062">
                <v-icon v-if="percentAlarm >= 0" color="#d56062" size="20">mdi-arrow-up</v-icon>
                <v-icon v-else color="#d56062" size="20">mdi-arrow-down</v-icon>
                <span class="mr-1">{{ diffOrderAlarm }}</span>
                <span>({{ percentAlarm }}%)</span>
              </div>
            </div>
          </v-col>
        </v-col>
        <!-- dashboard order pending -->
        <v-col class="formatWord align-center boxDashboard mt-4" :cols="IpadSize || IpadProSize ? 12 : 4">
          <span class="topicDashboard">สถานะ Order คงค้างทั้งหมด</span>
          <div id="chart_in_progress_order" v-if="dataBarPending.series.length > 0">
            <apexchart type="bar" height="350" :width="IpadSize ? '500' : '400'" :options="dataBarPending.chartOptions" :series="dataBarPending.series"></apexchart>
          </div>
        </v-col>
        <v-col :cols="IpadSize || IpadProSize ? 12 : 8" class="boxDashboard mt-4">
          <span class="topicDashboard">จำนวน Order คงค้างทั้งหมดจำแนกตาม Stage</span>
          <v-data-table
            :headers="headersPending"
            :items="stageOrderAll.slice(0, 6)"
            :search="search"
            style="width:100%; white-space: nowrap;"
            hide-default-footer
            :items-per-page="-1"
            >
              <template v-slot:item="{ item, index }">
                <tr :style="index === stageOrderAll.length - 1 ? 'background-color: #d8e9ff;' : ''">
                  <td v-for="header in headersPending" :key="header.value"
                  >
                    {{ item[header.value] }}
                  </td>
                </tr>
              </template>
              <template slot="body.append">
              <tr style="position: sticky; z-index: 20; bottom: 0; background-color: #d8e9ff;">
                <th style="white-space: nowrap;">รวมทั้งหมด</th>
                <th style="text-align: center">{{ stageNormal }}</th>
                <th style="text-align: center">{{ stageMonitor }}</th>
                <th style="text-align: center">{{ stageAlarm }}</th>
                <th style="text-align: center">{{ stageTotal }}</th>
              </tr>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      shopAll: null,
      OrderSuccess_3_day: null,
      OrderSuccess_6_day: null,
      OrderSuccess_7_day: null,
      totalOrder: null,
      successOrder: null,
      successOrderAll: null,
      dateOrderSuccess: null,
      MerchantPending: null,
      pendingOrder: null,
      cancelOrder: null,
      returnOrder: null,
      returnOrderSuccess: null,
      lostOrder: null,
      orderNormal: null,
      orderMonitor: null,
      orderAlarm: null,
      percentTotal: null,
      percentSuccess: null,
      percentPending: null,
      percentCancel: null,
      percentReturn: null,
      percentReturnSuccess: null,
      percentSuccessAll: null,
      percentAverage: null,
      percent_3_days: null,
      percent_6_days: null,
      percent_7_days: null,
      percentPendingAll: null,
      percentMerchant: null,
      percentNormal: null,
      percentMonitor: null,
      percentAlarm: null,
      percentLost: null,
      diffTotal: null,
      diffSuccess: null,
      diffPending: null,
      diffCancel: null,
      diffReturn: null,
      diffReturnSuccess: null,
      diffLost: null,
      diffSuccessAll: null,
      diffAverage: null,
      diffPendingAll: null,
      diffMerchant: null,
      diffNormal: null,
      diffMonitor: null,
      diffAlarm: null,
      diffOrderNormal: null,
      diffOrderMonitor: null,
      diffOrderAlarm: null,
      // filter date
      menu: false,
      selectedFilterType: 'yearly',
      selectedYear: new Date().getFullYear(),
      selectedYearMonth: new Date().getFullYear(),
      selectedMonth: String(new Date().getMonth() + 1).padStart(2, '0'),
      selectedDays: [],
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      dateFilter: {
        start: '',
        end: ''
      },
      displayDateFilterText: '',
      // table
      headersSuccess: [
        { text: 'ร้านค้า', value: 'name_th', align: 'center', sortable: false, class: 'backgroundTHShop fontTable--text' },
        { text: 'Total order ร้านค้า (ทั้งหมด)', value: 'total_order', align: 'center', sortable: true, class: 'backgroundThTotal_shop fontTable--text' }
      ],
      headersPending: [
        { text: 'Stage', value: 'status_group', align: 'center', sortable: false, class: 'backgroundThStage fontTable--text' },
        { text: 'Order Normal', value: 'normal', align: 'center', sortable: true, class: 'backgroundThNormal fontTable--text' },
        { text: 'Order Monitor', value: 'monitor', align: 'center', sortable: true, class: 'backgroundThMonitor fontTable--text' },
        { text: 'Order Alarm', value: 'alarm', align: 'center', sortable: true, class: 'backgroundThAlarm fontTable--text' },
        { text: 'Total', value: 'total_orders', align: 'center', sortable: true, class: 'backgroundThTotal fontTable--text' }
      ],
      shop_order:
      {
        shop: 'papi',
        total_order: 500
      },
      listSummaryOrder: [],
      listSummarySuccess: [],
      itemsPerPage: 5,
      page: 1,
      showOrder: [],
      stageOrderAll: [],
      MerchantOrder: {},
      search: '',
      // filter menu mobile
      mobileMenu: [
        'แดชบอร์ด Order ทั้งหมด',
        'ข้อมูล Order สำเร็จสะสม',
        'ข้อมูล Order คงค้าง'
      ],
      mobileMenuSelect: 'แดชบอร์ด Order ทั้งหมด',
      refreshPayload: {},
      stageTotal: null,
      stageNormal: null,
      stageMonitor: null,
      stageAlarm: null
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getSummaryOrder('active')
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.generateYears()
  },
  computed: {
    dataDonut () {
      return {
        series: [],
        chartOptions: {
          chart: {
            type: 'donut',
            fontFamily: 'Sukhumvit Set',
            fontWeight: 900,
            fontSize: '24px'
            // width: '80%'
          },
          plotOptions: {
            pie: {
              donut: {
                size: '50%',
                labels: {
                  show: true,
                  total: {
                    show: true,
                    label: 'Order จัดส่งสะสม',
                    fontWeight: '700',
                    color: '#000',
                    formatter: () => {
                      return this.totalOrder
                    },
                    style: {
                      fontSize: '32px',
                      fontWeight: '700',
                      color: '#000'
                    }
                  }
                }
              }
            }
          },
          dataLabels: {
            enabled: true,
            formatter: (val, opts) => {
              return opts.w.config.series[opts.seriesIndex]
            },
            style: {
              fontFamily: 'Sukhumvit Set',
              fontSize: '12px'
            }
          },
          labels: [
            'Order สำเร็จ',
            'Order คงค้าง',
            'Order ยกเลิก',
            'Order ตีกลับ',
            'Order ตีกลับสำเร็จ',
            'Order สูญหาย'
          ],
          colors: ['#40c89b', '#42a5f5', '#6ab8de', '#c48a8a', '#7bd750', '#f19b18'],
          legend: {
            show: false
          },
          responsive: [{
            // breakpoint: 480,
            options: {
              chart: {
                width: 200
              },
              legend: {
                position: 'bottom'
              }
            }
          }]
        }
      }
    },
    dataBar () {
      return {
        series: [
          {
            name: 'สำเร็จ 1-3 วัน',
            data: [this.OrderSuccess_3_day]
          },
          {
            name: 'สำเร็จ 4-6 วัน',
            data: [this.OrderSuccess_6_day]
          },
          {
            name: 'สำเร็จมากกว่า 7 วัน',
            data: [this.OrderSuccess_7_day]
          }
        ],
        chartOptions: {
          chart: {
            type: 'bar',
            toolbar: { show: false }
          },
          responsive: [{
            // breakpoint: 480,
            options: {
              chart: {
                width: 50
              },
              legend: {
                position: 'bottom'
              }
            }
          }],
          plotOptions: {
            bar: {
              dataLabels: {
                position: 'top' // top, center, bottom
              },
              horizontal: false,
              barHeight: '70%',
              // columnWidth: '80%',
              borderRadius: 4
            }
          },
          dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
              fontSize: '12px',
              colors: ['#304758']
            }
          },
          xaxis: {
            labels: {},
            // categories: ['สำเร็จภายใน 1-3 วัน', 'สำเร็จภายใน 4-6 วัน', 'สำเร็จมากกว่า 7 วัน'],
            categories: ['Order Success']
          },
          yaxis: {
            title: {
              text: 'Order'
            }
          },
          colors: ['#06d6a0', '#ffd166', '#d56062']
        }
      }
    },
    dataBarPending () {
      return {
        series: [
          {
            name: 'Order Normal',
            data: [this.orderNormal]
          },
          {
            name: 'Order Monitor',
            data: [this.orderMonitor]
          },
          {
            name: 'Order Alarm',
            data: [this.orderAlarm]
          }
        ],
        chartOptions: {
          chart: {
            type: 'bar',
            toolbar: { show: false }
          },
          responsive: [{
            // breakpoint: 480,
            options: {
              chart: {
                width: 50
              },
              legend: {
                position: 'bottom'
              }
            }
          }],
          plotOptions: {
            bar: {
              dataLabels: {
                position: 'top'
              },
              horizontal: false,
              barHeight: '70%',
              columnWidth: '80%',
              borderRadius: 4
            }
          },
          dataLabels: {
            enabled: true,
            offsetY: -20,
            style: {
              fontSize: '12px',
              colors: ['#304758']
            }
          },
          xaxis: {
            labels: {},
            // categories: ['สำเร็จภายใน 1-3 วัน', 'สำเร็จภายใน 4-6 วัน', 'สำเร็จมากกว่า 7 วัน'],
            categories: ['Order Pending']
          },
          yaxis: {
            title: {
              text: 'Order'
            }
          },
          colors: ['#06d6a0', '#ffd166', '#d56062']
        }
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dateFilterText () {
      const isLeapYear = (year) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
      const monthsWith30Days = ['04', '06', '09', '11']
      const monthsWith31Days = ['01', '03', '05', '07', '08', '10', '12']
      // Get current date details
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = String(today.getMonth() + 1).padStart(2, '0') // month is zero-based
      const currentDay = String(today.getDate()).padStart(2, '0')
      if (this.selectedFilterType === 'yearly') {
        const isCurrentYear = parseInt(this.selectedYear) === currentYear
        const endDate = isCurrentYear
          ? `${currentYear}-${currentMonth}-${currentDay}`
          : `${this.selectedYear}-12-31`
        return `${this.selectedYear}-01-01 - ${endDate}`
      } else if (this.selectedFilterType === 'monthly') {
        if (this.selectedMonth !== null) {
          const isCurrentMonthAndYear =
            (parseInt(this.selectedYearMonth) === currentYear && this.selectedMonth === currentMonth)
          let endDate
          if (monthsWith31Days.includes(this.selectedMonth)) {
            endDate = isCurrentMonthAndYear ? currentDay : '31'
          } else if (this.selectedMonth === '02') {
            endDate = isLeapYear(parseInt(this.selectedYearMonth)) ? '29' : '28'
            if (isCurrentMonthAndYear) endDate = currentDay
          } else if (monthsWith30Days.includes(this.selectedMonth)) {
            endDate = isCurrentMonthAndYear ? currentDay : '30'
          }
          return `${this.selectedYearMonth}-${this.selectedMonth}-01 - ${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
        } else {
          return ''
        }
      } else if (this.selectedDays.length > 1) {
        if (this.selectedDays[0] > this.selectedDays[1]) {
          return `${this.selectedDays[1]} - ${this.selectedDays[0]}`
        } else {
          return `${this.selectedDays[0]} - ${this.selectedDays[1]}`
        }
      } else if (this.selectedDays.length === 1) {
        return `${this.selectedDays[0]} - ${this.selectedDays[0]}`
      } else {
        return this.selectedDays[0] || ''
      }
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardTransportMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'dashboardTransport')
        this.$router.push({ path: '/dashboardTransport' }).catch(() => {})
      }
    },
    selectedFilterType (val) {
      this.selectedYear = new Date().getFullYear()
      if (val === 'daily') {
        this.formatCurrentDate()
      } else if (val === 'monthly') {
        this.selectedMonth = String(new Date().getMonth() + 1).padStart(2, '0')
      }
    }
  },
  mounted () {
    this.selectedFilterType = 'yearly'
    this.displayDateFilterText = this.dateFilterText
    this.intervalId = setInterval(() => {
      this.getSummaryOrderRefresh()
    }, 10000)
  },
  beforeDestroy () {
    clearInterval(this.intervalId)
  },
  methods: {
    async getSummaryOrder (active) {
      const [start, end] = this.dateFilterText.includes(' - ')
        ? this.dateFilterText.split(' - ') : ['', this.dateFilterText]
      const filterDate = { start_date: start, end_date: end }
      this.refreshPayload = filterDate
      await this.getSummaryOrderAll(filterDate, active)
      await this.getSummarySuccess(filterDate, active)
      await this.getSummaryPending(filterDate, active)
      // try {
      //
      // } catch (error) {
      //   console.error('Error fetching summary order:', error)
      // }
    },
    async getSummaryOrderRefresh (active) {
      // const [start, end] = this.dateFilterText.includes(' - ')
      //   ? this.dateFilterText.split(' - ') : ['', this.dateFilterText]
      // const filterDate = { start_date: start, end_date: end }
      await this.getSummaryOrderAll(this.refreshPayload, active)
      await this.getSummarySuccess(this.refreshPayload, active)
      await this.getSummaryPending(this.refreshPayload, active)
      // try {
      //
      // } catch (error) {
      //   console.error('Error fetching summary order:', error)
      // }
    },
    async getSummaryOrderAll (filterDate, active) {
      if (active) {
        this.$store.commit('openLoader')
      }
      await this.$store.dispatch('actionsSummaryOrder', filterDate)
      var response = await this.$store.state.ModuleDashboardTransport.stateSummaryOrder
      if (response.code === 200) {
        if (active) {
          this.$store.commit('closeLoader')
        }
        this.listSummaryOrder = response.data
        // shop all
        this.shopAll = this.listSummaryOrder[0].shops.total_shop
        // total order
        this.totalOrder = this.listSummaryOrder[0].orders.items
        this.percentTotal = this.listSummaryOrder[0].orders.rate
        this.diffTotal = this.listSummaryOrder[0].orders.diff
        // order success
        this.successOrder = this.listSummaryOrder[0].success_order.items
        this.percentSuccess = this.listSummaryOrder[0].success_order.rate
        this.diffSuccess = this.listSummaryOrder[0].success_order.diff
        // order in progress
        this.pendingOrder = this.listSummaryOrder[0].in_progress_order.items
        this.percentPending = this.listSummaryOrder[0].in_progress_order.rate
        this.diffPending = this.listSummaryOrder[0].in_progress_order.diff
        // order cancel
        this.cancelOrder = this.listSummaryOrder[0].cancel_order.items
        this.percentCancel = this.listSummaryOrder[0].cancel_order.rate
        this.diffCancel = this.listSummaryOrder[0].cancel_order.diff
        // order return
        this.returnOrder = this.listSummaryOrder[0].return_order.items
        this.diffReturn = this.listSummaryOrder[0].return_order.diff
        this.percentReturn = this.listSummaryOrder[0].return_order.rate
        // order return success
        this.returnOrderSuccess = this.listSummaryOrder[0].return_success_order.items
        this.diffReturnSuccess = this.listSummaryOrder[0].return_success_order.diff
        this.percentReturnSuccess = this.listSummaryOrder[0].return_success_order.rate
        // order lost
        this.lostOrder = this.listSummaryOrder[0].lost_order.items
        this.diffLost = this.listSummaryOrder[0].lost_order.diff
        this.percentLost = this.listSummaryOrder[0].lost_order.rate
        // series data donut
        this.dataDonut.series = [this.successOrder, this.pendingOrder, this.cancelOrder, this.returnOrder, this.returnOrderSuccess, this.lostOrder]
      } else {
        if (active) {
          this.$store.commit('closeLoader')
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async getSummarySuccess (filterDate, active) {
      if (active) {
        this.$store.commit('openLoader')
      }
      await this.$store.dispatch('actionsSummarySuccess', filterDate)
      var response = this.$store.state.ModuleDashboardTransport.stateSummarySuccess
      if (response.code === 200) {
        if (active) {
          this.$store.commit('closeLoader')
        }
        // val success_and_average
        this.orderSuccessAverage = response.data[0].success_and_average
        this.percentSuccessAll = this.orderSuccessAverage.success_order.rate
        this.successOrderAll = this.orderSuccessAverage.success_order.items
        this.diffSuccessAll = this.orderSuccessAverage.success_order.difference
        this.dateOrderSuccess = this.orderSuccessAverage.average_day_delivery.days
        this.percentAverage = this.orderSuccessAverage.average_day_delivery.rate
        this.diffAverage = this.orderSuccessAverage.average_day_delivery.difference
        // val delivery_success
        this.deliverySuccess = response.data[0].delivery_success
        this.OrderSuccess_3_day = this.deliverySuccess.stage.total_normal
        this.percent_3_days = this.deliverySuccess.rate.rating_normal
        this.diffNormal = this.deliverySuccess.difference.difference_normal
        this.OrderSuccess_6_day = this.deliverySuccess.stage.total_monitor
        this.percent_6_days = this.deliverySuccess.rate.rating_monitor
        this.diffMonitor = this.deliverySuccess.difference.difference_monitor
        this.OrderSuccess_7_day = this.deliverySuccess.stage.total_alarm
        this.percent_7_days = this.deliverySuccess.rate.rating_alarm
        this.diffAlarm = this.deliverySuccess.difference.difference_alarm
        // tatle totle order
        this.showOrder = response.data[0].total_shop_order
      } else {
        if (active) {
          this.$store.commit('closeLoader')
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async getSummaryPending (filterDate, active) {
      if (active) {
        this.$store.commit('openLoader')
      }
      await this.$store.dispatch('actionsSummaryPending', filterDate)
      var response = this.$store.state.ModuleDashboardTransport.stateSummaryPending
      if (response.code === 200) {
        if (active) {
          this.$store.commit('closeLoader')
        }
        // val order pending
        this.orderPending = response.data[0].summary_order
        this.percentPendingAll = this.orderPending.rate
        this.diffPendingAll = this.orderPending.difference
        this.MerchantPending = response.data[0].summary_shop
        this.MerchantOrder = this.MerchantPending.items
        this.diffMerchant = this.MerchantPending.difference
        this.percentMerchant = this.MerchantPending.rate
        // stage order
        this.stageOrder = response.data[0].stage_group
        this.orderNormal = this.stageOrder.normal.items
        this.percentNormal = this.stageOrder.normal.rate
        this.diffOrderNormal = this.stageOrder.normal.difference
        this.orderMonitor = this.stageOrder.monitor.items
        this.percentMonitor = this.stageOrder.monitor.rate
        this.diffOrderMonitor = this.stageOrder.monitor.difference
        this.orderAlarm = this.stageOrder.alarm.items
        this.percentAlarm = this.stageOrder.alarm.rate
        this.diffOrderAlarm = this.stageOrder.alarm.difference
        // data table
        this.stageOrderAll = response.data[0].state
        this.stageTotal = this.stageOrderAll[6].total_orders
        this.stageNormal = this.stageOrderAll[6].normal
        this.stageMonitor = this.stageOrderAll[6].monitor
        this.stageAlarm = this.stageOrderAll[6].alarm
      } else {
        if (active) {
          this.$store.commit('closeLoader')
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async applyDateFilter () {
      const today = new Date()
      const currentYear = today.getFullYear()
      const currentMonth = String(today.getMonth() + 1).padStart(2, '0')
      const currentDay = String(today.getDate()).padStart(2, '0')

      if (this.selectedFilterType === 'yearly') {
        const isCurrentYear = parseInt(this.selectedYear) === currentYear
        this.dateFilter = {
          start: `${this.selectedYear}-01-01`,
          end: isCurrentYear ? `${currentYear}-${currentMonth}-${currentDay}` : `${this.selectedYear}-12-31`
        }
        this.menu = false
        this.getSummaryOrder('active')
      } else if (this.selectedFilterType === 'monthly') {
        const isLeapYear = (year) => (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0)
        const monthsWith31Days = ['01', '03', '05', '07', '08', '10', '12']
        const monthsWith30Days = ['04', '06', '09', '11']
        if (this.selectedMonth !== null) {
          const isCurrentMonthAndYear = (parseInt(this.selectedYearMonth) === currentYear && this.selectedMonth === currentMonth)
          let endDate
          if (this.selectedMonth === '02') {
            endDate = isLeapYear(parseInt(this.selectedYearMonth)) ? '29' : '28'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.dateFilter = {
              start: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          } else if (monthsWith30Days.includes(this.selectedMonth)) {
            endDate = '30'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.dateFilter = {
              start: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          } else if (monthsWith31Days.includes(this.selectedMonth)) {
            endDate = '31'
            if (isCurrentMonthAndYear) endDate = currentDay
            this.dateFilter = {
              start: `${this.selectedYearMonth}-${this.selectedMonth}-01`,
              end: `${this.selectedYearMonth}-${this.selectedMonth}-${endDate}`
            }
          }
          this.menu = false
          this.getSummaryOrder('active')
        } else {
          await this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกเดือน'
          })
          this.menu = true
        }
      } else if (this.selectedFilterType === 'daily') {
        if (this.selectedDays.length !== 0) {
          if (this.selectedDays.length === 1) {
            this.dateFilter = { start: this.selectedDays[0], end: this.selectedDays[0] }
          } else if (this.selectedDays.length > 1) {
            if (this.selectedDays[0] > this.selectedDays[1]) {
              this.dateFilter = {
                start: this.selectedDays[1],
                end: this.selectedDays[0]
              }
            } else {
              this.dateFilter = {
                start: this.selectedDays[0],
                end: this.selectedDays[1]
              }
            }
          }
          this.menu = false
          this.getSummaryOrder('active')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาเลือกวัน'
          })
          this.menu = true
        }
      }
      this.displayDateFilterText = this.dateFilterText
      if (this.selectedFilterType === 'yearly') {
        this.selectedMonth = null
        this.selectedDays = []
        this.selectedYearMonth = new Date().getFullYear()
      } else if (this.selectedFilterType === 'monthly') {
        this.selectedYear = new Date().getFullYear()
        this.selectedDays = []
      } else {
        this.selectedYear = new Date().getFullYear()
        this.selectedMonth = null
        this.selectedYearMonth = new Date().getFullYear()
      }
    },
    handleDateChange (dates) {
      this.selectedDays = dates
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    handleMenuChange () {
      this.$store.commit('openLoader')
      setTimeout(() => {
        this.$store.commit('closeLoader')
      }, 1000)
    },
    async generateYears () {
      this.years = []
      const currentYear = new Date().getFullYear()
      const years = []
      for (let year = 2022; year <= currentYear; year++) {
        years.push(year)
      }
      this.years = years
    },
    async formatCurrentDate () {
      var datenow = new Date()
      var year = datenow.getFullYear()
      var month = (datenow.getMonth() + 1).toString().padStart(2, '0')
      var day = datenow.getDate().toString().padStart(2, '0')
      var formattedDate = `${year}-${month}-${day}`
      this.selectedDays = [formattedDate]
    }
  }
}
</script>

<style scoped>
.not-sortable {
  pointer-events: none;
}
.boxDashboard {
  border-radius: .5vw;
  /* padding: 0.5vw; */
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  /* box-shadow: rgba(0, 0, 0, 0.1) 0px 4px 12px; */
}
.topicDashboard {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: .5vw;
  margin: .5vw;
  font-size: 18px;
  font-weight: 700;
  background-color: #f0f0f0;
  padding: 1vw;
  border-radius: .5vw;
}
.styleItem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 1vw;
  background-color: #f5f5f5;
  /* height: 15vw; */
}
.styleItemMobile {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 1vw;
  background-color: #f5f5f5;
  height: 35vw;
  gap: 5vw;
}
.styleBox {
  display: flex;
  justify-content: center;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  border-radius: .5vw;
  gap: 8vw;
}
.groupData {
  display: flex;
  flex-direction: column;
  gap: 1vw;
  width: 46vw;
}
.styleImg {
  width: 3vw;
  margin-bottom: 1.5vw;
  border-radius: 3vw;
}
.styleImgMobile {
  width: 20vw;
  margin-bottom: 1.5vw;
  border-radius: 15vw;
}
.styleformatboxMobile {
  border: 1px solid #e5e5e5;
  border-radius: 1vw;
  background-color: #f5f5f5;
  height: 55vw;
  margin-bottom: 2.5vw;
}
.boxTopic {
  font-weight: 700;
  /* font-size: 16px; */
  margin-bottom: .5vw;
}
.amount {
  font-weight: 700;
  color: #2a4597;
}
.percent {
  color: #2a4597;
}
/* .boxMain {
  padding: 0.5vw;
  box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 5px 0px, rgba(0, 0, 0, 0.1) 0px 0px 1px 0px;
  margin: .5vw;
  border-radius: 1vw;
} */
.formatItem {
  display: flex;
  align-items: center;
  gap: 4vw;
}
.formatWord {
  display: flex;
  flex-direction: column;
}
.sizeWord {
  font-size: 20px;
  font-weight: 700;
}
.formatDivider {
  border: 1.8px dashed;
  width: 16vw;
  color: #929292;
  margin-top: 1vw;
  margin-bottom: .5vw;
}
.formatDividerMobile {
  border: 1px dashed;
  /* width: 77vw; */
  color: #929292;
  margin-top: 3vw;
  margin-bottom: 1.5vw;
}
.fontSizetitle {
  font-size: 18px;
  font-weight: 700;
}
::v-deep th.backgroundTHShop {
  background-color: #d8e9ff !important;
  color: black !important;
}
::v-deep th.backgroundThTotal_shop {
  background-color: #d8e9ff !important;
  color: black !important;
}
</style>

<style>
.backgroundThStage,
.backgroundThTotal,
.backgroundTHShop,
.backgroundThTotal_shop {
  background-color: #d8e9ff;
  position: sticky;
}
.backgroundThNormal {
  background-color: #c0f5e3;
}
.backgroundThMonitor {
  background-color: #fff6a4;
}
.backgroundThAlarm {
  background-color: #f38183;
}
#SvgjsText3280 {
  font-size: 32px;
  font-weight: 700;
  color: #000; /* เปลี่ยนเป็นสีที่ต้องการ */
}
.apexcharts-datalabel-value {
  font-size: 34px !important;
  font-weight: 700 !important;
  color: #2a4597 !important;
}
.not-sortable {
  pointer-events: none;
}
</style>
