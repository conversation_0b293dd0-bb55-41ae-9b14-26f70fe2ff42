<template>
  <div class="page elevation-0">
    <div>
      <Header :OrderSellerDetailProp="OrderSellerDetailProp"/>
    </div>
    <div>
      <v-row dense>
        <v-col cols="12" md="12">
          <v-card class="elevation-0">
            <v-row dense>
              <v-col cols="12" md="12" class="pa-0">
                <Body :OrderSellerDetailProp="OrderSellerDetailProp"/>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <Summary :OrderSellerDetailProp="OrderSellerDetailProp"/>
      </v-row>
    </div>
    <v-btn v-if="this.printvisible" @click="printpo()" color="green lighten-1" dark fab bottom right fixed><v-icon>mdi-printer</v-icon></v-btn>
  </div>
</template>

<script>
export default {
  components: {
    Header: () => import('@/components/Quotation/QuotationSeller/Header'),
    Body: () => import('@/components/Quotation/QuotationSeller/Body'),
    Summary: () => import('@/components/Quotation/QuotationSeller/Summary')
  },
  data: () => ({
    OrderSellerDetailProp: [],
    printvisible: true
  }),
  async created () {
    this.$EventBus.$emit('getPath')
    // await this.$store.dispatch('actionListOrderBuyer')
    this.OrderSellerDetailProp = await this.$store.state.ModuleOrder.stateOrderDetailSeller.data
    // console.log('OrderSellerDetailProp', this.OrderSellerDetailProp)
    // this.orderDetail()
  },
  methods: {
    printpo () {
      setTimeout(() => {
        window.print()
      }, 500)
      this.printvisible = false
      setTimeout(() => {
        this.printvisible = true
      }, 2000)
    }
  }
}
</script>

<style scoped>
.page {
  width: 21cm;
  height: 29.7cm;
  padding: 1cm;
  margin: 0cm auto;
  border: 1px #d3d3d3 solid;
  border-radius: 5px;
  background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
@page {
  size: A4;
  margin: 0;
}
@page {
  size: A4;
  margin: 0;
}
@media print {
  .page {
    margin: 0;
    box-shadow: 0;
  }
  * {
    -webkit-print-color-adjust: exact;
  }
  .btnPrint {
    display: none;
  }
}
</style>
