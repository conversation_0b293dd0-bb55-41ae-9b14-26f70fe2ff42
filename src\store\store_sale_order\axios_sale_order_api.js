import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async ListSales (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/list_sale_data`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListCustomerOfSales (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/detail_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // สร้างข้อมูลกลุ่มลูกค้า
  async CreateTierSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_tier_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllUserShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/get_all_user_in_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // รายการกลุ่มลูกค้า
  async GetListTierSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_tier_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateSaleShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/create_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // รายละเอียดข้อมูลกลุ่มลูกค้า
  async GetDetailTierSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_tier_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditSaleShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/update_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // แก้ไขข้อมูลกลุ่มลูกค้า
  async EditTierSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_tier_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ลบข้อมูลกลุ่มลูกค้า
  async DeleteTierSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_tier_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เช็คข้อมูล tier ก่อนลบ
  async CheckTierSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_tier_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteSaleShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/delete_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/edit_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateCustomerAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/update_customer_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/delete_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPurchaserSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_position_purchaser`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetPurchaserSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/set_position_purchaser`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditPurchaserSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/edit_position_purchaser`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListUserForSelect (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_approver_for_select`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListUserPurchaserForSelect (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_purchaser_for_select`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPositionSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeletePurchaserSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/delete_position_purchaser`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListPositionSalesOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateListApproverSalesOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/create_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ImportCustomer (data) {
    const auth = await GetToken()
    try {
      // var response = await axios.post(`${process.env.VUE_APP_BACK_END2}import/user/excel`, data, auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}import/Customer/excel`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckListApproverSalesOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_approver_for_select`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/detail_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListApproveSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/list_approve_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetEditApproverSalesOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/edit_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApproveSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/approve_sale_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailApproverSalesOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/detail_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteApproverSalesOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/delete_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListQTSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/list_QT_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListOrderSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/list_order_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailQTSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/detail_QT_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailOrderSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/detail_order_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApproveQTSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/approve_QT_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteCustomerAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/delete_customer_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteCustomerInvoiceAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/delete_customer_inv_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailQTApproveSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver_sale/detail_approve_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadSlip (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/upload_slip_payment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PayCashPayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/pay_cash_payment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateMobiCashPayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobilyst/createordermobilystrefshare`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailDueDateSaleOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order_credit_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateOrderCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_order_credit_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_credit_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailEditCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_detail_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditDetailCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_detail_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetQrCodePayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/getqrcodect`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListOrderCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PointListCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/point/list_customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PointTransfer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/point/transfer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListCustomerTypeOption (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/customer/list_customer_type_option`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateSODocument (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}customer/create_so_document`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadDocumentSO (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/upload_document`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AttachDocumentSO (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/attach_document`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendEmailSO (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}email/customer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSellerShopWorkflowID (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_seller_shop_workflow_id`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async InsertSellerShopWorkflowID (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/insert_seller_shop_workflow_id`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CloneOrderB2B (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/clone_order_b2b`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
