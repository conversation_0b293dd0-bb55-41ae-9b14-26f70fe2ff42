<template>
  <div>
    <v-col :class="!MobileSize ? 'pt-6 pl-6' : ''">
      <span class="" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;"
        v-if="!MobileSize">
        โปรแกรม Affiliate
      </span>
      <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backtoSeller()">mdi-chevron-left</v-icon>
        โปรแกรม Affiliate
      </span>
    </v-col>
    <div class="d-flex justify-center">
      <v-card width="90%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4' : 'mb-4' ]">
        <v-card style="overflow-x: hidden; height: 750px;">
          <v-row class="py-8" dense no-gutters>
            <v-img src="@/assets/inetlogo.png" contain height="58" width="121" position="center" class="mr-4"></v-img>
          </v-row>
          <v-card-text style="font-size: 18px; text-align:center; overflow: hidden;">{{title}}</v-card-text>
          <v-divider></v-divider>
          <v-card-text style="height: 300px;">
            <div v-html="this.detail" style="padding: 15px 0;"></div>
          </v-card-text>
        </v-card>
        <v-card-text style="overflow: hidden;">
          <v-checkbox v-model="checkbox">
            <template v-slot:label>
              <div>
                รับทราบ
                <span style="text-decoration: underline; color:#27AB9C;">ข้อกำหนดดังกล่าว</span>
              </div>
            </template>
          </v-checkbox>
        </v-card-text>
        <v-row justify="center" class="mb-4">
        <v-card-actions>
          <v-btn :disabled="!checkbox" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="confirmConsent()">
              รับทราบ
          </v-btn>
        </v-card-actions>
        </v-row>
      </v-card>
    </div>
  </div>
</template>

<script>
import { Encode, Decode } from '@/services'
export default {
  props: ['title', 'detail'],
  components: {
  },
  data () {
    return {
      PositionName: '',
      one_id: '',
      dialog: false,
      data: '',
      checkbox: false,
      shopSellerID: '',
      dataDetail: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    this.dataDetail = []
    this.shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
    if (localStorage.getItem('list_shop_detail') !== null) {
      this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    } else {
      this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
      this.$router.push({ path: '/' }).catch(() => {})
    }
    if (this.dataDetail.array_position.some(element => element.position_name === 'เจ้าของร้าน')) {
      this.PositionName = 'เจ้าของร้าน'
    } else if (this.dataDetail.array_position.some(element => element.position_name === 'เจ้าของนิติบุคคล')) {
      this.PositionName = 'เจ้าของนิติบุคคล'
    } else {
      this.PositionName = 'ไม่ใช่เจ้าของร้านค้า'
    }
  },
  methods: {
    async confirmConsent () {
      this.$store.commit('openLoader')
      // console.log('dataDetail', this.dataDetail)
      var data = {
        seller_shop_id: this.shopSellerID,
        approve_consent: true,
        // position_name: this.dataDetail.array_position[0].position_name
        position_name: this.PositionName
      }
      await this.$store.dispatch('actionsSellerJoinAffiliate', data)
      var res = await this.$store.state.ModuleAffiliate.stateSellerJoinAffiliate
      if (res.success === true) {
        await this.$store.dispatch('actionsAuthorityUser')
        var responseShop = await this.$store.state.ModuleUser.stateAuthorityUser
        var ListSeller = responseShop.data.list_shop_detail
        var CheckShop = ListSeller.filter(x => x.seller_shop_id === parseInt(this.shopSellerID))
        if (CheckShop.length !== 0) {
          for (let i = 0; i < ListSeller.length; i++) {
            if (parseInt(this.shopSellerID) === ListSeller[i].seller_shop_id) {
              await localStorage.setItem('list_shop_detail', Encode.encode(ListSeller[i]))
            }
          }
        }
        // console.log('CheckShop', CheckShop)
        await this.$EventBus.$emit('AuthorityUser')
        await this.$EventBus.$emit('AuthorityUsers')
        await this.$EventBus.$emit('AuthorityUsersSellerMobile')
        await this.$EventBus.$emit('GetConsent')
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'warning', text: `${res.message}`, showConfirmButton: false, timer: 3500 })
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.border-scroll {
    border: 1px solid #EBEBEB;
}
</style>
