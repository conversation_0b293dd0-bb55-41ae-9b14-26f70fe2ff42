<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" style="background: #FFFFFF;">
    <v-card width="100%" height="100%" elevation="0" class="mx-0 my-0">
      <div class="d-flex align-center" v-if="!MobileSize && !IpadSize">
        <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการหมวดหมู่สินค้า</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>จัดการหมวดหมู่สินค้า</v-card-title>
        <v-col class="d-flex ml-1 justify-end">
          <v-switch v-model="statusCategory" inset color="#27ab9c" hide-details @change="changeTypeCategory"></v-switch>
          <span class="textFieldStepOne">ใช้หมวดหมู่ Custom</span>
        </v-col>
        <div :style="MobileSize ? 'display: grid !important;' : 'display: flex; justify-content: end'" v-if="statusCategory === true">
          <v-btn class="mr-2" color="#27AB9C" rounded style="color: #fff;" @click="dialogAddCategory = true">
            <v-icon class="mr-1">mdi-plus-circle</v-icon>
            <span>เพิ่มหมวดหมู่สินค้า</span>
          </v-btn>
        </div>
      </div>
      <div class="d-flex flex-column" v-else>
        <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการหมวดหมู่สินค้า</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>จัดการหมวดหมู่สินค้า</v-card-title>
        <v-col class="d-flex ml-1 justify-start">
          <v-switch v-model="statusCategory" inset color="#27ab9c" hide-details @change="changeTypeCategory"></v-switch>
          <span class="textFieldStepOne">ใช้หมวดหมู่ Custom</span>
        </v-col>
        <div class="mb-6" style="display: grid !important;" v-if="statusCategory === true">
          <v-btn class="mr-2" color="#27AB9C" rounded style="color: #fff;" @click="dialogAddCategory = true">
            <v-icon class="mr-1">mdi-plus-circle</v-icon>
            <span>เพิ่มหมวดหมู่สินค้า</span>
          </v-btn>
        </div>
      </div>
    </v-card>
    <div v-if="statusCategory === true">
      <v-card ref="showListCategory" :class="MobileSize ? 'mt-5 mx-2' : 'px-7 mt-5 mx-2 mb-3'" v-if="dataCategory.length !== 0">
        <v-col class="px-0 d-flex align-center" style="font-size: 16px;">
          <span :class="MobileSize ? 'ml-1' : ''"><b>หมวดหมู่สินค้า</b></span>
          <v-spacer></v-spacer>
          <!-- <v-btn outlined rounded color="#27AB9C" @click="changeDisable()">แก้ไขหมวดหมู่</v-btn> -->
        </v-col>
        <div class="row">
          <div class="col-12">
            <CategoryProduct :dataList="dataList" @updateCategory="getListcategory" :filterCategory="filterCategory" />
          </div>
        </div>
        <!-- <div class="row">
          <div class="col-8">
            <h3>Nested draggable</h3>
            <CategoryProduct :dataList="dataList" @updateCategory="getListcategory" :filterCategory="filterCategory" />
          </div>
          <rawDisplayer class="col-3" :value="dataList" title="dataList" />
        </div> -->
      </v-card>
      <div ref="noCategory" class="px-5 mt-5" v-else>
        <v-card :style="MobileSize ? 'margin-top: -8vw' : ''">
          <v-card-text class="d-flex align-center flex-column" style="padding: 7vw;">
            <v-img
              src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png"
              width="300"
              height="204"
              contain
            ></v-img>
            <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">คุณยังไม่มีรายการหมวดหมู่สินค้า</span>
          </v-card-text>
        </v-card>
      </div>
    </div>
    <v-col v-else>
      <v-card :style="MobileSize ? 'margin-top: -8vw' : ''">
          <v-card-text class="d-flex align-center flex-column" style="padding: 7vw;">
            <v-img
              src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png"
              width="300"
              height="204"
              contain
            ></v-img>
            <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'">ร้านค้านี้ ใช้หมวดหมู่หลักในการแสดงหมวดหมู่สินค้า</span>
          </v-card-text>
        </v-card>
    </v-col>
    <v-dialog persistent content-class="elevation-0" v-model="dialogAddCategory" :width="MobileSize || IpadSize ? '100%' : IpadProSize ? '50%' : '30%'">
      <v-card width="100%" style="background: #FFFFFF; border-radius: 20px;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>เพิ่มหมวดหมู่สินค้า</b></span>
            </v-col>
            <v-btn fab small @click="closeDialogAddCategory" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>

        <v-card-text class="pt-4" v-if="dataCategory.length !== 0">
          <span>เลือกหมวดหมู่</span>
          <treeselect
            :value="selectCategory || null"
            :options="filterCategory"
            :normalizer="normalizer"
            placeholder="เลือกหมวดหมู่"
            style="z-index: 999;"
            :append-to-body="true"
            @input="updateCategory"
          ></treeselect>
        </v-card-text>
        <v-card-text class="mt-2">
          <span>ชื่อหมวดหมู่</span>
          <v-text-field
            v-model="nameCategory"
            outlined
            dense
            placeholder="ระบุชื่อหมวดหมู่"
            :rules="Rules.nameCategory"
            :validate-on-blur="true"
          >
          </v-text-field>
        </v-card-text>
        <v-card-text style="margin-top: -2vw;">
          <v-row dense justify="center">
            <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogAddCategory">ยกเลิก</v-btn>
            <v-btn :disabled="nameCategory === ''" :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="opendialogAwaitCreate()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- dialog await create -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitCreate" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="closedialogAwaitCreate()"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เพิ่มหมวดหมู่สินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะเพิ่มหมวดหมู่สินค้าใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closedialogAwaitCreate()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="createGategory()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  components: {
    Treeselect,
    CategoryProduct: () => import(/* webpackPrefetch: true */ '@/components/Shop/ShopProduct/CategoryProduct')
  },
  data () {
    return {
      shopSellerID: '',
      dataCategory: [],
      filterCategory: [],
      allCategory: {},
      dataList: [],
      open: [],
      theRedP: true,
      dialogAddCategory: false,
      // dialogAddProduct: false,
      selectCategory: '',
      nameCategory: '',
      dialogAwaitCreate: false,
      detailProduct: [],
      checkbox: false,
      selectedItem: [],
      normalizer (node) {
        const id = 'hierachy'
        const labelKey = 'name'
        const childrenKey = 'children'
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey] && node[childrenKey].length ? node[childrenKey] : undefined
        }
      },
      disableDrag: true,
      Rules: {
        nameCategory: [v => !!v || 'กรุณากรอกชื่อสินค้า']
      },
      dataCheck: '',
      statusCategory: '',
      statusCustom: ''
      // option: {
      //   page: 1,
      //   offset: 20
      // },
      // // countProduct: '',
      // selectCategoryEdit: '',
      // dialogAwaitAdd: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.shopSellerID = JSON.parse(localStorage.getItem('shopSellerID'))
    this.getListcategory()
  },
  watch: {
    selectCategory (val) {
      // console.log(val, '***')
      this.dataCheck = val.split('_')
      // console.log(this.dataCheck, 888)
    },
    dataList (val) {
      // console.log('ดูค่า data list เปลี่นแปลงมั้ย', val)
    },
    filterCategory (val) {
      // console.log(val, '+++++')
    },
    selectedItem (val) {
      // console.log(val, 'selectedItem')
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageCategoryProductMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageCategoryProduct' }).catch(() => {})
      }
    },
    statusCategory (val) {
      // console.log(val, 'statusCategory')
    }
  },
  mounted () {
    this.$root.$on('updateCategory', this.getListcategory)
    window.scrollTo(0, 0)
  },
  beforeDestroy () {
    this.$root.$off('updateCategory', this.getListcategory)
  },
  methods: {
    // changeDisable () {
    //   this.disableDrag = false
    // },
    // cancelEdit () {
    //   this.$root.$emit('disableDrag', true)
    //   this.getListcategory()
    // },
    async changeTypeCategory () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopSellerID,
        status: this.statusCategory === true ? 'yes' : 'no'
      }
      await this.$store.dispatch('actionChangeStatusCategory', data)
      var res = await this.$store.state.ModuleShop.stateChangeStatusCategory
      if (res.code === 200) {
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    updateCategory (value) {
      this.selectCategory = value
    },
    closeDialogAddCategory () {
      this.dialogAddCategory = false
      this.selectCategory = ''
      this.nameCategory = ''
    },
    opendialogAwaitCreate () {
      this.dialogAddCategory = false
      this.dialogAwaitCreate = true
    },
    closedialogAwaitCreate () {
      this.nameCategory = ''
      this.dialogAwaitCreate = false
      this.dialogAddCategory = true
    },
    async getListcategory () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopSellerID
      }
      await this.$store.dispatch('actionListGategory', data)
      var res = await this.$store.state.ModuleShop.stateListGategory
      if (res.code === 200 || res.message === 'Not Found Group.') {
        if (res.code === 200) {
          this.dataCategory = res.data.product_group_data
          this.statusCustom = res.data.custom_category
          // console.log(this.statusCustom)
          if (this.statusCustom === 'yes') {
            this.statusCategory = true
          } else {
            this.statusCategory = false
          }
          // console.log('see data', this.dataCategory)
          this.filterCategory = this.dataCategory.map(item => {
            return {
              id: item.id,
              name: item.name,
              children: item.children || [],
              hierachy: item.hierachy,
              index: item.index_group
            }
          })
          // console.log(this.filterCategory, 'filterCategory')
          this.allCategory = [{
            id: 'special_1',
            name: 'ทั้งหมด',
            children: this.filterCategory
          }]
          this.dataList = await this.transformData(this.dataCategory)
          this.$root.$on('updateCategorySuccess', res.data.product_group_data)
        }
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    transformData (data) {
      return data.map(item => ({
        id: item.id,
        name: item.name,
        children: item.children ? this.transformData(item.children) : [],
        hierachy: item.hierachy,
        index: item.index_group
      }))
    },
    async createGategory () {
      this.$store.commit('openLoader')
      if (this.dataCheck.length >= 4) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'เพิ่มหมวดหมู่ย่อยได้ไม่เกิน  3 หมวดหมู่'
        })
        this.dialogAddCategory = true
        this.dialogAwaitCreate = false
        this.$store.commit('closeLoader')
      } else {
        var data = {
          seller_shop_id: this.shopSellerID,
          hierachy: this.selectCategory,
          name: this.nameCategory,
          index: 999
        }
        await this.$store.dispatch('actionCreateCategory', data)
        var res = await this.$store.state.ModuleShop.stateCreateCategory
        if (res.code === 200) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1000,
            timerProgressBar: true,
            icon: 'success',
            text: 'เพิ่มหมวดหมู่สินค้าสำเร็จ'
          })
          this.nameCategory = ''
          this.selectCategory = ''
          this.dialogAwaitCreate = false
          this.getListcategory()
          this.$store.commit('closeLoader')
        } else if (res.message === 'Limit Children 3.') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'warning',
            html: 'กรุณาเลือกหมวดหมู่ใหม่ <br> <span style="font-size: 14px;">(ไม่สามารถเลือกหมวดหมู่ที่อยู่ขั้นที่ 3 ของหมวดหมู่หลักได้)</span>'
          })
          this.dialogAddCategory = true
          this.dialogAwaitCreate = false
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
          this.$store.commit('closeLoader')
        }
      }
    }
  }
}
</script>

<style>

</style>
<style scoped>
  /* >>> .v-dialog {
    overflow-y: hidden;
  } */
  .v-input--selection-controls {
    margin-top: 0 !important;
    padding-top: 0 !important;
  }
  ::v-deep .v-input__slot {
    display: flex;
    justify-content: end;
  }
</style>
