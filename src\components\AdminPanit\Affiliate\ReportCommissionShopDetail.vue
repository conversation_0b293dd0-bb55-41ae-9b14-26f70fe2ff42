<template>
  <v-container :style="MobileSize ? 'background-color: white' : ''">
    <v-row>
        <v-col class="d-flex align-center">
            <v-btn @click="backPagesMobile" icon v-if="MobileSize">
                <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            <v-btn @click="backPages" icon v-else>
                <v-icon>mdi-chevron-left</v-icon>
            </v-btn>
            <span style="font-size: 24px; font-weight: bold;">รายละเอียดร้านค้า</span>
        </v-col>
    </v-row>
    <v-row class="my-5">
        <v-col>
            <v-card>
                <v-data-table
                    :headers="shopCommissionsShopHeadersMockupDetaildata"
                    :items="detailShopData"
                >
                    <template v-slot:[`item.index`]="{index}">
                        <span>{{index + 1}}</span>
                    </template>
                </v-data-table>
            </v-card>
        </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      shopCommissionsShopMockupDetaildata: [
        { username: 'นฤเบศน์ อุไรรัตน์', email: '<EMAIL>', phone: '0975987453', commission: 584.25 },
        { username: 'นาย อำเภอ', email: '<EMAIL>', phone: '0975987453', commission: 584.25 },
        { username: 'สมคิด อุไรรัตน์', email: '<EMAIL>', phone: '0975987453', commission: 84.50 },
        { username: 'สหรัฐ เชิดสุข', email: '<EMAIL>', phone: '0975987453', commission: 58.25 }
      ],
      shopCommissionsShopHeadersMockupDetaildata: [
        { text: 'ลำดับ', value: 'index', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อผู้ใช้', value: 'first_name_th', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทรศัพท์', value: 'phone', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชันที่ได้ (บาท)', value: 'estimate_commission', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      detailShopData: [],
      detailShopDataRequestBody: {
        seller_shop_id: null
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.getShopDetailData()
    this.refetchShopDetailData()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    async backPages () {
      this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
    },
    async backPagesMobile () {
      this.$router.push({ path: '/reportCommissionAffiliateAdminMobile' }).catch(() => {})
    },
    async getShopDetailData () {
      var response = this.$store.state.ModuleAdminManage.shopDetailUserCommissions
      this.detailShopData = response
    },
    async refetchShopDetailData () {
      this.detailShopDataRequestBody = { seller_shop_id: this.$route.query.sellerShop_id }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsShopComissionAffiliateTable', this.detailShopDataRequestBody)
      var response = await this.$store.state.ModuleAdminManage.stateShopComissionAffiliateTable
      this.detailShopData = response.data.shop_data[0].users
      this.$store.commit('closeLoader')
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/reportCommissionAffiliateAdminMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'reportCommissionAffiliateAdmin')
        this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
        // this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
      }
    }
  }
}
</script>
