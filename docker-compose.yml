version: '3'
services:
  b2b-dev:
    image: git.inet.co.th:5555/inet-b2b/b2b_vuejs:0.0.1-test 
    #build: .
    volumes:
      - /var/www/frontlog:/frontlog
      - ./nginx/mime.types:/etc/nginx/mime.types
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
    ports:
      - 82:81
      #- 2000:443
    restart: always
    
    #network_mode: host
#  web_front_ssl:
#    image: git.inet.co.th:5555/eprocurement/onemarket2021/web_onemarket:12.4-dev
#    image: web_onemarket_ssl
#    volumes:
#      - /var/www/frontlog:/frontlog
#      - /home/<USER>/docker-compose-for-market-front/nginx/nginx.conf:/etc/nginx/nginx.conf
#      - /home/<USER>/docker-compose-for-market-front/nginx/conf.d/default.conf:/etc/nginx/conf.d/default.conf
#    ports:
#      - 80:80
#      - 443:443
#    restart: always
#    network_mode: host

#    networks:
#      - my_docker_network
#networks:
#  my_docker_network:

#networks:
#  static-network:
#    ipam:
#      config:
#        - subnet : **********/16

# prepare php for nginx
  #phpfpm_internal_own:
  #ownweb_phpfpm:
    #image: git.inet.co.th:5555/eprocurement/onemarket2021/php_seo:0.6-ownUATSEO
    #ports:
      #- 9001:9000
    #restart: always
    #network_mode: host
    #environment:
      #MYSQL_HOSTNAME: **********
      #MYSQL_DBNAME: ownweb_uat
      #MYSQL_DBUSERNAME: omnipotent
      #MYSQL_DBPASSWORD: EverybodyStartsToImproveBitsByBitsBetterAndFinallyTheyLevelUp
