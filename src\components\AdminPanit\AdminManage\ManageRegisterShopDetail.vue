<template>
  <v-container :class="MobileSize ? 'pa-0' : IpadSize ? '' : 'pa-3 pt-6'">
    <div style="background-color: #ffffff" :class="MobileSize ? 'pa-3' : ''" class="d-flex justify-center">
        <v-form ref="formOne" :lazy-validation="lazyOne">
          <v-row class="mb-3">
            <v-col cols="12">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>
              <span style="font-size: 24px; font-weight: bold;">ลงทะเบียนสนใจเปิดร้านค้าออนไลน์ Nex Gen Commerce</span>
            </v-col>
            <!-- <v-col cols="3" class="d-flex justify-end">
              <v-btn v-if="isEditing" rounded @click="EditRequestShopData" color="#27ab9c">
                <span class="white--text">แก้ไข</span>
              </v-btn>
            </v-col> -->
          </v-row>
          <v-row dense>
            <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
              <span class="mr-3" style="font-size: 16px; font-weight: bold;">ประเภทธุรกิจ</span>
              <v-radio-group
                v-model="businessType"
                row
                class="mt-0"
                hide-details
                readonly
              >
                <v-radio
                    label="บุคคลธรรมดา"
                    value="บุคคลธรรมดา"
                ></v-radio>
                <v-radio
                    label="นิติบุคคล"
                    value="นิติบุคคล"
                ></v-radio>
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
              <span class="mr-3" style="font-size: 16px; font-weight: bold;">ประเภทย่อยร้านค้า</span>
              <v-radio-group
                v-model="shopType"
                row
                class="mt-0"
                hide-details
                readonly
              >
                <v-radio
                    label="ร้านค้าทั่วไป"
                    value="ร้านค้าทั่วไป"
                ></v-radio>
                <v-radio
                    label="OTOP"
                    value="OTOP"
                ></v-radio>
                <v-radio
                    label="วิสาหกิจชุมชน"
                    value="วิสาหกิจชุมชน"
                ></v-radio>
                <v-radio
                    label="วิสาหกิจเพื่อสังคม"
                    value="วิสาหกิจเพื่อสังคม"
                ></v-radio>
                <v-radio
                    label="ประชารัฐรักสามัคคี"
                    value="ประชารัฐรักสามัคคี"
                ></v-radio>
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
              <span class="mr-3" style="font-size: 16px; font-weight: bold;">รูปแบบการเปิดร้านที่สนใจ</span>
              <v-radio-group
                v-model="openType"
                row
                class="mt-0"
                hide-details
                readonly
              >
                <v-radio
                    label="ร้านค้าซื้อขายปกติ"
                    value="ร้านค้าซื้อขายปกติ"
                ></v-radio>
                <v-radio
                    label="ร้านค้า + e-Tax invoice & e-Receipt"
                    value="ร้านค้า + e-Tax invoice & e-Receipt"
                ></v-radio>
                <v-radio
                    label="ร้านค้า + OMS"
                    value="ร้านค้า + OMS"
                ></v-radio>
                <v-radio
                    label="ร้านค้า + ERP"
                    value="ร้านค้า + ERP"
                ></v-radio>
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" :class="MobileSize || IpadSize || IpadProSize ? '' : IpadSize ? '' : 'd-flex pb-0'">
              <span style="font-size: 16px; font-weight: bold;" class="mr-3 mt-2">ช่องทางที่ทำให้ท่านสนใจเปิดร้านกับ Nex Gen Commerce</span>
              <v-row :class="MobileSize || IpadSize || IpadProSize ? 'pt-5' : ''">
                <v-col cols="12" md="6" sm="6">
                  <v-select
                    v-model="selectInterestChannel"
                    :items="itemsInterestChannel"
                    outlined
                    dense
                    placeholder="เลือกช่องทาง"
                    style="border-radius: 8px;"
                    readonly
                    class="setCustomSelect"
                    hide-details
                  ></v-select>
                </v-col>
                <v-col cols="12" md="6" sm="6" v-if="selectInterestChannel === 'Social Media'">
                  <v-select
                    v-model="selectedSocialMedia"
                    :items="itemSocial"
                    outlined
                    dense
                    placeholder="เลือกช่องทาง Social Media"
                    style="border-radius: 8px;"
                    readonly
                    hide-details
                  ></v-select>
                </v-col>
                <v-col cols="12" md="6" sm="6" v-if="selectInterestChannel === 'อื่นๆ (โปรดระบุ)...'">
                  <v-text-field
                    v-model="reasonChanel"
                    outlined
                    dense
                    placeholder="ระบุช่องทาง"
                    style="border-radius: 8px;"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense class="mt-5">
            <v-col cols="12">
              <span style="font-size: 18px; font-weight: bold;">ข้อมูลร้านค้า</span>
            </v-col>
            <v-col cols="12" md="4" sm="12" :class="MobileSize ? 'pt-5' : ''">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">ชื่อร้านค้า </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="shopName"
                    outlined
                    dense
                    placeholder="ระบุชื่อร้านค้า"
                    style="border-radius: 8px;"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="12">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ประเภทสินค้า </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="productType"
                      outlined
                      dense
                      :rules="Rules.productType"
                      placeholder="ระบุประเภทสินค้า"
                      style="border-radius: 8px;"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">หมายเลขประจำตัวผู้เสียภาษี </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="TaxId"
                      outlined
                      dense
                      placeholder="ระบุหมายเลขประจำตัวผู้เสียภาษี"
                      style="border-radius: 8px;"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
            <!-- <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">ชื่อ </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="firstName"
                    outlined
                    dense
                    :rules="Rules.firstname"
                    placeholder="ระบุชื่อ"
                    style="border-radius: 8px;"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">นามสกุล </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="lastName"
                    outlined
                    dense
                    :rules="Rules.lastname"
                    placeholder="ระบุนามสกุล"
                    style="border-radius: 8px;"
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">หมายเลขโทรศัพท์ร้านค้า </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="phoneNumber"
                    outlined
                    dense
                    placeholder="ระบุหมายเลขโทรศัพท์"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">อีเมลร้านค้า </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="email"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">รายละเอียดที่อยู่ร้านค้า (เช่น อาคาร ตึก ฯ) </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="detail"
                    outlined
                    dense
                    :rules="Rules.address"
                    placeholder="ระบุรายละเอียดที่อยู่ (เช่น อาคาร ตึก ฯ)"
                    style="border-radius: 8px;"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="4">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">เลขที่ </span><span style="color: red">*</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="houseNo"
                    outlined
                    dense
                    placeholder="เลขที่"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9/]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="4">
              <v-row>
                <v-col cols="12">
                  <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
                </v-col>
                <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-3' : ''">
                  <v-text-field
                    v-model="subdistricttext"
                    outlined
                    dense
                    placeholder="ตำบล"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" sm="4">
              <v-row>
                <v-col cols="12">
                  <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
                </v-col>
                <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-3' : ''">
                  <v-text-field
                    v-model="districtText"
                    outlined
                    dense
                    placeholder="อำเภอ"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row>
                <v-col cols="12">
                  <span>จังหวัด</span><span style="color: red;"> *</span>
                </v-col>
                <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-3' : ''">
                  <v-text-field
                    v-model="provinceText"
                    outlined
                    dense
                    placeholder="จังหวัด"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6">
              <v-row>
                <v-col cols="12">
                  <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                </v-col>
                <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-3' : ''">
                  <v-text-field
                    v-model="zipcodeText"
                    outlined
                    dense
                    placeholder="รหัสไปรษณีย์"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col> -->
          </v-row>
          <v-row :class="IpadSize || IpadProSize ? 'pt-5' : ''">
            <v-col>
              <span style="font-size: 16px; font-weight: bold;">ช่องทางที่ต้องการให้ทีมงานติดต่อประสานงาน</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">ชื่อผู้ติดต่อ </span><span style="color: red;"> *</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact1Name"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">หมายเลขโทรศัพท์ </span><span style="color: red;"> *</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact1Tel"
                    outlined
                    dense
                    placeholder="หมายเลขโทรศัพท์"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">line ID </span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact1LineId"
                    outlined
                    dense
                    placeholder="Line ID"
                    style="border-radius: 8px;"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">อีเมล </span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact1Email"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <span style="font-size: 16px; font-weight: bold;">ช่องทางที่ต้องการให้ทีมงานติดต่อประสานงานเพิ่มเติม</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">ชื่อผู้ติดต่อ </span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact2Name"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">หมายเลขโทรศัพท์ </span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact2Tel"
                    outlined
                    dense
                    placeholder="หมายเลขโทรศัพท์"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">line ID </span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact2LineId"
                    outlined
                    dense
                    placeholder="Line ID"
                    style="border-radius: 8px;"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" sm="6">
              <v-row dense>
                <v-col cols="12">
                  <span style="font-size: 16px;">อีเมล </span>
                </v-col>
                <v-col cols="12">
                  <v-text-field
                    v-model="contact2Email"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    oninput="this.value = this.value.replace(/[^a-zA-Z0-9@.]/g, '')"
                    readonly
                    hide-details
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <!-- <v-row>
            <v-col class="d-flex justify-end">
              <v-btn rounded outlined @click="cancleEditRequestShopData" color="#27ab9c">
                <span>ยกเลิก</span>
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded outlined @click="clearEditRequestShopData" color="#27ab9c" class="mr-2">
                <span>ล้างค่า</span>
              </v-btn>
              <v-btn rounded @click="confirmSentRequestShopData" color="#27ab9c">
                <span class="white--text">บันทึก</span>
              </v-btn>
            </v-col>
          </v-row> -->
        </v-form>
      </div>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      businessType: '',
      checkDistrictError: '',
      checkSubDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      subdistrictTextOther: '',
      subdistricttext: '',
      districtTextOther: '',
      provinceTextOther: '',
      zipcodeTextOther: '',
      districtText: '',
      provinceText: '',
      zipcodeText: '',
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        seller_name: [
          v => !!v || 'กรุณากรอกชื่อฝ่ายขาย',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        shop_name: [
          v => !!v || 'กรุณากรอกชื่อร้านค้า',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        short_name: [
          v => !!v || 'กรุณากรอกชื่อย่อร้านค้า',
          v => v.length <= 5 || 'กรุณากรอกชื่อย่อร้านค้าไม่เกิน 5 ตัวอักษร',
          v => /^[A-Za-z0-9\s]+$/.test(v) || 'กรุณากรอกแค่ตัวอักษรภาษาอังกฤษและตัวเลข'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 ตัว',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        firstname: [
          v => !!v || 'กรุณากรอกชื่อ',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        lastname: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        contactTel: [
          v => !v || /^[0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => !v || (v.length >= 9 && v.length <= 10) || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ',
          v => (v.length > 8 && v.length <= 20) || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        blankEmail: [
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        Merchant: [
          v => !!v || 'กรุณาระบุรหัสการจ่ายเงิน',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        bussinessType: [
          v => !!v || 'กรุณาเลือกประเภทธุรกิจ'
        ],
        contactType: [
          v => !!v || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemContact: [
          v => !!v || 'กรุณาเลือกประเภทสัญญา'
        ],
        ItempayType: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemPayment: [
          v => v.length !== 0 || 'กรุณาเลือกช่องทางการชำระเงิน'
        ],
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ],
        ItemBank: [
          v => v.length !== 0 || 'กรุณาเลือกธนาคาร'
        ],
        installment: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการผ่อนชำระ'
        ],
        BranchName: [
          v => v.length !== 0 || 'กรุณากรอกชื่อสาขาร้านค้า'
        ],
        ItemServiceType: [
          v => v.length !== 0 || 'กรุณาเลือกประเภทการให้บริการ'
        ],
        ItemPaymentType: [
          v => v.length !== 0 || 'กรุณาเลือกประเภทการชำระเงิน'
        ],
        bookBank: [
          v => v.length !== 0
        ],
        postCode: [
          v => v.length === 5 || 'กรุณากรอกรหัสไปรษณีย์'
        ],
        bookBankNo: [
          v => v.length > 9 || 'กรุณากรอกเลขบัญชีธนาคาร'
        ]
      },
      shopDataPayload: {
        business_type: '',
        shop_type: '',
        open_type: '',
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        house_no: '',
        street: '',
        province: '',
        tambon: '',
        amphoe: '',
        zipcode: '',
        detail: '',
        contact1_email: '',
        contact1_line_id: '',
        contact2_email: '',
        contact2_line_id: ''
      },
      shopType: '',
      openType: '',
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      houseNo: '',
      street: '',
      province: '',
      tambon: '',
      amphoe: '',
      zipcode: '',
      detail: '',
      contact1Email: '',
      contact1LineId: '',
      contact2Email: '',
      contact2LineId: '',
      isEditing: true,
      modalAwaitCancelOrder: false,
      modalSuccessCancelOrder: false,
      userId: -1,
      contact2Tel: '',
      contact1Tel: '',
      lazyOne: false,
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      selectInterestChannel: '',
      itemsInterestChannel: [
        'กรมการพัฒนาชุมชน กระทรวงมหาดไทย (พช.)',
        'Dx Platform / SME D Bank',
        'ธนาคารออมสิน',
        'สำนักงานส่งเสริมวิสาหกิจขนาดกลางและขนาดย่อม (สสว.)',
        'BizClub',
        'สถาบันอาหาร',
        'บูธ Nex Gen',
        'เจ้าหน้าที่ Nex Gen Commerce',
        'BigSeller Thailand 2025 E-commerce Summit',
        'กรมส่งเสริมอุตสาหกรรม',
        'กรมการพัฒนาชุมชน กระทรวงมหาดไทย',
        'กรมพัฒนาธุรกิจการค้า',
        // 'Tiktok',
        // 'Line',
        // 'Instagram',
        // 'Facebook',
        // 'Youtube',
        // 'Lemon8',
        // 'X (Twitter)',
        // 'Threads',
        'เจ้าหน้าที่ Nex Gen Commerce',
        'Social Media',
        'อื่นๆ (โปรดระบุ)...'
      ],
      contact1Name: '',
      contact2Name: '',
      shopName: '',
      itemSocial: [
        'Tiktok',
        'Line',
        'Instagram',
        'Facebook',
        'Youtube',
        'Lemon8',
        'X (Twitter)',
        'Threads'
      ],
      selectedSocialMedia: '',
      reasonChanel: '',
      productType: '',
      TaxId: ''
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.getListRegisShop()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/ManageRegisterShopDetailMobile?shopId=${this.$route.query.shopId}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageRegisterShopDetail?shopId=${this.$route.query.shopId}` }).catch(() => {})
      }
    }
  },
  methods: {
    async getListRegisShop () {
      var shopId = await this.$route.query.shopId
      await localStorage.setItem('shopIdParam', shopId)
      var data
      data = {
        id: this.$route.query.shopId
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsStoreRegisDetail', data)
      var response = await this.$store.state.ModuleAdminManage.stateStoreRegisDetail
      this.$store.commit('closeLoader')
      //   console.log(response, 'response')
      if (response.code === 200) {
        this.businessType = response.data.business_type
        this.shopType = response.data.shop_type
        this.openType = response.data.open_type
        this.email = response.data.email
        this.phoneNumber = response.data.phone_number
        this.houseNo = response.data.house_no
        this.provinceText = response.data.province
        this.subdistricttext = response.data.tambon
        this.districtText = response.data.amphoe
        this.zipcodeText = response.data.zipcode
        this.detail = response.data.detail
        this.contact1Email = response.data.contact1_email
        this.contact1LineId = response.data.contact1_line_id
        this.contact2Email = response.data.contact2_email
        this.contact2LineId = response.data.contact2_line_id
        this.contact1Tel = response.data.contact1_tel
        this.contact2Tel = response.data.contact2_tel
        this.contact1Name = response.data.contact1_name
        this.contact2Name = response.data.contact2_name
        this.shopName = response.data.shop_name
        this.selectInterestChannel = await this.checkComma(response.data.interest_channel)
        this.productType = response.data.product_type
        this.TaxId = response.data.tax_id
        // this.items = response.data
      }
    },
    async checkComma (val) {
      var str = val.split(',', 2)
      if (str.length === 2) {
        if (str[0] === 'Social Media') {
          this.selectedSocialMedia = str[1]
        } else if (str[0] === 'อื่นๆ (โปรดระบุ)...') {
          this.reasonChanel = str[1]
        }
      }
      return str[0]
    },
    async backtoMenu () {
      if (!this.MobileSize) {
        window.scrollTo(0, 0)
        this.$router.push({ path: '/ManageRegisterShop' }).catch(() => {})
      } else {
        window.scrollTo(0, 0)
        this.$router.push({ path: '/ManageRegisterShopMobile' }).catch(() => {})
      }
    }
  }
}
</script>

<style>

</style>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
  width: 300px;
}
</style>
