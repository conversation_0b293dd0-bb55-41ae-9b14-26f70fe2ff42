<template>
  <div v-if="disabledGroupShop">
    <v-container v-if="!MobileSize">
      <v-row dense justify="center">
        <v-card elevation="0" style="max-width: 1223px; width: 100%; max-height: 570px; height: 100%;" class="mt-6 backgroundColorPage">
          <!-- size pc -->
          <div v-if="!MobileSize && !IpadSize && !IpadProSize">
            <!-- ของใหม่ -->
            <!-- 1 Layout -->
            <v-row dense v-if="num_layout_banner === '1'">
              <v-col cols="12" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" max-height="400px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" max-height="400px" max-width="1223px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <!-- 2 Layout -->
            <v-row dense v-if="num_layout_banner === '2'">
              <v-col cols="6" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" max-height="300px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" max-height="300px" max-width="610px" style="border-radius: 4px; object-fit: none;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <!-- 3 Layout -->
            <v-row dense v-if="num_layout_banner === '3'">
              <div class="d-flex mx-auto ml-1" style="width: 100%; gap: 3px;">
                <div>
                  <v-card elevation="0" :outlined="false" max-height="328px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="imageGroupBanner[0].image" max-height="328px" max-width="854px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(imageGroupBanner[0].link, imageGroupBanner[0].name, imageGroupBanner[0].action_id)"></v-img>
                  </v-card-text>
                </v-card>
                </div>
                <div class="d-flex flex-column justify-between" style="gap: 4px;">
                  <div v-for="(item, index) in imageGroupBanner.slice(1)" :key="index">
                    <v-card elevation="0" :outlined="false" max-height="162px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="162px" max-width="366px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
              <!-- รายการแรก (ใหญ่) -->
              <!-- <v-col cols="12" md="8">
                <v-card :outlined="false" max-height="350px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="imageGroupBanner[0].image" max-height="350px" max-width="780px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(imageGroupBanner[0].link, imageGroupBanner[0].name, imageGroupBanner[0].action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col> -->

              <!-- รายการที่เหลือ (เล็ก) -->
              <!-- <v-col cols="12" md="4">
                <v-row dense>
                  <v-col cols="12" v-for="(item, index) in imageGroupBanner.slice(1)" :key="index" >
                    <v-card :outlined="false" max-height="171px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="171px" max-width="435px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col> -->
            </v-row>
            <v-row dense v-if="num_layout_banner === '4'">
              <!-- รายการแรก (ใหญ่) -->
              <div class="d-flex mx-auto ml-1" style="width: 100%; gap: 3px;">
                <div class="d-flex flex-column justify-between" style="gap: 4px;">
                  <div class="d-flex flex-column justify-between" style="gap: 4px;">
                    <div v-for="(item, index) in [imageGroupBanner[0], imageGroupBanner[2]]" :key="index">
                      <v-card elevation="0" :outlined="false" max-height="200px" max-width="100%">
                        <v-card-text class="pa-0">
                          <v-img :src="item.image" max-height="200px" max-width="610px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                        </v-card-text>
                      </v-card>
                    </div>
                  </div>
                </div>
                <div class="d-flex flex-column justify-between" style="gap: 4px;">
                  <div v-for="(item, index) in [imageGroupBanner[1], imageGroupBanner[3]]" :key="index">
                    <v-card elevation="0" :outlined="false" max-height="200px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="200px" max-width="610px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
              <!-- <v-col cols="6" v-for="(item, index) in imageGroupBanner.slice(0, 2)" :key="index">
                <v-card :outlined="false" max-height="277px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)" max-height="171px" max-width="610px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="6" v-for="(item, index) in imageGroupBanner.slice(2, 4).reverse()" :key="item.id || `reversed-${index}`">
                <v-card :outlined="false" max-height="277px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)" max-height="171px" max-width="610px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col> -->
            </v-row>
            <!-- ของเก่า -->
            <!-- 1 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%;" v-if="num_layout_banner === '1'">
              <div style="width: 100%; max-height: 420px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 420px; cursor: pointer; border-radius: 4px;" @click="goToLink('leftTop')"></v-img>
                </div>
              </div>
            </div> -->
            <!-- 2 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="num_layout_banner === '2'">
              <div style="width: 50%; max-height: 300px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 300px; cursor: pointer; border-radius: 4px;" @click="goToLink('leftTop')"></v-img>
                </div>
              </div>
              <div style="width: 50%; max-height: 300px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightTop !== ''" :src="backgroundImageLayoutRightTop" style="height: 300px; cursor: pointer; border-radius: 4px;" @click="goToLink('rightTop')"></v-img>
                </div>
              </div>
            </div> -->
            <!-- 3 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="num_layout_banner === '3'">
              <div style="width: 66vw; max-height: 390px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 390px; cursor: pointer; border-radius: 4px;" @click="goToLink('leftTop')"></v-img>
                </div>
              </div>
              <div style="width: 34vw; gap: 6px; max-height: 390px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightTop !== ''" :src="backgroundImageLayoutRightTop" style="height: 192px; cursor: pointer; border-radius: 4px;" @click="goToLink('rightTop')"></v-img>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightBottom !== ''" :src="backgroundImageLayoutRightBottom" style="height: 192px; cursor: pointer; border-radius: 4px;" @click="goToLink('rightBottom')"></v-img>
                </div>
              </div>
            </div> -->
            <!-- 4 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 570px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="num_layout_banner === '4'">
              <div style="width: 50%; gap: 6px; max-height: 570px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 277px; cursor: pointer; border-radius: 4px;" @click="goToLink('leftTop')"></v-img>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftBottom !== ''" :src="backgroundImageLayoutLeftBottom" style="height: 277px; cursor: pointer; border-radius: 4px;" @click="goToLink('leftBottom')"></v-img>
                </div>
              </div>
              <div style="width: 50%; gap: 6px; max-height: 570px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightTop !== ''" :src="backgroundImageLayoutRightTop" style="height: 277px; cursor: pointer; border-radius: 4px;" @click="goToLink('rightTop')"></v-img>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightBottom !== ''" :src="backgroundImageLayoutRightBottom" style="height: 277px; cursor: pointer; border-radius: 4px;" @click="goToLink('rightBottom')"></v-img>
                </div>
              </div>
            </div> -->
          </div>
          <!-- size ipadpro -->
          <div class="mx-1" v-if="IpadProSize">
            <!-- 1 Layout -->
            <v-row dense v-if="num_layout_banner === '1'">
              <v-col cols="12" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" max-height="400px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" max-height="400px" max-width="100%" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <!-- 2 Layout -->
            <v-row dense v-if="num_layout_banner === '2'">
              <v-col cols="6" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" max-height="300px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" max-height="300px" max-width="100%" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <!-- 3 Layout -->
            <v-row dense v-if="num_layout_banner === '3'">
              <div class="d-flex mx-auto" style="width: 100%; gap: 3px;">
                <div style="width: 70%;">
                  <v-card elevation="0" :outlined="false" max-height="328px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="imageGroupBanner[0].image" max-height="328px" max-width="854px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(imageGroupBanner[0].link, imageGroupBanner[0].name, imageGroupBanner[0].action_id)"></v-img>
                  </v-card-text>
                </v-card>
                </div>
                <div class="d-flex flex-column justify-between" style="gap: 4px; width: 30%;">
                  <div v-for="(item, index) in imageGroupBanner.slice(1)" :key="index">
                    <v-card elevation="0" :outlined="false" max-height="162px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="162px" max-width="366px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
              <!-- รายการแรก (ใหญ่) -->
              <!-- <v-col cols="12" md="8">
                <v-card :outlined="false" max-height="350px">
                  <v-card-text class="pa-0">
                    <v-img :src="imageGroupBanner[0].image" max-width="100%" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(imageGroupBanner[0].link, imageGroupBanner[0].name, imageGroupBanner[0].action_id)" min-height="327px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col> -->

              <!-- รายการที่เหลือ (เล็ก) -->
              <!-- <v-col cols="12" md="4">
                <v-row dense>
                  <v-col cols="12" v-for="(item, index) in imageGroupBanner.slice(1)" :key="index" >
                    <v-card :outlined="false" max-height="171px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="171px" max-width="435px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col> -->
            </v-row>
            <v-row dense v-if="num_layout_banner === '4'">
              <div class="d-flex mx-auto" style="width: 100%; gap: 3px;">
                <div class="d-flex flex-column justify-between" style="gap: 4px; width: 50%;">
                  <div v-for="(item, index) in [imageGroupBanner[0], imageGroupBanner[2]]" :key="index">
                    <v-card elevation="0" :outlined="false" max-height="200px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="200px" max-width="610px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
                <div class="d-flex flex-column justify-between" style="gap: 4px; width: 50%;">
                  <div v-for="(item, index) in [imageGroupBanner[1], imageGroupBanner[3]]" :key="index">
                    <v-card elevation="0" :outlined="false" max-height="200px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="200px" max-width="610px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
              <!-- รายการแรก (ใหญ่) -->
              <!-- <v-col cols="6" v-for="(item, index) in imageGroupBanner.slice(0,2)" :key="index">
                <v-card :outlined="false" max-height="277px">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" max-width="100%" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)" height="277px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="6" v-for="(item, index) in imageGroupBanner.slice(2, 4).reverse()" :key="item.id || `reversed-${index}`">
                <v-card :outlined="false" max-height="277px">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" max-width="100%" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)" height="277px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col> -->
            </v-row>
            <!-- 1 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%;" v-if="num_layout_banner === '1'">
              <div style="width: 100%; max-height: 380px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 380px; cursor: pointer;" @click="goToLink('leftTop')"></v-img>
                </div>
              </div>
            </div> -->
            <!-- 2 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="num_layout_banner === '2'">
              <div style="width: 48vw; max-height: 250px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 250px; cursor: pointer;" @click="goToLink('leftTop')"></v-img>
                </div>
              </div>
              <div style="width: 48vw; max-height: 250px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightTop !== ''" :src="backgroundImageLayoutRightTop" style="height: 250px; cursor: pointer;" @click="goToLink('rightTop')"></v-img>
                </div>
              </div>
            </div> -->
            <!-- 3 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 550px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="num_layout_banner === '3'">
              <div style="width: 63vw; max-height: 350px;">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 350px; cursor: pointer;" @click="goToLink('leftTop')"></v-img>
                </div>
              </div>
              <div style="width: 33vw; gap: 6px; max-height: 350px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightTop !== ''" :src="backgroundImageLayoutRightTop" style="height: 172px; cursor: pointer;" @click="goToLink('rightTop')"></v-img>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightBottom !== ''" :src="backgroundImageLayoutRightBottom" style="height: 172px; cursor: pointer;" @click="goToLink('rightBottom')"></v-img>
                </div>
              </div>
            </div> -->
            <!-- 4 Layout -->
            <!-- <div style="max-width: 1223px; width: 100%; max-height: 560px; height: 100%; display: flex; justify-content: center; gap: 5px;" v-else-if="num_layout_banner === '4'">
              <div style="width: 50%; gap: 6px; max-height: 560px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftTop !== ''" :src="backgroundImageLayoutLeftTop" style="height: 257px; cursor: pointer;" @click="goToLink('leftTop')"></v-img>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutLeftBottom !== ''" :src="backgroundImageLayoutLeftBottom" style="height: 257px; cursor: pointer;" @click="goToLink('leftBottom')"></v-img>
                </div>
              </div>
              <div style="width: 50%; gap: 6px; max-height: 560px;" class="d-flex flex-column">
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightTop !== ''" :src="backgroundImageLayoutRightTop" style="height: 257px; cursor: pointer;" @click="goToLink('rightTop')"></v-img>
                </div>
                <div style="display: flex; align-items: center; justify-content: center;">
                  <v-img v-if="backgroundImageLayoutRightBottom !== ''" :src="backgroundImageLayoutRightBottom" style="height: 257px; cursor: pointer;" @click="goToLink('rightBottom')"></v-img>
                </div>
              </div>
            </div> -->
          </div>
          <!-- size ipad -->
          <div class="mx-1" v-if="IpadSize">
            <!-- 1 Layout -->
            <v-row dense v-if="num_layout_banner === '1'">
              <v-col cols="12" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" max-height="400px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" max-height="400px" max-width="100%" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <!-- 2 Layout -->
            <v-row dense v-if="num_layout_banner === '2'">
              <v-col cols="6" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" max-height="300px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" max-height="300px" max-width="100%" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <!-- 3 Layout -->
            <v-row dense v-if="num_layout_banner === '3'">
              <div class="d-flex mx-auto" style="width: 100%; gap: 3px;">
                <div style="width: 70%;">
                  <v-card elevation="0" :outlined="false" max-height="328px" max-width="100%">
                  <v-card-text class="pa-0">
                    <v-img :src="imageGroupBanner[0].image" max-height="328px" max-width="854px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(imageGroupBanner[0].link, imageGroupBanner[0].name, imageGroupBanner[0].action_id)"></v-img>
                  </v-card-text>
                </v-card>
                </div>
                <div class="d-flex flex-column justify-between" style="gap: 4px; width: 30%;">
                  <div v-for="(item, index) in imageGroupBanner.slice(1)" :key="index">
                    <v-card :outlined="false" max-height="162px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="162px" max-width="366px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
              <!-- รายการแรก (ใหญ่) -->
              <!-- <v-col cols="12" md="8">
                <v-card :outlined="false" max-height="350px">
                  <v-card-text class="pa-0">
                    <v-img :src="imageGroupBanner[0].image" max-width="100%" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(imageGroupBanner[0].link, imageGroupBanner[0].name, imageGroupBanner[0].action_id)" min-height="327px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col> -->

              <!-- รายการที่เหลือ (เล็ก) -->
              <!-- <v-col cols="12" md="4">
                <v-row dense>
                  <v-col cols="12" v-for="(item, index) in imageGroupBanner.slice(1)" :key="index" >
                    <v-card :outlined="false" max-height="171px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="171px" max-width="435px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col> -->
            </v-row>
            <v-row dense v-if="num_layout_banner === '4'" class="px-1">
              <div class="d-flex mx-auto" style="width: 100%; gap: 3px;">
                <div class="d-flex flex-column justify-between" style="gap: 4px; width: 50%;">
                  <div class="d-flex flex-column justify-between" style="gap: 4px;">
                    <div v-for="(item, index) in [imageGroupBanner[0], imageGroupBanner[2]]" :key="index">
                      <v-card elevation="0" :outlined="false" max-height="200px" max-width="100%">
                        <v-card-text class="pa-0">
                          <v-img :src="item.image" max-height="200px" max-width="610px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                        </v-card-text>
                      </v-card>
                    </div>
                  </div>
                </div>
                <div class="d-flex flex-column justify-between" style="gap: 4px; width: 50%;">
                  <div v-for="(item, index) in [imageGroupBanner[1], imageGroupBanner[3]]" :key="index">
                    <v-card elevation="0" :outlined="false" max-height="200px" max-width="100%">
                      <v-card-text class="pa-0">
                        <v-img :src="item.image" max-height="200px" max-width="610px" style="border-radius: 4px;" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                      </v-card-text>
                    </v-card>
                  </div>
                </div>
              </div>
              <!-- รายการแรก (ใหญ่) -->
              <!-- <v-col cols="6" v-for="(item, index) in imageGroupBanner.slice(0,2)" :key="index">
                <v-card :outlined="false" max-height="277px">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" max-width="100%" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)" height="277px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col cols="6" v-for="(item, index) in imageGroupBanner.slice(2, 4).reverse()" :key="item.id || `reversed-${index}`">
                <v-card :outlined="false" max-height="277px">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" max-width="100%" :style="typeGroup === 'image' ? '' : 'cursor: pointer;'" alt="imageGroupBanner0" @click="handleClick(item.link, item.name, item.action_id)" height="277px"></v-img>
                  </v-card-text>
                </v-card>
              </v-col> -->
            </v-row>
          </div>
        </v-card>
      </v-row>
    </v-container>
    <v-container v-if="MobileSize" :class="MobileSize ? 'pa-0 mt-4' : ''">
      <v-row dense justify="center" :class="MobileSize ? 'pa-0' : 'mt-8'">
        <v-card elevation="0" class="backgroundColorPage" style="width: 100%; max-height: 550px; height: 100%;">
          <!-- size ipad && Mobile -->
          <v-card-text class="pa-0">
            <!-- 1 Layout -->
            <v-row dense v-if="num_layout_banner === '1'" class="px-1">
              <v-col cols="12" v-for="(item, index) in imageGroupBanner" :key="index">
                <v-card elevation="0" :outlined="false" width="100%" :height="IpadSize ? '350' : '114'">
                  <v-card-text class="pa-0">
                    <v-img :src="item.image" style="border-radius: 4px;" max-width="100%" :max-height="IpadSize ? '350px' : '114px'" :style="{ 'cursor': typeGroup === 'image' ? '' : 'pointer' }" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
            <v-row dense v-else class="px-1">
              <v-col cols="12">
                <v-carousel cycle :height="IpadSize ? '350px' : '150px'" show-arrows-on-hover>
                  <v-carousel-item
                    v-for="(item, index) in imageGroupBanner" :key="index"
                  >
                    <v-img :src="item.image" style="border-radius: 4px;" max-width="100%" :max-height="IpadSize ? '350px' : '150px'" :style="{ 'cursor': typeGroup === 'image' ? '' : 'pointer' }" :alt="'imageGroupBanner' + index" @click="handleClick(item.link, item.name, item.action_id)"></v-img>
                  </v-carousel-item>
                </v-carousel>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
  </div>
</template>

<script>
export default {
  data () {
    return {
      disabledGroupShop: false,
      backgroundImageLayoutLeftTop: '',
      backgroundImageLayoutLeftBottom: '',
      backgroundImageLayoutRightTop: '',
      backgroundImageLayoutRightBottom: '',
      linkLeftTop: '',
      linkLeftBottom: '',
      linkRightTop: '',
      linkRightBottom: '',
      num_layout_banner: '',
      imageGroupBanner: [],
      detailBanner: [],
      typeGroup: ''
    }
  },
  async created () {
    // this.getDetailBannerGroupShop()
    this.getDetailBannerGroup()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    async getDetailBannerGroup () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetBannerGroupShopV2')
      var res = await this.$store.state.ModuleShop.stateGetBannerGroupShopV2
      if (res.code === 200) {
        this.detailBanner = res.data
        this.imageGroupBanner = await this.getValidImageLayout(this.detailBanner.banner)
        this.typeGroup = this.detailBanner.type_layout_banner
        this.num_layout_banner = this.detailBanner.num_layout_banner
        // console.log('this.imageGroupBanner', this.imageGroupBanner)
        // if (this.num_layout_banner === null) {
        //   this.num_layout_banner = '1'
        // } else {
        //   this.num_layout_banner = this.countValidImagesType(this.detailBanner.num_layout_banner)
        // }
        if (this.imageGroupBanner.length !== 0) {
          this.disabledGroupShop = true
        } else {
          this.disabledGroupShop = false
        }
        this.$store.commit('openLoader')
      } else {
        this.disabledGroupShop = false
      }
      this.$store.commit('closeLoader')
    },
    getValidImageLayout (data) {
      var validImages = []

      data.forEach((item) => {
        if (item.image_web && item.image_web !== '' && item.image_web !== null) {
          validImages.push({
            image: item.image_web,
            image_mobile: item.image,
            link: item.link,
            layout: item.layout || '', // ถ้าไม่มี link ให้เก็บเป็น string ว่าง
            action_id: item.action_id,
            name: item.name || ''
          })
        }
      })

      return validImages
    },
    changePage (name, id) {
      const shopCleaned = name.replace(/\s/g, '-') || name
      if (this.typeGroup === 'group_shop') {
        this.$router.push({ path: `/GroupShoppage/${shopCleaned}-${id}` }).catch(() => {})
      } else if (this.typeGroup === 'shop') {
        this.$router.push({ path: `/Shoppage/${shopCleaned}-${id}` }).catch(() => {})
      }
    },
    // countValidImagesType (data) {
    //   console.log(data, 'ดูค่า data eieie')
    //   let count = 0
    //   data.forEach(key => {
    //     if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
    //       count++
    //     }
    //   })
    //   console.log(count, 'ดูค่า count')
    //   return count.toString()
    // },
    async getDetailBannerGroupShop () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetBannerGroupShop')
      var response = await this.$store.state.ModuleShop.stateGetBannerGroupShop
      // console.log('response', response)
      if (response.code === 200) {
        // // backgroung
        // this.backgroundImageLayoutLeftTop = response.data.image_layout_left_top
        // this.backgroundImageLayoutLeftBottom = response.data.image_layout_left_bottom
        // this.backgroundImageLayoutRightTop = response.data.image_layout_right_top
        // this.backgroundImageLayoutRightBottom = response.data.image_layout_right_bottom
        // // link of Background
        // this.linkLeftTop = response.data.link_layout_left_top
        // this.linkLeftBottom = response.data.link_layout_left_bottom
        // this.linkRightTop = response.data.link_layout_right_top
        // this.linkRightBottom = response.data.link_layout_right_bottom
        // this.num_layout_banner = response.data.num_layout_banner
        this.imageGroupBanner = await this.getValidImageLinks(response.data)
        if (this.num_layout_banner === null) {
          this.num_layout_banner = '1'
        } else {
          this.num_layout_banner = this.countValidImages(response.data)
        }
        // if ((this.backgroundImageLayoutLeftTop === null || this.backgroundImageLayoutLeftTop === '') &&
        // (this.backgroundImageLayoutLeftBottom === null || this.backgroundImageLayoutLeftBottom === '') &&
        // (this.backgroundImageLayoutRightTop === null || this.backgroundImageLayoutRightTop === '') &&
        // (this.backgroundImageLayoutRightBottom === null || this.backgroundImageLayoutRightBottom === '')) {
        //   this.disabledGroupShop = false
        // } else {
        //   this.disabledGroupShop = true
        // }
        if (this.imageGroupBanner.length !== 0) {
          this.disabledGroupShop = true
        } else {
          this.disabledGroupShop = false
        }
      } else {
        this.disabledGroupShop = false
      }
      this.$store.commit('closeLoader')
    },
    getValidImageLinks (data) {
      const keys = [
        { image: 'image_layout_left_top', link: 'link_layout_left_top' },
        { image: 'image_layout_right_top', link: 'link_layout_right_top' },
        { image: 'image_layout_left_bottom', link: 'link_layout_left_bottom' },
        { image: 'image_layout_right_bottom', link: 'link_layout_right_bottom' }
      ]

      var validImages = []

      keys.forEach(({ image, link }) => {
        if (data[image] && data[image] !== '' && data[image] !== null) {
          validImages.push({
            image: data[image],
            link: data[link] || '' // ถ้าไม่มี link ให้เก็บเป็น string ว่าง
          })
        }
      })

      return validImages
    },
    countValidImages (data) {
      const keys = [
        'image_layout_left_top',
        'image_layout_left_bottom',
        'image_layout_right_top',
        'image_layout_right_bottom'
      ]
      let count = 0
      keys.forEach(key => {
        if (data[key] !== null && data[key] !== undefined && data[key] !== '') {
          count++
        }
      })

      return count.toString()
    },
    goToLink (link) {
      if (link !== '') {
        // console.log('link', link)
        window.open(link, '_blank')
      }
      // if (layout === 'leftTop') {
      //   window.location.href = this.linkLeftTop
      // } else if (layout === 'leftBottom') {
      //   window.location.href = this.linkLeftBottom
      // } else if (layout === 'rightTop') {
      //   window.location.href = this.linkRightTop
      // } else if (layout === 'rightBottom') {
      //   window.location.href = this.linkRightBottom
      // }
    },
    handleClick (link, name, id) {
      // console.log('link', link)
      if (this.typeGroup === 'link') {
        this.goToLink(link)
      } else {
        this.changePage(name, id)
      }
    }
  }
}
</script>

<style scoped>
.backgroundColorPage {
  background-color: transparent;
}
</style>
