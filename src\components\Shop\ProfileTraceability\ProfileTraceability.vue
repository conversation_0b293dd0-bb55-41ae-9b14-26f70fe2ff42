<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">โปรไฟล์การสืบย้อนกลับข้อมูลสินค้า (Product Traceability Profile)</v-card-title>
      <v-card-title style="font-weight: 700;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToSellerMenu()">mdi-chevron-left</v-icon> โปรไฟล์การสืบย้อนกลับข้อมูลสินค้า<br>(Product Traceability Profile)
      </v-card-title>

      <v-col cols="12">
        <v-card
          elevation="0"
          class="pa-5 pb-0 overflow-hidden"
          color="#F4F9FB"
          style="border-radius: 10px;"
          :style="{
            backgroundImage: `url(${require('@/assets/TraceabilityProfile/bgPattern.jpg')})`,
            backgroundSize: 'cover',
            backgroundPosition: 'center'
          }"
        >
          <div
            style="
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              background-color: rgba(244, 249, 251, 0.95);
              z-index: 0;"
          ></div>

          <v-row justify="space-between" style="position: relative; z-index: 1;">
            <v-col cols="12" md="8">
              <span style="font-size: 18px; color: #27AB9C;"><b>โปรไฟล์ข้อมูลสืบย้อนกลับข้อมูลสินค้า (Product Traceability Profile)</b></span><br><br>
              <span style="font-size: 16px;">ร้านค้าสามารถเพิ่มข้อมูลสินค้า เพื่อเพิ่มคุณค่า และสร้างความมั่นใจให้ผู้ซื้อเป็นข้อมูลประกอบการตัดสินใจซื้อสินค้า</span>
            </v-col>
            <v-col cols="12" md="4" :class="MobileSize ? 'text-center' : 'text-center pb-0'">
              <v-img
                :src="require('@/assets/TraceabilityProfile/picHead.png')"
                contain
                max-width="200"
                class="mx-auto"
              />
            </v-col>
          </v-row>
        </v-card>
      </v-col>

      <v-col cols="12">
        <v-row>
          <v-col cols="12" md="6">
            <v-text-field v-model="search" placeholder="ค้นหา" outlined rounded dense hide-details style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" style="text-align: end;">
            <v-btn rounded color="#27AB9C" style="color: #FFFFFF;" @click="conditionDetail = true"><v-icon class="mr-3">mdi mdi-plus-circle</v-icon><b>เพิ่ม Traceability Profile</b></v-btn>
          </v-col>
        </v-row>
      </v-col>

      <v-col cols="12">
        <v-row v-if="detailProduct.length === 0" justify="center" align="center">
          <v-col cols="12" class="text-center">
            <v-card elevation="0" class="pa-12">
              <v-img
                :src="require('@/assets/TraceabilityProfile/noData.png')"
                contain
                max-width="300"
                class="mx-auto mb-6"
              />
              <span style="font-size: 16px; color: #989898;">ไม่มีข้อมูล Traceability Profile</span>
            </v-card>
          </v-col>
        </v-row>
        <v-row v-else>
          <v-col
            cols="12"
            sm="6"
            md="6"
            lg="6"
            v-for="product in detailProduct"
            :key="product.id"
          >
            <v-card elevation="1" height="390" style="border-radius: 10px;">
              <div class="card-actions">
                <v-btn fab small style="background-color: #edfff8;" @click="deleteProduct(product)">
                  <v-icon color="#27AB9C">mdi mdi-trash-can-outline</v-icon>
                </v-btn>
                <v-btn fab small style="background-color: #edfff8;" @click="editProduct(product)">
                  <v-icon color="#27AB9C">mdi mdi-pencil-outline</v-icon>
                </v-btn>
              </div>

              <v-img
                height="200"
                :src="product.image"
                style="border-top-left-radius: 10px; border-top-right-radius: 10px;"
              ></v-img>

              <v-card-title
                class="mb-3"
                style="font-size: 18px; font-weight: bold; color: #27AB9C;"
              >
                {{ product.title }}
              </v-card-title>
              <v-card-subtitle
                class="ellipsis-subtitle"
                style="font-size: 16px; color: #636363;"
              >
                {{ product.description }}
              </v-card-subtitle>
            </v-card>
          </v-col>
        </v-row>
      </v-col>

    </v-card>

    <v-dialog v-model="conditionDetail" max-width="650px">
      <v-card style="border-radius: 24px; position: relative;">
        <v-btn
          icon
          @click="conditionDetail = false"
          style="position: absolute; top: 10px; right: 10px; z-index: 1;"
        >
          <v-icon color="#999999">mdi-close</v-icon>
        </v-btn>

        <v-card-title
          class="justify-center"
          style="padding-top: 40px; font-size: 24px; color: #333;"
        >
          <b>ข้อกำหนดการใช้บริการ Product Traceability Profile</b>
        </v-card-title>

        <v-card-text style="font-size: 16px; color: #636363; max-height: 400px;">
          <div
            class="scrollable-content ck-content showTable"
            ref="termsContent"
            @scroll="handleScroll"
            v-html="condition"
          ></div>
        </v-card-text>

        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 150px;" @click="conditionDetail = false">ยกเลิก</v-btn>
          <v-btn rounded class="ma-2" color="#27AB9C" style="width: 150px; color: white;" :disabled="!isScrollComplete" @click="gotoCreate()">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      search: '',
      // detailProduct: []
      detailProduct: [
        {
          id: 1,
          title: 'ข้าวหอมมะลิ',
          description: 'ข้าวสวย หมายถึงข้าวที่ผ่านกระบวนการนึ่ง หรือการต้มน้ำให้เดือดอย่างใดอย่างหนึ่ง ข้าวสวยเป็นอาหารหลักในประเทศอินโดนีเซีย เกาหลีใต้ ญี่ปุ่น มาเลเซีย ปัจจุบันเทศกาล วัฒนดี ปักกิ่งสวนเวียดนาม ไทย พม่า ฟิลิปปินส์ และประเทศในทวีปอเมริกา และกว่าเอเชียตะวันออกจำนวนมากข้าวสวยยังเป็นส่วนผสมหลักในอาหารหลาย ๆ จานด้วย ข้าวสวย หมายถึงข้าวที่ผ่านกระบวนการนึ่ง หรือการต้มน้ำให้เดือดอย่างใดอย่างหนึ่ง ข้าวสวยเป็นอาหารหลักในประเทศอินโดนีเซีย เกาหลีใต้ ญี่ปุ่น มาเลเซีย ปัจจุบันเทศกาล วัฒนดี ปักกิ่งสวนเวียดนาม ไทย พม่า ฟิลิปปินส์ และประเทศในทวีปอเมริกา และกว่าเอเชียตะวันออกจำนวนมากข้าวสวยยังเป็นส่วนผสมหลักในอาหารหลาย ๆ จานด้วย',
          image: 'https://news.thaipbs.or.th/media/TSNBg3wSBdng7ijMho7k51Nzv9MyniZjx4TdAN0izb3.jpg'
        },
        {
          id: 2,
          title: 'ไม้งามจากสวน',
          description: 'ไม้จากต้นไม้หลังโรงเรียนบานเบอะตรั่งตั้งแห่งหนึ่ง มีความทนทานสูง สามารถใช้ได้นานถึง 1 ทศวรรษ และสามารถนำมาทำได้หลากหลายรูปแบบ',
          image: 'https://www.chiangraifocus.com/wp-content/uploads/2022/12/%E0%B8%A1%E0%B8%AB%E0%B8%81%E0%B8%A3%E0%B8%A3%E0%B8%A1%E0%B9%84%E0%B8%A1%E0%B9%89%E0%B8%94%E0%B8%AD%E0%B8%81%E0%B8%AD%E0%B8%B2%E0%B9%80%E0%B8%8B%E0%B8%B5%E0%B8%A2%E0%B8%99%E0%B9%80%E0%B8%8A%E0%B8%B5%E0%B8%A2%E0%B8%87%E0%B8%A3%E0%B8%B2%E0%B8%A2_2022%E0%B8%A7%E0%B8%B1%E0%B8%99%E0%B9%81%E0%B8%A3%E0%B8%81-1-1024x576.jpg'
        },
        {
          id: 3,
          title: 'ดอกไม้ออกเทคนิค',
          description: 'ดอกไม้จากสวนที่จังหวัดเชียงใหม่ ส่งกลิ่นหอม และปลูกด้วยวิธีธรรมชาติโดยไม่ใส่สารเร่งโต และเร่งสี สามารถนำไปใช้ในงานศิลปะต่างๆได้โดยไม่เป็นอันตราย และผ่านการตรวจสอบมาตรฐานจากกระทรวงเกษตรและได้รับรองด้านสุขภาพทางแมลง',
          image: 'https://s.isanook.com/wo/0/ud/44/224125/224125-20221224050832-801d966.jpg?ip/resize/w728/q80/jpg'
        },
        {
          id: 4,
          title: 'ไข่ไก่ยักษ์',
          description: 'ไข่ไก่จากฟาร์มขนาดใหญ่ทางภาคเหนือ จากไก่ที่เลี้ยงให้นอนหลับอย่างเพียงพอ และได้รับเพลงเพื่อสุขภาพจิตที่ดี ไข่ให้คุณภาพที่เน้นไปที่โปรตีนครบองค์ประกอบ และยืนยาวมากขึ้นเลือกใช้ไก่สร้างงานดีๆได้สวย เนื่องจากเปลือกไข่มีความแข็งแรง และทนทาน สามารถนำไปทำเมนูเด่นแตกได้ และได้รับการรับรองจากกระทรวงเกษตร',
          image: 'https://news.thaipbs.or.th/media/TSNBg3wSBdng7ijMho7k51Nzv9MyniZjx4TdAN0izb3.jpg'
        },
        {
          id: 5,
          title: 'นมสดจากวิกซ์กีสภายอาร์แลนด์',
          description: 'นมสดจากวัวที่มีการปลูกพืชหญ้าอย่างดีในสวิสเซอร์แลนด์ เพราะได้รับแสงในปริมาณที่เหมาะสม พักผ่อนเพียงพอ ได้อาหารดี และปราศจากพาหะ ทำให้ได้คุณภาพที่ดีมาก และโภชนาการสูง สามารถนำไปประกอบอาหาร และเครื่องดื่มได้หลายอย่าง',
          image: 'https://news.thaipbs.or.th/media/TSNBg3wSBdng7ijMho7k51Nzv9MyniZjx4TdAN0izb3.jpg'
        },
        {
          id: 6,
          title: 'ดอกไม้แห่งชีวิต',
          description: 'ปลูกบนดินในท่าน้ำของอุบลราชธานี ที่เชื่อว่าเป็นดินที่อุดมสมบูรณ์ที่สุดในประเทศไทย พร้อมกับการให้ต้นไม้ ดอกไม้เติบโตอย่างสอดรับ รังเชิง สามารถทำให้นำไปบูชาประดับใช้ตกแต่งงานพิเศษ ร่วมถึงสามารถนำประกอบการได้อีกด้วย',
          image: 'https://news.thaipbs.or.th/media/TSNBg3wSBdng7ijMho7k51Nzv9MyniZjx4TdAN0izb3.jpg'
        }
      ],
      conditionDetail: false,
      isScrollComplete: false,
      condition: `วัตถุประสงค์ของข้อกำหนดการใช้งานบัญชีระบบยืนยันตัวตนกลาง (ONE ID Account Terms of Use) (ต่อไปนี้เรียกว่า “ข้อกำหนด”) มีไว้เพื่อวางข้อกำหนดและเงื่อนไข สำหรับการใช้บริการทั้งหมดที่เกี่ยวกับบัญชีระบบยืนยันตัวตนกลาง (ONE ID Account) (ต่อไปนี้เรียกว่า “บัญชีกลาง”) ซึ่งให้บริการโดยไทย ไอเด็นติตี้ส์ (THAI IDENTITIES) และบริษัทในเครือ (ต่อไปนี้เรียกรวมกันว่า “บริษัท”)
      ลูกค้าจะต้องใช้บัญชีกลางโดยเป็นไปตามข้อกำหนดนี้ และข้อกำหนดและระเบียบการ (Terms and Conditions) นอกจากนี้ ลูกค้าจะต้องรับผิดชอบในการปฏิบัติตามกฎหมายและข้อบังคับทั้งหมดที่เกี่ยวข้องเมื่อใช้งานบัญชีกลาง
      ข้อ 1 การสมัครใช้งาน การยืนยันตัวตน การปฏิเสธการยืนยันตัวตน และการยกเลิกการยืนยันตัวตน
        1.1 ไม่ว่าลูกค้าจะเป็นบุคคลธรรมดาหรือนิติบุคคล ลูกค้าจะได้รับบัญชี (ต่อไปนี้เรียกว่า “บัญชีกลาง”) เพื่อใช้งานบัญชีกลาง โดยการสมัครขอใช้งานบัญชีกลางผ่านทางวิธีการที่บริษัทกำหนด
        1.2 เมื่อลูกค้าได้สมัครใช้งานบัญชีกลางผ่านทางวิธีการที่บริษัทกำหนดและได้รับการอนุมัติจากบริษัทแล้ว บริษัทอาจจะดำเนินการยืนยันบัญชีกลางของลูกค้าดังกล่าว (ต่อไปนี้จะเรียกบัญชีซึ่งได้รับการยืนยันจากบริษัทว่า “บัญชีกลางที่ได้รับการยืนยันแล้ว”)
        1.3 ในกรณีที่บริษัทเห็นว่ามีข้อใดข้อหนึ่งดังต่อไปนี้เกิดขึ้นกับลูกค้า บริษัทสามารถปฏิเสธคำขอของลูกค้าดังกล่าวสำหรับบัญชีกลางที่ได้รับการยืนยันแล้ว หรือยกเลิกการยืนยันตัวตนของบัญชีกลางที่ได้รับการยืนยันแล้วของลูกค้าดังกล่าวก็ได้
            (1) ในกรณีที่ลูกค้าให้ข้อมูลเท็จแก่บริษัท
            (2) ในกรณีที่ไม่เป็นไปตามมาตรฐานของการตรวจสอบ (ซึ่งบริษัทไม่มีหน้าที่ต้องเปิดเผยมาตรฐานดังกล่าว) ที่บริษัทกำหนดขึ้น
            (3) นอกจากนี้ ในกรณีที่บริษัทเห็นว่าเป็นเรื่องไม่เหมาะสมที่ลูกค้าจะใช้งานบัญชีกลาง
        1.4 บริษัทไม่มีวิธีการหรือนโยบายใดๆ ที่เป็นการขอหรือนำข้อมูลส่วนบุคคลและข้อมูลนิติบุคคล เพื่อนำมาสมัครบัญชีบุคคลธรรมดาหรือบัญชีนิติบุคคลให้กับผู้ใช้งาน ฉะนั้นผู้ใช้งานจะต้องทำการสมัครบัญชีกลางด้วยตนเอง
        1.5 การยืนยันตัวตน ลูกค้าจะต้องดำเนินการลงทะเบียนให้เสร็จสิ้นตามที่กำหนดไว้ และจะต้องปฏิบัติตามคำแนะนำที่ระบุไว้บนแพลตฟอร์มสำหรับเปิดบัญชีกลาง บริษัทและผู้ให้บริการแพลตฟอร์มมีสิทธิรวบรวมและจัดเก็บข้อมูลส่วนบุคคล (ข้อมูลการแสดงตน) และเอกสารที่เกี่ยวข้องเพื่อวัตถุประสงค์ในการทำการพิสูจน์ตัวตนลูกค้า (KYC) การตรวจสอบเพื่อทราบข้อเท็จจริงเกี่ยวกับลูกค้า (CDD) ตามกฎหมายปราบปรามการฟอกเงินและกฎหมายป้องกันและปรามปรามการสนับสนุนทางการเงินแก่การก่อการร้าย และการแพร่ขยายอาวุธที่มีอานุภาพและหากมีการร้องขอโดยบริษัทหรือผู้ให้บริการแพลตฟอร์ม ผู้ใช้งานจะต้องกรอกข้อมูลส่วนบุคคล (ข้อมูลการแสดงตน) และยื่นเอกสารที่เกี่ยวข้องเพิ่มเติมเท่าที่จำเป็นในการยืนยันตัวตนของผู้ใช้งาน โดยผู้ใช้งานจะต้องรับผิดชอบในความถูกต้องของข้อมูลที่ได้มอบไว้ให้กับบริษัทหรือผู้ให้บริการแพลตฟอร์ม
        1.6 ผู้ใช้งานตกลงและยอมรับว่าเป็นดุลยพินิจฝ่ายเดียวของบริษัทในการอนุมัติบัญชีกลาง และบริษัทอาจปฏิเสธการสร้างบัญชีกลางและการยืนยันตัวตนของผู้ใช้งาน
        1.7 ผู้ใช้งานตกลงว่าหากท่านให้ข้อมูลใด ๆ ที่ไม่เป็นความจริง ไม่ถูกต้อง ไม่เป็นปัจจุบันหรือไม่สมบูรณ์ (หรือกลายเป็นไม่จริง ไม่ถูกต้อง ไม่เป็นปัจจุบัน หรือไม่สมบูรณ์ในภายหลัง) หากบริษัทหรือผู้ให้บริการแพลตฟอร์ม มีเหตุอันควรเชื่อได้ว่าข้อมูลดังกล่าวไม่เป็นความจริง ไม่ถูกต้อง ไม่เป็นปัจจุบัน ไม่สมบูรณ์ หรือไม่สอดคล้องกับข้อตกลงนี้ บริษัทหรือผู้ให้บริการแพลตฟอร์มมีสิทธิที่จะระงับหรือยุติหรือปิดกั้นการเข้าถึงบัญชีกลางของผู้ใช้งานได้ และผู้ใช้งานจะต้องรับผิดชอบต่อความเสียหายที่เกิดจากข้อมูลใด ๆ ที่ไม่เป็นความจริง ไม่ถูกต้อง ไม่เป็นปัจจุบันหรือไม่สมบูรณ์แต่เพียงผู้เดียว
      ข้อ 2 การปรับปรุงข้อกำหนด บริษัทสามารถปรับปรุงข้อกำหนดนี้และลักษณะของการบริการ และอื่น ๆ ที่ให้บริการโดยบัญชีกลางได้โดยแจ้งให้ลูกค้าทราบถึงการปรับปรุงดังกล่าวโดยการประกาศหรือการบอกกล่าวตามที่บริษัทเห็นว่าจำเป็นโดยดุลพินิจฝ่ายเดียวของบริษัทหรือตามที่กฎหมายที่เกี่ยวข้องกำหนด ในกรณีที่ลูกค้ายังคงใช้งานบัญชีกล่งต่อไปหลังจากการปรับปรุง ให้ถือว่าลูกค้าดังกล่าวได้ให้ความยินยอมในการปรับปรุงข้อกำหนดนี้และลักษณะการบริการ และอื่น ๆ แล้ว`,
      shopID: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ProfileTraceabilityMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ProfileTraceability' }).catch(() => { })
      }
    }
  },
  async created () {
    // this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
  },
  mounted () {
    this.checkContentHeight()
  },
  methods: {
    backToSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    checkContentHeight () {
      this.$nextTick(() => {
        const content = this.$refs.termsContent
        if (content) {
          if (content.scrollHeight <= content.clientHeight) {
            this.isScrollComplete = true
          } else {
            this.isScrollComplete = false
          }
        }
      })
    },
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    },
    gotoCreate () {
      if (this.MobileSize) {
        this.$router.push({ path: '/CreateProfileTraceabilityMobile' })
      } else {
        this.$router.push({ path: '/CreateProfileTraceability' })
      }
    },
    editProduct () {
      if (this.MobileSize) {
        this.$router.push({ path: '/DetailProfileTraceabilityMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/DetailProfileTraceability' }).catch(() => { })
      }
    }
  }
}
</script>

<style scoped>
.ellipsis-title {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-subtitle {
  display: -webkit-box;
  -webkit-line-clamp: 5;           /* 👈 เปลี่ยนเป็น 5 บรรทัด */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 120px;               /* 5 บรรทัด x 24px */
  line-height: 24px;
  white-space: normal;
}

.card-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  z-index: 1;
}

.scrollable-content {
  max-height: 400px;
  overflow-y: auto;
  padding: 10px;
}
</style>
