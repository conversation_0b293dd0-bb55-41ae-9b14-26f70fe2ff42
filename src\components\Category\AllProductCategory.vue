<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }" >
        <v-breadcrumbs-item
          href=""
          :disabled="item.disabled"
          @click="gotoBannerPage(item)"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }" v-snip="2">{{ item.category_name }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container :style="MobileSize ? 'max-width: 100% !important;' : 'max-width: 1320px !important;'">
      <v-row dense align-content="center" justify="center">
        <v-col cols="12" md="12" xs="12">
          <v-row dense justify="center">
            <h2 class="pt-3 fontHeaderListProduct gradient-underline" :style="MobileSize ? 'font-size: 24px !important;' : ''"> {{ $t('AllProductCategory.Categories') }} </h2>
          </v-row>
        </v-col>
      </v-row>
      <!-- แสดง list หมวดหมู่ -->
      <v-row
       dense
       justify="center"
       class="mt-0 mb-8"
       style="background-color: transparent;"
      >
        <v-container class="pt-0" :class="IpadSize || IpadProSize || MobileSize ? 'px-0' : ''" style="background-color: transparent;" :style="IpadSize || IpadProSize || MobileSize ? 'max-width: 100% !important;' : 'max-width: 1320px !important;'">
          <v-col class="d-flex justify-center" :class="MobileSize ? 'px-0' : ''">
            <v-row dense v-if="loadingComponent">
              <v-col cols="12">
                <v-row dense>
                  <v-skeleton-loader
                    height="294"
                    width="100%"
                    type="image"
                  ></v-skeleton-loader>
                </v-row>
              </v-col>
            </v-row>
            <v-sheet
              v-else
              class="col-12"
              :class="IpadSize || IpadProSize || MobileSize ? 'px-0' : ''"
              style="border-radius: 8px; min-height: 240px; background-color: transparent;"
              :style="IpadSize || IpadProSize || MobileSize ? 'max-width: 100% !important;' : 'max-width: 1320px !important;'"
            >
              <div class="d-flex justify-center">
                <v-btn
                 class="d-flex align-self-center"
                 color="#F4F4F4"
                 fab
                 style="height: 40px; width: 40px;"
                 @click="goToPreviousPage"
                 :disabled="!hasPreviousPage"
                 elevation="0"
                >
                  <v-icon color="#269AFD">mdi-chevron-left</v-icon>
                </v-btn
              >
                <v-row
                  no-gutters
                  justify="center"
                  :class="IpadSize || IpadProSize || MobileSize ? 'px-0' : ''"
                  :style="IpadSize || IpadProSize || MobileSize ? 'max-width: 100% !important;' : 'max-width: 1320px !important;'"
                  style="min-height: 240px; background-color: transparent;"
                  v-if="currentPageItems % 8 !== 1"
                >
                  <div v-for="(item, index) in currentPageItems" :key="index">
                    <v-card
                     elevation="0"
                     class="rounded-0"
                     height="135px"
                     width="142px"
                     max-width="142px"
                     max-height="100%"
                     :href="item.pathLink"
                     style="background-color: transparent;"
                     @click.prevent="gotoListProduct(item.category_name, item.hierachy)"
                    >
                      <v-card-text class="pa-0">
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <div style="background-color: #F7F7F7; width: 79px; height: 80px; border-radius: 32px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 8px;">
                              <v-img
                                v-if="item.category_logo_path !== ''"
                                :src="item.category_logo_path"
                                loading="lazy"
                                width="54"
                                height="54"
                                max-height="54"
                                max-width="54"
                                contain
                              ></v-img>
                              <v-img
                                v-if="item.category_logo_path === ''"
                                src="@/assets/StoreNew.png"
                                loading="lazy"
                                width="54"
                                height="54"
                                max-height="54"
                                max-width="54"
                                contain
                              ></v-img>
                            </div>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                                cols="12"
                                align="center"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span
                                 class="text-truncate d-inline-block"
                                 style="max-width: 140px; font-size: 16px; font-weight: 400;"
                                >
                                  {{ item.category_name }}
                                </span>
                              </v-col>
                            </template>
                            <span>{{ item.category_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </div>
                </v-row>
                <v-row
                 no-gutters
                 justify="center"
                 :class="IpadSize || IpadProSize || MobileSize ? 'px-0' : ''"
                 :style="IpadSize || IpadProSize || MobileSize ? 'max-width: 100% !important;' : 'max-width: 1320px !important;'"
                 style="min-height: 240px; background-color: transparent;"
                 v-else
                >
                  <div v-for="(item, index) in currentPageItems" :key="index">
                    <v-card
                     elevation="0"
                     class="rounded-0"
                     height="135px"
                     width="142px"
                     max-width="142px"
                     max-height="100%"
                     :href="item.pathLink"
                     style="background-color: transparent;"
                     @click.prevent="gotoListProduct(item.category_name, item.hierachy)"
                    >
                      <v-card-text class="pa-0">
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <div style="background-color: #F7F7F7; width: 79px; height: 80px; border-radius: 32px; display: flex; justify-content: center; align-items: center; margin: auto; margin-bottom: 8px;">
                              <v-img
                               v-if="item.category_logo_path !== ''"
                               :src="item.category_logo_path"
                               loading="lazy"
                               width="54"
                               height="54"
                               max-height="54"
                               max-width="54"
                               contain
                              ></v-img>
                              <v-img
                               v-if="item.category_logo_path === ''"
                               src="@/assets/StoreNew.png"
                               loading="lazy"
                               width="54"
                               height="54"
                               max-height="54"
                               max-width="54"
                               contain
                              ></v-img>
                            </div>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                               cols="12"
                               align="center"
                               v-bind="attrs"
                               v-on="on"
                              >
                                <span
                                 class="text-truncate d-inline-block"
                                 style="max-width: 140px; font-size: 16px; font-weight: 400;"
                                >{{ item.category_name }}</span
                              >
                              </v-col>
                            </template>
                            <span>{{ item.category_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </div>
                </v-row>
                <v-btn
                 class="d-flex align-self-center"
                 color="#F4F4F4"
                 fab
                 style="height: 40px; width: 40px;"
                 @click="goToNextPage"
                 :disabled="!hasNextPage"
                 elevation="0"
                >
                  <v-icon color="#269AFD">mdi-chevron-right</v-icon>
                </v-btn>
              </div>
            </v-sheet>
          </v-col>
        </v-container>
      </v-row>
      <!-- ส่วนแสดงสินค้าทั้งหมด -->
      <div v-if="showSkeletonLoader === true">
        <v-row dense class="pb-4">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="item in 6" :key="item">
            <v-skeleton-loader
              type="image, list-item-two-line"
            ></v-skeleton-loader>
          </v-col>
        </v-row>
      </div>
      <div v-else>
        <v-row  no-gutters justify="center" v-if="AllProduct.length === 0">{{ $t('AllProductCategory.NoProduct') }}</v-row>
        <v-row justify="start" v-if="AllProduct.length !== 0 && !MobileSize && !IpadSize">
          <v-col cols="12" :md="IpadProSize ? '3' : '2'" sm="3" xs="4" v-for="(item, index) in paginated" :key="index" style="display: flex; justify-content: center;">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" v-if="AllProduct.length !== 0 && !MobileSize && IpadSize">
          <v-col cols="12" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index" style="display: flex; justify-content: center;">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" v-if="AllProduct.length !== 0 && MobileSize && !IpadSize" :class="MobileSize ? 'px-1' : ''">
          <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index" :class="MobileSize ? 'px-0' : ''" style="display: flex; justify-content: center;">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="my-6">
          <v-pagination
          color="#3EC6B6"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="MobileSize ? 5 : 7"
          class="paginationStyle"
          @input="pageChange($event)"
          ></v-pagination>
        </v-row>
      </div>
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      items: [
        {
          category_name: this.$t('AllProductCategory.HomePage'),
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          category_name: this.$t('AllProductCategory.Categories'),
          disabled: true,
          color: '#3EC6B6',
          href: '/'
        }
      ],
      showSkeletonLoader: true,
      loadingComponent: true,
      dataTypeCat: 'ทั้งหมด',
      typeCat: 'all',
      path: process.env.VUE_APP_DOMAIN,
      model: null,
      currentPage: 1,
      current: 1,
      itemCat: [],
      AllProduct: [],
      overlay2: false,
      pageMax: null,
      typeProduct: 'all_product_cat'
    }
  },
  created () {
    this.loadingComponent = true
    this.showSkeletonLoader = true
    if (this.$route.query.page !== undefined) {
      this.pageNumber = parseInt(this.$route.query.page)
    }
    localStorage.removeItem('PageTab')
    this.GetAllCategory()
  },
  watch: {
    async $route (to, from) {
      var getIDToParams = to.query.page
      var getIDFromParams = from.query.page

      if (getIDFromParams !== undefined && getIDToParams !== undefined) {
        if (parseInt(getIDToParams) !== parseInt(getIDFromParams)) {
          const newPage = parseInt(getIDToParams)

          // ตรวจสอบว่า pageNumber ถูกต้องก่อนเรียก pageChange()
          if (newPage !== this.pageNumber) {
            this.pageNumber = newPage
            this.pageChange(newPage)
          }
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    hasPreviousPage () {
      return this.currentPage > 1
    },
    hasNextPage () {
      return this.currentPage < this.totalPages
    },
    currentPageItems () {
      if (this.MobileSize) {
        var a = 4
        var start = (this.currentPage - 1) * a
        var end = start + a
        return this.itemCat.slice(start, end)
      } else {
        const start = (this.currentPage - 1) * this.itemsPerPage
        const end = start + this.itemsPerPage
        return this.itemCat.slice(start, end)
      }
    },
    itemsPerPage () {
      if (this.MobileSize) {
        return 4
      } else if (this.IpadProSize && this.IpadSize) {
        return 10
      } else if (this.IpadSize) {
        return 8
      } else {
        return 16
      }
    },
    totalPages () {
      if (this.MobileSize) {
        return Math.ceil(this.totalItems / 4)
      }
      return Math.ceil(this.totalItems / this.itemsPerPage)
    },
    totalItems () {
      return this.itemCat.length
    },
    paginated () {
      return this.AllProduct
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    async GetAllCategory () {
      var data = this.typeCat
      // console.log('GetAllCategory')
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.result === 'SUCCESS') {
        for (var i = 0; i < response.data.length; i++) {
          response.data[i].pathLink = this.path + 'ListProduct/' + encodeURIComponent(`${response.data[i].category_name}`) + '?id=' + `${response.data[i].hierachy}` + '&page=1'
        }
        var resData = response.data
        this.itemCat = resData.filter(e => e.hierachy !== '1_359')
        this.loadingComponent = false
        await this.getProductSameShop()
      } else {
        this.itemCat = []
        this.loadingComponent = false
        await this.getProductSameShop()
      }
    },
    goToPreviousPage () {
      if (this.hasPreviousPage) {
        this.currentPage--
      }
    },
    goToNextPage () {
      if (this.hasNextPage) {
        this.currentPage++
      }
    },
    async getProductSameShop () {
      this.showSkeletonLoader = true
      var data
      var roleUser = ''
      if (localStorage.getItem('roleUser') !== null) {
        roleUser = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        roleUser = 'ext_buyer'
      }
      var ShopID = JSON.parse(localStorage.getItem('shopID'))
      var companyID = ''
      if (localStorage.getItem('SetRowCompany') !== null && roleUser === 'purchaser') {
        var companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyId.company.company_id
      }
      if (roleUser === 'sale_order' || roleUser === 'sale_order_no_JV') {
        companyID = JSON.parse(localStorage.getItem('PartnerID'))
        ShopID = JSON.parse(localStorage.getItem('ShopID'))
      } else {
        ShopID = JSON.parse(localStorage.getItem('shopID'))
      }
      data = {
        company_id: roleUser !== 'ext_buyer' ? companyID : '-1',
        role_user: roleUser,
        category: '',
        seller_shop_id: this.typeProduct === 'all_product_cat' ? -1 : ShopID,
        orderBy: '',
        page: this.pageNumber,
        status_product: '',
        limit: this.typeProduct === 'all_product_cat' ? 48 : this.limit
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', data)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.overlay2 = false
          this.AllProduct = []
          this.AllProduct = await [...response.query_result]
          this.pageMax = response.pagination.max_page
          this.pageNumber = parseInt(this.$route.query.page)
          this.productCount = response.query_result.length
          this.showSkeletonLoader = false
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
          this.showSkeletonLoader = false
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
        this.showSkeletonLoader = false
      }
    },
    async pageChange (val) {
      val = this.pageNumber
      this.$router.push(`/AllProductCategory?page=${val}`).catch(() => {})
      await this.getProductSameShop()
      this.pageNumber = val
    },
    gotoListProduct (event, val) {
      const encodedEvent = encodeURIComponent(event)
      var fullPath
      if (event === 'เวาเชอร์') {
        fullPath = '/ListVoucher/go?&province=All&page=1'
      } else {
        fullPath = `/ListProduct/${encodedEvent}?id=${val}&page=1`
      }
      this.$router.push({ path: `${fullPath}` }).catch(() => {})
    },
    gotoBannerPage (val) {
      var dataR = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataR = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataR = 'ext_buyer'
      }
      if (dataR === 'sale_order' || dataR === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathShopSale }).catch(() => {})
      } else {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.paginationStyle /deep/ .v-pagination__item {
  background: transparent;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  font-size: 1rem;
  height: 40px;
  margin: 0.3rem;
  min-width: 40px;
  padding: 0 5px;
  text-decoration: none;
  transition: 0.3s cubic-bezier(0, 0, 0.2, 1);
  width: auto;
  box-shadow: none !important;
}
.paginationStyle /deep/ .v-pagination__navigation {
  box-shadow: none !important;
  border-radius: 6px !important;
  border: 1px solid #E6E6E6 !important;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  text-decoration: none;
  height: 40px;
  width: 40px;
  margin: 0.3rem 10px;
}
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #3EC6B6 !important;
}
.v-breadcrumbs li .v-icon {
  color: #3EC6B6 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
</style>
