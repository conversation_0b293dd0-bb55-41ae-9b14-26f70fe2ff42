<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" style="background: #FFFFFF;">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333; display: flex; gap: .5vw;" v-if="!MobileSize">
        <span>สินค้าบริการของฉัน</span>
        <v-chip color="#fff9e4" v-if="statusApprove === 'pending'"><span style="color: #faad14; font-size: 16px;">รออนุมัติ</span></v-chip>
        <v-chip color="#edf2f8" v-else-if="statusApprove === 'reject'"><span style="color: #1b5dd6; font-size: 16px;">รอแก้ไขข้อมูล</span></v-chip>
        <v-chip color="#f0f9ee" v-if="statusApprove === 'approve'"><span style="color: #52c41a; font-size: 16px;">อนุมัติแล้ว</span></v-chip>
        <v-spacer></v-spacer>
        <v-btn v-if="listService.length !== 0 && statusApprove !== 'pending'" color="#27ab9c" style="border-radius: 3vw;" @click="updateService">
          <v-icon size="large" color="#fff">mdi-lead-pencil</v-icon>
          <span style="color: #FFFFFF; font-size: medium">แก้ไข</span>
        </v-btn>
      </v-card-title>
      <v-card-title style="font-weight: 700; display: flex; gap: 1vw;" v-else>
        <v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon>
        <span>สินค้าบริการของฉัน</span>
        <v-chip color="#fff9e4" v-if="statusApprove === 'pending'"><span style="color: #faad14; font-size: 16px;">รออนุมัติ</span></v-chip>
        <v-chip color="#edf2f8" v-else-if="statusApprove === 'reject'"><span style="color: #1b5dd6; font-size: 16px;">รอแก้ไขข้อมูล</span></v-chip>
        <v-chip color="#f0f9ee" v-if="statusApprove === 'approve'"><span style="color: #52c41a; font-size: 16px;">อนุมัติแล้ว</span></v-chip>
      <v-col cols="12">
          <v-btn v-if="listService.length !== 0 && statusApprove !== 'pending'" color="#27ab9c" style="border-radius: 3vw; width: inherit;" @click="updateService">
            <v-icon size="large" color="#fff">mdi-lead-pencil</v-icon>
            <span style="color: #FFFFFF;">แก้ไข</span>
          </v-btn>
        </v-col>
      </v-card-title>
    </v-card>
    <v-card v-if="listService.length !== 0" class="ma-2">
      <div style="background-color: #f3f5f7; padding: 1vw;">
        <!-- <img class="mr-1" src="@/assets/Coorperation/iconInfoShop.png" width="20" height="20"> -->
        <span style="font-size: 16px; font-weight: bold;">ข้อมูลสินค้าบริการ</span>
      </div>
      <v-card-text class="mt-2">
        <v-row style="font-size: 16px;">
          <v-col cols="12">
            <img class="mr-2" src="@/assets/Coorperation/infoProduct.png" width="20" height="20">
            <span style="font-weight: bold;">ข้อมูลสินค้า</span>
          </v-col>
          <v-col cols="12">
            <span style="font-weight: bold;">ชื่อสินค้า : </span>
            <span>{{ partnerName }}</span>
          </v-col>
          <v-col style="margin-top: -1vw;">
            <span style="font-weight: bold;'">รายละเอียดสินค้า/คำอธิบายเพิ่มเติม : </span>
            <!-- <span v-if="detailPartner !== null">{{ detailPartner }}</span> -->
            <div v-if="detailPartner !== null" class="showTable ck-content mt-2" v-html="detailPartner"></div>
            <span v-else>-</span>
          </v-col>
        </v-row>
        <div class="pt-5 pb-5">
          <img class="mr-1" src="@/assets/Coorperation/iconProductList.png" width="24" height="24">
          <span style="font-size: 16px; font-weight: bold;">รายการสินค้า</span>
        </div>
        <div v-if="MobileSize || IpadSize || IpadProSize">
          <v-row style="font-size: 16px;" v-for="(items, index) in listPackage" :key="index">
            <v-row class="pa-3" style="background-color: #f3f5f7; margin: .5vw; border-radius: .5vw;" v-if="statusApprove !== 'cancel'">
              <v-col cols="12" class="d-flex align-center" style="gap: 1vw;">
                <span style="font-size: 16px; font-weight: bold; color: #27AB9C">รายการที่ {{ index + 1 }}</span>
                <v-chip color="#fff9e4" v-if="items.status === 'pending'"><span style="color: #faad14; font-size: 16px;">รออนุมัติ</span></v-chip>
                <v-chip color="#edf2f8" v-else-if="items.status === 'reject'"><span style="color: #1b5dd6; font-size: 16px;">รอแก้ไขข้อมูล</span></v-chip>
                <v-chip color="#f0f9ee" v-if="items.status === 'approve'"><span style="color: #52c41a; font-size: 16px;">อนุมัติแล้ว</span></v-chip>
              </v-col>
              <v-col cols="12" v-if="items.remark.update_at !== null" :style="IpadProSize ? '' : 'margin-top: -3vw;'">
                <v-card style="background-color: #fafafa; padding: 2.5vw;">
                  <v-row>
                    <v-col cols="12">
                      <span class="mr-2"><v-icon>mdi-calendar-month</v-icon> วันที่ส่งแก้ไขข้อมูล :</span>
                      <span style="font-weight: bold;">{{ items.remark.update_at }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span class="mr-2"><v-icon>mdi-file-document-edit-outline</v-icon> หมายเหตุส่งแก้ไขข้อมูล :</span>
                      <span style="font-weight: bold;">
                        {{ substringMobile(items.remark.remark) }}
                        <v-btn v-if="items.remark.remark.length > 50" text color="#27AB9C" @click="openDialog(items.remark.remark)">ดูเพิ่มเติม</v-btn>
                      </span>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
              <v-col cols="12" :class="MobileSize || IpadSize ? 'd-flex flex-column' : 'd-flex'" style="gap: 2vw;">
                <div class="d-flex" style="gap: 1vw;">
                  <span style="font-weight: bold;">ชื่อ Package : </span>
                  <span>{{ items.package_name }}</span>
                </div>
                <div>
                  <span style="font-weight: bold;">ประเภทการชำระเงิน : </span>
                  <span v-if="items.payment_type === 'Monthly'">รายเดือน</span>
                  <span v-else-if="items.payment_type === 'Yearly'">รายปี</span>
                  <span v-else>เปอร์เซ็นต์ (%)</span>
                </div>
                <div>
                  <span style="font-weight: bold;">ราคาสินค้า : </span>
                  <span v-if="items.payment_type === 'Percent'">{{ items.price }}%</span>
                  <span v-else-if="items.package_name === 'Custom'">>{{ items.price }}</span>
                  <span v-else>{{ items.price }}</span>
                </div>
              </v-col>
              <v-col
                v-if="items.package_name !== 'Custom'"
                cols="12"
                :class="MobileSize ? 'd-flex flex-column' : 'd-flex'"
                :style="MobileSize ? 'margin-top: -.5vw;' : 'margin-top: -.5vw; gap: 1vw;'"
              >
                <span class="text-no-wrap" :style="MobileSize ? 'font-weight: bold; margin-bottom: 1vw;' : 'font-weight: bold;'">รายละเอียด package : </span>
                <div class="d-flex flex-column" style="gap: 2vw;" v-if="items.list_function.length !== 0">
                  <div v-for="(itemFunc, indexFunc) in items.list_function" :key="indexFunc" class="d-flex flex-column">
                    <span>{{indexFunc + 1}}. รายการฟังก์ชั่นของ Package : <br v-if="MobileSize"><span style="color: #27ab9c;">{{itemFunc.name}}</span></span>
                    <span>จำนวน Transaction ของ Package  : <br v-if="MobileSize"><span v-if="itemFunc.limit === -1" style="color: #27ab9c;">ไม่จำกัด</span><span v-else style="color: #27ab9c;">{{itemFunc.limit}} {{itemFunc.unit_function}}</span></span>
                    <span>ราคา Transaction ส่วนเกิน : <br v-if="MobileSize">
                      <span v-if="itemFunc.excess_transaction_price !== 0 && itemFunc.excess_transaction_unit !== 'percent' && itemFunc.excess_transaction_unit !== 'unlimit'" style="color: #27ab9c;">{{itemFunc.excess_transaction_price}} บาท</span>
                      <span v-else-if="itemFunc.excess_transaction_price !== 0 && itemFunc.excess_transaction_unit === 'percent' && itemFunc.excess_transaction_unit !== 'unlimit'" style="color: #27ab9c;">{{itemFunc.excess_transaction_price}}%</span>
                      <span v-else style="color: #27ab9c;">-</span>
                    </span>
                  </div>
                </div>
                <span v-else>-</span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? 'd-flex flex-column' : 'd-flex' " style="margin-top: -.5vw; gap: 1.5vw;">
                <span v-if="MobileSize" style="white-space: nowrap; font-weight: bold; margin-bottom: 2vw;">รายละเอียด Package/คำอธิบายเพิ่มเติม : </span>
                <span v-else style="white-space: nowrap; font-weight: bold; font-weight: bold;">รายละเอียด Package <br>/คำอธิบายเพิ่มเติม : </span>
                <div class="showTable ck-content" v-if="items.package_detail !== null" v-html="items.package_detail" :style="items.package_name === 'Custom' ? 'margin-bottom: -3vw;' : ''"></div>
                <span v-else-if="items.package_detail === null && items.package_name === 'Custom'">กำหนดรายละเอียดเอง</span>
                <span v-else>-</span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? 'd-flex flex-column' : 'd-flex'" :style="MobileSize ? 'margin-top: -.5vw;' : 'margin-top: -.5vw; gap: 1.3vw;'">
                <span class="text-no-wrap font-weight-bold" :style="MobileSize ? 'margin-bottom: 2vw;' : ''">เงื่อนไขการให้บริการ : </span>
                <div class="showTable ck-content" v-html="items.service_detail" :style="items.package_name === 'Custom' ? 'margin-bottom: -2vw;' : ''"></div>
              </v-col>
              <v-col cols="12" v-if="items.package_name === 'Custom'" :style="MobileSize ? 'margin-top: -.5vw; display: flex;' : 'margin-top: 1.5vw; display: flex; gap: 5vw;'">
                <span class="text-no-wrap font-weight-bold" :style="MobileSize ? 'margin-right: 2vw;' : ''">เบอร์โทรศัพท์ : </span>
                <span>{{items.phone}}</span>
              </v-col>
              <v-col cols="12" v-if="items.package_name === 'Custom'" :style="MobileSize ? 'margin-top: -.5vw; display: flex;' : 'margin-top: -.5vw; display: flex; gap: 9.6vw;'">
                <span class="text-no-wrap font-weight-bold" :style="MobileSize ? 'margin-right: 2vw;' : ''">อีเมล : </span>
                <span>{{items.email}}</span>
              </v-col>
            </v-row>
          </v-row>
        </div>
        <div v-else>
          <v-row style="font-size: 16px;" v-for="(items, index) in listPackage" :key="index">
            <v-row class="pa-3" style="background-color: #f3f5f7; margin: .5vw; border-radius: .5vw;" v-if="statusApprove !== 'cancel'">
              <v-col cols="12" class="d-flex align-center" style="gap: 1vw;">
                <span style="font-size: 16px; font-weight: bold; color: #27AB9C">รายการที่ {{ index + 1 }}</span>
                <v-chip color="#fff9e4" v-if="items.status === 'pending'"><span style="color: #faad14; font-size: 16px;">รออนุมัติ</span></v-chip>
                <v-chip color="#edf2f8" v-else-if="items.status === 'reject'"><span style="color: #1b5dd6; font-size: 16px;">รอแก้ไขข้อมูล</span></v-chip>
                <v-chip color="#f0f9ee" v-if="items.status === 'approve'"><span style="color: #52c41a; font-size: 16px;">อนุมัติแล้ว</span></v-chip>
              </v-col>
              <v-col cols="5" class="d-flex flex-column" style="gap: 2vw;" v-if="items.status === 'reject'">
                <div>
                  <span style="font-weight: bold;">ชื่อ Package : </span>
                  <span>{{ items.package_name }}</span>
                </div>
                <div>
                  <span style="font-weight: bold;">ประเภทการชำระเงิน : </span>
                  <span v-if="items.payment_type === 'Monthly'">รายเดือน</span>
                  <span v-else-if="items.payment_type === 'Yearly'">รายปี</span>
                  <span v-else>เปอร์เซ็นต์ (%)</span>
                </div>
                <div>
                  <span style="font-weight: bold;">ราคาสินค้า : </span>
                  <span v-if="items.payment_type === 'Percent'">{{ items.price }}%</span>
                  <span v-else-if="items.package_name === 'Custom'">>{{ items.price }}</span>
                  <span v-else>{{ items.price }}</span>
                </div>
              </v-col>
              <v-col cols="12" class="d-flex" style="gap: 2vw;" v-else>
                <div>
                  <span style="font-weight: bold;">ชื่อ Package : </span>
                  <span>{{ items.package_name }}</span>
                </div>
                <div>
                  <span style="font-weight: bold;">ประเภทการชำระเงิน : </span>
                  <span v-if="items.payment_type === 'Monthly'">รายเดือน</span>
                  <span v-else-if="items.payment_type === 'Yearly'">รายปี</span>
                  <span v-else>เปอร์เซ็นต์ (%)</span>
                </div>
                <div>
                  <span style="font-weight: bold;">ราคาสินค้า : </span>
                  <span v-if="items.payment_type === 'Percent'">{{ items.price }}%</span>
                  <span v-else-if="items.package_name === 'Custom'">>{{ items.price }}</span>
                  <span v-else>{{ items.price }}</span>
                </div>
              </v-col>
              <v-col cols="7" v-if="items.remark.update_at !== null" :style="IpadProSize ? '' : 'margin-top: -3vw;'">
                <v-card class="ml-2 mr-2 pa-3" style="background-color: #fafafa;">
                  <v-row>
                    <v-col cols="12">
                      <span class="mr-2"><v-icon>mdi-calendar-month</v-icon> วันที่ส่งแก้ไขข้อมูล :</span>
                      <span style="font-weight: bold;">{{ items.remark.update_at }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span class="mr-2"><v-icon>mdi-file-document-edit-outline</v-icon> หมายเหตุส่งแก้ไขข้อมูล :</span>
                      <span style="font-weight: bold;">{{ substringMobile(items.remark.remark) }}<v-btn v-if="items.remark.remark.length > 50" text color="#27AB9C" @click="openDialog(items.remark.remark)">ดูเพิ่มเติม</v-btn></span>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
              <v-col v-if="items.package_name !== 'Custom'" cols="12" class="mt-1" style="margin-top: -.5vw; display: flex; gap: 1vw;">
                <span style="white-space: nowrap; font-weight: bold;">รายละเอียด package : </span>
                <div class="d-flex flex-column" style="gap: 2vw;" v-if="items.list_function.length !== 0">
                  <div v-for="(itemFunc, indexFunc) in items.list_function" :key="indexFunc" class="d-flex flex-column">
                    <span>{{indexFunc + 1}}. รายการฟังก์ชั่นของ Package : <span style="color: #27ab9c;">{{itemFunc.name}}</span></span>
                    <span>จำนวน Transaction ของ Package  : <span v-if="itemFunc.limit === -1" style="color: #27ab9c;">ไม่จำกัด</span><span v-else style="color: #27ab9c;">{{itemFunc.limit}} {{itemFunc.unit_function}}</span></span>
                    <span>ราคา Transaction ส่วนเกิน :
                      <span v-if="itemFunc.excess_transaction_price !== 0 && itemFunc.excess_transaction_unit !== 'percent' && itemFunc.excess_transaction_unit !== 'unlimit'" style="color: #27ab9c;">{{itemFunc.excess_transaction_price}} บาท</span>
                      <span v-else-if="itemFunc.excess_transaction_price !== 0 && itemFunc.excess_transaction_unit === 'percent' && itemFunc.excess_transaction_unit !== 'unlimit'" style="color: #27ab9c;">{{itemFunc.excess_transaction_price}}%</span>
                      <span v-else style="color: #27ab9c;">-</span>
                    </span>
                  </div>
                </div>
                <span v-else>-</span>
              </v-col>
              <v-col cols="12" class="mt-1" style="margin-top: -.5vw; display: flex; gap: 1.5vw;">
                <span style="white-space: nowrap; font-weight: bold;">รายละเอียด Package <br>/คำอธิบายเพิ่มเติม : </span>
                <div class="showTable ck-content" v-if="items.package_detail !== null" v-html="items.package_detail" :style="items.package_name === 'Custom' ? 'margin-bottom: -3vw;' : ''"></div>
                <span v-else-if="items.package_detail === null && items.package_name === 'Custom'">กำหนดรายละเอียดเอง</span>
                <span v-else>-</span>
              </v-col>
              <v-col cols="12" style="margin-top: 1vw; display: flex; gap: 1.3vw;">
                <span class="text-no-wrap font-weight-bold">เงื่อนไขการให้บริการ : </span>
                <div class="showTable ck-content" v-html="items.service_detail" :style="items.package_name === 'Custom' ? 'margin-bottom: -2vw;' : ''"></div>
              </v-col>
              <v-col cols="12" v-if="items.package_name === 'Custom'" style="margin-top: 1vw; display: flex; gap: 3.8vw;">
                <span style="white-space: nowrap; font-weight: bold;'">เบอร์โทรศัพท์ : </span>
                <span>{{items.phone}}</span>
              </v-col>
              <v-col cols="12" v-if="items.package_name === 'Custom'" style="margin-top: -.5vw; display: flex; gap: 7.3vw;">
                <span style="white-space: nowrap; font-weight: bold;">อีเมล : </span>
                <span>{{items.email}}</span>
              </v-col>
            </v-row>
          </v-row>
        </div>
      </v-card-text>
    </v-card>
    <div v-else class="ma-3">
      <v-card :style="MobileSize ? 'margin-top: -8vw' : ''">
        <v-card-text class="d-flex align-center flex-column" style="padding: 7vw;">
          <v-img
            src="@/assets/Coorperation/serviceProduct.jpg"
            width="173"
            height="204"
            contain
          ></v-img>
          <span class="mt-2" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">คุณยังไม่มีรายการสินค้าบริการภายในร้าน</span>
          <div class="mt-2 d-flex align-center" :style="MobileSize ? 'font-size: medium; gap: 2vw;' : 'font-size: large; gap: 1vw;'">
            <span>กดปุ่ม</span>
            <v-btn @click="createService()" style="color: #fff; border-radius: 10vw;" color="#27AB9C">เพิ่มสินค้า</v-btn>
            <span>ที่นี่</span>
          </div>
        </v-card-text>
      </v-card>
    </div>
    <v-dialog content-class="elevation-0" v-model="dialogShowMore" :width="MobileSize ? '100%' : IpadSize || IpadProSize ? '50%' : '40%'">
      <v-card style="border-radius: 1.5vw;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>หมายเหตุส่งแก้ไขข้อมูล</b></span>
            </v-col>
            <v-btn fab small @click="dialogShowMore = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>

        <v-card-text class="pt-4">
          <pre style="white-space: wrap" v-html="dataMore"></pre>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      product_name: '',
      description: '',
      package_name: '',
      payment_type: '',
      price: '',
      package_detail: '',
      service_detail: '',
      listService: [],
      listPackage: [],
      statusApprove: '',
      taxId: '',
      partnerCode: '',
      dialogShowMore: false,
      dataMore: '',
      partnerName: '',
      detailPartner: null,
      excess_transaction_price: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    this.$store.commit('openLoader')
    await this.getTaxId()
    await this.getPartnerCode()
    await this.getDetailPackage()
    this.$store.commit('closeLoader')
    // this.getPartnerCode()
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/serviceProductPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/serviceProductPartner' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    openDialog (item) {
      this.dialogShowMore = true
      this.dataMore = item
    },
    createService () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/createServicePartner' }).catch(() => { })
      } else {
        this.$router.push({ path: '/createServicePartnerMobile' }).catch(() => { })
      }
    },
    updateService () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/updateServicePartner' }).catch(() => { })
      } else {
        this.$router.push({ path: '/updateServicePartnerMobile' }).catch(() => { })
      }
    },
    // backtoPage () {
    //   if (!this.MobileSize) {
    //     this.$router.push({ path: '/serviceProductPartner' }).catch(() => { })
    //   } else {
    //     this.$router.push({ path: '/serviceProductPartnerMobile' }).catch(() => { })
    //   }
    // },
    backToMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    },
    async getTaxId () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          // console.log('see tax id', this.taxId)
          // this.taxId = response.data.array_business[0].owner_tax_id
        }
      }
    },
    async getPartnerCode () {
      var data = {
        id_card_num: this.taxId
      }
      // this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        if (response.data.partner_code !== null) {
          this.havePartner = false
          this.partnerCode = response.data.partner_code
        } else {
          this.havePartner = true
        }
      }
      // this.$store.commit('closeLoader')
    },
    async getDetailPackage () {
      // this.$store.commit('openLoader')
      var data = { partner_code: this.partnerCode }
      // console.log('see data', data)
      await this.$store.dispatch('actionsGetDetailPackage', data)
      var response = await this.$store.state.ModuleBusiness.stateGetDetailPackage
      if (response.code === 200) {
        // this.$store.commit('closeLoader')
        if (response.data.length !== 0) {
          this.listService = response.data[0]
          this.product_name = this.listService.partner_name
          this.description = this.listService.detail
          this.listPackage = this.listService.list_package
          this.statusApprove = this.listService.status
          this.partnerName = this.product_name
          this.detailPartner = this.description
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    substring (data) {
      return data.length > 70 ? data.substring(0, 70) + '...' : data
    },
    substringMobile (data) {
      return data.length > 50 ? data.substring(0, 50) + '...' : data
    }
  }
}
</script>

<style>

</style>
<style scoped>
::v-deep .v-text-field__details {
  display: none !important;
}
::v-deep .ck-content .image {
  margin: 0 !important;
}
::v-deep .ck-content .image img {
  margin-bottom: .5vw !important;
  width: 40vw !important;
}
@media only screen and (max-width: 768px) {
  ::v-deep .ck-content .image img
  {
    margin-bottom: .5vw !important;
    width: 73vw !important;
  }
}
</style>
