<template>
  <v-container :class="MobileSize ? 'py-0' : ''">
    <v-card-title class="mb-10 px-0 pt-0" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รายได้ของฉัน</v-card-title>
    <v-card-title class="pt-0" style="font-weight: bold; font-size: 18px; line-height: 24px; color: #333333; margin-left: -35px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> รายได้ของฉัน</v-card-title>
   <!--  <v-row>
      <v-col cols="8">
         <div class="top">รายได้ของร้านค้า</div>
      </v-col>
       <v-col cols="12" md="2">
         <v-menu
          ref="menu1"
          v-model="menu1"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          max-width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormatted"
              label=""
              hint=""
              persistent-hint
              append-icon="mdi-calendar"
              v-bind="attrs"
              v-on="on"
            ></v-text-field>
          </template>
          <v-date-picker
            v-model="dateStart"
            @
            no-title
            :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            @input="menu1 = false"
            @change="selectDate"
          ></v-date-picker>
        </v-menu>
    </v-col>
    <v-col cols="12" md="2">
          <v-select
          v-model="weekday"
          :items="weekdays"
          item-value="id"
          item-text="name"
          return-object
          dense
          outlined
          hide-details
          @change="filterBywm"
          label="เลือก"
          class="ma-2"
        ></v-select>
      </v-col>
    </v-row> -->
    <ContentUI />
   <!--  </v-card> -->
  </v-container>
</template>
<script>
// import eventBus from '@/components/eventBus'
export default {
  components: {
    ContentUI: () => import('@/components/Revenue/table')
  },
  data () {
    return {
      freq: false,
      counterList: '',
      dataFreq: [],
      dataSum: [],
      headers2: [],
      SETT: [],
      weekday: [1],
      weekdays: [
        {
          id: 1, name: 'ทั้งหมด'
        },
        {
          id: 2, name: 'สัปดาห์'
        },
        {
          id: 3, name: 'ปี'
        }
      ],
      dateStart: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      menu1: false,
      itemsData: [],
      itemsData2: [],
      ListData: '',
      percentage: ''
    }
  },
  created () {
    this.$EventBus.$on('appendData', this.appendData)
    this.$EventBus.$emit('changeNav')
    // this.init()
  },
  destroyed () {
    this.$EventBus.$off('appendData')
  },
  computed: {
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    },
    charactersLeft () {
      var char = this.ListData.split().length - 1
      return char
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    dateStart (val) {
      this.dateFormatted = this.formatDate(this.dateStart)
    }
  },
  methods: {
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    // async init () {
    //   const data = await {
    //     seller_shop_id: localStorage.getItem('shopSellerID'),
    //     choose_type: '',
    //     choose_date: ''
    //   }
    //   console.log(data)
    //   await this.$store.dispatch('actionsListRefMerchant', data)
    //   const { data: { incomeShop = '', listRefShare, percentageNewOld = '' } = {} } = await this.$store.state.ModuleShop.stateListRefMerchant
    //   this.itemsData = await listRefShare
    //   this.ListData = await listRefShare.length
    //   this.counterList = await incomeShop
    //   this.percentage = await percentageNewOld
    //   console.log('this.itemsData', this.itemsData)
    // },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}/${month}/${day}`
    },
    parseDate (date) {
      // console.log('parseDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${day}-${month}-${year}`
    },
    async selectDate () {
      // const data = await {
      //   date: his.dateStart
      // }
      // console.log('เข้า filter date')
      this.$store.state.ModuleShop.dateBus = this.dateStart
      await this.$EventBus.$emit('filterDate')
    },
    async filterBywm () {
      const data = await {
        name: this.weekday.name === 'สัปดาห์' ? 'week' : this.weekday.name === 'ปี' ? 'year' : ''
      }
      await this.$EventBus.$emit('filterData', data)
    },
    async appendData () {
      const data = await {
        seller_shop_id: localStorage.getItem('shopSellerID'),
        choose_type: this.weekday.name === 'สัปดาห์' ? 'week' : this.weekday.name === 'ปี' ? 'year' : '',
        choose_date: ''
      }
      // console.log(data)
      await this.$store.dispatch('actionsListRefMerchant', data)
      const { data: { incomeShop = '', listRefShare } = {} } = await this.$store.state.ModuleShop.stateListRefMerchant
      this.itemsData = await listRefShare
      this.ListData = await listRefShare.length
      this.counterList = await incomeShop
      // this.percentage = await percentageNewOld
    }
  }
}
</script>
<style scoped>
.h-card{
  width: 249px;
  height: 268px;
  background: #E4F4F1;
  border:1px solid #5BC3A2;
  border-radius: 12px;
}
.h-img-b {
 width: 88px;
 height: 88px;
 border-radius: 20px;
 background: #ffffff;
}
.top {
  font-size: 22px;
  margin-left: 1em;
  margin-top: 10px;
}
</style>
