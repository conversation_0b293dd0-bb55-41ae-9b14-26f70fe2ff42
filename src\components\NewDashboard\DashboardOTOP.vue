<template>
  <div class="d-flex justify-center">
    <v-container :class="MobileSize ? 'mt-3' : 'pa-0 mt-5'">
      <v-card>
        <v-img
          src="@/assets/bannerOTOP.png"
          contain
          v-if="!MobileSize && !IpadSize"
        ></v-img>
        <v-card width="100%" height="100%" style="background: #FFFFFF; !important; margin-top: 1px !important;" :style="MobileSize ? 'z-index: 99' : ''" elevation="0" class="mx-0 my-0 px-3 py-8 mb-5">
          <v-row class="d-flex align-end">
            <v-col>
              <v-btn @click="backtoSellerMenu()" outlined style="border: 0; margin-top: -2vw;" class="pa-0">
                <v-icon color="#27AB9C" size="30">mdi-chevron-left</v-icon>
                <span>กลับสู่หน้าหลักแอดมิน</span>
              </v-btn>
            </v-col>
            <v-spacer></v-spacer>
            <v-col class="d-flex justify-end" v-if="!MobileSize">
              <v-btn color="#9dd4ea" class="mx-2" style="width: 100px;" @click="confirmFilter" rounded>ค้นหา</v-btn>
              <v-btn color="#c6c6c6" style="width: 100px;" rounded @click="clearFilter">ล้างค่า</v-btn>
            </v-col>
          </v-row>
          <v-row v-if="!MobileSize">
            <v-col :cols="IpadSize ? 4 : 2" class="pr-0">
              <v-dialog
                ref="dialogStartDate"
                v-model="dialogStartDate"
                width="290px"
              >
              <template v-slot:activator="{ on, attrs }">
                <!-- <v-select
                  v-model="sentStartDate"
                  v-bind="attrs"
                  placeholder="วันที่"
                  outlined
                  dense
                  v-on="on"
                  class="customSelect mr-1"
                  hide-details
                  rounded
                >
                </v-select> -->
                <v-text-field
                  v-model="sentStartDate"
                  v-bind="attrs"
                  v-on="on"
                  hide-details
                  outlined
                  dense
                  rounded
                  placeholder="วันที่"
                  class="customSelect mr-1"
                  readonly
                >
                </v-text-field>
              </template>
              <v-card>
                <v-card-title>
                  <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันที่</span>
                  <v-spacer></v-spacer>
                  <v-btn text @click="cancelPickDate" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                </v-card-title>
                <v-date-picker
                  v-model="date"
                  range
                  locale="TH-th"
                  no-title
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-btn
                    text
                    color="primary"
                    @click="cancelPickDate"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="clearSelectDate"
                  >
                    ล้างค่า
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    :disabled="date.length === 0"
                    @click="changeformatdate"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-card>
            </v-dialog>
            </v-col>
            <v-col :cols="IpadSize ? 4 : 2" class="pr-0">
              <v-autocomplete
                v-model="selectsector"
                :items="sector"
                dense
                outlined
                placeholder="ภาค"
                class="customSelect mr-1"
                no-data-text="ไม่พบภาคที่ค้นหา"
                rounded
                hide-details
                item-text="name"
                item-value="id"
              ></v-autocomplete>
            </v-col>
            <v-col :cols="IpadSize ? 4 : 2" class="pr-0">
              <v-autocomplete
                v-model="selectProvince"
                :items="province"
                dense
                outlined
                placeholder="จังหวัด"
                class="customSelect mr-1"
                no-data-text="ไม่พบจังหวัดที่ค้นหา"
                rounded
                hide-details
                item-text="name_th"
                item-value="id"
              ></v-autocomplete>
            </v-col>
            <v-col :cols="IpadSize ? 4 : 2">
              <v-autocomplete
                v-model="selectedProductType"
                :items="productType"
                dense
                outlined
                placeholder="ประเภทสินค้า"
                class="customSelect mr-1"
                no-data-text="ไม่พบประเภทสินค้าที่ค้นหา"
                rounded
                hide-details
                item-text="type"
                item-value="id"
              ></v-autocomplete>
            </v-col>
            <v-col :cols="IpadSize ? 4 : 2" class="pr-0">
              <v-autocomplete
                v-model="selectedOTOPType"
                :items="OTOPTypeItem"
                placeholder="ประเภท OTOP"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                rounded
                item-text="type"
                item-value="val"
                no-data-text="ไม่พบประเภท OTOP"
              >
              </v-autocomplete>
            </v-col>
            <v-col :cols="IpadSize ? 4 : 2" class="d-flex">
              <v-autocomplete
                v-model="selectedShop"
                :items="shopList"
                dense
                outlined
                placeholder="ร้านค้า"
                class="customSelect mr-1"
                no-data-text="ไม่พบร้านค้าที่ค้นหา"
                rounded
                hide-details
                item-text="shop_name_th"
                item-value="id"
              ></v-autocomplete>
              <!-- <v-btn color="#9dd4ea" class="mx-2" style="width: 100px;" @click="confirmFilter" rounded>ค้นหา</v-btn>
              <v-btn color="#c6c6c6" style="width: 100px;" rounded @click="clearFilter">ล้างค่า</v-btn> -->
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col cols="12" style="display: grid;">
              <v-btn color="primary" rounded outlined @click="openFilterDialog">ตัวกรอง</v-btn>
              <v-dialog
                v-model="filterMobileDialog"
              >
                <v-card>
                  <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
                    <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
                      ตัวกรอง
                    </span>
                    <v-btn icon dark @click="closeFilterDialog">
                      <v-icon color="#27AB9C">mdi-close</v-icon>
                    </v-btn>
                  </v-toolbar>
                  <v-card-text class="mt-5">
                    <v-row>
                      <v-col cols="12" class="pa-3">
                        <v-dialog
                          ref="dialogStartDate"
                          v-model="dialogStartDate"
                          width="290px"
                        >
                          <template v-slot:activator="{ on, attrs }">
                            <v-text-field
                              v-model="sentStartDate"
                              v-bind="attrs"
                              v-on="on"
                              hide-details
                              outlined
                              dense
                              placeholder="วันที่"
                              class="customSelect mr-1"
                            >
                            </v-text-field>
                          </template>
                          <v-card>
                          <v-card-title>
                            <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันที่</span>
                            <v-spacer></v-spacer>
                            <v-btn text @click="cancelPickDate" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                          </v-card-title>
                          <v-date-picker
                              v-model="date"
                              range
                              locale="TH-th"
                              no-title
                              :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                            >
                              <v-btn
                                text
                                color="primary"
                                @click="cancelPickDate"
                              >
                                ยกเลิก
                              </v-btn>
                              <v-spacer></v-spacer>
                              <v-btn
                                text
                                color="primary"
                                @click="clearSelectDate"
                              >
                                ล้างค่า
                              </v-btn>
                              <v-btn
                                text
                                color="primary"
                                :disabled="date.length === 0"
                                @click="changeformatdate"
                              >
                                ตกลง
                              </v-btn>
                            </v-date-picker>
                          </v-card>
                        </v-dialog>
                      </v-col>
                      <v-col cols="12">
                        <v-autocomplete
                          v-model="selectsector"
                          :items="sector"
                          dense
                          outlined
                          placeholder="ภาค"
                          class="customSelect mr-1"
                          no-data-text="ไม่พบภาคที่ค้นหา"
                          hide-details
                          item-text="name"
                          item-value="id"
                        ></v-autocomplete>
                      </v-col>
                      <v-col cols="12">
                        <v-autocomplete
                          v-model="selectProvince"
                          :items="province"
                          dense
                          outlined
                          placeholder="จังหวัด"
                          class="customSelect mr-1"
                          no-data-text="ไม่พบจังหวัดที่ค้นหา"
                          hide-details
                          item-text="name_th"
                          item-value="id"
                        ></v-autocomplete>
                      </v-col>
                      <v-col cols="12">
                        <v-autocomplete
                          v-model="selectedProductType"
                          :items="productType"
                          dense
                          outlined
                          placeholder="ประเภทสินค้า"
                          class="customSelect mr-1"
                          no-data-text="ไม่พบประเภทสินค้าที่ค้นหา"
                          hide-details
                          item-text="type"
                          item-value="id"
                        ></v-autocomplete>
                      </v-col>
                      <v-col cols="12">
                        <v-autocomplete
                          v-model="selectedShop"
                          :items="shopList"
                          dense
                          outlined
                          placeholder="ร้านค้า"
                          class="customSelect mr-1"
                          no-data-text="ไม่พบร้านค้าที่ค้นหา"
                          hide-details
                          item-text="shop_name_th"
                          item-value="id"
                        ></v-autocomplete>
                      </v-col>
                      <v-col cols="12">
                        <v-autocomplete
                          v-model="selectedOTOPType"
                          :items="OTOPTypeItem"
                          placeholder="ประเภท OTOP"
                          outlined
                          dense
                          class="customSelect mr-1"
                          hide-details
                          item-text="type"
                          item-value="val"
                          no-data-text="ไม่พบประเภท OTOP"
                        >
                        </v-autocomplete>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col cols="6">
                        <v-btn block color="#2faea0" outlined @click="clearFilter">ล้างค่า</v-btn>
                      </v-col>
                      <v-col cols="6">
                        <v-btn block color="#2faea0" class="white--text" @click="confirmFilter">ค้นหา</v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-dialog>
            </v-col>
          </v-row>
          <v-row>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" class="px-0" style=" border-radius: 7px;">
              <!-- <v-card class="mx-3 mt-1" > -->
              <v-card class="mx-4 d-flex">
                <v-card-text class="d-flex flex-column align-center" style="color: #000000DE;" :style="IpadSize ? 'font-size: x-large' : 'font-size: medium'">
                  <img class="mr-2" src="@/assets/icons/checklist_2.png" width="80" height="80">
                  <span class="mt-2"><b>จำนวนร้านค้า</b></span>
                  <v-spacer></v-spacer>
                  <span><b>{{ newShop.toLocaleString('en-US') }} ร้าน</b></span>
                </v-card-text>
                <v-card-text class="d-flex flex-column align-center" style="color: #000000DE;" :style="IpadSize ? 'font-size: x-large' : 'font-size: medium'">
                  <img class="mr-2" src="@/assets/box_2.png" width="80" height="80">
                  <span class="mt-2"><b>จำนวนสินค้า</b></span>
                  <v-spacer></v-spacer>
                  <span><b>{{ summarySKU.toLocaleString('en-US') }} ชิ้น</b></span>
                </v-card-text>
              </v-card>
              <div
                  class="coupon-container"
              >
                <img
                :src="require('@/assets/bgOTOP.png')"
                class="coupon-image"
                :style="MobileSize ? 'height: 200px;' : IpadSize ? 'height: 275px;' : 'height: 215px;'"
                >
                <div class="coupon-content" style="top: 0; left: 0;" :style="IpadSize ? 'padding: 6vw;' : MobileSize ? 'padding: 6vw;' : 'padding: 30px;'">
                  <div class="d-flex pa-1" :style="IpadSize ? 'font-size: x-large' : 'font-size: large'">
                    <span><b>ยอดสะสมออร์เดอร์ (รายการ)</b></span>
                    <v-spacer></v-spacer>
                    <span><b>{{ totalOrders }}</b></span>
                  </div>
                  <div class="d-flex pa-1" :style="IpadSize ? 'font-size: x-large' : 'font-size: large'">
                    <span><b>ยอดสะสมรวม (บาท)</b></span>
                    <v-spacer></v-spacer>
                    <span><b>{{ totalSales }}</b></span>
                  </div>
                  <v-divider style="border: .5px dashed #a0a0a0 !important;"></v-divider>
                  <div class="d-flex align-center justify-space-around" style="margin-top: 12px" :style="IpadSize ? 'gap: 5vw;' : IpadProSize ? 'gap: 6px;' : 'gap: 7px;'">
                    <!-- <img v-if="!MobileSize" src="@/assets/ICON/iconOTOP1.png" alt="" :height="IpadSize ? 110 : 50" :width="IpadSize ? 110 : 50"> -->
                    <div class="d-flex flex-column align-center" :style="IpadSize ? 'font-size: large' : ''">
                      <span style="font-size: large;"><b>รับหน้าร้านสะสม</b></span>
                      <span class="text-truncate"><b><span style="color: #2b81d6;" :style="MobileSize ? 'font-size: x-small' : ''">{{ totalOrdersReceive }}</span> <span>รายการ</span> <span style="color: #2b81d6;" :style="IpadSize ? 'font-size: small;' : 'font-size: xx-small;'">(+{{ increasedOrdersReceive }})</span></b></span>
                      <span class="text-truncate"><b><span style="color: #64BD34;" :style="MobileSize ? 'font-size: x-small' : ''">{{ totalReceive }}</span> <span>บาท</span> <span style="color: #64BD34;" :style="IpadSize ? 'font-size: small;' : 'font-size: xx-small;'">(+{{ increasedReceive }})</span></b></span>
                    </div>
                    <v-divider style="border: .5px dashed #a0a0a0 !important;" vertical></v-divider>
                    <div class="d-flex flex-column align-center" :style="IpadSize ? 'font-size: large' : ''">
                      <span style="font-size: large;"><b>ซื้อออนไลน์สะสม</b></span>
                      <span class="text-truncate"><b><span style="color: #2b81d6;" :style="MobileSize ? 'font-size: x-small' : ''">{{ totalOrdersOnline }}</span> <span>รายการ</span> <span style="color: #2b81d6;" :style="IpadSize ? 'font-size: small;' : 'font-size: xx-small;'">(+{{ increasedOrdersOnline }})</span></b></span>
                      <span class="text-truncate"><b><span style="color: #64BD34;" :style="MobileSize ? 'font-size: x-small' : ''">{{ totalOnline }} </span> <span>บาท</span> <span style="color: #64BD34;" :style="IpadSize ? 'font-size: small;' : 'font-size: xx-small;'">(+{{ increasedOnline }})</span></b></span>
                      <!-- <span style="color: #014ee1;" :style="MobileSize ? 'font-size: medium' : IpadSize ? 'font-size: x-large' : 'font-size: large'"><b>{{ totalOnline }}</b></span>
                      <span><b>ออนไลน์สะสม (บาท)</b></span>
                      <span style="white-space: nowrap;" :style="IpadSize ? 'font-size: small' : 'font-size: x-small'"><b>ยอดเพิ่มขึ้น ณ วันปัจจุบัน <span style="color: #64BD34;">+{{ increasedOnline }}</span></b></span> -->
                    </div>
                  </div>
                </div>
              </div>
              <!-- </v-card> -->
              <v-card class="mx-4 pa-2" style="margin-top: -8px; !important;" :style="MobileSize || IpadSize ? '' : 'height: 505px;'">
                <div class="d-flex align-center pa-3">
                  <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
                  <div>
                    <span style="font-size: medium;"><b>Ranking Top 10</b></span><br>
                    <span>จัดอันดับจังหวัดที่มียอดขายสูงที่สุด</span>
                  </div>
                </div>
                <div>
                  <v-data-table
                    :headers="headers"
                    :items="rankingProvince"
                    :items-per-page="10"
                    class="elevation-1"
                    item-key="i"
                    no-data-text="ไม่มีรายการอันดับยอดขายของจังหวัด"
                    no-results-text="ไม่พบรายการอันดับยอดขายของจังหวัด"
                    style="white-space: nowrap;"
                    hide-default-footer
                    >
                    <template v-slot:[`item.indexOfUser`]="{ item }">
                        <img v-if="item.indexOfUser === 1" src="@/assets/ICON/iconFirst.png" alt="">
                        <img v-else-if="item.indexOfUser === 2" src="@/assets/ICON/iconSecond.png" alt="">
                        <img v-else-if="item.indexOfUser === 3" src="@/assets/ICON/iconThird.png" alt="">
                        <span v-else>{{ item.indexOfUser }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_all`]="{ item }">
                        <span>{{ item.total_revenue_all.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_front`]="{ item }">
                        <span>{{ item.total_revenue_front.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_online`]="{ item }">
                        <span>{{ item.total_revenue_online.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                  </v-data-table>
                </div>
              </v-card>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 6 : 4" :class="MobileSize || IpadSize ? 'mt-3' : ''" style=" border-radius: 7px;">
              <v-card class="pa-3" >
                <div>
                  <div class="d-flex align-center pa-3">
                    <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
                    <span style="font-size: medium;"><b>ยอดขายประเภทสินค้า 5 ประเภท (ร้อยละ)</b></span>
                  </div>
                  <div class="d-flex justify-center" v-if="!allZero">
                    <apexchart
                    type="donut"
                    :series="dataDonut.series"
                    :options="dataDonut.chartOptions"
                    height="302" width="302"
                    >
                    </apexchart>
                  </div>
                  <div v-else>
                    <v-card style="border: 0;" outlined>
                      <v-card-text class="d-flex align-center flex-column" style="padding: 2vw;">
                        <v-img
                        src="@/assets/graphNot.jpg"
                        :width="IpadProSize ? 230 : 221"
                        :height="IpadProSize ? 230 : 221"
                        contain
                        ></v-img>
                        <span style="font-size: small;">ไม่มียอดขายสินค้า</span>
                      </v-card-text>
                    </v-card>
                  </div>
                  <div class="d-flex flex-column align-center">
                    <div>
                      <span class="mr-1"><v-icon small color="#c39bd3">mdi-circle</v-icon>{{ listTypeCategory[0] }}</span>
                      <span class="mr-1"><v-icon small color="#ec7063">mdi-circle</v-icon>{{ listTypeCategory[1] }}</span>
                    </div>
                    <div>
                      <span class="mr-1"><v-icon small color="#f8c471">mdi-circle</v-icon>{{ listTypeCategory[2] }}</span>
                      <span class="mr-1"><v-icon small color="#58d68d">mdi-circle</v-icon>{{ listTypeCategory[3] }}</span>
                      <span><v-icon small color="#85c1e9">mdi-circle</v-icon>{{ listTypeCategory[4] }}</span>
                    </div>
                  </div>
                  <v-divider style="border: .5px dashed #a0a0a0 !important;" class="my-4"></v-divider>
                  <div>
                    <span><b>รายละเอียดยอดขายประเภทสินค้า 5 ประเภท</b></span>
                    <div
                    v-for="(item, index) in dataRank"
                    :key="index"
                    class="mb-4 mt-4"
                    style="font-size: small;"
                    >
                      <div class="d-flex">
                        <span class="mr-3"><b>{{ index+1 }}</b></span>
                        <span><b>{{ item.title }}</b></span>
                        <v-spacer></v-spacer>
                        <span class="mr-1"><b>{{ item.orders }} รายการ</b></span>
                        <span><b>{{ item.price }} บาท</b></span>
                      </div>
                      <v-spacer></v-spacer>
                      <div>
                        <div class="d-flex justify-space-between">
                          <div>หน้าร้าน : {{ item.rateOff }}%</div>
                          <div>Online : {{ item.rateOn }}%</div>
                        </div>
                        <div class="progress-wrapper" style="height: 20px; background-color: #e0e0e0; border-radius: 4px; overflow: hidden;">
                          <div
                            :style="{
                              width: item.rateOff + '%',
                              backgroundColor: '#6c8ef5',
                              height: '100%',
                              display: 'inline-block'
                            }"
                          ></div>
                          <div
                            :style="{
                              width: item.rateOn + '%',
                              backgroundColor: '#b6f3b0',
                              height: '100%',
                              display: 'inline-block'
                            }"
                          ></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </v-card>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 12 : 4" :class="IpadProSize ? 'd-flex' : ''" :style="IpadProSize ? 'gap: 2vw;' : ''">
              <v-card class="pa-2 mt-1" :style="!MobileSize && !IpadSize && !IpadProSize ? 'height: 505px;' : IpadProSize ? 'width: 47vw;' : ''">
                <div class="d-flex align-center pa-3">
                  <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
                  <div>
                    <span style="font-size: medium;"><b>Ranking Shop Top 10</b></span><br>
                    <span>จัดอันดับร้านค้าที่มียอดขายสูงที่สุด</span>
                  </div>
                </div>
                <div>
                  <v-data-table
                    :headers="headerShops"
                    :items="rankingShops"
                    :items-per-page="10"
                    class="elevation-1"
                    item-key="i"
                    no-data-text="ไม่มีรายการอันดับยอดขายของร้านค้า"
                    no-results-text="ไม่พบรายการอันดับยอดขายของร้านค้า"
                    style="white-space: nowrap;"
                    hide-default-footer
                    >
                    <template v-slot:[`item.indexOfUser`]="{ item }">
                        <img v-if="item.indexOfUser === 1" src="@/assets/ICON/iconFirst.png" alt="">
                        <img v-else-if="item.indexOfUser === 2" src="@/assets/ICON/iconSecond.png" alt="">
                        <img v-else-if="item.indexOfUser === 3" src="@/assets/ICON/iconThird.png" alt="">
                        <span v-else>{{ item.indexOfUser }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_all`]="{ item }">
                        <span>{{ item.total_revenue_all.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_front`]="{ item }">
                        <span>{{ item.total_revenue_front.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                    <template v-slot:[`item.total_revenue_online`]="{ item }">
                        <span>{{ item.total_revenue_online.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }}</span>
                    </template>
                  </v-data-table>
                </div>
              </v-card>
              <v-card :class="IpadProSize ? '' : 'mt-5'" :style="IpadProSize ? 'width: 50%' : ''">
                <div class="d-flex align-center pa-3">
                  <img class="mr-2" src="@/assets/ICON/iconRanking.png" width="35" height="35">
                  <div>
                    <span style="font-size: medium;"><b>Peak Time avg.</b></span><br>
                    <span>แสดงช่วงเวลาใน 1 วันที่มีการซื้อสูงสุด</span>
                  </div>
                </div>
                <div class="d-flex justify-center">
                  <apexchart
                    type="line"
                    :series="dataLine.series"
                    :options="dataLine.chartOptions"
                    height="262" :width="MobileSize ? 330 : IpadSize ? 730 : IpadProSize ? 480 : 450"
                  >
                  </apexchart>
                </div>
              </v-card>
            </v-col>
          </v-row>
        </v-card>
      </v-card>
    </v-container>
  </div>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import { Decode } from '@/services'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      totalSales: '',
      totalOrders: '',
      dateFilter: '',
      date: [`${new Date().getFullYear()}-01-01`, this.formatDateToYMD(new Date())],
      dialogStartDate: false,
      sentStartDate: '',
      filterMobileDialog: false,
      sector: ['ภาคเหนือ', 'ภาคตะวันออกเฉียงเหนือ', 'ภาคตะวันตก', 'ภาคกลาง', 'ภาคตะวันออก', 'ภาคใต้'],
      province: [
        'กรุงเทพมหานคร', 'อำนาจเจริญ', 'อ่างทอง', 'บึงกาฬ', 'บุรีรัมย์', 'ฉะเชิงเทรา', 'ชัยนาท', 'ชัยภูมิ', 'จันทบุรี', 'เชียงใหม่',
        'เชียงราย', 'ชลบุรี', 'ชุมพร', 'กาฬสินธุ์', 'กำแพงเพชร', 'กาญจนบุรี', 'ขอนแก่น', 'กระบี่', 'ลำปาง', 'ลำพูน',
        'เลย', 'ลพบุรี', 'แม่ฮ่องสอน', 'มหาสารคาม', 'มุกดาหาร', 'นครนายก', 'นครปฐม', 'นครพนม', 'นครราชสีมา', 'นครสวรรค์',
        'นครศรีธรรมราช', 'น่าน', 'นราธิวาส', 'หนองบัวลำภู', 'หนองคาย', 'นนทบุรี', 'ปทุมธานี', 'ปัตตานี', 'พังงา', 'พัทลุง',
        'พะเยา', 'เพชรบูรณ์', 'เพชรบุรี', 'พิจิตร', 'พิษณุโลก', 'พระนครศรีอยุธยา', 'แพร่', 'ภูเก็ต', 'ปราจีนบุรี', 'ประจวบคีรีขันธ์',
        'ระนอง', 'ราชบุรี', 'ระยอง', 'ร้อยเอ็ด', 'สระแก้ว', 'สกลนคร', 'สมุทรปราการ', 'สมุทรสาคร', 'สมุทรสงคราม', 'สระบุรี',
        'สตูล', 'สิงห์บุรี', 'ศรีสะเกษ', 'สงขลา', 'สุโขทัย', 'สุพรรณบุรี', 'สุราษฎร์ธานี', 'สุรินทร์', 'ตาก', 'ตรัง',
        'ตราด', 'อุบลราชธานี', 'อุดรธานี', 'อุทัยธานี', 'อุตรดิตถ์', 'ยะลา', 'ยโสธร'
      ],
      selectProvince: '',
      selectsector: '',
      productType: [
        'ทดสอบ1',
        'ทดสอบ2',
        'ทดสอบ3'
      ],
      selectedProductType: '',
      shopList: [],
      selectedShop: '',
      dataFilter: [],
      totalReceive: '',
      increasedReceive: '',
      totalOnline: '',
      increasedOnline: '',
      headers: [
        { text: 'ลำดับ', align: 'center', sortable: false, value: 'indexOfUser', class: 'backgroundTable fontTable--text' },
        { text: 'จังหวัด', align: 'center', sortable: false, value: 'province', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดขาย', align: 'center', sortable: false, value: 'total_revenue_all', class: 'backgroundTable fontTable--text' },
        { text: 'หน้าร้าน', align: 'center', sortable: false, value: 'total_revenue_front', class: 'backgroundTable fontTable--text' },
        { text: 'ออนไลน์', align: 'center', sortable: false, value: 'total_revenue_online', class: 'backgroundTable fontTable--text' }
      ],
      headerShops: [
        { text: 'ลำดับ', align: 'center', sortable: false, value: 'indexOfUser', class: 'backgroundTable fontTable--text' },
        { text: 'ร้านค้า', align: 'center', sortable: false, value: 'shop_name', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดขาย', align: 'center', sortable: false, value: 'total_revenue_all', class: 'backgroundTable fontTable--text' },
        { text: 'หน้าร้าน', align: 'center', sortable: false, value: 'total_revenue_front', class: 'backgroundTable fontTable--text' },
        { text: 'ออนไลน์', align: 'center', sortable: false, value: 'total_revenue_online', class: 'backgroundTable fontTable--text' }
      ],
      headersReceive: [
        { text: 'ช่วงเวลา', align: 'center', sortable: false, value: 'time', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดขายสะสม', align: 'center', sortable: false, value: 'revenue', class: 'backgroundTable fontTable--text' }
      ],
      rankingProvince: [],
      dataRank: [
        { title: '', price: '', offline: '', online: '', rateOff: '', rateOn: '' }
      ],
      rankingTimeReceive: [],
      rankingTimeOnline: [],
      listSummary: [],
      listTypeCategory: [],
      revenue: '',
      startDate: '',
      endDate: '',
      // OTOPTypeItem: [
      //   {'OTOP',
      //   'OTOP Midyear'}
      // ],
      OTOPTypeItem: [
        { type: 'OTOP', val: 'OTOP' },
        { type: 'OTOP Midyear', val: 'OTOP Midyear' }
      ],
      selectedOTOPType: 'OTOP',
      allZero: false,
      newShop: '0',
      summarySKU: '0',
      rankingShops: [],
      revenueOrder: [],
      totalOrdersReceive: '',
      totalOrdersOnline: '',
      increasedOrdersReceive: '',
      increasedOrdersOnline: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dataDonut () {
      return {
        series: [],
        chartOptions: {
          chart: {
            type: 'donut',
            fontFamily: 'Noto Sans Thai, sans-serif',
            fontWeight: 900,
            fontSize: '24px'
          },
          tooltip: {
            y: {
              formatter: function (val, opts) {
                const total = opts.globals.seriesTotals.reduce((a, b) => a + b, 0)
                const percent = ((val / total) * 100)
                return `(${percent.toFixed(2)}%)`
              },
              title: {
                formatter: function (seriesName) {
                  return seriesName + ':'
                }
              }
            }
          },
          plotOptions: {
            pie: {
              donut: {
                size: '50%',
                labels: {
                  show: false
                }
              }
            }
          },
          dataLabels: {
            enabled: true,
            formatter: (val, opts) => {
              return opts.w.config.series[opts.seriesIndex] + '%'
            },
            style: {
              fontFamily: 'Noto Sans Thai, sans-serif',
              fontSize: '12px'
            }
          },
          labels: this.listTypeCategory,
          colors: ['#c39bd3', '#ec7063', '#f8c471', '#58d68d', '#85c1e9'],
          legend: {
            show: false,
            fontSize: '8px'
          },
          responsive: [{
            options: {
              chart: {
                width: 200
              },
              legend: {
                position: 'bottom'
              }
            }
          }]
        }
      }
    },
    dataLine () {
      const dataRevenue = this
      return {
        series: [],
        chartOptions: {
          chart: {
            height: 350,
            type: 'line',
            zoom: {
              enabled: false
            },
            toolbar: {
              show: false
            }
          },
          colors: ['#D00000', '#004F7C'],
          dataLabels: {
            enabled: false
          },
          stroke: {
            curve: 'straight',
            width: 2
          },
          title: {
            // text: 'Product Trends by Month',
            align: 'left'
          },
          grid: {
            row: {
              colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
              opacity: 0.5
            }
          },
          xaxis: {
            categories: ['08:00', '10:00', '12:00', '14:00', '16:00', '18:00', '20:00', '22:00', '00:00', '02:00']
          },
          yaxis: {
            labels: {
              formatter: function (val) {
                return val.toFixed(0)
              }
            }
          },
          tooltip: {
            enabled: true,
            shared: true,
            custom: function ({ series, dataPointIndex, w }) {
              // const revenueData = this.revenueOrder
              const colors = ['#d00000', '#004f7c']

              const formatCurrency = (value) => {
                return value.toLocaleString('th-TH', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
              }

              let tooltipHtml = '<div class="apexcharts-tooltip-box">'

              w.globals.initialSeries.forEach((s, index) => {
                const seriesName = s.name
                const dataValue = s.data[dataPointIndex]
                const currentRevenue = dataRevenue.revenueOrder[index][dataPointIndex]
                const color = colors[index]

                tooltipHtml += `
                  <div style="display: flex; align-items: center; margin-bottom: 4px;">
                    <span style="
                      display: inline-block;
                      width: 10px;
                      height: 10px;
                      border-radius: 50%;
                      background-color: ${color};
                      margin-right: 6px;
                    "></span>
                    <div><b>${seriesName}</b>: ${dataValue} <b>ออร์เดอร์</b> ${formatCurrency(currentRevenue)} <b>บาท</b></div>
                  </div>`
              })

              tooltipHtml += '</div>'
              return tooltipHtml
            }
          }
        }
      }
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardOTOPMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardOTOP' }).catch(() => {})
      }
    }
  },
  async created () {
    await this.getItemData()
    this.getListDetailOTOP()
  },
  methods: {
    cancelPickDate () {
      this.date = [`${new Date().getFullYear()}-01-01`, this.formatDateToYMD(new Date())]
      this.changeformatdate()
      this.dialogStartDate = false
    },
    changeformatdate () {
      // console.log(this.date, '484')
      var date1 = this.date[0]
      var date2 = this.date[1] !== undefined ? this.date[1] : this.date[0]
      // var dateStart = ''
      // var dateEnd = ''

      // dateStart = date1.split('-')
      // dateEnd = date2.split('-')
      // console.log(date1, 4555)
      if (date1 > date2) {
        this.dataFilter = [date2, date1]
        this.sentStartDate = this.formatDateToThai(date2) + ' - ' + this.formatDateToThai(date1)
        this.endDate = date1
        this.startDate = date2
      } else if (date1 < date2) {
        this.dataFilter = [date1, date2]
        this.sentStartDate = this.formatDateToThai(date1) + ' - ' + this.formatDateToThai(date2)
        this.endDate = date2
        this.startDate = date1
      } else {
        this.sentStartDate = this.formatDateToThai(date1)
        this.endDate = date1
        this.startDate = date1
      }
      // console.log(this.sentStartDate, 'this.sentStartDate')
      this.dialogStartDate = false
    },
    cancelSelectDate () {
      this.sentStartDate = ''
      this.date = [`${new Date().getFullYear()}-01-01`, this.formatDateToYMD(new Date())]
      // this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.dialogStartDate = false
    },
    clearSelectDate () {
      this.sentStartDate = ''
      this.date = []
      // this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    },
    closeFilterDialog () {
      this.filterMobileDialog = false
    },
    openFilterDialog () {
      this.filterMobileDialog = true
    },
    async clearFilter () {
      this.date = [`${new Date().getFullYear()}-01-01`, this.formatDateToYMD(new Date())]
      await this.changeformatdate()
      this.selectsector = ''
      this.selectProvince = ''
      this.selectedShop = ''
      this.selectedProductType = ''
      this.selectedOTOPType = 'OTOP'
      // this.startDate = ''
      // this.endDate = ''
      await this.getItemData()
      this.getListDetailOTOP()
    },
    // confirmFilter () {
    //   this.filterMobileDialog = false
    //   this.getListDetailOTOP()
    // },
    async confirmFilter () {
      this.filterMobileDialog = false
      if (this.selectedOTOPType === null) {
        this.selectedOTOPType = 'OTOP'
      }
      await this.getItemData()
      this.getListDetailOTOP()
    },
    formatDateToThai (dateStr) {
      const [year, month, day] = dateStr.split('-')
      const buddhistYear = parseInt(year) + 543
      return `${day}/${month}/${String(buddhistYear).substring(4, 2)}`
    },
    formatDateToYMD (date) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      return `${year}-${month}-${day}`
    },
    async getItemData () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        // url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/filters`,
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/filters?${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.shopList = response.data.data.shops
          this.sector = response.data.data.regions
          this.province = response.data.data.provinces
          this.productType = response.data.data.types
          // this.newShop = response.data.data.shops.length
          // this.summarySKU = response.data.data.totalProductCount
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.changeformatdate()
      this.$store.commit('closeLoader')
    },
    async getListDetailOTOP () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // await this.axios({
      //   url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/filters`,
      //   // data: this.exportExcelBody,
      //   headers: { Authorization: `Bearer ${oneData.user.access_token}` },
      //   method: 'GET'
      // }).then((response) => {
      //   this.shopList = response.data.data.shops
      //   this.sector = response.data.data.regions
      //   this.province = response.data.data.provinces
      //   this.productType = response.data.data.types
      // })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/shops?${this.selectProvince !== '' && this.selectProvince !== null ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' && this.selectsector !== null ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' && this.selectedProductType !== null ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' && this.selectedShop !== null ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.newShop = response.data.data.shopsCount
          this.summarySKU = response.data.data.totalProductCount
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        // url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/summary`,
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/summary?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' && this.selectProvince !== null ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' && this.selectsector !== null ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' && this.selectedProductType !== null ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' && this.selectedShop !== null ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.listSummary = response.data.data
          this.totalReceive = this.listSummary[0].revenue.total_otop_revenue_vat.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.totalOrdersReceive = this.listSummary[0].orders.total_order.toLocaleString('en-US')
          this.totalOnline = this.listSummary[1].revenue.total_otop_revenue_vat.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.totalOrdersOnline = this.listSummary[1].orders.total_order.toLocaleString('en-US')
          this.increasedReceive = this.listSummary[0].revenue.difference.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.increasedOrdersReceive = this.listSummary[0].orders.difference.toLocaleString('en-US')
          this.increasedOnline = this.listSummary[1].revenue.difference.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
          this.increasedOrdersOnline = this.listSummary[1].orders.difference.toLocaleString('en-US')
          var total = this.listSummary[0].revenue.total_otop_revenue_vat + this.listSummary[1].revenue.total_otop_revenue_vat
          var order = this.listSummary[0].orders.total_order + this.listSummary[1].orders.total_order
          this.totalOrders = order.toLocaleString('en-US')
          this.totalSales = total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/top-provinces?limit=${10}&${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' && this.selectProvince !== null ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' && this.selectsector !== null ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' && this.selectedProductType !== null ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' && this.selectedShop !== null ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          this.rankingProvince = response.data.data
          for (var i = 0; i < this.rankingProvince.length; i++) {
            this.rankingProvince[i].indexOfUser = i + 1
          }
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/top-categories?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' && this.selectProvince !== null ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' && this.selectsector !== null ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' && this.selectedProductType !== null ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' && this.selectedShop !== null ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          var res = response.data.data
          this.listTypeCategory = res.percentage.map(item => item.type)
          this.dataDonut.series = res.percentage.map(item => item.percentRatio)
          this.allZero = this.dataDonut.series.every(value => value === 0)
          this.dataRank = res.grouped.map(item => {
            return {
              title: item.type,
              price: item.revenue.total.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }),
              orders: item.orders.total.toLocaleString('en-US'),
              offline: item.revenue.front,
              online: item.revenue.online,
              rateOff: item.revenue.ratio.front,
              rateOn: item.revenue.ratio.online
            }
          })
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/time-summary?${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' && this.selectProvince !== null ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' && this.selectsector !== null ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' && this.selectedProductType !== null ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' && this.selectedShop !== null ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          var res = response.data.data
          this.rankingTimeReceive = res.times[0].time_slots
          this.rankingTimeOnline = res.times[1].time_slots
          this.dataLine.series = res.peakTimes.map(item => {
            return {
              name: item.shipping_type === 'front' ? 'หน้าร้าน' : 'online',
              data: item.time_slots.map(item => item.quantity)
            }
          })
          this.revenueOrder = res.peakTimes.map(item => {
            return item.time_slots.map(slot => slot.revenue)
          })
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/dashboard/otop/sales/top-shops?limit=${10}&${this.startDate !== '' ? `start_date=${this.startDate}&` : ''}${this.endDate !== '' ? `end_date=${this.endDate}&` : ''}${this.selectProvince !== '' && this.selectProvince !== null ? `province=${this.selectProvince}&` : ''}${this.selectsector !== '' && this.selectsector !== null ? `region=${this.selectsector}&` : ''}${this.selectedProductType !== '' && this.selectedProductType !== null ? `product_type=${this.selectedProductType}&` : ''}${this.selectedShop !== '' && this.selectedShop !== null ? `shop=${this.selectedShop}&` : ''}${this.selectedOTOPType !== '' ? `otop_type=${this.selectedOTOPType}` : ''}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        if (response.data.status === 'success') {
          // var response = response.data.data
          this.rankingShops = response.data.data
          for (var i = 0; i < this.rankingShops.length; i++) {
            this.rankingShops[i].indexOfUser = i + 1
          }
        } else {
          this.$swal.fire({ icon: 'error', text: `${response.message}`, showConfirmButton: false, timer: 2000 })
        }
      })
      this.$store.commit('closeLoader')
    },
    backtoSellerMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      }
    }
  }
}
</script>

<style>

</style>
<style scoped>
.coupon-image {
  width: 100%;
  display: block;
  border: 0;
}
.coupon-container {
  position: relative;
  overflow: hidden;
}
.coupon-content {
  position: absolute;
  /* top: 0;
  left: 0; */
  width: 100%;
  height: 100%;
  box-sizing: border-box;
}
::v-deep td {
  font-size: smaller !important;
}
::v-deep .formatTable .v-data-table__wrapper {
  width: 320px !important;
  box-shadow: rgba(99, 99, 99, 0.2) 0px 2px 8px 0px;
  border-radius: 2pxj;
}
::v-deep .v-data-table > .v-data-table__wrapper > table > thead > tr > th,
::v-deep .v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
  height: 37px !important;
}
::v-deep .v-image__image {
  background-size: cover !important;
}
</style>
