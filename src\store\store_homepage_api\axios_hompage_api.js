import axios from 'axios'
import { Decode } from '@/services'
const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`
        // CacheControl: 'no-cache',
        // Pragma: 'no-cache',
        // Expires: '0'
      }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}
// const GetToken = () => {
//   if (localStorage.getItem('oneData') !== null) {
//     const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
//     const auth = {
//       headers: { Authorization: `Bearer ${oneData.user.access_token}` }
//     }
//     return auth
//   } else {
//     const auth = ''
//     return auth
//   }
// }
// const GetTokenNot = () => {
//   if (localStorage.getItem('oneData') !== null) {
//     const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
//     const auth = {
//       headers: { Authorization: oneData.user.access_token }
//     }
//     return auth
//   } else {
//     const auth = ''
//     return auth
//   }
// }

export default {
  // homepage
  async GetHomepage (val) {
    var response
    var data
    if (val === null) {
      data = {
        role: 'ext_buyer'
      }
    } else {
      data = {
        role: val.role
      }
    }
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/homepage_middle`, data)
      } else if (localStorage.getItem('oneData') !== null) {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/homepage_middle`, data, auth)
      }
      // console.log('response data Homepage ====>', response)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // more_new_products_home
  async GetMoreNewProduct (val) {
    var response
    var data
    if (val === null) {
      data = {
        role: 'ext_buyer'
      }
    } else {
      data = {
        role: val.role
      }
    }
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/more_new_products_home`, data)
      } else if (localStorage.getItem('oneData') !== null) {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/more_new_products_home`, data, auth)
      }
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // more_bs_products_home
  async GetMoreBestSeller (val) {
    var response
    var data
    if (val === null) {
      data = {
        role: 'ext_buyer'
      }
    } else {
      data = {
        role: val.role
      }
    }
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/more_bs_products_home`, data)
      } else if (localStorage.getItem('oneData') !== null) {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/more_bs_products_home`, data, auth)
      }
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Search Text
  async GetSearchResult (val) {
    // console.log('val data role====>', val)
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}products/search`, val)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}products/search`, val, auth)
      }
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Search Text New
  async GetSearchResultNew (val) {
    // console.log('val data role====>', val)
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/v2/search_bar`, val)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/v2/search_bar`, val, auth)
      }
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSearchResultImage (val) {
    // console.log('val data role====>', val)
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/search_bar/image`, val)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/search_bar/image`, val, auth)
      }
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBanner () {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_landing_page`)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCategory (data) {
    var response
    try {
      response = await axios.get(`${process.env.VUE_APP_BACK_END}api/category/get_category/${data}`)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetHomeCoupon (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}platform/coupon/list`, data, auth)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GroupStoreList (val) {
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        const vals = {
          user_id: null
        }
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_group_seller_shop`, vals)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_group_seller_shop`, val, auth)
      }
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailGroupSellerShop (val) {
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_group_seller_shop`, val)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_group_seller_shop`, val, auth)
      }
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteGroupSeller (val) {
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_group_seller_shop`, val)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_group_seller_shop`, val, auth)
      }
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GroupStoreName (val) {
    var response
    try {
      const auth = await GetToken()
      // console.log('Auth', auth)
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_all_seller_shop`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateGroupStoreName (val) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_group_seller_shop`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditGroupStoreName (val) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_group_seller_shop`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateBotChat (val) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_bot_chat`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listBotChatWithUser (val) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_bot_chat_with_user`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChatShop (val) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_user_shop`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async FlashSaleProduct (val) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/get_flash_sale`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async FlashSaleProductSystem (val) {
    var response
    try {
      const auth = await GetToken()
      // response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/get_flash_sale`, val, auth)
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/filter`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetLinkMobile (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}link_mobile/link_in`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetRedirectLinkMobile (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}link/product`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetProductCardLandingPage (data) {
    var response
    try {
      const auth = await GetToken()
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}product_card/landingpage`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendOTPResetPassword (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sendOTPResetPassword`, data, '')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ResetPasswordByOTP (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/resetPasswordByOTP`, data, '')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ResetPasswordByEmail (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/resetPasswordByEmail`, data, '')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetProvinceAll () {
    var response
    try {
      response = await axios.get(`${process.env.VUE_APP_BACK_END}api/all_shop_province`)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // async ProductOTOP (data) {
  //   const auth = await GetToken()
  //   var response
  //   try {
  //     response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/get_list_product_otop`, data, auth)
  //     return response.data
  //   } catch (error) {
  //     return error.response.data
  //   }
  // },
  async GetProduct (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/get_list_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ProductType (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/get_list_product_type`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ProductTypeV2 (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/get_list_product_type_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ProductStatus (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/get_product_status`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailServiceCoupon (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/list`, data, auth)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchTextProduct (val) {
    // console.log('val data role====>', val)
    var response
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/search_bar`, val)
      } else {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/search_bar`, val, auth)
      }
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendOTPRegister (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/send_otp_register`, data, '')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RegisterWebByOTP (data) {
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/register_web_by_otp`, data, '')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ViewDuration (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}product/log/view_duration`, data, auth)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchProductTag (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/get_shop_tags`, data, auth)
      // console.log('result text====>', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDataCoin () {
    const auth = await GetToken()
    var response
    try {
      response = await axios.get(`${process.env.VUE_APP_BACK_END}api/reward-coin`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateLogUser () {
    const auth = await GetToken()
    var response
    try {
      response = await axios.get(`${process.env.VUE_APP_DOMAIN}api/logs/admin/create_logs`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckInCoin (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END}api/reward-coin/daily-check-in`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchBarListAI (data) {
    const auth = await GetToken()
    var response
    try {
      response = await axios.post(`${process.env.VUE_APP_BACK_END2}search/product/searchBar/list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
