<template>
  <v-container style="min-height: 1000px;width: 1100px" :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
        <v-card elevation="0" width="100%" height="100%">
          <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="pt-6" v-if="!MobileSize">ตั้งค่าใบเสนอราคา</v-card-title>
          <v-card-title style="font-weight: 600; font-size: 20px; line-height: 22px; color: #333333;" class="pt-6" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> ตั้งค่าใบเสนอราคา</v-card-title>
        </v-card>
    </v-card>
    <div class="pa-3 py-0" style="display: flex;justify-content:center">
      <!-- min-height: 880px -->
      <!-- height: 1485px -->
      <div class="pa-6" style="background-color: rgb(196, 196, 196);">
        <div class="mt-2" style="background-color: white;width: 100%; margin-left: auto; margin-right: auto;">
          <v-row  class="pa-12 pt-10">
            <v-col cols="2" align-self="center" align="center">
              <v-row justify="center">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NoImageShop.png" width="134" height="134" max-width="134" max-height="134" contain class="" v-if="Detail.shop_logo_image.length === 0 || showError === true"></v-img>
                  <v-img :src="Detail.shop_logo_image[0].path" width="134" height="134" max-width="134" max-height="134" contain class="" v-else></v-img>
                  <v-btn width="125" height="40" text rounded color="#1B5DD6" class="" @click="uploadImageShop()" v-if="Detail.shop_logo_image.length === 0"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                  <v-file-input
                    v-model="DataImageShop"
                    :items="DataImageShop"
                    accept="image/*"
                    @change="UploadImageShop()"
                    @click="event => event.target.value = null"
                    id="imageShop"
                    :clearable="false"
                    style="display:none"
                  />
              </v-row>
              <!-- wait upload -->
              <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-4">
                <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span>
                <v-spacer></v-spacer>
                <span class="textUploadsizeImage pt-1">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span>
              </v-row>
              <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                <v-progress-linear :value="percentUpload" height="7" rounded :active="show"></v-progress-linear>
              </v-row>
              <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-1">
                <span class="textUploadsizeImage">กำลังอัปโหลด...</span>
                <v-spacer></v-spacer>
                <span class="textUploadpercentImage">{{ percentUpload }} %</span>
              </v-row>
              <v-row justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                <v-btn text color="#27AB9C" style="text-decoration-line: underline; font-size: 14px;" @click="cancelImageShop()">ยกเลิก</v-btn>
              </v-row>
              <!-- upload success -->
              <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === false" class="pt-0">
                <v-col cols="12" class="pt-2">
                  <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 20) }}<span v-if="Detail.shop_logo_image[0].name.length > 20">...</span></span><br/>
                  <span class="textUploadsizeImage">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span>
                </v-col>
                <v-col cols="12" class="pt-0">
                  <v-btn width="50" height="40" text color="#27AB9C" @click="uploadImageShop()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                </v-col>
              </v-row>
              <!-- upload fail -->
              <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === true" class="pt-0">
                <v-col cols="12" class="pt-2">
                  <span class="textUploadnameImageFail" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span><br/>
                  <span class="textFailUpload">{{ showErrorText }}</span>
                </v-col>
                <v-col cols="12" class="pt-0">
                  <v-btn width="50" height="40" text color="#636363" @click="cancelImageShop()"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="10" align="start">
              <v-col cols="12" class="pa-1">
                <span style="font-size: 14px; font-weight: 700;">
                  <input type="text" v-model="shopName"  style="border-radius:7px; border:1px solid grey; width: 25%;padding-left: 5px;">
                </span>
              </v-col>
              <v-col cols="12" class="pa-1">
                <span style="font-size: 14px; font-weight: 700;">
                  บ้านเลขที่ <input type="text" v-model="Detail.shop_address[0].house_no"  style="border-radius:7px; border:1px solid grey; width: 5%; text-align: center;">
                  แขวง/ตำบล <input type="text" v-model="Detail.shop_address[0].sub_district"  style="border-radius:7px; border:1px solid grey; width: 11%; text-align: center;">
                  เขต/อำเภอ <input type="text" v-model="Detail.shop_address[0].district"  style="border-radius:7px; border:1px solid grey; width: 11%; text-align: center;">
                  จังหวัด <input type="text" v-model="Detail.shop_address[0].province"  style="border-radius:7px; border:1px solid grey; width: 15%; text-align: center;">
                  รหัสไปรษณีย์ <input type="text" v-model="Detail.shop_address[0].zipcode"  style="border-radius:7px; border:1px solid grey; width: 10%; text-align: center;">
                </span>
              </v-col>
              <v-col cols="12" class="pa-1">
                <span style="font-size: 14px; font-weight: 700;">
                  โทร: <input type="text" v-model="mobileNumber"  style="border-radius:7px; border:1px solid grey; width: 12%; text-align: center;">
                  มือถือ: <input type="text" v-model="mobileNumber"  style="border-radius:7px; border:1px solid grey; width: 12%; text-align: center;">
                  อีเมล: <input type="text" v-model="email"  style="border-radius:7px; border:1px solid grey; width: 25%; text-align: center;">
                </span>
              </v-col>
              <v-col cols="12" class="pa-1">
                <v-row>
                  <v-col cols="6">
                    <span style="font-size: 14px; font-weight: 700;">
                      เลขประจำตัวผู้เสียภาษีอากร: {{ taxNumber }}
                    </span>
                  </v-col>
                  <v-col cols="6" align="end">
                    <span style="font-size: 14px; font-weight: 700;">
                      (

                      <input type="text" v-model="branch"  style="border-radius:7px; border:1px solid grey; width: 25%;text-align: center;">
                      )
                    </span>
                  </v-col>
                </v-row>

              </v-col>

            </v-col>
            <v-col class="mt-2" cols="12" align="center">
              <span style="font-size: 14px; font-weight: 700;">
                ใบเสนอราคา / Quotation
              </span>
            </v-col>
            <v-col class="mt-2" cols="12" style>
              <v-row>
                  <v-col cols="" style="border: 1px solid black;border-collapse: collapse;;border-right:0px">
                    <span style="font-size: 12px; font-weight: 700;">
                      ชื่อลูกค้า
                    </span>
                    <span style="font-size: 12px; font-weight: 400;">
                      ตัวอย่างชื่อ สกุล
                    </span>
                    <br>
                    <span style="font-size: 12px; font-weight: 700;">
                      ที่อยู่
                    </span>
                    <span style="font-size: 12px; font-weight: 400;">
                      บ้านเลขที่ - ห้องเลขที่ - ชั้นที่ - อาคาร - หมู่บ้าน -
                      หมู่ที่ - ตรอก/ซอย - แยก - ถนน - แขวง/ตำบล
                      - เขต/อำเภอ - จังหวัด -
                      รหัสไปรษณีย์ - เบอร์มือถือ- เบอร์โทรศัพท์ -
                    </span>
                  </v-col>
                  <v-col cols="2" style="border: 1px solid black;border-collapse: collapse;;border-right:0px">

                  </v-col>
                  <v-col cols="5" style="border: 1px solid black;border-collapse: collapse;">
                    <v-row class="">
                      <v-col cols="3" class="pr-0 pb-0">
                        <span style="font-size: 12px; font-weight: 700;">
                          เลขที่ใบเสนอราคา
                        </span>
                      </v-col>
                      <v-col cols="2" class="pl-1 pr-0 pb-0">
                        <!-- 27AB9C -->
                        <input type="text" :value="shortName"  style="border-radius:7px; border:1px solid grey; width: 100%; text-align: center;height: 70%;">
                      </v-col>
                      <v-col cols="4" class="pl-1 pb-0">
                        -20240100001
                      </v-col>
                    </v-row>
                    <v-row class="">
                      <v-col cols="12" class="pt-0 pb-0">
                        <span style="font-size: 12px; font-weight: 700;">
                          วันที่
                        </span>
                        <span style="font-size: 12px; font-weight: 400;">
                        16 มกราคม 2567
                      </span>
                      </v-col>
                    </v-row>
                    <v-row class="">
                      <v-col cols="12" class="pt-0 pb-0">
                        <span style="font-size: 12px; font-weight: 700;">
                          เงื่อนไขการชำระเงิน
                        </span>
                      </v-col>
                    </v-row>
                    <v-row class="">
                      <v-col cols="12" class="pt-0 pb-0">
                        <span style="font-size: 12px; font-weight: 700;">
                          เครดิต
                        </span>
                        <span style="font-size: 12px; font-weight: 400;">
                          30 วัน
                        </span>
                      </v-col>
                    </v-row>
                    <v-row class="">
                      <v-col cols="12" class="pt-0">
                        <span style="font-size: 12px; font-weight: 700;">
                          จำนวนงวด
                        </span>
                        <span style="font-size: 12px; font-weight: 400;">
                          1 งวด
                        </span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
            </v-col>

            <v-col class="" style="margin-top: -1px;" cols="12">
              <v-row>
                  <v-col cols="1" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      ลำดับ
                    </span>
                  </v-col>
                  <v-col cols="6" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      รายละเอียด
                    </span>
                  </v-col>
                  <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      จำนวน
                    </span>
                  </v-col>
                  <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      หน่วย
                    </span>
                  </v-col>
                  <v-col cols="2" align="center" style="border: 1px solid black;border-collapse: collapse;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      ราคาต่อหน่วย
                    </span>
                  </v-col>
                </v-row>
            </v-col>

            <v-col class="" style="margin-top: -1px;" cols="12">
              <v-row>
                  <v-col cols="1" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      1
                    </span>
                  </v-col>
                  <v-col cols="6" align="start" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      เสื้อยืด
                    </span>
                  </v-col>
                  <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      1
                    </span>
                  </v-col>
                  <v-col cols="" align="center" style="border: 1px solid black;border-collapse: collapse;border-right:0px;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      ชิ้น
                    </span>
                  </v-col>
                  <v-col cols="2" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px">
                    <span style="font-size: 12px; font-weight: 400;">
                      100.00
                    </span>
                  </v-col>
                </v-row>
            </v-col>

            <v-col class="" style="margin-top: -1px;" cols="12">
              <!-- border-left: 1px solid black; -->
              <v-row style="" fill-height>
                  <v-col cols="7" align="center" class="" align-self="end"  style="border-bottom: 1px solid black;border-collapse: collapse;min-height:214px;border-left: 1px solid black;display:flex;align-items: end; justify-content: space-around;">
                      <span style="font-size: 12px; font-weight: 700;">
                        ตัวอักษร ( หนึ่งร้อยเจ็ดบาทถ้วน )
                      </span>
                  </v-col>
                  <v-col cols="3" align="center" class="pa-0" style="border-bottom: 1px solid black;border-collapse: collapse;border-bottom: 0px;">
                    <span style="font-size: 12px; font-weight: 400;">
                      <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;">
                        <span style="font-size: 12px; font-weight: 700;">
                          รวมเงิน
                        </span>
                      </v-col>
                      <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;">
                        <span style="font-size: 12px; font-weight: 700;">
                          ส่วนลด
                        </span>
                      </v-col>
                      <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;">
                        <span style="font-size: 12px; font-weight: 700;">
                          ภาษีมูลค่าเพิ่ม 7%
                        </span>
                      </v-col>
                      <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;">
                        <span style="font-size: 12px; font-weight: 700;">
                          ค่าจัดส่ง
                        </span>
                      </v-col>
                      <v-col cols="12" align="start" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;border-right:0px;">
                        <span style="font-size: 12px; font-weight: 700;">
                          ราคารวมทั้งสิ้น
                        </span>
                      </v-col>
                    </span>
                  </v-col>
                  <v-col cols="2" align="center" class="pa-0" style="border-bottom: 1px solid black;border-collapse: collapse;border-top: 0px;border-bottom: 0px;">
                    <span style="font-size: 12px; font-weight: 400;">
                      <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;">
                        <span style="font-size: 12px; font-weight: 400;">
                          100.00
                        </span>
                      </v-col>
                      <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;">
                        <span style="font-size: 12px; font-weight: 400;">
                          0.00
                        </span>
                      </v-col>
                      <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;">
                        <span style="font-size: 12px; font-weight: 400;">
                          7.00
                        </span>
                      </v-col>
                      <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;">
                        <span style="font-size: 12px; font-weight: 400;">
                          0.00
                        </span>
                      </v-col>
                      <v-col cols="12" align="end" style="border: 1px solid black;border-collapse: collapse;border-top: 0px;">
                        <span style="font-size: 12px; font-weight: 400;">
                          107.00
                        </span>
                      </v-col>
                    </span>
                  </v-col>
                </v-row>
            </v-col>

            <v-col class="mt-3" style="" cols="12">
              <span style="font-size: 12px; font-weight: 400;">
                หมายเหตุ
              </span>
            </v-col>
            <v-col class="mt-1" style="" cols="12">
              <span style="font-size: 12px; font-weight: 400;">
                ขอบพระคุณที่ท่านให้ความสนใจบริการของเรา
              </span>
            </v-col>

            <v-col class="mt-12" cols="12">
              <v-row>
                  <v-col cols="4" align="center" style="">
                    <span style="font-size: 12px; font-weight: 400;">
                      สั่งซื้อโดย / Order By
                    </span>
                    <br>
                    <div class="mt-12" style="border-bottom:1px solid black ;width: 70%;">
                    </div>

                    <v-row class="mt-5" style="width: 70%;">
                      <span class="mr-auto">
                        (
                      </span>
                      <input disabled type="text" v-model="fullname"  style="border-radius:7px; border:1px solid grey; width: 90%; text-align: center;">
                      <span class="ml-auto">
                        )
                      </span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" align="center" style="">
                    <span style="font-size: 12px; font-weight: 400;">
                      ออกโดย / Prepared By
                    </span>
                    <br>
                    <div class="mt-12" style="border-bottom:1px solid black ;width: 70%;">
                    </div>

                    <v-row class="mt-5" style="width: 70%;">
                      <span class="mr-auto">
                        (
                      </span>
                      <input type="text" v-model="prepare_by"  style="border-radius:7px; border:1px solid grey; width: 90%; text-align: center;">
                      <span class="ml-auto">
                        )
                      </span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" align="center" style="">
                    <span style="font-size: 12px; font-weight: 400;">
                      ผู้มีอำนาจอนุมัติ / Authorized Signatory
                    </span>
                    <br>
                    <div class="mt-12" style="border-bottom:1px solid black ;width: 70%;">
                    </div>

                    <v-row class="mt-5" style="width: 70%;">
                      <span class="mr-auto">
                        (
                      </span>
                      <input type="text" v-model="authorize_by" style="border-radius:7px; border:1px solid grey; width: 90%; text-align: center;">
                      <span class="ml-auto">
                        )
                      </span>
                    </v-row>
                  </v-col>
                </v-row>
            </v-col>
            <!-- <v-img :src="Detail.shop_logo_image[0].path" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-else></v-img> -->
          </v-row>

        </div>
        <div>
          <v-row class="mt-4">
            <v-col cols="12" align="end">
              <v-btn class="d-inline rounded-e-xl mr-auto" style="border-radius: 7px 7px 7px 7px !important;" width="116" height="39" dark elevation="0" @click="confirm()" color="#27AB9C">
                ยืนยัน
              </v-btn>
            </v-col>
          </v-row>

        </div>
      </div>
    </div>
  </v-container>

</template>

<script>
import { Decode } from '@/services'

export default {
  data () {
    return {
      test: 0,
      shopName: '',
      shortName: '',
      percentUpload: 0,
      taxNumber: '',
      prepare_by: '',
      authorize_by: '',
      branch: '',
      fullname: '',
      Detail: {
        seller_shop_id: '',
        shop_logo_image: [],
        image_banner: [],
        image_news: [],
        shop_image: [],
        shop_image_banner: [],
        shop_advert: [],
        shop_name: '',
        shop_url: '',
        tax_id: '',
        line_id: '',
        shop_description: '',
        payment_method: [],
        shipping_method: [],
        installment_method: [],
        shop_status: '',
        public_show: '',
        partner_show: '',
        have_partner: '',
        facebook_url: '',
        shop_type: '',
        merchant_key: '',
        first_name: '',
        last_name: '',
        team_email: '',
        shop_address: [
          {
            id: '',
            default_address: '',
            house_no: '',
            detail: '',
            province: '',
            district: '',
            sub_district: '',
            zipcode: ''
          }
        ],
        shop_email: [],
        payment_costs: [],
        shop_phone: [
          { phone: '' },
          { phone: '' }
        ]
        // shop_shipping_type: []
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    // MobileSize (val) {
    //   if (val === true) {
    //     this.$router.push({ path: '/partnerSellerMobile' }).catch(() => {})
    //   } else {
    //     this.$router.push({ path: '/partnerSeller' }).catch(() => {})
    //   }
    // }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log(this.onedata)
    this.shopID = localStorage.getItem('shopSellerID').toString()
    // this.shopID = parseInt(this.$route.query.shopID)
    this.GetDetailShop()
    this.GetUserDetail()
  },
  methods: {
    confirm () {
      // console.log(1)
      // console.log(this.DataImageShop)
      // console.log(this.Detail.shop_logo_image[0].path)
      // console.log(this.shortName)
      var data = {
        seller_shop_id: this.shopID,
        shop_logo_image: this.DataImageShop !== undefined ? this.DataImageShop : this.Detail.shop_logo_image[0].path,
        short_name: this.shortName,
        prepare_by: this.prepare_by,
        authorize_by: this.authorize_by
      }
      console.log(data)
    },
    async GetUserDetail () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('actionsUserDetailPage', data)
      const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
      if (userdetail.message !== 'This user is unauthorized.') {
        this.userdetail = userdetail.data[0]
        if (this.userdetail.first_name_th === '' && this.userdetail.last_name_th === '') {
          this.fullname = '-'
        } else {
          this.fullname = this.userdetail.first_name_th + ' ' + this.userdetail.last_name_th
        }
      }
    },
    async GetDetailShop () {
      this.$store.commit('openLoader')
      // console.log(this.shopID)
      if (this.shopID === null) {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/' }).catch(() => {})
      } else {
        var data = {
          seller_shop_id: this.shopID,
          role: 'seller'
        }
        await this.$store.dispatch('actionDetailShop', data)
        var response = await this.$store.state.ModuleShop.stateDatailShop
        if (response.result === 'SUCCESS') {
          this.isJVShop = response.data[0].is_JV === 'yes'
          this.Detail.seller_shop_id = this.shopID
          if (response.data[0].shop_image.length !== 0) {
            this.Detail.shop_logo_image.push({
              image_data: response.data[0].shop_image[0].media_path,
              image_data_lazy: response.data[0].shop_image[0].media_path_lazy,
              path: response.data[0].shop_image[0].media_path,
              name: response.data[0].shop_image[0].name,
              size: ''
            })
            this.show = false
            this.showError = false
          } else {
            this.show = false
            this.showError = false
            this.Detail.shop_logo_image = []
          }
          // console.log(this.Detail.shop_logo_image)
          // console.log(response.data[0])
          this.shopName = response.data[0].shop_name
          this.taxNumber = response.data[0].tax_id
          this.branch = response.data[0].branch !== undefined ? response.data[0].branch : 'สำนักงานใหญ่'
          this.shopURL = response.data[0].url_name
          this.descriptionShop = response.data[0].shop_description
          this.shortName = response.data[0].short_name
          this.mobile = response.data[0].shop_phone.length > 0 ? response.data[0].shop_phone[0].phone : ''
          this.facebook = response.data[0].facebook_url
          if (response.data[0].shipping_method.length !== 0) {
            this.switchShipping = true
            this.SelectShipping = response.data[0].shipping_method
          } else {
            this.switchShipping = false
            this.SelectShipping = []
          }
          if (response.data[0].payment_method !== null) {
            if (response.data[0].payment_method.length !== 0) {
              this.SelectPaymentType = response.data[0].payment_method
            } else {
              this.SelectPaymentType = []
            }
          } else {
            this.SelectPaymentType = []
          }
          this.selectInstallmentType = response.data[0].installment_method
          this.line = response.data[0].line_id
          if (response.data[0].shop_status === 'active') {
            this.openshop = true
          } else {
            this.openshop = false
          }
          if (response.data[0].shop_status === 'active') {
            this.publicshop = true
          } else {
            this.publicshop = false
          }
          if (response.data[0].partner_show === 'yes') {
            this.partner = true
          } else {
            this.partner = false
          }
          this.MerchantKey = response.data[0].merchant_key
          if (response.data[0].payment_costs !== null) {
            if (response.data[0].payment_costs.length !== 0) {
              this.SelectType = 'contact'
              this.SelectTypePay = response.data[0].payment_costs
            } else {
              this.SelectType = 'no_contact'
              this.SelectTypePay = []
            }
          } else {
            this.SelectType = 'no_contact'
            this.SelectTypePay = []
          }
          this.name = response.data[0].first_name
          this.surname = response.data[0].last_name
          this.email = response.data[0].shop_email.length !== 0 ? response.data[0].shop_email[0].seller_email : ''
          this.mobileNumber = response.data[0].shop_phone.length > 1 ? response.data[0].shop_phone[1].phone : ''
          this.Detail.shop_address[0].id = response.data[0].address_detail[0].id
          this.addressDetail = response.data[0].address_detail[0].detail
          this.Detail.shop_address = response.data[0].address_detail
          this.houseNo = response.data[0].address_detail[0].house_no
          if (this.isJVShop) {
            this.teamEmail = response.data[0].team_email
          } else {
            this.teamEmail = ''
          }
          this.subdistrict = response.data[0].address_detail[0].sub_district
          this.district = response.data[0].address_detail[0].district
          this.province = response.data[0].address_detail[0].province
          this.details = response.data[0].address_detail[0].details
          this.zipcode = response.data[0].address_detail[0].zipcode
          this.default_address = response.data[0].address_detail[0].default_address === 'main'
          if (response.data[0].image_news.length !== 0) {
            for (let j = 0; j < response.data[0].image_news.length; j++) {
              this.Detail.shop_advert.push({
                image_data: response.data[0].image_news[j].path,
                image_data_lazy: response.data[0].image_news[j].path_lazy,
                path: response.data[0].image_news[j].path,
                // name: response.data[0].shop_profile[0].name,
                name: response.data[0].image_news[j].name,
                size: '',
                statusFail: false
              })
              this.DataToShowImageAdvert.push({
                image_data: response.data[0].image_news[j].path,
                image_data_lazy: response.data[0].image_news[j].path_lazy,
                path: response.data[0].image_news[j].path,
                // name: response.data[0].shop_profile[0].name,
                name: response.data[0].image_news[j].name,
                size: '',
                statusFail: false
              })
            }
          } else {
            this.Detail.shop_advert = []
            this.DataToShowImageAdvert = []
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
        }
        // console.log(this.Detail)
      }
    },
    uploadImageShop () {
      document.getElementById('imageShop').click()
    },
    UploadImageShop () {
      this.show = true
      this.showError = false
      this.showErrorText = ''
      this.percentUpload = 0
      var data = {}
      const element = this.DataImageShop
      const imageSize = element.size / 1024 / 1024
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
        this.Detail.shop_logo_image = []
        if (imageSize < 1) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = async () => {
            var resultReader = reader.result
            var url = URL.createObjectURL(element)
            data = {
              image: [resultReader.split(',')[1]],
              type: 'shop',
              seller_shop_id: this.shopID
            }
            await this.$store.dispatch('actionsUploadToS3', data)
            var response = await this.$store.state.ModuleShop.stateUploadToS3
            if (response.message === 'List Success.') {
              this.Detail.shop_logo_image.push({
                image_data: response.data.list_path[0].path,
                image_data_lazy: response.data.list_path[0].path_lazy,
                path: url,
                name: this.DataImageShop.name,
                size: this.DataImageShop.size
              })
              setInterval(() => {
                if (this.percentUpload === 75) {
                  this.show = false
                }
                this.percentUpload += 25
              }, 100)
            }
          }
        } else {
          this.show = false
          this.showError = true
          this.showErrorText = 'ไฟล์มีขนาดใหญ่เกินไป'
          this.Detail.shop_logo_image.push({
            name: this.DataImageShop.name
          })
          // this.$swal.fire({
          //   icon: 'warning',
          //   text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 1 MB',
          //   showConfirmButton: false,
          //   timer: 1500
          // })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    cancelImageShop () {
      this.show = false
      this.percentUpload = 0
      this.DataImageShop = []
      this.Detail.shop_logo_image = []
    }
  }
}
</script>

<style scoped>
input:focus {
 outline: 1px solid #27AB9C;
}

input:disabled {
 background-color: rgb(220, 220, 220);
 pointer-events:none;
}

input:hover {
 outline: 1px solid #27AB9C;
}
</style>
