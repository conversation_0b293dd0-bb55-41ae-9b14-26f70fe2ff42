import AxiosApprove from './axios_approve'

const Mo<PERSON>leApprove = {
  state: {
    // approve_position
    stateListApprovePosition: [],
    stateListUserForSelect: [],
    stateCreateApprovePosition: [],
    stateDetailPosition: [],
    stateEditApprovePosition: [],
    stateDeleteApprovePosition: [],

    // approve_purchaser
    stateListApprovePurchaser: [],
    stateListPurchaserForSelect: [],
    stateCreateApprovePurchaser: [],

    // approver_list
    stateApproverInApprovePosition: [],
    stateListSelectApprover: [],
    stateAddApproverInApprovePosition: [],
    stateEditUserApproverInApprovePosition: [],
    stateDeleteApproverInApprovePosition: [],
    // ฝั่งผู้ซื้อ
    stateListPurchaserHaveApprovePosition: [],
    stateListSelectPurchaser: [],
    stateSettingPurchaserApprovePosition: [],
    stateDeletePurchaserApprovePosition: [],
    stateListApproveCompany: [],
    stateDetailApproveCompany: [],
    stateApproveOrderCompany: [],
    stateListApprovePartnerShop: [],
    stateDetailApprovePartnerShop: [],
    stateApproveOrderPartnerShop: []
  },
  mutations: {
    // approve_position
    mutationsListApprovePosition (state, data) {
      state.stateListApprovePosition = data
    },
    mutationsListUserForSelect (state, data) {
      state.stateListUserForSelect = data
    },
    mutationsCreateApprovePosition (state, data) {
      state.stateCreateApprovePosition = data
    },
    mutationsDetailPosition (state, data) {
      state.stateDetailPosition = data
    },
    mutationsEditApprovePosition (state, data) {
      state.stateEditApprovePosition = data
    },
    mutationsDeleteApprovePosition (state, data) {
      state.stateDeleteApprovePosition = data
    },

    // approve_purchaser
    mutationsListApprovePurchaser (state, data) {
      state.stateListApprovePurchaser = data
    },
    mutationsListPurchaserForSelect (state, data) {
      state.stateListPurchaserForSelect = data
    },
    mutationsCreateApprovePurchaser (state, data) {
      state.stateCreateApprovePurchaser = data
    },

    // approver_list
    mutationsListApproverInApprovePosition (state, data) {
      state.stateApproverInApprovePosition = data
    },
    mutationsListSelectApprover (state, data) {
      state.stateListSelectApprover = data
    },
    mutationsAddApproverInApprovePosition (state, data) {
      state.stateAddApproverInApprovePosition = data
    },
    mutationsEditUserApproverInApprovePosition (state, data) {
      state.stateEditUserApproverInApprovePosition = data
    },
    mutationsDeleteApproverInApprovePosition (state, data) {
      state.stateDeleteApproverInApprovePosition = data
    },
    // ฝั่งผู้ซื้อ
    mutationsListPurchaser (state, data) {
      state.stateListPurchaserHaveApprovePosition = data
    },
    mutationsListSelectPurchaser (state, data) {
      state.stateListSelectPurchaser = data
    },
    mutationsSettingPurchaser (state, data) {
      state.stateSettingPurchaserApprovePosition = data
    },
    mutationsDeletePurchaser (state, data) {
      state.stateDeletePurchaserApprovePosition = data
    },
    mutationsListApproveCompany (state, data) {
      state.stateListApproveCompany = data
    },
    mutationsDetailApproveCompany (state, data) {
      state.stateDetailApproveCompany = data
    },
    mutationsApproveOrderCompany (state, data) {
      state.stateApproveOrderCompany = data
    },
    mutationsListApprovePartnerShop (state, data) {
      state.stateListApprovePartnerShop = data
    },
    mutationsDetailApprovePartnerShop (state, data) {
      state.stateDetailApprovePartnerShop = data
    },
    mutationsApproveOrderPartnerShop (state, data) {
      state.stateApproveOrderPartnerShop = data
    }
  },
  actions: {
    // approve_position
    async actionsListApprovePosition (context, access) {
      const response = await AxiosApprove.listApprovePosition(access)
      await context.commit('mutationsListApprovePosition', response)
    },
    async actionsListUserForSelectComp (context, access) {
      const response = await AxiosApprove.listUserForSelect(access)
      await context.commit('mutationsListUserForSelect', response)
    },
    async actionsCreateApprovePosition (context, access) {
      const response = await AxiosApprove.createApprovePosition(access)
      await context.commit('mutationsCreateApprovePosition', response)
    },
    async actionsDetailPosition (context, access) {
      const response = await AxiosApprove.detailPosition(access)
      await context.commit('mutationsDetailPosition', response)
    },
    async actionsEditApprovePosition (context, access) {
      const response = await AxiosApprove.editApprovePosition(access)
      await context.commit('mutationsEditApprovePosition', response)
    },
    async actionsDeleteApprovePosition (context, access) {
      const response = await AxiosApprove.deleteApprovePosition(access)
      await context.commit('mutationsDeleteApprovePosition', response)
    },

    // approve_purchaser
    async actionsListApprovePurchaser (context, access) {
      const response = await AxiosApprove.listApprovePurchaser(access)
      await context.commit('mutationsListApprovePurchaser', response)
    },
    async actionsListPurchaserForSelect (context, access) {
      const response = await AxiosApprove.listPurchaserForSelect(access)
      await context.commit('mutationsListPurchaserForSelect', response)
    },
    async actionsCreateApprovePurchaser (context, access) {
      const response = await AxiosApprove.createApprovePurchaser(access)
      await context.commit('mutationsCreateApprovePurchaser', response)
    },

    // approver_list
    async actionsListApproverInApprovePosition (context, access) {
      const response = await AxiosApprove.listApproverInApprovePosition(access)
      await context.commit('mutationsListApproverInApprovePosition', response)
    },
    async actionsListSelectApprover (context, access) {
      const response = await AxiosApprove.listSelectApprover(access)
      await context.commit('mutationsListSelectApprover', response)
    },
    async actionsAddApproverInApprovePosition (context, access) {
      const response = await AxiosApprove.addApproverInApprovePosition(access)
      await context.commit('mutationsAddApproverInApprovePosition', response)
    },
    async actionsEditUserApproverInApprovePosition (context, access) {
      const response = await AxiosApprove.editUserApproverInApprovePosition(access)
      await context.commit('mutationsEditUserApproverInApprovePosition', response)
    },
    async actionsDeleteApproverInApprovePosition (context, access) {
      const response = await AxiosApprove.deleteApproverInApprovePosition(access)
      await context.commit('mutationsDeleteApproverInApprovePosition', response)
    },
    // ฝั่งผู้ซื้อ
    async actionsListPurchaser (context, access) {
      const response = await AxiosApprove.ListPurchaser(access)
      await context.commit('mutationsListPurchaser', response)
    },
    async actionsListSelectPurchaser (context, access) {
      const response = await AxiosApprove.ListSelectPurchaser(access)
      await context.commit('mutationsListSelectPurchaser', response)
    },
    async actionsSettingPurchaser (context, access) {
      const response = await AxiosApprove.SettingPurchaser(access)
      await context.commit('mutationsSettingPurchaser', response)
    },
    async actionsDeletePurchaser (context, access) {
      const response = await AxiosApprove.DeletePurchaser(access)
      await context.commit('mutationsDeletePurchaser', response)
    },
    async actionsListApproveCompany (context, access) {
      const response = await AxiosApprove.ListApproveCompany(access)
      await context.commit('mutationsListApproveCompany', response)
    },
    async actionsDetailApproveCompany (context, access) {
      const response = await AxiosApprove.DetailApproveCompany(access)
      await context.commit('mutationsDetailApproveCompany', response)
    },
    async actionsApproveOrderCompany (context, access) {
      const response = await AxiosApprove.ApproveOrderCompany(access)
      await context.commit('mutationsApproveOrderCompany', response)
    },
    async actionsListApprovePartnerShop (context, access) {
      const response = await AxiosApprove.ListApprovePartnerShop(access)
      await context.commit('mutationsListApprovePartnerShop', response)
    },
    async actionsDetailApprovePartnerShop (context, access) {
      const response = await AxiosApprove.DetailApprovePartnerShop(access)
      await context.commit('mutationsDetailApprovePartnerShop', response)
    },
    async actionsApproveOrderPartnerShop (context, access) {
      const response = await AxiosApprove.ApproveOrderPartnerShop(access)
      await context.commit('mutationsApproveOrderPartnerShop', response)
    }
  }
}

export default ModuleApprove
