<template>
  <v-container class="pa-2 my-2">
    <v-card v-if="!MobileSize && !IpadSize" width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-2 my-2 py-4 px-6">
      <v-row>
        <v-col cols="12" v-if="itemDetailArticle.header_image_video" class="pt-2 px-2 mt-2" style="min-height: 300px; display: flex; justify-content: center !important; align-items: center; width: 100%;" id="image">
            <div v-if="itemDetailArticle.header_image_video.includes('youtube')" style="width: 80%;" class="my-4 mx-2 mt-2">
              <Youtube
                ref="youtube"
                :video-id="youtube_parser(itemDetailArticle.header_image_video)"
                @playing="playing"
                :player-vars="playerVars"
                style="width:100%; height:420px; border-radius:20px;"
              />
            </div>
            <div v-else>
              <v-img :src="itemDetailArticle.header_image_video" cover height="250" style="border-radius:20px; width: 100%; height: 420px;" class="mx-2 my-4" contain></v-img>
            </div>
        </v-col>
        <v-col cols="9">
          <v-row>
            <v-card-text style="color: #27ab9c; font-weight: 600; font-size: x-large; line-height: 1.5;">{{ itemDetailArticle.title }}</v-card-text>
          </v-row>
        </v-col>
        <v-col cols="3">
          <v-row style="display: flex; flex-direction: column; align-items: flex-end;">
            <v-card-text style="color: #bbb; font-size: medium; text-align: right;"><v-icon color="#bbb">mdi-calendar-range-outline</v-icon>{{ calDiffDate(itemDetailArticle.created_at) }}</v-card-text>
            <v-card-text style="color: #bbb; font-size: medium; text-align: right;"><v-icon color="#bbb">mdi-eye</v-icon> {{ itemDetailArticle.view }}</v-card-text>
          </v-row>
        </v-col>
          <v-card-text style="font-weight: 500; font-size: medium; color: #8c8c8c;">{{ itemDetailArticle.shot_description }}</v-card-text>
        <v-col cols="12" v-if="itemDetailArticle.banner_image || itemDetailArticle.banner_image !== null" class="pt-2 px-2 mt-2" style="min-height: 300px; display: flex; justify-content: center !important; align-items: center; width: 100%;">
          <v-img :src="itemDetailArticle.banner_image" cover height="250" style="border-radius:20px; width: 100%; height: 420px;" class="mx-2 my-4" contain></v-img>
        </v-col>
        <v-col cols="12">
            <v-card-text style="font-size:15px;" v-html='itemDetailArticle.description'></v-card-text>
        </v-col>
    </v-row>
    </v-card>
    <v-card v-else width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="my-2 py-4 px-6">
    <v-row>
      <v-icon color="#27AB9C" v-if="MobileSize" class="mr-2" @click="backToShop()">mdi-chevron-left</v-icon>
    </v-row>
      <v-row>
        <v-col cols="12" v-if="itemDetailArticle.header_image_video" class="pt-2 px-2 mt-2" style="min-height: 200px; display: flex; justify-content: center !important; align-items: center; width: 100%;" id="image">
            <div v-if="itemDetailArticle.header_image_video.includes('youtube')" style="width: 100%;" class="my-4 mt-2">
              <Youtube
                ref="youtube"
                :video-id="youtube_parser(itemDetailArticle.header_image_video)"
                @playing="playing"
                :player-vars="playerVars"
                style="width:100%; height:200px; border-radius:20px;"
              />
            </div>
            <div v-else>
              <v-img :src="itemDetailArticle.header_image_video" cover height="250" style="border-radius:20px; width: 100%; height: 200px;" class="my-4" contain></v-img>
            </div>
        </v-col>
        <v-col cols="8" v-if="IpadSize">
          <v-row>
            <v-card-text style="color: #27ab9c; font-weight: 600; font-size: x-large; line-height: 1.3;">{{ itemDetailArticle.title }}</v-card-text>
          </v-row>
        </v-col>
        <v-col cols="12" v-else-if="MobileSize">
          <v-row>
            <v-card-text style="color: #27ab9c; font-weight: 600; font-size: large; line-height: 1.3;">{{ itemDetailArticle.title }}</v-card-text>
          </v-row>
        </v-col>
        <v-col cols="4" v-if="IpadSize">
          <v-row style="display: flex; flex-direction: column; align-items: flex-end;">
            <v-card-text style="color: #bbb; font-size: medium; text-align: right;"><v-icon color="#bbb">mdi-calendar-range-outline</v-icon>{{ calDiffDate(itemDetailArticle.created_at) }}</v-card-text>
            <v-card-text style="color: #bbb; font-size: medium; text-align: right;"><v-icon color="#bbb">mdi-eye</v-icon> {{ itemDetailArticle.view }}</v-card-text>
          </v-row>
        </v-col>
        <v-col cols="12" v-else-if="MobileSize">
          <v-row>
            <v-card-text style="color: #bbb; font-size: small; display: flex; justify-content: space-between;">
              <span><v-icon small color="#bbb">mdi-calendar-range-outline</v-icon>{{ calDiffDate(itemDetailArticle.created_at) }}</span>
              <span><v-icon small color="#bbb">mdi-eye</v-icon> {{ itemDetailArticle.view }}</span>
            </v-card-text>
          </v-row>
        </v-col>
          <v-card-text v-if="IpadSize" style="font-weight: 500; color: #8c8c8c; font-size: medium; margin-top: -20px;">{{ itemDetailArticle.shot_description }}</v-card-text>
          <v-card-text v-else-if="MobileSize" style="font-weight: 500; color: #8c8c8c; font-size: small; margin-top: -20px;">{{ itemDetailArticle.shot_description }}</v-card-text>
        <v-col cols="12" v-if="itemDetailArticle.banner_image || itemDetailArticle.banner_image !== null" class="pt-2 px-2 mt-2" style="min-height: 200px; display: flex; justify-content: center !important; align-items: center; width: 100%;">
          <v-img :src="itemDetailArticle.banner_image" cover height="250" style="border-radius:20px; width: 100%; height: 200px;" class="my-4" contain></v-img>
        </v-col>
        <v-col cols="12">
          <v-row style="display: flex; flex-direction: column; align-items: flex-end;" class=" my-2">
            <v-card-text v-if="!MobileSize && !IpadSize" style="font-size:15px;" v-html='itemDetailArticle.description'></v-card-text>
            <v-card-text v-else style="font-size:13px;" v-html='itemDetailArticle.description'></v-card-text>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card v-if="disabledArticle === true" width="100%" height="100%" style="background: #FFFFFF;" elevation="0" :class="MobileSize || IpadSize? 'my-3 py-4 px-2': 'mx-2 my-3 py-4 px-2'">
      <div :class="IpadSize || IpadProSize? 'pt-5': MobileSize? 'pt-2': 'pt-5 pr-3'" style="font-size: 18px;">
        <v-col cols="12" md="12">
          <ListShopProduct
            :propsData="itemListDetailArticle"
            typeProduct="article"
            header="สินค้าในบทความ"
          />
        </v-col>
      </div>
    </v-card>
  </v-container>
</template>

<script>
import { Youtube } from 'vue-youtube'
export default {
  components: {
    Youtube,
    ListShopProduct: () => import('../Shop/ListShopProduct')
  },
  data () {
    return {
      itemDetailArticle: '',
      itemListDetailArticle: [],
      disabledArticle: true,
      playerVars: {
        autoplay: 0,
        controls: 1
      },
      options: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          windowed: 1300,
          padding: 0
        },
        item: {
          class: '',
          padding: 0
        }
      },
      loading: true
    }
  },
  created () {
    this.getDataDetailArticle()
    this.getProductAritcle('action')
    this.$EventBus.$on('getProductAritcle', this.getProductAritcle)
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  // mounted () {
  //   window.scrollTo(0, 0)
  // },
  methods: {
    backToShop () {
      var shopPathName = encodeURIComponent(this.$route.query.Name.replace(/\s/g, '-'))
      this.$router.push(`/shoppage/${shopPathName}-${this.$route.query.ID}?page=1`).catch(() => {})
    },
    async getDataDetailArticle () {
      this.$store.commit('openLoader')
      const articleId = this.$route.query.Article
      const payload = {
        article_id: articleId
      }
      // console.log('data item', item)
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END}api/detail_article`,
        method: 'POST',
        data: payload
      }).then((response) => {
        // console.log('response', response)
        // console.log('response', response.data.data)
        this.itemDetailArticle = response.data.data
        this.$store.commit('closeLoader')
      })
    },
    youtube_parser (url) {
      let ID = ''
      url = url.replace(/(>|<)/gi, '').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
      if (url[2] !== undefined) {
        ID = url[2].split(/[^0-9a-z_-]/i)
        ID = ID[0]
      } else {
        ID = false
      }
      return ID
    },
    playing () {
      // console.log('we are watching!!!')
    },
    calDiffDate (dateA) {
      const months = [
        'January', 'February', 'March', 'April', 'May', 'June',
        'July', 'August', 'September', 'October', 'November', 'December'
      ]
      const date1 = new Date(dateA)
      const day = date1.getDate()
      const month = months[date1.getMonth()]
      const year = date1.getFullYear()
      return ` ${day} ${month} ${year}`
    },
    async getProductAritcle (action) {
      if (action === 'action') {
        this.$store.commit('openLoader')
      }
      const articleId = this.$route.query.Article
      const sellerId = this.$route.query.ID
      const payload = {
        article_id: articleId,
        seller_shop_id: sellerId
      }
      await this.$store.dispatch('actionsListProductAritcle', payload)
      var response = await this.$store.state.ModuleArticle.stateListProductAritcle
      if (response.result === 'SUCCESS') {
        // console.log('response', response)
        // console.log('response itemListDetailArticle', response.data.data)
        this.itemListDetailArticle = response.data
        if (response.data.length === 0) {
          this.disabledArticle = false
        } else {
          this.disabledArticle = true
        }
        if (action === 'action') {
          this.$store.commit('closeLoader')
        }
      }
    }
  }
}
</script>

<style scoped>

</style>
