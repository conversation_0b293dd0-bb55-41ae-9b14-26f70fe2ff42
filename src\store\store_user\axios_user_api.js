import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  // Shop Userdetail Page
  async GetUserDetailPage (data) {
    const auth = await GetToken()
    // console.log('GetUserDetailPage', data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Bind Account and Auto Add OneChat Bot + Create Shop
  async SetBindAccount (data) {
    const auth = await GetToken()
    // console.log('SetBindAccount', data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/bind_account_with_one_id`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangePasswordMarket (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/reset_password`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosListUserAddress () {
    // console.log(val)
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/list_user_address_ups`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateUserAddress (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_user_address_v2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateUserAddress (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_user_address_v2`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เพิ่มที่อยู่ลูกค้า sale
  async CreateCustomerAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/create_customer_address `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เพิ่มลูกค้า sale
  async CreateCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/create_customer `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateCustomerInvAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/update_customer_inv_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateCustomerInvAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/create_customer_inv_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteUserAddress (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_user_address`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DefaultUserAddress (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/set_default_address`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreatedAddressTaxinvoice (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}taxInvoices/insert`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAddressTaxinvoice (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}taxInvoices`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAddressTaxinvoicePurchase () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_corporate_address`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosUPSListUserAddressTex () {
    // console.log(val)
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/list_invoice_address_ups`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateUserProfile (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_user_profile`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AuthorityUserAccount (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail_mp_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetInvoice (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/getInvoice`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpsertInvoice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/upsert`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBusiness () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_business_approved`, '', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddInvoice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/create/v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetInvoiceAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/getInvoice/v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllInvoiceAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/getAllInvoice`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetDefaultInvoice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/setDefault`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteInvoice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}invoice/delete`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AcceptConsent (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}consentPlatform/accept`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApprovedConsent (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}consentPlatform/approved`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetTextConsent (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}consentPlatform/consentText`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetTokenWLC (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}shareToken/getToken`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}coupon/list_collected`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListShopJV (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}sale/seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RefreshToken (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/refresh_token_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeShippingAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_shipping_address`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosListAccountBankUser () {
    // console.log(val)
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_account`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosCreateAccountBankUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/add_account`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosUpdateAccountBankUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_account`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosDeleteAccountBankUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_account`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async axiosChangeMainAccountBankUser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_main_account`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDataInviteCode () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/reward-coin/show-invite-code`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
