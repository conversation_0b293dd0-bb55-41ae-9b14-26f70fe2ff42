<template>
  <div class="text-center">
    <v-dialog v-model="DialogDataUser" width="519" persistent scrollable>
      <v-card class="inner-right" id="style-15">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>ผู้ซื้อประจำ</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="DialogDataUser = !DialogDataUser" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
      <v-card-title>
      <v-avatar size="40">
             <img
              src="../../assets/icons/checklist 1.png"
              alt="Product"
             >
      </v-avatar>
      <div class="mx-3"></div>
          ผู้ซื้อประจำ
      </v-card-title>
  <hr/>
<v-list height="1042">
      <v-list-item-group
      >
        <v-list-item
          v-for="(item, i) in itemsListUser"
          :key="item.user_id"
        >
        <div>{{i+1}}</div>
          <v-list-item-avatar size="80" class="mx-8">
            <img
              src="../../assets/icons/checklist 1.png"
              alt="Product"
             >
          </v-list-item-avatar>
          <v-list-item-content class="mb-2">
            <v-list-item-title class="text-center" v-text="item.name"></v-list-item-title>
            <span class="text-center" style="font-size: 10px;color: #e7e7e7;">(Partner)</span>
          </v-list-item-content>
          <v-list-item-content style="font-size: 16px;color: #1AB759;" >
            <v-list-item-title class="text-center" v-text="toFixData(item.price)"></v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list-item-group>
    </v-list>
          <!-- <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="CreateAddress()">บันทึก</v-btn>
          </v-card-actions> -->
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      itemsListUser: [],
      DialogDataUser: false
    }
  },
  watch: {
  },
  created () {
    this.$EventBus.$on('dialogDataUser', this.dialogDataUser)
  },
  destroyed () {
    this.$EventBus.$off('dialogDataUser')
  },
  mounted () {
  },
  methods: {
    toFixData (data) {
      return data.toFixed(2)
    },
    async dialogDataUser (data) {
      // console.log('dialogDataUser', data)
      this.DialogDataUser = await !this.DialogDataUser
      this.itemsListUser = await data
    }
  }
}
</script>
<style scoped>
.inner-right {
    height: auto;
    overflow-y: scroll;
}

#style-15::-webkit-scrollbar-track
{
  -webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.1);
  background-color: #F5F5F5;
  border-radius: 10px;
}

#style-15::-webkit-scrollbar
{
  width: 10px;
  background-color: #F5F5F5;
}

#style-15::-webkit-scrollbar-thumb
{
  border-radius: 10px;
  background-color: #FFF;
  background-image: -webkit-gradient(linear,
                     40% 0%,
                     75% 84%,
                     from(#27ab9c),
                     to(#27ab9c),
                     color-stop(.6,#27ab9c))
}
</style>
