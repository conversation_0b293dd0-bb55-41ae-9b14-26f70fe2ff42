import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  // ดึงข้อมูลของกราฟรายได้ และ ผลรวมรายได้กับจำนวนรายการ
  async RevenuGraphSummary (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/revenugraphsummary`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ข้อมูลรายการการสั่งซื้อ
  async OrderList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/orderlist`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ข้อมูลสินค้าที่รายมากสุด10อันดับ ทั้งจากจำนวนชึ้นและมูลค่า
  async TopProducts (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/topproducts`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ผู้ซื้อที่ซื้อมากที่สุด10คน
  async TopBuyers (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/topbuyers`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ร้านค้าที่มีคำสั่งซื้อมากที่สุด 10 ร้าน
  async TopSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/topseller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // export ไฟล์ excel
  async ExportDashboard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/exportdashboard`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
