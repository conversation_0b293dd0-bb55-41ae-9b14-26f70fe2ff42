<template>
  <v-container :class="MobileSize ? 'background_color_Mobile' : 'background_color'" grid-list-xs rounded>
    <v-row dense :class="MobileSize ? 'pt-3 pb-3' : 'pt-3 pb-3'">
      <v-col cols="12">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; line-height: 32px; font-weight: bold;'">
          <v-icon v-if="MobileSize" @click="cancle()" color="#27AB9C" size="30">mdi-chevron-left</v-icon>
          เตรียมจัดส่ง
        </span>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'pt-1 pb-3' : 'pt-1 pb-3'">
      <v-col cols="12">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 16px; line-height: 24px;' : 'font-size: 20px; line-height: 30px; font-weight: bold;'">คำสั่งซื้อที่ยังไม่ดำเนินการ</span>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'pt-1 pb-3' : 'pt-1 pb-3'">
      <v-col cols="12" md="5" sm="6" align="start">
        <v-text-field @keyup="checkSearch" hide-details v-model="search" placeholder="ค้นหา" outlined dense style="max-height: 36px;">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" md="4" sm="6" align="start" :class="MobileSize ? 'my-2' : ''" style="display: flex; column-gap: 1rem;">
        <div style="height: 40px;">
          <v-dialog
            ref="dialogStartDate"
            v-model="dialogStartDate"
            :return-value.sync="date"
            persistent
            width="290px"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="searchStartDate"
                placeholder="DD/MM/YYYY"
                dense
                outlined
                readonly
                hide-details
                label="วันที่เริ่มต้น"
                style="max-width: 208px;"
                v-bind="attrs"
                v-on="on"
              >
                <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              v-model="date"
              scrollable
              reactive
              no-title
              locale="Th-th"
              @change="setValueStartDate(date)"
              :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            >
              <v-spacer></v-spacer>
              <v-btn
                text
                color="primary"
                @click="closeDialogStartdate()"
              >
                ยกเลิก
              </v-btn>
              <v-btn
                text
                color="primary"
                @click="$refs.dialogStartDate.save(date)"
              >
                ตกลง
              </v-btn>
            </v-date-picker>
          </v-dialog>
        </div>
        <div style="height: 40px;">
          <v-dialog
            ref="dialogEndDate"
            v-model="dialogEndDate"
            :return-value.sync="date1"
            persistent
            width="290px"
          >
          <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="searchEndDate"
                placeholder="DD/MM/YYYY"
                dense
                outlined
                readonly
                hide-details
                label="วันที่สิ้นสุด"
                style="max-width: 208px;"
                v-bind="attrs"
                v-on="on"
              >
                <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
              </v-text-field>
            </template>
            <v-date-picker
              v-model="date1"
              scrollable
              reactive
              no-title
              locale="Th-th"
              @change="setValueEndDate(date1)"
              :min="date"
              :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            >
              <v-spacer></v-spacer>
              <v-btn
                text
                color="primary"
                @click="closeDialogModogEnddate()"
              >
                ยกเลิก
              </v-btn>
              <v-btn
                text
                color="primary"
                @click="$refs.dialogEndDate.save(date1), getFilterDate(date, date1)"
              >
                ตกลง
              </v-btn>
            </v-date-picker>
          </v-dialog>
        </div>
      </v-col>
      <v-col cols="12" md="3" sm="6" :align="IpadSize ? 'start': 'end'">
        <v-select v-model="selectShipping" :items="dataCourier" item-text="courier_name" item-value="courier_code"  hide-details outlined dense label="ขนส่ง" no-data-text="ไม่มีรายชื่อขนส่ง"></v-select>
      </v-col>
    </v-row>
    <!-- เลือกรายการทั้งหมด -->
    <v-row dense :class="MobileSize ? 'py-1' : 'py-1 pl-3'" v-if="dataOrder.length !== 0">
      <v-col cols="12" md="6" sm="6">
        <v-checkbox v-model="checkAllOrder" label="เลือกรายการทั้งหมด" color="#27AB9C" @click="SelectAll(dataOrder, checkAllOrder)" hide-details></v-checkbox>
      </v-col>
      <v-col cols="12" md="6" sm="6" :align="IpadSize ? 'end': 'end'" class="mt-2">
        <v-btn :block="MobileSize ? true : false" @click="ShowDialogAll(dataOrder)" :height="!MobileSize && !IpadProSize && !IpadSize ? '40' : '40'" rounded color="#27AB9C" :disabled="proofSendOrder()" class="white--text">เตรียมจัดส่งพัสดุ</v-btn>
      </v-col>
    </v-row>
    <!-- แสดงส่วนที่แสดงสินค้าและเลือกรายการสินค้า -->
    <v-row dense :class="MobileSize ? '' : 'pl-3'" v-if="dataOrder.length !== 0">
      <v-col cols="12" v-for="(items, index) in dataOrder" :key="index" class="mb-4" :class="MobileSize ? 'px-0' : ''">
        <v-card width="100%" height="100%" outlined style="border-radius: 8px;">
          <v-card-text :class="MobileSize ? 'px-2' : ''">
            <!-- Actions -->
            <v-col cols="12" align="end" v-if="MobileSize">
              <v-row justify="end">
                <!-- <v-col cols="6" class="pa-1">
                  <v-btn block color="#27AB9C" height="36" rounded class="white--text fontres" @click="ShowDialog3(items)" elevation="0">
                    ส่งโดยร้านค้าแล้ว
                  </v-btn>
                </v-col> -->
                <!-- <v-col cols="6" class="pa-1">
                  <v-btn block outlined color="#27AB9C" height="36" rounded class="fontres"  @click="ShowDialog2(items)">
                    <v-icon small class="mr-2 disableIcon">mdi-plus-circle-outline</v-icon>เปลี่ยนขนส่ง
                  </v-btn>
                </v-col> -->
                <v-col cols="6" class="pa-1 mt-2">
                  <v-btn block outlined color="#27AB9C" height="36" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                    <v-icon small class="mr-2">mdi-plus-circle-outline</v-icon>เตรียมจัดส่งพัสดุ
                  </v-btn>
                </v-col>
                <!-- <v-col cols="6" class="pa-1">
                  <v-btn :disabled="proofCutOrders(items.product_list)" block outlined height="36" color="#27AB9C" class="fontres" @click="CutOrders(items,index)" rounded>
                    <v-icon color="#27AB9C" class="mr-2 " small>mdi-archive-outline</v-icon> แบ่งออเดอร์
                  </v-btn>
                </v-col> -->
              </v-row>
            </v-col>
            <v-row dense>
              <v-col :cols="!MobileSize && !IpadProSize && !IpadSize ? '6' : MobileSize ? '12' : '6'" align="start" class="pt-3 my-checkbox">
                <v-checkbox v-model="items.isSelect" class="mt-0" :label="'รายการสั่งซื้อสินค้า' + ' ' + `${ items.product_list.length }` + ' ' + 'รายการ'" color="#27AB9C" hide-details></v-checkbox>
              </v-col>
              <!-- <pre>{{ items }}</pre> -->
              <!-- :disabled="items.isSelect === false ? true : false -->
              <v-col cols="6" align="end" v-if="!MobileSize && !IpadProSize && !IpadSize" style="display: inline-block; margin: auto;">
                <!-- <pre>
                  {{items.product_list}}
                </pre> -->
                <!-- <v-btn color="#27AB9C" height="48" rounded class="white--text fontres" :disabled="items.isSelect ? true : false" @click="ShowDialog3(items)" elevation="0">
                  ส่งโดยร้านค้าแล้ว
                </v-btn> -->
                <!-- <v-btn outlined color="#27AB9C" height="48" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog2(items)">
                  <v-icon small class="mr-2 disableIcon">mdi-plus-circle-outline</v-icon><div class="stepAside">เปลี่ยนขนส่ง</div>
                </v-btn> -->
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">เตรียมจัดส่งพัสดุ</div>
                </v-btn>
                <!-- <v-btn :disabled="proofCutOrders(items.product_list) || items.isSelect" class="fontres" outlined height="48" color="#27AB9C" @click="CutOrders(items,index)" rounded>
                  <v-icon color="#27AB9C" class="mr-2 " small>mdi-archive-outline</v-icon><div class="stepAside">แบ่งออเดอร์</div>
                </v-btn> -->
              </v-col>
              <v-col cols="6" align="end" v-if="IpadProSize && !IpadSize && !MobileSize" style="display: inline-block; margin: auto;">
                <!-- <pre>
                  {{items.product_list}}
                </pre> -->
                <!-- <v-btn color="#27AB9C" height="48" rounded class="white--text fontres" :disabled="items.isSelect ? true : false" @click="ShowDialog3(items)" elevation="0">
                  ส่งโดยร้านค้าแล้ว
                </v-btn> -->
                <!-- <v-btn outlined color="#27AB9C" height="48" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog2(items)">
                  <v-icon small class="mr-2 disableIcon">mdi-plus-circle-outline</v-icon><div class="stepAside">เปลี่ยนขนส่ง</div>
                </v-btn> -->
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">เตรียมจัดส่งพัสดุ</div>
                </v-btn>
                <!-- <v-btn :disabled="proofCutOrders(items.product_list) || items.isSelect" class="fontres" outlined height="48" color="#27AB9C" @click="CutOrders(items,index)" rounded>
                  <v-icon color="#27AB9C" class="mr-2 " small>mdi-archive-outline</v-icon><div class="stepAside">แบ่งออเดอร์</div>
                </v-btn> -->
              </v-col>
              <v-col cols="6" align="end" v-if="!IpadProSize && IpadSize && !MobileSize" style="display: inline-block; margin: auto;">
                <!-- <pre>
                  {{items.product_list}}
                </pre> -->
                <!-- <v-btn color="#27AB9C" height="48" rounded class="white--text fontres" :disabled="items.isSelect ? true : false" @click="ShowDialog3(items)" elevation="0">
                  ส่งโดยร้านค้าแล้ว
                </v-btn> -->
                <!-- <v-btn outlined color="#27AB9C" height="48" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog2(items)">
                  <v-icon small class="mr-2 disableIcon">mdi-plus-circle-outline</v-icon><div class="stepAside">เปลี่ยนขนส่ง</div>
                </v-btn> -->
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="items.isSelect" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">เตรียมจัดส่งพัสดุ</div>
                </v-btn>
                <!-- <v-btn :disabled="proofCutOrders(items.product_list) || items.isSelect" class="fontres" outlined height="48" color="#27AB9C" @click="CutOrders(items,index)" rounded>
                  <v-icon color="#27AB9C" class="mr-2 " small>mdi-archive-outline</v-icon><div class="stepAside">แบ่งออเดอร์</div>
                </v-btn> -->
              </v-col>
              <!-- <v-col cols="6" offset="2" v-else> -->
            </v-row>
            <!-- Data -->
            <v-row dense class="pt-2">
              <v-col cols="12">
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">หมายเหตุจากขนส่ง : <b style="color: #FAAD14;">{{ items.shipping_remark === '' ? '-' : items.shipping_remark }}</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">รหัสจัดส่งสินค้า : <b>{{ items.order_delivery_number }}</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">รหัสคำสั่งซื้อ : <b>{{ items.transaction_number }}</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">วันที่สั่งซื้อ : <b>{{ items.buy_date !== '-' ? new Date(items.buy_date).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric", hour: "numeric", minute: "numeric" }) + ' น.' : '-' }}</b></p>
                <p style="font-weight: 700; font-size: 18px; line-height: 24px; color: #000000;"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;"><b>{{ items.address.name }}</b> <br v-if="MobileSize"/> {{ items.address.address }}</p><br>
                <!-- <p style="font-weight: 700; font-size: 18px; line-height: 24px; color: #000000;"><b>เอกสารที่ต้องเพิ่มลงกล่อง</b></p> -->
                <!-- <div class="mb-3 ml-2">
                  <span>ใบเสร็จรับเงิน : </span>
                  <span v-if="items.remark !== '-'" style="color:#1B5DD6; cursor: pointer;" @click="downloadPDF(items.bill_pdf)">คลิกที่นี่</span>
                  <span v-else>
                    -
                  </span>
                </div> -->
                <!-- <div class="mb-3 ml-2 d-flex grow flex-wrap" style="column-gap: 3px;align-items: center;">
                  <span>เอกสารที่ผู้ซื้อร้องขอ :</span>
                  <span v-if="!items.document_buyer.length"> - </span>
                  <div v-else v-for="(item, index) in items.document_buyer" :key="index" class="d-flex flex-wrap mr-2" style="align-items: center;border: solid 0.25px;border-color: #EFEFEE;border-radius: 8px;background-color: white;color: #333333;padding: 4px;">
                        <v-img
                        max-height="20"
                        max-width="20"
                        src="@/assets/iShip/pdf.svg"></v-img>
                        <span class="pl-2">{{item}}</span>
                  </div>
                </div> -->
                <!-- <div class="mb-3 ml-2">
                  <span>ความต้องการพิเศษจากผู้ซื้อ : </span>
                  <span v-if="items.remark !== '-'" style="color:#1B5DD6">{{ items.remark }}</span>
                  <span v-else>
                    -
                  </span>
                </div> -->
                <ul>
                  <li>
                    <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><b>Standard Delivery</b> : <br v-if="MobileSize"/><b>{{ items.shipping_name }}</b></span><br>
                    <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Tracking Number : <b>{{ items.tracking_number }}</b></span> -->
                  </li>
                </ul>
              </v-col>
            </v-row>
            <!-- table -->
            <v-row dense class="pt-2">
              <v-col cols="12">
                <v-data-table
                 :headers="MobileSize ? dataTableheaderMobile : dataTableheader"
                 :hide-default-header="MobileSize ? true : false"
                 :items="items.expand_table === false ? items.product_list_not_expand : items.product_list"
                 :items-per-page="50"
                 hide-default-footer
                 style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));"
                >
                  <template v-slot:[`item.product_detailMobile`]="{ item }">
                    <v-row dense>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                        <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <v-row dense class="py-4">
                      <v-col cols="2" class="">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                  <template v-slot:[`item.total_price`]="{ item }">
                    {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                </v-data-table>
                <v-data-table
                 :headers="MobileSize ? dataTableheaderPromotionMobile : dataTableheaderPromotion"
                 :items="items.product_promotion"
                 v-if="items.product_promotion !== undefined && items.product_promotion.length !== 0"
                 hide-default-footer
                 class="headerPromotion"
                 style="background: #F5FBFF; filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));"
                >
                  <template v-slot:[`item.product_detailMobile`]="{ item, index }">
                    <v-row dense justify="start">
                      <v-col cols="12" class="pt-4" align="start" v-if="index === 0">
                        <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #27AB9C;" >สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                        <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.product_detail`]="{ item, index }">
                    <v-row dense>
                      <v-col cols="12" class="pt-4"  v-if="index === 0">
                        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="2" class="">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="start">
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    <v-row dense>
                      <v-col cols="12">
                        {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.quantity`]="{ item }">
                    <v-row dense>
                      <v-col cols="12">
                        {{ item.quantity }}
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.net_price`]="{ item }">
                    <v-row dense>
                      <v-col cols="12">
                        {{ item.vat_default === 'yes' ? Number(item.revenue_default_with_vat_total).toLocaleString(undefined, {minimumFractionDigits: 2}) : Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </v-col>
                    </v-row>
                  </template>
                </v-data-table>
                <v-row dense justify="center" class="mt-0" v-if="items.product_list.length > 1">
                  <v-col cols="12" align="center">
                    <v-btn text block @click="ShowExpand(dataOrder, items.prepare_order_id, !items.expand_table)">
                      <span :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'" style="font-weight: 400; line-height: 22px; text-decoration-line: underline; color: #27AB9C;" v-if="items.expand_table === false">ดูเพิ่มเติม</span><v-icon color="#27AB9C" v-if="items.expand_table === false">mdi-chevron-down</v-icon>
                      <span :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'" style="font-weight: 400; line-height: 22px; text-decoration-line: underline; color: #27AB9C;" v-if="items.expand_table === true">ย่อขนาด</span><v-icon color="#27AB9C" v-if="items.expand_table === true">mdi-chevron-up</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense v-else>
      <v-col cols="12">
        <v-card width="100%" :height="MobileSize ? '20vh' : '10vh'" outlined style="border-radius: 8px; border-color: #27AB9C;">
          <v-card-text class="text-center">
            <v-row justify="center" style="text-align: center; margin: auto;">
              <span style="font-size: 1rem; font-weight: 700;" :class="MobileSize ? 'pt-6' : IpadSize ? 'pt-6' : 'pt-2'">ไม่มีคำสั่งซื้อที่ยังไม่ดำเนินการ</span>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <!-- Dialog เรียกพนักงานรับพัสดุ -->
    <v-dialog v-model="dialogCallShipping" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการเตรียมจัดส่งพัสดุ
          </span>
           <v-btn icon dark @click="dialogCallShipping = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span>ยืนยันการเตรียมจัดส่งพัสดุ</span><br>
              <span>ใช่หรือไม่</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogCallShipping = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmOrderCourier(orderSelect)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSendBySeller" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการส่งพัสดุโดยร้านค้า
          </span>
           <v-btn icon dark @click="dialogSendBySeller = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span>ยืนยันการส่งรายการพัสดุ</span><br>
              <span>ใช่หรือไม่</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogSendBySeller = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="disabledToClickCreateShipping" @click="confirmSendBySeller(orderSelect)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog เรียกพนักงานรับพัสดุทั้งหมด -->
    <v-dialog v-model="dialogCallShippingAll" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการเรียกพนักงานเข้ารับพัสดุทั้งหมด
          </span>
           <v-btn icon dark @click="dialogCallShippingAll = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span>ยืนยันการเรียกพนักงานเข้ารับพัสดุทั้งหมด</span><br>
              <span>ใช่หรือไม่</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogCallShippingAll = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmOrderCourierAll(orderSelectAll)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog ยืนยันการเตรียมจัดส่งพัสดุ  -->
    <v-dialog v-model="dialogConfirmSelectOrder" persistent width="379">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการเตรียมจัดส่งพัสดุ
          </span>
           <v-btn icon dark @click="closeDialogConfirmSelectOrder()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <!-- {{ confirmOrderSelectData }} -->
            <!-- <v-col cols="12" align="start" class="pb-0">
              <v-card width="328" max-width="100%" max-height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text>
                  <v-avatar :size="80">
                    <v-img :src="confirmOrderSelectData.courier_image" ></v-img>
                  </v-avatar>
                </v-card-text>
              </v-card>
            </v-col> -->
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รหัสการสั่งซื้อ : {{ confirmOrderSelectData.order_delivery_number }}</span>
            </v-col>
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 700; color: red; font-size: 16px;">*** หมายเหตุ: </span><br/>
              <ul>
                <li><b style="color: #333333">Kerry, DHL</b> และ <b style="color: #333333">BestExpress</b> จะเป็นการเรียกขนส่งเข้ามารับพัสดุทันที</li>
                <li><b style="color: #333333">Flash</b> และ <b style="color: #333333">ขนส่งอื่นๆ</b> ต้องกดปุ่ม <b style="color: #333333">"เรียกขนส่งเข้ารับ(คูเรียร์)"</b> อีกครั้งในหน้ารายงานการจัดส่ง</li>
              </ul>
            </v-col>
            <!-- <v-col cols="12" align="start">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">หมายเหตุ</span>
              <v-textarea v-model="remark" outlined hide-details height="100"></v-textarea>
            </v-col> -->
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirmSelectOrder()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="disabledToClickCreateShipping"  @click="comfirm(confirmOrderSelectData, remark)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog ยืนยันการเตรียมจัดส่งพัสดุทั้งหมด202020  -->
    <v-dialog v-model="dialogConfirmSelectOrderAll" persistent width="379">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการเตรียมจัดส่งพัสดุทั้งหมด
          </span>
           <v-btn icon dark @click="closeDialogConfirmSelectOrderAll()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <!-- {{ confirmOrderSelectData }} -->
            <!-- <v-col cols="12" align="start" class="pb-0">
              <v-card width="328" max-width="100%" max-height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text>
                  <v-avatar :size="80">
                    <v-img></v-img>
                  </v-avatar>
                </v-card-text>
              </v-card>
            </v-col> -->
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รหัสการสั่งซื้อ :
                <span v-for="(item, index) in confirmOrderSelectDataAll" :key="index"> {{ item.order_delivery_number }} <span v-if="index !== confirmOrderSelectDataAll.length - 1">,</span> </span>
              </span>
            </v-col>
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 700; color: red; font-size: 16px;">*** หมายเหตุ: </span><br/>
              <ul>
                <li><b style="color: #333333">Kerry, DHL</b> และ <b style="color: #333333">BestExpress</b> จะเป็นการเรียกขนส่งเข้ามารับพัสดุทันที</li>
                <li><b style="color: #333333">Flash</b> และ <b style="color: #333333">ขนส่งอื่นๆ</b> ต้องกดปุ่ม <b style="color: #333333">"เรียกขนส่งเข้ารับ(คูเรียร์)"</b> อีกครั้งในหน้ารายงานการจัดส่ง</li>
              </ul>
            </v-col>
            <!-- <v-col cols="12" align="start">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">หมายเหตุ</span>
              <v-textarea v-model="remark" outlined hide-details height="100"></v-textarea>
            </v-col> -->
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirmSelectOrderAll()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="disabledToClickCreateShipping" @click="comfirmAll(confirmOrderSelectDataAll, remark)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirmSelectOrderSome" persistent width="379">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการเตรียมจัดส่งพัสดุ
          </span>
           <v-btn icon dark @click="closeDialogConfirmSelectOrderSome()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <!-- {{ confirmOrderSelectData }} -->
            <!-- <v-col cols="12" align="start" class="pb-0">
              <v-card width="328" max-width="100%" max-height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text>
                  <v-avatar :size="80">
                    <v-img></v-img>
                  </v-avatar>
                </v-card-text>
              </v-card>
            </v-col> -->
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รหัสการสั่งซื้อ :
                <span v-for="(item, index) in orderSelectAll" :key="index"> {{ item.order_delivery_number }} <span v-if="index !== orderSelectAll.length - 1">,</span> </span>
              </span>
            </v-col>
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 700; color: red; font-size: 16px;">*** หมายเหตุ: </span><br/>
              <ul>
                <li><b style="color: #333333">Kerry</b> และ <b style="color: #333333">DHL</b> จะเป็นการเรียกขนส่งเข้ามารับพัสดุทันที</li>
                <li><b style="color: #333333">Flash</b> และ <b style="color: #333333">ขนส่งอื่นๆ</b> ต้องกดปุ่ม <b style="color: #333333">"เรียกขนส่งเข้ารับ(คูเรียร์)"</b> อีกครั้งในหน้ารายงานการจัดส่ง</li>
              </ul>
            </v-col>
            <!-- <v-col cols="12" align="start">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">หมายเหตุ</span>
              <v-textarea v-model="remark" outlined hide-details height="100"></v-textarea>
            </v-col> -->
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirmSelectOrderSome()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="disabledToClickCreateShipping" @click="comfirmAll(orderSelectAll, remark)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
     <!-- Dialog แบ่งสินค้าออเดอร์ -->
    <v-dialog v-model="dialogCutOrder" persistent width="822">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แบ่งออเดอร์
          </span>
          <v-btn icon dark @click="closeCutOrder()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="start" dense class="pt-6">
            <v-col cols="12" md="6" align="start" class="pb-0">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รหัสการสั่งซื้อ : {{ formDetail.orders_number}}</span>
            </v-col>
          </v-row>
          <v-row justify="start" dense class="pt-6">
            <v-col cols="12" md="12" align="start" class="pb-0">
              <span style="color: #333333;">กล่องที่ 1</span>
              <!-- <span class="pl-4">{{ showCountOrder }} / {{ lengthProduct }}</span> -->
            </v-col>
          </v-row>
          <v-row>
          <v-col cols="12" md="6" sm="6">
            <v-select
              v-model="selectOrder"
              :items="selectItemOrder"
              @change="selectedOrder(0)"
              item-text="product_name"
              item-value="index"
              placeholder="กรุณาเลือกสินค้า"
              outlined
              return-object
              hide-details
              dense
              no-data-text="ไม่พบข้อมูล"
              label="กรุณาเลือกสินค้า"
            >
            </v-select>
          </v-col>
          <v-col cols="12" md="6" sm="6" align="end">
            <div class="">
              <v-btn
                rounded
                color="primary"
                :block="MobileSize ? true : false"
                outlined
                :disabled="checkDisableBotton(selectItemOrder.length, indexAddBox)"
                @click="addBox()"
              >
              <v-icon
                x-small
                color="primary"
              >
                mdi-plus-circle-outline
              </v-icon>
              เพิ่มกล่องสินค้า
              </v-btn>
            </div>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <v-card width="100%" height="100%" outlined style="border-radius: 8px; border-color: #B9DAF6;">
              <div class="text-center " :class="MobileSize ? 'my-4 mx-4' : 'my-6 mx-6'">
                <div v-if="itemInboxAll.length !== 0">
                  <v-data-table
                    :headers="MobileSize ? dataTableheader2Mobile : dataTableheader2"
                    :items="itemInboxAll[[0]]"
                    hide-default-footer
                    :hide-default-header="MobileSize ? true : false"
                    @pagination="countMember"
                    no-data-text="ยังไม่มีสินค้า"
                    :style="MobileSize ? '' : 'filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));'"
                    >
                      <template v-slot:[`item.product_detailMobile`]="{ item }">
                        <v-row dense>
                          <v-col cols="2" class="mt-3">
                            <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                          </v-col>
                          <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                            <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                            <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                            <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                            <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                          </v-col>
                        </v-row>
                      </template>
                      <template v-slot:[`item.product_detail`]="{ item }">
                        <!-- <v-row dense v-if="items.expand_table === false">
                          <v-col cols="12" v-if="(index === items.product_list_not_expand.length - 1)" class="pt-6 pb-4">
                            <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                          </v-col>
                          <v-col cols="12" v-else class="pt-4">
                          </v-col>
                        </v-row>
                        <v-row dense v-else-if="items.expand_table === true">
                          <v-col cols="12" v-if="(index === items.productInbox.length - 1)" class="pt-6 pb-4">
                            <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                          </v-col>
                          <v-col cols="12" v-else class="pt-4">
                          </v-col>
                        </v-row> -->
                        <v-row dense>
                          <v-col cols="2" class="mt-3">
                            <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                          </v-col>
                          <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                            <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                            <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                            <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                          </v-col>
                        </v-row>
                      </template>
                      <template v-slot:[`item.price`]="{ item }">
                        {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </template>
                      <template v-slot:[`item.total_price`]="{ item }">
                        {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </template>
                    </v-data-table>
                </div>
                <div v-else >
                ยังไม่มีสินค้า
              </div>
              </div>
            </v-card>
          </v-col>
        </v-row>
        <div v-for="(item, index) in formCut.product_split" :key="index">
          <!---index 2-->
          <v-row justify="start" dense class="pt-6">
            <v-col cols="12" md="12" align="start" class="pb-0">
              <span style="color: #333333;">กล่องที่ {{ index + 2 }}</span>
              <!-- <span class="pl-4">{{ showCountOrderOther }} / {{ lengthProduct }}</span> -->
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="6" sm="6">
              <v-select
                v-model="selectOrder"
                :items="selectItemOrder"
                @change="selectedOrder(item.indexBox)"
                item-text="product_name"
                item-value="index"
                placeholder="กรุณาเลือกสินค้า"
                outlined
                hide-details
                return-object
                dense
                no-data-text="ไม่พบข้อมูล"
                label="กรุณาเลือกสินค้า"
              >
              </v-select>
            </v-col>
            <v-col cols="12" md="6">
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <v-card width="100%" height="100%" outlined style="border-radius: 8px; border: 1px solid var(--primary-b-9-daf-6, #B9DAF6);">
                <div class="text-center" :class="MobileSize ? 'my-4 mx-4' : 'my-6 mx-6'">
                  <div v-if="itemInboxAll.length !== 0">
                    <v-data-table
                      :headers="MobileSize ? dataTableheader2Mobile : dataTableheader2"
                      :hide-default-header="MobileSize ? true : false"
                      :items="itemInboxAll[[index + 1]]"
                      hide-default-footer
                      @pagination="countMemberOther"
                      no-data-text="ยังไม่มีสินค้า"
                      :style="MobileSize ? '' : 'filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));'"
                    >
                      <template v-slot:[`item.product_detailMobile`]="{ item }">
                        <v-row dense>
                          <v-col cols="2" class="mt-3">
                            <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                          </v-col>
                          <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                            <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                            <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                            <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                            <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                          </v-col>
                        </v-row>
                      </template>
                      <template v-slot:[`item.product_detail`]="{ item }">
                        <!-- <v-row dense v-if="items.expand_table === false">
                          <v-col cols="12" v-if="(index === items.product_list_not_expand.length - 1)" class="pt-6 pb-4">
                            <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                          </v-col>
                          <v-col cols="12" v-else class="pt-4">
                          </v-col>
                        </v-row>
                        <v-row dense v-else-if="items.expand_table === true">
                          <v-col cols="12" v-if="(index === items.productInbox.length - 1)" class="pt-6 pb-4">
                            <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                          </v-col>
                          <v-col cols="12" v-else class="pt-4">
                          </v-col>
                        </v-row> -->
                        <v-row dense>
                          <v-col cols="2" class="mt-3">
                            <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                          </v-col>
                          <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                            <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                            <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                            <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                          </v-col>
                        </v-row>
                      </template>
                      <template v-slot:[`item.price`]="{ item }">
                        {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </template>
                      <template v-slot:[`item.total_price`]="{ item }">
                        {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                      </template>
                    </v-data-table>
                  </div>
                  <div v-else >
                    ยังไม่มีสินค้า
                  </div>
                </div>
              </v-card>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
      <v-card-actions>
        <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeCutOrder()" class="mr-2">ยกเลิก</v-btn>
          <v-btn width="110" height="40" :disabled="selectItemOrder.length === 0 && indexAddBox ? false : true" rounded color="#27AB9C" class="white--text" @click="confirmData()">บันทึก</v-btn>
        </v-row>
      </v-card-actions>
    </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSelectCourier" width="580" persistent :scrollable="dataChangeCourier.length !== 0 ? true : false">
      <v-card style="border-radius: 12px;">
        <v-toolbar dense elevation="0"  color="#DAF1E9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>เลือกขนส่ง</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialogSelect()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text class="pb-0" :style="dataChangeCourier.length === 0 ? 'overflow-y: hidden !important;' : ''">
          <v-row dense class="mt-4" v-if="dataChangeCourier.length !== 0">
            <v-col cols="12" v-for="(item, index) in dataChangeCourier" :key="index" class="mb-4">
              <v-card elevation="0" :width="MobileSize ? '100%' : '774'" :height="MobileSize ? '100%' : '100%'" outlined style="border: 1px solid #DAF1E9; border-radius: 8px;">
                <v-card-text style="display: block; margin: auto;">
                  <v-row>
                    <v-col cols="1" md="1" :align="MobileSize ? '' : 'center'" :class="MobileSize ? 'px-1' : ''" style="display: block; margin: auto;">
                      <v-radio-group v-model="selectCourier">
                        <v-radio :value="item.courier_code" @click="saveSelectCourier(item.courier_code)"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <v-col cols="5" md="5" align="start" style="display: block; margin: auto;">
                      <v-img :src="item.img_path" max-width="90" max-height="90" style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16)); border-radius: 50%;"></v-img>
                    </v-col>
                    <v-col cols="6" md="6" align="end" :class="MobileSize ? 'px-1' : ''" style="display: block; margin: auto;">
                      <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="font-weight: 400; line-height: 26px; color: #333333;">{{ item.courier_name }}</span><br/>
                      <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; line-height: 24px; color: #1B5DD6;">ค่าส่งเริ่มต้น {{ item.total_price }} บาท</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row justify="center" v-else>
            <v-col cols="12" align="center" class="my-4">
              <v-card elevation="0">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center">
                      <v-img class="mb-4" :src="require('@/assets/iShip/car.png')" :width="MobileSize ? '100%' : '460'" height='228' contain></v-img>
                      <span style="font-weight: 700; line-height: 32px; color: #333333;" :style="!MobileSize ? 'font-size: 24px;' : 'font-size: 16px;'">ไม่มีขนส่งรองรับ</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccessChange" width="472" persistent>
    <v-card style="background: #FFFFFF; border-radius: 4px;" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center ml-9" style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">
          เปลี่ยนขนส่ง
        </span>
      </v-toolbar>
      <v-card-text>
        <v-row dense justify="center">
          <v-col cols="12" align="center" class="py-4">
            <!-- <v-icon color="#27AB9C" size="70px">mdi-check-circle</v-icon> -->
            <v-img  :src="require('@/assets/iShip/FrameSusses.png')" loading="lazy"  width="164px"  height="139px" ></v-img>
            <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #012A73;" class="pt-4">เสร็จสิ้น</p>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      search: '',
      shopID: '',
      checkAllOrder: false,
      dataOrder: [],
      dstAddress: '',
      showCountOrder: 0,
      showCountOrderOther: 0,
      dataOrderPromotion: [],
      dataTableheader: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', sortable: false, align: 'start', width: '50%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', sortable: false, align: 'center', width: '10%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'total_price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheaderMobile: [
        { text: 'รายละเอียดสินค้า', value: 'product_detailMobile', sortable: false, align: 'start', width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheaderPromotion: [
        { text: '', value: 'product_detail', sortable: false, align: 'start', width: '50%', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'quantity', sortable: false, align: 'center', width: '10%', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'net_price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheaderPromotionMobile: [
        { text: 'รายละเอียดสินค้า', value: 'product_detailMobile', sortable: false, align: 'start', width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheader2: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', sortable: false, align: 'start', width: '50%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', sortable: false, align: 'center', width: '10%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'total_price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheader2Mobile: [
        { text: 'รายละเอียดสินค้า', value: 'product_detailMobile', sortable: false, align: 'start', width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      dialogSendBySeller: false,
      dialogCallShipping: false,
      dialogCallShippingAll: false,
      dialogConfirmSelectOrderSome: false,
      dialogCutOrder: false,
      selectOrder: [],
      dataChangeCourier: [],
      selectItemOrder: [],
      formCut: {
        order_number: '',
        seller_shop_id: '',
        product_split: []
      },
      indexAddBox: 0,
      productInbox: [],
      formDetail: {
        orders_number: ''
      },
      itemInboxAll: [],
      orderSelect: [],
      data1: [],
      prepareid: '',
      dialogConfirmSelectOrder: false,
      confirmOrderSelectData: [],
      remark: '',
      orderSelectAll: [],
      dialogConfirmSelectOrderAll: false,
      dialogSuccessChange: false,
      dialogSelectCourier: false,
      confirmOrderSelectDataAll: [],
      lengthProduct: '',
      searchStartDate: '',
      searchEndDate: '',
      dialogStartDate: false,
      dialogEndDate: false,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      searchStartDateNotFormat: '',
      searchEndDateNotFormat: '',
      selectCourier: '',
      sumQuantity: 0,
      selectShipping: '',
      dataCourier: [],
      disabledToClickCreateShipping: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    // listShow () {
    //   if (this.search === '' && this.selectShipping === '') {
    //     console.log('here1')
    //     return this.dataOrder
    //   } else if (this.search !== '' && this.selectShipping === '') {
    //     console.log('here2')
    //     return this.dataOrder.filter(element => {
    //       return element.transaction_number.toLowerCase().includes(this.search.toLowerCase())
    //     })
    //   } else if (this.search === '' && this.selectShipping !== '') {
    //     console.log('here3')
    //     return this.dataOrder.filter(element => {
    //       return element.shipping_name.toLowerCase().includes(this.selectShipping.toLowerCase())
    //     })
    //   } else {
    //     console.log('here4')
    //     return this.dataOrder.filter(element => {
    //       return element.transaction_number.toLowerCase().includes(this.search.toLowerCase()) && element.shipping_name.toLowerCase().includes(this.selectShipping.toLowerCase())
    //     })
    //   }
    // },
    dateStart () {
      return this.formatDate(this.date)
    },
    dateEnd () {
      return this.formatDate(this.date1)
    }
  },
  watch: {
    selectShipping (val) {
      this.filterListOrder()
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/createShippingB2BMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createShippingB2B' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('SelectPath')
    this.$EventBus.$emit('checkAuthUser')
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
    await this.getOrderShipping()
    await this.getCourierList()
  },
  methods: {
    checkSearch () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.filterListOrder()
      }, 1000)
    },
    filterListOrder () {
      if (this.checkAllOrder === true) {
        this.checkAllOrder = false
      }
      this.getOrderShipping()
    },
    async getCourierList () {
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierListB2B', data)
      const response = await this.$store.state.ModuleManageShop.stateGetCourierListB2B
      if (response.status === 'success') {
        var listCourier = response.data
        var dataListCourier = []
        dataListCourier = [
          {
            courier_name: 'ขนส่งทั้งหมด',
            courier_code: ''
          },
          ...listCourier.map(e => ({
            courier_name: e.name,
            courier_code: e.type_id
          }))
        ]
        // for (let i = 0; i < listCourier.length; i++) {
        //   dataListCourier.push({
        //     courier_name: listCourier[i] === '' ? 'ขนส่งทั้งหมด' : listCourier[i],
        //     courier_code: listCourier[i]
        //   })
        // }
        this.dataCourier = await dataListCourier
        console.log(this.dataCourier, 'dataCourier')
      } else if (response.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        this.dataCourier = []
      }
    },
    proofSendOrder () {
      return this.dataOrder.every(e => e.isSelect === false)
    },
    numInBoxCheck (item) {
      if (item.length === 1 && item[0].quantity === 1) {
        return false
      } else {
        return true
      }
    },
    downloadPDF (e) {
      window.open(e)
    },
    countMember (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    countMemberOther (pagination) {
      this.showCountOrderOther = pagination.itemsLength
    },
    cancle () {
      if (this.MobileSize) {
        this.$router.push({ path: '/ManageShippingB2BMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageShippingB2B' }).catch(() => {})
      }
    },
    ShowExpand (data, orderId, value) {
      // var i = data.filter((x, i) => retux.prepare_order_id === orderId)
      var ii
      data.some((x, i) => { if (x.prepare_order_id === orderId) return (ii = i) })
      this.dataOrder = []
      this.dataOrder = data
      this.dataOrder[ii].expand_table = value
    },
    SelectAll (data, check) {
      // console.log(data, check)
      if (check === true) {
        this.dataOrder = []
        for (var i = 0; i < data.length; i++) {
          data[i].isSelect = true
        }
        this.dataOrder = data
      } else {
        this.dataOrder = []
        for (var j = 0; j < data.length; j++) {
          data[j].isSelect = false
        }
        this.dataOrder = data
      }
    },
    ShowDialogAll (dataAll) {
      this.orderSelectAll = []
      if (!this.checkAllOrder) {
        for (let i = 0; i < dataAll.length; i++) {
          if (dataAll[i].isSelect) {
            this.orderSelectAll.push(dataAll[i])
          }
        }
        this.dialogConfirmSelectOrderSome = true
      } else {
        for (let i = 0; i < dataAll.length; i++) {
          if (dataAll[i].isSelect) {
            this.orderSelectAll.push(dataAll[i])
          }
        }
        // this.orderSelectAll = dataAll
        this.dialogCallShippingAll = true
      }
    },
    confirmOrderCourierAll (dataAll) {
      this.dialogCallShippingAll = false
      this.confirmOrderSelectDataAll = []
      this.confirmOrderSelectDataAll = dataAll
      this.dialogConfirmSelectOrderAll = true
    },
    proofCutOrders (item) {
      // console.log(item, '9685968596')
      var a = true
      if (item.length === 1 && item[0].quantity > 1) {
        a = false
      } else if (item.length > 1) {
        a = false
      }
      return a
    },
    async comfirm (data, remark) {
      this.$store.commit('openLoader')
      this.disabledToClickCreateShipping = true
      // this.dialogConfirmSelectOrder = false
      var dataSend = {
        order_number: [data.prepare_order_id]
      }
      await this.$store.dispatch('ActionsCreateOrderIshipB2B', dataSend)
      const response = await this.$store.state.ModuleManageShop.stateCreateOrderIshipB2B
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.dialogConfirmSelectOrder = false
        this.disabledToClickCreateShipping = false
        this.$store.commit('closeLoader')
        this.remark = ''
        this.$swal.fire({ icon: 'success', text: response.message, showConfirmButton: false, timer: 1500 })
        if (this.MobileSize) {
          this.$router.push({ path: '/ManageShippingB2BMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageShippingB2B' }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
        this.disabledToClickCreateShipping = false
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
        }
      }
      this.$store.commit('closeLoader')
    },
    async comfirmAll (dataAll, remark) {
      this.$store.commit('openLoader')
      this.disabledToClickCreateShipping = true
      var prepareIDAll = []
      for (var i = 0; i < dataAll.length; i++) {
        prepareIDAll.push(dataAll[i].prepare_order_id)
      }
      var dataSend = {
        order_number: prepareIDAll
      }
      await this.$store.dispatch('ActionsCreateOrderIshipB2B', dataSend)
      const response = await this.$store.state.ModuleManageShop.stateCreateOrderIshipB2B
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.dialogConfirmSelectOrder = false
        this.dialogConfirmSelectOrderSome = false
        this.disabledToClickCreateShipping = false
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'success', text: response.message, showConfirmButton: false, timer: 1500 })
        if (this.MobileSize) {
          this.$router.push({ path: '/ManageShippingB2BMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageShippingB2B' }).catch(() => {})
        }
      } else {
        this.dialogConfirmSelectOrderSome = false
        this.disabledToClickCreateShipping = false
        this.$store.commit('closeLoader')
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
        }
      }
    },
    async confirmSendBySeller (order) {
      var orderNum = order.transaction_number
      var PreNum = order.prepare_order_id
      var dataSend = {
        order_number: orderNum,
        prepare_order_id: PreNum
      }
      await this.$store.dispatch('actionsSendOrderBySeller', dataSend)
      const response = await this.$store.state.NSGModuleIship.stateSendOrderBySeller
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.dialogSendBySeller = false
        await this.$swal.fire({ icon: 'success', text: 'ยืนยันการส่งพัสดุ', showConfirmButton: false, timer: 2500 })
        window.location.reload()
      } else {
        this.dialogSendBySeller = false
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          await this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2500 })
        }
      }
    },
    closeDialogConfirmSelectOrder () {
      this.confirmOrderSelectData = []
      this.remark = ''
      this.dialogConfirmSelectOrder = false
    },
    closeDialogConfirmSelectOrderAll () {
      this.confirmOrderSelectDataAll = []
      this.remark = ''
      this.dialogConfirmSelectOrderAll = false
    },
    closeDialogConfirmSelectOrderSome () {
      this.orderSelectAll = []
      this.remark = ''
      this.dialogConfirmSelectOrderSome = false
    },
    confirmOrderCourier (val) {
      // console.log('confirmOrderCourier', val)
      this.dialogCallShipping = false
      this.confirmOrderSelectData = []
      this.confirmOrderSelectData = val
      // console.log('this.confirmOrderSelectData', this.confirmOrderSelectData)
      this.dialogConfirmSelectOrder = true
    },
    ShowDialog (dataSelect) {
      // console.log('ShowDialog', dataSelect)
      this.orderSelect = []
      this.orderSelect = dataSelect
      this.dialogCallShipping = true
    },
    async ShowDialog2 (dataSelect) {
      await this.getData1(dataSelect.prepare_order_id)
      await this.getCourierPrice(this.dataprime)
      this.dialogSelectCourier = true
      // this.closeDialogSetting()
    },
    async ShowDialog3 (dataSelect) {
      this.orderSelect = []
      this.orderSelect = dataSelect
      this.dialogSendBySeller = true
      // this.closeDialogSetting()
    },
    async saveSelectCourier (courier) {
      await this.ChangeCourier(courier)
      this.dialogSelectCourier = false
      // console.log(this.selectDataCourier)
    },
    async ChangeCourier (courier) {
      if (this.dataprime.remark === null || this.dataprime.remark === undefined) {
        this.dataprime.remark = ''
      }
      var data = {
        prepare_order_id: [this.dataprime.prepare_order_id],
        order_number: this.dataprime.order_number,
        courier_code: courier,
        shop_id: 1,
        remark: this.dataprime.remark
      }
      // console.log(data, data.prepare_order_id, 'asdjjjjjd')
      await this.$store.dispatch('actionsChangeCourierNoTranspotCourier', data)
      const response = await this.$store.state.NSGModuleIship.stateChangeCourierNoTranspotCourier
      if (response.result === 'Success') {
        this.dialogSuccessChange = true
        setTimeout(() => {
          this.dialogSuccessChange = false
        }, 2000)
        this.selectCourier = ''
        this.dialogSelectCourier = false
        this.getOrderShipping(this.searchStartDateNotFormat, this.searchEndDateNotFormat)
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          if (response.message === 'เปลี่ยนขนส่งไม่สำเร็จ') {
            this.$swal.fire({ icon: 'error', title: response.message, text: response.data, showConfirmButton: false, timer: 3000 })
          } else {
            this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
          }
        }
        this.selectCourier = ''
        this.dialogSelectCourier = false
      }
    },
    async getCourierPrice (dataPri) {
      const courierCodeArray = dataPri.current_courier
      var data = {
        seller_shop_id: 1,
        src_zipcode: dataPri.src_zipcode,
        src_province: dataPri.src_province,
        src_amphure: dataPri.src_amphure,
        src_district: dataPri.src_district,
        dst_zipcode: dataPri.dstZipcode,
        dst_province: dataPri.dstProvince,
        dst_amphure: dataPri.dstAmphure,
        dst_district: dataPri.dstDistrict,
        weight: dataPri.weight,
        width: dataPri.width,
        length: dataPri.length,
        height: dataPri.height
      }
      await this.$store.dispatch('actionsGetCourierForBuyer', data)
      const response = await this.$store.state.NSGModuleIship.stateGetCourierForBuyer
      const resp = response.data
      const differenceCouriers = resp.couriers.filter((element) => element.courier_code !== courierCodeArray)
      // console.log(response)
      if (response.result === 'Success') {
        this.dataChangeCourier = []
        this.dataChangeCourier = differenceCouriers
        // console.log(this.dataChangeCourier, 'dataChangeCourier')
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    closeDialogSelect () {
      // if (this.selectCourier === '' || this.checkSelectCourier === false) {
      //   this.selectCourier = ''
      //   this.dialogSelectCourier = false
      // } else {
      //   this.dialogSelectCourier = false
      // }
      this.dialogSelectCourier = false
    },
    CutOrders (data, index) {
      localStorage.setItem('routeNameBefore', this.$route.name)
      this.$router.push(`/divideorder/${data.prepare_order_id}`).catch(() => {})
    },
    addBox () {
      this.indexAddBox = (this.indexAddBox + 1)
      this.formCut.product_split.push({
        indexBox: this.indexAddBox
      })
      this.addIndex(this.indexAddBox)
    },
    addIndex (index) {
      this.productInbox[[index]] = []
      // console.log('productInbox', this.productInbox)
    },
    async selectedOrder (index, indexId) {
      this.itemInboxAll = []
      await this.productInbox[[index]].push(this.selectOrder)
      // console.log(this.selectOrder, 'Order')
      await setTimeout(() => {
        this.removeObjectWithId(this.selectItemOrder, this.selectOrder.index)
      }, 100)
      this.itemInboxAll = this.productInbox
    },
    removeObjectWithId (arr, index) {
      const objWithIdIndex = arr.findIndex((obj) => obj.index === index)
      if (objWithIdIndex > -1) {
        arr.splice(objWithIdIndex, 1)
        return arr
      }
    },
    checkDisableBotton (selectItemLength, itemBoxLength) {
      if (selectItemLength === 0 && (itemBoxLength + 1 !== this.lengthProduct)) {
        return true
      } else if (itemBoxLength + 1 === this.lengthProduct) {
        return true
      } else {
        return false
      }
    },
    closeCutOrder () {
      this.selectOrder = []
      this.itemInboxAll = []
      this.productInbox = []
      this.formCut.product_split = []
      this.dialogCutOrder = false
      this.indexAddBox = 0
      this.getOrderShipping()
    },
    async confirmData () {
      // const src = 'https://panit.sdi.inet.co.th/backend/img/news71/description/64465e7657061.jpeg'
      // var ter = this.imageToBase64(src)
      // console.log('TER', ter)
      var split = this.productInbox.map(e => {
        return e.map(x => {
          return {
            product_id: x.product_id,
            product_name: x.product_name,
            main_sku: x.sku,
            sku: x.sku,
            product_image: x.product_image,
            quantity: x.quantity
          }
        })
      })
      var data = {
        order_number: this.formDetail.orders_number,
        prepare_order_id: this.prepareid,
        seller_shop_id: 1,
        product_split: split
      }
      // console.log(data)
      await this.$store.dispatch('actionsCutOrder', data)
      const response = await this.$store.state.NSGModuleIship.stateCutOrder
      if (response.result === 'Success') {
        this.$swal.fire({ icon: 'success', text: response.message, showConfirmButton: false, timer: 1500 })
        this.getOrderShipping()
        this.productInbox = []
        this.indexAddBox = 0
        this.dialogCutOrder = false
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
        }
        this.getOrderShipping()
        this.productInbox = []
        this.indexAddBox = 0
        this.dialogCutOrder = false
      }
      // console.log('Resddd', response)
    },
    setValueStartDate (val) {
      this.searchStartDateNotFormat = val
      // console.log(this.searchDateNotFormat)
      this.searchStartDate = this.formatDate(val)
      this.searchEndDate = ''
      this.searchEndDateNotFormat = ''
    },
    setValueEndDate (val) {
      this.searchEndDateNotFormat = val
      // console.log(this.searchDateNotFormat)
      this.searchEndDate = this.formatDate(val)
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    closeDialogStartdate () {
      this.dialogStartDate = false
      this.searchStartDate = ''
      this.searchStartDateNotFormat = ''
    },
    async closeDialogModogEnddate () {
      this.dialogEndDate = false
      this.searchEndDate = ''
      this.searchEndDateNotFormat = ''
      await this.getOrderShipping(this.searchStartDateNotFormat, this.searchEndDateNotFormat)
    },
    async getFilterDate (startDate, endDate) {
      await this.getOrderShipping(startDate, endDate)
    },
    async getOrderShipping (startDate, endDate) {
      this.$store.commit('openLoader')
      const data = {
        seller_shop_id: this.shopID,
        start_date: startDate,
        end_date: endDate,
        search_keyword: this.search,
        type_id: this.selectShipping
      }
      await this.$store.dispatch('ActionsGetlistCreateShippingB2B', data)
      const response = await this.$store.state.ModuleManageShop.stateGetlistCreateShippingB2B
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.dataOrder = response.data.map(e => {
          return {
            isSelect: false,
            transaction_number: e.order_number,
            order_delivery_number: e.order_delivery_number,
            buy_date: e.paid_datetime,
            address: {
              name: e.shipping_data.dst_data === undefined ? '-' : e.shipping_data.dst_data.dst_name,
              address: e.shipping_data.dst_data === undefined ? ' ' : e.shipping_data.dst_data.dst_address + ' แขวง/ตำบล ' + e.shipping_data.dst_data.dst_district_name + ' เขต/อำเภอ ' + e.shipping_data.dst_data.dst_city_name + ' ' + `${e.shipping_data.dst_data.dst_province_name === 'กรุงเทพมหานคร' ? e.shipping_data.dst_data.dst_province_name : 'จังหวัด ' + e.shipping_data.dst_data.dst_province_name}` + ' ' + e.shipping_data.dst_data.dst_postal_code + ' เบอร์โทรศัพท์ ' + e.shipping_data.dst_data.dst_phone
            },
            shipping_remark: e.message,
            prepare_order_id: e.order_delivery_number,
            shipping_name: e.shipping_data.tpl_name,
            courier_image: e.shipping_data.media_path,
            tracking_number: '',
            product_list: e.product_list,
            total_quantity: e.total_quantity
          }
        })
        // console.log(this.dataOrder, 'hello')
        for (var i = 0; i < this.dataOrder.length; i++) {
          this.dataOrder[i].product_list_not_expand = this.dataOrder[i].product_list.filter((item, index) => {
            return (index === 0)
          })
          if (this.dataOrder[i].product_promotion !== undefined) {
            this.dataOrder[i].product_promotion = []
            for (var j = 0; j < this.dataOrder[i].product_list.length; j++) {
              if (this.dataOrder[i].product_list[j].product_promotion !== undefined) {
                if (this.dataOrder[i].product_list[j].product_promotion.length !== 0) {
                  this.dataOrder[i].product_promotion.push(this.dataOrder[i].product_list[j].product_promotion[0])
                } else {
                  this.dataOrder[i].product_promotion = []
                }
              } else {
                this.dataOrder[i].product_promotion = []
              }
            }
          }
          this.dataOrder[i].expand_table = Boolean(false)
        }
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async getData1 (id) {
      this.$store.commit('openLoader')
      this.data1 = await []
      this.dataprime = await []
      this.NotToggle = await []
      const data = await {
        id: id,
        type: 'transports_prepare_order'
      }
      await this.$store.dispatch('actionsGetDaTa', data)
      const response = await this.$store.state.NSGModuleIship.stateDaTa
      // console.log(response.data.couriers)
      if (response.result === 'Success') {
        this.dataprime = response.data
        this.data1 = response.data.products
        // console.log(this.data1)
        this.NotToggle.push(response.data.products[0])
        // console.log(this.NotToggle)
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
      }
    },
    async getChangeCourier () {
      this.itemsShipping = await []
      const data = await {
        prepare_order_id: 1,
        order_number: 'EC231004000705',
        courier_code: '',
        shop_id: 1,
        remark: ''
      }
      await this.$store.dispatch('actionsGetCourier', data)
      const response = await this.$store.state.NSGModuleIship.stateGetCourier
      // console.log(response.data.couriers)
      if (response.result === 'Success') {
        this.dataCourier = await [...response.data.couriers]
        this.itemsShipping = await response.data.couriers
        var first = await {
          courier_code: 'all',
          courier_id: 0,
          courier_name: 'ขนส่งทั้งหมด',
          img_path: '',
          seller_shop_id: 1,
          status: true
        }
        await this.itemsShipping.splice(0, 0, first)
        // this.itemsShipping.push({
        //   courier_code: '',
        //   courier_id: 0,
        //   courier_name: 'ทั้งหมด',
        //   img_path: '',
        //   seller_shop_id: 1,
        //   status: true
        // })
        // for (let i = 0; i < response.data.couriers.length; i++) {
        //   this.itemsShipping.push(response.data.couriers[i])
        // }
        // console.log('this.itemsShipping', this.itemsShipping)
        if (this.dataCourier.every((key) => key.status === false)) {
          this.disableButton = await true
        } else {
          this.disableButton = await false
        }
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => { })
        } else {
          await this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      }
    }
    // async imageToBase64 (img) {
    //   var canvas = document.createElement('canvas')
    //   canvas.width = img.width
    //   canvas.height = img.height
    //   var ctx = canvas.getContext('2d')
    //   ctx.drawImage(img, 0, 0)
    //   var dataURL = canvas.toDataURL('image/png')
    //   return dataURL.replace(/^data:image\/?[A-z]*;base64,/)
    // }
  }
}
</script>

<style scoped>
.headerPromotion /deep/ .v-data-table__wrapper > table > thead > tr:last-child > th {
  color: #6f8fb9 !important;
  border-bottom: none !important;
  height: 0px !important;
}
.background_color {
  background-color: #FFFFFF;
  min-height: 1000px;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DAF1E9;
  border-radius: 8px;
}
ul li::before {
  content: "\2022";
  color: #52C41A;
  font-weight: bold;
  display: inline-block;
  width: 1em;
  margin-left: -1em;
}
::v-deep textarea{ resize: none !important; }
</style>

<style lang="scss" scoped>
// ::v-deep tbody {
//   tr:nth-last-child(1) {
//     background-color: #F5FBFF;
//   }
// }
</style>

<style>
@media (min-width: 1420px) {
  .fontres {
    font-size: 14px !important;
    margin-right: 8px !important;
  }
  .my-checkbox .v-label {
    font-size: 16px !important;
  }
}
@media (max-width: 1419px) and (min-width: 1325px) {
  .fontres {
    font-size: 14px !important;
    margin-right: 8px !important;
  }
  .disableIcon {
    display: none !important;
  }
  .my-checkbox .v-label {
    font-size: 14px !important;
  }
}
@media (max-width: 1325px) and (min-width: 1264px) {
  .fontres {
    font-size: 13px !important;
    font-weight: 600 !important;
    margin-right: 4px !important;
  }
  .my-checkbox .v-label {
    font-size: 13px !important;
    font-weight: 600 !important;
  }
  .disableIcon {
    display: none !important;
  }
  .stepAside {
    padding-right: 4px !important;
  }
}
@media (max-width: 1263px) and (min-width: 740px){
  .fontres {
    font-size: 13px !important;
    font-weight: 600 !important;
  }
  .my-checkbox .v-label {
    font-size: 16px !important;
    font-weight: 600 !important;
  }
  .disableIcon {
    display: none !important;
  }
}
</style>
