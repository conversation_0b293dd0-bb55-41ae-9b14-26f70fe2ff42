<template>
  <div>
     <v-row>
        <v-col cols="12" md="6" class="pl-8">
          <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">แดชบอร์ด</v-card-title>
            <v-card-title style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333; margin-left: -35px;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>แดชบอร์ด</v-card-title>
      </v-col>
       <v-col cols="12" md="6">
      </v-col>
      </v-row>
    <div class="cs d-flex">
      <v-row>
          <v-col cols="12" md="5" align="right" v-show="numChange === '1'">
            <div style="padding-top: 1.6em;font-size: 12px;">เริ่มต้น - สิ้นสุด</div>
          </v-col>
      <v-col
        cols="12"
        md="2"
        lg="2"
        v-show="numChange === '1'"
      >
        <v-menu
          ref="menu1"
          v-model="menu1"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          max-width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormatted"
              label=""
              hint=""
              persistent-hint
              append-icon="mdi-calendar"
              dense
              outlined
              class="mt-2"
              v-bind="attrs"
              v-on="on"
            ></v-text-field>
          </template>
          <v-date-picker
            v-model="dateStart"
            no-title
            :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            @input="menu1 = false"
          ></v-date-picker>
        </v-menu>
      </v-col>

      <v-col
        cols="12"
        md="2"
        lg="2"
        v-show="numChange === '1'"
      >
        <v-menu
          v-model="menu2"
          :close-on-content-click="false"
          transition="scale-transition"
          offset-y
          max-width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormatted2"
              label=""
              hint=""
              persistent-hint
              append-icon="mdi-calendar"
              dense
              outlined
              v-bind="attrs"
              v-on="on"
              class="mt-2"
            ></v-text-field>
          </template>
          <v-date-picker
            v-model="dateEnd"
            no-title
            :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
            :min="dateStart"
            @input="menu2 = false"
            @change="filterDate"
          ></v-date-picker>
        </v-menu>
      </v-col>
      <v-col cols="12" md="3">
         <v-select
        v-show="numChange === '1'"
         style="position: 'absolute'; elevation: 50; zIndex:50 ;"
          v-model="weekday"
          :items="weekdays"
          item-value="id"
          item-text="name"
          return-object
          @change="filterOther"
          dense
          outlined
          hide-details
          label="เลือก"
          class="ma-2"
        ></v-select>
      </v-col>
    </v-row>
    <v-row class="mt-1" v-show="numChange === '0'">
      <v-col
      cols="12"
      md="6"
        lg="6"
      >
      </v-col>
      <v-col cols="12"
        md="6"
        lg="6">
        <v-select
          v-model="weekday2"
          :items="weekdays2"
          item-value="id"
          item-text="name"
          return-object
          @change="filltWeek2"
          dense
          outlined
          hide-details
          label="เลือก"
          class="ma-2 pl-10"
        ></v-select>
      </v-col>
    </v-row>
    </div>
  </div>
</template>
<script>
import dataTest from '../library/dataTest.json'
// import eventBus from '@/components/eventBus'
export default {
  data () {
    return {
      weekday: {},
      weekday2: {},
      weekdays: [
        {
          id: 2, name: '1 เดือน'
        },
        {
          id: 3, name: '6 เดือน'
        },
        {
          id: 4, name: '1 ปี'
        },
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      weekdays2: [
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      mode: '',
      type: [],
      types: [],
      modes: [],
      dataMain: dataTest,
      dateStart: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateEnd: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateFormatted2: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      menu1: false,
      menu2: false,
      numChange: '1'
    }
  },
  created () {
    this.$EventBus.$on('numChangeData', this.numChangeData)
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
  },
  destroyed () {
    this.$EventBus.$off('numChangeData')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    dateStart (val) {
      this.dateFormatted = this.formatDate(this.dateStart)
    },
    dateEnd (val) {
      this.dateFormatted2 = this.formatDate(this.dateEnd)
    }
  },
  methods: {
    backtoUserMenu () {
      this.$router.push({ path: '/sellerMobile' }).catch(() => {})
    },
    async numChangeData () {
      this.numChange = '0'
    },
    async filterDate () {
      var preStart = await this.afterDate(this.formatDate(this.dateStart))
      var preEnd = await this.afterDate(this.formatDate(this.dateEnd))
      // const start = await this.timeStamp(preStart)
      // const end = await this.timeStamp(preEnd)
      // const start = '31-2-2022'
      // const end = '31-2-2022'
      // const dataFilter = this.$store.getters.filterDate(start, end)
      // this.$store.state.ModuleShop.stateDashboard = []
      // this.$store.state.ModuleShop.stateDashboard = {
      //   data: dataFilter
      // }
      // const GetKey = dataFilter.map(name => {
      //   return {
      //     buyer_name: name.buyer_name
      //   }
      // })
      const data = await {
        start_date: preStart,
        end_date: preEnd,
        search_type: 'date'
      }
      // console.log('Start End', data)
      await this.$store.dispatch('actionsDashboard', data)
      var response = await []
      response = await this.$store.state.ModuleShop.stateDashboard
      var dataFilter = await []
      dataFilter = await response.data
      // console.log('dataFilter++', dataFilter)
      this.$store.state.ModuleShop.stateMax = await Math.max.apply(Math, dataFilter.map(o => o.value))
      // const val = {
      //   name: Object.values(GetKey),
      //   data: dataFilter.map(e => { return e.value })
      // }
      // this.$store.state.ModuleShop.setKey = []
      // for (const i in dataFilter) {
      //   const val = {
      //     name: dataFilter[i].buyer_name,
      //     data: dataFilter[i].value
      //   }
      //   this.$store.state.ModuleShop.setKey.push(dataFilter[i].buyer_name)
      //   this.$store.state.ModuleShop.stateSeries.push(val)
      // }
      // *****************************************************************
      // var grouped = await {}
      // for (const i in dataFilter) {
      //   grouped[dataFilter[i].buyer_name] = await grouped[dataFilter[i].buyer_name] || []
      //   await grouped[dataFilter[i].buyer_name].push(dataFilter[i].value)
      // }
      // const statusObj = await []
      // for (const [k, v] of Object.entries(grouped)) {
      //   await statusObj.push({
      //     name: k,
      //     data: v
      //   })
      // }
      // for (const x in statusObj) {
      //   await this.$store.state.ModuleShop.stateColor.push(this.randomColor())
      //   console.log(x)
      // }
      this.$store.state.ModuleShop.stateSeries = []
      // this.$store.state.ModuleShop.stateSeries = statusObj
      this.$EventBus.$emit('filterBy')
      this.$EventBus.$emit('appendData')
      // console.log('filterDate**')
    },
    async filterOther () {
      const today = await new Date()
      // console.log('SDE', this.weekday.id)
      if (this.weekday.id === 2) {
        const data = await {
          text: 'one_month',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 3) {
        var today2 = await new Date()
        var end = await ''
        if (((today2.getMonth() + 1) - 6) < 0) {
          await today2.setMonth((today2.getMonth() + 1) - 6)
          end = await today2
        } else {
          end = await today.setMonth(today.getMonth() - 6)
        }
        const data = await {
          text: 'six_months',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 4) {
        var today3 = await new Date()
        var end3 = await ''
        if (((today3.getMonth() + 1) - 12) < 0) {
          await today3.setMonth((today3.getMonth()) - 12)
          end3 = await today3
        } else {
          end3 = await today.setMonth(today.getMonth() - 12)
        }
        const data = await {
          text: 'one_year',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end3).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        // const data = await {
        //   text: 'one_year',
        //   start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
        //   end: new Date(today.setFullYear((today.getFullYear() + 1) - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        // }
        await this.EventBusCall(data)
      } else {
        const data = await {
          text: 'all'
        }
        await this.EventBusCall(data)
        await this.filterOther2()
      }
      // console.log('filterOther', new Date().toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }))
    },
    async filltWeek2 () {
      // console.log('filltWeek2', this.weekday2.id)
      if (this.weekday2.id === 5) {
        const data = await {
          text: 'all'
        }
        this.numChange = await '1'
        await this.EventBusCall(data)
        await this.filterOther2()
      }
    },
    async EventBusCall (data) {
      await this.$EventBus.$emit('updateData', data)
    },
    async filterOther2 () {
      const num = await {
        number: '1'
      }
      await this.$EventBus.$emit('FuntionChange2', num)
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    parseDate (date) {
      // console.log('parseDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${day}-${month}-${year}`
    },
    afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${year}-${month}-${day}`
    },
    timeStamp (Dates) {
      var myDate = Dates.split('-')
      var newDate = new Date(myDate[2], myDate[1] - 1, myDate[0])
      return newDate.getTime()
    },
    randomColor () {
      const randomColor = Math.floor(Math.random() * 16777215).toString(16)
      return `#${randomColor}`
    }
  }
}
</script>

<style>
 @media only screen and (max-width: 800px) {
  .cs {
    display: none;
  }
}
</style>
