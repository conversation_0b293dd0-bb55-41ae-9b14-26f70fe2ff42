<template>
  <div class="text-center">
    <v-dialog v-model="openDialog" :style="MobileSize ? 'z-index: 16000004' : ''" width="800" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>เพิ่มสินค้า</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="CloseModal()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container  grid-list-xs>
           <v-text-field
           v-model="search"
            outlined
            dense
            label="ค้นหา"
            append-icon="mdi-magnify"
          ></v-text-field>
         <v-data-table
            v-model="selected"
            :search="search"
            :headers="headers"
            :items="productOfShop"
            @toggle-select-all="selectAll"
            item-key="indexItem"
            show-select
            class="elevation-1"
            checkbox-color="#27AB9C"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          >
          <template v-slot:[`item.product_image`]="{ item }">
            <v-img
              max-height="90"
              max-width="100"
              height="90"
              width="100"
              :src="item.product_image"
              contain
              v-if="item.product_image !== null"
            ></v-img>
            <v-img
              max-height="90"
              max-width="100"
              height="90"
              width="100"
              src="@/assets/NoImage.png"
              contain
              v-else
            ></v-img>
          </template>
          <template v-slot:[`item.quantity`]="{  item, index }">
            <v-col cols="12" class="py-0 px-0">
              <v-btn elevation="1" x-small color="#27AB9C" :min-height="24" :max-width="24" class="mx-1"  @click="minusQuantity(index, item.quantity)" :disabled="item.quantity === 1 ? true : false">
                <v-icon x-small color="white">mdi-minus</v-icon>
              </v-btn>
              <input alige="center" class="AddNumberProduct" readonly :value="item.quantity" size="4" width="10" type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"/>
              <v-btn elevation="1" x-small color="#27AB9C" :min-height="24" :max-width="24" class="mx-1" @click="plusQuantity(index, item.quantity)" >
                <v-icon x-small color="white">mdi-plus</v-icon>
              </v-btn>
            </v-col>
          </template>
          <template v-slot:[`item.product_name`]="{ item = {} }">
            {{item.product_name}}<br>
            <div v-if="item.have_attribute === 'yes'" style="font-size: 12px; margin-top: 6px; margin-bottom: 4px;">
              <span v-if="item.attribute_1_key !== null">{{item.attribute_1_key}}&nbsp;:&nbsp;&nbsp;{{item.attribute_priority_1}}</span><br>
              <span v-if="item.attribute_2_key !== null">{{item.attribute_2_key}}&nbsp;:&nbsp;&nbsp;{{item.attribute_priority_2}}</span>
            </div>
          </template>
          <template v-slot:[`item.revenue_default`]="{ item: { revenue_default } = {} }">
            {{ Number(revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
          </template>
          <v-alert slot="no-results" :value="true">
            การค้นหาของคุณ "{{ search }}" ไม่พบผลลัพธ์
          </v-alert>
        </v-data-table>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="CloseModal()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="enterSelect">ส่งคำขอ</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import draggable from 'vuedraggable'
// import { Decode } from '@/services'
export default {
  data () {
    return {
      openDialog: false,
      data: [],
      lazy: false,
      disabledForm: true,
      stateopenModalPurchaseOrder: false,
      shop_name: '',
      productData: {},
      detailProduct: {
        order_number: '',
        product_image: [],
        returnProductDescription: '',
        email: '',
        returnProductSelectReasonReason: ''
      },
      DataImage: [],
      returnProductItems: [
        { text: 'ฉันไม่ได้รับสินค้าสำหรับคำสั่งซื้อนี้', value: 'ฉันไม่ได้รับสินค้าสำหรับคำสั่งซื้อนี้' },
        { text: 'ได้รับสินค้าที่ไม่สมบูรณ์ (ชิ้นส่วนบางชิ้นหายไป)', value: 'ได้รับสินค้าที่ไม่สมบูรณ์ (ชิ้นส่วนบางชิ้นหายไป)' },
        { text: 'ได้รับสินค้าที่ไม่ถูกต้องตามที่ได้สั่ง เช่น ไซส์ผิด สีผิด สินค้าผิด', value: 'ได้รับสินค้าที่ไม่ถูกต้องตามที่ได้สั่ง เช่น ไซส์ผิด สีผิด สินค้าผิด' },
        { text: 'ได้รับสินค้าสภาพไม่ดี', value: 'ได้รับสินค้าสภาพไม่ดี' },
        { text: 'ได้รับสินค้าที่การทำงานไม่สมบูรณ์', value: 'ได้รับสินค้าที่การทำงานไม่สมบูรณ์' }
      ],
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        email: [
          // v => !!v || 'กรุณาระบุอีเมล',
          v => /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+([.]?[a-zA-Z])*(\.[a-zA-Z]{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ]
        // first_name: [
        //   v => !!v || 'กรุณากรอกชื่อจริงผู้รับ'
        // ]
      },
      singleSelect: false,
      search: '',
      selected: [],
      headers: [
        {
          text: 'รายการสินค้า',
          align: 'start',
          sortable: false,
          value: 'product_image',
          width: '50px'
        },
        { text: '', value: 'product_name', width: '50px' },
        { text: 'ราคาต่อชิ้น', value: 'revenue_default', width: '100px' },
        { text: 'จำนวน', value: 'quantity', width: '100px' }
      ]
    }
  },
  watch: {
  },
  mounted () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    productOfShop () {
      var data = this.$store.state.ModuleOrder.stateSearchProduct.query_result.map((e, i) => {
        // console.log(e.have_attribute === 'yes')
        var q = []
        if (e.have_attribute === 'yes') {
          q = this.$store.state.ModuleOrder.stateSearchProduct.query_result.filter(x => x.product_id === e.product_id && x.product_attribute_detail.product_attribute_id === e.attribute_id)
        } else if (e.have_attribute === 'no') {
          q = this.$store.state.ModuleOrder.stateSearchProduct.query_result.filter(x => x.product_id === e.product_id)
        }
        if (q.length === 0) {
          return {
            indexItem: i,
            attribute_1_key: e.attribute_1_key,
            attribute_2_key: e.attribute_2_key,
            attribute_id: e.attribute_id,
            attribute_priority_1: e.attribute_priority_1,
            attribute_priority_2: e.attribute_priority_2,
            description: e.description,
            discount: e.discount,
            fake_price: e.fake_price,
            have_attribute: e.have_attribute,
            product_name: e.name,
            product_id: e.id,
            product_ratio: e.product_ratio,
            product_image: e.images_URL[0],
            product_sku: e.product_sku,
            product_status: e.product_status,
            real_price: e.real_price,
            stock_count: e.stock_count,
            quantity: 1,
            actual_cost: e.actual_cost,
            actual_stock: e.actual_stock,
            category_data: e.category_data,
            manufacturer_data: e.manufacturer_data,
            cost_unit: e.cost_unit,
            eng_cost: e.eng_cost,
            external: e.external,
            external_jv: e.external_jv,
            internal: e.internal,
            inventory_code: e.inventory_code,
            item_code: e.item_code,
            main_sku: e.main_sku,
            revenue_default: e.revenue_price,
            sku: e.sku,
            supplier_data: e.supplier_data,
            unit_type: e.unit_type,
            vat_type: e.vat_type
          }
        }
      })
      return data.filter(e => e !== undefined)
    }
  },
  methods: {
    open () {
      this.openDialog = true
    },
    CloseModal () {
      this.openDialog = false
      // console.log(this.productOfShop)
      for (var i = 0; i < this.productOfShop.length; i++) {
        this.productOfShop[i].quantity = 1
      }
      this.search = ''
      this.selected = []
    },
    selectAll (props) {
      if (this.search === '') {
        if (this.selected.length !== this.productOfShop.length) {
          this.selected = []
          const self = this
          this.productOfShop.forEach(item => {
            self.selected.push(item)
          })
        } else {
          this.selected = []
        }
      }
    },
    plusQuantity (index, quantity) {
      if (quantity > 0) {
        this.productOfShop[index].quantity = parseInt(quantity) + 1
        // this.$store.state.ModuleAdminManage.ListProductOfShop[index].quantity = parseInt(quantity) + 1
        // this.selected[index].quantity = parseInt(quantity) + 1
      }
      // this.productOfShop()
    },
    minusQuantity (index, quantity) {
      if (quantity > 0 && quantity !== 0) {
        this.productOfShop[index].quantity = parseInt(quantity) - 1
        // this.$store.state.ModuleAdminManage.ListProductOfShop[index].quantity = parseInt(quantity) - 1
        // this.selected[index].quantity = parseInt(quantity) - 1
      }
      // this.productOfShop()
    },
    async selectData () {
      // console.log('SelectData', this.$store.state.ModuleAdminManage.ListProductOfShop)
    },
    async enterSelect () {
      // var Index = await parseInt(this.$store.state.ModuleShop.stateIndexAddProduct)
      // const porductId = this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.map(e => { return e.product_id })
      // const attributeId = this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.map(e => { return e.product_attribute_detail.product_attribute_id })
      // co
      // console.log('porductId', porductId, 'attributeId', attributeId)
      await this.selected.map((val) => {
        const obj = {
          discount_percent: val.discount,
          have_attribute: val.have_attribute,
          key_1_value: val.attribute_1_key,
          key_2_value: val.attribute_2_key,
          max_per_order: '',
          min_per_order: '',
          net_price: '',
          price: val.real_price,
          price_discount: '',
          price_vat_with_gp: '',
          product_attribute_detail: {
            attribute_priority_1: val.attribute_priority_1,
            attribute_priority_2: val.attribute_priority_2,
            product_attribute_id: val.attribute_id
          },
          product_id: val.product_id,
          product_image: val.product_image,
          product_name: val.product_name,
          product_price_option: '',
          product_status: val.product_status,
          quantity: val.quantity,
          service_type: '',
          short_description: '',
          sku: val.sku,
          status: '',
          stock: val.stock_count,
          stock_check_status: '',
          use_tier: '',
          use_to_return_stock: {
            actual_stock: 0,
            effective_stock: 0,
            inventory_actual_stock: 0,
            inventory_effective_stock: 0
          },
          use_type_price: '',
          volumn: {
            height: '',
            length: '',
            weight: '',
            width: ''
          },
          product_sku: val.product_sku,
          real_price: val.real_price,
          stock_count: val.stock_count,
          actual_cost: val.actual_cost,
          actual_stock: val.actual_stock,
          category_data: val.category_data,
          manufacturer_data: val.manufacturer_data,
          cost_unit: val.cost_unit,
          eng_cost: val.eng_cost,
          external: val.external,
          external_jv: val.external_jv,
          internal: val.internal,
          inventory_code: val.inventory_code,
          item_code: val.item_code,
          main_sku: val.sku,
          revenue_default: parseFloat(val.revenue_default),
          revenue_price: parseFloat(val.revenue_default),
          supplier_data: val.supplier_data,
          unit_type: val.unit_type,
          vat_type: val.vat_type,
          revenue_vat: val.quantity * val.revenue_default
        }
        // const x = this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list
        // this.$store.state.ModuleAdminManage.QuotationformData.qu_detail.product_list.push(obj)
        this.$store.state.ModuleOrder.stateOrderListSellerDetail.data.data_list[0].product_list.push(obj)
        return obj
      })
      this.search = ''
      this.selected = await []
      // console.log(this.$store.state.ModuleAdminManage.stateDetailOrderPurchaser.data.data_list[0].product_list)
      this.$store.state.ModuleOrder.stateopenModalPurchaseOrder = await false
      this.$EventBus.$emit('clearData')
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
#upload-video {
  margin-top: 20px;
  border-radius: 0px;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
.title-product {
  font-size: 14px;
}
.detail-product {
  font-size: 14px;
}
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.AddNumberProduct {
  height: 30px;
  width: 60px;
  box-shadow: inset 0 1px 3px 0 rgba(232, 232, 232, 0.5);
  background-color: #ffffff;
  text-align: center;
}
</style>
