<template>
  <div class="d-flex justify-center">
    <v-container :class="MobileSize || IpadSize || IpadProSize ? '' : 'ma-16 mt-3'" class="d-flex">
      <v-card style="background-color: #ffffff; border-radius: 8px;" :class="MobileSize ? 'pa-3' : 'pa-6 pt-6'" class="d-flex justify-center" elevation="0" outlined>
        <v-card-text class="pa-0" style="color: #333333;">
          <v-form ref="formOne" :lazy-validation="lazyOne">
            <v-row class="mb-3">
              <v-col cols="12">
                <span style="font-weight: bold;" :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'">ลงทะเบียนสนใจเปิดร้านค้าออนไลน์ <br v-if="MobileSize"/> Nex Gen Commerce</span>
              </v-col>
              <!-- <v-col cols="3" class="d-flex justify-end">
                <v-btn v-if="isEditing" rounded @click="EditRequestShopData" color="#27ab9c">
                  <span class="white--text">แก้ไข</span>
                </v-btn>
              </v-col> -->
            </v-row>
            <v-row dense>
              <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
                <span class="mr-3" style="font-size: 16px; font-weight: bold;">ประเภทธุรกิจ</span>
                <v-radio-group
                  v-model="businessType"
                  row
                  class="mt-0"
                  hide-details
                  :rules="Rules.empty"
                >
                  <v-radio
                      label="บุคคลธรรมดา"
                      value="บุคคลธรรมดา"
                  ></v-radio>
                  <v-radio
                      label="นิติบุคคล"
                      value="นิติบุคคล"
                  ></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
                <span class="mr-3" style="font-size: 16px; font-weight: bold;">ประเภทย่อยร้านค้า</span>
                <v-radio-group
                  v-model="shopType"
                  row
                  class="mt-0"
                  hide-details
                  :rules="Rules.empty"
                >
                  <v-radio
                      label="ร้านค้าทั่วไป"
                      value="ร้านค้าทั่วไป"
                  ></v-radio>
                  <v-radio
                      label="OTOP"
                      value="OTOP"
                  ></v-radio>
                  <v-radio
                      label="วิสาหกิจชุมชน"
                      value="วิสาหกิจชุมชน"
                  ></v-radio>
                  <v-radio
                      label="วิสาหกิจเพื่อสังคม"
                      value="วิสาหกิจเพื่อสังคม"
                  ></v-radio>
                  <v-radio
                      label="ประชารัฐรักสามัคคี"
                      value="ประชารัฐรักสามัคคี"
                  ></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
                <span class="mr-3" style="font-size: 16px; font-weight: bold;">รูปแบบการเปิดร้านที่สนใจ</span>
                <v-radio-group
                  v-model="openType"
                  row
                  class="mt-0"
                  hide-details
                  :rules="Rules.empty"
                >
                  <v-radio
                      label="ร้านค้าซื้อขายปกติ"
                      value="ร้านค้าซื้อขายปกติ"
                  ></v-radio>
                  <v-radio
                      label="ร้านค้า + e-Tax invoice & e-Receipt"
                      value="ร้านค้า + e-Tax invoice & e-Receipt"
                  ></v-radio>
                  <v-radio
                      label="ร้านค้า + OMS"
                      value="ร้านค้า + OMS"
                  ></v-radio>
                  <v-radio
                      label="ร้านค้า + ERP"
                      value="ร้านค้า + ERP"
                  ></v-radio>
                </v-radio-group>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex pb-0'">
                <span style="font-size: 16px; font-weight: bold;" class="mr-3 mt-2">ช่องทางที่ทำให้ท่านสนใจเปิดร้านกับ Nex Gen Commerce</span>
                <v-row :class="MobileSize || IpadSize || IpadProSize ? 'pt-5' : ''">
                  <v-col cols="12" md="6" sm="6">
                    <v-select
                      v-model="selectInterestChannel"
                      :items="itemsInterestChannel"
                      outlined
                      dense
                      :rules="Rules.empty"
                      placeholder="เลือกช่องทาง"
                      style="border-radius: 8px;"
                      @change="selectedSocialMedia = ''; reasonChanel = '';"
                      class="setCustomSelect"
                    >
                    </v-select>
                  </v-col>
                  <v-col cols="12" md="6" sm="6" v-if="selectInterestChannel === 'Social Media'">
                    <v-select
                      v-model="selectedSocialMedia"
                      :items="itemSocial"
                      outlined
                      dense
                      :rules="Rules.empty"
                      placeholder="เลือกช่องทาง Social Media"
                      style="border-radius: 8px;"
                    ></v-select>
                  </v-col>
                  <v-col cols="12" md="6" sm="6" v-if="selectInterestChannel === 'อื่นๆ (โปรดระบุ)...'">
                    <v-text-field
                      v-model="reasonChanel"
                      outlined
                      dense
                      placeholder="ระบุช่องทาง"
                      :rules="Rules.emptyText_firstBlank"
                      style="border-radius: 8px;"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row dense class="mt-5">
              <v-col cols="12" :class="IpadSize ? 'pb-5' : ''">
                <span style="font-size: 18px; font-weight: bold;">ข้อมูลร้านค้า</span>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ชื่อร้านค้า </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="shopName"
                      outlined
                      dense
                      :rules="Rules.firstname"
                      placeholder="ระบุชื่อร้านค้า"
                      style="border-radius: 8px;"
                      @keypress="CheckSpacebarOne($event)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ประเภทสินค้า </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="productType"
                      outlined
                      dense
                      :rules="Rules.productType"
                      placeholder="ระบุประเภทสินค้า"
                      style="border-radius: 8px;"
                      @keypress="CheckSpacebarOne($event)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="12">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">หมายเลขประจำตัวผู้เสียภาษี </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="TaxID"
                      outlined
                      dense
                      placeholder="ระบุหมายเลขประจำตัวผู้เสียภาษี"
                      style="border-radius: 8px;"
                      :rules="Rules.TaxID"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 13)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <!-- <v-col cols="12" md="6" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ชื่อ </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="firstName"
                      outlined
                      dense
                      :rules="Rules.firstname"
                      placeholder="ระบุชื่อ"
                      style="border-radius: 8px;"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">นามสกุล </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="lastName"
                      outlined
                      dense
                      :rules="Rules.lastname"
                      placeholder="ระบุนามสกุล"
                      style="border-radius: 8px;"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col> -->
              <!-- <v-col cols="12" md="6" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">หมายเลขโทรศัพท์ร้านค้า </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="phoneNumber"
                      outlined
                      dense
                      :rules="Rules.tel"
                      placeholder="ระบุหมายเลขโทรศัพท์ร้านค้า"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 10)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">อีเมลร้านค้า </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="email"
                      outlined
                      dense
                      :rules="Rules.email"
                      placeholder="ระบุอีเมลร้านค้า"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^a-zA-Z0-9@_.]/g, '')"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">รายละเอียดที่อยู่ร้านค้า (เช่น อาคาร ตึก ฯ) </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="detail"
                      outlined
                      dense
                      :rules="Rules.address"
                      placeholder="ระบุรายละเอียดที่อยู่ร้านค้า (เช่น อาคาร ตึก ฯ)"
                      style="border-radius: 8px;"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">เลขที่ </span><span style="color: red">*</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="houseNo"
                      outlined
                      dense
                      :rules="Rules.house_Num"
                      placeholder="เลขที่"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^0-9/-]/g, '')"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ตำบล/แขวง<span style="color: red;"> *</span></span>
                  </v-col>
                  <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-12' : ''">
                    <addressinput-subdistrict label="" style="border-radius: 8px !important;" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล"/>
                    <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="4" sm="4">
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">อำเภอ/เขต</span><span style="color: red;"> *</span>
                  </v-col>
                  <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-12' : ''">
                    <addressinput-district label="" style="border-radius: 8px !important;" v-model="districtText" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ"/>
                    <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">จังหวัด</span><span style="color: red;"> *</span>
                  </v-col>
                  <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-12' : ''">
                    <addressinput-province label="" style="border-radius: 8px !important;" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="provinceText" placeholder="ระบุจังหวัด"/>
                    <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="6">
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                  </v-col>
                  <v-col cols="12" class="py-0" :class="MobileSize ? 'mb-12' : ''">
                    <addressinput-zipcode numbered style="border-radius: 8px !important;" label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์"/>
                    <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                  </v-col>
                </v-row>
              </v-col> -->
            </v-row>
            <v-row :class="IpadSize ? 'pt-5' : ''">
              <v-col>
                <span style="font-size: 16px; font-weight: bold;">ช่องทางที่ต้องการให้ทีมงานติดต่อประสานงาน</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ชื่อผู้ติดต่อ </span><span style="color: red;"> *</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact1Name"
                      outlined
                      dense
                      placeholder="ชื่อผู้ติดต่อ"
                      style="border-radius: 8px;"
                      :rules="Rules.emptyText"
                      oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"
                      @keypress="CheckSpacebarOne($event)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">หมายเลขโทรศัพท์ </span><span style="color: red;"> *</span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact1Tel"
                      outlined
                      dense
                      placeholder="หมายเลขโทรศัพท์"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 10)"
                      :rules="Rules.tel"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">Line ID </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact1LineId"
                      outlined
                      dense
                      placeholder="Line ID"
                      style="border-radius: 8px;"
                      @keypress="CheckSpacebarOne($event)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">อีเมล </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact1Email"
                      outlined
                      dense
                      placeholder="ระบุอีเมล"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^a-zA-Z0-9@_.]/g, '')"
                      :rules="Rules.blankEmail"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row>
              <v-col>
                <span style="font-size: 16px; font-weight: bold;">ช่องทางที่ต้องการให้ทีมงานติดต่อประสานงานเพิ่มเติม</span>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ชื่อผู้ติดต่อ </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact2Name"
                      outlined
                      dense
                      placeholder="ชื่อผู้ติดต่อ"
                      style="border-radius: 8px;"
                      :rules="Rules.firstName"
                      oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"
                      @keypress="CheckSpacebarOne($event)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">หมายเลขโทรศัพท์ </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact2Tel"
                      outlined
                      dense
                      placeholder="หมายเลขโทรศัพท์"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 10)"
                      :rules="Rules.contactTel"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">Line ID </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact2LineId"
                      outlined
                      dense
                      placeholder="Line ID"
                      style="border-radius: 8px;"
                      @keypress="CheckSpacebarOne($event)"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="3" sm="3">
                <v-row dense>
                  <v-col cols="12">
                    <span style="font-size: 16px;">อีเมล </span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field
                      v-model="contact2Email"
                      outlined
                      dense
                      placeholder="ระบุอีเมล"
                      style="border-radius: 8px;"
                      oninput="this.value = this.value.replace(/[^a-zA-Z0-9@_.]/g, '')"
                      :rules="Rules.blankEmail"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
            <v-row>
              <v-col class="d-flex justify-end">
                <v-btn rounded outlined @click="cancleEditRequestShopData" color="#27ab9c">
                  <span>ยกเลิก</span>
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded outlined @click="clearEditRequestShopData" color="#27ab9c" class="mr-2">
                  <span>ล้างค่า</span>
                </v-btn>
                <v-btn rounded @click="SentRequestShopData" color="#27ab9c">
                  <span class="white--text">บันทึก</span>
                </v-btn>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
      </v-card>
      <v-dialog v-model="modalAwaitCancelOrder" width="450" persistent content-class="elevation-0">
        <v-card style="background: #FFFFFF; border-radius: 24px; overflow: hidden;">
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="cancleConfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
          <v-row>
            <v-col class="d-flex justify-center">
              <img style="height: 200px;" :src="require('@/assets/ICON/iconSuccess.png')"/>
            </v-col>
          </v-row>
          <v-container>
            <v-card-text style="text-align: center;" class="px-0">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลงทะเบียนสำเร็จ</b></p>
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">เราได้รับข้อมูลของท่านแล้ว โปรดรอการติดต่อกลับจากทีมงาน</span><br/>
              <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือไม่</span> -->
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <!-- <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancleConfirm()">ยกเลิก</v-btn> -->
                <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessModal()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <v-dialog v-model="modalSuccessCancelOrder" width="424" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="closeSuccessModal()"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการสร้างร้านค้าเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessModal()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
    </v-container>
  </div>
</template>

<script>
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      businessType: '',
      checkDistrictError: '',
      checkSubDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      subdistrictTextOther: '',
      subdistricttext: '',
      districtTextOther: '',
      provinceTextOther: '',
      zipcodeTextOther: '',
      districtText: '',
      provinceText: '',
      zipcodeText: '',
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        emptyText_firstBlank: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        seller_name: [
          v => !!v || 'กรุณากรอกชื่อฝ่ายขาย',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        shop_name: [
          v => !!v || 'กรุณากรอกชื่อร้านค้า',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        short_name: [
          v => !!v || 'กรุณากรอกชื่อย่อร้านค้า',
          v => v.length <= 5 || 'กรุณากรอกชื่อย่อร้านค้าไม่เกิน 5 ตัวอักษร',
          v => /^[A-Za-z0-9\s]+$/.test(v) || 'กรุณากรอกแค่ตัวอักษรภาษาอังกฤษและตัวเลข'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 ตัว',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        firstname: [
          v => !!v || 'กรุณากรอกชื่อ',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        lastname: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        contactTel: [
          v => !v || /^[0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => !v || (v.length >= 9 && v.length <= 10) || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ',
          v => (v.length > 8 && v.length <= 20) || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        blankEmail: [
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        Merchant: [
          v => !!v || 'กรุณาระบุรหัสการจ่ายเงิน',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        bussinessType: [
          v => !!v || 'กรุณาเลือกประเภทธุรกิจ'
        ],
        contactType: [
          v => !!v || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemContact: [
          v => !!v || 'กรุณาเลือกประเภทสัญญา'
        ],
        ItempayType: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemPayment: [
          v => v.length !== 0 || 'กรุณาเลือกช่องทางการชำระเงิน'
        ],
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ],
        ItemBank: [
          v => v.length !== 0 || 'กรุณาเลือกธนาคาร'
        ],
        installment: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการผ่อนชำระ'
        ],
        BranchName: [
          v => v.length !== 0 || 'กรุณากรอกชื่อสาขาร้านค้า'
        ],
        ItemServiceType: [
          v => v.length !== 0 || 'กรุณาเลือกประเภทการให้บริการ'
        ],
        ItemPaymentType: [
          v => v.length !== 0 || 'กรุณาเลือกประเภทการชำระเงิน'
        ],
        bookBank: [
          v => v.length !== 0
        ],
        postCode: [
          v => v.length === 5 || 'กรุณากรอกรหัสไปรษณีย์'
        ],
        bookBankNo: [
          v => v.length > 9 || 'กรุณากรอกเลขบัญชีธนาคาร'
        ],
        productType: [
          v => v.length !== 0 || 'กรุณากรอกประเภทสินค้า'
        ],
        TaxID: [
          v => (v.length === 0 || v.length === 13) || 'กรุณากรอกหมายเลขประจำตัวผู้เสียภาษี 13 หลัก'
        ]
      },
      shopDataPayload: {
        business_type: '',
        shop_type: '',
        open_type: '',
        first_name: '',
        last_name: '',
        email: '',
        phone_number: '',
        house_no: '',
        street: '',
        province: '',
        tambon: '',
        amphoe: '',
        zipcode: '',
        detail: '',
        contact1_email: '',
        contact1_line_id: '',
        contact2_email: '',
        contact2_line_id: ''
      },
      shopType: '',
      openType: '',
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      houseNo: '',
      street: '',
      province: '',
      tambon: '',
      amphoe: '',
      zipcode: '',
      detail: '',
      contact1Email: '',
      contact1LineId: '',
      contact2Email: '',
      contact2LineId: '',
      isEditing: true,
      modalAwaitCancelOrder: false,
      modalSuccessCancelOrder: false,
      userId: -1,
      contact2Tel: '',
      contact1Tel: '',
      lazyOne: false,
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      selectInterestChannel: '',
      itemsInterestChannel: [
        'กรมการพัฒนาชุมชน กระทรวงมหาดไทย (พช.)',
        'Dx Platform / SME D Bank',
        'ธนาคารออมสิน',
        'สำนักงานส่งเสริมวิสาหกิจขนาดกลางและขนาดย่อม (สสว.)',
        'BizClub',
        'สถาบันอาหาร',
        'บูธ Nex Gen',
        'เจ้าหน้าที่ Nex Gen Commerce',
        // 'BigSeller Thailand 2025 E-commerce Summit',
        // 'กรมส่งเสริมอุตสาหกรรม',
        // 'กรมการพัฒนาชุมชน กระทรวงมหาดไทย',
        // 'กรมพัฒนาธุรกิจการค้า',
        // 'Tiktok',
        // 'Line',
        // 'Instagram',
        // 'Facebook',
        // 'Youtube',
        // 'Lemon8',
        // 'X (Twitter)',
        // 'Threads',
        'เจ้าหน้าที่ Nex Gen Commerce',
        'Social Media',
        'อื่นๆ (โปรดระบุ)...'
      ],
      contact1Name: '',
      contact2Name: '',
      shopName: '',
      itemSocial: [
        'Tiktok',
        'Line',
        'Instagram',
        'Facebook',
        'Youtube',
        'Lemon8',
        'X (Twitter)',
        'Threads'
      ],
      selectedSocialMedia: '',
      reasonChanel: '',
      productType: '',
      TaxID: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    subdistricttext (val) {
      if (/\s/g.test(val)) {
        this.subdistricttext = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcodeText = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    districtText (val) {
      if (/\s/g.test(val)) {
        this.districtText = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.provinceText = ''
        }
      }
    },
    provinceText (val) {
      if (/\s/g.test(val)) {
        this.provinceText = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.districtText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.districtText = ''
        }
      }
    },
    zipcodeText (val) {
      if (/\s/g.test(val)) {
        this.zipcodeText = val.replace(/\s/g, '').substring(0, 5)
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            // this.checkAdressError('checkZipcodeError')
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistricttext = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.subdistricttext = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    }
  },
  async created () {
    // await this.GetRequestShopData()
    await this.getUserId()
  },
  methods: {
    async GetRequestShopData () {
      // console.log(Address2021, 'Address2021')
      await this.$store.dispatch('ActionsRequestSellerShopDetail')
      var response = await this.$store.state.ModuleManageShop.stateRequestSellerShopDetail
      if (response.data.length !== 0 && response.code === 200) {
        this.businessType = response.data.business_type
        this.shopType = response.data.shop_type
        this.openType = response.data.open_type
        this.firstName = response.data.first_name
        this.lastName = response.data.last_name
        this.email = response.data.email
        this.phoneNumber = response.data.phone_number
        this.houseNo = response.data.house_no
        this.provinceText = response.data.province
        this.subdistricttext = response.data.tambon
        this.districtText = response.data.amphoe
        this.zipcodeText = response.data.zipcode
        this.detail = response.data.detail
        this.contact1Email = response.data.contact1_email
        this.contact1LineId = response.data.contact1_line_id
        this.contact2Email = response.data.contact2_email
        this.contact2LineId = response.data.contact2_line_id
        // console.log(response.data, '1')
      } else {
        this.isEditing = false
      }
    },
    async SentRequestShopData () {
      if (this.$refs.formOne.validate(true)) {
        this.$store.commit('openLoader')
        this.shopDataPayload = {
          business_type: this.businessType === '' ? '-' : this.businessType,
          shop_type: this.shopType === '' ? '-' : this.shopType,
          open_type: this.openType === '' ? '-' : this.openType,
          first_name: this.firstName === '' ? '-' : this.firstName,
          last_name: this.lastName === '' ? '-' : this.lastName,
          email: this.email === '' ? '-' : this.email,
          phone_number: this.phoneNumber === '' ? '-' : this.phoneNumber,
          house_no: this.houseNo === '' ? '-' : this.houseNo,
          province: this.provinceText === '' ? '-' : this.provinceText,
          tambon: this.subdistricttext === '' ? '-' : this.subdistricttext,
          amphoe: this.districtText === '' ? '-' : this.districtText,
          zipcode: this.zipcodeText === '' ? '-' : this.zipcodeText,
          detail: this.detail === '' ? '-' : this.detail,
          contact1_email: this.contact1Email === '' ? '-' : this.contact1Email,
          contact1_line_id: this.contact1LineId === '' ? '-' : this.contact1LineId,
          contact2_email: this.contact2Email === '' ? '-' : this.contact2Email,
          contact2_line_id: this.contact2LineId === '' ? '-' : this.contact2LineId,
          user_id: this.userId,
          contact1_tel: this.contact1Tel === '' ? '-' : this.contact1Tel,
          contact2_tel: this.contact2Tel === '' ? '-' : this.contact2Tel,
          contact1_name: this.contact1Name,
          contact2_name: this.contact2Name === '' ? '-' : this.contact2Name,
          shop_name: this.shopName,
          interest_channel: this.selectInterestChannel === 'Social Media' ? this.selectInterestChannel + ',' + this.selectedSocialMedia : this.selectInterestChannel === 'อื่นๆ (โปรดระบุ)...' ? this.selectInterestChannel + ',' + this.reasonChanel : this.selectInterestChannel,
          tax_id: this.TaxID === '' ? '-' : this.TaxID,
          product_type: this.productType === '' ? '-' : this.productType
        }
        await this.$store.dispatch('ActionsRequestSellerShop', this.shopDataPayload)
        var response = await this.$store.state.ModuleManageShop.stateRequestSellerShop
        if (response) {
          // console.log(response, '2')
        }
        if (response.code === 200) {
          // this.modalSuccessCancelOrder = true
          this.modalAwaitCancelOrder = true
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.modalAwaitCancelOrder = false
          this.$swal.fire({
            icon: 'warning',
            text: `${response.message}`,
            showConfirmButton: false,
            timer: 3000
          })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
      // console.log(response, '3')
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistricttext && data.amphoe === this.districtText && data.province === this.provinceText && data.zipcode === Number(this.zipcodeText)
      })
      return check
    },
    EditRequestShopData () {
      this.isEditing = false
    },
    cancleEditRequestShopData () {
      this.$router.push({ path: '/' }).catch(() => {})
      this.isEditing = true
      this.businessType = ''
      this.shopType = ''
      this.openType = ''
      this.firstName = ''
      this.lastName = ''
      this.email = ''
      this.phoneNumber = ''
      this.houseNo = ''
      this.provinceText = ''
      this.subdistricttext = ''
      this.districtText = ''
      this.zipcodeText = ''
      this.detail = ''
      this.contact1Email = ''
      this.contact1LineId = ''
      this.contact2Email = ''
      this.contact2LineId = ''
      this.TaxID = ''
      this.productType = ''
      this.shopDataPayload = {
        business_type: this.businessType,
        shop_type: this.shopType,
        open_type: this.openType,
        first_name: this.firstName,
        last_name: this.lastName,
        email: this.email,
        phone_number: this.phoneNumber,
        house_no: this.houseNo,
        province: this.provinceText,
        tambon: this.subdistricttext,
        amphoe: this.districtText,
        zipcode: this.zipcodeText,
        detail: this.detail,
        contact1_email: this.contact1Email,
        contact1_line_id: this.contact1LineId,
        contact2_email: this.contact2Email,
        contact2_line_id: this.contact2LineId,
        tax_id: this.TaxID,
        product_type: this.productType
      }
    },
    clearEditRequestShopData () {
      this.businessType = ''
      this.shopType = ''
      this.openType = ''
      this.firstName = ''
      this.lastName = ''
      this.email = ''
      this.phoneNumber = ''
      this.houseNo = ''
      this.provinceText = ''
      this.subdistricttext = ''
      this.districtText = ''
      this.zipcodeText = ''
      this.detail = ''
      this.contact1Email = ''
      this.contact1LineId = ''
      this.contact2Email = ''
      this.contact2LineId = ''
      this.contact1Tel = ''
      this.contact2Tel = ''
      this.contact1Name = ''
      this.contact2Name = ''
      this.shopName = ''
      this.selectInterestChannel = ''
      this.reasonChanel = ''
      this.selectedSocialMedia = ''
      this.TaxID = ''
      this.productType = ''
    },
    async confirmSentRequestShopData () {
      if (this.$refs.formOne.validate(true)) {
        this.modalAwaitCancelOrder = true
        // if ((this.checksubdistrictConfirm(this.subdistricttext) || this.checkdistrictConfirm(this.districtText) || this.checkprovinceConfirm(this.provinceText) || this.checkzipcodeConfirm(this.zipcodeText))) {
        //   if (this.subdistricttext === this.checkSubdistrict && this.districtText === this.checkDistrict && this.provinceText === this.checkProvince && this.zipcodeText === this.checkZipcode) {
        //     const check = this.checkSendAddress()
        //     if (check.length !== 0) {
        //       this.modalAwaitCancelOrder = true
        //     } else {
        //       this.$store.commit('closeLoader')
        //       this.callCheckAdress()
        //       this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        //     }
        //   } else {
        //     this.$store.commit('closeLoader')
        //     this.checkConfirmAddress()
        //     this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        //   }
        // } else {
        //   this.$store.commit('closeLoader')
        //   this.callCheckAdress()
        //   this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        // }
        // this.modalAwaitCancelOrder = true
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async closeSuccessModal () {
      // this.modalSuccessCancelOrder = false
      this.modalAwaitCancelOrder = false
      this.$router.push({ path: '/' }).catch(() => {})
      this.isEditing = true
      // await this.GetRequestShopData()
    },
    cancleConfirm () {
      this.modalAwaitCancelOrder = false
    },
    async getUserId () {
      if (localStorage.getItem('oneData') === null) {
        this.userId = -1
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        var response = await this.$store.state.ModuleUser.stateAuthorityUser
        if (response.code === 200) {
          this.userId = response.data.user_id
        } else if (response.code === 401) {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: `${response.message}`,
            showConfirmButton: false,
            timer: 3000
          })
        }
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistricttext)
      this.checkdistrictConfirm(this.districtText)
      this.checkprovinceConfirm(this.provinceText)
      this.checkzipcodeConfirm(this.zipcodeText)
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistricttext
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.districtText
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.provinceText
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcodeText)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    }
  }
}
</script>

<style>
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  border-radius: 8px !important;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  color: #212121;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
  width: 300px;
}
</style>
