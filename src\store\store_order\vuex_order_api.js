import AxiosOrder from './axios_order_api'

const ModuleOrder = {
  state: {
    // Order List
    stateOrderListData: [],
    // Order Detail
    stateOrderDetailData: [],
    // stateListOrderSeller
    stateOrderListSeller: [],
    // stateListOrderSellerDetail
    stateOrderListSellerDetail: [],
    // stateListOrderRenew
    stateOrderRenew: [],
    // Order Detail
    stateOrderDetailSeller: [],
    // update send status seller
    stateUpdateStatusSeller: [],
    // cancel send status seller
    stateCancelStatusSeller: [],
    // update send status buyer
    stateUpdateStatusBuyer: [],
    // Order Detail Buyer
    stateDetailOrder: [],
    // get list order approve
    stateListOrderApprove: [],
    // Approve
    stateApprove: [],
    // acceptProduct
    stateAcceptProduct: [],
    // CheckAcceptProduct
    stateCheckAcceptProduct: [],
    // Tracking Order Flash
    stateTrackingOrderFlash: [],
    // List UPS Order
    stateListOrder: [],
    // Print Smalllabel Flash
    statePrintSmalllabelFlash: [],
    // Print Biglabel Flash
    statePrintBiglabelFlash: [],
    // Refund buyer list
    stateRefundBuyerList: [],
    // Refund order detail
    stateRefundOrderDetail: [],
    // Send Refund Buyer
    stateSendRefundBuyer: [],
    // Approve Refund Seller
    stateApproveRefundSeller: [],
    // Detail Approve Refund Seller
    stateApproveDetailRefundSeller: [],
    stateGetallProductReviewSeller: [],
    stateDetailProductReviewSeller: [],
    stateChangeStatusReview: [],
    stateDetailFeedbackReview: [],
    stateCreateReplyComment: [],
    stateEditReplyComment: [],
    // Check Liist Review Product Buyer
    stateCheckLiistReviewProductBuyer: [],
    // Create Comment Review Product Buyer
    stateCreateCommentReviewProductBuyer: [],
    // Create Comment Review Product Buyer
    stateEditCommentReviewProductBuyer: [],
    stateOrderb2bSeller: [],
    stateOrderReportLink: [],
    statePOSellerb2b: [],
    stateOrderChange: [],
    stateSearchProduct: [],
    stateopenModalPurchaseOrder: false,
    stateOrderRenewAndChange: [],
    stateOrderBuyAgain: [],
    stateCheckUpdateOrderRepeat: [],
    stateUploadOrderDocument: [],
    stateGetOrderDocument: [],
    stateGetOrderDetail: [],
    stateCancelOrder: [],
    stateCancelOrderDetails: [],
    stateApproveAndRejectOrder: [],
    // order list package seller
    stateOrderListERP: [],
    stateGetServiceID: [],
    stateListService: [],
    stateEtaxCreditNote: [],
    stateEtaxCheckShop: [],
    stateOrderPaymentPartner: [],
    stateDetailOrderPaymentPartner: [],
    stateDetailOrderPurchasePartner: [],
    stateDetailOrderBuyer: [],
    stateOrderListDataV3: [],
    stateGetPaymentMethodWithOrderPlatform: [],
    statePOSellerb2bV3: [],
    statePONewListBuyer: [],
    stateReplyComment: [],
    stateGetPaymentUsers: [],
    statePaymenyCheckewht: []
  },
  mutations: {
    // Order List
    mutationsOrderList (state, data) {
      state.stateOrderListData = data
    },
    // Order Detail
    mutationsOrderDetail (state, data) {
      state.stateOrderDetailData = data
    },
    // List Order Seller
    mutationsOrderListSeller (state, data) {
      state.stateOrderListSeller = data
    },
    // List Order Seller Detail
    mutationsOrderListSellerDetail (state, data) {
      state.stateOrderListSellerDetail = data
    },
    // List Order Renew
    mutationsOrderRenew (state, data) {
      state.stateOrderRenew = data
    },
    // Order Detail
    mutationsOrderDetailSeller (state, data) {
      state.stateOrderDetailSeller = data
    },
    // update send status seller
    mutationsUpdateStatusSeller (state, data) {
      state.stateUpdateStatusSeller = data
    },
    // cancel send status seller
    mutationsCancelStatusSeller (state, data) {
      state.stateCancelStatusSeller = data
    },
    // update send status Buyer
    mutationsUpdateStatusBuyer (state, data) {
      state.stateUpdateStatusBuyer = data
    },
    // get list order approve
    mutationListOrderApprove (state, data) {
      state.stateListOrderApprove = data
    },
    // Approve
    mutationApprove (state, data) {
      state.stateApprove = data
    },
    // acceptProduct
    mutationAcceptProduct (state, data) {
      state.stateAcceptProduct = data
    },
    // CheckAcceptProduct
    mutationCheckAcceptProduct (state, data) {
      state.stateCheckAcceptProduct = data
    },
    // Tracking Order Flash
    mutationsTrackingOrderFlash (state, data) {
      state.stateTrackingOrderFlash = data
    },
    // List UPS Order
    mutationsListOrder (state, data) {
      state.stateListOrder = data
    },
    // Refund buyer list
    mutationsRefundListOrder (state, data) {
      state.stateRefundBuyerList = data
    },
    // Refund order detail
    mutationsRefundOrderDetail (state, data) {
      state.stateRefundOrderDetail = data
    },
    // Send Refund Buyer
    mutationsSendRefundBuyer (state, data) {
      state.stateSendRefundBuyer = data
    },
    // Approve Refund Seller
    mutationsApproveRefundSeller (state, data) {
      state.stateApproveRefundSeller = data
    },
    // Detail Approve Refund Seller
    mutationsApproveDetailRefundSeller (state, data) {
      state.stateApproveDetailRefundSeller = data
    },
    mutationsGetallProductReviewSeller (state, data) {
      state.stateGetallProductReviewSeller = data
      // console.log('GetallProductReviewSeller///', state.stateGetallProductReviewSeller)
    },
    mutationsCreateReplyComment (state, data) {
      state.stateCreateReplyComment = data
      // console.log('GetallProductReviewSeller///', state.stateCreateReplyComment)
    },
    mutationsEditReplyComment (state, data) {
      state.stateEditReplyComment = data
      // console.log('GetallProductReviewSeller///', state.stateEditReplyComment)
    },
    // Check Liist Review Product Buyer
    mutationsCheckLiistReviewProductBuyer (state, data) {
      state.stateCheckLiistReviewProductBuyer = data
    },
    // Create Comment Review Product Buyer
    mutationsCreateCommentReviewProductBuyer (state, data) {
      state.stateCreateCommentReviewProductBuyer = data
    },
    // Edit Comment Review Product Buyer
    mutationsEditCommentReviewProductBuyer (state, data) {
      state.stateEditCommentReviewProductBuyer = data
    },
    mutationsDetailProductReviewSeller (state, data) {
      state.stateDetailProductReviewSeller = data
    },
    mutationsChangeStatusReview (state, data) {
      state.stateChangeStatusReview = data
    },
    mutationsDetailFeedbackReview (state, data) {
      state.stateDetailFeedbackReview = data
    },
    mutationsOrderb2bSeller (state, data) {
      state.stateOrderb2bSeller = data
    },
    mutationsOrderReportLink (state, data) {
      state.stateOrderReportLink = data
    },
    mutationsPOSellerb2b (state, data) {
      state.statePOSellerb2b = data
    },
    mutationsOrderChange (state, data) {
      state.stateOrderChange = data
    },
    mutationsSearchProduct (state, data) {
      state.stateSearchProduct = data
    },
    mutationsOrderRenewAndChange (state, data) {
      state.stateOrderRenewAndChange = data
    },
    mutationsOrderBuyAgain (state, data) {
      state.stateOrderBuyAgain = data
    },
    mutationsCheckUpdateOrderRepeat (state, data) {
      state.stateCheckUpdateOrderRepeat = data
    },
    mutationsUploadOrderDocument (state, data) {
      state.stateUploadOrderDocument = data
    },
    mutationsGetOrderDocument (state, data) {
      state.stateGetOrderDocument = data
    },
    mutationsGetOrderDetail (state, data) {
      state.stateGetOrderDetail = data
    },
    mutationsCancelOrder (state, data) {
      state.stateCancelOrder = data
    },
    mutationsCancelOrderDetails (state, data) {
      state.stateCancelOrderDetails = data
    },
    mutationsApproveAndRejectOrder (state, data) {
      state.stateApproveAndRejectOrder = data
    },
    mutationsOrderListERP (state, data) {
      state.stateOrderListERP = data
    },
    mutationsGetServiceID (state, data) {
      state.stateGetServiceID = data
    },
    mutationsListService (state, data) {
      state.stateListService = data
    },
    mutationsEtaxCreditNote (state, data) {
      state.stateEtaxCreditNote = data
    },
    mutationsEtaxCheckShop (state, data) {
      state.stateEtaxCheckShop = data
    },
    mutationsOrderPaymentPartner (state, data) {
      state.stateOrderPaymentPartner = data
    },
    mutationsDetailOrderPaymentPartner (state, data) {
      state.stateDetailOrderPaymentPartner = data
    },
    mutationsDetailOrderPurchasePartner (state, data) {
      state.stateDetailOrderPurchasePartner = data
    },
    mutationsDetailOrderBuyer (state, data) {
      state.stateDetailOrderBuyer = data
    },
    mutationsOrderListV3 (state, data) {
      state.stateOrderListDataV3 = data
    },
    mutationsGetPaymentMethodWithOrderPlatform (state, data) {
      state.stateGetPaymentMethodWithOrderPlatform = data
    },
    mutationsPOSellerb2bV3 (state, data) {
      state.statePOSellerb2bV3 = data
    },
    mutationPONewListBuyer (state, data) {
      state.statePONewListBuyer = data
    },
    mutationsReplyComment (state, data) {
      state.stateReplyComment = data
    },
    mutationsGetPaymentUsers (state, data) {
      state.stateGetPaymentUsers = data
    },
    mutationsPaymenyCheckewht (state, data) {
      state.statePaymenyCheckewht = data
    }
  },
  actions: {
    // Order Detail
    async actionListOrderBuyer (context, access) {
      const dataOrder = await AxiosOrder.GetOrderList(access)
      await context.commit('mutationsOrderList', dataOrder)
    },
    async actionOrderDetail (context, access) {
      const response = await AxiosOrder.OrderDetail(access)
      await context.commit('mutationsOrderDetail', response)
    },
    async actionListOrderSeller (context, access) {
      const response = await AxiosOrder.OrderListSeller(access)
      await context.commit('mutationsOrderListSeller', response)
    },
    async actionListOrderSellerDetail (context, access) {
      const response = await AxiosOrder.OrderListSellerDetail(access)
      await context.commit('mutationsOrderListSellerDetail', response)
    },
    async actionListOrderRenew (context, access) {
      const response = await AxiosOrder.OrderRenew(access)
      await context.commit('mutationsOrderRenew', response)
    },
    async actionOrderDetailSeller (context, access) {
      const response = await AxiosOrder.OrderDetailSeller(access)
      await context.commit('mutationsOrderDetailSeller', response)
    },
    async actionUpdateStatusSeller (context, access) {
      const response = await AxiosOrder.UpdateStatusSeller(access)
      await context.commit('mutationsUpdateStatusSeller', response)
    },
    // Status Order Detail Seller
    async actionCancelStatusSeller (context, access) {
      const response = await AxiosOrder.CancelStatusSeller(access)
      await context.commit('mutationsCancelStatusSeller', response)
    },
    async actionUpdateStatusBuyer (context, access) {
      const response = await AxiosOrder.UpdateStatusBuyer(access)
      await context.commit('mutationsUpdateStatusBuyer', response)
    },
    // get list order approve
    async actionDetListOrderApprove (context) {
      const response = await AxiosOrder.GetListOrderApprove()
      await context.commit('mutationListOrderApprove', response)
    },
    // Approve
    async actionApprove (context, access) {
      const response = await AxiosOrder.ApproveData(access)
      await context.commit('mutationApprove', response)
    },
    // acceptProduct
    async actionAcceptProduct (context, access) {
      const response = await AxiosOrder.AcceptProduct(access)
      await context.commit('mutationAcceptProduct', response)
    },
    // CheckAcceptProduct
    async actionCheckAcceptProduct (context, access) {
      const response = await AxiosOrder.CheckAcceptProduct(access)
      await context.commit('mutationCheckAcceptProduct', response)
    },
    // Tracking Order Flash
    async actionTrackingOrderFlash (context, access) {
      const response = await AxiosOrder.GetTrackingOrderFlash(access)
      await context.commit('mutationsTrackingOrderFlash', response)
    },
    // List UPS Order
    async actionListOrder (context, access) {
      const response = await AxiosOrder.GetListOrder(access)
      await context.commit('mutationsListOrder', response)
    },
    // Refund buyer list
    async actionsRefundBuyerList (context, access) {
      const response = await AxiosOrder.RefundBuyerList(access)
      await context.commit('mutationsRefundListOrder', response)
    },
    // Refund order detail
    async actionsRefundOrderDetail (context, access) {
      const response = await AxiosOrder.RefundOrderDetail(access)
      await context.commit('mutationsRefundOrderDetail', response)
    },
    // Send Refund Buyer
    async actionsSendRefundBuyer (context, access) {
      const response = await AxiosOrder.SendRefundBuyer(access)
      await context.commit('mutationsSendRefundBuyer', response)
    },
    // Approve Refund Seller
    async actionsApproveRefundSeller (context, access) {
      const response = await AxiosOrder.ApproveRefundSeller(access)
      await context.commit('mutationsApproveRefundSeller', response)
    },
    // Detail Approve Refund Seller
    async actionsApproveDetailRefundSeller (context, access) {
      const response = await AxiosOrder.ApproveDetailRefundSeller(access)
      await context.commit('mutationsApproveDetailRefundSeller', response)
    },
    async actionGetallProductReviewSeller (context, access) {
      var responseData = await AxiosOrder.GetallProductReviewSeller(access)
      await context.commit('mutationsGetallProductReviewSeller', responseData)
    },
    async actionCreateReplyComment (context, access) {
      var responseData = await AxiosOrder.CreateReplyComment(access)
      await context.commit('mutationsCreateReplyComment', responseData)
    },
    async actionEditReplyComment (context, access) {
      var responseData = await AxiosOrder.EditReplyComment(access)
      await context.commit('mutationsEditReplyComment', responseData)
    },
    async actionsDetailProductReviewSeller (context, access) {
      var responseData = await AxiosOrder.DetailReviewSeller(access)
      await context.commit('mutationsDetailProductReviewSeller', responseData)
    },
    async actionsChangeStatusReview (context, access) {
      var responseData = await AxiosOrder.ChangeStatusReview(access)
      await context.commit('mutationsChangeStatusReview', responseData)
    },
    async actionsDetailFeedbackReview (context, access) {
      var responseData = await AxiosOrder.DetailFeedbackReview(access)
      await context.commit('mutationsDetailFeedbackReview', responseData)
    },
    async actionReportOrderb2b (context, access) {
      const response = await AxiosOrder.Reportb2bSeller(access)
      await context.commit('mutationsOrderb2bSeller', response)
    },
    async actionReportLink (context, access) {
      const response = await AxiosOrder.ReportLink(access)
      await context.commit('mutationsOrderReportLink', response)
    },
    async actionPOSellerb2b (context, access) {
      const response = await AxiosOrder.POSellerb2b(access)
      await context.commit('mutationsPOSellerb2b', response)
    },
    async actionOrderChange (context, access) {
      const response = await AxiosOrder.OrderChange(access)
      await context.commit('mutationsOrderChange', response)
    },
    async actionSearchProduct (context, access) {
      const response = await AxiosOrder.SearchProduct(access)
      await context.commit('mutationsSearchProduct', response)
    },
    async actionOrderRenewAndChange (context, access) {
      const response = await AxiosOrder.OrderRenewAndChange(access)
      await context.commit('mutationsOrderRenewAndChange', response)
    },
    async actionOrderBuyAgain (context, access) {
      const response = await AxiosOrder.OrderBuyAgain(access)
      await context.commit('mutationsOrderBuyAgain', response)
    },
    async actionCheckUpdateOrderRepeat (context, access) {
      const response = await AxiosOrder.CheckUpdateOrderRepeat(access)
      await context.commit('mutationsCheckUpdateOrderRepeat', response)
    },
    async actionUploadOrderDocument (context, access) {
      const response = await AxiosOrder.UploadOrderDocument(access)
      await context.commit('mutationsUploadOrderDocument', response)
    },
    async actionGetOrderDocument (context, access) {
      const response = await AxiosOrder.GetOrderDocument(access)
      await context.commit('mutationsGetOrderDocument', response)
    },
    async actionGetOrderDetail (context, access) {
      const response = await AxiosOrder.GetOrderDetail(access)
      await context.commit('mutationsGetOrderDetail', response)
    },
    async actionCancelOrder (context, access) {
      const response = await AxiosOrder.CancelOrder(access)
      await context.commit('mutationsCancelOrder', response)
    },
    async actionCancelOrderDetails (context, access) {
      const response = await AxiosOrder.GetCancelOrderDetails(access)
      await context.commit('mutationsCancelOrderDetails', response)
    },
    async actionApproveAndRejectOrder (context, access) {
      const response = await AxiosOrder.ApproveAndRejectOrder(access)
      await context.commit('mutationsApproveAndRejectOrder', response)
    },
    async actionOrderListERP (context, access) {
      const response = await AxiosOrder.OrderListERP(access)
      await context.commit('mutationsOrderListERP', response)
    },
    async actionGetServiceID (context, access) {
      const response = await AxiosOrder.GetServiceID(access)
      await context.commit('mutationsGetServiceID', response)
    },
    async actionListService (context, access) {
      const response = await AxiosOrder.ListServicw(access)
      await context.commit('mutationsListService', response)
    },
    async actionEtaxCreditNote (context, access) {
      const response = await AxiosOrder.EtaxCreditNote(access)
      await context.commit('mutationsEtaxCreditNote', response)
    },
    async actionEtaxCheckShop (context, access) {
      const response = await AxiosOrder.EtaxCheckShop(access)
      await context.commit('mutationsEtaxCheckShop', response)
    },
    async actionOrderPaymentPartner (context, access) {
      const response = await AxiosOrder.OrderPaymentPartner(access)
      await context.commit('mutationsOrderPaymentPartner', response)
    },
    async actionDetailOrderPaymentPartner (context, access) {
      const response = await AxiosOrder.DetailOrderPaymentPartner(access)
      await context.commit('mutationsDetailOrderPaymentPartner', response)
    },
    async actionDetailOrderPurchasePartner (context, access) {
      const response = await AxiosOrder.DetailOrderPurchasePartner(access)
      await context.commit('mutationsDetailOrderPurchasePartner', response)
    },
    async actionGetDetailOrderBuyer (context, access) {
      const response = await AxiosOrder.GetDetailOrderBuyer(access)
      await context.commit('mutationsDetailOrderBuyer', response)
    },
    async actionListOrderBuyerV3 (context, access) {
      const dataOrder = await AxiosOrder.GetOrderListV3(access)
      await context.commit('mutationsOrderListV3', dataOrder)
    },
    async actionGetPaymentMethodWithOrderPlatform (context, access) {
      const dataOrder = await AxiosOrder.GetPaymentMethodWithOrderPlatform(access)
      await context.commit('mutationsGetPaymentMethodWithOrderPlatform', dataOrder)
    },
    async actionPOSellerb2bV3 (context, access) {
      const response = await AxiosOrder.POSellerb2bV3(access)
      await context.commit('mutationsPOSellerb2bV3', response)
    },
    async actionPONewListBuyer (context, access) {
      const response = await AxiosOrder.PONewListBuyer(access)
      await context.commit('mutationPONewListBuyer', response)
    },
    async actionReplyComment (context, access) {
      var responseData = await AxiosOrder.ReplyComment(access)
      await context.commit('mutationsReplyComment', responseData)
    },
    async actionGetPaymentUsers (context, access) {
      var responseData = await AxiosOrder.GetPaymentUsers(access)
      await context.commit('mutationsGetPaymentUsers', responseData)
    },
    async actionPaymenyCheckewht (context, access) {
      var responseData = await AxiosOrder.PaymenyCheckewht(access)
      await context.commit('mutationsPaymenyCheckewht', responseData)
    }
  }
}
export default ModuleOrder
