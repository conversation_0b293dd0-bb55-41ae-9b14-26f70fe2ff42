<template>
<div class='text-center'>
    <v-dialog v-model='openDetail' :width="MobileSize ? '100%' : '50%'" persistent>
    <v-card min-height='600'>
        <v-toolbar flat color='#E6F5F3'>
        <v-row>
            <v-col class='d-flex justify-space-around'>
            <v-toolbar-title
                ><span
                style='color: #27ab9c'
                :class="MobileSize ? 'title-mobile' : ''"
                ><b>รายละเอียดรายการสั่งซื้อ</b></span
                ></v-toolbar-title>
            </v-col>
        </v-row>
        <v-btn fab small @click='cancel ()' icon
            ><v-icon color='#27AB9C'>mdi-close</v-icon></v-btn
        >
        </v-toolbar>
        <v-container grid-list-xs>
        <v-row class='pt-2 px-5'>
            <v-col cols='12'>
            <div>
                <v-row>
                <v-col cols="10" >
                    <span>order_number : {{ orderNumber }}</span><br>
                </v-col>
                <v-col cols="2" class="d-flex justify-end">
                    <v-btn plain small @click="toggleDisabled"><v-icon color="#27AB9C">mdi-pencil-outline</v-icon></v-btn>
                </v-col>
                </v-row>
                <v-row class="align-center">
                <v-col cols='12'>
                    <v-row class="mt-1 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>Send PDF</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="sendPDF" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-1 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>Response PDF</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="responsePDF" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-1 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>PR Document</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="prDoc" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-1 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>PO Document</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="poDoc" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-1 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>Ref Callback SO</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="refSO" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                </v-col>
                </v-row>
                <v-row class="align-center">
                <v-col cols='12'>
                    <h2>Personal</h2>
                    <div style="margin-bottom: 8%;">
                    <h3>purchasing_chief</h3>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>name</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="purchasing_chief_name" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>phone</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="purchasing_chief_phone" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>email</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="purchasing_chief_email" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>position</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="purchasing_chief_position" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                  </div>
                  <div style="margin-bottom: 8%;">
                    <h3>inspectors_one</h3>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>name</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="inspectors_one_name" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>phone</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="inspectors_one_phone" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>email</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="inspectors_one_email" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>position</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="inspectors_one_position" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    </div>
                    <div style="margin-bottom: 8%;">
                    <h3>inspectors_two</h3>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>name</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="inspectors_two_name" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                        </v-row>
                        <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                            <span>phone</span>
                        </v-col>
                        <v-col cols="8">
                            <v-text-field v-model="inspectors_two_phone" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                        </v-row>
                        <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                            <span>email</span>
                        </v-col>
                        <v-col cols="8">
                            <v-text-field v-model="inspectors_two_email" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                        </v-row>
                        <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                            <span>position</span>
                        </v-col>
                        <v-col cols="8">
                            <v-text-field v-model="inspectors_two_position" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                        </v-row>
                        </div>
                </v-col>
                </v-row>
                <v-row class="align-center">
                <v-col cols='12'>
                    <div>
                    <h2>Buyer</h2>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>name</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="buyerName" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>phone</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="buyerPhone" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    <v-row class="mt-2 d-flex" style="align-items: center;">
                        <v-col cols="4">
                        <span>email</span>
                        </v-col>
                        <v-col cols="8">
                        <v-text-field v-model="buyerEmail" outlined rounded dense hide-details :disabled="toggle"></v-text-field>
                        </v-col>
                    </v-row>
                    </div>
                </v-col>
                </v-row>
                <!-- <v-row class="align-center">
                <v-col cols="4">
                    <span>business_type</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.business_type : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>callbackUrl</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.callbackUrl : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_address</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_address : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_city_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_city_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_district_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_district_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_phone</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_phone : '0'" outlined rounded dense type="number" hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_postal_code</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_postal_code : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>dst_province_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.dst_province_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>formula_code</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.formula_code : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>height</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.height : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>insured</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.insured : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>length</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.length : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>reference_id</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.reference_id : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>remark</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.remark : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>service_type</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.service_type : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_address</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_address : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_city_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_city_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_district_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_district_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_phone</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_phone : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_postal_code</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_postal_code : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>src_province_name</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.src_province_name : '-'" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>weight</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.weight : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                <v-col cols="4">
                    <span>width</span>
                </v-col>
                <v-col cols="8">
                    <v-text-field :value="item.json_mobilyst_req != null ? item.json_mobilyst_req.width : '0'" type="number" outlined rounded dense hide-details></v-text-field>
                </v-col>
                </v-row> -->
                <v-row class="justify-end align-center mr-2">
                <v-btn rounded @click="dialogConfirm()" color="#27AB9C" class="white--text mt-3 mb-3">บันทึก</v-btn>
                </v-row>
                <!-- @input="updateValue($event, index, 'buyer_name')" -->
            </div>
            </v-col>
        </v-row>
        </v-container>
    </v-card>
    </v-dialog>
    <v-dialog v-model='openConfirm' :width="MobileSize ? '60%' : '30%'" persistent>
    <v-card min-height='100%'>
        <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
        <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
        >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='cancel ()' icon
            ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
            >
        </v-app-bar>
        </v-img>
        <v-container>
        <v-card-text style="text-align: center;">
            คุณต้องการทำรายการนี้ ใช่หรือไม่
        </v-card-text>
        <v-card-actions>
        <v-row dense class='d-flex justify-space-between' style="gap: 20px;">
            <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="openConfirm = !openConfirm">ยกเลิก</v-btn>
            <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="confirmEditData()">ตกลง</v-btn>
        </v-row>
        </v-card-actions>
        </v-container>
    </v-card>
    </v-dialog>
    <v-dialog v-model='openSuccess' :width="MobileSize ? '60%' : '30%'" persistent>
    <v-card min-height='100%'>
        <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
        <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
        >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='cancel ()' icon
            ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
            >
        </v-app-bar>
        </v-img>
        <v-container>
        <v-card-text style="text-align: center;">
            คุณได้ทำการแก้ไขรายการสั่งซื้อนี้เรียบร้อย
        </v-card-text>
        <v-card-actions>
        <v-row dense class='d-flex justify-center' style="padding: 0 25%;">
            <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="cancel()">ตกลง</v-btn>
        </v-row>
        </v-card-actions>
        </v-container>
    </v-card>
    </v-dialog>
</div>
</template>
<script>
import { mapActions } from 'vuex'
export default {
  data () {
    return {
      openDetail: false,
      openConfirm: false,
      openSuccess: false,
      toggle: true,
      OrderNumberID: '',
      orderNumber: '',
      purchasing_chief_name: '',
      purchasing_chief_phone: '',
      purchasing_chief_email: '',
      purchasing_chief_position: '',
      inspectors_one_name: '',
      inspectors_one_phone: '',
      inspectors_one_email: '',
      inspectors_one_position: '',
      inspectors_two_name: '',
      inspectors_two_phone: '',
      inspectors_two_email: '',
      inspectors_two_position: '',
      buyerName: '',
      buyerPhone: '',
      buyerEmail: '',
      sendPDF: null,
      responsePDF: null,
      poDoc: null,
      prDoc: null,
      refSO: null
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SearchOrderMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'SearchOrder')
        this.$router.push({ path: '/SearchOrder' }).catch(() => {})
      }
    }
  },
  methods: {
    ...mapActions(['actionsEditOrderList']),
    open (data) {
      // console.log('detail', detail)
      var detail = data
      this.orderNumber = detail[0].order_number
      this.prDoc = detail[0].pr_document_id || '-'
      this.poDoc = detail[0].po_document_id || '-'
      this.refSO = detail[0].ref_callback_so_id || '-'
      this.sendPDF = detail[0].data_send_pdf_pr || '-'
      this.responsePDF = detail[0].data_response_pdf_pr || '-'
      var personal = JSON.parse(detail[0].json_personal)
      if (personal && personal[0] && personal[0].purchasing_chief && personal[0].purchasing_chief[0]) {
        this.purchasing_chief_name = personal[0].purchasing_chief[0].name
        this.purchasing_chief_phone = personal[0].purchasing_chief[0].phone
        this.purchasing_chief_email = personal[0].purchasing_chief[0].email
        this.purchasing_chief_position = personal[0].purchasing_chief[0].position
      } else {
        this.purchasing_chief_name = '-'
        this.purchasing_chief_phone = '-'
        this.purchasing_chief_email = '-'
        this.purchasing_chief_position = '-'
      }
      if (personal && personal[0] && personal[0].inspectors_one && personal[0].inspectors_one[0]) {
        this.inspectors_one_name = personal[0].inspectors_one[0].name
        this.inspectors_one_phone = personal[0].inspectors_one[0].phone
        this.inspectors_one_email = personal[0].inspectors_one[0].email
        this.inspectors_one_position = personal[0].inspectors_one[0].position
      } else {
        this.inspectors_one_name = '-'
        this.inspectors_one_phone = '-'
        this.inspectors_one_email = '-'
        this.inspectors_one_position = '-'
      }
      if (personal && personal[0] && personal[0].inspectors_two && personal[0].inspectors_two[0]) {
        this.inspectors_two_name = personal[0].inspectors_two[0].name
        this.inspectors_two_phone = personal[0].inspectors_two[0].phone
        this.inspectors_two_email = personal[0].inspectors_two[0].email
        this.inspectors_two_position = personal[0].inspectors_two[0].position
      } else {
        this.inspectors_two_name = '-'
        this.inspectors_two_phone = '-'
        this.inspectors_two_email = '-'
        this.inspectors_two_position = '-'
      }
      var buyer = JSON.parse(detail[0].json_buyer)
      if (buyer && buyer[0]) {
        this.buyerName = buyer[0].buyer_name
        this.buyerPhone = buyer[0].buyer_phone
        this.buyerEmail = buyer[0].buyer_email
      } else {
        this.buyerName = '-'
        this.buyerPhone = '-'
        this.buyerEmail = '-'
      }
      // console.log('--->', personal)
      // console.log('--->2', buyer)
      this.openDetail = true
    },
    async cancel () {
      if (this.openDetail === true && this.openConfirm === true && this.openSuccess === true) {
        this.openDetail = false
        this.openConfirm = false
        this.openSuccess = false
      } else if (this.openDetail === true && this.openConfirm === true) {
        this.openDetail = false
        this.openConfirm = false
      } else if (this.openDetail === true) {
        this.openDetail = false
      }
    },
    toggleDisabled () {
      this.toggle = !this.toggle
    },
    dialogConfirm () {
      this.openConfirm = true
    },
    async confirmEditData () {
      // console.log('json_buyer', this.buyerName)
      if (this.prDoc === '' || this.prDoc === '-' || this.prDoc == null) {
        this.prDoc = null
      }
      if (this.poDoc === '' || this.poDoc === '-' || this.poDoc == null) {
        this.poDoc = null
      }
      if (this.refSO === '' || this.refSO === '-' || this.refSO == null) {
        this.refSO = null
      }
      if (this.sendPDF === '' || this.sendPDF === '-' || this.sendPDF == null) {
        this.sendPDF = null
      }
      if (this.responsePDF === '' || this.responsePDF === '-' || this.responsePDF == null) {
        this.responsePDF = null
      }
      const dataEdit = {
        order_number: this.orderNumber,
        pr_document_id: this.prDoc,
        po_document_id: this.poDoc,
        ref_callback_so_id: this.refSO,
        data_send_pdf_pr: this.sendPDF,
        data_response_pdf_pr: this.responsePDF,
        json_personal: {
          purchasing_chief: [
            {
              name: this.purchasing_chief_name,
              phone: this.purchasing_chief_phone,
              email: this.purchasing_chief_email,
              position: this.purchasing_chief_position
            }
          ],
          inspectors_one: [
            {
              name: this.inspectors_one_name,
              phone: this.inspectors_one_phone,
              email: this.inspectors_one_email,
              position: this.inspectors_one_position
            }
          ],
          inspectors_two: [
            {
              name: this.inspectors_two_name,
              phone: this.inspectors_two_phone,
              email: this.inspectors_two_email,
              position: this.inspectors_two_position
            }
          ]
        },
        json_buyer: {
          buyer_name: this.buyerName,
          buyer_phone: this.buyerPhone,
          buyer_email: this.buyerEmail
        }
      }
      // console.log('Data_Edit', dataEdit)
      await this.actionsEditOrderList(dataEdit)
      await this.$store.state.ModuleOrderList.stateEditOrderList.data
      // const dataSuccess =
      // console.log('dataSuccess', dataSuccess)
      this.openSuccess = true
    }
  }
}
</script>
    <style lang='css' scoped>
  ::-webkit-scrollbar {
    width: 6px;
  } /* Track */
  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }
  ::-webkit-scrollbar-thumb {
    background: #e6e6e6;
    border-radius: 8px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #888;
    border-radius: 8px;
  }
</style>

<style>
  .checkbox-admin .v-input--selection-controls__input {
    margin-right: 0px !important;
  }
  .v-text-field input {
    font-size: 0.9em;
  }
  .v-text-field__details {
    margin-bottom: 4px !important;
  }
  .input_text {
    height: 60px;
    opacity: 1;
  }
  .doc-detail {
    font-size: 14px;
    text-align: center;
    font-weight: 600;
  }
  .blod-detail {
    font-size: 16px;
    font-weight: 600;
  }
  .title-detail {
    font-size: 14px;
    font-weight: 400;
  }
</style>
