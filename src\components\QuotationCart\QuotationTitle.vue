<template>
  <v-container grid-list-xs>
    <v-row v-if="orderType === 'quotation'">
      <v-col cols="4" class="pa-0">
        <v-row>
          <div class="line_size">
            <v-col cols="12" class="mb-3 pt-0 pb-0">
              <v-img
                class="titleleft"
                style="background-color: white"
                :src="require(`@/assets/new_epro.png`)"
              ></v-img>
            </v-col>
          </div>
        </v-row>
      </v-col>
      <v-col cols="4" class="pa-0">
        <v-row>
          <v-col cols="12" class="text-center pl-1 pr-1">
            <span class="titlerightQuotation">{{ $t("Quotation.name") }}</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="4" class="pa-0">
        <v-row>
          <v-col cols="6" style="text-align: right" class="POpadding1">
            <span class="contact_center font-weight-bold">
              {{ $t("ReferenceDocument.name") }}:
            </span>
          </v-col>
          <v-col cols="6" style="text-align: left" class="POpadding">
            <span class="contact_center">{{ order_number }}</span>
          </v-col>
          <v-col cols="6" style="text-align: right" class="POpadding1">
            <span class="contact_center font-weight-bold">
              {{ $t("date.name") }}:
            </span>
          </v-col>
          <v-col cols="6" style="text-align: left" class="POpadding">
            <span class="contact_center">{{
              created_at.substring(10, 0)
            }}</span>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row v-if="orderType === 'purchase'">
      <v-col cols="4" class="pa-0">
        <v-row>
          <div class="line_size">
            <v-col cols="12" class="mb-3 pt-0 pb-0">
              <v-img
                class="titleleft"
                style="background-color: white"
                :src="require(`@/assets/new_epro.png`)"
              ></v-img>
            </v-col>
          </div>
        </v-row>
      </v-col>
      <v-col cols="4" class="pa-0">
        <v-row>
          <v-col cols="12" class="text-center l-1 pr-1">
            <span class="titlerightQuotation">{{ $t("QuotationPurchaser.name") }}</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="4" class="pa-0">
        <v-row>
          <v-col cols="6" style="text-align: right" class="POpadding1">
            <span class="contact_center font-weight-bold">
              {{ $t("ReferenceDocument.name") }}:
            </span>
          </v-col>
          <v-col cols="6" style="text-align: left" class="POpadding">
            <span class="contact_center">{{ order_number }}</span>
          </v-col>
          <v-col cols="6" style="text-align: right" class="POpadding1">
            <span class="contact_center font-weight-bold">
              {{ $t("date.name") }}:
            </span>
          </v-col>
          <v-col cols="6" style="text-align: left" class="POpadding">
            <span class="contact_center">{{created_at.substring(10, 0)}}</span>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      order_number: '',
      created_at: '',
      orderType: ''
    }
  },
  created () {
    this.orderType = localStorage.getItem('typeOrder')
    // console.log('orderType', this.orderType)
    this.getData()
    // this.checkLocale()
  },
  // computed: {
  //   orderType () {
  //     return localStorage.getItem('typeOrder')
  //   }
  // },
  methods: {
    getData () {
      // var data = JSON.parse(JSON.parse(Decode.decode(localStorage.getItem('PDF_Data'))))
      // console.log('data title', data)
      // this.order_number = data.cartData.order_number
      // this.created_at = data.cartData.created_at
    }
    // checkLocale () {
    //   const langPurchaser = localStorage.getItem('StorageLanguage')
    //   if (langPurchaser === 'th') {
    //     this.$i18n.locale = 'th'
    //     this.lang = 'Thai'
    //     this.images = 'thai.png'
    //   } else {
    //     this.$i18n.locale = 'en'
    //     this.lang = 'English'
    //     this.images = 'eng.png'
    //   }
    // }
  }
}
</script>

<style scoped>
@import url("https://fonts.googleapis.com/css?family=Sarabun&display=swap");

.contact_center {
  font-family: "Sarabun" !important;
  font-size: 12px !important;
}
.POpadding1 {
  padding-top: 0px;
  padding-left: 0px;
  padding-right: 5px;
  padding-bottom: 0px;
}
.POpadding {
  padding-top: 0px;
  padding-left: 0px;
  padding-right: 0px;
  padding-bottom: 0px;
}
.titleright {
  font-family: "Sarabun" !important;
  font-size: 12px !important;
  text-align: right;
  color: rgb(3, 39, 0);
  background-color: white;
  margin-top: -15px;
}
.titlerightQuotation {
  font-size: 32px !important;
  font-family: "Sarabun" !important;
  text-align: center;
  color: rgb(0, 0, 0);
  font-weight: bold;
  line-height: 10px;
  margin-top: 25px;
  margin-bottom: 20px;
}
.titleleft {
  text-align: left;
  width: 200px;
  height: 50px;
}
.line_size {
  font-family: "Sarabun" !important;
  line-height: 1 !important;
}
</style>
