<template>
  <div>
    <v-container>
      <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
        <h2 v-if="!MobileSize" class="ml-4" style="font-size:15px"><B>การวางบิล</B></h2>
        <v-container>
          <v-text-field style="width:400px" v-model="search" append-icon="mdi-magnify" label="ค้นหารหัสการสั่งซื้อ"
            v-if="show" outlined dense rounded hide-details>
          </v-text-field><br />
          <v-data-table v-if="show" v-model="selected" :headers="headers" :items="data" :search="search"
            item-key="id" color="blue" class="elevation-1">
            <template v-slot:[`item.action`]="{ item }">
              <v-icon @click="goDetail(item.partner_id)"> mdi-cloud-upload </v-icon>
            </template>
            <!-- <template v-slot:[`item.action`]="{ item }">
              <span v-if="item.partner_id !== 0">
                <v-btn icon @click="goDetail()">
                  <v-icon class="ma-2" color="green" text-color="#E9A016">mdi-cloud-upload</v-icon>
                </v-btn>
              </span>
            </template> -->
          </v-data-table>
          <v-row justify="center" align-content="center" v-if="disableTable === true">
            <v-col cols="12" md="12" align="center" style="min-height: 636px;">
              <div style="padding-top: 90px;">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                  height="100%" width="100%" contain aspect-ratio="2"></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #1AB759;">
                <span style="font-weight: bold; font-size: 24px; line-height: 32px;">ยังไม่มีรายการสินค้า</span><br />
              </h2>
            </v-col>
          </v-row>
          <!-- <div><span style="color:red">หมายเหตุ</span> : จะสามารถเรียกคูเรียร์/พนักงานเข้ารับได้ทีละ 1 งานเท่านั้น โดย 1 งานมีกี่คำสั่งซื้อก็ได้ หากต้องการความช่วยเหลือ ให้ติดต่อทาง Flash โดยตรง</div> -->
          <!-- <div v-if="show" class="mt-1"><span style="color:red">หมายเหตุ</span> : หากต้องการที่จะพิมพ์บาร์โค้ด(Barcode) สามารถกดที่ "สัญญาลักษณ์บาร์โค้ด(Icon Barcode)"</div> -->
        </v-container>
      </v-card>
    </v-container>
  </div>
</template>

<script>
export default {
  data () {
    return {
      search: '',
      show: true,
      selected: [],
      headers: [
        { text: 'ลำดับที่', value: 'no', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'เลขที่ใบสั่งซื้อ', value: 'order_no', width: '140', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'บริษัทในเครือ', value: 'partner', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'หัวเรื่อง', value: 'topic', sortable: false, width: '140', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่เอกสาร', value: 'date', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'การจัดการ', value: 'action', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      data: [
        {
          no: 1,
          order_no: '123',
          partner_id: '011',
          partner: 'aaaaaaaaaaaa',
          topic: 'qwwertyuiop[]lkjhgfdsazxzxcvbnm,./qwertyuiop[]',
          date: '10/6/22'
        },
        {
          no: 2,
          order_no: '456',
          partner_id: '012',
          partner: 'aaaaaaaaaaaa',
          topic: 'qwwertyuiop[]lkjhgfdsazxzxcvbnm,./qwertyuiop[]',
          date: '10/6/22'
        }
      ],
      page: 1,
      pageCount: 0,
      itemsPerPage: 10,
      headerssss: [
        {
          text: 'Dessert (100g serving)',
          align: 'start',
          sortable: false,
          value: 'name'
        },
        { text: 'Calories', value: 'calories' },
        { text: 'Fat (g)', value: 'fat' },
        { text: 'Carbs (g)', value: 'carbs' },
        { text: 'Protein (g)', value: 'protein' },
        { text: 'Iron (%)', value: 'iron' }
      ],
      desserts: [
        {
          name: 'Frozen Yogurt',
          calories: 159,
          fat: 6.0,
          carbs: 24,
          protein: 4.0,
          iron: '1%'
        },
        {
          name: 'Ice cream sandwich',
          calories: 237,
          fat: 9.0,
          carbs: 37,
          protein: 4.3,
          iron: '1%'
        },
        {
          name: 'Eclair',
          calories: 262,
          fat: 16.0,
          carbs: 23,
          protein: 6.0,
          iron: '7%'
        },
        {
          name: 'Cupcake',
          calories: 305,
          fat: 3.7,
          carbs: 67,
          protein: 4.3,
          iron: '8%'
        }
      ]
    }
  },
  methods: {
    goDetail (item) {
      // console.log('go!!' + item)
      this.$router.push({ path: '/billingDetail' }).catch(() => {})
    }
  }
}
</script>

<style lang="scss">
  .elevation-1  th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
