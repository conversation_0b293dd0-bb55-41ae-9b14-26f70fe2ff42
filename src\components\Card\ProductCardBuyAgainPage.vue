<template>
  <v-col no-gutters cols="12" class="d-flex justify-center" style="margin-top:-50px" >
    <v-card v-if="!MobileSize && !IpadSize" class="d-flex align-center rounded-lg" height="240" width="600" style="background-color:#fff;">
        <div v-if="itemProduct.product_image !== null || itemProduct.product_image !== undefined || itemProduct.product_image !== ''" class="rounded-lg ml-2"  style=" width:175px; height:215px; ">
          <v-img class="rounded-lg " :src="itemProduct.product_image" width="175" height="215"></v-img>
        </div>
        <div v-else class="rounded-lg"  style="width:175px; height:215px;">
          <v-img class="rounded-lg" src="@/assets/NoImage.png" width="175" height="215"></v-img>
        </div>
        <div style="margin-right:0px">
          <h2 class="ml-2 text-truncate" style="width:185px"><b>{{itemProduct.product_name}} </b></h2>
          <p class="ml-2 text-truncate" style="font-size: 14px; width: 185px;">{{itemProduct.short_description}}</p>
          <v-row no-gutters class="ml-2">
            <v-rating
            v-model="itemProduct.stars"
            class="mr-3 "
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
            ></v-rating>
            <p style="margin-top:1px">ขายแล้ว {{itemProduct.total_sold | formatNumber}} ชิ้น</p>
          </v-row>
          <v-row no-gutters class="d-flex justify-space-between align-center ml-2">
            <div>
              <h1><b style="color:red">฿ {{itemProduct.fake_price}}</b></h1>
            </div>
            <div>
              <v-row no-gutters>
                <p><b style="color:#C4C4C4; text-decoration:line-through; ">฿ {{itemProduct.real_price}}</b></p>
                <v-chip color="#FEE7E8" text-color="#FF7C9C" class="ml-4" style="margin-top:1px" x-small>ส่วนลด -{{itemProduct.discount_percent}}%</v-chip>
              </v-row>
            </div>
          </v-row>
          <v-row no-gutters class="d-flex justify-space-between pl-3 pr-3" style="margin-top:0px">
            <v-chip @click="addToCart()" x-small class="rounded-pill" color="primary" style="padding-top:22px; padding-bottom:22px" outlined ><v-icon>mdi-cart</v-icon>หยิบใส่ตะกร้า</v-chip>
            <v-chip @click="addToCart('QuickAddProduct')" x-small class="rounded-pill" color="primary" style="padding-top:22px; padding-bottom:22px"><v-icon>mdi-shopping-outline</v-icon>สั่งซื้ออีกครั้ง</v-chip>
          </v-row>
        </div>
    </v-card>
    <v-card v-if="MobileSize" class="d-flex align-center rounded-lg" height="290" width="60vw" style="background-color:#fff;">
      <v-col >
        <v-row no-gutters style="margin-top:-25px" class="pa-0">
          <v-img class="rounded-lg " :src="itemProduct.img" width="50%" height="50%"></v-img>
        </v-row>
        <h4 class="text-truncate mt-1"><b>{{itemProduct.title}} </b></h4>
        <p style="font-size: 14px;">{{itemProduct.subtitle}}</p>
        <p style="font-size: 14px;">฿ {{itemProduct.price}}</p>
      </v-col>
    </v-card>
  </v-col>
</template>

<script>
import { Decode } from '@/services'
import Vue from 'vue'
export default {
  props: ['itemProduct'],
  data () {
    return {
      item: this.itemProduct
    }
  },
  created () {
    // console.log('--------->', this.item)
    this.formatSold()
  },
  watch: {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    formatSold () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'พัน'
        }
        return value.toString()
      })
    },
    async addToCart (ActionKey) {
      // console.log('ActionKey------->', ActionKey)
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // console.log('dataRole', dataRole.role)
      var companyDetail
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyDetail = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        // console.log('companyDetail', companyDetail)
      } else if (dataRole.role === 'sale_order') {
        this.companyId = localStorage.getItem('PartnerID')
        // console.log('this.companyIdAddToCartWithLogin', thsis.companyId)
      }
      const data = {
        seller_shop_id: this.itemProduct.seller_shop_id,
        role_user: dataRole.role,
        product_id: this.itemProduct.product_id,
        pay_type: this.itemProduct.pay_type,
        order_type: this.itemProduct.order_type,
        attribute_option_1: this.itemProduct.attribute_option_1 === 'yes' ? this.selection : '',
        attribute_option_2: this.itemProduct.attribute_option_2 === 'yes' ? this.selectionSize : '',
        quantity: this.itemProduct.quantity,
        company_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail.company.company_id,
        company_position: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.role_id : -1,
        com_perm_id: dataRole.role === 'ext_buyer' ? -1 : companyDetail !== undefined ? companyDetail.position.com_perm_id : -1
      }
      // console.log('data', data)
      await this.$store.dispatch('ActionAddToCart', data)
      const res = await this.$store.state.ModuleCart.stateAddToCart
      if (res.message === 'Add to Cart Success') {
        this.$EventBus.$emit('getCartPopOver')
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>เพิ่มสินค้าลงในรถเข็นเรียบร้อย</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย' })
        }
        if (ActionKey === 'QuickAddProduct') {
          this.$router.push({ path: '/shoppingcart' }).catch(() => {})
        }
      } else if (res.message === 'This product is already in cart') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h4>มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว</h4>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว' })
        }
      } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>ใส่ข้อมูลไม่ครบ</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'ใส่ข้อมูลไม่ครบ' })
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h3>SERVER ERROR</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'SERVER ERROR' })
        }
      } else if (res.message === 'Please insert quantity > 0') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', html: '<h4>กรุณาใส่จำนวนสินค้าอย่างน้อย 1 ชิ้น</h4>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'error', title: 'กรุณาใส่จำนวนสินค้าอย่างน้อย 1 ชิ้น' })
        }
      } else if (res.message === 'Not found product with attribute detail.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: '<h3>กรุณาเลือกตัวเลือกของสินค้าก่อน</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', title: 'กรุณาเลือกตัวเลือกของสินค้าก่อน' })
        }
      } else if (res.message === 'Not found this product.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', html: '<h3>กรุณาเลือกตัวเลือกของสินค้าก่อน</h3>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'warning', title: 'กรุณาเลือกตัวเลือกของสินค้าก่อน' })
        }
      } else if (res.message === 'Please clear same product in your cart before add a new product cause product data had change.') {
        if (this.MobileSize) {
          this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', html: '<h3>ไม่สามารถดำเนินการได้</h3><br><p>มีสินค้ารายการนี้อยู่ในรถเข็นของคุณแล้ว และสินค้านี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้านี้ใหม่</p>' })
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', title: 'ไม่สามารถดำเนินการได้', text: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้วของคุณแล้ว และสินค้านี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้านี้ใหม่' })
        }
      } else if (res.message === 'Select Product Limit') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', title: 'ไม่สามารถดำเนินการได้', text: 'ตระกร้าสินค้าสามารถเพิ่มสูงสุดได้ 30 รายการเท่านั้น' })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          text: `${res.message}`
        })
      }
    }
  }
}
</script>
