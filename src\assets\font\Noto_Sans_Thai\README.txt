Noto Sans Thai Variable Font
============================

This download contains Noto Sans Thai as both a variable font and static fonts.

Noto Sans Thai is a variable font with these axes:
  wdth
  wght

This means all the styles are contained in a single file:
  NotoSansThai-VariableFont_wdth,wght.ttf

If your app fully supports variable fonts, you can now pick intermediate styles
that aren’t available as static fonts. Not all apps support variable fonts, and
in those cases you can use the static font files for Noto Sans Thai:
  static/NotoSansThai_ExtraCondensed-Thin.ttf
  static/NotoSansThai_ExtraCondensed-ExtraLight.ttf
  static/NotoSansThai_ExtraCondensed-Light.ttf
  static/NotoSansThai_ExtraCondensed-Regular.ttf
  static/NotoSansThai_ExtraCondensed-Medium.ttf
  static/NotoSansThai_ExtraCondensed-SemiBold.ttf
  static/NotoSansThai_ExtraCondensed-Bold.ttf
  static/NotoSansThai_ExtraCondensed-ExtraBold.ttf
  static/NotoSansThai_ExtraCondensed-Black.ttf
  static/NotoSansThai_Condensed-Thin.ttf
  static/NotoSansThai_Condensed-ExtraLight.ttf
  static/NotoSansThai_Condensed-Light.ttf
  static/NotoSansThai_Condensed-Regular.ttf
  static/NotoSansThai_Condensed-Medium.ttf
  static/NotoSansThai_Condensed-SemiBold.ttf
  static/NotoSansThai_Condensed-Bold.ttf
  static/NotoSansThai_Condensed-ExtraBold.ttf
  static/NotoSansThai_Condensed-Black.ttf
  static/NotoSansThai_SemiCondensed-Thin.ttf
  static/NotoSansThai_SemiCondensed-ExtraLight.ttf
  static/NotoSansThai_SemiCondensed-Light.ttf
  static/NotoSansThai_SemiCondensed-Regular.ttf
  static/NotoSansThai_SemiCondensed-Medium.ttf
  static/NotoSansThai_SemiCondensed-SemiBold.ttf
  static/NotoSansThai_SemiCondensed-Bold.ttf
  static/NotoSansThai_SemiCondensed-ExtraBold.ttf
  static/NotoSansThai_SemiCondensed-Black.ttf
  static/NotoSansThai-Thin.ttf
  static/NotoSansThai-ExtraLight.ttf
  static/NotoSansThai-Light.ttf
  static/NotoSansThai-Regular.ttf
  static/NotoSansThai-Medium.ttf
  static/NotoSansThai-SemiBold.ttf
  static/NotoSansThai-Bold.ttf
  static/NotoSansThai-ExtraBold.ttf
  static/NotoSansThai-Black.ttf

Get started
-----------

1. Install the font files you want to use

2. Use your app's font picker to view the font family and all the
available styles

Learn more about variable fonts
-------------------------------

  https://developers.google.com/web/fundamentals/design-and-ux/typography/variable-fonts
  https://variablefonts.typenetwork.com
  https://medium.com/variable-fonts

In desktop apps

  https://theblog.adobe.com/can-variable-fonts-illustrator-cc
  https://helpx.adobe.com/nz/photoshop/using/fonts.html#variable_fonts

Online

  https://developers.google.com/fonts/docs/getting_started
  https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Fonts/Variable_Fonts_Guide
  https://developer.microsoft.com/en-us/microsoft-edge/testdrive/demos/variable-fonts

Installing fonts

  MacOS: https://support.apple.com/en-us/HT201749
  Linux: https://www.google.com/search?q=how+to+install+a+font+on+gnu%2Blinux
  Windows: https://support.microsoft.com/en-us/help/314960/how-to-install-or-remove-a-font-in-windows

Android Apps

  https://developers.google.com/fonts/docs/android
  https://developer.android.com/guide/topics/ui/look-and-feel/downloadable-fonts

License
-------
Please read the full license text (OFL.txt) to understand the permissions,
restrictions and requirements for usage, redistribution, and modification.

You can use them in your products & projects – print or digital,
commercial or otherwise.

This isn't legal advice, please consider consulting a lawyer and see the full
license for all details.
