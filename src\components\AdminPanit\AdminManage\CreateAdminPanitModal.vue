<template>
  <div class="text-center">
    <v-dialog v-model="openModalCreateAdmin" width="732" persistent>
      <v-card min-height="558px">
        <v-toolbar dark dense elevation="0" color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>กำหนดสิทธิ์การใช้งาน</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-row class="pt-2 px-5">
            <v-col cols="12">
              <v-img class="float-left" src="@/assets/icons/Buyer.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 16px; line-height: 26px; color: #333333;">กำหนดสิทธิ์การใช้งาน</v-card-title>
            </v-col>
          </v-row>
          <v-row justify="center" align-content="center" :class="MobileSize ? 'px-6' : 'px-12'">
            <v-col cols="12" :class="MobileSize ? '' : 'px-12'">
              <p>ค้นหาผู้ใช้งาน</p>
              <v-text-field @change="getsearchAdminUserList()" class="rounded-lg" v-model="searchAdminUser" placeholder="กรอก Username" outlined dense hide-details>
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col cols="12" class="px-12" v-if="notfoundAdminUser === false && !MobileSize">
              <v-card outlined>
                <div class="d-flex flex-no-wrap">
                  <v-avatar class="ma-4" size="132" tile>
                    <v-img :src="searchAdminUsertList.img_path" contain style="border-radius: 8px;" v-if="searchAdminUsertList.img_path !== null "></v-img>
                    <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                  </v-avatar>
                  <div class="ma-4">
                    <p>ชื่อ-สกุล : <b>{{ name }}</b></p>
                    <p>อีเมล : <b>{{ searchAdminUsertList.email }}</b></p>
                    <p>เบอร์โทรศัพท์ : <b>{{ searchAdminUsertList.phone }}</b></p>
                    <p class="mb-0">Username : <b>{{ searchAdminUsertList.username }}</b></p>
                  </div>
                </div>
              </v-card>
            </v-col>
            <v-col cols="12" class="px-6" v-else-if="notfoundAdminUser === false && MobileSize">
              <v-card outlined>
                <v-card-text class="px-0">
                  <div class="d-flex flex-no-wrap">
                    <v-avatar class="ma-4" size="60" tile>
                      <v-img :src="searchAdminUsertList.img_path" contain style="border-radius: 8px;" v-if="searchAdminUsertList.img_path !== null "></v-img>
                      <v-img src="@/assets/icons/businessman.jpg" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" v-else></v-img>
                    </v-avatar>
                    <div class="ma-4" style="font-size: 12px;">
                      <p>ชื่อ-สกุล : <br/><b>{{ name }}</b></p>
                      <p>อีเมล : <br/><b>{{ searchAdminUsertList.email }}</b></p>
                      <p>เบอร์โทรศัพท์ : <br/><b>{{ searchAdminUsertList.phone }}</b></p>
                      <p class="mb-0">Username : <br/><b>{{ searchAdminUsertList.username }}</b></p>
                    </div>
                  </div>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" :class="MobileSize ? 'px-6' : 'px-12'" v-if="notfoundAdminUser === false">
              <v-row no-gutters>
                <v-col cols="9" class="pt-2">
                  <span>Super Admin Platform</span>
                </v-col>
                <v-col cols="3" align="end">
                  <v-checkbox class="checkbox-admin float-right mb-0" dense v-model="userType" value="super_admin_platform" :rules="Rules.emptyCheckbox"></v-checkbox>
                </v-col>
                <v-col cols="9" class="pt-2">
                  <span>Admin Platform</span>
                </v-col>
                <v-col cols="3" align="end">
                  <v-checkbox class="checkbox-admin float-right mb-0" dense v-model="userType" value="admin_platform" :rules="Rules.emptyCheckbox"></v-checkbox>
                </v-col>
                <p style="font-size: 12px; color: #333333;"><b>หมายเหตุ: </b>กำหนดสิทธิ์การใช้งานได้ 1 สิทธิ์เท่านั้น</p>
              </v-row>
            </v-col>
            <v-col cols="12" class="px-12" v-if="notfoundAdminUser === true" align="center">
              <div style="padding-top: 30px;">
                <v-img src="@/assets/icons/notfoundAdminUser.png" max-height="146px" max-width="135px" height="100%" width="100%" contain></v-img>
              </div>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #333333;">
                <span style="font-weight: bold; font-size: 24px; line-height: 30px;">ไม่พบผู้ใช้งาน</span><br/>
              </h2>
            </v-col>
          </v-row>
          <v-row cols="12" :class="MobileSize ? 'px-6 mt-0 pb-2' : 'px-12 mt-0 pb-2'" v-if="notfoundAdminUser === false">
            <v-col cols="12" :class="MobileSize ? 'pt-0' : 'pt-0 px-12'" :align="MobileSize ? 'center' : 'end'">
              <v-btn outlined class="mr-2" color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn class="white--text" color="#27AB9C" @click="confirmSettingAdmin()">บันทึก</v-btn>
            </v-col>
          </v-row>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
// import { Decode } from '@/services'
// import eventBus from '@/components/eventBus'
export default {
  data () {
    return {
      userType: '',
      selectedItem: '',
      searchAdminUser: '',
      username: '',
      searchAdminUsertList: '',
      lazy: false,
      openModalCreateAdmin: false,
      notfoundAdminUser: Boolean,
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล'],
        emptyCheckbox: [v => !!v || '']
      }
    }
  },
  watch: {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    open () {
      this.openModalCreateAdmin = true
    },
    cancel () {
      this.clearData()
      this.openModalCreateAdmin = false
    },
    clearData () {
      this.searchAdminUser = ''
      this.userType = ''
      this.notfoundAdminUser = null
    },
    async getsearchAdminUserList () {
      this.$store.commit('openLoader')
      var sendData = {
        username: this.searchAdminUser
      }
      await this.$store.dispatch('actionsSearchAdminPlatform', sendData)
      var response = await this.$store.state.ModuleAdminManage.stateSearchAdminPlatform
      if (response.message === 'Get user data success.') {
        this.$store.commit('closeLoader')
        this.notfoundAdminUser = false
        this.searchAdminUsertList = response.data
        this.name = response.data.first_name + ' ' + response.data.last_name
        this.username = response.data.username
      } else if (response.message === 'Not found this user data.') {
        this.$store.commit('closeLoader')
        this.notfoundAdminUser = true
        this.$swal.fire({ icon: 'warning', text: 'ไม่พบข้อมูลผู้ใช้ที่จะเพิ่มเป็นแอดมิน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'Please select at least one option super admin or admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'คุณยังไม่ได้กำหนดสิทธิ์การใช้งานได้ให้กับผู้ใช้งานนี้', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is inactive.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ผู้ใช้งานที่ค้นหาถูกปิดการใช้งานอยู่', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    confirmSettingAdmin () {
      if (this.userType) {
        var userType = this.userType === 'super_admin_platform' ? 'Super Admin Platform' : 'Admin Platform'
        const msgText = 'คุณได้ทำการกำหนดสิทธิ์การใช้งาน ' + this.name + ' เป็น ' + userType + ' คุณต้องการทำรายการนี้ใช่ หรือไม่'
        this.$swal.fire({
          icon: 'warning',
          text: msgText,
          showCancelButton: true,
          confirmButtonText: 'ตกลง',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.createAdmin()
          } else if (result.dismiss === this.$swal.DismissReason.cancel) {
          }
        }).catch(() => {
        })
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณากำหนดสิทธิ์การใช้งาน', showConfirmButton: false, timer: 1500 })
      }
    },
    async createAdmin () {
      this.$store.commit('openLoader')
      var sendData = {
        username: this.username,
        admin_type: this.userType,
        status_user: 'active'
      }
      // status_user active inactive
      await this.$store.dispatch('actionsCreateAdminPlatform', sendData)
      var response = await this.$store.state.ModuleAdminManage.stateCreateAdminPlatform
      if (response.message === 'Insert user data success.') {
        this.$store.commit('closeLoader')
        this.openModalCreateAdmin = false
        this.clearData()
        this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', title: 'ดำเนินการสำเร็จ' })
        this.$EventBus.$emit('createAdminPanitSuccess')
      } else if (response.message === 'Not found this user data.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ไม่พบข้อมูลผู้ใช้ที่จะเพิ่มเป็นแอดมิน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user has already been admin platform.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'คนที่กำลังจะเพิ่มเป็นแอดมินของระบบอยู่แล้ว', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is not super admin.') {
        this.$store.commit('closeLoader')
        this.openModalCreateAdmin = false
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดูรายละเอียดได้เนื่องจากคุณไม่มีสิทธิ์ Super Admin Platform', showConfirmButton: false, timer: 2500 })
      } else if (response.message === 'Please select at least one option super admin or admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณากำหนดสิทธิ์การใช้งาน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.openModalCreateAdmin = false
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    }
  }
}
</script>
<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
</style>
