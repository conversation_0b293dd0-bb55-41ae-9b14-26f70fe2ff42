<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%" :class="MobileSize ? 'pa-2' : ''">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">
        แก้ไข Member ร้านค้า
      </v-card-title>
      <v-card-title v-else style="font-weight: 700;">
        <v-icon color="#27AB9C" class="mr-2" @click="backToManage()">mdi-chevron-left</v-icon>
        แก้ไข Member ร้านค้า
      </v-card-title>

      <v-card style="background-color: #f9f9f9;" elevation="0" class="pa-2">
        <v-col cols="12">
          <v-icon>mdi-circle-medium</v-icon>
          <span style="font-size: 16px;"><b>Member</b></span>
        </v-col>
        <v-col cols="12">
          <div v-if="MobileSize || IpadSize">
            <v-row v-for="(member, index) in membersList" :key="index">
              <v-col cols="12">
                <span style="font-size: 16px;">ระดับ :</span>
                <v-text-field v-model="member.level" dense outlined hide-details placeholder="ระบุระดับ" />
              </v-col>
              <v-col cols="12">
                <span style="font-size: 16px;">ยอด :</span>
                <v-text-field v-model="member.amount" dense outlined hide-details placeholder="ระบุยอด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
              </v-col>
              <v-col cols="12">
                <span style="font-size: 16px;">ประเภทส่วนลด :</span>
                <v-radio-group v-model="member.discountType" row dense hide-details style="margin-top: 0px; padding-top: 0px;">
                  <v-radio label="ไม่ใช้งาน" value="no" />
                  <v-radio label="%" value="%" />
                  <v-radio label="บาท" value="บาท" />
                </v-radio-group>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 16px;">ส่วนลด :</span>
                <v-text-field v-model="member.discount" :disabled="member.discountType === 'no'" dense outlined hide-details placeholder="ระบุส่วนลด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
              </v-col>
              <v-col cols="12" class="text-center d-flex align-center justify-center pt-0">
                <v-btn icon v-if="isAddButton(index)" @click="addNewMember">
                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                </v-btn>
                <v-btn icon v-if="isRemoveButton(index)" @click="removeMember(index)">
                  <v-icon color="red">mdi-minus-circle</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </div>
          <div v-else-if="IpadProSize">
            <v-row v-for="(member, index) in membersList" :key="index">
              <v-col cols="6">
                <span style="font-size: 16px;">ระดับ :</span>
                <v-text-field v-model="member.level" dense outlined hide-details placeholder="ระบุระดับ" />
              </v-col>
              <v-col cols="6">
                <span style="font-size: 16px;">ยอด :</span>
                <v-text-field v-model="member.amount" dense outlined hide-details placeholder="ระบุยอด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
              </v-col>
              <v-col cols="6">
                <span style="font-size: 16px;">ประเภทส่วนลด :</span>
                <v-radio-group v-model="member.discountType" row dense hide-details style="margin-top: 0px; padding-top: 0px;">
                  <v-radio label="ไม่ใช้งาน" value="no" />
                  <v-radio label="%" value="%" />
                  <v-radio label="บาท" value="บาท" />
                </v-radio-group>
              </v-col>
              <v-col cols="5">
                <span style="font-size: 16px;">ส่วนลด :</span>
                <v-text-field v-model="member.discount" :disabled="member.discountType === 'no'" dense outlined hide-details placeholder="ระบุส่วนลด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
              </v-col>
              <v-col cols="1" class="text-center d-flex align-center justify-center pt-9">
                <v-btn icon v-if="isAddButton(index)" @click="addNewMember">
                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                </v-btn>
                <v-btn icon v-if="isRemoveButton(index)" @click="removeMember(index)">
                  <v-icon color="red">mdi-minus-circle</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </div>
          <div v-else>
            <v-row style="position: sticky; top: 0; z-index: 10;">
              <v-col cols="3" class="pb-0"><span style="font-size: 16px;">ระดับ :</span></v-col>
              <v-col cols="3" class="pb-0"><span style="font-size: 16px;">ยอด :</span></v-col>
              <v-col cols="3" class="pb-0"><span style="font-size: 16px;">ประเภทส่วนลด :</span></v-col>
              <v-col cols="2" class="pb-0"><span style="font-size: 16px;">ส่วนลด :</span></v-col>
              <!-- <v-col cols="2" class="pb-0"><span style="font-size: 16px;">แต้มพิเศษ :</span></v-col> -->
              <v-col cols="1" class="pb-0"></v-col>
            </v-row>

            <v-row v-for="(member, index) in membersList" :key="index">
              <v-col cols="3" class="pt-0">
                <v-text-field v-model="member.level" dense outlined hide-details placeholder="ระบุระดับ" />
              </v-col>
              <v-col cols="3" class="pt-0">
                <v-text-field v-model="member.amount" dense outlined hide-details placeholder="ระบุยอด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
              </v-col>
              <v-col cols="3" class="pt-0">
                <v-radio-group v-model="member.discountType" row dense hide-details style="margin-top: 5px;">
                  <v-radio label="ไม่ใช้งาน" value="no" />
                  <v-radio label="%" value="%" />
                  <v-radio label="บาท" value="บาท" />
                </v-radio-group>
              </v-col>
              <v-col cols="2" class="pt-0">
                <v-text-field v-model="member.discount" :disabled="member.discountType === 'no'" dense outlined hide-details placeholder="ระบุส่วนลด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
              </v-col>
              <v-col cols="1" class="text-center d-flex align-center justify-center pt-0">
                <v-btn icon v-if="isAddButton(index)" @click="addNewMember">
                  <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                </v-btn>
                <v-btn icon v-if="isRemoveButton(index)" @click="removeMember(index)">
                  <v-icon color="red">mdi-minus-circle</v-icon>
                </v-btn>
              </v-col>
            </v-row>
          </div>
        </v-col>
        <v-col cols="12">
          <v-row v-if="MobileSize || IpadSize">
            <v-col cols="12">
              <v-icon>mdi-circle-medium</v-icon>
              <span style="font-size: 16px;"><b>เงื่อนไขการรักษา Member</b></span>
            </v-col>
            <v-col cols="12">
              <v-radio-group v-model="conditions" row dense hide-details style="margin-top: 0px; padding-top: 0px;" @change="onToggleConditions">
                <v-radio label="ใช้งาน" value="yes" />
                <v-radio label="ไม่ใช้งาน" value="no" />
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row v-else-if="IpadProSize">
            <v-col cols="auto">
              <v-icon>mdi-circle-medium</v-icon>
              <span style="font-size: 16px;"><b>เงื่อนไขการรักษา Member</b></span>
            </v-col>
            <v-col cols="auto">
              <v-radio-group v-model="conditions" row dense hide-details style="margin-top: 0px; padding-top: 0px;" @change="onToggleConditions">
                <v-radio label="ใช้งาน" value="yes" />
                <v-radio label="ไม่ใช้งาน" value="no" />
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col cols="3">
              <v-icon>mdi-circle-medium</v-icon>
              <span style="font-size: 16px;"><b>เงื่อนไขการรักษา Member</b></span>
            </v-col>
            <v-col cols="3">
              <v-radio-group v-model="conditions" row dense hide-details style="margin-top: 0px; padding-top: 0px;" @change="onToggleConditions">
                <v-radio label="ใช้งาน" value="yes" />
                <v-radio label="ไม่ใช้งาน" value="no" />
              </v-radio-group>
            </v-col>
          </v-row>
        </v-col>
        <div v-if="conditions === 'yes'">
          <div v-if="MobileSize || IpadSize">
            <v-col cols="12">
              <v-row>
                <v-col cols="12">
                  <span style="font-size: 16px;">ระยะเวลาการสะสมยอด :</span>
                  <v-select v-model="selectedTime" :items="membersTime" item-text="label" item-value="value" placeholder="เลือกระยะเวลา" dense outlined hide-details />
                </v-col>
                <v-col cols="12">
                  <span style="font-size: 16px;">วันที่เริ่มต้น :</span>
                  <v-menu v-model="startDateMenu" :close-on-content-click="false" transition="scale-transition" offset-y min-width="290px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field v-model="startDateFormatted" placeholder="เลือกวันที่เริ่มต้น" dense outlined hide-details v-bind="attrs" v-on="on" />
                    </template>
                    <v-card>
                      <v-date-picker v-model="tempStartDate" locale="th-TH" scrollable :min="today" />
                      <v-card-actions class="justify-end">
                        <v-btn rounded color="#FFFFFF" style="color: #333333;" @click="cancelStartDate">ยกเลิก</v-btn>
                        <v-btn rounded color="#27AB9C" style="color: #FFFFFF;" :disabled="tempStartDate === ''" @click="confirmStartDate">ตกลง</v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-menu>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" v-if="membersList.some(member => member.level || member.amount || member.discountType !== 'no' || member.discount)">
              <v-row v-for="(member, index) in ConditionsList" :key="'member-range-' + index">
                <v-col cols="12">
                  <span style="font-size: 16px;">ระดับ :</span>
                  <v-text-field v-model="member.ConditionsLevel" dense outlined hide-details placeholder="ระดับ" disabled />
                </v-col>
                <v-col cols="12" class="pb-0"><span style="font-size: 16px;">ยอดรักษา Member :</span></v-col>
                <v-col cols="5" class="pt-0">
                  <v-text-field v-model="member.amountMin" dense outlined hide-details placeholder="ระบุยอดเริ่มต้น" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                </v-col>
                <v-col cols="2" class="text-center d-flex align-center justify-center pt-0">
                  <span style="font-size: 16px;">ถึง</span>
                </v-col>
                <v-col cols="5" class="pt-0">
                  <v-text-field v-model="member.amountMax" dense outlined hide-details placeholder="ระบุยอดสิ้นสุด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                </v-col>
              </v-row>
            </v-col>
          </div>
          <div v-else-if="IpadProSize">
            <v-col cols="12">
              <v-row>
                <v-col cols="6">
                  <span style="font-size: 16px;">ระยะเวลาการสะสมยอด :</span>
                  <v-select v-model="selectedTime" :items="membersTime" item-text="label" item-value="value" placeholder="เลือกระยะเวลา" dense outlined hide-details />
                </v-col>
                <v-col cols="6">
                  <span style="font-size: 16px;">วันที่เริ่มต้น :</span>
                  <v-menu v-model="startDateMenu" :close-on-content-click="false" transition="scale-transition" offset-y min-width="290px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field v-model="startDateFormatted" placeholder="เลือกวันที่เริ่มต้น" dense outlined hide-details v-bind="attrs" v-on="on" />
                    </template>
                    <v-card>
                      <v-date-picker v-model="tempStartDate" locale="th-TH" scrollable :min="today" />
                      <v-card-actions class="justify-end">
                        <v-btn rounded color="#FFFFFF" style="color: #333333;" @click="cancelStartDate">ยกเลิก</v-btn>
                        <v-btn rounded color="#27AB9C" style="color: #FFFFFF;" :disabled="tempStartDate === ''" @click="confirmStartDate">ตกลง</v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-menu>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" v-if="membersList.some(member => member.level || member.amount || member.discountType !== 'no' || member.discount)">
              <v-row v-for="(member, index) in ConditionsList" :key="'member-range-' + index">
                <v-col cols="12">
                  <span style="font-size: 16px;">ระดับ :</span>
                  <v-text-field v-model="member.ConditionsLevel" dense outlined hide-details placeholder="ระดับ" disabled />
                </v-col>
                <v-col cols="12" class="pb-0"><span style="font-size: 16px;">ยอดรักษา Member :</span></v-col>
                <v-col cols="5" class="pt-0">
                  <v-text-field v-model="member.amountMin" dense outlined hide-details placeholder="ระบุยอดเริ่มต้น" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                </v-col>
                <v-col cols="2" class="text-center d-flex align-center justify-center pt-0">
                  <span style="font-size: 16px;">ถึง</span>
                </v-col>
                <v-col cols="5" class="pt-0">
                  <v-text-field v-model="member.amountMax" dense outlined hide-details placeholder="ระบุยอดสิ้นสุด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                </v-col>
              </v-row>
            </v-col>
          </div>
          <div v-else>
            <v-col cols="12">
              <v-row>
                <v-col cols="3">
                  <span style="font-size: 16px;">ระยะเวลาการสะสมยอด :</span>
                  <v-select v-model="selectedTime" :items="membersTime" item-text="label" item-value="value" placeholder="เลือกระยะเวลา" dense outlined hide-details />
                </v-col>
                <v-col cols="3">
                  <span style="font-size: 16px;">วันที่เริ่มต้น :</span>
                  <v-menu v-model="startDateMenu" :close-on-content-click="false" transition="scale-transition" offset-y min-width="290px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field v-model="startDateFormatted" placeholder="เลือกวันที่เริ่มต้น" dense outlined hide-details v-bind="attrs" v-on="on" />
                    </template>
                    <v-card>
                      <v-date-picker v-model="tempStartDate" locale="th-TH" scrollable :min="today" />
                      <v-card-actions class="justify-end">
                        <v-btn rounded color="#FFFFFF" style="color: #333333;" @click="cancelStartDate">ยกเลิก</v-btn>
                        <v-btn rounded color="#27AB9C" style="color: #FFFFFF;" :disabled="tempStartDate === ''" @click="confirmStartDate">ตกลง</v-btn>
                      </v-card-actions>
                    </v-card>
                  </v-menu>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" v-if="membersList.some(member => member.level || member.amount || member.discountType !== 'no' || member.discount)">
              <v-row>
                <v-col cols="3" class="pb-0"><span style="font-size: 16px;">ระดับ :</span></v-col>
                <v-col cols="6" class="pb-0"><span style="font-size: 16px;">ยอดรักษา Member :</span></v-col>
              </v-row>
              <v-row v-for="(member, index) in ConditionsList" :key="'member-range-' + index">
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="member.ConditionsLevel" dense outlined hide-details placeholder="ระดับ" disabled />
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="member.amountMin" dense outlined hide-details placeholder="ระบุยอดเริ่มต้น" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                </v-col>
                <v-col cols="1" class="text-center d-flex align-center justify-center pt-0">
                  <span style="font-size: 16px;">ถึง</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="member.amountMax" dense outlined hide-details placeholder="ระบุยอดสิ้นสุด" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                </v-col>
              </v-row>
            </v-col>
          </div>
        </div>
        <v-col cols="12">
          <v-row v-if="MobileSize || IpadSize">
            <v-col cols="12">
              <v-icon>mdi-circle-medium</v-icon>
              <span style="font-size: 16px;"><b>สิทธิพิเศษสำหรับ Member</b></span>
            </v-col>
            <v-col cols="12">
              <v-radio-group v-model="special" row dense hide-details style="margin-top: 0px; padding-top: 0px;" @change="onToggleSpecial">
                <v-radio label="ใช้งาน" value="yes" />
                <v-radio label="ไม่ใช้งาน" value="no" />
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row v-else-if="IpadProSize">
            <v-col cols="auto">
              <v-icon>mdi-circle-medium</v-icon>
              <span style="font-size: 16px;"><b>สิทธิพิเศษสำหรับ Member</b></span>
            </v-col>
            <v-col cols="auto">
              <v-radio-group v-model="special" row dense hide-details style="margin-top: 0px; padding-top: 0px;" @change="onToggleSpecial">
                <v-radio label="ใช้งาน" value="yes" />
                <v-radio label="ไม่ใช้งาน" value="no" />
              </v-radio-group>
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col cols="3">
              <v-icon>mdi-circle-medium</v-icon>
              <span style="font-size: 16px;"><b>สิทธิพิเศษสำหรับ Member</b></span>
            </v-col>
            <v-col cols="3">
              <v-radio-group v-model="special" row dense hide-details style="margin-top: 0px; padding-top: 0px;" @change="onToggleSpecial">
                <v-radio label="ใช้งาน" value="yes" />
                <v-radio label="ไม่ใช้งาน" value="no" />
              </v-radio-group>
            </v-col>
          </v-row>
        </v-col>
        <div v-if="special === 'yes'">
          <div v-if="MobileSize || IpadSize">
            <div v-for="(condition, i) in specialConditions" :key="'condition-' + i">
              <v-col cols="12">
                <v-row>
                  <v-col cols="10">
                      <span style="font-size: 16px;">เงื่อนไข :</span>
                      <v-select
                        :items="availableSpecialTypes(i)"
                        item-text="text"
                        item-value="value"
                        v-model="condition.type"
                        placeholder="เลือกประเภทพิเศษ"
                        dense
                        outlined
                        hide-details
                      />
                  </v-col>
                  <v-col cols="2" class="text-center d-flex align-center justify-center pt-8">
                    <v-btn icon v-if="showAddButton(i)" @click="addSpecialCondition">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                    </v-btn>

                    <v-btn icon v-if="showRemoveButton(i)" @click="removeSpecialCondition(i)">
                      <v-icon color="red">mdi-minus-circle</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>

              <v-col cols="12" v-if="membersList.some(member => member.level || member.amount || member.discountType !== 'no' || member.discount)">
                <v-row v-for="(member, j) in condition.members" :key="'member-' + i + '-' + j">
                  <v-col cols="12">
                    <span style="font-size: 16px;">ระดับ :</span>
                    <v-text-field
                      v-model="member.specialLevel"
                      dense
                      outlined
                      hide-details
                      disabled
                    />
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16px;">มูลค่า :</span>
                    <v-text-field v-model="member.value" dense outlined hide-details oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16px;">ขั้นต่ำการซื้อ :</span>
                    <v-text-field v-model="member.minimum" dense outlined hide-details oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16px;">หน่วย :</span>
                    <v-radio-group
                      v-model="member.unit"
                      row
                      dense
                      hide-details
                      style="margin-top: 5px;"
                    >
                      <v-radio label="%" value="percent" />
                      <v-radio label="บาท" value="bath" />
                      <v-radio label="แต้ม" value="point" />
                    </v-radio-group>
                  </v-col>
                  <v-col cols="12" class="text-center d-flex align-center justify-center">
                    <v-btn icon v-if="showAddMemberButton(i, j)" @click="addMemberToSpecialCondition(i)">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                    </v-btn>
                    <v-btn icon v-if="showRemoveMemberButton(i, j)" @click="removeMemberFromSpecialCondition(i, j)">
                      <v-icon color="red">mdi-minus-circle</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">รายละเอียด :</span>
                    <v-textarea outlined v-model="condition.detail"></v-textarea>
                  </v-col>
                </v-row>
              </v-col>
            </div>
          </div>
          <div v-else-if="IpadProSize">
            <div v-for="(condition, i) in specialConditions" :key="'condition-' + i">
              <v-col cols="12">
                <v-row>
                  <v-col cols="6">
                      <span style="font-size: 16px;">เงื่อนไข :</span>
                      <v-select
                        :items="availableSpecialTypes(i)"
                        item-text="text"
                        item-value="value"
                        v-model="condition.type"
                        placeholder="เลือกประเภทพิเศษ"
                        dense
                        outlined
                        hide-details
                      />
                  </v-col>
                  <v-col cols="1" class="text-center d-flex align-center justify-center pt-8">
                    <v-btn icon v-if="showAddButton(i)" @click="addSpecialCondition">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                    </v-btn>

                    <v-btn icon v-if="showRemoveButton(i)" @click="removeSpecialCondition(i)">
                      <v-icon color="red">mdi-minus-circle</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>

              <v-col cols="12" v-if="membersList.some(member => member.level || member.amount || member.discountType !== 'no' || member.discount)">
                <v-row v-for="(member, j) in condition.members" :key="'member-' + i + '-' + j">
                  <v-col cols="6">
                    <span style="font-size: 16px;">ระดับ :</span>
                    <v-text-field
                      v-model="member.specialLevel"
                      dense
                      outlined
                      hide-details
                      disabled
                    />
                  </v-col>
                  <v-col cols="6">
                    <span style="font-size: 16px;">มูลค่า :</span>
                    <v-text-field v-model="member.value" dense outlined hide-details oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                  </v-col>
                  <v-col cols="6">
                    <span style="font-size: 16px;">ขั้นต่ำการซื้อ :</span>
                    <v-text-field v-model="member.minimum" dense outlined hide-details oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                  </v-col>
                  <v-col cols="5">
                    <span style="font-size: 16px;">หน่วย :</span>
                    <v-radio-group
                      v-model="member.unit"
                      row
                      dense
                      hide-details
                      style="margin-top: 5px;"
                    >
                      <v-radio label="%" value="percent" />
                      <v-radio label="บาท" value="bath" />
                      <v-radio label="แต้ม" value="point" />
                    </v-radio-group>
                  </v-col>
                  <v-col cols="1" class="text-center d-flex align-center justify-center">
                    <v-btn icon v-if="showAddMemberButton(i, j)" @click="addMemberToSpecialCondition(i)">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                    </v-btn>
                    <v-btn icon v-if="showRemoveMemberButton(i, j)" @click="removeMemberFromSpecialCondition(i, j)">
                      <v-icon color="red">mdi-minus-circle</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">รายละเอียด :</span>
                    <v-textarea outlined v-model="condition.detail"></v-textarea>
                  </v-col>
                </v-row>
              </v-col>
            </div>
          </div>
          <div v-else>
            <div v-for="(condition, i) in specialConditions" :key="'condition-' + i">
              <v-col cols="12">
                <v-row>
                  <v-col cols="3">
                      <span style="font-size: 16px;">เงื่อนไข :</span>
                      <v-select
                        :items="availableSpecialTypes(i)"
                        item-text="text"
                        item-value="value"
                        v-model="condition.type"
                        placeholder="เลือกประเภทพิเศษ"
                        dense
                        outlined
                        hide-details
                      />
                  </v-col>
                  <v-col cols="1" class="text-center d-flex align-center justify-center pt-8">
                    <v-btn icon v-if="showAddButton(i)" @click="addSpecialCondition">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                    </v-btn>

                    <v-btn icon v-if="showRemoveButton(i)" @click="removeSpecialCondition(i)">
                      <v-icon color="red">mdi-minus-circle</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-col>

              <v-col cols="12" v-if="membersList.some(member => member.level || member.amount || member.discountType !== 'no' || member.discount)">
                <v-row v-for="(member, j) in condition.members" :key="'member-' + i + '-' + j">
                  <v-col cols="3">
                    <span style="font-size: 16px;">ระดับ :</span>
                    <v-text-field
                      v-model="member.specialLevel"
                      dense
                      outlined
                      hide-details
                      disabled
                    />
                  </v-col>
                  <v-col cols="2">
                    <span style="font-size: 16px;">มูลค่า :</span>
                    <v-text-field v-model="member.value" dense outlined hide-details oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                  </v-col>
                  <v-col cols="2">
                    <span style="font-size: 16px;">ขั้นต่ำการซื้อ :</span>
                    <v-text-field v-model="member.minimum" dense outlined hide-details oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" />
                  </v-col>
                  <v-col cols="3">
                    <span style="font-size: 16px;">หน่วย :</span>
                    <v-radio-group
                      v-model="member.unit"
                      row
                      dense
                      hide-details
                      style="margin-top: 5px;"
                    >
                      <v-radio label="%" value="percent" />
                      <v-radio label="บาท" value="bath" />
                      <v-radio label="แต้ม" value="point" />
                    </v-radio-group>
                  </v-col>
                  <v-col cols="2" class="text-center d-flex align-center justify-center">
                    <v-btn icon v-if="showAddMemberButton(i, j)" @click="addMemberToSpecialCondition(i)">
                      <v-icon color="#27AB9C">mdi-plus-circle</v-icon>
                    </v-btn>
                    <v-btn icon v-if="showRemoveMemberButton(i, j)" @click="removeMemberFromSpecialCondition(i, j)">
                      <v-icon color="red">mdi-minus-circle</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px;">รายละเอียด :</span>
                    <v-textarea outlined v-model="condition.detail"></v-textarea>
                  </v-col>
                </v-row>
              </v-col>
            </div>
          </div>
        </div>
      </v-card>

      <v-col cols="12" class="d-flex justify-end">
        <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="backToManage()" class="mr-2"><span style="font-size: 16px;">ยกเลิก</span></v-btn>
        <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" :disabled="!isFormValid" @click="CCMembers()"><span style="font-size: 16px;">ยืนยัน</span></v-btn>
      </v-col>
    </v-card>
  </v-container>
</template>

<script>
import moment from 'moment'
export default {
  data () {
    return {
      membersList: [
        {
          level: '',
          amount: '',
          discountType: 'no',
          discount: ''
          // promotionPoint: ''
        }
      ],
      ConditionsList: [
        {
          ConditionsLevel: '',
          amountMin: '',
          amountMax: ''
        }
      ],
      conditions: 'no',
      membersTime: [
        { label: '3 เดือน', value: '3m' },
        { label: '6 เดือน', value: '6m' },
        { label: '1 ปี', value: '1y' }
      ],
      selectedTime: '',
      today: moment().format('YYYY-MM-DD'),
      startDateMenu: false,
      startDate: '',
      tempStartDate: '',
      special: 'no',
      itemSpecial: [
        { text: 'เดือนเกิดแจกแต้ม', value: 0 },
        { text: 'ส่วนลดพิเศษวันเกิด', value: 1 },
        { text: 'คูณแต้ม x เท่าวันเกิด', value: 2 }
      ],
      selectedSpecial: null,
      specialList: [
        {
          specialLevel: '',
          value: '',
          minimum: '',
          unit: 'percent'
        }
      ],
      specialConditions: [],
      lastRemovedLevel: null
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    startDateFormatted () {
      return this.startDate ? moment(this.startDate).format('DD/MM/YYYY') : ''
    },
    isFormValid () {
      // ตรวจสอบว่า membersList ทุกคนต้องมี level และ amount
      const membersValid = this.membersList.every(member =>
        member.level && member.amount
      )

      // ตรวจสอบว่า ถ้า discountType ไม่ใช่ no ต้องมี discount
      const discountsValid = this.membersList.every(member => {
        if (member.discountType !== 'no') {
          return member.discount !== '' && member.discount !== null
        }
        return true
      })

      // ตรวจสอบว่า special === 'yes' แล้ว specialConditions ต้องครบ
      const specialsValid = this.special !== 'yes' || this.specialConditions.every(condition => {
        return condition.type && condition.members.every(member => member.value && member.minimum && member.unit)
      })

      return membersValid && discountsValid && specialsValid
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/EditMembersMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/EditMembers' }).catch(() => {})
      }
    },
    membersList: {
      handler (val) {
        this.ConditionsList = val.map(m => ({
          ConditionsLevel: m.level,
          amountMin: '',
          amountMax: ''
        }))
        this.specialList = val.map(m => ({
          specialLevel: m.level,
          value: '',
          minimum: '',
          unit: 'percent'
        }))
        this.specialConditions = this.specialConditions.map(condition => ({
          ...condition,
          members: val.map(m => ({
            specialLevel: m.level,
            value: '',
            minimum: '',
            unit: 'percent'
          }))
        }))
      },
      deep: true,
      immediate: true
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
  },
  methods: {
    backToManage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/ManageMembersMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageMembers' }).catch(() => {})
      }
    },
    addNewMember () {
      if (this.membersList.length >= 5) return
      this.membersList.push({
        level: '',
        amount: '',
        discountType: 'no',
        discount: ''
        // promotionPoint: ''
      })
    },
    removeMember (index) {
      this.membersList.splice(index, 1)
    },
    isAddButton (index) {
      return this.membersList.length < 5 && index === this.membersList.length - 1
    },
    isRemoveButton (index) {
      return this.membersList.length > 1 && index !== 0
    },
    cancelStartDate () {
      this.startDate = ''
      this.startDateMenu = false
    },
    confirmStartDate () {
      this.startDate = this.tempStartDate
      this.startDateMenu = false
    },
    onToggleConditions (value) {
      if (value === 'no') {
        this.selectedTime = null
        this.tempStartDate = ''
        this.ConditionsList = []
      }
    },
    onToggleSpecial (value) {
      if (value === 'no') {
        this.selectedSpecial = null
        this.specialConditions = []
      } else if (value === 'yes') {
        this.specialConditions = [
          {
            type: null,
            detail: '',
            members: this.membersList.map(m => ({
              specialLevel: m.level,
              value: '',
              minimum: '',
              unit: 'percent'
            }))
          }
        ]
      }
    },
    availableSpecialTypes (index) {
      const selected = this.specialConditions.map((c, i) => i !== index ? c.type : null)
      return this.itemSpecial.filter(item => !selected.includes(item.value))
    },
    showAddButton (index) {
      return (
        this.specialConditions.length < this.itemSpecial.length &&
        index === this.specialConditions.length - 1
      )
    },
    showRemoveButton (index) {
      return this.specialConditions.length > 1
    },
    addSpecialCondition () {
      if (this.specialConditions.length >= this.itemSpecial.length) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'ไม่สามารถเพิ่มเงื่อนไขพิเศษได้อีก'
        })
        return
      }

      this.specialConditions.push({
        type: null,
        detail: '',
        members: this.membersList.map(m => ({
          specialLevel: m.level,
          value: '',
          minimum: '',
          unit: 'percent'
        }))
      })
    },
    removeSpecialCondition (index) {
      this.specialConditions.splice(index, 1)
    },
    showAddMemberButton (conditionIndex, memberIndex) {
      const members = this.specialConditions[conditionIndex].members
      const levelCount = this.membersList.length
      // แสดงปุ่ม + ก็ต่อเมื่อ จำนวนสมาชิกยังไม่เต็ม และเป็นสมาชิกตัวสุดท้าย
      return members.length < levelCount && memberIndex === members.length - 1
    },
    showRemoveMemberButton (conditionIndex, memberIndex) {
      const members = this.specialConditions[conditionIndex].members
      // แสดงปุ่ม - ก็ต่อเมื่อมีสมาชิกมากกว่า 1
      return members.length > 1
    },
    addMemberToSpecialCondition (conditionIndex) {
      const condition = this.specialConditions[conditionIndex]
      const levelCount = this.membersList.length
      const currentCount = condition.members.length

      if (currentCount >= levelCount) return

      const usedLevels = condition.members.map(m => m.specialLevel)
      const nextLevelObj = this.membersList.find(m => !usedLevels.includes(m.level))
      const nextLevel = nextLevelObj ? nextLevelObj.level : ''

      condition.members.push({
        specialLevel: nextLevel,
        value: '',
        minimum: '',
        unit: 'percent'
      })

      // 🎯 จัดเรียงสมาชิกใหม่ตามลำดับใน membersList
      condition.members.sort((a, b) => {
        const aIndex = this.membersList.findIndex(m => m.level === a.specialLevel)
        const bIndex = this.membersList.findIndex(m => m.level === b.specialLevel)
        return aIndex - bIndex
      })
    },
    removeMemberFromSpecialCondition (conditionIndex, memberIndex) {
      const condition = this.specialConditions[conditionIndex]
      condition.members.splice(memberIndex, 1)

      // ✅ จัดเรียงใหม่หลังลบ
      condition.members.sort((a, b) => {
        const aIndex = this.membersList.findIndex(m => m.level === a.specialLevel)
        const bIndex = this.membersList.findIndex(m => m.level === b.specialLevel)
        return aIndex - bIndex
      })
    },
    CCMembers () {
      if (!this.isFormValid) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: 'กรุณากรอกข้อมูลให้ครบถ้วน'
        })
        return
      }
      const payload = {
        members: this.membersList,
        memberRetention: this.conditions === 'yes' ? this.ConditionsList : [],
        retentionSetting: {
          active: this.conditions === 'yes',
          accumulationPeriod: this.selectedTime,
          startDate: this.startDate
        },
        specialPrivileges: {
          active: this.special === 'yes',
          conditions: this.special === 'yes' ? this.specialConditions : []
        }
      }
      console.log('Payload:', payload)
    }
  }
}
</script>

<style>

</style>
