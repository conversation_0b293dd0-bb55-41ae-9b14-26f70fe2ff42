<template>
<v-row :class="MobileSize || IpadSize ? 'pt-0' : ''">
  <v-col class="d-flex justify-center" v-if="!MobileSize && !IpadSize">
    <div style="position: relative" v-if="loadingComponent === false">
      <span style="font-size: 23px; color: #fff; margin-left: 50px; margin-top:20px; position: absolute; z-index:3"><b>หมวดหมู่สินค้า</b></span>
        <v-btn v-if="IpadProSize" elevation="0" dense color="#FFFFFF" class="rounded-lg" style="position: absolute; z-index:4;margin-top: 21px; margin-left: 66vw; padding-left: 10px; width: 120px; height: 38px; color: #1F87AF; font-size:16px; font-weight:600;" @click.prevent="GetAllProducts('all_product_cat')" :href="pathFullProduct">สินค้าทั้งหมด</v-btn>
        <v-btn v-else dense color="#FFFFFF" elevation="0" class="rounded-lg" style="position: absolute; z-index:4;margin-top: 21px; margin-left: 890px; padding-left: 10px; width: 120px; height: 38px; color: #1F87AF; font-size:16px; font-weight:600;" @click.prevent="GetAllProducts('all_product_cat')" :href="pathFullProduct">สินค้าทั้งหมด</v-btn>
        <v-select
          dense
          color="#FFFFFF"
          background-color="#FFFFFF"
          class="rounded-lg"
          style="
            position: absolute;
            z-index: 2;
            margin-top: 20px;
            width: 165px;
            height: 38px;
            border: 1px solid #1F87AF;
          "
          :style="IpadProSize ? 'margin-left: 79vw;' : 'margin-left: 1020px;'"
          v-model="dataTypeCat"
          @change="selectType()"
          :items="['ทั้งหมด', 'สินค้า', 'บริการ']"
          solo
        >
          <template v-slot:selection="{ item }">
            <span style="color: #1F87AF; font-size:16px; font-weight:600">{{ item }}</span>
          </template>
          <template v-slot:append>
            <v-icon color="#1F87AF">mdi-chevron-down</v-icon>
          </template>
        </v-select>
      <div class="d-flex justify-center">
        <v-sheet
          class="mx-auto"
          :max-width="IpadProSize ? '98vw' : '1220px'"
          style="
            background-color: transparent;
            position: absolute;
            z-index: 3;
            margin-top: 60px;
          "
        >
          <v-slide-group
            class="pa-4"
            active-class="success"
            show-arrows
            center-active
          >
            <template v-slot:next>
              <v-icon color="#ffff" large>mdi-chevron-right-circle</v-icon>
            </template>
            <template v-slot:prev>
              <v-icon color="#ffff" large>mdi-chevron-left-circle</v-icon>
            </template>
            <v-slide-item v-for="(image, index) in itemCat" :key="index">
              <v-card
                elevation="1"
                class="ma-4 rounded-lg"
                height="148"
                width="148"
                style="background-color: white; border-radius: 8px;"
                :href="image.pathLink"
                @click.prevent="CheckClick(image.category_name, image.hierachy)"
              >
              <v-col>
                <div class="mb-4 d-flex justify-center" style="height:74px">
                  <v-img v-if="image.category_logo_path === null || image.category_logo_path === ''" src="@/assets/NoCategory.png" alt="Image" max-width="100px" max-height="90px" />
                  <v-img v-else :src="image.category_logo_path" alt="Image" max-width="100px" max-height="90px" contain/>
                </div>
                <div >
                  <span class="d-flex justify-center " style="font-size: 14px">{{ image.category_name }}</span>
                </div>
              </v-col>
              </v-card>
            </v-slide-item>
          </v-slide-group>
        </v-sheet>
      </div>
      <v-img
        style="z-index: 0"
        class="rounded-lg"
        :width="IpadProSize ? '98vw' : '1223px'"
        height="270"
        src="@/assets/ImageINET-Marketplace/Banner/banner-cat.webp"
      ></v-img>
    </div>
    <div v-else style="position: relative">
        <v-img
          style="z-index: 0"
          class="rounded-lg"
          :width="IpadProSize ? '98vw' : '1223px'"
          height="270"
          src="@/assets/ImageINET-Marketplace/Banner/banner-cat.webp"
        ></v-img>
    </div>
  </v-col>
  <v-col class="d-flex justify-center px-0 pt-0" v-if="MobileSize">
    <div style="position: relative" v-if="loadingComponent === false">
      <span style="font-size: 20px; position: absolute; z-index:3; margin-top:30px; margin-left:20px; color:#fff"><b>หมวดหมู่สินค้า</b></span>
      <v-row no-gutters class="d-flex justify-end">
      <div style="position: absolute; z-index:2; margin-top: 25px; display: flex;">
        <v-btn dense color="#FFFFFF" elevation="0" class="rounded-lg" style="margin-right: 10px; width: 102px; height: 38px; color: #1F87AF; font-size:14px;" @click.prevent="GetAllProducts('all_product_cat')">สินค้าทั้งหมด</v-btn>
          <v-select
            dense
            color="#1F87AF"
            background-color="#ffff"
            class="rounded-lg"
            style="
              width: 90px;
              height: 43px;
              margin-right: 10px;
            "
            v-model="dataTypeCat"
            @change="selectType()"
            :items="['ทั้งหมด', 'สินค้า', 'บริการ']"
            label="ทั้งหมด"
            solo
          >
            <template v-slot:selection="{ item }">
              <span style="color: #1F87AF; ">{{ item }}</span>
            </template>
            <template v-slot:append>
              <v-icon color="#1F87AF">mdi-chevron-down</v-icon>
            </template>
          </v-select>
        </div>
      </v-row>
      <div class="d-flex justify-center">
        <v-sheet
          width="100vw"
          style="
            background-color: transparent;
            position: absolute;
            z-index: 3;
            margin-top: 75px;
          "
        >
          <v-slide-group
            active-class="success"
            center-active
          >
            <template v-slot:next>
              <v-icon color="#ffff" large>mdi-chevron-right-circle</v-icon>
            </template>
            <template v-slot:prev>
              <v-icon color="#ffff" large>mdi-chevron-left-circle</v-icon>
            </template>
            <v-slide-item v-for="(image, index) in itemCat" :key="index">
              <v-card
                elevation="0"
                class="ma-4 rounded-lg"
                width="120px"
                height="145px"
                style="background-color: white; border-radius: 8px;"
                :href="image.pathLink"
                @click.prevent="CheckClick(image.category_name, image.hierachy)"
              >
                <!-- <v-col cols="12">
                  <img :src="image.img" alt="Image" style="width: 80px" />
                  <p class="d-flex justify-center">{{ image.name }}</p>
                </v-col> -->
                <v-col>
                <div class="d-flex justify-center" style="height:74px">
                  <v-img v-if="image.category_logo_path === null || image.category_logo_path === ''" src="@/assets/NoCategory.png" alt="Image" max-width="60px" max-height="60px" contain />
                  <v-img v-else :src="image.category_logo_path" alt="Image" contain max-width="60px" max-height="60px" />
                </div>
                <div>
                  <span class="d-flex justify-center" style="font-size: 12px;"><b>{{ image.category_name }}</b></span>
                </div>
              </v-col>
              </v-card>
            </v-slide-item>
          </v-slide-group>
        </v-sheet>
      </div>
      <v-img
        style="z-index: 0"
        class=""
        width="100vw"
        height="250px"
        src="@/assets/ImageINET-Marketplace/Banner/banner-cat.webp"
      ></v-img>
    </div>
    <div v-else>
      <v-row dense>
        <v-col cols="12">
          <v-skeleton-loader
            height="250"
            width="100vw"
            type="card"
          ></v-skeleton-loader>
        </v-col>
      </v-row>
    </div>
  </v-col>
  <v-col class="d-flex justify-center pt-0" v-if="IpadSize">
    <v-col cols="12" style="position: relative;" class="pt-0" v-if="loadingComponent === false" >
      <span style="font-size: 20px; color: #fff; margin-left: 30px; margin-top:40px; position: absolute; z-index:3"><b>หมวดหมู่สินค้า</b></span>
        <v-btn dense color="#FFFFFF" elevation="0" class="rounded-lg" style="position: absolute; z-index:4;margin-top: 40px; margin-left: 458px; padding-left: 10px; width: 120px; height: 38px; color: #1F87AF; font-size:16px; font-weight:600;" @click="GetAllProducts('all_product_cat')" :href="pathFullProduct">สินค้าทั้งหมด</v-btn>
      <v-select
        dense
        color="#FFFFFF"
        background-color="#FFFFFF"
        class="rounded-lg"
        style="
          position: absolute;
          z-index: 2;
          margin-top: 40px;
          width: 146px;
          height: 38px;
          margin-left: 590px;
          padding-left: 10px;
        "
        v-model="dataTypeCat"
        @change="selectType()"
        :items="['ทั้งหมด', 'สินค้า', 'บริการ']"
        solo
      >
        <template v-slot:selection="{ item }">
          <span style="color: #1F87AF; font-size:16px; font-weight:600">{{ item }}</span>
        </template>
        <template v-slot:append>
          <v-icon color="#1F87AF">mdi-chevron-down</v-icon>
        </template>
      </v-select>
      <v-col cols="12" class="d-flex justify-center">
        <v-sheet
          class="mx-auto"
          max-width="100%"
          style="
            background-color: transparent;
            position: absolute;
            z-index: 3;
            margin-top: 60px;
          "
        >
          <v-slide-group
            class="pa-4"
            active-class="success"
            show-arrows
          >
            <template v-slot:next>
              <v-icon color="#ffff" large>mdi-chevron-right-circle</v-icon>
            </template>
            <template v-slot:prev>
              <v-icon color="#ffff" large>mdi-chevron-left-circle</v-icon>
            </template>
            <v-slide-item v-for="(image, index) in itemCat" :key="index">
              <v-card
                elevation="0"
                class="ma-4 rounded-lg"
                height="148"
                width="148"
                style="background-color: white; border-radius: 8px;"
                :href="image.pathLink"
                @click.prevent="CheckClick(image.category_name, image.hierachy)"
              >
                <!-- <img :src="image.img" alt="Image" style="width: 148px" /> -->
                <div class="mb-4 d-flex justify-center" style="height:74px">
                  <v-img v-if="image.category_logo_path === null || image.category_logo_path === ''" src="@/assets/NoCategory.png" alt="Image" max-width="100px" max-height="90px" />
                  <v-img v-else :src="image.category_logo_path" alt="Image" max-width="100px" max-height="90px" contain/>
                </div>
                <span class="d-flex justify-center" style="text-align: center; font-size: 14px">{{ image.category_name }}</span>
              </v-card>
            </v-slide-item>
          </v-slide-group>
        </v-sheet>
      </v-col>
      <v-img
        style="z-index: 0"
        class="rounded-lg"
        width="1223px"
        height="270"
        src="@/assets/ImageINET-Marketplace/Banner/banner-cat.webp"
      ></v-img>
    </v-col>
    <div v-else>
      <v-row dense>
        <v-col cols="12">
          <v-skeleton-loader
            height="270"
            width="100vw"
            type="card"
          ></v-skeleton-loader>
        </v-col>
      </v-row>
    </div>
  </v-col>
</v-row>
</template>

<script>
// import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      // item: [
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 1'
      //   },
      //   {
      //     img: require('@/assets/Card/Image2.png'),
      //     name: 'อุปกรณ์สำนักงาน 2'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 3'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 4'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 5'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 6'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 7'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 8'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 9'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 10'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 11'
      //   },
      //   {
      //     img: require('@/assets/Card/Image1.png'),
      //     name: 'อุปกรณ์สำนักงาน 12'
      //   }
      // ],
      dataTypeCat: 'ทั้งหมด',
      typeCat: 'all',
      itemCat: [],
      cat_logo_path: 0,
      bannerFirst: '',
      bannerSecond: '',
      bannerThird: '',
      bannerBig: '',
      header: 'หมวดหมู่สินค้า',
      path: process.env.VUE_APP_DOMAIN,
      pathFullProduct: process.env.VUE_APP_DOMAIN + 'ListProduct/all_product_cat?page=1',
      loadingComponent: true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    // this.GetAllBanner()
    this.loadingComponent = true
    localStorage.removeItem('PageTab')
    this.GetAllCategory()
  },
  methods: {
    GetAllProducts (header) {
      this.$router.push(`/ListProduct/${header}?page=1`).catch(() => {})
    },
    selectType () {
      // console.log('this.dataTypeCat', this.dataTypeCat)
      if (this.dataTypeCat === 'ทั้งหมด') {
        this.typeCat = 'all'
      } else if (this.dataTypeCat === 'สินค้า') {
        this.typeCat = 'product'
      } else if (this.dataTypeCat === 'บริการ') {
        this.typeCat = 'service'
      }
      this.GetAllCategory()
    },
    CheckClick (event, val) {
      // console.log('event', event)
      // console.log('val', val)
      // this.$router.push('/product')
      const encodedEvent = encodeURIComponent(event)
      var fullPath
      if (event === 'เวาเชอร์') {
        fullPath = '/ListVoucher/go?&province=All&page=1'
      } else {
        fullPath = `/ListProduct/${encodedEvent}?id=${val}&page=1`
      }
      this.$router.push({ path: `${fullPath}` }).catch(() => {})
      // this.$router.push(`/ListProduct/${this.header}?id=${val}`).catch(() => {})
      // this.$router.push(`/ListProduct/${test}?id=${val}`).catch(() => {})
    },
    async GetAllCategory () {
      var data = this.typeCat
      // console.log('GetAllCategory')
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.result === 'SUCCESS') {
        for (var i = 0; i < response.data.length; i++) {
          response.data[i].pathLink = this.path + 'ListProduct/' + encodeURIComponent(`${response.data[i].category_name}`) + '?id=' + `${response.data[i].hierachy}` + '&page=1'
        }
        var resData = response.data
        // this.itemCat = response.data
        this.itemCat = resData.filter(e => e.hierachy !== '1_359')
        this.loadingComponent = false
        // console.log('itemCat', this.itemCat)
      } else {
        this.itemCat = []
        this.loadingComponent = false
      }
      // console.log('this.item', this.itemCat)
      // for (const item of this.itemCat) {
      //   console.log('item------>', item.category_logo_path)
      //   if (item.category_logo_path === null) {
      //     // console.log('item', item.category_logo_path)
      //     // item.category_logo_path = require('@/assets/NoImage.png')
      //   }
      //   // console.log('item', item.category_logo_path)
      // }
      // console.log('ListCategory', this.ListCategory)
    }
  }
}
</script>
<style scoped>
/* .v-input__slot .v-select__selection--comma {
  color: #ffffff;
} */
/* ::v-deep #selectedAll > .v-select.v-input--dense > .v-select__selection--comma {
  color: #ffffff !important;
} */
</style>
