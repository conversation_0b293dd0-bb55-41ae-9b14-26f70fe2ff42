<template>
  <div>
  <v-card width="100%" elevation="0" class="mt-2">
    <v-card-text class="px-1">
      <!--การ์ด-->
      <v-row :class="IpadSize ? '' : 'ml-10'" :justify="IpadSize ? 'start' : ''">
        <div :class="IpadSize ? 'h-card-ipad pt-4 ml-4' : 'h-card pt-10 ml-4'">
          <div :class="IpadSize ? 'h-img-ipad ml-4 pt-3' : 'h-img-b ml-8 pt-3 pb-2'">
            <v-img
             class="mx-auto py-auto"
             lazy-src="@/assets/icons/shopping-basket 1.png"
             :max-height="IpadSize ? '37' : '48'"
             :max-width="IpadSize ? '37' : '48'"
             src="@/assets/icons/shopping-basket 1.png"
            ></v-img>
          </div>
          <div :class="IpadSize ? 'pt-4 pl-4' : 'mt-10 pt-12 pl-5'" style="font-weight: 700; color: #333333;" :style="IpadSize ? 'font-size: 14px; line-height: 22px;' : 'font-size: 16px; line-height: 24px;'">
            รวมรายได้ทั้งหมด
          </div>
          <!--  <div class="ml-5">
            รายการถอนเงิน {{ListData}} รายการ
          </div> -->
          <div class="mt-1 pt3 pl-5 " :style="MobileSize ? 'font-size: 15px; line-height: 40px;' : IpadSize ? 'font-size: 20px; font-weight: 700; line-height: 30px;' : 'font-weight: 700; font-size: 16px; line-height: 40px;'">
            {{ Number($store.getters.Summary.summary_total ).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
            <!--  <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;"></span> -->
          </div>
        </div>
        <!-- <v-col v-else cols="12" align="center">
          <div class="h-card">
            <div class="h-img-b mt-10 pt-6">
              <v-img
               class="mx-auto py-auto"
               lazy-src="@/assets/icons/shopping-basket 1.png"
               max-height="48"
               max-width="48"
               src="@/assets/icons/shopping-basket 1.png"
              ></v-img>
            </div>
            <div class="mt-10" style="font-weight: 700; font-size: 16px; line-height: 24px;">
              ถอนเงิน
            </div>
            <div class="">
              รายการถอนเงิน  รายการ
            </div>
            <div class="mt-1 fontsize-28" style="font-weight: 700; font-size: 0.9vw; line-height: 40px;">
              <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;"></span>
            </div>
          </div> -->
        <!-- </v-col> -->
        <!--การ์ด 1-->
        <!-- <div class="h-card pt-10 ml-4" v-if="!isMobile" style="background: #ECECFF; border: 2px solid #ffffff;">
            <div class="h-img-b ml-8 pt-3 pb-2">
              <v-img
               class="mx-auto py-auto"
               lazy-src="@/assets/icons/shopping-basket 1waitingBank.png"
               max-height="48"
               max-width="48"
               src="@/assets/icons/shopping-basket 1waitingBank.png"
              ></v-img>
            </div>
            <div class="mt-10 pt-12 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
              เตรียมการโอนเงิน
            </div>
            <div class="ml-5">
              รายการถอนเงิน {{ListData}} รายการ
            </div> -->
            <!-- <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 0.9vw; line-height: 40px;'">
              {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
            </div>
          </div>
          <v-col v-else cols="12" align="center">
            <div class="h-card">
              <div class="h-img-b mt-10 pt-6">
                <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1waitingBank.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1waitingBank.png"
                ></v-img>
              </div>
              <div class="mt-10" style="font-weight: 700; font-size: 16px; line-height: 24px;">
                ถอนเงิน
              </div>
              <div class="">
                รายการถอนเงิน {{ListData}} รายการ
              </div>
              <div class="mt-1 fontsize-28" style="font-weight: 700; font-size: 0.9vw; line-height: 40px;">
                {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
              </div>
            </div>
          </v-col>-->
          <!--การ์ด2-->
          <!--  <div class="h-card pt-10 ml-4" v-if="!isMobile" style="background: #F5F1DA; border: 2px solid #ffffff;">
            <div class="h-img-b ml-8 pt-3 pb-2">
              <v-img
                class="mx-auto py-auto"
                lazy-src="@/assets/icons/shopping-basket 1sucessBank.png"
                max-height="48"
                max-width="48"
                src="@/assets/icons/shopping-basket 1sucessBank.png"
              ></v-img>
            </div>
            <div class="mt-10 pt-12 pl-5" style="font-weight: 700; font-size: 16px; line-height: 24px;">
              โอนเงินสำเร็จ
            </div> -->
            <!-- <div class="ml-5">
              รายการถอนเงิน {{ListData}} รายการ
            </div> -->
            <!-- <div class="mt-1 pt3 pl-5 " :style="isMobile ? 'font-size: 15px; line-height: 40px;' : 'font-weight: 700; font-size: 0.9vw; line-height: 40px;'">
              {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
            </div>
          </div>
          <v-col v-else cols="12" align="center">
            <div class="h-card">
              <div class="h-img-b mt-10 pt-6">
                <v-img
                  class="mx-auto py-auto"
                  lazy-src="@/assets/icons/shopping-basket 1sucessBank.png"
                  max-height="48"
                  max-width="48"
                  src="@/assets/icons/shopping-basket 1sucessBank.png"
                ></v-img>
              </div>
              <div class="mt-10" style="font-weight: 700; font-size: 16px; line-height: 24px;">
                ถอนเงิน
              </div>
              <div class="">
                รายการถอนเงิน {{ListData}} รายการ
              </div>
              <div class="mt-1 fontsize-28" style="font-weight: 700; font-size: 0.9vw; line-height: 40px;">
                {{ Number(counterList).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                <v-icon color="#00AD1E">mdi-arrow-up</v-icon> <span style="color: #00AD1E;">{{percentage}}</span>
              </div>
            </div>
          </v-col> -->
      </v-row>
      <!-- กราฟ -->
      <v-row dense class="mt-12">
        <v-card outlined elevation="0" width="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;">
          <v-card-text>
            <div id='chart' class="mt-5">
              <apexchart  height='350' :options='chartOptions' :series='$store.state.ModuleAdminManage.stateGpChart.value'></apexchart>
            </div>
          </v-card-text>
        </v-card>
      </v-row>
    </v-card-text>
  </v-card>
</div>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
export default {
  name: 'ApexChart',
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      selectFilter: 1,
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      modal: false,
      modal1: false,
      series: [],
      dateStart: '',
      dateEnd: '',
      dateFormatted: '',
      dateFormatted2: '',
      selectFilteritem: [
        { id: 1, name: 'ไม่ระบุ' },
        { id: 2, name: 'รายวัน' },
        { id: 3, name: 'รายเดือน' },
        { id: 4, name: 'รายปี' },
        { id: 5, name: 'ทั้งหมด' }
      ]
    }
  },
  created () {
  //   this.$EventBus.$on('selectedChanged', this.selectedChanged)
  },
  destroyed () {
    // this.$EventBus.$off('selected-changed')
  },
  mounted () {
  },
  updated () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    chartOptions () {
      return {
        chart: {
          height: 400,
          type: 'line',
          zoom: {
            enabled: true
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          width: [2, 2, 2, 2],
          curve: 'straight',
          dashArray: [0, 0, 0, 0]
        },
        title: {
          text: 'กราฟข้อมูลรายได้ทั้งหมดของระบบ',
          align: 'left',
          margin: this.MobileSize ? 60 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        legend: {
          // tooltipHoverFormatter: function (val, opts) {
          //   return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
          // },
          position: 'top',
          horizontalAlign: 'left',
          offsetY: -5
        },
        markers: {
          size: 4,
          hover: {
            sizeOffset: 6
          }
        },
        yaxis: [
          {
            labels: {
              formatter: function (val) {
                return parseFloat(val).toFixed(2)
              }
            }
          }
        ],
        xaxis: {
          categories: this.$store.state.ModuleAdminManage.stateGpChart.date
          // labels: {
          //   formatter: function (val) {
          //     return new Date(val).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
          //   }
          // }
        },
        tooltip: {
          y: [
            {
              title: {
                formatter: function (val) {
                  return 'กำไรของระบบ(บาท) : '
                }
              }
            }
          ]
        },
        grid: {
          borderColor: '#f1f1f1'
        }
      }
    }
  },
  watch: {
  }
}
</script>

<style >
.header-style {
  background: #D4F1E4 !important;
}
.status-1 {
  color: #E9A016;
}

.h-card{
  width: 249px;
  height: 268px;
  background: #E4F4F1;
  border-radius: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  border: 2px solid #5BC3A2;
}
.h-card-ipad {
  width: 135px;
  height: 161px;
  background: #E4F4F1;
  border-radius: 12px;
  position: relative;
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  border: 2px solid #5BC3A2;
}
.h-img-b {
 width: 80px;
 height: 78px;
 background: #ffffff;
 border-radius: 12px;
 margin: -18px 0px 0px -20px;
 padding: -28px 0px 0px -20px;
}
.h-img-ipad {
 width: 53px;
 height: 53px;
 background: #ffffff;
 border-radius: 12px;
}
</style>
