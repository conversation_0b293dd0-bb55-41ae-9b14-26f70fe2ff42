<template>
  <div>
    <v-container :class="MobileSize ? '' : 'mb-6'" v-if="flashSaleProduct.length !== 0 ">
      <v-row dense v-if="isLoading === true">
        <v-col class="d-flex justify-center">
          <v-card elevation="0" :style="MobileSize ? 'height: 100%;' : 'height: 548px;'" style="width: 100vw; background-color: transparent;">
            <!-- ภาพ flash sale -->
            <v-row justify="center" dense>
              <v-col cols="12" align="center" :class="MobileSize ? 'px-0' : ''">
                <v-img :src="lang === 'th' && MobileSize ? require('@/assets/defaultFlashSaleMobile.png') : lang === 'th' && !MobileSize ? require('@/assets/defaultFlashSale.png') : lang === 'en' && MobileSize ? require('@/assets/defaultFlashSaleEngMobile.png') : lang === 'en' && !MobileSize ? require('@/assets/defaultFlashSaleEng.png') : `${backgroundImage}`" max-width="100%" :max-height="MobileSize ? '48px' : '121px'" :width="IpadProSize ? '100%' : MobileSize ? '100%' : '1400px'" height="100%" :style="MobileSize ? 'border-radius: 12px;' : 'border-radius: 16px;'">
                  <div class="d-flex" style="align-content: center;" :style="MobileSize ? 'max-height: 48px; height: 48px;' : 'max-height: 121px; height: 121px;'">
                    <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" class="mr-auto my-auto" :width="MobileSize ? '75px' : IpadSize ? '140px' : '232px'" :height="MobileSize ? '42px' : IpadSize ? '70px' : '111px'" :style="MobileSize ? 'max-height: 48px;' : IpadSize ? 'max-height: 70px;' : 'max-height: 111px;'" style="display: flex; align-content: center;" alt="flashsalelogo">
                    <img v-if="logoImage !== ''" :src="logoImage" class="mr-auto" :width="MobileSize ? '75px' : IpadSize ? '140px' : '232px'" :height="MobileSize ? '42px' : IpadSize ? '70px' : '111px'" :style="MobileSize ? 'max-height: 48px;' : IpadSize ? 'max-height: 70px;' : 'max-height: 111px;'" style="display: flex;" alt="flashsalelogo">
                    <!-- <div :class="MobileSize ? 'countdown-wrapper-mobile' : 'countdown-wrapper'" class="ml-auto">
                      <span :class="MobileSize ? 'label-mobile' : 'label'">สิ้นสุดใน</span>
                      <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ hours }}</div>
                      <span :class="MobileSize ? 'colon-mobile' : 'colon'">:</span>
                      <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ minutes }}</div>
                      <span :class="MobileSize ? 'colon-mobile' : 'colon'">:</span>
                      <div :class="MobileSize ? 'time-box-mobile' : 'time-box'">{{ seconds }}</div>
                    </div> -->
                  </div>
                </v-img>
              </v-col>
            </v-row>
            <!-- ดูทั้งหมด -->
            <v-row dense justify="center" style="padding: 18px 0px 0px 0px;">
              <v-col cols="12" :class="MobileSize ? 'd-flex justify-end' : 'd-flex justify-end'" :style="IpadProSize ? 'max-width: 100%;' : 'max-width: 1350px;'">
                <v-btn @click="GetAllProducts(flashSaleProduct, 'flash_sale')" text elevation="0">
                  <span style="font-size: 16px; color: #3EC6B6; font-weight: 400; text-transform: none;">{{ $t('TextHome.ViewAllProducts') }} <v-icon color="#3EC6B6" size="20">mdi-arrow-right-circle-outline</v-icon></span>
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense class="pt-0">
              <v-col class="pa-0">
                <vue-horizontal-list :class="MobileSize || IpadSize ? '' : ''" :items='flashSaleProduct' :options='optionsItems' :key="flashSaleProduct.length">
                  <template v-slot:nav-prev>
                    <div><v-icon color="#269AFD" size="32">mdi-chevron-left</v-icon></div>
                  </template>
                  <template v-slot:nav-next>
                    <div><v-icon color="#269AFD" size="32">mdi-chevron-right</v-icon></div>
                  </template>
                  <template v-slot:default="{ item }">
                    <v-col class="px-0" :class="MobileSize ? 'py-0' : 'py-0'">
                      <component v-if="isDataReady" :is="MobileSize || IpadSize ? 'CardProductsFlashSaleMobile' : 'CardProductsFlashSale'" :itemProduct="item"/>
                    </v-col>
                  </template>
                </vue-horizontal-list>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col class="d-flex justify-center">
          <v-card style="height: 350px; position: relative;" :style="MobileSize ? 'border-radius: 0px; height: 230px; margin-bottom: 28px;' : IpadProSize ? 'border-radius: 10px; width: 100%;' : 'border-radius: 10px; width:1223px;'" class="elevation-0">
            <v-img
              :height="!MobileSize ? 350 : 250"
              :src="backgroundImage"
              :style="!MobileSize ? 'border-radius: 5px;' : ' border-radius: 0px;'"
            >
              <v-row>
                <v-col :class="MobileSize ? 'd-flex justify-end mt-1 mr-5 pb-0 pr-0' : 'd-flex justify-end mt-5 mr-10'">
                  <v-btn @click="GetAllProducts(flashSaleProduct, 'flash_sale')" dark color="white" style="border: 1px">
                    <span style="font-size: small; color: #a55918; font-weight: bold;">ดูทั้งหมด</span>
                  </v-btn>
                </v-col>
              </v-row>
              <v-row>
                <v-col class="pa-0">
                  <vue-horizontal-list :class="MobileSize || IpadSize ? 'mx-4 py-0' : 'mx-10'"  :items='flashSaleProduct' :options='optionsItems' :key="flashSaleProduct.length">
                    <template v-slot:nav-prev>
                      <div><v-icon color="#008E00" size="40">mdi-chevron-left</v-icon></div>
                    </template>
                    <template v-slot:nav-next>
                      <div><v-icon color="#008E00" size="40">mdi-chevron-right</v-icon></div>
                    </template>
                    <template v-slot:default="{ item }">
                      <v-col :style="IpadProSize ? 'max-width: 100%;' : 'max-width: 1248px;'">
                        <component v-if="isDataReady" :is="MobileSize || IpadSize ? 'CardProductsFlashSaleMobile' : 'CardProductsFlashSale'" :itemProduct="item"/>
                      </v-col>
                    </template>
                  </vue-horizontal-list>
                </v-col>
              </v-row>
            </v-img>
            <img v-if="logoImage === ''" src="@/assets/ImageINET-Marketplace/ICONShop/flashSaleIcon.png" :width="MobileSize ? '140px' : '200px'" :style="MobileSize ? 'position: absolute; top: -15px; left: 0px' : 'position: absolute; top: -40px; left: 20px;'" alt="flashsalelogo">
            <img v-if="logoImage !== ''" :src="logoImage" :width="MobileSize ? '140px' : '200px'" :style="MobileSize ? 'position: absolute; top: -15px; left: 0px' : 'position: absolute; top: -20px; left: 20px;'" alt="flashsalelogo">
          </v-card>
        </v-col>
      </v-row>
    </v-container>
    <v-container :class="MobileSize ? 'pa-0' : 'pt-8 pb-0'" v-else>
      <v-row dense :class="MobileSize ? '' : 'px-16'">
        <v-col cols="12">
          <v-skeleton-loader type="image" min-height="350px"></v-skeleton-loader>
        </v-col>
      </v-row>
    </v-container>
  </div>
</template>

<script>
import { Encode } from '@/services'
import VueHorizontalList from 'vue-horizontal-list'
export default {
  data () {
    return {
      flashSaleProduct: [],
      logoImage: '',
      backgroundImage: null,
      isLoading: false,
      statusFlashSale: '',
      hours: '00',
      minutes: '00',
      seconds: '00',
      targetTime: '',
      timer: null,
      lang: 'th'
    }
  },
  components: {
    VueHorizontalList,
    CardProductsFlashSale: () => import(/* webpackPrefetch: true */ '@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome'),
    CardProductsFlashSaleMobile: () => import(/* webpackPrefetch: true */ '@/components/Shop/ManageFlashSale/FlashSaleItem/CardFlashSaleHome')
  },
  mounted () {
    this.updateTimer()
    this.timer = setInterval(this.updateTimer, 1000)
  },
  beforeDestroy () {
    clearInterval(this.timer)
  },
  async created () {
    // console.log('here1')
    this.flashSaleProduct = []
    this.lang = localStorage.getItem('lang')
    this.$EventBus.$on('getAllProductFlashSale', this.getFlashSaleProduct)
    await this.StatusFlashSale()
    await this.GetBanner()
    if (this.statusFlashSale === 'no') {
      await this.getFlashSaleProduct()
    } else if (this.statusFlashSale === 'yes') {
      // await this.getFlashSaleProduct()
      await this.getFlashSaleProductSystem()
    }
  },
  computed: {
    optionsItems () {
      return {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 4 },
          { start: 768, end: 1024, size: 4 },
          { start: 1024, end: 1200, size: 6 },
          { start: 1200, size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1200,
          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: this.IpadSize || this.IpadProSize ? '8' : this.MobileSize ? '4' : '24'
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    isDataReady () {
      return this.flashSaleProduct && this.flashSaleProduct.length > 0
    }
  },
  methods: {
    updateTimer () {
      const now = new Date()
      const diff = Math.max(0, this.targetTime - now)

      const h = Math.floor(diff / (1000 * 60 * 60))
      const m = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60))
      const s = Math.floor((diff % (1000 * 60)) / 1000)

      this.hours = String(h).padStart(2, '0')
      this.minutes = String(m).padStart(2, '0')
      this.seconds = String(s).padStart(2, '0')
    },
    async getFlashSaleProduct () {
      var data = {
        page: 1,
        limit: 18
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsFlashSaleProductatShop', data)
      var response = await this.$store.state.ModuleHompage.stateFlashSaleProduct
      if (response.message === 'Get Product Success.') {
        this.flashSaleProduct = response.data.products
        // this.backgroundImage = response.data.img_background
        // this.logoImage = response.data.img_logo
        this.isLoading = true
        this.$store.commit('closeLoader')
      } else {
        this.flashSaleProduct = []
        this.$store.commit('closeLoader')
      }
      // console.log('flashSaleProduct', this.flashSaleProduct)
      // this.flashSaleProduct = response.query_result[0].result
    },
    async getFlashSaleProductSystem () {
      var dataRole = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        dataRole = {
          role: 'ext_buyer'
        }
      }
      var data = {
        company_id: -1,
        status_product: 'discount',
        role_user: dataRole.role
      }
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsFlashSaleProductSystem', data)
      // var response = await this.$store.state.ModuleHompage.stateFlashSaleProductSystem
      await this.$store.dispatch('actionsProductCardLandingPage', data)
      var response = await this.$store.state.ModuleHompage.stateGetProductCardLandingPage
      if (response.ok === 'y') {
        this.flashSaleProduct = response.query_result
        // this.backgroundImage = response.data.img_background
        // this.logoImage = response.data.img_logo
        this.isLoading = true
        this.$store.commit('closeLoader')
      } else {
        this.flashSaleProduct = []
        this.$store.commit('closeLoader')
      }
      // console.log('flashSaleProduct', this.flashSaleProduct)
      // this.flashSaleProduct = response.query_result[0].result
    },
    GetAllProducts (allItem, header) {
      if (allItem.length !== 0) {
        localStorage.setItem('itemShop', Encode.encode(allItem))
        this.$router.push(`/ListProduct/${header}?page=1`).catch(() => {})
      }
    },
    async StatusFlashSale () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsStatusFlashSale')
      const responseStatusFlashSale = await this.$store.state.ModuleAdminManage.stateStatusFlashSale

      if (responseStatusFlashSale.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.statusFlashSale = responseStatusFlashSale.data
      }
    },
    async GetBanner () {
      await this.$store.dispatch('actionsGetBanner')
      var response = await this.$store.state.ModuleHompage.stateGetBanner
      if (response.data.img_flash_sale_background.length !== 0 || response.data.img_flash_sale_logo.length !== 0) {
        this.backgroundImage = response.data.img_flash_sale_background[0].path
        this.logoImage = response.data.img_flash_sale_logo[0].path
        // this.$EventBus.$emit('GetGroupShopBackGround', response.data.img_flash_sale_background[0].path)
      } else {
        this.backgroundImage = ''
        this.logoImage = ''
        // this.$EventBus.$emit('GetGroupShopBackGround', '')
      }
      if (response.data.store_category_bg.length !== 0) {
        this.$EventBus.$emit('GetGroupShopBackGround', response.data.store_category_bg[0].path)
      }
      // else {
      //   this.$EventBus.$emit('GetGroupShopBackGround', '-')
      // }
      if (response.data.img_flash_sale_background.length !== 0 || response.data.img_flash_sale_logo.length !== 0) {
        this.backgroundImage = response.data.img_flash_sale_background[0].path
        this.logoImage = response.data.img_flash_sale_logo[0].path
      } else {
        this.backgroundImage = ''
        this.logoImage = ''
      }
    }
  }
}
</script>

<style scoped>
::v-deep .vhl-list>*[data-v-8b923bbc] {
  flex-shrink: 1 !important;
}
::v-deep .vhl-container {
  margin: auto !important;
  max-width: 1320px !important;
}
::v-deep .vhl-btn-left {
  max-width: 40px !important;
  max-height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  box-shadow: none !important;
  background: transparent !important;
  z-index: 2;
}
::v-deep .vhl-btn-right {
  max-width: 40px !important;
  max-height: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  background: transparent !important;
  box-shadow: none !important;
  z-index: 2;
}
/* margin: auto;
max-width: 1290px; */
</style>
