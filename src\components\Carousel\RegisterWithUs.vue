<template>
  <v-container :class="MobileSize || IpadSize ? 'mt-0 pt-0' : 'mt-4'">
    <!-- ร้านค้าใหม่ -->
    <v-row
      dense
      justify="center"
      class="mt-0 mb-0"
      v-if="!MobileSize && !IpadSize"
    >
      <v-container>
        <v-col class="d-flex justify-center">
          <v-row dense v-if="showSkeletonLoader">
            <v-col cols="12">
              <v-row dense>
                <v-skeleton-loader
                  height="294"
                  width="100%"
                  type="image"
                ></v-skeleton-loader>
              </v-row>
            </v-col>
          </v-row>
          <v-sheet
            v-else
            class="col-12 pa-4"
            style="border-radius: 8px; max-width: 1225px; min-height: 294px"
          >
          <div class="d-flex justify-space-between align-center">
            <div class="d-flex align-center  pl-5 pt-3 mb-6">
              <v-icon color="#27AB9C" size="28"> mdi-store </v-icon>
              <span
                class="pl-2"
                style="font-size: 24px; font-weight: 700; color: #27ab9c"
                >{{ $t('Headers.HomeText.Shops') }}</span
              >
            </div>
            <div>
              <v-btn text @click.prevent="AllShop()" :href="pathAllShop" color="#27AB9C " class="d-flex align-center pl-5 mt-3 mb-6" plain style="font-size: 16px; font-weight: 600; text-transform: none;">
                {{ $t('Headers.HomeText.AllShops') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-0">
                  <v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn>
                </v-btn>
            </div>
          </div>
            <div class="d-flex justify-center">
              <v-btn
                class="d-flex align-self-center mr-5"
                color="#7CCFB4"
                fab
                x-small
                @click="goToPreviousPage"
                :disabled="!hasPreviousPage"
                elevation="0"
                ><v-icon class="mr-1" large color="white"
                  >mdi-chevron-left</v-icon
                ></v-btn
              >
              <v-row
                no-gutters
                style="max-width: 1225px; min-height: 294px"
                v-if="currentPageItems % 6 !== 1"
              >
                <div v-for="(itemShop, index) in currentPageItems" :key="index">
                  <v-hover v-slot="{ hover }">
                    <v-card
                      :elevation="hover ? 4 : 0"
                      :style="
                        hover
                          ? 'border: 1px solid #27AB9C;'
                          : 'border: 1px solid #F3F5F7;'
                      "
                      class="ma-1 rounded-0"
                      height="139"
                      width="173"
                      :href="itemShop.pathLink"
                      @click.prevent="gotoShopDetail(itemShop)"
                    >
                      <v-card-text>
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <v-img
                              v-if="itemShop.shop_logo !== ''"
                              :src="itemShop.shop_logo"
                              loading="lazy"
                              width="130"
                              height="90"
                              contain
                            ></v-img>
                            <v-img
                              v-if="itemShop.shop_logo === ''"
                              src="@/assets/StoreNew.png"
                              loading="lazy"
                              width="84"
                              height="88"
                              contain
                            ></v-img>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                                cols="12"
                                align="center"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span
                                  class="text-truncate d-inline-block"
                                  style="max-width: 145px"
                                  >{{ itemShop.shop_name }}</span
                                >
                              </v-col>
                            </template>
                            <span>{{ itemShop.shop_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-hover>
                </div>
              </v-row>
              <v-row
                no-gutters
                style="max-width: 1225px; min-height: 294px"
                v-else
              >
                <div v-for="(itemShop, index) in currentPageItems" :key="index">
                  <v-hover v-slot="{ hover }">
                    <v-card
                      :elevation="hover ? 4 : 0"
                      :style="
                        hover
                          ? 'border: 1px solid #27AB9C;'
                          : 'border: 1px solid #F3F5F7;'
                      "
                      class="ma-1 rounded-0"
                      height="139"
                      width="173"
                      :href="itemShop.pathLink"
                      @click.prevent="gotoShopDetail(itemShop)"
                    >
                      <v-card-text>
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <v-img
                              v-if="itemShop.shop_logo !== ''"
                              :src="itemShop.shop_logo"
                              loading="lazy"
                              width="130"
                              height="90"
                              contain
                            ></v-img>
                            <v-img
                              v-if="itemShop.shop_logo === ''"
                              src="@/assets/StoreNew.png"
                              loading="lazy"
                              width="84"
                              height="88"
                              contain
                            ></v-img>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                                cols="12"
                                align="center"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span
                                  class="text-truncate d-inline-block"
                                  style="max-width: 145px"
                                  >{{ itemShop.shop_name }}</span
                                >
                              </v-col>
                            </template>
                            <span>{{ itemShop.shop_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-hover>
                </div>
              </v-row>
              <v-btn
                class="d-flex align-self-center"
                color="#7CCFB4"
                fab
                x-small
                @click="goToNextPage"
                :disabled="!hasNextPage"
                elevation="0"
                ><v-icon large color="white">mdi-chevron-right</v-icon></v-btn
              >
            </div>
          </v-sheet>
        </v-col>
      </v-container>
    </v-row>
    <v-row justify="center" class="mt-2 mb-2 py-0" v-if="MobileSize">
      <v-col cols="12" class="d-flex justify-center py-0">
        <v-row dense v-if="showSkeletonLoader">
          <v-col cols="12">
            <v-row dense>
              <v-skeleton-loader
                height="170"
                width="100%"
                type="image"
              ></v-skeleton-loader>
            </v-row>
          </v-col>
        </v-row>
        <v-sheet
          v-else
          class="pa-0"
          height="100%"
          width="100vw"
          style="border-radius: 8px;"
        >
        <!-- <div class="d-flex justify-space-between align-center">
          <div class="d-flex align-center  pl-5 pt-3 mb-6">
            <v-icon color="#27AB9C" size="28"> mdi-store </v-icon>
            <span
              class="pl-2"
              style="font-size: 24px; font-weight: 700; color: #27ab9c"
              >ร้านค้า</span
            >
          </div>
          <div>
            <v-btn text @click="AllShop()" color="#27AB9C " class="d-flex align-center pl-5 pt-3 mb-6" plain style="font-size: 16px; font-weight: 600;">
              ร้านค้าทั้งหมด <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-0">
                <v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn>
              </v-btn>
          </div>
        </div> -->
        <div class="d-flex justify-space-between align-center">
          <div class="d-flex align-center pl-5 pt-2">
            <v-icon color="#27AB9C" size="26"> mdi-store </v-icon>
            <span
              class="pl-2"
              style="font-size: 18px; font-weight: 700; color: #27ab9c"
              >{{ $t('Headers.HomeText.Shops') }}</span
            >
          </div>
          <div>
            <v-btn text @click.prevent="AllShop()" :href="pathAllShop" color="#27AB9C" class="d-flex align-center pl-5 mt-2" plain style="font-size: 14px; font-weight: 600; text-transform: none;">
              {{ $t('Headers.HomeText.AllShops') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-0">
                <v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn>
              </v-btn>
          </div>
        </div>
          <div class="d-flex justify-center">
            <v-row
              no-gutters
              justify="center"
              style="width: 100%;"
            >
              <v-col cols="12">
                <v-sheet width="100vw">
                  <v-slide-group
                    active-class="success"
                    center-active
                  >
                    <template v-slot:next>
                      <v-icon color="#ffff" large>mdi-chevron-right-circle</v-icon>
                    </template>
                    <template v-slot:prev>
                      <v-icon color="#ffff" large>mdi-chevron-left-circle</v-icon>
                    </template>
                    <v-slide-item v-for="(itemShop, index) in productSearch" :key="index">
                      <v-hover v-slot="{ hover }">
                        <v-card
                          :elevation="hover ? 4 : 0"
                          :style="
                            hover
                              ? 'border: 1px solid #27AB9C;'
                              : 'border: 1px solid #F3F5F7;'
                          "
                          class="ma-1 rounded-0"
                          height="120"
                          width="120"
                          :href="itemShop.pathLink"
                          @click.prevent="gotoShopDetail(itemShop)"
                        >
                          <v-card-text>
                            <v-row dense justify="center">
                              <v-col cols="12" align="center">
                                <v-img
                                  v-if="itemShop.shop_logo !== ''"
                                  :src="itemShop.shop_logo"
                                  loading="lazy"
                                  width="60"
                                  height="60"
                                  contain
                                ></v-img>
                                <v-img
                                  v-if="itemShop.shop_logo === ''"
                                  src="@/assets/StoreNew.png"
                                  loading="lazy"
                                  width="60"
                                  height="60"
                                  contain
                                ></v-img>
                              </v-col>
                              <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                  <v-col
                                    cols="12"
                                    align="center"
                                    v-bind="attrs"
                                    v-on="on"
                                  >
                                    <span
                                      class="text-truncate d-inline-block"
                                      style="max-width: 95px"
                                      ><b>{{ itemShop.shop_name }}</b></span
                                    >
                                  </v-col>
                                </template>
                                <span>{{ itemShop.shop_name }}</span>
                              </v-tooltip>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-hover>
                    </v-slide-item>
                  </v-slide-group>
                </v-sheet>
                <!-- <div v-for="(itemShop, index) in productSearch" :key="index">
                  <v-hover v-slot="{ hover }">
                    <v-card
                      :elevation="hover ? 4 : 0"
                      :style="
                        hover
                          ? 'border: 1px solid #27AB9C;'
                          : 'border: 1px solid #F3F5F7;'
                      "
                      class="ma-1 rounded-0"
                      height="139"
                      width="120"
                      :href="itemShop.pathLink"
                      @click.prevent="gotoShopDetail(itemShop)"
                    >
                      <v-card-text>
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <v-img
                              v-if="itemShop.shop_logo !== ''"
                              :src="itemShop.shop_logo"
                              loading="lazy"
                              width="130"
                              height="90"
                              contain
                            ></v-img>
                            <v-img
                              v-if="itemShop.shop_logo === ''"
                              src="@/assets/StoreNew.png"
                              loading="lazy"
                              width="84"
                              height="88"
                              contain
                            ></v-img>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                                cols="12"
                                align="center"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span
                                  class="text-truncate d-inline-block"
                                  style="max-width: 95px"
                                  >{{ itemShop.shop_name }}</span
                                >
                              </v-col>
                            </template>
                            <span>{{ itemShop.shop_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-hover>
                </div> -->
              </v-col>
            </v-row>
            <!-- <v-row no-gutters  class="justify-center" style="max-width: 1225px; min-height: 294px" v-else>
              <div v-for="(itemShop, index) in currentPageItems" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="173"
                    :href="itemShop.pathLink"
                    @click.prevent="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 95px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row> -->
            <!-- <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToNextPage"
              :disabled="!hasNextPage"
              elevation="0"
              ><v-icon large color="white">mdi-chevron-right</v-icon></v-btn
            > -->
          </div>
        </v-sheet>
      </v-col>
    </v-row>
    <v-row justify="center" class="mt-4" v-if="IpadSize">
      <v-col cols="12" style="" class="d-flex justify-center mt-0">
        <v-row dense v-if="showSkeletonLoader">
          <v-col cols="12">
            <v-row dense>
              <v-skeleton-loader
                height="294"
                width="100vw"
                type="image"
              ></v-skeleton-loader>
            </v-row>
          </v-col>
        </v-row>
        <v-sheet
          v-else
          class="pa-0"
          style="border-radius: 8px; width: 100vw; min-height: 294px"
        >
        <div class="d-flex justify-space-between align-center">
          <div class="d-flex align-center  pl-5 pt-3 mb-6">
            <v-icon color="#27AB9C" size="28"> mdi-store </v-icon>
            <span
              class="pl-2"
              style="font-size: 24px; font-weight: 700; color: #27ab9c"
              >{{ $t('Headers.HomeText.Shops') }}</span
            >
          </div>
          <div>
            <v-btn text @click.prevent="AllShop()" :href="pathAllShop" color="#27AB9C " class="d-flex align-center pl-5 mt-3 mb-6" plain style="font-size: 16px; font-weight: 600; text-transform: none;">
              {{ $t('Headers.HomeText.AllShops') }} <v-btn height="24" width="24" fab x-small color="#DCFFDC" elevation="0" class="ml-0">
                <v-icon color="#008E00">mdi-chevron-right</v-icon></v-btn>
              </v-btn>
          </div>
        </div>
          <div class="d-flex justify-center mb-8 pr-5 pl-5">
            <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToPreviousPage"
              :disabled="!hasPreviousPage"
              elevation="0"
              ><v-icon class="mr-1" large color="white"
                >mdi-chevron-left</v-icon
              ></v-btn
            >
            <v-row
              no-gutters
              justify="center"
              style="width: 100%; min-height: 294px"
              v-if="currentPageItems % 4 === 0"
            >
              <div v-for="(itemShop, index) in currentPageItems" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="130"
                    :href="itemShop.pathLink"
                    @click.prevent="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 105px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row>
            <v-row no-gutters class="justify-center" style="max-width: 1225px; min-height: 294px" v-else>
              <div v-for="(itemShop, index) in currentPageItems" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="173"
                    :href="itemShop.pathLink"
                    @click.prevent="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 145px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row>
            <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToNextPage"
              :disabled="!hasNextPage"
              elevation="0"
              ><v-icon large color="white">mdi-chevron-right</v-icon></v-btn
            >
          </div>
        </v-sheet>
      </v-col>
    </v-row>
    <!-- ร้านค้า etax -->
    <!-- <v-row
      dense
      justify="center"
      class="mt-0 mb-2"
      v-if="!MobileSize && !IpadSize && this.productSearchEtax.length !== 0"
    >
      <v-container>
        <v-col class="d-flex justify-center">
          <v-row dense v-if="showSkeletonLoader">
            <v-col cols="6" md="2" sm="4" v-for="item in 12" :key="item">
              <v-skeleton-loader
                max-height="139"
                max-width="173"
                type="image"
              ></v-skeleton-loader>
            </v-col>
          </v-row>
          <v-sheet
            v-else
            class="col-12 pa-4 mt-0"
            style="border-radius: 8px; max-width: 1225px; min-height: 294px"
          >
            <div class="d-flex align-center pl-5 pt-3 mb-6">
              <v-icon color="#27AB9C" size="28"> mdi-store </v-icon>
              <span
                class="pl-2"
                style="font-size: 24px; font-weight: 700; color: #27ab9c"
                >ร้านค้าที่ออกใบกำกับภาษีอิเล็กทรอนิกส์</span
              >
            </div>
            <div class="d-flex justify-center">
              <v-btn
                class="d-flex align-self-center mr-5"
                color="#7CCFB4"
                fab
                x-small
                @click="goToPreviousPageEtax"
                :disabled="!hasPreviousPageEtax"
                elevation="0"
                ><v-icon class="mr-1" large color="white"
                  >mdi-chevron-left</v-icon
                ></v-btn
              >
              <v-row
                no-gutters
                style="max-width: 1225px; min-height: 294px"
                v-if="currentPageItemsEtax % 6 !== 1"
              >
                <div v-for="(itemShop, index) in currentPageItemsEtax" :key="index">
                  <v-hover v-slot="{ hover }">
                    <v-card
                      :elevation="hover ? 4 : 0"
                      :style="
                        hover
                          ? 'border: 1px solid #27AB9C;'
                          : 'border: 1px solid #F3F5F7;'
                      "
                      class="ma-1 rounded-0"
                      height="139"
                      width="173"
                      @click="gotoShopDetail(itemShop)"
                    >
                      <v-card-text>
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <v-img
                              v-if="itemShop.shop_logo !== ''"
                              :src="itemShop.shop_logo"
                              loading="lazy"
                              width="130"
                              height="90"
                              contain
                            ></v-img>
                            <v-img
                              v-if="itemShop.shop_logo === ''"
                              src="@/assets/StoreNew.png"
                              loading="lazy"
                              width="84"
                              height="88"
                              contain
                            ></v-img>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                                cols="12"
                                align="center"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span
                                  class="text-truncate d-inline-block"
                                  style="max-width: 145px"
                                  >{{ itemShop.shop_name }}</span
                                >
                              </v-col>
                            </template>
                            <span>{{ itemShop.shop_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-hover>
                </div>
              </v-row>
              <v-row
                no-gutters
                style="max-width: 1225px; min-height: 294px"
                v-else
              >
                <div v-for="(itemShop, index) in currentPageItemsEtax" :key="index">
                  <v-hover v-slot="{ hover }">
                    <v-card
                      :elevation="hover ? 4 : 0"
                      :style="
                        hover
                          ? 'border: 1px solid #27AB9C;'
                          : 'border: 1px solid #F3F5F7;'
                      "
                      class="ma-1 rounded-0"
                      height="139"
                      width="173"
                      @click="gotoShopDetail(itemShop)"
                    >
                      <v-card-text>
                        <v-row dense justify="center">
                          <v-col cols="12" align="center">
                            <v-img
                              v-if="itemShop.shop_logo !== ''"
                              :src="itemShop.shop_logo"
                              loading="lazy"
                              width="130"
                              height="90"
                              contain
                            ></v-img>
                            <v-img
                              v-if="itemShop.shop_logo === ''"
                              src="@/assets/StoreNew.png"
                              loading="lazy"
                              width="84"
                              height="88"
                              contain
                            ></v-img>
                          </v-col>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <v-col
                                cols="12"
                                align="center"
                                v-bind="attrs"
                                v-on="on"
                              >
                                <span
                                  class="text-truncate d-inline-block"
                                  style="max-width: 145px"
                                  >{{ itemShop.shop_name }}</span
                                >
                              </v-col>
                            </template>
                            <span>{{ itemShop.shop_name }}</span>
                          </v-tooltip>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-hover>
                </div>
              </v-row>
              <v-btn
                class="d-flex align-self-center"
                color="#7CCFB4"
                fab
                x-small
                @click="goToNextPageEtax"
                :disabled="!hasNextPageEtax"
                elevation="0"
                ><v-icon large color="white">mdi-chevron-right</v-icon></v-btn
              >
            </div>
          </v-sheet>
        </v-col>
      </v-container>
    </v-row> -->
    <!-- <v-row justify="center" class="mt-0 mb-4" v-if="MobileSize && this.productSearchEtax.length !== 0">
      <v-col cols="12" style="" class="d-flex justify-center">
        <v-row dense v-if="showSkeletonLoader">
          <v-col cols="6" md="2" sm="4" v-for="item in 12" :key="item">
            <v-skeleton-loader
              max-height="139"
              max-width="10"
              type="image"
            ></v-skeleton-loader>
          </v-col>
        </v-row>
        <v-sheet
          v-else
          class="pa-0"
          style="border-radius: 8px; width: 100%; min-height: 294px"
        >
          <div class="d-flex align-center pl-5 pt-5 mb-6">
            <v-icon color="#27AB9C" size="26"> mdi-store </v-icon>
            <span
              class="pl-2"
              style="font-size: 20px; font-weight: 700; color: #27ab9c"
              >ร้านค้าที่ออกใบกำกับภาษีอิเล็กทรอนิกส์</span
            >
          </div>
          <div class="d-flex justify-center mb-8">
            <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToPreviousPageEtax"
              :disabled="!hasPreviousPageEtax"
              elevation="0"
              ><v-icon class="mr-1" large color="white"
                >mdi-chevron-left</v-icon
              ></v-btn
            >
            <v-row
              no-gutters
              justify="center"
              style="width: 100%; min-height: 294px"
              v-if="currentPageItemsEtax % 6 !== 1"
            >
              <div v-for="(itemShop, index) in currentPageItemsEtax" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="120"
                    @click="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 95px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row>
            <v-row no-gutters  class="justify-center" style="max-width: 1225px; min-height: 294px" v-else>
              <div v-for="(itemShop, index) in currentPageItemsEtax" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="173"
                    @click="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 95px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row>
            <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToNextPageEtax"
              :disabled="!hasNextPageEtax"
              elevation="0"
              ><v-icon large color="white">mdi-chevron-right</v-icon></v-btn
            >
          </div>
        </v-sheet>
      </v-col>
    </v-row> -->
    <!-- <v-row justify="center" class="mt-0" v-if="IpadSize && this.productSearchEtax.length !== 0">
      <v-col cols="12" style="" class="d-flex justify-center">
        <v-sheet
          class="pa-0"
          style="border-radius: 8px; width: 100vw; min-height: 294px"
        >
          <div class="d-flex align-center pl-5 pt-5 mb-6">
            <v-icon color="#27AB9C" size="28"> mdi-store </v-icon>
            <span
              class="pl-2"
              style="font-size: 24px; font-weight: 700; color: #27ab9c"
              >ร้านค้าที่ออกใบกำกับภาษีอิเล็กทรอนิกส์</span
            >
          </div>
          <div class="d-flex justify-center mb-8 pr-5 pl-5">
            <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToPreviousPageEtax"
              :disabled="!hasPreviousPageEtax"
              elevation="0"
              ><v-icon class="mr-1" large color="white"
                >mdi-chevron-left</v-icon
              ></v-btn
            >
            <v-row
              no-gutters
              justify="center"
              style="width: 100%; min-height: 294px"
              v-if="currentPageItemsEtax % 4 === 0"
            >
              <div v-for="(itemShop, index) in currentPageItemsEtax" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="130"
                    @click="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 105px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row>
            <v-row no-gutters class="justify-center" style="max-width: 1225px; min-height: 294px" v-else>
              <div v-for="(itemShop, index) in currentPageItemsEtax" :key="index">
                <v-hover v-slot="{ hover }">
                  <v-card
                    :elevation="hover ? 4 : 0"
                    :style="
                      hover
                        ? 'border: 1px solid #27AB9C;'
                        : 'border: 1px solid #F3F5F7;'
                    "
                    class="ma-1 rounded-0"
                    height="139"
                    width="173"
                    @click="gotoShopDetail(itemShop)"
                  >
                    <v-card-text>
                      <v-row dense justify="center">
                        <v-col cols="12" align="center">
                          <v-img
                            v-if="itemShop.shop_logo !== ''"
                            :src="itemShop.shop_logo"
                            loading="lazy"
                            width="130"
                            height="90"
                            contain
                          ></v-img>
                          <v-img
                            v-if="itemShop.shop_logo === ''"
                            src="@/assets/StoreNew.png"
                            loading="lazy"
                            width="84"
                            height="88"
                            contain
                          ></v-img>
                        </v-col>
                        <v-tooltip bottom>
                          <template v-slot:activator="{ on, attrs }">
                            <v-col
                              cols="12"
                              align="center"
                              v-bind="attrs"
                              v-on="on"
                            >
                              <span
                                class="text-truncate d-inline-block"
                                style="max-width: 145px"
                                >{{ itemShop.shop_name }}</span
                              >
                            </v-col>
                          </template>
                          <span>{{ itemShop.shop_name }}</span>
                        </v-tooltip>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-row>
            <v-btn
              class="d-flex align-self-center"
              color="#7CCFB4"
              fab
              x-small
              @click="goToNextPageEtax"
              :disabled="!hasNextPageEtax"
              elevation="0"
              ><v-icon large color="white">mdi-chevron-right</v-icon></v-btn
            >
          </div>
        </v-sheet>
      </v-col>
    </v-row> -->
    <!-- โปรโมชัน -->
    <!-- <v-row dense justify="center" class="mt-0 mb-4">
      <v-col cols="12" md="12" sm="12">
        <h1 v-if="IpadSize" v-bind:style="{'font-size': '20px'}"><B>โปรโมชัน</B></h1>
        <h1 v-else :style="MobileSize ? 'font-size: 18px' : 'font-size: 32px'"><B>โปรโมชัน</B></h1>
      </v-col>
      <v-col cols="6" md="4" sm="4" v-for="(item, index) in Promotion" :key="index" class="pr-4">
        <v-row dense>
          <v-card width="100%" height="100%" elevation="4" style="cursor: pointer;">
            <v-card-text class="pa-0" style="border-radius: 8px;">
              <v-img :src="item.img_promotion" width="100%" height="190" style="border-radius: 8px;"></v-img>
            </v-card-text>
          </v-card>
        </v-row>
      </v-col>
    </v-row> -->
    <!-- สั่งซื้ออีกครั้ง -->
    <!-- {{IpadSize}} -->
    <v-row
      justify="center"
      class=""
      v-if="statusLogin === true && !MobileSize && !IpadSize && Allfalse === 0"
    >
      <v-col cols="12" md="12" sm="12" v-if="itemsCard.length !== 0">
        <v-row
          style="margin-left: 75px; margin-right: 60px; margin-bottom: 10px"
        >
          <!-- <v-row
          v-if="listOfProduct !== 'ยังไม่มีรายการสินค้า'"
          style="margin-left: 75px; margin-right: 60px; margin-bottom: 10px"
        > -->
          <h1 style="font-size: 24px; font-weight: 700; color: #27ab9c">
            <B>สั่งซื้ออีกครั้ง</B>
          </h1>
          <v-spacer
            class="ml-4 spacerStyle"
            style="margin-top: 17px"
          ></v-spacer>
          <v-btn
            text
            @click="AllBuyAgain()"
            color="#27AB9C"
            class="mt-0"
            plain
            style="font-weight: 600"
          >
            ดูทั้งหมด
            <v-btn
              height="24"
              width="24"
              fab
              x-small
              elevation="0"
              class="ml-0"
            >
              <v-icon color="#27AB9C">mdi-arrow-right-circle-outline</v-icon>
            </v-btn>
          </v-btn>
        </v-row>
        <!-- <v-skeleton-loader
          max-height="240"
          max-width="600"
          type="card"
          style="margin-top: -10px; margin-right: 60px; margin-left: 75px;"
        ></v-skeleton-loader> -->
        <div v-if="itemsCard.length !== 0">
          <vue-horizontal-list
            :items="itemsCard"
            :options="optionsCard"
            style="margin-top: -30px; margin-left: 59px; margin-right: 60px"
          >
            <template v-slot:nav-prev>
              <div>
                <v-icon color="#008E00" size="32">mdi-chevron-left</v-icon>
              </div>
            </template>
            <template v-slot:nav-next>
              <div>
                <v-icon color="#008E00" size="32">mdi-chevron-right</v-icon>
              </div>
            </template>
            <template v-slot:default="{ item }">
              <a-skeleton :loading="check === true ? !loading : loading">
                <CardProductsBuyAgain :itemProduct="item" />
              </a-skeleton>
            </template>
          </vue-horizontal-list>
        </div>
      </v-col>
      <!-- <v-row dense v-else>
        <v-col cols="12" md="6" sm="6" v-for="item in 2" :key="item">
          <v-skeleton-loader
            max-width="600"
            type="card-avatar, article, actions"
          ></v-skeleton-loader>
        </v-col>
      </v-row> -->
    </v-row>
    <v-row class="py-0" v-if="statusLogin === true && MobileSize && Allfalse === 0">
      <v-col cols="12" md="12" sm="12" class="pt-0 pb-2" v-if="itemsCard.length !== 0">
        <v-row
          align="center"
          class="mb-0"
          style=""
        >
          <h2
            class="pt-1 ml-3 mt-2"
            style="font-size: 18px; font-weight: 700; color: #27ab9c"
          >
            <B>สั่งซื้ออีกครั้ง</B>
          </h2>
          <v-spacer class="ml-4 mb-8 spacerStyle" style=""></v-spacer>
          <v-btn
            text
            @click="AllBuyAgain()"
            color="#27AB9C"
            class="mt-0"
            plain
            style="font-weight: 600; font-size: 14px"
          >
            ดูทั้งหมด
            <v-btn
              height="24"
              width="24"
              fab
              x-small
              color="#DCFFDC"
              elevation="0"
              class="ml-2"
            >
              <v-icon color="#008E00">mdi-chevron-right</v-icon>
            </v-btn>
          </v-btn>
        </v-row>
        <v-row dense justify="center" class="px-0">
          <vue-horizontal-list
            :items="itemsCard"
            :options="optionsCardMobile"
            style="
              margin-top: -50px;
              width: 80vw;
            "
          >
            <template v-slot:nav-prev>
              <div>
                <v-icon color="#008E00" size="32">mdi-chevron-left</v-icon>
              </div>
            </template>
            <template v-slot:nav-next>
              <div>
                <v-icon color="#008E00" size="32">mdi-chevron-right</v-icon>
              </div>
            </template>
            <template v-slot:default="{ item }">
              <a-skeleton :loading="check === true ? !loading : loading">
                <CardProductsBuyAgain :itemProduct="item" />
              </a-skeleton>
            </template>
          </vue-horizontal-list>
        </v-row>
      </v-col>
      <!-- <v-row dense v-else>
        <v-col cols="12" md="6" sm="6" v-for="item in 1" :key="item">
          <v-skeleton-loader
            max-width="600"
            type="card-avatar, article, actions"
          ></v-skeleton-loader>
        </v-col>
      </v-row> -->
    </v-row>
    <v-row v-if="statusLogin === true && IpadSize && Allfalse === 0">
      <v-col cols="12" md="12" sm="12" v-if="itemsCard.length !== 0">
        <v-row
          align="center"
          class="mb-5"
          style=""
        >
          <h2
            class="pt-1 ml-3 mt-2"
            style="font-size: 24px; font-weight: 700; color: #27ab9c"
          >
            <B>สั่งซื้ออีกครั้ง</B>
          </h2>
          <v-spacer class="ml-4 mb-8 spacerStyle" style=""></v-spacer>
          <v-btn
            text
            @click="AllBuyAgain()"
            color="#27AB9C"
            class="mt-0"
            plain
            style="font-weight: 600; font-size: 14px"
          >
            ดูทั้งหมด
            <v-btn
              height="24"
              width="24"
              fab
              x-small
              color="#DCFFDC"
              elevation="0"
              class="ml-2"
            >
              <v-icon color="#008E00">mdi-chevron-right</v-icon>
            </v-btn>
          </v-btn>
        </v-row>
        <v-row no-gutters justify="center" v-if="itemsCard.length !== 0">
          <vue-horizontal-list
            :items="itemsCard"
            :options="optionsCardIpad"
            class="mr-1"
            style="margin-top: -50px; width: 95vw;"
          >
            <template v-slot:nav-prev>
              <div>
                <v-icon color="#008E00" size="32">mdi-chevron-left</v-icon>
              </div>
            </template>
            <template v-slot:nav-next>
              <div>
                <v-icon color="#008E00" size="32">mdi-chevron-right</v-icon>
              </div>
            </template>
            <template v-slot:default="{ item }">
              <a-skeleton :loading="check === true ? !loading : loading">
                <CardProductsBuyAgain :itemProduct="item" />
              </a-skeleton>
            </template>
          </vue-horizontal-list>
        </v-row>
      </v-col>
      <!-- <v-row dense v-else>
        <v-col cols="12" md="6" sm="6" v-for="item in 2" :key="item">
          <v-skeleton-loader
            max-width="400"
            type="card-avatar, article, actions"
          ></v-skeleton-loader>
        </v-col>
      </v-row> -->
    </v-row>
  </v-container>
</template>

<script>
// import VueSlickCarousel from 'vue-slick-carousel'
import 'vue-slick-carousel/dist/vue-slick-carousel-theme.css'
import 'vue-slick-carousel/dist/vue-slick-carousel.css'
import VueHorizontalList from 'vue-horizontal-list'
import { Encode, Decode } from '@/services'
import { Skeleton } from 'ant-design-vue'
export default {
  components: {
    'a-skeleton': Skeleton,
    VueHorizontalList,
    // VueSlickCarousel,
    // CardProducts: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardUI'),
    CardProductsBuyAgain: () =>
      import(
        /* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardBuyAgain'
      )
    // CardProductsResponsive: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "cardProduct-chunk" */ '@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      // itemsPerPage: 12,
      Allfalse: 1,
      dataType: 'ทั้งหมด',
      currentPage: 1,
      currentGroupStorePage: 1,
      model: null,
      selectedCard: null,
      settings: {
        infinite: false,
        slidesToShow: 10,
        speed: 900,
        rows: 2,
        slidesPerRow: 1,
        slidesToScroll: 10
      },
      settingresponsive: {
        infinite: false,
        slidesToShow: 2.5,
        speed: 900,
        rows: 2,
        slidesPerRow: 1,
        slidesToScroll: 2
      },
      options: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 5 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 16
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      optionsCard: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 1 },
          { start: 768, end: 992, size: 2 },
          { start: 992, end: 1200, size: 2 },
          { size: 2 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1200,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      optionsCardMobile: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 1 },
          { start: 768, end: 992, size: 2 },
          { start: 992, end: 1200, size: 2 },
          { size: 2 }
        ],
        list: {
          class: 'setCenter',
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: this.screendisplay / 2,

          // Because: #app {padding: 80px 24px;}
          padding: 40
        },
        navigation: {
          // when to show navigation
          start: 300
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: -1
        }
      },
      optionsCardIpad: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 1 },
          { start: 768, end: 992, size: 2 },
          { start: 992, end: 1200, size: 2 },
          { size: 2 }
        ],
        list: {
          // class: 'setCenter',
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: this.screendisplay / 2,

          // Because: #app {padding: 80px 24px;}
          padding: 40
        },
        navigation: {
          // when to show navigation
          start: 300
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: -1
        }
      },
      productSearch: [],
      NoDataInSearch: false,
      datas: [],
      statusLogin: false,
      onedata: [],
      Promotion: [],
      BuyProductAgain: [],
      screendisplay: '',
      tokenstatus: '',
      dataRole: '',
      StatusHomeNewProduct: false,
      status: false,
      check: false,
      loading: true,
      showSkeletonLoader: false,
      showSkeletonLoaderGroupShop: false,
      showSkeletonLoaderBuyAgain: false,
      listOfProduct: [],
      NameOfProduct: '',
      ImgOfProduct: '',
      ShotDesOfProduct: '', // ยังไม่มี
      SoldOfProduct: '', // ยังไม่มี
      RealPriceOfProduct: '', // ยังไม่มี revenue_defaul
      FakePriceOfProduct: '', // ยังไม่มี -
      DiscountOfProduct: '', // ยังไม่มี
      productSearchEtax: [],
      currentPageEtax: 1,
      currentGroupItems: [],
      path: process.env.VUE_APP_DOMAIN,
      pathAllShop: process.env.VUE_APP_DOMAIN + 'allShop?page=1',
      pathAllGroupShop: process.env.VUE_APP_DOMAIN + 'allGroupShop?page=1',
      dataTypeCat: '',
      itemFilter: []
    }
  },
  created () {
    this.showSkeletonLoader = true
    this.showSkeletonLoaderGroupShop = true
    this.showSkeletonLoaderBuyAgain = true
    this.screendisplay = screen.width
    // console.log('screen.width', screen.width)
    this.$EventBus.$on('getBuyProductAgain', this.getBuyProductAgain)
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.onedata = []
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(this.onedata)
      if (this.onedata.user === undefined && this.onedata.user === '') {
        localStorage.removeItem('oneData')
      }
      if (this.onedata.user !== undefined) {
        // this.username = onedata.user[0].first_name_th + ' ' + onedata.user[0].last_name_th
        this.statusLogin = true
        // this.AuthorityUser()
      } else {
        this.statusLogin = false
      }
    } else {
      this.statusLogin = false
      this.checkButton = false
    }
    this.getData()
    this.getResultAllShop()
    this.getResultAllGroupShop()
    // this.getResultGroupShop(this.dataTypeCat)
    this.getGroupStore()
    // this.getPromotion()
    if (localStorage.getItem('oneData') !== null) {
      this.getBuyProductAgain()
    }
  },
  mounted () {
    // this.$EventBus.$on('getBuyProductAgain', this.getBuyProductAgain)
    // this.$on('hook:beforeDestroy', () => {
    //   this.$EventBus.$off('getBuyProductAgain')
    // })
  },
  methods: {
    selectType () {
      // console.log('this.dataType', this.dataType)
      this.getResultAllShop()
    },
    goToPreviousGroupStorePage () {
      if (this.hasPreviousGroupStore) {
        this.currentGroupStorePage--
      }
    },
    goToPreviousPage () {
      if (this.hasPreviousPage) {
        this.currentPage--
      }
    },
    goToNextPage () {
      if (this.hasNextPage) {
        this.currentPage++
      }
    },
    goToPreviousPageEtax () {
      if (this.hasPreviousPageEtax) {
        this.currentPageEtax--
      }
    },
    goToNextPageEtax () {
      if (this.hasNextPageEtax) {
        this.currentPageEtax++
      }
    },
    goToNextGroupStorePage () {
      if (this.hasNextGroupStorePage) {
        this.currentGroupStorePage++
      }
    },
    async getGroupStore () {
      await this.$store.dispatch('actionsGroupStoreList')
      var response = await this.$store.state.ModuleHompage.stateGroupStoreList
      if (response.result === 'SUCCESS') {
        this.currentGroupItems = response.data.group_seller_data.map(el => {
          return {
            shop_logo: el.group_shop_media_path,
            shop_name: el.group_name,
            shop_id: el.id,
            pathLink: this.path + 'GroupShoppage/' + encodeURIComponent(`${el.group_name.replace(/\s/g, '-')}-${el.id}`)
          }
        })
        this.showSkeletonLoaderGroupShop = false
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        }
        this.showSkeletonLoaderGroupShop = false
      }
    },
    AllShop () {
      this.$router.push({ path: '/allShop?page=1' }).catch(() => {})
    },
    AllGroupShop () {
      this.$router.push({ path: '/allGroupShop?page=1' }).catch(() => {})
    },
    async AllBuyAgain () {
      this.$store.commit('openLoader')
      var responsePositionApprove = []
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (dataRole.role === 'purchaser') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          const companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          var Companyid = companyId.company.company_id
          const data = {
            company_id: Companyid
          }
          await this.$store.dispatch('actionsDetailCompany', data)
          // await this.$store.dispatch('actionsAuthorityUser')
          var responseCompanyApprove = await this.$store.state.ModuleAdminManage.stateDetailCompany
          // var responsePositionApprove = await this.$store.state.ModuleUser.stateAuthorityUser
          if (this.$store.getters.getDataAuthorityUser.length !== 0) {
            responsePositionApprove = await this.$store.getters.getDataAuthorityUser
          } else {
            await this.$store.dispatch('actionsAuthorityUser')
            responsePositionApprove = await this.$store.state.ModuleUser.stateAuthorityUser
          }
          var listCompanyAprrove = responsePositionApprove.data.list_company
          for (let i = 0; i < listCompanyAprrove.length; i++) {
            if (responseCompanyApprove.data.id === listCompanyAprrove[i].company_id) {
              localStorage.removeItem('list_Company_detail')
              localStorage.setItem('list_Company_detail', Encode.encode(listCompanyAprrove[i]))
            }
          }
          localStorage.setItem('CompanyData', Encode.encode(responseCompanyApprove.data))
          this.$store.commit('closeLoader')
          if (!this.MobileSize) {
            this.$router.push({ path: '/orderRecordCompany' }).catch(() => {})
          } else {
            this.$router.push({ path: '/orderRecordCompanyMobile' }).catch(() => {})
          }
        }
        // console.log('purchaser')
      } else {
        this.$store.commit('closeLoader')
        // console.log('ext_buyer')
        if (!this.MobileSize) {
          this.$router.push({ path: '/pobuyerProfileRecord' }).catch(() => {})
        } else {
          this.$router.push({ path: '/pobuyerProfileRecordMobile' }).catch(() => {})
        }
      }
    },
    GotoListProductCategory (item) {
      localStorage.setItem('category', Encode.encode(item))
      this.$router.push(`/ListProduct/${item.category_name}`).catch(() => {})
    },
    async getData () {
      // var datacategory = {
      //   role: 'ext_buyer',
      //   seller_shop_id: '16'
      // }
      this.datas = []
      await this.$store.dispatch('actionCategory')
      const response = await this.$store.state.ModuleShop.stateCategory
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.datas = await response.data
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.datas = []
        }
      }
    },
    async getResultAllShop () {
      var dataAllShop
      if (
        localStorage.getItem('roleUser') !== null &&
        localStorage.getItem('oneData') !== null
      ) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId = ''
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(
            Decode.decode(localStorage.getItem('SetRowCompany'))
          )
        }
        dataAllShop = {
          role_user: dataRole.role,
          company_id: companyId !== '' ? companyId.company.company_id : '-1',
          page: 'all'
        }
      } else {
        dataAllShop = {
          role_user: 'ext_buyer',
          company_id: '-1',
          page: 'all'
        }
      }
      await this.$store.dispatch('actionListAllShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateListAllShop
      // console.log('Result Search ======>', response)
      if (response.code === 200) {
        for (var i = 0; i < response.data.list_shop.length; i++) {
          // console.log('testq')
          response.data.list_shop[i].pathLink = this.path + 'shoppage/' + encodeURIComponent(`${response.data.list_shop[i].shop_name.replace(/\s/g, '-')}-${response.data.list_shop[i].shop_id}`) + '?page=1'
        }
        this.productSearch = await response.data.list_shop
        this.productSearchEtax = await response.data.shop_with_etax
        this.showSkeletonLoader = false
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.productSearch = []
          this.productSearchEtax = []
          this.showSkeletonLoader = false
          this.NoDataInSearch = true
          return
        }
      }
      // ดึงข้อมูลจาก page: 2 และถัดไป
      // for (let page = 2; ; page++) {
      //   dataAllShop.page = page
      //   await this.$store.dispatch('actionListAllShop', dataAllShop)
      //   var responsePage = await this.$store.state.ModuleShop.stateListAllShop
      //   if (responsePage.code === 200) {
      //     this.productSearch = this.productSearch.concat(
      //       responsePage.data.list_shop
      //     )
      //     if (Number(response.data.total_page) < page) {
      //       break
      //     }
      //   } else {
      //     break
      //   }
      // }
      this.showSkeletonLoader = false
    },
    gotoShopDetail (val) {
      // console.log('val------>', val)
      const shopCleaned = encodeURIComponent(val.shop_name.replace(/\s/g, '-'))
      this.$router
        .push({ path: `/shoppage/${shopCleaned}-${val.shop_id}?page=1` })
        .catch(() => {})
    },
    gotoGroupHome (val) {
      // console.log('val------>', val)
      const shopCleaned = val.shop_name.replace(/\s/g, '-')
      this.$router
        .push({ path: `/GroupShoppage/${shopCleaned}-${val.shop_id}` })
        .catch(() => {})
    },
    getPromotion () {
      this.Promotion = []
      for (var i = 0; i < 3; i++) {
        this.Promotion.push({
          img_promotion: `https://picsum.photos/500/300?image=${i * 5 + 10}`
        })
      }
    },
    async getBuyProductAgain () {
      var companyID = ''
      this.listOfProduct = []
      if (localStorage.getItem('roleUser') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (localStorage.getItem('oneData') !== null) {
          this.tokenstatus = this.onedata.user.access_token
        } else {
          this.tokenstatus = ''
        }
      } else {
        this.dataRole = ''
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(
          Decode.decode(localStorage.getItem('SetRowCompany'))
        )
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var data
      data = {
        role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
        company_id: companyID,
        type: 'limit',
        limit_item: '6'
      }
      // console.log('data------>', data)
      await this.$store.dispatch('actionOrderBuyAgain', data)
      var response = await this.$store.state.ModuleOrder.stateOrderBuyAgain
      // console.log('responseOrderBuyAgain ======>', response)
      if (response.result === 'success') {
        if (response.data.orders.length !== 0) {
          this.listOfProduct = await [...response.data.orders]
          // console.log('BuyProductAgain ===========>', this.BuyProductAgain)
          this.status = true
          this.check = true
          this.showSkeletonLoaderBuyAgain = false
          // this.StatusHomeNewProduct = true
        } else {
          this.status = true
          this.check = true
          this.showSkeletonLoaderBuyAgain = false
          // this.StatusHomeNewProduct = false
        }
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.showSkeletonLoaderBuyAgain = false
          this.listOfProduct = []
          this.status = true
          this.check = true
        }
        // this.StatusHomeNewProduct = false
      }
    },
    getBuyProductAgain22 () {
      // console.log('getBuyProductAgain------>', this.itemFake.data.orders)
      // var countOrder
      // var listOrder = []
      var data
      data = this.itemFake.data
      this.listOfProduct = data
      // countOrder = data.count_order
      // console.log('countOrder-----', countOrder)
      // console.log('data------>', data)
      for (const item of data.orders) {
        // console.log('item------>', item)
        this.NameOfProduct = item.product_name
        this.ImgOfProduct = item.product_image
        this.ShotDesOfProduct = item.short_description
        this.SoldOfProduct = item.total_sold
        this.RealPriceOfProduct = item.real_price
        this.FakePriceOfProduct = item.fake_price
        this.DiscountOfProduct = item.discount_percent
        // listOrder.push(this.NameOfProduct, this.ImgOfProduct)
        // console.log('NameOfProduct------>', this.NameOfProduct)
        // console.log('ImgOfProduct------>', this.ImgOfProduct)
        // console.log('ShotDesOfProduct------>', this.ShotDesOfProduct)
        // console.log('SoldOfProduct------>', this.SoldOfProduct)
        // console.log('FakePriceOfProduct------>', this.FakePriceOfProduct)
        // console.log('DiscountOfProduct------>', this.DiscountOfProduct)
        // console.log('ListOrder', listOrder)
      }
    },
    selectTypeGroup (value) {
      this.getResultGroupShop(value)
    },
    async getResultAllGroupShop () {
      this.$store.commit('openLoader')
      var dataAllShop = {
        group_id: ''
      }
      await this.$store.dispatch('actionFilterGroupShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateFilterGroupShop
      this.itemFilter = [{ name: 'ทั้งหมด', id: '' }]
      response.data.group_seller_data.forEach(item => {
        this.itemFilter.push({ name: item.group_name, id: item.id })
      })
      this.$store.commit('closeLoader')
    },
    async getResultGroupShop (value) {
      this.$store.commit('openLoader')
      var dataAllShop = {
        group_id: value
      }
      await this.$store.dispatch('actionFilterGroupShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateFilterGroupShop
      if (response.result === 'SUCCESS') {
        this.currentGroupItems = response.data.group_seller_data.map(el => {
          return {
            shop_logo: el.group_shop_media_path,
            shop_name: el.group_name,
            shop_id: el.id,
            pathLink: this.path + 'GroupShoppage/' + encodeURIComponent(`${el.group_name.replace(/\s/g, '-')}-${el.id}`)
          }
        })
        this.showSkeletonLoaderGroupShop = false
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        }
        this.showSkeletonLoaderGroupShop = false
      }
      this.$store.commit('closeLoader')
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm, md } = this.$vuetify.breakpoint
      return !!sm || !!md
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    totalItems () {
      return this.productSearch.length
    },
    itemsPerPage () {
      if (this.MobileSize) {
        return 4
      } else if (this.IpadProSize && this.IpadSize) {
        return 10
      } else if (this.IpadSize) {
        return 6
      } else {
        return 12
      }
    },
    totalPages () {
      if (this.MobileSize) {
        return Math.ceil(this.totalItems / 4)
      }
      return Math.ceil(this.totalItems / this.itemsPerPage)
    },
    currentPageItems () {
      if (this.MobileSize) {
        var a = 4
        var start = (this.currentPage - 1) * a
        var end = start + a
        // console.log(
        //   'currentPageItemsMobile------->',
        //   this.productSearch.slice(start, end)
        // )
        return this.productSearch.slice(start, end)
      } else {
        const start = (this.currentPage - 1) * this.itemsPerPage
        const end = start + this.itemsPerPage
        // console.log(
        //   'currentPageItems------->',
        //   this.productSearch.slice(start, end)
        // )
        return this.productSearch.slice(start, end)
      }
    },
    currentGroupPageItems () {
      if (this.MobileSize) {
        var a = 4
        var start = (this.currentGroupStorePage - 1) * a
        var end = start + a
        // console.log(
        //   'currentPageItemsMobile------->',
        //   this.productSearch.slice(start, end)
        // )
        return this.currentGroupItems.slice(start, end)
      } else {
        const start = (this.currentGroupStorePage - 1) * this.itemsPerPage
        const end = start + this.itemsPerPage
        // console.log(
        //   'currentPageItems------->',
        //   this.productSearch.slice(start, end)
        // )
        return this.currentGroupItems.slice(start, end)
      }
    },
    hasPreviousPage () {
      return this.currentPage > 1
    },
    hasPreviousGroupStore () {
      return this.currentGroupStorePage > 1
    },
    hasNextPage () {
      return this.currentPage < this.totalPages
    },
    totalItemsEtax () {
      return this.productSearchEtax.length
    },
    totalItemsGroupStore () {
      return this.currentGroupItems.length
    },
    itemsPerPageEtax () {
      if (this.MobileSize) {
        return 4
      } else if (this.IpadProSize && this.IpadSize) {
        return 10
      } else if (this.IpadSize) {
        return 6
      } else {
        return 12
      }
    },
    totalPagesEtax () {
      if (this.MobileSize) {
        return Math.ceil(this.totalItemsEtax / 4)
      }
      return Math.ceil(this.totalItemsEtax / this.itemsPerPageEtax)
    },
    totalPagesGroupStore () {
      if (this.MobileSize) {
        return Math.ceil(this.totalItemsGroupStore / 4)
      }
      return Math.ceil(this.totalItemsGroupStore / this.itemsPerPageEtax)
    },
    currentPageItemsEtax () {
      if (this.MobileSize) {
        var a = 4
        var start = (this.currentPageEtax - 1) * a
        var end = start + a
        // console.log(
        //   'currentPageItemsMobile------->',
        //   this.productSearch.slice(start, end)
        // )
        return this.productSearchEtax.slice(start, end)
      } else {
        const start = (this.currentPageEtax - 1) * this.itemsPerPageEtax
        const end = start + this.itemsPerPageEtax
        // console.log(
        //   'currentPageItems------->',
        //   this.productSearch.slice(start, end)
        // )
        return this.productSearchEtax.slice(start, end)
      }
    },
    hasPreviousPageEtax () {
      return this.currentPageEtax > 1
    },
    hasNextPageEtax () {
      return this.currentPageEtax < this.totalPagesEtax
    },
    hasNextGroupStorePage () {
      return this.currentGroupStorePage < this.totalPagesGroupStore
    },
    itemsCard () {
      var repeatOrder = []
      var product = ''
      if (this.listOfProduct.length !== 0) {
        for (const item of this.listOfProduct) {
          // console.log('item------>', item)
          product = {
            title: item.product_name,
            subtitle: item.short_description,
            amount: item.total_sold,
            price: item.fake_price,
            realPrice: item.real_price,
            vatInclude: item.vat_include,
            vatDefault: item.vat_default,
            discount: item.discount_percent,
            img: item.product_image,
            orderType: item.order_type,
            payType: item.pay_type,
            productID: item.product_id,
            quantity: item.quantity,
            shopID: item.seller_shop_id,
            attribute: item.have_attribute,
            attributeDetail: item.product_attribute_detail,
            id: item.main_sku,
            stars: parseFloat(item.stars),
            link: this.path + 'DetailProduct/' + encodeURIComponent(item.product_name.replace(/\s/g, '-') + '-' + item.product_id),
            createdAT: item.order_created_at,
            actual_stock: item.actual_stock,
            effective_stock: item.effective_stock,
            product_type: item.product_type
            // attribute_option_1: ""
            // attribute_option_2: ""
            // com_perm_id: 233
            // company_id: 30
            // company_position: 101
            // role_user: "purchaser"
          }
          repeatOrder.push(product)
        }
        return repeatOrder
      } else {
        return []
      }
    }
  }
}
</script>

<style scoped>
::v-deep .v-text-field.v-text-field--solo:not(.v-text-field--solo-flat) > .v-input__control > .v-input__slot {
    box-shadow: 0 0 0 0 !important;
    font-size: medium;
    border: 1px solid #27AB9C;
}
::v-deep .theme--light.v-input input, .theme--light.v-input textarea {
  color: #27AB9C;
  line-height: 1.5;
}
::v-deep .mdi-menu-down::before {
  color: #27AB9C;
}
.cardHover {
  transition: box-shadow 0.3s;
  margin: 0 auto;
}
.cardHover:hover {
  box-shadow: 0 0 12px rgba(33, 33, 33, 0.5);
  transform: scale(1);
}
.itemShop {
  position: relative;
  display: inline-block;
  transition: all 0.4s ease;
}
.itemShop:hover {
  transform: scale(1);
  box-shadow: 0 0 12px rgba(33, 33, 33, 0.5);
}
.textCategory {
  color: rgba(0, 0, 0, 0.8);
  font-size: 0.875rem;
  text-decoration: none;
  line-height: 1.25rem;
  height: 1.5rem;
  margin-bottom: 0.625rem;
  word-break: break-word;
  overflow: hidden;
  display: -webkit-box;
  text-overflow: ellipsis;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.container {
  max-width: 1250px;
}
</style>
<style scoped>
.setCenter {
  display: inline-block;
  justify-content: center;
}
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }

  .displayIPAD {
    display: none;
  }

  .diaplayWeb {
    display: none;
  }
}

@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }

  .displayMobile {
    display: none;
  }

  .diaplayWeb {
    display: none;
  }
}

@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }

  .displayMobile {
    display: none;
  }

  .diaplayWeb {
    display: inline;
  }
}
</style>

<style lang="scss" scoped>
.Background {
  padding-left: 2%;
  padding-right: 2%;
  padding-top: 32px;
}
.BackgroundMobile {
  padding-left: 5%;
  padding-right: 5%;
}
.Backgroundipad {
  padding-left: 2%;
  padding-right: 2%;
}
.image_card {
  padding-left: 2%;
}
.page_class {
  justify-content: center;
}
.slick-slider {
  width: 100%;
  //  padding-left: 5px;
  //  padding-right: 5px;
  ::v-deep .slick-arrow:before {
    color: #d4fad4;
    font-size: 32px;
    height: 23px;
    background: #008e00;
    border-radius: 90%;
    display: flex;
    justify-content: center;
    align-items: center;
    -webkit-text-stroke: 0.1px #008e00;
  }
}
.resposive_mobile {
  padding-left: 40%;
}
.two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  white-space: normal;
  word-break: keep-all;
}
</style>

<style>
.my-slider > .v-slide-group__next.theme--light.v-icon {
  color: rgb(234, 10, 10) !important;
}
</style>
<!-- <style scoped>
  v-sheet {
    max-width: 100%; /* Ensure v-sheet takes full width */
  }
</style> -->
