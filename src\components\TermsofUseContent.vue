<template>
  <v-card width="100%" height="100%" elevation="0" style="border-radius: 8px; border: 1px solid #F2F2F2;" class="px-4 fontStyle">
    <div v-html="dataofTermsofUse[0].data_consent"></div>
    <!-- <v-card-title class="fontStyle mb-4">
      <v-row justify="center">
        <v-col cols="12" align="center" style="font-weight: 700; font-size: 18px !important; text-decoration: underline;" class="fontStyle">
          ข้อกำหนดหลักเกี่ยวกับการบริการแพลตฟอร์ม NEX GEN COMMERCE
        </v-col>
      </v-row>
    </v-card-title>
    <v-card-text style="font-size: 16px !important;" class="fontStyle">
      <div>
        <p class="fontStyle" v-html="dataofTermsofUse"></p>
      </div>
    </v-card-text> -->
  </v-card>
</template>

<script>
export default {
  data () {
    return {
      dataofTermsofUse: ''
    }
  },
  created () {
    this.GetTermsofUse()
  },
  methods: {
    async GetTermsofUse () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsManageConsent')
      var response = await this.$store.state.ModuleAdminManage.stateManageConsent
      if (response.result === 'SUCCESS') {
        this.dataofTermsofUse = response.data.filter(e => e.name === 'Terms Of Use')
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${response.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    }
  }
}
</script>

<style scoped>
.fontStyle {
  font-family: 'Noto Sans Thai' !important;
  color: #333333;
  font-size: 16px !important;
}
</style>
