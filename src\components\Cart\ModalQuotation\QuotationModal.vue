<!-- Modal ขอใบเสนอราคา -->
<template>
  <div class="text-center">
    <v-dialog v-model="dialog" width="730" persistent>
      <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :style="{'font-size': MobileSize ? '20px' : '32px'}"><b>ขอใบเสนอราคา</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeModal()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row  no-gutters dense justify="start">
              <v-col cols="12" md="12" sm="12" xs="12" align="start" justify="start">
                <v-avatar tile>
                  <v-img src="@/assets/icons/write1.png" :width="MobileSize ? '10' : '20'" :height="MobileSize ? '30' : '40'" contain></v-img>
                </v-avatar>
                <span :style="{'font-size': MobileSize ? '18px' : '24px'}" style="font-weight: bold; color: #333333;" class="mt-2 ml-2">ข้อมูลสำหรับการขอใบเสนอราคา</span>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text>
            <v-form ref="FromPo" :lazy-validation="lazy">
              <v-row no-gutters dense>
                <!-- ชื่อบริษัท -->
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">ชื่อบริษัท</span>
                </v-col>
                <v-text-field @input="checkInput()" placeholder="ระบุชื่อบริษัทหรือชื่อองค์กร" outlined dense v-model="data.organization" class="input_text" :rules="Rules.spaceRule"></v-text-field>
                <!-- ชื่อ-นามสกุล -->
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">ชื่อ-นามสกุล</span>
                </v-col>
                <v-text-field @input="checkInput()" placeholder="ระบุชื่อและนามสกุล" outlined dense v-model="data.fullname" class="input_tex" :rules="Rules.alphabetSpaceRule"></v-text-field>
                <!-- เบอร์โทรศัพท์ -->
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">เบอร์โทรศัพท์</span>
                </v-col>
                <v-text-field @input="checkInput()" class="input_text" placeholder="ระบุหมายเลขโทรศัพท์ 10 หลัก" maxlength="10" outlined dense v-model="data.telephone" :rules="Rules.telRule" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                <!-- อีเมล -->
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">อีเมล</span>
                </v-col>
                <v-text-field @input="checkInput()" placeholder="ระบุอีเมล" outlined dense v-model="data.email" :rules="Rules.emailRule" class="input_text"></v-text-field>
                <!-- การชำระเงิน -->
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">การชำระเงิน</span>
                </v-col>
                <v-text-field @input="checkInput()" placeholder="การชำระเงิน" outlined dense v-model="data.payTerm" class="input_text" :rules="Rules.spaceRule"></v-text-field>
                <!-- ยื่นราคา -->
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">ยื่นราคา</span>
                </v-col>
                <v-text-field @input="checkInput()" placeholder="ยื่นราคา" outlined dense v-model="data.validUntil" class="input_text" :rules="Rules.spaceRule"></v-text-field>
                <!-- ชื่อ-นามสกุล Admin-->
                <v-col v-if="isAdmin" cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">ชื่อ-นามสกุล เจ้าหน้าที่ฝ่ายขาย</span>
                </v-col>
                <v-text-field v-if="isAdmin" @input="checkInput()" placeholder="ระบุชื่อและนามสกุล" outlined dense v-model="data.fullnameAdmin" :rules="Rules.alphabetSpaceRule" class="input_tex"></v-text-field>
                <!-- เบอร์โทรศัพท์ Admin-->
                <v-col v-if="isAdmin" cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">เบอร์โทรศัพท์ เจ้าหน้าที่ฝ่ายขาย</span>
                </v-col>
                <v-text-field v-if="isAdmin" @input="checkInput()" class="input_text" placeholder="ระบุหมายเลขโทรศัพท์ 10 หลัก" maxlength="10" outlined dense v-model="data.telephoneAdmin" :rules="Rules.telRule" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                <!-- อีเมล Admin-->
                <v-col v-if="isAdmin" cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;">อีเมล เจ้าหน้าที่ฝ่ายขาย</span>
                </v-col>
                <v-text-field v-if="isAdmin" @input="checkInput()" placeholder="ระบุอีเมล" outlined dense v-model="data.emailAdmin" :rules="Rules.emailRule" class="input_text"></v-text-field>
                <v-col cols="12" md="12" sm="12" xs="12">
                  <span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #333333;"><span :style="{'font-size': MobileSize ? '16px' : '20px'}" style="color: #D1392B;">*** </span>ไม่บังคับการระบุข้อมูลด้านบน</span>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn rounded class="px-5 mr-6 white--text" color="#27AB9C" :disabled="disabledBtnCreateAddress" @click="createAddress()">ขอข้อมูล</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  props: ['header'],
  data () {
    return {
      dialog: false,
      lazy: false,
      data: {
        organization: '',
        fullname: '',
        telephone: '',
        email: '',
        fullnameAdmin: '',
        telephoneAdmin: '',
        emailAdmin: '',
        payTerm: '',
        validUntil: ''
      },
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ตัวอักษรแรกไม่ควรเป็นช่องว่าง'
        ],
        alphabetSpaceRule: [
          v => /^[ก-๙A-Za-z\u00C0-\u00ff\s()]+$/.test(v) || (v === '') || 'กรอกข้อมูลไม่ถูกต้อง กรุณากรอกเฉพาะตัวอักษร',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ตัวอักษรแรกไม่ควรเป็นช่องว่าง'
        ],
        emailRule: [
          // v => /^\w+([.-]?\w+)*@\w+([.]?\w+)*(\.\w{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
          v => /^[a-zA-Z0-9._-]+@[a-zA-Z]+([.]?[a-zA-Z])*(\.[a-zA-Z]{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        telRule: [
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ]
      },
      productList: '',
      totalShipping: '',
      discount: 0,
      vat: 0,
      disabledBtnCreateAddress: false,
      isAdmin: false,
      sellerShopID: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  created () {
    this.$EventBus.$on('openModalQuotation', this.openModal)
  },
  beforeDestroy () {
    this.$EventBus.$off('openModalQuotation')
  },
  methods: {
    openModal (openModalQuotation) {
      this.dialog = !this.dialog
      this.data.organization = openModalQuotation.companyName
      this.data.fullname = openModalQuotation.fullname
      this.data.telephone = openModalQuotation.phone
      this.data.email = openModalQuotation.email
      this.productList = openModalQuotation.list_data
      this.totalShipping = openModalQuotation.total_shipping
      this.discount = openModalQuotation.discount
      this.vat = openModalQuotation.vat
      this.isAdmin = openModalQuotation.isAdmin
      if (this.isAdmin === true) {
        this.data.fullnameAdmin = openModalQuotation.fullnameAdmin
        this.data.telephoneAdmin = openModalQuotation.phoneAdmin
        this.data.emailAdmin = openModalQuotation.emailAdmin
      }
      this.sellerShopID = openModalQuotation.sellerShopID
    },
    closeModal () {
      this.dialog = !this.dialog
      this.$EventBus.$emit('closeModalQuotation', this.data)
    },
    checkInput () {
      if (!this.$refs.FromPo.validate()) {
        this.disabledBtnCreateAddress = true
      } else {
        this.disabledBtnCreateAddress = false
      }
    },
    async createAddress () {
      if (!this.$refs.FromPo.validate()) {
        this.disabledBtnCreateAddress = true
      } else {
        this.disabledBtnCreateAddress = false
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var roleUser = dataRole !== null ? dataRole.role : 'local'
        const data = {
          seller_shop_id: this.sellerShopID,
          role_user: roleUser,
          customer_company: this.data.organization,
          customer_name: this.data.fullname,
          customer_phone: this.data.telephone,
          customer_email: this.data.email,
          pay_term: this.data.payTerm,
          valid_until: this.data.validUntil,
          product_list_select: this.productList,
          total_shipping: this.totalShipping,
          discount: this.discount,
          vat: this.vat,
          contact_name: this.data.fullnameAdmin,
          contact_email: this.data.emailAdmin,
          contact_tel: this.data.telephoneAdmin
        }
        await this.$store.dispatch('ActionCreateQuManual', data)
        const response = await this.$store.state.ModuleCart.stateCreateQuManual
        if (response.result === 'SUCCESS') {
          setTimeout(function () { window.open(response.data) }, 200)
          this.dialog = !this.dialog
        } else {
          if (this.MobileSize) {
            this.$swal.fire({ html: '<h3>ดำเนินการไม่สำเร็จ</h3>', text: response.message, icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
          } else {
            this.$swal.fire({ title: 'ดำเนินการไม่สำเร็จ', text: response.message, icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
          }
        }
      }
    }
  }
}
</script>
<style scoped>
.input_text {
  height: 60px;
  opacity: 1;
}
</style>
