<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-baseline">
        <v-col>
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">แก้ไขที่อยู่ขนส่ง</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>แก้ไขที่อยู่ขนส่ง</v-card-title>
        </v-col>
        <v-col>
          <v-autocomplete no-data-text="ไม่พบร้านค้าที่ค้นหา" v-model="selectShop" :items="itemsShop" item-text="name_th" item-value="id" outlined dense placeholder="เลือกร้านค้าภายในระบบ" :menu-props="{ offsetY: true, offsetOverflowAuto: true }" @change="handleSelectChange"></v-autocomplete>
        </v-col>
      </v-row>
      <div v-if="selectShop !== 0 && selectShop !== null">
        <!-- filter menu -->
        <v-row dense class="pt-3 pb-6">
          <v-col>
            <v-sheet elevation="0">
            <v-tabs
              bg-color="indigo"
              next-icon="mdi-arrow-right-bold-box-outline"
              prev-icon="mdi-arrow-left-bold-box-outline"
              show-arrows
            >
              <v-tab
                v-for="(item, index) in dataToSelectPage"
                :key="index"
                @click="changeTab(index, item.value)"
              >
              <!-- {{item.icon}} -->
              <v-icon>{{item.icon}}</v-icon>
              <span style="font-weight: 600; font-size: 14px; line-height: 16px;" class="pt-1 pl-2">
                {{ item.text }}
                <v-chip
                  small
                  label
                  class="px-2 ml-1"
                  :color="item.color"
                  style="color: white;"
                >
                  <b>{{ item.count }}</b>
                </v-chip>
              </span>
              </v-tab>
            </v-tabs>
          </v-sheet>
          </v-col>
        </v-row>
        <!-- change date type -->
        <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3" v-if="!MobileSize">
          <!-- วันนี้ -->
          <v-col cols="3">
            <div @click="changeFilterDate('day')">
              <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px;" :style="seleteFilterDate === 'day' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
                <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'day' ? 'color: #333333;' : 'color: #27AB9C;'">วันนี้</span>
              </div>
            </div>
          </v-col>
          <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" class="mr-2 ml-2" @click="changeFilterDate('day')">
            <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px;" :style="seleteFilterDate === 'day' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'day' ? 'color: #333333;' : 'color: #27AB9C;'">วันนี้</span>
            </div>
          </div> -->
          <!-- เมื่อวาน -->
          <v-col cols="3">
            <div @click="changeFilterDate('yesterday')">
              <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #ffffff00;" :style="seleteFilterDate === 'yesterday' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
                <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'yesterday' ? 'color: #333333;' : 'color: #27AB9C;'">เมื่อวาน</span>
              </div>
            </div>
          </v-col>
          <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" class="mr-2" @click="changeFilterDate('yesterday')">
            <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #ffffff00;" :style="seleteFilterDate === 'yesterday' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'yesterday' ? 'color: #333333;' : 'color: #27AB9C;'">เมื่อวาน</span>
            </div>
          </div> -->
          <!-- สัปดาห์นี้ -->
          <v-col cols="3">
            <div @click="changeFilterDate('week')">
              <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'week' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
                <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'week' ? 'color: #333333;' : 'color: #27AB9C;'">สัปดาห์นี้</span>
              </div>
            </div>
          </v-col>
          <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" class="mr-2" @click="changeFilterDate('week')">
            <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'week' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'week' ? 'color: #333333;' : 'color: #27AB9C;'">สัปดาห์นี้</span>
            </div>
          </div> -->
          <!-- เดือนนี้ -->
          <v-col cols="3">
            <div @click="changeFilterDate('month')">
              <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'month' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
                <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'month' ? 'color: #333333;' : 'color: #27AB9C;'">เดือนนี้</span>
              </div>
            </div>
          </v-col>
          <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" @click="changeFilterDate('month')">
            <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'month' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'month' ? 'color: #333333;' : 'color: #27AB9C;'">เดือนนี้</span>
            </div>
          </div> -->
        </v-row>
        <!-- Select filter date Action -->
        <v-row :justify="IpadSize ? 'center' : 'center'" class="mt-3" v-if="!MobileSize">
          <v-container class="mx-4">
            <v-row dense>
              <!-- start date -->
              <v-col cols="3" :style="IpadSize ? '' : ''" class="">
                <v-dialog
                  ref="dialogStartDate"
                  v-model="dialogStartDate"
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="sentStartDate"
                      v-bind="attrs"
                      placeholder="เริ่มต้น"
                      outlined
                      readonly
                      dense
                      v-on="on"
                    >
                      <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                    </v-text-field>
                  </template>
                  <v-card>
                    <v-card-title>
                      <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันเริ่มต้น</span>
                      <v-spacer></v-spacer>
                      <v-btn text @click="dialogStartDate = false" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                    </v-card-title>
                    <v-date-picker
                    v-model="date"
                    scrollable
                    reactive
                    locale="TH-th"
                    no-title
                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                  >
                    <!-- @change="sentStartDate === '' ? sentStartDate = formatDate(date)  : '', $refs.dialogStartDate.save(date);" -->
                    <!-- <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="dialogStartDate = false"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="sentStartDate === '' ? sentStartDate = formatDate(date)  : '', $refs.dialogStartDate.save(date)"
                    >
                      ตกลง
                    </v-btn> -->
                    <v-btn
                      text
                      color="primary"
                      @click="cancelChooseStartDate"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="clearChooseStartDate"
                    >
                      ล้างค่า
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      :disabled="date === ''"
                      @click="setValueStartDate(date); dialogStartDate = false"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                  </v-card>
                </v-dialog>
              </v-col>
              <!-- end date -->
              <v-col cols="3" :style="IpadSize ? '' : ''" class="">
                <v-dialog
                  ref="dialogEndDate"
                  v-model="dialogEndDate"
                  width="290px"
                  persistent
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="sentEndDate"
                      v-bind="attrs"
                      placeholder="สิ้นสุด"
                      outlined
                      readonly
                      dense
                      v-on="on"
                      :disabled="sentStartDate === ''"
                    >
                      <v-icon slot="append" :color="sentStartDate === '' ? '#a0a0a0' : '#2faea0'">mdi-calendar</v-icon>
                    </v-text-field>
                  </template>
                  <v-card>
                    <v-card-title>
                    <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันสิ้นสุด</span>
                    <v-spacer></v-spacer>
                    <v-btn text @click="dialogEndDate = false" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                  </v-card-title>
                  <v-date-picker
                    v-model="date1"
                    scrollable
                    reactive
                    no-title
                    locale="TH-th"
                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                    :min="date"
                  >
                    <!-- <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="dialogEndDate = false"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="$refs.dialogEndDate.save(date1), setValueEndDate(date1)"
                    >
                      ตกลง
                    </v-btn> -->
                    <v-btn
                      text
                      color="primary"
                      @click="cancelChooseEndDate"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="clearChooseEndDate"
                    >
                      ล้างค่า
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      :disabled="date1 === ''"
                      @click="setValueEndDate(date1); dialogEndDate = false"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                  </v-card>
                </v-dialog>
              </v-col>
              <!-- select type shipping -->
              <v-col cols="3" :style="IpadSize ? '' : ''" class="">
                <v-select
                  v-model="courierCode"
                  :items="listCourier"
                  item-text="courier_name"
                  item-value="courier_code"
                  placeholder="ขนส่งทั้งหมด"
                  dense
                  :menu-props="{ overflowY: true, offsetY: true }"
                  outlined
                  class="vSelectLineHeight"
                  no-data-text="ไม่มีข้อมูลขนส่ง"
                ></v-select>
              </v-col>
              <!-- <v-col cols="3" :style="IpadSize ? '' : ''" class="">
                <v-select
                  v-model="selectedProvider"
                  :items="serviceProviderItem"
                  placeholder="ประเภทขนส่ง"
                  dense
                  :menu-props="{ overflowY: true, offsetY: true }"
                  outlined
                  class="vSelectLineHeight"
                ></v-select>
              </v-col> -->
              <!-- clear -->
              <!-- <v-col cols="3" :style="IpadSize ? '' : ''" class="">
                <v-btn outlined rounded color="primary"  @click="reset()" style="font-Weight: 700">ล้างการค้นหา</v-btn>
              </v-col> -->
            </v-row>
          </v-container>
          <!-- select status -->
          <!-- <div style="width: 200px; height: 40px;">
            <v-select
              :items="itemsStatus"
              label="สถานะทั้งหมด"
              dense
              :menu-props="{overflowY: true, offsetY: true }"
              outlined
            ></v-select>
          </div> -->
        </v-row>
        <!-- search order -->
        <v-row dense :class="MobileSize ? 'px-3' : 'px-3 pt-0'">
          <v-col cols="8" md="7" sm="7">
          <v-text-field
              v-model="searchh"
              dense
              style="border-radius: 4px;"
              outlined
              placeholder="ค้นหารหัสการสั่งซื้อ, เลขพัสดุ"
              @keyup="checkSearch"
            >
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="3" :style="IpadSize ? '' : ''" class="">
            <v-btn outlined rounded color="primary"  @click="reset()" style="font-Weight: 700">ล้างการค้นหา</v-btn>
          </v-col>
        </v-row>
        <!-- Mobile action -->
        <v-row dense v-if="MobileSize">
          <v-col cols="5" class="mt-6">
            <v-row dense>
              <!-- วันนี้ -->
              <div style="width: 124px; height: 34px;" class="ml-5 mb-4" @click="changeFilterDate('day')">
                <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'day' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'day' ? 'color: #27AB9C;' : 'color: #27AB9C;'">วันนี้</span>
                </div>
              </div>
              <!-- เมื่อวาน -->
              <div style="width: 124px; height: 34px;" class="ml-5 mb-4" @click="changeFilterDate('yesterday')">
                <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'yesterday' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'yesterday' ? 'color: #27AB9C;' : 'color: #27AB9C;'">เมื่อวาน</span>
                </div>
              </div>
              <!-- สัปดาห์นี้ -->
              <div style="width: 124px; height: 34px;" class="ml-5 mb-4" @click="changeFilterDate('week')">
                <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'week' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'week' ? 'color: #27AB9C;' : 'color: #27AB9C;'">สัปดาห์นี้</span>
                </div>
              </div>
              <!-- เดือนนี้ -->
              <div style="width: 124px; height: 34px;" class="ml-5" @click="changeFilterDate('month')">
                <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'month' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'month' ? 'color: #27AB9C;' : 'color: #27AB9C;'">เดือนนี้</span>
                </div>
              </div>
            </v-row>
          </v-col>
          <v-col cols="7" class="pt-0 mt-6">
            <!-- <v-row dense justify="start"> -->
              <!-- start date -->
              <div style="width: 175px; height: 40px;" class="ml-4 mb-3">
                <v-dialog
                  ref="dialogStartDate"
                  v-model="dialogStartDate"
                  :return-value.sync="date"
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="sentStartDate"
                      v-bind="attrs"
                      readonly
                      placeholder="เริ่มต้น"
                      outlined
                      dense
                      v-on="on"
                    >
                      <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                    </v-text-field>
                  </template>
                    <v-date-picker
                      v-model="date"
                      scrollable
                      reactive
                      locale="TH-th"
                      @change="setValueStartDate(date)"
                      no-title
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="dialogStartDate = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="sentStartDate === '' ? sentStartDate = formatDate(date)  : '', $refs.dialogStartDate.save(date)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                </v-dialog>
              </div>
              <!-- end date -->
              <div style="width: 175px; height: 40px;" class="ml-4 mb-2">
                <v-dialog
                  ref="dialogEndDate"
                  v-model="dialogEndDate"
                  :return-value.sync="date1"
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      v-model="sentEndDate"
                      v-bind="attrs"
                      readonly
                      placeholder="สิ้นสุด"
                      outlined
                      dense
                      v-on="on"
                    >
                      <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                    </v-text-field>
                  </template>
                  <v-date-picker
                    v-model="date1"
                    scrollable
                    reactive
                    no-title
                    locale="TH-th"
                    :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                    :min="date"
                  >
                    <v-spacer></v-spacer>
                    <v-btn
                      text
                      color="primary"
                      @click="dialogEndDate = false"
                    >
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      @click="$refs.dialogEndDate.save(date1), setValueEndDate(date1)"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </div>
              <!-- select type shipping -->
              <div style="width: 175px; height: 40px;" class="ml-4">
                <v-select
                  v-model="courierCode"
                  :items="listCourier"
                  item-text="courier_name"
                  item-value="courier_code"
                  placeholder="ขนส่งทั้งหมด"
                  dense
                  :menu-props="{ overflowY: true, offsetY: true }"
                  outlined
                  class="vSelectLineHeight"
                ></v-select>
              </div>
              <div :style="MobileSize ? 'width: 200px; height: 40px; margin: 10px 0px 0px 7px;' : 'width: 200px; height: 40px;'" class="ml-4 mr-2">
                <v-btn class="white--text" color="#27AB9C" :style="MobileSize ? 'min-width: 175px;' : 'min-width: 200px;'" @click="reset()"><v-icon small>mdi-delete-outline</v-icon>ล้างการค้นหา</v-btn>
              </div>
              <!-- select status -->
              <!-- <div style="width: 200px; height: 40px;">
                <v-select
                  :items="itemsStatus"
                  label="สถานะทั้งหมด"
                  dense
                  :menu-props="{overflowY: true, offsetY: true }"
                  outlined
                ></v-select>
              </div> -->
            <!-- </v-row> -->
          </v-col>
        </v-row>
        <!-- table Shipping order -->
        <v-row dense justify="center" :class="MobileSize ? 'mt-6' : 'px-3 mt-6'">
          <v-col cols="12" :class="MobileSize ? 'px-0' : ''">
            <v-data-table
              :headers="MobileSize ? headersMobile : headers"
              :items="itemTransportsOrder"
              :search="searchh"
              :hide-default-header="MobileSize ? true : false"
              :style="MobileSize ? '' : 'filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16)); border-radius: 8px;'"
              no-data-text="ไม่มีข้อมูลขนส่งในตาราง"
              no-results-text="ไม่พบข้อมูลขนส่งในตาราง"
              :footer-props="{ itemsPerPageText: 'จำนวนแถว' , itemsPerPageOptions: [5, 10, 15, 25, 50] }"
              :server-items-length="totalItems"
              :items-per-page="options.itemsPerPage"
              @update:options="updateOptions"
              :class="MobileSize ? 'px-0' : 'elevation-1'"
            >
              <!-- <template v-slot:[`header.data-table-select`]="{ on , props }">
                  <v-simple-checkbox
                    v-model="checkboxAll"
                    :indeterminate="checkboxIndeterminate"
                    :ripple="false"
                    v-bind="props"
                    v-on="on"
                    disabled
                  ></v-simple-checkbox>
              </template> -->
              <template v-slot:[`item.data-table-select`]="{ isSelected, select, item }">
                <!-- <v-simple-checkbox :ripple="false" :value="(item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน') || item.service_provider === null ? null : isSelected" @input="select($event)" :disabled="(item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน' || selectShop === -3) || item.service_provider === null ? true : false" v-if="!MobileSize"></v-simple-checkbox> -->
                <v-card elevation="0" class="d-flex pb-0" v-if="MobileSize" style="max-width: 100%; border-top: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 8px 8px 0px 0px;">
                  <v-card-text class="d-flex pb-0">
                    <v-simple-checkbox :ripple="false" :value="item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน' ? null : isSelected" @input="select($event)" :disabled="item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน' || selectShop === -3 ? true : false ? true : false" class="pt-1 mr-auto"></v-simple-checkbox>
                    <v-menu offset left bottom nudge-bottom="20">
                      <template v-slot:activator="{ attrs, on }">
                        <v-btn
                          v-if="item" class="ml-3" v-bind="attrs" v-on="on"
                          small outlined icon tile
                          style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                        >
                          <v-icon color="primary">mdi-dots-vertical</v-icon>
                        </v-btn>
                      </template>
                      <v-list>
                        <v-list-item>
                          <v-list-item-title style="cursor: pointer;"  @click="openDialogUpdateRemarkAdmin(item)">
                            <span style="font-size: small;">จัดการหมายเหตุแอดมิน</span>
                          </v-list-item-title>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-title style="cursor: pointer;" @click="item.service_provider === null || item.service_provider === 'OUT_SOURCE' ? openDialogUpdateDataStatus(item, 'out') : openDialogUpdateDataStatus(item, 'in')">
                            <span style="font-size: small;">จัดการสถานะขนส่ง</span>
                          </v-list-item-title>
                        </v-list-item>
                        <v-list-item>
                          <v-list-item-title style="cursor: pointer;" @click="item.business_type !== 'own_shipping' || selectShop === -3 ? ConfirmdeleteOrderCourier(item) : null">
                            <span style="font-size: small;" :style="item.business_type !== 'own_shipping' || selectShop === -3 ? 'color: inherit;' : 'color: #A1A1A1;'">ยกเลิกรายการขนส่ง</span>
                          </v-list-item-title>
                        </v-list-item>
                      </v-list>
                    </v-menu>
                    <!-- <v-btn class="ml-1" small @click="openDialogUpdateDataStatus(item)" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                      <v-icon  color="primary">mdi-pencil</v-icon>
                    </v-btn>
                    <v-btn class="ml-1" small @click="ConfirmdeleteOrderCourier(item)" :disabled="item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน'  || selectShop === -3 ? true : false ? true : false" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                      <v-icon color="#A1A1A1">mdi-delete</v-icon>
                    </v-btn> -->
                  </v-card-text>
                </v-card>
              </template>
            <!-- </template> -->
              <!-- Mobile -->
              <template v-slot:[`item.Detail`]="{ item }" v-if="MobileSize">
                <v-card class="pa-3 mb-4" elevation="0" style="border-bottom: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 0px 0px 8px 8px;">
                  <v-row dense>
                    <!-- icon -->
                    <!-- <v-col cols="6" align="start">
                      <v-simple-checkbox :value="isSelected" @input="select($event)"></v-simple-checkbox>
                    </v-col> -->
                    <!-- <v-col cols="6" align="end">
                      <v-btn @click="ConfirmdeleteOrderCourier(item)" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                        <v-icon color="#A1A1A1">mdi-delete</v-icon>
                      </v-btn>
                    </v-col> -->
                    <v-col cols="12">
                      <!-- <v-btn color="#27AB9C" style="border: 0" outlined width="28" height="28">
                        <v-icon>mdi-dots-vertical</v-icon>
                      </v-btn> -->
                      <v-menu offset left bottom nudge-bottom="20">
                        <template v-slot:activator="{ attrs, on }">
                          <v-btn
                            v-if="item" class="ml-3" v-bind="attrs" v-on="on"
                            small outlined icon tile
                            style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          >
                            <v-icon color="primary">mdi-dots-vertical</v-icon>
                          </v-btn>
                        </template>
                        <v-list>
                          <v-list-item>
                            <v-list-item-title style="cursor: pointer;"  @click="openDialogEditAddress(item)">
                              <span style="font-size: small;">แก้ไขที่อยู่ที่จัดส่ง</span>
                            </v-list-item-title>
                          </v-list-item>
                        </v-list>
                      </v-menu>
                    </v-col>
                    <!-- รูป -->
                    <v-col cols="6" align="start">
                      <v-avatar rounded>
                        <!-- <v-img :src="item.courier_image_path" max-width="60" max-height="60" width="100%" height="100%" contain></v-img> -->
                        <v-img :src="item.courier_image_path !== '' && item.courier_image_path !== null ? item.courier_image_path : noIMG " max-width="60" max-height="60" width="100%" height="100%" contain></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="6" align="end" class="pt-4">
                      <span style="font-weight: 700; font-size: 14px; line-height: 24px; color: #000000;">{{ item.order_no }}</span>
                    </v-col>
                    <!-- วันที่ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">วันที่</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ new Date(item.paid_datetime).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span>
                    </v-col>
                    <!-- รหัสสั่งซื้อสินค้า -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">รหัสสั่งซื้อสินค้า</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.order_number }}</span>
                    </v-col>
                    <!-- พัสดุ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">พัสดุ</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.tracking_number }}</span>
                    </v-col>
                    <!-- ประเภทขนส่ง -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">ประเภทขนส่ง</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <v-chip v-if="item.transport_ngc === 'N'" color="#fcf5bd" style="color: #bcae47;" small>ขนส่งนอก</v-chip>
                      <v-chip v-else-if="item.transport_ngc === 'Y'" color="#cdf3dc" style="color: #4ab273;" small>ขนส่งใน</v-chip>
                    </v-col>
                    <!-- สถานะ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">สถานะ</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <v-chip @click="getStatusIship(item.order_no, item.service_provider)" :color="getColorAlter(item.delivery_status)" :text-color="getTextColorAlter(item.delivery_status)" small>{{ item.delivery_status === 'ขนส่งนอก' ? 'ขนส่งนอก' : item.delivery_status }}</v-chip>
                    </v-col>
                    <!-- ผู้รับ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">ผู้รับ</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span v-if="item.dst_address !== null" style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.dst_address.dst_name }}</span>
                    </v-col>
                    <!-- เบอร์โทร -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">เบอร์โทร</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.dst_address !== null && item.dst_address !== undefined ? addDashes(item.dst_address.dst_phone) : '-' }}</span>
                    </v-col>
                    <!-- ที่อยู่ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">ที่อยู่</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.dst_address !== null ? item.dst_address.dst_address + ' ' + item.dst_address.dst_city_name + ' ' + item.dst_address.dst_district_name + ' '+ item.dst_address.dst_province_name + ' ' + item.dst_address.dst_postal_code : '-' }}</span>
                    </v-col>
                    <!-- พิมพ์ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">พิมพ์</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <v-chip :color="getPrintColor(item.print_status)" :text-color="getTextPrintColor(item.print_status)" small>{{ getTextPrint(item.print_status) }}</v-chip>
                    </v-col>
                    <!-- COD -->
                    <!-- <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">COD</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.cod_amount }}</span>
                    </v-col> -->
                    <!-- หมายเหตุ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">หมายเหตุร้านค้า</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.remark_shop ? item.remark_shop : '-' }}</span>
                    </v-col>
                    <!-- หมายเหตุ -->
                    <v-col cols="6" align="start">
                      <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">หมายเหตุแอดมิน</span>
                    </v-col>
                    <v-col cols="6" align="end">
                      <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.remark_admin ? item.remark_admin : '-' }}</span>
                    </v-col>
                  </v-row>
                </v-card>
              </template>
              <template v-slot:[`item.paid_datetime`]="{ item }">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ new Date(item.paid_datetime).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span>
              </template>
              <template v-slot:[`item.order_number`]="{ item }">
                <!-- <span v-if="item.delivery_status !== 'ยกเลิก'" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><a @click="dialogShipping(item)">{{ item.order_number }}</a></span> -->
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.order_number }}</span>
              </template>
              <template v-slot:[`item.order_no`]="{ item }">
                <v-row no-gutters>
                  <v-col cols="12" align="center">
                    <v-avatar>
                      <v-img :src="item.courier_image_path !== '' && item.courier_image_path !== null ? item.courier_image_path : noIMG " max-width="100%" max-height="100%" width="58" height="57" contain></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="12" align="center">
                    <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">{{ item.order_no === item.order_number || item.order_no === null ? '-' : item.order_no }}</span>
                  </v-col>
                </v-row>
                <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.order_no }}</span> -->
              </template>
              <template v-slot:[`item.dst_name`]="{ item }">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.dst_address !== null ? item.dst_address.dst_name : '-' }}</span>
              </template>
              <template v-slot:[`item.dst_phone`]="{ item }">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.dst_address !== null && item.dst_address !== undefined ? addDashes(item.dst_address.dst_phone) : '-' }}</span>
              </template>
              <template v-slot:[`item.dst_address`]="{ item }">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.dst_address !== null ? item.dst_address.dst_address + ' ' + item.dst_address.dst_city_name + ' ' + item.dst_address.dst_district_name + ' '+ item.dst_address.dst_province_name + ' ' + item.dst_address.dst_postal_code : '-' }}</span>
              </template>
              <template v-slot:[`item.delivery_status`]="{ item }">
                <v-chip :color="getColorAlter(item.delivery_status, item.transport_ngc)" :text-color="getTextColorAlter(item.delivery_status)" small>{{ item.delivery_status === 'ขนส่งนอก' ? 'ขนส่งนอก' : item.delivery_status }}</v-chip>
                <!-- <v-chip @click="getStatusIship(item.order_no, item.service_provider)" :color="getColorAlter(item.delivery_status)" :text-color="getTextColorAlter(item.delivery_status)" small>{{ item.delivery_status === 'ขนส่งนอก' ? 'ขนส่งนอก' : item.delivery_status }}</v-chip> -->
              </template>
              <template v-slot:[`item.transport_type`]="{ item }">
                <v-chip v-if="item.transport_ngc === 'N'" color="#fcf5bd" style="color: #bcae47;" small>ขนส่งนอก</v-chip>
                <v-chip v-else-if="item.transport_ngc === 'Y'" color="#cdf3dc" style="color: #4ab273;" small>ขนส่งใน</v-chip>
              </template>
              <template v-slot:[`item.remark_shop`]="{ item }">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.remark_shop === '' ? '-' : item.remark_shop }}</span>
              </template>
              <template v-slot:[`item.remark_admin`]="{ item }">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.remark_admin === '' ? '-' : item.remark_admin }}</span>
              </template>
              <template v-slot:[`item.print_label_status`]="{ item }">
                <v-chip :color="getPrintColor(item.print_status)" :text-color="getTextPrintColor(item.print_status)" small>{{ getTextPrint(item.print_status) }}</v-chip>
              </template>
              <!-- <template v-slot:[`item.tracking_number`]="{ item }">
                <v-row dense justify="center">
                  <v-col cols="12" align="center">
                    <v-avatar>
                      <v-img :src="item.courier_image_path" max-width="100%" max-height="100%" width="58" height="57" contain></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="12" align="center">
                    <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">{{ item.tracking_number }}</span>
                  </v-col>
                </v-row>
              </template> -->
              <template v-slot:[`item.action`]="{ item }">
                <!-- <v-btn @click="EditOrder(item)" :disabled="item.delivery_status !== 'รอเรียกพนักงาน' ? true : false ? true : false" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                  <v-icon color="#A1A1A1">mdi-pencil</v-icon>
                </v-btn> -->
                <v-menu offset left bottom nudge-bottom="20">
                  <template v-slot:activator="{ attrs, on }">
                    <v-btn
                      v-if="item" class="ml-3" v-bind="attrs" v-on="on"
                      small outlined icon tile
                      style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                    >
                      <v-icon color="primary">mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item>
                      <v-list-item-title style="cursor: pointer;"  @click="openDialogEditAddress(item)">
                        <span style="font-size: small;">แก้ไขที่อยู่ที่จัดส่ง</span>
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
                <!-- <v-btn class="ml-3" small @click="openDialogUpdateDataStatus(item)" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                  <v-icon  color="primary">mdi-pencil</v-icon>
                </v-btn>
                <v-btn class="ml-3" small @click="ConfirmdeleteOrderCourier(item)" :disabled="item.business_type !== 'own_shipping' || selectShop === -3 ? true : false ? true : false" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                  <v-icon color="#A1A1A1">mdi-delete</v-icon>
                </v-btn> -->
              </template>
              <template v-slot:[`item.datetime_status`]="{ item }">
                <span>{{item.datetime_status !== '-' ? new Date(item.datetime_status.substring(0, 10)).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) : item.datetime_status}}</span>
              </template>
            </v-data-table>
          </v-col>
        </v-row>
      </div>
      <div v-else>
        <v-col>
          <v-card :style="MobileSize ? 'margin-top: -8vw' : ''">
              <v-card-text class="d-flex align-center flex-column" style="padding: 7vw;">
                <v-img
                  src="@/assets/NoProducts.png"
                  width="300"
                  height="204"
                  contain
                ></v-img>
                <span :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'">ไม่มีรายการขนส่ง กรุณาเลือกร้านค้าในระบบ</span>
              </v-card-text>
            </v-card>
        </v-col>
      </div>
    </v-card>
    <v-dialog v-model="dialogEditAddress" width="800px" content-class="elevation-0">
      <v-card style="border-radius: 24px;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: x-large; font-weight: 700; color: #FFFFFF;">แก้ไขที่อยู่ขนส่ง
          </span>
          <v-btn icon dark @click="closeDialogEditAddress()">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row :style="MobileSize ? '' : 'font-size: medium'">
            <v-col cols="12">
              <v-sheet elevation="0">
                <v-tabs
                  v-model="typeEdit"
                  bg-color="indigo"
                  next-icon="mdi-arrow-right-bold-box-outline"
                  prev-icon="mdi-arrow-left-bold-box-outline"
                  show-arrows
                >
                  <v-tab
                    v-for="(item, index) in itemsEdit"
                    :key="index"
                    @change="changeTabEdit(index)"
                  >
                    <!-- {{item.icon}} -->
                    <v-icon>{{item.icon}}</v-icon>
                    <span style="font-weight: 600; font-size: 14px; line-height: 16px;" class="pt-1 pl-2">
                      {{ item.text }}
                  </span>
                  </v-tab>
                </v-tabs>
              </v-sheet>
            </v-col>
            <v-col>
              <span>หมายเลขคำสั่งซื้อ: <b>{{ orderNumber }}</b></span>
            </v-col>
            <v-col cols="12" v-if="typeEdit === 1">
              <span>ชื่อผู้รับ <span style="color: red;">*</span></span>
              <v-text-field
                v-model="nameEdit"
                style="border-radius: 4px;"
                outlined
                placeholder="กรอกที่อยู่เข้ารับ"
                dense
                :rules="Rules.empty"
              >
              </v-text-field>
            </v-col>
            <v-col cols="12" :style="typeEdit === 1 ? (MobileSize ? 'margin-top: -8vw' : IpadProSize || IpadSize ? 'margin-top: -3vw' : 'margin-top: -2vw') : ''">
              <span>รายละเอียดที่อยู่ <span style="color: red;">*</span></span>
              <v-text-field
                v-model="detailAddress"
                style="border-radius: 4px;"
                outlined
                :placeholder="typeEdit === 0 ? 'กรอกที่อยู่เข้ารับ' : 'กรอกที่อยู่จัดส่ง'"
                dense
                :rules="Rules.empty"
              >
              </v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6" :style="MobileSize ? 'margin-top: -8vw' : IpadProSize || IpadSize ? 'margin-top: -3vw' : 'margin-top: -2vw'">
              <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
              <addressinput-subdistrict label="" style="border-radius: 8px !important;" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล" />
              <div v-if="checkSubDistrictError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6" :style="IpadProSize || IpadSize ? 'margin-top: -3vw' : 'margin-top: -2vw'">
              <span>อำเภอ/เขต<span style="color: red;"> *</span></span>
              <addressinput-district label="" style="border-radius: 8px !important;" v-model="districtText" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ"/>
              <div v-if="checkDistrictError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6">
              <span>จังหวัด<span style="color: red;"> *</span></span>
              <addressinput-province label="" style="border-radius: 8px !important;" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="provinceText" placeholder="ระบุจังหวัด"/>
              <div v-if="checkProvinceError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6">
              <span>รหัสไปรษณีย์<span style="color: red;"> *</span></span>
              <addressinput-zipcode style="border-radius: 8px !important;" label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์"/>
              <div v-if="checkZipcodeError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6">
              <span>หมายเลขโทรศัพท์<span style="color: red;"> *</span></span>
              <v-text-field
                v-model="phone"
                style="border-radius: 4px;"
                outlined
                placeholder="กรอกที่อยู่เข้ารับ"
                dense
                :rules="Rules.tel"
                oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 10)"
              >
              </v-text-field>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: -8vw' : IpadProSize || IpadSize ? 'margin-top: -3vw' : 'margin-top: -2vw'">
              <span>หมายเหตุ<span style="color: red;"> *</span></span>
              <v-textarea
                v-model="remarkEdit"
                outlined
                :rules="Rules.empty"
                placeholder="ระบุหมายเหตุการแก้ไขที่อยู่"
                :style="MobileSize ? 'height: 25vw;' : ''"
                :validate-on-blur="true"
              ></v-textarea>
            </v-col>
            <v-col cols="12" class="d-flex" :class="MobileSize ? 'mt-10' : ''" :style="!MobileSize ? 'margin-top: -2vw;' : ''">
              <v-btn outlined rounded color="#27AB9C" @click="closeDialogEditAddress()">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded color="#27AB9C" style="color: #fff;" @click="editShippingAddress()">ตกลง</v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog content-class="elevation-0" v-model="dialogConfirmEdit" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="backtoPage()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขที่อยู่ขนส่งเรียบร้อย</b></p>
            <!-- <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>กรุณารอเจ้าหน้าที่ตรวจสอบ</b></p>
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ภายใน 24 ชั่วโมง</b></p> -->
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeDialogEditAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
Vue.use(VueThailandAddress)
// import { Decode } from '@/services'
export default {
  data () {
    return {
      selectShop: 0,
      itemsShop: [],
      dataToSelectPage: [
        { icon: 'mdi-package-variant-closed', text: 'ทั้งหมด', value: '', count: 0, color: '#27AB9C' },
        { icon: 'mdi-clock-outline', text: 'รอเตรียมจัดส่ง', value: 'waiting_shipping', count: 0, color: '#49C1D4' },
        { icon: 'mdi-account-clock', text: 'รอเรียกพนักงาน', value: 'waiting', count: 0, color: '#FAAD14' },
        { icon: 'mdi-calendar-clock', text: 'รอเข้ารับพัสดุ', value: 'waiting_picked_up', count: 0, color: '#FAAD14' }
      ],
      itemsEdit: [
        { icon: 'mdi-store', text: 'แก้ไขที่อยู่ร้านค้า', value: 'seller', count: 0, color: '#27AB9C' },
        { icon: 'mdi-cube-send', text: 'แก้ไขที่อยู่ผู้ซื้อ', value: 'buyer', count: 0, color: '#FAAD14' }
      ],
      shopID: 0,
      seleteFilterDate: '',
      options: {
        page: 1,
        itemsPerPage: 10
      },
      dialogStartDate: false,
      sentStartDate: '',
      dialogEndDate: false,
      sentEndDate: '',
      courierCode: '',
      // selectedProvider: '',
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      listCourier: [],
      // serviceProviderItem: [
      //   { text: 'ทั้งหมด', value: 'all' },
      //   { text: 'ขนส่งใน', value: 'Y' },
      //   { text: 'ขนส่งนอก', value: 'N' }
      // ],
      dateStartToSent: '',
      headers: [
        // { text: 'วันที่', value: 'paid_datetime', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text' }
        { text: 'วันที่', value: 'paid_datetime', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อสินค้า', value: 'order_number', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'พัสดุ', value: 'order_no', sortable: false, align: 'start', width: '170', class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทขนส่ง', value: 'transport_type', sortable: false, align: 'center', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'delivery_status', sortable: false, align: 'start', width: '150', class: 'backgroundTable fontTable--text' },
        { text: 'ผู้รับ', value: 'dst_name', sortable: false, align: 'start', width: '150', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทร', value: 'dst_phone', sortable: false, align: 'start', width: '140', class: 'backgroundTable fontTable--text' },
        { text: 'ที่อยู่', value: 'dst_address', sortable: false, align: 'start', width: '300', class: 'backgroundTable fontTable--text' },
        { text: 'พิมพ์', value: 'print_label_status', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        // { text: 'COD', value: 'cod_amount', sortable: false, align: 'start', width: '123', class: 'backgroundTable fontTable--text' },
        { text: 'วันจัดส่งสำเร็จ', value: 'datetime_status', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุร้านค้า', value: 'remark_shop', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุแอดมิน', value: 'remark_admin', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' }
      ],
      headersMobile: [
        { text: 'รายละเอียด', value: 'Detail', sortable: false, width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      itemTransportsOrder: [],
      searchh: '',
      totalItems: 0,
      noIMG: require('@/assets/NoImage.png'),
      statusCode: ['waiting_shipping', 'waiting', 'waiting_picked_up'],
      dialogEditAddress: false,
      tab: null,
      typeEdit: 0,
      detailAddress: '',
      orderNumber: '',
      checkDistrictError: '',
      checkSubDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      mobileNumber: '',
      subdistricttext: '',
      phone: '',
      districtText: '',
      provinceText: '',
      zipcodeText: '',
      remarkEdit: '',
      nameEdit: '',
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        empty: [
          v => !!v || 'กรุณากรอกข้อมูล'
        ]
      },
      dialogShippingDetail: false,
      dialogConfirmEdit: ''
      // Rules: {
      //   empty: [v => !!v || 'กรุณากรอกข้อมูล']
      // }
    }
  },
  created () {
    this.getShopData()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkValidate () {
      if (this.detailAddress === '' || this.phone === '' || this.subdistricttext === '' || this.districtText === '' || this.provinceText === '' || this.zipcodeText === '' || this.remarkEdit === '' || (this.typeEdit === 1 && this.nameEdit === '')) {
        return true
      } else {
        return false
      }
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/UpdateShippingAddressMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'UpdateShippingAddress')
        this.$router.push({ path: '/UpdateShippingAddress' }).catch(() => {})
      }
    },
    sentStartDate (val) {
      // console.log(val, 'start')
    },
    sentEndDate (val) {
      // console.log(val, 'end')
    },
    dateStartToSent (val) {
      // console.log(val, 'dateStartToSent')
    },
    // async selectedProvider () {
    //   await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    // },
    courierCode (val) {
      this.options.page = 1
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, val, this.seleteFilterDate, this.searchh)
    },
    typeEdit (val) {
      // console.log(val)
    },
    subdistricttext (val) {
      if (/\s/g.test(val)) {
        this.subdistricttext = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcodeText = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    districtText (val) {
      if (/\s/g.test(val)) {
        this.districtText = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.provinceText = ''
        }
      }
    },
    provinceText (val) {
      if (/\s/g.test(val)) {
        this.provinceText = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.districtText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.districtText = ''
        }
      }
    },
    zipcodeText (val) {
      if (/\s/g.test(val)) {
        this.zipcodeText = val.replace(/\s/g, '').substring(0, 5)
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            this.zipcodeText = this.checkZipcode
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistricttext = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.subdistricttext = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    // validateField () {
    //   this.$refs.remarkEdit.validate()
    // },
    openDialogEditAddress (item) {
      this.dialogEditAddress = true
      this.sellerEdit = item.src_address
      this.buyerEdit = item.dst_address
      this.detailAddress = this.sellerEdit.src_address
      this.orderNumber = item.order_number
      this.phone = this.sellerEdit.src_phone
      this.subdistricttext = this.sellerEdit.src_district_name
      this.districtText = this.sellerEdit.src_city_name
      this.provinceText = this.sellerEdit.src_province_name
      this.zipcodeText = this.sellerEdit.src_postal_code
      this.nameEdit = this.sellerEdit.src_name
    },
    changeTabEdit (item) {
      // this.$store.commit('openLoader')
      // setTimeout(() => {
      this.typeEdit = item
      if (this.typeEdit === 0) {
        this.detailAddress = this.sellerEdit.src_address
        this.phone = this.sellerEdit.src_phone
        this.subdistricttext = this.sellerEdit.src_district_name
        this.districtText = this.sellerEdit.src_city_name
        this.provinceText = this.sellerEdit.src_province_name
        this.zipcodeText = this.sellerEdit.src_postal_code
        this.nameEdit = this.sellerEdit.src_name
        this.remarkEdit = ''
      } else {
        this.detailAddress = this.buyerEdit.dst_address
        this.phone = this.buyerEdit.dst_phone
        this.subdistricttext = this.buyerEdit.dst_district_name
        this.districtText = this.buyerEdit.dst_city_name
        this.provinceText = this.buyerEdit.dst_province_name
        this.zipcodeText = this.buyerEdit.dst_postal_code
        this.nameEdit = this.buyerEdit.dst_name
        this.remarkEdit = ''
        // console.log(this.subdistricttext, this.districtText, this.districtText, this.provinceText, this.zipcodeText)
      }
      // this.$store.commit('closeLoader')
      // }, 100)
    },
    closeDialogEditAddress () {
      this.dialogEditAddress = false
      this.typeEdit = 0
      this.remarkEdit = ''
      this.dialogConfirmEdit = false
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      // console.log(this.typeEdit)
    },
    async editShippingAddress () {
      this.$store.commit('openLoader')
      if (this.checkValidate) {
        if (this.nameEdit === '') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณากรอกชื่อผู้รับ'
          })
        } else if (this.detailAddress === '') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณากรอกรายละเอียดที่อยู่'
          })
        } else if (this.subdistricttext === '' || this.districtText === '' || this.provinceText === '' || this.zipcodeText === '') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณากรอกข้อมูลที่อยู่ให้ครบถ้วน'
          })
        } else if (this.phone === '') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณากรอกหมายเลขโทรศัพท์'
          })
        } else if (this.remarkEdit === '') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณากรอกหมายเหตุการแก้ไขที่อยู่'
          })
        }
        this.$store.commit('closeLoader')
      } else {
        var data = {
          seller_shop_id: this.shopID,
          order_number: this.orderNumber,
          phone: this.phone,
          address: this.detailAddress,
          sub_district: this.subdistricttext,
          district: this.districtText,
          province: this.provinceText,
          zipcode: this.zipcodeText,
          remark: this.remarkEdit,
          edit: this.typeEdit === 0 ? 'seller' : 'buyer',
          name: this.nameEdit
        }
        // await this.$store.dispatch('ActionsEditShippingAddress', data)
        // var res = await this.$store.state.ModuleManageShop.stateEditShippingAddress
        await this.$store.dispatch('ActionsEditShippingAddress', data)
        const response = await this.$store.state.ModuleManageShop.stateEditShippingAddress
        if (response.status === 201) {
          this.dialogConfirmEdit = true
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
        }
      }
    },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsgetIshiplistAllShop')
      var response = await this.$store.state.ModuleDashboardTransport.stategetIshiplistAllShop
      if (response.code === 200) {
        // var statAllShop = [{ name_th: 'ทั้งหมด', id: -3 }]
        this.itemsShop = response.data
        // this.itemsShop = statAllShop.concat(this.itemsShop)
      }
      this.$store.commit('closeLoader')
    },
    async getListIship (code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword, type) {
      this.$store.commit('openLoader')
      // console.log('code----->', code)
      // console.log('startDate----->', startDate)
      // console.log('endDate----->', endDate)
      // console.log('courierCode----->', courierCode)
      // console.log('seleteFilterDate----->', seleteFilterDate)
      // console.log('searchkeyword----->', searchkeyword)
      // console.log('type----->', type)
      // var data = {
      //   role_user: 'admin',
      //   seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
      //   status_code: code !== undefined && code !== 'all' ? code : '',
      //   start_date: startDate !== undefined ? startDate : '',
      //   end_date: endDate !== undefined ? endDate : '',
      //   courier_code: courierCode !== undefined ? courierCode : '',
      //   search_keyword: searchkeyword !== undefined ? searchkeyword : '',
      //   date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
      //   page: this.options.page,
      //   count: this.options.itemsPerPage,
      //   transport_ngc: this.selectedProvider === '' ? '' : this.selectedProvider === 'Y' ? '1' : '0'
      //   // date_type: 'day' 'yesterday' 'week' 'month',
      // }
      // var transportNgc = ''
      // if (this.selectedProvider === 'all') {
      //   transportNgc = ''
      // } else {
      //   transportNgc = this.selectedProvider
      // }
      // if (type === 'export') {
      //   const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      //   var data = {
      //     role_user: 'admin',
      //     seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
      //     status_code: code !== undefined && code !== 'all' ? code : '',
      //     start_date: startDate !== undefined ? (new Date(Date.now() - (30 * 24 * 60 * 60 * 1000) - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10) : '',
      //     end_date: endDate !== undefined ? (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10) : '',
      //     courier_code: courierCode !== undefined ? courierCode : '',
      //     search_keyword: searchkeyword !== undefined ? searchkeyword : '',
      //     date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
      //     transport_ngc: transportNgc === '' ? '' : transportNgc === 'Y' ? '1' : '0'
      //     // date_type: 'day' 'yesterday' 'week' 'month',
      //   }
      //   await this.axios({
      //     url: `${process.env.VUE_APP_BACK_END2}iship/exportOrderList`,
      //     headers: { Authorization: `Bearer ${oneData.user.access_token}` },
      //     method: 'POST',
      //     data: data,
      //     responseType: 'blob'
      //   }).then((response) => {
      //     this.$store.commit('closeLoader')
      //     const fileURL = window.URL.createObjectURL(new Blob([response.data]))
      //     const fileLink = document.createElement('a')
      //     fileLink.href = fileURL
      //     fileLink.setAttribute('download', 'Report_Delivery.xlsx')
      //     document.body.appendChild(fileLink)
      //     fileLink.click()
      //   }).catch((error) => {
      //     this.$store.commit('closeLoader')
      //     console.log(error)
      //     if (error.response && error.response.status === 500) {
      //       this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2000 })
      //     }
      //   })
      // }
      var data = {
        role_user: 'admin',
        seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
        status_code: code !== undefined && code !== 'all' ? code : '',
        start_date: startDate !== undefined ? startDate : '',
        end_date: endDate !== undefined ? endDate : '',
        courier_code: courierCode !== undefined ? courierCode : '',
        search_keyword: searchkeyword !== undefined ? searchkeyword : '',
        date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
        page: this.options.page,
        count: this.options.itemsPerPage,
        transport_ngc: '1'
        // date_type: 'day' 'yesterday' 'week' 'month',
      }
      // console.log(data)
      this.itemTransportsOrder = []
      this.listOrder = await false
      await this.$store.dispatch('ActionsGetListIshipAdmin', data)
      const response = await this.$store.state.ModuleManageShop.stateGetListIshipAdmin
      if (response.data.result === 'Success') {
        // this.$store.commit('closeLoader')
        var itemsStatus = response.data.transports_status
        // console.log(itemsStatus)
        this.dataToSelectPage[0].count = await itemsStatus[1].total + itemsStatus[2].total + itemsStatus[3].total
        this.dataToSelectPage[1].count = await itemsStatus[1].total
        this.dataToSelectPage[2].count = await itemsStatus[2].total
        this.dataToSelectPage[3].count = await itemsStatus[3].total
        this.listOrder = await true
        this.itemTransportsOrder = [...response.data.data]
        this.totalItems = response.data.max_items
        this.checkboxAll = false
        this.selected = []
        this.itemTransportsOrder.forEach(item => {
          if (item.delivery_status === 'รอเข้ารับพัสดุ') {
            this.statusShipping = false
          } else { this.statusShipping = false }
        })
        if (response.data.data.length === 0) {
          this.totalItems = 0
        }
        window.scrollTo(0, 0)
        this.$store.commit('closeLoader')
      } else if (response.message === 'This user does not have permission') {
        this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์เข้าถึงรายงานการจัดส่ง', showConfirmButton: false, timer: 2000 })
      } else {
        this.$store.commit('closeLoader')
        if (response.data.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          this.listOrder = await false
          this.$swal.fire({ icon: 'error', text: response.data.message, showConfirmButton: false, timer: 2000 })
        }
      }
      // this.$store.commit('openLoader')
      // this.itemTransportsOrder = response.data.data
    },
    async getCourierType (val) {
      var data = {
        seller_shop_id: val === -3 ? -3 : this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierType', data)
      const response = await this.$store.state.ModuleManageShop.GetCourierType
      this.listCourier = response.data.courier_list
    },
    async handleSelectChange () {
      // console.log(this.selectShop, 'selectShop')
      this.$store.commit('openLoader')
      this.shopID = this.selectShop
      this.options.page = 1
      await this.getCourierType()
      await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      this.$store.commit('closeLoader')
    },
    changeFilterDate (filter) {
      this.seleteFilterDate = filter
      this.options.page = 1
      this.dateStartToSent = undefined
      this.sentStartDate = ''
      this.dateEndToSent = undefined
      this.sentEndDate = ''
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    changeTab (val, value) {
      // console.log(val, 6666)
      this.selectItem = val
      this.statusCode = value === '' ? ['waiting_shipping', 'waiting', 'waiting_picked_up'] : [value]
      this.options.page = 1
      // this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
      // code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    cancelChooseStartDate () {
      this.date = ''
      this.date1 = ''
      this.sentStartDate = ''
      this.dialogStartDate = false
      this.sentEndDate = ''
    },
    clearChooseStartDate () {
      this.date = ''
      this.date1 = ''
      this.sentStartDate = ''
      this.sentEndDate = ''
    },
    cancelChooseEndDate () {
      this.date1 = ''
      this.sentEndDate = ''
      this.dateEndToSent = ''
      this.dialogEndDate = false
    },
    clearChooseEndDate () {
      this.date1 = ''
      this.sentEndDate = ''
      this.dateEndToSent = ''
    },
    setValueStartDate (val) {
      this.sentStartDate = this.formatDate(val)
      this.sentEndDate = ''
      this.dateEndToSent = ''
      this.dateStartToSent = val
      this.date1 = ''
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    setValueEndDate (val) {
      // console.log(this.dateStartToSent, 'this.dateStartToSent')
      this.sentEndDate = this.formatDate(val)
      this.dateEndToSent = val
      this.seleteFilterDate = undefined
      // code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      // this.compareDates()
    },
    compareDates () {
      var date1 = new Date(this.dateStartToSent)
      var date2 = new Date(this.dateEndToSent)
      var now = new Date(this.formatDateToSent(new Date().toISOString().substr(0, 10)))
      var diffTime = Math.abs(date2 - date1)
      var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
      var lastDayOfMonth = new Date(date2.getFullYear(), date2.getMonth() + 1, 0).getDate()
      // console.log(date1, '//////', date2, '//////', now, '//////', diffTime, '//////', diffDays)

      if (isNaN(diffDays) || parseInt(diffDays) === 0) {
        var toDayORYester = Math.ceil(Math.abs(now - date1) / (1000 * 60 * 60 * 24))
        this.seleteFilterDate = isNaN(toDayORYester) || parseInt(toDayORYester) === 0 ? 'day' : parseInt(toDayORYester) === 1 ? 'yesterday' : ''
      } else {
        if (parseInt(diffDays) >= 1 && parseInt(diffDays) <= 7) {
          var minOfWeek = new Date()
          minOfWeek.setDate(minOfWeek.getDate() - (minOfWeek.getDay()))
          var maxOfWeek = new Date()
          maxOfWeek.setDate(maxOfWeek.getDate() + (6 - maxOfWeek.getDay()))
          var min = new Date(this.formatDateToSent(new Date(minOfWeek).toISOString().substr(0, 10)))
          var max = new Date(this.formatDateToSent(new Date(maxOfWeek).toISOString().substr(0, 10)))
          if (Math.abs(date1) >= Math.abs(min) && Math.abs(max) >= Math.abs(date2)) {
            this.seleteFilterDate = 'week'
          }
        } else if (parseInt(diffDays) >= 1 && parseInt(diffDays) <= 30) {
          // if ((new Date(this.dateStartToSent)).getMonth() === (new Date(this.dateEndToSent)).getMonth() && (new Date(this.dateStartToSent)).getMonth() === (new Date()).getMonth()) {
          //   this.seleteFilterDate = 'month'
          // }
          if (date1.getDate() === 1 && (date2.getDate() === lastDayOfMonth || date2.getTime() >= now.getTime())) {
            this.seleteFilterDate = 'month'
          }
        }
        // if (parseInt(diffDays) === 6) {
        //   this.seleteFilterDate = 'week'
        // var minOfWeek = new Date()
        // minOfWeek.setDate(minOfWeek.getDate() - (minOfWeek.getDay()))
        // var maxOfWeek = new Date()
        // maxOfWeek.setDate(maxOfWeek.getDate() + (6 - maxOfWeek.getDay()))
        // var min = new Date(this.formatDateToSent(new Date(minOfWeek).toISOString().substr(0, 10)))
        // var max = new Date(this.formatDateToSent(new Date(maxOfWeek).toISOString().substr(0, 10)))
        // if (Math.abs(date1) >= Math.abs(min) && Math.abs(max) >= Math.abs(date2)) {
        //   this.seleteFilterDate = 'week'
        // }
        // else if (parseInt(diffDays) >= 1 && parseInt(diffDays) <= 30) {
        //   if (date1.getDate() === 1 && (date2.getDate() === lastDayOfMonth || date2.getTime() >= now.getTime())) {
        //     this.seleteFilterDate = 'month'
        //   }
        // }
      }
    },
    formatDateToSent (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    updateOptions (options) {
      this.options = options
      if (this.selectShop !== 0) {
        this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      }
    },
    getColorAlter (val, transportNgc) {
      if (val === 'รอเรียกพนักงาน' || val === 'รอเข้ารับพัสดุ' || val === 'รอเรียกพนักงาน') {
        return '#d7e2f6'
      } else if (val === 'พัสดุเข้าระบบ') {
        return '#d7e2f6'
      } else if (val === 'รอเตรียมจัดส่ง') {
        return transportNgc === 'N' ? '#b9b9b9' : '#49C1D4'
      } else if (val === 'รอเลือกขนส่ง') {
        return '#d7e2f6'
      } else if (val === 'รับพัสดุเข้าระบบ') {
        return '#b8ccee'
      } else if (val === 'อยู่ระหว่างขนส่ง') {
        return '#b8ccee'
      } else if (val === 'อยู่ระหว่างจัดส่ง') {
        return '#b8ccee'
      } else if (val === 'พัสดุถึงสถานีคัดแยก') {
        return '#b8ccee'
      } else if (val === 'กำลังนำส่ง') {
        return '#b8ccee'
      } else if (val === 'จัดส่งแล้ว') {
        return '#def9d1'
      } else if (val === 'ส่งคืนสำเร็จ') {
        return '#def9d1'
      } else if (val === 'ชำระเงินสำเร็จ') {
        return '#def9d1'
      } else if (val === 'จัดส่งสำเร็จ') {
        return '#def9d1'
      } else if (val === 'ขนส่งนอก') {
        return ''
      } else {
        return '#fdcbcd'
      }
    },
    getTextColorAlter (val) {
      if (val === 'รอเรียกพนักงาน' || val === 'รอเข้ารับพัสดุ' || val === 'รอเรียกพนักงาน') {
        return '#1c3d77'
      } else if (val === 'พัสดุเข้าระบบ') {
        return '#1c3d77'
      } else if (val === 'รอเตรียมจัดส่ง') {
        return '#ffffff'
      } else if (val === 'รอเลือกขนส่ง') {
        return '#1c3d77'
      } else if (val === 'รับพัสดุเข้าระบบ') {
        return '#497bd4'
      } else if (val === 'อยู่ระหว่างขนส่ง') {
        return '#497bd4'
      } else if (val === 'อยู่ระหว่างจัดส่ง') {
        return '#497bd4'
      } else if (val === 'พัสดุถึงสถานีคัดแยก') {
        return '#497bd4'
      } else if (val === 'กำลังนำส่ง') {
        return '#497bd4'
      } else if (val === 'จัดส่งแล้ว') {
        return '#52C41A'
      } else if (val === 'ส่งคืนสำเร็จ') {
        return '#52C41A'
      } else if (val === 'ชำระเงินสำเร็จ') {
        return '#52C41A'
      } else if (val === 'จัดส่งสำเร็จ') {
        return '#52C41A'
      } else if (val === 'ขนส่งนอก') {
        return ''
      } else {
        return '#F5222D'
      }
      // { icon: require('@/assets/NSG/shipping_icon/All.png'), text: 'ทั้งหมด', value: 'all', count: 0, color: '#27AB9C' },
      // { icon: require('@/assets/NSG/shipping_icon/wait_shipping.png'), text: 'รอเรียกพนักงาน', value: 'waiting', count: 0, color: '#FAAD14' },
      // { icon: require('@/assets/NSG/shipping_icon/wait_shipping.png'), text: 'พัสดุเข้าระบบ', value: 'picked_up', count: 0, color: '#49C1D4' },
      // { icon: require('@/assets/NSG/shipping_icon/shipping.png'), text: 'ระหว่างขนส่ง', value: 'shipping', count: 0, color: '#AC6BF1' },
      // { icon: require('@/assets/NSG/shipping_icon/shipping.png'), text: 'กำลังนำส่ง', value: 'progress', count: 0, color: '#85BEEF' },
      // { icon: require('@/assets/NSG/shipping_icon/success.png'), text: 'จัดส่งสำเร็จ', value: 'shipped', count: 0, color: '#52C41A' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ติดต่อผู้รับไม่ได้', value: 'issue', count: 0, color: '#FAAD14' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'พัสดุตีกลับ', value: 'return', count: 0, color: '#FA1414' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ส่งคืนสำเร็จ', value: 'return_success', count: 0, color: '#52C41A' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ชำระเงินสำเร็จ', value: 'payment_success', count: 0, color: '#52C41A' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ยกเลิก', value: 'cancel', count: 0, color: '#F5222D' }
    },
    addDashes (f) {
      if (f === undefined || f === null) {
        return '-'
      } else {
        var number = f.slice(0, 3) + '-' + f.slice(3, 11)
        return number
      }
    },
    getPrintColor (val) {
      if (val === 'N') {
        return '#FCF0DA'
      } else {
        return '#F0F9EE'
      }
    },
    getTextPrintColor (val) {
      if (val === 'N') {
        return '#FAAD14'
      } else {
        return '#00B500'
      }
    },
    getTextPrint (val) {
      if (val === 'N') {
        return 'รอพิมพ์'
      } else {
        return 'พิมพ์แล้ว'
      }
    },
    reset () {
      this.courierCode = ''
      this.selectItem = 0
      // this.statusCode = ''
      this.dateStartToSent = ''
      this.dateEndToSent = ''
      this.seleteFilterDate = ''
      this.sentEndDate = ''
      this.sentStartDate = ''
      this.search = ''
      this.searchh = ''
      this.selectedProvider = ''
      this.date = ''
      this.date1 = ''
      // this.getCourier()
      // this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    checkSearch () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.options.page = 1
        this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      }, 500)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistricttext)
      this.checkdistrictConfirm(this.districtText)
      this.checkprovinceConfirm(this.provinceText)
      this.checkzipcodeConfirm(this.zipcodeText)
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistricttext
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.districtText
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.provinceText
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcodeText)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style>

</style>
<style lang="scss" scoped>
::v-deep table {
    tbody {
    tr {
        td:nth-child(13) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        text-align: center !important;
        }
    }
    }
    thead {
    tr {
        th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(13) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        }
    }
    }
}
</style>
<style scoped>
::v-deep .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
  white-space: nowrap !important;
  text-align: center !important;
}
.v-tab.v-tab--active {
  margin-left: 0 !important;
}
::v-deep .th-address-input {
  border-radius: 4px;
}
::v-deep input.th-address-input {
  color: black !important;
}
</style>
