<template>
  <div>
    <div v-if="itemsCart.length === 0">
      <v-row>
        <v-col cols="12" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptycart.png" max-height="350px" max-width="350px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; color: #27AB9C;"><b>ยังไม่มีสินค้าในรถเข็น</b></h2>
          <div style="padding-top: 20px; padding-bottom: 50px;">
            <v-btn class="white--text" color="#27AB9C" @click="goHomepage()">ช้อปปิ้งกันเลย</v-btn>
          </div>
        </v-col>
      </v-row>
    </div>
    <div v-else>
      <v-breadcrumbs :items="items">
        <template v-slot:divider>
          <v-icon>mdi-chevron-right</v-icon>
        </template>
          <template v-slot:item="{ item }">
            <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
              <span :style="{color: item.disabled === true ? '#27AB9C' : '#636363','font-size': '16px'}">{{ item.text }}</span>
            </v-breadcrumbs-item>
          </template>
        </v-breadcrumbs>
        <v-divider class="mt-1"></v-divider>
      <v-row class="mt-4">
      <v-col cols="12" md="8">
        <v-row no-gutters>
          <v-col cols="12">
            <v-card>
              <v-col cols="12" class="pl-5 pt-5">
                <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>รถเข็น</b></p>
              </v-col>
              <!-- <v-col cols="12" align="right" class="pt-0 pl-5">
                <v-btn outlined fab icon x-small color="primary"><v-icon>mdi-delete</v-icon></v-btn>
                <span class="pl-2"><b>ลบสินค้าที่เลือก</b></span>
              </v-col> -->
              <v-container grid-list-xs>
                <div v-for="(item,index) in itemsCart" :key="index">
                  <!-- table ของร้านที่ถูกเลือก -->
                  <a-table v-if="item.checkStatus === false" bordered :style="{'margin-top': index === 0 ? '' : '12px'}" :class="IpadProSize ? 'custom-table-ipadpro' : ''"
                  :data-source="item.product_list"
                  :rowKey="(record, indexI) => checkRowKey(record)"
                  :columns="MobileSize ? headersMobile : headers"
                  :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
                  :showHeader="MobileSize ? false : true">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col :cols="MobileSize ? 9 : 6" justify="center">
                          <v-checkbox class="float-left check-all" color="#27AB9C" v-model="checkAll" @click="clickCheckAll(item)"/>
                          <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
                          <b class="ml-3" style="line-height: 30px; cursor: pointer;" @click="gotoShopDetail(item.shop_name, item.shop_id)">{{item.shop_name}}</b>
                        </v-col>
                        <v-col :cols="MobileSize ? 3 : 6" class="pr-0"  justify="center" align="right">
                          <v-btn v-if="MobileSize" class="button-delete-all hide-background-hover" text small :ripple="false" :disabled="selectProduct === false" @click="deleteSelected(item)">
                            <v-icon color="#A1A1A1">mdi-delete-outline</v-icon>
                          </v-btn>
                          <v-btn v-else class="button-delete-all hide-background-hover" text small :ripple="false" :disabled="selectProduct === false" @click="deleteSelected(item)">
                            <v-icon color="#A1A1A1">mdi-delete-outline</v-icon>
                            ลบสินค้าที่เลือก
                          </v-btn>
                        </v-col>
                      </v-row>
                    </template>
                   <template slot="actions" slot-scope="text, record">
                      <v-icon class="button-edit-delete" @click="deleteCartItem(record, 'DELETE')">
                        mdi-delete-outline
                      </v-icon>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <!-- [product detail] Desktop, ipadPro, ipad size -->
                      <v-row v-if="!MobileSize">
                        <v-col cols="12" md="4" class="px-0">
                          <v-img :src="`${record.product_image}`" contain :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''" @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0 captionSku">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                          <span v-if="record.attribute_option_1" class="mb-0 captionSku">{{record.key_1_value}}: {{record.attribute_option_1}}</span>
                          <span v-if="record.attribute_option_2" class="pl-2 mb-0 captionSku">{{record.key_2_value}}: {{record.attribute_option_2}}</span>
                          <p v-if="record.status_data_change === 'yes'" class="msgErr mt-3 mb-0">{{msgErr}}</p>
                          <p v-if="record.stock_ready_to_sell === 'no' && record.status_data_change === 'no'" class="msgErr mt-3 mb-0">{{msgOutOfStockErr}}</p>
                        </v-col>
                      </v-row>
                      <!-- [product detail] mobile size -->
                      <row v-else>
                        <v-row>
                          <v-col cols="4" class="pb-0 px-0">
                            <v-img :src="`${record.product_image}`" contain width="80" height="80" v-if="record.product_image !== ''" @click="goProductDetail(record)" />
                            <v-img src="@/assets/NoImage.png" width="80" height="80" contain v-else @click="goProductDetail(record)" />
                          </v-col>
                          <v-col class="pb-0" cols="8">
                            <p class="mb-3 captionSkuMobile">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                            <span v-if="record.attribute_option_1" class="mb-0 captionSku">{{record.key_1_value}}: {{record.attribute_option_1}}</span>
                            <span v-if="record.attribute_option_2" class="pl-2 mb-0 captionSku">{{record.key_2_value}}: {{record.attribute_option_2}}</span><br>
                            <p v-if="record.status_data_change === 'yes'" class="msgErr mt-3 mb-0">{{msgErr}}</p><br>
                            <p v-if="record.stock_ready_to_sell === 'no' && record.status_data_change === 'no'" class="msgErr mt-3 mb-0">{{msgOutOfStockErr}}</p>
                            <v-row>
                              <v-col cols="8">
                                <v-btn elevation="1" x-small icon outlined @click="record.status_data_change === 'yes' ? record.quantity : record.quantity--, changeQuantity(record, 'UPDATE')" :disabled="checkQuantity || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                                  <v-icon small>mdi-minus</v-icon>
                                </v-btn>
                                <input v-model="record.quantity" @change="changeQuantity(record, 'UPDATE')" class="AddNumberProduct" size="4" maxlength="4" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')" :disabled="record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'"/>
                                <v-btn elevation="1" x-small icon class="plus-icon" @click="record.status_data_change === 'yes' ? record.quantity : record.quantity++, changeQuantity(record, 'UPDATE')" :disabled="record.stock_check_status || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                                  <v-icon small color="white"> mdi-plus</v-icon>
                                </v-btn>
                              </v-col>
                              <v-col cols="4" justify="center" align="end">
                                <v-btn icon @click="deleteCartItem(record, 'DELETE')">
                                  <v-icon class="button-edit-delete">mdi-delete-outline</v-icon>
                                </v-btn>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                        <v-row>
                          <v-col cols="4"></v-col>
                          <v-col cols="8">
                            <p class="mb-0" style="font-weight: bold; font-size: 18px; color: #333333;">฿ {{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</p>
                          </v-col>
                        </v-row>
                      </row>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <v-col cols="12" class="py-0 px-0">
                        <v-btn elevation="1" x-small icon outlined :width="IpadProSize ? '16': '18'" :height="IpadProSize ? '16': '18'" @click="record.status_data_change === 'yes' ? record.quantity : record.quantity--, changeQuantity(record, 'UPDATE')" :disabled="checkQuantity || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                          <v-icon x-small>mdi-minus</v-icon>
                        </v-btn>
                        <input v-model="record.quantity" @change="changeQuantity(record, 'UPDATE')" class="AddNumberProduct" size="4" maxlength="4" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')" :disabled="record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'"/>
                        <v-btn elevation="1" x-small icon class="plus-icon" :width="IpadProSize ? '16': '18'" :height="IpadProSize ? '16': '18'" @click="record.status_data_change === 'yes' ? record.quantity : record.quantity++, changeQuantity(record, 'UPDATE')" :disabled="record.stock_check_status || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                          <v-icon small color="white">mdi-plus</v-icon>
                        </v-btn>
                      </v-col>
                    </template>
                    <template slot="price" slot-scope="text, record">
                      <v-col cols="12">
                        <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </template>
                    <template slot="net_price" slot-scope="text, record">
                      <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </a-table>
                  <!-- table ของร้านที่ไม่ได้เลือก -->
                  <a-table v-else bordered :style="{'margin-top': index === 0 ? '' : '12px'}" :class="IpadProSize ? 'custom-table-ipadpro' : ''"
                  :data-source="item.product_list"
                  :rowKey="(record, indexI) => checkRowKey(record)"
                  :columns="MobileSize ? headersMobile : headers"
                  :showHeader="MobileSize ? false : true">
                    <template slot="title">
                      <v-row class="text-left">
                        <v-col justify="center">
                          <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
                          <b class="ml-3" style="line-height: 30px; cursor: pointer;" @click="gotoShopDetail(item.shop_name, item.shop_id)">{{item.shop_name}}</b>
                        </v-col>
                      </v-row>
                    </template>
                    <template slot="actions" slot-scope="text, record">
                      <v-icon class="button-edit-delete" @click="deleteCartItem(record, 'DELETE')">
                        mdi-delete-outline
                      </v-icon>
                    </template>
                    <template slot="productdetails" slot-scope="text, record">
                      <!-- [product detail] Desktop, ipadPro, ipad size -->
                      <v-row v-if="!MobileSize">
                        <v-col cols="12" md="4" class="px-0">
                          <v-img :src="`${record.product_image}`" contain :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''" @click="goProductDetail(record)" />
                          <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else @click="goProductDetail(record)" />
                        </v-col>
                        <v-col cols="12" md="8">
                          <p class="mb-0 captionSku">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                          <span v-if="record.attribute_option_1" class="mb-0 captionSku">{{record.key_1_value}}: {{record.attribute_option_1}}</span>
                          <span v-if="record.attribute_option_2" class="pl-2 mb-0 captionSku">{{record.key_2_value}}: {{record.attribute_option_2}}</span>
                          <p v-if="record.status_data_change === 'yes'" class="msgErr mt-3 mb-0">{{msgErr}}</p>
                          <p v-if="record.stock_ready_to_sell === 'no' && record.status_data_change === 'no'" class="msgErr mt-3 mb-0">{{msgOutOfStockErr}}</p>
                        </v-col>
                      </v-row>
                      <!-- [product detail] mobile size -->
                      <row v-else>
                        <v-row>
                          <v-col cols="4" class="pb-0 px-0">
                            <v-img :src="`${record.product_image}`" contain width="80" height="80" v-if="record.product_image !== ''" @click="goProductDetail(record)" />
                            <v-img src="@/assets/NoImage.png" width="80" height="80" contain v-else @click="goProductDetail(record)" />
                          </v-col>
                          <v-col class="pb-0" cols="8">
                            <p class="mb-3 captionSkuMobile">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                            <span v-if="record.attribute_option_1" class="mb-0 captionSku">{{record.key_1_value}}: {{record.attribute_option_1}}</span>
                            <span v-if="record.attribute_option_2" class="pl-2 mb-0 captionSku">{{record.key_2_value}}: {{record.attribute_option_2}}</span><br>
                            <p v-if="record.status_data_change === 'yes'" class="msgErr mt-3 mb-0">{{msgErr}}</p><br>
                            <p v-if="record.stock_ready_to_sell === 'no' && record.status_data_change === 'no'" class="msgErr mt-3 mb-0">{{msgOutOfStockErr}}</p>
                            <v-row>
                              <v-col cols="8">
                                <v-btn elevation="1" x-small icon outlined @click="record.status_data_change === 'yes' ? record.quantity : record.quantity--, changeQuantity(record, 'UPDATE')" :disabled="checkQuantity || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                                  <v-icon small>mdi-minus</v-icon>
                                </v-btn>
                                <input v-model="record.quantity" @change="changeQuantity(record, 'UPDATE')" class="AddNumberProduct" size="4" maxlength="4" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')" :disabled="record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'"/>
                                <v-btn elevation="1" x-small icon class="plus-icon" @click="record.status_data_change === 'yes' ? record.quantity : record.quantity++, changeQuantity(record, 'UPDATE')" :disabled="record.stock_check_status || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                                  <v-icon small color="white"> mdi-plus</v-icon>
                                </v-btn>
                              </v-col>
                              <v-col cols="4" justify="center" align="end">
                                <v-btn icon @click="deleteCartItem(record, 'DELETE')">
                                  <v-icon class="button-edit-delete">mdi-delete-outline</v-icon>
                                </v-btn>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                        <v-row>
                          <v-col cols="4"></v-col>
                          <v-col cols="8">
                            <p class="mb-0" style="font-weight: bold; font-size: 18px; color: #333333;">฿ {{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</p>
                          </v-col>
                        </v-row>
                      </row>
                    </template>
                    <template slot="quantity" slot-scope="text, record">
                      <v-col cols="12" class="py-0 px-0">
                        <v-btn elevation="1" x-small icon outlined :width="IpadProSize ? '16': '18'" :height="IpadProSize ? '16': '18'" @click="record.status_data_change === 'yes' ? record.quantity : record.quantity--, changeQuantity(record, 'UPDATE')" :disabled="checkQuantity || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                          <v-icon x-small>mdi-minus</v-icon>
                        </v-btn>
                        <input v-model="record.quantity" @change="changeQuantity(record, 'UPDATE')" class="AddNumberProduct" size="4" maxlength="4" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')" :disabled="record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'"/>
                        <v-btn elevation="1" x-small icon class="plus-icon" :width="IpadProSize ? '16': '18'" :height="IpadProSize ? '16': '18'" @click="record.status_data_change === 'yes' ? record.quantity : record.quantity++, changeQuantity(record, 'UPDATE')" :disabled="record.stock_check_status || record.stock_ready_to_sell === 'no' || record.status_data_change === 'yes'">
                          <v-icon small color="white"> mdi-plus</v-icon>
                        </v-btn>
                      </v-col>
                    </template>
                    <template slot="price" slot-scope="text, record">
                      <v-col cols="12">
                        <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </template>
                    <template slot="net_price" slot-scope="text, record">
                      <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </template>
                  </a-table>
                </div>
              </v-container>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="4">
        <v-card>
          <v-container grid-list-xs>
            <v-row>
              <v-col cols="12">
                <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>สรุปรายการสั่งซื้อสินค้า</b></p>
              </v-col>
              <v-col cols="8" class="py-0">
                <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="4" align="right" class="py-0">
                <span>{{ Number(CartData.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
                <v-divider></v-divider>
              </v-col>
              <!-- <v-col cols="12" align="right" class="py-0">
                <v-expansion-panels flat>
                  <v-expansion-panel>
                    <v-expansion-panel-header class="pa-0 discount-header">ใช้โค้ดส่วนลด</v-expansion-panel-header>
                    <v-expansion-panel-content class="discount-panel">
                      <v-text-field class="discount-text-field" placeholder="ระบุโค้ดส่วนลดของคุณที่นี่" outlined dense></v-text-field>
                    </v-expansion-panel-content>
                  </v-expansion-panel>
                </v-expansion-panels>
              </v-col> -->
              <!-- <v-col cols="8" class="py-0">
                <span>ค่าจัดส่ง</span>
              </v-col> -->
              <!-- <v-col cols="4" class="py-0" align="right">
                <span>{{ Number(CartData.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col> -->
              <!-- <v-col cols="8" class="py-0">
                <span style="font-size: 8px">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไปขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่ จัดส่งจะติดต่อคุณ</span>
              </v-col> -->
              <v-col cols="8">
                <span>ภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="4" align="right">
                <span>{{ Number(CartData.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="8">
                <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="4" align="right">
                <span>{{ Number(CartData.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <!-- <v-col cols="8">
                <span>ค่าส่ง</span>
                <p style="font-size: 10px; color: #8C8C8C;" class="mb-0">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไปขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ</p>
              </v-col>
              <v-col cols="4" align="right">
                <span>{{ Number(CartData.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</span>
              </v-col> -->
              <v-col cols="8">
                <span class="totalPriceFont"><b>ราคารวมทั้งหมด</b></span>
              </v-col>
              <v-col cols="4" align="right">
                <span class="totalPriceFont"><b>{{ Number(CartData.total_net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></span>
              </v-col>
              <v-col cols="12" align="right">
                <v-btn class="white--text" block color="#27AB9C" @click="goCheckout()" :disabled="selectProduct === false">
                  <span style="font-size: 16px; font-style: normal; font-weight: bold;">ยืนยันการสั่งซื้อ</span>
                </v-btn>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
    <v-row class="mt-4">
      <v-col cols="12">
        <RecommendProducts v-if="StatusHomeRecoment" :propsData='recommendProduct' header='สินค้าแนะนำ' :check='status'/>
      </v-col>
    </v-row>
  </div>
</div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
const recommend = []
for (let i = 0; i < 50; i++) {
  recommend.push({
    product_id: i,
    name: `Data Title recommend ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  components: {
    'a-table': Table,
    RecommendProducts: () => import(/* webpackPrefetch: true */ '@/components/Home/HomeProductUI')
  },
  data () {
    return {
      recommendProduct: [],
      StatusHomeRecoment: false,
      tokenstatus: '',
      status: true,
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'รถเข็น',
          disabled: true,
          href: '/shoppingcart'
        }
      ],
      itemsCart: [],
      selectedRowKeys: [],
      shopNameList: {
        data: []
      },
      shopIdList: {
        data: []
      },
      CartData: [],
      selectProduct: false,
      disabledinput_plus: false,
      totalPriceNoVat: 0,
      falseAlert: true,
      checkQuantity: false,
      productToCalList: [],
      msgErr: '*สินค้ามีการเปลี่ยนแปลง กรุณานำออกจากรถเข็น',
      msgOutOfStockErr: '*ไม่มีสินค้าในสต็อก',
      checkAll: false,
      LengthSelectAll: 0
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          slots: { title: 'customTitleProductdetails' },
          scopedSlots: { customRender: 'productdetails' },
          width: '32%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '17%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          align: 'center',
          key: 'net_price',
          width: '20%'
        },
        {
          title: 'จัดการ',
          align: 'center',
          key: 'actions',
          scopedSlots: { customRender: 'actions' },
          width: '11%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          slots: { title: 'customTitleProductdetails' },
          scopedSlots: { customRender: 'productdetails' },
          width: '100%'
        }
      ]
      return headers
    }
  },
  async created () {
    window.scrollTo(0, 0)
    // this.$EventBus.$on('getCartTable', this.Start)
    this.getCart()
    this.getRecommendedProduct()
  },
  methods: {
    gotoShopDetail (name, id) {
      const shopCleaned = name.replace(/\s/g, '-')
      this.$router.push(`/shoppage/${shopCleaned}-${id}`)
    },
    // เช็คว่าเลือกทั้งหมดรึยัง กรณีที่กดปุ่มเลือกทั้งหมด
    clickCheckAll (item) {
      var checkRowKeyList = []
      var checkRowKey = ''
      this.LengthSelectAll = item.product_list.length
      if (this.checkAll) {
        item.product_list.forEach(element => {
          checkRowKey = this.checkRowKey(element)
          if (this.selectedRowKeys.length !== 0) {
            this.selectedRowKeys.forEach(select => {
              if (select !== element) {
                checkRowKeyList.push(checkRowKey)
              }
            })
          } else {
            checkRowKeyList.push(checkRowKey)
          }
        })
        // console.log('checkRowKeyList', checkRowKeyList)
        this.onSelectChange(checkRowKeyList)
      } else {
        checkRowKeyList = []
        this.onSelectChange(checkRowKeyList)
      }
    },
    // เช็คว่าเลือกทั้งหมดรึยัง กรณีที่ กดเลือสินค้าทีละตัวจนครบแต่ไม่ได้กดปุ่มเลือกทั้งหมด
    checkSelectedAllItem (listData, selectedRowKeys) {
      // console.log('selectedRowKeys=', selectedRowKeys.length)
      // console.log('listData=', listData.product_list.length)
      if (listData.product_list.length !== selectedRowKeys.length) {
        this.checkAll = false
      } else {
        this.checkAll = true
      }
    },
    checkRowKey (record) {
      // set key กรณีที่ สินค้ามี หนึ่ง attribute
      if (record.key_2_value === '') {
        if (record.status_data_change === 'yes') {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null) {
              return record.attribute_option_1
            }
          } else {
            return record.sku
          }
        } else {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null) {
              return record.attribute_option_1
            }
          } else {
            return record.sku
          }
        }
      } else {
        if (record.status_data_change === 'yes') {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null && record.key_2_value !== null) {
              var newKey = record.attribute_option_1.concat(record.attribute_option_2.toString())
              return newKey
            }
          } else {
            return record.sku
          }
        } else {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null && record.key_2_value !== null) {
              var newKey2 = record.attribute_option_1.concat(record.attribute_option_2.toString())
              return newKey2
            }
          } else {
            return record.sku
          }
        }
      }
    },
    async onSelectChange (selectedRowKeys) {
      if (selectedRowKeys.length === 0) {
        this.checkAll = false
      } else {
        if (this.LengthSelectAll !== selectedRowKeys.length) {
          this.checkAll = false
        } else {
          this.checkAll = true
        }
      }
      // set sku select
      this.selectedRowKeys = selectedRowKeys
      // เช็ค status_data_change ของสินค้าถ้าเป็น yes จะไม่ให้ทำการ checked
      this.itemsCart.forEach(element => {
        element.product_list.forEach(check1 => {
          // console.log('ccheck stock', check1.stock_ready_to_sell)
          if (selectedRowKeys.length !== 0) {
            selectedRowKeys.forEach((final, index) => {
              // กรณีที่มีการเปลี่ยนแปลงข้อมูล แต่ยังมีสินค้าพร้อมขาย => ให้เข้า case สินค้ามีการเปลี่ยนแปลง
              if (check1.status_data_change === 'yes' && check1.stock_ready_to_sell === 'yes') {
                this.checkReadyToSell(check1, final, index, 'ไม่สามารถดำเนินการได้', '  นี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้าใหม่')
              // กรณีที่มีการเปลี่ยนแปลงข้อมูล และ สินค้าหมด => ให้เข้า case สินค้ามีการเปลี่ยนแปลง
              } else if (check1.status_data_change === 'yes' && check1.stock_ready_to_sell === 'no') {
                this.checkReadyToSell(check1, final, index, 'ไม่สามารถดำเนินการได้', '  นี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้าใหม่')
              // กรณีที่มีไม่มีการเปลี่ยนแปลงข้อมูล และ สินค้าหมด => ให้เข้า case ไม่มีสินค้าในสต็อก
              } else if (check1.status_data_change === 'no' && check1.stock_ready_to_sell === 'no') {
                this.checkReadyToSell(check1, final, index, 'ไม่มีสินค้าในสต็อก', '  ไม่มีสินค้าในสต็อก กรุณานำออกจากรถเข็น')
              }
            })
          }
        })
      })
      // console.log('selectedRowKeys---', this.selectedRowKeys)
      await this.checkProductToCal(this.selectedRowKeys)
      await localStorage.setItem('_selectProduct', Encode.encode(this.selectedRowKeys))
      if (selectedRowKeys.length === 0) {
        this.selectProduct = false
      } else {
        this.selectProduct = true
      }
      // set shop select
      this.itemsCart.forEach(element => {
        element.product_list.forEach(check1 => {
          selectedRowKeys.forEach(final => {
            if (check1.status_data_change === 'yes') {
              if (check1.have_attribute === 'yes') {
                // if (check1.attribute_option_1 === final) {
                //   element.selectData.push({ shopName: element.shop_name, shopId: element.shop_id })
                // }
                if (check1.key_2_value !== '') {
                  if (check1.attribute_option_1.concat(check1.attribute_option_2.toString()) === final) {
                    this.checkSelectedAllItem(element, selectedRowKeys)
                    element.selectData.push({ item: check1, shopName: element.shop_name, shopId: element.shop_id })
                  }
                } else {
                  if (check1.attribute_option_1 === final) {
                    this.checkSelectedAllItem(element, selectedRowKeys)
                    element.selectData.push({ item: check1, shopName: element.shop_name, shopId: element.shop_id })
                  }
                }
              } else {
                if (check1.sku === final) {
                  this.checkSelectedAllItem(element, selectedRowKeys)
                  element.selectData.push({ item: check1, shopName: element.shop_name, shopId: element.shop_id })
                }
              }
            } else {
              if (check1.have_attribute === 'yes') {
                if (check1.key_2_value !== '') {
                  if (check1.attribute_option_1.concat(check1.attribute_option_2.toString()) === final) {
                    this.checkSelectedAllItem(element, selectedRowKeys)
                    element.selectData.push({ item: check1, shopName: element.shop_name, shopId: element.shop_id })
                  }
                } else {
                  if (check1.attribute_option_1 === final) {
                    this.checkSelectedAllItem(element, selectedRowKeys)
                    element.selectData.push({ item: check1, shopName: element.shop_name, shopId: element.shop_id })
                  }
                }
              } else {
                if (check1.sku === final) {
                  this.checkSelectedAllItem(element, selectedRowKeys)
                  element.selectData.push({ shopName: element.shop_name, shopId: element.shop_id })
                }
              }
            }
          })
        })
      })
      const setShopNameList = []
      const setShopIdList = []
      for (let index = 0; index < this.itemsCart.length; index++) {
        const element = this.itemsCart[index]
        if (element.selectData.length !== 0) {
          for (let index = 0; index < element.selectData.length; index++) {
            const element2 = element.selectData[index]
            setShopNameList.push(element2.shopName)
            setShopIdList.push(element2.shopId)
          }
        }
      }
      const dataShopNameList = [...new Set(setShopNameList)]
      const dataShopIdList = [...new Set(setShopIdList)]
      this.shopNameList.data = [...dataShopNameList]
      this.shopIdList.data = [...dataShopIdList]
      if (this.selectedRowKeys.length === 0) {
        this.shopNameList.data = []
      }
      // console.log('shop name select', this.shopNameList)
      localStorage.setItem('_sellerShop', this.shopNameList.data[0])
      this.getCart()
    },
    checkReadyToSell (check1, final, indexData, alertTitle, alertText) {
      if (check1.have_attribute === 'yes') {
        if (check1.key_2_value !== '') {
          if (check1.attribute_option_1.concat(check1.attribute_option_2.toString()) === final) {
            this.$swal.fire({ title: alertTitle, text: 'สินค้ารหัส ' + check1.sku.toString() + alertText, icon: 'warning', timerProgressBar: true, showConfirmButton: false })
            this.selectedRowKeys.splice(indexData, 1)
          }
        } else {
          if (check1.attribute_option_1 === final) {
            this.$swal.fire({ title: alertTitle, text: 'สินค้ารหัส ' + check1.sku.toString() + alertText, icon: 'warning', timerProgressBar: true, showConfirmButton: false })
            this.selectedRowKeys.splice(indexData, 1)
          }
        }
      } else {
        if (check1.sku === final) {
          this.$swal.fire({ title: alertTitle, text: 'สินค้ารหัส ' + check1.sku.toString() + alertText, icon: 'warning', timerProgressBar: true, showConfirmButton: false })
          this.selectedRowKeys.splice(indexData, 1)
        }
      }
    },
    async getCart () {
      this.$store.commit('openLoader')
      this.productList = []
      this.checkQuantity = true
      if (localStorage.getItem('_cartData') !== null) {
        var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
        var productToCal = this.checkProductToCalData()
        var shopName = []
        this.shopNameList.data.length !== 0 ? shopName.push(this.shopNameList.data[0]) : shopName = []
        const data = {
          shop_to_cal: shopName,
          product_to_calculate: productToCal,
          shop_list: this.checkDataShopList(cartData.shop_list)
        }
        await this.$store.dispatch('ActionLocalstorageDetailCart', data)
        var res = await this.$store.state.ModuleCart.stateLocalstorageDetailCart
        this.itemsCart = []
        if (res.message === 'Get localstorage detail cart success') {
          this.$store.commit('closeLoader')
          this.checkQuantity = false
          if (res.data.shop_list.length !== 0) {
            this.itemsCart = res.data.shop_list
            res.data.shop_list.forEach((list, index) => {
              if (this.shopNameList.data.length !== 0) {
                this.falseAlert = false
                if (list.shop_name !== this.shopNameList.data[0]) {
                  list.checkStatus = true
                } else {
                  list.checkStatus = false
                }
              } else {
                this.falseAlert = true
                list.checkStatus = false
              }
            })
            this.CartData = res.data
            this.totalPriceNoVat = res.data.total_price_no_vat
          }
          var setData = {
            product_to_cal: res.data.product_to_calculate,
            shop_to_cal: res.data.shop_to_cal,
            address_data: {},
            shop_list: res.data.shop_list
          }
          await localStorage.setItem('_cartData', Encode.encode(setData))
        } else {
          this.$store.commit('closeLoader')
        }
      }
    },
    goHomepage () {
      this.$router.push('/')
    },
    goProductDetail (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.product_id}` } })
      window.location.assign(routeData.href, '_blank')
    },
    deleteCartItem (item, strkey) {
      this.deleteItem = item
      if (item) {
        if (this.MobileSize) {
          this.$swal.fire({
            icon: 'warning',
            html: '<h3>คุณต้องการลบสินค้าหรือไม่</h3>',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          }).then((result) => {
            if (result.isConfirmed) {
              if (strkey === 'UPDATE') {
                item.quantity = 1
                this.updateCartItem(item, 'UPDATE')
              } else {
                item.quantity = 0
                this.updateCartItem(item, 'DELETE')
              }
            } else if (result.dismiss === this.$swal.DismissReason.cancel) {
              if (parseInt(item.quantity) === 0) {
                item.quantity = 1
                this.updateCartItem(item, 'UPDATE')
              }
            }
          }).catch(() => {
          })
        } else {
          this.$swal.fire({
            icon: 'warning',
            title: 'คุณต้องการลบสินค้าหรือไม่',
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          }).then((result) => {
            if (result.isConfirmed) {
              if (strkey === 'UPDATE') {
                item.quantity = 1
                this.updateCartItem(item, 'UPDATE')
              } else {
                item.quantity = 0
                this.updateCartItem(item, 'DELETE')
              }
            } else if (result.dismiss === this.$swal.DismissReason.cancel) {
              if (parseInt(item.quantity) === 0) {
                item.quantity = 1
                this.updateCartItem(item, 'UPDATE')
              }
            }
          }).catch(() => {
          })
        }
      }
    },
    updateContent () {
      this.getCart()
      // this.$EventBus.$emit('getCartPopOver')
    },
    changeQuantity (item, strkey) {
      if (parseInt(item.quantity) === 0 || (parseInt(item.quantity) >= parseInt(item.min_per_order) && parseInt(item.quantity) <= parseInt(item.max_per_order))) {
        if (item.status_data_change === 'yes') {
          this.$swal.fire({ title: 'ไม่สามารถดำเนินการได้', text: 'สินค้ารหัส ' + item.sku.toString() + ' นี้มีการเปลี่ยนแปลง กรุณานำออกจากรถเข็นและทำการเพิ่มสินค้าใหม่', icon: 'warning', timerProgressBar: true, showConfirmButton: false })
        } else {
          if (strkey === 'UPDATE') {
            if (parseInt(item.quantity) === 0) {
              this.deleteCartItem(item, 'DELETE')
            } else if (parseInt(item.quantity) < 0 || item.quantity === '') {
              item.quantity = 1
              this.updateCartItem(item, 'UPDATE')
            } else {
              this.updateCartItem(item, 'UPDATE')
            }
          }
        }
      } else {
        const msg = 'สินค้าชิ้นนี้จำกัดการสั่งซื้อต่ำสุดที่ ' + item.min_per_order + ' และการสั่งซื้อสูงสุดที่ ' + item.max_per_order
        this.$swal.fire({ showConfirmButton: false, timer: 3500, timerProgressBar: true, icon: 'warning', html: msg })
        this.updateContent()
      }
    },
    updateCartItem (product, strkey) {
      var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
      // var finish = false
      cartData.shop_list.forEach((shop, shopIndex) => {
        shop.product_list.forEach((item, productIndex) => {
          // update กรณีที่ สินค้ามี  attribute
          if (product.have_attribute === 'yes') {
            // กรณีที่สินค้ามี 2 attribute
            if (item.attribute_option_2 !== '') {
              if (item.attribute_option_1 === product.attribute_option_1 && item.attribute_option_2 === product.attribute_option_2) {
                this.checkQuantity = false
                if (parseInt(product.quantity) === 0) {
                  cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                  cartData.shop_list.forEach((element, index) => {
                    if (element.product_list.length === 0) {
                      cartData.shop_list.splice(index, 1)
                    }
                  })
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'ลบสินค้าในรถเข็นเรียบร้อย', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  this.updateStatusSelectRowKey((item.attribute_option_1.concat(item.attribute_option_1.toString())), 'have attribute', item)
                  // this.updateContent()
                } else if (parseInt(product.quantity) > parseInt(product.stock)) {
                  this.disabledinput_plus = true
                  product.quantity = product.stock
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>มีสินค้าในสต็อกไม่เพียงพอ</h3>', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'มีสินค้าในสต็อกไม่เพียงพอ', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  // this.updateContent()
                } else {
                  this.disabledinput_plus = false
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  // this.updateContent()
                }
              }
            } else {
              // กรณีที่สินค้ามี 1 attribute
              if (item.attribute_option_1 === product.attribute_option_1) {
                this.checkQuantity = false
                if (parseInt(product.quantity) === 0) {
                  cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                  cartData.shop_list.forEach((element, index) => {
                    if (element.product_list.length === 0) {
                      cartData.shop_list.splice(index, 1)
                    }
                  })
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'ลบสินค้าในรถเข็นเรียบร้อย', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  this.updateStatusSelectRowKey(item.attribute_option_1, 'have attribute', item)
                  // this.updateContent()
                } else if (parseInt(product.quantity) > parseInt(product.stock)) {
                  this.disabledinput_plus = true
                  product.quantity = product.stock
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>มีสินค้าในสต็อกไม่เพียงพอ</h3>', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'มีสินค้าในสต็อกไม่เพียงพอ', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  // this.updateContent()
                } else {
                  this.disabledinput_plus = false
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  // this.updateContent()
                }
              }
            }
          } else {
            // update กรณีที่ สินค้าไม่มี  attribute
            if (product.attribute_option_2 !== '') {
              if (item.sku === product.sku && product.attribute_option_1 === item.attribute_option_1product.attribute_option_2 === item.attribute_option_2) {
                this.checkQuantity = false
                if (parseInt(product.quantity) === 0) {
                  // console.log('productIndex', productIndex)
                  cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                  cartData.shop_list.forEach((element, index) => {
                    if (element.product_list.length === 0) {
                      cartData.shop_list.splice(index, 1)
                    }
                  })
                  // console.log('AAAAAA c', cartData)
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'ลบสินค้าในรถเข็นเรียบร้อย', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  this.updateStatusSelectRowKey(item.sku, 'no attribute', item)
                  // this.updateContent()
                } else if (parseInt(product.quantity) > parseInt(product.stock)) {
                  this.disabledinput_plus = true
                  product.quantity = product.stock
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>มีสินค้าในสต็อกไม่เพียงพอ</h3>', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'มีสินค้าในสต็อกไม่เพียงพอ', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  // this.updateContent()
                } else {
                  this.disabledinput_plus = false
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  // this.updateContent()
                }
              }
            } else {
              if (item.sku === product.sku && product.attribute_option_1 === item.attribute_option_1) {
                this.checkQuantity = false
                if (parseInt(product.quantity) === 0) {
                  // console.log('productIndex', productIndex)
                  cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                  cartData.shop_list.forEach((element, index) => {
                    if (element.product_list.length === 0) {
                      cartData.shop_list.splice(index, 1)
                    }
                  })
                  // console.log('AAAAAA c', cartData)
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'ลบสินค้าในรถเข็นเรียบร้อย', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  this.updateStatusSelectRowKey(item.sku, 'no attribute', item)
                  // this.updateContent()
                } else if (parseInt(product.quantity) > parseInt(product.stock)) {
                  this.disabledinput_plus = true
                  product.quantity = product.stock
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  if (this.MobileSize) {
                    this.$swal.fire({ html: '<h3>มีสินค้าในสต็อกไม่เพียงพอ</h3>', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  } else {
                    this.$swal.fire({ title: 'มีสินค้าในสต็อกไม่เพียงพอ', icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
                  }
                  // this.updateContent()
                } else {
                  this.disabledinput_plus = false
                  cartData.shop_list[shopIndex].product_list[productIndex].net_price = cartData.shop_list[shopIndex].product_list[productIndex].price * product.quantity
                  cartData.shop_list[shopIndex].product_list[productIndex].quantity = product.quantity
                  localStorage.setItem('_cartData', Encode.encode(cartData))
                  // this.updateContent()
                }
              }
            }
          }
        })
      })
      this.updateContent()
    },
    goCheckout () {
      this.$router.push({ path: '/checkout' }).catch(() => {})
    },
    checkProductToCal (selectedRowKeys) {
      const data = []
      this.itemsCart.forEach(element => {
        element.product_list.forEach(check1 => {
          selectedRowKeys.forEach(final => {
            if (check1.status_data_change === 'no') {
              if (check1.have_attribute === 'yes') {
                if (check1.key_2_value !== '') {
                  if (check1.attribute_option_1.concat(check1.attribute_option_2.toString()) === final) {
                    data.push({ have_attribute: check1.have_attribute, product_attribute_id: check1.product_attribute_id, product_id: check1.product_id, attribute_option_1: check1.attribute_option_1 ? check1.attribute_option_1 : '', attribute_option_2: check1.attribute_option_2 ? check1.attribute_option_2 : '' })
                  }
                } else {
                  if (check1.attribute_option_1 === final) {
                    data.push({ have_attribute: check1.have_attribute, product_attribute_id: check1.product_attribute_id, product_id: check1.product_id, attribute_option_1: check1.attribute_option_1 ? check1.attribute_option_1 : '', attribute_option_2: check1.attribute_option_2 ? check1.attribute_option_2 : '' })
                  }
                }
              } else {
                if (check1.sku === final) {
                  data.push({ have_attribute: check1.have_attribute, product_attribute_id: check1.product_attribute_id, product_id: check1.product_id, attribute_option_1: check1.attribute_option_1 ? check1.attribute_option_1 : '', attribute_option_2: check1.attribute_option_2 ? check1.attribute_option_2 : '' })
                }
              }
            }
          })
        })
      })
      this.productToCalList = data
      localStorage.setItem('_productToCal', Encode.encode(this.productToCalList))
    },
    checkDataShopList (data) {
      const shoplist = []
      data.forEach(element => {
        shoplist.push({ shop_id: element.shop_id, selectData: [], shop_name: element.shop_name, product_list: this.checkProductList(element.product_list) })
      })
      this.checkProductToCal(this.selectedRowKeys)
      return shoplist
    },
    checkProductList (data) {
      const productlist = []
      data.forEach(element => {
        productlist.push({
          key_1_value: element.key_1_value,
          key_2_value: element.key_2_value,
          have_attribute: element.have_attribute,
          product_id: element.product_id,
          attribute_option_1: element.attribute_option_1,
          attribute_option_2: element.attribute_option_2,
          product_attribute_id: element.product_attribute_id,
          product_name: element.product_name,
          product_image: element.product_image,
          sku: element.sku,
          stock: element.stock,
          quantity: element.quantity,
          price: element.price,
          net_price: element.net_price,
          max_per_order: element.max_per_order,
          min_per_order: element.min_per_order
        })
      })
      return productlist
    },
    checkProductSelectData () {
      const checkSelectProduct = localStorage.getItem('_selectProduct')
      var selectProduct = []
      if (checkSelectProduct !== null) {
        selectProduct = JSON.parse(Decode.decode(localStorage.getItem('_selectProduct')))
      }
      return selectProduct
    },
    checkProductToCalData () {
      const checkProductTocal = localStorage.getItem('_productToCal')
      var productTocal = []
      if (checkProductTocal !== null) {
        productTocal = JSON.parse(Decode.decode(localStorage.getItem('_productToCal')))
      }
      return productTocal
    },
    checkSellerShopData () {
      const checkSellerShop = localStorage.getItem('_sellerShop')
      var sellerShop = []
      if (checkSellerShop !== null) {
        sellerShop = JSON.parse(Decode.decode(localStorage.getItem('_sellerShop')))
      }
      return sellerShop
    },
    async deleteSelected (item) {
      if (this.MobileSize) {
        this.$swal.fire({
          icon: 'warning',
          html: '<h3>คุณต้องการลบสินค้าหรือไม่</h3>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.selectedDelete(item)
          } else if (result.dismiss === this.$swal.DismissReason.cancel) {
          }
        }).catch(() => {
        })
      } else {
        this.$swal.fire({
          icon: 'warning',
          title: 'คุณต้องการลบสินค้าหรือไม่',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.selectedDelete(item)
          } else if (result.dismiss === this.$swal.DismissReason.cancel) {
          }
        }).catch(() => {
        })
      }
    },
    async selectedDelete (item) {
      var selectProduct = this.checkProductSelectData()
      var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
      selectProduct.forEach(element => {
        cartData.shop_list.forEach((shoplist, shopIndex) => {
          shoplist.product_list.forEach((product, productIndex) => {
            if (product.have_attribute === 'yes') {
              if (product.key_2_value !== '') {
                // have 2 attribute
                this.checkQuantity = false
                if (element === product.attribute_option_1.concat(product.attribute_option_2.toString())) {
                  cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                  cartData.product_to_cal.forEach((productToCal, productToCalIndex) => {
                    if (productToCal.product_attribute_id === product.product_attribute_id) {
                      cartData.product_to_cal.splice(productToCalIndex, 1)
                    }
                  })
                  cartData.shop_list.forEach((element, index) => {
                    if (element.product_list.length === 0) {
                      cartData.shop_list.splice(index, 1)
                    }
                  })
                }
              } else {
                // have 1 attribute
                this.checkQuantity = false
                if (element === product.attribute_option_1) {
                  cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                  cartData.product_to_cal.forEach((productToCal, productToCalIndex) => {
                    if (productToCal.product_attribute_id === product.product_attribute_id) {
                      cartData.product_to_cal.splice(productToCalIndex, 1)
                    }
                  })
                  cartData.shop_list.forEach((element, index) => {
                    if (element.product_list.length === 0) {
                      cartData.shop_list.splice(index, 1)
                    }
                  })
                }
              }
            } else {
              if (element === product.sku) {
                this.checkQuantity = false
                cartData.shop_list[shopIndex].product_list.splice(productIndex, 1)
                cartData.product_to_cal.forEach((productToCal, productToCalIndex) => {
                  if (productToCal.product_id === product.product_id) {
                    cartData.product_to_cal.splice(productToCalIndex, 1)
                  }
                })
                cartData.shop_list.forEach((element, index) => {
                  if (element.product_list.length === 0) {
                    cartData.shop_list.splice(index, 1)
                  }
                })
              }
            }
          })
        })
      })
      localStorage.setItem('_cartData', Encode.encode(cartData))
      if (this.MobileSize) {
        this.$swal.fire({ html: '<h3>ลบสินค้าในรถเข็นเรียบร้อย</h3>', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
      } else {
        this.$swal.fire({ title: 'ลบสินค้าในรถเข็นเรียบร้อย', icon: 'success', timer: 1500, timerProgressBar: true, showConfirmButton: false })
      }
      var checkProductToCal = this.checkProductToCalData()
      this.checkAll = false
      this.selectedRowKeys = []
      this.shopNameList.data = []
      localStorage.setItem('_productToCal', Encode.encode(checkProductToCal))
      localStorage.setItem('_selectProduct', Encode.encode(this.selectedRowKeys))
      this.updateContent()
    },
    updateStatusSelectRowKey (key, attribute, item) {
      this.selectedRowKeys.forEach((element, index) => {
        if (element === key) {
          this.selectedRowKeys.splice(index, 1)
        }
      })
      var checkProductToCal = this.checkProductToCalData()
      checkProductToCal.forEach((elementToCal, indexToCal) => {
        if (attribute === 'no attribute') {
          if (parseInt(elementToCal.product_id === key)) {
            checkProductToCal.splice(indexToCal, 1)
          }
        } else {
          if (item.key_2_value !== '') {
            if (elementToCal.attribute_option_1.concat(elementToCal.attribute_option_1.toString()) === key) {
              checkProductToCal.splice(indexToCal, 1)
            }
          } else {
            if (elementToCal.attribute_option_1 === key) {
              checkProductToCal.splice(indexToCal, 1)
            }
          }
        }
      })
      if (checkProductToCal.length === 0) {
        this.checkAll = false
        this.selectedRowKeys = []
        this.shopNameList.data = []
      }
      localStorage.setItem('_productToCal', Encode.encode(checkProductToCal))
      localStorage.setItem('_selectProduct', Encode.encode(this.selectedRowKeys))
    },
    async getRecommendedProduct () {
      var oneData
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      }
      if (localStorage.getItem('roleUser') !== null) {
        this.tokenstatus = oneData.user.access_token
      } else {
        this.tokenstatus = ''
      }
      var data = {
        token: this.tokenstatus,
        role_user: 'ext_buyer',
        begin_search_type: 'component',
        begin_search_details: {
          custom_user_ID: '1',
          what_component: 'recommended_product',
          component_id: ''
        }
      }
      await this.$store.dispatch('actionsProduct', data)
      var response = await this.$store.state.ModuleProductNode.stateProducts
      this.recommendProduct = []
      if (response.ok === 'y') {
        if (response.query_result.length !== 0) {
          this.recommendProduct = response.query_result
          this.status = true
          this.StatusHomeRecoment = true
        } else {
          this.status = true
          this.StatusHomeRecoment = false
          this.recommendProduct = []
        }
      }
    }
  }
}
</script>

<style lang="css" scoped>
input {
  width: 100%;
  margin: 8px 0;
  box-sizing: border-box;
  border: 1px solid rgb(139, 136, 136);
  background-color: lightblue;
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 4px 0px 14px 0px !important;
}
.AddNumberProduct {
  height: 25px;
  width: 40px;
  border: 0px;
  outline: none !important;
  background-color: #ffffff;
  text-align: center;
}
.v-btn--fab.v-size--x-small {
  height: 20px;
  width: 20px;
}
.msgErr {
  font-size: 10px;
  color: red;
}
.captionSku {
  font-size: 13px;
  font-style: normal;
  /* font-weight: 500; */
}
.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  /* font-weight: 500; */
}
.button-edit-delete {
  background: #FFFFFF;
  color: #A1A1A1;
  border: 1px solid #F2F2F2;
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
}
.button-edit-delete:hover {
  background: #E6F5F3;
  color: #27AB9C;
  border: 1px solid #27AB9C;
}
.totalPriceFont {
  font-size: 18px;
}
.plus-icon {
  background-color: #A1A1A1;
}
.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}
.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}
.button-delete-all {
  justify-content: flex-end;
  color: #333333 !important;
  font-size: 14px !important;
}
</style>
<style scoped>
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 4px 0px 4px 0px !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C;
}
.theme--light.v-btn.v-btn--disabled.v-btn--has-bg {
  color: #FFFFFF !important;
  background-color: #CCCCCC !important;
}
.card-product-mobile {
  background-color: transparent;
  box-shadow: none !important;
}
.v-card__subtitle, .v-card__text {
  line-height: unset;
}
::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}
::v-deep .ant-table-thead > tr > th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-tbody > tr > td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-thead > tr > th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-body > table {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table-bordered .ant-table-tbody > tr > td {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #e8e8e8;
  margin-bottom: 6px;
  border-radius: 8px;
}
p.text-left {
  margin: 0px;
}
::v-deep .ant-table-pagination {
  display: none;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
.v-input--selection-controls {
  margin: 0px;
    /* padding-top: 4px; */
}
.v-application--is-ltr .v-messages {
    min-height: 0px;
}
/* .v-expansion-panel-content__wrap {
  padding: 0px !important;
}
.theme--light.v-expansion-panels .v-expansion-panel-header .v-expansion-panel-header__icon .v-icon {
  color: #00B500 !important;
} */
</style>

<style>
.check-all .v-input__control {
  height: 0px;
}
.check-all .v-input--selection-controls__input .v-icon {
  color: #D9D9D9;
}
.discount-header .mdi-chevron-down::before {
  color: #27AB9C;
}
.discount-text-field {
  font-size: 12px;
}
.discount-panel .v-expansion-panel-content__wrap {
  padding: 0;
}
.custom-table-ipadpro .ant-table-thead > tr > th {
  padding: 16px 10px !important;
}
.custom-table-ipadpro .ant-table-tbody > tr > td {
  padding: 16px 10px !important;
}
</style>
