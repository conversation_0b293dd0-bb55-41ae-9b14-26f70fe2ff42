<template>
    <QuotationSetting/>
</template>
<script>
export default {
  components: {
    QuotationSetting: () => import('@/components/Shop/QuotationSettingSeller/QuotationSetting')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/QuotationSettingSellerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/QuotationSettingSeller' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>
