<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-dialog v-model="ModalDetailPartner" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>คำขอเข้าร่วมโปรแกรม Affiliate</b></span>
              </v-col>
              <v-btn fab small @click="ModalDetailPartner = !ModalDetailPartner" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12" class="mr-0 d-flex" v-if="!MobileSize">
                    <v-row dense class="mr-auto">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                    </v-row>
                    <span class="ml-auto" style="text-align: end;">
                      <v-row dense justify="end">
                        <span class="mt-5 pr-2" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">สถานะ :</span>
                        <v-chip class="ma-2 mt-5" :color="colorChipStatus(DataApprove.status)" small :text-color="textChipStatus(DataApprove.status)">{{ textStatusOrder(DataApprove.status) }}</v-chip>
                      </v-row>
                    </span>
                  </v-col>
                  <v-col cols="12" v-else>
                    <v-row dense class="mr-auto">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/Partner.png" max-height="62" max-width="62"></v-img>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 18px; line-height: 26px; color: #333333;" v-if="!MobileSize">รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                      <p class="mt-5 ml-4" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;" v-else>รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</p>
                    </v-row>
                    <v-row dense justify="start">
                      <span class="mt-5 pr-2" style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">สถานะ :</span>
                      <v-chip class="ma-2 mt-5" :color="colorChipStatus(DataApprove.status)" small :text-color="textChipStatus(DataApprove.status)">{{ textStatusOrder(DataApprove.status) }}</v-chip>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="py-5">
                    <v-divider></v-divider>
                  </v-col>
                  <v-col cols="12" v-if="DataApprove.status === 'Reject' || DataApprove.status === 'Cancel'">
                    <span style="font-size: 16px; line-height: 22px; color: #333333;" v-if="DataApprove.status === 'Cancel'"><b>เหตุผลการยกเลิกการเข้าร่วม :</b> {{ DataApprove.remark }}</span>
                    <span style="font-size: 16px; line-height: 22px; color: #333333;" v-if="DataApprove.status === 'Reject'"><b>เหตุผลการไม่อนุมัติคำขอการเข้าร่วม :</b> {{ DataApprove.remark }}</span>
                  </v-col>
                  <v-col cols="12" class="py-5" v-if="DataApprove.status === 'Reject' || DataApprove.status === 'Cancel'">
                    <v-divider></v-divider>
                  </v-col>
                  <v-col cols="12" class="pb-4">
                    <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดการเข้าร่วมโปรแกรม</span>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col align="center" cols="12" md="6" sm="12">
                        <v-img v-if="DataApprove.img_path === '' || DataApprove.img_path === null || DataApprove.img_path === undefined" src="@/assets/Businessman.png" width="150px" height="150px"></v-img>
                        <v-img v-else :src="DataApprove.img_path" width="150px" height="150px"></v-img>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัส Affiliate : <b>{{DataApprove.an_id}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อ-นามสกุล : <b>{{DataApprove.first_name_th}} {{DataApprove.last_name_th}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{DataApprove.phone}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อีเมล : <b>{{DataApprove.email}}</b></p>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="pb-4 pt-6">
                    <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบัญชีโซเชียลมีเดีย</span>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="12" md="12" sm="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Facebook : <b>{{DataApprove.facebook_link !== '' ? DataApprove.facebook_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.facebook_link !== ''" @click="copyLink('facebook')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">TikTok : <b>{{DataApprove.tiktok_link !== '' ? DataApprove.tiktok_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.tiktok_link !== ''" @click="copyLink('tiktok')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Youtube : <b>{{DataApprove.youtube_link !== '' ? DataApprove.youtube_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.youtube_link !== ''" @click="copyLink('youtube')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Instagram : <b>{{DataApprove.instagram_link !== '' ? DataApprove.instagram_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.instagram_link !== ''" @click="copyLink('instagram')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Line : <b>{{DataApprove.line_link !== '' ? DataApprove.line_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.line_link !== ''" @click="copyLink('line')" color="#27AB9C">mdi-link</v-icon></p>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <v-card-actions v-if="DataApprove.status === 'Approve'">
            <v-row dense class="justify-end" style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 10px;' : 'padding: 0px 44px;'">
              <v-btn rounded width="157" height="40" dense outlined color="#27AB9C" class="my-auto" @click="NotApproveWhenAccept(DataApprove)">ยกเลิกการเข้าร่วม</v-btn>
              <!-- <v-spacer></v-spacer> -->
              <!-- <v-btn rounded dense color="#27AB9C" :width="MobileSize ? '120' : '154'" height="40" class="my-auto white--text" @click="EditParterAfter(DataApprove)">
                <v-icon class="pr-2" small>mdi-pencil-outline</v-icon>
                แก้ไข
              </v-btn> -->
            </v-row>
          </v-card-actions>
          <v-card-actions v-else-if="DataApprove.status === 'Waiting'">
            <v-row dense class="justify-end" style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 4px;' : 'padding: 0px 44px;'">
              <v-btn rounded :width="MobileSize ? '38vw' : '157'" height="40" dense outlined color="#27AB9C" class="my-auto px-2" @click="NotApprove()"><span style="font-size: 14px;">ปฏิเสธการเข้าร่วม</span></v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded dense color="#27AB9C" :width="MobileSize ? '38vw' : '157'" height="40" class="my-auto white--text px-2" @click="CheckApprove()">
                <span style="font-size: 14px;">อนุมัติการเข้าร่วม</span>
              </v-btn>
            </v-row>
          </v-card-actions>
          <v-card-actions v-else-if="DataApprove.status === 'Cancel'">
            <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-spacer></v-spacer>
              <v-btn dense color="#27AB9C" width="154" height="40" class="my-auto white--text" @click="CheckApprove()">เปิดใช้งานเข้าร่วม</v-btn>
            </v-row>
          </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalNotApproveAfter" width="750" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>การยกเลิกการเข้าร่วม</b></span>
              </v-col>
              <v-btn fab small @click="CancelNotApproveAfter()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 20px 20px 20px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12">
                    <v-row dense no-gutters justify="center">
                      <v-img src="@/assets/ImageINET-Marketplace/Shop/DocNotApprove.png" contain max-height="175" max-width="160"></v-img>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12">
                    <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">เหตุผลการยกเลิกการเข้าร่วม <span style="color:red"> *</span> สูงสุด 30 ตัวอักษร</span>
                    <v-form ref="Form2" :lazy-validation="lazy">
                      <v-textarea @keypress="CheckSpacebar($event)"
                        outlined
                        maxlength="30"
                        style="border-radius: 8px;"
                        placeholder="เหตุผลการยกเลิกการคู่ค้า"
                        v-model="reason"
                      ></v-textarea>
                    </v-form>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <v-card-actions>
            <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-btn rounded width="125" height="40" class="my-auto" outlined color="#27AB9C" @click="CancelNotApproveAfter()">
                ยกเลิก
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded width="125" height="40" class="white--text my-auto" color="#27AB9C" :disabled="reason !== '' ? false : true" @click="CheckNotApproveAfter()">
                บันทึก
              </v-btn>
            </v-row>
          </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalNotApprove" width="750" persistent :style="MobileSize ? 'z-index: 16000004' : ''">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>คำขอการเข้าร่วมโปรแกรม</b></span>
              </v-col>
              <v-btn fab small @click="CancelNotApprove()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 20px 20px 20px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" class="pb-4">
                    <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดการยื่นขอเข้าร่วมโปรแกรม</span>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col align="center" cols="12" md="4" sm="12">
                        <v-img v-if="DataApprove.img_path === '' || DataApprove.img_path === null || DataApprove.img_path === undefined" src="@/assets/Businessman.png" width="150px" height="150px"></v-img>
                        <v-img v-else :src="DataApprove.img_path" width="150px" height="150px"></v-img>
                      </v-col>
                      <v-col cols="12" md="6" sm="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัส Affiliate : <b>{{DataApprove.an_id}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ชื่อ-นามสกุล : <b>{{DataApprove.first_name_th}} {{DataApprove.last_name_th}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">หมายเลขโทรศัพท์ : <b>{{DataApprove.phone}}</b></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">อีเมล : <b>{{DataApprove.email}}</b></p>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" class="pb-4 pt-6">
                    <span style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดบัญชีโซเชียลมีเดีย</span>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="12" md="12" sm="12">
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Facebook : <b>{{DataApprove.facebook_link !== '' ? DataApprove.facebook_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.facebook_link !== ''" @click="copyLink('facebook')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">TikTok : <b>{{DataApprove.tiktok_link !== '' ? DataApprove.tiktok_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.tiktok_link !== ''" @click="copyLink('tiktok')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Youtube : <b>{{DataApprove.youtube_link !== '' ? DataApprove.youtube_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.youtube_link !== ''" @click="copyLink('youtube')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Instagram : <b>{{DataApprove.instagram_link !== '' ? DataApprove.instagram_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.instagram_link !== ''" @click="copyLink('instagram')" color="#27AB9C">mdi-link</v-icon></p>
                        <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">Line : <b>{{DataApprove.line_link !== '' ? DataApprove.line_link : '-'}}</b><v-icon class="ml-1" v-if="DataApprove.line_link !== ''" @click="copyLink('line')" color="#27AB9C">mdi-link</v-icon></p>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12">
                    <span class="pt-5 pb-4" style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">เหตุผลการไม่อนุมัติคำขอการเข้าร่วม <span style="color:red"> *</span> สูงสุด 30 ตัวอักษร</span>
                      <v-textarea @keypress="CheckSpacebar($event)"
                        outlined
                        maxlength="30"
                        style="border-radius: 8px;"
                        placeholder="กรอกเหตุผลการไม่อนุมัติคำขอการเข้าร่วม"
                        v-model="reason"
                      ></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
          <v-card-actions>
            <v-row dense style="height: 88px; background: #F5FCFB;" :style="MobileSize ? 'padding: 0px 20px;' : 'padding: 0px 44px;'">
              <v-btn rounded width="125" height="40" dense outlined color="#27AB9C" class="my-auto" @click="CancelNotApprove()">
                ยกเลิก
              </v-btn>
              <v-spacer></v-spacer>
              <v-btn rounded dense color="#27AB9C" width="125" height="40" class="my-auto white--text" :disabled="reason !== '' ? false : true" @click="CheckNotApprove()">
                บันทึก
              </v-btn>
            </v-row>
          </v-card-actions>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirmNotApprove" width="424"  :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="ModalConfirmNotApprove = !ModalConfirmNotApprove"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ไม่อนุมัติคำขอการเข้าร่วมโปรแกรม</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmNotApprove = !ModalConfirmNotApprove">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ApproveUsers(DataApprove, 'Reject')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirmNotApproveAfter" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="ModalConfirmNotApproveAfter = !ModalConfirmNotApproveAfter"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกการเข้าร่วม</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmNotApproveAfter = !ModalConfirmNotApproveAfter">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ApproveUsers(DataApprove, 'Cancel')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirmApprove" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="ModalConfirmApprove = !ModalConfirmApprove"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>อนุมัติการเข้าร่วม</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="ModalConfirmApprove = !ModalConfirmApprove">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ApproveUsers(DataApprove, 'Approve')">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalConfirmAutoApprove" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="CancelApproveAuto(TextSwitch)"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{TextSwitch}}การอนุมัติอัตโนมัติผู้เข้าร่วม</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="CancelApproveAuto(TextSwitch)">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="ApproveAuto(TextSwitch)">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalDetailApproveOrder" persistent width="747">
      <v-card style="background: #FFFFFF; border-radius: 4px;">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''"><b>รายละเอียดการอนุมัติการเข้าร่วม Affiliate</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="ModalDetailApproveOrder = !ModalDetailApproveOrder" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row dense justify="start">
              <v-col cols="12">
                <p style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดการอนุมัติการเข้าร่วม Affiliate</p>
              </v-col>
            </v-row>
            <!-- {{ DetailApproveOrder }} -->
            <v-row dense>
              <v-col cols="12" md="12">
                <p>รหัส Affiliate: {{ DataApprove.an_id }}</p>
                <p>สถานะการเข้าร่วม: <v-chip small :color="colorChipStatus(DataApprove.status)" :text-color="textChipStatus(DataApprove.status)">{{ textStatusOrder(DataApprove.status) }}</v-chip></p>
                <p>ชื่อลูกค้า: {{ DataApprove.first_name_th }} {{ DataApprove.last_name_th }}</p>
                <p>เบอร์โทรศัพท์: {{ DataApprove.phone }}</p>
                <p>อีเมล: {{ DataApprove.email }}</p>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-card elevation="0" width="100%" height="100%">
      <v-row class="my-4">
        <v-col>
          <v-row style="align-items: baseline;" class="pl-3">
            <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">รายชื่อผู้เข้าร่วม Affiliate</v-card-title>
            <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> รายชื่อผู้เข้าร่วม Affiliate</v-card-title>
          </v-row>
        </v-col>
        <v-col v-if="!MobileSize">
          <v-row style="align-items: baseline;" class="pr-6 justify-end">
            <v-switch class="pl-1" false-value="no" true-value="yes" inset v-model="AutoApprove" readonly @click="SwitchApprove(AutoApprove)"></v-switch>
            <span class="pb-0" style="font-weight: 400; font-size:18px; line-height: 32px;">เปิด-ปิด อนุมัติผู้เข้าร่วม</span>
          </v-row>
          <v-col class="d-flex justify-end">
            <span class="pb-0" style="font-weight: 700; font-size:14px; line-height: 32px; color: #A1A1A1;">(* เปิด = อนุมัติอัตโนมัติ / ปิด = อนุมัติด้วยตนเอง)</span>
          </v-col>
        </v-col>
        <v-col cols="12" v-if="MobileSize" class="py-0 pl-6">
          <v-row class="pl-6 align-baseline">
            <v-switch class="pl-1" false-value="no" true-value="yes" inset v-model="AutoApprove" readonly @click="SwitchApprove(AutoApprove)"></v-switch>
            <span class="pb-0" style="font-weight: 400; font-size:18px; line-height: 32px;">เปิด-ปิด อนุมัติผู้เข้าร่วม</span>
          </v-row>
          <v-col class="pb-0">
            <span class="pb-0" style="font-weight: 700; font-size:14px; line-height: 32px; color: #A1A1A1;">(* เปิด = อนุมัติอัตโนมัติ / ปิด = อนุมัติด้วยตนเอง)</span>
          </v-col>
        </v-col>
      </v-row>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" class="py-0 pr-3">
            <a-tabs @change="SelectDetailOrder">
              <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
              <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countOrderWaiting }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderApprove }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="3"><span slot="tab">ปฏิเสธอนุมัติ <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderReject }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col>
          <v-row class="align-center">
            <v-col v-if="disableTable === false" :cols="MobileSize ? '12' : '6'">
            <v-text-field v-model="search" dense hide-details outlined placeholder="ค้นหาจากรหัสหรือชื่อของผู้เข้าร่วม" style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- <v-col v-if="disableTable === false" cols="1">
            <span style="font-size: 16px; font-weight: 600">Pay Type:</span>
          </v-col>
          <v-col v-if="disableTable === false" cols="3">
            <v-select dense hide-details outlined placeholder="ทั้งหมด" style="border-radius: 8px;" :items="['ทั้งหมด', 'One Time', 'Recurring']">
        </v-select>
          </v-col> -->
          </v-row>
          <v-col cols="12" class="" v-if="disableTable === false">
            <v-row dense>
              <v-col cols="12" md="6" class="pt-2">
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 0">รายการทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 1">รายการรออนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 2">รายการอนุมัติแล้วทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 3">รายการปฏิเสธอนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 4">รายการยกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" class="px-0">
            <v-card v-if="disableTable === false" outlined class="small-card mr-2 my-5" min-height="436">
              <v-data-table
                :headers="headerApproveOrder"
                :items="filteredList"
                :page.sync="page"
                style="width:100%;"
                height="100%"
                no-results-text="ไม่พบรายการอนุมัติที่ค้นหา"
                no-data-text="ไม่มีรายการอนุมัติในตาราง"
                @pagination="countOrdarApprove"
                :items-per-page="10"
                class=""
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                :update:items-per-page="itemsPerPage"
              >
              <template v-slot:[`item.status`]="{ item }">
                <span>
                  <v-chip :color="colorChipStatus(item.status)" :text-color="textChipStatus(item.status)">{{ textStatusOrder(item.status) }}</v-chip>
                </span>
              </template>
              <template v-slot:[`item.name`]="{ item }">
                <span>
                  {{ item.first_name_th }} {{ item.last_name_th }}
                </span>
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <v-tooltip bottom>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      width="30"
                      height="30"
                      v-bind="attrs"
                      v-on="on"
                      style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      outlined icon @click="DetailApprove(item)">
                      <v-icon color="#27AB9C" class="" size="18">mdi-account-details-outline</v-icon>
                    </v-btn>
                  </template>
                  <span>รายละเอียดการอนุมัติรายการเข้าร่วม</span>
                </v-tooltip>
                <v-btn small text rounded color="#27AB9C" @click="DetailApprove(item)">
                  <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
                  <!-- <v-icon small>mdi-chevron-right</v-icon> -->
                </v-btn>
              </template>
              </v-data-table>
            </v-card>
          </v-col>
          <v-col cols="12" v-if="disableTable === true" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <div v-if="IpadSize">
              <h3 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0"><b>คุณยังไม่มีรายการอนุมัติ</b></h3>
              <h3 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1"><b>คุณยังไม่มีรายการที่รออนุมัติ</b></h3>
              <h3 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2"><b>คุณยังไม่มีรายการที่อนุมัติแล้ว</b></h3>
              <h3 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 3"><b>คุณยังไม่มีรายการที่ปฏิเสธอนุมัติ</b></h3>
              <h3 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 4"><b>คุณยังไม่มีรายการที่ยกเลิก</b></h3>
            </div>
            <div v-else>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0"><b>คุณยังไม่มีรายการอนุมัติ</b></h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1"><b>คุณยังไม่มีรายการที่รออนุมัติ</b></h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2"><b>คุณยังไม่มีรายการที่อนุมัติแล้ว</b></h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 3"><b>คุณยังไม่มีรายการที่ปฏิเสธอนุมัติ</b></h2>
              <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 4"><b>คุณยังไม่มีรายการที่ยกเลิก</b></h2>
            </div>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
// import { Decode, Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      TextSwitch: '',
      AutoApprove: '',
      lazy: false,
      reason: '',
      DataApprove: [],
      countOrderAll: 0,
      countOrderWaiting: 0,
      countOrderApprove: 0,
      countOrderProcess: 0,
      countOrderReject: 0,
      countOrderCancel: 0,
      disableTable: false,
      search: '',
      page: 1,
      ModalConfirmAutoApprove: false,
      ModalConfirmApprove: false,
      ModalConfirmNotApprove: false,
      ModalNotApprove: false,
      ModalConfirmNotApproveAfter: false,
      ModalNotApproveAfter: false,
      ModalDetailPartner: false,
      ModalDetailApproveOrder: false,
      headerApproveOrder: [
        { text: 'รหัส Affiliate', value: 'an_id', sortable: false, filterable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ชื่อ-นามสกุล', value: 'name', align: 'center', sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'action', align: 'center', filterable: false, sortable: false, width: '200px', class: 'backgroundTable fontTable--text fontSizeDetail' }
        // { text: '', value: 'consent', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      showCountOrder: 0,
      itemsPerPage: 10,
      StateStatus: 0,
      DataTable: [],
      orderList: [],
      DetailApproveOrder: [],
      DetailApproveOrderBy: [],
      oneData: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.getListApprove()
  },
  computed: {
    filteredList () {
      if (this.search !== '') {
        return this.DataTable.filter(item => {
          return item.an_id.toLowerCase().includes(this.search.toLowerCase()) || item.first_name_th.toLowerCase().includes(this.search.toLowerCase()) || item.last_name_th.toLowerCase().includes(this.search.toLowerCase()) || (item.first_name_th + ' ' + item.last_name_th).toLowerCase().includes(this.search.toLowerCase())
        })
      } else {
        return this.DataTable
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ListUserJoinAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ListUserJoinAffiliate' }).catch(() => {})
      }
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.orderList.All
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.Waiting
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.Approve
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 3) {
        this.DataTable = this.orderList.Reject
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 4) {
        this.DataTable = this.orderList.Cancel
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      }
    }
  },
  methods: {
    CancelApproveAuto (item) {
      if (item === 'เปิด') {
        this.AutoApprove = 'no'
        this.ModalConfirmAutoApprove = false
      } else {
        this.AutoApprove = 'yes'
        this.ModalConfirmAutoApprove = false
      }
    },
    async ApproveAuto (item) {
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      if (item === 'เปิด') {
        this.AutoApprove = 'yes'
      } else {
        this.AutoApprove = 'no'
      }
      var data = {
        seller_shop_id: shopId,
        status: this.AutoApprove
      }
      await this.$store.dispatch('actionsAffiliateAutoApprove', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateAutoApprove
      if (res.message === 'update status to yes success' || res.message === 'update status to no success') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          html: `<h3>${this.TextSwitch}การอนุมัติอัตโนมัติผู้เข้าร่วมสำเร็จ</้>`
        })
        this.ModalConfirmAutoApprove = false
        this.getListApprove()
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: 'เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง'
        })
        this.AutoApprove = this.AutoApprove === 'yes' ? 'no' : 'yes'
        this.ModalConfirmAutoApprove = false
        this.$store.commit('closeLoader')
      }
    },
    SwitchApprove (item) {
      this.ModalConfirmAutoApprove = true
      this.TextSwitch = item === 'yes' ? 'ปิด' : 'เปิด'
    },
    copyLink (text) {
      if (text === 'facebook') {
        navigator.clipboard.writeText(this.DataApprove.facebook_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'tiktok') {
        navigator.clipboard.writeText(this.DataApprove.tiktok_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'youtube') {
        navigator.clipboard.writeText(this.DataApprove.youtube_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'instagram') {
        navigator.clipboard.writeText(this.DataApprove.instagram_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      } else if (text === 'line') {
        navigator.clipboard.writeText(this.DataApprove.line_link).then(() => {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'คัดลอกลิงก์สำเร็จ'
          })
        })
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    CheckApprove () {
      this.ModalConfirmApprove = !this.ModalConfirmApprove
      this.ModalDetailPartner = !this.ModalDetailPartner
    },
    CheckNotApprove () {
      this.ModalNotApprove = !this.ModalNotApprove
      this.ModalConfirmNotApprove = !this.ModalConfirmNotApprove
    },
    CancelNotApprove () {
      this.reason = ''
      this.ModalNotApprove = !this.ModalNotApprove
    },
    NotApprove () {
      this.ModalDetailPartner = !this.ModalDetailPartner
      this.ModalNotApprove = !this.ModalNotApprove
    },
    CheckNotApproveAfter () {
      this.ModalNotApproveAfter = !this.ModalNotApproveAfter
      this.ModalConfirmNotApproveAfter = !this.ModalConfirmNotApproveAfter
    },
    CancelNotApproveAfter () {
      this.reason = ''
      this.ModalNotApproveAfter = !this.ModalNotApproveAfter
    },
    NotApproveWhenAccept (val) {
      this.reason = ''
      this.dataForAccept = val
      this.ModalDetailPartner = !this.ModalDetailPartner
      this.ModalNotApproveAfter = !this.ModalNotApproveAfter
    },
    DetailApprove (item) {
      this.ModalDetailPartner = !this.ModalDetailPartner
      this.DataApprove = item
      // console.log(this.DataApprove)
    },
    async ApproveUsers (item, text) {
      // console.log('item', item)
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: shopId,
        status: text,
        user_id: item.user_id,
        remark: this.reason
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsApproveBuyer', data)
      const res = await this.$store.state.ModuleAffiliate.stateApproveBuyer
      // console.log('res', res)
      if (res.success === true) {
        if (text === 'Approve') {
          this.ModalConfirmApprove = false
          this.$swal.fire({ icon: 'success', text: 'อนุมัติผู้เข้าร่วมสำเร็จ', showConfirmButton: false, timer: 3000 })
        } else if (text === 'Reject') {
          this.ModalConfirmNotApprove = false
          this.$swal.fire({ icon: 'success', text: 'ปฏิเสธผู้เข้าร่วมสำเร็จ', showConfirmButton: false, timer: 3000 })
        } else if (text === 'Cancel') {
          this.ModalConfirmNotApproveAfter = false
          this.$swal.fire({ icon: 'success', text: 'ยกเลิกผู้เข้าร่วมสำเร็จ', showConfirmButton: false, timer: 3000 })
        }
        this.reason = ''
        this.getListApprove()
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 3000 })
      }
    },
    SelectDetailOrder (item) {
      this.StateStatus = item
      this.page = 1
    },
    async getListApprove () {
      this.$store.commit('openLoader')
      // var msg = ''
      this.countOrderAll = 0
      this.countOrderWaiting = 0
      this.countOrderApprove = 0
      this.countOrderProcess = 0
      this.countOrderReject = 0
      this.countOrderCancel = 0
      this.DataTable = []
      const shopId = localStorage.getItem('shopSellerID')
      const data = {
        seller_shop_id: shopId
      }
      await this.$store.dispatch('actionsListUserJoin', data)
      var response = await this.$store.state.ModuleAffiliate.stateListUserJoin
      if (response.success === true) {
        this.$store.commit('closeLoader')
        this.disableTable = false
        this.AutoApprove = response.auto_approve
        if (response.data.length !== 0) {
          this.OrderRes = response.data
          const categories = {
            All: [],
            Approve: [],
            Cancel: [],
            Waiting: [],
            Process: [],
            Reject: []
          }
          this.OrderRes.forEach(item => {
            categories.All.push(item)
            if (categories[item.status]) {
              categories[item.status].push(item)
            }
          })
          this.orderList = categories
          // console.log('this.orderList', this.orderList)
          this.countOrderAll = this.orderList.All.length
          this.countOrderWaiting = this.orderList.Waiting.length
          this.countOrderApprove = this.orderList.Approve.length
          this.countOrderProcess = this.orderList.Process.length
          this.countOrderReject = this.orderList.Reject.length
          this.countOrderCancel = this.orderList.Cancel.length
          if (this.StateStatus === 0) {
            this.DataTable = this.orderList.All
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 1) {
            this.DataTable = this.orderList.Waiting
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 2) {
            this.DataTable = this.orderList.Approve
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 3) {
            this.DataTable = this.orderList.Reject
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 4) {
            this.DataTable = this.orderList.Cancel
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          }
        } else {
          this.DataTable = []
          this.orderList = []
          this.disableTable = true
        }
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.disableTable = true
          this.DataTable = []
          this.orderList = []
        }
      }
    },
    backToMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router
          .push({
            path:
              '/sellerMobile?ShopID=' +
              shopDetail.id +
              '&ShopName=' +
              shopDetail.name
          })
          .catch(() => {})
      } else {
        this.$router.push({
          path:
            '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name
        })
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (msg === 'Data missing. Please check your [" company_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Company Permission not found.') {
        return 'ไม่พบสิทธิ์ผู้ใช้องค์กรนี้'
      } else if (msg === 'This is not you Company Permission.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ใช่ของคุณ'
      } else if (msg === 'This Permission not in your Company.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ได้อยู่ในองค์กรของคุณ'
      } else if (msg === 'Data missing. Please check your [" com_perm_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสสิทธิ์ผู้ใช้องค์กร ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Purchaser not found.') {
        return 'ไม่พบข้อมูลผู้ซื้อองค์กร'
      } else if (msg === '') {
        return ''
      } else {
        return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
      }
    },
    checkType (val) {
      let type = ''
      if (val === 'all') {
        type = 'อนุมัติทั้งหมด'
      } else if (val === 'one') {
        type = 'อนุมัติ 1 คน'
      } else if (val === 'many') {
        type = 'ระบุวงเงิน'
      } else {
        type = ''
      }
      return type
    },
    countOrdarApprove (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    textStatusOrder (val) {
      if (val === 'Waiting') {
        return 'รออนุมัติ'
      } else if (val === 'Success') {
        return 'อนุมัติแล้ว'
      } else if (val === 'Approve') {
        return 'อนุมัติแล้ว'
      } else if (val === 'Process') {
        return 'กำลังดำเนินการ'
      } else if (val === 'Reject') {
        return 'ปฏิเสธอนุมัติ'
      } else {
        return 'ยกเลิก'
      }
    },
    colorChipStatus (val) {
      if (val === 'Waiting') {
        return '#fefdec'
      } else if (val === 'Success') {
        return '#F0F9EE'
      } else if (val === 'Approve') {
        return '#F0F9EE'
      } else if (val === 'Process') {
        return '#e8f6f9'
      } else if (val === 'Reject') {
        return '#f7c5ad'
      } else {
        return '#F7D9D9'
      }
    },
    textChipStatus (val) {
      if (val === 'Waiting') {
        return '#cfc60b'
      } else if (val === 'Success') {
        return '#1AB759'
      } else if (val === 'Approve') {
        return '#1AB759'
      } else if (val === 'Process') {
        return '#6EC4D6'
      } else if (val === 'Reject') {
        return '#f50'
      } else {
        return '#D1392B'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
<style>
.v-data-table > .v-data-table__wrapper > table > tbody > tr > th,
.v-data-table > .v-data-table__wrapper > table > thead > tr > th,
.v-data-table > .v-data-table__wrapper > table > tfoot > tr > th {
  font-size: 14px !important;
}
.disable-events {
  pointer-events: none
}
</style>
