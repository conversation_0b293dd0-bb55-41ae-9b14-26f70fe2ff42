<template>
  <div>
    <v-row dense>
      <v-col cols="6">
        <v-btn @click="GoToRegister()" color="white" width="113px" height="44px" rounded v-if="!IpadSize && !MobileSize" outlined style="font-size: 16px; font-weight: 500; text-transform: none;">{{ $t('AppBar.Register') }}</v-btn>
        <v-btn @click="GoToRegister()" color="white" height="44px" rounded v-else style="font-size: 12px; font-weight: 500; text-transform: none;" outlined>{{ $t('AppBar.Register') }}</v-btn>
      </v-col>
      <v-col cols="6" style="justify-content: end; display: flex;">
        <v-btn @click="GoToLogin()" rounded elevation="0" color="white" width="113px" height="44px" v-if="!IpadSize && !MobileSize" style="font-size: 16px; font-weight: 500; color: #27AB9C; text-transform: none;">{{ $t('AppBar.Login') }}</v-btn>
        <v-btn @click="GoToLogin()" rounded elevation="0" color="white" height="44px" v-else style="font-size: 12px; font-weight: 500; color: #27AB9C; text-transform: none;">{{ $t('AppBar.Login') }}</v-btn>
      </v-col>
    </v-row>
  </div>
</template>
<script>
export default {
  data () {
    return {
    }
  },
  created () {
    localStorage.removeItem('_sellerShop')
    localStorage.removeItem('_selectProduct')
    localStorage.removeItem('_productToCal')
    localStorage.removeItem('lineUserId')
    if (localStorage.getItem('roleUser') === null) {
      localStorage.setItem('roleUser', JSON.stringify({ role: 'ext_buyer' }))
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    GoToLogin () {
      // console.log('this.$router.currentRoute.path', this.$router.currentRoute.fullPath)
      sessionStorage.setItem('pathRedirect', this.$router.currentRoute.fullPath)
      this.$router.push({ path: '/Login' }).catch(() => {})
    },
    GoToRegister () {
      this.$router.push({ path: '/Register' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.borderButtomText {
  margin-bottom: -30px
}
</style>

<!-- <style>
.numeric-input .ant-tooltip-inner {
  min-width: 50px;
  min-height: 35px;
}
.numeric-input .numeric-input-title {
  font-size: 14px;
}
.ant-popover-inner-content{
  padding: 0;
}
#components-form-demo-normal-login .login-form {
  max-width: 350px;
}
#components-form-demo-normal-login .login-form-forgot {
  float: right;
  margin-bottom: 10px;
  margin-top: -15px;
}
#components-form-demo-normal-login .login-form-button {
  width: 100%;
}
</style> -->
