<template>
  <div>
    <!-- <v-btn
      color="green darken-1" dark absolute bottom right fixed fab style="margin-bottom: 65px" @click="printpo" v-if="printvisible" class="btnPrint">
      <v-icon>mdi-printer</v-icon>
    </v-btn> -->
    <div class="page elevation-0">
      <div>
        <Header :OrderDetailProp="OrderDetailProp"/>
      </div>
      <div>
        <v-row dense>
          <v-col cols="12" md="12">
            <v-card class="elevation-0">
              <v-row dense>
                <!-- <v-col cols="12" md="12" class="pa-0">
                  <span>ร้านค้า: sdsdsdsdsd</span>
                </v-col> -->
                <v-col cols="12" md="12" class="pa-0">
                  <Body :OrderDetailProp="OrderDetailProp"/>
                  <!-- <v-card outlined>
                    <v-data-table
                      hide-default-footer
                      disable-sort
                      :disable-pagination="disable_pagination"
                      :headers="locallang === 'en' ? headers_en : headers_th"
                      :items="table1"
                      item-key="name"
                      :calculate-widths="widths"
                      dense
                      class="fixed_header"
                    >
                      <template
                        v-slot:[`item.sku`]="{ item }"
                        :width="headers.width"
                      >
                        <span style="font-size: 10px; font-weight: 301">{{
                          item.sku
                        }}</span>
                      </template>
                      <template v-slot:[`item.name`]="{ item }">
                        <span style="font-size: 10px; font-weight: 301">{{
                          item.name
                        }}</span>
                      </template>
                      <template v-slot:[`item.quantity`]="{ item }">
                        <span style="font-size: 10px; font-weight: 301">{{
                          Number(item.quantity).toLocaleString()
                        }}</span>
                      </template>
                      <template v-slot:[`item.price`]="{ item }">
                        <span style="font-size: 10px; font-weight: 301">{{
                          Number(item.price).toLocaleString()
                        }}</span>
                      </template>
                      <template v-slot:[`item.discount_percent`]="{ item }">
                        <span style="font-size: 10px; font-weight: 301">{{
                          item.discount_percent
                        }}</span>
                      </template>
                      <template v-slot:[`item.net_price`]="{ item }">
                        <span style="font-size: 10px; font-weight: 301">{{
                          Number(item.net_price).toLocaleString()
                        }}</span>
                      </template>
                    </v-data-table>
                  </v-card> -->
                </v-col>
              </v-row>
            </v-card>
          </v-col>
          <Summary :OrderDetailProp="OrderDetailProp"/>
          <!-- <v-col cols="12" md="12" v-if="table_body.length > 12 && table_body.length == 20" class="page mt-2">
            <v-card outlined class="elevation-0">
              <QuotationFooter />
            </v-card>
          </v-col> -->
        </v-row>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  components: {
    Header: () => import('@/components/Quotation/Header'),
    Body: () => import('@/components/Quotation/Body'),
    Summary: () => import('@/components/Quotation/Summary')
  },
  data: () => ({
    orderList: [],
    OrderDetailProp: []
  }),
  async created () {
    this.$EventBus.$emit('getPath')
    await this.$store.dispatch('actionListOrderBuyer')
    this.orderList = await this.$store.state.ModuleOrder.stateOrderListData
    // console.log('orderlist', this.orderList)
    this.orderDetail()
  },
  methods: {
    async orderDetail () {
      var data = {
        payment_transaction_number: this.orderList.data.not_paid[0].payment_transaction_number
      }
      // console.log('data naja', data)
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('order detail na', response)
      if (response.result === 'SUCCESS') {
        this.OrderDetailProp = response.data
        // console.log('กี่ร้าน', response.data)
      }
    },
    printpo () {
      setTimeout(() => {
        window.print()
      }, 500)
      this.printvisible = false
      setTimeout(() => {
        this.printvisible = true
      }, 2000)
    }
  }
}
</script>
<style scoped>
.page {
  width: 21cm;
  height: 29.7cm;
  padding: 1cm;
  margin: 0cm auto;
  border: 1px #d3d3d3 solid;
  border-radius: 5px;
  background: white;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.1);
}
@page {
  size: A4;
  margin: 0;
}
@page {
  size: A4;
  margin: 0;
}
@media print {
  .page {
    margin: 0;
    box-shadow: 0;
  }
  * {
    -webkit-print-color-adjust: exact;
  }
  .btnPrint {
    display: none;
  }
}
</style>
