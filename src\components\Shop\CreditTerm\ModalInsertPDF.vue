<template>
  <v-dialog v-model="ModalPDF" width="800px" persistent>
    <v-card width="100%" :max-height="!MobileSize ? '100%' : '450'" class="rounded-lg">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span class="flex text-center ml-5" style="font-size:20px">
          <font color="#27AB9C">แนบเอกสารใบแจ้งหนี้</font>
        </span>
        <v-btn icon dark @click="ModalPDF = false">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row :class="!MobileSize ? 'mt-1' : 'mt-0'" dense>
          <v-col cols="12">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
              <v-card-text>
                <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสการสั่งซื้อที่ : {{ DataPDF.order_number }}</p>
                <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">งวดที่ : {{ DataPDF.credit_term }}</p>
                <v-card
                  elevation="0"
                  style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                  @click="onPickFile()"
                >
                  <v-card-text>
                    <v-row
                      no-gutters
                      align="center"
                      justify="center"
                      style="cursor: pointer;"
                    >
                      <v-file-input
                        v-model="DataImage"
                        :items="DataImage"
                        accept=".pdf"
                        @change="UploadImage()"
                        id="file_input"
                        :clearable="false"
                        style="display:none"
                      >
                      </v-file-input>
                      <v-col cols="12" md="12" class="mb-6">
                        <v-row justify="center" :class="!MobileSize ? 'pt-10' : 'pa-0'">
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                            :width="!MobileSize ? '280.34' : '40px'"
                            :height="!MobileSize ? '154.87' : '40px'"
                            contain
                          ></v-img>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" :class="!MobileSize ? 'mt-6' : 'ma-0'">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="12" style="text-align: center;">
                            <span style="line-height: 24px; font-weight: 400;" :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">เพิ่มเอกสารของคุณที่นี่หรือ</span><br/>
                            <span style="line-height: 24px; font-weight: 400;" :style="MobileSize ? 'font-size: 12px;' : 'font-size: 16px;'">เลือกเอกสารจากคอมพิวเตอร์ของคุณ</span><br/>
                            <span style="line-height: 16px; font-weight: 400;" :style="MobileSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ไฟล์นามสกุล .pdf เพิ่มได้สูงสุด 1 ไฟล์)</span><br/>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
                <div v-if="DataImage.length !== 0" class="mt-2">
                  <draggable v-model="DataImage"  :move="onMove1" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                    <v-col cols="12" md="2">
                      <v-card outlined class="pa-0" :min-width="!MobileSize ? '146' : '100%'" :max-height="!MobileSize ? '100%' : '95px'">
                        <v-card-text>
                          <v-btn icon x-small style="float: right; background-color: #ff5252;" v-if="MobileSize">
                            <v-icon x-small color="white" dark @click="RemoveImage()">mdi-close</v-icon>
                          </v-btn>
                          <v-row dense class="ma-0">
                            <v-col cols="12" align="center">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" :width="!MobileSize ? '130' : '40px'" :height="!MobileSize ? '130' : '40px'" contain>
                                <v-btn icon x-small style="float: right; background-color: #ff5252;" v-if="!MobileSize">
                                  <v-icon x-small color="white" dark @click="RemoveImage()">mdi-close</v-icon>
                                </v-btn>
                              </v-img>
                            </v-col>
                            <v-col cols="12">
                              <p class="mb-2" style="text-align: center;" v-snip="1">{{ DataImage.name }}</p>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </draggable>
                </div>
              </v-card-text>
              <v-row justify="end" dense class="mr-2">
                <v-btn text outlined class="pr-8 pl-8 mr-4" @click="ModalPDF = false" color="primary" style="border: 1px solid #27AB9C;">ยกเลิก</v-btn>
                <v-btn :disabled="DataImage === '' ? true : false" color="primary" class="pr-8 pl-8" @click="confirm()">บันทึก</v-btn>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
  <!-- dialog show detail of request -->
  <!-- <v-dialog v-model="ModalPDF" width="800px" persistent>
    <v-card width="100%" height="100%" class="rounded-lg">
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span class="flex text-center ml-5" style="font-size:20px">
          <font color="#27AB9C">แนบเอกสารใบแจ้งหนี้</font>
        </span>
        <v-btn icon dark @click="ModalPDF = false">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row class="mt-2" dense>
          <v-col cols="12">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
              <v-card-text>
                <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">รหัสการสั่งซื้อที่ : {{ DataPDF.order_number }}</p>
                <p style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">งวดที่ : {{ DataPDF.credit_term }}</p>
                <v-card
                  elevation="0"
                  style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                  @click="onPickFile()"
                >
                  <v-card-text>
                    <v-row
                      no-gutters
                      align="center"
                      justify="center"
                      style="cursor: pointer;"
                    >
                      <v-file-input
                        v-model="DataImage"
                        :items="DataImage"
                        accept=".pdf"
                        @change="UploadImage()"
                        id="file_input"
                        :clearable="false"
                        multiple
                        style="display:none"
                      >
                      </v-file-input>
                      <v-col cols="12" md="12" class="mb-6">
                        <v-row justify="center" class="pt-10">
                          <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                            width="280.34"
                            height="154.87"
                            contain
                          ></v-img>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" class="mt-6">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="12" style="text-align: center;">
                            <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มเอกสารของคุณที่นี่</span><br/>
                            <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกเอกสารจากคอมพิวเตอร์ของคุณ</span><br/>
                            <span style="line-height: 16px; font-weight: 400;" :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ไฟล์นามสกุล .pdf เพิ่มได้สูงสุด 1 ไฟล์)</span><br/>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
                <div v-if="DataImage.length !== 0" class="mt-4">
                  <draggable v-model="DataImage"  :move="onMove1" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                    <v-col v-for="(item, index) in DataImage" :key="index" cols="12" md="2">
                      <v-card outlined class="pa-1" width="146" height="100%">
                        <v-card-text>
                          <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="130" height="130" contain>
                          </v-img>
                          <p style="text-align: center;">{{ item.name }}</p>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </draggable>
                </div>
              </v-card-text>
              <v-row justify="end" dense class="mr-2">
                <v-btn text outlined class="pr-8 pl-8 mr-4" @click="ModalPDF = false" color="primary" style="border: 1px solid #27AB9C;">ยกเลิก</v-btn>
                <v-btn color="primary" class="pr-8 pl-8" @click="confirm()">บันทึก</v-btn>
              </v-row>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog> -->
</template>

<script>
import draggable from 'vuedraggable'
import { Decode, Encode } from '@/services'
export default {
  components: {
    draggable
  },
  data () {
    return {
      order_detail: [],
      ModalPDF: false,
      DataImage: [],
      DataPDF: {
        order_number: '',
        seller_shop_id: '',
        credit_term: '',
        pdf: ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    onPickFile () {
      // console.log('onPickFile')
      document.getElementById('file_input').click()
    },
    UploadImage () {
      if (this.DataImage !== '') {
        const element = this.DataImage
        const imageSize = element.size / 1024 / 1024
        if (imageSize < 2) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            this.DataPDF.pdf = resultReader.split(',')[1]
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
            showConfirmButton: false,
            timer: 1500
          })
          this.DataImage = ''
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    RemoveImage () {
      this.DataImage = ''
      this.DataPDF.pdf = ''
    },
    // UploadImage () {
    //   // console.log('UploadImage', this.DataImage.length)
    //   // this.DataPDF.pdf = this.DataImage
    //   if (this.DataImage.length <= 1) {
    //     for (let i = 0; i < this.DataImage.length; i++) {
    //       const element = this.DataImage[i]
    //       const imageSize = element.size / 1024 / 1024
    //       if (imageSize < 2) {
    //         const reader = new FileReader()
    //         reader.readAsDataURL(element)
    //         reader.onload = () => {
    //           var resultReader = reader.result
    //           this.DataPDF.pdf = resultReader.split(',')[1]
    //           // console.log('dd', this.DataPDF)
    //         }
    //       } else {
    //         this.$swal.fire({
    //           icon: 'warning',
    //           text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 2 MB',
    //           showConfirmButton: false,
    //           timer: 1500
    //         })
    //       }
    //     }
    //   } else {
    //     this.$swal.fire({
    //       icon: 'warning',
    //       text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 1 รูป',
    //       showConfirmButton: false,
    //       timer: 1500
    //     })
    //   }
    // },
    onMove1 ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    async confirm () {
      await this.$store.dispatch('actionsUploadInvoiceCreditTerm', this.DataPDF)
      var response = await this.$store.state.ModuleShop.stateUploadInvoiceCreditTerm
      if (response.result === 'SUCCESS') {
        // console.log(response.data[0])
        localStorage.removeItem('creditTerm')
        this.$swal.fire({ icon: 'success', title: 'บันทึกเอกสารสำเร็จ', showConfirmButton: false, timer: 2000 })
        localStorage.setItem('creditTerm', Encode.encode(response.data[0]))
        await this.$EventBus.$emit('start')
        this.ModalPDF = !this.ModalPDF
        // this.CreditTermDetail()
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
      }
    },
    open () {
      this.DataImage = ''
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // console.log('CreateTaxinvoiceAddressRole', dataRole)
      // this.getAddressTaxinvoiceData()
      this.order_detail = JSON.parse(Decode.decode(localStorage.getItem('creditTerm')))
      this.DataPDF.order_number = this.order_detail.order_number
      this.DataPDF.seller_shop_id = this.order_detail.seller_shop_id
      this.DataPDF.credit_term = this.order_detail.credit_term
      // console.log(this.order_detail)
      this.ModalPDF = true
    }
  }

}
</script>

<style>
.fontSizeTitle {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;
}

</style>
