<template>
  <div>
    <v-card outlined>
      <v-toolbar flat>
        <v-col cols="4" md="4">
          <v-text-field v-model="search" append-icon="mdi-magnify" label="ค้นหา" filled rounded dense single-line hide-details></v-text-field>
        </v-col>
        <v-spacer></v-spacer>
        <v-btn color="info" small outlined rounded @click="goPage('/createdepartment')">
          <v-icon small>mdi-plus</v-icon>เพิ่มแผนก
        </v-btn>
        <v-menu bottom left>
          <template v-slot:activator="{ on, attrs }">
            <v-btn icon small v-bind="attrs" v-on="on">
              <v-icon>mdi-dots-vertical</v-icon>
            </v-btn>
          </template>
          <v-list nav dense>
            <v-list-item-group color="primary">
              <v-list-item v-for="(item, i) in items" :key="i">
                <v-list-item-icon>
                  <v-icon v-text="item.icon"></v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title v-text="item.text"></v-list-item-title>
                </v-list-item-content>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </v-menu>
      </v-toolbar>
      <v-card-title primary-title>
          รายการแผนก
      </v-card-title>
      <v-divider></v-divider>
      <v-data-table :headers="headers" :items="data" @page-count="pageCount = $event" :page.sync="page" :items-per-page="itemsPerPage" class="elevation-0, rounded-lg" :search="search" hide-default-footer>
        <template v-slot:item.detail>
          <v-chip outlined color="green" @click="goPage('/detaildepartment')">
            รายละเอียด
          </v-chip>
        </template>
        <template v-slot:[`item.status`]="{ item }">
          <v-switch style="margin: 0px;" v-model="item.status" :color="getColor(item.status)" @change="Actions(item.status)" hide-details></v-switch>
        </template>
        <template v-slot:[`item.actions`]="{ item }">
          <v-btn icon @click="Delete(item)">
            <v-icon>mdi-delete-outline</v-icon>
          </v-btn>
          <v-btn icon @click="goPage('/createdepartment')">
            <v-icon>mdi-pencil-outline</v-icon>
          </v-btn>
        </template>
      </v-data-table>
    </v-card>
    <div class="text-center pt-2">
      <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
    </div>
  </div>
</template>

<script>
export default {
  data () {
    return {
      search: '',
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      headers: [
        { text: 'ลำดับที่', value: 'number', sortable: true, align: 'left', width: '40' },
        { text: 'รหัสบริษัท', value: 'company_id', sortable: false, width: '80' },
        { text: 'รหัสแผนก', value: 'department_id', sortable: false, align: 'center', width: '80' },
        { text: 'ชื่อแผนก', value: 'department_name', sortable: false, align: 'center', width: '80' },
        { text: 'ข้อมูล', value: 'detail', sortable: false, align: 'center', width: '80' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'center', width: '40' },
        { text: 'Actions', value: 'actions', sortable: false, align: 'center', width: '40' }
      ],
      data: [
        {
          number: 1,
          company_id: 'Test01',
          department_id: 'Depart01',
          department_name: 'name01',
          detail: true,
          status: true
        },
        {
          number: 2,
          company_id: 'Test02',
          department_id: 'Depart02',
          department_name: 'name02',
          detail: true,
          status: false
        },
        {
          number: 3,
          company_id: 'Test03',
          department_id: 'Depart03',
          department_name: 'name03',
          detail: true,
          status: true
        },
        {
          number: 4,
          company_id: 'Test04',
          department_id: 'Depart04',
          department_name: 'name04',
          detail: true,
          status: true
        },
        {
          number: 5,
          company_id: 'Test05',
          department_id: 'Depart05',
          department_name: 'name05',
          detail: true,
          status: true
        },
        {
          number: 6,
          company_id: 'Test06',
          department_id: 'Depart06',
          department_name: 'name06',
          detail: true,
          status: true
        },
        {
          number: 7,
          company_id: 'Test07',
          department_id: 'Depart07',
          department_name: 'name07',
          detail: true,
          status: true
        },
        {
          number: 8,
          company_id: 'Test08',
          department_id: 'Depart08',
          department_name: 'name08',
          detail: true,
          status: true
        },
        {
          number: 9,
          company_id: 'Test09',
          department_id: 'Depart09',
          department_name: 'name09',
          detail: true,
          status: true
        },
        {
          number: 10,
          company_id: 'Test10',
          department_id: 'Depart10',
          department_name: 'name10',
          detail: true,
          status: true
        }
      ],
      items: [
        { text: 'ตั้งค่าบริษัท', icon: 'mdi-cog-outline' }
      ]
    }
  },
  methods: {
    goPage (str) {
      this.$router.push(str)
    },
    getColor (status) {
      if (status === false) return 'grey'
      else return 'green'
    },
    Delete (item) {
      // console.log('item', item)
      const ToastDelete = this.$swal.mixin({
        toast: true,
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        cancelButtonColor: '#d33'
      })
      ToastDelete.fire({
        icon: 'warning',
        title: 'คุนต้องการลบแผนกหรือไม่'
      }).then((result) => {
        if (result.isConfirmed) {
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    Actions (item) {
      // console.log('item', item)
      if (item === false) {
        const ToastDelete = this.$swal.mixin({
          toast: true,
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          cancelButtonColor: '#d33'
        })
        ToastDelete.fire({
          icon: 'warning',
          title: 'ยืนยันการปิดการใช้งาน(ฝ่าย/แผนก จะถูกปิดการใช้งาน)'
        }).then((result) => {
          if (result.isConfirmed) {
          } else if (result.isDismissed) {

          }
        }).catch(() => {
        })
      } else {
        const ToastDelete = this.$swal.mixin({
          toast: true,
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          cancelButtonColor: '#d33'
        })
        ToastDelete.fire({
          icon: 'warning',
          title: 'ยืนยันการเปิดการใช้งาน(ฝ่าย/แผนก จะถูกเปิดการใช้งาน)'
        }).then((result) => {
          if (result.isConfirmed) {
          } else if (result.isDismissed) {

          }
        }).catch(() => {
        })
      }
    }
  }
}
</script>
