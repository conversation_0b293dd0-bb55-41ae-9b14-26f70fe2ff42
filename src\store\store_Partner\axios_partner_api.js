import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async ListVenderPR (data) {
    try {
      var response = await axios.get(`${process.env.VUE_APP_API_PR_PORTAL_PRODUCTION}api/external_service/api/v1/show_item_vendor?tax_id=${data}`, {
        headers: { 'client-id': process.env.VUE_APP_PR_PORTAL_CLIENT_ID, 'client-secret': process.env.VUE_APP_PR_PORTAL_CLIENT_SECRET }
      })
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPartner (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/list_partner_sellershop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailPartner (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/detail_partner_sellershop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchCompany (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/search_company`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreatePartner (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/create_partner_for_sellershop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailSettingPartner (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/detail_setting_partner_sellershop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SettingPartner (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/setting_partner_for_sellershop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // List Main Documents Partner
  async documentPartnerList (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_main_documents_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Set Main Documents Partner
  async setDocumentPartner (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/set_main_documents_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getTokenCustomerName (data) {
    // const auth = GetToken()
    try {
      var response = await axios.post('http://203.154.202.141:5012/api/auth/login', data)
      return response
    } catch (error) {
      return error.response.message
    }
  }

}
