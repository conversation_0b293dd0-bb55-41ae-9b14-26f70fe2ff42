<template>
  <v-container grid-list-xs>
    <v-row dense>
      <v-col cols="12">
        <AffiliateConsent :title="title" :detail="detail" v-if="isBuyer === '0'"/>
        <ProductAffiliate v-if="isBuyer === '1'"/>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    AffiliateConsent: () => import('@/components/UserProfile/Affiliate/consentAffiliate'),
    ProductAffiliate: () => import('@/components/UserProfile/Affiliate/showShopSellerAffiliate')
  },
  data () {
    return {
      checkLogin: false,
      isBuyer: '',
      title: '',
      detail: ''
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('GetConsent', this.getConsent)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('GetConsent')
    })
  },
  created () {
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('oneData') !== null) {
      this.checkLogin = true
      this.getConsent()
    } else {
      this.checkLogin = false
    }
  },
  methods: {
    async getConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var res = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      this.title = res.data.title
      this.detail = res.data.consent_text
      this.isBuyer = res.isBuyer
      this.$store.commit('closeLoader')
    }
  }
}
</script>
