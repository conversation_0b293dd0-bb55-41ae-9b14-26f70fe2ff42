<template>
  <div>
    <!-- Website -->
    <div style="background: #F6F6F6;" v-if="!MobileSize && !IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center">
          <v-col cols="6" md="12" align="center" class="my-16">
            <v-form ref="formRegis" :lazy-validation="lazy">
              <v-card width="620px" height="550px" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
                <v-card-text>
                  <v-container>
                    <v-row dense justify="center" align-content="center" class="mt-2 mb-8">
                      <v-img :src="require('@/assets/new_logo_panit.png')" max-height="117" max-width="123" contain/>
                    </v-row>
                    <v-row dense justify="center" align-content="center" class="mt-6 mb-8">
                      <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #000000;">ลงทะเบียน หมอพร้อม Commerce </span>
                    </v-row>
                    <v-row no-gutters dense class="justify-space-between mx-6 pt-8">
                      <v-col cols="5" >
                        <v-card @click="Register()" width="293px" style="background: #F1F6F9; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2); border-radius: 16px;">
                          <div class="pb-10">
                            <v-img class="pt-15 "  src="@/assets/Group1000000948.png" max-height="117" max-width="123" contain></v-img>
                            <h2 class="mt-4" style="color:#27AB9C">ผู้ขาย</h2>
                            <h5>ลงทะเบียนผู้ขายและเปิดร้านค้ากับเรา</h5>
                          </div>
                        </v-card>
                      </v-col>
                      <v-col cols="5" >
                        <v-card width="293px" @click="Register()" style="background: #F1F6F9; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2); border-radius: 16px;">
                          <div class="pb-10">
                            <v-img class="pt-15 " src="@/assets/Group1000000946.png" max-height="117" max-width="123" contain></v-img>
                            <h2 class="mt-4" style="color:#27AB9C">ผู้ซื้อ</h2>
                            <h5>ลงทะเบียนผู้ซื้อและซื้อสินค้ากับเรา</h5>
                          </div>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card-text>
              </v-card>
            </v-form>
          </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- IPAD -->
    <div style="background: #F6F6F6;" v-if="!MobileSize && IpadSize">
      <v-container>
        <v-row dense justify="center" align-content="center" class="my-16">
          <v-card width="60%" height="480px" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
            <v-card-text>
              <v-container>
                <v-row dense justify="center" align-content="center" class="mt-0 mb-4">
                  <v-img :src="require('@/assets/new_logo_panit.png')" max-height="117" max-width="123" contain/>
                </v-row>
                <v-row dense justify="center" align-content="center" class="mt-6 mb-8">
                  <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #000000;">ลงทะเบียน หมอพร้อม Commerce </span>
                </v-row>
                <v-row no-gutters dense class="justify-space-between mx-6 ">
                  <v-col cols="5" >
                    <v-card @click="Register()" width="293px" align="center" style="background: #F1F6F9; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2); border-radius: 16px;">
                      <div class="pb-10">
                       <v-img class="pt-10 "  src="@/assets/Group1000000948.png" max-height="100" max-width="90" contain></v-img>
                        <h2 class="mt-4" style="color:#27AB9C">ผู้ขาย</h2>
                        <h6 style="font-size: 9px">ลงทะเบียนผู้ขายและเปิดร้านค้ากับเรา</h6>
                     </div>
                    </v-card>
                  </v-col>
                  <v-col cols="5" >
                    <v-card width="293px" @click="Register()" align="center" style="background: #F1F6F9; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2); border-radius: 16px;">
                      <div class="pb-10">
                       <v-img class="pt-10 " src="@/assets/Group1000000946.png" max-height="100" max-width="90" contain></v-img>
                       <h2 class="mt-4" style="color:#27AB9C">ผู้ซื้อ</h2>
                       <h6>ลงทะเบียนผู้ซื้อและซื้อสินค้ากับเรา</h6>
                      </div>
                    </v-card>
                  </v-col>
                </v-row>
              </v-container>
            </v-card-text>
          </v-card>
        </v-row>
      </v-container>
    </div>
    <!-- App -->
    <div  style="background: #F6F6F6;" v-if="MobileSize">
      <v-container class="my-6">
        <v-row dense justify="center" align-content="center">
          <v-col cols="12" md="12">
            <v-card width="343px" height="477px" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
              <v-card-text>
                <v-container>
                  <v-row dense justify="center" align-content="center" class="mt-0 mb-4">
                    <v-img :src="require('@/assets/new_logo_panit.png')" max-height="117" max-width="123" contain/>
                  </v-row>
                  <v-row dense justify="center" align-content="center" class="mt-6 mb-8">
                    <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #000000;">ลงทะเบียน หมอพร้อม Commerce</span>
                  </v-row>
                 <v-row no-gutters dense class="justify-space-between  ">
                  <v-col cols="6"  >
                    <v-card @click="Register()" width="293px" align="center" style="background: #F1F6F9; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2); border-radius: 16px;">
                      <div class="pb-10">
                       <v-img class="pt-10 "  src="@/assets/Group1000000948.png" max-height="100" max-width="90" contain></v-img>
                        <h3 class="mt-4" style="color:#27AB9C">ผู้ขาย</h3>
                        <h6 style="font-size:9px">ลงทะเบียนผู้ขายและเปิดร้านค้ากับเรา</h6>
                     </div>
                    </v-card>
                  </v-col>
                  <v-col cols="6" class="pl-1">
                    <v-card width="293px" @click="Register()" align="center" style="background: #F1F6F9; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.2); border-radius: 16px;">
                      <div class="pb-10">
                       <v-img class="pt-10 " src="@/assets/Group1000000946.png" max-height="100" max-width="90" contain></v-img>
                       <h3 class="mt-4" style="color:#27AB9C">ผู้ซื้อ</h3>
                       <h6 style="font-size:9px">ลงทะเบียนผู้ซื้อและซื้อสินค้ากับเรา</h6>
                      </div>
                    </v-card>
                  </v-col>
                </v-row>
                </v-container>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-container>
    </div>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      telephone: '',
      checkConsent: false,
      lazy: false,
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ]
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
  },
  methods: {
    Register () {
      this.$router.push({ path: '/Registerbuyer' }).catch(() => {})
    },
    async confirmOTP () {
      if (this.$refs.formRegis.validate(true)) {
        this.$store.commit('openLoader')
        var data = {
          mobile_no: this.telephone
        }
        await this.$store.dispatch('actionsGetOTP', data)
        var res = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
        // console.log(res)
        if (res.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          var dataOTP = {
            otp: res.data.otp,
            ref_code: res.data.ref_code,
            mobileNo: this.telephone
          }
          localStorage.setItem('OTPData', Encode.encode(dataOTP))
          // console.log(dataOTP)
          this.$router.replace({ path: '/otp' }).catch(() => {})
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', title: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
        }
      }
    }
  }
}
</script>

<style scoped>
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
</style>
