import AxiosSalesOrder from './axios_sale_order_api'

const ModuleSaleOrder = {
  state: {
    // List Sales
    stateListSales: [],
    stateCreateTierSale: [],
    stateGetListTierSale: [],
    stateGetDetailTierSale: [],
    stateEditTierSale: [],
    stateDeleteTierSale: [],
    stateCheckTierSale: [],
    // List Customer of Sales
    stateListCustomerOfSales: [],
    // Get All User In Shop
    stateGetAllUserShop: [],
    // Create Sale Shop
    stateCreateSaleShop: [],
    // Edit Sale Shop
    stateEditSaleShop: [],
    // Delete Sale Shop
    stateDeleteSaleShop: [],
    // Delete Customer
    stateDeleteCustomer: [],
    // Edit Customer
    stateEditCustomer: [],
    // เส้นใหม่ของน้องญา หน้ารายการอนุมัติฝ่ายขาย
    stateListPurchaserSale: [],
    stateSetPurchaserSale: [],
    stateEditPurchaserSale: [],
    stateListUserForSelect: [],
    stateListUserPurchaserForSelect: [],
    stateListPositionSale: [],
    stateDeletePurchaserSale: [],
    stateUpdateCustomerAddress: [],
    stateGetListPositionSalesOrder: [],
    stateCreateListApproverSalesOrder: [],
    stateCheckListApproverSalesOrder: [],
    stateImportCustomer: [],
    stateGetDetailCustomer: [],
    stateListApproveSale: [],
    stateApproveSale: [],
    stateGetEditApproverSalesOrder: [],
    stateGetDetailApproverSalesOrder: [],
    stateDeleteApproverSalesOrder: [],
    stateListQTSale: [],
    stateDetailQTSale: [],
    stateApproveQTSale: [],
    stateListOrderSale: [],
    stateDetailOrderSale: [],
    stateDeleteCustomerAddress: [],
    stateDeleteCustomerInvoiceAddress: [],
    stateDetailQTApproveSale: [],
    stateUploadSlip: [],
    statePayCashPayment: [],
    stateCreateMobiCashPayment: [],
    stateGetDetailDueDateSaleOrder: [],
    stateGetDetailCreditTerm: [],
    stateCreateOrderCreditTerm: [],
    stateGetDetailEditCustomer: [],
    stateEditDetailCustomer: [],
    stateGetQrCodePayment: [],
    stateListOrderCustomer: [],
    statePointListCustomer: [],
    stateListCustomerTypeOption: [],
    statePointTransfer: [],
    stateCreateSODocument: [],
    stateUploadDocumentSO: [],
    stateAttachDocumentSO: [],
    stateSendEmailSO: [],
    stateGetSellerShopWorkflowID: [],
    stateInsertSellerShopWorkflowID: [],
    stateCloneOrderB2B: []
  },
  mutations: {
    mutationsListSales (state, data) {
      state.stateListSales = data
    },
    mutationsListCustomerOfSales (state, data) {
      state.stateListCustomerOfSales = data
    },
    mutationsCreateTierSale (state, data) {
      state.stateCreateTierSale = data
    },
    mutationsGetListTierSale (state, data) {
      state.stateGetListTierSale = data
    },
    mutationsGetDetailTierSale (state, data) {
      state.stateGetDetailTierSale = data
    },
    mutationsEditTierSale (state, data) {
      state.stateEditTierSale = data
    },
    mutationsDeleteTierSale (state, data) {
      state.stateDeleteTierSale = data
    },
    mutationsCheckTierSale (state, data) {
      state.stateCheckTierSale = data
    },
    mutationsGetAllUserShop (state, data) {
      state.stateGetAllUserShop = data
    },
    mutationsCreateSaleShop (state, data) {
      state.stateCreateSaleShop = data
    },
    mutationsEditSaleShop (state, data) {
      state.stateEditSaleShop = data
    },
    mutationsEditCustomer (state, data) {
      state.stateEditCustomer = data
    },
    mutationsUpdateCustomerAddress (state, data) {
      state.stateUpdateCustomerAddress = data
    },
    mutationsDeleteSaleShop (state, data) {
      state.stateDeleteSaleShop = data
    },
    mutationsDeleteCustomer (state, data) {
      state.stateDeleteCustomer = data
    },
    // เส้นใหม่ของน้องญา หน้ารายการอนุมัติฝ่ายขาย
    mutationsListPurchaserSale (state, data) {
      state.stateListPurchaserSale = data
    },
    mutationsSetPurchaserSale (state, data) {
      state.stateSetPurchaserSale = data
    },
    mutationsEditPurchaserSale (state, data) {
      state.stateEditPurchaserSale = data
    },
    mutationsListUserForSelect (state, data) {
      state.stateListUserForSelect = data
    },
    mutationsListUserPurchaserForSelect (state, data) {
      state.stateListUserPurchaserForSelect = data
    },
    mutationsListPositionSale (state, data) {
      state.stateListPositionSale = data
    },
    mutationsDeletePurchaserSale (state, data) {
      state.stateDeletePurchaserSale = data
    },
    mutationsGetListPositionSalesOrder (state, data) {
      state.stateGetListPositionSalesOrder = data
    },
    mutationsCreateListApproverSalesOrder (state, data) {
      state.stateCreateListApproverSalesOrder = data
    },
    mutationsCheckListApproverSalesOrder (state, data) {
      state.stateCheckListApproverSalesOrder = data
    },
    mutationsImportCustomer (state, data) {
      state.stateImportCustomer = data
    },
    mutationsGetDetailCustomer (state, data) {
      state.stateGetDetailCustomer = data
    },
    mutationsListApproveSale (state, data) {
      state.stateListApproveSale = data
    },
    mutationsApproveSale (state, data) {
      state.stateApproveSale = data
    },
    mutationsGetEditApproverSalesOrder (state, data) {
      state.stateGetEditApproverSalesOrder = data
    },
    mutationsGetDetailApproverSalesOrder (state, data) {
      state.stateGetDetailApproverSalesOrder = data
    },
    mutationsDeleteApproverSalesOrder (state, data) {
      state.stateDeleteApproverSalesOrder = data
    },
    mutationsListQTSale (state, data) {
      state.stateListQTSale = data
    },
    mutationsDetailQTSale (state, data) {
      state.stateDetailQTSale = data
    },
    mutationsApproveQTSale (state, data) {
      state.stateApproveQTSale = data
    },
    mutationsListOrderSale (state, data) {
      state.stateListOrderSale = data
    },
    mutationsDetailOrderSale (state, data) {
      state.stateDetailOrderSale = data
    },
    mutationsDeleteCustomerAddress (state, data) {
      state.stateDeleteCustomerAddress = data
    },
    mutationsDeleteCustomerInvoiceAddress (state, data) {
      state.stateDeleteCustomerInvoiceAddress = data
    },
    mutationsDetailQTApproveSale (state, data) {
      state.stateDetailQTApproveSale = data
    },
    mutationsUploadSlip (state, data) {
      state.stateUploadSlip = data
    },
    mutationsPayCashPayment (state, data) {
      state.statePayCashPayment = data
    },
    mutationsCreateMobiCashPayment (state, data) {
      state.stateCreateMobiCashPayment = data
    },
    mutationsGetDetailDueDateSaleOrder (state, data) {
      state.stateGetDetailDueDateSaleOrder = data
    },
    mutationsGetDetailCreditTerm (state, data) {
      state.stateGetDetailCreditTerm = data
    },
    mutationsCreateOrderCreditTerm (state, data) {
      state.stateCreateOrderCreditTerm = data
    },
    mutationsGetDetailEditCustomer (state, data) {
      state.stateGetDetailEditCustomer = data
    },
    mutationsEditDetailCustomer (state, data) {
      state.stateEditDetailCustomer = data
    },
    mutationsGetQrCodePayment (state, data) {
      state.stateGetQrCodePayment = data
    },
    mutationsListOrderCustomer (state, data) {
      state.stateListOrderCustomer = data
    },
    mutationsPointListCustomer (state, data) {
      state.statePointListCustomer = data
    },
    mutationsPointTransfer (state, data) {
      state.statePointTransfer = data
    },
    mutationsListCustomerTypeOption (state, data) {
      state.stateListCustomerTypeOption = data
    },
    mutationsCreateSODocument (state, data) {
      state.stateCreateSODocument = data
    },
    mutationsUploadDocumentSO (state, data) {
      state.stateUploadDocumentSO = data
    },
    mutationsAttachDocumentSO (state, data) {
      state.stateAttachDocumentSO = data
    },
    mutationsSendEmailSO (state, data) {
      state.stateSendEmailSO = data
    },
    mutationsGetSellerShopWorkflowID (state, data) {
      state.stateGetSellerShopWorkflowID = data
    },
    mutationsInsertSellerShopWorkflowID (state, data) {
      state.stateInsertSellerShopWorkflowID = data
    },
    mutationsCloneOrderB2B (state, data) {
      state.stateCloneOrderB2B = data
    }
  },
  actions: {
    async actionsListSales (context, access) {
      const response = await AxiosSalesOrder.ListSales(access)
      await context.commit('mutationsListSales', response)
    },
    async actionsListCustomerOfSales (context, access) {
      const response = await AxiosSalesOrder.ListCustomerOfSales(access)
      await context.commit('mutationsListCustomerOfSales', response)
    },
    async actionsCreateTierSale (context, access) {
      const response = await AxiosSalesOrder.CreateTierSale(access)
      await context.commit('mutationsCreateTierSale', response)
    },
    async actionsGetListTierSale (context, access) {
      const response = await AxiosSalesOrder.GetListTierSale(access)
      await context.commit('mutationsGetListTierSale', response)
    },
    async actionsGetDetailTierSale (context, access) {
      const response = await AxiosSalesOrder.GetDetailTierSale(access)
      await context.commit('mutationsGetDetailTierSale', response)
    },
    async actionsEditTierSale (context, access) {
      const response = await AxiosSalesOrder.EditTierSale(access)
      await context.commit('mutationsEditTierSale', response)
    },
    async actionsDeleteTierSale (context, access) {
      const response = await AxiosSalesOrder.DeleteTierSale(access)
      await context.commit('mutationsDeleteTierSale', response)
    },
    async actionsCheckTierSale (context, access) {
      const response = await AxiosSalesOrder.CheckTierSale(access)
      await context.commit('mutationsCheckTierSale', response)
    },
    async actionsGetAllUserShop (context, access) {
      const response = await AxiosSalesOrder.GetAllUserShop(access)
      await context.commit('mutationsGetAllUserShop', response)
    },
    async actionsCreateSaleShop (context, access) {
      const response = await AxiosSalesOrder.CreateSaleShop(access)
      await context.commit('mutationsCreateSaleShop', response)
    },
    async actionsEditSaleShop (context, access) {
      const response = await AxiosSalesOrder.EditSaleShop(access)
      await context.commit('mutationsEditSaleShop', response)
    },
    async actionsDeleteSaleShop (context, access) {
      const response = await AxiosSalesOrder.DeleteSaleShop(access)
      await context.commit('mutationsDeleteSaleShop', response)
    },
    async actionsEditCustomer (context, access) {
      const response = await AxiosSalesOrder.EditCustomer(access)
      await context.commit('mutationsEditCustomer', response)
    },
    async actionsUpdateCustomerAddress (context, access) {
      const response = await AxiosSalesOrder.UpdateCustomerAddress(access)
      await context.commit('mutationsUpdateCustomerAddress', response)
    },
    async actionsDeleteCustomer (context, access) {
      const response = await AxiosSalesOrder.DeleteCustomer(access)
      await context.commit('mutationsDeleteCustomer', response)
    },
    async actionsListPurchaserSale (context, access) {
      const response = await AxiosSalesOrder.ListPurchaserSale(access)
      await context.commit('mutationsListPurchaserSale', response)
    },
    async actionsSetPurchaserSale (context, access) {
      const response = await AxiosSalesOrder.SetPurchaserSale(access)
      await context.commit('mutationsSetPurchaserSale', response)
    },
    async actionsEditPurchaserSale (context, access) {
      const response = await AxiosSalesOrder.EditPurchaserSale(access)
      await context.commit('mutationsEditPurchaserSale', response)
    },
    async actionsListUserForSelect (context, access) {
      const response = await AxiosSalesOrder.ListUserForSelect(access)
      await context.commit('mutationsListUserForSelect', response)
    },
    async actionsListUserPurchaserForSelect (context, access) {
      const response = await AxiosSalesOrder.ListUserPurchaserForSelect(access)
      await context.commit('mutationsListUserPurchaserForSelect', response)
    },
    async actionsListPositionSale (context, access) {
      const response = await AxiosSalesOrder.ListPositionSale(access)
      await context.commit('mutationsListPositionSale', response)
    },
    async actionsDeletePurchaserSale (context, access) {
      const response = await AxiosSalesOrder.DeletePurchaserSale(access)
      await context.commit('mutationsDeletePurchaserSale', response)
    },
    async actionsGetListPositionSalesOrder (context, access) {
      const response = await AxiosSalesOrder.GetListPositionSalesOrder(access)
      await context.commit('mutationsGetListPositionSalesOrder', response)
    },
    async actionsCreateListApproverSalesOrder (context, access) {
      const response = await AxiosSalesOrder.CreateListApproverSalesOrder(access)
      await context.commit('mutationsCreateListApproverSalesOrder', response)
    },
    async actionsCheckListApproverSalesOrder (context, access) {
      const response = await AxiosSalesOrder.CheckListApproverSalesOrder(access)
      await context.commit('mutationsCheckListApproverSalesOrder', response)
    },
    async actionsImportCustomer (context, access) {
      const response = await AxiosSalesOrder.ImportCustomer(access)
      await context.commit('mutationsImportCustomer', response)
    },
    async actionsGetDetailCustomer (context, access) {
      const response = await AxiosSalesOrder.GetDetailCustomer(access)
      await context.commit('mutationsGetDetailCustomer', response)
    },
    async actionsListApproveSale (context, access) {
      const response = await AxiosSalesOrder.ListApproveSale(access)
      await context.commit('mutationsListApproveSale', response)
    },
    async actionsApproveSale (context, access) {
      const response = await AxiosSalesOrder.ApproveSale(access)
      await context.commit('mutationsApproveSale', response)
    },
    async actionsGetEditApproverSalesOrder (context, access) {
      const response = await AxiosSalesOrder.GetEditApproverSalesOrder(access)
      await context.commit('mutationsGetEditApproverSalesOrder', response)
    },
    async actionsGetDetailApproverSalesOrder (context, access) {
      const response = await AxiosSalesOrder.GetDetailApproverSalesOrder(access)
      await context.commit('mutationsGetDetailApproverSalesOrder', response)
    },
    async actionsDeleteApproverSalesOrder (context, access) {
      const response = await AxiosSalesOrder.DeleteApproverSalesOrder(access)
      await context.commit('mutationsDeleteApproverSalesOrder', response)
    },
    async actionsListQTSale (context, access) {
      const response = await AxiosSalesOrder.ListQTSale(access)
      await context.commit('mutationsListQTSale', response)
    },
    async actionsDetailQTSale (context, access) {
      const response = await AxiosSalesOrder.DetailQTSale(access)
      await context.commit('mutationsDetailQTSale', response)
    },
    async actionsApproveQTSale (context, access) {
      const response = await AxiosSalesOrder.ApproveQTSale(access)
      await context.commit('mutationsApproveQTSale', response)
    },
    async actionsListOrderSale (context, access) {
      const response = await AxiosSalesOrder.ListOrderSale(access)
      await context.commit('mutationsListOrderSale', response)
    },
    async actionsDetailOrderSale (context, access) {
      const response = await AxiosSalesOrder.DetailOrderSale(access)
      await context.commit('mutationsDetailOrderSale', response)
    },
    async actionsDeleteCustomerAddress (context, access) {
      const response = await AxiosSalesOrder.DeleteCustomerAddress(access)
      await context.commit('mutationsDeleteCustomerAddress', response)
    },
    async actionsDeleteCustomerInvoiceAddress (context, access) {
      const response = await AxiosSalesOrder.DeleteCustomerInvoiceAddress(access)
      await context.commit('mutationsDeleteCustomerInvoiceAddress', response)
    },
    async actionsDetailQTApproveSale (context, access) {
      const response = await AxiosSalesOrder.DetailQTApproveSale(access)
      await context.commit('mutationsDetailQTApproveSale', response)
    },
    async actionsUploadSlip (context, access) {
      const response = await AxiosSalesOrder.UploadSlip(access)
      await context.commit('mutationsUploadSlip', response)
    },
    async actionsPayCashPayment (context, access) {
      const response = await AxiosSalesOrder.PayCashPayment(access)
      await context.commit('mutationsPayCashPayment', response)
    },
    async actionsCreateMobiCashPayment (context, access) {
      const response = await AxiosSalesOrder.CreateMobiCashPayment(access)
      await context.commit('mutationsCreateMobiCashPayment', response)
    },
    async actionsGetDetailDueDateSaleOrder (context, access) {
      const response = await AxiosSalesOrder.GetDetailDueDateSaleOrder(access)
      await context.commit('mutationsGetDetailDueDateSaleOrder', response)
    },
    async actionsGetDetailCreditTerm (context, access) {
      const response = await AxiosSalesOrder.GetDetailCreditTerm(access)
      await context.commit('mutationsGetDetailCreditTerm', response)
    },
    async actionsCreateOrderCreditTerm (context, access) {
      const response = await AxiosSalesOrder.CreateOrderCreditTerm(access)
      await context.commit('mutationsCreateOrderCreditTerm', response)
    },
    async actionsGetDetailEditCustomer (context, access) {
      const response = await AxiosSalesOrder.GetDetailEditCustomer(access)
      await context.commit('mutationsGetDetailEditCustomer', response)
    },
    async actionsEditDetailCustomer (context, access) {
      const response = await AxiosSalesOrder.EditDetailCustomer(access)
      await context.commit('mutationsEditDetailCustomer', response)
    },
    async actionsGetQrCodePayment (context, access) {
      const response = await AxiosSalesOrder.GetQrCodePayment(access)
      await context.commit('mutationsGetQrCodePayment', response)
    },
    async actionsListOrderCustomer (context, access) {
      const response = await AxiosSalesOrder.ListOrderCustomer(access)
      await context.commit('mutationsListOrderCustomer', response)
    },
    async actionsPointListCustomer (context, access) {
      const response = await AxiosSalesOrder.PointListCustomer(access)
      await context.commit('mutationsPointListCustomer', response)
    },
    async actionsPointTransfer (context, access) {
      const response = await AxiosSalesOrder.PointTransfer(access)
      await context.commit('mutationsPointTransfer', response)
    },
    async actionsListCustomerTypeOption (context, access) {
      const response = await AxiosSalesOrder.ListCustomerTypeOption(access)
      await context.commit('mutationsListCustomerTypeOption', response)
    },
    async actionsCreateSODocument (context, access) {
      const response = await AxiosSalesOrder.CreateSODocument(access)
      await context.commit('mutationsCreateSODocument', response)
    },
    async actionsUploadDocumentSO (context, access) {
      const response = await AxiosSalesOrder.UploadDocumentSO(access)
      await context.commit('mutationsUploadDocumentSO', response)
    },
    async actionsAttachDocumentSO (context, access) {
      const response = await AxiosSalesOrder.AttachDocumentSO(access)
      await context.commit('mutationsAttachDocumentSO', response)
    },
    async actionsSendEmailSO (context, access) {
      const response = await AxiosSalesOrder.SendEmailSO(access)
      await context.commit('mutationsSendEmailSO', response)
    },
    async actionsGetSellerShopWorkflowID (context, access) {
      const response = await AxiosSalesOrder.GetSellerShopWorkflowID(access)
      await context.commit('mutationsGetSellerShopWorkflowID', response)
    },
    async actionsInsertSellerShopWorkflowID (context, access) {
      const response = await AxiosSalesOrder.InsertSellerShopWorkflowID(access)
      await context.commit('mutationsInsertSellerShopWorkflowID', response)
    },
    async actionsCloneOrderB2B (context, access) {
      const response = await AxiosSalesOrder.CloneOrderB2B(access)
      await context.commit('mutationsCloneOrderB2B', response)
    }
  }
}

export default ModuleSaleOrder
