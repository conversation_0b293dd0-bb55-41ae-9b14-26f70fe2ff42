<template>
  <div class="text-center">
    <v-dialog v-model="ModalTaxAddressLocal" width="800" persistent>
      <v-card>
        <v-toolbar flat color="#E6F5F3">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobil' : ''"><b>เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="ModalTaxAddressLocal = !ModalTaxAddressLocal" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <!-- Desktop -->
          <v-card-text v-show="!MobileSize">
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="12">
                  <v-radio-group v-model="taxRoles" row>
                    <v-radio color="#27AB9C" label="ผู้ใช้ทั่วไป" value="GENERAL"></v-radio>
                    <v-radio color="#27AB9C" label="นิติบุคคล" value="INDIVIDUAL"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">ชื่อที่ใช้ในการออกใบกำกับภาษี</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ชื่อที่ใช้ในการออกใบกำกับภาษี" outlined dense v-model="fullname" :rules="Rules.fullname"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">เลขประจำตัวผู้เสียภาษี</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" maxlength="13" placeholder="เลขประจำตัวผู้เสียภาษี" outlined dense v-model="taxID" :rules="Rules.taxID" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">ที่อยู่ในการออกใบกำกับภาษี</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ที่อยู่ในการออกใบกำกับภาษี" outlined dense v-model="addressDetail"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <span class="labelInputSize">แขวง/ตำบล</span>
                </v-col>
                <v-col cols="6">
                  <span class="labelInputSize">เขต/อำเภอ</span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-subdistrict :rules="Rules.empty" label="" class="input_text-thai-address" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                </v-col>
                <v-col cols="6">
                  <addressinput-district label="" v-model="district" class="input_text-thai-address" placeholder="ระบุเขต/อำเภอ" />
                </v-col>
                <v-col cols="6">
                  <span class="labelInputSize">จังหวัด</span>
                </v-col>
                <v-col cols="6">
                  <span class="labelInputSize">รหัสไปรษณีย์</span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-province label="" v-model="province" placeholder="ระบุจังหวัด" />
                </v-col>
                <v-col cols="6">
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <!-- Mobile -->
          <v-card-text v-show="MobileSize">
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="12">
                  <v-radio-group v-model="taxRoles" row>
                    <v-radio color="#27AB9C" label="ผู้ใช้ทั่วไป" value="GENERAL"></v-radio>
                    <v-radio color="#27AB9C" label="นิติบุคคล" value="INDIVIDUAL"></v-radio>
                  </v-radio-group>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">ชื่อที่ใช้ในการออกใบกำกับภาษี</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ชื่อที่ใช้ในการออกใบกำกับภาษี" outlined dense v-model="fullname" :rules="Rules.fullname" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">เลขประจำตัวผู้เสียภาษี</span>
                </v-col>
                <v-col cols="12" class="">
                  <v-text-field class="input_text" maxlength="13" placeholder="เลขประจำตัวผู้เสียภาษี" outlined dense v-model="taxID" :rules="Rules.taxID" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">ที่อยู่ในการออกใบกำกับภาษี</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ที่อยู่ในการออกใบกำกับภาษี" outlined dense v-model="addressDetail"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">แขวง/ตำบล</span>
                </v-col>
                <v-col cols="12">
                  <addressinput-subdistrict :rules="Rules.empty" label="" v-model="subdistrict" class="input_text-thai-address" placeholder="ระบุแขวง/ตำบล"/>
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">เขต/อำเภอ</span>
                </v-col>
                <v-col cols="12">
                  <addressinput-district label="" v-model="district" class="input_text-thai-address" placeholder="ระบุเขต/อำเภอ" />
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">จังหวัด</span>
                </v-col>
                <v-col cols="12">
                  <addressinput-province label="" v-model="province" class="input_text-thai-address" placeholder="ระบุจังหวัด" />
                </v-col>
                <v-col cols="12">
                  <span class="labelInputSize">รหัสไปรษณีย์</span>
                </v-col>
                <v-col cols="12">
                  <addressinput-zipcode label="" v-model="zipcode" class="input_text-thai-address" placeholder="ระบุรหัสไปรษณีย์" />
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="Cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="CreateTaxinvoiceAddress()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      data: [],
      lazy: false,
      fullname: '',
      taxID: '',
      addressDetail: '',
      ModalTaxAddressLocal: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      taxRoles: 'GENERAL',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        fullname: [
          v => !!v || 'กรุณากรอกชื่อและนามสกุลผู้รับ'
        ],
        taxID: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีผู้รับ'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    subdistrict (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
        } else {
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
        } else {
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
        } else {
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
        } else {
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  methods: {
    open () {
      this.ModalTaxAddressLocal = true
    },
    Cancel () {
      this.ModalTaxAddressLocal = false
      // อย่าลืม clear data
      // localStorage.removeItem('AddressData')
      // this.$router.push({ path: '/UPS/shoppingcart' })
    },
    async CreateTaxinvoiceAddress () {
      if (this.$refs.FormAddress.validate(true)) {
        var data = {
          token: '',
          role: 'non-login',
          user_type: this.taxRoles,
          name: this.fullname,
          address: this.addressDetail,
          postal_code: this.zipcode,
          province: this.province,
          district: this.district,
          sub_district: this.subdistrict,
          tax_id: this.taxID,
          seller_shop_id: '',
          seller_shop_id_data: ''
        }
        await this.$store.dispatch('actionUPSCreatedAddressTaxinvoice', data)
        var res = this.$store.state.ModuleUser.stateUPSCreatedAddressTaxinvoice
        if (res.message.ok === 'y') {
          var checkdata = {
            role: 'non-login',
            name: this.fullname,
            invoice_id: res.message.data.invoice_id,
            address: this.addressDetail,
            postal_code: this.zipcode,
            province: this.province,
            district: this.district,
            sub_district: this.subdistrict,
            tax_id: this.taxID
          }
          this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
          this.$EventBus.$emit('GetTaxAddressLocal', checkdata)
          this.ModalTaxAddressLocal = false
        } else if (res.message.ok === 'n') {
          this.$swal.fire({ icon: 'warning', title: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
          this.ModalTaxAddressLocal = false
        }
        // if (res.message === 'Create user address success') {
        //   this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
        //   this.dialog = false
        //   localStorage.removeItem('AddressData')
        // } else if (res.message === 'Parameter is missing') {
        //   this.$swal.fire({ icon: 'warning', title: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        // }
      } else {
        this.$swal.fire({ icon: 'warning', title: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 2500 })
      }
    }
  }
}
</script>

<style>
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
.labelInputSize {
  font-size: 16px;
}
.input_text-thai-address {
  height: 60px;
}
.title-mobil {
  font-size: 18px;
}
</style>
