<template lang="html">
  <div>
    <DesignShopUI/>
  </div>
</template>

<script>
export default {
  components: {
    DesignShopUI: () => import('@/components/Shop/MyShop/DesignShopUI')
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/designShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/designShop' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  }
}
</script>

<style lang="css" scoped>
</style>
