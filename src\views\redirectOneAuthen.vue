<template>
  <div></div>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  async created () {
    if (this.$router.currentRoute.query.shared_token !== undefined) {
      var data = {
        code: this.$router.currentRoute.query.shared_token
      }
      const response = await this.axios.post(`${process.env.VUE_APP_CALLBACK}`, data)
      if (response.data.code !== 500) {
        if (response.data.data.access_token !== '') {
          if (localStorage.getItem('oneData') !== null) {
            // console.log('เข้าบนมี OneData')
            var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            onedata.user = response.data.data
            localStorage.setItem('oneData', Encode.encode(onedata))
          } else {
            // console.log('เข้าล่างมี onedataShareToken')
            var onedataShareToken = {}
            onedataShareToken.CurrentPath = '/'
            onedataShareToken.user = response.data.data
            localStorage.setItem('oneData', Encode.encode(onedataShareToken))
          }
          //   var PathRedirect = ''
          //   if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
          //     PathRedirect = sessionStorage.getItem('pathRedirect')
          //     if (PathRedirect === '/Login' || PathRedirect === '/Register') {
          //       PathRedirect = '/'
          //     }
          //   } else {
          //     PathRedirect = '/'
          //   }
          var dataRole = {
            role: 'ext_buyer'
          }
          localStorage.setItem('roleUser', JSON.stringify(dataRole))
          this.$EventBus.$emit('checkPDPA', response.data.data.one_id)
          var productName = 'PKI Token'
          const nameCleaned = productName.replace(/\s/g, '-')
          var productID = ''
          if (process.env.VUE_APP_SERVER === 'DEV') {
            productID = 2917
          } else if (process.env.VUE_APP_SERVER === 'UAT') {
            productID = 2598
          }
          this.$router.push({ path: '/DetailProduct/' + encodeURIComponent(nameCleaned) + '-' + (productID) }).catch(() => {})
          await this.$store.dispatch('actionsConGetBizDetail')
          var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
          if (responseBiz.message === 'updated data from one id successful.') {
            localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
          }
        } else {
          localStorage.removeItem('oneData')
          window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
        }
      } else {
        localStorage.removeItem('oneData')
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      }
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  }
}
</script>
