<template>
  <v-container class="backgroundSeller">
    <v-row v-if="!MobileSize" class="mx-0">
      <v-col cols="12" md="3" sm="4" xs="12" v-if="!MobileSize">
        <v-card class="mt-6 mb-6" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-home</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 30px;">{{ $t('CreateShop.GoBack') }}</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- จัดการร้านค้า -->
            <div v-if="this.itemsShop[0].items.length !== 0">
              <v-list-group v-for="item in itemsShop" :key="item.key" v-model="item.active" :prepend-icon="item.action"
                no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                    {{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>
                <v-list-item-group v-model="defaultSelect"
                  :mandatory="defaultSelect === 16 ? false : defaultSelect === 17 ? false : defaultSelect === 18 ? false : defaultSelect === 19 ? false : defaultSelect === 20 ? false : defaultSelect === 21 ? false : defaultSelect === 22 ? false : defaultSelect === 23 ? false : defaultSelect === 24 ? false : defaultSelect === 25 ? false : defaultSelect === 26 ? false : defaultSelect === 27 ? false : defaultSelect === 28 ? false : defaultSelect === 29 ? false : defaultSelect === 30 ? false : defaultSelect === 31 ? false : defaultSelect === 32 ? false : defaultSelect === 34 ? false : defaultSelect === 37 ? false : defaultSelect === 38 ? false : defaultSelect === 39  ? false : defaultSelect === 40  ? false : defaultSelect === 41 ? false : defaultSelect === 42 ? false : defaultSelect === 43 ? false : defaultSelect === 44 ? false : defaultSelect === 45 ? false : defaultSelect === 46 ? false : defaultSelect === 47 ? false : defaultSelect === 48 ? false : defaultSelect === 49 ? false : defaultSelect === 50 ? false : true">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- Members -->
            <div v-if="this.itemsMembers[0].items.length !== 0">
              <v-list-group v-for="item in itemsMembers" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectMembers"
                  :mandatory="defaultSelect === 49 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- จัดการบัญชีร้านค้า -->
            <div v-if="this.itemsShopAccount[0].items.length !== 0">
              <v-list-group v-for="item in itemsShopAccount" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectShopAccount"
                  :mandatory="defaultSelect === 41 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- LiveStreaming -->
            <div v-if="this.itemsLiveStreaming[0].items.length !== 0">
              <v-list-group v-for="item in itemsLiveStreaming" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectLiveStreaming"
                  :mandatory="defaultSelect === 42 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ManageTag -->
            <div v-if="this.itemsManageTag[0].items.length !== 0">
              <v-list-group v-for="item in itemsManageTag" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectManageTag"
                  :mandatory="defaultSelect === 43 ? true : defaultSelect === 44 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- บริษัทคู่ค้า -->
            <div v-if="this.itemsCompany[0].items.length !== 0">
              <v-list-group v-for="item in itemsCompany" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="Select"
                  :mandatory="defaultSelect === 16 ? true : defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : defaultSelect === 21 ? true : defaultSelect === 22 ? true : defaultSelect === 23 ? true : defaultSelect === 46 ? true :  defaultSelect === 51 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- Sales Order -->
            <div v-if="this.itemsSalesOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSalesOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSalesOrder"
                  :mandatory="defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : defaultSelect === 21 ? true : defaultSelect === 22 ? true : defaultSelect === 23 ? true : defaultSelect === 24 ? true : defaultSelect === 25 ? true : defaultSelect === 26 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- โปรโม -->
            <div v-if="this.itemsPromotion[0].items.length !== 0">
              <v-list-group v-for="item in itemsPromotion" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectProMo"
                  :mandatory="defaultSelect === 27 ? true : defaultSelect === 28 ? true : defaultSelect === 29 ? true : defaultSelect === 45 ? true : defaultSelect === 48 ? true : defaultSelect === 50 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <div v-if="this.itemsAffiliate[0].items.length !== 0 && (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1' || (this.PositionName === 'เจ้าของนิติบุคคล' || this.PositionName === 'เจ้าของร้าน'))">
              <v-list-group v-for="item in itemsAffiliate" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectAffiliate"
                  :mandatory="defaultSelect === 30 ? true :defaultSelect === 31 ? true : defaultSelect === 32 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <div v-if="this.itemsPartner[0].items.length !== 0 && this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1'">
              <v-list-group v-for="item in itemsPartner" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectPartner"
                  :mandatory="defaultSelect === 34 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <div v-if="this.itemsMarketplace[0].items.length !== 0 && this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1'">
              <v-list-group v-for="item in itemsMarketplace" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="white-space: normal; font-weight: bold; line-height: 30px;" :style="IpadSize ? 'font-size: 16px !important;' : 'font-size: 18px !important;'">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectMarketplace"
                  :mandatory="defaultSelect === 38 ? true :defaultSelect === 39 ? true : defaultSelect === 40 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="line-height: 26px;" :style="IpadSize ? 'font-size: 14px !important;' : 'font-size: 16px !important;'">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9" sm="8" xs="12" class="pl-0 pr-0 mt-3">
        <v-main style="padding: 0px;">
          <v-container>
            <v-row dense class="mt-0" v-if="checkCreateShop">
              <v-col cols="12" md="12" class="mt-0 pt-0">
                <v-img v-if="$i18n.locale === 'th'" src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.png" max-height="468"
                  max-width="100%" contain></v-img>
                <v-img v-else src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShopEN.png" max-height="468"
                  max-width="100%" contain></v-img>
              </v-col>
            </v-row>
            <div
              v-if="this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditPicturesShop' || this.$router.currentRoute.name === 'EditShop'"
              max-height="100%" height="100%" width="100%">
              <router-view></router-view>
            </div>
            <v-card elevation="0" v-else max-height="100%" height="100%" width="100%" style="overflow-y: hidden; border-radius: 8px; border: 1px solid #F2F2F2;">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { createChat } from '@/components/library/CallChatMe/callChatMe'
import { Decode, Encode } from '@/services'
import axios from 'axios'
export default {
  data () {
    return {
      imgLogoShop: '',
      oneData: null,
      myOptions: {
        useKeyboardNavigation: false,
        labels: {
          buttonSkip: 'ข้าม',
          buttonPrevious: 'ย้อนกลับ',
          buttonNext: 'ถัดไป',
          buttonStop: 'เสร็จสิ้น'
        },
        highlight: true
      },
      steps: [
        {
          target: '[data-v-step="0"]',
          content: '<b>เริ่มต้นสำหรับเปิดร้านค้าครั้งแรก!!!</b><br>แถบนี้คือเมนูของร้านค้า โดยคุณสามารถเลือกเมนูเพื่อจัดการร้านค้าของคุณได้',
          params: {
            placement: 'right'
          }
        },
        {
          target: '[data-v-step="1"]',
          content: 'ส่วนนี้จะเป็นส่วนของการแสดงข้อมูลในแต่ละเมนูที่คุณเลือก',
          params: {
            placement: 'left'
          }
        },
        {
          target: '[data-v-step="2"]',
          content: 'ถ้าคุณยังไม่มีสินค้า คุณสามารถเพิ่มสินค้าของคุณได้ 2 แบบ',
          params: {
            placement: 'top'
          }
        },
        {
          target: '[data-v-step="3"]',
          content: 'แบบที่ 1 คุณสามรารถเพิ่มสินค้าโดยการ import สินค้าเข้าระบบ',
          params: {
            placement: 'top'
          }
        },
        {
          target: '[data-v-step="4"]',
          content: 'โดยคุณสามารถ download ไฟล์ excel เพื่อทำการกรอกข้อมูลสินค้าของคุณและทำการเพิ่มเข้าระบบ',
          params: {
            placement: 'top'
          }
        },
        {
          target: '[data-v-step="5"]',
          content: 'แบบที่ 2 คุณสามารถกดเพิ่มสินค้าเพื่อสร้างสินค้าในระบบคุณได้เลย',
          params: {
            placement: 'top'
          }
        }
      ],
      isJV: '',
      pathData: '',
      shopname: '',
      roleUser: '',
      ImgShop: '',
      checkCreateShop: false,
      activeMenu: true,
      dataDetail: '',
      dataBreadcrumb: [
        {
          name: 'หน้าหลัก',
          path: 'selleI'
        },
        {
          name: 'รายการสินค้า',
          path: 'seller'
        }
      ],
      BtnLink: [
        { name: 'รายละเอียดร้านค้า', icon: 'idcard', path: 'shop' },
        { name: 'ออกจากระบบ', icon: 'export', path: '' }
      ],
      defaultSelect: 0,
      Select: 0,
      SelectSalesOrder: 0,
      SelectProMo: 0,
      SelectAffiliate: 0,
      SelectShopAccount: 0,
      SelectPartner: 0,
      SelectMarketplace: 0,
      SelectLiveStreaming: 0,
      SelectManageTag: 0,
      SelectMembers: 0,
      itemsShop: [
        {
          key: 1,
          action: 'mdi-storefront',
          active: true,
          title: 'จัดการร้านค้า',
          items: []
        }
      ],
      itemsCreateShop: [
        {
          key: 1,
          action: 'mdi-storefront',
          active: true,
          title: `${this.$t('CreateShop.ManageShop')}`,
          items: [
            { key: 1, title: 'สร้างร้านค้า', path: 'createShop' }
          ]
        }
      ],
      items: [
        {
          key: 1,
          action: 'mdi-storefront',
          active: true,
          title: 'จัดการร้านค้า',
          items: []
        }
      ],
      itemsMembers: [
        {
          key: 11,
          action: 'mdi-account-box-multiple',
          active: false,
          title: 'จัดการข้อมูลสมาชิก',
          items: []
        }
      ],
      itemsShopAccount: [
        {
          key: 8,
          action: 'mdi-account-cash',
          active: false,
          title: 'จัดการบัญชี',
          items: []
        }
      ],
      itemsLiveStreaming: [
        {
          key: 9,
          action: 'mdi mdi-camera-wireless',
          active: false,
          title: 'จัดการ Live Streaming',
          items: []
        }
      ],
      itemsManageTag: [
        {
          key: 10,
          action: 'mdi mdi-tag',
          active: false,
          title: 'จัดการแท็ก',
          items: []
        }
      ],
      itemsCompany: [
        {
          key: 2,
          action: 'mdi-domain',
          active: false,
          title: 'บริษัทคู่ค้า',
          items: []
        }
      ],
      itemsSalesOrder: [
        {
          key: 3,
          action: 'mdi-briefcase-account',
          active: false,
          title: 'Sales Order',
          items: []
        }
      ],
      itemsPromotion: [
        {
          key: 4,
          action: 'mdi-ticket-percent',
          active: false,
          title: 'จัดการโปรโมชัน',
          items: []
        }
      ],
      itemsAffiliate: [
        {
          key: 5,
          action: 'mdi-chart-line',
          active: false,
          title: 'โปรแกรม Affiliate',
          items: []
        }
      ],
      itemsPartner: [
        {
          key: 6,
          action: 'mdi-account-group',
          active: false,
          title: 'เชื่อมต่อ Partner',
          items: []
        }
      ],
      itemsMarketplace: [
        {
          key: 7,
          action: 'mdi-handshake',
          active: false,
          title: 'Software Marketplace',
          items: []
        }
      ]
    }
  },
  async created () {
    var path
    this.dataRole()
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('resetSearch')
    this.$EventBus.$on('AuthorityUsers', this.AuthorityUsers)
    if (localStorage.getItem('pathShopSale') !== null) {
      path = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
      this.pathData = path
    } else {
      path = '/'
      this.pathData = path
    }
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (localStorage.getItem('list_shop_detail') !== null) {
        this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      }
      this.$EventBus.$emit('getPath')
      this.$EventBus.$emit('CheckFooter')
      this.$EventBus.$on('changeNav', this.SelectPath)
      this.$EventBus.$on('ChangeActiveMenu', this.ChangeActiveMenu)
      await this.checkJVShop()
      await this.checkPath2()
    }
    window.addEventListener('scroll', this.closeModal)
  },
  watch: {
    MobileSize (val) {
      if (this.$router.currentRoute.name === 'sellerUI') {
        var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
        if (val === true) {
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
        } else {
          this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
        }
      } else if (this.$router.currentRoute.name === 'manageproductMobileUI' || this.$router.currentRoute.name === 'manageproductUI') {
        var data = JSON.parse(sessionStorage.getItem('statusPageManageProduct'))
        if (val === true && data.statusPage === 'Create') {
          this.$router.push({ path: `/manageproductMobile?Status=Create&ShopID=${data.shopID}` }).catch(() => {})
        } else if (val === false && data.statusPage === 'Create') {
          this.$router.push({ path: `/manageproduct?Status=Create&ShopID=${data.shopID}` }).catch(() => {})
        } else if (val === true && data.statusPage === 'Edit') {
          this.$router.push({ path: `/manageproductMobile?Status=Edit&ShopID=${data.shopID}&ProductID=${data.productID}` }).catch(() => {})
        } else if (val === false && data.statusPage === 'Edit') {
          this.$router.push({ path: `/manageproduct?Status=Edit&ShopID=${data.shopID}&ProductID=${data.productID}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'POSellerDetailUI' || this.$router.currentRoute.name === 'posellerDetailMobileUI') {
        var orderData = JSON.parse(Decode.decode(localStorage.getItem('orderNumberSeller')))
        if (val === true) {
          this.$router.push({ path: `/posellerDetailMobile?orderNumber=${orderData.order_number}&tranNumber=${orderData.payment_transaction_number}&termNumber=${orderData.num_credit_term}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/POSellerDetail?orderNumber=${orderData.order_number}&tranNumber=${orderData.payment_transaction_number}&termNumber=${orderData.num_credit_term}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'createShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/createShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/createShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DownloadFiles' || this.$router.currentRoute.name === 'DownloadFilesMobile') {
        if (val === true) {
          this.$router.push({ path: '/DownloadFilesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DownloadFiles' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'Curier' || this.$router.currentRoute.name === 'CurierMobile') {
        if (val === true) {
          this.$router.push({ path: '/CurierMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Curier' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'Tackingorder' || this.$router.currentRoute.name === 'TackingorderMobile') {
        if (val === true) {
          this.$router.push({ path: '/TackingorderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Tackingorder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'designShopMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/designShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/designShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'inventoryUI' || this.$router.currentRoute.name === 'inventoryMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/inventoryMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/inventory' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'posellerUI' || this.$router.currentRoute.name === 'posellerMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/posellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/poseller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'RevenueUI' || this.$router.currentRoute.name === 'RevenueMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/revenueMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/revenue' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequest' || this.$router.currentRoute.name === 'SettingPartnerRequestMobile') {
        if (val === true) {
          this.$router.push({ path: '/SettingPartnerRequestMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/SettingPartnerRequest' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'SettingTier' || this.$router.currentRoute.name === 'SettingTierMobile') {
        if (val === true) {
          this.$router.push({ path: '/SettingTierMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/SettingTier' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QuotationSettingSeller' || this.$router.currentRoute.name === 'QuotationSettingSellerMobile') {
        if (val === true) {
          this.$router.push({ path: '/QuotationSettingSellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/QuotationSettingSeller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QuotationAll' || this.$router.currentRoute.name === 'QuotationDetail') {
        if (this.$router.currentRoute.name === 'QuotationAll') {
          if (val === true) {
            this.$router.push({ path: '/QuotationAllMobile' }).catch(() => { })
          } else {
            this.$router.push({ path: '/QuotationAll' }).catch(() => { })
          }
        } else {
          if (val === true) {
            this.$router.push({ path: '/QuotationDetailMobile' }).catch(() => { })
          } else {
            this.$router.push({ path: '/QuotationDetail' }).catch(() => { })
          }
        }
      } else if (this.$router.currentRoute.name === 'ListDelivery' || this.$router.currentRoute.name === 'ListDeliveryMobile') {
        if (val === true) {
          this.$router.push({ path: '/ListDeliveryMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ListDelivery' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageDelivery' || this.$router.currentRoute.name === 'ManageDeliveryMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageDeliveryMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageDelivery' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailDelivery' || this.$router.currentRoute.name === 'DetailDeliveryMobile') {
        if (val === true) {
          this.$router.push({ path: '/DetailDeliveryMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DetailDelivery' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'EtaxCredentail' || this.$router.currentRoute.name === 'EtaxCredentailMobile') {
        if (val === true) {
          this.$router.push({ path: '/EtaxCredentailMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/EtaxCredentail' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QuotationSetting' || this.$router.currentRoute.name === 'QuotationSettingMobile') {
        if (val === true) {
          this.$router.push({ path: '/QuotationSettingMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/QuotationSetting' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'MerchantShop' || this.$router.currentRoute.name === 'MerchantShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/MerchantShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/MerchantShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManagaCupon' || this.$router.currentRoute.name === 'ManagaCuponMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManagaCuponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManagaCupon' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerlistCreditOrder' || this.$router.currentRoute.name === 'sellerlistCreditOrderMobile') {
        if (val === true) {
          this.$router.push({ path: '/sellerlistCreditOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerlistCreditOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'creditTerm' || this.$router.currentRoute.name === 'creditTermMobile') {
        var number = JSON.parse(localStorage.getItem('creditTermOrdernumber'))
        if (val === true) {
          this.$router.push({ path: `/sellerlistCreditTermMobile?order_number=${number}` }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerlistCreditTerm' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerInvoicePDF' || this.$router.currentRoute.name === 'sellerInvoicePDFMobile') {
        number = JSON.parse(localStorage.getItem('creditTermOrdernumber'))
        if (val === true) {
          this.$router.push({ path: `/sellerInvoicePDFMobile?order_number=${number}` }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerInvoicePDF' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageComments' || this.$router.currentRoute.name === 'manageCommentsMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageCommentsMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageComments' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'specialPrice' || this.$router.currentRoute.name === 'specialPriceMobile') {
        if (val === true) {
          this.$router.push({ path: '/specialPriceMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/specialPrice' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'partnerSeller' || this.$router.currentRoute.name === 'partnerSellerMobile') {
        if (val === true) {
          this.$router.push({ path: '/partnerSellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/partnerSeller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listShopPosition' || this.$router.currentRoute.name === 'listShopPositionMobile') {
        if (val === true) {
          this.$router.push({ path: '/listShopPositionMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listShopPosition' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listShopUser' || this.$router.currentRoute.name === 'listShopUserMobile') {
        if (val === true) {
          this.$router.push({ path: '/listShopUserMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listShopUser' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ListRequestNewTerm' || this.$router.currentRoute.name === 'ListRequestNewTermMobile') {
        if (val === true) {
          this.$router.push({ path: '/ListRequestNewTermMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ListRequestNewTerm' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardMobile' || this.$router.currentRoute.name === 'dashboardUI') {
        if (val === true) {
          this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboard' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerdashboardMobile' || this.$router.currentRoute.name === 'sellerdashboard') {
        if (val === true) {
          this.$router.push({ path: '/sellerdashboardMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerdashboard' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrderMobile' || this.$router.currentRoute.name === 'DashboardSaleOrder') {
        if (val === true) {
          this.$router.push({ path: '/DashboardSaleOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DashboardSaleOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ShippingReportMobile' || this.$router.currentRoute.name === 'ShippingReport') {
        if (val === true) {
          this.$router.push({ path: '/ShippingReportMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ShippingReport' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listSales' || this.$router.currentRoute.name === 'listSalesMobile') {
        if (val === true) {
          this.$router.push({ path: '/listSalesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listSales' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listCustomerGroupSalesMobile' || this.$router.currentRoute.name === 'listCustomerGroupSales') {
        if (val === true) {
          this.$router.push({ path: '/listCustomerGroupSalesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listCustomerGroupSales' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listCustomerSaleOrderMobile' || this.$router.currentRoute.name === 'listCustomerSaleOrder') {
        if (val === true) {
          this.$router.push({ path: '/listCustomerSaleOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listCustomerSaleOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listCustomerSalesMobile' || this.$router.currentRoute.name === 'listCustomerSales') {
        var dataOfSale = JSON.parse(Decode.decode(localStorage.getItem('Detail_sales')))
        if (val === true) {
          this.$router.push({ path: `/listCustomerSalesMobile?sale_code=${dataOfSale.sale_code}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/listCustomerSales?sale_code=${dataOfSale.sale_code}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listQuotationSalesMobile' || this.$router.currentRoute.name === 'listQuotationSales') {
        if (val === true) {
          this.$router.push({ path: '/listQuotationSalesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listQuotationSales' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailQuotationSales' || this.$router.currentRoute.name === 'DetailQuotationSalesMobile') {
        var QTDetail = JSON.parse(Decode.decode(localStorage.getItem('detailItemQUSale')))
        var sellerShopId = ''
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (dataRole.role !== 'sale_order') {
          sellerShopId = JSON.parse(localStorage.getItem('shopDetail'))
        } else {
          sellerShopId = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
        }
        if (val === true) {
          this.$router.push({ path: `/DetailQuotationSalesMobile?QUNumber=${QTDetail.order_number}&id=${dataRole.role !== 'sale_order' ? sellerShopId.id : sellerShopId.seller_shop_id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/DetailQuotationSales?QUNumber=${QTDetail.order_number}&id=${dataRole.role !== 'sale_order' ? sellerShopId.id : sellerShopId.seller_shop_id}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardShopAffiliateMobile' || this.$router.currentRoute.name === 'DashboardShopAffiliate') {
        if (val === true) {
          this.$router.push({ path: '/DashboardShopAffiliateMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DashboardShopAffiliate' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listAttorneyMobile' || this.$router.currentRoute.name === 'listAttorney') {
        if (val === true) {
          this.$router.push({ path: '/listAttorneyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listAttorney' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageArticleMobile' || this.$router.currentRoute.name === 'manageArticle') {
        if (val === true) {
          this.$router.push({ path: '/manageArticleMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageArticle' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageShopAccountMobile' || this.$router.currentRoute.name === 'ManageShopAccount') {
        if (val === true) {
          this.$router.push({ path: '/ManageShopAccountMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageShopAccount' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageQTExternalMobile' || this.$router.currentRoute.name === 'ManageQTExternal') {
        if (val === true) {
          this.$router.push({ path: '/ManageQTExternalMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageQTExternal' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageQTJVMobile' || this.$router.currentRoute.name === 'ManageQTJV') {
        if (val === true) {
          this.$router.push({ path: '/ManageQTJVMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageQTJV' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageFlashSaleMobile' || this.$router.currentRoute.name === 'manageFlashSale') {
        if (val === true) {
          this.$router.push({ path: '/manageFlashSaleMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageFlashSale' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DashboardWithdrawMoneyShopMobile' || this.$router.currentRoute.name === 'DashboardWithdrawMoneyShop') {
        if (val === true) {
          this.$router.push({ path: '/DashboardWithdrawMoneyShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DashboardWithdrawMoneyShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ERPPartnerMobile' || this.$router.currentRoute.name === 'ERPPartner') {
        if (val === true) {
          this.$router.push({ path: '/ERPPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ERPPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageFlashSaleEditMobile' || this.$router.currentRoute.name === 'manageFlashSaleEdit') {
        if (val === true) {
          this.$router.push({ path: '/manageFlashSaleEditMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageFlashSaleEdit' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ShopJoinPartnerDetailsMobile' || this.$router.currentRoute.name === 'ShopJoinPartnerDetails') {
        if (val === true) {
          this.$router.push({ path: '/ShopJoinPartnerDetailsMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ShopJoinPartnerDetails' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ShopPartnerDetailsMobile' || this.$router.currentRoute.name === 'ShopPartnerDetails') {
        var SelectPartnerCode = localStorage.getItem('SelectPartnerCode')
        if (val === true) {
          this.$router.push({ path: `/ShopPartnerDetailsMobile?SelectPartnerCode=${SelectPartnerCode}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/ShopPartnerDetails?SelectPartnerCode=${SelectPartnerCode}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ConfirmPartnerPackageMobile' || this.$router.currentRoute.name === 'ConfirmPartnerPackage') {
        var SelectPartnerCode2 = localStorage.getItem('SelectPartnerCode')
        if (val === true) {
          this.$router.push({ path: `/ConfirmPartnerPackageMobile?SelectPartnerCode=${SelectPartnerCode2}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/ConfirmPartnerPackage?SelectPartnerCode=${SelectPartnerCode2}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'PaymentPackageMobile' || this.$router.currentRoute.name === 'PaymentPackage') {
        if (val === true) {
          this.$router.push({ path: '/PaymentPackageMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/PaymentPackage' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'orderListPartnerMobile' || this.$router.currentRoute.name === 'orderListPartner') {
        if (val === true) {
          this.$router.push({ path: '/orderListPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/orderListPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailOrderProductShopMobile' || this.$router.currentRoute.name === 'DetailOrderProductShop') {
        if (val === true) {
          this.$router.push({ path: '/orderListPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DetailOrderProductShopMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'paymentPartnerMobile' || this.$router.currentRoute.name === 'paymentPartner') {
        if (val === true) {
          this.$router.push({ path: '/paymentPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/paymentPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'paymentListMobile' || this.$router.currentRoute.name === 'paymentList') {
        if (val === true) {
          this.$router.push({ path: '/paymentListMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/paymentList' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'successPaymentPartnerMobile' || this.$router.currentRoute.name === 'successPaymentPartner') {
        if (val === true) {
          this.$router.push({ path: '/successPaymentPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/successPaymentPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageShippingSellerMobile' || this.$router.currentRoute.name === 'manageShippingSeller') {
        if (val === true) {
          this.$router.push({ path: '/manageShippingSellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageShippingSeller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'hostMobile' || this.$router.currentRoute.name === 'host') {
        if (val === true) {
          this.$router.push({ path: '/hostMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/host' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageCategoryProductMobile' || this.$router.currentRoute.name === 'ManageCategoryProduct') {
        if (val === true) {
          this.$router.push({ path: '/ManageCategoryProductMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageCategoryProduct' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageSalesPartnerApproval' || this.$router.currentRoute.name === 'ManageSalesPartnerApprovalMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageSalesPartnerApproval' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageSalesPartnerApprovalMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageSalePartnerApprove' || this.$router.currentRoute.name === 'manageSalePartnerApproveMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageSalePartnerApprove' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageSalePartnerApproveMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ListApprovePartnerShop' || this.$router.currentRoute.name === 'ListApprovePartnerShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/ListApprovePartnerShop' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ListApprovePartnerShopMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listApproveSalesPartner' || this.$router.currentRoute.name === 'listApproveSalesPartnerMobile') {
        if (val === true) {
          this.$router.push({ path: '/listApproveSalesPartner' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listApproveSalesPartnerMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageTagShop' || this.$router.currentRoute.name === 'ManageTagShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageTagShop' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageTagShopMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageTagProductShop' || this.$router.currentRoute.name === 'ManageTagProductShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageTagProductShop' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageTagProductShopMobile' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'host' || this.$router.currentRoute.name === 'hostMobile') {
        if (val === true) {
          this.$router.push({ path: '/hostMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/host' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ProfileTraceability' || this.$router.currentRoute.name === 'ProfileTraceabilityMobile') {
        if (val === true) {
          this.$router.push({ path: '/ProfileTraceabilityMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ProfileTraceability' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'CreateProfileTraceability' || this.$router.currentRoute.name === 'CreateProfileTraceabilityMobile') {
        if (val === true) {
          this.$router.push({ path: '/CreateProfileTraceabilityMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/CreateProfileTraceability' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'bundleDeal' || this.$router.currentRoute.name === 'bundleDealMobile') {
        if (val === true) {
          this.$router.push({ path: '/bundleDealMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/bundleDeal' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManageMembers' || this.$router.currentRoute.name === 'ManageMembersMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManageMembersMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManageMembers' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'AddMembers' || this.$router.currentRoute.name === 'AddMembersMobile') {
        if (val === true) {
          this.$router.push({ path: '/AddMembersMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/AddMembers' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'EditMembers' || this.$router.currentRoute.name === 'EditMembersMobile') {
        if (val === true) {
          this.$router.push({ path: '/EditMembersMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/EditMembers' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'createBundleDeal' || this.$router.currentRoute.name === 'createBundleDealMobile') {
        if (val === true) {
          this.$router.push({ path: '/createBundleDealMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/createBundleDeal' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'POSellerB2BDetail' || this.$router.currentRoute.name === 'POSellerB2BDetailMobile') {
        var ordernumber = localStorage.getItem('orderNumber')
        var transactionNumber = localStorage.getItem('transactionNumber')
        var termNumber = localStorage.getItem('termNumber')
        if (val === true) {
          this.$router.push({ path: `/POSellerB2BDetailMobile?orderNumber=${ordernumber}&tranNumber=${transactionNumber}&termNumber=${termNumber}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/POSellerB2BDetail?orderNumber=${ordernumber}&tranNumber=${transactionNumber}&termNumber=${termNumber}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'POSellerB2BList' || this.$router.currentRoute.name === 'POSellerB2BListMobile') {
        if (val === true) {
          this.$router.push({ path: '/POSellerB2BListMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/POSellerB2BList' }).catch(() => {})
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.checkJVShop()
    this.checkpath()
    // this.$tours['myTour.ShopPage'].start()
    this.$EventBus.$on('checkJVShop', this.checkJVShop)
    this.$EventBus.$on('checkpath', this.checkpath)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('AuthorityUsers')
      this.$EventBus.$off('checkJVShop')
      this.$EventBus.$off('checkpath')
    })
  },
  methods: {
    async createChatShop () {
      const ShopID = await localStorage.getItem('shopSellerID')
      const data = await {
        user_id: this.oneData.user.user_id,
        seller_shop_id: ShopID
      }
      await this.$store.dispatch('actionsChatShop', data)
      var response = await this.$store.state.ModuleHompage.stateChatShop
      if (response.result === 'SUCCESS') {
        if (response.data.shop_have_bot.length !== 0) {
          const sharedToken = await createChat.sharetoken()
          const shopData = await 'https://www.chatme.co.th/uat/chatmeservice/converseme/AuthPlugin?shared_token=' + sharedToken.data.shared_token + '&shop_name=' + response.data.shop_have_bot[0].botName + '&shop_id=' + response.data.shop_have_bot[0].shopNGSID
          // console.log('shopData', shopData)
          await window.open(shopData)
        } else {
          this.$swal.fire({
            icon: 'warning',
            html: '<h2>ร้านค้านี้ยังไม่ถูกสร้างแชท</h2> <h2>คุณต้องการสร้างแชทหรือไม่ ?</h2>',
            showCancelButton: true,
            confirmButtonText: 'ใช่',
            cancelButtonText: 'ไม่ใช่',
            confirmButtonColor: '#27AB9C',
            reverseButtons: true
          }).then((result) => {
            if (result.isConfirmed) {
              this.$store.commit('openLoader')
              var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
              this.createChat(shopDetail)
            } else {
            }
          }).catch(() => {
          })
        }
      }
    },
    async createChat (item) {
      const sharedToken = await createChat.sharetoken()
      const req = await {
        user_id: this.oneData.user.user_id,
        shopNGSID: item.id,
        shopNGSName: item.name,
        shopNGSProfileUrl: this.imgLogoShop,
        sharedToken: sharedToken.data.shared_token
      }
      await this.$store.dispatch('actionsCreateBotChat', req)
      var res = await this.$store.state.ModuleHompage.stateCreateBotChat
      if (res.data.length !== 0) {
        const data = await {
          botID: res.data[0].botID
        }
        await createChat.chatMe2(data)
        this.$store.commit('closeLoader')
        await this.$EventBus.$emit('initChat')
        // await window['sentChatMore']()
        // console.log('test na be', res)
      } else {
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', html: res.message })
        await this.$store.commit('closeLoader')
      }
    },
    dataRole () {
      var data = JSON.parse(localStorage.getItem('roleUser'))
      this.roleUser = data
    },
    closeModal () {
      this.$EventBus.$emit('closeModalLogin')
      this.$EventBus.$emit('closeModalRegister')
      this.$EventBus.$emit('closeModalSuccess')
      this.$EventBus.$emit('closeModalCartNoLogin')
      this.$EventBus.$emit('closeModalCart')
      this.$EventBus.$emit('OpenNotification')
      this.$EventBus.$emit('OpenChatAll')
      this.$EventBus.$emit('Open_No_Notification')
    },
    changePage (val) {
      this.$EventBus.$emit('resetAdminShop')
      if (this.roleUser.role === 'sale_order' || this.roleUser.role === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathData.path }).catch(() => {})
      } else {
        this.$router.push({ path: `${val}` }).catch(() => {})
      }
    },
    async checkJVShop () {
      // console.log('1=======>')
      this.isJV = ''
      var shopDetail = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: shopDetail,
        role: 'seller'
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        const img = response.data[0].shop_profile
        // console.log('response==', response)
        localStorage.setItem('sellerShopMP', Encode.encode(response.data[0]))
        if (img !== undefined) {
          this.imgLogoShop = img[0].media_path
        } else {
          this.imgLogoShop = ''
        }
        if (response.data[0].is_JV === 'yes') {
          this.isJV = 'yes'
        } else {
          this.isJV = 'no'
        }
      }
      await this.AuthorityUsers()
    },
    async checkPath2 () {
      this.AuthorityUsers()
      for (let i = 0; i < this.itemsShop[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsShop[0].items[i].path) {
          this.defaultSelect = i
        }
      }
      for (let i = 0; i < this.itemsCompany[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsCompany[0].items[i].path) {
          this.Select = i
        }
      }
      for (let i = 0; i < this.itemsSalesOrder[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsSalesOrder[0].items[i].path) {
          this.SelectSalesOrder = i
        }
      }
      for (let i = 0; i < this.itemsPromotion[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsPromotion[0].items[i].path) {
          this.SelectProMo = i
        }
      }
      for (let i = 0; i < this.itemsAffiliate[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsAffiliate[0].items[i].path) {
          this.SelectAffiliate = i
        }
      }
      for (let i = 0; i < this.itemsPartner[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsPartner[0].items[i].path) {
          this.SelectPartner = i
        }
      }
      for (let i = 0; i < this.itemsMarketplace[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsMarketplace[0].items[i].path) {
          this.SelectMarketplace = i
        }
      }
      for (let i = 0; i < this.itemsShopAccount[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsShopAccount[0].items[i].path) {
          this.SelectShopAccount = i
        }
      }
      for (let i = 0; i < this.itemsLiveStreaming[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsLiveStreaming[0].items[i].path) {
          this.SelectLiveStreaming = i
        }
      }
      for (let i = 0; i < this.itemsManageTag[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsManageTag[0].items[i].path) {
          this.SelectManageTag = i
        }
      }
      for (let i = 0; i < this.itemsMembers[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsMembers[0].items[i].path) {
          this.SelectMembers = i
        }
      }
    },
    async AuthorityUsers () {
      this.dataDetail = []
      if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'stepCreateShop') {
      } else {
        if (localStorage.getItem('list_shop_detail') !== null) {
          this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
          if (this.dataDetail.array_position.some(element => element.position_name === 'เจ้าของร้าน')) {
            this.PositionName = 'เจ้าของร้าน'
          } else if (this.dataDetail.array_position.some(element => element.position_name === 'เจ้าของนิติบุคคล')) {
            this.PositionName = 'เจ้าของนิติบุคคล'
          } else {
            this.PositionName = 'ไม่ใช่เจ้าของร้านค้า'
          }
        } else {
          this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => {})
        }
      }
      // console.log('123', this.dataDetail)
      var item1 = []
      var item2 = []
      var item3 = []
      var item4 = []
      var item5 = []
      var item6 = []
      var item7 = []
      var item8 = []
      var item9 = []
      var item10 = []
      var item11 = []
      if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'stepCreateShop') {
        item1 = this.itemsCreateShop
      } else {
        if (this.dataDetail.is_attorney === 'no') {
          if (this.dataDetail.can_use_function_in_shop.manage_product === '1') {
            item1.push({ key: 4, title: 'รายการสินค้า', path: 'seller' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
            item1.push({ key: 5, title: 'รายการสั่งซื้อสินค้า', path: 'poseller' })
            item1.push({ key: 11, title: 'รายงานการจัดส่ง', path: 'ShippingReport' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_order === '1' && this.isJV === 'yes') {
            item1.push({ key: 13, title: 'รายงานการสั่งซื้อ', path: 'Reportseller' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 12, title: 'จัดการร้านค้า', path: 'designShop' })
            item1.push({ key: 52, title: 'จัดการขนส่ง', path: 'manageShippingSeller' })
            item1.push({ key: 53, title: 'จัดการหมวดหมู่สินค้า', path: 'ManageCategoryProduct' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 32, title: 'จัดการใบเสนอราคา', path: 'ManageQTExternal' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 60, title: 'จัดการใบเสนอราคา JV', path: 'ManageQTJV' })
          }
          // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          //   item1.push({ key: 30, title: 'จัดการสินค้า Flash sale', path: 'manageFlashSale' })
          // }
          if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1' && this.isJV === 'yes') {
            item1.push({ key: 14, title: 'แดชบอร์ด', path: 'dashboard' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_user_with_position === '1') {
            item1.push({ key: 17, title: 'จัดการตำแหน่งและสิทธิ์การใช้งาน', path: 'listShopPosition' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_user_with_position === '1') {
            item1.push({ key: 18, title: 'จัดการผู้ใช้งาน', path: 'listShopUser' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
            item1.push({ key: 19, title: 'จัดการความคิดเห็น', path: 'manageComments' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 29, title: 'จัดการบทความ', path: 'manageArticle' })
          }
          // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          //   item1.push({ key: 30, title: 'จัดการบัญชีร้านค้า', path: 'ManageShopAccount' })
          // }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 23, title: 'e-Tax Credential', path: 'EtaxCredentail' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1') {
            item1.push({ key: 25, title: 'แดชบอร์ดร้านค้า', path: 'sellerdashboard' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1') {
            item1.push({ key: 31, title: 'แดชบอร์ดระบบถอนเงิน', path: 'DashboardWithdrawMoneyShop' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 26, title: 'ประกาศงาน/ค้นหาผู้สมัคร', path: 'WLCAdmin' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 27, title: 'แชทร้านค้า', path: '' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1' || (this.PositionName === 'เจ้าของนิติบุคคล' || this.PositionName === 'เจ้าของร้าน')) {
            item1.push({ key: 28, title: 'รายการเอกสารมอบอำนาจ', path: 'listAttorney' })
          }
          if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
            item1.push({ key: 59, title: 'Traceability Profile', path: 'ProfileTraceability' })
          }
          // if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1' || this.PositionName === 'เจ้าของร้านค้า') {
          //   item1.push({ key: 28, title: 'โปรแกรม Affiliate', path: 'sellerAffiliate' })
          // }
        } else {
          item1.push({ key: 1, title: 'ยินยอมรับมอบอำนาจช่วง', path: 'Attorney' })
          // item1 = []
        }
      }
      this.items[0].items = []
      this.items[0].items = item1
      this.itemsShop[0].items = []
      this.itemsShop[0].items = item1
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item2.push({ key: 20, title: 'รายการเอกสารขอเป็นคู่ค้า', path: 'SettingPartnerRequest' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item2.push({ key: 21, title: 'ตั้งค่ากลุ่มคู่ค้า (Tier)', path: 'SettingTier' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item2.push({ key: 29, title: 'รายชื่อคู่ค้า', path: 'partnerSeller' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item2.push({ key: 30, title: 'ใบเสนอราคา', path: 'QuotationAll' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item2.push({ key: 54, title: 'รายการใบส่งสินค้า', path: 'ListDelivery' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item2.push({ key: 55, title: 'จัดการรูปแบบการอนุมัติคู่ค้า', path: 'ManageSalesPartnerApproval' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item2.push({ key: 56, title: 'จัดการลำดับการอนุมัติคู่ค้า', path: 'manageSalePartnerApprove' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item2.push({ key: 57, title: 'รายการอนุมัติคู่ค้า', path: 'ListApprovePartnerShop' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item2.push({ key: 58, title: 'รายการสั่งซื้อสินค้า', path: 'POSellerB2BList' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item2.push({ key: 53, title: 'จัดการรูปแบบการอนุมัติ', path: '' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item2.push({ key: 54, title: 'จัดการลำดับการอนุมัติ', path: '' })
        // }
        // if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        //   item2.push({ key: 55, title: 'รายการอนุมัติ', path: '' })
        // }
        this.itemsCompany[0].items = []
        this.itemsCompany[0].items = item2
        if (this.itemsCompany[0].items.length === 0) {
          this.itemsCompany[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 31, title: 'รายชื่อลูกค้า', path: 'listCustomerSaleOrder' })
        }
        if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 32, title: 'กลุ่มลูกค้า', path: 'listCustomerGroupSales' })
        }
        if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 33, title: 'ใบเสนอราคา', path: 'listQuotationSales' })
        }
        if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 34, title: 'รายการสั่งซื้อฝ่ายขาย', path: 'orderSales' })
        }
        if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 35, title: 'รายชื่อฝ่ายขาย', path: 'listSales' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 36, title: 'จัดการรูปแบบการอนุมัติฝ่ายขาย', path: 'ManageSalesApproval' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 37, title: 'จัดการลำดับการอนุมัติฝ่ายขาย', path: 'manageSaleApprove' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 38, title: 'รายการอนุมัติฝ่ายขาย', path: 'listApproveSales' })
        }
        if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
          item3.push({ key: 39, title: 'แดชบอร์ดฝ่ายขาย', path: 'DashboardSaleOrder' })
        }
        this.itemsSalesOrder[0].items = []
        this.itemsSalesOrder[0].items = item3
        if (this.itemsSalesOrder[0].items.length === 0) {
          this.itemsSalesOrder[0].active = false
          this.itemsShop[0].active = true
        }
      }
      // if (this.dataDetail.can_use_function_in_shop.manage_product !== '1') {
      //   this.$router.push({ path: this.items[0].items[0].path }).catch(() => {})
      // }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
          item4.push({ key: 40, title: 'จัดการคูปอง', path: 'manageCoupon' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
          item4.push({ key: 41, title: 'จัดการแต้ม', path: 'setPoint' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
          item4.push({ key: 42, title: 'รายชื่อลูกค้าสะสมแต้ม', path: 'allPointFromCostomer' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
          item4.push({ key: 43, title: 'จัดการสินค้า Flash sale', path: 'manageFlashSale' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
          item4.push({ key: 61, title: 'Bundle Deal', path: 'bundleDeal' })
        }
        // if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
        //   item4.push({ key: 44, title: 'Bundle Deal', path: 'bundleDeal' })
        // }
        if (this.dataDetail.can_use_function_in_shop.manage_promotion === '1') {
          item4.push({ key: 45, title: 'Add-on Deal', path: 'AddOnDeal' })
        }
        this.itemsPromotion[0].items = []
        this.itemsPromotion[0].items = item4
        if (this.itemsPromotion[0].items.length === 0) {
          this.itemsPromotion[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1' || (this.PositionName === 'เจ้าของนิติบุคคล' || this.PositionName === 'เจ้าของร้าน')) {
          item5.push({ key: 44, title: 'จัดการ Affiliate', path: 'sellerAffiliate' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1') {
          item5.push({ key: 45, title: 'รายชื่อผู้เข้าร่วม Affiliate', path: 'ListUserJoinAffiliate' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_affiliate === '1') {
          item5.push({ key: 46, title: 'แดชบอร์ด Affiliate', path: 'DashboardShopAffiliate' })
        }
        this.itemsAffiliate[0].items = []
        this.itemsAffiliate[0].items = item5
        if (this.itemsAffiliate[0].items.length === 0) {
          this.itemsAffiliate[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
          item6.push({ key: 47, title: 'เชื่อมต่อบริการ', path: 'ERPPartner' })
        }
        this.itemsPartner[0].items = []
        this.itemsPartner[0].items = item6
        if (this.itemsPartner[0].items.length === 0) {
          this.itemsPartner[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
          item7.push({ key: 48, title: 'ข้อมูลร้านค้า Partner', path: 'ShopJoinPartnerDetails' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
          item7.push({ key: 49, title: 'รายการคำสั่งซื้อ', path: 'orderListPartner' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_partner === '1') {
          item7.push({ key: 50, title: 'รายการชำระเงิน', path: 'paymentPartner' })
        }
        this.itemsMarketplace[0].items = []
        this.itemsMarketplace[0].items = item7
        if (this.itemsMarketplace[0].items.length === 0) {
          this.itemsMarketplace[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_account_bank === '1') {
          item8.push({ key: 51, title: 'จัดการบัญชีร้านค้า', path: 'ManageShopAccount' })
        }
        this.itemsShopAccount[0].items = []
        this.itemsShopAccount[0].items = item8
        if (this.itemsShopAccount[0].items.length === 0) {
          this.itemsShopAccount[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item9.push({ key: 52, title: 'Live Streaming', path: 'host' })
        }
        this.itemsLiveStreaming[0].items = []
        this.itemsLiveStreaming[0].items = item9
        if (this.itemsLiveStreaming[0].items.length === 0) {
          this.itemsLiveStreaming[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item10.push({ key: 57, title: 'จัดการแท็กร้านค้า', path: 'ManageTagShop' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item10.push({ key: 58, title: 'จัดการแท็กสินค้า', path: 'ManageTagProductShop' })
        }
        this.itemsManageTag[0].items = []
        this.itemsManageTag[0].items = item10
        if (this.itemsManageTag[0].items.length === 0) {
          this.itemsManageTag[0].active = false
          this.itemsShop[0].active = true
        }
      }
      if (this.dataDetail.is_attorney === 'no') {
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item11.push({ key: 61, title: 'จัดการข้อมูลสมาชิก', path: 'ManageMembers' })
        }
        this.itemsMembers[0].items = []
        this.itemsMembers[0].items = item11
        if (this.itemsMembers[0].items.length === 0) {
          this.itemsMembers[0].active = false
          this.itemsShop[0].active = true
        }
      }
      // console.log(this.dataDetail, 'this.dataDetail')
      this.SelectPath()
    },
    checkpath () {
      if (this.$router.currentRoute.name === 'sellerUI' || this.$router.currentRoute.name === 'manageproductUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'posellerUI' || this.$router.currentRoute.name === 'POSellerDetailUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ShippingReport') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'Curier') { // inventoryUI'
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'Tackingorder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'inventoryUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'returnUI' || this.$router.currentRoute.name === 'returndetailUI') { // supplier_seller
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'dashboardUI') { // promotion_seller
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'DownloadFiles') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'RevenueUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'SettingShop' || this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'partnerSeller' || this.$router.currentRoute.name === 'partnerSellerDetail') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'stepCreateShop') {
        this.itemsShop = this.itemsCreateShop
        this.checkCreateShop = true
      } else if (this.$router.currentRoute.name === 'createShopMobile' || this.$router.currentRoute.name === 'stepCreateShopMobile') {
        this.itemsShop = this.items
        this.checkCreateShop = true
      } else if (this.$router.currentRoute.name === 'QuotationSetting') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'sellerdashboard') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'QuotationSettingSeller') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'manageShippingSeller') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ManageCategoryProduct') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequest') {
        this.itemsShop = this.items
        this.Select = 0
        this.defaultSelect = 16
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'SettingTier') {
        this.itemsShop = this.items
        this.Select = 1
        this.defaultSelect = 17
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'partnerSeller') {
        this.itemsShop = this.items
        this.Select = 2
        this.defaultSelect = 18
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'QuotationAll') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 3
        this.defaultSelect = 19
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listCustomerSaleOrder' || this.$router.currentRoute.name === 'ListOrderCustomer' || this.$router.currentRoute.name === 'CouponSalesOrder' || this.$router.currentRoute.name === 'DetailOrderCustomer' || this.$router.currentRoute.name === 'DetailDueDateCustomer') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 4
        this.SelectSalesOrder = 0
        this.defaultSelect = 20
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listCustomerGroupSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 5
        this.SelectSalesOrder = 1
        this.defaultSelect = 21
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listQuotationSales' || this.$router.currentRoute.name === 'DetailQuotationSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 6
        this.SelectSalesOrder = 2
        this.defaultSelect = 22
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'orderSales' || this.$router.currentRoute.name === 'DetailOrderSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 7
        this.SelectSalesOrder = 3
        this.defaultSelect = 23
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listSales' || this.$router.currentRoute.name === 'listCustomerSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 8
        this.SelectSalesOrder = 4
        this.defaultSelect = 24
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageSalesApproval' || this.$router.currentRoute.name === 'ManageSalesApprovalDetail') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 9
        this.SelectSalesOrder = 5
        this.defaultSelect = 25
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageSaleApprove') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 10
        this.SelectSalesOrder = 6
        this.defaultSelect = 26
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listApproveSales' || this.$router.currentRoute.name === 'DetailListApproveSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 11
        this.SelectSalesOrder = 7
        this.defaultSelect = 27
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 12
        this.SelectSalesOrder = 8
        this.defaultSelect = 28
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageCoupon' || this.$router.currentRoute.name === 'coupondiscount' || this.$router.currentRoute.name === 'couponfreeproduct' || this.$router.currentRoute.name === 'couponfreeshipping') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 13
        this.SelectSalesOrder = 9
        this.SelectProMo = 0
        this.defaultSelect = 29
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'setPoint') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 14
        this.SelectSalesOrder = 10
        this.SelectProMo = 1
        this.defaultSelect = 30
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'allpointfromcostomer') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 15
        this.SelectSalesOrder = 11
        this.SelectProMo = 2
        this.defaultSelect = 31
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'sellerAffiliate') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 16
        this.SelectSalesOrder = 12
        this.SelectProMo = 4
        this.SelectAffiliate = 0
        this.defaultSelect = 32
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ListUserJoinAffiliate') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 17
        this.SelectSalesOrder = 13
        this.SelectProMo = 5
        this.SelectAffiliate = 1
        this.defaultSelect = 33
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'DashboardShopAffiliate') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 18
        this.SelectSalesOrder = 14
        this.SelectProMo = 6
        this.SelectAffiliate = 2
        this.defaultSelect = 34
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ERPPartner') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 19
        this.SelectSalesOrder = 16
        this.SelectProMo = 8
        this.SelectPartner = 0
        this.defaultSelect = 35
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ShopJoinPartnerDetails' || this.$router.currentRoute.name === 'ShopPartnerDetails' || this.$router.currentRoute.name === 'ConfirmPartnerPackage' || this.$router.currentRoute.name === 'PaymentPackage') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 20
        this.SelectSalesOrder = 17
        this.SelectProMo = 9
        this.SelectMarketplace = 0
        this.defaultSelect = 36
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = true
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'orderListPartner') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 21
        this.SelectSalesOrder = 18
        this.SelectProMo = 10
        this.SelectMarketplace = 1
        this.defaultSelect = 37
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = true
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageShopAccount') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 22
        this.SelectSalesOrder = 19
        this.SelectProMo = 11
        this.SelectShopAccount = 0
        this.defaultSelect = 38
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = true
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'host') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 23
        this.SelectSalesOrder = 20
        this.SelectProMo = 12
        this.SelectLiveStreaming = 0
        this.defaultSelect = 39
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = true
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ListDelivery' || this.$router.currentRoute.name === 'ManageDelivery' || this.$router.currentRoute.name === 'DetailDelivery') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 24
        this.defaultSelect = 40
        this.SelectSalesOrder = 21
        this.SelectProMo = 13
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageSalesPartnerApproval') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 25
        this.defaultSelect = 41
        this.SelectSalesOrder = 22
        this.SelectProMo = 14
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageSalePartnerApprove') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 26
        this.defaultSelect = 42
        this.SelectSalesOrder = 23
        this.SelectProMo = 15
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listApproveSalesPartner') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 27
        this.defaultSelect = 43
        this.SelectSalesOrder = 24
        this.SelectProMo = 16
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageTagShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 28
        this.defaultSelect = 44
        this.SelectSalesOrder = 25
        this.SelectProMo = 17
        this.SelectManageTag = 0
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = true
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageTagProductShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 29
        this.defaultSelect = 45
        this.SelectSalesOrder = 26
        this.SelectProMo = 18
        this.SelectManageTag = 1
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = true
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ListApprovePartnerShop' || this.$router.currentRoute.name === 'DetailApprovePartnerShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 7
        this.defaultSelect = 46
        this.SelectSalesOrder = 27
        this.SelectProMo = 19
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageMembers' || this.$router.currentRoute.name === 'EditMembers' || this.$router.currentRoute.name === 'AddMembers') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 30
        this.defaultSelect = 47
        this.SelectSalesOrder = 28
        this.SelectMembers = 0
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = true
      } else if (this.$router.currentRoute.name === 'POSellerB2BList' || this.$router.currentRoute.name === 'POSellerB2BDetail') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 8
        this.defaultSelect = 51
        this.SelectSalesOrder = 29
        this.SelectProMo = 20
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageArticle') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      // } else if (this.$router.currentRoute.name === 'ManageShopAccount') {
      //   this.itemsShop = this.items
      //   this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'manageFlashSale') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ManageQTExternal') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ManageQTJV') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'DashboardWithdrawMoneyShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ProfileTraceability' || this.$router.currentRoute.name === 'CreateProfileTraceability' || this.$router.currentRoute.name === 'DetailProfileTraceability') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      }
    },
    async Gopage (val) {
      // console.log('valiii', val)
      if (val.title === 'แชทร้านค้า') {
        this.createChatShop()
      }
      if (val.path !== 'seller') {
        if (val.path === 'WLCAdmin') {
          // API get share_token
          var auth
          var response
          if (localStorage.getItem('oneData') !== null) {
            var oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            auth = {
              headers: { Authorization: `Bearer ${oneData.user.access_token}` }
            }
          }
          var WLC = ''
          var URLCurrent = ''
          if (location.protocol === 'https:') {
            URLCurrent = window.location.href.substring(8, 11)
            if (URLCurrent === 'dev') {
              if (oneData.typeLoginOne === 'OneID') {
                WLC = 'prd'
                response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
                if (response.data.message === 'Success.') {
                  window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
                }
              } else {
                WLC = 'uat'
                response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
                if (response.data.message === 'Success.') {
                  window.open(`${process.env.VUE_APP_WLC_ADMIN_UAT}=${response.data.data.share_token}`)
                }
              }
            } else if (URLCurrent === 'uat') {
              WLC = 'prd'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
              }
            } else {
              WLC = 'prd'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
              }
            }
          } else {
            if (oneData.typeLoginOne === 'OneID') {
              WLC = 'prd'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN}=${response.data.data.share_token}`)
              }
            } else {
              WLC = 'uat'
              response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_share_token?WLC=${WLC}`, '', auth)
              if (response.data.message === 'Success.') {
                window.open(`${process.env.VUE_APP_WLC_ADMIN_UAT}=${response.data.data.share_token}`)
              }
            }
          }
        } else {
          if (val.path === 'ManageSalesApproval' || val.path === 'ManageSalesPartnerApproval') {
            this.$EventBus.$emit('GetListPositionSalesOrder', val.path === 'ManageSalesApproval' ? 'sale_order' : 'partner_order')
          } else if (val.path === 'manageSaleApprove' || val.path === 'manageSalePartnerApprove') {
            this.$EventBus.$emit('getListSaleApprove', val.path === 'manageSaleApprove' ? 'sale_order' : 'partner_order')
          } else if (val.path === 'listApproveSales' || val.path === 'listApproveSalesPartner') {
            this.$EventBus.$emit('getListApproveSales', val.path === 'listApproveSales' ? 'sale_order' : 'partner_order')
          }
          this.$router.push(val.path).catch(() => {})
        }
      } else {
        var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
        if (this.MobileSize === true) {
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
        } else {
          this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
        }
      }
    },
    ChangeActiveMenu (val) {
      this.activeMenu = val
    },
    SelectPath () {
      if (this.$router.currentRoute.name === 'sellerUI' || this.$router.currentRoute.name === 'manageproductUI') {
        // รายการสินค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 0
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'posellerUI' || this.$router.currentRoute.name === 'POSellerDetailUI') {
        // รายการสั่งซื้อสินค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 1
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ShippingReport' || this.$router.currentRoute.name === 'ShippingReportMobile' || this.$router.currentRoute.name === 'createShipping' || this.$router.currentRoute.name === 'createShippingMobile') {
        // รายงานการสั่งซื้อ
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 2
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'Reportseller' || this.$router.currentRoute.name === 'ReportsellerMobile') {
        // Reportseller
        this.defaultSelect = 3
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
        // จัดการร้านค้า
        this.itemsShop = this.items
        if (this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
          this.defaultSelect = this.items[0].items.findIndex((result) => {
            return '/' + result.path === '/designShop'
          })
        } else {
          this.defaultSelect = this.items[0].items.findIndex((result) => {
            return '/' + result.path === this.$router.currentRoute.path
          })
        }
        if (this.isJV === 'yes') {
          this.defaultSelect = 4
        } else {
          this.defaultSelect = 3
        }
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'dashboardUI') {
        // แดชบอร์ด
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 5
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'listShopPosition') {
        // จัดการตำแหน่งและสิทธิ์การใช้งาน
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 6
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'listShopUser') {
        // จัดการผู้ใช้งาน
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 7
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'manageComments') {
        // จัดการความคิดเห็น
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 8
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'EtaxCredentail') {
        // e-Tax Credential
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 11
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'sellerdashboard') {
        // แดชบอร์ดร้านค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 12
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'QuotationSetting') {
        // ตั้งค่าใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 13
        // this.Select = false
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'QuotationSettingSeller') {
        // ตั้งค่าใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = 14
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.checkCreateShop = false
        // End of Menu Seller
      } else if (this.$router.currentRoute.name === 'listAttorney') {
        // ตั้งค่าใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = 15
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.checkCreateShop = false
        // End of Menu Seller
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequest') {
        // รายการเอกสารขอเป็นคู่ค้า
        // this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        // this.defaultSelect = 9
        // this.checkCreateShop = false
        this.Select = 0
        this.defaultSelect = 16
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'SettingTier') {
        // ตั้งค่ากลุ่มคู่ค้า (Tier)
        // this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        // this.defaultSelect = 10
        // this.checkCreateShop = false
        this.Select = 1
        this.defaultSelect = 17
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'partnerSeller') {
        //  รายชื่อคู่ค้า
        // this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 2
        this.defaultSelect = 18
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'QuotationAll' || this.$router.currentRoute.name === 'QuotationDetail') {
        // ใบเสนอราคา
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 3
        this.defaultSelect = 19
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ListDelivery' || this.$router.currentRoute.name === 'ManageDelivery' || this.$router.currentRoute.name === 'DetailDelivery') {
        // ส่งของ
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 4
        this.defaultSelect = 20
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageSalesPartnerApproval') {
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 5
        this.defaultSelect = 21
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageSalePartnerApprove') {
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 6
        this.defaultSelect = 22
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listApproveSalesPartner') {
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 7
        this.defaultSelect = 23
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
        // End of menu บริษัท
      } else if (this.$router.currentRoute.name === 'listCustomerSaleOrder') {
        // รายชื่อลูกค้า
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 0
        this.Select = 4
        this.defaultSelect = 18
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listCustomerGroupSales') {
        // console.log('listCustomerGroupSales')
        // กลุ่มลูกค้า
        this.itemsShop = this.items
        this.SelectSalesOrder = 1
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 5
        this.defaultSelect = 19
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listQuotationSales' || this.$router.currentRoute.name === 'DetailQuotationSales') {
        // ใบเสนอราคา
        this.itemsShop = this.items
        this.SelectSalesOrder = 2
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 6
        this.defaultSelect = 20
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'orderSales' || this.$router.currentRoute.name === 'DetailOrderSales') {
        // รายการสั่งซื้อ Sales Order
        this.itemsShop = this.items
        this.SelectSalesOrder = 3
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.Select = 7
        this.defaultSelect = 21
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listSales' || this.$router.currentRoute.name === 'listCustomerSales') {
        // รายชื่อ Sales
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 4
        this.Select = 8
        this.defaultSelect = 22
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageSalesApproval' || this.$router.currentRoute.name === 'ManageSalesApprovalDetail') {
        // จัดการรูปแบบการอนุมัติฝ่ายขาย
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 5
        this.Select = 9
        this.defaultSelect = 23
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageSaleApprove') {
        // รายชื่อ Sales
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 6
        this.Select = 10
        this.defaultSelect = 24
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'listApproveSales' || this.$router.currentRoute.name === 'DetailListApproveSales') {
        // รายการอนุมัติฝ่ายขาย
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 7
        this.Select = 11
        this.defaultSelect = 25
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 8
        this.Select = 12
        this.defaultSelect = 26
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageCoupon' || this.$router.currentRoute.name === 'coupondiscount' || this.$router.currentRoute.name === 'couponfreeproduct' || this.$router.currentRoute.name === 'couponfreeshipping') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 9
        this.Select = 13
        this.defaultSelect = 27
        this.SelectProMo = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPartner[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'setPoint') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 10
        this.Select = 14
        this.defaultSelect = 28
        this.SelectProMo = 1
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPartner[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'allpointfromcostomer') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 11
        this.Select = 15
        this.defaultSelect = 29
        this.SelectProMo = 2
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPartner[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'sellerAffiliate') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        // this.defaultSelect = this.items[0].items.findIndex((result) => {
        //   return '/' + result.path === this.$router.currentRoute.path
        // })
        this.SelectSalesOrder = 11
        this.Select = 16
        this.defaultSelect = 30
        this.SelectAffiliate = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ListUserJoinAffiliate') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 12
        this.Select = 17
        this.defaultSelect = 31
        this.SelectAffiliate = 1
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'DashboardShopAffiliate') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 13
        this.Select = 18
        this.defaultSelect = 32
        this.SelectAffiliate = 2
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsAffiliate[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ERPPartner') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 15
        this.Select = 19
        this.defaultSelect = 34
        this.SelectPartner = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = true
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageArticle') {
        // จัดการบทความ
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 33
        this.checkCreateShop = false
      // } else if (this.$router.currentRoute.name === 'ManageShopAccount') {
      //   // จัดการบทความ
      //   this.itemsShop = this.items
      //   this.defaultSelect = this.items[0].items.findIndex((result) => {
      //     return '/' + result.path === this.$router.currentRoute.path
      //   })
      //   this.defaultSelect = 40
      //   this.checkCreateShop = false
      // }
      // else if (this.$router.currentRoute.name === 'manageFlashSale') {
      //   // จัดการบทความ
      //   this.itemsShop = this.items
      //   this.defaultSelect = this.items[0].items.findIndex((result) => {
      //     return '/' + result.path === this.$router.currentRoute.path
      //   })
      //   this.defaultSelect = 35
      //   this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ManageQTExternal') {
        // จัดการบทความ
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 37
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'DashboardWithdrawMoneyShop') {
        // Dashboard ถอนเงินร้านค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 36
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ProfileTraceability' || this.$router.currentRoute.name === 'CreateProfileTraceability' || this.$router.currentRoute.name === 'DetailProfileTraceability') {
        // Profile Traceability
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 45
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ShopJoinPartnerDetails' || this.$router.currentRoute.name === 'ShopPartnerDetails' || this.$router.currentRoute.name === 'ConfirmPartnerPackage' || this.$router.currentRoute.name === 'PaymentPackage') {
        this.itemsShop = this.items
        this.Select = 20
        this.SelectSalesOrder = 16
        this.SelectMarketplace = 0
        this.defaultSelect = 38
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = true
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'orderListPartner') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 17
        this.Select = 21
        this.defaultSelect = 39
        this.SelectMarketplace = 1
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = true
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'paymentPartner') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 18
        this.Select = 22
        this.defaultSelect = 40
        this.SelectMarketplace = 2
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = true
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageShopAccount') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 19
        this.Select = 23
        this.defaultSelect = 41
        this.SelectShopAccount = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = true
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'host') {
        this.itemsShop = this.items
        this.SelectSalesOrder = 20
        this.Select = 24
        this.defaultSelect = 42
        this.SelectLiveStreaming = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsAffiliate[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = true
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageTagShop') {
        this.itemsShop = this.items
        this.Select = 25
        this.defaultSelect = 43
        this.SelectManageTag = 0
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = true
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageTagProductShop') {
        this.itemsShop = this.items
        this.Select = 26
        this.defaultSelect = 44
        this.SelectManageTag = 1
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = true
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'manageFlashSale') {
        this.itemsShop = this.items
        this.Select = 27
        this.defaultSelect = 45
        this.SelectProMo = 3
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'bundleDeal' || this.$router.currentRoute.name === 'createbundleDeal') {
        this.itemsShop = this.items
        this.Select = 28
        this.defaultSelect = 48
        this.SelectProMo = 4
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageMembers' || this.$router.currentRoute.name === 'EditMembers' || this.$router.currentRoute.name === 'AddMembers') {
        this.itemsShop = this.items
        this.Select = 29
        this.defaultSelect = 49
        this.SelectMembers = 0
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = true
      } else if (this.$router.currentRoute.name === 'AddOnDeal' || this.$router.currentRoute.name === 'createAddOnDeal') {
        this.itemsShop = this.items
        this.Select = 30
        this.defaultSelect = 50
        this.SelectProMo = 5
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
        this.itemsPartner[0].active = false
        this.itemsMarketplace[0].active = false
        this.itemsShopAccount[0].active = false
        this.itemsLiveStreaming[0].active = false
        this.itemsManageTag[0].active = false
        this.itemsMembers[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageQTJV') {
        // จัดการบทความ
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 47
        this.checkCreateShop = false
      // this.AuthorityUsers()
      }
      for (let i = 0; i < this.itemsShop[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsShop[0].items[i].path) {
          this.defaultSelect = i
          this.Select = false
          this.SelectSalesOrder = false
          this.SelectManageTag = false
          this.SelectProMo = false
          this.SelectAffiliate = false
          this.SelectPartner = false
          this.SelectMarketplace = false
          this.SelectShopAccount = false
          this.SelectLiveStreaming = false
          this.SelectMembers = false
        }
      }
    }
  }
}
</script>

<style scoped>
.backgroundSeller {
  max-width: 100% !important;
  background: #F7FCFC;
}
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
