<template>
  <div class="text-center">
    <v-dialog v-model="EditaddressDialog" width="800" persistent>
      <v-card>
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>แก้ไขที่อยู่จัดส่งสินค้า</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-card-text v-show="!MobileSize">
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="6">
                  <span>ชื่อ<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6">
                  <span>นามสกุล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="first_name" :rules="Rules.first_name" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field class="input_text" placeholder="ระบุนามสกุล" outlined dense v-model="last_name" :rules="Rules.last_name" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <span>เบอร์โทรศัพท์<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6">
                  <span>อีเมล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" maxlength="10" outlined dense v-model="phone" :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field class="input_text" placeholder="ระบุอีเมล" outlined dense v-model="email" :rules="Rules.email"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>รายละเอียด<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="บ้านเลขที่, หมู่, ชื่ออาคาร, อพาร์ทเมนต์, ถนน, ตรอก, ซอย และอื่นๆ" outlined dense v-model="detail" :rules="Rules.address"></v-text-field>
                </v-col>
                <v-col cols="6">
                  <span>แขวง/ตำบล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6">
                  <span>เขต/อำเภอ<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-subdistrict :rules="Rules.empty" label="" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล" />
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <addressinput-district label="" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <span>จังหวัด<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6">
                  <span>รหัสไปรษณีย์<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-province label="" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="province" placeholder="ระบุจังหวัด" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <addressinput-zipcode numbered label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-text v-show="MobileSize">
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="12">
                  <span>ชื่อ<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="first_name" :rules="Rules.first_name" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>นามสกุล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุนามสกุล" outlined dense v-model="last_name" :rules="Rules.last_name" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>เบอร์โทรศัพท์<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" maxlength="10" outlined dense v-model="phone" :rules="Rules.tel" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>อีเมล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุอีเมล" outlined dense v-model="email" :rules="Rules.email"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>รายละเอียด<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="บ้านเลขที่, หมู่, ชื่ออาคาร, อพาร์ทเมนต์, ถนน, ตรอก, ซอย และอื่นๆ" outlined dense v-model="detail" :rules="Rules.address"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>แขวง/ตำบล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-subdistrict :rules="Rules.empty" label="" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistrict" placeholder="ระบุแขวง/ตำบล" />
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12">
                  <span>เขต/อำเภอ<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-district label="" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12">
                  <span>จังหวัด<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-province label="" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="province" placeholder="ระบุจังหวัด" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12">
                  <span>รหัสไปรษณีย์<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-zipcode numbered label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="save()">บันทึก</v-btn>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  props: ['EditAddressDetail', 'checkAddress'],
  data () {
    return {
      lazy: false,
      first_name: '',
      last_name: '',
      phone: '',
      detail: '',
      email: '',
      EditaddressDialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        first_name: [
          v => !!v || 'กรุณากรอกชื่อผู้รับ'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => /^[a-zA-Z0-9._-]+@[a-zA-Z0-9]+([.]?[a-zA-Z])*(\.[a-zA-Z]{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  mounted () {
    this.$EventBus.$on('EditModalAddress', this.EditModalAddress)
  },
  methods: {
    EditModalAddress () {
      this.EditaddressDialog = !this.EditaddressDialog
      if (localStorage.getItem('AddressUserDetail') !== null) {
        this.data = JSON.parse(Decode.decode(localStorage.getItem('AddressUserDetail')))
        // console.log('this.data', this.data)
        var addressData = this.data
        this.first_name = addressData.first_name
        this.last_name = addressData.last_name
        this.email = addressData.email
        this.detail = addressData.address_detail
        this.subdistrict = addressData.sub_district
        this.district = addressData.district
        this.province = addressData.province
        this.phone = addressData.phone
        this.zipcode = addressData.zipcode
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    save () {
      // console.log('ก่อนเข้าตรวจสอบข้อมูล')
      if (this.$refs.FormAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkSendAddress()
            if (check.length !== 0) {
              var data = {
                first_name: this.first_name,
                last_name: this.last_name,
                address_detail: this.detail,
                email: this.email,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                phone: this.phone,
                zipcode: this.zipcode
              }
              this.$EventBus.$emit('SentGetAddressLocal', data)
              this.$EventBus.$emit('SentGetCartLocal')
              this.$swal.fire({ icon: 'success', text: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
              this.EditaddressDialog = false
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', text: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณากรอกข้อมูลให้ครบ', showConfirmButton: false, timer: 2500 })
      }
    },
    cancel () {
      this.$refs.FormAddress.resetValidation()
      this.EditaddressDialog = !this.EditaddressDialog
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistrict
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.district
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.province
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcode)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistrict)
      this.checkdistrictConfirm(this.district)
      this.checkprovinceConfirm(this.province)
      this.checkzipcodeConfirm(this.zipcode)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style>
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
