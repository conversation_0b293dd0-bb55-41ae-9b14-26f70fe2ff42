<template>
  <div>
    <!-- Mobilyst -->
    <div v-if="useShipping === 'Mobilyst'">
      <ShippingReport v-if="!MobileSize"/>
      <ShippingReportMobile v-else />
    </div>
    <!-- iShip -->
    <div v-else>
      <IshipReport />
    </div>
  </div>
</template>

<script>
export default {
  components: {
    ShippingReport: () => import('@/components/Shop/ShippingReport/ShippingReport'),
    ShippingReportMobile: () => import('@/components/Shop/ShippingReport/ShippingReportMobile'),
    IshipReport: () => import('@/components/iShip/ManageShipping')
  },
  data () {
    return {
      useShipping: 'iShip'
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        if (this.useShipping === 'Mobilyst') {
          this.$router.push({ path: '/ShippingReportMobile' }).catch(() => {})
        }
      } else {
        if (this.useShipping === 'Mobilyst') {
          this.$router.push({ path: '/ShippingReport' }).catch(() => {})
        }
      }
    }
  }
}
</script>
