<template>
  <v-container :class="MobileSize ? 'mt-3' : ''" style="background: #FFFFFF;">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการชำระเงิน</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>รายการชำระเงิน</v-card-title>
    </v-card>
    <v-row dense class="d-flex justify-center mt-5">
      <v-col :cols="MobileSize || IpadSize ? 12 : 12" :class="MobileSize || IpadSize ? 'd-flex justify-center flex-column' :'d-flex justify-center'" :style="MobileSize || IpadSize ? '': IpadProSize ? 'gap: 2vh;' :'gap: 5vh;'">
        <v-card class="d-flex align-content-center formatBox" height="120" width="480" >
          <v-row dense class="d-flex align-center">
            <v-col cols="4" class="d-flex justify-center">
              <v-avatar rounded size="85">
                <v-img contain src="@/assets/Marketplace_partner/paymentpartner.png"></v-img>
              </v-avatar>
            </v-col>
            <v-col :cols="MobileSize || IpadProSize ? 8 : 6" class="d-flex align-center flex-column">
              <span :style="MobileSize || IpadProSize || IpadSize ? 'font-size: 18px; font-weight: bold; color: #636363;' : 'font-size: 20px; font-weight: bold; color: #636363;'">ยอดที่ต้องชำระทั้งหมด (บาท)</span>
              <span class="pt-3" style="font-size: 32px; font-weight: bold; color: #FAAD14;">{{ allPayments === '-' ? '0.00' : allPayments }}</span>
            </v-col>
          </v-row>
        </v-card>
        <v-card :class="MobileSize || IpadSize ? 'd-flex align-content-center mt-5 formatBox' : 'd-flex align-content-center formatBox'" height="120" width="480">
          <v-row dense class="d-flex align-center">
            <v-col cols="4" class="d-flex justify-center">
              <v-avatar rounded size="75">
                <v-img contain src="@/assets/Marketplace_partner/paymentTransaction.png"></v-img>
              </v-avatar>
            </v-col>
            <v-col :cols="MobileSize || IpadProSize ? 8 : 6" class="d-flex align-center flex-column">
              <span :style="MobileSize || IpadProSize || IpadSize ? 'font-size: 18px; font-weight: bold; color: #636363;' : 'font-size: 20px; font-weight: bold; color: #636363;'">จำนวน Transaction ที่ใช้จริง</span>
              <span class="pt-3" style="font-size: 32px; font-weight: bold; color: #5892D8;">{{ allTransactions === '-' ? '0.00' : allTransactions }}</span>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <!-- <v-col :cols="MobileSize || IpadSize ? 12 : 6" class="d-flex justify-center">
        <v-card class="d-flex align-content-center" elevation="2" color="white" height="120" width="480" style="border-radius: 20px;" >
          <v-row dense class="d-flex align-center">
            <v-col cols="4" class="d-flex justify-center">
              <v-avatar rounded size="75">
                <v-img contain src="@/assets/Marketplace_partner/paymentTransaction.png"></v-img>
              </v-avatar>
            </v-col>
            <v-col :cols="MobileSize || IpadProSize ? 8 : 6" class="d-flex align-center flex-column">
              <span :style="MobileSize || IpadProSize ? 'font-size: 18px; font-weight: bold; color: #636363;' : 'font-size: 20px; font-weight: bold; color: #636363;'">จำนวน Transaction ที่ใช้จริง</span>
              <span class="pt-3" style="font-size: 32px; font-weight: bold; color: #5892D8;">{{ allTransactions === '-' ? '0' : allTransactions }}</span>
            </v-col>
          </v-row>
        </v-card>
      </v-col> -->
    </v-row>
    <v-row dense :class="!MobileSize ? 'pt-10 pl-5' : 'pt-5'">
      <v-col cols="12" md="6">
        <v-text-field @keyup="checkSearch" v-model="search" dense hide-details style="border-radius: 8px;" outlined placeholder="ค้นหาข้อมูลจากรหัสการสั่งซื้อ">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row dense :class="!MobileSize ? 'pt-5 pl-6' : 'pt-5'">
      <v-col cols="12" md="5" sm="12">
        <v-row dense no-gutters>
          <v-col cols="4" class="d-flex align-center">
            <span style="font-size: 16px;">{{IpadProSize ? 'สถานะใช้งาน :' : 'สถานะการใช้งาน :'}}</span>
          </v-col>
          <v-col cols="8">
            <v-select @change="OrderList()" v-model="selectedStatus" :items="statusSelected" item-value="value" item-text="text" dense hide-details style="border-radius: 8px;" outlined></v-select>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" :md="IpadProSize ? 6 : 5" sm="12" :class="!MobileSize && !IpadSize ? 'ml-5' : 'pt-5'">
        <v-row dense no-gutters>
          <v-col cols="4" class="d-flex align-center">
            <span style="font-size: 16px;">วันที่รอบบริการ :</span>
          </v-col>
          <v-col cols="8">
            <v-dialog
              ref="modalRangeDate"
              v-model="modalRangeDate"
              :return-value.sync="dateRange"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  readonly
                  v-model="RangedateApprove"
                  v-bind="attrs"
                  v-on="on"
                  style="border-radius: 8px;"
                  outlined
                  dense
                  hide-details
                  :class="MobileSize || IpadSize ? '' : 'pr-4'"
                  placeholder="วว/ดด/ปปปป"
                  ><v-icon slot="append" color="#27AB9C"
                    >mdi-calendar-multiselect</v-icon
                  ></v-text-field>
              </template>
              <v-date-picker
                color="#27AB9C"
                v-model="dateRange"
                scrollable
                reactive
                locale="Th-th"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
              >
                <v-spacer></v-spacer>
                <v-btn text color="primary" @click="CloseModalRangeDate()">
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="setValueRangeDate(dateRange)"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <v-row dense class="pt-5"><v-row dense :class="!MobileSize ? 'pt-4 pl-6' : ''">
      <v-col cols="12" md="5" sm="6" class="d-flex align-end">
        <v-row dense no-gutters>
          <v-col :cols="!MobileSize && !IpadProSize ? 8 : 12">
            <span style="font-size: 16px;">{{IpadSize ? `ทั้งหมด ${this.showCountOrder} รายการ` : `รายการชำระเงินทั้งหมด ${this.showCountOrder} รายการ`}}</span>
          </v-col>
        </v-row>
      </v-col>
      <v-spacer></v-spacer>
      <v-col cols="12" md="5" sm="6" v-if="!MobileSize">
        <v-row dense no-gutters class="d-flex justify-end">
          <v-col cols="4" class="d-flex justify-end">
            <v-btn :disabled="disableDataTable === true" outlined rounded height="40" color="#27AB9C" class="mr-2" @click="exportExcel()">
              <v-img height="20px" width="20px" contain src="@/assets/Marketplace_partner/iconexport.png" :style="disableDataTable === true ? { filter: 'grayscale(100%) contrast(0.8)', opacity: 0.7 } : ''"></v-img>
              <span>Export File</span>
            </v-btn>
            <v-btn elevation="0" @click="MobileSize ? changeStatusProductMobile('Orders') : changeStatusProduct('Orders')" :disabled="selected.length === 0" rounded color="#27AB9C" height="40" class="white--text">
              <v-icon>mdi-cash-multiple</v-icon>
              <span>ชำระเงิน</span>
            </v-btn>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" v-if="MobileSize" class="pt-5">
        <v-btn block outlined rounded height="40" color="#27AB9C" class="mr-2" @click="exportExcel()">
          <img height="20px" width="20px" contain src="@/assets/Marketplace_partner/iconexport.png" class="mr-3"/>
          <span>Export File</span>
        </v-btn>
      </v-col>
      <v-col cols="12" v-if="MobileSize">
        <v-btn elevation="0" block @click="MobileSize ? changeStatusProductMobile('Orders') : changeStatusProduct('Orders')" :disabled="selected.length === 0" rounded color="#27AB9C" height="40" class="white--text">
          <v-icon class="mr-3">mdi-cash-multiple</v-icon>
          <span>ชำระเงิน</span>
        </v-btn>
      </v-col>
    </v-row>
    </v-row>
    <v-row dense class="mt-5" v-if="disableDataTable === false">
      <v-col cols="12">
        <v-card>
          <v-data-table @pagination="countOrder" class="elevation-1" @toggle-select-all="selectAllToggle" v-model="selected" :headers="headers" :items="items" show-select item-key="billOrderNumber" no-data-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา">
            <template v-slot:[`header.data-table-select`]>
              <!-- <v-simple-checkbox
                v-model="selectAll"
                :ripple="false"
                v-bind="props"
                v-on="on"
                color="#27AB9C"
                :indeterminate="checkboxIndeterminate"
              ></v-simple-checkbox> -->
            </template>
            <template v-slot:[`item.data-table-select`]="{isSelected, item, select}">
              <v-simple-checkbox
                @input="select($event)"
                :value="item.status !== 'ชำระเงินแล้ว' && item.status !== '-' && item.status !== 'ยกเลิกการชำระเงิน' ? isSelected : null"
                :ripple="false" v-if="item.status !== 'ชำระเงินแล้ว' && item.status !== '-' && item.status !== 'ยกเลิกการชำระเงิน'"
                color="#27AB9C"
                :disabled="isSelectionRestricted(item, isSelected)">
              </v-simple-checkbox>
            </template>
            <template v-slot:[`item.status`]="{ item }">
              <v-chip small v-if="item.status !== '-'" style="font-weight: bold;" :text-color="textColorStatus(item.status)" :color="backgroundColorStatus(item.status)">{{ item.status }}</v-chip>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.billOrderNumber`]="{ item }">
              <span style="text-decoration: underline; color: #1B5DD6 !important; cursor: pointer;" v-if="item.billOrderNumber !== '-' && item.transactionCodeInvoice !== '-'" @click="getInvoice(item.transactionCodeInvoice, 'download')">
                {{ item.billOrderNumber }}</span>
              <span v-else>-</span>
              <a v-if="item.billOrderNumber !== '-' && item.transactionCodeInvoice !== '-'" id="downloadLinkInvoice" :href="invoiceFile" style="display: none;"></a>
            </template>
            <template v-slot:[`item.serviceStartDate`]="{ item }">
              <span v-if="item.serviceStartDate !== '-' && item.serviceEndDate !== '-'">{{ item.serviceStartDate }} - {{ item.serviceEndDate }}</span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.receiptNumber`]="{ item }">
              <span style="text-decoration: underline; color: #1B5DD6 !important; cursor: pointer;" v-if="item.receiptNumber !== '-' && item.transactionCodeReceipt !== '-'" @click="getReceipt(item.transactionCodeReceipt, 'download')">
                {{ item.receiptNumber }}</span>
              <span v-else>-</span>
              <a :href="receiptFile" download="receipt.pdf" id="downloadLinkReceipt" v-if="item.receiptNumber !== '-' && item.transactionCodeReceipt !== '-'" style="display: none;"></a>
            </template>
            <template v-slot:[`item.transactionAmount`]="{ item }">
              <span>{{item.transactionAmount !== '-' ? Number(item.transactionAmount).toLocaleString(undefined) : '-' }}</span>
            </template>
            <template v-slot:[`item.action`]="{ item }">
              <v-tooltip bottom>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    width="30"
                    height="30"
                    v-bind="attrs"
                    v-on="on"
                    style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                    outlined icon @click="openDialog(item)">
                    <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                  </v-btn>
                </template>
                  <span>รายละเอียดการชำระเงิน</span>
              </v-tooltip>
              <v-btn small text rounded color="#27AB9C" @click="openDialog(item)">
                <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
              </v-btn>
          </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <v-row justify="center" align-content="center" v-else>
      <v-col cols="12" align="center">
        <div class="my-5 mt-10">
          <v-img
              src="@/assets/Marketplace_partner/noinfoPaymentPartner.png"
              max-height="550px"
              max-width="550px"
              height="100%"
              width="100%"
              contain
              aspect-ratio="2">
          </v-img>
        </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #636363">
            <span>ยังไม่มีรายการชำระเงิน</span>
          </h2>
      </v-col>
    </v-row>
    <!-- modal รายละเอียด -->
    <v-dialog v-model="modalDetails" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : IpadProSize ? '90%': '1100'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : IpadProSize ? 'width: 100%' : 'width: 1100px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายละเอียดการชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="closeDialog()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '1100px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 30px 10px 30px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="py-0">
                <v-row dense>
                  <v-col :cols="!MobileSize ? 6 : 9">
                    <span style="font-size: 18px; color: black;">สถานะ :
                      <v-chip v-if="detaildata.status !== '-'" style="font-weight: bold; font-size: 16px;" :text-color="textColorStatus(detaildata.status)" :color="backgroundColorStatus(detaildata.status)">{{ detaildata.status }}</v-chip>
                      <span v-else>-</span>
                    </span>
                  </v-col>
                  <v-spacer></v-spacer>
                  <v-col v-if="detaildata.status !== 'ชำระเงินแล้ว' && detaildata.status !== '-' && detaildata.status !== 'ยกเลิกการชำระเงิน'" :cols="!MobileSize ? 6 : 3" class="d-flex justify-end">
                    <v-btn v-if="MobileSize" small dense rounded color="primary" @click="MobileSize ? changeStatusProductMobile('oneOrder') :changeStatusProduct('oneOrder')">
                      <v-icon>mdi-cash-multiple</v-icon>
                      <span>ชำระเงิน</span>
                    </v-btn>
                    <v-btn v-else dense rounded color="primary" @click="MobileSize ? changeStatusProductMobile('oneOrder') :changeStatusProduct('oneOrder')">
                      <v-icon>mdi-cash-multiple</v-icon>
                      <span>ชำระเงิน</span>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row dense :class="MobileSize ? 'mt-10 pl-5 pb-5 d-flex flex-column' : 'mt-10 pl-5 pb-5'">
                  <v-row dense :class="MobileSize ? 'd-flex flex-column' : ''">
                    <v-col :cols="MobileSize ? 12 : 6">
                      <span style="font-size: 16px; color: #636363;">เลขประจำตัวผู้เสียภาษีอาการ : <span style="font-size: 16px; color: black;">{{ detaildata.taxId }}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? 'pt-5' : ''">
                      <span style="font-size: 16px; color: #636363;">partner : <span style="font-size: 16px; color: black;">{{ detaildata.partnerName }}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" class="pt-5" >
                      <span style="font-size: 16px; color: #636363;">วันที่รอบบริการ : <span style="font-size: 16px; color: black;">{{ detaildata.serviceStartDate }}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">จำนวน transaction : <span style="font-size: 16px; color: black;">{{ detaildata.transactionAmount }}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">จำนวนเงิน : <span style="font-size: 16px; color: black;">{{ detaildata.amountMoney === '-' ? `${detaildata.amountMoney}` : `${detaildata.amountMoney} บาท`}}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" class="pt-5">
                      <span style="font-size: 16px; color: #636363;">วันที่วางบิล : <span style="font-size: 16px; color: black;">{{ detaildata.billingDate }}</span></span>
                    </v-col>
                  </v-row>
                </v-row>
                <v-row dense class="mt-5 pl-0">
                  <v-col cols="12">
                    <!-- <a-tabs @change="SelectDetailOrder"> -->
                      <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-
                      tab-pane> -->
                      <!-- <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="1"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countOrderWaiting }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="2"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderApprove }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="3"><span slot="tab">ปฏิเสธอนุมัติ <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderReject }}</a-tag></span></a-tab-pane>
                      <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
                    </a-tabs> -->
                    <a-tabs :activeKey="tabselect" @change="SelectDetailOrder" class="custom-tabs">
                        <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
                      <a-tab-pane :key="0"><span :style="tabselect !== 0 ? 'font-weight: bold; color: #C4C4C4'  : 'font-weight: bold;'" slot="tab">ใบแจ้งหนี้</span></a-tab-pane>
                      <a-tab-pane :key="1"><span :style="tabselect !== 1 ? 'font-weight: bold; color: #C4C4C4'  : 'font-weight: bold;'" slot="tab">ใบเสร็จ</span></a-tab-pane>
                    </a-tabs>
                  </v-col>
                </v-row>
                <v-row dense v-if="tabselect === 0" class="mt-3">
                  <v-col :cols="MobileSize ? 12 : 5">
                      <span style="font-size: 16px; color: #636363;">เลขที่ใบแจ้งหนี้ : <br v-if="MobileSize || IpadSize"><span style="font-size: 16px; color: black;">{{ detaildata.invoiceNumber }}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-5'">
                      <span style="font-size: 16px; color: #636363;">วันที่ทำรายการแจ้งหนี้ : <span style="font-size: 16px; color: black;">{{ detaildata.invoiceApproveDate }}</span></span>
                  </v-col>
                </v-row>
                <v-row dense v-if="tabselect === 1" class="mt-3">
                  <v-col :cols="MobileSize ? 12 : 5">
                      <span style="font-size: 16px; color: #636363;">เลขที่ใบเสร็จ : <span style="font-size: 16px; color: black;">{{ detaildata.receiptNumber }}</span></span>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-5'">
                      <span style="font-size: 16px; color: #636363;">วันที่ทำรายการใบเสร็จ : <span style="font-size: 16px; color: black;">{{ detaildata.receiptApproveDate }}</span></span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5 justify-center" v-if="tabselect === 0">
                  <v-card dense v-if="detaildata.transactionCodeInvoice !== '-' && invoiceFile !== '-'" outlined width="100%" height="100%" class="mt-2 mb-5" :href="this.invoiceFile">
                    <v-card-text >
                      <v-row dense :class="MobileSize ? 'd-flex justify-center' : 'd-flex justify-start'">
                        <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                        </v-img>
                        <span style="text-align: center; align-content: center;" class="text-truncate pt-2 pr-2">ดาวน์โหลดใบรายละเอียดใบแจ้งหนี้</span>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <!-- <iframe v-if="detaildata.transactionCodeInvoice !== '-' && invoiceFile !== '-'" style="border: 3px solid grey; border-radius: 4px;"  :src="invoiceFile" width="100%" :height="MobileSize ? '500' : IpadSize ? '700' : '1100'"></iframe> -->
                  <span v-else style="font-size: 16px; color: black;" class="pt-10 pb-10">ไม่พบรายละเอียดใบแจ้งหนี้</span>
                </v-row>
                <v-row dense class="mt-5 justify-center" v-if="tabselect === 1">
                  <v-card dense v-if="detaildata.transactionCodeReceipt !== '-' && receiptFile !== '-'" outlined width="100%" height="100%" class="mt-2 mb-5" :href="receiptFile">
                    <v-card-text >
                      <v-row dense :class="MobileSize ? 'd-flex justify-center' : 'd-flex justify-start'">
                        <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                        </v-img>
                        <span style="text-align: center; align-content: center;" class="text-truncate pt-2 pr-2">ดาวน์โหลดใบรายละเอียดใบเสร็จ</span>
                      </v-row>
                    </v-card-text>
                  </v-card>
                  <!-- <iframe v-if="detaildata.transactionCodeReceipt !== '-' && receiptFile !== '-'" style="border: 3px solid grey; border-radius: 4px;"  :src="receiptFile" width="100%" :height="MobileSize ? '500' : IpadSize ? '700' : '1100'"></iframe> -->
                  <span v-else style="font-size: 16px; color: black;" class="pt-10 pb-10">ไม่พบรายละเอียดใบเสร็จ</span>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-actions class="pa-0" v-if="detaildata.status !== 'ชำระเงินแล้ว' && detaildata.status !== '-' && detaildata.status !== 'ยกเลิกการชำระเงิน'">
          <v-row class="d-flex align-center justify-center mt-5 ma-0 pa-0" style="height: 88px; background: #F5FCFB;">
            <v-col cols="12" class="d-flex align-center justify-center">
              <v-btn dense rounded color="primary" @click="MobileSize ? changeStatusProductMobile('oneOrder') :changeStatusProduct('oneOrder')">
                <v-icon>mdi-cash-multiple</v-icon>
                <span>ชำระเงิน</span></v-btn>
            </v-col>
          </v-row>
        </v-card-actions> -->
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Tabs } from 'ant-design-vue'
import { Decode } from '@/services'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane
  },
  data () {
    return {
      loading: true,
      invoiceFile: '-',
      receiptFile: '-',
      orderNumber: '',
      disableDataTable: true,
      showCountOrder: 0,
      allTransactions: '',
      allPayments: '',
      tabselect: 0,
      detaildata: [],
      modalDetails: false,
      startDateRange: '',
      endDateRange: '',
      dateRange: '',
      RangedateApprove: '',
      modalRangeDate: false,
      selectedStatus: '',
      statusSelected: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'รอชำระเงิน', value: 'unpaid' },
        { text: 'ชำระเงินแล้ว', value: 'paid' },
        { text: 'เกินกำหนดชำระ', value: 'overdue_paid' },
        { text: 'ยกเลิกการชำระเงิน', value: 'canceled' }
      ],
      search: '',
      checkboxIndeterminate: false,
      singleSelect: false,
      selected: [],
      selectAll: false,
      headers: [
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'purchaseOrderNumber',
          sortable: false,
          align: 'start',
          width: '200px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะดำเนินการ',
          value: 'status',
          sortable: false,
          align: 'start',
          width: '190px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Partner',
          value: 'partnerName',
          sortable: false,
          align: 'start',
          width: '200px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จำนวน Transaction',
          value: 'transactionAmount',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบแจ้งหนี้',
          value: 'billOrderNumber',
          sortable: false,
          align: 'start',
          width: '250px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่รอบบริการ',
          value: 'serviceStartDate',
          sortable: false,
          align: 'start',
          width: '220px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขใบเสร็จ',
          value: 'receiptNumber',
          sortable: false,
          align: 'start',
          width: '250px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จำนวนเงิน',
          value: 'amountMoney',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'action',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      items2: [],
      items: []
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    await this.OrderList()
    if (this.items.length > 0) {
      this.disableDataTable = false
    } else {
      this.disableDataTable = true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    // dateRange (val) {
    //   // console.log('dateRange', val)
    //   this.startDateToSend = val[0] !== undefined ? val[0] : ''
    //   this.endDateToSend = val[1] !== undefined ? val[1] : ''
    //   var contractStartDate = val[0] !== undefined ? this.formatDateToShow(val[0]) : ''
    //   var contractEndDate = val[1] !== undefined ? this.formatDateToShow(val[1]) : ''
    //   if (contractStartDate !== '' && contractEndDate !== '') {
    //     this.RangedateApprove = contractStartDate + ' - ' + contractEndDate
    //   } else {
    //     this.RangedateApprove = ''
    //   }
    // },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/paymentPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentPartner' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    isSelectionRestricted (item, isSelected) {
      if (this.selected.length === 0) {
        return false // ยังไม่มีการเลือกใด ๆ
      }
      const selectedPartner = this.selected[0].partnerName // ใช้ partnerName ของตัวแรกที่ถูกเลือก
      return this.selected.length > 0 && !isSelected && item.partnerName !== selectedPartner
    },
    countOrder (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    checkSearch () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.OrderList()
      }, 1000)
    },
    SelectDetailOrder (val) {
      this.tabselect = val
    },
    async openDialog (val) {
      // this.detaildata = []
      // this.modalDetails = true
      this.$store.commit('openLoader')
      this.detaildata = []
      this.orderNumber = ''
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopID,
        billOrderNumber: val.billOrderNumber
      }
      await this.$store.dispatch('actionDetailOrderPaymentPartner', data)
      var respons = await this.$store.state.ModuleOrder.stateDetailOrderPaymentPartner
      if (respons.code === 200) {
        if (respons.message !== 'Success, not found payment detail in this order') {
          // this.modalDetails = true
          this.orderNumber = respons.billOrderNumber
          this.detaildata = respons.data[0]
          this.invoiceFile = '-'
          this.receiptFile = '-'
          if (this.detaildata.transactionCodeInvoice !== '-' || this.detaildata.transactionCodeReceipt !== '-') {
            if (this.detaildata.transactionCodeInvoice !== '-') {
              this.getInvoice(this.detaildata.transactionCodeInvoice, 'preview')
            }
            if (this.detaildata.transactionCodeReceipt !== '-') {
              this.getReceipt(this.detaildata.transactionCodeReceipt, 'preview')
            }
            this.$store.commit('closeLoader')
          } else {
            this.invoiceFile = '-'
            this.receiptFile = '-'
            this.$store.commit('closeLoader')
          }
          this.modalDetails = true
          // this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
          })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    async getReceipt (val, type) {
      this.receiptFile = '-'
      var data = {
        transactionCode: val
      }
      await this.$store.dispatch('actionsPartnerEtaxDocumentReceipt', data)
      var response = this.$store.state.ModuleBusiness.statePartnerEtaxDocumentReceipt
      if (response.result === 'OK') {
        var receipt = response.etaxResponse.pdfURL
        if (type === 'preview') {
          this.receiptFile = receipt
        } else {
          this.receiptFile = receipt
          setTimeout(() => {
            document.getElementById('downloadLinkReceipt').click()
          }, 200)
          // window.open(receipt, '_blank')
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>ไม่สามารถแสดงใบเสร็จได้</h3>'
        })
      }
    },
    async getInvoice (val, type) {
      this.invoiceFile = '-'
      var data = {
        transactionCode: val
      }
      await this.$store.dispatch('actionsPartnerEtaxDocumentInvoice', data)
      var response = this.$store.state.ModuleBusiness.statePartnerEtaxDocumentInvoice
      if (response.result === 'OK') {
        var invoice = response.etaxResponse.pdfURL
        if (type === 'preview') {
          this.invoiceFile = invoice
        } else {
          this.invoiceFile = invoice
          setTimeout(() => {
            document.getElementById('downloadLinkInvoice').click()
          }, 200)
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>ไม่สามารถแสดงใบแจ้งหนี้ได้</h3>'
        })
        this.$store.commit('closeLoader')
      }
    },
    closeDialog () {
      this.modalDetails = false
      this.orderNumber = ''
      this.detaildata = []
      this.tabselect = 0
    },
    async changeStatusProduct (type) {
      var productIdToChange = []
      if (type === 'oneOrder') {
        productIdToChange.push(this.orderNumber)
      } else if (type === 'Orders') {
        this.selected.forEach(item => {
          productIdToChange.push(item.billOrderNumber)
        })
      }
      var paymentTypePartner = 'renew'
      localStorage.setItem('orderPartner', JSON.stringify(productIdToChange))
      localStorage.setItem('paymentTypePartner', paymentTypePartner)
      this.$router.push({ path: '/paymentList' }).catch(() => {})
    },
    async changeStatusProductMobile (type) {
      var productIdToChange = []
      if (type === 'oneOrder') {
        productIdToChange.push(this.orderNumber)
      } else if (type === 'Orders') {
        this.selected.forEach(item => {
          productIdToChange.push(item.billOrderNumber)
        })
      }
      var paymentTypePartner = 'renew'
      localStorage.setItem('orderPartner', JSON.stringify(productIdToChange))
      localStorage.setItem('paymentTypePartner', paymentTypePartner)
      this.$router.push({ path: '/paymentListMobile' }).catch(() => {})
    },
    async OrderList () {
      this.$store.commit('openLoader')
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      var data = {
        seller_shop_id: shopID,
        startDate: this.RangedateApprove,
        search: this.search,
        status: this.selectedStatus
      }
      await this.$store.dispatch('actionOrderPaymentPartner', data)
      var respons = await this.$store.state.ModuleOrder.stateOrderPaymentPartner
      if (respons.code === 200) {
        this.selected = []
        this.selectAll = false
        this.checkboxIndeterminate = false
        this.items = respons.data
        this.allPayments = respons.totalAmountDue
        this.allTransactions = respons.transactionsActuallyUsed
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    async CloseModalRangeDate () {
      this.$refs.modalRangeDate.save('')
      this.modalRangeDate = false
      this.dateRange = ''
      this.RangedateApprove = ''
      this.OrderList()
    },
    async setValueRangeDate (val) {
      this.$refs.modalRangeDate.save(val)
      // var Range = await val.sort((a, b) => {
      //   var dateA = new Date(a)
      //   var dateB = new Date(b)
      //   return dateA - dateB
      // })
      // var Range = val
      this.RangedateApprove = this.formatDateToShow(this.dateRange)
      // this.startDateRange = this.dateRange[0]
      // this.endDateRange = this.dateRange[1]
      // if (!this.MobileSize) {
      //   await this.getOrderList()
      // }
      // this.modalRangeDate = false
      this.OrderList()
    },
    selectAllToggle () {
      if (this.selected.length !== this.items.length && this.selectAll) {
        this.selected = []
        const self = this
        if (this.search !== '') {
          this.items.forEach(item => {
            if (item.status !== 'ชำระเงินแล้ว') {
              self.selected.push(item)
            }
          })
        } else {
          this.items.forEach(item => {
            if (item.status !== 'ชำระเงินแล้ว') {
              self.selected.push(item)
            }
          })
        }
        // this.items.forEach(item => {
        //   if (item.status !== 'ชำระเงินแล้ว') {
        //     self.selected.push(item)
        //   }
        // })
        if (this.selected.length > 0 && (this.items.length === this.selected.length)) {
          this.selectAll = true
          this.checkboxIndeterminate = false
        } else if (this.selected.length > 0) {
          this.checkboxIndeterminate = true
        }
      } else {
        this.selected = []
        this.selectAll = false
        this.checkboxIndeterminate = false
      }
    },
    async exportExcel () {
      var shopID = JSON.parse(localStorage.getItem('shopSellerID'))
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        seller_shop_id: shopID
      }
      try {
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}exports/list_payment_partner`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          responseType: 'blob',
          data: data
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          const date = new Date().getDate().toString().padStart(2, '0') + '_' + (new Date().getMonth() + 1).toString().padStart(2, '0') + '_' + new Date().getFullYear()
          const time = new Date().getHours().toString().padStart(2, '0') + '_' + new Date().getMinutes().toString().padStart(2, '0')
          fileLink.setAttribute('download', 'payment_list_' + date + '_' + time + '.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      } catch (error) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณาติดต่อเจ้าหน้าที่</h3>'
        })
      }
    },
    backgroundColorStatus (val) {
      if (val === 'รอชำระเงิน') {
        return '#EDF2F8'
      } else if (val === 'ชำระเงินแล้ว') {
        return '#F0F9EE'
      } else {
        return '#FEE7E8'
      }
    },
    textColorStatus (val) {
      if (val === 'รอชำระเงิน') {
        return '#1B5DD6'
      } else if (val === 'ชำระเงินแล้ว') {
        return '#52C41A'
      } else {
        return '#F5222D'
      }
    },
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    onLoad () {
      // this.isloading = false
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
::v-deep .custom-tabs .ant-tabs-ink-bar {
    background-color: #27AB9C !important;
  }
  .formatBox {
    border: 1px solid #cbeae0;
    border-radius: 20px;
    box-shadow: rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px !important;
  }
</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(10) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(10) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
