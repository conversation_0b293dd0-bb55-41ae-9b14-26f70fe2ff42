<template>
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="pb-0" v-if="!MobileSize">{{ $t('Affiliate.AffiliateShop') }}</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;" class="pb-0" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>{{ $t('Affiliate.AffiliateShop') }}</v-card-title>

      <v-divider class="my-4"></v-divider>

      <v-card-text class="px-2">
        <v-row dense style="padding-bottom: 10px;">
          <v-col cols="12" md="4" sm="12">
            <v-text-field v-model="search" @keyup="searchData(search)" outlined dense style="border-radius: 8px;" hide-details :placeholder="this.$t('Affiliate.SearchShop')" append-icon="mdi-magnify"></v-text-field>
          </v-col>
        </v-row>

        <div style="padding-top: 20px; padding-bottom: 20px;" v-if="this.search === ''">
          <v-row align="center" style="justify-content: center;">
            <!-- <v-col cols="auto">
              <span style="font-size: 16px; font-weight: 400; padding-right: 10px; color: #333333;">เรียงลำดับตาม</span>
            </v-col> -->
            <v-col cols="auto">
              <v-tabs v-model="activeTab" @change="getAffiliateReturn" show-arrows>
                <v-tab :key="0">
                  <span style="text-transform: none;">{{ $t('Affiliate.AllShops') }} <v-chip color="#27AB9C" class="white--text">{{ countAll }}</v-chip></span>
                </v-tab>
                <v-tab :key="1">
                  <span style="text-transform: none;">{{ $t('Affiliate.JoinAffiliate') }} <v-chip color="#1AB759" class="white--text">{{ countApprove }}</v-chip></span>
                </v-tab>
                <v-tab :key="2">
                  <span style="text-transform: none;">{{ $t('Affiliate.WaitingForApproval') }} <v-chip color="#FAD02C" class="white--text">{{ countWaiting }}</v-chip></span>
                </v-tab>
                <v-tab :key="3">
                  <span style="text-transform: none;">{{ $t('Affiliate.NotJoined') }} <v-chip color="#FFA500" class="white--text">{{ countNotJoin }}</v-chip></span>
                </v-tab>
                <v-tab :key="4">
                  <span style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }} <v-chip color="#D1392B" class="white--text">{{ countCancel }}</v-chip></span>
                </v-tab>
              </v-tabs>
            </v-col>
          </v-row>
        </div>

        <div v-if="dataTable.length > 0">
        <h4 v-if="MobileSize && this.search === ''" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600; text-align: center;" class="mb-3 mt-1">
            {{ pageHeaders }} {{ countAffiliate }} {{ $t('Affiliate.List') }}
        </h4>

        <div style="padding-top: 15px;">
            <v-data-table
              v-model="selected"
              @toggle-select-all="selectAllToggle"
              :headers="headers"
              :items="dataTable"
              item-key="name_th"
              color="blue"
              show-select
              :page.sync="page"
              :footer-props="{'items-per-page-text':'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
              :items-per-page="options.itemsPerPage"
              :server-items-length="countAffiliate"
              :options.sync="options"
              @update:options="updateOptions"
              class="elevation-1"
              :no-results-text="this.$t('Affiliate.NoShopFound')"
              :no-data-text="this.$t('Affiliate.NoAffiliateShop')"
              :hide-default-footer="search !== ''"
            >

            <template v-slot:[`header.data-table-select`]="{ on, props }">
                <v-simple-checkbox
                  v-model="checkboxAll"
                  :indeterminate="checkboxIndeterminate"
                  v-ripple
                  v-bind="props"
                  v-on="on"
                ></v-simple-checkbox>
            </template>
            <template v-slot:[`item.data-table-select`]="{ item, isSelected, select }">
              <v-simple-checkbox
                :value="isSelected"
                :readonly="item.buyer_status === 'Approve' || item.buyer_status === 'Waiting' || item.buyer_status === 'Cancel'"
                :disabled="item.buyer_status === 'Approve' || item.buyer_status === 'Waiting' || item.buyer_status === 'Cancel'"
                @input="select($event)"
                @click="chackselectitem()">
              </v-simple-checkbox>
            </template>
            <template v-slot:[`item.name_th`]="{ item }">
              <v-row align="center">
                <v-col cols="auto">
                  <v-img width="50" height="70" src="@/assets/NoImage.png" v-if="item.lazy_logo === ''" contain></v-img>
                  <v-img width="50" height="70" :src="`${item.logo}`" v-else contain></v-img>
                </v-col>
                <v-col style="text-align: start;">
                  <span>{{ item.name_th }}</span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.max_commission`]="{ item }">
              <v-row align="center">
                <v-col style="text-align: center;">
                  <span v-if="(item.max_commission !== null || item.max_commission !== 0) && item.type_commission !== null">
                    {{ $t('Affiliate.UpTo') }} {{ item.max_commission }} {{ item.type_commission === 'percent' ? '%' : '฿' }}
                  </span>
                  <span v-else-if="(item.max_commission === null || item.max_commission === 0) && item.type_commission === null">
                    {{ $t('Affiliate.NotSpecified') }}
                  </span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.created`]="{ item }">
                <v-col style="text-align: start;">
                  <span>{{ $t('Affiliate.StartDate') }} : {{ item.created }}</span><br>
                  <span>{{ $t('Affiliate.EndDate') }} : {{ $t('Affiliate.Unlimited') }}</span>
                </v-col>
            </template>
            <template v-slot:[`item.buyer_status`]="{ item }">
              <span v-if="item.buyer_status === 'Approve'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">{{ $t('Affiliate.JoinAffiliate') }}</v-chip>
              </span>
              <span v-if="item.buyer_status === 'Waiting'">
                <v-chip class="ma-2" color="#fff9de" text-color="#FAD02C">{{ $t('Affiliate.WaitingForApproval') }}</v-chip>
              </span>
              <span v-else-if="item.buyer_status === 'Not Joined'">
                <v-chip class="ma-2" color="#FCF0DA" text-color="#FFA500">{{ $t('Affiliate.NotJoinedAffiliate') }}</v-chip>
              </span>
              <span v-else-if="item.buyer_status === 'Cancel' || item.buyer_status === 'Reject' ">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">{{ $t('Affiliate.CancelRequest') }}</v-chip>
              </span>
            </template>

              <template v-slot:[`item.callcuriers`]="{ item }">
                <v-row v-if="item.buyer_status === 'Not Joined'">
                  <v-col>
                    <v-btn small dense class="ma-2" outlined color="#27AB9C" @click="OpenDialogSelectAffiliate(item)">
                      <B style="text-transform: none;">{{ $t('Affiliate.JoinAffiliate') }}</B>
                    </v-btn>
                  </v-col>
                </v-row>
                <v-row v-else-if="item.buyer_status === 'Approve'">
                  <v-col>
                    <v-btn small dense class="ma-2" outlined color="#1AB759" @click="showProductSellerAffiliate(item)">
                      <B style="text-transform: none;">{{ $t('Affiliate.ViewProducts') }}</B>
                    </v-btn>
                  </v-col>
                </v-row>
                <!-- <v-row v-else>
                  <v-col>
                    <v-btn outlined class="custom-btn-style">
                      <v-icon>mdi-dots-vertical</v-icon>
                    </v-btn>
                  </v-col>
                </v-row> -->
              </template>

          </v-data-table>
        </div>

        <div style="padding-top: 10px;">
              <v-container v-if="!MobileSize">
                <v-row dense justify="end">
                    <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true" outlined
                      @click="OpenDialogDelete()"><B>ยกเลิก</B></v-btn>
                    <v-btn dense color="#27AB9C" :disabled="selected.length !== 0? false:true"
                      class="ml-1 white--text" @click="OpenDialogCallCurierAll()">
                      <B style="text-transform: none;">{{ $t('Affiliate.JoinAllAffiliateShops') }} ({{this.selected.length}})</B>
                    </v-btn>
                </v-row>
              </v-container>
              <v-container v-if="MobileSize">
                <v-row class="justify-end">
                  <v-col cols="3">
                    <v-btn small dense color="#27AB9C" :disabled="selected.length !== 0? false:true" outlined
                      @click="OpenDialogDelete()"><B style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</B></v-btn>
                  </v-col>
                  <v-col cols="9">
                    <v-btn small dense color="#27AB9C" :disabled="selected.length !== 0? false:true"
                      class="ml-1 white--text" @click="OpenDialogCallCurierAll()">
                        <B style="text-transform: none;">{{ $t('Affiliate.JoinAllAffiliateShops') }} ({{this.selected.length}})</B>
                    </v-btn>
                  </v-col>
                </v-row>
              </v-container>
        </div>
      </div>
      </v-card-text>

      <v-container v-if="dataTable.length === 0">
        <v-row justify="center" align-content="center" >
          <v-col cols="12" md="12" align="center" style="min-height: 636px;">
            <div style="padding-top: 90px;">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
              <span v-if="this.search !== ''" style="font-weight: bold; font-size: 20px; line-height: 32px;">{{ $t('Affiliate.NoShopFound') }}</span><br />
              <span v-if="this.search === ''" style="font-weight: bold; font-size: 20px; line-height: 32px;">{{ $t('Affiliate.NoItems') }} ({{ emptyPageText }})</span><br />
            </h2>
          </v-col>
        </v-row>
      </v-container>
    </v-card>

      <v-dialog v-model="dialog" persistent max-width="550" style="overflow: hidden;">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">{{ $t('Affiliate.JoinAffiliate') }}</font>
          </span>
          <v-btn icon dark @click="Close()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card width="100%" style="overflow: hidden;" align="center">
          <v-card-text class="pt-6">
            <!-- <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">กรุณาตรวจสอบก่อนที่จะทำการยืนยันการ Affiliate กับร้านค้า</span> -->
            <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">{{ $t('Affiliate.ConfirmShopNameBeforeJoining') }}</span>
          </v-card-text>
          <v-card-actions class="pb-4">
            <v-spacer></v-spacer>
            <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
            <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessSelectJoinAffiliate()" style="text-transform: none;">{{ $t('Affiliate.Confirm') }}</v-btn>
            <v-spacer></v-spacer>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogAllselect" persistent max-width="550" style="overflow: hidden;">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C" style="text-transform: none;">{{ $t('Affiliate.JoinAllAffiliateShops') }}</font>
          </span>
          <v-btn icon dark @click="Close()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card width="100%" style="overflow: hidden;" align="center">
          <v-card-text class="pt-6">
            <!-- <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">กรุณาตรวจสอบก่อนที่จะทำการยืนยันการ Affiliate กับร้านค้า</span> -->
            <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">{{ $t('Affiliate.ConfirmShopNameBeforeJoining') }}</span>
          </v-card-text>
          <v-card-actions class="pb-4">
            <v-spacer></v-spacer>
            <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
            <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="SuccessSelectJoinAffiliateAll()" style="text-transform: none;">{{ $t('Affiliate.Confirm') }}</v-btn>
            <v-spacer></v-spacer>
          </v-card-actions>
        </v-card>
      </v-dialog>

    <v-dialog v-model="dialog_Delete" width="550" persistent>
      <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
        <span
          class="flex text-center ml-5"
          style="font-weight: bold"
          :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
        >
          <font color="#27AB9C">{{ $t('Affiliate.CancelSelection') }}</font>
        </span>
        <v-btn icon dark @click="Close()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card align="center">
        <v-card-text class="pt-6">
          <!-- <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">กรุณาตรวจสอบก่อนที่จะยกเลิกการ Affiliate ทั้งหมด</span> -->
          <span style="white-space: normal; display: inline-block; word-break:break-word; font-size: 16px">{{ $t('Affiliate.ConfirmShopNameBeforeCancelAll') }}</span>
        </v-card-text>
        <v-card-actions class="pb-4">
          <v-spacer></v-spacer>
          <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()" style="text-transform: none;">{{ $t('Affiliate.CancelRequest') }}</v-btn>
          <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="CancelSelect()" style="text-transform: none;">{{ $t('Affiliate.Confirm') }}</v-btn>
          <v-spacer></v-spacer>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      activeTab: null,
      sortBy: 'latest',
      search: '',
      DataAffiliate: '',
      checkboxAll: false,
      checkboxIndeterminate: false,
      disabledCount: 0,
      showCountAffiliate: '',
      pageHeaders: '',
      emptyPageText: '',
      userId: '',
      sellerShopId: '',
      dialog: false,
      dialog_Delete: false,
      dialogAllselect: false,
      buyerHasJoinArray: [],
      dataTable: [],
      countAffiliate: 0,
      countApprove: 0,
      countWaiting: 0,
      countNotJoin: 0,
      countCancel: 0,
      countAll: 0,
      selected: [],
      currentTab: 0,
      selectedStatus: null,
      selectedUserId: null,
      page: 1,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      headers: [
        { text: this.$t('Affiliate.DataTable.ShopName'), value: 'name_th', width: '200', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: this.$t('Affiliate.DataTable.OfferDuration'), value: 'created', width: '170', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: this.$t('Affiliate.DataTable.CommissionRate'), value: 'max_commission', width: '170', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: this.$t('Affiliate.DataTable.Status'), value: 'buyer_status', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: this.$t('Affiliate.DataTable.Note'), value: 'remark', sortable: false, width: '200', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: this.$t('Affiliate.DataTable.Action'), value: 'callcuriers', sortable: false, width: '200', align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  async created () {
    this.checkConsent()
    this.$EventBus.$emit('changeNavAccount')
    // await this.getAllShopAffiliate(this.search)
    await this.autoJoinAffiliate()
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      if (this.searchNameProduct !== '') {
        return this.AllProduct.filter(item => {
          return item.name.toLowerCase().includes(this.searchNameProduct.toLocaleLowerCase())
        })
      } else {
        return this.AllProduct.slice(this.indexStart, this.indexEnd)
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/showShopSellerAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/showShopSellerAffiliate' }).catch(() => {})
      }
    }
  },
  methods: {
    async autoJoinAffiliate () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAutoJoin')
      var response = await this.$store.state.ModuleAffiliate.stateAutoJoin
      if (response.message === 'Buyer auto approve process completed') {
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: response.message
        })
      }
    },
    chackselectitem () {
      if (this.selected.length === 1) {
        this.checkboxAll = false
        this.checkboxIndeterminate = false
      }
    },
    backtoUserMenu () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    OpenDialogDelete () {
      this.dialog_Delete = true
    },
    OpenDialogCallCurierAll () {
      this.dialogAllselect = true
    },
    async updateOptions (options) {
      this.options = options
      this.page = options.page
      await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
    },
    searchData (val) {
      if (this.timer) {
        clearTimeout(this.timer)
      }
      this.timer = setTimeout(() => {
        this.page = 1 // รีเซ็ตหน้าทุกครั้งเมื่อค้นหา
        this.getAllShopAffiliate(val, this.selectedStatus, this.selectedUserId)
      }, 500)
    },
    async getAllShopAffiliate (textSearch, status, userId) {
      this.$store.commit('openLoader')

      if (textSearch === '') {
        const onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        const data = {
          status,
          user_id: onedata.user.user_id,
          search: this.search,
          page: this.page,
          limit: this.options.itemsPerPage
        }

        await this.$store.dispatch('actionsAffiliateListShop', data)
        var response = await this.$store.state.ModuleAffiliate.stateAffiliateListShop

        if (response.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
          return
        }

        if (response.success) {
          this.$EventBus.$emit('changeNav')
          this.selected = []
          this.checkboxAll = false
          this.checkboxIndeterminate = false
          this.dataTable = response.data

          this.countApprove = response.isApproveCount
          this.countWaiting = response.isWaitingCount
          this.countNotJoin = response.isNotJoinCount
          this.countCancel = response.isCancelCount
          this.countAffiliate = response.all
          this.countAll = response.all

          if (this.currentTab === 1) {
            this.countAffiliate = this.countApprove
          } else if (this.currentTab === 2) {
            this.countAffiliate = this.countWaiting
          } else if (this.currentTab === 3) {
            this.countAffiliate = this.countNotJoin
          } else if (this.currentTab === 4) {
            this.countAffiliate = this.countCancel
          }

          this.$store.commit('closeLoader')
        }
      } else {
        this.$store.commit('openLoader')

        const onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        const data = {
          user_id: onedata.user.user_id,
          keyword: textSearch
        }

        await this.$store.dispatch('actionsAffiliateSearchShop', data)
        var responseSearch = await this.$store.state.ModuleAffiliate.stateAffiliateSearchShop

        if (responseSearch.message === 'Search shop success') {
          this.$store.commit('closeLoader')
          this.dataTable = responseSearch.data
        }
      }
    },
    selectAllToggle (props) {
      // if (this.search === '') {
      if (this.selected.length !== this.dataTable.length - this.disabledCount && this.checkboxAll) {
        // console.log('เลือก', this.checkboxAll)
        this.selected = []
        const self = this
        this.dataTable.forEach(item => {
          if (item.buyer_status === 'Not Joined' || item.buyer_status === 'Reject' || item.buyer_status === 'Cancel') {
            self.selected.push(item)
          }
        })
        if (this.selected.length > 0 && this.dataTable.length === this.selected.length) {
          // console.log('เลือกได้ทั้งหมดเลย')
          this.checkboxAll = true
          this.checkboxIndeterminate = false
        } else if (this.selected.length > 0) {
          // console.log('เลือกได้ทั้งหมดที่เลือกได้')
          this.checkboxIndeterminate = true
        }
      } else {
        this.selected = []
        this.checkboxAll = false
        this.checkboxIndeterminate = false
      }
      // }
    },
    async getAffiliateReturn (item) {
      this.selected = []
      this.currentTab = item

      if (item === 0) {
        this.selectedStatus = null
        this.selectedUserId = null
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
        this.pageHeaders = this.$t('Affiliate.AllShopsAffiliate')
        this.emptyPageText = this.$t('Affiliate.AllShops')
      } else if (item === 1) {
        this.selectedStatus = 'approve'
        this.selectedUserId = this.userId
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
        this.pageHeaders = this.$t('Affiliate.JoinAll')
        this.emptyPageText = this.$t('Affiliate.JoinAffiliate')
      } else if (item === 2) {
        this.selectedStatus = 'Waiting'
        this.selectedUserId = this.userId
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
        this.pageHeaders = this.$t('Affiliate.WaitingAll')
        this.emptyPageText = this.$t('Affiliate.WaitingForApproval')
      } else if (item === 3) {
        this.selectedStatus = 'Not Joined'
        this.selectedUserId = this.userId
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
        this.pageHeaders = this.$t('Affiliate.RejectAll')
        this.emptyPageText = this.$t('Affiliate.NotJoined')
      } else if (item === 4) {
        this.selectedStatus = 'Cancel'
        this.selectedUserId = this.userId
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
        this.pageHeaders = this.$t('Affiliate.CancelRequest')
        this.emptyPageText = this.$t('Affiliate.CancelRequest')
      }
    },
    OpenDialogSelectAffiliate (item) {
      this.dialog = true
      this.DataAffiliate = item
      this.sellerShopId = item.seller_shop_id
    },
    async confirmJoinAffiliate () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))

      var data = {
        seller_shop_id: [this.sellerShopId],
        user_id: onedata.user.user_id
      }

      await this.$store.dispatch('actionsAffiliateBuyerJoinSeller', data)
      var responseConfirmJoin = await this.$store.state.ModuleAffiliate.stateAffiliateBuyerJoinSeller
      // console.log(responseConfirmJoin)
      if (responseConfirmJoin.message === 'join success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: this.$t('Affiliate.WaitingApproval')
        })
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
      } else if (responseConfirmJoin.message === 'Approve') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: this.$t('Affiliate.JoinSuccess')
        })
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
      }
      this.$store.commit('closeLoader')
    },
    async SuccessSelectJoinAffiliate () {
      this.dialog = false
      this.$store.commit('openLoader')
      this.confirmJoinAffiliate()
    },
    async SuccessSelectJoinAffiliateAll () {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.dialog = false
      this.$store.commit('openLoader')

      const sellerShopIds = this.selected.map(item => String(item.seller_shop_id))

      var data = {
        seller_shop_id: sellerShopIds,
        user_id: onedata.user.user_id
      }

      await this.$store.dispatch('actionsAffiliateBuyerJoinSeller', data)
      var responsSelectJoinAffiliateAll = await this.$store.state.ModuleAffiliate.stateAffiliateBuyerJoinSeller

      if (responsSelectJoinAffiliateAll.message === 'join success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: this.$t('Affiliate.WaitingApproval')
        })
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
      } else if (responsSelectJoinAffiliateAll.message === 'Approve') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'info',
          title: this.$t('Affiliate.JoinSuccess')
        })
        await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
      }

      this.dialogAllselect = false
      this.selected = []
      await this.getAllShopAffiliate(this.search, this.selectedStatus, this.selectedUserId)
      this.$store.commit('closeLoader')
    },
    CancelSelect () {
      this.selected = []
      this.dialog_Delete = false
    },
    Close () {
      this.dialog_Delete = false
      this.dialog = false
      this.dialogAllselect = false
      this.DataAffiliate = ''
    },
    async showProductSellerAffiliate (item) {
      await this.$EventBus.$emit('clearAllProductAffiliate')
      await this.$EventBus.$emit('clearAllProductShowProductSellerJoinAffiliate')
      this.$router.push(`/showProductSellerJoinAffiliateMobile?shopID=${item.seller_shop_id}`)
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.custom-btn-style {
  width: 50px;
  height: 50px;
  border: 1px solid #F2F2F2;
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
}
.text-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.text-break {
  white-space: normal;
  display: inline-block;
  word-break: break-word;
  max-width: 600px;
}
.v-data-table /deep/ .v-data-table-header-mobile__wrapper {
    display: flex;
    justify-content: end;
  }
  .v-data-table /deep/ .v-simple-checkbox {
    padding-left: 25px;
  }
  .v-data-table /deep/ .v-data-footer {
    font-size: 0.62rem;
  }
  .whiteNumber {
    color: #FFF;
  }
</style>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
</style>
