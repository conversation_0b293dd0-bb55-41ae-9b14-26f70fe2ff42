<template>
    <v-container :class="MobileSize ? 'mt-3' : ''">
        <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
        <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ร้านค้าเชื่อมต่อ Partner</v-card-title>
        <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ร้านค้าเชื่อมต่อ Partner</v-card-title>
        <v-card-text>
            <v-row dense>
            <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
                <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้า Partner" outlined dense hide-details style="border-radius: 10px;">
                <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="12">
                <v-row>
                  <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 12" :class="IpadSize ? 'pt-0 d-flex flex-column' : MobileSize ? 'pt-0 d-flex flex-column'  : 'pt-3 pb-0 d-flex flex-row'">
                    <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200;" v-if="(!MobileSize && !IpadSize)">รายการร้านค้าเชื่อมต่อ ทั้งหมด {{ countShop }} ร้านค้า</span>
                    <span class="pb-2 pt-2" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายการร้านค้าเชื่อมต่อ ทั้งหมด {{ countShop }} ร้านค้า</span>
                    <v-spacer v-if="(!MobileSize && !IpadSize)"></v-spacer>
                    <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200; color: #989898;" v-if="(!MobileSize && !IpadSize)">จำนวนร้านค้าเชื่อมต่อทั้งหมด : <span style="color: black;">{{ amountShop }}  ร้านค้า</span></span>
                    <span class="pb-0" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200; color: #989898;" v-else-if="(MobileSize || IpadSize)">จำนวนร้านค้าเชื่อมต่อทั้งหมด : <span style="color: black;">{{ amountShop }}  ร้านค้า</span></span>
                    <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 200; color: #989898;" v-if="(MobileSize || IpadSize)">จำนวนร้านค้าเชื่อมต่อทั้งหมด : <span style="color: black;">{{ amountShop }}  ร้านค้า</span></span> -->
                  </v-col>
                </v-row>
                <v-row dense v-if="noInfo === false">
                  <v-col cols="12">
                    <v-data-table
                      :headers="header"
                      :items="filteredData"
                      :search="search"
                      class="elevation-1 mt-4"
                      :items-per-page="10"
                      style="width:100%;"
                      height="100%">
                        <template v-slot:[`item.partner_code`]="{item}">
                            <!-- <v-btn :disabled="item.user_count === 0" outlined color="#27AB9C" @click="MobileSize ? openDialogDetailsMobile(item) : openDialogDetails(item)">ดูรายชื่อ</v-btn> -->
                            <v-tooltip bottom>
                                <template v-slot:activator="{ on, attrs }">
                                    <v-btn
                                    width="30"
                                    height="30"
                                    v-bind="attrs"
                                    v-on="on"
                                    style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                                    outlined icon @click="MobileSize ? openDialogDetailsMobile(item) : openDialogDetails(item)">
                                    <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                                    </v-btn>
                                </template>
                                <span>รายละเอียดการเชื่อมต่อร้านค้า</span>
                            </v-tooltip>
                            <v-btn small text rounded color="#27AB9C" @click="MobileSize ? openDialogDetailsMobile(item) : openDialogDetails(item)">
                                <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
                            </v-btn>
                        </template>
                    </v-data-table>
                  </v-col>
                </v-row>
                <v-row justify="center" align-content="center" v-else>
                    <v-col cols="12" align="center">
                      <div class="my-5">
                        <v-img
                            :src="checkData === false ? require('@/assets/noinfo.png') : require('@/assets/noExist.png')"
                            max-height="500px"
                            max-width="500px"
                            height="100%"
                            width="100%"
                            contain
                            aspect-ratio="2">
                        </v-img>
                      </div>
                        <h2 v-if="checkData === false" style="padding-top: 20px; padding-bottom: 50px; color: #9A9A9A">
                          <span>ไม่มีข้อมูลรายการร้านค้าเชื่อมต่อ</span>
                        </h2>
                        <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #9A9A9A" class="d-flex flex-column">
                          <span>ไม่พบข้อมูลร้านค้าที่เชื่อมบริการ ที่คุณค้นหา</span>
                          <span class="mt-2">กรุณาค้นหาใหม่อีกครั้ง</span>
                        </h2>
                    </v-col>
                </v-row>
            </v-col>
            </v-row>
        </v-card-text>
        </v-card>
    </v-container>
</template>
<script>
export default {
  data () {
    return {
      checkData: false,
      noInfo: true,
      shopID: null,
      userDetails: [],
      search: '',
      header: [
        { text: 'ชื่อร้านค้า Partner', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'partner_name' },
        { text: 'วันที่สร้างบริการ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'created_at' },
        { text: 'วันที่อนุมัติสร้างบริการ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'approve_at' },
        { text: 'จำนวนร้านที่เชื่อมต่อ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '120', value: 'partner_connected_count' },
        { text: 'จัดการ', align: 'center', sortable: false, class: 'backgroundTable fontTable--text', width: '100', value: 'partner_code' }
      ],
      // tableData2: [
      //   { partner_code: 1, partner_name: 'ร้าน  Double Plus 3', created_at: '25/10/2024', approve_at: '25/10/2024', partner_connected_count: 50 },
      //   { partner_code: 2, partner_name: 'ร้าน Smile Plus', created_at: '15/10/2024', approve_at: '16/10/2024', partner_connected_count: 50 },
      //   { partner_code: 3, partner_name: 'ร้าน  Double Plus', created_at: '09/09/2024', approve_at: '09/09/2024', partner_connected_count: 50 },
      //   { partner_code: 4, partner_name: 'ร้าน  One One 3', created_at: '10/08/2024', approve_at: '10/08/2024', partner_connected_count: 100 },
      //   { partner_code: 5, partner_name: 'ร้าน  One One 2', created_at: '31/07/2024', approve_at: '31/07/2024', partner_connected_count: 100 },
      //   { partner_code: 6, partner_name: 'ร้าน  One One', created_at: '21/07/2024', approve_at: '21/07/2024', partner_connected_count: 100 },
      //   { partner_code: 7, partner_name: 'ร้าน  DD Purin 3', created_at: '30/06/2024', approve_at: '01/07/2024', partner_connected_count: 100 },
      //   { partner_code: 8, partner_name: 'ร้าน  DD Purin 2', created_at: '30/06/2024', approve_at: '01/07/2024', partner_connected_count: 100 },
      //   { partner_code: 9, partner_name: 'ร้าน  DD Purin', created_at: '20/06/2024', approve_at: '21/06/2024', partner_connected_count: 100 },
      //   { partner_code: 10, partner_name: 'ร้าน  DSound', created_at: '20/06/2024', approve_at: '21/06/2024', partner_connected_count: 100 }
      // ],
      tableData: []
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAdmin')
    await this.getTableData()
    if (this.tableData.length > 0) {
      this.checkData = true
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filteredData () {
      // กรองข้อมูลตาม search
      return this.tableData.filter(item => {
        const partnerName = item.partner_name || ''
        return partnerName.toLowerCase().includes(this.search.trim().toLowerCase())
      })
    },
    amountShop () {
      return this.filteredData.reduce((sum, item) => sum + (item.partner_connected_count || 0), 0)
    },
    noInfoComputed () {
      return this.filteredData.length === 0
    },
    countShop () {
    // แสดงจำนวนร้านค้าที่เหลืออยู่หลังการค้นหา
      return this.filteredData.length
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminManageShopPartnerMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'AdminManageShopPartner')
        this.$router.push({ path: '/AdminManageShopPartner' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    },
    search () {
      this.noInfo = this.noInfoComputed
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    openDialogDetails (val) {
      // this.shopID = val.seller_shop_id
      // this.$store.commit('mutationsSellerShopId', this.shopID)
      // this.$EventBus.$emit('shop-id', this.shopID)
      this.$router.push(`/DetailShopPartner?id=${val.partner_code}`)
    },
    openDialogDetailsMobile (val) {
      // this.shopID = val.seller_shop_id
      // this.$store.commit('mutationsSellerShopId', this.shopID)
      this.$router.push(`/DetailShopPartnerMobile?id=${val.partner_code}`)
    },
    async getTableData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsShopPartnerAdmin')
      var response = await this.$store.state.ModuleAdminManage.stateShopPartnerAdmin
      if (response.code === 200) {
        this.tableData = response.data
        if (this.tableData.length > 0) {
          this.noInfo = false
        } else {
          this.noInfo = true
        }
      } else {
        this.tableData = []
        this.noInfo = true
      }
      this.$store.commit('closeLoader')
    },
    // countConnectedShop (pagination) {
    //   this.countShop = pagination.itemsLength
    // },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    }
  }
}
</script>
