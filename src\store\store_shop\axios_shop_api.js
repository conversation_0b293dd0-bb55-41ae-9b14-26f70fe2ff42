import axios from 'axios'
import { Decode } from '@/services'
// import dataTest from '@/components/library/dataTest.json'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async getCategory () {
    // const auth = await GetToken()
    // console.log('GetUPSCategory', data)
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/category/get_category`)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Shop Detail Page
  async GetShopDetailPage (val) {
    var response
    var data
    if (val === null) {
      data = {
        seller_shop_id: val.seller_shop_id,
        role_user: 'ext_buyer'
      }
    } else {
      data = {
        seller_shop_id: val.seller_shop_id,
        role_user: val.role,
        company_id: val.company_id
      }
    }
    try {
      if (localStorage.getItem('oneData') === null) {
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller_shop_page`, data)
      } else if (localStorage.getItem('oneData') !== null) {
        const auth = await GetToken()
        response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller_shop_page`, data, auth)
      }
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateSellerShop (val) {
    const auth = await GetToken()
    const data = val.dataShop
    // console.log('val nna', data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_shop_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateSettingSellerShop (val) {
    const auth = await GetToken()
    // const data = val.dataShop
    const data = val
    // console.log(val, 'val')
    // console.log('val nna', data)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/setting_shop_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // delete product
  async deleteProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/product/delete_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // delete many products
  async DeleteBySeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}products/deleteBySeller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // product select option
  async GetSelectOption (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/product_select_option`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpShopaddresss (data) {
    // const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_shop_address`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUpShopaddresss (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_shop_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PostListRefundSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_refund_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PostDetailRefundSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_refund_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PostNotiRefundSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_refund_approve_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Dashboard (data) {
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const dataAll = await {
      shopSellerID: shopSellerID,
      search_type: data.search_type,
      start_date: data.start_date,
      end_date: data.end_date
    }
    // console.log('dataall', dataAll)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/getDashboard`, dataAll)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Dashboard2 (data) {
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const dataAll = await {
      shopSellerID: shopSellerID,
      search_type: data.search_type,
      start_date: data.start_date,
      end_date: data.end_date
    }
    // console.log('SSDashboard2', dataAll)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/getDashboard`, dataAll)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetProductBestSeller (data) {
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const dataAll = await {
      shopSellerID: shopSellerID
    }
    // console.log('dataall', dataAll)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/getProductBestSeller`, dataAll)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetUserMostBuyProduct (data) {
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const dataAll = await {
      shopSellerID: shopSellerID
    }
    // console.log('dataall', dataAll)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/getUserMostBuyProduct`, dataAll)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListRefMerchant (data) {
    // console.log('GetListRefMerchant')
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_ref_share_to_merchant`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetRefMerchant (data) {
    // console.log('GetRefMerchant xxx')
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/ref_share_to_merchant`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_new_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_seller_shop_detail_mp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_seller_shop_mp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ShowProductShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_seller_shop_mp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailMerchantFromPayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_merchant_from_payment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Get Review Prodoct
  async GetReviewProduct (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_all_review_in_product`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },

  async CheckRequestPartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/check_request_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },

  async GetListPartnerBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/list_seller_partner_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },

  async PostRequestPartnerBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/2022/company/request_partner_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },

  async DetailDocumentShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_document_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailSellerPartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_seller_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditPartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/edit_request_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // api Quotation List  seller
  async ListQuotationSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/list_for_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  //
  async ListUserCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/list_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailUserCompanyPosition (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreatePositon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/create_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Edit Position
  async EditPosition (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/edit_position_of_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // list Company Position
  async ListCompanyPosition (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/list_user_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Search User Company
  async SearchUserCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/search_user`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Detail User Company
  async DetailUserCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_user_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Edit Position User Company
  async EditPositionCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/edit_user_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreatePositionCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/add_user_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // listData Manage Buyer
  async ListManageBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/list_manage_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PostReport (data) {
    // console.log('PostReport', data)
    // const shopSellerID = localStorage.getItem('shopSellerID').toString()
    // const auth = await GetToken()
    // const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // const dataAll = {
    //   token: oneData.user.access_token,
    //   search: data
    // }
    // const dataAll = await {
    //   shopSellerID: shopSellerID,
    //   search_type: data.search_type,
    //   start_date: data.start_date,
    //   end_date: data.end_date
    // }
    // console.log('PostReport', dataAll)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}report/company/getReport`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailManageBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/detail_manage_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListAppover (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/list_approver_manage_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddAppover (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/add_approver_manage_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditAppover (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/2022/company/edit_approver_manage_buyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AppoverQU (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/qu/approve_qu`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AppoverQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller_update_status_qt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ConfirmAppoverQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}approve/quotation`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async BuyerUpdateStatusQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/buyer_update_status_qt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CancelAppoverQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/buyer_cancel_qt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // async CancelAppoverQT (data) {
  //   const auth = await GetToken()
  //   try {
  //     var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/order_terminate`, data, auth)
  //     return response.data
  //   } catch (error) {
  //     return error.response.data
  //   }
  // },
  // (YaPalm)Seller List Order Credit Term
  async listOrderCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/list_by_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm)Seller List Credit Term
  async listCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/list_credit_term_by_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm)Seller List Credit Term
  async uploadInvoiceCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/upload_invoice_credit_term`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm)Seller Request New Credit Term
  async listRequestChangeTermBySellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/list_request_change_term_by_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (YaPalm)Seller Request New Credit Term
  async updateRequestCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/credit_term/update_request_credit_term_by_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Ya)Seller List Special Price Seller
  async listSpecialPriceSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}listSpecialPriceSeller`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Ya)Seller Detail Special Price Seller
  async detailSpecialPrice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}detailSpecialPrice`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Non) New Seller Detail Special Price Seller
  async newDetailSpecialPrice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}detailSpecialPriceSeller`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Ya)Seller Approve Special Price Seller request auth
  async approveSpecialPrice (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}approveSpecialPrice`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Ya)Seller Calculate Special Price Seller
  async calculateSpecialPrice (data) {
    const auth = GetToken()
    try {
      // console.log('before fn')
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}calculateSpecialPrice`, data, auth)
      // console.log('fn pass', response.data)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Ya)Seller Edit Special Price Seller
  async editSpecialPrice (data) {
    const auth = GetToken()
    try {
      // console.log('before fn')
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}editSpecialPriceSeller`, data, auth)
      // console.log('fn pass', response.data)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  // (Ya)Merchant
  async checkMerchant (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/check_merchant`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  async createMerchant (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_merchant`, data, auth)
      return response.data
    } catch (error) {
      return error.response
    }
  },
  async createUpdatePositionInShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_update_position_in_shop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async getPositionDetailInShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_position_detail_in_shop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listPositionInShop (data) {
    // console.log('888 axios', data)
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_position_in_shop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async manageUserWithRoleInShop (data) {
    // console.log('888 axios', data)
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/manage_user_with_role_in_shop`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // shop coupon
  async listShopCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}ngs/listShopCoupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async detailShopCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}ngs/detailShopCoupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async deleteShopCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}ngs/deleteShopCoupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async createShopCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}ngs/createShopCoupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async searchCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/search_coupon `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async editShopCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}ngs/editShopCoupon`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailMerchantCrterm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_merchant_crterm_from_payment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RefShareCreditTerm (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/refshare_credit_term_to_merchant`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async NewGetDashboard (data) {
    // console.log('NewGetDashboard', data)
    const auth = await GetToken()
    const shopSellerID = localStorage.getItem('shopSellerID').toString()
    const dataAll = await {
      seller_shop_id: shopSellerID,
      type: data.type,
      start_date: data.start_date,
      end_date: data.end_date
    }
    // console.log('dataall', dataAll)
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/v2/getDashboard`, dataAll, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdatePositionStatus (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/update_position_status`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // Check User Business
  async CheckUserBusiness () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/check_user_business`, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetNewRefMerchant (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/withdraw_seller`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListAllShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_all_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // B2B
  async ImportExcel (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}import/excel/product`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // async ImportExcelNoJV (data) {
  //   const auth = await GetToken()
  //   try {
  //     var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}import/product/excel`, data, auth)
  //     // console.log(response.data)
  //     return response.data
  //   } catch (error) {
  //     return error.response.data
  //   }
  // },
  async ImportExcelNoJV (data) {
    console.log(data)
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_DOMAIN}api/logs/import/product/excel`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ImportExcelNoJVSimple (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}import/product/simpleExcel`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateJSON_QT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}create_json_qt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async NewListQTSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/list_QT_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async NewListQTSellerV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/list_QT_seller_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async NewDetailQTSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/detail_QT_seller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListIncludeQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/list_include_QT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetQTDashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashboard/sumaryDocumentQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSumTrendDashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashboard/summaryTrendQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSumDocQTDashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashborad/summaryDocumentBarQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSumDocSODashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashborad/summaryDocumentBarSO`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSumDocPODashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashborad/summaryDocumentBarPO`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSumDocPRDashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashborad/summaryDocumentBarPR`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSumTopFiveQTDashBoard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashborad/summaryTopFiveQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadToS3 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/upload_to_s3`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCategoryShopList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}search/product/categoryShopList `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetSaleOrder (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/sale/detail_sale `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListTierCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/list_tier_customer `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListCustomerAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/list_customer_address `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListCustomerInvAddress (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/list_customer_inv_address `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListCustomer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/customer/list_customer `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SelectCategoryShopList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}search/product/filter`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetProvince (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}/search/product/listProvince`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OutOfStockProducts (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}search/product/outOfStockProducts`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AccecptProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/accecpt_product/accecpt_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateTrackingNumber (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}mobilyst/updateTrackingNumber`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GenerateShortenlink (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}link/generate_shorten_link`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckSKU (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/product/check_sku_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CalPriceVat (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/product/cal_price_vat`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CountSummaryProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}search/product/countSummaryProduct`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}coupon/list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CollectCoupon (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}coupon/collect`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListBank () {
    try {
      var response = await axios.get(`${process.env.VUE_APP_NEW_BACK_END}api/list_category_bank `)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateSellerBot (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/update_seller_bot`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListAttorneyShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}attorney/list/shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddAttorney (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}attorney/addAttorney`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAttorneyUserDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}attorney/getAttorneyUserDetail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AttorneyUpdateAndConfrime (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}attorney/attorneyUpdateAndConfrime`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListReciver () {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}attorney/list/reciver`, '', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetActiveAttorney (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}attorney/updateStatus`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadFDAFile (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/upload_fda`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListDocumentsSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/list_documents_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApprovePRDocument (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}approve/pr_shop_external`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddProductERP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/zort/product/add_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListSyncShopErp (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}/api/admin_platform/seller/list_sync_shop_erp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetServiceKeyERP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/seller_shop/set_service_key_erp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateServiceKeyERP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/seller_shop/update_service_key_erp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateStatusServiceKeyERP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/seller_shop/update_status_service_key_erp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailServiceKeyERP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/seller_shop/detail_service_key_erp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetSyncShopErp (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/admin_platform/seller/set_sync_shop_erp`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateStockErp (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/zort/product/update_available_stock_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DashboardWDshopOrderHistory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashboard/order_history`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async WithdrawSummary (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashboard/withdraw_summary`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async WithdrawDetails (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashboard/withdraw_details`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async transactionHistory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}dashboard/transaction_history`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async transferShopClick (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}payment/transferShopClick`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SettingQTExternal (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/setting_qt_external`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailSettingQTExternal (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/detail_setting_qt_external`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async updateExcelProducts (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}import/updateProduct/excel`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetFilterAllGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/filter_all_seller_shop_paginate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetFilterGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/filter_group_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListDetailPartnerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/list_partner`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateStatusAfterBuyPartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/create_order_package`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListInterestedPartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/seller_shop/list_interested_partner?seller_shop_id=${data.seller_shop_id}`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ApproveSlipPayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}payment/approveslippayment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadSlipLotus (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/upload_payment_slip`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Addwarehouse (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/partner/shop/add_warehouse`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SyncWarehouse (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/partner/shop/sync_warehouse`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CustomGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_group_seller_shop_type`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AccountDetailShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/account_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCustomGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_group_seller_shop_type`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AccountDetailShopUpdate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/account_update`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ActiveCustomGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_status_group_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateAccountShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}shop/account_create`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeStatusProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/product/set_status`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PayWithCreditCard (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}marketplace/getcredit`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PayWithQRCode (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}marketplace/getqrcode`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckResultQRCodeMarketplace (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}marketplace/checkresultmarketplace`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPackageList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/get_package_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditShippingSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_seller_shop_shipping`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddUserManual (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/manual`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeStatusShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_shop_status`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteUserManual (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/delete_manual`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailUserManual (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/manual_detail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangePartnerShow (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_partner_show`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async TypeGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_status_group_shop_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditBannerGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_group_seller_shop_type_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBannerGroupShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_group_seller_shop_type_v2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditBannerGroupShopV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_banner_type`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetBannerGroupShopV2 (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_banner_type`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllRoom (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}getAllRoom`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListGategory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_product_group`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async LoginSSO (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/login_sso`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateCategory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_product_group`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditCategory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_product_group`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OldPartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/partner/create_order_package`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteCategory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/delete_product_group`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/get_product_group_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailPosition (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/detail_position`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteRoom (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}deleteRoom`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Delivery (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/detail_order_delivery`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetDetailProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/product/list_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async EditProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/edit_product_group_product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListProductDelivery (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/list_product_delivery`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateProductDelivery (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/create_delivery_note`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ChangeStatusCategory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/change_shop_status_cutom_category`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderDeliveryShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order_delivery/shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async OrderDeliveryOrderDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order_delivery/detail_order`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateDateDelivery (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order_delivery/update_dete_delivery`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteDelivery (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}order_delivery/delete_delivery`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListShopTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/list_shop_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateShopTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/create_shop_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateShopTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/update_shop_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateShopTagStatus (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/update_shop_tag_status`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteShopTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/delete_shop_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteShopTagItem (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/delete_shop_tag_item`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListProductTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/list_product_tagging`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SelectListProductTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/list_product_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CreateProductTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/create_product_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateProductTag (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/update_product_tag`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetShopProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/seller/get_shop_products`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RegisterOnePlatformToRegisterShop (data) {
    // const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/users/register/one_platform`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UploadPDF (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/upload_to_s3_form_data`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async PreviewSettingQT (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/preview_setting_qt`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AccecptAllProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/accecpt_product/accecpt_product_mobile`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SelectListProductFlashSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END}api/seller/list_product_flash_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListBundle (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}bundle/list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckStatusDBDL (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}shop/check_have_dbd_req`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListBusiness (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}shop/type_list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RequestDBD (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}shop/dbd_request`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CheckVerify (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}payment/otp/isVerify`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async RegOTP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}payment/otp/requestTransfer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SendOTP (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}payment/otp/verifyTransfer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
