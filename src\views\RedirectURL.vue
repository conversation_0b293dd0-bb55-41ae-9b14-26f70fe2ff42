<!-- เก่า -->
<template>
  <div></div>
</template>

<script>
export default {
  data () {
    return {
      link: '',
      isAndroid: false,
      isiOS: false,
      URL: '',
      URLToStore: '',
      productID: ''
    }
  },
  async created () {
    // var OS = this.detectOS()
    // console.log('OS', OS)
    this.link = window.location.href
    // console.log('this.link', this.link)
    var path = this.$router.currentRoute.params.data
    var cleanPath = path.split('-')
    // this.IdProduct = cleanPath[cleanPath.length - 1]
    let isAppOpened = false
    let timeout
    const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
    this.isAndroid = /Android/i.test(userAgent)
    // this.isiOS = /iPad|Macintosh|iPod|iPhone/i.test(navigator.userAgent) || navigator.maxTouchPoints > 1
    this.isiOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream
    this.URL = ''
    this.URLToStore = ''
    // console.log('userAgent', navigator.userAgent)
    // console.log('isAndroid', this.isAndroid)
    // console.log('isiOS', this.isiOS)
    // console.log('URL', this.URL)
    this.productID = cleanPath[cleanPath.length - 1]
    // console.log('this.productID', this.productID)
    const data = {
      product_id: this.productID
    }
    await this.$store.dispatch('actionsGetRedirectLinkMobile', data)
    const response = await this.$store.state.ModuleHompage.stateGetRedirectLinkMobile
    // console.log('response.data.short_url', response.data.short_url)
    if (this.isAndroid === true) {
      this.URL = response.data.intentLink
      // this.URLToStore = 'https://play.google.com/store/apps/details?id=com.inet.nexgenshop'
      // console.log('isAndroid', this.isAndroid)
      // console.log('this.URL', this.URL)
    } else if (this.isiOS === true) {
      this.URL = response.data.short_url
      this.URL = this.URL.replace(/https/gi, 'nexgencommerce')
      this.URLToStore = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
      // console.log('isiOS', this.isiOS)
      // console.log('this.URL', this.URL)
    } else {
      this.URL = this.link.replace(/share/gi, 'api/backend_2/redirect')
      // console.log('this.URL', this.URL)
    }
    const onVisibilityChange = () => {
      // console.log(document.hidden)
      if (document.hidden) {
        isAppOpened = true
        clearTimeout(timeout)
      }
    }
    document.addEventListener('visibilitychange', onVisibilityChange)

    const now = Date.now()
    window.location.href = this.URL

    if (this.isiOS === true) {
      timeout = setTimeout(() => {
        const elapsed = Date.now() - now
        if (!isAppOpened && elapsed < 5000) {
          window.location.href = this.URLToStore
          // isRedirected = true
        }
        document.removeEventListener('visibilitychange', onVisibilityChange)
      }, 4000)
    }
  }
}
</script>

<!-- ลองแก้ -->
<!-- <template>
<div></div>
</template>

<script>
export default {
data () {
  return {
    link: '',
    isAndroid: false,
    isIphone: false,
    isIpad: false,
    isMac: false,
    URL: '',
    URLToStore: ''
  }
},
async created () {
  this.link = window.location.href
  const userAgent = navigator.userAgent
  const isTouchDevice = navigator.maxTouchPoints > 1
  // ตรวจสอบประเภทอุปกรณ์
  this.isAndroid = /Android/i.test(userAgent)
  this.isIphone = /iPod|iPhone/i.test(userAgent) || isTouchDevice
  this.isIpad = /iPad|Macintosh/i.test(userAgent) && isTouchDevice
  this.isMac = /Macintosh/i.test(userAgent) && !isTouchDevice
  if (this.isAndroid) {
    this.URL = this.link.replace(/share/gi, 'api/backend_2/product')
    this.URLToStore = 'https://play.google.com/store/apps/details?id=com.inet.nexgenshop'
  } else if (this.isIphone || this.isIpad) {
    this.URL = this.link.replace(/https/gi, 'nexgencommerce').replace(/share/gi, 'api/backend_2/product')
    this.URLToStore = 'https://apps.apple.com/us/app/nexgen/id6651839687'
  } else if (this.isMac) {
    this.URL = this.link.replace(/share/gi, 'api/backend_2/redirect')
  } else {
    this.URL = this.link.replace(/share/gi, 'api/backend_2/redirect')
  }

  const now = Date.now()
  setTimeout(() => {
    const elapsed = Date.now() - now
    window.location.href = elapsed < 2000 && this.URLToStore ? this.URLToStore : this.URL
  }, 1500)
}
}
</script> -->
