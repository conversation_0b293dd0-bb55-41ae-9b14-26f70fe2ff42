<template>
  <div>
    <v-dialog v-model="dialogOpenSafari" width="100%" persistent>
      <v-card width="100%">
        <v-card-text class="styleText pt-4">
          เปิดลิงก์นี้ใน Safari เพื่อใช้งานได้สมบูรณ์
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text color="#27AB9C" @click="openSafari()">เปิดใน Safari</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
export default {
  data () {
    return {
      link: '',
      isAndroid: false,
      isiOS: false,
      isFacebookWebView: false,
      URL: '',
      URLToStore: '',
      productID: '',
      dialogOpenSafari: false
    }
  },
  async created () {
    this.link = window.location.href
    var path = this.$router.currentRoute.params.data
    var cleanPath = path.split('-')
    let isAppOpened = false
    let timeout
    const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
    this.isFacebookWebView = /FBAN|FBAV/.test(userAgent) // เช็กว่าเป็น Messenger WebView
    this.isAndroid = /Android/i.test(userAgent) // เช็กว่าเป็น Android
    this.isiOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream // เช็กว่าเป็น iOS
    this.URLToStore = ''
    this.productID = cleanPath[cleanPath.length - 1]
    const data = {
      product_id: this.productID
    }
    await this.$store.dispatch('actionsGetRedirectLinkMobile', data)
    const response = await this.$store.state.ModuleHompage.stateGetRedirectLinkMobile
    if (this.isAndroid === true) {
      this.URL = response.data.intentLink
    } else if (this.isiOS === true) {
      this.URL = response.data.short_url
      this.URL = this.URL.replace(/https/gi, 'nexgencommerce')
      this.URLToStore = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
    } else {
      var datalink = this.link
      this.URL = datalink.replace(/share/gi, 'api/backend_2/redirect')
    }
    // เช็คว่าเปิดผ่าน Messenger WebView ไหม
    if (this.isFacebookWebView) {
      if (this.isiOS) {
        this.dialogOpenSafari = true
      } else if (this.isAndroid) {
        // Android: Redirect ไป Chrome
        window.location.href = this.URL
      } else {
        window.location.href = this.URL
      }
    } else {
      const onVisibilityChange = () => {
        if (document.hidden) {
          isAppOpened = true
          clearTimeout(timeout)
        }
      }
      document.addEventListener('visibilitychange', onVisibilityChange)
      const now = Date.now()
      window.location.href = this.URL
      if (this.isiOS === true) {
        timeout = setTimeout(() => {
          const elapsed = Date.now() - now
          if (!isAppOpened && elapsed < 5000) {
            window.location.href = this.URLToStore
          }
          document.removeEventListener('visibilitychange', onVisibilityChange)
        }, 4000)
      }
    }
  },
  methods: {
    openSafari () {
      const url = this.link // ใช้ URL ปัจจุบัน
      const a = document.createElement('a')
      a.href = url
      a.target = '_blank'
      a.rel = 'noopener noreferrer'
      document.body.appendChild(a)
      setTimeout(() => {
        a.click()
        document.body.removeChild(a)
      }, 500)
    }
  }
}
</script>

<style scoped>
.styleText {
  color: #333333;
  font-weight: 700;
  display: flex;
  align-items: center;
}
</style>
