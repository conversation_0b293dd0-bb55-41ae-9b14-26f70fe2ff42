<template>
  <v-container class="" style="max-width: 100% !important;">
    <v-row v-if="!MobileSize">
      <!-- Website -->
      <v-col cols="12" md="3" sm="4" xs="12" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <v-card class="mt-6" max-height="100%" height="900px" style="border-radius: 8px;">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- นิติบุคคล -->
            <v-list-group
              v-for="item in MenuCorporation"
              :key="item.title"
              v-model="CorporationActive"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectCorporation"
                :mandatory="defaultSelectCorporation !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  color="#27AB9C"
                  link
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- จัดการสั่งซื้อ -->
            <v-list-group
              v-for="item in MenuManageOrder"
              :key="item.title"
              v-model="ManageUser"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageOrder"
                :mandatory="defaultSelectManageOrder !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!--  จัดการ partner -->
            <v-list-group
              v-for="item in MenuManagePartner"
              :key="item.title"
              v-model="ManagePartner"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManagePartner"
                :mandatory="defaultSelectManagePartner !== null ? true : false"
              >
                <v-list-item
                  v-for="child in havePartnerShop ? item.items : item.itemsNoPartner"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD PRO -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && !IpadSize && IpadProSize">
        <v-card class="mt-6" max-height="100%" height="900px" style="border-radius: 8px;">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- นิติบุคคล -->
            <v-list-group
              v-for="item in MenuCorporation"
              :key="item.title"
              v-model="CorporationActive"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectCorporation"
                :mandatory="defaultSelectCorporation !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  color="#27AB9C"
                  link
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- จัดการสั่งซื้อ -->
            <v-list-group
              v-for="item in MenuManageOrder"
              :key="item.title"
              v-model="ManageUser"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageOrder"
                :mandatory="defaultSelectManageOrder !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!--  จัดการ partner -->
            <v-list-group
              v-for="item in MenuManagePartner"
              :key="item.title"
              v-model="ManagePartner"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManagePartner"
                :mandatory="defaultSelectManagePartner !== null ? true : false"
              >
                <v-list-item
                  v-for="child in havePartnerShop ? item.items : item.itemsNoPartner"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && IpadSize && !IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1100px" style="border-radius: 8px;">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <!-- นิติบุคคล -->
            <v-list-group
              v-for="item in MenuCorporation"
              :key="item.title"
              v-model="CorporationActive"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectCorporation"
                :mandatory="defaultSelectCorporation !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  color="#27AB9C"
                  link
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!-- จัดการสั่งซื้อ -->
            <v-list-group
              v-for="item in MenuManageOrder"
              :key="item.title"
              v-model="ManageUser"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManageOrder"
                :mandatory="defaultSelectManageOrder !== null ? true : false"
              >
                <v-list-item
                  v-for="child in item.items"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-10"
                  style="font-size: 14px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
            <!--  จัดการ partner -->
            <v-list-group
              v-for="item in MenuManagePartner"
              :key="item.title"
              v-model="ManagePartner"
              :prepend-icon="item.action"
              no-action
              color="#27AB9C"
            >
              <template v-slot:activator>
                <v-list-item-content>
                  <v-list-item-title style="white-space: normal; font-weight: bold; font-size: 16px; line-height: 30px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
              </template>

              <v-list-item-group
                v-model="defaultSelectManagePartner"
                :mandatory="defaultSelectManagePartner !== null ? true : false"
              >
                <v-list-item
                  v-for="child in havePartnerShop ? item.items : item.itemsNoPartner"
                  :key="child.title"
                  link
                  color="#27AB9C"
                  dense
                  class="pl-16"
                  style="font-size: 16px; line-height: 26px;"
                >
                  <v-list-item-content @click="Gopage(child)">
                    <v-list-item-action>{{ child.title }}</v-list-item-action>
                  </v-list-item-content>
                </v-list-item>
              </v-list-item-group>
            </v-list-group>
          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9" sm="8" xs="12" class="pl-0 pr-0">
        <v-main style="padding: 0px;">
          <v-container>
            <v-card max-height="100%" height="100%" width="100%" class="mt-3">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
export default {
  data () {
    return {
      taxId: '',
      defaultSelectCorporation: 0,
      defaultSelectManageOrder: null,
      defaultSelectManagePartner: null,
      CorporationActive: true,
      ManageUser: false,
      ManagePartner: false,
      MenuCorporation: [
        {
          key: 'sub1',
          action: 'mdi-domain',
          active: true,
          title: 'จัดการนิติบุคคล',
          items: [
            // { key: 0, title: '', path: '', isDisabled: true },
            { key: 0, title: 'ข้อมูลนิติบุคคล', path: 'detailbusinesssid', isDisabled: false },
            { key: 1, title: 'จัดการตำแหน่ง', path: 'managePositionComapny&Bussiness', isDisabled: false },
            { key: 2, title: 'จัดการผู้ใช้งาน', path: 'manageUser', isDisabled: false },
            { key: 3, title: 'จัดการบริษัท & ร้านค้า', path: 'manageCompanyShop', isDisabled: false }
          ]
        }
      ],
      MenuManageOrder: [
        {
          key: 'sub2',
          action: 'mdi-file-document-outline',
          active: false,
          title: 'จัดการายการสั่งซื้อ',
          items: [
            { key: 0, title: 'รายการขายสินค้าของร้านค้า', path: 'listOrderShop', isDisabled: false },
            { key: 1, title: 'รายการสั่งซื้อสินค้าของบริษัท', path: 'listOrderCompany', isDisabled: false }
          ]
        }
      ],
      MenuManagePartner: [
        {
          key: 'sub3',
          action: 'mdi-handshake',
          active: false,
          title: 'Software Marketplace',
          items: [
            { key: 0, title: 'ข้อมูลร้านค้า Partner', path: 'partnerShopInfo', isDisabled: false },
            { key: 1, title: 'สินค้าบริการของฉัน', path: 'serviceProductPartner', isDisabled: false },
            { key: 2, title: 'Preview สินค้าและบริการ', path: 'PreviewDetailPartner', isDisabled: false },
            { key: 3, title: 'คู่มือการใช้งานและสัมมนา', path: 'PackageUserManualAndSeminar', isDisabled: false },
            // { key: 3, title: 'จัดการเข้าร่วมสัมมนา', path: 'PackageSeminar', isDisabled: false },
            { key: 4, title: 'ร้านค้าที่เชื่อมต่อ', path: 'shopConnected', isDisabled: false },
            { key: 5, title: 'รายการคำสั่งซื้อ', path: 'partnerOrderList', isDisabled: false },
            { key: 6, title: 'วางบิล', path: 'PartnerBilling', isDiabiled: false },
            { key: 7, title: 'แดชบอร์ด', path: 'dashboardPartner', isDisabled: false }
          ],
          itemsNoPartner: [
            { key: 0, title: 'ข้อมูลร้านค้า Partner', path: 'partnerShopInfo', isDisabled: false }
          ]
        }
      ],
      havePartnerShop: true
    }
  },
  created () {
    // console.log('hahaha')
    this.getTaxID()
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('LinkPage')
    this.$EventBus.$emit('resetSearch')
    this.$EventBus.$on('changeNavBusiness', this.SelectPathBusiness)
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      // console.log(this.$router.currentRoute.path)
      if (this.$router.currentRoute.path === '/detailbusinesssid') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/managePositionComapny&Bussiness') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        this.$router.push({ path: '/managePositionComapny&Bussiness' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/detailPositionCompanyShop') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        var id = parseInt(localStorage.getItem('paramID'))
        var position = localStorage.getItem('paramName')
        this.$router.push({ path: `/detailPositionCompanyShop?id=${id}&position=${position}` }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/manageUser') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        this.$router.push({ path: '/manageUser' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/manageCompanyShop') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/showDetailCompany') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        var id1 = parseInt(localStorage.getItem('paramID'))
        var compname = localStorage.getItem('paramName')
        this.$router.push({ path: `/showDetailCompany?companyID=${id1}&name=${compname}` }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/detailShop') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        var shopID = parseInt(localStorage.getItem('paramID'))
        var shopName = localStorage.getItem('paramName')
        this.$router.push({ path: `/detailShop?shopID=${shopID}&name=${shopName}` }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/detailUsersCompany') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        var detailusID = parseInt(localStorage.getItem('paramID'))
        var detailusTaxId = parseInt(localStorage.getItem('paramTaxId'))
        var DetailusName = localStorage.getItem('paramName')
        this.$router.push({ path: `/detailUsersCompany?taxID=${detailusTaxId}&companyID=${detailusID}&nameTH=${DetailusName}` }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/managePositionCompany') {
        this.CorporationActive = true
        this.ManageUser = false
        this.ManagePartner = false
        var id2 = parseInt(localStorage.getItem('paramID'))
        this.$router.push({ path: `/managePositionCompany?companyID=${id2}}` }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/listOrderShop') {
        this.CorporationActive = false
        this.ManageUser = true
        this.ManagePartner = false
        this.$router.push({ path: '/listOrderShop' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/listOrderCompany') {
        this.CorporationActive = false
        this.ManageUser = true
        this.ManagePartner = false
        this.$router.push({ path: '/listOrderCompany' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/detailUsersCompany') {
        this.CorporationActive = false
        this.ManageUser = true
        this.ManagePartner = false
        this.$router.push({ path: '/detailUsersCompany' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/detailUsersShops') {
        this.CorporationActive = false
        this.ManageUser = true
        this.ManagePartner = false
        this.$router.push({ path: '/detailUsersShops' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/partnerShopInfo') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/serviceProductPartner') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/serviceProductPartner' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/PackageUserManualAndSeminar') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/PackageUserManualAndSeminar' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/PackageSeminar') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/PackageSeminar' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/PartnerBilling') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/PartnerBilling' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/shopConnected') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/shopConnected' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/partnerOrderList') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/partnerOrderList' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/dashboardPartner') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/dashboardPartner' }).catch(() => {})
      } else if (this.$router.currentRoute.path === '/PreviewDetailPartner') {
        this.CorporationActive = false
        this.ManageUser = false
        this.ManagePartner = true
        this.$router.push({ path: '/PreviewDetailPartner' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
      }
    }
    this.SelectPathBusiness()
    this.$EventBus.$on('CheckPartnerCode', this.getPartnerCode)
  },
  // beforeDestroy () {
  //   localStorage.removeItem('pathBusiness')
  // },
  watch: {
    MobileSize (val) {
      // var responseID = this.$store.state.ModuleAdminManage.stateSellerShopID
      if (this.$router.currentRoute.name === 'detailbusinesssidMobile' || this.$router.currentRoute.name === 'detailbusinesssidui') {
        if (val === true) {
          this.$router.push({ path: '/detailbusinesssidMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'detailOrderCompanyMobile' || this.$router.currentRoute.name === 'detailOrderCompany') {
        if (val === true) {
          var id = localStorage.getItem('paramID')
          this.$router.push({ path: `/detailOrderCompanyMobile?order_number=${id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/detailOrderCompany?order_number=${id}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listOrderCompanyMobile' || this.$router.currentRoute.name === 'listOrderCompany') {
        if (val === true) {
          this.$router.push({ path: '/listOrderCompanyMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listOrderCompany' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'managePositionComapny&BussinessMobile' || this.$router.currentRoute.name === 'managePositionComapny&Bussiness') {
        if (val === true) {
          this.$router.push({ path: '/managePositionComapny&BussinessMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/managePositionComapny&Bussiness' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listOrderShopMobile' || this.$router.currentRoute.name === 'listOrderShop') {
        if (val === true) {
          this.$router.push({ path: '/listOrderShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listOrderShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'detailPositionCompanyShopMobile' || this.$router.currentRoute.name === 'detailPositionCompanyShop') {
        if (val === true) {
          this.$router.push({ path: '/managePositionComapny&BussinessMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/managePositionComapny&Bussiness' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageCompanyShopMobile' || this.$router.currentRoute.name === 'manageCompanyShop') {
        if (val === true) {
          this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageUserMobile' || this.$router.currentRoute.name === 'manageUser') {
        if (val === true) {
          this.$router.push({ path: '/manageUserMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageUser' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'detailShop' || this.$router.currentRoute.name === 'detailShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'detailUsersShops' || this.$router.currentRoute.name === 'detailUsersShopsMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'showDetailCompany' || this.$router.currentRoute.name === 'showDetailCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'detailUsersCompany' || this.$router.currentRoute.name === 'detailUsersCompanyMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'createBussinessBranchShop' || this.$router.currentRoute.name === 'createBussinessBranchShopMobile') {
        var step = localStorage.getItem('step')
        if (val === true) {
          this.$router.push({ path: `/createBussinessBranchShopMobile?step=${step}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/createBussinessBranchShop?step=${step}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'managePositionShop' || this.$router.currentRoute.name === 'managePositionShopMobile') {
        var sellerShopId = localStorage.getItem('shopSellerID')
        if (val === true) {
          this.$router.push({ path: `/managePositionShopMobile?seller_shop_id=${sellerShopId}` }).catch(() => {})
        } else {
          this.$router.push({ path: `managePositionShop?seller_shop_id=${sellerShopId}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'managePositionCompany' || this.$router.currentRoute.name === 'managePositionCompanyMobile') {
        var companyData = JSON.parse(localStorage.getItem('companyID'))
        if (val === true) {
          this.$router.push({ path: `/managePositionCompanyMobile?company=${companyData.id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/managePositionCompany?company=${companyData.id}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'partnerShopInfoMobile' || this.$router.currentRoute.name === 'partnerShopInfo') {
        if (val === true) {
          this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'serviceProductPartnerMobile' || this.$router.currentRoute.name === 'serviceProductPartner') {
        if (val === true) {
          this.$router.push({ path: '/serviceProductPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/serviceProductPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'PackageUserManualAndSeminarMobile' || this.$router.currentRoute.name === 'PackageUserManualAndSeminar') {
        if (val === true) {
          this.$router.push({ path: '/PackageUserManualAndSeminarMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/PackageUserManualAndSeminar' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'PackageSeminarMobile' || this.$router.currentRoute.name === 'PackageSeminar') {
        if (val === true) {
          this.$router.push({ path: '/PackageSeminarMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/PackageSeminar' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'shopConnectedMobile' || this.$router.currentRoute.name === 'shopConnected') {
        if (val === true) {
          this.$router.push({ path: '/shopConnectedMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/shopConnected' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'partnerOrderListMobile' || this.$router.currentRoute.name === 'partnerOrderList') {
        if (val === true) {
          this.$router.push({ path: '/partnerOrderListMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/partnerOrderList' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardPartnerMobile' || this.$router.currentRoute.name === 'dashboardPartner') {
        if (val === true) {
          this.$router.push({ path: '/dashboardPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'updateServicePartnerMobile' || this.$router.currentRoute.name === 'updateServicePartner') {
        if (val === true) {
          this.$router.push({ path: '/updateServicePartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/updateServicePartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'createServicePartnerMobile' || this.$router.currentRoute.name === 'createServicePartner') {
        if (val === true) {
          this.$router.push({ path: '/createServicePartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/createServicePartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'EditPartnerMobile' || this.$router.currentRoute.name === 'EditPartner') {
        if (val === true) {
          this.$router.push({ path: '/editPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/editPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'PartnerBillingMobile' || this.$router.currentRoute.name === 'PartnerBilling') {
        if (val === true) {
          this.$router.push({ path: '/PartnerBillingMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/PartnerBilling' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailOrderProductPartnerMobile' || this.$router.currentRoute.name === 'DetailOrderProductPartner') {
        if (val === true) {
          this.$router.push({ path: '/partnerOrderListMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DetailOrderProductPartner' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'PreviewDetailPartnerMobile' || this.$router.currentRoute.name === 'PreviewDetailPartner') {
        if (val === true) {
          this.$router.push({ path: '/PreviewDetailPartnerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/PreviewDetailPartner' }).catch(() => {})
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    changePage (val) {
      if (val === '/') {
        this.$EventBus.$emit('LinkPage', 'ext_buyer', '')
        localStorage.removeItem('pathBusiness')
        this.$router.push({ path: `${val}` }).catch(() => {})
      } else {
        this.$router.push({ path: `${val}` }).catch(() => {})
      }
    },
    Gopage (val) {
      // console.log(val)
      this.$router.push({ path: `${val.path}` }).catch(() => {})
    },
    async getTaxID () {
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        this.taxId = ownerBusiness[0].owner_tax_id
        // console.log(this.taxId, 'home')
        this.CheckUser(this.taxId)
        await this.getPartnerCode()
      }
    },
    async getPartnerCode (val, isNewTaxID) {
      // console.log(val, isNewTaxID, 'checkEmitVal')
      var data = {
        id_card_num: isNewTaxID === 'newTaxId' ? val.tax_id : this.taxId
      }
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      // console.log(response, '456')
      if (response.code === 200) {
        if (response.data.partner_code === null) {
          this.havePartnerShop = false
        } else {
          this.havePartnerShop = true
        }
      }
    },
    CheckUser (val) {
      var data = {
        tax_id: val
      }
      this.$store.dispatch('actionsCheckUser', data)
    },
    // ChangeActiveMenu (val) {
    //   this.activeMenu = val
    // },
    // chackAuthorityCompanyMenu () {
    //   this.authority = ''
    //   if (localStorage.getItem('list_Company_detail') !== null) {
    //     this.authority = JSON.parse(Decode.decode(localStorage.getItem('list_Company_detail')))
    //   }
    // },
    SelectPathBusiness () {
      // console.log('เข้าไหม', this.$router.currentRoute.name)
      this.defaultSelectCorporation = null
      this.defaultSelectManageOrder = null
      this.defaultSelectManagePartner = null
      if (this.$router.currentRoute.name === 'detailbusinesssidui') {
        this.defaultSelectCorporation = 0
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'managePositionComapny&Bussiness' || this.$router.currentRoute.name === 'detailPositionCompanyShop') {
        this.defaultSelectCorporation = 1
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'manageUser') {
        // console.log('เข้ามานี่')
        this.defaultSelectCorporation = 2
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'manageCompanyShop' || this.$router.currentRoute.name === 'showDetailCompany' || this.$router.currentRoute.name === 'detailShop' || this.$router.currentRoute.name === 'detailUsersCompany' || this.$router.currentRoute.name === 'managePositionCompany') {
        this.defaultSelectCorporation = 3
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'detailOrderCompany') {
        this.defaultSelectCorporation = 4
        this.defaultSelectManageOrder = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'listOrderShop' || this.$router.currentRoute.name === 'detailUsersShops') {
        // console.log('one')
        this.defaultSelectManageOrder = 0
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'listOrderCompany' || this.$router.currentRoute.name === 'detailUsersCompany') {
        // console.log('two')
        this.defaultSelectManageOrder = 1
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = null
      } else if (this.$router.currentRoute.name === 'partnerShopInfo') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 0
      } else if (this.$router.currentRoute.name === 'serviceProductPartner') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 1
      } else if (this.$router.currentRoute.name === 'PreviewDetailPartner') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 2
      } else if (this.$router.currentRoute.name === 'PackageUserManualAndSeminar') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 3
      } else if (this.$router.currentRoute.name === 'PackageSeminar') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 4
      } else if (this.$router.currentRoute.name === 'shopConnected') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 5
      } else if (this.$router.currentRoute.name === 'partnerOrderList') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 6
      } else if (this.$router.currentRoute.name === 'PartnerBilling') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 7
      } else if (this.$router.currentRoute.name === 'dashboardPartner') {
        this.defaultSelectManageOrder = null
        this.defaultSelectCorporation = null
        this.defaultSelectManagePartner = 8
      }
      // console.log(this.checkCreateShop)
    }
  }
}
</script>

<style scoped>
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
