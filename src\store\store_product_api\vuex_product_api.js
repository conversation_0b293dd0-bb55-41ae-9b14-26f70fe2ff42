import AxiosProduct from '../store_product_api/axios_product_api'

const ModuleProduct = {
  state: {
    // Product Detail
    stateProductDetailData: [],
    // Add Product
    stateAddProduct: []
  },
  mutations: {
    // Product Detail
    mutationsProductDetail (state, data) {
      state.stateProductDetailData = data
    },
    // Add Product
    mutationsAddProduct (state, data) {
      state.stateAddProduct = data
    }
  },
  actions: {
    // Product Detail
    async actionsProductDetail (context, access) {
      const dataProduct = await AxiosProduct.GetProductDetail(access)
      await context.commit('mutationsProductDetail', dataProduct)
    },
    async actionsAddProduct (context, access) {
      const response = await AxiosProduct.AddProduct(access)
      await context.commit('mutationsAddProduct', response)
    }
  }
}

export default ModuleProduct
