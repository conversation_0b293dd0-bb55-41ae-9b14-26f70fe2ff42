<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ผู้สมัครเข้าร่วม Affiliate</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> ผู้สมัครเข้าร่วม Affiliate</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาชื่อ-สกุลผู้เข้าร่วม Affiliate" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="userDataList.length !== 0 && (!MobileSize && !IpadSize)">รายการชื่อผู้เข้าร่วม Affiliate ทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="userDataList.length !== 0 && (MobileSize || IpadSize)">รายการชื่อผู้เข้าร่วม Affiliate ทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''" style="text-align: end;">
                <v-btn color="#38b2a4" rounded @click="exportUserExcel"><span class="white--text">EXPORT EXCEL</span></v-btn>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="userDataList"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบรายชื่อผู้สมัครเข้าร่วม Affiliate"
                no-data-text="ไม่มีรายชื่อผู้สมัครเข้าร่วม Affiliate"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                <template v-slot:[`item.username`]="{ item }">
                  <span>{{ item.username }}</span>
                </template>
                <template v-slot:[`item.detailUser`]="{ item }">
                  <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          width="30"
                          height="30"
                          v-bind="attrs"
                          v-on="on"
                          style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          outlined icon @click="showDetailUser(item)">
                          <v-icon color="#27AB9C" class="" size="18">mdi-account-details-outline</v-icon>
                        </v-btn>
                      </template>
                      <span>รายละเอียดผู้สมัครเข้าร่วม Affiliate</span>
                  </v-tooltip>
                  <v-btn small text rounded color="#27AB9C" @click="showDetailUser(item)">
                    <b style="text-decoration: underline;; font-size: 14px;">รายละเอียด</b>
                  </v-btn>
                </template>
                <template v-slot:[`item.detailShop`]="{ item }">
                  <v-tooltip bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-btn
                          width="30"
                          height="30"
                          v-bind="attrs"
                          v-on="on"
                          style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                          outlined icon @click="showDetailShop(item)">
                          <v-icon color="#27AB9C" class="" size="18">mdi-storefront theme--light</v-icon>
                        </v-btn>
                      </template>
                      <span>รายละเอียดร้านค้าที่ผู้สมัครได้เข้าร่วม</span>
                  </v-tooltip>
                  <v-btn small text rounded color="#27AB9C" @click="showDetailShop(item)">
                    <b style="text-decoration: underline;; font-size: 14px;">ร้านค้าที่เข้าร่วม</b>
                  </v-btn>
                </template>
              </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <DetailUserModal ref="DetailUserModal" />
    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'

export default {
  components: {
    DetailUserModal: () => import(/* webpackPrefetch: true */ '@/components/AdminPanit/Affiliate/DialogUserJoinAffiliate')
  },
  data () {
    return {
      search: '',
      userDataList: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-สกุล', value: 'username', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'detailUser', filterable: false, width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ร้านค้าที่เข้าร่วม', value: 'detailShop', filterable: false, width: '190', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/userJoinAffiliateMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'userJoinAffiliate')
        this.$router.push({ path: '/userJoinAffiliate' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getUserJoinAffiliate()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getUserJoinAffiliate () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetUserJoinAffiliate')
      var response = await this.$store.state.ModuleAdminManage.stateGetUserJoinAffiliate
      if (response.message === 'Get user with affiliate') {
        this.$store.commit('closeLoader')
        this.userDataList = response.data.users_with_affiliate
        if (this.userDataList.length !== 0) {
          this.userDataList.forEach(element => {
            element.username = element.first_name_th + ' ' + element.last_name_th
          })
          for (var i = 0; i < this.userDataList.length; i++) {
            this.userDataList[i].indexOfUser = i + 1
          }
        }
      } else if (response.message === 'This user didn’t have permissions.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ผู้ใช้รายนี้ไม่มีสิทธิ์ใช้งานแอดมิน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'Error message.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    showDetailUser (item) {
      const data = item
      this.$refs.DetailUserModal.open(data)
    },
    showDetailShop (item) {
      if (this.MobileSize) {
        this.$router.push(`/showShopUserJoinAffiliateMobile?userID=${item.id}`)
      } else {
        this.$router.push(`/showShopUserJoinAffiliate?userID=${item.id}`)
      }
    },
    async exportUserExcel () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}affiliate/export/buyer`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'reportUserJoinAffiliate.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function (error) {
        console.log(error)
        this.$store.commit('closeLoader')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(5) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(5) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
