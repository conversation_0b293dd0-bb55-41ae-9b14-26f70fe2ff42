<template>
  <v-container>
    <v-overlay :value="overlay2">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <a-row type="flex">
      <a-col :span="24">
        <a-row type="flex">
          <a-col :span="12">
            <span style="font-weight: bold;">ร้านค้าที่เกี่ยวข้องกับ "{{ textSearch }}"</span>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="24" style="padding: 0; margin-top: -20px; margin-bottom: -20px;">
        <a-divider></a-divider>
      </a-col>
      <a-col :span="24" style="margin-top: 0px;">
        <v-card class="mx-auto mt-5 mb-5" max-width="100%" outlined hover v-for="(item, index) in allShopData" :key="index" @click="gotoShopDetail(item)">
          <v-card-text style="padding: 1.5625rem;">
            <v-row no-gutters justify="start">
                <v-col cols="1" md="1" sm="12" xs="12">
                <v-avatar size="60" @click="gotoShopDetail(item)" v-if="item.path_logo !== ''" style="cursor: pointer;">
                  <img
                   alt="user"
                   :src="`${item.path_logo}?=${new Date().getTime()}`"
                  >
                </v-avatar>
                <v-avatar height="100" width="100" v-else style="cursor: pointer;" @click="gotoShopDetail(item)">
                  <v-icon>
                    mdi-storefront
                  </v-icon>
                </v-avatar>
                </v-col>
                <v-col cols="2" md="2" sm="12" xs="12">
                <v-row dense no-gutters justify="start">
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <p style="font-weight: bold; font-size: 15px;">{{ item.name_th }}</p>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <v-btn outlined small color="orange" @click="gotoShopDetail(item)"><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
                    </v-col>
                </v-row>
                </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </a-col>
    </a-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Col, Row } from 'ant-design-vue'
export default {
  components: {
    'a-row': Row,
    'a-col': Col
  },
  data () {
    return {
      allShopData: [],
      overlay2: true,
      textSearch: '',
      PathImage: process.env.VUE_APP_IMAGE
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('AllShopSearch') !== null) {
      this.getAllItem()
    } else {
      this.$swal.fire({
        icon: 'error',
        title: 'ไม่เจอร้านค้า',
        showConfirmButton: false,
        timer: 1500
      })
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    getAllItem () {
      this.textSearch = this.$router.currentRoute.params.data
      this.allShopData = JSON.parse(Decode.decode(localStorage.getItem('AllShopSearch')))
      this.overlay2 = false
      // console.log(this.allShopData)
    },
    gotoShopDetail (val) {
      const shopCleaned = encodeURIComponent(val.name_th.replace(/\s/g, '-'))
      this.$router.push(`/shoppage/${shopCleaned}-${val.id}`).catch(() => {})
    }
  }
}
</script>

<style>

</style>
