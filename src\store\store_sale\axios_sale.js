import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  // approve_position
  async listSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/list_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listSelectUserSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/list_select_user_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async listSelectCompany (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/list_select_company`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async settingSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/sale/setting_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
