<template>
  <div>
    <v-form ref="form" v-model="valid" lazy-validation>
      <v-card outlined>
        <v-card-text>
          <v-subheader><h2>รายละเอียดบริษัท</h2></v-subheader>
        </v-card-text>
        <div class="pa-5">
          <v-row>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  ชื่อบริษัท (ไทย)
                </v-col>
                <v-col cols="3">
                  บริษัท ลิเวอร์พูล จำกัด
                </v-col>
                <v-col cols="3">
                  ชื่อบริษัท (อังกฤษ)
                </v-col>
                <v-col cols="3">
                  liverpool
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  รหัสบริษัท
                </v-col>
                <v-col cols="3">
                  Ballllll
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  เป็นบริษัทลูกหรือไม่ ? <span class="red--text">*</span>
                </v-col>
                <v-col cols="3">
                  <v-radio-group v-model="radiovalue1" row required>
                    <v-radio label="Yes" value="yes" color="info"></v-radio>
                    <v-radio label="No" value="no" color="info"></v-radio>
                  </v-radio-group>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-if="radiovalue1 ==='no'">
              <v-row>
                <v-col cols="3">
                  คุณมีสาขาย่อยหรือไม่ ? <span class="red--text">*</span>
                </v-col>
                <v-col cols="3">
                  <v-radio-group v-model="radiovalue2" row>
                    <v-radio label="Yes" color="info"></v-radio>
                    <v-radio label="No" color="info"></v-radio>
                  </v-radio-group>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-if="radiovalue1 === 'yes'">
              <v-row>
                <v-col cols="3">
                  ชื่อแผนก (ภาษาไทย) <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="companynameTH" :rules="[() => !!companynameTH || 'This field is required']" hint="โปรดระบุคำนำหน้าชื่อบริษัท" persistent-hint outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-else>
              <v-row>
                <v-col cols="3">
                  ชื่อแผนก (ภาษาไทย) <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field outlined dense value="ชื่อภาษาไทย" disabled></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-if="radiovalue1 === 'yes'">
              <v-row>
                <v-col cols="3">
                  ชื่อบริษัท (ภาษาอังกฤษ) <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="companynameEN" :rules="[() => !!companynameEN || 'This field is required']" hint="โปรดระบุคำนำหน้าชื่อบริษัท" persistent-hint outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-else>
              <v-row>
                <v-col cols="3">
                  ชื่อบริษัท (ภาษาอังกฤษ) <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field outlined dense value="eng name" disabled></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-if="radiovalue1 === 'yes'">
              <v-row>
                <v-col cols="3">
                  รหัสลูกค้า <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="usercode" :rules="[() => !!usercode || 'This field is required']" hint="กรอก 6-15 ตัวอักษร (กรอกภาษาอังกฤษ ตัวเลข เท่านั้น)" persistent-hint outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-else>
              <v-row>
                <v-col cols="3">
                  รหัสลูกค้า <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field outlined dense value="รหัส" disabled></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-if="radiovalue1 === 'yes'">
              <v-row>
                <v-col cols="3">
                  รหัสประจำตัวผู้เสียภาษี <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="texid" :rules="[() => !!texid || 'This field is required']" hint="กรอกตัวเลข 13 ตัว" persistent-hint outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12" v-else>
              <v-row>
                <v-col cols="3">
                  รหัสประจำตัวผู้เสียภาษี <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field outlined dense value="รหัสประจำตัวผู้เสียภาษี" disabled></v-text-field>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </div>
        <v-divider></v-divider>
        <v-card-text>
          <v-subheader><h2>เพิ่มแผนก</h2></v-subheader>
        </v-card-text>
        <div class="pa-5">
          <v-row>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  วงเงิน <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="credit" :rules="[() => !!credit || 'This field is required']" disabled outlined dense></v-text-field>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-dialog transition="dialog-bottom-transition" max-width="600">
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        color="primary"
                        v-bind="attrs"
                        v-on="on"
                        outlined
                        rounded
                      >เพิ่มวงเงิน</v-btn>
                    </template>
                    <template v-slot:default="dialog">
                      <v-card max-width="600" class="mx-auto">
                        <v-toolbar color="primary" dark>
                          <v-toolbar-title>
                            วงเงิน : ปี
                          </v-toolbar-title>
                        </v-toolbar>
                        <v-container grid-list-xs>
                          <v-row class="pt-5">
                            <v-col cols="3" class="text-right">
                              ปี 1
                            </v-col>
                            <v-col cols="3" class="pt-0">
                              <v-text-field type="number" v-model="credit" outlined dense></v-text-field>
                            </v-col>
                            <span class="red--text pt-3">*วันที่เริ่ม : february</span>
                          </v-row>
                        </v-container>
                        <v-card-actions class="justify-end">
                          <v-btn rounded outlined color="primary" @click="dialog.value = false">ใช้</v-btn>
                        </v-card-actions>
                      </v-card>
                    </template>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  รหัสแผนก <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="departmentcode" :rules="[() => !!departmentcode || 'This field is required']" outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  ชื่อแผนก (ภาษาไทย) <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="departmentnameTH" :rules="[() => !!departmentnameTH || 'This field is required']" outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              <v-row>
                <v-col cols="3">
                  ชื่อแผนก (ภาษาอังกฤษ) <span class="red--text">*</span>
                </v-col>
                <v-col cols="3" class="pt-0">
                  <v-text-field v-model="departmentnameEN" :rules="[() => !!departmentnameEN || 'This field is required']" outlined dense></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="12">
              การจัดส่ง
              <v-container grid-list-xs>
                <v-row>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        ชื่อผู้รับ <span class="red--text">*</span>
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field v-model="recipientname" :rules="[() => !!recipientname || 'This field is required']" outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        หมายเลขโทรศัพท์ <span class="red--text">*</span>
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field v-model="recipienttel" :rules="[() => !!recipienttel || 'This field is required']" outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        หมายเลขมือถือ
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field v-model="tel" outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        แฟกซ์
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field v-model="fax" outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-container>
            </v-col>
            <v-col cols="12" md="12">
              การเรียกเก็บเงิน
              <v-container grid-list-xs>
                <v-row>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        หมายเลขโทรศัพท์ <span class="red--text">*</span>
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field v-model="collectmoneytel" :rules="[() => !!collectmoneytel || 'This field is required']" outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        หมายเลขมือถือ
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row>
                      <v-col cols="3">
                        แฟกซ์
                      </v-col>
                      <v-col cols="3" class="pt-0">
                        <v-text-field outlined dense></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-container>
            </v-col>
          </v-row>
        </div>
        <v-card-actions>
          <v-btn color="primary" outlined rounded @click="goPage('/departmentanddivision')">ย้อนกลับ</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="success" outlined rounded @click="validate()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-form>
  </div>
</template>

<script>
export default {
  data () {
    return {
      valid: true,
      radiovalue1: 'yes',
      radiovalue2: null,
      companynameTH: '',
      companynameEN: '',
      usercode: '',
      texid: '',
      credit: null,
      departmentcode: '',
      departmentnameTH: '',
      departmentnameEN: '',
      recipientname: '',
      recipienttel: '',
      collectmoneytel: '',
      tel: '',
      fax: ''
    }
  },
  watch: {
    radiovalue1 (val) {
      if (val === 'yes') {
        this.radiovalue2 = null
      }
    }
  },
  created () {
  },
  methods: {
    goPage (str) {
      this.$router.push(str)
    },
    validate () {
      this.$refs.form.validate()
    },
    reset () {
      this.$refs.form.reset()
    },
    resetValidation () {
      this.$refs.form.resetValidation()
    }
  }
}
</script>

<style scoped>
.v-input--selection-controls {
    margin-top: 0px;
    padding-top: 0px;
}
</style>
