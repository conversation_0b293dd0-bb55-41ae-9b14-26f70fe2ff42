<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row v-if="showDetailOrder.length > 0">
        <!-- หัวข้อเรื่อง -->
        <v-col cols="12">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายละเอียดบริษัท</v-card-title>
          <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายละเอียดบริษัท</v-card-title>
        </v-col>
        <v-col cols="12">
          <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
            <v-card-text>
              <v-row dense style="display: flex; gap: 1vw;">
                <!-- ข้อมูลส่วน box แรก -->
                <v-col cols="12" md="6" sm="12" :style="MobileSize ? 'background: #ECEFF4; padding: 6vw; border-radius: 10px;' : 'background: #F9FAFD; padding: 3vw; border-radius: 20px;'">
                  <v-row>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].payment_transaction }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ : </span>
                      <span v-if="showDetailOrder[0].buyer_name !== null" style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].buyer_name }}</span>
                      <span v-else>-</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ : </span>
                      <span v-if="showDetailOrder[0].created_at !== '-'" style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(showDetailOrder[0].created_at).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>{{ showDetailOrder[0].created_at }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ชำระเงิน : </span>
                      <span v-if="showDetailOrder[0].paid_datetime !== '-'" style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(showDetailOrder[0].paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                      <span v-else>{{ showDetailOrder[0].paid_datetime }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">ใบกำกับภาษี : </span>
                      <span v-if="showDetailOrder[0].transactionCode === '-'" style="font-size: 16px; font-weight: 400; color: #333333;">ขอใบกำกับภาษี</span>
                      <a v-else-if="showDetailOrder[0].transactionCode !== '-'" @click="GetETax(showDetailOrder[0].transactionCode)">
                        <span style="font-size: 16px; font-weight: 400; color: #27ab9c; border-bottom: 1px solid;">{{ showDetailOrder[0].payment_transaction }}</span>
                      </a>
                      <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">ไม่มีใบกำกับภาษี</span>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- ข้อมูลส่วน box สอง -->
                <v-col style="background: #F9FAFD; padding: 2vw; border-radius: 20px;">
                  <v-card elevation="0" :width="!IpadSize && !MobileSize ? '100%' : '100%'" height="100%" style="border-radius: 8px; background: #FFFFFF; padding: 2vw;">
                    <v-card-text>
                      <v-row :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">
                        <v-col cols="12">
                          <!-- transection status -->
                          <span style="font-weight: 600;">สถานะการสั่งซื้อ : </span>
                          <span v-if="showDetailOrder[0].delivery_status === 9">
                            <span v-if="showCountRequest[0].buyer_received_status === 'received'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                            <span v-else-if="showCountRequest[0].buyer_received_status === 'not_received'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                            <span v-else>{{ showDetailOrder[0].transportation_status }}</span>
                          </span>
                          <span v-else-if="showDetailOrder[0].delivery_status === 0" style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                          <span v-else-if="!showDetailOrder[0].delivery_status" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                          <span v-else-if="showDetailOrder[0].delivery_status !== 9 && showDetailOrder[0].delivery_status !== 1 && showDetailOrder[0].delivery_status !== 0" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                          <span v-else style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-weight: 600;">วัน-เวลาส่งสินค้า : </span>
                          <span v-if="showDetailOrder[0].delivery_status !== 1 && showDetailOrder[0].tracking.send_date !== '-'">{{new Date(showDetailOrder[0].tracking.send_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                          <span v-else-if="showDetailOrder[0].delivery_status !== 1">{{ showDetailOrder[0].tracking.send_date }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-weight: 600;">วัน-เวลารับสินค้า : </span>
                          <span v-if="showDetailOrder[0].tracking.received_date !== '-'">{{new Date(showDetailOrder[0].tracking.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                          <span v-else-if="showDetailOrder[0].delivery_status === 9">{{ showDetailOrder[0].tracking.received_date }}</span>
                          <span v-else>{{ showDetailOrder[0].tracking.received_date }}</span>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
            <v-col cols="12" class="pt-6" v-if="MobileSize">
              <v-divider></v-divider>
            </v-col>
            <!-- รายละเอียดเอกสาร -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 7.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/docCompany.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  รายละเอียดเอกสาร
                </span>
              </v-row>
              <v-row>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสนอราคา : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].payment_transaction }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบขอซื้อ (PR) : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].pr_document_id }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบสั่งซื้อ (PO) : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].po_document_id }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)"  :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ Sale Order : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].ref_callback_so_id }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสร็จ : </span>
                  <span v-if="showDetailOrder[0].receipt[0].orderIDRef !== null">{{ showDetailOrder[0].receipt[0].orderIDRef }}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
                <!-- ที่อยู่ในการจัดส่งสินค้า -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/mapCompany.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  ที่อยู่ในการจัดส่งสินค้า
                </span>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="12" sm="12">
                  <v-row class="pt-6">
                    <v-col cols="12" style="font-size: 16px; font-weight: 400; color: #333333;">
                      <span v-if="showDetailOrder[0].user_address !== ''">
                        <span v-if="showDetailOrder[0].invoice_address === [] || showDetailOrder[0].invoice_address[0] === '-'">
                          {{ showDetailOrder[0].user_address }}
                        </span>
                        <span v-else>
                          {{ showDetailOrder[0].invoice_address[0].first_name + ' ' + showDetailOrder[0].invoice_address[0].last_name }} {{ showDetailOrder[0].user_address }}
                        </span>
                      </span>
                      <span v-else style="color: #7777;">ไม่มีที่อยู่ในการจัดส่งสินค้า</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
                <!-- ที่อยู่ในการจัดส่งใบกำกับภาษี -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/mapCom2.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  ที่อยู่ในการจัดส่งใบกำกับภาษี
                </span>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="12" sm="12">
                  <v-row class="pt-6">
                    <v-col cols="12" style="font-size: 16px; font-weight: 400; color: #333333;">
                      <span v-if="showDetailOrder[0].invoice_address.length !== 0 && showDetailOrder[0].invoice_address[0] !== '-'">
                        <span v-if="showDetailOrder[0].invoice_address.length !== 0 && showDetailOrder[0].invoice_address[0].id !== null">
                          {{ showDetailOrder[0].invoice_address[0].first_name + ' ' + showDetailOrder[0].invoice_address[0].last_name }}
                          {{ showDetailOrder[0].invoice_address[0].detail }} หมู่บ้าน {{ showDetailOrder[0].invoice_address[0].street }} แขวง/ตำบล {{ showDetailOrder[0].invoice_address[0].sub_district }}
                          เขต/อำเภอ {{ showDetailOrder[0].invoice_address[0].district }} จังหวัด {{ showDetailOrder[0].invoice_address[0].province }} รหัสไปรษณีย์ {{ showDetailOrder[0].invoice_address[0].zip_code }}
                          เบอร์มือถือ {{ showDetailOrder[0].invoice_address[0].phone }} เบอร์โทรศัพท์ {{ showDetailOrder[0].invoice_address[0].phone_ext }}
                        </span>
                        <span v-if="showDetailOrder[0].invoice_address[0].phone_ext === null">-</span>
                      </span>
                      <span v-else style="color: #7777;">ไม่มีที่อยู่ในการจัดส่งใบกำกับภาษี</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- รายการสั่งซื้อสินค้า -->
            <v-col cols="12" :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/cart.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  รายการสั่งซื้อสินค้า
                </span>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 700;">สินค้าทั่วไป</span>
                  <v-data-table
                  :headers="headers"
                  :items="showDetailOrder[0].product_list"
                  style="width:100%; text-align: center !important;"
                  height="100%"
                  @pagination="countRequest"
                  :items-per-page="5"
                  class="elevation-1 mt-4"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  >
                    <template v-slot:[`item.sku`]="{ item }">
                      <span style="white-space: nowrap;">{{ item.sku }}</span>
                    </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6" v-if="MobileSize">
              <v-divider></v-divider>
            </v-col>
            <!-- โปรโมชันและคูปองส่วนลด -->
            <v-col cols="12" :style="MobileSize ? 'padding: 1vw 2vw 2vw 5.5vw;' : 'padding: 1vw 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/coupon.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  โปรโมชันและคูปองส่วนลด
                </span>
              </v-row>
              <v-row>
                <v-col cols="12" style="background-color: #e6f5f3; padding: 1vw; font-size: medium;">
                  <span>ส่วนลดจากแต้ม {{ showDetailOrder[0].total_price_discount }} บาท</span>
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
                  <span>{{ Number(showDetailOrder[0].total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ส่วนลด</span>
                  <span>{{ Number(showDetailOrder[0].total_price_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ภาษีมูลค่าเพิ่ม</span>
                  <span>{{ Number(showDetailOrder[0].total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
                  <span>{{ Number(showDetailOrder[0].total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ค่าจัดส่ง</span>
                  <span>{{ showDetailOrder[0].total_shipping }} บาท</span>
                </v-col>
                <v-col cols="12" :style="MobileSize ? 'display: flex; justify-content: space-between; margin-top: -6.5vw;' : 'display: flex; justify-content: space-between; margin-top: -1.5vw;'">
                  <span style="font-size: 12px; color: #bbb;">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป <br> ขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span style="font-size: 18px; font-weight: 700;">ราคารวมทั้งหมด</span>
                  <span style="color: rgb(39, 171, 156); font-size: 18px; font-weight: 700;">{{ Number(showDetailOrder[0].net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- รายละเอียดรายการสั่งซื้อ -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/detailSell.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  รายละเอียดรายการสั่งซื้อ
                </span>
              </v-row>
              <v-row style="font-size: 16px; font-weight: 600;">
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>วันที่เริ่มสัญญา : </span>
                  <span v-if="showDetailOrder[0].start_date_contract !== null" style="font-size: 16px; font-weight: 400; color: #333333;"> {{ showDetailOrder[0].start_date_contract }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>วันที่สิ้นสุดสัญญา : </span>
                  <span v-if="showDetailOrder[0].end_date_contract !== null" style="font-size: 16px; font-weight: 400; color: #333333;">{{ showDetailOrder[0].end_date_contract }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>Pay Type : </span>
                  <v-chip v-if="showDetailOrder[0].pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)"> One Time</v-chip>
                  <v-chip v-else-if="showDetailOrder[0].pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                  <v-chip v-else-if="showDetailOrder[0].pay_type === 'general'" text-color="#808c96" color="#eff2f2">general</v-chip>
                  <span v-else>-</span>
                  <!-- <v-chip class="ma-2" color="green" text-color="white">Green Chip</v-chip> -->
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>ส่วนลด : </span>
                  <span>{{ Number(showDetailOrder[0].total_price_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>หมวดงบประมาณ : </span>
                  <span v-if="showDetailOrder[0].type_budget !== null">{{ showDetailOrder[0].type_budget }} บาท</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>หมวดตัดงบ : </span>
                  <span v-if="showDetailOrder[0].budget_cut !== null">{{ showDetailOrder[0].budget_cut }} บาท</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>หมายเหตุ : </span>
                  <span>{{ showDetailOrder[0].remark }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- การชำระเงิน -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense style="display: flex; justify-content: space-between;">
                <div class="d-flex">
                  <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/pay.png')" max-height="30" max-width="30"></v-img>
                  <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                    การชำระเงิน
                  </span>
                  <span v-if="showDetailOrder[0].transaction_status === 'Success'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ showDetailOrder[0].order_status }}</span>
                  <span v-if="showDetailOrder[0].transaction_status === 'Fail'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].order_status }}</span>
                  <span v-if="showDetailOrder[0].transaction_status === 'Cancel' || showDetailOrder[0].transaction_status === 'Not Paid'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].order_status }}</span>
                  <!-- <span v-else style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ showDetailOrder[0].order_status }}</span> -->
                </div>
                <!-- <div>
                  <v-chip small text-color="#E9A016" color="#FCF0DA" rounded>
                    <span class="pt-1">รอร้านค้าอนุมัติคำสั่งซื้อ</span>
                  </v-chip>
                </div> -->
              </v-row>
              <v-row v-if="showDetailOrder[0].order_status === 'ชำระเงินสำเร็จ'">
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการชำระเงิน : </span>
                  <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ showDetailOrder[0].receipt[0].orderId }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">จำนวนเงิน : </span>
                  <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ Number(showDetailOrder[0].receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">วันเวลาที่ชำระเงิน : </span>
                  <span>{{new Date(showDetailOrder[0].tracking.paid_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ref : </span>
                  <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ showDetailOrder[0].receipt[0].orderIDRef }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รูปแบบการชำระเงิน : </span>
                  <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ showDetailOrder[0].receipt[0].payType }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ผลการชำระเงิน : </span>
                  <span v-if="showDetailOrder[0].receipt[0].id !== null">
                    <span v-if="showDetailOrder[0].receipt[0].rmsg === 'Success'">{{ showDetailOrder[0].order_status}}</span>
                    <span v-else-if="showDetailOrder[0].receipt[0].rmsg === 'Fail'">{{ showDetailOrder[0].order_status}}</span>
                    <span v-if="showDetailOrder[0].receipt[0].rmsg === 'Cancel'">{{ showDetailOrder[0].order_status}}</span>
                  </span>
                  <span v-else>-</span>
                </v-col>
              </v-row>
              <v-row v-else>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการชำระเงิน : </span>
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">จำนวนเงิน : </span>
                  <!-- <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ Number(showDetailOrder[0].receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span> -->
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">วันเวลาที่ชำระเงิน : </span>
                  <!-- <span>{{new Date(showDetailOrder[0].tracking.paid_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span> -->
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ref : </span>
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รูปแบบการชำระเงิน : </span>
                  <!-- <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ showDetailOrder[0].receipt[0].payType }}</span> -->
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ผลการชำระเงิน : </span>
                  <!-- <span v-if="showDetailOrder[0].receipt[0].id !== null">
                    <span v-if="showDetailOrder[0].receipt[0].rmsg === 'Success'">{{ showDetailOrder[0].order_status}}</span>
                    <span v-else-if="showDetailOrder[0].receipt[0].rmsg === 'Fail'">{{ showDetailOrder[0].order_status}}</span>
                    <span v-if="showDetailOrder[0].receipt[0].rmsg === 'Cancel'">{{ showDetailOrder[0].order_status}}</span>
                  </span> -->
                  <span>-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      showDetailOrder: [],
      // order_number: { order_number: 'NGS-20240801655' },
      // order_number: { order_number: 'BB-20240800663' },
      headers: [
        { text: 'รหัส SKU', value: 'sku', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียดสินค้า', value: 'product_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'revenue_default', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'total_revenue_default', sortable: false, class: 'backgroundTable fontTable--text' }
        // { text: 'Amount', value: 'AM', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    async MobileSize (val) {
      var id = this.$route.query.order_number
      localStorage.setItem('paramID', id)
      if (val === true) {
        this.$router.push({ path: `/detailOrderCompanyMobile?order_number=${id}` }).catch(() => {})
      } else {
        await localStorage.setItem('pathBusiness', 'detailOrderCompany')
        this.$router.push({ path: '/detailOrderCompany' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    // var id = Number(this.$route.query.seller_shop_id)
    if (localStorage.getItem('oneData') !== null) {
      this.orderDetailCompany()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    var id = this.$route.query.order_number
    localStorage.setItem('paramID', id)
  },
  methods: {
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/listOrderCompany' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listOrderCompanyMobile' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async orderDetailCompany () {
      this.$store.commit('openLoader')
      var body = { order_number: this.$route.query.order_number }
      // await this.$store.dispatch('actionsOrderDetailCompany', this.order_number)
      await this.$store.dispatch('actionsOrderDetailCompany', body)
      var response = await this.$store.state.ModuleBusiness.stateOrderDetailCompany
      this.showDetailOrder = response.query_result.orderJV_details
      this.$store.commit('closeLoader')
    },
    async GetETax (val) {
      var data = { transactionCode: val }
      await this.$store.dispatch('ActionsGetETaxPDF', data)
      var response = await this.$store.state.ModuleCart.stateGetETaxPDF
      if (response.result === 'OK') {
        if (response.etaxResponse.status === 'OK') {
          if (response.etaxResponse.urlPdf !== undefined) {
            window.open(`${response.etaxResponse.urlPdf}`, '_blank')
            // console.log('response', response.etaxResponse.urlPdf)
          } else {
            window.open(`${response.etaxResponse.pdfURL}`, '_blank')
          }
          // window.open(`${response.etaxResponse.pdfURL}`)
        }
      }
    }
  }
}
</script>

<style scoped>
div {
  border-radius: 5px;
}
.v-application--is-ltr .v-data-table > .v-data-table__wrapper > table > thead > tr > th {
  text-align: center !important;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
  text-align: center !important;
}
</style>
