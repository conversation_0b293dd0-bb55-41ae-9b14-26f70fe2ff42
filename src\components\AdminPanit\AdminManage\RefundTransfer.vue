<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col :cols="MobileSize ? 8 : 6">
          <v-card-title style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333;" v-if="!MobileSize">โอนเงินคืนลูกค้า</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2 d-flex" @click="backtoPage()">mdi-chevron-left</v-icon>โอนเงินคืนลูกค้า</v-card-title>
        </v-col>
      </v-row>
      <v-form ref="form" v-model="formRefund" lazy-validation>
        <v-row>
          <v-col cols="12">
            <v-card :class="MobileSize || IpadSize ? '' : 'mx-15 mb-10'">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="50">
                <span class="flex text-center ml-5" style="font-size: large; font-weight: 700; color: #FFFFFF;">ข้อมูลการโอนเงิน</span>
              </v-toolbar>
              <v-card-text>
                <v-row>
                  <v-col :cols="MobileSize ? 12 : 6">
                    <span>เลข order <span style="color: red;">*</span></span>
                    <v-text-field
                      v-model="orderNumber"
                      outlined
                      dense
                      placeholder="ระบุเลข order"
                      :rules="checkValidateRule ? Rules.empty : []"
                      @focus="checkValidateRule = true"
                      oninput="this.value = this.value.replace(/[^a-zA-Z0-9_-\s]/g, '')"
                    ></v-text-field>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 6" :style="MobileSize ? 'margin-top: -20px' : ''">
                    <span>เลข transaction <span style="color: red;">*</span></span>
                    <v-text-field
                      v-model="transactionNumber"
                      outlined
                      dense
                      placeholder="ระบุเลข transaction"
                      :rules="checkValidateRule ? Rules.empty : []"
                      @focus="checkValidateRule = true"
                      oninput="this.value = this.value.replace(/[^a-zA-Z0-9_-\s]/g, '')"
                    ></v-text-field>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 6" style="margin-top: -20px">
                    <span>บัญชีธนาคาร <span style="color: red;">*</span></span>
                    <v-select
                      v-model="bankAccount"
                      :items="listBank"
                      placeholder="ระบุบัญชีธนาคาร"
                      dense
                      outlined
                      item-value="code"
                      item-text="name"
                      :rules="checkValidateRule ? Rules.empty : []"
                      @focus="checkValidateRule = true"
                      @change="handleChange"
                    ></v-select>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 6" style="margin-top: -20px">
                    <span>เลขบัญชีธนาคาร <span style="color: red;">*</span></span>
                    <v-text-field
                      v-model="bankNumber"
                      outlined
                      dense
                      placeholder="ระบุเลขบัญชีธนาคาร"
                      :rules="checkValidateRule ? Rules.empty : []"
                      @focus="checkValidateRule = true"
                      oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 15)"
                      :maxLength="checkLengthValid"
                    ></v-text-field>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 6" style="margin-top: -20px">
                    <span>จำนวนเงิน <span style="color: red;">*</span></span>
                    <v-text-field
                      v-model="amount"
                      outlined
                      dense
                      placeholder="ระบุจำนวนเงิน"
                      :rules="checkValidateRule ? Rules.empty : []"
                      @focus="checkValidateRule = true"
                      oninput="this.value = this.value.replace(/[^0-9.]/g, '')"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions class="d-flex px-5">
                <v-btn rounded outlined color="#27AB9C" @click="clsValidate()">ล้างค่า</v-btn>
                <v-spacer></v-spacer>
                <v-btn rounded color="#27AB9C" style="color: #fff;" @click="checkValidate()">โอนเงิน</v-btn>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>
      </v-form>
    </v-card>
    <v-dialog persistent v-model="dialogReqOTP" width="400px" content-class="elevation-0">
      <v-card style="border-radius: 22px;" class="d-flex justify-center pa-4">
        <v-row>
          <v-col cols="12" class="d-flex justify-end">
            <v-btn icon dark @click="backToMainPage">
              <v-icon color="#a0a0a0">mdi-close</v-icon>
            </v-btn>
          </v-col>
          <v-col class="d-flex flex-column align-center" style="gap: 5px;">
            <img src="@/assets/sendOTP.png" alt="" width="200" height="200">
            <!-- <v-img
              src="@/assets/sendOTP.png"
              max-width="500"
              max-height="500"
              contain
            /> -->
            <span :style="MobileSize || IpadSize ? 'margin-top: 8px; font-size: medium;' : 'margin-top: 8px; font-size: medium;'"><b>ยืนยันสิทธิ์จัดการการโอนเงิน</b></span>
            <span style="font-size: 16px;">ระบบได้ส่งรหัส OTP ไปยัง Email แล้ว</span>
            <v-otp-input
              v-model="otp"
              :length="6"
              type="number"
              dense
              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
            ></v-otp-input>
            <span v-if="message !== ''" style="font-size: 16px;">{{ message }} <b>{{ email }}</b></span>
            <span style="font-size: 16px;">สามารถขอรหัส OTP อีกครั้งภายใน {{ resendCountdown }} <v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon>mdi-refresh</v-icon></v-btn></span>
            <!-- <v-btn
              rounded
              color="#27AB9C"
              width="190"
              style="color: #fff;"
              @click="sendOTP()"
              :disabled="this.otp.length !== 6"
            >
              ยืนยัน OTP
            </v-btn> -->
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirmRefund" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogConfirmRefund = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-container>
          <div class="d-flex justify-center">
            <v-avatar :size="MobileSize ? 90 : 250" tile><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/icons/shopping-basket 1sucessBank.png" alt=""></v-avatar>
          </div>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: large; line-height: 24px; color: #333333;" class="my-4"><b>โอนเงินคืนลูกค้า</b></p>
            <span style="font-weight: 400; font-size: small; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการโอนเงิน ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogConfirmRefund = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="refundTransfer">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      orderNumber: '',
      transactionNumber: '',
      bankNumber: '',
      amount: '',
      bankAccount: '',
      listBank: [],
      formRefund: true,
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล']
      },
      message: '',
      email: '',
      dialogReqOTP: false,
      resendCountdown: '00:00',
      disableRefreshOTP: false,
      otp: '',
      dialogConfirmRefund: false,
      checkValidateRule: true
    }
  },
  async created () {
    await this.checkVerify()
    this.getListBank()
  },
  mounted () {
    window.scrollTo(0, 0)
    this.otp = ''
    this.resendCountdown = '00:00'
    this.intervalId = setInterval(() => {
      this.checkVerify()
    }, 180000)
  },
  beforeDestroy () {
    clearInterval(this.intervalId)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkLengthValid () {
      if (this.bankAccount === '030') {
        return 15
      } else if (this.bankAccount === '033' || this.bankAccount === '034') {
        return 12
      } else {
        return 10
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/refundTransferMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/refundTransfer' }).catch(() => {})
      }
    },
    otp (val) {
      if (val && val.length === 6) {
        this.sendOTP()
      }
    }
  },
  methods: {
    handleChange () {
      this.bankNumber = ''
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async getListBank () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      // console.log(response)
      if (response.code === 200) {
        this.listBank = response.data
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: response.message,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    checkValidate () {
      if (this.$refs.form.validate(true)) {
        this.dialogConfirmRefund = true
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
        setTimeout(() => {
          this.$store.commit('closeLoader')
        }, 1000)
      }
    },
    async refundTransfer () {
      this.$store.commit('openLoader')
      var data = {
        order_number: this.orderNumber,
        transaction_id: this.transactionNumber,
        account_no: this.bankNumber,
        bank_code: this.bankAccount,
        amount: this.amount
      }
      await this.$store.dispatch('actionsRefundToCustomer', data)
      var res = await this.$store.state.ModuleAdminManage.stateRefundToCustomer
      if (res.code === 200) {
        this.checkValidateRule = false
        this.orderNumber = ''
        this.transactionNumber = ''
        this.bankNumber = ''
        this.bankAccount = ''
        this.amount = ''
        this.$nextTick(() => {
          this.bankNumberValidate = true
        })
        this.dialogConfirmRefund = false
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          html: 'โอนเงินคืนลูกค้าสำเร็จ',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      } else if (res.code === 401) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.dialogConfirmRefund = false
        // if (this.MobileSize) {
        //   this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        // } else {
        //   this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        // }
      }
    },
    clsValidate () {
      this.orderNumber = ''
      this.transactionNumber = ''
      this.bankNumber = ''
      this.bankAccount = ''
      this.amount = ''
    },
    async checkVerify () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionCheckVerify')
      var res = await this.$store.state.ModuleAdminManage.stateCheckVerify
      if (res.code === 200) {
        this.$store.commit('closeLoader')
      } else if (res.code === 400) {
        this.$store.commit('closeLoader')
        this.reqOTP()
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      }
    },
    async reqOTP () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionRegOTP')
      var res = await this.$store.state.ModuleAdminManage.stateRegOTP
      if (res.code === 200) {
        this.otp = ''
        this.countdownCheck(60)
        var nameEmailRes = res.message
        const parts = nameEmailRes.split('อีเมล ')
        this.message = parts[0].trim() + 'อีเมล'
        this.email = parts[1].trim()
        this.dialogReqOTP = true
        this.$store.commit('closeLoader')
      } else if (res.code === 401 || res.message === 'ไม่พบ Email ในระบบ') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: res.message === 'ไม่พบ Email ในระบบ' ? 'ไม่พบ Email ในระบบ' : 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else if (res.message === 'คุณสามารถขอ OTP ได้ทุก ๆ 1 นาที') {
        this.$store.commit('closeLoader')
        // await this.$swal.fire({
        //   icon: 'error',
        //   html: `${res.message}`,
        //   showConfirmButton: false,
        //   timer: 3000,
        //   timerProgressBar: true
        // })
        this.countdownCheck(60)
        this.dialogReqOTP = true
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    async sendOTP () {
      this.$store.commit('openLoader')
      var data = {
        otp: this.otp
      }
      await this.$store.dispatch('actionSendOTP', data)
      var res = await this.$store.state.ModuleAdminManage.stateSendOTP
      if (res.code === 200) {
        this.dialogReqOTP = false
        this.$store.commit('closeLoader')
      } else if (res.code === 401) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.otp = ''
        this.$store.commit('closeLoader')
      }
    },
    async RefreshOTP () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionRegOTP')
      var res = await this.$store.state.ModuleAdminManage.stateRegOTP
      if (res.code === 200) {
        this.countdownCheck(60)
        var nameEmailRes = res.message
        const parts = nameEmailRes.split('อีเมล ')
        this.message = parts[0].trim() + 'อีเมล'
        this.email = parts[1].trim()
        this.$store.commit('closeLoader')
      } else if (res.code === 401 || res.message === 'ไม่พบ Email ในระบบ') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: res.message === 'ไม่พบ Email ในระบบ' ? 'ไม่พบ Email ในระบบ' : 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    countdownCheck (second) {
      this.counter = second
      const interval = setInterval(() => {
        var minutes = Math.floor(this.counter / 60)
        var seconds = this.counter % 60
        seconds = seconds < 10 ? `0${seconds}` : seconds
        this.resendCountdown = `${minutes}:${seconds}`
        this.counter--
        if (this.counter < 0) {
          this.disableRefreshOTP = false
          clearInterval(interval)
        } else {
          this.disableRefreshOTP = true
        }
      }, 1000)
    },
    backToMainPage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      }
    }
  }
}
</script>

<style>

</style>
