<template>
  <v-container>
    <v-row class="mb-6" v-if="MobileSize">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card class="mt-6" max-height="100%" height="100%" style="border-radius: 8px; border: 1px solid #F2F2F2;" elevation="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-storefront</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>
            <v-list-item-group
              color="#27AB9C"
            >
              <v-list-item
                v-for="(item, i) in havePartnerShop ? items : itemsNoPartner"
                :key="i"
                :disabled="item.isDisabled"
                @click="changePage(item.path)"
              >
                <v-list-item-icon>
                  <v-icon>{{ item.action }}</v-icon>
                </v-list-item-icon>
                <v-list-item-content>
                  <v-list-item-title style="font-size: 16px; line-height: 24px;">{{ item.title }}</v-list-item-title>
                </v-list-item-content>
                <v-list-item-action v-if="item.action === ''">
                  <v-icon>mdi-chevron-right</v-icon>
                </v-list-item-action>
              </v-list-item>
            </v-list-item-group>
          </v-list>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  // components: {
  //   UserProfile: () => import('@/components/UserProfile/UserProfileUI')
  // },
  async created () {
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('getPath')
    this.$EventBus.$emit('resetSearch')
    this.$EventBus.$on('CheckPartnerCode', this.getPartnerCode)
    // this.$EventBus.$emit('CheckFooter')
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.assign('/')
      }
    })
    await this.getTaxId()
    await this.getPartnerCode()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    selectedItem (val) {
      // console.log(val)
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/detailbusinesssidMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'BackToMobile')
        this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  data () {
    return {
      selectedItem: 0,
      select: 0,
      items: [
        { key: 0, action: 'mdi-domain', title: 'จัดการนิติบุคคล', path: '', isDisabled: true },
        { key: 1, action: '', title: 'ข้อมูลนิติบุคคล', path: '/detailbusinesssidMobile', isDisabled: false },
        { key: 2, action: '', title: 'จัดการตำแหน่ง', path: '/managePositionComapny&BussinessMobile', isDisabled: false },
        { key: 3, action: '', title: 'จัดการผู้ใช้งาน', path: 'manageUserMobile', isDisabled: false },
        { key: 4, action: '', title: 'จัดการบริษัท & ร้านค้า', path: 'manageCompanyShopMobile', isDisabled: false },
        { key: 5, action: 'mdi-file-document-outline', title: 'จัดการรายการสั่งซื้อ', path: '', isDisabled: true },
        { key: 6, action: '', title: 'รายการสั่งซื้อร้านค้า', path: 'listOrderShopMobile', isDisabled: false },
        { key: 7, action: '', title: 'รายการสั่งซื้อบริษัท', path: 'listOrderCompanyMobile', isDisabled: false },
        { key: 8, action: 'mdi-file-document-outline', title: 'Software Marketplace', path: '', isDisabled: true },
        { key: 9, action: '', title: 'ข้อมูลร้านค้า Partner', path: 'partnerShopInfoMobile', isDisabled: false },
        { key: 10, action: '', title: 'สินค้าบริการของฉัน', path: 'serviceProductPartnerMobile', isDisabled: false },
        { key: 11, action: '', title: 'Preview สินค้าและบริการ', path: 'PreviewDetailPartnerMobile', isDisabled: false },
        { key: 12, action: '', title: 'คู่มือการใช้งานและสัมมนา', path: 'PackageUserManualAndSeminarMobile', isDisabled: false },
        { key: 13, action: '', title: 'ร้านค้าที่เชื่อมต่อ', path: 'shopConnectedMobile', isDisabled: false },
        { key: 14, action: '', title: 'รายการคำสั่งซื้อ', path: 'partnerOrderListMobile', isDisabled: false },
        { key: 15, action: '', title: 'วางบิล', path: 'PartnerBillingMobile', isDisabled: false },
        { key: 16, action: '', title: 'แดชบอร์ด', path: 'dashboardPartnerMobile', isDisabled: false }
      ],
      itemsNoPartner: [
        { key: 0, action: 'mdi-domain', title: 'จัดการนิติบุคคล', path: '', isDisabled: true },
        { key: 1, action: '', title: 'ข้อมูลนิติบุคคล', path: '/detailbusinesssidMobile', isDisabled: false },
        { key: 2, action: '', title: 'จัดการตำแหน่ง', path: '/managePositionComapny&BussinessMobile', isDisabled: false },
        { key: 3, action: '', title: 'จัดการผู้ใช้งาน', path: 'manageUserMobile', isDisabled: false },
        { key: 4, action: '', title: 'จัดการบริษัท & ร้านค้า', path: 'manageCompanyShopMobile', isDisabled: false },
        { key: 5, action: 'mdi-file-document-outline', title: 'จัดการรายการสั่งซื้อ', path: '', isDisabled: true },
        { key: 6, action: '', title: 'รายการสั่งซื้อร้านค้า', path: 'listOrderShopMobile', isDisabled: false },
        { key: 7, action: '', title: 'รายการสั่งซื้อบริษัท', path: 'listOrderCompanyMobile', isDisabled: false },
        { key: 8, action: 'mdi-file-document-outline', title: 'Software Marketplace', path: '', isDisabled: true },
        { key: 9, action: '', title: 'ข้อมูลร้านค้า Partner', path: 'partnerShopInfoMobile', isDisabled: false }
      ],
      havePartnerShop: true,
      taxId: ''
      // {
      //   action: 'mdi-file-document-outline',
      //   key: 2,
      //   items: [{ key: 5, title: 'รายการสั่งซื้อของฉัน', path: 'pobuyeruiProfile' }],
      //   title: 'รายการสั่งซื้อ'
      // }
    }
  },
  methods: {
    changePage (val) {
      this.$router.push({ path: `${val}` }).catch(() => {})
    },
    async getPartnerCode (val, isNewTaxID) {
      // console.log(val, isNewTaxID, 'checkEmitVal')
      var data = {
        id_card_num: isNewTaxID === 'newTaxId' ? val.tax_id : this.taxId
      }
      await this.$store.dispatch('actionsGetPartnerCode', data)
      var response = this.$store.state.ModuleBusiness.stateGetPartnerCode
      if (response.code === 200) {
        if (response.data.partner_code === null) {
          this.havePartnerShop = false
        } else {
          this.havePartnerShop = true
        }
      }
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        // this.taxId = response.data.array_business[0].owner_tax_id
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.backgroundPage{
  background-color: #F6F6F6;
}
.container {
  max-width: 100%;
}
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
