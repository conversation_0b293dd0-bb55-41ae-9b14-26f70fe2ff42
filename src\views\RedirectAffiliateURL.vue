<template>
    <div></div>
</template>

<script>
export default {
  data () {
    return {
      link: '',
      isAndroid: false,
      isiOS: false,
      URL: '',
      URLToStore: '',
      anID: '',
      productID: '',
      linkAffiliate: ''
    }
  },
  async created () {
    this.link = window.location.href
    // this.link = 'https://devinet-eprocurement.one.th/linkAffiliate/syLT4i3ue3'
    // this.linkAffiliate = window.location.href
    // this.link = 'https://devinet-eprocurement.one.th/DetailProduct/39395?utm_campaign=-&utm_content=----&utm_medium=affiliate&utm_source=an_06568882d4&utm_term=-'
    // console.log('link', this.link)
    let isAppOpened = false
    let timeout
    this.linkAffiliate = this.link.replace(/linkAffiliate/gi, 'api/backend_2/s')
    // console.log('this.link', this.link)
    await this.axios({
      url: this.linkAffiliate,
      method: 'GET'
    }).then((response) => {
      // console.log('response', response)
      if (response.data.code === 200) {
        this.anID = response.data.data.an_id
        this.productID = response.data.data.product_id
      }
    })
    // console.log('this.anID', this.anID)
    // console.log('this.productID', this.productID)
    const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
    this.isAndroid = /Android/i.test(userAgent)
    this.isiOS = /iPad|iPhone|iPod/.test(userAgent) && !window.MSStream
    this.URL = ''
    this.URLToStore = ''
    var dataGetlinkMobile = {
      an_id: this.anID,
      product_id: this.productID,
      type: 'affiliate'
    }
    await this.$store.dispatch('actionsGetLinkMobile', dataGetlinkMobile)
    const response = await this.$store.state.ModuleHompage.stateGetLinkMobile
    if (this.isAndroid === true) {
      this.URL = response.data.intentLink
    } else if (this.isiOS === true) {
      this.URL = response.data.short_url
      // console.log('this.URL 1', this.URL)
      this.URL = this.URL.replace(/https/gi, 'nexgencommerce')
      // console.log('this.URL 2', this.URL)
      this.URLToStore = 'https://apps.apple.com/th/app/nexgen-commerce/id6651839687?l=th'
    } else {
      this.URL = this.link.replace(/linkAffiliate/gi, 'api/backend_2/r')
    }
    // console.log('this.URL', this.URL)
    const onVisibilityChange = () => {
      // console.log(document.hidden)
      if (document.hidden) {
        isAppOpened = true
        clearTimeout(timeout)
      }
    }

    // console.log(this.URL, this.URLToStore)
    document.addEventListener('visibilitychange', onVisibilityChange)

    const now = Date.now()
    window.location.href = this.URL

    if (this.isiOS === true) {
      timeout = setTimeout(() => {
        const elapsed = Date.now() - now
        if (!isAppOpened && elapsed < 5000) {
          window.location.href = this.URLToStore
          // isRedirected = true
        }
        document.removeEventListener('visibilitychange', onVisibilityChange)
      }, 4000)
    }
    // window.location.replace(URL)
  }
}
</script>
