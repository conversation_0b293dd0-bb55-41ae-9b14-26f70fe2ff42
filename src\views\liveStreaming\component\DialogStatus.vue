<template>
  <v-dialog v-model="modal" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
    <v-card style="background: #FFFFFF; border-radius: 24px;">
      <div style="background: #FEE7E8">
        <v-col class="d-flex justify-center align-center" style="height: 240px;">
          <v-icon size="178" color="#F44336">mdi-alpha-x-circle</v-icon>
        </v-col>
      </div>
      <v-container>
        <v-card-text style="text-align: center;">
          <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title }}</b></p>
          <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ message }}</span><br />
          <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ message2 }}</span>
        </v-card-text>
        <v-card-text>
          <v-row dense justify="center">
            <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="$emit('close')">ปิด</v-btn>
          </v-row>
        </v-card-text>
      </v-container>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  props: {
    size: {
      type: Number,
      default: 70
    },
    width: {
      type: Number,
      default: 60
    },
    message: {
      type: String,
      default: ''
    },
    message2: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    },
    status: {
      type: String,
      default: 'success'
    },
    live: {
      type: Boolean,
      default: false
    },
    modal: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  }
}
</script>

<style>

</style>
