const state = {
  firstName: 'Thiti',
  lastName: 'Yamsung',
  currentCounter: 0
}

const getters = {
  fullName: (state, getters, rootState) => {
    return state.firstName + ' ' + state.lastName
  }
}

const actions = {
  setName: ({ commit, state }, payload) => {
    commit('SET_FIRST_NAME', payload.firstName)
    commit('SET_LAST_NAME', payload.lastName)
  },
  increaseCounter: ({ commit, state }, payload) => {
    commit('INCREASE_CURRENT_COUNTER')
  }
}

const mutations = {
  SET_FIRST_NAME (state, payload) {
    state.firstName = payload
  },
  SET_LAST_NAME (state, payload) {
    state.lastName = payload
  },
  INCREASE_CURRENT_COUNTER (state, payload) {
    state.currentCounter++
  }
}

export default {
  namespaced: true,
  state,
  getters,
  actions,
  mutations
}
