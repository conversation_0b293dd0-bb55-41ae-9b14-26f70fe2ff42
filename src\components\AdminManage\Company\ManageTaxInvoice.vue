<template>
  <v-container :class="MobileSize ? 'mt-4' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-dialog v-model="deleteDialog" :style="MobileSize ? 'z-index: 16000004' : ''" persistent width="424">
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="deleteDialog = !deleteDialog"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4" v-if="TypeAddress === 'address'"><b>ลบที่อยู่จัดส่ง</b></p>
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4" v-if="TypeAddress === 'invoice'"><b>ลบที่อยู่ใบกำกับภาษี</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
            </v-card-text>
            <v-card-text>
              <v-row v-if="!MobileSize" dense justify="center">
                <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="deleteDialog = !deleteDialog">ยกเลิก</v-btn>
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="deleteAddress()" v-if="TypeAddress === 'address'">ตกลง</v-btn>
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="DeleteInvoice()" v-if="TypeAddress === 'invoice'">ตกลง</v-btn>
              </v-row>
              <v-row v-if="MobileSize" dense justify="center">
                <v-btn height="38" outlined rounded color="#27AB9C" class="mr-4" :style="{ flex: '1' }" @click="deleteDialog = !deleteDialog">ยกเลิก</v-btn>
                <v-btn height="38" class="white--text" rounded color="#27AB9C" :style="{ flex: '1' }" @click="deleteAddress()" v-if="TypeAddress === 'address'">ตกลง</v-btn>
                <v-btn height="38" class="white--text" rounded color="#27AB9C" :style="{ flex: '1' }" @click="DeleteInvoice()" v-if="TypeAddress === 'invoice'">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <v-dialog v-model="ModalSuccessDelete" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
        <v-card style="background: #FFFFFF; border-radius: 24px;">
          <v-img
            height="240px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="CloseModalSuccessDelete()"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
              <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบที่อยู่จัดส่งเสร็จสิ้น</b></p>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลบที่อยู่จัดส่งเรียบร้อย</span>
            </v-card-text>
            <v-card-text>
              <v-row dense justify="center">
                <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="CloseModalSuccessDelete()">ตกลง</v-btn>
              </v-row>
            </v-card-text>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- section ที่อยู่ในการจัดส่งสินค้า -->
      <v-row dense>
        <v-col cols="12" class="d-flex mt-2">
          <span
            style="
              font-weight: bold;
              font-size: 24px;
              line-height: 32px;
              color: #333333;
            "
            class="mr-auto pt-2"
            v-if="!MobileSize && !IpadSize"
            >ที่อยู่ในการจัดส่งสินค้า</span
          >
          <span
            style="
              font-weight: bold;
              font-size: 18px;
              line-height: 32px;
              color: #333333;
            "
            class="mr-auto pt-2"
            v-if="MobileSize"
            ><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon>ที่อยู่ในการจัดส่งสินค้า</span
          >
          <span
            style="
              font-weight: bold;
              font-size: 18px;
              line-height: 32px;
              color: #333333;
            "
            class="mr-auto pt-2"
            v-if="IpadSize"
            >ที่อยู่ในการจัดส่งสินค้า</span
          >
          <v-btn
            v-if="Companydetail.length !== 10"
            color="#27AB9C"
            rounded
            width="126"
            height="40"
            dark
            @click="ManageCompanyAddress('add', '')"
            class="ml-auto"
            ><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มที่อยู่</v-btn
          >
          <span
            v-else
            class="pt-1"
            style="
              font-size: 16px;
              font-weight: 500;
              color: #989898;
              line-height: 32px;
            "
            ><v-icon size="20" color="#989898" class="pr-2"
              >mdi-information-outline</v-icon
            >ไม่สามารถเพิ่มที่อยู่ได้ (เพิ่มได้สูงสุด 10 รายการ)</span
          >
        </v-col>
      </v-row>
      <v-row dense class="mt-9" v-if="Companydetail.length !== 0">
        <v-col
          cols="12"
          class="pt-2"
          v-for="(item, index) in Companydetail"
          :key="index"
        >
          <v-card
            min-height="149"
            elevation="0"
            outlined
            :style="
              item.default === 'Y'
                ? 'border-color: #27AB9C'
                : 'border-color: #C4C4C4'
            "
          >
            <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
            <v-row class="pa-4">
              <v-col cols="12" md="12">
                <v-row dense>
                  <!-- action -->
                  <v-col cols="12" md="12" class="d-flex">
                    <div class="mr-auto">
                      <span style="font-weight: 700; font-size: 18px">{{
                        item.name_th
                      }}</span>
                      <span style="color: #EBEBEB;">|</span> <span style="color: #333333; font-weight: 700; font-size: 18px;">{{ item.tel }}</span>
                      <br />
                      <span
                        style="
                          color: #333333;
                          font-weight: 400;
                          font-size: 18px;
                          line-height: 32px;
                        "
                        >เลขประจำตัวผู้เสียภาษี: </span
                      ><span
                        style="
                          color: #333333;
                          font-weight: 700;
                          font-size: 18px;
                          line-height: 32px;
                        "
                        >{{ item.tax_id }}</span
                      >
                    </div>
                    <div class="ml-auto">
                      <div v-if="!MobileSize">
                        <v-btn
                          color="#27AB9C"
                          height="20"
                          width="56"
                          text
                          elevation="0"
                          @click="ManageCompanyAddress('edit', item)"
                          ><v-icon color="#27AB9C" size="20"
                            >mdi-pencil-outline</v-icon
                          >แก้ไข
                        </v-btn>
                        <v-btn
                          v-if="Companydetail.length !== 1 && index !== 0"
                          height="20"
                          width="41"
                          text
                          :disabled="item.default === 'Y'"
                          color="#9A9A9A"
                          elevation="0"
                          @click="openDeleteDialog(item, 'address')"
                          ><v-icon size="20" color="#9A9A9A"
                            >mdi-delete-outline</v-icon
                          >ลบ
                        </v-btn>
                      </div>
                      <v-row dense v-else>
                        <v-col cols="6">
                          <v-btn
                          color="#27AB9C"
                          height="30"
                          width="30"
                          icon
                          outlined
                          elevation="0"
                          @click="ManageCompanyAddress('edit', item)"
                          ><v-icon color="#27AB9C" size="20"
                            >mdi-pencil-outline</v-icon
                          >
                        </v-btn>
                        </v-col>
                        <v-col cols="6">
                          <v-btn
                          v-if="Companydetail.length !== 1 && index !== 0"
                          height="30"
                          width="30"
                          icon
                          outlined
                          :disabled="item.default === 'Y'"
                          color="#9A9A9A"
                          elevation="0"
                          class="ml-1"
                          @click="openDeleteDialog(item, 'address')"
                          ><v-icon size="20" color="#9A9A9A"
                            >mdi-delete-outline</v-icon
                          >
                        </v-btn>
                        </v-col>
                      </v-row>
                    </div>
                  </v-col>
                  <!-- ที่อยู่ -->
                  <v-col cols="12" md="12">
                    <span
                      v-snip="3"
                      style="color: #333333; font-weight: 400; font-size: 14px"
                      >{{ item.detail }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.zip_code }}</span
                    >
                  </v-col>
                  <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                  <v-col cols="12" md="12">
                    <v-radio-group v-model="item.default">
                      <v-radio
                        color="#27AB9C"
                        label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                        value="Y"
                        :disabled="item.default === 'Y' ? true : false"
                        @click="setDefaultAddressCompany(item)"
                        style="color: #333333"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row justify="center" dense class="pt-6" v-else>
        <v-col cols="12" class="pt-6" align="center">
          <v-img
            :src="
              require('@/assets/ImageINET-Marketplace/ICONProfile/NotAddress.png')
            "
            height="100%"
            width="100%"
            max-height="143"
            max-width="318"
            class="mb-6"
          ></v-img>
          <p
            style="
              font-size: 18px;
              font-weight: 600;
              line-height: 26px;
              color: #636363;
            "
          >
            ยังไม่มีที่อยู่ของคุณ
          </p>
          <span
            style="
              font-size: 16px;
              font-weight: 400;
              line-height: 26px;
              color: #9a9a9a;
            "
            >สามารถกด</span
          ><span
            style="
              font-size: 18px;
              font-weight: 600;
              line-height: 26px;
              color: #1b5dd6;
              text-decoration: underline;
            "
            >เพิ่มที่อยู่</span
          ><span
            style="
              font-size: 16px;
              font-weight: 400;
              line-height: 26px;
              color: #9a9a9a;
            "
            >เพื่อใช้ในการจัดส่งสินค้า</span
          >
        </v-col>
      </v-row>
      <!-- section ที่อยู่ในใบกำกับภาษี -->
      <v-row dense>
        <v-col cols="12" class="d-flex mt-2">
          <span
            style="
              font-weight: bold;
              font-size: 24px;
              line-height: 32px;
              color: #333333;
            "
            class="mr-auto pt-2"
            v-if="!MobileSize && !IpadSize"
            >ที่อยู่ในการออกใบกำกับภาษี</span
          >
          <span
            style="
              font-weight: bold;
              font-size: 18px;
              line-height: 32px;
              color: #333333;
            "
            class="mr-auto pt-2"
            v-if="MobileSize"
            >ที่อยู่ในการออกใบกำกับภาษี</span
          >
          <span
            style="
              font-weight: bold;
              font-size: 18px;
              line-height: 32px;
              color: #333333;
            "
            class="mr-auto pt-2"
            v-if="IpadSize"
            >ที่อยู่ในการออกใบกำกับภาษี</span
          >
          <v-btn
            v-if="userInvoiceAddress.length !== 5"
            color="#27AB9C"
            rounded
            width="126"
            height="40"
            dark
            @click="manageTaxAddress('add', '')"
            class="ml-auto"
            ><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มที่อยู่</v-btn
          >
          <span
            v-else
            class="pt-1"
            style="
              font-size: 16px;
              font-weight: 500;
              color: #989898;
              line-height: 32px;
            "
            ><v-icon size="20" color="#989898" class="pr-2"
              >mdi-information-outline</v-icon
            >ไม่สามารถเพิ่มที่อยู่ได้ (เพิ่มได้สูงสุด 5 รายการ)</span
          >
        </v-col>
      </v-row>
      <v-row dense class="mt-9" v-if="userInvoiceAddress.length !== 0">
        <v-col
          cols="12"
          class="pt-2"
          v-for="(item, index) in userInvoiceAddress"
          :key="index"
        >
          <v-card
            min-height="149"
            elevation="0"
            outlined
            :style="
              item.default_invoice === 'Y'
                ? 'border-color: #27AB9C'
                : 'border-color: #C4C4C4'
            "
          >
            <!-- <v-card elevation="0" outlined style="border-color: #C4C4C4;"> -->
            <v-row class="pa-4">
              <v-col cols="12" md="12">
                <v-row dense>
                  <!-- action -->
                  <v-col cols="12" md="12" class="d-flex">
                    <div class="mr-auto">
                      <span style="font-weight: 700; font-size: 18px">{{
                        item.name
                      }}</span>
                      <span style="color: #ebebeb">|</span>
                      <span
                        style="font-size: 18px; font-weight: 600; color: #27ab9c"
                        >{{
                          item.tax_type === 'Personal'
                            ? 'บุคคลธรรมดา'
                            : 'นิติบุคคล'
                        }}</span
                      ><br />
                      <span
                        style="
                          color: #333333;
                          font-weight: 400;
                          font-size: 18px;
                          line-height: 32px;
                        "
                        >รหัสสาขา: </span
                      ><span
                        style="
                          color: #333333;
                          font-weight: 700;
                          font-size: 18px;
                          line-height: 32px;
                        "
                        >{{ item.branch_id }}</span
                      ><br />
                      <span
                        style="
                          color: #333333;
                          font-weight: 400;
                          font-size: 18px;
                          line-height: 32px;
                        "
                        >เลขประจำตัวผู้เสียภาษี: </span
                      ><span
                        style="
                          color: #333333;
                          font-weight: 700;
                          font-size: 18px;
                          line-height: 32px;
                        "
                        >{{ item.tax_id }}</span
                      >
                    </div>
                    <div class="ml-auto">
                      <div v-if="!MobileSize">
                        <v-btn
                          color="#27AB9C"
                          height="20"
                          width="56"
                          text
                          elevation="0"
                          @click="manageTaxAddress('edit', item)"
                          ><v-icon color="#27AB9C" size="20"
                            >mdi-pencil-outline</v-icon
                          >แก้ไข</v-btn
                        >
                        <v-btn
                          v-if="userInvoiceAddress.length !== 1"
                          height="20"
                          width="41"
                          text
                          :disabled="item.default_invoice === 'Y'"
                          color="#9A9A9A"
                          elevation="0"
                          @click="openDeleteDialog(item, 'invoice')"
                          ><v-icon size="20" color="#9A9A9A"
                            >mdi-delete-outline</v-icon
                          >ลบ</v-btn
                        >
                      </div>
                      <v-row dense v-else>
                        <v-col cols="6">
                          <v-btn
                           color="#27AB9C"
                           height="30"
                           width="30"
                           icon
                           outlined
                           elevation="0"
                           @click="manageTaxAddress('edit', item)"
                          ><v-icon color="#27AB9C" size="20"
                            >mdi-pencil-outline</v-icon
                          ></v-btn
                        >
                        </v-col>
                        <v-col cols="6">
                          <v-btn
                          v-if="userInvoiceAddress.length !== 1"
                          height="30"
                          width="30"
                          icon
                          outlined
                          :disabled="item.default_invoice === 'Y'"
                          color="#9A9A9A"
                          elevation="0"
                          @click="openDeleteDialog(item, 'invoice')"
                          ><v-icon size="20" color="#9A9A9A"
                            >mdi-delete-outline</v-icon
                          ></v-btn
                        >
                        </v-col>
                      </v-row>
                    </div>
                    <!-- <v-row justify="end" class="mt-1 mr-1">
                    <v-btn color="#F2F2F2" fab x-small elevation="0" @click="editAddress(item)"><v-icon color="#A1A1A1">mdi-pencil</v-icon></v-btn>
                    <v-btn :disabled="userdetail.length === 1 || item.default_address === 'Y'" color="#F2F2F2" fab x-small class="ml-2" elevation="0" @click="openDeleteDialog(item)"><v-icon color="#A1A1A1">mdi-delete-outline</v-icon></v-btn>
                  </v-row> -->
                  </v-col>
                  <!-- ชื่อ-นามสกุล -->
                  <!-- <v-col cols="12" md="12" class="pl-5 pb-0">
                  <span style="font-weight: 700; font-size: 16px;">{{ item.first_name }} {{ item.last_name }}</span>
                </v-col> -->
                  <!-- หมายเลขโทรศัพท์ -->
                  <!-- <v-col cols="12" md="12" class="pl-5 pb-0">
                  <span style="color: #333333; font-weight: 400; font-size: 14px;">{{ item.phone }}</span>
                </v-col> -->
                  <!-- ที่อยู่ -->
                  <v-col cols="12" md="12">
                    <span
                      v-snip="3"
                      style="color: #333333; font-weight: 400; font-size: 14px"
                      >{{ item.address }} {{ item.sub_district }}
                      {{ item.district }} {{ item.province }}
                      {{ item.postal_code }}</span
                    >
                  </v-col>
                  <!-- ตั้งค่าที่อยู่เริ่มต้น -->
                  <v-col cols="12" md="12">
                    <v-radio-group v-model="item.default_invoice">
                      <v-radio
                        color="#27AB9C"
                        label="ตั้งค่าเป็นที่อยู่เริ่มต้น"
                        value="Y"
                        :disabled="item.default_invoice === 'Y' ? true : false"
                        @click="setDefaultAddressInvoice(item)"
                        style="color: #333333"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
      <v-row justify="center" dense class="pt-6" v-else>
        <v-col cols="12" class="pt-6" align="center">
          <v-img
            :src="
              require('@/assets/ImageINET-Marketplace/ICONProfile/NotAddress.png')
            "
            height="100%"
            width="100%"
            max-height="143"
            max-width="318"
            class="mb-6"
          ></v-img>
          <p
            style="
              font-size: 18px;
              font-weight: 600;
              line-height: 26px;
              color: #636363;
            "
          >
            ยังไม่มีที่อยู่ของคุณ
          </p>
          <span
            style="
              font-size: 16px;
              font-weight: 400;
              line-height: 26px;
              color: #9a9a9a;
            "
            >สามารถกด</span
          ><span
            style="
              font-size: 18px;
              font-weight: 600;
              line-height: 26px;
              color: #1b5dd6;
              text-decoration: underline;
            "
            >เพิ่มที่อยู่</span
          ><span
            style="
              font-size: 16px;
              font-weight: 400;
              line-height: 26px;
              color: #9a9a9a;
            "
            >เพื่อใช้ในการออกใบกำกับภาษี</span
          >
        </v-col>
      </v-row>
      <ModalCompanyAddress :EditAddressDetail="EditAddressDetail" :title="titleAddress" :page="page"/>
      <ModalTaxAddress
        ref="ModalTaxAddress"
        :title="titleTaxAddress"
        :page="page"
      />
    </v-card>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  components: {
    ModalCompanyAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/AddressCompanyModal.vue'),
    ModalTaxAddress: () => import(/* webpackPrefetch: true */ '@/components/Cart/ModalAddress/TaxInvoiceAddress')
  },
  data () {
    return {
      OldId: '',
      deleteAddressInvoiceData: [],
      TypeAddress: '',
      titleTaxAddress: '',
      ModalSuccessDelete: false,
      deleteDialog: false,
      EditAddressDetail: [],
      AddressTex: '',
      Companydetail: [],
      userInvoiceAddress: [],
      hidden: false,
      userShop: [],
      oneUserType: '',
      BindAccountdialog: false,
      CheckDataTex: false,
      usernameOne: '',
      passwordOne: '',
      telnumber: '',
      lazy: false,
      radioGroup: '',
      titleAddress: '',
      deleteAdressData: '',
      page: '',
      Rules: {
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้',
          v =>
            /^[A-Za-z0-9]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรและตัวเลขเท่านั้น'
        ],
        password: [v => !!v || 'กรุณากรอกรหัสผ่าน']
      }
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageAddressCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageAddressCompany' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  mounted () {
    this.$EventBus.$on('EditAddressCompanyComplete', data => {
      this.getCompanyAddress(data)
    })
    this.$EventBus.$on('getAddressTex', this.getAddressTex)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getAddressTex')
    })
  },
  async created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.getCompanyAddress()
      this.getAddressTex()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('EditAddressComplete')
  },
  methods: {
    manageTaxAddress (key, data) {
      this.page = 'ManageTaxInvoice'
      if (key === 'add') {
        this.titleTaxAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
        this.$refs.ModalTaxAddress.open()
      } else if (key === 'edit') {
        const editData = {
          id: data.id,
          user_id: data.user_id,
          company_id: data.company_id,
          tax_type: data.tax_type,
          tax_id: data.tax_id,
          buyer_one_id: data.buyer_one_id,
          name: data.name,
          email: data.email,
          address: data.address,
          postal_code: data.postal_code,
          province: data.province,
          district: data.district,
          sub_district: data.sub_district,
          role: data.role,
          default_invoice: data.default_invoice,
          branch_id: data.branch_id
        }
        // console.log(editData)
        // console.log('editdata')
        localStorage.setItem('InvoiceAddress', Encode.encode(editData))
        this.titleTaxAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
        this.$refs.ModalTaxAddress.open()
      }
      //   var address = this.userdetailAddress.filter(e => e.default_address === 'default')
      //   this.$refs.ModalTaxAddress.open(key, data, address[0], '', 'userProfile')
    },
    async setDefaultAddressInvoice (item) {
      const data = {
        old_id: this.OldId !== '' ? this.OldId : 0,
        new_id: item.id,
        company_id: item.company_id
      }
      await this.$store.dispatch('actionsSetDefaultInvoice', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultInvoice
      if (res.result === 'SUCCESS') {
        this.$swal.fire({
          icon: 'success',
          title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>',
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
      this.getAddressTex()
    },
    async DeleteInvoice () {
      // console.log('DeleteInvoice', this.deleteAdressData)
      this.$store.commit('openLoader')
      this.deleteDialog = false
      const data = {
        invoice_id: this.deleteAdressData.id,
        company_id: this.deleteAdressData.company_id
      }
      await this.$store.dispatch('actionsDeleteInvoice', data)
      var res = await this.$store.state.ModuleUser.stateDeleteInvoice
      if (res.result === 'SUCCESS') {
        this.$swal.fire({
          icon: 'success',
          title: 'ลบที่อยู่สำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        this.getAddressTex()
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
      }
      this.$store.commit('closeLoader')
    },
    CloseModalSuccessDelete () {
      if (this.TypeAddress === 'address') {
        this.getCompanyAddress()
      } else {
        // this.getAddressTex()
      }
      this.ModalSuccessDelete = false
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    async getCompanyAddress () {
      this.$store.commit('openLoader')
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      var data = {
        company_id: companyData.id
      }
      await this.$store.dispatch('actionsGetAddressCompany', data)
      var Companydetail = await this.$store.state.ModuleAdminManage.stateGetAddressCompany
      if (Companydetail.ok === 'y') {
        this.$store.commit('closeLoader')
        this.Companydetail = await Companydetail.query_result
        // console.log('getCompanyAddress', this.Companydetail)
      } else {
        if (Companydetail.message === 'This user is Unauthorized' || Companydetail.message === 'This user is unauthorized.' || Companydetail.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.Companydetail = []
        }
      }
    },
    openDeleteDialog (item, type) {
      this.deleteDialog = !this.deleteDialog
      this.TypeAddress = type
      this.deleteAdressData = item
    },
    async deleteAddress () {
      this.$store.commit('openLoader')
      this.deleteDialog = false
      const data = {
        id: this.deleteAdressData.id,
        company_id: this.deleteAdressData.company_id
      }
      await this.$store.dispatch('actionsDeleteAddressCompany', data)
      const res = await this.$store.state.ModuleAdminManage.stateDeleteAddressCompany
      if (res.ok === 'y') {
        // this.$swal.fire({ icon: 'success', title: 'ลบที่อยู่ในการจัดส่งสินค้าสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.$store.commit('closeLoader')
        this.ModalSuccessDelete = true
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            title: 'ลบที่อยู่ในการจัดส่งสินค้าไม่สำเร็จ',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    ManageCompanyAddress (actions, data) {
      this.$store.commit('openLoader')
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      // console.log('companyData', companyData)
      if (actions === 'add') {
        const val = {
          company_id: companyData.id,
          building_name: '',
          default_address: '',
          detail: '',
          district: '',
          name_th: companyData.name_th,
          floor: '',
          house_no: '',
          last_name: '',
          moo_ban: '',
          moo_no: '',
          order_number: '',
          phone: '',
          tel: '',
          province: '',
          room_no: '',
          soi: '',
          status: '',
          street: '',
          id: companyData.company_address_id,
          sub_district: '',
          yaek: '',
          zipcode: ''
        }
        localStorage.setItem('AddressCompanyDetail', Encode.encode(val))
        this.EditAddressDetail = val
        this.titleAddress = 'เพิ่มที่อยู่ในการจัดส่งสินค้า'
        this.page = 'CompanyDetail'
        this.$EventBus.$emit('EditModalCompanyAddress')
      } else {
        const editData = {
          building_name: data.building_name,
          // default_address: data.default_address,
          detail: data.detail,
          district: data.district,
          // email: data.email,
          name_th: data.name_th,
          floor: data.floor,
          house_no: data.house_no,
          id: data.id,
          tel: data.tel,
          company_id: data.company_id,
          // last_name: data.last_name,
          moo_ban: data.moo_ban,
          moo_no: data.moo_no,
          // order_number: data.order_number,
          phone: data.phone,
          province: data.province,
          room_no: data.room_no,
          soi: data.soi,
          status: data.status,
          street: data.street,
          sub_district: data.sub_district,
          // user_id: data.user_id,
          yaek: data.yaek,
          zipcode: data.zip_code
        }
        // console.log('', editData)
        localStorage.setItem('AddressCompanyDetail', Encode.encode(editData))
        this.EditAddressDetail = editData
        this.titleAddress = 'แก้ไขที่อยู่ในการจัดส่งสินค้า'
        this.page = 'CompanyDetail'
        this.$EventBus.$emit('EditModalCompanyAddress')
      }
    },
    async setDefaultAdress (item) {
      const data = {
        id: item.id,
        default_address: item.default_address
      }
      await this.$store.dispatch('actionDefaultUserAddress', data)
      var res = await this.$store.state.ModuleUser.stateSetDefaultUserAddress
      if (res.message === 'Update default address success') {
        this.$swal.fire({
          icon: 'success',
          title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>',
          showConfirmButton: false,
          timer: 1500
        })
        this.userdetail = [...res.data]
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
      // this.getAddress()
    },
    async getAddressTex () {
      this.$store.commit('openLoader')
      this.userInvoiceAddress = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(
          Decode.decode(localStorage.getItem('SetRowCompany'))
        )
      }
      const data = {
        user_id: onedata.user.user_id,
        // user_id: 16,
        company_id: companyId.company.company_id
      }
      await this.$store.dispatch('actionsGetAllInvoiceAddress', data)
      var res = await this.$store.state.ModuleUser.stateGetAllInvoiceAddress
      if (res.message === 'Success') {
        this.userInvoiceAddress = res.data
        this.userInvoiceAddress.forEach(element => {
          if (element.default_invoice === 'Y') {
            this.OldId = element.id
          }
        })
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
      }
      this.$store.commit('closeLoader')
    },
    editAddressTex (val) {
      localStorage.setItem('AddressTexUserDetail', Encode.encode(val))
      this.EditAddressDetail = val
      this.$EventBus.$emit('EditModalAddressTex')
    },
    async setDefaultAddressCompany (dataAddress) {
      var data = {
        id: dataAddress.id,
        company_id: dataAddress.company_id
      }
      await this.$store.dispatch('actionsSetDefaultAddressCompany', data)
      const resp = await this.$store.state.ModuleAdminManage.stateSetDefaultAddressCompany
      if (resp.ok === 'y') {
        this.$swal.fire({
          icon: 'success',
          title: '<h5>ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว</h5>',
          showConfirmButton: false,
          timer: 1500
        })
        await this.getCompanyAddress()
      } else {
        if (resp.message === 'This user is Unauthorized' || resp.message === 'This user is unauthorized.' || resp.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            title: '<h5>ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ</5>',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    }
  }
}
</script>
