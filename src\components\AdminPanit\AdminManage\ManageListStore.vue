<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
  <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
    <v-row v-if="!MobileSize && !IpadSize">
      <v-col cols="8">
        <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;">จัดการลำดับร้านค้า</v-card-title>
      </v-col>
      <v-col cols="4" style="display: flex; justify-content: end; align-items: center; gap: 5%; padding: 0 5%;">
        <v-btn @click="saveIndex('random')" color="primary"><v-icon color="white">mdi-dice-multiple</v-icon><span style="margin-left: 10%;">Random</span></v-btn>
        <v-btn color="primary" text @click="cancel" :disabled="this.btnCancel">cancel</v-btn>
        <v-btn color="primary" @click="openConfirm = !openConfirm" :disabled="this.btnSave">save</v-btn>
      </v-col>
    </v-row>
    <v-row v-else>
      <v-col cols="12">
        <v-card-title style="font-weight: 700; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการลำดับร้านค้า</v-card-title>
      </v-col>
      <v-col width="100%" style="display: flex; justify-content: space-between; margin-top: -10px; padding: 0 5%;" class="mb-1 ml-1 mr-1">
        <div>
          <v-btn @click="saveIndex('random')" small color="primary"><v-icon color="white">mdi-dice-multiple</v-icon><span style="margin-left: 10%;">Random</span></v-btn>
        </div>
        <div>
          <v-btn color="primary" small text @click="cancel" :disabled="this.btnCancel">cancel</v-btn>
          <v-btn color="primary" small @click="openConfirm = !openConfirm" :disabled="this.btnSave">save</v-btn>
        </div>
      </v-col>
    </v-row>
    <v-row>
      <v-col width="100%">
        <v-card width="100%" outlined style="border: none;">
          <draggable
            :list="paginatedList"
            :disabled="!enabled"
            class="list-group flex-container"
            ghost-class="ghost"
            :move="checkMove"
            @start="dragging = true"
            @end="onDragEnd"
            tag="div"
          >
            <div v-for="(element,index) in paginatedList" :key="index" class="flex-item">
              <v-card class="list-group-item" width="104">
                <v-col class="mt-1" style="height: 80px; display: flex; justify-content: center; align-items: center;">
                  <v-avatar tile size="70" v-if="element.path_logo !== null && element.path_logo !== ''"><img
                    :src="element.path_logo"
                    style="width: 100%; height: 100%; object-fit: contain;"
                  ></v-avatar>
                  <v-avatar tile size="70" v-if="element.path_logo === null || element.path_logo === ''"><img
                    src="@/assets/NoImage.png"
                    style="width: 100%; height: 100%; object-fit: contain;"
                  ></v-avatar>
                </v-col>
                  <v-btn center icon x-small style="cursor: pointer; position: absolute; top: 2px; right: 2px; border-radius: 50%; background-color: #27ab9c; border: 1px solid #F0F0F0;" @click="openDialog(index)"><v-icon color="white" x-small>mdi-pencil</v-icon></v-btn>
                <v-col class="text-center" style="height: 25px; width: 100px; margin-bottom: 10px;">
                  <p class="truncate" style="font-size: 70%; text-align: center;">
                    <v-tooltip top>
                      <template v-slot:activator="{ attrs }">
                        <span style="font-size: 12px; width: 100px; cursor: pointer;" v-bind="attrs">{{ element.shop_name }}</span>
                      </template>
                      <span style="font-size: 12px; width: 100px;">{{ element.shop_name }}</span>
                    </v-tooltip>
                  </p>
                </v-col>
              </v-card>
            </div>
          </draggable>
        </v-card>
        <v-pagination
            v-model="page"
            :length="pageCount"
            class="pagination"
            :total-visible="7"
            @input="onPageChange"
          ></v-pagination>
      </v-col>
    </v-row>
    <v-dialog v-model="dialog" :width="MobileSize ? '100%' : '50%'" persistent>
      <v-card>
        <v-col cols="12" style="display: flex; justify-content: space-between; align-items: center;">
          <v-card-title class="headline" style="font-size: 22px !important;">แก้ไขลำดับร้านค้า</v-card-title>
          <v-btn text @click="dialog = false" small><v-icon>mdi-close</v-icon></v-btn>
        </v-col>
          <v-container>
            <v-row>
              <v-col cols="12" class="mt-2">
                <v-text-field
                  v-model="data"
                  label="ลำดับ"
                  type="number"
                  outlined
                  @keyup.enter="saveIndex('save')"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-container>
        <v-row>
          <v-col cols="12">
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="primary" style="display: flex; justify-content: end;"
              text @click="saveIndex('save')">บันทึก</v-btn>
            </v-card-actions>
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
    <v-dialog v-model='openConfirm' :width="MobileSize ? '60%' : '30%'" persistent>
    <v-card min-height='100%'>
        <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
        <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
        >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='openConfirm = !openConfirm' icon
            ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
            >
        </v-app-bar>
        </v-img>
        <v-container>
        <v-card-text style="text-align: center;">
            คุณต้องการทำรายการนี้ ใช่หรือไม่
        </v-card-text>
        <v-card-actions>
        <v-row dense class='d-flex justify-space-between' style="gap: 20px;">
            <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="openConfirm = !openConfirm">ยกเลิก</v-btn>
            <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="saveData()">ตกลง</v-btn>
        </v-row>
        </v-card-actions>
        </v-container>
    </v-card>
    </v-dialog>
    <v-dialog v-model='openSuccess' :width="MobileSize ? '60%' : '30%'" persistent>
    <v-card min-height='100%'>
        <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
        <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
        >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='openSuccess = !openSuccess' icon
            ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
            >
        </v-app-bar>
        </v-img>
        <v-container>
        <v-card-text style="text-align: center;">
            คุณได้ทำการแก้ไขรายการนี้เรียบร้อย
        </v-card-text>
        <v-card-actions>
        <v-row dense class='d-flex justify-center' style="padding: 0 25%;">
            <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openSuccess = !openSuccess">ตกลง</v-btn>
        </v-row>
        </v-card-actions>
        </v-container>
    </v-card>
    </v-dialog>
  </v-card>
  </v-container>
</template>

<script>
import draggable from 'vuedraggable'
import { Decode } from '@/services'
export default {
  components: {
    draggable
  },
  data () {
    return {
      list: [],
      enabled: true,
      dragging: false,
      page: 1,
      perPage: 36,
      dialog: false,
      openConfirm: false,
      openSuccess: false,
      originalIndex: 0,
      startPage: 0,
      data: 0,
      btnSave: true,
      btnCancel: true
    }
  },
  created () {
    this.fetchData()
    this.adjustPerPage()
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageListStoreMobile' }).catch(() => {})
        this.perPage = 12
      } else {
        await this.adjustPerPage()
        await localStorage.setItem('pathAdmin', 'manageListStore')
        this.$router.push({ path: '/manageListStore' }).catch(() => {})
      }
    },
    async IpadSize (val) {
      if (val === true) {
        this.perPage = 18
      } else {
        await this.adjustPerPage()
      }
    },
    async IpadProSize (val) {
      if (val === true) {
        this.perPage = 18
      } else {
        await this.adjustPerPage()
      }
    }
  },
  computed: {
    pageCount () {
      return Math.ceil(this.list.length / this.perPage)
    },
    paginatedList () {
      // console.log('paginatedList')
      const start = (this.page - 1) * this.perPage
      const end = start + this.perPage
      return this.list.slice(start, end)
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    adjustPerPage () {
      if (this.MobileSize) {
        this.perPage = 12
      } else if (this.IpadSize || this.IpadProSize) {
        this.perPage = 18
      } else {
        this.perPage = 36
      }
    },
    checkMove: function (e) {
      this.startPage = (this.page - 1) * this.perPage
      const Index = this.startPage + 1 + e.draggedContext.futureIndex
      this.list.index = Index
    },
    onDragEnd (event) {
      this.dragging = false
      const indexPage = 1 + ((this.page - 1) * this.perPage)
      const newIndex = event.newIndex + indexPage
      const oldIndex = event.oldIndex + indexPage
      // console.log({ oldIndex })
      // console.log({ newIndex })
      if (newIndex !== undefined && oldIndex !== undefined) {
        // console.log('this.list[event.oldIndex]', this.list[oldIndex - 1])
        const itemToMove = this.list[oldIndex - 1]
        this.list.splice(oldIndex - 1, 1)
        this.list.splice(newIndex - 1, 0, itemToMove)
        // console.log('list', this.list)
      }
      this.list.forEach((item, index) => {
        item.index = index + 1
      })
      this.btnSave = false
      this.btnCancel = false
    },
    onPageChange (page) {
      this.page = page
      window.scrollTo(0, 0)
      // console.log(this.data)
    },
    async cancel () {
      this.$store.commit('openLoader')
      await this.fetchData()
      this.btnSave = true
      this.btnCancel = true
      this.$store.commit('closeLoader')
    },
    openDialog (index) {
      this.originalIndex = index + 1 + ((this.page - 1) * this.perPage)
      this.data = this.originalIndex
      // console.log(this.data)
      // console.log('originalIndex', this.originalIndex)
      this.dialog = true
    },
    saveIndex (status) {
      if (status === 'save') {
        const newIndex = parseInt(this.data) - 1
        const oldIndex = this.originalIndex - 1
        const itemToMove = this.list[oldIndex]
        this.list.splice(oldIndex, 1)
        this.list.splice(newIndex, 0, itemToMove)
        this.list.forEach((item, index) => {
          item.index = index + 1
        })
        this.dialog = false
        this.btnSave = false
        this.btnCancel = false
      }
      if (status === 'random') {
        const availableNumbers = Array.from({ length: this.list.length }, (_, i) => i + 1)
        const shuffledNumbers = availableNumbers.sort(() => Math.random() - 0.5)
        const newList = []
        shuffledNumbers.forEach((newIndex, index) => {
          const item = this.list.find((_, i) => i + 1 === newIndex)
          newList.push(item)
        })
        this.list = newList
        this.list.forEach((item, index) => {
          item.index = index + 1
        })
        this.btnSave = false
        this.btnCancel = false
      }
    },
    async fetchData () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}admin/seller/shop_list_index`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then(response => {
        this.list = response.data.data
      })
        .catch(error => {
          console.error('There was an error fetching the data: ', error)
        })
    },
    async saveData () {
      this.$store.commit('openLoader')
      const shopIndex = this.list.map((item) => { return { id: item.id, index: item.index } })
      const dataEdit = {
        shop_index: shopIndex
      }
      // console.log('Data_Edit', dataEdit)
      try {
        const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}admin/seller/shop_edit_index`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          data: dataEdit
        })
      } catch (error) {
        console.error('There was an error saveing the data: ', error)
      }
      this.$store.commit('closeLoader')
      this.openConfirm = false
      this.openSuccess = true
      this.btnSave = true
      this.btnCancel = true
    }
  }
}

</script>
<style scoped>
.flex-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
}
.flex-item {
  display: flex;
  margin: 5px;

}
.pagination {
  margin-top: 20px;
}
.ghost {
  opacity: 0.5;
  background: #c8ebfb;
}
.truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
