<template>
<div>
  <popover  :width="screen" trigger="hover" placement="bottom">
    <span class="subtitle-2" @mouseover="DefaultSelect()">{{props.main_category}}</span>
    <div slot="content" class="card_propover">
      <a-row type="flex">
        <a-col :span='6' class="card_keycategory">
          <div class="list_keycategory"  v-for="(item,index) in KeySubCategory" :key="index"  @mouseover="changeKey(index)">
            <span>{{item}}<br></span>
          </div>
        </a-col>
        <a-col :span='14' class="card_category">
          <div v-for="(item,index) in SubCategory" :key="index" class="list_keycategory" @click="PageWithCategory(item)">
            <span >{{item}}<br></span>
          </div>
        </a-col>
      </a-row>
    </div>
  </popover>
</div>
</template>

<script>
import 'vue-blu/src/scss/elements/popover.scss'
export default {
  props: ['props', 'IndexSelect'],
  data () {
    return {
      visible: false,
      KeySubCategory: [],
      SubCategory: [],
      SetSubCategory: [],
      screen: 1430
    }
  },
  methods: {
    changeKey (val) {
      this.SubCategory = this.SetSubCategory[val]
    },
    DefaultSelect () {
      this.screen = window.screen.width - 10
      this.SetSubCategory = Object.values((Object.values(this.props.sub_category))[0])
      this.KeySubCategory = Object.keys((Object.values(this.props.sub_category))[0])
      this.SubCategory = this.SetSubCategory[0]
    },
    PageWithCategory (val) {
      // console.log('PageWithCategory', val)
    }
  }
}
</script>
<style scoped>
.list_keycategory {
  cursor: pointer;
  padding-top: 10px;
  overflow: auto
}
.card_propover {
  height: 500px;
}
.card_category {
  border-left: 1px solid gray;
  padding-left:20px;
  overflow: auto;
  height:500px
}
.card_keycategory {
  overflow: auto;
  height:500px
}
</style>
