<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายงาน e-Tax ของระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายงาน e-Tax ของระบบ</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าของระบบ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="adminShopListeTax.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อร้านค้าของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="adminShopListeTax.length !== 0 && (MobileSize || IpadSize)">รายชื่อร้านค้าของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="adminShopListeTax"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อร้านค้าของระบบ"
                no-data-text="ไม่มีชื่อร้านค้าของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.errorMessage`]="{ item }">
                    <v-btn v-if="item.status === 'ER'" color="#27AB9C" outlined @click="OpenDialogShowData('error', item.errorMessage)">ดู Error Message</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.inputCSV`]="{ item }">
                    <v-btn color="#27AB9C" @click="OpenDialogShowData('csv', item.inputCSV)" class="white--text">ดูข้อมูล CSV</v-btn>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
        <v-card>
            <v-card-title class="text-h5 grey lighten-2">
              {{ dataHeader }}
            </v-card-title>

            <v-card-text class="pt-4">
              <pre v-html="dataToShow"></pre>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                text
                @click="modalShowData = false"
              >
                ปิด
              </v-btn>
            </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
export default {
  data () {
    return {
      search: '',
      modalShowData: false,
      adminShopListeTax: [],
      dataToShow: '',
      dataHeader: '',
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      headers: [
        { text: 'buyer_ID', value: 'buyer_ID', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'buyer_taxID', value: 'buyer_taxID', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'seller_shop_ID', value: 'seller_shop_ID', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'seller_shop_taxID', value: 'seller_shop_taxID', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'payment_transaction_ID', value: 'payment_transaction_ID', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'status', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'errorMessage', value: 'errorMessage', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'transactionCode', value: 'transactionCode', width: '250', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'inputCSV', value: 'inputCSV', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/eTaxAdminMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'eTaxAdmin')
        this.$router.push({ path: '/eTaxAdmin' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getShopDataeTax()
    //   this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  methods: {
    OpenDialogShowData (type, data) {
      this.dataToShow = ''
      this.dataHeader = ''
      if (type === 'error') {
        this.dataHeader = 'Error Message'
        this.dataToShow = data
      } else {
        this.dataHeader = 'CSV Data'
        this.dataToShow = data
      }
      this.modalShowData = true
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getShopDataeTax () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionseTaxAdmin')
      var response = await this.$store.state.ModuleAdminManage.stateeTaxAdmin
      // console.log('list etax ====>', response)
      if (response.message === 'Data Report Etax') {
        this.$store.commit('closeLoader')
        this.adminShopListeTax = [...response.data]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    }
  }
}
</script>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
