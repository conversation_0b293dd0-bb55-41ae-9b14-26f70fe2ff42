import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('ONEDATA NAAAa', oneData)
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async GetDataTable (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/buyerAffiliate`, val, auth)
      return response.data
    } catch (e) {
      // console.log('cant fetch', e)
    }
  },
  async GetTopTen (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/buyerTopClicked`, val, auth)
      return response.data
    } catch (e) {
      // console.log('cant fetch', e)
    }
  },
  async GetReportClick (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/buyer/clickReport`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetShopList (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/shopAffiliated`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetGraph (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/summaryBuyerGraph`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetOrderReport (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/buyerReport`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetTopSold (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/buyerTopSolds`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCommissionTable (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate_dashboard/buyerCommission`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
