<template>
  <v-container class="pa-4">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <div class="px-2">
        <v-row>
          <v-col cols="12" md="6" class="pt-6">
            <span v-if="MobileSize" class="f-left" style="color: #333333; font-weight: 700; font-size: 24px;">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายละเอียดสินค้าบริการของ Partner
            </span>
            <span v-else class="f-left" style="color: #333333; font-weight: 700; font-size: 24px;">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายละเอียดสินค้าบริการของ Partner
            </span>
          </v-col>

          <v-col cols="12" md="6" class="pt-6" style="text-align: end;">
            <span v-if="showpartnerdetail && showpartnerdetail.status === 'approve'">
              <v-chip class="ma-2" color="#E8F5E9" text-color="#388E3C"><b>อนุมัติแล้ว</b></v-chip>
            </span>
            <span v-if="showpartnerdetail && showpartnerdetail.status === 'pending'">
              <v-chip class="ma-2" color="#FFFDE7" text-color="#FBC02D"><b>รออนุมัติ</b></v-chip>
            </span>
            <span v-if="showpartnerdetail && showpartnerdetail.status === 'reject'">
              <v-chip class="ma-2" color="#E1F5FE" text-color="#0288D1"><b>รอแก้ไขข้อมูล</b></v-chip>
            </span>
          </v-col>
        </v-row>
        <br>
        <v-card class="elevation-0" style="background-color: #f5f5f5;">
          <v-row>
            <v-col cols="12" md="6" sm="6" style="padding-left: 20px;">
              <v-icon>mdi-calendar-range</v-icon>
              <span v-if="showpartnerdetail && showpartnerdetail.created_date"> วันที่สร้างบริการ : {{ new Date(showpartnerdetail && showpartnerdetail.created_date).toLocaleDateString('th-TH', {
                  timeZone: "UTC",
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) }}
              </span>
              <span v-else>วันที่สร้างบริการ : -</span>
            </v-col>
            <v-col cols="12" md="6" sm="6" style="padding-left: 20px;">
              <v-icon>mdi-calendar-range</v-icon>
              <span v-if="showpartnerdetail && showpartnerdetail.approved_at !== '-'">
                วันที่อนุมัติ : {{ new Date(showpartnerdetail && showpartnerdetail.approved_at).toLocaleDateString('th-TH', {
                  timeZone: "UTC",
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                }) }}
              </span>
              <span v-else> วันที่อนุมัติ : -</span>
            </v-col>
          </v-row>
        </v-card>
        <br>
        <v-tabs>
          <v-tab :class="{ 'font-weight-bold': activeTab === 'shopDetail' }" @click="activeTab = 'shopDetail'">
            ข้อมูลร้านค้า
          </v-tab>
          <v-tab :class="{ 'font-weight-bold': activeTab === 'serviceDetail' }" @click="activeTab = 'serviceDetail'">
            ข้อมูลสินค้าบริการ
          </v-tab>
        </v-tabs>
        <br />
        <v-row v-if="activeTab === 'shopDetail'">
          <!-- First Card for Partner Images -->
          <v-col cols="12">
            <v-card class="custom-card elevation-0">
              <div class="card-header">
                รูปภาพร้านค้า Partner
              </div>
              <v-card-text>
                <v-row>
                  <v-col cols="12" md="3">
                    <v-card class="text-center elevation-2">
                      <v-img
                        v-if="showpartnerdetail && showpartnerdetail.cover_image && showpartnerdetail.cover_image.find(image => image.image_type === 'main')"
                        :src="showpartnerdetail.cover_image.find(image => image.image_type === 'main').media_path"
                        class="custom-image"
                        height="250px"
                        width="100%"
                        contain
                      ></v-img>
                      <v-img
                        v-else
                        src="@/assets/NoImage.png"
                        class="custom-image"
                        height="250px"
                        width="100%"
                        contain
                      ></v-img>
                    </v-card>
                    <br>
                    <v-row>
                      <v-col>
                        <span style="display: flex; justify-content: center; padding: 14px;"><b>โปรไฟล์ Partner</b></span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="9">
                    <v-card class="elevation-2">
                      <v-img
                        v-if="showpartnerdetail && showpartnerdetail.cover_image && showpartnerdetail.cover_image.find(image => image.image_type === 'banner')"
                        :src="showpartnerdetail.cover_image.find(image => image.image_type === 'banner').media_path"
                        class="custom-image"
                        height="250px"
                        width="100%"
                        contain
                      >
                        <v-card-title class="white--text text-center">
                          หน้าปกร้านค้า Partner
                        </v-card-title>
                      </v-img>
                      <v-img
                        v-else
                        src="@/assets/ImageINET-Marketplace/Banner/BannerSoftwareMarketplace.png"
                        class="custom-image"
                        height="250px"
                        width="100%"
                        contain
                      ></v-img>
                    </v-card>
                    <br>
                    <v-row>
                      <v-col
                        v-for="(image, index) in (Array.isArray(showpartnerdetail && showpartnerdetail.cover_image) ? showpartnerdetail && showpartnerdetail.cover_image.filter(image => image.image_type === 'banner') : [])"
                        :key="index"
                        cols="4"
                        sm="2"
                      >
                        <v-card class="elevation-1">
                          <v-img
                            :src="image.media_path || '@/assets/NoImage.png'"
                            height="50px"
                            width="100%"
                            contain
                          ></v-img>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12">
            <v-card class="custom-card elevation-0">
              <div class="card-header">
                ข้อมูล Partner
              </div>
              <v-card-text>
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 30px;"><b>{{ showpartnerdetail && showpartnerdetail.shop_name || '-' }}</b></span>
                  </v-col>
                  <v-col cols="12">
                    <div style="display: flex; overflow-x: auto; overflow-y: hidden; white-space: nowrap; border-radius: 5px; max-width: 100%;">
                      <span style="position: sticky; left: 0; background-color: white; flex-shrink: 0; z-index: 1; display: flex; align-items: center;">
                        ประเภทบริการ :
                      </span>
                      <v-chip-group class="d-inline-flex" style="display: flex; flex-wrap: nowrap; min-width: fit-content;">
                        <v-chip
                          v-if="showpartnerdetail.service_type && JSON.parse(showpartnerdetail.service_type).includes('ERP')"
                          label
                          class="ml-2"
                          style="background: linear-gradient(45deg, #FAB490, #EC5A53); color: #fff;"
                        >
                          <b>ERP</b>
                        </v-chip>
                        <v-chip
                          v-if="showpartnerdetail.service_type && JSON.parse(showpartnerdetail.service_type).includes('Web Development')"
                          label
                          class="ml-2"
                          style="background: linear-gradient(45deg, #C89CF3, #7A6AE2); color: #fff;"
                        >
                          <b>Web Development</b>
                        </v-chip>
                        <v-chip
                          v-if="showpartnerdetail.service_type && JSON.parse(showpartnerdetail.service_type).includes('POS')"
                          label
                          class="ml-2"
                          style="background: linear-gradient(45deg, #FB5D9F, #9020A3); color: #fff;"
                        >
                          <b>POS</b>
                        </v-chip>
                        <v-chip
                          v-if="showpartnerdetail.service_type && JSON.parse(showpartnerdetail.service_type).includes('OMS')"
                          label
                          class="ml-2"
                          style="background: linear-gradient(45deg, #ABDCFD, #0998FF); color: #fff;"
                        >
                          <b>OMS</b>
                        </v-chip>
                        <v-chip
                          v-if="showpartnerdetail.service_type && JSON.parse(showpartnerdetail.service_type).includes('Marketing')"
                          label
                          class="ml-2"
                          style="background: linear-gradient(45deg, #ACF39C, #45C16E); color: #fff;"
                        >
                          <b>Marketing</b>
                        </v-chip>
                      </v-chip-group>
                    </div>
                    <span>เลขประจำตัวผู้เสียภาษีอากร : {{ showpartnerdetail && showpartnerdetail.card_number || '-' }}</span><br>
                    <span>URL ร้านค้า : {{ showpartnerdetail && showpartnerdetail.url_shop || '-' }}</span>
                  </v-col>
                  <br>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/Capa_1.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>เกี่ยวกับ Partner</b></span>
                  </v-col>
                  <v-col cols="12">
                      <span>{{ showpartnerdetail && showpartnerdetail.detail }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/Layer_1_3.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>ข้อมูลติดต่อ</b></span>
                  </v-col>
                  <v-col cols="12">
                      <span>เบอร์โทรศัพท์ : {{ showpartnerdetail && showpartnerdetail.phone_no || '-' }}</span><br>
                      <span>Line : {{ showpartnerdetail && showpartnerdetail.line || '-' }}</span><br>
                      <span>Facebook : {{ showpartnerdetail && showpartnerdetail.facebook || '-' }}</span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12">
            <v-card class="custom-card elevation-0">
              <div class="card-header">
                ข้อมูลบัญชี
              </div>
              <v-card-text>
                <v-row>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/Layer_1.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>ข้อมูลบัญชี</b></span>
                  </v-col>
                  <v-col cols="12" md="4">
                    <span>
                      <b>ประเภทการชำระเงิน :</b>
                      {{ showaccountdetail[0] && showaccountdetail[0].service_pay === 'savings' ? 'บัญชีออมทรัพย์' : showaccountdetail[0] && showaccountdetail[0].service_pay === 'current' ? 'บัญชีฝากประจำ' : '-' }}
                    </span>
                  </v-col>
                  <v-col cols="12" md="4">
                    <span><b>ชื่อบัญชี :</b> {{ showaccountdetail[0] && showaccountdetail[0].account_name || '-' }}</span>
                  </v-col>
                  <v-col cols="12" md="4">
                    <span><b>ชื่อธนาคาร :</b> {{ showaccountdetail[0] && showaccountdetail[0].bank_name || '-' }}</span>
                  </v-col>

                  <v-col cols="12" md="4">
                    <span><b>ชื่อสาขาธนาคาร :</b> {{ showaccountdetail[0] && showaccountdetail[0].branch_name || '-' }}</span>
                  </v-col>
                  <v-col cols="12" md="8">
                    <span><b>หมายเลขบัญชีธนาคาร :</b> {{ showaccountdetail[0] && showaccountdetail[0].account_number || '-' }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span><b>รูปหน้าบัญชีธนาคาร :</b></span>
                    <br>
                    <img
                      :src="showaccountdetail[0] && showaccountdetail[0].bank_picture"
                      class="responsive-image mt-2"
                      contain
                    />
                  </v-col>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/Layer_1_2.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>ข้อมูลภาษี</b></span>
                  </v-col>
                  <v-col cols="12">
                    <span><b>หมายเลขประจำตัวผู้เสียภาษี :</b> {{ showaccountdetail[0] && showaccountdetail[0].id_number || '-' }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/image_3.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>ที่อยู่นิติบุคคล</b></span>
                  </v-col>
                  <v-col cols="12">
                    <!-- <span><b>ประเภทที่อยู่ :</b> {{ showaccountdetail[0] && showaccountdetail[0].address_type || '-' }}</span> -->
                    <span><b>ประเภทที่อยู่ :</b> {{ showaccountdetail[0] && showaccountdetail[0] ? (showaccountdetail[0].address_type === 'business' ? 'ที่อยู่ตามนิติบุคคล' : 'ที่อยู่ใหม่') : '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>ที่อยู่ :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].house_no || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>ห้องเลขที่ :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].room_no || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>ชั้นที่ :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].floor || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>อาคาร :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].building_name || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>หมู่บ้าน :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].moo_ban || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>หมู่ที่ :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].moo_no || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>ตรอก/ซอย :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].soi || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>แยก :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].yaek || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>ถนน :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].street || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>ตำบล/แขวง :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].tambon || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>อำเภอ/เขต :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].amphoe || '-' }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 12" md="4">
                    <span><b>จังหวัด :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].province || '-' }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span><b>รหัสไปรษณีย์ :</b> {{ showaccountdetail[0] && showaccountdetail[0].address[0].zipcode || '-' }}</span>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>

        <v-row v-if="activeTab === 'serviceDetail'">
          <v-col cols="12">
            <v-card class="custom-card elevation-0">
              <!-- Header -->
              <div class="card-header">
                ข้อมูลสินค้าบริการ
              </div>
              <!-- Content -->
              <v-card-text>
                <v-row>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/Capa_1_2.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>ข้อมูลสินค้า</b></span>
                  </v-col>
                  <v-col cols="12">
                    <span><b>ชื่อสินค้า : </b><span style="color: black">{{ showpackages.product_name }}</span></span><br>
                    <span><b>รายละเอียดสินค้า/คำอธิบายเพิ่มเติม :</b></span><br>
                    <!-- <div v-if="detailPartner !== null" class="showTable ck-content mt-2" v-html="detailPartner"></div> -->
                    <span style="color: black" v-html="showpackages.product_detail"></span>
                  </v-col>
                  <v-col cols="12" class="d-flex align-center">
                    <img
                      src="@/assets/Marketplace_partner/Capa_1_3.png"
                      alt="เกี่ยวกับ Partner"
                      width="25px"
                      class="mr-2"
                    />
                    <span><b>รายการสินค้า</b></span>
                  </v-col>
                  <v-col>
                    <v-card v-for="(packages, index) in showpackages.product_list" :key="index" class="elevation-0 mb-10" style="background-color: #F3F5F7;">
                      <v-row style="padding-left: 10px; padding-right: 10px;">
                        <v-col v-if="packages.status === 'pending'" cols="12" style="text-align: end;">
                          <v-btn rounded @click="openDialogApprove(index)" color="#27AB9C" class="ma-2" style="width: 100px;" dark>อนุมัติ</v-btn>
                          <v-btn rounded @click="openDialogReject(index)" class="ma-2" outlined color="red" style="border-color: red; color: red; width: 100px;">ไม่อนุมัติ</v-btn>
                        </v-col>
                        <v-col cols="12" md="6" v-if="packages.status === 'reject'">
                          <span style="color: #27AB9C"><b>รายการสินค้าที่ {{ Number(index) + 1 }}</b></span>
                          <span v-if="packages.status === 'approve'">
                            <v-chip class="ma-2" color="#E8F5E9" text-color="#388E3C"><b>อนุมัติแล้ว</b></v-chip>
                          </span>
                          <span v-if="packages.status === 'pending'">
                            <v-chip class="ma-2" color="#FFFDE7" text-color="#FBC02D"><b>รออนุมัติ</b></v-chip>
                          </span>
                          <span v-if="packages.status === 'reject'">
                            <v-chip class="ma-2" color="#E1F5FE" text-color="#0288D1"><b>รอแก้ไขข้อมูล</b></v-chip>
                          </span>
                          <span v-if="packages.status === 'cancel'">
                            <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D"><b>ยกเลิก</b></v-chip>
                          </span>
                        </v-col>
                        <v-col cols="12" md="6" class="d-flex align-center" v-if="packages.status === 'reject'">
                          <div style="position: relative; display: inline-block;">
                            <span class="d-flex flex-column">
                              <span>
                                <v-icon>mdi-pencil</v-icon>การส่งแก้ไขข้อมูล :
                                <span
                                  style="cursor: pointer; color: blue; text-decoration: underline;"
                                  @click="openDialogRejectReason(index)"
                                >
                                  {{ Object.keys(packages.reject_timestamp).length }} ครั้ง
                                </span>
                              </span>
                              <div
                                v-if="showReasonEdit && selectedPackageIndex === index"
                                :class="MobileSize ? 'tooltip-box-mobile' : 'tooltip-box'"
                              >
                                <div class="tooltip-header">
                                  <v-icon class="info-icon">mdi-information</v-icon>
                                  <span>เหตุผลแก้ไข</span>
                                </div>
                                <ul>
                                  <li v-for="(item, index) in selectedRejectReasons" :key="index">
                                    ครั้งที่ {{ Number(index) + 1 }} ({{ new Date(item.time_stamp).toLocaleDateString('th-TH', {
                                      timeZone: "UTC",
                                      year: 'numeric',
                                      month: 'long',
                                      day: 'numeric'
                                    }) }}) : {{ item.remark }}
                                  </li>
                                </ul>
                              </div>
                            </span>
                          </div>
                        </v-col>
                        <v-col cols="12" v-else>
                          <span style="color: #27AB9C"><b>รายการสินค้าที่ {{ Number(index) + 1 }}</b></span>
                          <span v-if="packages.status === 'approve'">
                            <v-chip class="ma-2" color="#E8F5E9" text-color="#388E3C"><b>อนุมัติแล้ว</b></v-chip>
                          </span>
                          <span v-if="packages.status === 'pending'">
                            <v-chip class="ma-2" color="#FFFDE7" text-color="#FBC02D"><b>รออนุมัติ</b></v-chip>
                          </span>
                          <span v-if="packages.status === 'reject'">
                            <v-chip class="ma-2" color="#E1F5FE" text-color="#0288D1"><b>รอแก้ไขข้อมูล</b></v-chip>
                          </span>
                          <span v-if="packages.status === 'cancel'">
                            <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D"><b>ยกเลิก</b></v-chip>
                          </span>
                        </v-col>
                        <v-col cols="12" md="3">
                          <span style="color: #00000099;"><b>ชื่อ Package : </b></span><span>{{ packages.package_name || '-'}}</span>
                        </v-col>
                        <v-col cols="12" md="3">
                          <span style="color: #00000099;"><b>ประเภทการชำระเงิน : </b></span>
                          <span v-if="packages.payment_type === 'Monthly'">รายเดือน</span>
                          <span v-else-if="packages.payment_type === 'Yearly'">รายปี</span>
                          <span v-else-if="packages.payment_type === 'Percent'">เปอร์เซ็น</span>
                          <span v-else>{{ packages.payment_type || '-' }}</span>
                        </v-col>
                        <v-col cols="12" md="6">
                          <span style="color: #00000099;"><b>ราคาสินค้า : </b></span><span>{{ packages.product_price || '-' }}</span>
                        </v-col>
                        <v-col cols="12" md="3" :hidden="packages.package_name === 'Custom'">
                          <span style="color: #00000099;"><b>รายละเอียด Package :</b></span>
                        </v-col>
                        <v-col cols="12" md="9" :hidden="packages.package_name === 'Custom'">
                          <div ref="termsContent" @scroll="handleScroll">
                            <div v-if="packages.package_functions.length !== 0">
                              <v-row v-for="(func, index) in packages.package_functions" :key="index">
                                <v-col cols="auto">
                                  <span>{{ index + 1 }}.</span>
                                </v-col>
                                <v-col cols="10">
                                  <span>รายการฟังก์ชั่นของ Package : <span style="color: #27ab9c;">{{ func.name }}</span></span><br>
                                  <span>จำนวน Transaction ของ Package :
                                    <span style="color: #27ab9c;">
                                      {{ func.limit === -1 ? 'ไม่จำกัด' : func.limit.toLocaleString() }} {{ func.unit }}
                                    </span>
                                  </span><br>
                                  <span>ราคา Transaction ส่วนเกิน :
                                    <span v-if="func.excess_transaction_price !== 0 && func.excess_transaction_unit !== 'percent' && func.excess_transaction_unit !== 'unlimit'" style="color: #27ab9c;">
                                      {{ func.excess_transaction_price }} บาท
                                    </span>
                                    <span v-else-if="func.excess_transaction_price !== 0 && func.excess_transaction_unit === 'percent'" style="color: #27ab9c;">
                                      {{ func.excess_transaction_price }}%
                                    </span>
                                    <span v-else style="color: #27ab9c;">-</span>
                                  </span>
                                </v-col>
                              </v-row>
                            </div>
                            <div v-else>
                              <span>-</span>
                            </div>
                          </div>
                        </v-col>
                        <v-col cols="12" md="3">
                          <span style="color: #00000099;"><b>รายละเอียด Package/คำอธิบายเพิ่มเติม :</b></span>
                        </v-col>
                        <v-col cols="12" md="9">
                          <div class="scrollable-content-details showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="packages.package_detail || '-'"></div>
                        </v-col>
                        <v-col cols="12" md="3">
                          <span style="color: #00000099;"><b>เงื่อนไขการให้บริการ : </b></span>
                        </v-col>
                        <v-col cols="12" md="9">
                          <div class="scrollable-content-details showTable ck-content" ref="termsContent" @scroll="handleScroll" v-html="packages.terms_of_service || '-'"></div>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <br>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-card>

    <v-dialog v-model="dialogApprove" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogApprove = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center">
          <v-img
            src="@/assets/Marketplace_partner/Group3.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text>
          <div class="text-center mb-4">
            <span><b>อนุมัติสินค้าบริการของ Partner</b></span><br>
            <span>
              คุณยืนยันจะอนุมัติสินค้าบริการของ Partner ใช่ไหม
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-2" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogApprove = false">ยกเลิก</v-btn>
          <v-btn rounded dark color="#27AB9C" class="ma-2" style="width: 100px;" @click="confirmApprove()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogConfirmApprove" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirmApprove = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            src="@/assets/Marketplace_partner/Group1.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text>
          <div class="text-center mb-4">
            <span><b>คุณได้ทำการอนุมัติสินค้าบริการของ Partner เรียบร้อย</b></span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="dialogConfirmApprove = false">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogReject" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogReject = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span><b>ไม่อนุมัติสินค้าบริการของ Partner</b></span><br>
            <span>
              คุณยืนยันจะไม่อนุมัติสินค้าบริการของ Partner ใช่ไหม
            </span>
          </div>
          <br>
          <v-textarea
            v-model="reason"
            label="กรุณาระบุเหตุผลที่ไม่อนุมัติ"
            outlined
            rows="3"
            maxlength="200"
          ></v-textarea>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogReject = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" :disabled="!reason" @click="confirmReject()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogConfirmReject" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogConfirmReject = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
          <v-img
            src="@/assets/Marketplace_partner/Group1.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text>
          <div class="text-center mb-4">
            <span><b>คุณได้ทำการไม่อนุมัติสินค้าบริการของ Partner เรียบร้อย</b></span><br>
            <span>
              กรุณารอทาง Partner แก้ไขข้อมูลอีกครั้ง
            </span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="dialogConfirmReject = false">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
export default {
  data () {
    return {
      activeTab: 'shopDetail',
      selectedIndexApprove: [],
      selectedIndexReject: [],
      selectedRejectReasons: [],
      selectedPackageIndex: null,
      showReasonEdit: false,
      dialogApprove: false,
      dialogConfirmApprove: false,
      dialogReject: false,
      dialogConfirmReject: false,
      showpartnerdetail: [],
      showaccountdetail: [],
      showpackages: [],
      crateDate: '',
      approveDate: '',
      reason: '',
      clickCounts: {}
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DetailProductShopPartnerMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'DetailProductShopPartner')
        this.$router.push({ path: '/DetailProductShopPartner' }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAdmin')
    window.scrollTo(0, 0)
    this.partnerCode = this.$route.query.partnerCode
    localStorage.setItem('partnerCode', this.partnerCode)
    await this.getdataServicePartner()
  },
  methods: {
    backtoPage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/AdminManageProductPartnerMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/AdminManageProductPartner' }).catch(() => {})
      }
    },
    async getdataServicePartner () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsServicePartner')
      var response = await this.$store.state.ModuleAdminPanit.stateServicePartner

      if (response.message === 'get product service success.') {
        this.$store.commit('closeLoader')
        this.stateShowDataServicePartner = Object.values(response.data).filter(partnerCode => partnerCode.partner_code === this.partnerCode)
        this.showpartnerdetail = await this.stateShowDataServicePartner[0].partnerdetail
        this.showaccountdetail = await this.stateShowDataServicePartner[0].accountdetail
        this.showpackages = await this.stateShowDataServicePartner[0].packages
      }
    },
    async openDialogRejectReason (index) {
      if (this.clickCounts[index] === undefined) {
        this.clickCounts[index] = 0
      }

      this.clickCounts[index]++

      // เปิด/ปิด dialog ตามจำนวนครั้งที่คลิ๊ก
      if (this.clickCounts[index] % 2 !== 0) {
        // คลิกครั้งที่ 1, 3, 5... เปิด dialog
        this.showReasonEdit = true
      } else {
        // คลิกครั้งที่ 2, 4, 6... ปิด dialog
        this.showReasonEdit = false
      }

      const selectedPackage = await this.showpackages.product_list[index]
      if (selectedPackage.status === 'reject') {
        this.selectedRejectReasons = await selectedPackage.reject_timestamp
      }
      this.selectedPackageIndex = index
    },
    openDialogApprove (index) {
      this.selectedIndexApprove = this.showpackages.product_list[index]
      this.dialogApprove = true
    },
    async confirmApprove () {
      this.$store.commit('openLoader')
      this.dialogApprove = false

      var data = {
        partner_code: this.partnerCode,
        package_code: this.selectedIndexApprove.package_code,
        status: 'approve',
        remark: ''
      }

      await this.$store.dispatch('actionsUpdateServicePartner', data)
      var response = await this.$store.state.ModuleAdminPanit.stateUpdateServicePartner
      if (response.message === 'update product service success.') {
        this.$store.commit('closeLoader')
        await this.getdataServicePartner()
        this.dialogConfirmApprove = true
      }
    },
    openDialogReject (index) {
      this.reason = ''
      this.selectedIndexReject = this.showpackages.product_list[index]
      this.dialogReject = true
    },
    async confirmReject () {
      this.$store.commit('openLoader')
      this.dialogReject = false
      var data = {
        partner_code: this.partnerCode,
        package_code: this.selectedIndexReject.package_code,
        status: 'reject',
        remark: this.reason
      }

      await this.$store.dispatch('actionsUpdateServicePartner', data)
      var response = await this.$store.state.ModuleAdminPanit.stateUpdateServicePartner
      if (response.message === 'update product service success.') {
        this.$store.commit('closeLoader')
        await this.getdataServicePartner()
        this.dialogConfirmReject = true
      }
    },
    handleScroll () {
      const content = this.$refs.termsContent
      if (content) {
        const scrollableHeight = content.scrollHeight - content.clientHeight
        const scrolledPosition = content.scrollTop

        const isAtBottom = Math.abs(scrolledPosition - scrollableHeight) < 1

        if (isAtBottom) {
          this.isScrollComplete = true
        } else {
          this.isScrollComplete = false
        }
      }
    }
  }
}
</script>

<style scoped>

.font-weight-bold {
  font-weight: bold;
}

.custom-card {
  border: 1px solid #ccc;
  border-radius: 8px;
}

.card-header {
  background-color: #f5f5f5;
  color: #333;
  font-weight: bold;
  padding: 16px;
  border-bottom: 1px solid #ccc;
}

.responsive-image {
  max-width: 350px;
  width: 100%;
  height: auto;
}

/* .tooltip-box {
  position: absolute;
  background-color: #424242;
  color: white;
  border-radius: 8px;
  padding: 10px;
  width: 375px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  z-index: 10;
}

.tooltip-box-mobile {
  position: absolute;
  background-color: #424242;
  color: white;
  border-radius: 8px;
  padding: 10px;
  width: 300px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  z-index: 10;
} */

.tooltip-box,
.tooltip-box-mobile {
  position: absolute;
  top: 100%;
  left: 89%;
  transform: translateX(-50%);
  background-color: #424242;
  color: white;
  border-radius: 8px;
  padding: 10px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
  font-size: 14px;
  z-index: 10;
  width: 375px;
}

.tooltip-box::after,
.tooltip-box-mobile::after {
  content: '';
  position: absolute;
  top: -10px;
  left: 50%;
  transform: translateX(-50%);
  border-width: 6px;
  border-style: solid;
  border-color: transparent transparent #424242 transparent;
}

.tooltip-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.info-icon {
  margin-right: 5px;
  color: white;
}

.scrollable-content-details {
  max-height: 300px;
  overflow-y: auto;
  /* padding: 10px; */
}
</style>
