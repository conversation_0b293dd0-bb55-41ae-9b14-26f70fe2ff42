<template>
  <div class="text-center">
    <v-dialog v-model="ModalTaxAddress" width="918" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-6">
                  <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{ ChangeLang(title) }}</b></span>
                </v-col>
                <v-btn fab small @click="Cancel()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
          <v-container class="pa-0">
            <!-- Desktop -->
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
                <v-card-text v-if="!MobileSize">
              <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="30" height="30"></v-img>
              <span style="font-size: 20px; font-weight: 700;">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.GeneralInformation') }}</span>
              <v-form v-if="showForm" ref="FormAddress" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="12" v-if="dataRoute === 'addressProfile' || (role.role === 'ext_buyer' && (dataRoute === 'checkoutExt' || dataRoute === 'checkoutui'))">
                    <v-radio-group v-model="taxRoles" row :rules="Rules.tax" :disabled="title === 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'">
                      <v-radio color="#27AB9C" :label="$t('AddressProfilePage.Individual')" value="Personal"></v-radio>
                      <v-radio color="#27AB9C" :label="$t('AddressProfilePage.LegalEntity')" value="Business"></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col cols="12" v-if="title !== 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'" >
                    <v-checkbox v-model="checkbox" :label="$t('AddressProfilePage.TaxInvoiceAddressModal.SameAddress')" @click="(role.role !== 'ext_buyer' && dataRoute === 'ManageAddressCompany') || (role.role !== 'ext_buyer' && dataRoute === 'checkoutui') ? getCompanyAddress() : getAddress()">
                  </v-checkbox>
                  </v-col>
                  <v-col cols="12" class="mt-4">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceName') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceName')" outlined dense v-model="fullname" :rules="Rules.fullname" counter="100" @keypress="CheckSpacebarName($event)"></v-text-field>
                  </v-col>
                  <!-- {{taxRoles}} -->
                  <v-col cols="12" v-if="taxRoles === 'Business' || dataRoute === 'ManageAddressCompany' || (role.role !== 'ext_buyer' && dataRoute === 'checkoutui')" >
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.BranchNo') }}<span style="color: red;">*</span></span>
                    <v-col class="pa-0">
                      <span style="font-size: 12px;">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.enterCode') }}</span>
                    </v-col>
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.BranchNo')" outlined dense v-model="branchcode" :rules="Rules.code" @keypress="CheckSpacebar($event)"  maxlength="5" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceNo') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field class="input_text" :maxLength="13" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceNo')" outlined dense v-model="taxID" :rules="Rules.taxID" @keypress="CheckSpacebar($event)" @input="validateTaxID()" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceEmail') }} <span style="color: red;">*</span></span>
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceEmail')" outlined dense v-model="email" :rules="Rules.email" @keypress="CheckSpacebar($event)"></v-text-field>
                  </v-col>
                  <!-- <v-col cols="6">
                    <span class="labelInputSize">เบอร์โทรศัพท์<span style="color: red;">*</span></span>
                    <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.tel"  @keypress="CheckSpacebar($event)" maxlength="10"  oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col> -->
                  <v-col cols="12" class="my-4">
                    <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                    <span style="font-size: 20px; font-weight: 700;">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceInformation') }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceAddress') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceAddress')" outlined dense v-model="addressDetail" :rules="Rules.address" @keypress="CheckSpacebarName($event)"></v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.SubDistrict') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="6">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.District') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="6" class="pr-5">
                    <addressinput-subdistrict :rules="Rules.empty" label="" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistrict" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderSubDistrict')"/>
                    <div v-if="checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                  <v-col cols="6">
                    <addressinput-district label="" :rules="Rules.empty" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderDistrict')" />
                    <div v-if="checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                  <v-col cols="6">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.Province') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="6">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.Zipcode') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="6" class="pr-5">
                    <addressinput-province label="" :rules="Rules.empty" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderProvince')" />
                    <div v-if="checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                  <v-col cols="6">
                    <addressinput-zipcode label="" :rules="Rules.empty" numbered v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderZipCode')" />
                    <div v-if="checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                </v-row>
              </v-form>
            </v-card-text>
            <!-- Mobile -->
            <v-card-text v-if="MobileSize" class="pa-0">
              <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="24" height="24"></v-img>
              <span style="font-size: 16px; font-weight: 700;">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.GeneralInformation') }}</span>
              <v-form v-if="showForm" ref="FormAddress" :lazy-validation="lazy">
                <v-row no-gutters>
                  <v-col cols="12" v-if="dataRoute === 'addressProfileMobile' || (role.role === 'ext_buyer' && (dataRoute === 'checkoutExt' || dataRoute === 'checkoutui'))">
                    <v-radio-group v-model="taxRoles" row :rules="Rules.tax" :disabled="title === 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'">
                      <v-radio color="#27AB9C" :label="$t('AddressProfilePage.Individual')" value="Personal"></v-radio>
                      <v-radio color="#27AB9C" :label="$t('AddressProfilePage.LegalEntity')" value="Business"></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col cols="12" v-if="title !== 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'" >
                    <v-checkbox v-model="checkbox" :label="$t('AddressProfilePage.TaxInvoiceAddressModal.SameAddress')" @click="(role.role !== 'ext_buyer' && dataRoute === 'ManageAddressCompanyMobile') || (role.role !== 'ext_buyer' && dataRoute === 'checkoutui') ? getCompanyAddress() : getAddress()">
                  </v-checkbox>
                  </v-col>
                  <v-col cols="12" class="mt-4">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceName') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceName')" outlined dense v-model="fullname" :rules="Rules.fullname" counter="100" @keypress="CheckSpacebarName($event)"></v-text-field>
                  </v-col>
                  <v-col cols="12" v-if="taxRoles === 'Business' || dataRoute === 'ManageAddressCompanyMobile' || (role.role !== 'ext_buyer' && dataRoute === 'checkoutui')" >
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.BranchNo') }}<span style="color: red;">*</span></span>
                    <v-col class="pa-0">
                      <span style="font-size: 12px;">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.enterCode') }}</span>
                    </v-col>
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.BranchNo')" outlined dense v-model="branchcode" :rules="Rules.code" @keypress="CheckSpacebar($event)"  maxlength="5" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceNo') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" class="">
                    <v-text-field class="input_text" maxlength="13" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceNo')" outlined dense v-model="taxID" :rules="Rules.taxID" @keypress="CheckSpacebar($event)"  @input="validateTaxID()" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceEmail') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" class="">
                    <v-text-field :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceEmail')" outlined dense v-model="email" :rules="Rules.email"></v-text-field>
                  </v-col>
                  <!-- <v-col cols="12">
                    <span class="labelInputSize">เบอร์โทรศัพท์<span style="color: red;">*</span></span>
                    <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.tel"  @keypress="CheckSpacebar($event)" maxlength="10"  oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col> -->
                  <v-col cols="12" class="my-4">
                    <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                    <span style="font-size: 16px; font-weight: 700;">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceInformation') }}</span>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceAddress') }} <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.TaxInvoiceAddressModal.TaxInvoiceAddress')" outlined dense v-model="addressDetail" :rules="Rules.address" @keypress="CheckSpacebarName($event)"></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.SubDistrict') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <addressinput-subdistrict :rules="Rules.empty" label="" v-model="subdistrict" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderSubDistrict')"/>
                    <div v-if="checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.District') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <addressinput-district label="" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderDistrict')" />
                    <div v-if="checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.Province') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <addressinput-province label="" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderProvince')" />
                    <div v-if="checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                  <v-col cols="12">
                    <span class="labelInputSize">{{ $t('AddressProfilePage.ShippingAddressModal.Zipcode') }}<span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12">
                    <addressinput-zipcode label="" numbered v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderZipCode')" />
                    <div v-if="checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                  </v-col>
                </v-row>
              </v-form>
            </v-card-text>
            </div>
            </v-card>
          </v-container>
          </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="px-12" style="height: 88px; background-color: #F5FCFB;">
            <v-btn class="px-5 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="Cancel()">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
            <v-spacer></v-spacer>
            <!-- <v-btn class="px-5 white--text" style="border-radius: 40px;" color="#27AB9C" @click="CreateTaxinvoiceAddress()" v-if="ShippingType === 'Normal' || this.ShippingType === 'QUPage' || this.ShippingType === 'PO'">บันทึก</v-btn> -->
            <v-btn class="px-5 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Save') }}</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="px-5" style="height: 88px; background-color: #F5FCFB;">
            <v-btn class="px-5 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="Cancel()">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
            <v-spacer></v-spacer>
            <!-- <v-btn class="px-5 white--text" style="border-radius: 40px;" color="#27AB9C" @click="CreateTaxinvoiceAddress()" v-if="ShippingType === 'Normal' || this.ShippingType === 'QUPage' || this.ShippingType === 'PO'">บันทึก</v-btn> -->
            <v-btn class="px-5 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Save') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? $t('AddressProfilePage.TaxInvoiceAddressModal.TitleAddAddressInvoice') : $t('AddressProfilePage.TaxInvoiceAddressModal.TitleEditAddressInvoice') }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('BankAccountPage.Proceed') }}</span>
          </v-card-text>
          <v-card-text v-if="!MobileSize">
            <v-row dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="(title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'addressProfile') ? CreateTaxinvoiceAddressEtax() : (title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'checkout') ? CreateTaxinvoiceAddressEtax() : (title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'userdetail') ? CreateTaxinvoiceAddressEtax() : (title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'ManageTaxInvoice') ? CreateTaxinvoiceAddressEtax() : CreateTaxinvoiceAddress()">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
          <v-card-text v-if="MobileSize">
            <v-row dense justify="between">
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="(title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'addressProfile') ? CreateTaxinvoiceAddressEtax() : (title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'checkout') ? CreateTaxinvoiceAddressEtax() : (title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'userdetail') ? CreateTaxinvoiceAddressEtax() : (title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && page === 'ManageTaxInvoice') ? CreateTaxinvoiceAddressEtax() : CreateTaxinvoiceAddress()">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogSuccess = !dialogSuccess"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 20px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? $t('AddressProfilePage.TaxInvoiceAddressModal.Success1') : $t('AddressProfilePage.TaxInvoiceAddressModal.Success2') }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? $t('AddressProfilePage.TaxInvoiceAddressModal.Success3') : $t('AddressProfilePage.TaxInvoiceAddressModal.Success4') }}</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogSuccess = !dialogSuccess">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogSuccess = !dialogSuccess">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode, Encode } from '@/services'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
Vue.use(VueThailandAddress)
export default {
  props: ['title', 'page'],
  data () {
    return {
      dialogAwaitConfirm: false,
      dialogSuccess: false,
      showForm: true,
      comAddress: [],
      role: '',
      invoicedetail: [],
      addresID: '',
      defaultinvoice: '',
      dataInvoice: [],
      userdetail: [],
      checkbox: false,
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      data: [],
      lazy: false,
      ModalTaxAddress: false,
      fullname: '',
      taxID: '',
      addressDetail: '',
      dialog: false,
      subdistrict: '',
      district: '',
      email: '',
      phone: '',
      branchcode: '',
      province: '',
      zipcode: '',
      taxRoles: 'No',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      dataRoute: '',
      Rules: {
        empty: [v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error2')],
        fullname: [
          v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error3'),
          v => v.length <= 100 || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error4')
        ],
        taxID: [
          v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error5'),
          v => v.length >= 13 || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error6'),
          v => this.validNationalID(v) || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error7')
        ],
        address: [
          v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error8')
        ],
        email: [
          // v => !!v || 'กรุณากรอกอีเมล',
          // v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          // v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          // v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล',
          // v => v.length <= 256 || 'ห้ามใส่ตัวอักษรเกิน 256 ตัวอักษร'
          v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error9'),
          v => /^[^@]+@\w+(\.\w+)+\w$/.test(v) || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error10'),
          // v => /^\w+([.-]?\w+)*@[a-zA-Z]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => v.length <= 256 || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error11')
        ],
        tel: [
          v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error12'),
          v => v.charAt(0) === '0' || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error13'),
          v => v.length === 10 || v === '' || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error14')
        ],
        code: [
          v => !!v || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error15'),
          v => v.length === 5 || v === '' || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error16')
        ],
        tax: [
          v => !!this.taxRoles || this.$t('AddressProfilePage.TaxInvoiceAddressModal.error17')
        ]
      }
    }
  },
  mounted () {
    if (this.ShippingType === 'Normal' || this.ShippingType === 'QUPage') {
      this.getAddressTaxinvoiceData()
    } else {
    }
  },
  created () {
    this.dataRoute = this.$route.name
    // console.log(this.$route.name)
    // this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    // ShippingType (val) {
    // },
    checkAddress (val) {
      this.EditaddressDialog = val
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  methods: {
    ChangeLang (val) {
      if (val === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี') {
        if (this.$i18n.locale === 'th') {
          return val
        } else {
          return this.$t('AddressProfilePage.TaxInvoiceAddressModal.TitleAddAddressInvoice')
        }
      } else if (val === 'แก้ไขที่อยู่ในการออกใบกำกับภาษี') {
        if (this.$i18n.locale === 'th') {
          return val
        } else {
          return this.$t('AddressProfilePage.TaxInvoiceAddressModal.TitleEditAddressInvoice')
        }
      }
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateTaxID () {
      if (this.validNationalID(this.taxID)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    async GetAllinvoice () {
      this.invoicedetail = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (dataRole.role === 'purchaser') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      const data = {
        user_id: onedata.user.user_id,
        company_id: dataRole.role !== 'purchaser' ? '' : companyId.company.company_id
      }
      await this.$store.dispatch('actionsGetAllInvoiceAddress', data)
      var res = await this.$store.state.ModuleUser.stateGetAllInvoiceAddress
      this.invoicedetail = res.data
      if (res.message === 'Success') {
        this.dataInvoice = await JSON.parse(Decode.decode(localStorage.getItem('InvoiceAddress')))
        var addressData = this.dataInvoice
        // console.log(addressData)
        if (this.title === 'แก้ไขที่อยู่ในการออกใบกำกับภาษี') {
          this.taxRoles = addressData.tax_type
          this.addresID = addressData.id
          this.taxID = addressData.tax_id
          this.data = addressData.buyer_one_id
          this.branchcode = addressData.branch_id
          this.fullname = addressData.name
          this.email = addressData.email
          this.addressDetail = addressData.address
          this.zipcode = addressData.postal_code
          this.province = addressData.province
          this.district = addressData.district
          this.subdistrict = addressData.sub_district
          this.defaultinvoice = addressData.default_invoice
          // user_id: addressData.user_id,
          // company_id: addressData.company_id,
          // role: addressData.role,
        } else {
          this.$refs.FormAddress.resetValidation()
          this.taxRoles = 'No'
          this.addresID = ''
          this.taxID = ''
          this.data = ''
          this.fullname = ''
          this.branchcode = ''
          this.email = ''
          this.addressDetail = ''
          this.zipcode = ''
          this.province = ''
          this.district = ''
          this.subdistrict = ''
          this.defaultinvoice = ''
        }
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
      }
    },
    async getCompanyAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var companyId = ''
      if (dataRole.role !== 'ext_buyer') {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var data = {
        company_id: companyId.company.company_id
      }
      await this.$store.dispatch('actionsGetCompanyAddress', data)
      var res = await this.$store.state.ModuleCart.stateGetCompanyAddress
      if (res.ok === 'y') {
        this.comAddress = res.query_result
        if (this.checkbox === true) {
          this.comAddress.forEach(element => {
            if (element.default === 'Y') {
              this.fullname = element.name_th
              this.addressDetail = element.detail
              this.subdistrict = element.sub_district
              this.district = element.district
              this.province = element.province
              this.zipcode = element.zip_code
            }
          })
        } else {
          this.fullname = ''
          this.phone = ''
          this.addressDetail = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
          this.zipcode = ''
          this.branchcode = ''
          this.$refs.FormAddress.resetValidation()
        }
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: res.message
          })
        }
      }
    },
    async getAddress () {
      await this.$store.dispatch('actionListUserAddress')
      var userdetail = await this.$store.state.ModuleUser.stateListUserAddress
      this.userdetail = [...userdetail.data]
      if (this.userdetail.length === 0) {
        if (this.userdetail.length === 0) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error18')}</h3>`
          })
          this.checkbox = false
        }
      } else if (this.userdetail[0].default_address !== 'Y' && this.userdetail.length === 1) {
        if (this.userdetail[0].default_address !== 'Y' && this.userdetail.length === 1) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error19')}</h3>`
          })
          this.checkbox = false
        }
      }
      if (this.checkbox === true) {
        this.userdetail.forEach(element => {
          if (element.default_address === 'Y') {
            this.fullname = element.first_name + ' ' + element.last_name
            this.addressDetail = element.detail
            this.subdistrict = element.sub_district
            this.district = element.district
            this.province = element.province
            this.zipcode = element.zip_code
          }
        })
      } else {
        this.fullname = ''
        this.phone = ''
        this.addressDetail = ''
        this.subdistrict = ''
        this.district = ''
        this.province = ''
        this.zipcode = ''
        this.$refs.FormAddress.resetValidation()
      }
    },
    CheckSpacebarName (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    async open () {
      // var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      if (this.ShippingType === 'Normal' || this.frompage === 'QUPage' || this.frompage === 'PO') {
        this.getAddressTaxinvoiceData()
      } else {
        this.role = JSON.parse(localStorage.getItem('roleUser'))
        // console.log(1)
        this.GetAllinvoice()
        // console.log(2)
        // this.fullname = ''
        // this.email = ''
        // this.addressDetail = ''
        // this.zipcode = ''
        // this.province = ''
        // this.district = ''
        // this.subdistrict = ''
        // this.taxID = ''
      }
      // console.log(this.frompage)
      this.ModalTaxAddress = true
    },
    Cancel () {
      this.ClearData()
      this.checkbox = false
      this.taxRoles = 'No'
      this.branchcode = ''
      this.fullname = ''
      this.email = ''
      this.phone = ''
      this.addressDetail = ''
      this.zipcode = ''
      this.province = ''
      this.district = ''
      this.subdistrict = ''
      this.taxID = ''
      this.$refs.FormAddress.resetValidation()
      this.showForm = false
      this.$nextTick(() => {
        this.showForm = true
      })
      this.ModalTaxAddress = false
      // อย่าลืม clear data
      // localStorage.removeItem('AddressData')
      // this.$router.push({ path: '/UPS/shoppingcart' })
    },
    ClearData () {
      this.checkbox = false
      this.taxRoles = 'No'
      this.branchcode = ''
      this.phone = ''
      this.fullname = ''
      this.email = ''
      this.addressDetail = ''
      this.zipcode = ''
      this.province = ''
      this.district = ''
      this.subdistrict = ''
      this.taxID = ''
      this.$refs.FormAddress.resetValidation()
    },
    async getAddressTaxinvoiceData () {
      this.taxinvoiceAddress = []
      this.data = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        user_id: onedata.user.user_id,
        role: this.frompage === 'QUPage' ? 'purchaser' : dataRole.role,
        company_id: dataCompany,
        tax_type: this.taxRoles
      }
      await this.$store.dispatch('actionsGetInvoice', data)
      this.taxinvoiceAddress = await this.$store.state.ModuleUser.stategetInvoice
      // console.log('taxinvoiceAddress modal', this.taxinvoiceAddress.data)
      if (this.taxinvoiceAddress.message === 'Success') {
        if (this.taxinvoiceAddress.data.length !== 0) {
          this.data = this.taxinvoiceAddress.data[0]
          this.fullname = this.data.name
          this.email = this.data.email
          this.addressDetail = this.data.address
          this.zipcode = this.data.postal_code
          this.province = this.data.province
          this.district = this.data.district
          this.subdistrict = this.data.sub_district
          this.taxID = this.data.tax_id
        } else {
          this.fullname = ''
          this.email = ''
          this.addressDetail = ''
          this.zipcode = ''
          this.province = ''
          this.district = ''
          this.subdistrict = ''
          this.taxID = ''
        }
      } else {
        if (this.taxinvoiceAddress.message === 'This user is Unauthorized' || this.taxinvoiceAddress.message === 'This user is unauthorized.' || this.taxinvoiceAddress.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: this.taxinvoiceAddress.message
          })
        }
      }
    },
    async CreateTaxinvoiceAddress () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCompany = ''
      // console.log('taxType===', this.taxType)
      if (this.dataRoute !== 'addressProfile' && this.dataRoute !== 'addressProfileMobile') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          dataCompany = companyDataSet.company.company_id
        } else {
          dataCompany = null
        }
      } else {
        dataCompany = null
      }
      if (this.$refs.FormAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode && this.taxRoles !== 'No') {
            const check = this.checkSendAddress()
            if (check.length !== 0) {
              var data = {
                id: this.addresID,
                user_id: onedata.user.user_id,
                company_id: dataCompany,
                tax_type: this.taxRoles,
                // tax_type: this.dataRoute === 'ManageTaxInvoice' ? 'Business' : this.taxRoles,
                branch_id: this.branchcode,
                buyer_one_id: this.data,
                role: dataRole.role,
                name: this.fullname,
                email: this.email,
                address: this.addressDetail,
                postal_code: this.zipcode,
                province: this.province,
                district: this.district,
                sub_district: this.subdistrict,
                tax_id: this.taxID,
                default_invoice: this.defaultinvoice
              }
              // console.log('data for update invoice =====>', data)
              await this.$store.dispatch('actionsUpsertInvoice', data)
              var res = await this.$store.state.ModuleUser.stateupsertInvoice
              // console.log('taxInvoiceAddress', res)
              if (res.message === 'create invoice success' || res.message === 'update invoice success') {
                // this.$swal.fire({ icon: 'success', title: 'แก้ไขที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
                this.dialog = false
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                this.dialogAwaitConfirm = false
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
                // this.GetAllinvoice()
                // await this.$EventBus.$emit('getAddressTex')
                this.$refs.FormAddress.resetValidation()
                this.showForm = false
                this.$nextTick(() => {
                  this.showForm = true
                })
                this.ModalTaxAddress = false
                var TaxinvoiceAddress = {
                  name: this.fullname,
                  email: this.email,
                  address: this.addressDetail,
                  branch_id: this.branchcode,
                  postal_code: this.zipcode,
                  province: this.province,
                  district: this.district,
                  sub_district: this.subdistrict,
                  tax_id: this.taxID
                }
                if (this.page === 'userdetail' || this.page === 'ManageTaxInvoice' || (this.role.role !== 'ext_buyer' && this.dataRoute === 'checkoutui')) {
                  await this.$EventBus.$emit('getAddressTex')
                }
                if (this.frompage === 'CartPage') {
                  await this.$EventBus.$emit('GetTaxAddress')
                  // console.log(1)
                } else if (this.frompage === 'QUPage') {
                  await this.$EventBus.$emit('checkAddressTaxinvoice')
                  // console.log(2)
                } else if (this.frompage === 'PO') {
                  localStorage.setItem('TaxinvoiceAddress', Encode.encode(TaxinvoiceAddress))
                  await this.$EventBus.$emit('checkAddressTaxInvoicePO')
                }
                this.ClearData()
              } else {
                if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
                  this.$store.commit('closeLoader')
                  this.$EventBus.$emit('refreshToken')
                } else {
                  this.$store.commit('closeLoader')
                  this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}`, showConfirmButton: false, timer: 1500 })
                }
                // this.ModalTaxAddress = false
              }
              // if (res.message === 'Create user address success') {
              //   this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
              //   this.dialog = false
              //   localStorage.removeItem('AddressData')
              // } else if (res.message === 'Parameter is missing') {
              //   this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}`, text: `${this.$t('AddressProfilePage.ShippingAddressModal.error22')}`, showConfirmButton: false, timer: 2500 })
              // }
            } else {
              this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}</h5>`, text: `${this.$t('AddressProfilePage.ShippingAddressModal.error21')}`, showConfirmButton: false, timer: 2500 })
            }
          } else if (this.taxRoles === 'No') {
            this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}</h5>`, text: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error21')}`, showConfirmButton: false, timer: 2500 })
          } else {
            this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}</h5>`, text: `${this.$t('AddressProfilePage.ShippingAddressModal.error22')}`, showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error21')}`, showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error22')}`, showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    async CreateTaxinvoiceAddressEtax () {
      this.$store.commit('openLoader')
      console.log(this.invoicedetail.length)
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var dataCompany = ''
      // console.log('QAQAQAQAQA')
      // console.log('taxType===', this.taxType)
      if (this.dataRoute === 'ManageAddressCompany' || this.dataRoute === 'ManageAddressCompanyMobile' || (this.role.role !== 'ext_buyer' && this.dataRoute === 'checkoutui')) {
        this.taxRoles = 'Business'
      }
      // console.log('55555', this.dataRoute)
      if (this.dataRoute !== 'addressProfile' && this.dataRoute !== 'addressProfileMobile') {
        if (localStorage.getItem('SetRowCompany') !== null) {
          var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
          dataCompany = companyDataSet.company.company_id
        } else {
          dataCompany = null
        }
      } else {
        dataCompany = null
      }
      if (this.$refs.FormAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode && this.taxRoles !== 'No') {
            // const check = this.checkSendAddress()
            if (this.invoicedetail.length === 0) {
              var data = {
                default_invoice: 'Y',
                user_id: onedata.user.user_id,
                company_id: dataCompany,
                tax_type: this.taxRoles,
                branch_id: this.branchcode,
                buyer_one_id: this.data !== undefined ? this.data.buyer_one_id : null,
                role: this.frompage === 'QUPage' ? 'purchaser' : this.frompage === 'PO' ? 'purchaser' : dataRole.role,
                name: this.fullname,
                email: this.email,
                address: this.addressDetail,
                postal_code: this.zipcode,
                province: this.province,
                district: this.district,
                sub_district: this.subdistrict,
                tax_id: this.taxID
              }
              // console.log('data for update invoice =====>', data)
              await this.$store.dispatch('actionsAddInvoice', data)
              var res = await this.$store.state.ModuleUser.stateAddInvoice
              if (res.message === 'create invoice success' || res.message === 'update invoice success') {
                // this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
                this.dialog = false
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                this.dialogAwaitConfirm = false
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
                this.fullname = ''
                this.email = ''
                this.branchcode = ''
                this.addressDetail = ''
                this.zipcode = ''
                this.province = ''
                this.district = ''
                this.subdistrict = ''
                this.taxID = ''
                this.$refs.FormAddress.resetValidation()
                this.showForm = false
                this.$nextTick(() => {
                  this.showForm = true
                })
                this.ModalTaxAddress = false
                // await this.$EventBus.$emit('getAddressTex')
                if (this.page === 'userdetail' || this.page === 'ManageTaxInvoice' || (this.role.role !== 'ext_buyer' && this.dataRoute === 'checkoutui')) {
                  await this.$EventBus.$emit('getAddressTex')
                }
                if (this.frompage === 'CartPage') {
                  await this.$EventBus.$emit('GetInvoiceAddress')
                }
                this.ClearData()
                // else if (this.frompage === 'QUPage') {
                //   await this.$EventBus.$emit('checkAddressTaxinvoice')
                // }
              } else {
                if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
                  this.$store.commit('closeLoader')
                  this.$EventBus.$emit('refreshToken')
                } else {
                  this.$store.commit('closeLoader')
                  this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}`, showConfirmButton: false, timer: 1500 })
                }
                // this.ModalTaxAddress = false
              }
              // if (res.message === 'Create user address success') {
              //   this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
              //   this.dialog = false
              //   localStorage.removeItem('AddressData')
              // } else if (res.message === 'Parameter is missing') {
              //   this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}`, text: `${this.$t('AddressProfilePage.ShippingAddressModal.error22')}`, showConfirmButton: false, timer: 2500 })
              // }
            } else if (this.invoicedetail.length !== 0) {
              var data1 = {
                default_invoice: 'N',
                user_id: onedata.user.user_id,
                company_id: dataCompany,
                branch_id: this.branchcode,
                tax_type: this.taxRoles,
                buyer_one_id: this.data !== undefined ? this.data.buyer_one_id : null,
                role: this.frompage === 'QUPage' ? 'purchaser' : this.frompage === 'PO' ? 'purchaser' : dataRole.role,
                name: this.fullname,
                email: this.email,
                address: this.addressDetail,
                postal_code: this.zipcode,
                province: this.province,
                district: this.district,
                sub_district: this.subdistrict,
                tax_id: this.taxID
              }
              // console.log('data for update invoice =====>', data)
              await this.$store.dispatch('actionsAddInvoice', data1)
              var res1 = await this.$store.state.ModuleUser.stateAddInvoice
              if (res1.message === 'create invoice success' || res.message === 'update invoice success') {
                // this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
                this.dialog = false
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                this.dialogAwaitConfirm = false
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
                this.fullname = ''
                this.email = ''
                this.branchcode = ''
                this.addressDetail = ''
                this.zipcode = ''
                this.province = ''
                this.district = ''
                this.subdistrict = ''
                this.taxID = ''
                this.$refs.FormAddress.resetValidation()
                this.showForm = false
                this.$nextTick(() => {
                  this.showForm = true
                })
                this.ModalTaxAddress = false
                if (this.page === 'userdetail' || this.page === 'ManageTaxInvoice' || (this.role.role !== 'ext_buyer' && this.dataRoute === 'checkoutui')) {
                  await this.$EventBus.$emit('getAddressTex')
                }
                if (this.frompage === 'CartPage') {
                  await this.$EventBus.$emit('GetInvoiceAddress')
                }
                this.ClearData()
                // else if (this.frompage === 'QUPage') {
                //   await this.$EventBus.$emit('checkAddressTaxinvoice')
                // }
              } else {
                if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
                  this.$store.commit('closeLoader')
                  this.$EventBus.$emit('refreshToken')
                } else {
                  this.$store.commit('closeLoader')
                  this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}`, showConfirmButton: false, timer: 1500 })
                }
                // this.ModalTaxAddress = false
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}</h5>`, text: `${this.$t('AddressProfilePage.ShippingAddressModal.error21')}`, showConfirmButton: false, timer: 2500 })
            }
          } else if (this.taxRoles === 'No') {
            this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}</h5>`, text: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error21')}`, showConfirmButton: false, timer: 2500 })
          } else {
            this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error20')}</h5>`, text: `${this.$t('AddressProfilePage.ShippingAddressModal.error22')}`, showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error21')}`, showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: `${this.$t('AddressProfilePage.TaxInvoiceAddressModal.error22')}`, showConfirmButton: false, timer: 2500 })
      }
      this.$refs.FormAddress.validate()
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>

<style>
.th-address-autocomplete {
  width: 100% !important;
  max-height: 200px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
/* .theme--light.v-input input, .theme--light.v-input textarea {
  color: #333333;
} */
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  /* opacity: 0.6; */
}
.labelInputSize {
  font-size: 16px;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
