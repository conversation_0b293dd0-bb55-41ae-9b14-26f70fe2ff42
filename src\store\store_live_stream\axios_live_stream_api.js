import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async GenTokenStream (val) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}live_shop`, val, '')
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async LiveListProduct (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}live_list_product`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
