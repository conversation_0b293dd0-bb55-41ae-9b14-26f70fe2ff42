<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายชื่อแอดมินของระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายชื่อแอดมินของระบบ</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อ-สกุลแอดมินของระบบ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="adminDataList.length !== 0 && (!MobileSize && !IpadSize)">รายการชื่อแอดมินของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="adminDataList.length !== 0 && (MobileSize || IpadSize)">รายการชื่อแอดมินของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 4 : 6" align="end" class="pt-0">
                <v-btn class="mt-2 white--text" color="#27AB9C"  @click="addAdminpanit()" :disabled="isSuperAdmin === false"><v-icon left>mdi-plus-circle-outline</v-icon>เพิ่มข้อมูล</v-btn>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="adminDataList"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบรายชื่อแอดมินของระบบ"
                no-data-text="ไม่มีรายชื่อแอดมินของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                <!-- <template v-slot:[`item.idUser`]="{ item, index }">
                  {{ index + 1 }}
                </template> -->
                <template v-slot:[`item.username`]="{ item }">
                  <span>{{ item.username }}</span>
                </template>
                <template v-slot:[`item.admin_type`]="{ item }">
                  <span v-if="item.admin_type === 'super_admin_platform'">Super Admin Platform</span>
                  <span v-if="item.admin_type === 'admin_platform'">Admin Platform</span>
                </template>
                <template v-slot:[`item.actions`]="{ item }">
                  <v-row dense>
                    <span
                      outlined
                      color="#27AB9C"
                      @click="isSuperAdmin === true ? adminDetail(item) : ''"
                      cclass="pt-4 pb-4"
                      :style="{'color': isSuperAdmin === true ? '#27AB9C' : '#BDBDBD', 'cursor': isSuperAdmin === true ? 'pointer' : 'default'}"
                    >
                    <v-icon :color="isSuperAdmin === true ? '#27AB9C': '#BDBDBD'">mdi-file-document-outline</v-icon>
                      รายละเอียด <v-icon :style="{'color': isSuperAdmin === true ? '#27AB9C' : '#BDBDBD'}">mdi-chevron-right</v-icon>
                    </span>
                  </v-row>
                </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <CreateAdminModal ref="CreateAdminModal" />
      <DetailAdminModal ref="DetailAdminModal" />
    </v-card>
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
export default {
  components: {
    CreateAdminModal: () => import(/* webpackPrefetch: true */ '@/components/AdminPanit/AdminManage/CreateAdminPanitModal'),
    DetailAdminModal: () => import(/* webpackPrefetch: true */ '@/components/AdminPanit/AdminManage/DetailAdminPanitModal')
    // EditAdminModal: () => import('@/components/AdminPanit/AdminManage/EditAdminPanitModal')
  },
  data () {
    return {
      search: '',
      adminDataList: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ-สกุล', value: 'username', width: '170', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', width: '120', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สิทธิ์การใช้งาน', value: 'admin_type', filterable: false, width: '180', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', filterable: false, width: '150', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminPanitManageMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'adminPanitManage')
        this.$router.push({ path: '/adminPanitManage' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$on('createAdminPanitSuccess', this.getAdimData)
    this.$EventBus.$on('deleteAdminPanitSuccess', this.getAdimData)
    this.$EventBus.$on('editAdminPanitSuccess', this.getAdimData)
    if (localStorage.getItem('oneData') !== null) {
      this.getAdimData()
      this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('createAdminPanitSuccess')
    this.$EventBus.$off('deleteAdminPanitSuccess')
    this.$EventBus.$off('editAdminPanitSuccess')
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async AuthorityUser () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.message === 'Get user detail success') {
        if (response.data.current_role_user.super_admin_platform === true) {
          this.isSuperAdmin = true
        } else {
          this.isSuperAdmin = false
        }
      }
    },
    async getAdimData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetListAdminPlatform')
      var response = await this.$store.state.ModuleAdminManage.stateGetListAdminPlatform
      if (response.message === 'Get list admin success.') {
        this.$store.commit('closeLoader')
        this.adminDataList = response.data.list_active_admin
        if (this.adminDataList.length !== 0) {
          this.adminDataList.forEach(element => {
            element.username = element.first_name_th + ' ' + element.last_name_th
          })
          for (var i = 0; i < this.adminDataList.length; i++) {
            this.adminDataList[i].indexOfUser = i + 1
          }
        }
      } else if (response.message === 'Not found this user data.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ไม่พบข้อมูลคนที่จะเพิ่มเป็นแอดมิน', showConfirmButton: false, timer: 1500 })
      } else if (response.message === 'This user is not super admin.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ไม่สามารถดูรายละเอียดได้เนื่องจากคุณไม่มีสิทธิ์ Super Admin Platform', showConfirmButton: false, timer: 2500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    addAdminpanit () {
      this.$refs.CreateAdminModal.open()
    },
    adminDetail (item) {
      const data = item
      this.$refs.DetailAdminModal.open(data, this.isSuperAdmin)
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(5) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(5) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
