<template>
  <v-app class="backgroundPage" id="app">
    <AppBar />
    <v-main>
      <router-view v-on:scroll="closeModal"/>
    </v-main>
    <Footer />
  </v-app>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    AppBar: () => import(/* webpackPrefetch: true */ '@/components/UPS/Home/AppBar'),
    Footer: () => import(/* webpackPrefetch: true */ '@/components/UPS/Home/Footer')
  },
  data: () => ({
    path: false,
    fab: false,
    dialogInside: false,
    chat: '',
    showChat: false,
    onechatToken: '',
    valueDot: '',
    chatDot: ''
  }),
  destroyed () {
    // console.log('destroyed')
    window.removeEventListener('scroll', this.closeModal)
  },
  created () {
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user.one_chat_token !== undefined) {
        this.onechatToken = oneData.user.one_chat_token
        // this.valueDot = 'https://chat.one.th:8096/login_service/E-Procurement/Ad631277f26ad58df9b1380cbd99d53f854a6f4b780d04b519c5d630f7782736ba0ab7687e1294a68851b7ec98eb915eb?onechat_token=' + this.onechatToken
        // console.log(this.valueDot)
        this.getChat()
      } else {
        this.onechatToken = ''
      }
      // console.log('oneData===>', oneData.user)
      if (oneData.user === undefined) {
        localStorage.removeItem('oneData')
      }
    }
    window.addEventListener('scroll', this.closeModal)
    window.addEventListener('storage', function (event) {
      if (event.key === 'oneData' && !event.newValue) {
        window.location.reload()
      }
    })
    this.CheckPathFooter()
  },
  computed: {
    main () {
      return this.$route.name === 'HomeProduct'
    }
  },
  mounted () {
    this.$EventBus.$on('CheckFooter', this.CheckPathFooter)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('CheckFooter')
    })
  },
  methods: {
    ShowAlert () {
      // console.log('เข้า function')
    },
    closeModal () {
      this.$EventBus.$emit('closeModalLogin')
      this.$EventBus.$emit('closeModalRegister')
      this.$EventBus.$emit('closeModalSuccess')
      this.$EventBus.$emit('closeModalCartNoLogin')
      this.$EventBus.$emit('closeModalCart')
    },
    async CheckPathFooter () {
      var currentRoutepath = this.$router.currentRoute.path
      if (currentRoutepath === '/shop' || currentRoutepath === '/poseller' || currentRoutepath === '/manageproduct' || currentRoutepath === '/orderdetailseller' || currentRoutepath === '/seller') {
        this.path = false
      } else {
        this.path = true
      }
    }
  }
}
</script>

<style scoped>
.backgroundPage{
  background-color: #E5E5E5;
}
</style>

