<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
        <v-row>
            <!-- หัวข้อเรื่อง -->
            <v-col cols="12">
                <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการผู้ใช้งานบริษัท</v-card-title>
                <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการผู้ใช้งานบริษัท</v-card-title>
            </v-col>
            <v-col>
                <v-row>
                    <v-col cols="12">
                    <v-col cols="12" md="6" sm="12">
                        <v-text-field v-model="search" placeholder="ค้นหาจากรายชื่อบริษัทในระบบ" outlined rounded dense hide-details>
                            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                        </v-text-field>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                        <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="(!MobileSize && !IpadSize)">รายชื่อผู้ใช้งานบริษัทในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                        <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="(MobileSize || IpadSize)">รายชื่อผู้ใช้งานบริษัทในระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                    </v-col>
                    <v-col cols="12">
                        <v-data-table
                        :headers="headers"
                        :search="search"
                        :items="adminCompanyList"
                        style="width:100%; text-align: center; white-space: nowrap;"
                        height="100%"
                        @pagination="countRequest"
                        no-results-text="ไม่พบรายการบริษัทในระบบ"
                        no-data-text="ไม่พบรายการบริษัทในระบบ"
                        :items-per-page="10"
                        class="elevation-1 mt-4"
                        :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                        >
                        <template v-slot:[`item.indexOfUser`]="{ index }">
                            {{ index + 1 }}
                            </template>
                        <template v-slot:[`item.id`]="{item}">
                            <v-btn color="#27AB9C" text @click="changePage(item)">
                            <v-icon>mdi-account </v-icon><br>
                            <span> ดูผู้ใช้งานบริษัท</span>
                            </v-btn>
                        </template>
                        <template v-slot:[`item.tax_id`]="{ item }">
                            <span v-if="item.tax_id" color="#27AB9C" > {{ item.tax_id}}</span>
                            <span v-else>-</span>
                        </template>
                        <template v-slot:[`item.name_th`]="{ item }">
                            <span v-if="item.name_th" color="#27AB9C" > {{ item.name_th}}</span>
                            <span v-else>-</span>
                        </template>
                        <template v-slot:[`item.shop_name`]="{ item }">
                            <span v-if="item.shop_name" color="#27AB9C" > {{ item.shop_name}}</span>
                            <span v-else>-</span>
                        </template>
                        </v-data-table>
                    </v-col>
                    </v-col>
                </v-row>
            </v-col>
        </v-row>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      showCountRequest: '',
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขผู้ใช้ภาษี', value: 'tax_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_th', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สาขา', value: 'branch', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'id', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      adminCompanyList: [],
      search: ''
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageUserCompanyMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'manageUserCompany')
        this.$router.push({ path: '/manageUserCompany' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getCompanyData()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getCompanyData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetCompanyData')
      var response = await this.$store.state.ModuleAdminManage.stateGetCompanyData
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.adminCompanyList = response.data.companies
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    changePage (item) {
      if (!this.MobileSize) {
        this.$router.push({ path: `/ManageUserListCompany?company_id=${item.id}&name=${item.name_th}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageUserListCompanyMobile?company_id=${item.id}&name=${item.name_th}` }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
  .theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
    white-space: nowrap !important;
    text-align: center !important;
  }
  .v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
    text-align: center !important;
  }
</style>
