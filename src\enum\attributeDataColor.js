export const attributeManage = {
  attribute_1_value: '',
  attribute_image: {
    id: '',
    image_data: '',
    path: '',
    name: ''
  },
  attribute_2_value: [
    {
      new_sku: '',
      value: '',
      barcode: '',
      show_status: true,
      message_status: 'no_status',
      actual_stock: '',
      effective_stock: '',
      TypeShipping: false,
      min_per_order: '',
      max_per_order: '',
      manage_price: {
        useWholesale: false,
        wholesale: [{
          // index: '',
          minimum: '',
          maximum: '',
          price: ''
        }],
        fake_price: '',
        real_price: '',
        fake_price_vat: '',
        real_price_vat: '',
        vat_include: '',
        vat_default: 'no',
        use_tier: false,
        tier_price: []
      },
      use_shipping_cost: 'yes',
      shipping_cost: [{
        minimum_unit: '1',
        maximum_unit: '',
        shipping_cost: ''
      }],
      volumn: {
        width: '',
        length: '',
        height: '',
        weight: ''
      }
    }
  ]
}
export const attributeDataSize = {
  new_sku: '',
  value: '',
  barcode: '',
  show_status: true,
  message_status: 'no_status',
  actual_stock: '',
  effective_stock: '',
  TypeShipping: false,
  min_per_order: '',
  max_per_order: '',
  manage_price: {
    useWholesale: false,
    wholesale: [{
      // index: '',
      minimum: '',
      maximum: '',
      price: ''
    }],
    fake_price: '',
    real_price: '',
    fake_price_vat: '',
    real_price_vat: '',
    vat_include: '',
    vat_default: 'no',
    use_tier: false,
    tier_price: []
  },
  use_shipping_cost: 'yes',
  shipping_cost: [{
    minimum_unit: '1',
    maximum_unit: '',
    shipping_cost: ''
  }],
  volumn: {
    width: '',
    length: '',
    height: '',
    weight: ''
  }
}
export const attributeDataSizeEdit = {
  new_sku: '',
  value: '',
  barcode: '',
  show_status: true,
  message_status: 'no_status',
  actual_stock: '',
  effective_stock: '',
  product_attribute_id: '-1',
  TypeShipping: false,
  min_per_order: '',
  max_per_order: '',
  manage_price: {
    useWholesale: false,
    wholesale: [{
      // index: '',
      minimum: '',
      maximum: '',
      price: ''
    }],
    fake_price: '',
    real_price: '',
    fake_price_vat: '',
    real_price_vat: '',
    vat_include: '',
    vat_default: 'no',
    use_tier: false,
    tier_price: []
  },
  use_shipping_cost: 'yes',
  shipping_cost: [{
    minimum_unit: '1',
    maximum_unit: '',
    shipping_cost: ''
  }],
  volumn: {
    width: '',
    length: '',
    height: '',
    weight: ''
  }
}

export const option = {
  new_sku: '',
  value: '',
  barcode: '',
  show_status: true,
  message_status: 'no_status',
  actual_stock: '',
  effective_stock: '',
  TypeShipping: false,
  min_per_order: '',
  max_per_order: '',
  manage_price: {
    useWholesale: false,
    wholesale: [{
      // index: '',
      minimum: '',
      maximum: '',
      price: ''
    }],
    fake_price: '',
    real_price: '',
    fake_price_vat: '',
    real_price_vat: '',
    vat_include: '',
    vat_default: 'no',
    use_tier: false,
    tier_price: []
  },
  use_shipping_cost: 'yes',
  shipping_cost: [{
    minimum_unit: '1',
    maximum_unit: '',
    shipping_cost: ''
  }],
  volumn: {
    width: '',
    length: '',
    height: '',
    weight: ''
  }
}

export const attributeObtion = {
  new_sku: '',
  value: '',
  barcode: '',
  show_status: true,
  message_status: 'no_status',
  actual_stock: '',
  effective_stock: '',
  fake_price: '',
  real_price: '',
  fake_price_vat: '',
  real_price_vat: '',
  vat_include: '',
  vat_default: 'no',
  use_tier: true,
  pricetier_1: '',
  pricetier_2: '',
  pricetier_3: '',
  pricetier_4: '',
  pricetier_5: '',
  width: '',
  length: '',
  height: '',
  weight: ''
}

export const removeItem = (item, index) => {
  // console.log(item)
  return item.splice(index, 1)
}

export const CaseSwal = [
  { case: 'Weight', text: 'น้ำหนักสินค้าต้องไม่เกิน 50,000 กรัม' },
  { case: 'Height', text: 'ความยาวของสินค้าต้องไม่เกิน 150 เซนติเมตร' },
  { case: 'Width', text: 'ความกว้างของสินค้าต้องไม่เกิน 150 เซนติเมตร' },
  { case: 'Wide', text: 'ความกว้างของสินค้าต้องไม่เกิน 150 เซนติเมตร' }
]
