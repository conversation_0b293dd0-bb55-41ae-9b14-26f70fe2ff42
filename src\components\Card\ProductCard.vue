<template>
  <v-hover v-slot="{ hover }">
    <v-card class="rounded-lg" :href="pathProductDetail" height="100%" width="230" :elevation="hover ? 8 : 1"
      :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;"
      @click="DetailProduct(itemProduct)">
      <v-img :src="itemProduct.images_URL[0]" height="190" width="214" v-if="itemProduct.images_URL.length !== 0"
        contain class="align-start">
        <v-chip v-if="itemProduct.stock_count === 0 || itemProduct.stock_status === 'out of stock'" class="ma-2" text-color="#D1392B" color="rgba(255, 255, 255)"
          small>
          <v-avatar left color="#D1392B" size="10">
            <v-icon small color="white">mdi-close</v-icon>
          </v-avatar>
          สินค้าหมด
        </v-chip>
        <v-row dense v-else>
          <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
            <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain
              style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
            <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain
              style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
            <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain
              style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
            <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain
              style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
            <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain
              style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'">
            </v-img>
            <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain
              style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'">
            </v-img>
            <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain
              style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'">
            </v-img>
            <!-- <v-img src="@/assets/Tag/E-Receipt.png" height="75" width="60" contain style="margin-left: 10px; margin-top: -20px;" v-else-if="itemProduct.message_status === 'shop_dee_me_kuen'"></v-img> -->
          </v-col>
          <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;"
            v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize">
            <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 47%;">
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 16px; display: block;"
                class="pt-2">{{ parseInt(itemProduct.discount_percent) }}%</span>
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 18px; display: block;">ลด</span>
            </v-img>
          </v-col>
          <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;"
            v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize">
            <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 16px; display: block;"
                class="pt-2">{{ parseInt(itemProduct.discount_percent) }}%</span>
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 18px; display: block;">ลด</span>
            </v-img>
          </v-col> -->
        </v-row>
      </v-img>
      <v-img src="@/assets/NoImage.png" height="200" width="230" contain v-else>
        <v-chip v-if="itemProduct.stock_count === 0 || itemProduct.stock_status === 'out of stock'" class="ma-2"
          text-color="#D1392B" color="rgba(255, 255, 255)" small>
          <v-avatar left color="#D1392B" size="10">
            <v-icon small color="white">mdi-close</v-icon>
          </v-avatar>
          สินค้าหมด
        </v-chip>
        <v-row v-else dense>
          <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
            <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain
              style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
            <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain
              style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
            <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain
              style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
            <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain
              style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
            <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain
              style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'">
            </v-img>
            <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain
              style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'">
            </v-img>
            <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain
              style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'">
            </v-img>
            <v-img src="@/assets/Tag/Event1.png" height="75" width="60" contain style="margin-left: 10px; margin-top: -20px;" v-else-if="itemProduct.message_status === 'shop_dee_me_kuen'"></v-img>
          </v-col>
          <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;"
            v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize">
            <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 47%;">
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 16px; display: block;"
                class="pt-2">{{ parseInt(itemProduct.discount_percent) }}%</span>
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 18px; display: block;">ลด</span>
            </v-img>
          </v-col>
          <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;"
            v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize">
            <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 16px; display: block;"
                class="pt-2">{{ parseInt(itemProduct.discount_percent) }}%</span>
              <span
                style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px; line-height: 18px; display: block;">ลด</span>
            </v-img>
          </v-col> -->
        </v-row>
      </v-img><br />
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <!-- <v-card-text v-bind="attrs" v-on="on">{{ itemProduct.product_name|truncate(26, '...') }}</v-card-text> -->
          <p v-bind="attrs" v-on="on"
            style="font-size: 16px; font-weight: bold; line-height: 26px; color: #333333; width: 214px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; height: 50px;"
            class="mb-0 mt-1 px-2">{{ itemProduct.name | truncate(50, '...') }}</p>
        </template>
        <span>{{ itemProduct.name }}</span>
      </v-tooltip>
      <!-- <p class="pt-1 px-2 mb-1" style="font-size: 16px; line-height: 26px; color: #333333; width: 214px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; height: 50px;"
        v-if="itemProduct.short_description !== null">{{ itemProduct.short_description }}</p>
      <v-card-text class="pt-2 px-2 mb-1"
        style="font-size: 16px; line-height: 26px; color: #333333; width: 214px; display: -webkit-box; -webkit-box-orient: vertical; -webkit-line-clamp: 2; overflow: hidden; height: 50px;"
        v-else></v-card-text> -->
      <v-card-text class="pt-4 pb-0">
        <v-row dense>
          <v-rating v-model="itemProduct.stars" color="#FB9300" background-color="#C4C4C4" empty-icon="$ratingFull"
            half-increments hover small dense readonly></v-rating>
          <v-spacer></v-spacer>
          <!-- <v-btn icon small @click="CheckaddFavorites()" @click.stop="DetailProduct('no')" onclick="return false;" v-if="roleUser.role !== 'purchaser' && itemProduct.isFavorite !== undefined">
            <v-icon color="#D1392B" v-if="itemProduct.isFavorite === false || itemProduct.isFavorite === 'false'">
              mdi-heart-outline</v-icon>
            <v-icon color="#D1392B" v-else>mdi-heart</v-icon>
          </v-btn> -->
        </v-row>
      </v-card-text>
      <v-card-text class="pt-0" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <span
          v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)"
          style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {
              minimumFractionDigits:
                2
            })
          }}</span>
        <div
          v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span><br>
          <span class="specialPrice">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span>
        </div>
        <span
          v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)"
          style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {
              minimumFractionDigits:
                2
            })
          }}</span>
        <div
          v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span><br>
          <span class="specialPrice">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
      </v-card-text>
      <v-card-text class="pt-0" v-if="!MobileSize && !IpadSize && IpadProSize">
        <span
          v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)"
          style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {
              minimumFractionDigits:
                2
            })
          }}</span>
        <div
          v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span><br>
          <span class="specialPriceIPADPro">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span>
        </div>
        <span
          v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)"
          style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {
              minimumFractionDigits:
                2
            })
          }}</span>
        <div
          v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span><br>
          <span class="specialPriceIPADPro">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
      </v-card-text>
      <v-card-text class="pt-0" v-else-if="!MobileSize && IpadSize && !IpadProSize">
        <span
          v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)"
          style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {
              minimumFractionDigits:
                2
            })
          }}</span>
        <div
          v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecreseIPAD">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span><br>
          <span class="specialPriceIPAD">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span>
        </div>
        <span
          v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)"
          style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {
              minimumFractionDigits:
                2
            })
          }}</span>
        <div
          v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecreseIPAD">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span><br>
          <span class="specialPriceIPAD">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined,
              { minimumFractionDigits: 2 })
          }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
      </v-card-text>
      <!-- <span class="ml-4" v-if="itemProduct.give_tier !== 'y'">฿ {{ Number(itemProduct.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
      <div v-else>
        <span style="font-weight: 300" class="priceDecrese ml-4" >฿ {{ Number(itemProduct.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <span class="specialPrice">฿ {{ Number(itemProduct.product_tier_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
      </div><br/> -->
    </v-card>
  </v-hover>
</template>

<script>
import { Decode } from '@/services'
export default {
  props: ['itemProduct'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discription: 'หมวกนิรภัยป้องกันอุบัติหมวกนิรภัยป้องกันอุบัติ',
      rating: 5,
      favorite: false,
      priceSame: false,
      oneData: [],
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN,
      productID: '',
      namesPath: '',
      roleUser: ''
    }
  },
  created () {
    this.$EventBus.$on('checkRole', this.checkRole)
    this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
      this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.id)
    } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
      this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.product_id)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    checkRole () {
      this.roleUser = ''
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    },
    DetailProduct (val) {
      // console.log(val)
      if (val !== 'no') {
        // console.log(val)
        const nameCleaned = val.name.replace(/\s/g, '-')
        // this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.product_id}` } }).catch(() => {})
        const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.product_id}` } })
        window.location.assign(routeData.href, '_blank')
        // this.$router.push({ path: routeData.href })
      }
    },
    CheckaddFavorites () {
      if (localStorage.getItem('oneData') !== null) {
        var ProductID
        if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
          ProductID = this.itemProduct.id
        } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
          ProductID = this.itemProduct.product_id
        }
        this.addFavorites(ProductID)
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'กรุณาเข้าสู่ระบบ เพื่อเพิ่มลงในสินค้าที่ถูกใจของคุณ'
        })
      }
    },
    async addFavorites (val) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
      var companyId
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var data
      if (this.roleUser.role === 'purchaser') {
        data = {
          role_user: this.roleUser.role,
          product_id: val,
          company_id: companyId.company.company_id,
          company_position_id: companyId.position.role_id,
          com_perm_id: companyId.position.com_perm_id
        }
      } else if (this.roleUser.role === 'ext_buyer') {
        data = {
          role_user: this.roleUser.role,
          product_id: val,
          company_id: -1,
          company_position_id: -1,
          com_perm_id: -1
        }
      }
      await this.$store.dispatch('actionsUPSAddFavoriteProduct', data)
      var response = await this.$store.state.ModuleFavoriteProduct.stateAddFavoriteProduct
      // console.log('response favorite =======>', response)
      if (response.result === 'SUCCESS') {
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('getResultSearch')
        this.$EventBus.$emit('getNewProduct')
        this.$EventBus.$emit('getProductRecommentBrand')
        if (this.$router.currentRoute.name === 'DetailProduct') {
          this.$EventBus.$emit('getProductDetail')
        }
        this.$EventBus.$emit('ClickFavorites')
        this.$EventBus.$emit('getAllNewProduct')
        this.$EventBus.$emit('getAllBestSeller')
        this.$EventBus.$emit('getAllSameProductShop')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductSame')
        this.$EventBus.$emit('getHomepageItems')
        this.$EventBus.$emit('getSellerShopPage')
        this.$EventBus.$emit('getAllProductShop')
        this.$EventBus.$emit('getRecommendedProducts')
        this.$EventBus.$emit('getAllNewProductInShop')
        this.$EventBus.$emit('getAllBestSellerInShop')
        this.$EventBus.$emit('getAllRecomenProductShopInShop')
        this.$EventBus.$emit('getAllProductShopInShop')
      }
    }
  }
}
</script>

<style scoped>
.priceDecrese {
  font-size: 14px;
  text-decoration: line-through;
  color: #929292;
  margin-right: 0px;
}

.specialPrice {
  font-size: 18px;
  color: red;
  font-weight: 700;
}

.priceDecreseIPAD {
  font-size: 16px;
  text-decoration: line-through;
  color: #636363;
  font-weight: 500;
  line-height: 22px;
  margin-right: 0px;
}

.specialPriceIPAD {
  font-size: 22px;
  font-weight: bold;
  color: #D1392B;
  line-height: 40px;
}

.specialPriceIPADPro {
  font-size: 20px;
  font-weight: bold;
  color: #D1392B;
  line-height: 40px;
}
</style>
