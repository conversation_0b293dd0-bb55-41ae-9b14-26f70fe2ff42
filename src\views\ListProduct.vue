<template>
  <v-container grid-list-xs>
    <v-overlay :value="overlay2">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <a-row type="flex" :gutter="[16, 8]">
      <a-col :span="24" style="text-align:center">
        <span class="display-1">{{header}}</span>
      </a-col>
      <a-col :span="24">
        <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} รายการสำหรับ "{{ header }}"</span>
      </a-col>
      <a-col :span="24" style="padding: 0; margin-top: -20px;">
        <a-divider></a-divider>
      </a-col>
      <div v-if="AllProduct.length !== 0">
        <!-- <pre>{{AllProduct}}</pre> -->
        <a-col :span="24" :md="4" :sm="12" :xs="24" v-for="(item,index) in AllProduct" :key="index">
          <CardProducts :itemProduct="item" />
        </a-col>
      </div>
      <div v-else>
        <h2>ยังไม่มีรายการสินค้า{{ header }}</h2>
      </div>
    </a-row>
  </v-container>
</template>
<script>
import { Col, Divider, Row } from 'ant-design-vue'
const FakeData = []
for (let i = 0; i < 30; i++) {
  FakeData.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: 'https://picsum.photos/id/1039/600/600'
  })
}
export default {
  components: {
    'a-row': Row,
    'a-col': Col,
    'a-divider': Divider,
    CardProducts: () => import('@/components/Card/ProductCard')
  },
  data () {
    return {
      header: '',
      FakeData,
      overlay2: true,
      productCount: null,
      AllProduct: []
    }
  },
  created () {
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    this.header = this.$router.currentRoute.params.data
    if (this.header === 'สินค้าเข้าใหม่') {
      this.$EventBus.$on('getAllNewProduct', this.getAllNewProduct)
      this.getAllNewProduct()
    } else if (this.header === 'สินค้าขายดี') {
      this.$EventBus.$on('getAllBestSeller', this.getAllBestSeller)
      this.getAllBestSeller()
    }
  },
  // mounted () {
  //   this.$EventBus.$on('getAllNewProduct', this.getAllNewProduct)
  //   this.$EventBus.$on('getAllBestSeller', this.getAllBestSeller)
  //   this.$on('hook:beforeDestroy', () => {
  //     this.$EventBus.$off('getAllNewProduct')
  //     this.$EventBus.$off('getAllBestSeller')
  //   })
  // },
  methods: {
    async getAllNewProduct () {
      var data
      if (localStorage.getItem('roleUser') !== null) {
        data = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        data = {
          role: 'ext_buyer'
        }
      }
      await this.$store.dispatch('actionMoreNewProductHome', data)
      var response = await this.$store.state.ModuleHompage.stateMoreNewProductHome
      // console.log('response all New product=======>', response)
      if (response.result === 'SUCCESS') {
        if (response.data.length !== 0) {
          this.overlay2 = false
          this.AllProduct = response.data
          this.productCount = response.data.length
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
      }
    },
    async getAllBestSeller () {
      var data
      if (localStorage.getItem('roleUser') !== null) {
        data = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        data = {
          role: 'ext_buyer'
        }
      }
      await this.$store.dispatch('actionMoreBestSeller', data)
      var response = await this.$store.state.ModuleHompage.stateMoreBestSeller
      // console.log('response all Best Seller =======>', response)
      if (response.result === 'SUCCESS') {
        if (response.data !== 'No products ready to sell.') {
          this.overlay2 = false
          this.AllProduct = response.data
          this.productCount = response.data.length
        } else {
          this.overlay2 = false
          this.AllProduct = []
          this.productCount = 0
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: `${response.message}`
        })
      }
    }
  }
}
</script>
