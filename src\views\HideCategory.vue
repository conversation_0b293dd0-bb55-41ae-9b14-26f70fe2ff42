
<template>
  <div class="ma-5">
    <h1>แค็ตตาล็อก</h1>
    <v-card>
        <!-- <v-card-title>แค็ตตาล็อก</v-card-title> -->
        <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
     <v-row no-gutters justify="end">
      <v-col cols="12" class="py-0">
        <v-tabs >
          <v-tab v-for="item in OrderName" :key="item.key" :tab="item.name">
            {{item.name}}
          </v-tab>
          <v-tab-item>
            <ShowCategory/>
          </v-tab-item>
          <v-tab-item>
            <HidenCategory/>
          </v-tab-item>
        </v-tabs>
      </v-col>
     </v-row>
    </v-card>
  </div>
</template>
<script>
export default {
  data () {
    return {
      overlay: false,
      OrderName: [
        { key: 0, name: 'รายการสินค้า' },
        { key: 1, name: 'รายการที่ซ่อน' }
      ]
    }
  },
  components: {
    HidenCategory: () => import('@/components/HidenCategory'),
    ShowCategory: () => import('@/components/ShowCategory')
  }
}
</script>
