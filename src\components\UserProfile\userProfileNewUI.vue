<template>
  <v-container :class="!MobileSize ? 'pt-0 px-0 pb-8' : ''" style="border: 1px solid rgb(242, 242, 242);">
    <v-card width="100%" height="100%"  style="border-radius: 8px;" :elevation="MobileSize ? 1 : 0" :class="MobileSize ? 'my-4' : 'py-4 px-4'">
        <v-row>
          <v-col cols="12" style="display: flex; align-items: center;">
            <v-btn v-if="MobileSize" icon style="background-color: #edfcf7;" width="24px" height="24px" class="mr-2 ml-2 pa-0" @click="backtoUser()"><v-icon small color="#27AB9C">mdi-chevron-left</v-icon></v-btn><span style="font-weight: 700; font-size: 24px;">{{ $t('NewUserProfileUI.UserProfile') }}</span>
            <v-spacer></v-spacer>
            <v-btn v-if="!MobileSize" color="#3ec6b6" outlined rounded :height="IpadSize ? '40' : '48'" style="box-shadow: 0px 0px 1px 0px #28293D0A; box-shadow: 0px 2px 4px 0px #60617029;" @click="openDialogEdit"><img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/edit.png" style="width: 15px; height: 15px;" /><span style="font-weight: 500; font-size: 16px; letter-spacing: normal;">{{ $t('NewUserProfileUI.Edit') }}</span></v-btn>
          </v-col>
          <v-col v-if="MobileSize" cols="12" style="display: flex; align-items: center;">
            <v-btn width="96%" class="mx-auto" color="#3ec6b6" outlined rounded height="40" style="box-shadow: 0px 0px 1px 0px #28293D0A; box-shadow: 0px 2px 4px 0px #60617029;" @click="openDialogEdit"><img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/edit.png" style="width: 15px; height: 15px;" /><span style="font-weight: 500; font-size: 16px; letter-spacing: normal;">{{ $t('NewUserProfileUI.Edit') }}</span></v-btn>
          </v-col>
        </v-row>
        <v-row :class="MobileSize ? 'mt-4 ml-1 mr-1' : 'mt-4'">
          <v-col cols="12" style="display: flex; align-items: center;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
            <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/info.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700;" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'">{{ $t('NewUserProfileUI.PersonalInformation') }}</span>
          </v-col>
          <v-col v-if="!MobileSize && !IpadSize" cols="12" style="display: flex; align-items: center;">
            <div class="mr-8" style="position: relative; display: inline-block;">
               <v-avatar size="150px">
                  <img v-if="image === null || image === ''" src="@/assets/ImageINET-Marketplace/ICONProfile/noprofile.png" />
                  <img v-else :src="image" />
                </v-avatar>
                <v-btn icon style="background-color: #EAF6F2; z-index: 2; position: absolute; bottom: 0; right: 10px; cursor: pointer;" width="28px" height="28px">
                  <input
                    ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChange($event)"
                    style="opacity: 0; position: absolute; left: 0; top: 0; width: 28px; height: 28px; cursor: pointer; z-index: 1;"
                  />
                  <img src="@/assets/ImageINET-Marketplace/ICONProfile/edit.png" style="width: 15px; height: 15px; cursor: pointer;" /></v-btn>
            </div>
            <div class="d-flex flex-column">
              <span style="font-weight: 700; font-size: 28px;">{{ username }}</span>
              <div class="d-flex align-center" style="margin-top: 20px;">
                <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_name.png" style="width: 24px; height: 24px;" />
                <span class="mr-2" style="font-weight: 500; font-size: 16px;">{{ $t('NewUserProfileUI.FirstName') }} - {{ $t('NewUserProfileUI.LastName') }} :</span>
                <span style="font-weight: 700; font-size: 16px;">{{ fullname }}</span>
              </div>
              <div class="d-flex align-center" style="margin-top: 20px;">
                <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_phone.png" style="width: 24px; height: 24px;" />
                <span class="mr-2" style="font-weight: 500; font-size: 16px;">{{ $t('NewUserProfileUI.Phone') }} :</span>
                <span style="font-weight: 700; font-size: 16px;">{{ userdetail.phone === null ? '-' : userdetail.phone }}</span>
              </div>
              <div class="d-flex align-center" style="margin-top: 20px;">
                <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_mail.png" style="width: 24px; height: 24px;" />
                <span class="mr-2" style="font-weight: 500; font-size: 16px;">{{ $t('NewUserProfileUI.Email') }} :</span>
                <span style="font-weight: 700; font-size: 16px;">{{userdetail.email === null ? '-' :userdetail.email }}</span>
              </div>
            </div>
          </v-col>
          <!-- เพิ่ม truncate แล้ว -->
          <v-col v-if="MobileSize" cols="12" style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
            <div style="position: relative; display: inline-block;">
               <v-avatar size="150px">
                  <img v-if="image === null || image === ''" src="@/assets/ImageINET-Marketplace/ICONProfile/noprofile.png" />
                  <img v-else :src="image" />
                </v-avatar>
                <v-btn icon style="background-color: #EAF6F2; z-index: 2; position: absolute; bottom: 0; right: 10px;" width="28px" height="28px">
                  <input
                    ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChange($event)"
                    style="opacity: 0; position: absolute; left: 0; top: 0; width: 28px; height: 28px; cursor: pointer; z-index: 1;"
                  />
                  <img src="@/assets/ImageINET-Marketplace/ICONProfile/edit.png" style="width: 15px; height: 15px;" /></v-btn>
            </div>
            <div class="d-flex flex-column justify-center pl-3">
              <span class="mt-3" style="font-weight: 700; font-size: 24px; text-align: center;">{{ username }}</span>
              <div class="d-flex align-center ml-1 mr-1" style="margin-top: 20px; width: 90vw;">
                <div style="width: 50%;">
                  <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_name.png" style="width: 24px; height: 24px;" />
                  <span class="mr-2" style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.FirstName') }} - {{ $t('NewUserProfileUI.LastName') }} :</span>
                </div>
                <div class="d-inline-block text-truncate" style="width: 46%;">
                  <span style="font-weight: 700; font-size: 15px;">{{ fullname }}</span>
                </div>
              </div>
              <div class="d-flex align-center ml-1 mr-1" style="margin-top: 20px; width: 90vw;">
                <div style="width: 50%;">
                  <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_phone.png" style="width: 24px; height: 24px;" />
                  <span class="mr-2" style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.Phone') }} :</span>
                </div>
                <div class="d-inline-block text-truncate" style="width: 46%;">
                  <span style="font-weight: 700; font-size: 15px;">{{ userdetail.phone === null ? '-' : userdetail.phone }}</span>
                </div>
              </div>
              <div class="d-flex align-center ml-1 mr-1" style="margin-top: 20px; width: 90vw;">
                <div style="width: 50%;">
                  <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_mail.png" style="width: 24px; height: 24px;" />
                  <span class="mr-2" style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.Email') }} :</span>
                </div>
                <div class="d-inline-block text-truncate" style="width: 46%;">
                  <span style="font-weight: 700; font-size: 15px;">{{userdetail.email === null ? '-' :userdetail.email }}</span>
                </div>
              </div>
            </div>
          </v-col>
          <!-- เพิ่ม truncate แล้ว -->
          <v-col v-if="IpadSize" cols="12" style="display: flex; align-items: center; justify-content: center; flex-direction: column;">
            <div style="position: relative; display: inline-block;">
               <v-avatar size="150px">
                  <img v-if="image === null || image === ''" src="@/assets/ImageINET-Marketplace/ICONProfile/noprofile.png" />
                  <img v-else :src="image" />
                </v-avatar>
                <v-btn icon style="background-color: #EAF6F2; z-index: 2; position: absolute; bottom: 0; right: 10px;" width="28px" height="28px">
                  <input
                    ref="fileInput" type="file" accept="image/jpeg, image/jpg, image/png" @change="onFileChange($event)"
                    style="opacity: 0; position: absolute; left: 0; top: 0; width: 28px; height: 28px; cursor: pointer; z-index: 1;"
                  />
                  <img src="@/assets/ImageINET-Marketplace/ICONProfile/edit.png" style="width: 15px; height: 15px;" /></v-btn>
            </div>
            <div class="d-flex flex-column justify-center" style="width: 100%; ">
              <span class="mt-3" style="font-weight: 700; font-size: 24px; text-align: center;">{{ username }}</span>
              <div class="d-flex align-center mr-1" style="margin-top: 20px;">
                <div style="width: 52%;">
                  <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_name.png" style="width: 24px; height: 24px;" />
                  <span class="mr-2" style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.FirstName') }} - {{ $t('NewUserProfileUI.LastName') }} :</span>
                </div>
                <div class="d-inline-block text-truncate" style="width: 48%;">
                  <span style="font-weight: 700; font-size: 15px;">{{ fullname }}</span>
                </div>
              </div>
              <div class="d-flex align-center mr-1" style="margin-top: 20px;">
                <div style="width: 52%;">
                  <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_phone.png" style="width: 24px; height: 24px;" />
                  <span class="mr-2" style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.Phone') }} :</span>
                </div>
                <div class="d-inline-block text-truncate" style="width: 48%;">
                  <span style="font-weight: 700; font-size: 15px;">{{ userdetail.phone === null ? '-' : userdetail.phone }}</span>
                </div>
              </div>
              <div class="d-flex align-center mr-1" style="margin-top: 20px;">
                <div style="width: 52%;">
                  <img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONProfile/icon_mail.png" style="width: 24px; height: 24px;" />
                  <span class="mr-2" style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.Email') }} :</span>
                </div>
                <div class="d-inline-block text-truncate" style="width: 48%;">
                  <span style="font-weight: 700; font-size: 15px;">{{userdetail.email === null ? '-' :userdetail.email }}</span>
                </div>
              </div>
            </div>
          </v-col>
        </v-row>
        <!-- ส่วน บัญชีของฉัน -->
        <v-row v-if="!MobileSize && !IpadSize">
          <v-col cols="12" style="display: flex; align-items: center;">
            <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/account.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; font-size: 18px;">{{ $t('NewUserProfileUI.MyAccount') }}</span>
          </v-col>
          <div style="width: 48%; padding-left: 12px;" class="d-flex justify-start mr-3 mb-3 mt-1">
            <div :style="IpadProSize ? 'width: 40%;' : 'width: 154px;'" class="mr-2">
              <span style="font-weight: 500; font-size: 16px;">{{ $t('NewUserProfileUI.BankName') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" :style="IpadProSize ? 'width: 60%;' : ''">
              <span style="font-weight: 600; font-size: 16px;">{{ userdetail.bank_name !== '' || userdetail.bank_name !== '-' || userdetail.bank_name !== null ? userdetail.bank_name : '-' }}</span>
            </div>
          </div>
          <div style="width: 48%; padding-left: 12px;" class="d-flex justify-start mb-3 mt-1">
            <div :style="IpadProSize ? 'width: 40%;' : 'width: 154px;'" class="mr-2">
              <span style="font-weight: 500; font-size: 16px;">{{ $t('NewUserProfileUI.BankAccountNumber') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" :style="IpadProSize ? 'width: 60%;' : ''">
              <span style="font-weight: 600; font-size: 16px;">{{ userdetail.bank_no !== '' || userdetail.bank_no !== '-' || userdetail.bank_no !== null ? userdetail.bank_no : '-' }}</span>
            </div>
          </div>
          <div style="width: 48%; padding-left: 12px;" class="d-flex justify-start mr-3">
            <div :style="IpadProSize ? 'width: 40%;' : 'width: 154px;'" class="mr-2">
              <span style="font-weight: 500; font-size: 16px;">{{ $t('NewUserProfileUI.BankAccountName') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" :style="IpadProSize ? 'width: 60%;' : ''">
              <span style="font-weight: 600; font-size: 16px;">{{ userdetail.bank_user_name !== '' || userdetail.bank_user_name !== '-' || userdetail.bank_user_name !== null ? userdetail.bank_user_name : '-' }}</span>
            </div>
          </div>
        </v-row>
        <!-- ส่วน บัญชีของฉัน -->
        <v-row class="ml-1 mr-1" v-if="MobileSize">
          <v-col cols="12" style="display: flex; align-items: center;" class="pa-0 py-2 px-1 mt-2">
            <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/account.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; font-size: 16px;">{{ $t('NewUserProfileUI.MyAccount') }}</span>
          </v-col>
          <div class="d-flex align-center ml-1 mr-1" style="margin-top: 15px; width: 90vw;">
            <div style="width: 50%;">
              <span style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.BankName') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" style="width: 50%;">
              <span style="font-weight: 600; font-size: 15px;">{{ userdetail.bank_name !== '' || userdetail.bank_name !== '-' || userdetail.bank_name !== null ? userdetail.bank_name : '-' }}</span>
            </div>
          </div>
          <div class="d-flex align-center ml-1 mr-1" style="margin-top: 15px; width: 90vw;">
            <div style="width: 50%;">
              <span style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.BankAccountNumber') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" style="width: 50%;">
              <span style="font-weight: 600; font-size: 15px;">{{ userdetail.bank_no !== '' || userdetail.bank_no !== '-' || userdetail.bank_no !== null ? userdetail.bank_no : '-' }}</span>
            </div>
          </div>
          <div class="d-flex align-center ml-1 mr-1" style="margin: 15px 0 20px 0; width: 90vw;">
            <div style="width: 50%;">
              <span style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.BankAccountName') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" style="width: 50%;">
              <span style="font-weight: 600; font-size: 15px;">{{ userdetail.bank_user_name !== '' || userdetail.bank_user_name !== '-' || userdetail.bank_user_name !== null ? userdetail.bank_user_name : '-' }}</span>
            </div>
          </div>
        </v-row>
        <!-- ส่วน บัญชีของฉัน -->
        <v-row class="mr-1" v-if="IpadSize">
          <v-col cols="12" style="display: flex; align-items: center;">
            <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/account.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; font-size: 18px;">{{ $t('NewUserProfileUI.MyAccount') }}</span>
          </v-col>
          <div class="d-flex align-center ml-1 pl-2 mr-1 d-inline-block text-truncate" style="margin-top: 15px; width: 90vw;">
            <div style="width: 52%;">
              <span style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.BankName') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" style="width: 48%;">
              <span style="font-weight: 600; font-size: 15px;">{{ userdetail.bank_name !== '' || userdetail.bank_name !== '-' || userdetail.bank_name !== null ? userdetail.bank_name : '-' }}</span>
            </div>
          </div>
          <div class="d-flex align-center ml-1 pl-2 mr-1 d-inline-block text-truncate" style="margin-top: 15px; width: 90vw;">
            <div style="width: 52%;">
              <span style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.BankAccountNumber') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" style="width: 48%;">
              <span style="font-weight: 600; font-size: 15px;">{{ userdetail.bank_no !== '' || userdetail.bank_no !== '-' || userdetail.bank_no !== null ? userdetail.bank_no : '-' }}</span>
            </div>
          </div>
          <div class="d-flex align-center ml-1 pl-2 mr-1 d-inline-block text-truncate" style="margin: 15px 0 20px 0; width: 90vw;">
            <div style="width: 52%;">
              <span style="font-weight: 500; font-size: 15px;">{{ $t('NewUserProfileUI.BankAccountName') }} :</span>
            </div>
            <div class="d-inline-block text-truncate" style="width: 48%;">
              <span style="font-weight: 600; font-size: 15px;">{{ userdetail.bank_user_name !== '' || userdetail.bank_user_name !== '-' || userdetail.bank_user_name !== null ? userdetail.bank_user_name : '-' }}</span>
            </div>
          </div>
        </v-row>
    </v-card>
    <!-- Dialog Edit -->
    <v-dialog v-model="dialogEdit" persistent :width="MobileSize ? '350' : '600'" style="border-radius: 24px;">
      <v-card style="background: #FFFFFF; border-top-left-radius: 24px; border-top-right-radius: 24px; z-index: 1;" width="100%" height="100%">
          <v-img height="120px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')" style="opacity: 0.93;">
            <v-app-bar flat color="rgba(0, 0, 0, 0)" style="margin-top: 20px;" class="mx-4">
              <v-spacer></v-spacer>
              <v-toolbar-title style="color: #FFFFFF; font-size: 24px; font-weight: 700; text-align: center;">{{ $t('NewUserProfileUI.Edit') }}</v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn style="" width="24" height="24" color="#fff" icon
                @click="closeDialogEdit">
                <v-icon size="16">mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
      </v-card>
      <v-card style="background: #FFFFFF; border-top-left-radius: 24px; border-top-right-radius: 24px; z-index: 3;" width="100%" height="100%">
        <v-card-text style="border-radius: 24px; margin-top: -20px;">
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center">
              <v-col cols="12" style="display: flex; align-items: center;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/info.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; color: #333333;" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'">{{ $t('NewUserProfileUI.PersonalInformation') }}</span>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('NewUserProfileUI.FirstName') }}</span>
                <v-text-field class="mb-3" dense v-model="firstName" outlined :placeholder="this.$t('NewUserProfileUI.EnterFirstName')" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" maxlength="30" :rules="Rules.firstname"></v-text-field>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('NewUserProfileUI.LastName') }}</span>
                <v-text-field class="mb-3" dense v-model="lastName" outlined :placeholder="this.$t('NewUserProfileUI.EnterLastName')" @keypress="CheckSpacebar($event)" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" maxlength="30" :rules="Rules.lastname"></v-text-field>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('NewUserProfileUI.Phone') }}</span>
                <v-text-field class="mb-3" disabled dense v-model="phone" outlined :placeholder="this.$t('NewUserProfileUI.EnterPhone')" background-color="#ffffff00" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" maxlength="10" :rules="Rules.phone"></v-text-field>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('NewUserProfileUI.Email') }}</span>
                <v-text-field class="mb-3" disabled dense v-model="email" outlined :placeholder="this.$t('NewUserProfileUI.EnterEmail')" background-color="#ffffff00" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.email"></v-text-field>
              </v-col>
              <!-- ส่วน บัญชีของฉัน -->
              <!-- <v-col cols="12" style="display: flex; align-items: center;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/account.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; color: #333333;" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'">บัญชีของฉัน</span>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">ชื่อบัญชี</span>
                <v-text-field class="mb-3" dense v-model="bankUsername" outlined placeholder="ระบุชื่อบัญชี" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankusername"></v-text-field>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">ธนาคาร</span>
                <v-select class="mb-3" dense v-model="bankCode" :items="listBank" item-value="code" item-text="name" outlined placeholder="เลือกธนาคาร" @change="handleBankName" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankname"></v-select>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">เลขบัญชี</span>
                <v-text-field class="mb-3" dense v-model="bankNo" outlined placeholder="ระบุเลขบัญชี" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankno" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 15)"></v-text-field>
              </v-col> -->
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
      <v-card class="pt-5" style="background: #FFFFFF; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px; z-index: 1; margin-top: -10px; background-color: #F5FCFB;" elevation="0" width="100%" height="88px">
        <v-card-actions>
          <v-row dense class="pb-4 mx-2">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogEdit">{{ $t('NewUserProfileUI.Cancel') }}</v-btn>
            <v-spacer></v-spacer>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="dialogEdit = false; dialogConfirm = true">{{ $t('NewUserProfileUI.Save') }}</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog Confirm -->
    <v-dialog v-model="dialogConfirm" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar color="#FFFBF5" height="240" dark dense elevation="0" width="100%">
          <div style="width: 100%;">
            <div class="d-flex justify-end">
              <v-btn width="32" icon dark @click="dialogConfirm = false">
              <v-icon size="20" color="#CCCCCC">mdi-close</v-icon>
            </v-btn>
            </div>
            <div class="d-flex justify-center" style="margin-top: -20px;">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONProfile/warning.png')" style="max-width: 196px;" />
            </div>
          </div>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-2">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: 24px; font-weight: 700; line-height: 3; color: #333333;">{{ $t('NewUserProfileUI.EditUserProfile') }}</span><br>
              <span style="font-size: 16px; font-weight: 400;">{{ $t('NewUserProfileUI.CheckedInformation') }}</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4 mb-2">
            <v-btn width="150" height="40" outlined rounded color="#27AB9C" @click="dialogConfirm = false; dialogEdit = true" class="mr-2">{{ $t('NewUserProfileUI.Cancel') }}</v-btn>
            <v-btn width="150" height="40" rounded color="#27AB9C" class="white--text" @click="EditAccount('data')">{{ $t('NewUserProfileUI.Confirm') }}</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog Success -->
    <v-dialog v-model="dialogSuccess" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar color="#F8FFF5" height="240" dark dense elevation="0" width="100%">
          <div style="width: 100%;">
            <div class="d-flex justify-end">
              <v-btn width="32" icon dark @click="dialogSuccess = false">
              <v-icon size="20" color="#CCCCCC">mdi-close</v-icon>
            </v-btn>
            </div>
            <div class="d-flex justify-center" style="margin-top: -20px;">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONProfile/success.png')" style="max-width: 196px;" />
            </div>
          </div>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-2">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: 24px; font-weight: 700; line-height: 3; color: #333333;">{{ $t('NewUserProfileUI.EditSuccess') }}</span><br>
              <span style="font-size: 16px; font-weight: 400;">{{ $t('NewUserProfileUI.SuccessEditYourData') }}</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4 mb-2">
            <v-btn width="320" height="40" rounded color="#27AB9C" class="white--text" @click="dialogSuccess = false">{{ $t('NewUserProfileUI.Confirm') }}</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      userdetail: [],
      firstName: '',
      lastName: '',
      phone: '',
      email: '',
      Rules: {
        firstname: [
          v => !!v || this.$t('NewUserProfileUI.PleaseEnterName'),
          v => v.length <= 30 || this.$t('NewUserProfileUI.NotEnterMoreName'),
          v => /[^฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || this.$t('NewUserProfileUI.OnlyLetterEnter')
        ],
        lastname: [
          v => !!v || this.$t('NewUserProfileUI.PleaseEnterSurName'),
          v => v.length <= 30 || this.$t('NewUserProfileUI.NotEnterMoreSurName'),
          v => /[^฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || this.$t('NewUserProfileUI.OnlyLetterEnter')
        ],
        // ยังไม่ทำ 2 ภาษาเพราะส่วนนี้ไม่สามารถแก้ไขข้อมูลได้
        phone: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        // ยังไม่ทำ 2 ภาษาเพราะส่วนนี้ไม่สามารถแก้ไขข้อมูลได้
        email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ],
        // ยังไม่ทำ 2 ภาษาเพราะส่วนนี้มีหน้าสำหรับแก้ไขอยู่แล้ว
        bankusername: [
          v => !!v || 'กรุณากรอกชื่อบัญชีธนาคาร',
          v => /[^฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || 'กรอกได้เฉพาะตัวอักษรเท่านั้น'
        ],
        // ยังไม่ทำ 2 ภาษาเพราะส่วนนี้มีหน้าสำหรับแก้ไขอยู่แล้ว
        bankname: [
          v => !!v || 'กรุณาเลือกธนาคาร'
        ],
        // ยังไม่ทำ 2 ภาษาเพราะส่วนนี้มีหน้าสำหรับแก้ไขอยู่แล้ว
        bankno: [
          v => !!v || 'กรุณากรอกเลขบัญชีธนาคาร',
          v => v.length > 9 || 'กรุณากรอกเลขบัญชีธนาคาร'
        ]
      },
      fullname: '',
      image: '',
      dialogEdit: false,
      dialogConfirm: false,
      dialogSuccess: false,
      listBank: [],
      bankUsername: '',
      bankName: '',
      bankCode: '',
      bankNo: '',
      username: '',
      image_url: ''
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAccount')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.getProfile()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/userprofileDetail' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userprofile' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    async getListBank () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      // console.log(response)
      if (response.code === 200) {
        this.listBank = response.data
      }
      this.$store.commit('closeLoader')
    },
    async openDialogEdit () {
      this.firstName = this.userdetail.first_name_th
      this.lastName = this.userdetail.last_name_th
      this.phone = this.userdetail.phone
      this.email = this.userdetail.email
      this.getListBank()
      this.bankCode = this.userdetail.bank_code
      this.bankUsername = this.userdetail.bank_user_name
      this.bankNo = this.userdetail.bank_no
      this.dialogEdit = true
    },
    closeDialogEdit () {
      this.dialogEdit = false
      this.firstName = ''
      this.lastName = ''
      this.phone = ''
      this.email = ''
      this.bankName = ''
      this.bankUsername = ''
      this.bankNo = ''
      this.bankCode = ''
    },
    async getProfile () {
      this.$store.commit('openLoader')
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        this.oneUserType = onedata.user.type_user
        // console.log('type user', this.oneUserType)
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var data = {
          role_user: dataRole.role
        }
        await this.$store.dispatch('actionsUserDetailPage', data)
        const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
        // console.log('userdetail data', userdetail)
        if (userdetail.message !== 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.userdetail = userdetail.data[0]
          this.image = this.userdetail.img_path
          // Username
          if (this.userdetail.username !== null && this.userdetail.username_oneid === null) {
            this.username = this.userdetail.username
          } else if (this.userdetail.username === null && this.userdetail.username_oneid !== null) {
            this.username = this.userdetail.username_oneid
          } else if (this.userdetail.username !== null && this.userdetail.username_oneid !== null) {
            this.username = this.userdetail.username
          } else {
            this.username = this.$t('NewUserProfileUI.NoUsername')
          }
          // Full name
          if ((this.userdetail.first_name_th === '' || this.userdetail.first_name_th === null) && (this.userdetail.last_name_th === '' || this.userdetail.last_name_th === null)) {
            this.fullname = this.$t('NewUserProfileUI.NoFullname')
          } else {
            this.fullname = this.userdetail.first_name_th + ' ' + this.userdetail.last_name_th
          }
          // if (this.userdetail.address_data !== undefined) {
          //   this.userAddress = this.userdetail.address_data[0]
          // } else {
          //   this.userAddress = []
          // }
          // this.userShop = this.userdetail.shop_data[0]
          // console.log('userdetail data', userdetail, this.userAddress, this.userShop)
        } else {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
          // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timer: 1500 })
          // window.location.assign('/')
        }
      } else {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/ ' }).catch(() => {})
      }
    },
    handleBankName (val) {
      this.bankCode = val
      const selected = this.listBank.find(b => b.code === this.bankCode)
      this.bankName = selected ? selected.name : ''
      // console.log('bankCode', this.bankCode)
      // console.log('bankName', this.bankName)
    },
    async onFileChange (event) {
      if (event.target.files && event.target.files.length > 0) {
        this.$store.commit('openLoader')
        const file = event.target.files[0]
        if (['image/jpeg', 'image/jpg', 'image/png'].includes(file.type)) {
          const url = URL.createObjectURL(file)
          this.image_url = url
          const reader = new FileReader()
          reader.onload = async () => {
            const base64 = reader.result.split(',')[1]
            const data = {
              image: [base64],
              type: 'user_profile'
            }
            await this.$store.dispatch('actionsUploadToS3', data)
            const response = this.$store.state.ModuleShop.stateUploadToS3
            if (response.message === 'List Success.') {
              this.image = response.data.list_path[0].path
              this.EditAccount('img')
            }
          }
          reader.readAsDataURL(file)
          this.$forceUpdate()
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: this.$t('NewUserProfileUI.PleaseAddImage'),
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    // ใช้
    async EditAccount (status) {
      this.$store.commit('openLoader')
      var data = {}
      if (status === 'img') {
        data = {
          first_name_th: '',
          last_name_th: '',
          bank_user_name: '',
          bank_name: '',
          bank_no: '',
          bank_code: '',
          image: this.image
        }
      } else if (status === 'data') {
        data = {
          first_name_th: this.firstName,
          last_name_th: this.lastName,
          bank_user_name: this.bankUsername,
          bank_name: this.bankName,
          bank_no: this.bankNo,
          bank_code: this.bankCode,
          image: this.image
        }
      }
      await this.$store.dispatch('actionsEditUserProfile', data)
      var responseEditAccount = await this.$store.state.ModuleUser.stateEditUserProfile
      // console.log(responseEditAccount, 'responseEditAccount')
      if (responseEditAccount.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        if (status === 'data') {
          this.dialogConfirm = false
          this.dialogSuccess = true
        }
        this.getProfile()
        this.$EventBus.$emit('getUserDetail')
        // window.location.reload()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', title: this.$t('NewUserProfileUI.UnableProceed'), showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>

<style scoped>
::v-deep .theme--light.v-input--is-disabled {
  background-color: #F2F2F2;
  color: #989898;
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
