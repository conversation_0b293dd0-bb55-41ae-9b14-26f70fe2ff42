<template>
  <div>
    <!-- <v-container grid-list-xs> -->
    <v-card elevation="0" width="100%" height="100%" :class="IpadSize ? 'px-0 py-0' : '' ">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">{{ $t('MyOrders.title') }}</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else>
        <v-icon color="#1AB759" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> {{ $t('MyOrders.title') }}
      </v-card-title>
      <!-- <v-overlay :value="overlay">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay> -->
      <v-row no-gutters>
        <!-- <v-col cols="12" class="py-0">
          <a-tabs @change="SelectDetailOrder">
            <a-tab-pane key="ทั้งหมด"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{
            countOrderAll }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ยังไม่ชำระเงิน"><span slot="tab">ยังไม่ชำระเงิน <a-tag color="#E9A016" style="border-radius: 8px;">{{
            countOrderNotpaid }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ชำระเงินสำเร็จ"><span slot="tab">ชำระเงินสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{
            countOrderSuccess }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ชำระเงินไม่สำเร็จ"><span slot="tab">ชำระเงินไม่สำเร็จ <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderFail }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ยกเลิก"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col> -->
        <v-col v-if="disableTable === true" cols="12" md="12" sm="12" :class="!MobileSize ? 'pl-0 pr-0 mb-3' : 'pl-2 pr-2 mb-3'">
          <v-row no-gutters>
            <v-col :cols="MobileSize? '12':'5'" md="4" sm="12" class="px-2">
              <v-text-field v-model="search" @keyup="searchText(search)" dense hide-details outlined :placeholder="$t('MyOrders.search')" style="border-radius: 8px;">
                <v-icon slot="append">mdi-magnify</v-icon>
              </v-text-field>
            </v-col>
            <v-col :cols="MobileSize? '12':'3'" md="4" sm="12" :class="IpadSize ? 'px-2' : 'px-0'">
              <v-row no-gutters :class="MobileSize ? 'my-4' : IpadSize ? 'my-4' : ''">
                <v-col cols="4" class="pt-2 pl-0">
                  <span class="fontDeskSearch">{{ $t('MyOrders.orderStatus') }} :</span>
                </v-col>
                <v-col cols="8">
                  <v-select
                    style="border-radius: 8px;"
                    outlined
                    dense
                    v-model="statusList"
                    :items="itemStatusList"
                    @change="searchList(statusList)"
                    :placeholder="$t('MyOrders.statusAll')"
                    hide-details
                  >
                    <template v-slot:selection="{ item }">
                      <span>{{ item.text }}</span>
                    </template>
                    <template v-slot:append>
                      <v-icon>mdi-chevron-down</v-icon>
                    </template>
                  </v-select>
                </v-col>
              </v-row>
            </v-col>
            <v-col :cols="MobileSize? '12':'4'" md="4" sm="12" :class="IpadSize ? 'px-2' : 'px-2'">
              <v-row no-gutters>
                <v-col cols="4" class="pt-2">
                  <span class="fontDeskSearch">{{ $t('MyOrders.orderDate') }} :</span>
                </v-col>
                <v-col cols="8">
                  <v-dialog
                    ref="dialogContractStartDate"
                    v-model="modalContractStartDate"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field style="border-radius: 8px;" readonly v-model="contractStartDate" hide-details v-bind="attrs" v-on="on" outlined dense :placeholder="$t('MyOrders.formatDate')"><v-icon slot="append" color="#CCCCCC">mdi-calendar-multiselect</v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      color="#27AB9C"
                      v-model="startDate"
                      scrollable
                      reactive
                      :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                      :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="closeModalContractStartDate()"
                      >
                        {{ $t('MyOrders.btnCancel') }}
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="setValueContractStartDate(startDate)"
                      >
                        {{ $t('MyOrders.btnConfirm') }}
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-col>
        <!-- <v-col v-if="disableTable === true" cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-5 mb-3 mt-3' : 'pl-2 pr-2 mb-3 mt-3'">
          <v-row no-gutters class="d-flex">
            <v-col cols="12" md="6" sm="6" class="mr-auto pt-2">
              <v-row no-gutters v-if="countOrderAll !== 0">
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="keyCheckHead === 0">รายการสั่งซื้อทั้งหมด {{ orderListData.total_all }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 1">รายการสั่งซื้อที่ยังไม่ชำระเงินทั้งหมด {{ orderListData.total_all }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 2">รายการสั่งซื้อที่ชำระเงินสำเร็จทั้งหมด {{ orderListData.total_all }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 3">รายการสั่งซื้อที่ชำระเงินไม่สำเร็จทั้งหมด {{ orderListData.total_all }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 4">รายการสั่งซื้อที่ยกเลิกทั้งหมด {{ orderListData.total_all }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 5">รายการสั่งซื้อที่รออนุมติทั้งหมด {{ orderListData.total_all }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="keyCheckHead === 6">รายการสั่งซื้อที่อนุมติทั้งหมด {{ orderListData.total_all }} รายการ</span>
              </v-row>
              <v-row no-gutters v-else>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" >ยังไม่มีรายการสั่งซื้อที่คุณค้นหา</span>
              </v-row>
            </v-col>
            <v-col cols="12" md="6" sm="6" :class="MobileSize ? 'mt-2' : ''" class="ml-auto">
              <v-row no-gutters justify="end">
                <v-btn :disabled="DataTable.length === 0" @click="exportFile()" outlined color="primary" rounded height="40px">
                  <v-img width="24px" height="24px" src="@/assets/Export_File.png"></v-img>
                  <span class="ml-1" style="font-size: 16px; color: primary; font-weight: 500;">Export File</span>
                </v-btn>
                <v-btn elevation="0" @click="CheckStockBeforeOpenModalPayment(selected[0].order_number)" :block="MobileSize" :disabled="selected.length === 0" rounded color="#27AB9C" height="40" :class="MobileSize ? 'white--text' : 'white--text ml-2'">
                  <v-icon>mdi-cash-multiple</v-icon>
                  <span>ชำระเงิน</span>
                </v-btn>
              </v-row>
            </v-col>
          </v-row>
        </v-col> -->
        <!-- {{selected.length}} -->
        <v-col cols="12">
          <v-card v-if="disableTable === true" class="small-card mx-4 my-5" elevation="0">
            <!-- <v-data-table
              :headers="headersAll"
              :items="DataTable"
              :page.sync="page" height="100%"
              @pagination="countOrdar"
              :items-per-page="10"
              no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
              no-data-text="ไม่มีรายการสั่งซื้อในตาราง"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              v-model="selected"
              show-select
              key="order_platform_number"
              class="shop-table"
              >
              <template v-slot:[`header.data-table-select`]></template>
              <template v-slot:[`item.data-table-select`]="{isSelected, item, select}">
                <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-if="(selected.length === 0 && (item.payment_transaction_status !== 'ชำระเงินสำเร็จ' && item.payment_transaction_status !== 'เกินกำหนดชำระเงิน') || isSelected)" color="#27AB9C"></v-simple-checkbox>
              </template>
              <template v-slot:[`item.created_at`]="{ item }" :style="IpadSize ? 'overflow-x: scroll; overflow-y: scroll; max-width: 464px !important; z-index: 2;' : 'max-width: 900px; overflow-y: hidden !important;'" calculate-widths>
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric'
                })}}
              </template>
              <template v-slot:[`item.paid_datetime`]="{ item }">
                {{ item.paid_datetime === '-' ? item.paid_datetime : new Date(item.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                <div>
                  {{item.payment_transaction_number}}
                </div>
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                {{ Number(item.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              </template>
              <template v-slot:[`item.transportation_status`]="{ item }">
                <v-chip v-if="item.transportation_status === 'สั่งซื้อสินค้าสำเร็จ'" color="#F0FEE8" text-color="#1AB759">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'ผู้ส่งกำลังเตรียมพัสดุ'" color="#d7e2f6" text-color="#1c3d77">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'รอขนส่งเข้ารับพัสดุ'" color="#d7e2f6" text-color="#1c3d77">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง'" color="#DBECFA" text-color="#2A70C3">{{item.transportation_status}}</v-chip>
                <v-btn v-if="item.transportation_status === 'รอการตรวจสอบและยอมรับสินค้า'" small rounded class="white--text" color="#27AB9C" @click="CheckAcceptProductData(item)">ได้รับสินค้าแล้ว</v-btn>
                <v-btn v-if="item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status === 'not_received'" small rounded class="white--text" color="#27AB9C" @click="CheckAcceptProductData(item)">ได้รับสินค้าแล้ว</v-btn>
                <v-chip v-if="item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status !== 'not_received'" color="#F0FEE8" text-color="#1AB759">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'ยกเลิกคำสั่งซื้อ' || item.transportation_status === 'ติดต่อผู้รับไม่ได้' || item.transportation_status === 'พัสดุตีกลับ'" color="#F5222D1A" text-color="#F5222D">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'อยู่ระหว่างดำเนินการ'" color="#FEF6E6" text-color="#FAAD14">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'ส่งคืนสินค้า'" color="#F5222D1A" text-color="#F5222D">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'อยู่ระหว่างการขนส่ง'" color="#DBECFA" text-color="#2A70C3">{{item.transportation_status}}</v-chip>
                <v-chip v-if="item.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'" color="#FEF6E6" text-color="#FAAD14">{{item.transportation_status}}</v-chip>
                <v-btn v-if="item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status === 'not_received'" small rounded class="white--text" color="#27AB9C" @click="CheckAcceptProductData(item)">ได้รับสินค้าแล้ว</v-btn>
                <v-chip v-if="item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status !== 'not_received'" color="#F0FEE8" text-color="#1AB759">ยอมรับสินค้าแล้ว</v-chip>
                <v-chip v-if="item.transportation_status === '-' && item.shipping_type === 'front'" color="#ADDFFF" text-color="#0059FF">รับสินค้าหน้าร้าน</v-chip>
                <span v-if="item.transportation_status === '-' && item.shipping_type === 'online'">-</span>
              </template>
              <template v-slot:[`item.tracking`]="{ item }">
                <div v-if="item.list_tracking.length !== 0" class="d-flex flex-wrap">
                  <div v-for="(items, index) in item.list_tracking" :key="index">
                    <span v-if="items.url_tracking !== '-' && items.url_tracking !== ''" :style="items.url_tracking !== '-' && items.url_tracking !== '' ? 'text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C !important; cursor: pointer;' : 'font-size: 16px; font-weight: 400;'" class="pl-2" @click="linkToURLTracking(items.url_tracking)">{{ items.tracking_no + `${index === item.list_tracking.length - 1 ? '' : ','}` }}</span>
                    <a id="urlTracking" :href="urlTracking" target="_blank" style="display: none;"></a>
                    <div @click="copyClipboard()" v-if="items.url_tracking === ''" style="cursor: pointer;" class="pl-2">
                      <v-icon color="#1B5DD6" size="16" class="pr-1">mdi-content-copy</v-icon><span style="text-decoration: underline; font-size: 16px; font-weight: 400; color: #27AB9C;">{{ items.tracking_no + `${index === item.list_tracking.length - 1 ? '' : ','}` }}</span>
                      <input type="text" :value="items.tracking_no" id="trackingNumber" style="display: none;">
                    </div>
                  </div>
                </div>
                <div v-else>
                  <span style="font-size: 16px; font-weight: 400;" class="pl-2"> - </span>
                </div>
              </template>
              <template v-slot:[`item.payment`]="{ item }">
                <v-row justify="center">
                  <v-btn v-if="item.seller_sent_status ==='Success'" text disabled rounded color="#1AB759" small
                    @click="GoToPayment(item)">
                    <b>จ่ายเงิน</b>
                    <v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                  <v-btn v-else-if="item.seller_sent_status ==='cancel'" text disabled rounded color="#1AB759" small
                    @click="GoToPayment(item)">
                    <b>จ่ายเงิน</b>
                    <v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                  <v-btn v-else text rounded color="#1AB759" small @click="GoToPayment(item)">
                    <b>จ่ายเงิน</b>
                    <v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                </v-row>
              </template>
              <template v-slot:[`item.payment_transaction_number_icon`]="{ item }">
                <div v-if="item.QT_order !== '-'">
                  <a @click="orderDetail(item)">
                    <v-btn x-small
                      style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                      class="pt-4 pb-4">
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn>
                  </a>
                </div>
                <div v-else>
                  <span>{{ '-' }}</span>
                </div>
              </template>
              <template v-slot:[`item.transaction_code_icon`]="{ item }">
                <div v-if="item.transaction_code !== '-' && item.required_invoice !== '-'">
                  <span style="color: #1B5DD6; text-decoration: underline; cursor: pointer;" @click="GetETax(item)">{{item.order_number}}</span>
                  <a :href="downloadLink" download="filename.pdf" id="downloadLink" style="display: none;"></a>
                </div>
                <div v-else-if="item.transaction_code === '-' && item.required_invoice !== '-'">
                  <span>{{ item.required_invoice }}</span>
                </div>
                <div v-else>
                  <span>{{ '-' }}</span>
                </div>
              </template>
              <template v-slot:[`item.transaction_status`]="{ item }">
                <span class="fontStatus" style="color: #52C41A;" v-if="(item.transaction_status === 'Success' || item.transaction_status === 'Waiting_Cancel') && item.seller_sent_status !== 'cancel'">
                  <v-icon color="#52C41A">mdi-circle-medium</v-icon>
                    ชำระเงินแล้ว
                </span>
                <span class="fontStatus" style="color: #f76620;" v-else-if="item.seller_sent_status === 'cancel' || item.transaction_status === 'Cancel'">
                  <v-icon color="#f76620">mdi-circle-medium</v-icon>
                  ยกเลิกคำสั่งซื้อ
                </span>
                <span v-else-if="item.transaction_status === 'Pending'" class="fontStatus" style="color: #E9A016;">
                  <v-icon color="#E9A016">mdi-circle-medium</v-icon>
                    รออนุมัติ
                </span>
                <span v-else-if="item.transaction_status === 'Approve'" class="fontStatus" style="color: #1AB759;">
                  <v-icon color="#1AB759">mdi-circle-medium</v-icon>
                    อนุมัติ
                </span>
                <span v-else-if="item.transaction_status === 'Fail'" class="fontStatus" style="color: #D1392B;">
                  <v-icon color="#D1392B">mdi-circle-medium</v-icon>
                    ชำระเงินไม่สำเร็จ
                </span>
                <span v-else class="fontStatus mr-2" style="color: #E9A016;">
                  <v-icon color="#E9A016">mdi-circle-medium</v-icon>
                    รอชำระเงิน
                </span>
              </template>
              <template v-slot:[`item.buyer_received_status`]="{ item }">
                <v-row class="pt-5">
                  <v-select v-model="item.buyer_received_status" :items="receive_items" item-text="text"
                    item-value="value" @change="UpdateStatusBuyer(item)" outlined dense></v-select>
                </v-row>
              </template>
              <template v-slot:[`item.detail`]="{ item }" :style="IpadSize ? 'position: sticky !important; right: 0 !important; position: -webkit-sticky !important;' : ''">
                <v-btn text rounded color="#27AB9C" small @click="goDetailPO(item)">
                  <b>รายละเอียด</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
              <template v-slot:[`item.invoice_path`]="{ item }" >
                <div v-if="item.invoice_path !== ''">
                <span>{{item.invoice_path}}</span>
                </div>
                <div v-else>
                  -
                </div>
              </template>
              <template v-slot:[`item.receipt_number`]="{ item }" >
                <div v-if="item.receipt_number !== ''">
                <span>{{item.receipt_number}}</span>
                </div>
                <div v-else>
                  -
                </div>
              </template>
            </v-data-table> -->
            <v-row dense v-for="(item, index) in DataTable" :key="index" class="mb-5">
              <v-card outlined width="985" class="pa-5" style="border-radius: 8px;" @click.prevent="goDetailPO(item)">
                <v-row dense no-gutters>
                  <v-col :cols="MobileSize ? 12 : 8" :class="IpadSize ? 'd-flex align-start' : 'd-flex align-center'">
                    <span style="font-size: 17px; font-weight: bold; color: #333333;">{{ $t('MyOrders.shop') }} : {{ item.name_th }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 4" :class="IpadSize ? 'd-flex flex-wrap' : MobileSize ? 'd-flex pl-5 justify-center flex-wrap pt-5' : 'd-flex justify-end'" :style="IpadSize || MobileSize ? 'gap: 5px;' : ''">
                    <!-- แบบจ่ายเงิน -->
                    <v-btn :block="MobileSize ? true : false" v-if="item.payment_transaction_status === 'ชำระเงินสำเร็จ' && (item.transportation_status === 'สั่งซื้อสินค้าสำเร็จ' || (item.transportation_status === '-' && item.shipping_type === 'front' && item.transaction_status === 'Success' && item.seller_sent_status === 'not_sent')) && item.transaction_status !== 'Waiting_Cancel'" @click.stop="getAccountBank(item)" rounded outlined color="#D1392B" class="mr-4">{{ $t('MyOrders.cancelOrder') }}</v-btn>
                    <!-- แบบไม่จ่ายเงิน -->
                    <v-btn :block="MobileSize ? true : false" v-if="item.payment_transaction_status === 'ยังไม่ชำระเงิน' && (item.transportation_status !== 'สั่งซื้อสินค้าสำเร็จ' || item.transportation_status === '-')" @click.stop="openCancelOrder(item)" rounded outlined color="#D1392B" class="mr-4">{{ $t('MyOrders.cancelOrder') }}</v-btn>
                    <v-btn :block="MobileSize ? true : false" @click.stop="CheckStockBeforeOpenModalPayment(item, 'payment')" v-if="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' && item.payment_transaction_status !== 'เกินกำหนดชำระเงิน'" rounded color="#3EC6B6" elevation="0" class="mr-4"><v-icon small color="white">mdi-wallet-outline</v-icon><span style="color: white;" class="pl-1">{{ $t('MyOrders.payment') }}</span></v-btn>
                    <!-- <v-btn v-if="item.payment_transaction_status !== 'ยังไม่ชำระเงิน' && (item.transportation_status !== '-' && item.transportation_status !== 'การจัดส่งสำเร็จ' && item.transportation_status !== 'ยืนยันการรับสินค้าแล้ว' && item.transportation_status !== 'รอการตรวจสอบและยอมรับสินค้า' && item.transportation_status !== 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า')" rounded outlined color="#3EC6B6" class="mr-4">ติดต่อผู้ขาย</v-btn> -->
                    <v-btn :block="MobileSize ? true : false" @click.stop="reviewProduct(item.order_number)" v-if="item.payment_transaction_status === 'ชำระเงินสำเร็จ' && ((item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status !== 'not_received') || (item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status !== 'not_received'))" rounded outlined color="#3EC6B6" class="mr-4"><v-icon small class="pr-1">mdi-star</v-icon>{{ $t('MyOrders.review') }}</v-btn>
                    <v-btn :block="MobileSize ? true : false" @click.stop="CheckAcceptProductData(item)" v-if="item.payment_transaction_status === 'ชำระเงินสำเร็จ' && (item.transportation_status === 'รอการตรวจสอบและยอมรับสินค้า' || (item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status === 'not_received' ) || item.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า' || (item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status === 'not_received'))" rounded outlined color="#3EC6B6" class="mr-4">{{ $t('MyOrders.received') }}</v-btn>
                    <v-btn :block="MobileSize ? true : false" @click.stop="CheckStockBeforeOpenModalPayment(item, 'addtocart')" v-if="item.payment_transaction_status !== 'ยังไม่ชำระเงิน' && (item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' || item.transportation_status === 'การจัดส่งสำเร็จ' || item.transportation_status === 'ยกเลิกคำสั่งซื้อ' || (item.transportation_status === '-' && (item.seller_sent_status === 'cancel' || item.transaction_status === 'Cancel' || (item.shipping_type === 'front' && item.transaction_status === 'Success' && item.seller_sent_status === 'sent'))) || item.transportation_status === 'ติดต่อผู้รับไม่ได้' || item.transportation_status === 'พัสดุตีกลับ')" rounded color="#3EC6B6" elevation="0" class="mr-4"><v-icon small color="white">mdi-shopping-outline</v-icon><span style="color: white;" class="pl-1">{{ $t('MyOrders.reorder') }}</span></v-btn>
                  </v-col>
                </v-row>
                <v-row dense no-gutters class="mt-4">
                  <v-col :cols="IpadProSize ? 7 : IpadSize || MobileSize ? 12 : 8" class="d-flex align-center">
                    <span v-if="$i18n.locale === 'th'" style="font-size: 16px; color: #333333;">จำนวนสินค้าทั้งหมด <span style="font-size: 16px; color: #3EC6B6;"> {{ item.product_list.length }} ชิ้น</span></span>
                    <span v-else style="font-size: 16px; color: #333333;">Total Items: <span style="font-size: 16px; color: #3EC6B6;"> {{ item.product_list.length }}</span></span>
                  </v-col>
                  <v-spacer></v-spacer>
                  <v-col :cols="IpadProSize ? 5 : IpadSize || MobileSize ? 12 : 4" :class="IpadSize || MobileSize ? 'pt-4' : 'd-flex justify-end align-center'">
                    <span style="font-size: 14px; color: #333333;" class="mr-2">{{ $t('MyOrders.orderStatus') }} :</span>
                    <v-chip small v-if="item.transportation_status === 'สั่งซื้อสินค้าสำเร็จ'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.paymentSuccess') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'ผู้ส่งกำลังเตรียมพัสดุ'" color="#FFF2E0" text-color="#FAAD14">{{$i18n.locale === 'th' ? item.transportation_status : 'Preparing package'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'รอขนส่งเข้ารับพัสดุ'" color="#FFF2E0" text-color="#FAAD14">{{$i18n.locale === 'th' ? item.transportation_status : 'Pending Pickup'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง' && item.seller_sent_status === 'not_sent'" color="#d7e2f6" text-color="#1c3d77">{{ $t('MyOrders.pickedUp') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'พัสดุอยู่ระหว่างการขนส่ง' && item.seller_sent_status === 'sent'" color="#F4F2FF" text-color="#9747FF">{{ $t('MyOrders.shippingInprogress') }}</v-chip>
                    <!-- <v-btn small v-if="item.transportation_status === 'รอการตรวจสอบและยอมรับสินค้า'" rounded class="white--text" color="#27AB9C" @click="CheckAcceptProductData(item)">ได้รับสินค้าแล้ว</v-btn>
                    <v-btn small v-if="item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status === 'not_received'" rounded class="white--text" color="#27AB9C" @click="CheckAcceptProductData(item)">ได้รับสินค้าแล้ว</v-btn> -->
                    <v-chip small v-if="item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status === 'not_received'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.delivered') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'รอการตรวจสอบและยอมรับสินค้า'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.delivered') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status === 'not_received'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.delivered') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'ยืนยันการรับสินค้าแล้ว' && item.buyer_received_status !== 'not_received'" color="#F0FEE8" text-color="#1AB759">{{$i18n.locale === 'th' ? item.transportation_status : 'Confirm receipt'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'ยกเลิกคำสั่งซื้อ' || item.transportation_status === 'ติดต่อผู้รับไม่ได้' || item.transportation_status === 'พัสดุตีกลับ'" color="#F5222D1A" text-color="#F5222D">{{$i18n.locale === 'th' ? item.transportation_status : 'Return to sender'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'อยู่ระหว่างดำเนินการ'" color="#FEF6E6" text-color="#FAAD14">{{$i18n.locale === 'th' ? item.transportation_status : 'In progress'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'ส่งคืนสินค้า'" color="#F5222D1A" text-color="#F5222D">{{$i18n.locale === 'th' ? item.transportation_status : 'Product return'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'อยู่ระหว่างการขนส่ง'" color="#DBECFA" text-color="#2A70C3">{{$i18n.locale === 'th' ? item.transportation_status : 'Shipping in Progress'}}</v-chip>
                    <v-chip small v-if="item.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'" color="#FEF6E6" text-color="#FAAD14">{{$i18n.locale === 'th' ?  item.transportation_status : 'Under Inspection and Acceptance'}}</v-chip>
                    <!-- <v-btn small v-if="item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status === 'not_received'" rounded class="white--text" color="#27AB9C" @click="CheckAcceptProductData(item)">ได้รับสินค้าแล้ว</v-btn> -->
                    <v-chip small v-if="item.transportation_status === 'การจัดส่งสำเร็จ' && item.buyer_received_status !== 'not_received'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.received') }}</v-chip>
                    <!-- <v-chip small v-if="item.transportation_status === '-' && item.shipping_type === 'front' && (item.transaction_status !== 'Approve' && item.transaction_status !== 'Pending')" color="#ADDFFF" text-color="#0059FF">รับสินค้าหน้าร้าน</v-chip> -->
                    <v-chip small v-if="item.transportation_status === '-' && (item.seller_sent_status === 'cancel' || item.transaction_status === 'Cancel')" color="#F5222D1A" text-color="#F5222D">{{ $t('MyOrders.cancelOrder') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && (item.transaction_status === 'Waiting_Cancel' && item.payment_transaction_status === 'ชำระเงินสำเร็จ')" color="#F5222D1A" text-color="#F5222D">{{ $t('MyOrders.cancelOrder') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.transaction_status === 'Not Paid'  && item.shipping_type !== 'front'" color="#FEF6E6" text-color="#FAAD14">{{ $t('MyOrders.pendingPayment') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.transaction_status === 'Not Paid'  && item.shipping_type === 'front'" color="#FEF6E6" text-color="#FAAD14">{{ $t('MyOrders.pendingPayment') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.transaction_status === 'Success'  && item.shipping_type === 'front' && item.seller_sent_status === 'not_sent'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.paymentSuccess') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.transaction_status === 'Success'  && item.shipping_type === 'front' && item.seller_sent_status === 'sent'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.ConfirmReceipt') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.shipping_type !== 'front' && ((item.transaction_status === 'Success') && item.seller_sent_status !== 'cancel')" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.paymentSuccess') }}</v-chip>
                    <v-chip small v-if="item.transaction_status === 'Fail'" color="#F5222D1A" text-color="#F5222D">{{ $t('MyOrders.statusFail') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.shipping_type === 'front' && item.transaction_status === 'Pending'" color="#FEF6E6" text-color="#FAAD14">{{ $t('MyOrders.statusPending') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.shipping_type === 'front' && item.transaction_status === 'Approve'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.statusApprove') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.shipping_type !== 'front' && item.transaction_status === 'Pending'" color="#FEF6E6" text-color="#FAAD14">{{ $t('MyOrders.statusPending') }}</v-chip>
                    <v-chip small v-if="item.transportation_status === '-' && item.shipping_type !== 'front' && item.transaction_status === 'Approve'" color="#F0FEE8" text-color="#1AB759">{{ $t('MyOrders.statusApprove') }}</v-chip>
                  </v-col>
                </v-row>
                <v-row dense no-gutters class="mt-7 px-7">
                  <v-card outlined min-width="100%" style="border-color: white;">
                    <v-data-table :item-class="() => 'red-border-row'" :headers="MobileSize ? headersTestMobile : headersTest" :items="item.product_list.length > 3 ? item.product_list.slice(0, 3) : item.product_list" hide-default-footer hide-default-header>
                      <template v-slot:[`item.product_image`]="{ item }" v-if="!MobileSize">
                        <td style="width: 100px;">
                        <v-row dense no-gutters style="height: 100px; " align="center" class="pl-5">
                          <v-col cols="12">
                            <v-avatar rounded size="50" v-if="item.product_image !== null && item.product_image !== ''"><v-img
                              :src="item.product_image"
                              style="width: 100%; height: 100%; object-fit: contain;"
                            ></v-img></v-avatar>
                            <v-avatar rounded size="50" v-if="item.product_image === null || item.product_image === ''"><v-img
                              src="@/assets/NoImage.png"
                              style="width: 100%; height: 100%; object-fit: contain;"
                            ></v-img></v-avatar>
                          </v-col>
                        </v-row>
                        </td>
                      </template>
                      <template v-slot:[`item.product_name`]="{ item }">
                        <td style="width: 250px;" v-if="!MobileSize">
                        <v-row dense class="my-3">
                          <v-col cols="12" class="mb-0 pb-0" v-if="!MobileSize">
                            <span>{{ item.product_name }}</span>
                          </v-col>
                          <v-col v-if="item.product_attribute_detail.attribute_priority_1 !== '' && !MobileSize" cols="12" class="mt-0 pt-0">
                            <span style="color: #989898;">{{ $t('MyOrders.attribute') }} </span><span>: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                          </v-col>
                          <v-col cols="12" class="mt-0 pt-0">
                            <v-chip v-if="item.product_type === 'general'" x-small color="#3EC6B6"><span style="color: white; font-size: 12px;">{{ $t('MyOrders.generalProduct') }}</span></v-chip>
                            <v-chip v-if="item.product_type === 'service'" x-small color="#fda808"><span style="color: white; font-size: 12px;">{{ $t('MyOrders.serviceProduct') }}</span></v-chip>
                          </v-col>
                        </v-row>
                        </td>
                        <td v-if="MobileSize" style="width: 500px;">
                        <v-card v-if="MobileSize" elevation="0" width="100%">
                          <v-row dense>
                            <v-col cols="4" class="d-flex justify-center pt-5 mr-0 pr-0">
                              <v-avatar rounded size="70" v-if="item.product_image !== null && item.product_image !== ''"><img
                                :src="item.product_image"
                                style="width: 100%; height: 100%; object-fit: contain;"
                              ></v-avatar>
                              <v-avatar rounded size="70" v-if="item.product_image === null || item.product_image === ''"><img
                                src="@/assets/NoImage.png"
                                style="width: 100%; height: 100%; object-fit: contain;"
                              ></v-avatar>
                            </v-col>
                            <v-col cols="8">
                              <v-col cols="12" class="mb-0 pb-0 text-start">
                                <span style="font-weight: bold;">{{ item.product_name }}</span>
                              </v-col>
                              <v-col v-if="item.attribute !== ''" cols="12" class="mt-0 mb-0 pb-0 pt-2 text-start">
                                <span style="color: #989898;">{{ $t('MyOrders.attribute') }} </span><span>: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                              </v-col>
                              <v-col cols="12" class="mb-0 mt-0 pt-2 pb-0 text-start">
                                <span style="color: #989898;">{{ $t('MyOrders.quantity') }} </span><span>: {{ item.quantity }}</span>
                              </v-col>
                              <v-col cols="12" class="mb-0 mt-0 pt-2 pb-0 text-start">
                                <span style="color: #989898;">{{ $t('MyOrders.price') }} </span><span>: {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                              </v-col>
                              <v-col cols="12" class="mb-0 mt-0 pt-2 pb-2 text-start">
                                <v-chip v-if="item.product_type === 'general'" x-small color="#3EC6B6"><span style="color: white; font-size: 12px;">{{ $t('MyOrders.generalProduct') }}</span></v-chip>
                                <v-chip v-if="item.product_type === 'service'" x-small color="#fda808"><span style="color: white; font-size: 12px;">{{ $t('MyOrders.serviceProduct') }}</span></v-chip>
                              </v-col>
                            </v-col>
                          </v-row>
                        </v-card>
                        </td>
                      </template>
                      <template v-slot:[`item.quantity`]="{ item }" v-if="!MobileSize">
                        <td style="width: 150px;">
                        <span v-if="!IpadSize">{{ $t('MyOrders.quantity') }} : {{ item.quantity }}</span>
                        <span v-if="IpadSize">{{ $t('MyOrders.quantity') }}: {{ item.quantity }}</span>
                        </td>
                      </template>
                      <template v-slot:[`item.price`]="{ item }" v-if="!MobileSize">
                        <td style="width: 200px;">
                        <span v-if="!IpadSize">{{ $t('MyOrders.price') }} : {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <span v-if="IpadSize">{{ $t('MyOrders.price') }}: {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        </td>
                      </template>
                    </v-data-table>
                    <v-row class="d-flex justify-center mt-1 mb-1" v-if="item.product_list.length > 3">
                      <v-col cols="12" class="d-flex justify-center align-center">
                        <span @click.stop="goDetailPO(item)" style="color: #1B5DD6; cursor: pointer; font-size: 14px; text-decoration: underline;">{{ $t('MyOrders.seeMore') }}</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-row>
                <!-- สินค้าแถม -->
                 <v-row dense no-gutters class="mt-7 px-7" v-if="item.product_free.length !== 0">
                  <v-card outlined min-width="100%" style="border-color: white;">
                    <v-data-table :item-class="() => 'red-border-row'" :headers="MobileSize ? headersTestMobile : headersTest" :items="item.product_free.length > 3 ? item.product_free.slice(0, 3) : item.product_free" hide-default-footer hide-default-header>
                      <template v-slot:[`item.product_image`]="{ item }" v-if="!MobileSize">
                        <td style="width: 100px;">
                        <v-row dense no-gutters style="height: 100px; " align="center" class="pl-5">
                          <v-col cols="12">
                            <v-avatar rounded size="50" v-if="item.product_image !== null && item.product_image !== ''"><v-img
                              :src="item.product_image"
                              style="width: 100%; height: 100%; object-fit: contain;"
                            ></v-img></v-avatar>
                            <v-avatar rounded size="50" v-if="item.product_image === null || item.product_image === ''"><v-img
                              src="@/assets/NoImage.png"
                              style="width: 100%; height: 100%; object-fit: contain;"
                            ></v-img></v-avatar>
                          </v-col>
                        </v-row>
                        </td>
                      </template>
                      <template v-slot:[`item.product_name`]="{ item }">
                        <td style="width: 250px;" v-if="!MobileSize">
                        <v-row dense class="my-3">
                          <v-col cols="12" class="mb-0 pb-0" v-if="!MobileSize">
                            <span>{{ item.product_name }}</span>
                          </v-col>
                          <v-col v-if="item.product_attribute_detail.attribute_priority_1 !== '' && !MobileSize" cols="12" class="mt-0 pt-0">
                            <span style="color: #989898;">{{ $t('MyOrders.attribute') }} </span><span>: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                          </v-col>
                          <v-col cols="12" class="mt-0 pt-0">
                            <v-chip x-small color="#ff3d1e"><span style="color: white; font-size: 12px;">{{ $t('MyOrders.freeGift') }}</span></v-chip>
                          </v-col>
                        </v-row>
                        </td>
                        <td v-if="MobileSize" style="width: 500px;">
                        <v-card v-if="MobileSize" elevation="0" width="100%">
                          <v-row dense>
                            <v-col cols="4" class="d-flex justify-center pt-5 mr-0 pr-0">
                              <v-avatar rounded size="70" v-if="item.product_image !== null && item.product_image !== ''"><img
                                :src="item.product_image"
                                style="width: 100%; height: 100%; object-fit: contain;"
                              ></v-avatar>
                              <v-avatar rounded size="70" v-if="item.product_image === null || item.product_image === ''"><img
                                src="@/assets/NoImage.png"
                                style="width: 100%; height: 100%; object-fit: contain;"
                              ></v-avatar>
                            </v-col>
                            <v-col cols="8">
                              <v-col cols="12" class="mb-0 pb-0 text-start">
                                <span style="font-weight: bold;">{{ item.product_name }}</span>
                              </v-col>
                              <v-col v-if="item.attribute !== ''" cols="12" class="mt-0 mb-0 pb-0 pt-2 text-start">
                                <span style="color: #989898;">{{ $t('MyOrders.attribute') }} </span><span>: {{ item.product_attribute_detail.attribute_priority_1 }}</span>
                              </v-col>
                              <v-col cols="12" class="mb-0 mt-0 pt-2 pb-0 text-start">
                                <span style="color: #989898;">{{ $t('MyOrders.quantity') }} </span><span>: {{ item.quantity }}</span>
                              </v-col>
                              <v-col cols="12" class="mb-0 mt-0 pt-2 pb-0 text-start">
                                <span style="color: #989898;">{{ $t('MyOrders.price') }} </span><span>: 0.00</span>
                              </v-col>
                              <v-col cols="12" class="mb-0 mt-0 pt-2 pb-2 text-start">
                                <v-chip x-small color="#ff3d1e"><span style="color: white; font-size: 12px;">{{ $t('MyOrders.freeGift') }}</span></v-chip>
                              </v-col>
                            </v-col>
                          </v-row>
                        </v-card>
                        </td>
                      </template>
                      <template v-slot:[`item.quantity`]="{ item }" v-if="!MobileSize">
                        <td style="width: 150px;">
                        <span v-if="!IpadSize">{{ $t('MyOrders.quantity') }} : {{ item.quantity }}</span>
                        <span v-if="IpadSize">{{ $t('MyOrders.quantity') }}: {{ item.quantity }}</span>
                        </td>
                      </template>
                      <template v-slot:[`item.price`] v-if="!MobileSize">
                        <td style="width: 200px;">
                        <span v-if="!IpadSize">{{ $t('MyOrders.price') }} : 0.00</span>
                        <span v-if="IpadSize">{{ $t('MyOrders.price') }}: 0.00</span>
                        </td>
                      </template>
                    </v-data-table>
                    <v-row class="d-flex justify-center mt-1 mb-1" v-if="item.product_list.length > 3">
                      <v-col cols="12" class="d-flex justify-center align-center">
                        <span @click.stop="goDetailPO(item)" style="color: #1B5DD6; cursor: pointer; font-size: 14px; text-decoration: underline;">{{ $t('MyOrders.seeMore') }}</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-row>
                <v-row dense no-gutters class="mt-7">
                  <v-col :cols="MobileSize ? 6 : 4" >
                    <span style="font-size: 18px; font-weight: bold; color: #333333;">{{ $t('MyOrders.totalPrice') }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 6 : 8" class="d-flex justify-end">
                    <span :style="!IpadSize && !MobileSize ? 'font-size: 20px; font-weight: bold; color: #3EC6B6;' : 'font-size: 16px; font-weight: bold; color: #3EC6B6;'">{{Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2})}} <span>{{$i18n.locale === 'th' ? 'บาท' : 'Baht'}}</span></span>
                  </v-col>
                </v-row>
              </v-card>
            </v-row>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === true && DataTable.length === 0" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain
              aspect-ratio="2"></v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>{{ $t('MyOrders.noOrderAt') }}{{ StateStatus }}</b></h2>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain
              aspect-ratio="2"></v-img>
          </div>
          <h2 v-if="StateStatus === 'ทั้งหมด'" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>{{ $t('MyOrders.noOrder') }}</b></h2>
          <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>{{ $t('MyOrders.noOrderAt') }}{{ StateStatus }}</b></h2>
        </v-col>
      </v-row>
    </v-card>
    <!-- </v-container> -->
    <ModalReviewProduct ref="ModalReviewProduct" />
    <v-dialog v-model="dialogChooesPayType" persistent width="550">
      <v-card :height=" MobileSize || IpadSize ? '439':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="75px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')">
          <v-toolbar flat color="rgba(0, 0, 0, 0)">
            <v-spacer></v-spacer>
            <v-toolbar-title class="mt-5" style="font-size: 18px; font-weight: 700; color: #FFFFFF;">{{ $t('MyOrders.selectPayment') }}</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn class="mt-3" color="#CCCCCC" icon
              @click="openDialogPayment()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
        </v-img>
        <!-- <v-card-title style="place-content: center;" class="px-0">
          <span style="font-size: 20px;font-weight: 700; color: #FAAD14;">วิธีการชำระเงิน</span>
        </v-card-title> -->
        <v-card-text >
          <v-row dense no-gutters class="mb-10 mt-3">
            <!-- {{selected[0].payment_method[2]}} -->
            <!-- <v-radio-group v-model="radioPayment" row class="ma-0 pa-0" v-if="selected.length !== 0">
              <v-radio value="radio-qr" v-if="paymentMethods[0] === 'qrcode' || paymentMethods[1] === 'qrcode' || paymentMethods[2] === 'qrcode'"><template v-slot:label>
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio value="radio-credit" v-if="paymentMethods[0] === 'creditcard' || paymentMethods[1] === 'creditcard' || paymentMethods[2] === 'creditcard'"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              <v-radio value="radio-installment" v-if="paymentMethods[0] === 'installment' || paymentMethods[1] === 'installment' || paymentMethods[2] === 'installment'" >
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio>
            </v-radio-group> -->
            <!-- <v-radio-group v-model="radioPayment" row class="ma-0 pa-0" v-if="selected.length !== 0">
              <v-radio value="radio-qr" v-if="selected[0].payment_method[0] === 'qrcode' || selected[0].payment_method[1] === 'qrcode' || selected[0].payment_method[2] === 'qrcode'"><template v-slot:label>
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio value="radio-credit" v-if="selected[0].payment_method[0] === 'creditcard' || selected[0].payment_method[1] === 'creditcard' || selected[0].payment_method[2] === 'creditcard'"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              <v-radio value="radio-installment" v-if="selected[0].payment_method[0] === 'installment' || selected[0].payment_method[1] === 'installment' || selected[0].payment_method[2] === 'installment'" >
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio>
            </v-radio-group> -->
            <v-radio-group v-model="radioPayment" class="px-5">
              <v-card outlined :style="radioPayment === 'radio-qr' ? 'border-radius: 8px; border-color: #3EC6B6;' : 'border-radius: 8px;'" min-height="60" :min-width="MobileSize ? '' : 460" class="d-flex align-content-center mb-5">
                <v-radio color="#3EC6B6" value="radio-qr" class="pl-3"><template v-slot:label>
                    <v-avatar rounded class="mr-2 ml-3">
                      <v-img src="@/assets/QRIcon.png" width="30.86" height="30.86" contain></v-img>
                    </v-avatar>
                    <span style="font-size: 16px; font-weight: bold; color: black;">QR Code</span>
                  </template>
                </v-radio>
              </v-card>
              <v-card outlined :style="radioPayment === 'radio-credit' ? 'border-radius: 8px; border-color: #3EC6B6;' : 'border-radius: 8px;'" min-height="60" :min-width="MobileSize ? '' : 460" class="d-flex align-content-center">
                <v-radio color="#3EC6B6" value="radio-credit" class="pl-3"><template v-slot:label>
                  <v-avatar rounded class="mr-2 ml-3">
                    <v-img src="@/assets/CreditCardIcon.png" width="30.86" height="30.86" contain></v-img>
                  </v-avatar>
                  <span style="font-size: 16px; font-weight: bold; color: black;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              </v-card>
              <!-- <v-radio value="radio-installment">
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio> -->
            </v-radio-group>
          </v-row>
          <v-row no-gutters justify="center" class="mt-5" v-if="radioPayment === 'radio-installment'">
            <v-col :cols="MobileSize ? 6 : 4" class="pl-8">
              <span style="font-size: 16px;">{{ $t('MyOrders.installmentTerm') }}</span>
            </v-col>
            <v-col :cols="MobileSize ? 6 : 8" class="ma-0 pa-0" md="8" sm="6">
              <v-select style="border-radius: 8px;" outlined dense :label="$t('MyOrders.selectTerm')" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" >
                <template v-slot:append>
                  <v-icon>mdi-chevron-down</v-icon>
                </template>
                <template v-slot:no-data>
                  <v-list-item>
                    <v-list-item-content class="text-center">
                      <v-list-item-title v-if="$i18n.locale === 'th'">ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง <span style="color: #27AB9C;">'ขั้นต่ำ'</span> ที่กำหนดไว้</v-list-item-title>
                      <v-list-item-title v-else>Installment not available because the specified <span style="color: #27AB9C;">'minimum'</span> is not met</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-text class="mt-10 py-5" style="background-color: #F5FCFB;">
          <v-row dense justify="center" class="px-10">
            <v-btn :width="MobileSize? '100':'100'" height="38" outlined rounded color="#27AB9C" class="mr-4"
              @click="openDialogPayment()">{{ $t('MyOrders.btnCancel') }}</v-btn>
            <v-spacer></v-spacer>
            <v-btn elevation="0" :disabled="radioPayment === 'no' || radioPayment === 'radio-installment' && radioCreditTerm === ''" :width="MobileSize? '100':'100'" height="38" class="white--text " rounded color="#27AB9C"
              @click="confirmPayment()">{{ $t('MyOrders.btnSave') }}</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogQR" persistent :width="MobileSize ? '100%' : '640'">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
              style="position: absolute; height: 120px; ">
              <v-row style="height: 120px; ">
                <v-col style="text-align: center;" class="pt-4">
                  <span style="margin-left: 47px"
                    :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('MyOrders.scanQR') }}</b></span>
                </v-col>
                <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                    color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '640px'"
                style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
                style="background: #FFFFFF; border-radius: 20px;">
                <div style="text-align: center;">
                  <!-- <v-img height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"></v-img>
                  <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn> -->
                  <v-col class="py-0">
                    <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"/>
                  </v-col>
                  <v-col class="py-0">
                    <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">{{ $t('MyOrders.btnSaveImage') }}</v-btn>
                  </v-col>
                  <div>
                    <v-col>
                      <span style="font-size: 20px; font-weight: 700;">{{ $t('MyOrders.paymentAmount') }} : {{ Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
                        <span>{{$i18n.locale === 'th' ? 'บาท' : 'Baht'}}</span></span>
                    </v-col>
                    <v-col>
                      <span style="font-size: 14px; font-weight: 400;">{{ $t('MyOrders.referenceCode') }} {{Ref1}}</span>
                    </v-col>
                    <v-col class="py-0">
                      <span style="font-size: 14px; font-weight: 600; color: #A1A1A1;">{{ $t('MyOrders.stepMobile') }}</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">1. {{ $t('MyOrders.capture') }}<br><span style="color: red; font-weight: 700;">{{ $t('MyOrders.caseiOS') }}</span> {{ $t('MyOrders.clickSave') }} <b>{{ $t('MyOrders.saveToApp') }}</b></span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">2. {{ $t('MyOrders.selectMenuScan') }}</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">3. {{ $t('MyOrders.selectScreenshot') }}</span>
                    </v-col>
                  </div>
                </div>
              </v-card>
            </div>
          </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirm" width="424" persistent>
      <v-card :height=" MobileSize || IpadSize ? '400':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn @click="dialogConfirm = false" color="#CCCCCC" icon>
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b></b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('MyOrders.textConfirmPay') }}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogConfirm = false">{{ $t('MyOrders.btnCancel') }}</v-btn>
              <v-btn :width="MobileSize? '100':'156'" height="38" class="white--text" rounded color="#27AB9C" @click="radioPayment === 'radio-qr' ? GetQRCode('cashPayment'): GetCC('cashPayment') ">{{ $t('MyOrders.btnConfirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Modal ยกเลิก order เมื่อจ่ายเงินแล้ว -->
    <v-dialog v-model="modalInputReasonRefund" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('MyOrders.cancelOrder') }}</b></span>
              </v-col>
              <v-btn fab small @click="closemodalInputReasonRefund()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <v-row v-if="cancelModalStep === 1">
                  <v-col cols="12" >
                    <v-img src="@/assets/stepperNew/transport/cancelModalStep1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="4"></v-img>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center" v-if="cancelModalStep === 1">
                    <span style="font-size: 16px; font-weight: 700; color: #000000">{{ $t('MyOrders.cancelOrder') }}</span>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center" v-if="cancelModalStep === 1">
                    <span style="font-size: 16px; font-weight: 700;" :style="cancelModalStep === 2 ? 'color: #269afd' : 'color: #cccccc'">{{ $t('MyOrders.refundAccount') }}</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5" v-if="cancelModalStep === 1">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; color: #333333;">{{ $t('MyOrders.reasonCancel') }}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reasonRefund" :counter="250" maxLength="250" outlined :placeholder="$t('MyOrders.reasonCancel')" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5" v-if="cancelModalStep === 2">
                  <v-col cols="12">
                    <v-img src="@/assets/stepperNew/transport/cancelModalStep2.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="4"></v-img>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700; color: #000000">{{ $t('MyOrders.cancelOrder') }}</span>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700;" :style="cancelModalStep === 2 ? 'color: #269afd' : 'color: #cccccc'">{{ $t('MyOrders.refundAccount') }}</span>
                  </v-col>
                  <v-col cols="12" class="d-flex justify-center">
                    <span v-if="urName === '' && bankNameRefund === '' && bankNumberRefund === ''" style="font-size: 16px; font-weight: 700; color: #cccccc">{{ $t('MyOrders.textAddAccount') }}</span>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 12" :class="MobileSize ? '' : 'pl-1'">
                    <v-col class="py-0 px-0">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('MyOrders.accountName') }}</span>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-text-field @keypress="CheckSpacebarOne($event)" v-model="urName" :placeholder="$t('MyOrders.textEnterName')" dense outlined hide-details></v-text-field>
                    </v-col>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 12" :class="MobileSize ? '' : 'pl-1'">
                    <v-col class="py-0 px-0">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('MyOrders.bankName') }}</span>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-select v-model="bankNameRefund" :items="listAccount" item-text="name" item-value="code" :menu-props="{ offsetY: false, maxHeight: '231px', maxWidth: '280px', overflowY: 'hidden' }" :placeholder="$t('MyOrders.textEnterBank')" dense outlined hide-details></v-select>
                    </v-col>
                  </v-col>
                  <v-col :cols="MobileSize ? 12 : 12">
                    <v-col class="py-0 px-0">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">{{ $t('MyOrders.accountNumber') }}</span>
                    </v-col>
                    <v-col cols="12" class="px-0">
                      <v-text-field
                        @keypress="CheckSpacebarOne($event)"
                        v-model="bankNumberRefund"
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                        :placeholder="$t('MyOrders.textAccountNumber')"
                        dense
                        outlined
                        hide-details>
                      </v-text-field>
                    </v-col>
                  </v-col>
                </v-row>
                <!-- <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.data_list[0].order_number}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.buyer_name}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-3">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(items.created_at).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric'
                    })}}น.</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 pa-0 align-center' : 'd-flex ma-0 pa-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-1'">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field v-model="urName" placeholder="ระบุชื่อบัญชีธนาคาร" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เบอร์โทรศัพท์ <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field oninput="this.value = this.value.replace(/[^0-9\s]/g, '')" v-model="telNumber" placeholder="ระบุเบอร์โทรศัพท์" dense outlined hide-details></v-text-field>
                      </v-col>
                    </v-col>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" :class="MobileSize ? 'ma-0 align-center' : 'd-flex ma-0 align-center'">
                    <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? '' : 'pl-1'">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">ชื่อธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-select v-model="bankNameRefund" :items="listAccount" item-text="name" item-value="code" :menu-props="{ offsetY: false, maxHeight: '231px', maxWidth: '280px', overflowY: 'hidden' }" placeholder="ระบุธนาคาร" dense outlined hide-details></v-select>
                      </v-col>
                    </v-col>
                    <v-col :cols="MobileSize ? 12 : 6">
                      <v-col class="py-0 px-0">
                        <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขบัญชีธนาคาร <span style="font-size: 16px; font-weight: 600; color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" class="px-0">
                        <v-text-field
                          @keypress="CheckSpacebarOne($event)"
                          v-model="bankNumberRefund"
                          oninput="this.value = this.value.replace(/[^0-9\s]/g, '')"
                          placeholder="ระบุเลขบัญชีธนาคาร"
                          dense
                          outlined
                          hide-details>
                        </v-text-field>
                      </v-col>
                    </v-col>
                  </v-col>
                </v-row>
                <v-row dense class="mt-4">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reasonRefund" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row> -->
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closemodalInputReasonRefund()">{{ $t('MyOrders.btnCancel') }}</v-btn>
          <v-spacer></v-spacer>
          <v-btn v-if="cancelModalStep === 1" color="#27AB9C" rounded width="125" @click="openDialogConfirmCancel('payment')" :disabled="reasonRefund === ''" height="40" class="white--text">{{ $t('MyOrders.btnConfirm') }}</v-btn>
          <v-btn v-if="cancelModalStep === 2" color="#27AB9C" rounded width="125" @click="openDialogConfirmCancelStep2()" :disabled="urName === '' || bankNameRefund === '' || bankNumberRefund === ''" height="40" class="white--text">{{ $t('MyOrders.btnConfirm') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- ยกเลิกคำสั่งซื้อไม่จ่ายตัง -->
     <v-dialog v-model="modalInputReason" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ $t('MyOrders.cancelOrder') }}</b></span>
              </v-col>
              <v-btn fab small @click="closemodalInputReason()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 15px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <!-- <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.data_list[0].order_number}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{items.buyer_name}}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ :</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(items.created_at).toLocaleDateString('th-TH', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: 'numeric',
                      minute: 'numeric'
                    })}}น.</span>
                  </v-col>
                </v-row> -->
                <v-row>
                  <v-col cols="12">
                    <v-img src="@/assets/stepperNew/transport/cancelModalStep1.png" :max-height="IpadProSize ? '100vh' : '1000px'" :max-width="IpadProSize ? '65vw' : '1000px'" height="100%" width="100%" contain aspect-ratio="4"></v-img>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700; color: #000000">{{ $t('MyOrders.cancelOrder') }}</span>
                  </v-col>
                  <v-col cols="6" class="d-flex justify-center">
                    <span style="font-size: 16px; font-weight: 700; color: #cccccc">{{ $t('MyOrders.refundAccount') }}</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; color: #333333;">{{ $t('MyOrders.reasonCancel') }}</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reason" :counter="250" maxLength="250" outlined :placeholder="$t('MyOrders.reasonCancel')" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closemodalInputReason()">{{ $t('MyOrders.btnCancel') }}</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" @click="openDialogConfirmCancel('no payment'); modalAwaitCancelOrder = true; modalInputReason = false" :disabled="reason !== '' ? false : true" height="40" class="white--text">{{ $t('MyOrders.btnConfirm') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="modalAwaitCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogConfirmCancel()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ $t('MyOrders.cancelOrder') }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('MyOrders.textcancel') }}</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('MyOrders.textThisOrder') }}</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogConfirmCancel()">{{ $t('MyOrders.btnCancel') }}</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmCancel()">{{ $t('MyOrders.btnConfirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="modalSuccessCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeSuccessCancelOrder()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ $t('MyOrders.cancelSuccess') }}</b></p>
            <span v-if="cancelType === 'no payment'" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('MyOrders.textHaveCancel') }}</span>
            <span v-if="cancelType === 'payment'" style="font-weight: 700; font-size: 16px; line-height: 24px; color: red;">{{ $t('MyOrders.receivedRefund') }}</span>
            <span v-if="cancelType === 'payment'" style="font-weight: 700; font-size: 16px; line-height: 24px; color: red;"> {{ $t('MyOrders.byDate') }}*</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessCancelOrder()">{{ $t('MyOrders.btnConfirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <div v-if="isLoading" class="text-center my-4">
      <v-progress-circular indeterminate color="primary"></v-progress-circular>
    </div>
    <div ref="targetSection" class="scroll-target"></div>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  components: {
    ModalReviewProduct: () => import('@/components/UserProfile/ModalReview/ReviewProduct')
  },
  data () {
    return {
      modalSuccessCancelOrder: false,
      modalAwaitCancelOrder: false,
      cancelType: '',
      reason: '',
      modalInputReason: false,
      headersTest: [
        { text: `${this.$t('MyOrders.image')}`, value: 'product_image', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.productName')}`, value: 'product_name', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.quantity')}`, value: 'quantity', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.price')}`, value: 'price', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersTestMobile: [
        { text: `${this.$t('MyOrders.productName')}`, value: 'product_name', filterable: false, sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      check: '',
      timer: null,
      checkCountAll: 0,
      statusType: '',
      contractStartDate: '',
      searchContractStartDate: '',
      startDate: '',
      modalContractStartDate: false,
      statusList: '',
      itemStatusList: [
        { text: `${this.$t('MyOrders.statusAll')}`, value: '' },
        { text: `${this.$t('MyOrders.statusSuccess')}`, value: 'Success' },
        { text: `${this.$t('MyOrders.statusCancel')}`, value: 'Cancel' },
        { text: `${this.$t('MyOrders.statusPending')}`, value: 'Pending' },
        { text: `${this.$t('MyOrders.statusApprove')}`, value: 'Approve' },
        { text: `${this.$t('MyOrders.statusFail')}`, value: 'Fail' },
        { text: `${this.$t('MyOrders.statusNotPaid')}`, value: 'Not Paid' }
      ],
      orderList: [],
      StateStatus: 'ทั้งหมด',
      showCountOrder: 0,
      disableTable: false,
      dataRole: '',
      page: 1,
      OrderNamePurchaser: [
        // { key: 0, name: 'ข้อมูลไม่ครบ' },
        { key: 0, name: 'ทั้งหมด' },
        { key: 2, name: 'ยังไม่ชำระเงิน' },
        { key: 1, name: 'รออนุมัติ' },
        { key: 6, name: 'อนุมัติ' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' },
        { key: 4, name: 'ยกเลิก' }

      ],
      OrderNameExtBuyer: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 2, name: 'ยังไม่ชำระเงิน' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' },
        { key: 4, name: 'ยกเลิก' }
      ],
      keyCheckHead: 0,
      headers: [
        { text: `${this.$t('MyOrders.thCreateAt')}`, value: 'created_at', align: 'center', filterable: false, sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thReceipt')}`, value: 'receipt_number', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thInvoice')}`, value: 'invoice_path', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ร้านค้า', value: 'order_number' },
        // { text: 'สินค้า', value: 'order_number' },
        // { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, value: 'transaction_code_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPaidDate')}`, value: 'paid_datetime', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPayment')}`, value: 'payment', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersSuccess: [
        { text: `${this.$t('MyOrders.thCreateAt')}`, value: 'created_at', filterable: false, align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, filterable: false, value: 'transaction_status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thReceipt')}`, filterable: false, value: 'receipt_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, filterable: false, value: 'payment_transaction_number_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thInvoice')}`, filterable: false, value: 'invoice_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ราคา', value: 'total_amount', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, filterable: false, value: 'transaction_code_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
        // { text: 'วัน/เวลาทำธุรกรรม', filterable: false, value: 'paid_datetime', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
        // { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        // { text: 'วันที่ได้รับของ', value: 'received_date', align: 'center' }
      ],
      headersPending: [
        { text: `${this.$t('MyOrders.thCreateAt')}`, filterable: false, value: 'created_at', align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, filterable: false, value: 'transaction_status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thReceipt')}`, filterable: false, value: 'receipt_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, filterable: false, value: 'payment_transaction_number_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thInvoice')}`, filterable: false, value: 'invoice_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ราคา', filterable: false, value: 'total_amount', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, filterable: false, value: 'transaction_code_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'detail', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' }
        // { text: 'สถานะการรับของ', value: 'buyer_received_status', align: 'center' },
        // { text: 'วันที่ได้รับของ', value: 'received_date', align: 'center' }
      ],
      headersApprove: [
        { text: `${this.$t('MyOrders.thCreateAt')}`, filterable: false, value: 'created_at', align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, filterable: false, value: 'transaction_status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thReceipt')}`, filterable: false, value: 'receipt_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, filterable: false, value: 'payment_transaction_number_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thInvoice')}`, filterable: false, value: 'invoice_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ราคา', filterable: false, value: 'total_amount', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, filterable: false, value: 'transaction_code_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'detail', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' }
      ],
      headersNoPaid: [
        { text: `${this.$t('MyOrders.thCreateAt')}`, filterable: false, value: 'created_at', align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, filterable: false, value: 'transaction_status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thReceipt')}`, filterable: false, value: 'receipt_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, filterable: false, value: 'payment_transaction_number_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thInvoice')}`, filterable: false, value: 'invoice_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ราคา', filterable: false, value: 'total_amount', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, filterable: false, value: 'transaction_code_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: `${this.$t('MyOrders.thPayment')}`, filterable: false, value: 'payment', align: 'center', divider: true, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' }
      ],
      headersCancel: [
        { text: `${this.$t('MyOrders.thCreateAt')}`, value: 'created_at', align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, value: 'transaction_status', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' },
        { text: `${this.$t('MyOrders.thReceipt')}`, value: 'receipt_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, value: 'payment_transaction_number_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thInvoice')}`, value: 'invoice_path', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' },
        // { text: 'ราคา', value: 'total_amount', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, value: 'transaction_code_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' }
        // { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' }
      ],
      headersAll: [
        // สถานะขนส่ง = โชว์สถานะตาม key ที่ส่งมา
        // Tracking number
        { text: `${this.$t('MyOrders.thCreateAt')}`, value: 'created_at', align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransactionNumber')}`, value: 'payment_transaction_number', sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thShop')}`, value: 'seller_shop_name', sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransactionStatus')}`, value: 'transaction_status', sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransportation')}`, value: 'transportation_status', sortable: false, width: '180px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thReceipt')}`, value: 'receipt_number', sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thPaymentIcon')}`, value: 'payment_transaction_number_icon', sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thInvoice')}`, value: 'invoice_path', sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        // { text: 'ราคา', value: 'total_amount', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: `${this.$t('MyOrders.thTransactionCode')}`, value: 'transaction_code_icon', sortable: false, width: '180px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: 'Tracking Number', value: 'tracking', sortable: false, width: '200px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        // { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail scrollData' },
        { text: '', value: 'detail', align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail fixedHeader' }
      ],
      receive_items: [
        { text: `${this.$t('MyOrders.thNotReceipt')}`, value: 'not_received' },
        { text: `${this.$t('MyOrders.thReceived')}`, value: 'received' }
      ],
      statusSend: { text: `${this.$t('MyOrders.thNotReceipt')}`, value: 'not_received' },
      DataTable: [],
      customClick: (record) => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: '',
      countOrderAll: 0,
      countOrderNotpaid: 0,
      countOrderSuccess: 0,
      countOrderFail: 0,
      countOrderCancel: 0,
      orderListData: [],
      downloadLink: '',
      urlTracking: '',
      trackingNoOutSource: '',
      selected: [],
      newsListShow: [],
      dialogChooesPayType: false,
      radioPayment: 'no',
      radioCreditTerm: '',
      dialogConfirm: false,
      items: [],
      DialogQR: false,
      netPrice: '',
      ImageQR: '',
      TypeOS: '',
      Ref1: '',
      test: [],
      limit: 10,
      maxItem: 0,
      option: {
        page: 1,
        itemsPerPage: 10
      },
      Image: '',
      CloseDialog: false,
      paymentMethods: [],
      orderNumberCancel: [],
      paymentNumber: '',
      stopLazyLoad: false,
      isLoading: false,
      lastId: '',
      urName: '',
      bankNameRefund: '',
      telNumber: '',
      bankNumberRefund: '',
      reasonRefund: '',
      modalInputReasonRefund: false,
      listAccount: [],
      cancelModalStep: 1
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    window.addEventListener('scroll', this.scrollToTargetSection)
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.scrollToTargetSection)
    // window.removeEventListener('scroll', this.handleScroll)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/pobuyerProfile' }).catch(() => {})
      }
    },
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    },
    StateStatus (val) {
      if (val === 'ทั้งหมด') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ยังไม่ชำระเงิน') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ชำระเงินสำเร็จ') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ชำระเงินไม่สำเร็จ') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ยกเลิก') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 4
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'รออนุมัติ') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 5
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'อนุมัติ') {
        this.DataTable = this.orderListData.all
        this.keyCheckHead = 6
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
      // console.log('this.DataTable', val, this.DataTable)
    },
    // selected (val) {
    //   const keyToMatch = val[0].order_platform_number
    //   console.log(keyToMatch, 'keytomatch')
    //   const matchingItems = this.DataTable.filter(i => i.order_platform_number === keyToMatch)
    //   console.log(matchingItems, 'matchingItems')
    //   // this.selected = matchingItems
    // }
    selected: {
      handler (newSelected, oldSelected) {
        if (oldSelected.length < newSelected.length) {
          const addedItems = newSelected.filter(item => !oldSelected.includes(item))
          if (addedItems.length === 0) return
          const keyToMatch = newSelected[0].order_platform_number
          const matchingItems = this.DataTable.filter(i => i.order_platform_number === keyToMatch)
          this.selected = matchingItems
          this.items = matchingItems[0]
        } else if (oldSelected.length > newSelected.length) {
          if (newSelected.length === 0) {
            return
          }
          this.selected = []
          this.items = {}
        }
      },
      deep: true
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAccount')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.check = 'check'
      await this.ListDataTable()
    }
  },
  methods: {
    async getPaymentUsers () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionGetPaymentUsers')
      var res = await this.$store.state.ModuleOrder.stateGetPaymentUsers
      if (res.code === 200) {
        // console.log(res.data[0], 'res')
        this.bankNumberRefund = res.data[0].bank_code === '-' ? '' : res.data[0].bank_no
        this.urName = res.data[0].bank_user_name === '-' ? '' : res.data[0].bank_user_name
        this.bankNameRefund = res.data[0].bank_no === '-' ? '' : res.data[0].bank_code
      }
      this.$store.commit('closeLoader')
    },
    async getAccountBank (orderNumber) {
      this.orderNumberCancel = orderNumber.payment_transaction_number
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      this.modalInputReasonRefund = true
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      if (response.code === 200) {
        this.listAccount = response.data
        // this.listAccount = response.data.map((item) => ({
        //   text: item.name
        // }))
        // console.log('see', this.listAccount)
      } else {
        this.listAccount = []
      }
    },
    openCancelOrder (orderNumber) {
      this.orderNumberCancel = orderNumber
      this.modalInputReason = true
    },
    async closeSuccessCancelOrder () {
      this.cancelModalStep = 1
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      this.reason = ''
      this.orderNumberCancel = []
      this.modalSuccessCancelOrder = false
      this.$store.commit('openLoader')
      await this.resetAndLoad()
      // await this.getItemProductB2C()
      // await this.getOrderDocument()
    },
    async confirmCancel () {
      this.modalAwaitCancelOrder = false
      if (this.cancelType === 'no payment') {
        this.$store.commit('openLoader')
        const data = {
          order_number: this.orderNumberCancel.order_platform_number === null || this.orderNumberCancel.order_platform_number === undefined || this.orderNumberCancel.order_platform_number === '' ? this.orderNumberCancel.payment_transaction_number : this.orderNumberCancel.order_platform_number,
          remark: this.reason
        }
        // console.log(data, 'data')
        await this.$store.dispatch('actionCancelOrder', data)
        var responseNopayment = this.$store.state.ModuleOrder.stateCancelOrder
        if (responseNopayment.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalSuccessCancelOrder = true
        } else {
          this.$store.commit('closeLoader')
          this.reason = ''
          this.$swal.fire({
            icon: 'error',
            text: responseNopayment.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else if (this.cancelType === 'payment') {
        this.$store.commit('openLoader')
        const data = {
          order_number: this.orderNumberCancel,
          account_name: this.urName,
          phone: this.telNumber,
          bank_code: this.bankNameRefund,
          account_no: this.bankNumberRefund,
          remark: this.reasonRefund
        }
        await this.$store.dispatch('actionCancelOrder', data)
        var response = this.$store.state.ModuleOrder.stateCancelOrder
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.modalSuccessCancelOrder = true
        } else {
          this.cancelModalStep = 1
          this.$store.commit('closeLoader')
          this.$swal.fire({
            icon: 'error',
            text: response.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.cancelModalStep = 1
        this.orderNumberCancel = []
        this.urName = ''
        this.bankNameRefund = ''
        this.telNumber = ''
        this.bankNumberRefund = ''
        this.reasonRefund = ''
        this.reason = ''
        this.$swal.fire({
          icon: 'error',
          text: `${this.$t('MyOrders.UnableCancel')}`,
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    closeDialogConfirmCancel () {
      this.cancelModalStep = 1
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.reasonRefund = ''
      this.orderNumberCancel = []
      this.reason = ''
      this.modalAwaitCancelOrder = false
    },
    async openDialogConfirmCancel (val) {
      this.cancelType = ''
      this.cancelType = val
      this.cancelModalStep = 2
      if (this.cancelType === 'payment') {
        await this.getPaymentUsers()
      }
      // this.modalInputReason = false
      // this.modalAwaitCancelOrder = true
    },
    openDialogConfirmCancelStep2 () {
      this.modalInputReasonRefund = false
      this.modalAwaitCancelOrder = true
    },
    closemodalInputReasonRefund () {
      this.orderNumberCancel = []
      this.urName = ''
      this.bankNameRefund = ''
      this.telNumber = ''
      this.bankNumberRefund = ''
      this.modalInputReasonRefund = false
      this.cancelModalStep = 1
      this.reasonRefund = ''
    },
    closemodalInputReason () {
      this.orderNumberCancel = []
      this.reason = ''
      this.modalInputReason = false
    },
    resetAndLoad () {
      this.DataTable = []
      this.lastId = ''
      this.stopLazyLoad = false
      this.loadData()
    },
    async loadData () {
      if (this.isLoading || this.stopLazyLoad) return
      this.isLoading = true
      await this.ListDataTable()
      this.isLoading = false
    },
    scrollToTargetSection () {
      const target = this.$refs.targetSection // หา div ที่อยู่ล่างสุด
      if (target && this.DataTable.length > 0) {
        const rect = target.getBoundingClientRect() // ตรวจสอบตำแหน่งของ div
        const isVisible = rect.top <= window.innerHeight && rect.bottom >= 0 // ตรวจสอบว่า div อยู่ใน viewport หรือไม่

        if (isVisible && this.stopLazyLoad !== true) {
          this.loadData() // โหลดข้อมูลเพิ่มเติมเมื่อเลื่อนมาถึง div ล่างสุด
        }
      }
    },
    handleScroll () {
      const el = this.$refs.scrollContainer
      if (el.scrollTop + el.clientHeight >= el.scrollHeight - 10) {
        console.log('ถึงขอบล่างสุดแล้ว 🎉')
      }
    },
    async reviewProduct (orderNumber) {
      const dataForReview = {
        expired_review: '',
        order_number: orderNumber,
        status: 'waiting_review'
      }
      await this.$refs.ModalReviewProduct.open(dataForReview, orderNumber, 'create', 'ext_buyer')
    },
    async addtoCart (id) {
      console.log('id', id)
      this.$store.commit('openLoader')
      var data = {
        order_number: id,
        role_user: 'ext_buyer',
        company_id: -1,
        com_perm_id: -1
      }
      await this.$store.dispatch('actionsRepeatOrder', data)
      const response = await this.$store.state.ModuleCart.stateRepeatOrder
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/shoppingcart' }).catch(() => {})
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: `${this.$t('MyOrders.textError')}`
        })
      }
    },
    testClick () {
      console.log('testClick')
    },
    linkToURLTracking (url) {
      this.urlTracking = url
      setTimeout(() => {
        document.getElementById('urlTracking').click()
      }, 200)
    },
    copyClipboard () {
      const track = document.getElementById('trackingNumber')
      // Select the text field
      track.select()
      track.setSelectionRange(0, 99999) // For mobile devices

      // Copy the text inside the text field
      navigator.clipboard.writeText(track.value)
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        position: 'center',
        icon: 'success',
        title: `${this.$t('MyOrders.textCopy')}`
      })
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async acceptProduct (order) {
      var data = {
        payment_transaction_number: order.order_number,
        order_number: order.order_number,
        role_user: 'ext_buyer',
        seller_shop_id: '',
        reason: '',
        status: 'accepted'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      // console.log('res', res)
      if (res.message === 'Update status success.') {
        await this.$store.commit('closeLoader')
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: `${this.$t('MyOrders.textConfirmReceipt')}`
        })
        await this.resetAndLoad()
        const dataForReview = {
          expired_review: '',
          order_number: order.order_number,
          status: 'waiting_review'
        }
        await this.$refs.ModalReviewProduct.open(dataForReview, order.order_number, 'create', 'ext_buyer')
        // if (this.MobileSize === false) {
        //   this.$router.push({ path: '/reviewBuyer' }).catch(() => {})
        // } else {
        //   this.$router.push({ path: '/reviewBuyerMobile' }).catch(() => {})
        // }
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      }
    },
    exportFile () {
      // console.log('orderList.export_reports', this.orderList.export_reports)
      var url = this.orderListData.export_reports
      // window.open(url)
      window.location.href = url
    },
    async closeModalContractStartDate () {
      if (this.startDate !== '') {
        this.modalContractStartDate = false
        this.contractStartDate = ''
        this.startDate = ''
        this.$store.commit('openLoader')
        await this.resetAndLoad()
      } else {
        this.modalContractStartDate = false
        this.contractStartDate = ''
      }
    },
    async setValueContractStartDate (val) {
      if (val !== '') {
        // console.log('val', val)
        this.$refs.dialogContractStartDate.save(val)
        this.searchContractStartDate = val
        // console.log(this.searchDateNotFormat)
        this.contractStartDate = this.formatDateToShow(val)
        // console.log('contractStartDate', this.contractStartDate)
        this.$store.commit('openLoader')
        await this.resetAndLoad()
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          text: `${this.$t('MyOrders.textPlsSelect')}`
        })
      }
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = this.$i18n.locale === 'th' ? parseInt(year) + 543 : year
      return `${day}/${month}/${yearChange}`
    },
    async searchText (text) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        this.search = text
        this.$store.commit('openLoader')
        await this.resetAndLoad()
      }, 500)
    },
    async searchList (item) {
      this.statusType = item
      if (this.statusType === '') {
        this.StateStatus = 'ทั้งหมด'
      } else if (this.statusType === 'Success') {
        this.StateStatus = 'ชำระเงินสำเร็จ'
      } else if (this.statusType === 'Cancel') {
        this.StateStatus = 'ยกเลิกสินค้า'
      } else if (this.statusType === 'Pending') {
        this.StateStatus = 'รออนุมัติ'
      } else if (this.statusType === 'Approve') {
        this.StateStatus = 'อนุมัติ'
      } else if (this.statusType === 'Fail') {
        this.StateStatus = 'ชำระเงินไม่สำเร็จ'
      } else if (this.statusType === 'Not Paid') {
        this.StateStatus = 'ยังไม่ชำระเงิน'
      }
      this.$store.commit('openLoader')
      await this.resetAndLoad()
    },
    async ListDataTable () {
      if (this.check === 'check') {
        this.$store.commit('openLoader')
      }
      const data = {
        role_user: 'ext_buyer',
        search_keyword: this.search,
        order_status: this.statusType,
        create_date: this.contractStartDate,
        limit: '5',
        last_id: this.lastId || '' // ใช้ lastId ที่เก็บไว้
      }

      await this.$store.dispatch('actionPONewListBuyer', data)
      const response = this.$store.state.ModuleOrder.statePONewListBuyer

      this.$store.commit('closeLoader')

      if (response.code !== 401) {
        const resData = response.data
        const newList = resData && resData.all ? resData.all : []
        this.orderListData = resData

        // ถ้าเป็นการโหลดรอบแรก
        if (this.lastId === '') {
          this.DataTable = []
          this.DataTable = newList
        } else {
          this.DataTable = [...this.DataTable, ...newList]
        }
        // อัปเดต lastId ถ้ามีข้อมูลใหม่
        if (resData && resData.next_cursor && resData.next_cursor.last_id) {
          this.lastId = resData.next_cursor.last_id
        }

        // หยุดโหลดถ้าข้อมูลที่ได้มาน้อยกว่า limit
        if (newList.length < 5) {
          this.stopLazyLoad = true
        }
        // เพิ่ม logic ของคุณต่อจากนี้ได้ เช่น นับจำนวน
        this.countOrderAll = this.DataTable.length
        if (this.check === 'check') {
          this.checkCountAll = this.orderListData.all.length
          this.check = ''
        }
        if (this.checkCountAll !== 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else {
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            icon: 'warning',
            text: response.message
          })
        }
      }
    },
    async CheckAcceptProductData (item) {
      this.$store.commit('openLoader')
      var data = {
        payment_transaction_number: item.order_platform_number === undefined || item.order_platform_number === null ? item.order_number : item.order_platform_number
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      if (res.result === 'SUCCESS') {
        this.checkAcceptProduct = res.data
        // console.log('checkAcceptProduct', this.checkAcceptProduct)
        this.acceptProduct(item)
        // this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', text: res.message })
        this.checkAcceptProduct = []
        this.$router.push('/pobuyerProfile')
      }
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async UpdateStatusBuyer (item) {
      if (item.seller_sent_status === 'not_sent') {
        this.$swal.fire({
          icon: 'warning',
          text: `${this.$t('MyOrders.textNotSend')}`,
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      } else {
        const update = {
          order_number: item.order_number,
          buyer_received_status: item.buyer_received_status
        }
        await this.$store.dispatch('actionUpdateStatusBuyer', update)
        this.$swal.fire({
          icon: 'success',
          text: `${this.$t('MyOrders.textSaveSuccess')}`,
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      }
    },
    async GoToPayment (item) {
      const PaymentID = {
        payment_transaction_number: item.payment_transaction_number
      }
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    async GetETax (val) {
      // ของใหม่
      // ป้องกันการทำงานของลิงก์เริ่มต้น
      // event.preventDefault()

      var data = {
        transactionCode: val.transaction_code
      }
      await this.$store.dispatch('ActionsGetETaxPDF', data)
      const response = await this.$store.state.ModuleCart.stateGetETaxPDF
      if (response.result === 'OK') {
        if (response.etaxResponse.status === 'OK') {
          var pdfUrl = ''
          if (response.etaxResponse.urlPdf !== undefined) {
            // this.pdfUrl = response.etaxResponse.urlPdf
            pdfUrl = response.etaxResponse.urlPdf
          } else {
            // this.pdfUrl = response.etaxResponse.pdfURL
            pdfUrl = response.etaxResponse.pdfURL
          }
          this.downloadLink = pdfUrl
          setTimeout(() => {
            document.getElementById('downloadLink').click()
          }, 500)
        }
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: `${this.$t('MyOrders.textNotFoundInvoice')}`
        })
      }
      // ของเก่า
      // var data = {
      //   // transactionCode: '73e2aa7e-68b6-47b0-5079-4ecf9ace4540'
      //   transactionCode: val.transaction_code
      // }
      // await this.$store.dispatch('ActionsGetETaxPDF', data)
      // var response = await this.$store.state.ModuleCart.stateGetETaxPDF
      // // console.log('response', response)
      // if (response.result === 'OK') {
      //   if (response.etaxResponse.status === 'OK') {
      //     if (response.etaxResponse.urlPdf !== undefined) {
      //       window.open(`${response.etaxResponse.urlPdf}`, '_blank')
      //       // console.log('response', response.etaxResponse.urlPdf)
      //     } else {
      //       window.open(`${response.etaxResponse.pdfURL}`, '_blank')
      //     }
      //     // window.open(`${response.etaxResponse.urlPdf}`)
      //     // console.log('response', response.etaxResponse.urlPdf)
      //   }
      // } else {
      //   this.$swal.fire({
      //     toast: true,
      //     showConfirmButton: false,
      //     timer: 1500,
      //     timerProgressBar: true,
      //     icon: 'error',
      //     title: 'ไม่พบเอกสารใบกำกับภาษี'
      //   })
      // }
      // const timeoutId = setTimeout(async () => {
      // }, 1500)
      // console.log('4', timeoutId)
      // if (timeoutId > 1500) {
      //   this.$swal.fire({
      //     toast: true,
      //     showConfirmButton: false,
      //     timer: 1500,
      //     timerProgressBar: true,
      //     icon: 'error',
      //     title: 'ไม่พบเอกสารใบกำกับภาษี'
      //   })
      // }
    },
    async orderDetail (val) {
      window.open(`${val.QT_order}`)
    },
    // SelectDetailOrder (item) {
    //   this.StateStatus = item
    //   this.page = 1
    // },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.resetAndLoad()
    },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateOrderDetailData
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem('MyOrderDetail', Encode.encode(JSON.stringify(response.data)))
        this.$router.push('/myorderdetail')
      }
    },
    goDetailPO (item) {
      var orderNumber = item.payment_transaction_number
      var orderPlatformNumber = item.order_platform_number === null ? '-' : item.order_platform_number
      if (this.MobileSize === false) {
        this.$router.push({ path: `/pobuyerdetail?orderNumber=${orderNumber}&orderPlatformNumber=${orderPlatformNumber}` }).catch(() => {})
        window.scrollTo(0, 0)
      } else {
        this.$router.push({ path: `/pobuyerdetailMobile?orderNumber=${orderNumber}&orderPlatformNumber=${orderPlatformNumber}` }).catch(() => {})
        window.scrollTo(0, 0)
      }
      // if (item.transaction_status === 'Pending' || item.transaction_status === 'Approve') {
      //   if (this.MobileSize === false) {
      //     this.$router.push({ path: '/pobuyerdetailapprove' }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: '/pobuyerdetailapproveMobile' }).catch(() => {})
      //   }
      // } else {
      //   var orderNumber = item.payment_transaction_number
      //   if (this.MobileSize === false) {
      //     this.$router.push({ path: `/pobuyerdetail?orderNumber=${orderNumber}` }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: `/pobuyerdetailMobile?orderNumber=${orderNumber}` }).catch(() => {})
      //   }
      // }
    },
    gotoRefund () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/refundOrderBuyer' }).catch(() => {})
      } else {
        this.$router.push({ path: '/refundOrderBuyerMobile' }).catch(() => {})
      }
    },
    async CheckStockBeforeOpenModalPayment (orderNumber, type) {
      var messageCheckError = ''
      var i
      var data
      if (type === 'payment') {
        data = {
          payment_transaction_number: orderNumber.order_platform_number !== null && orderNumber.order_platform_number !== undefined ? orderNumber.order_platform_number : orderNumber.order_number
        }
      } else {
        data = {
          payment_transaction_number: orderNumber.order_number
        }
      }
      await this.$store.dispatch('actionsCheckStockBeforePayment', data)
      const response = await this.$store.state.ModuleCart.stateCheckStockBeforePayment
      if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
        // await this.getPaymentMethod(orderNumber)
        if (type === 'payment') {
          this.dialogChooesPayType = true
          this.paymentNumber = orderNumber.order_platform_number !== null && orderNumber.order_platform_number !== undefined ? orderNumber.order_platform_number : orderNumber.order_number
        } else if (type === 'addtocart') {
          this.addtoCart(orderNumber.order_number)
        }
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
        for (i = 0; i < response.data.product_free.length; i++) {
          messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: `${this.$t('MyOrders.textNotFree')}` + messageCheckError + `${this.$t('MyOrders.textContact')}`
        })
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: `${this.$t('MyOrders.textNoDataProduct')}` + messageCheckError + `${this.$t('MyOrders.textContact')}`
        })
      } else {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: `${this.$t('MyOrders.textNotEnough')}` + messageCheckError + `${this.$t('MyOrders.textContact')}`
        })
      }
    },
    confirmPayment () {
      var dataRole = JSON.parse((localStorage.getItem('roleUser'))).role
      // console.log('dataRole', dataRole)
      if (dataRole === 'ext_buyer') {
        this.dialogConfirm = true
        this.dialogChooesPayType = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: `${this.$t('MyOrders.textUnablePayment')}`,
          text: `${this.$t('MyOrders.textForGeneral')}`
        })
      }
    },
    openDialogPayment () {
      this.dialogChooesPayType = false
      this.radioPayment = 'no'
      this.paymentNumber = ''
    },
    async GetCC (paymentTypeData) {
      // console.log('GetCC', this.radioCreditTerm)
      this.$store.commit('openLoader')
      this.dialogConfirm = false
      var data
      var resCC
      var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
      if (this.radioPayment !== 'radio-installment') {
        this.radioCreditTerm = ''
      }
      data = {
        go_local: goLocalValue,
        payment_transaction_number: this.paymentNumber,
        term: this.radioCreditTerm ? this.radioCreditTerm : ''
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsGetCCV2', data)
      resCC = await this.$store.state.ModuleCart.stateGetCCV2
      if (resCC.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        localStorage.setItem('PaymentData', Encode.encode(resCC.data))
        this.$router.push('/RedirectPaymentPage').catch(() => {})
      } else if (resCC.message === 'ERROR ระบบ Payment มีปัญหาไม่สามารถส่งหรือรับข้อมูลได้') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: `${this.$t('MyOrders.textErrorPayment')}`,
          text: `${this.$t('MyOrders.textUnablePayment')}`
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: `${this.$t('MyOrders.textUnablePayment')}`
        })
      }
      this.$store.commit('closeLoader')
    },
    async GetQRCode (paymentTypeData) {
      // console.log('GetQRCode', paymentTypeData)
      const paymentType = paymentTypeData
      if (paymentType === 'cashPayment') {
        await this.openDialogQR()
      }
    },
    async openDialogQR () {
      // console.log('openDialogQR')
      var data
      var resQR = ''
      data = {
        payment_transaction_number: this.paymentNumber
      }
      await this.$store.dispatch('actionsGetQRCodeV2', data)
      resQR = await this.$store.state.ModuleCart.stateGetQRCodeV2
      if (resQR.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        this.netPrice = resQR.data.net_price
        this.Ref1 = resQR.data.ref1
        this.Ref2 = resQR.data.ref2
        this.imageBase64 = 'data:image/png;base64,' + resQR.data.img_base
        this.ImageQR = ''
        this.ImageQR = await resQR.data.img_base64
        setTimeout(() => {
          this.showIMG(this.ImageQR)
        }, 1000)
        // this.DialogQR = true
      } else if (resQR.result === 'FAILED') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: `${this.$t('MyOrders.textPaymentError')}`
        })
        this.DialogQR = false
        this.dialogConfirm = false
      } else {
        if (resQR.message === 'This user is Unauthorized' || resQR.message === 'This user is unauthorized.' || resQR.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: `${this.$t('MyOrders.Fail')}`
          })
          this.DialogQR = false
          this.dialogConfirm = false
        }
        // }
        // this.$store.commit('closeLoader')
      }
    },
    async checkOrderResult (orderNumber) {
      const data = {
        payment_transaction_number: this.paymentNumber
      }
      var value = orderNumber
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: `${this.$t('MyOrders.Paymentincomplete')}`
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    async updateOptions (options) {
      this.options = options
      await this.ListDataTable()
    },
    async showIMG (ImageQR) {
      // console.log('ImageQR', ImageQR)
      this.Image = ImageQR
      // console.log('ImageQR', ImageQR)
      this.DialogQR = true
      this.$store.commit('closeLoader')
      var data
      data = {
        payment_transaction_number: this.paymentNumber
      }
      var value = data.payment_transaction_number
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: `${this.$t('MyOrders.Paymentincomplete')}`
          })
        }
      }
    },
    async getPaymentMethod (orderNumber) {
      var data = {
        order_platform: orderNumber
      }
      await this.$store.dispatch('actionGetPaymentMethodWithOrderPlatform', data)
      var response = await this.$store.state.ModuleOrder.stateGetPaymentMethodWithOrderPlatform
      console.log(response, 'success')
      if (response.code === 200) {
        this.paymentMethods = response.data
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: response.message
        })
      }
    },
    async closeDialogQR () {
      // this.$store.commit('openLoader')
      this.DialogQR = false
      this.CloseDialog = true
      this.dialogConfirm = false
      this.$store.commit('closeLoader')
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  // ::v-deep table {
  //   tbody {
  //     tr {
  //       td:nth-child(12) {
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //         z-index: 10;
  //         background: white;
  //       }
  //     }
  //   }
  //   thead {
  //     tr {
  //       th:nth-child(1) {
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //         z-index: 10;
  //         background: white;
  //       }
  //     }
  //   }
  //   thead {
  //     tr {
  //       th:nth-child(12) {
  //         z-index: 11;
  //         background: white;
  //         position: sticky !important;
  //         position: -webkit-sticky !important;
  //         right: 0;
  //       }
  //     }
  //   }
  // }
  ::v-deep .shop-table table {
    thead {
      tr th:nth-child(1) {
        background: #E6F5F3 !important;
        border-style: none !important;
      }
    }
    tbody {
      tr {
        td:nth-child(12) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
          th {
            white-space: nowrap;
          }
          th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
          }
      }
    }
    thead {
      tr {
          th:nth-child(12) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          }
      }
    }
  }
::v-deep .product-table table {
  thead {
  tr {
      th {
        white-space: nowrap;
      }
  }
  }
}
</style>

<style scoped>
::v-deep .red-border-row {
  border-bottom: 1px solid #e0e0e0;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.fontStatus {
font-size: 14px;
font-weight: 400;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.fontDeskSearch {
  font-size: 16px;
  color: #333333;
  font-weight: 500;
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
