import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async ListMemberLevelRank (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}/api/list/member/level-rank`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListMemberRanked (data) {
    const auth = GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}/api/list/member/list_member_ranked`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
