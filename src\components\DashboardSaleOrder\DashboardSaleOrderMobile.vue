<template>
  <v-container>
    <v-card elevation="0">
      <v-row no-gutters class="d-flex" align="center">
        <v-col cols="8">
          <v-card-title style="font-size: 18px; font-style: normal; font-weight: 700;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon>แดชบอร์ดฝ่ายขาย</v-card-title>
        </v-col>
        <v-col cols="4" class="pr-3 d-flex justify-end">
          <v-btn @click="openSelectFilter()"
            elevation="0"
            outlined
            height="32px"
            class="custom-btn"
            style="border-radius: 40px; background: #FFF"
          >
            <v-icon style="color:#27AB9C">mdi-filter-outline</v-icon>
            <span style="color:#27AB9C" class="exportButtonText">ตัวกรอง</span>
          </v-btn>
        </v-col>
      </v-row>
      <v-row no-gutters>
        <v-col cols="12" class="pr-3" align="end">
          <div v-if="selectedItem === 'รายการสั่งซื้อสินค้าทั้งหมด'">
            <v-btn class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'orderlist', 'รายการสั่งซื้อสินค้าทั้งหมด', tabsType, saleList, customerGroup, customerList)" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 สินค้าขายดี'">
            <v-btn class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestsold', 'TOP_10_สินค้าขายดี', tabsType, saleList, customerGroup, customerList)" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 มูลค่าการสั่งซื้อ'">
            <v-btn class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestvalue', 'TOP_10_มูลค่าการสั่งซื้อ', tabsType, saleList, customerGroup, customerList)" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด'">
            <v-btn class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestcustomer', 'TOP_10_ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด', tabsType, saleList, customerGroup, customerList)" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 Sales ที่มียอดค่าสะสมเยอะที่สุด'">
            <v-btn @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestsale', 'TOP_10_Sales_ที่มียอดค่าสะสมเยอะที่สุด')" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
          <div v-else-if="selectedItem === 'TOP 10 กลุ่มลูกค้า'">
            <v-btn class="ml-1" @click="getExportDashboard(startDate, endDate, shopID, dateFilter, 'bestcustomergroup', 'TOP_10_กลุ่มลูกค้า', tabsType, saleList, customerGroup, customerList)" height="32px" style="border-radius: 40px; background: #27AB9C">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="exportButtonText ml-1">Export</span>
          </v-btn>
          </div>
        </v-col>
      </v-row>
      <v-row class="pl-4 pr-4">
        <v-col cols="12" class="pb-0">
          <v-select
            v-model="selectedItem"
            :items="selectedItemsHeader"
            class="subTitleText"
            height="22px"
            dense
            :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
          ></v-select>
          <!-- <span class="subTitleText ml-4">ข้อมูลรายได้</span> -->
        </v-col>
      </v-row>
      <v-row class="pl-4 pr-4">
        <v-col>
          <div v-if="selectedItem === 'ข้อมูลรายได้'">
            <v-card width="100%" height="100%" elevation="0">
              <!-- รายชื่อเซลล์, กลุ่มลูกค้า, รายชื่อลูกค้า -->
              <v-row>
                <v-col cols="6">
                  <v-row>
                    <span style="font-size: 16px; font-weight: 500;" class="ml-3 mr-2 mt-2">รายชื่อเซลล์ :</span>
                    <v-autocomplete
                      class="mr-4 ml-2 custom-autocomplete"
                      v-model="saleList"
                      :items="itemsSaleList"
                      style="border-radius: 8px;"
                      dense
                      outlined
                      :readonly="filterSaleReadOnly"
                      item-text="sale_name"
                      item-value="sale_name"
                      @change="changeCustomerName()"
                    >
                    <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-autocomplete>
                  </v-row>
                </v-col>
                <v-col cols="6">
                  <v-row>
                    <span style="font-size: 16px; font-weight: 500;" class="ml-1 mr-2 mt-2">กลุ่มลูกค้า :</span>
                    <v-autocomplete
                      class="mr-4 custom-autocomplete"
                      v-model="customerGroup"
                      :items="itemsCustomerGroup"
                      style="border-radius: 8px;"
                      placeholder="เลือกกลุ่มลูกค้า"
                      dense
                      outlined
                      :disabled="filterGroupDisabled"
                      item-text="sale_name"
                      item-value="sale_name"
                      @change="changeCustomerName()"
                    >
                    <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-autocomplete>
                  </v-row>
                </v-col>
                <v-col cols="6">
                  <v-row>
                    <span style="font-size: 16px; font-weight: 500;" class="ml-3 mr-2 mt-2">รายชื่อลูกค้า :</span>
                    <v-autocomplete
                      class="mr-4 ml-2 custom-autocomplete"
                      v-model="customerList"
                      :items="itemsCustomerList"
                      style="border-radius: 8px;"
                      placeholder="เลือกรายชื่อลูกค้า"
                      dense
                      outlined
                      :disabled="filterCusDisabled"
                      item-text="sale_name"
                      item-value="sale_name"
                      @change="changeCustomerName()"
                    >
                    <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-autocomplete>
                  </v-row>
                </v-col>
                <v-col align="end" class="pr-5 mt-5">
                  <v-btn @click="resetFilter" color="primary" dark>
                    <v-icon>mdi-cached</v-icon>
                  </v-btn>
                </v-col>
              </v-row>
            </v-card>
            <!-- start ข้อมูลรายได้ -->
            <v-card class="mb-4" style="border-radius: 8px;" elevation="0">
              <ul class="nav nav-tabs mt-5 pl-0" id="myTab" role="tablist" style="display: flex; flex-wrap: nowrap;">
                <!-- ข้อมูลรายได้ เริ่ม -->
                <li class="nav-item" role="presentation" style="flex: -1;">
                  <button class="nav-link" :class="{ 'active': active_tab === 0, 'grey-tab': active_tab !== 0 }" @click="active_tab = 0, changeTabsType()" id="income-tab" data-bs-toggle="tab" data-bs-target="#income" type="button" role="tab" aria-controls="income" aria-selected="true">
                    <v-avatar rounded size="24">
                      <v-img contain :src="passiveIncomeIconPath"></v-img>
                    </v-avatar>
                    <span v-if="customerGroup === 'ทั้งหมด' && customerList === 'ทั้งหมด'" :class="{ 'subTitleText': active_tab === 0, 'grey-tab': active_tab !== 0 }" class="ml-2">ข้อมูลรายได้</span>
                    <span v-else :class="{ 'subTitleText': active_tab === 0, 'grey-tab': active_tab !== 0 }" class="ml-2">ข้อมูลรายได้ : {{ customerName }}</span>
                  </button>
                </li>
                <!-- ข้อมูลรายได้ จบ -->
                <!-- ข้อมูลการขาย เริ่ม -->
                <li class="nav-item" role="presentation" style="flex: -1;">
                  <button class="nav-link" :class="{ 'active': active_tab === 1, 'grey-tab': active_tab !== 1 }" @click="active_tab = 1, changeTabsType()" id="sales-tab" data-bs-toggle="tab" data-bs-target="#sales" type="button" role="tab" aria-controls="sales" aria-selected="false">
                    <v-avatar rounded size="24">
                      <v-img contain :src="saleOrderDetail"></v-img>
                    </v-avatar>
                    <span v-if="customerGroup === 'ทั้งหมด' && customerList === 'ทั้งหมด'" :class="{ 'subTitleText': active_tab === 1, 'grey-tab': active_tab !== 1 }" class="ml-2">ข้อมูลการขาย</span>
                    <span v-else :class="{ 'subTitleText': active_tab === 1, 'grey-tab': active_tab !== 1 }" class="ml-2">ข้อมูลการขาย : {{ customerName }}</span>
                  </button>
                </li>
                <!-- ข้อมูลการขาย จบ -->
              </ul>
              <div class="tab-content" id="myTabContent">
                <div class="tab-pane fade" :class="{ 'show active': active_tab === 0 }" id="income" role="tabpanel" aria-labelledby="income-tab"></div>
                <div class="tab-pane fade" :class="{ 'show active': active_tab === 1 }" id="sales" role="tabpanel" aria-labelledby="sales-tab"></div>
              </div>
            </v-card>

            <!-- tabs 0 ข้อมูลรายได้ -->
            <v-card v-show="active_tab === 0" class="mb-6" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;">
              <v-row>
                <!-- จำนวนรายการสั่งซื้อทั้งหมด -->
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #E9FBFB; height: 70px;" elevation="0">
                    <v-row>
                      <v-col cols="7" align="start" class="pt-5 pb-1">
                        <span class="ml-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333">จำนวนรายการสั่งซื้อทั้งหมด </span>
                      </v-col>
                      <v-col cols="5" align="end" class="pt-3 pb-1">
                        <span style="font-size: 22px; font-style: normal; font-weight: 700; color:#2ADAC5">{{ totalOrder }}</span>
                        <span class="ml-2 mr-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333"> รายการ</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
                <!-- ยอดขาย -->
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #F1F6FA; height: 70px;" elevation="0">
                    <v-row>
                      <v-col cols="5" align="start" class="pt-5 pb-1">
                        <span class="ml-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333">ยอดขายทั้งหมด </span>
                      </v-col>
                      <v-col cols="7" align="end" class="pt-3 pb-1">
                        <span style="font-size: 22px; font-style: normal; font-weight: 700; color:#6597D6">{{ Number(totalSale).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                        <span class="ml-2 mr-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333"> บาท</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <!-- กราฟ -->
              <v-layout align-center justify-center>
                <v-flex>
                  <v-card class="mt-2" elevation="0">
                    <v-card-title>
                      <v-row>
                        <v-col cols="12" align="end">
                          <v-icon class="mdi-rotate-90" color="#2ADAC5" size="30">mdi-source-commit</v-icon>
                          <span class="ml-2 mr-3" style="font-size: 12px; font-style: normal; font-weight: 400;">จำนวนการสั่งซื้อ</span>
                          <v-icon class="mdi-rotate-90" color="#6597D4" size="30">mdi-source-commit</v-icon>
                          <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">ยอดขาย</span>
                        </v-col>
                      </v-row>
                    </v-card-title>
                    <v-card-text>
                      <apexchart  height="400" type="bar" :options="chartOptions" :series="chartSeries_tab1"></apexchart>
                    </v-card-text>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-card>

            <!-- tabs 1 ข้อมูลการขาย -->
            <v-card v-show="active_tab === 1" class="mb-6" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;">
              <v-row>
                <!-- จำนวนรายการสั่งซื้อทั้งหมด -->
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #E9FBFB; height: 70px;" elevation="0">
                    <v-row>
                      <v-col cols="7" align="start" class="pt-5 pb-1">
                        <span class="ml-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333">จำนวนรายการสั่งซื้อทั้งหมด </span>
                      </v-col>
                      <v-col cols="5" align="end" class="pt-3 pb-1">
                        <span style="font-size: 22px; font-style: normal; font-weight: 700; color:#2ADAC5">{{ summaryOrder }}</span>
                        <span class="ml-2 mr-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333"> รายการ</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
                <!-- จำนวนใบเสนอราคา -->
                <v-col align="center" justify="center" cols="12">
                  <v-card style="border-radius: 8px;background: #FFF2F0; height: 70px;" elevation="0">
                    <v-row>
                      <v-col cols="7" align="start" class="pt-5 pb-1">
                        <span class="ml-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333">จำนวนใบเสนอราคา </span>
                      </v-col>
                      <v-col cols="5" align="end" class="pt-3 pb-1">
                        <span style="font-size: 22px; font-style: normal; font-weight: 700; color:#FE9C8F">{{ summaryOrder }}</span>
                        <span class="ml-2 mr-3" style="font-size: 14px; font-style: normal; font-weight: 600; color:#333333"> ใบ</span>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <!-- กราฟ -->
              <v-layout align-center justify-center>
                <v-flex>
                  <v-card class="mt-2" elevation="0">
                    <v-card-title>
                      <v-row>
                        <v-col cols="12" align="end">
                          <v-icon class="mdi-rotate-90" color="#2ADAC5" size="30">mdi-source-commit</v-icon>
                          <span class="ml-2 mr-3" style="font-size: 12px; font-style: normal; font-weight: 400;">จำนวนการสั่งซื้อ</span>
                          <v-icon class="mdi-rotate-90" color="#FE9C8F" size="30">mdi-source-commit</v-icon>
                          <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">จำนวนใบเสนอราคา</span>
                        </v-col>
                      </v-row>
                    </v-card-title>
                    <v-card-text>
                      <apexchart  height="400" type="bar" :options="chartOptions2" :series="chartSeries_tab2"></apexchart>
                    </v-card-text>
                  </v-card>
                </v-flex>
              </v-layout>
            </v-card>
            <!-- end ข้อมูลรายได้ -->
          </div>

          <div v-if="selectedItem === 'รายการสั่งซื้อสินค้าทั้งหมด'">
          <!-- start รายการสั่งซื้อสินค้าทั้งหมด -->
          <v-row>
            <v-col cols="12" class="pt-0">
              <span v-show="active_tab === 0" class="subTitleText ml-2">รายการสั่งซื้อสินค้าทั้งหมด ({{ totalOrder }} รายการ)</span>
              <span v-show="active_tab === 1" class="subTitleText ml-2">รายการสั่งซื้อสินค้าทั้งหมด ({{ summaryOrder }} รายการ)</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-data-table
                v-if="saleOrder.length !== 0"
                :headers="headers"
                :items="saleOrder"
                :items-per-page="5"
                class="elevation-0"
                height="390px"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                item-key="i"
                >
                <template v-slot:item="{ item }">
                  <v-card class="mb-4" style="border-radius: 12px; border: 1px solid #F2F2F2;" elevation="0">
                    <v-card-text>
                      <v-row>
                        <v-col cols="12">
                          <v-row>
                            <v-col cols="6">
                              <b style="font-size: 14px;">รหัสผู้ซื้อ</b>
                            </v-col>
                            <v-col cols="6">
                              {{ item.id }}
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col cols="6">
                              <b style="font-size: 14px;">รายชื่อลูกค้า</b>
                            </v-col>
                            <v-col cols="6">
                              {{ item.buyer_name }}
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col cols="6">
                              <b style="font-size: 14px;">วันที่ทำรายการ</b>
                            </v-col>
                            <v-col cols="6">
                              {{ new Date(item.start_date_contract).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col cols="6">
                              <b style="font-size: 14px;">เลขที่ทำรายการสั่งซื้อ</b>
                            </v-col>
                            <v-col cols="6">
                              {{ item.order_number }}
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col cols="6">
                              <b style="font-size: 14px;">ราคา (บาท)</b>
                            </v-col>
                            <v-col cols="6">
                              {{ Number(item.total_price_no_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}
                            </v-col>
                          </v-row>
                          <v-row>
                            <v-col cols="6">
                            </v-col>
                            <v-col cols="6">
                              <v-btn class="pl-0" elevation="0" style="background: #FFF" @click="openDialog(item)">
                                <span style="font-size: 14px; color: #27AB9C">รายการสินค้า</span>
                              </v-btn>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </template>
              </v-data-table>
              <v-card v-else height="200px" elevation="0" class="d-flex align-center justify-center">
                <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
              </v-card>
            </v-col>
          </v-row>
          <!-- end รายการสั่งซื้อสินค้าทั้งหมด -->
          </div>
          <div v-if="selectedItem === 'TOP 10 สินค้าขายดี'">
          <!-- start TOP 10 สินค้าขายดี -->
            <v-card v-if="bestSeller.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <v-row>
                <v-col cols="12">
                  <v-row no-gutters>
                    <v-col v-for="(item, index) in bestSeller" :key="index" cols="12" class="mb-4">
                      <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <!-- 1 -->
                            <!-- Conditionally render specific avatar for index 0 -->
                            <v-avatar v-if="index === 0" rounded size="43">
                              <v-img contain :src="goldMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    1
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 2 -->
                            <v-avatar v-else-if="index === 1" rounded size="43">
                              <v-img contain :src="silverMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    2
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 3 -->
                            <v-avatar v-else-if="index === 2" rounded size="43">
                              <v-img contain :src="bronzeMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    3
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- For other indices, render a different avatar -->
                            <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 1 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                              <!-- <span>No image</span> -->
                              <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else rounded size="44" color="#FFF">
                              <v-img contain :src="item.product_image"></v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="two-lines">{{ item.product_name }}</span>
                            <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #27AB9C">
                                {{ Number( item.sold ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 สินค้าขายดี -->
          </div>
          <div v-if="selectedItem === 'TOP 10 มูลค่าการสั่งซื้อ'">
          <!-- start TOP 10 มูลค่าการสั่งซื้อ -->
            <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <v-row>
                <!-- First half -->
                <v-col cols="12">
                  <v-row>
                    <v-col v-for="(item, index) in orderValue" :key="index" cols="12">
                      <v-card height="100%" style="border: 1px solid #FDF9EC;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <v-avatar v-if="index === 0" rounded size="43">
                              <!-- Content for index 0 -->
                              <v-img contain :src="goldMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    1
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 2 -->
                            <v-avatar v-else-if="index === 1" rounded size="43">
                              <v-img contain :src="silverMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    2
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 3 -->
                            <v-avatar v-else-if="index === 2" rounded size="43">
                              <v-img contain :src="bronzeMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    3
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- For other indices, render a different avatar -->
                            <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 1 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF">
                              <!-- <span>No image</span> -->
                              <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else rounded size="44" color="#FFF">
                              <v-img contain :src="item.product_image"></v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="two-lines">{{ item.product_name }}</span>
                            <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #FE6F07">
                                {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 มูลค่าการสั่งซื้อ -->
          </div>
          <div v-if="selectedItem === 'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด'">
          <!-- start TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด -->
            <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <!-- Top three -->
              <v-row justify="center" class="mb-0 pb-0">
                <v-col cols="4" class="d-flex justify-end mb-0 mt-3 pb-0">
                  <v-card v-if="topBuyers.length >= 2" height="140px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
                    <v-row>
                      <v-col align="center">
                        <v-avatar rounded size="40" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                          <v-img contain :src="silverMedalIconPath">
                            <span
                              class="display-1 font-weight-bold"
                              style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                            >
                              <font style="font-size: 20px;">
                                2
                              </font>
                            </span>
                          </v-img>
                        </v-avatar>
                        <v-avatar v-if="topBuyers[1].user_image === null || topBuyers[1].user_image ===  'not found image'" rounded size="50" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </v-avatar>
                        <v-avatar v-else size="50" color="#FFF">
                          <v-img contain :src="topBuyers[1].user_image"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-0 pb-0">
                        <span class="one-lines">{{ topBuyers[1].buyer_name }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-1 pl-0 pr-0">
                        <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                          <span class="vchipFontSize" style="color: #1B5DD6">
                            <!-- {{ Number(topBuyers[1].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท -->
                            {{ formatPrice(topBuyers[1].sum_price) }} บาท
                          </span>
                        </v-chip>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>

                <v-col cols="4" class="d-flex justify-center mb-0 pb-0">
                  <v-card v-if="topBuyers.length >= 1" height="160px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
                    <v-row>
                      <v-col align="center">
                        <v-avatar rounded size="40" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                          <v-img contain :src="goldMedalIconPath">
                            <span
                              class="display-1 font-weight-bold"
                              style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                            >
                              <font style="font-size: 20px;">
                                1
                              </font>
                            </span>
                          </v-img>
                        </v-avatar>
                        <v-avatar v-if="topBuyers[0].user_image === null || topBuyers[0].user_image === 'not found image'" rounded size="60" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </v-avatar>
                        <v-avatar v-else size="60" color="#FFF">
                          <v-img contain :src="topBuyers[0].user_image"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-0 pb-0">
                        <span class="one-lines">{{ topBuyers[0].buyer_name }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-1 pl-0 pr-0">
                        <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                          <span class="vchipFontSize" style="color: #1B5DD6">
                            <!-- {{ Number(topBuyers[0].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท -->
                            {{ formatPrice(topBuyers[0].sum_price) }} บาท
                          </span>
                        </v-chip>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>

                <v-col cols="4" class="d-flex mb-0 mt-3 pb-0 mt-3">
                  <v-card v-if="topBuyers.length >= 3" height="140px" width="150px" elevation="0" style="border: 0px solid #EFECFD;">
                    <v-row>
                      <v-col align="center">
                        <v-avatar rounded size="40" style="position: absolute; top: 15%; left: 20%; transform: translate(-50%, -50%); z-index: 2;">
                          <v-img contain :src="bronzeMedalIconPath">
                            <span
                              class="display-1 font-weight-bold"
                              style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                            >
                              <font style="font-size: 20px;">
                                3
                              </font>
                            </span>
                          </v-img>
                        </v-avatar>
                        <v-avatar v-if="topBuyers[2].user_image === null || topBuyers[2].user_image === 'not found image'" rounded size="50" color="#FFF">
                          <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </v-avatar>
                        <v-avatar v-else size="50" color="#FFF">
                          <v-img contain :src="topBuyers[2].user_image"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-0 pb-0">
                        <span class="one-lines">{{ topBuyers[2].buyer_name }}</span>
                      </v-col>
                    </v-row>
                    <v-row>
                      <v-col align="center" class="pt-1 pl-0 pr-0">
                        <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                          <span class="vchipFontSize" style="color: #1B5DD6">
                            <!-- {{ Number(topBuyers[2].sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท -->
                            {{ formatPrice(topBuyers[2].sum_price) }} บาท
                          </span>
                        </v-chip>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
              <v-row class="mt-0 pt-0">
                <v-col cols="12">
                  <v-row>
                    <v-col v-for="(item, index) in topBuyers.slice(3, 10)" :key="index" cols="12">
                      <v-card height="100%" style="border: 1px solid #EFECFD;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 4 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.user_image === null || item.user_image === 'not found image'" rounded size="44" color="#FFF">
                              <!-- <span>No image</span> -->
                              <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else size="44" color="#FFF">
                              <v-img contain :src="item.user_image"></v-img>
                            </v-avatar>
                          </v-col>
                          <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="one-lines">{{ item.buyer_name }}</span>
                            <v-chip class="custom-chip" color="#4276FB0D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #1B5DD6">
                                {{ Number(item.sum_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด -->
          </div>
          <!-- <div v-if="selectedItem === 'TOP 10 ฝ่ายขายที่ยอดขายสะสมเยอะที่สุด'">
            start TOP 10 ฝ่ายขายที่ยอดขายสะสมเยอะที่สุด
            end TOP 10 ฝ่ายขายที่ยอดขายสะสมเยอะที่สุด
          </div> -->
          <div v-if="selectedItem === 'TOP 10 กลุ่มลูกค้า'">
          <!-- start TOP 10 กลุ่มลูกค้า -->
            <v-card v-if="orderValue.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
              <v-row>
                <!-- First half -->
                <v-col cols="12">
                  <v-row>
                    <v-col v-for="(item, index) in orderValue" :key="index" cols="12">
                      <v-card height="100%" style="border: 1px solid #FDF9EC;" elevation="0">
                        <v-row class="pa-1">
                          <v-col cols="2">
                            <v-avatar v-if="index === 0" rounded size="43">
                              <!-- Content for index 0 -->
                              <v-img contain :src="goldMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    1
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 2 -->
                            <v-avatar v-else-if="index === 1" rounded size="43">
                              <v-img contain :src="silverMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    2
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- 3 -->
                            <v-avatar v-else-if="index === 2" rounded size="43">
                              <v-img contain :src="bronzeMedalIconPath">
                                <span
                                  class="display-1 font-weight-bold"
                                  style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;"
                                >
                                  <font style="font-size: 20px;">
                                    3
                                  </font>
                                </span>
                              </v-img>
                            </v-avatar>
                            <!-- For other indices, render a different avatar -->
                            <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 1 }}
                            </v-avatar>
                          </v-col>
                          <!-- <v-col cols="2" class="pl-0">
                            <v-avatar v-if="item.product_image === null || item.product_image === 'not found image'" rounded size="44" color="#FFF"> -->
                              <!-- <span>No image</span> -->
                              <!-- <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                            </v-avatar>
                            <v-avatar v-else rounded size="44" color="#FFF">
                              <v-img contain :src="item.product_image"></v-img>
                            </v-avatar>
                          </v-col> -->
                          <v-col cols="9" class="pl-0 pr-0 d-flex justify-space-between align-center">
                            <span class="two-lines">{{ item.product_name }}</span>
                            <v-chip class="custom-chip" color="#D668030D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #FE6F07">
                                {{ Number(item.total_revenu).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else height="230px" elevation="0" class="d-flex align-center justify-center mt-1">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: #C4C4C4">ไม่มีรายการสั่งซื้อสินค้า</h1>
            </v-card>
          <!-- end TOP 10 มูลค่าการสั่งซื้อ -->
          </div>
        </v-col>
      </v-row>
    </v-card>

    <v-dialog v-model="selectDateFilter" width="300px" content-class="rounded" persistent>
      <v-card class="rounded-xl" height="300px" align="center">
        <v-toolbar align="center" color="#FFF" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#333">ตัวกรอง</font>
          </span>
          <v-btn icon dark @click="closeSelectFilter('readonly')">
            <v-icon color="#333">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card class="mt-6" style="background-color: #F3F5F7; border-radius: 12px;" elevation="0" width="90%" height="46px">
          <div v-if="selectedFilterDates === 'รายปี'">
            <v-row>
              <v-col cols="4" class="pr-0 pt-1" align="center">
                <v-card style="border-radius: 12px;" @click="selectedFilterDate('รายปี')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #27AB9C">รายปี</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายเดือน')" width="90%" height="38px" elevation="0">
                  <span class="pt-2" style="font-size: 16px; color: #CCCCCC">รายเดือน</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายวัน')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายวัน</span>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <div v-if="selectedFilterDates === 'รายเดือน'">
            <v-row>
              <v-col cols="4" class="pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายปี')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายปี</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pr-0 pt-1" align="center">
                <v-card style="border-radius: 12px;" @click="selectedFilterDate('รายเดือน')" width="90%" height="38px" elevation="0">
                  <span class="pt-2" style="font-size: 16px; color: #27AB9C">รายเดือน</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายวัน')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายวัน</span>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <div v-if="selectedFilterDates === 'รายวัน'">
            <v-row>
              <v-col cols="4" class="pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายปี')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #CCCCCC">รายปี</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pr-0 pt-1" align="center">
                <v-card style="background-color: #F3F5F7; border-radius: 12px;" @click="selectedFilterDate('รายเดือน')" width="90%" height="38px" elevation="0">
                  <span class="pt-2" style="font-size: 16px; color: #CCCCCC">รายเดือน</span>
                </v-card>
              </v-col>
              <v-col cols="4" class="pl-0 pt-1" align="center">
                <v-card style="border-radius: 12px;" @click="selectedFilterDate('รายวัน')" width="90%" height="38px" elevation="0">
                  <span style="font-size: 16px; color: #27AB9C">รายวัน</span>
                </v-card>
              </v-col>
            </v-row>
          </div>
          <v-row>
            <v-col>
              <!-- รายปี -->
              <div v-if="selectedFilterDates === 'รายปี'">
                <v-row style="height: 120px;">
                  <v-col cols="2" class="d-flex align-start mt-2">
                    <span style="font-size: 16px;">ปี: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-select
                      v-model="selectedYear"
                      :items="years"
                      placeholder="เลือกปี"
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      class="d-inline-block custom-text-field"
                      height="22px"
                      @change="checkSelected()"
                      dense
                      outlined
                      :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
                    >
                    <template slot="selection">
                      {{ selectedYear + 543 }}
                    </template>
                    <template slot="item" slot-scope="data">
                      {{ data.item + 543 }}
                    </template>
                      <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-select>
                  </v-col>
                </v-row>
              </div>

              <!-- รายเดือน -->
              <div v-if="selectedFilterDates === 'รายเดือน'">
                <v-row style="height: 60px;">
                  <v-col cols="2" class="d-flex align-start mt-2">
                    <span style="font-size: 16px;">ปี: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-select
                      v-model="selectedYear"
                      :items="years"
                      placeholder="เลือกปี"
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      class="d-inline-block custom-text-field"
                      height="22px"
                      @change="checkSelected()"
                      dense
                      outlined
                      :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
                    >
                    <template slot="selection">
                      {{ selectedYear + 543 }}
                    </template>
                    <template slot="item" slot-scope="data">
                      {{ data.item + 543 }}
                    </template>
                      <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-select>
                  </v-col>
                </v-row>
                <v-row style="height: 60px;">
                  <v-col cols="2" class="d-flex align-start mt-2">
                    <span style="font-size: 16px;">เดือน: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-select
                      v-model="selectedMonth"
                      :items="months"
                      placeholder="เลือกเดือน"
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      class="d-inline-block custom-text-field"
                      height="22px"
                      @change="checkSelected()"
                      dense
                      outlined
                      :menu-props="{ offsetY: true, offsetOverflowAuto: true }"
                    >
                      <v-spacer></v-spacer>
                      <v-icon slot="append" class="mdi-rotate-180" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-select>
                  </v-col>
                </v-row>
              </div>

              <!-- รายวัน -->
              <div v-if="selectedFilterDates === 'รายวัน'">
                <v-row style="height: 120px;">
                  <v-col cols="2" class="pr-0 mt-2">
                    <span style="font-size: 16px;">วันที่: </span>
                  </v-col>
                  <v-col cols="10">
                    <v-dialog ref="modalDateSelect" v-model="showDatePicker" class="d-inline-block" persistent :return-value.sync="date" width="480px">
                      <template v-slot:activator="{ on, attrs }">
                        <v-text-field
                          v-model="dateRangeText"
                          placeholder="วว/ดด/ปปปป"
                          dense
                          rounded
                          readonly
                          style="border: 1px solid #EBEBEB; border-radius: 8px;"
                          v-bind="attrs"
                          v-on="on"
                          class="d-inline-block custom-text-field my-input"
                        >
                          <v-spacer></v-spacer>
                          <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                        </v-text-field>
                      </template>
                      <v-date-picker
                        style="font-size:29px !important; height: 420px !important"
                        v-model="dates"
                        scrollable
                        reactive
                        locale="Th-th"
                        range
                        no-title
                        @change="checkSelected()"
                        full-width
                        :min="minDate"
                        :max="new Date(Date.now() - new Date().getTimezoneOffset() * 60000).toISOString().substr(0, 10)"
                      >
                        <v-row>
                          <v-col align="end">
                            <v-btn text color="primary" @click="closeDateSelect()">ยกเลิก</v-btn>
                            <v-btn text color="primary" @click="saveDates(dates)">ตกลง</v-btn>
                          </v-col>
                        </v-row>
                      </v-date-picker>
                    </v-dialog>
                  </v-col>
                </v-row>
              </div>

            </v-col>
          </v-row>
          <v-row>
            <v-col>
              <v-btn text @click="handleOptionChange()" elevation="0"
              :disabled="disabledSelected === true ? true : false"
              >
               <span :style="disabledSelected ? 'font-size: 16px; color: #CCCCCC' : 'font-size: 16px; color: #27AB9C'" >ล้างค่า</span>
                 <!-- <span :style="{ 'font-size': '16px', 'color': disabledSelected ? '#27AB9C' : '#333' }" >ล้างค่า</span> -->
              </v-btn>
            </v-col>
            <v-col>
              <v-btn @click="confirmSelect()" style="border-radius: 40px; background: #27AB9C; width: 125px"
              :disabled="disabledSelected === true ? true : false"
              >
                <span style="font-size: 16px; color: #FFFFFF">ยืนยัน</span>
              </v-btn>
            </v-col>
          </v-row>
        </v-card>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialog_detail"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
      >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">รายการสินค้า</font>
          </span>
          <v-btn icon dark @click="CloseDialog('readonly')">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pl-3 pt-3">
                <span style="color:#333333; font-size: 16px; font-weight: 600;">รายการสินค้าทั้งหมด {{ Number( order_Detail.length ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} ชิ้น</span>
              </v-col>
            </v-row>
        </v-card-text>
        <v-card-text v-bind:style="{'height' : '400px'}">
          <v-container>
            <v-row v-for="(item, index) in order_Detail" :key="index">
              <v-col cols="12">
                <v-card class="mt-4">
                  <v-row>
                    <v-col cols="4" class="pt-7" align="center">
                      <v-avatar tile size="100">
                        <div v-if="item.product_image !== ''">
                          <img :src="item.product_image" alt="Product Image" class="avatar-image" />
                        </div>
                        <div v-else>
                          <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                        </div>
                      </v-avatar>
                    </v-col>
                    <v-col cols="8">
                      <v-card-title class="no-word-break pb-1">{{ item.product_name }}</v-card-title>
                      <v-card-text>
                        <div><b>รหัสสินค้า:</b> {{ item.product_id }}</div>
                        <div><b>SKU:</b> {{ item.main_sku }}</div>
                        <div><b>ราคา:</b> {{ Number( item.revenue_default ).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</div>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import { Decode } from '@/services'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      chartSeries_tab1: [],
      chartSeries_tab2: [],
      selectedItem: null,
      selectedFilterDates: 'รายปี',
      disabledSelected: true,
      itemsHeader: [
        'ข้อมูลรายได้',
        'รายการสั่งซื้อสินค้าทั้งหมด',
        'TOP 10 สินค้าขายดี',
        'TOP 10 มูลค่าการสั่งซื้อ',
        'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด',
        'TOP 10 Sales ที่มียอดค่าสะสมเยอะที่สุด',
        'TOP 10 กลุ่มลูกค้า'
      ],
      itemsHeaderSale: [
        'ข้อมูลรายได้',
        'รายการสั่งซื้อสินค้าทั้งหมด',
        'TOP 10 สินค้าขายดี',
        'TOP 10 มูลค่าการสั่งซื้อ',
        'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด',
        'TOP 10 กลุ่มลูกค้า'
      ],
      itemsHeaderGroup: [
        'ข้อมูลรายได้',
        'รายการสั่งซื้อสินค้าทั้งหมด',
        'TOP 10 สินค้าขายดี',
        'TOP 10 มูลค่าการสั่งซื้อ',
        'TOP 10 ลูกค้าที่ซื้อมูลค่าสะสมเยอะที่สุด'
      ],
      itemsHeaderCus: [
        'ข้อมูลรายได้',
        'รายการสั่งซื้อสินค้าทั้งหมด',
        'TOP 10 สินค้าขายดี',
        'TOP 10 มูลค่าการสั่งซื้อ'
      ],
      datesFilter: [
        'รายปี',
        'รายเดือน',
        'รายวัน'
      ],
      selectDateFilter: false,
      showBestSeller: false,
      showOrderValue: false,
      showTopBuyers: false,
      monthSelected: '',
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31', // Set your maximum date here
      dialog_detail: false,
      order_Detail: [],
      saleOrder: [],
      keyField: 'i',
      bestSeller: [],
      orderValue: [],
      topBuyers: [],
      xAxis: '',
      // exportFile: [],
      UrlExponential: '',
      // exportDashboard: '',
      startDate: new Date().getFullYear(),
      endDate: new Date().getFullYear(),
      shopID: '',
      dateFilter: 'year',
      modalDateSelect: false,
      dates: [],
      picker: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      options: ['รายปี', 'รายเดือน', 'รายวัน'],
      selectedOption: 'รายปี',
      years: [2022, 2023, 2024, 2025],
      showYearDropdown: null,
      selectedDropdown: 'รายปี',
      // showMonthDropdown: false,
      selectedYear: new Date().getFullYear(),
      showDatePicker: false,
      months: [
        { text: 'มกราคม', value: '01' },
        { text: 'กุมภาพันธ์', value: '02' },
        { text: 'มีนาคม', value: '03' },
        { text: 'เมษายน', value: '04' },
        { text: 'พฤษภาคม', value: '05' },
        { text: 'มิถุนายน', value: '06' },
        { text: 'กรกฎาคม', value: '07' },
        { text: 'สิงหาคม', value: '08' },
        { text: 'กันยายน', value: '09' },
        { text: 'ตุลาคม', value: '10' },
        { text: 'พฤศจิกายน', value: '11' },
        { text: 'ธันวาคม', value: '12' }
      ],
      selectedMonth: null,
      selectedMonthValue: null,
      menu: false,
      selectedDates: [],
      availableYears: [],
      totalSale: '',
      totalOrder: '',
      moneyIconPath: require('@/assets/icons/SellerDashboard/money (1) 1.png'),
      boxIconPath: require('@/assets/icons/SellerDashboard/box 1.png'),
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      dataModelIconPath: require('@/assets/icons/SellerDashboard/data-model 1.png'),
      uniqueIconPath: require('@/assets/icons/SellerDashboard/unique 1.png'),
      commissionIconPath: require('@/assets/icons/SellerDashboard/commission 1.png'),
      bestCustomerIconPath: require('@/assets/icons/SellerDashboard/best-customer-experience 1.png'),
      rankingIconPath: require('@/assets/icons/SellerDashboard/ranking (1) 1.png'),
      graphLineIconPath: require('@/assets/icons/SellerDashboard/graph-line 1.png'),
      goldMedalIconPath: require('@/assets/icons/SellerDashboard/gold-medal.png'),
      silverMedalIconPath: require('@/assets/icons/SellerDashboard/silver-medal.png'),
      bronzeMedalIconPath: require('@/assets/icons/SellerDashboard/bronze-medal.png'),
      saleOrderDetail: require('@/assets/icons/SellerDashboard/SaleOrderDetail.png'),
      topCustomerGroup: require('@/assets/icons/SellerDashboard/TopCustomerGroup.png'),
      topTenSales: require('@/assets/icons/SellerDashboard/TopTenSales.png'),

      headers: [
        { text: 'รหัสผู้ซื้อ', align: 'start', sortable: false, value: 'id' },
        { text: 'รายชื่อลูกค้า', align: 'start', sortable: false, value: 'buyer_name' },
        { text: 'วันที่ทำรายการ', sortable: false, value: 'start_date_contract' },
        { text: 'เลขที่ทำรายการสั่งซื้อ', sortable: false, value: 'order_number' },
        { text: 'ราคา (บาท)', sortable: false, value: 'total_price_no_vat' },
        { text: 'รายการสินค้า', sortable: false, align: 'center', value: 'product_list' }
      ],
      headersModal: [
        { text: 'สินค้า', align: 'start', sortable: false, value: 'product_image' },
        { text: 'รหัสสินค้า', align: 'start', sortable: false, value: 'product_id' },
        { text: 'รายการสินค้า', align: 'start', sortable: false, value: 'product_name' },
        { text: 'SKU', align: 'start', sortable: false, value: 'main_sku' },
        { text: 'ราคา (บาท)', sortable: false, value: 'revenue_default' }
      ],
      // mock
      tabsType: 'income',
      active_tab: 0,
      summaryOrder: 0,
      saleList: 'ทั้งหมด', // รายชื่อเซลล์
      itemsSaleList: [],
      customerGroup: 'ทั้งหมด', // กลุ่มลูกค้า
      itemsCustomerGroup: [],
      customerList: 'ทั้งหมด', // รายชื่อลูกค้า
      itemsCustomerList: [],
      customerName: '', // ใช้เก็บค่าที่จะแสดง เมื่อเลือก กลุ่มลูกค้า, รายชื่อลูกค้า
      filterSaleReadOnly: true,
      filterGroupDisabled: true,
      filterCusDisabled: true,
      manageSaleOrder: '', // เช็คสิทธิืเซลล์
      saleNameTH: '', // ชื่อเซลล์ ภาษาไทย
      chartOptions: {
        legend: {
          show: false // Set to false to remove the legend
        },
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      },
      chartOptions2: {
        legend: {
          show: false // Set to false to remove the legend
        },
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: ['Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct']
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              // Format the number with commas for thousands
              return new Intl.NumberFormat('en-US').format(value)
            }
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
            }
          }
        }
      }
    }
  },
  mounted () {
    this.selectedItem = this.itemsHeader[0]
    this.selectedFilterDates = this.datesFilter[0]
    const link = document.createElement('link')
    link.rel = 'stylesheet'
    link.href = 'https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css'
    document.head.appendChild(link)
  },
  beforeDestroy () {
    // Remove Bootstrap CSS when component is destroyed
    const links = document.querySelectorAll('link[href="https://maxcdn.bootstrapcdn.com/bootstrap/4.5.2/css/bootstrap.min.css"]')
    links.forEach(link => link.parentNode.removeChild(link))
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DashboardSaleOrderMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/DashboardSaleOrder' }).catch(() => { })
      }
    }
  },
  created () {
    const listShopDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
    console.log(listShopDetail.can_use_function_in_shop.manage_sale_order, 'list_shop_detail')
    this.manageSaleOrder = listShopDetail.can_use_function_in_shop.manage_sale_order
    const userDetail = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
    // console.log(userDetail.data[0].first_name_th + ' ' + userDetail.data[0].last_name_th, 'userDetail')
    this.saleNameTH = userDetail.data[0].first_name_th + ' ' + userDetail.data[0].last_name_th

    if (this.manageSaleOrder === '1') {
      this.filterSaleReadOnly = false
    } else {
      this.filterSaleReadOnly = true
      this.filterGroupDisabled = false
      this.saleList = this.saleNameTH
    }

    // this.chartSeries[0].data = [0, 0, 0, 0, 0, 0, 178500, 212210, 625136297.93, 626470, 224280, 8800]
    this.$EventBus.$emit('changeNav')
    // Set default values or perform initial actions on component creation
    if (this.selectedDropdown === 'รายปี') {
      this.showYearDropdown = true
    }
    if (localStorage.getItem('shopSellerID') === '' || localStorage.getItem('shopSellerID') === null || localStorage.getItem('shopSellerID') === undefined) {
      this.$router.push({ path: '/' })
    } else {
      this.shopID = localStorage.getItem('shopSellerID')
      // console.log(this.shopID, 'sshhooppiidd')
    }
    this.getPageData()
    this.getSaleorderOrderListAndTopProduct(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
    this.getSaleorderTopCustomer(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
    this.getSaleorderTopSale(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
    this.getSaleorderTopCustomerGroup(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
    this.getSaleorderSaleCustomerGroup(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
  },
  computed: {
    dateRangeText () {
      if (this.dates.length > 1) {
        var startDays = new Date(this.dates[0]).toLocaleDateString('th-TH')
        var endDays = new Date(this.dates[1]).toLocaleDateString('th-TH')
        var totalDays = startDays + ' - ' + endDays
        return totalDays
      } else if (this.dates.length === 1) {
        var oneDays = new Date(this.dates).toLocaleDateString('th-TH')
        return oneDays
      }
      return this.dates.join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    selectedItemsHeader () {
      // Use the condition to determine which itemsHeader array to use
      if (this.saleList === 'ทั้งหมด') {
        return this.itemsHeader
      } else if (this.saleList !== 'ทั้งหมด' && this.customerGroup === 'ทั้งหมด' && this.customerList === 'ทั้งหมด') {
        return this.itemsHeaderSale
      } else if (this.saleList !== 'ทั้งหมด' && this.customerGroup !== 'ทั้งหมด' && this.customerList === 'ทั้งหมด') {
        return this.itemsHeaderGroup
      } else if (this.saleList !== 'ทั้งหมด' && this.customerGroup !== 'ทั้งหมด' && this.customerList !== 'ทั้งหมด') {
        return this.itemsHeaderCus
      } else {
        return this.itemsHeader
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    formatPrice (price) {
      // Format the price based on its magnitude
      if (price >= 1000000) {
        return (price / 1000000).toLocaleString(undefined, { minimumFractionDigits: 2 }) + 'M'
      } else if (price >= 1000) {
        return (price / 1000).toLocaleString(undefined, { minimumFractionDigits: 2 }) + 'K'
      } else {
        return price.toLocaleString(undefined, { minimumFractionDigits: 2 })
      }
    },
    selectedFilterDate (filter) {
      this.selectedFilterDates = filter
      if (filter === 'รายปี') {
        this.dateFilter = 'year'
        this.checkSelected()
        this.handleOptionChange()
      } else if (filter === 'รายเดือน') {
        this.dateFilter = 'month'
        this.checkSelected()
        this.handleOptionChange()
      } else if (filter === 'รายวัน') {
        this.dateFilter = 'day'
        this.checkSelected()
        this.handleOptionChange()
      }
      // console.log(this.dateFilter, 'this.dateFilter')
    },
    checkSelected () {
      if (this.selectedFilterDates === 'รายปี') {
        if (this.selectedYear === null) {
          this.disabledSelected = true
        } else {
          this.disabledSelected = false
        }
      } else if (this.selectedFilterDates === 'รายเดือน') {
        if (this.selectedYear && this.selectedMonth === null) {
          this.disabledSelected = true
        } else {
          this.disabledSelected = false
        }
      } else if (this.selectedFilterDates === 'รายวัน') {
        if (this.dates.length === 0) {
          this.disabledSelected = true
        } else {
          this.disabledSelected = false
        }
      }
    },
    confirmSelect () {
      // this.$store.commit('openLoader')
      if (this.selectedFilterDates === 'รายปี') {
        this.onYearSelected()
      } else if (this.selectedFilterDates === 'รายเดือน') {
        this.onMonthSelected()
      } else if (this.selectedFilterDates === 'รายวัน') {
        this.onDatesSelected()
      }
      this.handleOptionChange()
      // this.$store.commit('closeLoader')
    },
    async saveDates (val) {
      if (this.dates.length === 1) {
        this.startDate = this.dates
        this.endDate = this.dates
        this.showDatePicker = false
      } else {
        this.$refs.modalDateSelect.save(val)
        var Range = await val.sort((a, b) => {
          var startDay = new Date(a)
          var endDay = new Date(b)
          return startDay - endDay
        })
        this.dateRange = Range
        this.startDate = this.dates[0]
        this.endDate = this.dates[1]
        this.showDatePicker = false
      }
      this.checkSelected()
    },
    async onDatesSelected () {
      if (this.dates.length === 1) {
        this.startDate = this.dates
        this.endDate = this.dates
        this.getSaleorderRevenueGraph(this.dates, this.dates, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderOrderListAndTopProduct(this.dates, this.dates, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderTopCustomer(this.dates, this.dates, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderTopSale(this.dates, this.dates, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderTopCustomerGroup(this.dates, this.dates, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderSaleCustomerGroup(this.dates, this.dates, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.selectDateFilter = false
      } else {
        this.dateRange = Range
        this.startDate = this.dates[0]
        this.endDate = this.dates[1]
        this.getSaleorderRevenueGraph(this.dates[0], this.dates[1], this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderOrderListAndTopProduct(this.dates[0], this.dates[1], this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderTopCustomer(this.dates[0], this.dates[1], this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderTopSale(this.dates[0], this.dates[1], this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderTopCustomerGroup(this.dates[0], this.dates[1], this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.getSaleorderSaleCustomerGroup(this.dates[0], this.dates[1], this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
        this.selectDateFilter = false
      }
    },
    getPageData () {
      // โหลดครั้งแรกตอนเปิดหน้านี้
      this.getSaleorderRevenueGraph(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
    },
    openSelectFilter () {
      this.selectDateFilter = true
    },
    closeSelectFilter () {
      this.selectDateFilter = false
      this.handleOptionChange()
    },
    openDialog (item) {
      this.dialog_detail = true
      this.order_Detail = item.product_list
    },
    CloseDialog (val) {
      this.type = val
      this.dialog_detail = false
    },
    ChangeChartOptions (val) {
      this.chartOptions = {
        legend: {
          show: false // Set to false to remove the legend
        },
        chart: {
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          }
        },
        xaxis: {
          categories: this.selectedFilterDates === 'รายเดือน' ? val : this.selectedFilterDates === 'รายวัน' ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#2ADAC5', '#6597D4'],
        tooltip: {
          custom: function ({ series, seriesIndex, dataPointIndex, w }) {
            return seriesIndex === 0
              ? 'จำนวนรายการสั่งซื้อทั้งหมด' + ': ' + series[seriesIndex][dataPointIndex] + ' รายการ'
              : 'ยอดขายทั้งหมด' + ': ' + series[seriesIndex][dataPointIndex].toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') + ' บาท'
          }
        }
      }
      this.chartOptions2 = {
        chart: {
          legend: {
            show: false // Set to false to remove the legend
          },
          id: 'income-difference-chart',
          stacked: false,
          toolbar: {
            show: false // Set toolbar to false to hide it
          }
        },
        xaxis: {
          categories: this.selectedFilterDates === 'รายเดือน' ? val : this.selectedFilterDates === 'รายวัน' ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
        },
        plotOptions: {
          bar: {
            horizontal: false
          }
        },
        dataLabels: {
          enabled: false
        },
        colors: ['#2ADAC5', '#FE9C8F'],
        tooltip: {
          custom: function ({ series, seriesIndex, dataPointIndex, w }) {
            return seriesIndex === 0
              ? 'จำนวนรายการสั่งซื้อทั้งหมด' + ': ' + series[seriesIndex][dataPointIndex] + ' รายการ'
              : 'จำนวนใบเสนอราคา' + ': ' + series[seriesIndex][dataPointIndex].toLocaleString() + ' ใบ'
          }
        }
      }
    },
    async getSaleorderRevenueGraph (startDate, endDate, shopID, dateFilter, tabsType, saleList, customerGroup, customerList) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        type: tabsType,
        role_user: 'ext_buyer',
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      // console.log(data, 'SRG')
      await this.$store.dispatch('actionSaleorderRevenueGraph', data)
      var response = await this.$store.state.ModuleDashBoardSaleOrder.stateSaleorderRevenueGraph
      // console.log('actionSaleorderRevenueGraph', response)
      if (response.ok === 'y') {
        var dataTableRevenue = []
        for (let i = 0; i < response.query_result.graph.length; i++) {
          dataTableRevenue.push(response.query_result.graph[i].revenue)
          // console.log(dataTableRevenue, 'dataTableRevenuedataTableRevenue')
        }
        var dataTableDoc = []
        for (let i = 0; i < response.query_result.graph.length; i++) {
          dataTableDoc.push(response.query_result.graph[i].doc_number)
          // console.log(dataTableDoc, 'dataTableRevenuedataTableDoc')
        }
        var dataTableDocOrder = []
        for (let i = 0; i < response.query_result.graph.length; i++) {
          dataTableDocOrder.push(response.query_result.graph[i].doc_order)
          // console.log(dataTableDocOrder, 'dataTableRevenuedataTableDocOrder')
        }
        var dataTableDocQt = []
        for (let i = 0; i < response.query_result.graph.length; i++) {
          dataTableDocQt.push(response.query_result.graph[i].doc_qt)
          // console.log(dataTableDocQt, 'dataTableRevenuedataTableDocQT')
        }
        var dayInMonth = []
        for (let i = 0; i < response.query_result.graph.length; i++) {
          dayInMonth.push(new Date(response.query_result.graph[i].date).toLocaleDateString('th-TH', { month: 'long', day: 'numeric' }))
        }
        var dateToDate = []
        for (let i = 0; i < response.query_result.graph.length; i++) {
          dateToDate.push(new Date(response.query_result.graph[i].date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        }
        // console.log(dayInMonth, 'dayInMonth')
        if (response.ok === 'y') {
          this.chartSeries_tab1 = await [{ name: 'จำนวนรายการสั่งซื้อทั้งหมด', data: dataTableDoc }, { name: 'รายได้', data: dataTableRevenue }]
          this.chartSeries_tab2 = await [{ name: 'จำนวนรายการสั่งซื้อทั้งหมด', data: dataTableDocOrder }, { name: 'จำนวนใบเสนอราคา', data: dataTableDocQt }]
          // console.log(dataTableRevenue, 'dataTableRD1')
          // console.log(dataTableDoc, 'dataTableRD2')
          if (this.selectedFilterDates === 'รายเดือน') {
            this.ChangeChartOptions(dayInMonth)
          } else if (this.selectedFilterDates === 'รายวัน') {
            this.ChangeChartOptions(dateToDate)
          } else {
            this.ChangeChartOptions(false)
          }
          if (response.query_result.sumary.doc_number === '' || response.query_result.sumary.doc_number === null || response.query_result.sumary.doc_number === undefined) {
            // ในกรณีที่ ค่าจำนวนรายการสั่งซื้อทั้งหมด = ค่าว่าง, null, undefined
            this.totalOrder = 0 // จำนวนรายการสั่งซื้อทั้งหมด
          } else {
            this.totalOrder = response.query_result.sumary.doc_number // จำนวนรายการสั่งซื้อทั้งหมด
          }
          if (response.query_result.sumary.revenue === '' || response.query_result.sumary.revenue === null || response.query_result.sumary.revenue === undefined) {
            // ในกรณีที่ ค่ายอดขายทั้งหมด = ค่าว่าง, null, undefined
            this.totalSale = 0 // ยอดขายทั้งหมด
          } else {
            this.totalSale = response.query_result.sumary.revenue // ยอดขายทั้งหมด
          }
          if (response.query_result.sumary.sumaryOrder === '' || response.query_result.sumary.sumaryOrder === null || response.query_result.sumary.sumaryOrder === undefined) {
            // ในกรณีที่ ค่ายอดขายทั้งหมด = ค่าว่าง, null, undefined
            this.summaryOrder = 0 // ยอดขายทั้งหมด
          } else {
            this.summaryOrder = response.query_result.sumary.sumaryOrder // ยอดขายทั้งหมด
          }
        } else {
          // if response.message === 'n'
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: response.message
          })
        }
      }
    },
    async getSaleorderOrderListAndTopProduct (startDate, endDate, shopID, dateFilter, tabsType, saleList, customerGroup, customerList) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        type: tabsType,
        role_user: 'ext_buyer',
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      await this.$store.dispatch('actionSaleorderOrderListAndTopProduct', data)
      var response = await this.$store.state.ModuleDashBoardSaleOrder.stateSaleorderOrderListAndTopProduct
      if (response.ok === 'y') {
        // console.log('actionSaleorderOrderListAndTopProduct', response.query_result.orderList)
        this.saleOrder = response.query_result.orderList
        var count = 1
        this.saleOrder.forEach((item) => {
          item.i = count
          count = count + 1
        })
        // this.saleOrder = datasale
        // console.log(this.saleOrder, 'tong')
        this.bestSeller = response.query_result.countProduct
        this.orderValue = response.query_result.sumRevenuProduct
      }
    },
    async getSaleorderTopSale (startDate, endDate, shopID, dateFilter, tabsType, saleList, customerGroup, customerList) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        type: tabsType,
        role_user: 'ext_buyer',
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      await this.$store.dispatch('actionSaleorderTopSale', data)
      var response = await this.$store.state.ModuleDashBoardSaleOrder.stateSaleorderTopSale
      if (response.ok === 'y') {
        // console.log('actionSaleorderTopSale', response)
        this.topSales = response.query_result
      }
    },
    async getSaleorderTopCustomer (startDate, endDate, shopID, dateFilter, tabsType, saleList, customerGroup, customerList) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        type: tabsType,
        role_user: 'ext_buyer',
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      await this.$store.dispatch('actionSaleorderTopCustomer', data)
      var response = await this.$store.state.ModuleDashBoardSaleOrder.stateSaleorderTopCustomer
      if (response.ok === 'y') {
        // console.log('actionTopBuyers', response)
        this.topBuyers = response.query_result
      }
    },
    async getSaleorderTopCustomerGroup (startDate, endDate, shopID, dateFilter, tabsType, saleList, customerGroup, customerList) {
      var data = {
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        type: tabsType,
        role_user: 'ext_buyer',
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      await this.$store.dispatch('actionSaleorderTopCustomerGroup', data)
      var response = await this.$store.state.ModuleDashBoardSaleOrder.stateSaleorderTopCustomerGroup
      if (response.ok === 'y') {
        // console.log('actionSaleorderTopCustomerGroup', response)
        this.topBuyersGroup = response.query_result
      }
    },
    async getSaleorderSaleCustomerGroup (startDate, endDate, shopID, dateFilter, tabsType, saleList, customerGroup, customerList) {
      var data = {
        // start: startDate,
        // end: endDate,
        // filter: dateFilter,
        // type: tabsType,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'ext_buyer',
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      await this.$store.dispatch('actionSaleorderSaleCustomerGroup', data)
      var response = await this.$store.state.ModuleDashBoardSaleOrder.stateSaleorderSaleCustomerGroup
      // console.log('actionSaleorderSaleCustomerGroup', response)
      if (response.ok === 'y') {
        if (this.itemsCustomerGroup.length > 1) {
          this.itemsCustomerGroup = ['ทั้งหมด', ...response.query_result.GroupNames]
        } else {
          this.itemsCustomerGroup = response.query_result.GroupNames
        }

        if (this.itemsCustomerList.length > 1) {
          this.itemsCustomerList = ['ทั้งหมด', ...response.query_result.CusNames]
        } else {
          this.itemsCustomerList = response.query_result.CusNames
        }
        // this.itemsCustomerGroup = ['ทั้งหมด', ...response.query_result.GroupNames]
        // this.itemsCustomerList = ['ทั้งหมด', ...response.query_result.CusNames]
        if (this.manageSaleOrder === '1') {
          this.itemsSaleList = ['ทั้งหมด', ...response.query_result.SaleNames]
        } else {
          this.itemsSaleList = ['ทั้งหมด', ...response.query_result.SaleNames]
          this.saleList = this.saleNameTH
        }
      }
    },
    async getExportDashboard (startDate, endDate, shopID, dateFilter, exportDashboard, fileName, tabsType, saleList, customerGroup, customerList) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        export_excel: exportDashboard,
        start: startDate,
        end: endDate,
        filter: dateFilter,
        seller_shop_id: shopID !== '' && shopID !== null ? shopID : '',
        role_user: 'ext_buyer',
        type: tabsType,
        sale_name: saleList,
        group_cus: customerGroup,
        cus_name: customerList
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}saleorderDashboard/exportSaleorderDashboard`,
        data: data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', fileName + '.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },

    closeDateSelect ($refs) {
      this.showDatePicker = false
      this.modalDateSelect = false
      this.dates = []
      this.handleOptionChange()
    },
    handleOptionChange () {
      // Reset selections when the option changes
      this.selectedYear = null
      this.selectedMonth = null
      this.selectedMonthValue = null
      // this.selectedDates = []
      this.dates = []
      this.disabledSelected = true
    },
    onMonthSelected () {
      this.selectedMonthValue = `${this.selectedYear}-${this.selectedMonth}`
      this.startDate = this.selectedMonthValue
      this.endDate = this.selectedMonthValue
      // console.log(this.selectedMonthValue, 'selecteddd')
      this.getSaleorderRevenueGraph(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderOrderListAndTopProduct(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopCustomer(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopSale(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopCustomerGroup(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderSaleCustomerGroup(this.selectedMonthValue, this.selectedMonthValue, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.selectDateFilter = false
    },
    onYearSelected () {
      this.startDate = this.selectedYear
      this.endDate = this.selectedYear
      // console.log(this.selectedYear, 'selecteddd')
      this.getSaleorderRevenueGraph(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderOrderListAndTopProduct(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopCustomer(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopSale(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopCustomerGroup(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderSaleCustomerGroup(this.selectedYear, this.selectedYear, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.selectDateFilter = false
    },
    changeCustomerName () {
      // ใช้เช็คค่า เมื่อเลือก กลุ่มลูกค้า, รายชื่อลูกค้า
      if (this.customerGroup === 'ทั้งหมด') {
        if (this.customerList === 'ทั้งหมด') {
          this.customerName = ''
        } else {
          this.customerName = this.customerList
        }
      } else if (this.customerGroup !== 'ทั้งหมด') {
        if (this.customerList !== 'ทั้งหมด') {
          this.customerName = this.customerList
        } else {
          this.customerName = this.customerGroup
        }
      }

      if (this.saleList !== 'ทั้งหมด' && this.saleList) {
        this.filterGroupDisabled = false
        if (this.customerGroup !== 'ทั้งหมด' && this.customerGroup) {
          this.filterCusDisabled = false
        } else {
          this.filterCusDisabled = true
        }
      } else {
        this.filterGroupDisabled = true
        this.filterCusDisabled = true
      }
      if (this.filterGroupDisabled === true) {
        this.customerGroup = 'ทั้งหมด'
        this.customerList = 'ทั้งหมด'
      }
      if (this.filterCusDisabled === true) {
        this.customerList = 'ทั้งหมด'
      }

      this.getPageData()
      this.getSaleorderOrderListAndTopProduct(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopCustomer(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopSale(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderTopCustomerGroup(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      this.getSaleorderSaleCustomerGroup(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
    },
    changeTabsType () {
      // ใช้เปลี่ยน TabsType
      if (this.active_tab === 0) {
        this.tabsType = 'income'
        this.getSaleorderRevenueGraph(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      } else {
        this.tabsType = 'total_sale'
        this.getSaleorderRevenueGraph(this.startDate, this.endDate, this.shopID, this.dateFilter, this.tabsType, this.saleList, this.customerGroup, this.customerList)
      }
    },
    resetFilter () {
      if (this.manageSaleOrder === '1') {
        this.saleList = 'ทั้งหมด'
      } else {
        this.saleList = this.saleNameTH
      }
      this.customerGroup = 'ทั้งหมด'
      this.customerList = 'ทั้งหมด'
      this.changeCustomerName()
    }
  }
}
</script>

<style scoped>
.custom-btn {
  border-color: #27AB9C; /* Change to the color you desire */
}
.custom-btn .v-icon {
  font-size: 20px; /* Change to the desired size */
}
.v-data-table .v-application--is-ltr .v-data-footer__pagination {
  margin: 0 10px 0 10px !important;
}
/* .v-data-table .v-application--is-rtl .v-data-footer__pagination {
  margin: 0 10px 0 10px !important;
} */
::v-deep .v-dialog {
  box-shadow: none !important;
}
.chart-container {
  width: 100%;
  overflow-x: auto; /* Enable horizontal scrolling */
  overflow-y: hidden;
}
.v-card__actions {
    align-items: center;
    display: revert;
    padding: 8px;
}
.v-card--link {
    cursor: pointer;
    padding-top: 8px;
}
.apple-keyboard-control {
  size: 20px;
}
.two-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  white-space: normal;
}
.one-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.custom-text-field {
  height: 37px;
  width: 280px;
}
.custom-chip {
  overflow: visible !important;
}
.no-word-break {
  font-size: 16px;
  word-break: normal;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.backgroundSellerDashboard {
  max-width: 100% !important;
  background: #F7FCFC;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C;
}
.exportButtonText {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF
}
.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
.v-btn__content {
    font-size: 16px;
}
.v-text-field--rounded > .v-input__control > .v-input__slot {
    padding: 0 10px;
}
.my-input.v-input .v-input__slot {
  padding: 0 10px;
}
.nav-link:focus {
  outline: none !important;
}
.nav-tabs .nav-link {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.grey-tab {
  color: #989898; /* Set the text color to grey */
  background-color: #FAFAFA; /* Set the background color to a light grey */
  border-color: #dee2e6; /* Set the border color to grey */
  border-bottom-color: #FAFAFA;
  font-size: 16px;
  font-weight: 500;
}
</style>
