<template>
  <v-card width="100%" height="100%" elevation="0">
    <v-card-text class="px-1">
      <v-row dense>
        <v-col :cols="MobileSize ? 1 : 4" v-if="!IpadSize">
          <!-- <v-row dense>
            <v-col cols="12" md="3" class="pt-3">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;">เลือกร้านค้า :</span>
            </v-col>
            <v-col cols="12" md="8">
              <v-autocomplete v-model="shopID" :items="shopList" dense outlined item-text="shop_name" item-value="shop_id" placeholder="เลือกร้านค้า" @change="selectShop($event)"></v-autocomplete>
            </v-col>
          </v-row> -->
        </v-col>
        <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 8">
          <v-row dense :justify="dataSelect !== 1 ? 'center' : 'end'">
            <v-col cols="12" md="2" class="pt-3" v-if="dataSelect !== 1">
              <span style="font-weight: 500; font-size: 12px; line-height: 16px;" >วันที่เริ่ม - สิ้นสุด</span>
            </v-col>
            <v-col cols="12" :md="dataSelect !== 1 ? 7 : 4">
              <v-row dense v-if="dataSelect !== 1">
                <!-- เลือกวันเริ่มต้น -->
                <v-col cols="5" class="px-0">
                  <v-dialog
                    ref="dialog"
                    v-model="modal"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="dateStart"
                        v-bind="attrs"
                        v-on="on"
                        readonly
                        outlined
                        :disabled="dataSelect === 5 ? true : false"
                        dense
                        placeholder="DD/MM/YYYY"
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      ref="pickerDateStart"
                      v-model="dateFormatted"
                      :active-picker.sync="activePicker"
                      scrollable
                      reactive
                      no-title
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        color="primary"
                        @click="getSelectDate(dateFormatted)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
                <v-col cols="2" md="1" sm="2" :class="MobileSize ? 'pl-6 pt-3' : IpadSize ? 'pl-7 pt-3' : 'pt-3 pl-3'">-</v-col>
                <!-- เลือกวันสิ้นสุด -->
                <v-col cols="5" class="px-0">
                  <v-dialog
                    ref="dialog1"
                    v-model="modal1"
                    :return-value.sync="date"
                    persistent
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field
                        v-model="dateEnd"
                        v-bind="attrs"
                        v-on="on"
                        :readonly="dataSelect === 2 ? true : false"
                        dense
                        :disabled="dataSelect === 5 ? true : dataSelect === 2 ? true : false"
                        placeholder="DD/MM/YYYY"
                        outlined
                      ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                    </template>
                    <v-date-picker
                      v-model="dateFormatted2"
                      ref="pickerDateEnd"
                      :active-picker.sync="activePicker"
                      scrollable
                      no-title
                      :readonly="dataSelect === 2 ? true : false"
                      :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      :min="dateFormatted"
                    >
                      <v-spacer></v-spacer>
                      <v-btn
                        text
                        color="primary"
                        @click="modal1 = false"
                      >
                        ยกเลิก
                      </v-btn>
                      <v-btn
                        text
                        :disabled="dataSelect === 2 ? true : false"
                        color="primary"
                        @click="getSelectDateEnd(dateFormatted2)"
                      >
                        ตกลง
                      </v-btn>
                    </v-date-picker>
                  </v-dialog>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="3" class="px-0">
              <v-select
               v-model="selectFilter"
               :items="selectFilteritem"
               @change="getFilter($event)"
               item-value="id"
               item-text="name"
               dense
               outlined
               hide-details
               placeholder="ไม่ระบุ"
               style="z-index: 11 !important;"
              >
              </v-select>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <!-- กราฟ -->
      <v-row dense style="z-index: -1;">
        <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border: 1px solid #EBEBEB; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.25); border-radius: 6px;">
          <v-card-text>
            <div id='chart' class="mt-5">
              <apexchart type='line' height='400' :options='chartOptions' :series='series'></apexchart>
            </div>
          </v-card-text>
        </v-card>
      </v-row>
      <!-- ตารางข้อมูล -->
      <v-row dense>
        <v-col cols="12">
          <table class="display nowrap" id="example" cellpadding="0" cellspacing="0" style="width:100%">
            <thead>
              <tr>
                <th class="fontData">
                  รหัสผู้ซื้อ
                </th>
                <th class="fontData">
                  ชื่อผู้ซื้อ
                </th>
                <th class="fontData">
                  รหัสบริษัท
                </th>
                <th class="fontData">
                  ชื่อบริษัท
                </th>
                <th class="fontData">
                  วันที่ทำรายการ
                </th>
                <th class="fontData">
                  เลขที่ทำรายการชำระเงิน
                </th>
                <th class="fontData">
                  รหัสสินค้า
                </th>
                <th class="fontData">
                  เลข SKU
                </th>
                <th class="fontData">
                  มูลค่าสินค้า
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in dataRes" :key="index">
                <td >{{item.user_id}}</td>
                <td class="dot" >{{item.buyer_name}}</td>
                <td >{{item.company_id}}</td>
                <td >{{item.company_name}}</td>
                <td>{{item.updated_at}}</td>
                <!-- <td >{{ new Date(item.updated_at).toLocaleString('th-TH', { timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric' }) }}</td> -->
                <td >{{ item.order_number }}</td>
                <td >{{ item.product_id }}</td>
                <td class="dot">{{item.sku}}</td>
             <!--    <td >{{item.quantity}}</td> -->
                <td >{{item.net_price }}</td>
                <!-- <td >{{ item.transaction_status }}</td> -->
              </tr>
            </tbody>
          </table>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import VueApexCharts from 'vue-apexcharts'
import 'datatables.net'
import 'datatables.net-dt/css/jquery.dataTables.min.css'
import 'jszip'
import 'datatables.net-buttons-dt'
import 'datatables.net-buttons-dt/css/buttons.dataTables.min.css'
import 'datatables.net-buttons/js/buttons.colVis'
import 'datatables.net-buttons/js/buttons.flash'
import 'datatables.net-buttons/js/buttons.html5'
import 'datatables.net-buttons/js/buttons.print'
import 'datatables.net-buttons/js/dataTables.buttons'
import 'datatables.net-responsive-dt'
import $ from 'jquery'
window.JSZip = require('jszip')
export default {
  name: 'ApexChart',
  components: {
    apexchart: VueApexCharts
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      // console.log(value)
      return value
    }
  },
  data () {
    return {
      headerProps: {
        sortByText: 'เรียงตาม'
      },
      month: '',
      shopnameG: '',
      shopID: '',
      activePicker: null,
      modalMonth: false,
      modalYear: false,
      selectFilter: [{ id: 1, name: 'ไม่ระบุ' }],
      dataSelect: 0,
      dataRes: [],
      shopList: [],
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      modal: false,
      modal1: false,
      series: [],
      dateGraph: [],
      dateStart: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateEnd: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      startDate: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      endDate: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      year: '',
      dateSelectFunctionOne: '',
      dateSelectFunctionTwo: '',
      FirstDateSelect: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      EndDateSelect: this.dateFormat((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10), 'yyyy-MM-dd'),
      typeFilterForShop: '',
      MonthFormatted: null,
      YearFormatted: null,
      dateFormatted: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted2: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      selectFilteritem: [
        { id: 1, name: 'ไม่ระบุ' },
        { id: 2, name: 'เดือน' },
        { id: 3, name: '6เดือน' },
        { id: 4, name: 'ปี' },
        { id: 5, name: 'ทั้งหมด' }
      ]
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.setIconData()
  },
  updated () {
    $('#help').remove()
    this.setIconData()
    if (this.InnerWidth < 965) {
      document.getElementsByClassName('td.dt-control.sorting_1 .dtr-control')[0].style.visibility = 'hidden'
      $('td.dt-control.sorting_1').remove()
      // $('table.dataTable td.dt-control:before').hide()
    }
  },
  watch: {
    modalYear (val) {
      // console.log(val)
      val && this.$nextTick(() => (this.activePicker = 'YEAR'))
    },
    modalMonth (val) {
      val && this.$nextTick(() => (this.activePicker = 'MONTH'))
    },
    dialog (val) {
      val && this.$nextTick(() => (this.activePicker = 'DATE'))
    }
  },
  computed: {
    InnerWidth () {
      return window.innerWidth
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    chartOptions () {
      return {
        chart: {
          height: 400,
          type: 'line',
          zoom: {
            enabled: true
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          width: [2, 2, 2, 2],
          curve: 'straight',
          dashArray: [0, 0, 0, 0]
        },
        title: {
          text: `ตาราง และกราฟข้อมูลรายได้ของ ${this.MobileSize ? this.$options.filters.truncate(this.shopnameG.name, 13, '...') : this.shopnameG.name}`,
          align: 'left',
          margin: this.MobileSize ? 60 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        legend: {
          // tooltipHoverFormatter: function (val, opts) {
          //   return val + ' - ' + opts.w.globals.series[opts.seriesIndex][opts.dataPointIndex] + ''
          // },
          position: 'top',
          horizontalAlign: 'left',
          offsetY: -5
        },
        markers: {
          size: 4,
          hover: {
            sizeOffset: 6
          }
        },
        yaxis: [
          {
            labels: {
              formatter: function (val) {
                return parseFloat(val).toFixed(2)
              }
            }
          }
        ],
        xaxis: {
          categories: this.dateGraph
          // labels: {
          //   formatter: function (val) {
          //     return val.toFixed(0)
          //   }
          // }
        },
        tooltip: {
          y: [
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            },
            {
              title: {
                formatter: function (val) {
                  return val + ' : '
                }
              }
            }
          ]
        },
        grid: {
          borderColor: '#f1f1f1'
        },
        colors: ['#27AB9C', '#1AB759', '#D1392B', '#FAAD14', '#f50']
      }
    }
  },
  created () {
    var typefilter = 'day'
    this.getDataTransaction(this.startDate, this.endDate, typefilter, this.shopID)
    this.shopnameG = JSON.parse(localStorage.getItem('shopDetail'))
  },
  methods: {
    setIconData () {
      var img = require('@/assets/icon_image/infoStore.png')
      // console.log('example_filter')
      // $('#example_filter').append('<i id="help" class="fa fa-exclamation-circle fa-lg stroke-transparent ml-2" style="color: #27AB9C;"></i>')
      $('#example_filter').append('<svg id="help" xmlns="http://www.w3.org/2000/svg" width="25" height="40" fill="currentColor" style="color: #27AB9C;" class="bi bi-exclamation-circle ml-2 pt-5" viewBox="0 0 16 16"><path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/><path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/></svg>')
      $(`<div class="hide" style="position: absolute; z-index: 1; margin-left: 20vw" ><img src="${img}" width="182px" height="246px"/></div>`).appendTo('#example_filter')
      $('.hide').hide()
      // $('.hide').attr('src', require('./info.jpg'))
      // $('#help').hover(function () { $('.hide').show() }, function () { $('.hide').hide() })
      $(document).on('mouseenter', '#help', function (event) {
        $('.hide').show()
      }).on('mouseleave', '#help', function () {
        $('.hide').hide()
      })
      // $(#help).on({
      //   mouseenter: function () {
      //     $('.hide').show()
      //   },
      //   mouseleave: function () {
      //     $('.hide').hide()
      //   }
      // })
      // $('#help').hover(function () { console.log('hover!!!!') })
    },
    async selectShop (val) {
      // console.log(val)
      await this.getDataTransactionReload(this.FirstDateSelect, this.EndDateSelect, this.typeFilterForShop, val)
    },
    async getDataTransaction (startDate, endDate, type, shopID) {
      this.dataRes = []
      this.series = []
      this.dateGraph = []
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      await this.$store.dispatch('actionNewGetDashboard', data)
      var response = await this.$store.state.ModuleShop.stateNewGetDashboard
      // console.log('actionNewGetDashboard', response)
      if (response) {
        // console.log('transection', response.data_transaction)
        this.dataRes = await response.data_transaction.map(x => {
          return {
            user_id: x.user_id,
            buyer_name: x.buyer_name,
            company_id: x.company_id !== -1 ? x.company_id : '-',
            company_name: x.company_name,
            updated_at: x.updated_at !== '' ? this.formatDateToShow(x.updated_at.substr(0, 10)) : '',
            order_number: x.order_number + '#' + x.pdf_path_qu,
            sku: x.sku,
            product_id: x.product_id,
            net_price: parseFloat(x.net_price).toFixed(2)
          }
        })
        this.series = await [{ name: 'รายได้ทั้งหมด', data: response.data_graph }]
        // this.shopList = await response.shopList
        this.shopList.push({
          shop_id: '',
          shop_name: 'ทั้งหมด'
        })
        var dataDateGraph = await response.date_graph

        for (var i = 0; i < dataDateGraph.length; i++) {
          // console.log(new Date(dataDateGraph[i]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          this.dateGraph.push(new Date(dataDateGraph[i]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
        }
        // console.log(this.dataRes)
      }
      await $('#example').DataTable({
        dom: 'Bfrtip',
        destroy: true,
        language: {
          Search: '',
          searchPlaceholder: 'ค้นหา'
        },
        oLanguage: {
          sSearch: '',
          searchPlaceholder: 'ค้นหา',
          emptyTable: 'ไม่มีข้อมูลรายได้ของร้านค้าในตาราง',
          zeroRecords: 'ไม่พบข้อมูลรายได้ของร้านค้าในตาราง'
        },
        responsive: {
          details: {
            display: $.fn.dataTable.Responsive.display.childRowImmediate
          }
        },
        columnDefs: [{
          type: 'extract-date',
          targets: [4]
        },
        {
          targets: 5,
          data: 'order_number',
          render: function (data, type, row, meta) {
            var arr = data.split('#')
            return '<a href="' + arr[1] + '"  target="_blank">' + arr[0] + '</a>'
          }
        }],
        columns: [
          { data: 'user_id' },
          { data: 'buyer_name' },
          { data: 'company_id' },
          {
            data: 'company_name',
            visible: false
          },
          { data: 'updated_at' },
          { data: 'order_number' },
          { data: 'product_id' },
          { data: 'sku' },
          { data: 'net_price' }
        ],
        buttons: [
          {
            extend: 'csv',
            text: '<i class="fa fa-file"></i> Csv',
            charset: 'utf-8',
            extension: '.csv',
            filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            bom: true
          },
          {
            extend: 'copy',
            text: '<i class="fa fa-copy"></i> Copy',
            titleAttr: 'Copy'
          },
          {
            extend: 'excel',
            text: ' <i class="fa fa-file-excel"></i> Excel',
            customizeData: function (data) {
              for (var i = 0; i < data.body.length; i++) {
                data.body[i][5] = `${data.body[i][5]}`
              }
            }
            // exportOptions: {
            //   columns: [0, 1, 2, 3, 4, 5, 6],
            //   format: {
            //     body: function (data, row, column, node) {
            //       console.log(data)
            //       var cellData
            //       cellData = data.indexOf('<') < 0 ? data : $(data).text()
            //       return column === 1 ? '\u200C' + cellData : cellData
            //     }
            //   }
            // }
          },
          {
            extend: 'print',
            text: '<i class="fa fa-print"></i> Print',
            titleAttr: 'Print'
          }
        ]
      })
      this.$EventBus.$emit('getDataTop10buyers')
      this.$EventBus.$emit('getDataTop10purchasers')
    },
    async getDataTransactionReload (startDate, endDate, type, shopID) {
      // console.log('type', type)
      this.dataRes = []
      this.series = []
      this.dateGraph = []
      var dataDateFilterReload = ''
      var data = {
        start_date: startDate,
        end_date: endDate,
        type: type,
        shop_id: shopID !== '' && shopID !== null ? shopID : ''
      }
      await this.$store.dispatch('actionNewGetDashboard', data)
      var response = await this.$store.state.ModuleShop.stateNewGetDashboard
      // console.log('NewGetDashboard Reload', response)
      if (response) {
        this.series = await [{ name: 'รายได้ทั้งหมด', data: response.data_graph }]
        this.$store.state.ModuleShop.state10User = await response.top_user
        this.$store.state.ModuleShop.state10Product = await response.top_product
        if (type === 'year') {
          dataDateFilterReload = await response.date_graph
          for (var x = 0; x < dataDateFilterReload.length; x++) {
            this.dateGraph.push(new Date(dataDateFilterReload[x]).toLocaleDateString('en-SG', { year: 'numeric', month: '2-digit' }))
          }
          // await this.dateForYear(response.date_graph)
          this.dataRes = await response.data_transaction.map(x => {
            return {
              user_id: x.user_id,
              buyer_name: x.buyer_name,
              company_id: x.company_id !== -1 ? x.company_id : '-',
              company_name: x.company_name,
              updated_at: x.updated_at !== '' ? this.formatDateToShow(x.updated_at.substr(0, 10)) : '',
              order_number: x.order_number + '#' + x.pdf_path_qu,
              sku: x.sku,
              product_id: x.product_id,
              net_price: parseFloat(x.net_price).toFixed(2)
            }
          })
        } else if (type === 'all') {
          dataDateFilterReload = await response.date_graph
          for (var j = 0; j < dataDateFilterReload.length; j++) {
            this.dateGraph.push(new Date(dataDateFilterReload[j]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long' }))
          }
          this.dataRes = await response.data_transaction.map(x => {
            return {
              user_id: x.user_id,
              buyer_name: x.buyer_name,
              company_id: x.company_id !== -1 ? x.company_id : '-',
              company_name: x.company_name,
              updated_at: x.updated_at !== '' ? this.formatDateToShow(x.updated_at.substr(0, 10)) : '',
              order_number: x.order_number + '#' + x.pdf_path_qu,
              sku: x.sku,
              product_id: x.product_id,
              net_price: parseFloat(x.net_price).toFixed(2)
            }
          })
        } else {
          dataDateFilterReload = await response.date_graph
          for (var i = 0; i < dataDateFilterReload.length; i++) {
            this.dateGraph.push(new Date(dataDateFilterReload[i]).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }))
          }
          this.dataRes = await response.data_transaction.map(x => {
            return {
              user_id: x.user_id,
              buyer_name: x.buyer_name,
              company_id: x.company_id !== -1 ? x.company_id : '-',
              company_name: x.company_name,
              updated_at: x.updated_at !== '' ? this.formatDateToShow(x.updated_at.substr(0, 10)) : '',
              order_number: x.order_number + '#' + x.pdf_path_qu,
              product_id: x.product_id,
              sku: x.sku,
              net_price: parseFloat(x.net_price).toFixed(2)
            }
          })
        }
        // console.log(this.dataRes)
      }
      await $('#help').remove()
      var table = await $('#example').DataTable({
        dom: 'Bfrtip',
        destroy: true,
        language: {
          Search: '',
          searchPlaceholder: 'ค้นหา'
        },
        oLanguage: {
          sSearch: '',
          searchPlaceholder: 'ค้นหา',
          emptyTable: 'ไม่มีข้อมูลรายได้ของร้านค้าในตาราง',
          zeroRecords: 'ไม่พบข้อมูลรายได้ของร้านค้าในตาราง'
        },
        columns: [
          // {
          //   className: 'dt-control',
          //   orderable: false,
          //   data: null,
          //   defaultContent: ''
          // },
          { data: 'user_id' },
          { data: 'buyer_name' },
          { data: 'company_id' },
          {
            data: 'company_name',
            visible: false
          },
          { data: 'updated_at' },
          { data: 'order_number' },
          { data: 'product_id' },
          { data: 'sku' },
          { data: 'net_price' }
        ],
        // responsive: {
        //   details: {
        //     type: 'column',
        //     display: $.fn.dataTable.Responsive.display.modal({
        //       header: function (row) {
        //         var data = row.data()
        //         return 'Details for ' + data[0] + ' ' + data[1]
        //       }
        //     }),
        //     renderer: function (api, rowIdx, columns) {
        //       var data = $.map(columns, function (col, i) {
        //         return '<tr>' +
        //           '<td>' + col.title + ':' + '</td> ' +
        //           '<td>' + col.data + '</td>' +
        //           '</tr>'
        //       }).join('')
        //       return $('<table width="100%"/>').append(data)
        //     }
        //   }
        // },
        responsive: {
          details: {
            display: $.fn.dataTable.Responsive.display.childRowImmediate
          }
        },
        // initComplete: function () {
        //   showHideColumn()
        // },
        // responsive: true,
        columnDefs: [
          {
            targets: 0,
            data: 'user_id',
            render: function (data, type, row, meta) {
              return '<span style="margin-left: 10px;">' + data + '</span>'
            }
          },
          {
            targets: 1,
            render: function (data) {
              return '<div style="display: inline-block; width: 140px; white-space: nowrap;overflow: hidden !important;text-overflow: ellipsis; padding-top: 8px;">' + data + '</div>'
            }
          },
          {
            targets: 5,
            data: 'order_number',
            render: function (data, type, row, meta) {
              var arr = data.split('#')
              return '<a href="' + arr[1] + '"  target="_blank">' + arr[0] + '</a>'
            }
          },
          {
            targets: 7,
            render: function (data) {
              return '<div style="display: inline-block; width: 140px; white-space: nowrap;overflow: hidden !important;text-overflow: ellipsis; padding-top: 8px;">' + data + '</div>'
            }
          }
        ],
        buttons: [
          {
            extend: 'csv',
            text: '<i class="fa fa-file"></i> Csv',
            charset: 'utf-8',
            extension: '.csv',
            filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            bom: true
          },
          {
            extend: 'copy',
            text: '<i class="fa fa-copy"></i> Copy',
            titleAttr: 'Copy'
          },
          {
            extend: 'excel',
            text: ' <i class="fa fa-file-excel"></i> Excel',
            customizeData: function (data) {
              for (var i = 0; i < data.body.length; i++) {
                data.body[i][5] = `${data.body[i][5]}`
              }
            }
            // exportOptions: {
            //   columns: [0, 1, 2, 3, 4, 5, 6],
            //   format: {
            //     body: function (data, row, column, node) {
            //       console.log(data)
            //       var cellData
            //       cellData = data.indexOf('<') < 0 ? data : $(data).text()
            //       return column === 1 ? '\u200C' + cellData : cellData
            //     }
            //   }
            // }
          },
          {
            extend: 'print',
            text: '<i class="fa fa-print"></i> Print',
            titleAttr: 'Print'
          }
        ]
      })
      this.$EventBus.$emit('getDataTop10buyers')
      this.$EventBus.$emit('getDataTop10purchasers')
      await table.clear().rows.add(this.dataRes).draw()
      // await table.on('responsive-resize', (e, datatable, columns) => {
      //   var count = columns.reduce(function (a, b) {
      //     return b === false ? a + 1 : a
      //   }, 0)
      //   console.log(count + 'column(s) are hidden')
      // })
      function showHideColumn () {
        if (table.responsive.hasHidden()) {
          // Some columns are hidden, so, show the controls
          table.columns([0]).addClass('dt-control')
        } else {
          // No columns are hidden, so, hide the controls
          // table.columns([3]).visible(false)
        }
      }
      await this.setIconData()
      $(window).resize(function () {
        clearTimeout(window.refresh_size)
        window.refresh_size = setTimeout(function () { showHideColumn() }, 250)
      })
    },
    getSelectDateEnd (val) {
      this.$refs.dialog1.save(val)
      this.dateSelectFunctionTwo = ''
      this.dateSelectFunctionTwo = new Date(this.dateFormatted2)
      this.dateEnd = this.formatDate(new Date(this.dateSelectFunctionTwo.setDate(this.dateSelectFunctionTwo.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      var startSelect = this.dateFormat(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      var endDate = this.dateFormat(new Date(this.dateSelectFunctionTwo.setDate(this.dateSelectFunctionTwo.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10), 'yyyy-MM-dd')
      this.FirstDateSelect = startSelect
      this.EndDateSelect = endDate
      var typefilter = 'day'
      this.typeFilterForShop = ''
      this.$EventBus.$emit('getDataTop10buyers', startSelect, endDate, typefilter, this.shopID)
      this.$EventBus.$emit('getDataTop10purchasers', startSelect, endDate, typefilter, this.shopID)
      this.$EventBus.$emit('getDataShopTopTen', startSelect, endDate, typefilter, this.shopID)
      this.getDataTransactionReload(startSelect, endDate, typefilter, this.shopID)
      this.modal1 = false
    },
    async getSelectDate (val) {
      this.$refs.dialog.save(val)
      this.$refs.pickerDateStart.activePicker = 'DATE'
      this.dateStart = ''
      this.dateEnd = ''
      this.dateSelectFunctionOne = ''
      this.dateSelectFunctionOne = new Date(this.dateFormatted)
      this.dateStart = this.formatDate(new Date(this.dateSelectFunctionOne.setDate(this.dateSelectFunctionOne.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
      this.modal = false
    },
    dateFormat (inputDate, format) {
      // parse the input date
      const date = new Date(inputDate)
      // console.log(date)
      // extract the parts of the date
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      // replace the month
      format = format.replace('MM', month.toString().padStart(2, '0'))
      // replace the year
      if (format.indexOf('yyyy') > -1) {
        format = format.replace('yyyy', year.toString())
      } else if (format.indexOf('yy') > -1) {
        format = format.replace('yy', year.toString().substr(2, 2))
      }
      // replace the day
      format = format.replace('dd', day.toString().padStart(2, '0'))
      return format
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    async getFilter (valSelect) {
      const today = await new Date()
      // console.log('SDE', this.weekday.id)
      if (valSelect === 2) {
        this.dataSelect = await 1
        var firstDateYear = await (new Date(today.setMonth(today.getMonth() - 1))).toISOString().substr(0, 10)
        var LastDateYear = await (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var typefilter = await 'month'
        await this.getDataTransactionReload(firstDateYear, LastDateYear, typefilter, this.shopID)
      } else if (valSelect === 3) {
        this.dataSelect = 1
        var today2 = await new Date()
        var start = await ''
        if (((today2.getMonth() + 1) - 6) < 0) {
          await today2.setMonth((today2.getMonth() + 1) - 6)
          start = await today2
        } else {
          start = await today.setMonth(today.getMonth() - 6)
        }
        // const [month, day, year] = await new Date(end).toLocaleDateString('default', { year: 'numeric', month: '2-digit', day: '2-digit' }).split('/')
        var firstDateYear2 = await new Date(start).toISOString().substr(0, 10)
        var LastDateYear2 = await (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var typefilter2 = '6month'
        await this.getDataTransactionReload(firstDateYear2, LastDateYear2, typefilter2, this.shopID)
      } else if (valSelect === 4) {
        this.dataSelect = 1
        var today3 = await new Date()
        var end3 = await ''
        if (((today3.getMonth() + 1) - 12) < 0) {
          await today3.setMonth((today3.getMonth()) - 12)
          end3 = await today3
        } else {
          end3 = await today.setMonth(today.getMonth() - 12)
        }
        var firstDateYear3 = await new Date(end3).toISOString().substr(0, 10)
        var LastDateYear3 = await (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var typefilter3 = 'year'
        await this.getDataTransactionReload(firstDateYear3, LastDateYear3, typefilter3, this.shopID)
      } else if (valSelect === 1) {
        this.dataSelect = 0
      } else {
        this.dataSelect = 1
        var firstDateYear4 = await (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var LastDateYear4 = await (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        var typefilter4 = 'all'
        this.$EventBus.$emit('getDataTop10buyers', firstDateYear4, LastDateYear4, typefilter4, this.shopID)
        this.$EventBus.$emit('getDataTop10purchasers', firstDateYear4, LastDateYear4, typefilter4, this.shopID)
        this.$EventBus.$emit('getDataShopTopTen', firstDateYear4, LastDateYear4, typefilter4, this.shopID)
        await this.getDataTransactionReload(firstDateYear4, LastDateYear4, typefilter4, this.shopID)
      }
    },
    dateForYear (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [year, month] = date.split('-')
      return `${month}/${year}`
    },
    async afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [month, day, year] = await date.split('/')
      return `${year}-${month}-${day}`
    },
    getStatus (item) {
      if (item === 'Success') return 'ชำระเงินสำเร็จ'
      else if (item === 'Not Paid') return 'ยังไม่ชำระเงิน'
      else if (item === 'Fail') return 'ชำระเงินไม่สำเร็จ'
      else return 'ยกเลิก'
    },
    getColorText (item) {
      if (item === 'Success') return '#1AB759'
      else if (item === 'Not Paid') return '#1B5DD6'
      else if (item === 'Fail') return '#E9A016'
      else return '#F5222D'
    },
    getColorBack (item) {
      if (item === 'Success') return 'rgba(236, 248, 234, 0.8)'
      else if (item === 'Not Paid') return 'rgba(245, 249, 255, 0.8)'
      else if (item === 'Fail') return '#FCF0DA'
      else return 'rgba(251, 229, 228, 0.8)'
    }
  }
}
</script>

<style scoped="scss">
.dot {
    display: inline-block;
    width: 145px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    padding-top: 8px;
  }
@media only screen and (max-width: 650px) {
  .table-striped {
    display: none;
  }
  ::v-deep .dataTables_paginate.paging_simple_numbers {
    display: none;
  }
  ::v-deep #example_filter.dataTables_filter {
    display: none;
  }
}
@media only screen and (min-width: 750px) {
  ::v-deep .mobile {
    display: none;
  }
}
::v-deep #example_info {
  display: none;
}
::v-deep .dataTables_filter {
  margin-bottom: 1em;
}
::v-deep .dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    width: 331px;
    background-color: transparent;
    margin-left: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABmJLR0QA/wD/AP+gvaeTAAABs0lEQVQ4ja3TTWsTURjF8f+ZTMUwLV10VSgVVBhDt4KrutB2aYVgBqSC4LQ7v4IbBf0KlmYErYJMJpZ+h4KLrgOm1IVvq+IqaqPNzOMiddFwk2L1LO9z+XEuPBf+UzRsEG4lE6XDfDYv9KPN5EeiKP8rKMySy5g9BBYB/+h436CuM3rSvhl3XJB3DGkky5htA188z7vy81up7BtTSPdl3LBf9rbSXJse2eioyTYobtfiV4MXZ9K0HNDZBBtv1+J5JHM3MnsEbLgQgM9RdCDfX0bMhVl9yfm0cCuZABZk9tSF/Mm76t2vgoag6oRKh/ks4He/+61RUL84LUPnnVBP/gHA2UkvOAmSFMis64R28/EPwL4VvesnNsKuFdKOEyKKckkJhT2YSdPyMKSSri8CV8eMZ24IsDEeS3gBnc1Lb55PuZBCeg2WtqKVvcH5sc2uNNemi7zUQMyZlIqiJVNgaAFsHiwF1ZDF7VurL4dC/WqmMKsvCapmuoDoStopFZa0opW9MFu/gykZxIZ+2lEJsyTCbMPQ6m4tfnFqqI/1m/V63sX3t+99Oq0DQKVZP/dPgCu/AanyqVoXnFHTAAAAAElFTkSuQmCC) !important;
    background-position: 570px 10px !important;
    outline: transparent;
    border: 1px solid #cacaca;
    border-radius: 6px;
  }
  ::v-deep .input {
    display: inline-block;
    white-space: nowrap;
  }
  ::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
}
::v-deep #example {
  padding-top: 12px;
}
::v-deep .sorting {
  color: #e6f5f3;
  background-color: #e6f5f3;
}
::v-deep table.dataTable tbody td.sorting_1 {
  text-align: center;
}
::v-deep #example > thead > tr > th {
  background-color: #e6f5f3;
  color: #27AB9C;
  font-size: 12px;
}
::v-deep table.dataTable th, table.dataTable td  {
  border-bottom: 1px solid #e7e7e7;
}
::v-deep div.dt-buttons {
  float: right;
 }
 ::v-deep .dataTables_wrapper .dataTables_filter  {
  float: left;
 }
  .fontData {
    font-size: 14px;
  }
  .tableLong {
    width: 200px;
  }
  .container {
    margin-right: 25px;
  }
  .sec {
    width: 30vw !important;
  }
  .dot {
    display: inline-block;
    width: 140px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    padding-top: 8px;
}
.hiDe {
  display: none;
}
.dot2 {
    display: inline-block;
    width: 140px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
    padding-top: 8px;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty {
  cursor: default !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.child:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dataTables_empty:before {
  display: none !important;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
}
table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr > th.dtr-control:before {
  top: 50%;
  left: 5px;
  height: 1em;
  width: 1em;
  margin-top: -9px;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed > tbody > tr.parent > th.dtr-control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control {
  padding-left: 27px;
}
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-inline.collapsed.compact > tbody > tr > th.dtr-control:before {
  left: 4px;
  height: 14px;
  width: 14px;
  border-radius: 14px;
  line-height: 14px;
  text-indent: 3px;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control,
table.dataTable.dtr-column > tbody > tr > th.dtr-control,
table.dataTable.dtr-column > tbody > tr > td.control,
table.dataTable.dtr-column > tbody > tr > th.control {
  position: relative;
  cursor: pointer;
}
table.dataTable.dtr-column > tbody > tr > td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr > td.control:before,
table.dataTable.dtr-column > tbody > tr > th.control:before {
  top: 50%;
  left: 50%;
  height: 0.8em;
  width: 0.8em;
  margin-top: -0.5em;
  margin-left: -0.5em;
  display: none;
  position: absolute;
  color: white;
  border: 0.15em solid white;
  border-radius: 1em;
  box-shadow: 0 0 0.2em #444;
  box-sizing: content-box;
  text-align: center;
  text-indent: 0 !important;
  font-family: "Courier New", Courier, monospace;
  line-height: 1em;
  content: "+";
  background-color: #31b131;
}
table.dataTable.dtr-column > tbody > tr.parent td.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent th.dtr-control:before,
table.dataTable.dtr-column > tbody > tr.parent td.control:before,
table.dataTable.dtr-column > tbody > tr.parent th.control:before {
  content: "-";
  background-color: #d33333;
}
table.dataTable > tbody > tr.child {
  padding: 0.5em 1em;
}
table.dataTable > tbody > tr.child:hover {
  background: transparent !important;
}
table.dataTable > tbody > tr.child ul.dtr-details {
  display: inline-block;
  list-style-type: none;
  margin: 0;
  padding: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li {
  border-bottom: 1px solid #efefef;
  padding: 0.5em 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:first-child {
  padding-top: 0;
}
table.dataTable > tbody > tr.child ul.dtr-details > li:last-child {
  border-bottom: none;
}
table.dataTable > tbody > tr.child span.dtr-title {
  display: inline-block;
  min-width: 75px;
  font-weight: bold;
}
div.dtr-modal {
  position: fixed;
  box-sizing: border-box;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
  z-index: 100;
  padding: 10em 1em;
}
div.dtr-modal div.dtr-modal-display {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  width: 50%;
  height: 50%;
  overflow: auto;
  margin: auto;
  z-index: 102;
  overflow: auto;
  background-color: #f5f5f7;
  border: 1px solid black;
  border-radius: 0.5em;
  box-shadow: 0 12px 30px rgba(0, 0, 0, 0.6);
}
div.dtr-modal div.dtr-modal-content {
  position: relative;
  padding: 1em;
}
div.dtr-modal div.dtr-modal-close {
  position: absolute;
  top: 6px;
  right: 6px;
  width: 22px;
  height: 22px;
  border: 1px solid #eaeaea;
  background-color: #f9f9f9;
  text-align: center;
  border-radius: 3px;
  cursor: pointer;
  z-index: 12;
}
div.dtr-modal div.dtr-modal-close:hover {
  background-color: #eaeaea;
}
div.dtr-modal div.dtr-modal-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 101;
  background: rgba(0, 0, 0, 0.6);
}

@media screen and (max-width: 767px) {
  div.dtr-modal div.dtr-modal-display {
    width: 95%;
  }
}

</style>
