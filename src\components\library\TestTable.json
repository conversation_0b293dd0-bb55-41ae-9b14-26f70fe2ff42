{"data": [{"date": "24 พฤษภาคม 2565", "code": "1234567890111213", "price": "3,429", "paymentDate": "20-05-2564  09:52:18", "status": "Pending"}, {"date": "24 พฤษภาคม 2565", "code": "1234567890111213", "price": "3,429", "paymentDate": "20-05-2564  09:52:18", "status": "Approve"}, {"date": "24 พฤษภาคม 2565", "code": "1234567890111213", "price": "3,429", "paymentDate": "20-05-2564  09:52:18", "status": "Pending"}, {"date": "24 พฤษภาคม 2565", "code": "1234567890111213", "price": "3,429", "paymentDate": "20-05-2564  09:52:18", "status": "Approve"}, {"date": "24 พฤษภาคม 2565", "code": "1234567890111213", "price": "3,429", "paymentDate": "20-05-2564  09:52:18", "status": "Not Paid"}], "data-2": [{"code": 200, "data": {"listRefShare": [{"payment_transaction_number": "220420000000000348", "total_amount": "47412", "transaction_status": "Success", "orderIDRef": "P22042006389030", "updated_at": "2022-04-20 10:54:56", "total_shipping": 225, "income": 45771.39}, {"payment_transaction_number": "220420000000000349", "total_amount": "70821", "transaction_status": "Success", "orderIDRef": "P22042006389212", "updated_at": "2022-04-20 11:11:30", "total_shipping": 415, "income": 68293.82}, {"payment_transaction_number": "220420000000000350", "total_amount": "70077", "transaction_status": "Success", "orderIDRef": "P22042006389490", "updated_at": "2022-04-20 11:11:30", "total_shipping": 420, "income": 67567.29}, {"payment_transaction_number": "220420000000000351", "total_amount": "1198425", "transaction_status": "Success", "orderIDRef": "P22042006389610", "updated_at": "2022-04-20 11:11:30", "total_shipping": 25, "income": 1162448}, {"payment_transaction_number": "220420000000000352", "total_amount": "23409", "transaction_status": "Success", "orderIDRef": "P22042006389740", "updated_at": "2022-04-20 11:11:30", "total_shipping": 190, "income": 22522.43}], "incomeShop": 0}, "result": "SUCCESS", "message": "List data refshare success"}], "headers": [{"text": "วันที่", "align": "start", "sortable": false, "filterable": false, "value": "created_at", "class": "backgroundTable fontTable--text"}, {"text": "รหัสการสั่งซื้อ", "align": "center", "sortable": false, "filterable": true, "value": "payment_transaction_numbe", "class": "backgroundTable fontTable--text"}, {"text": "ราคา", "align": "end", "value": "user_name", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "สถานะการคืนสินค้า", "value": "status_refund", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text mx-3"}, {"text": "", "value": "actions", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}], "headers-2": [{"text": "ลำดับ", "align": "start", "value": "index", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "รหัสการสั่งซื้อ", "align": "center", "value": "order_id", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text"}, {"text": "วันที่", "align": "end", "sortable": false, "value": "transfer_date", "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "ราคาสุทธิ", "value": "receive_amount", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text mx-3"}, {"text": "เลขที่ทำรายการชำระเงิน", "value": "payment_reference_id", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text"}, {"text": "สถานะการถอนเงิน", "value": "response_message", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "กำไรของร้านค้า", "value": "final_receive_amount", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}], "headers-2.2": [{"text": "ลำดับ", "align": "start", "value": "index", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "รหัสการสั่งซื้อ", "align": "center", "value": "order_id", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text"}, {"text": "วันที่", "align": "end", "value": "transaction_date", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "ราคาสุทธิ", "value": "receive_amount", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text mx-3"}, {"text": "เลขที่ทำรายการชำระเงิน", "value": "payment_reference_id", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text"}, {"text": "สถานะการถอนเงิน", "value": "response_message", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "กำไรของร้านค้า", "value": "final_receive_amount", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}], "headers-3": [{"text": "รหัสผู้ซื้อ", "align": "center", "sortable": false, "value": "buyer_ID", "filterable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "ชื่อผู้ซื้อ", "align": "end", "value": "buyer_name", "filterable": false, "sortable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "รหัสบริษัท", "value": "buyer_org_ID", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text mx-3 text-md-center"}, {"text": "ชื่อบริษัท", "value": "buyer_org_name", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "เลขที่ทำรายการ", "value": "order_number", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "รหัสสินค้า", "value": "product_id", "filterable": false, "sortable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "รหัสร้านค้า", "value": "shop_ID", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "เลข SKU", "value": "sku", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "หน่วย", "value": "unit", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text text-md-center"}, {"text": "มูลค่า", "value": "value", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text text-md-center"}], "headers-4": [{"text": "ลำดับ", "align": "start", "value": "index", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "รหัสการสั่งซื้อ", "align": "center", "value": "orderIDRef", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text"}, {"text": "วันที่", "align": "end", "value": "updated_at", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "ราคาสินค้า", "value": "total_amount", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text mx-3"}, {"text": "เลขที่ทำรายการชำระเงิน", "value": "payment_transaction_number", "sortable": false, "filterable": true, "class": "backgroundTable fontTable--text"}, {"text": "สถานะการถอนเงิน", "value": "transaction_status", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}, {"text": "กำไรของร้านค้า", "value": "income", "sortable": false, "filterable": false, "class": "backgroundTable fontTable--text"}], "datatables": [{"date": "20-05-2022 10:52:05", "order_number": "220520000000000010_4", "buyer_ID": "86", "buyer_name": "อคัรพนัธ์ เจนธีรพงศ์", "buyer_org_ID": 0, "buyer_org_name": null, "shop_ID": 288, "shop_name": "เทสต้า 6 ร้านค้าของอาย", "product_id": 24, "sku": "TEST06-01", "value": 2900, "unit": 1}, {"date": "30-05-2022 24:16:10", "order_number": "220531000000000039_4", "buyer_ID": "1", "buyer_name": "นายจอมขวัญ คมชัด", "buyer_org_ID": 11, "buyer_org_name": "ทดสอบหนึ่ง", "shop_ID": 288, "shop_name": "เทสต้า 6 ร้านค้าของอาย", "product_id": 24, "sku": "TEST06-01", "value": 2900, "unit": 1}, {"date": "30-05-2022 24:20:15", "order_number": "220531000000000040_4", "buyer_ID": "1", "buyer_name": "นายจอมขวัญ คมชัด", "buyer_org_ID": 11, "buyer_org_name": "ทดสอบหนึ่ง", "shop_ID": 288, "shop_name": "เทสต้า 6 ร้านค้าของอาย", "product_id": 24, "sku": "TEST06-01", "value": 2900, "unit": 1}, {"date": "01-06-2022 13:30:18", "order_number": "220601000000000046_1", "buyer_ID": "67", "buyer_name": "ทดสอบแปด ทดสอบแปด", "buyer_org_ID": -1, "buyer_org_name": null, "shop_ID": 288, "shop_name": "เทสต้า 6 ร้านค้าของอาย", "product_id": 29, "sku": "TEST06-03", "value": 100, "unit": 1}]}