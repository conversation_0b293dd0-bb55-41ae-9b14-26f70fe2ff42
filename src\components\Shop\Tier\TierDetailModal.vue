<template>
  <div class="text-center">
    <v-dialog v-model="openModalTierDetail" :width="MobileSize ? '100%' : IpadSize ? '100%' : '782'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 782px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ข้อมูลกลุ่มคู่ค้า</b></span>
              </v-col>
              <v-btn fab small @click="cancel()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '782px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 20px 20px 10px 20px;' : 'padding: 40px 48px 10px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" class="d-flex" v-if="!MobileSize">
                    <v-row dense class="mr-auto">
                      <v-img src="@/assets/Create_Store/partnerShopDetail.png" max-height="62" max-width="62"></v-img>
                      <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 16px;"> ข้อมูลเทียร์ของร้านค้า </span>
                    </v-row>
                    <span class="ml-auto pt-3" style="text-align: end;">
                      <span style="font-weight: 400; color: #333333; font-size: 16px;" >สถานะการใช้งาน :
                        <span v-if="data.status === 'active'">
                          <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                        </span>
                        <span v-else>
                          <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                        </span>
                      </span>
                    </span>
                  </v-col>
                  <v-col cols="12" v-else>
                    <v-row dense>
                      <v-img src="@/assets/Create_Store/partnerShopDetail.png" max-height="62" max-width="62"></v-img>
                      <span class="pt-5 pl-4" style="font-weight: 600; color: #333333; font-size: 16px;"> ข้อมูลเทียร์ของร้านค้า </span>
                    </v-row>
                    <v-row dense class="pl-1 pt-2">
                      <span style="font-weight: 400; color: #333333; font-size: 14px;">สถานะการใช้งาน :
                        <span v-if="data.status === 'active'">
                          <v-chip color="#F0FEE8" text-color="#52C41A">กำลังใช้งาน</v-chip>
                        </span>
                        <span v-else>
                          <v-chip color="#FEE7E8" text-color="#F5222D">ยกเลิกใช้งาน</v-chip>
                        </span>
                      </span>
                    </v-row>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="4" md="2" sm="2">
                        <span class="fontStyleTitle" :style="MobileSize ? 'font-size: 14px;' : ''">ชื่อกลุ่มคู่ค้า :</span>
                      </v-col>
                      <v-col cols="8" md="10" sm="10">
                        <span class="fontStyleData">{{ data.tier_name }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="4" md="2" sm="2">
                        <span class="fontStyleTitle" :style="MobileSize ? 'font-size: 14px;' : ''">ส่วนลด :</span>
                      </v-col>
                      <v-col cols="8" md="10" sm="10">
                        <span class="fontStyleData" v-if="data.discount_percent !== '0.00'">{{ data.discount_percent }} %</span>
                        <span class="fontStyleData" v-else>ไม่มีส่วนลด</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="9">
                        <span class="fontStyleTitle" :style="MobileSize ? 'font-size: 14px;' : ''">ต้องการกำหนดเอกสารในการขอเป็นคู่ค้าหรือไม่</span><br/>
                        <span style="font-size: 12px; font-weight: 400; color: #CCCCCC; line-height: 16px;">*ถ้าต้องการกำหนดเอกสารจะบังคับให้แสดงข้อมูลเป็นใช่</span>
                      </v-col>
                      <v-col cols="2">
                        <v-radio-group v-model="data.req_document" class="mt-0 float-left" hide-details>
                          <v-radio :style="MobileSize ? 'font-size: 14px;' : ''" :readonly="true" value="Y" color="#27AB9C" label="ใช่" v-if="data.req_document === 'Y'"></v-radio>
                          <v-radio :style="MobileSize ? 'font-size: 14px;' : ''" :readonly="true" value="N" color="#27AB9C" label="ไม่ใช่" v-if="data.req_document === 'N'"></v-radio>
                        </v-radio-group>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <v-row dense>
                      <v-col cols="9">
                        <span class="fontStyleTitle" :style="MobileSize ? 'font-size: 14px;' : ''">ต้องการแสดงข้อมูลให้ผู้ซื้อเห็น</span>
                      </v-col>
                      <v-col cols="2">
                        <v-radio-group v-model="data.show_buyer" class="mt-0 float-left" hide-details>
                          <v-radio :style="MobileSize ? 'font-size: 14px;' : ''" :readonly="true" value="Y" color="#27AB9C" label="ใช่" v-if="data.show_buyer === 'Y'"></v-radio>
                          <v-radio :style="MobileSize ? 'font-size: 14px;' : ''" :readonly="true" value="N" color="#27AB9C" label="ไม่ใช่" v-if="data.show_buyer === 'N'"></v-radio>
                        </v-radio-group>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" v-if="data.tier_document_list.length !== 0">
                    <span style="font-weight: 600; color: #333333; font-size: 16px;">รายการเอกสารที่ร้องขอ</span>
                  </v-col>
                  <v-col v-for="(item, index) in data.tier_document_list" :key="index" cols="12" class="mt-3 px-5">
                    <v-card height="100" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-3 py-2">
                      <v-row class="mx-0">
                        <v-col class="px-0">
                          <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="84px" height="84px"></v-img>
                          <v-card-title class="float-left pl-3 doc-detail">{{ item.name_document }}</v-card-title>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions class="px-0">
          <v-row dense justify="center" style="height: 88px; background: #F5FCFB;">
            <v-btn rounded width="154" height="40" @click="editTier(data)" color="#27AB9C" style="color: #FFFFFF;" class="#FFFFFF--text my-auto" >
              <v-icon left color="#FFFFFF">mdi-pencil-outline</v-icon>แก้ไข
            </v-btn>
          </v-row>
        </v-card-actions>
        <!-- MobileSize -->
        <!-- <v-container v-if="MobileSize" grid-list-xs>
          <v-row class="px-5" no-gutters>
            <v-col cols="12">
              <v-img class="float-left" src="@/assets/icons/Tier.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 16px; line-height: 26px; color: #333333;">ข้อมูลเทียร์ของร้านค้า</v-card-title>
            </v-col>
            <v-col cols="12" align="end">
              <v-btn text @click="editTier()" color="#27AB9C" style="color: #33333;" class="#333333--text" >
                <v-icon left color="#27AB9C">mdi-pencil-outline</v-icon>แก้ไขข้อมูล
              </v-btn>
            </v-col>
            <v-col cols="12" class="mt-6">
              <span class="float-left mr-5">ชื่อ  : <span class="doc-detail">{{ data.tier_name }}</span></span>
              <span class="float-left">ส่วนลด  : <span class="discount doc-detail">{{ data.discount_percent }} %</span> </span>
            </v-col>
            <v-col cols="12" class="mt-3">
              <span></span>
            </v-col>
            <v-col cols="12">ต้องการกำหนดเอกสารในการขอเป็นคู่ค้าหรือไม่
              <v-checkbox :readonly="true" class="checkbox-qu " dense v-model="data.req_document" label="ใช่" value="Y"></v-checkbox>
              <v-checkbox :readonly="true" class="checkbox-qu float-left pl-3" dense v-model="data.req_document" label="ไม่" value="N"></v-checkbox>
            </v-col>
            <v-col cols="12" class="mt-4">
              <span>ต้องการแสดงข้อมูลให้ผู้ซื้อเห็นหรือไม่</span>float-left
            </v-col>
            <v-col cols="12" class="mb-0">
              <v-checkbox :readonly="true" class="checkbox-qu float-left" dense v-model="data.show_buyer" label="ใช่" value="Y"></v-checkbox>
              <v-checkbox :readonly="true" class="checkbox-qu float-left pl-3" dense v-model="data.show_buyer" label="ไม่" value="N"></v-checkbox>
            </v-col>
            <v-col v-for="(item, index) in data.tier_document_list" :key="index" cols="12" class="mt-3 px-5">
              <v-card height="100" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-3 py-2">
                <v-row class="mx-0">
                  <v-col class="px-0">
                    <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="84px" height="84px"></v-img>
                    <v-card-title class="float-left pl-3 doc-detail">{{ item.name_document }}</v-card-title>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-container> -->
        <!-- desktopSize -->
        <!-- <v-container v-else grid-list-xs>
          <v-row class="px-5 pb-5" no-gutters>
            <v-col cols="6" class="pa-5">
              <v-img class="float-left" src="@/assets/icons/Tier.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">ข้อมูลเทียร์ของร้านค้า</v-card-title>
            </v-col>
            <v-col cols="6" align="end" class="pa-5">
              <v-btn text @click="editTier()" color="#27AB9C" style="color: #33333;" class="#333333--text" >
                <v-icon left color="#27AB9C">mdi-pencil-outline</v-icon>แก้ไขข้อมูล
              </v-btn>
            </v-col>
            <v-col cols="12" class="pa-5">
              <span class="float-left mr-5">ชื่อ  : <span class="doc-detail">{{ data.tier_name }}</span></span>
              <span class="float-left">ส่วนลด  : <span class="discount doc-detail">{{ data.discount_percent }} %</span> </span>
            </v-col>
            <v-col cols="12" class="px-5">
              <span>ต้องการกำหนดเอกสารในการขอเป็นคู่ค้าหรือไม่</span>
              <v-checkbox :readonly="true" class="float-right pl-3" dense v-model="data.req_document" label="ไม่" value="N"></v-checkbox>
              <v-checkbox :readonly="true" align="end" class="float-right pl-3" dense v-model="data.req_document" label="ใช่" value="Y"></v-checkbox>
            </v-col>
            <v-col cols="12" class="px-5">
              <span>ต้องการแสดงข้อมูลให้ผู้ซื้อเห็นหรือไม่</span>
              <v-checkbox :readonly="true" class="float-right pl-3" dense v-model="data.show_buyer" label="ไม่" value="N"></v-checkbox>
              <v-checkbox :readonly="true" class="float-right pl-3" dense v-model="data.show_buyer" label="ใช่" value="Y"></v-checkbox>
            </v-col>
            <v-col v-for="(item, index) in data.tier_document_list" :key="index" cols="12" class="mt-3 px-5">
              <v-card height="100" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-3 py-2">
                <v-row class="mx-0">
                  <v-col class="px-0">
                    <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="84px" height="84px"></v-img>
                    <v-card-title class="float-left pl-3 doc-detail">{{ item.name_document }}</v-card-title>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-container> -->
      </v-card>
    </v-dialog>
    <ModalSettingTier ref="ModalSettingTier" />
  </div>
</template>

<script>
// import { Decode } from '@/services'
import eventBus from '@/components/eventBus'
export default {
  components: {
    ModalSettingTier: () => import('@/components/Shop/Tier/SettingTierModal')
  },
  data () {
    return {
      data: '',
      lazy: false,
      openModalTierDetail: false,
      items: ''
    }
  },
  watch: {
  },
  mounted () {
  },
  created () {
    this.$EventBus.$on('UpdatesettingTierSuccess', this.cancelAndEmitData)
    this.$EventBus.$on('cancelProcessGetNewData', this.getDetailSettingTier)
  },
  beforeDestroy () {
    this.$EventBus.$off('UpdatesettingTierSuccess')
    this.$EventBus.$off('cancelProcessGetNewData')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    async succesStatus () {
      await eventBus.$emit('setStatus')
      localStorage.setItem('statusTransaction', 'approve')
    },
    open (item) {
      this.openModalTierDetail = true
      this.items = item
      this.getDetailSettingTier()
    },
    async getDetailSettingTier () {
      this.$store.commit('openLoader')
      // console.log(this.items)
      await this.$store.dispatch('actionsDetailTier', this.items)
      const res = await this.$store.state.ModuleSettingTier.stateDetailTier
      if (res.message === 'List tier and extra document success') {
        this.$store.commit('closeLoader')
        this.data = res.data[0]
      } else if (res.message === 'This User is not have business_id') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ผู้ใช้งานนี้ไม่มีข้อมูลนิติบุคคล'
        })
      } else if (res.message === 'Not Found Seller Shop') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ไม่พบข้อมูลร้านค้า'
        })
      } else if (res.message === 'Not found data.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: 'ไม่พบข้อมูล'
        })
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 7000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ดำเนินการไม่สำเร็จ',
            text: `${res.message}`
          })
        }
      }
    },
    editTier (val) {
      this.cancel()
      this.$refs.ModalSettingTier.open('edit', val)
    },
    cancel () {
      this.openModalTierDetail = false
    },
    cancelAndEmitData () {
      this.$EventBus.$emit('editSettingTierSuccess')
      this.openModalTierDetail = false
    }
  }
}
</script>
<style lang="css" scoped>
.fontStyleData {
  font-size: 16px;
  color: #333333;
  font-weight: 700;
}
.fontStyleTitle {
  font-size: 16px;
  color: #333333;
  font-weight: 400;
}
.discount {
  background: #F3F5F7;
  border-radius: 4px;
  padding: 5px;
}
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>
<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
.title-mobil {
  font-size: 16px;
}
.v-messages {
  min-height: 0px;
}
</style>
