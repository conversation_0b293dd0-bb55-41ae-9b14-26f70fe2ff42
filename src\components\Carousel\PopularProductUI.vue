<template>
  <v-container>
    <v-col cols="12" md="12" sm="12" v-if="this.chcekAllResultEmpty === false">
      <v-row v-if="!IpadSize && !MobileSize && !IpadProSize" style="margin-left:60px; margin-right:60px">
        <h1 style="font-size:24px; font-weight: 700; color:#27AB9C;">หมวดหมู่สินค้ายอดนิยม</h1>
        <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px;"></v-spacer>
      </v-row>
      <v-row v-if="IpadProSize">
        <h1 style="font-size:24px; font-weight: 700; color:#27AB9C;">หมวดหมู่สินค้ายอดนิยม</h1>
        <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px;"></v-spacer>
      </v-row>
      <v-row v-if="MobileSize">
        <h1 class="ml-2" style="font-size:18px; font-weight: 700; color:#27AB9C;">หมวดหมู่สินค้ายอดนิยม</h1>
        <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px; margin-right:10px;"></v-spacer>
      </v-row>
      <v-row v-if="IpadSize">
        <h1 class="ml-2" style="font-size:24px; font-weight: 700; color:#27AB9C;">หมวดหมู่สินค้ายอดนิยม</h1>
        <v-spacer style="border-top: 3px solid #DAF1E9; margin-top: 14px; margin-left: 10px; margin-right:10px;"></v-spacer>
      </v-row>
    </v-col>
    <v-col  cols="12" md="12" sm="12" style="margin-top: -100px" v-else></v-col>
    <div v-for="(item, index) in listPopular" :key="index">
      <div v-if="!IpadSize && !MobileSize" class="mb-4">
        <div class="d-flex justify-center" >
          <v-container v-if="item.result.length !== 0" style="position: absolute; z-index:3; height:348px;" :style="!IpadProSize ? 'width:1223px;' : 'width:100%;'">
            <v-row align="center" no-gutters class="" style="margin-top:10px; margin-left: -65px;" >
              <v-col cols="2" v-if="item.result.length !== 0">
                <p class="d-flex justify-end" style="font-size:18px; font-weight:500; color:#FFFFFF">หมวดหมู่</p>
                <p class="float-end text-end" style="font-weight:700; color:white; width:150px" :style="IpadProSize ? 'font-size: 18px;' : 'font-size:20px;'">{{item.category}}</p>
                <v-row no-gutters class="d-flex justify-end float-end" style="cursor: pointer;">
                  <!-- <p style="font-size:12px; font-weight:400; color:#FFFFFF">ดูทั้งหมด</p>
                  <v-icon color="#FFFFFF" x-small class="mb-4 ml-2">mdi-arrow-right</v-icon> -->
                  <v-btn x-small style="font-size:12px; font-weight:400; color:#FFFFFF;" text @click.prevent="GetPopularProductDetail(item.categoryID, item.category)" :href="item.pathCateLink">ดูทั้งหมด <v-icon color="#FFFFFF" x-small class="pl-1">mdi-arrow-right</v-icon></v-btn>
                </v-row>
              </v-col>
              <v-col cols="10">
                <v-row no-gutters v-if="item.result.length !== 0 && !IpadProSize">
                  <div v-for="(item2, index) in item.result" :key="index">
                    <v-row no-gutters v-if="item2.product.name !== undefined">
                      <v-col no-gutters style="" class="" cols="12" >
                        <v-row no-gutters >
                          <div class="pl-5 ">
                            <v-card outlined class="rounded-lg custom-card" height="330" width="188" @click.prevent="DetailProduct(item2.product, $event)" onclick="return false;" :href="item2.product.pathLink">
                              <v-row dense>
                                <v-col cols="6" md="6" sm="6" xs="6">
                                  <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: 8px; position:absolute; z-index:1;" v-if="item2.product.message_status === 'sale'"></v-img>
                                  <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-top: -3px; margin-left:3px; position:absolute; z-index:1;" v-else-if="item2.product.message_status === 'new'"></v-img>
                                  <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -5px; position:absolute; z-index:1;" v-else-if="item2.product.message_status === 'hot'"></v-img>
                                  <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -10px; margin-top: -13px; position:absolute; z-index:1;" v-else-if="item2.product.message_status === 'cool'"></v-img>
                                  <v-img src="@/assets/Tag/Recommend2.png" height="65" width="119" contain style="margin-left: -16px; margin-top: -10px; position:absolute; z-index:1;" v-if="item2.product.message_status === 'recommend'"></v-img>
                                  <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -10px; position:absolute; z-index:1;" v-else-if="item2.product.message_status === 'pre-order'"></v-img>
                                  <v-img src="@/assets/Tag/BestSeller.svg" height="40" width="60" contain style="margin-left: -12px; margin-top: 4px; position:absolute; z-index:1;" v-else-if="item2.product.message_status === 'best-seller'"></v-img>
                                </v-col>
                              </v-row>
                              <v-container v-if="checkArrayHaveValueImage(item2.product.images_URL)" >
                                <div class="image-container">
                                  <v-img
                                    loading='lazy'
                                    v-lazyload
                                    :src="checkArrayHaveValueImage(item2.product.images_URL) ? item2.product.images_URL[0] : '@/assets/NoImage.png'"
                                    height="115px"
                                    width="100%"
                                    contain
                                    style="border-radius: 8px;"
                                    alt="PopularProductImage"
                                    class="base-image">
                                  </v-img>
                                  <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" height="100%" width="100%" />
                                  <img v-if="item2.product.etax === 'N' && item2.product.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" height="100%" width="100%" />
                                  <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" height="100%" width="100%" />
                                </div>
                              </v-container>
                              <v-container v-else height="115" width="172">
                                <v-img
                                  v-lazyload
                                  src="@/assets/NoImage.png"
                                  height="115"
                                  width="100%"
                                  style="border-radius: 8px;"
                                  loading='lazy'
                                  class="">
                                </v-img>
                              </v-container>
                              <v-col cols="12" class="pa-0">
                                <v-row no-gutters class="ml-2 mr-1 d-flex justify-space-between" >
                                  <v-col cols="10" class="pa-0">
                                    <v-tooltip bottom>
                                      <template v-slot:activator="{ on, attrs }">
                                        <p v-bind="attrs" v-on="on" class="pb-2" style="max-width: 140px; font-size: 14px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;" :class="MobileSize? 'fontMobile' : IpadSize? 'fontIpad' : 'font'">{{ item2.product.name }}</p>
                                        <!-- <p class="text-truncate" style="border: 0px solid red; max-width:140px; font-weight:700; font-size: 16px;" >{{item2.product.name}}</p> -->
                                      </template>
                                      <span>{{ item2.product.name }}</span>
                                    </v-tooltip>
                                  </v-col>
                                  <v-col cols="2" class="pa-0">
                                    <v-btn icon small @click.prevent="CheckaddFavorites(item2.product, $event)" @click.stop="DetailProduct('no', $event)" class="mb-2"  v-if="roleUser.role !== 'purchaser' && item2.product.isFavorite !== undefined">
                                      <v-icon size="15" class="mb-1" v-if="item2.product.isFavorite === false || item2.product.isFavorite === 'false'" color="#D1392B" >mdi-heart-outline</v-icon>
                                      <v-icon size="15" class="mb-1" v-else color="#D1392B" >mdi-heart</v-icon>
                                    </v-btn>
                                  </v-col>
                                </v-row>
                              </v-col>
                              <div v-if="item2.product.short_description !== null && item2.product.short_description !== ''" style="margin-top:-10px; margin-left:8px; ">
                                <p class="text-truncate mb-1" style="max-width:170px; border: 0px solid red; font-size: 12px; font-weight: 400; color: #9A9A9A;" >{{item2.product.short_description}}</p>
                              </div>
                              <div v-else style="margin-left:8px; margin-top:-10px;">
                                <p class="text-truncate mb-1" style="max-width:170px; border: 0px solid red; font-size: 12px; font-weight: 400; color: transparent;" >-</p>
                              </div>
                              <v-col cols="12" class="pa-0 pl-2" :class="MobileSize || IpadSize ? 'pb-2' : 'pb-3'">
                                <v-chip x-small v-if="item2.product.fda_number !== null && item2.product.fda_number !== ''" color="#F3F5F7" class="square-chip" text-color="#636363"><v-img style="border-radius: 999px;" max-height="14px" max-width="14px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">เครื่องหมาย อย.</span></v-chip>
                                <p v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; max-width: 132px; color:transparent">-</p>
                              </v-col>
                              <v-card-text class="ml-2 pt-0 pb-0 px-0 mt-n3" >
                                <v-row dense>
                                  <v-rating
                                    v-model="item2.product.stars"
                                    color="#FB9300"
                                    background-color="#C4C4C4"
                                    empty-icon="$ratingFull"
                                    half-increments
                                    hover
                                    small
                                    dense
                                    readonly
                                  ></v-rating>
                                  <v-spacer></v-spacer>
                                  <p class="mr-5 mt-1" style="font-size: 10px; font-weight: 400; margin-top: 1px;">ขายแล้ว {{item2.product.sold | formatNumber}} ชิ้น</p>
                                </v-row>
                              </v-card-text>
                              <v-card-text class="pt-0 px-0 ml-2" v-if="item2.product.discount_percent !== '0%'">
                                <span style="font-size: 24px; font-weight: 700; color: #F5222D;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <span style="font-size: 24px; font-weight: 700; color: #F5222D;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <v-spacer></v-spacer>
                                <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >฿ </span>
                                <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <v-chip class="ml-2" color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small >ส่วนลด -{{item2.product.discount_percent}}</v-chip>
                              </v-card-text>
                              <v-card-text class="pt-5 px-0 ml-2" v-else>
                                <span style="font-size: 24px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <span style="font-size: 24px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                              </v-card-text>
                              <!-- <span style="font-size: 24px; font-weight: 700; color: #F5222D;" v-if="item2.product.discount_percent !== '0%'">฿{{ Number(item2.product.real_price).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                              <div class="pt-3" v-else>
                                <span style="font-size: 24px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <span style="font-size: 24px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                              </div>
                              <v-spacer></v-spacer>
                              <span  style="font-size: 12px; font-weight: 600; color: #636363;" v-if="item2.product.discount_percent !== '0%'">฿ </span>
                              <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #636363;" v-if="item2.product.discount_percent !== '0%'">{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                              <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="item2.product.discount_percent !== '0%'">ส่วนลด -{{item2.product.discount_percent}}</v-chip> -->
                            </v-card>
                          </div>
                        </v-row>
                      </v-col>
                    </v-row>
                  </div>
                </v-row>
                <v-row no-gutters v-if="item.result.length !== 0 && IpadProSize">
                  <div v-for="(item2, index) in item.result" :key="index">
                    <v-row no-gutters v-if="item2.product.name !== undefined && index < 4">
                        <v-col no-gutters style="" class="" cols="12" >
                          <v-row no-gutters >
                            <div class="pl-5 ">
                              <v-card outlined class="rounded-lg custom-card" height="330" width="188" @click.prevent="DetailProduct(item2.product, $event)" onclick="return false;"  :href="item2.product.pathLink">
                              <v-row dense>
                                  <v-col cols="6" md="6" sm="6" xs="6">
                                    <v-img src="@/assets/Tag/Recommend2.png" height="65" width="119" contain style="margin-left: -16px; margin-top: -10px; position:absolute; z-index:1;" ></v-img>
                                  </v-col>
                              </v-row>
                              <v-container v-if="checkArrayHaveValueImage(item2.product.images_URL)">
                                <div class="image-container">
                                  <v-img
                                    loading='lazy'
                                    v-lazyload
                                    :src="checkArrayHaveValueImage(item2.product.images_URL) ? item2.product.images_URL[0] : '@/assets/NoImage.png'"
                                    height="115"
                                    width="100%"
                                    style="border-radius: 8px;"
                                    contain
                                    alt="PopularProductImage"
                                    class="base-image">
                                  </v-img>
                                  <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" height="100%" width="100%" />
                                  <img v-if="item2.product.etax === 'N' && item2.product.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" height="100%" width="100%" />
                                  <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" height="100%" width="100%" />
                                </div>
                              </v-container>
                              <v-container v-else height="115" width="172">
                                <v-img
                                  v-lazyload
                                  src="@/assets/NoImage.png"
                                  height="115"
                                  width="100%"
                                  style="border-radius: 8px;"
                                  loading='lazy'
                                  class="">
                                </v-img>
                              </v-container>
                              <v-col cols="12" class="pa-0">
                                <v-row no-gutters class="ml-2 mr-1 d-flex justify-space-between" >
                                  <v-col cols="10" class="pa-0">
                                    <v-tooltip bottom>
                                      <template v-slot:activator="{ on, attrs }">
                                        <p v-bind="attrs" v-on="on" class="pb-2" style="max-width: 140px; font-size: 14px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;" :class="MobileSize? 'fontMobile' : IpadSize? 'fontIpad' : 'font'">{{ item2.product.name }}</p>
                                        <!-- <p class="text-truncate" style="border: 0px solid red; max-width:140px; font-weight:700; font-size: 16px;" >{{item2.product.name}}</p> -->
                                      </template>
                                      <span>{{ item2.product.name }}</span>
                                    </v-tooltip>
                                  </v-col>
                                  <v-col cols="2" class="pa-0">
                                    <v-btn icon small @click.prevent="CheckaddFavorites(item2.product, $event)" @click.stop="DetailProduct('no', $event)" class="mb-2"  v-if="roleUser.role !== 'purchaser' && item2.product.isFavorite !== undefined">
                                      <v-icon size="15" class="mb-1" v-if="item2.product.isFavorite === false || item2.product.isFavorite === 'false'" color="#D1392B" >mdi-heart-outline</v-icon>
                                      <v-icon size="15" class="mb-1" v-else color="#D1392B" >mdi-heart</v-icon>
                                    </v-btn>
                                  </v-col>
                                </v-row>
                              </v-col>
                              <div v-if="item2.product.short_description !== null && item2.product.short_description !== ''" style="margin-top:-10px; margin-left:8px; ">
                                <p class="text-truncate mb-1" style="max-width:170px; border: 0px solid red; font-size: 12px; font-weight: 400; color: #9A9A9A;" >{{item2.product.short_description}}</p>
                              </div>
                              <div v-else style="margin-top:-10px; margin-left:8px; ">
                                <p class="text-truncate mb-1" style="max-width:170px; border: 0px solid red; font-size: 12px; font-weight: 400; color: transparent;" >-</p>
                              </div>
                              <v-col cols="12" class="pa-0 pb-4 pl-2">
                                <v-chip x-small v-if="item2.product.fda_number !== null && item2.product.fda_number !== ''" color="#F3F5F7" class="square-chip" text-color="#636363"><v-img style="border-radius: 999px;" max-height="14px" max-width="14px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">เครื่องหมาย อย.</span></v-chip>
                                <p v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; max-width: 132px; color:transparent">-</p>
                              </v-col>
                              <v-card-text class="ml-2 pt-0 pb-0 px-0 mt-n3" >
                                <v-row dense>
                                  <v-rating
                                    v-model="item2.product.stars"
                                    color="#FB9300"
                                    background-color="#C4C4C4"
                                    empty-icon="$ratingFull"
                                    half-increments
                                    hover
                                    small
                                    dense
                                    readonly
                                  ></v-rating>
                                  <v-spacer></v-spacer>
                                  <p class="mr-5 mt-1" style="font-size: 10px; font-weight: 400; margin-top: 1px;">ขายแล้ว {{item2.product.sold | formatNumber}} ชิ้น</p>
                                </v-row>
                              </v-card-text>
                              <!-- <v-card-text class="pt-0 px-0 ml-2">
                                <span style="font-size: 24px; font-weight: 700; color: #F5222D;" v-if="item2.product.discount_percent !== '0%'">฿{{ Number(item2.product.real_price).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <div class="pt-3" v-else>
                                  <span style="font-size: 24px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                  <span style="font-size: 24px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                </div>
                                <v-spacer></v-spacer>
                                <span  style="font-size: 12px; font-weight: 600; color: #636363;" v-if="item2.product.discount_percent !== '0%'">฿ </span>
                                <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #636363;" v-if="item2.product.discount_percent !== '0%'">{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="item2.product.discount_percent !== '0%'">ส่วนลด -{{item2.product.discount_percent}}</v-chip>
                              </v-card-text> -->
                              <v-card-text class="pt-0 px-0 ml-2" v-if="item2.product.discount_percent !== '0%'">
                                <span style="font-size: 18px; font-weight: 700; color: #F5222D;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <span style="font-size: 18px; font-weight: 700; color: #F5222D;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <v-spacer></v-spacer>
                                <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >฿ </span>
                                <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <v-chip class="ml-2" x-small color="#FEE7E8" text-color="#F5222D" style="font-size: 8px; font-weight: 400;">ส่วนลด -{{item2.product.discount_percent}}</v-chip>
                              </v-card-text>
                              <v-card-text class="pt-5 px-0 ml-2" v-else>
                                <span style="font-size: 18px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                                <span style="font-size: 18px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                              </v-card-text>
                              </v-card>
                            </div>
                          </v-row>
                        </v-col>
                    </v-row>
                  </div>
                </v-row>
              </v-col>
            </v-row>
          </v-container>
          <div v-if="item.result.length !== 0">
            <v-img class="rounded-lg" style="position: relative; z-index:1" :height="IpadProSize? '370px' : '375px'" :max-width="IpadProSize ? '98vw' : '1240'" :src="item.subBanner"></v-img>
          </div>
        </div>
      </div>
      <div v-if="MobileSize">
        <div v-if="item.result.length !== 0">
          <v-img height="110" width="100%" :src="item.subBanner" class="d-flex align-center rounded-lg mb-3 mt-3">
            <v-row no-gutters class="mx-3 d-flex" align="center">
              <p class="mr-auto pt-4" style="font-size: 18px; font-weight:700; color:#FFFFFF;">{{item.category}}</p>
              <v-btn x-small class="ml-auto" style="font-size: 14px; font-weight: 400; color:#FFFFFF;" text @click.prevent="GetPopularProductDetail(item.categoryID, item.category)" :href="item.pathCateLink">ดูทั้งหมด <v-icon color="#FFFFFF" x-small class="pl-1">mdi-arrow-right</v-icon></v-btn>
              <!-- <p class="d-flex align-center" style="font-size:14px; font-weight:400; color:#FFF; cursor: pointer;" @click.prevent="GetPopularProductDetail(item.categoryID, item.category)" :href="item.pathCateLink">ดูทั้งหมด <v-icon color="#FFF" x-small class="ml-2">mdi-arrow-right</v-icon></p> -->
            </v-row>
          </v-img>
          <v-row dense>
            <v-col cols="6" v-for="(item2, index) in item.result" :key="index" class="px-1">
              <div v-if="Object.keys(item2.product).length !== 0">
                <v-hover v-slot="{ hover }">
                    <v-card outlined class="rounded-lg custom-card" height="100%" width="188" elevation="0" :class="{ 'on-hover': hover }" style="cursor: pointer;" @click.prevent="DetailProduct(item2.product, $event)" onclick="return false;" :href="item2.product.pathLink">
                      <v-row dense>
                        <v-col cols="6" md="6" sm="6" xs="6">
                          <v-img src="@/assets/Tag/Recommend2.png" height="65" width="119" contain style="margin-left: -10px; margin-top: -10px; position:absolute; z-index:1;"></v-img>
                        </v-col>
                      </v-row>
                      <v-container v-if="checkArrayHaveValueImage(item2.product.images_URL)" >
                        <div class="image-container">
                          <v-img
                            loading='lazy'
                            v-lazyload
                            :src="checkArrayHaveValueImage(item2.product.images_URL) ? item2.product.images_URL[0] : '@/assets/NoImage.png'"
                            height="115"
                            width="100%"
                            style="border-radius: 8px;"
                            contain
                            alt="PopularProductImage"
                            class="base-image">
                          </v-img>
                          <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" height="100%" width="100%" />
                          <img v-if="item2.product.etax === 'N' && item2.product.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" height="100%" width="100%" />
                          <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" height="100%" width="100%" />
                        </div>
                      </v-container>
                      <v-container v-else height="115" width="172">
                        <v-img
                          v-lazyload
                          src="@/assets/NoImage.png"
                          height="115"
                          width="100%"
                          style="border-radius: 8px;"
                          loading='lazy'
                          class="">
                        </v-img>
                      </v-container>
                      <v-col cols="12" class="pa-0">
                        <v-row no-gutters class="mr-1 ml-2 d-flex justify-space-between" >
                          <v-col cols="10" class="pa-0">
                            <v-tooltip bottom>
                              <template v-slot:activator="{ on, attrs }">
                                <p v-bind="attrs" v-on="on" class="pb-2" style="max-width: 140px; font-size: 12px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;"
                                :class="MobileSize? 'fontMobile' : IpadSize? 'fontIpad' : 'font'">{{item2.product.name}}</p>
                              </template>
                              <span>{{ item2.product.name }}</span>
                            </v-tooltip>
                          </v-col>
                          <v-col cols="2" class="pa-0">
                            <v-btn icon small @click.prevent="CheckaddFavorites(item2.product, $event)" @click.stop="DetailProduct('no', $event)" class="mb-2"  v-if="roleUser.role !== 'purchaser' && item2.product.isFavorite !== undefined">
                              <v-icon size="15" class="mb-1" v-if="item2.product.isFavorite === false || item2.product.isFavorite === 'false'" color="#D1392B" >mdi-heart-outline</v-icon>
                              <v-icon size="15" class="mb-1" v-else color="#D1392B" >mdi-heart</v-icon>
                            </v-btn>
                          </v-col>
                        </v-row>
                      </v-col>
                      <div v-if="item2.product.short_description !== null" style="margin-top:-10px; margin-left: 8px; margin-right: 8px;">
                        <p class="text-truncate mb-1" style="max-width: 100vw; border: 0px solid red; font-size: 12px; font-weight: 400; color: #9A9A9A;" >{{item2.product.short_description}}</p>
                      </div>
                      <div v-else style="margin-top:-10px; margin-left:8px; ">
                        <p class="text-truncate mb-1" style="max-width: 100vw; border: 0px solid red; font-size: 12px; font-weight: 400; color: #9A9A9A; padding-top:16px" ></p>
                      </div>
                      <v-col cols="12" class="pa-0 pb-1 pl-2">
                        <v-chip x-small v-if="item2.product.fda_number !== null && item2.product.fda_number !== ''" color="#F3F5F7" class="square-chip" text-color="#636363"><v-img style="border-radius: 999px;" max-height="14px" max-width="14px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">เครื่องหมาย อย.</span></v-chip>
                        <p v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; max-width: 132px; color:transparent">-</p>
                      </v-col>
                      <v-card-text class="pt-0 pb-0 px-0" >
                        <v-row dense>
                          <v-rating
                            v-model="item2.product.stars"
                            color="#FB9300"
                            background-color="#C4C4C4"
                            empty-icon="$ratingFull"
                            half-increments
                            hover
                            x-small
                            dense
                            readonly
                            class="pl-2"
                          ></v-rating>
                          <v-spacer></v-spacer>
                          <p class="pr-2" style="font-size: 10px; font-weight: 400; margin-top: 1px;">ขายแล้ว {{item2.product.sold | formatNumber}} ชิ้น</p>
                        </v-row>
                      </v-card-text>
                      <v-card-text class="pt-0 px-0 ml-2" v-if="item2.product.discount_percent !== '0%'">
                        <span style="font-size: 24px; font-weight: 700; color: #F5222D;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        <span style="font-size: 24px; font-weight: 700; color: #F5222D;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        <v-spacer></v-spacer>
                        <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >฿ </span>
                        <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small >ส่วนลด -{{item2.product.discount_percent}}</v-chip>
                      </v-card-text>
                      <v-card-text class=" px-0 ml-2" v-else>
                        <span style="font-size: 20px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        <span style="font-size: 20px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                      </v-card-text>
                      <!-- <v-card-text class="pt-0 px-0 ml-2">
                        <span style="font-size: 18px; font-weight: 700; color: #F5222D;" v-if="item2.product.discount_percent !== '0%'">฿{{ Number(item2.product.real_price).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        <div class="pt-3" v-else>
                          <span style="font-size: 18px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                          <span style="font-size: 18px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        </div>
                        <v-spacer></v-spacer>
                        <span  style="font-size: 12px; font-weight: 600; color: #636363;" v-if="item2.product.discount_percent !== '0%'">฿ </span>
                        <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #636363;" v-if="item2.product.discount_percent !== '0%'">{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                        <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="item2.product.discount_percent !== '0%'">ส่วนลด -{{item2.product.discount_percent}}</v-chip>
                      </v-card-text> -->
                    </v-card>
                </v-hover>
              </div>
            </v-col>
          </v-row>
        </div>
      </div>
      <div v-if="IpadSize">
        <div v-if="item.result.length !== 0">
          <v-img height="110" width="100%" :src="item.subBanner" class="d-flex align-center rounded-lg mb-3 mt-3">
            <v-row no-gutters class="mx-3 d-flex" align="center">
              <p class="mr-auto pt-4" style="font-size: 24px; font-weight:700; color:#FFFFFF;">{{item.category}}</p>
              <v-btn x-small class="ml-auto" style="font-size: 14px; font-weight: 400; color:#FFFFFF;" text @click.prevent="GetPopularProductDetail(item.categoryID, item.category)" :href="item.pathCateLink">ดูทั้งหมด <v-icon color="#FFFFFF" x-small class="pl-1">mdi-arrow-right</v-icon></v-btn>
              <!-- <p class="d-flex align-center" style="font-size:14px; font-weight:400; color:#FFF; cursor: pointer;" @click.prevent="GetPopularProductDetail(item.categoryID, item.category)" :href="item.pathCateLink">ดูทั้งหมด <v-icon color="#FFF" x-small class="ml-2">mdi-arrow-right</v-icon></p> -->
            </v-row>
          </v-img>
          <v-row dense>
            <v-col cols="3" v-for="(item2, index) in item.result" :key="index">
              <div v-if="Object.keys(item2.product).length !== 0">
                <v-hover v-slot="{ hover }">
                  <v-card class="rounded-lg custom-card" height="100%" width="188" elevation="0" :class="{ 'on-hover': hover }" style="cursor: pointer;" @click.prevent="DetailProduct(item2.product, $event)" onclick="return false;" :href="item2.product.pathLink">
                    <v-row dense>
                      <v-col cols="6" md="6" sm="6" xs="6">
                        <v-img src="@/assets/Tag/Recommend2.png" height="65" width="119" contain style="margin-left: -10px; margin-top: -10px; position:absolute; z-index:1;"></v-img>
                      </v-col>
                    </v-row>
                    <!-- <v-img
                      v-lazyload
                      :src="item2.product.images_URL[0]"
                      height="156"
                      width="100%"
                      loading='lazy'
                      contain
                      class="align-start"
                    >
                    </v-img> -->
                    <v-container v-if="checkArrayHaveValueImage(item2.product.images_URL)" >
                      <div class="image-container">
                        <v-img
                          loading='lazy'
                          v-lazyload
                          :src="checkArrayHaveValueImage(item2.product.images_URL) ? item2.product.images_URL[0] : '@/assets/NoImage.png'"
                          height="115"
                          width="100%"
                          style="border-radius: 8px;"
                          contain
                          alt="PopularProductImage"
                          class="base-image">
                        </v-img>
                        <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'N'" :src="require('@/assets/icon_etax.png')" alt="eTaxImage" class="overlay-image" height="100%" width="100%" />
                        <img v-if="item2.product.etax === 'N' && item2.product.otop === 'Y'" :src="require('@/assets/OTOP.png')" alt="otopImage" class="overlay-image" height="100%" width="100%" />
                        <img v-if="item2.product.etax === 'Y' && item2.product.otop === 'Y'" :src="require('@/assets/E-TAX_OTOP.png')" alt="eTaxOtopImage" class="overlay-image" height="100%" width="100%" />
                      </div>
                    </v-container>
                    <v-container v-else height="115" width="172">
                      <v-img
                        v-lazyload
                        src="@/assets/NoImage.png"
                        height="115"
                        width="100%"
                        style="border-radius: 8px;"
                        loading='lazy'
                        class="">
                      </v-img>
                    </v-container>
                    <v-col cols="12" class="pa-0">
                      <v-row no-gutters class="ml-2 mr-1 d-flex justify-space-between" >
                        <v-col cols="10" class="pa-0">
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <p v-bind="attrs" v-on="on" class="pb-2" style="max-width: 140px; font-size: 14px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box; overflow: hidden; text-overflow: ellipsis;" :class="MobileSize? 'fontMobile' : IpadSize? 'fontIpad' : 'font'">{{ item2.product.name }}</p>
                              <!-- <p class="text-truncate" style="border: 0px solid red; max-width:100px; font-weight:700;" >{{item2.product.name}}</p> -->
                            </template>
                            <span>{{ item2.product.name }}</span>
                          </v-tooltip>
                        </v-col>
                        <v-col cols="2" class="pa-0">
                          <v-btn icon small @click.prevent="CheckaddFavorites(item2.product, $event)" @click.stop="DetailProduct('no', $event)" class="mb-2"  v-if="roleUser.role !== 'purchaser' && item2.product.isFavorite !== undefined">
                            <v-icon size="15" class="mb-1" v-if="item2.product.isFavorite === false || item2.product.isFavorite === 'false'" color="#D1392B" >mdi-heart-outline</v-icon>
                            <v-icon size="15" class="mb-1" v-else color="#D1392B" >mdi-heart</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                    </v-col>
                    <div v-if="item2.product.short_description !== null && item2.product.short_description !== ''" style="margin-top:-10px; margin-left:8px; ">
                      <p class="text-truncate mb-1" style="max-width:150px; border: 0px solid red; font-size: 12px; font-weight: 400; color: #9A9A9A;" >{{item2.product.short_description}}</p>
                    </div>
                    <div v-else style="margin-top:-10px; margin-left:8px; ">
                      <p class="text-truncate" style="max-width:170px; border: 0px solid red; font-size: 12px; font-weight: 400; color: transparent;">-</p>
                    </div>
                    <v-col cols="12" class="pa-0 pb-4 pl-2">
                      <v-chip x-small v-if="item2.product.fda_number !== null && item2.product.fda_number !== ''" color="#F3F5F7" class="square-chip" text-color="#636363"><v-img style="border-radius: 999px;" max-height="14px" max-width="14px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">เครื่องหมาย อย.</span></v-chip>
                      <p v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; max-width: 132px; color:transparent">-</p>
                    </v-col>
                    <v-card-text class="ml-2 pt-0 pb-0 px-0 mt-n3" >
                      <v-row dense>
                        <v-rating
                          v-model="item2.product.stars"
                          color="#FB9300"
                          background-color="#C4C4C4"
                          empty-icon="$ratingFull"
                          half-increments
                          hover
                          x-small
                          dense
                          readonly
                        ></v-rating>
                        <p class="mr-5 ml-auto" style="font-size: 10px; font-weight: 400; margin-top: 0px;">ขายแล้ว {{item2.product.sold | formatNumber}} ชิ้น</p>
                      </v-row>
                    </v-card-text>
                    <v-card-text class="pt-0 px-0 ml-2" v-if="item2.product.discount_percent !== '0%'">
                      <span style="font-size: 18px; font-weight: 700; color: #F5222D;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                      <span style="font-size: 18px; font-weight: 700; color: #F5222D;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                      <v-spacer></v-spacer>
                      <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;">฿ </span>
                      <span class="text-decoration-line-through" style="font-size: 12px; font-weight: 600; color: #929292;" >{{ Number(parseFloat(item2.product.fake_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                      <v-chip class="ml-2" color="#FEE7E8" text-color="#F5222D" style="font-size: 8px; font-weight: 400;" x-small >ส่วนลด -{{item2.product.discount_percent}}</v-chip>
                    </v-card-text>
                    <v-card-text class="pt-5 px-0 ml-2" v-else>
                      <span style="font-size: 18px; font-weight: 700; color: #636363;" v-if="item2.product.vat_default === 'yes'">฿{{ Number(parseFloat(item2.product.real_price) + parseFloat(item2.product.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                      <span style="font-size: 18px; font-weight: 700; color: #636363;" v-else>฿{{ Number(parseFloat(item2.product.real_price)).toLocaleString(undefined, {minimumFractionDigits: 2})}}</span>
                    </v-card-text>
                  </v-card>
                </v-hover>
              </div>
            </v-col>
          </v-row>
        </div>
      </div>
    </div>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import Vue from 'vue'
export default {
  data () {
    return {
      windowWidth: window.innerWidth,
      listPopular: [],
      listPopularCard: '',
      cardName: '',
      catName: '',
      catRank: '',
      listResult: '',
      roleUser: '',
      listDataPopular: [],
      fev: false,
      header: 'สินค้ายอดนิยม',
      backGroundImg: [
        require('@/assets/BannerSubA.webp'),
        require('@/assets/BannerSubB.webp'),
        require('@/assets/BannerSubC.webp')
      ],
      path: process.env.VUE_APP_DOMAIN,
      chcekAllResultEmpty: false
    }
  },
  mounted () {
    this.$nextTick(() => {
      window.addEventListener('resize', this.onResize)
    })
  },
  beforeDestroy () {
    window.removeEventListener('resize', this.onResize)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.$EventBus.$on('GetPopularProduct', this.GetPopularProduct)
    if (localStorage.getItem('roleUser') !== null) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    } else {
      this.roleUser = 'ext_buyer'
    }
    this.chcekAllResultEmpty = false
    this.GetPopularProduct()
    this.formatSold()
    // console.log('PopularProduct')
    // console.log('backGroundImg', this.backGroundImg)
  },
  methods: {
    checkArrayHaveValueImage (val) {
      if (val !== undefined) {
        if (val.length !== 0) {
          return true
        } else {
          return false
        }
      } else {
        return false
      }
    },
    onResize () {
      this.windowWidth = window.innerWidth
    },
    formatSold () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return ''
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'พัน'
        }
        return value.toString()
      })
    },
    DetailProduct (val, event) {
      if (event.type === 'click' || event.type === 'touchend') {
        event.preventDefault()
        if (val !== 'no') {
          // console.log(val)
          const nameCleaned = val.name.replace(/\s/g, '-')
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } }).catch(() => {})
          // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } })
          // window.location.assign(routeData.href, '_blank')
          // this.$router.push({ path: routeData.href })
        }
      }
    },
    async GetPopularProductDetail (val, val2) {
      // console.log('catID', val, val2)
      this.$router.push(`/ListProduct/popular_product?id=${val}&page=1`).catch(() => {})
    },
    async GetPopularProduct () {
      // console.log('pppp')
      var dataRole = ''
      if (localStorage.getItem('roleUser') !== null) {
        dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        dataRole = 'ext_buyer'
      }
      var companyId
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      } else {
        companyId = {
          company: {
            company_id: -1
          }
        }
      }
      var data = {
        role_user: dataRole,
        status: 'active',
        company_id: dataRole === 'ext_buyer' ? -1 : companyId.company.company_id
      }
      // console.log('dataGetPopularProduct', data)
      await this.$store.dispatch('actionsGetPopularProduct', data)
      var response = await this.$store.state.ModuleProductNode.stateGetPopularProduct
      if (response.ok === 'y') {
        for (var i = 0; i < response.query_result.length; i++) {
          for (var j = 0; j < response.query_result[i].result.length; j++) {
            response.query_result[i].pathCateLink = this.path + 'ListProduct/popular_product?id=' + response.query_result[i].categoryID + '&page=1'
            // console.log('response.query_result[i].result[j].product', response.query_result[i].result[j].product.name === undefined)
            if (response.query_result[i].result[j].product.name !== undefined) {
              response.query_result[i].result[j].product.pathLink = this.path + 'DetailProduct/' + encodeURIComponent(response.query_result[i].result[j].product.name.replace(/\s/g, '-') + '-' + response.query_result[i].result[j].product.id)
            }
          }
        }
        this.listPopular = await response.query_result
        this.chcekAllResultEmpty = this.listPopular.every(item => Array.isArray(item.result) && item.result.length === 0)
        this.listPopular[0].subBanner = this.backGroundImg[0] //
        this.listPopular[1].subBanner = this.backGroundImg[1] //
        this.listPopular[2].subBanner = this.backGroundImg[2] //
        this.listDataPopular = await this.listPopular
      }
    },
    CheckaddFavorites (val, event) {
      // console.log('val', val.isFavorite, val)\
      if (event.type === 'click' || event.type === 'touchend') {
        event.preventDefault()
        if (localStorage.getItem('oneData') !== null) {
          var ProductID
          if (val.id !== undefined && val.id !== '') {
            ProductID = val.id
          } else if (val.product_id !== undefined && val.product_id !== '') {
            ProductID = val.product_id
          }
          // console.log('ProductID------>', ProductID)
          this.addFavorites(ProductID)
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true,
            icon: 'warning',
            text: 'กรุณาเข้าสู่ระบบ เพื่อเพิ่มลงในสินค้าที่ถูกใจของคุณ'
          })
        }
      }
    },
    async addFavorites (val) {
      // console.log('val----addFavorites', val)
      if (localStorage.getItem('roleUser') !== null) {
        this.roleUser = JSON.parse(localStorage.getItem('roleUser')).role
      } else {
        this.roleUser = 'ext_buyer'
      }
      var companyId
      if (localStorage.getItem('SetRowCompany') !== null) {
        companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
      }
      var data
      if (this.roleUser === 'purchaser') {
        data = {
          role_user: this.roleUser,
          product_id: val,
          company_id: companyId.company.company_id,
          company_position_id: companyId.position.role_id,
          com_perm_id: companyId.position.com_perm_id
        }
        // console.log('purchaser', data)
      } else if (this.roleUser === 'ext_buyer') {
        data = {
          role_user: this.roleUser,
          product_id: val,
          company_id: -1,
          company_position_id: -1,
          com_perm_id: -1
        }
        // console.log('ext_buyer', data)
      }
      await this.$store.dispatch('actionsUPSAddFavoriteProduct', data)
      var response = await this.$store.state.ModuleFavoriteProduct.stateAddFavoriteProduct
      // console.log('response favorite =======>', response)
      if (response.result === 'SUCCESS') {
        this.GetPopularProduct()
        this.$EventBus.$emit('getAllFavoriteProduct')
        this.$EventBus.$emit('getResultSearch')
        this.$EventBus.$emit('getNewProduct')
        this.$EventBus.$emit('getProductRecommentBrand')
        if (this.$router.currentRoute.name === 'DetailProduct') {
          this.$EventBus.$emit('getProductDetail')
        }
        this.$EventBus.$emit('ClickFavorites')
        this.$EventBus.$emit('getAllNewProduct')
        this.$EventBus.$emit('getAllBestSeller')
        this.$EventBus.$emit('getAllSameProductShop')
        this.$EventBus.$emit('getAllProductCategory')
        this.$EventBus.$emit('getBuyProductAgain')
        this.$EventBus.$emit('getAllProductSame')
        this.$EventBus.$emit('getHomepageItems')
        this.$EventBus.$emit('getSellerShopPage')
        this.$EventBus.$emit('getAllProductShop')
        this.$EventBus.$emit('getAllProductCategoryDetail')
        this.$EventBus.$emit('getRecommendedProducts')
        this.$EventBus.$emit('getAllNewProductInShop')
        this.$EventBus.$emit('getAllBestSellerInShop')
        this.$EventBus.$emit('getAllRecomenProductShopInShop')
        this.$EventBus.$emit('getAllProductShopInShop')
      }
    }
    // test (val) {
    //   console.log('val', val.id, val)
    //   // this.fev = !this.fev
    // }
  }
}
</script>

<style scoped>
.image-container {
  position: relative;
}

.base-image {
  width: 100%;
  height: 100%;
  z-index: 0;
}

.overlay-image {
  position: absolute;
  top: 0px; /* Adjust positioning as needed */
  left: 0px; /* Adjust positioning as needed */
  z-index: 1;
  width: 72%; /* Adjust overlay size as needed */
  height: 100%;
  object-fit: contain;
}
.square-chip {
  padding: 1px 0px 0px 1px;
  width: 53%; /* กำหนดความกว้าง */
  height: 14px; /* กำหนดความสูง */
  font-size: 10px; /* ขนาดตัวอักษร */
}
.fontMobile{
  line-height: 18px;
  height: 35px;
  max-height: 35px;
}
.fontIpad{
  line-height: 18px;
  height: 38.5px;
  max-height: 40px;
}
.font{
 line-height: 20px;
 height: 40px;
 max-height: 40px;
}
.custom-card {
  border: 1px solid #BDE7D9 !important; /* สีขอบของการ์ด */
  border-color: #BDE7D9 !important; /* สีขอบของการ์ดเมื่อไม่ได้โฮเวอร์ */
}
@media (max-width: 1366px) and (min-width: 1250px) {
  /* Media Query สำหรับ iPad Pro (1024px) */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 52%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
}
@media (max-width: 1180px) and (min-width: 1025px) {
  /* Media Query สำหรับ iPad air แนวนอน */
  .square-chip {
    /* padding: 0px !important; */
    width: 52%;
    /* height: 14px;
    font-size: 10px; */
  }
}
@media (max-width: 1250px) and (min-width: 1181px) {
  /* Media Query สำหรับ โน๊ตบุ๊คหน้าจอขนาดเล็ก */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 52%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
}
</style>
