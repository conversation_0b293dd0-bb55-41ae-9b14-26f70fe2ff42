<template>
  <div v-if="status === true">
    <!-- <Carousel/> -->
    <!-- <div v-if="StatusHomeBestProduct === true && StatusAPIBestProductSuccess === true">
      <HomeProducts v-if="StatusHomeBestProduct && bestSeller !== 'ยังไม่มีรายการสินค้าขายดี'" :propsData='bestSeller' typeProduct='best_seller_landing' header='สินค้าขายดี' :check='status'/>
    </div>
    <v-container v-if="StatusHomeBestProduct === false && StatusAPIBestProductSuccess === false">
      <v-row dense>
        <v-col cols="6" md="2" sm="4" v-for="i in 6" :key="i">
            <v-skeleton-loader
            class="mx-auto"
            max-width="175"
            type="image, article"
            >
            </v-skeleton-loader>
          </v-col>
        </v-row>
    </v-container> -->
    <!-- <div v-if="StatusHomeNewProduct === true && StatusAPINewProductSuccess === true">
      <HomeProducts v-if="StatusHomeNewProduct && newArrivals !== 'ยังไม่มีรายการสินค้ามาใหม่'" :propsData='newArrivals' typeProduct='new_product_landing' header='สินค้ามาใหม่' :check='status'/>
    </div>
    <v-container v-if="StatusHomeNewProduct === false && StatusAPINewProductSuccess === false">
      <v-row dense>
        <v-col cols="6" md="2" sm="4" v-for="i in 6" :key="i">
            <v-skeleton-loader
            class="mx-auto"
            max-width="175"
            type="image, article"
            >
            </v-skeleton-loader>
          </v-col>
        </v-row>
    </v-container> -->
    <div v-if="dataRole === 'ext_buyer'">
      <div v-if="StatusHomeRecoment === true && StatusAPIRecomentSuccess === true">
        <HomeProducts v-if="StatusHomeRecoment && recommend !== this.$t('Headers.HomeText.NoRecommendedProducts') " :propsData='recommend' typeProduct='recommended_product_landing' :header="this.$t('Headers.HomeText.RecommendedProducts')" :check='status'/>
      </div>
      <v-container v-if="StatusHomeRecoment === false && StatusAPIRecomentSuccess === false">
        <v-row dense>
          <v-col cols="6" md="2" sm="4" v-for="i in 6" :key="i">
              <v-skeleton-loader
              class="mx-auto"
              max-width="175"
              type="image, article"
              >
              </v-skeleton-loader>
            </v-col>
        </v-row>
      </v-container>
    </div>
    <div v-else>
      <div v-if="StatusHomeBestProduct === true && StatusAPIBestProductSuccess === true">
        <HomeProducts v-if="StatusHomeBestProduct && bestSeller !== this.$t('Headers.HomeText.NoBestSellers') " :propsData='bestSeller' typeProduct='best_seller_landing' :header="this.$t('Headers.HomeText.BestSeller')" :check='status'/>
      </div>
      <v-container v-if="StatusHomeBestProduct === false && StatusAPIBestProductSuccess === false">
        <v-row dense>
          <v-col cols="6" md="2" sm="4" v-for="i in 6" :key="i">
              <v-skeleton-loader
              class="mx-auto"
              max-width="175"
              type="image, article"
              >
              </v-skeleton-loader>
            </v-col>
          </v-row>
      </v-container>
      <div v-if="StatusHomeNewProduct === true && StatusAPINewProductSuccess === true">
        <HomeProducts v-if="StatusHomeNewProduct && newArrivals !== this.$t('Headers.HomeText.NoNewArrivals') " :propsData='newArrivals' typeProduct='new_product_landing' :header="this.$t('Headers.HomeText.NewProducts')" :check='status'/>
      </div>
      <v-container v-if="StatusHomeNewProduct === false && StatusAPINewProductSuccess === false">
        <v-row dense>
          <v-col cols="6" md="2" sm="4" v-for="i in 6" :key="i">
              <v-skeleton-loader
              class="mx-auto"
              max-width="175"
              type="image, article"
              >
              </v-skeleton-loader>
            </v-col>
          </v-row>
      </v-container>
      <div v-if="StatusHomeRecoment === true && StatusAPIRecomentSuccess === true">
        <HomeProducts v-if="StatusHomeRecoment && recommend !== this.$t('Headers.HomeText.NoRecommendedProducts') " :propsData='recommend' typeProduct='recommended_product_landing' :header="this.$t('Headers.HomeText.RecommendedProducts')" :check='status'/>
      </div>
      <v-container v-if="StatusHomeRecoment === false && StatusAPIRecomentSuccess === false">
        <v-row dense>
          <v-col cols="6" md="2" sm="4" v-for="i in 6" :key="i">
              <v-skeleton-loader
              class="mx-auto"
              max-width="175"
              type="image, article"
              >
              </v-skeleton-loader>
            </v-col>
        </v-row>
      </v-container>
    </div>
    <!-- <ThaiDotCom /> -->
    <!-- <BannerB loading="lazy" v-if="show"/> -->
    <!-- <NewArrivals :propsData='bestSeller'/> -->
  </div>
</template>

<script>
import { Decode } from '@/services'
const newArrivals = []
const bestSeller = []
const recommend = []
const Category = []
const CategoryList = []
CategoryList.push({ data: Category })
export default {
  components: {
    // Carousel: () => import('@/components/Carousel/CarouselHome'),
    HomeProducts: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Home/HomeProductUI')
    // BannerB: () => import(/* webpackPrefetch: true */ /* webpackChunkName: "home-chunk" */ '@/components/Carousel/CarouselBannerB')
    // ThaiDotCom: () => import(/* webpackChunkName: "thiaDotCom-chunk" */ '@/components/Carousel/ThaiDotCom')
    // HomeCategoryProduct: () => import('@/components/Home/HomeCategoryProduct')
    // NewArrivals: () => import('@/components/Home/HomeProduct/NewArrivals')
  },
  data () {
    return {
      GetAllProduct: {
        newArrivals,
        bestSeller,
        recommend,
        CategoryList
      },
      newArrivals: [],
      bestSeller: [],
      recommend: [],
      status: true,
      StatusHomeNewProduct: false,
      StatusHomeBestProduct: false,
      StatusHomeRecoment: false,
      StatusAPINewProductSuccess: false,
      StatusAPIBestProductSuccess: false,
      StatusAPIRecomentSuccess: false,
      tokenstatus: '',
      dataRole: '',
      show: false
    }
  },
  async created () {
    this.StatusHomeNewProduct = false
    this.StatusHomeBestProduct = false
    this.StatusHomeRecoment = false
    this.StatusAPINewProductSuccess = false
    this.StatusAPIBestProductSuccess = false
    this.StatusAPIRecomentSuccess = false
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    // this.checkRemoveDataGracz()
    window.scrollTo(0, 0)
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    this.$EventBus.$on('getHomepageItems', this.getRecommentProductExt)
    this.$EventBus.$on('getBestSeller', this.getBestSeller)
    if (localStorage.getItem('roleUser') !== null) {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.dataRole = 'ext_buyer'
    }
    // await this.getHomepageItems()
    if (this.dataRole === 'ext_buyer') {
      await this.getRecommentProductExt()
    } else {
      await this.getBestSeller()
    }
  },
  mounted () {
    window.addEventListener('scroll', (event) => {
      this.show = true
    })
    window.addEventListener('mouseup', (event) => {
      this.show = true
    })
  },
  methods: {
    async getNewProduct () {
      // console.log('getNewProduct')
      this.StatusAPINewProductSuccess = false
      var companyID = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        if (localStorage.getItem('oneData') !== null) {
          this.tokenstatus = this.oneData.user.access_token
        } else {
          this.tokenstatus = ''
        }
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var data
      data = {
        role_user: this.dataRole,
        company_id: companyID,
        status_product: 'new'
      }
      // console.log('dat', data)
      await this.$store.dispatch('actionsProductCardLandingPage', data)
      var response = await this.$store.state.ModuleHompage.stateGetProductCardLandingPage
      if (response.ok === 'y') {
        this.newArrivals = []
        if (response.query_result.length !== 0) {
          this.newArrivals = await [...response.query_result]
          this.status = true
          this.StatusHomeNewProduct = true
          this.StatusAPINewProductSuccess = true
        } else {
          this.newArrivals = 'ยังไม่มีรายการสินค้ามาใหม่'
          this.status = true
          this.StatusHomeNewProduct = true
          this.StatusAPINewProductSuccess = true
        }
        await this.getRecommentProduct()
      } else {
        this.newArrivals = []
        this.status = true
        this.StatusHomeNewProduct = false
        this.StatusAPINewProductSuccess = false
        await this.getRecommentProduct()
      }
    },
    async getRecommentProduct () {
      this.StatusAPIRecomentSuccess = false
      var companyID = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        if (localStorage.getItem('oneData') !== null) {
          this.tokenstatus = this.oneData.user.access_token
        } else {
          this.tokenstatus = ''
        }
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var data3
      // data3 = {
      //   token: this.tokenstatus,
      //   role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
      //   begin_search_type: 'component',
      //   begin_search_details: {
      //     custom_user_ID: '1',
      //     what_component: 'recommended_product',
      //     component_id: ''
      //   },
      //   user_detail: {
      //     company_id: companyID
      //   }
      // }
      data3 = {
        role_user: this.dataRole,
        company_id: companyID,
        status_product: 'recommend'
      }
      await this.$store.dispatch('actionsProductCardLandingPage', data3)
      var response3 = await this.$store.state.ModuleHompage.stateGetProductCardLandingPage
      if (response3.ok === 'y') {
        this.recommend = []
        if (response3.query_result.length !== 0) {
          this.recommend = await [...response3.query_result]
          this.status = true
          this.StatusHomeRecoment = true
          this.StatusAPIRecomentSuccess = true
        } else {
          this.recommend = 'ยังไม่มีรายการสินค้าแนะนำ'
          this.status = true
          this.StatusHomeRecoment = true
          this.StatusAPIRecomentSuccess = true
        }
        this.$store.commit('closeLoader')
      } else {
        this.recommend = []
        this.status = true
        this.StatusHomeRecoment = false
        this.StatusAPIRecomentSuccess = false
        this.$store.commit('closeLoader')
      }
    },
    async getBestSeller () {
      // alert('เข้า')
      this.StatusAPIBestProductSuccess = false
      this.$store.commit('openLoader')
      var companyID = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        if (localStorage.getItem('oneData') !== null) {
          this.tokenstatus = this.oneData.user.access_token
        } else {
          this.tokenstatus = ''
        }
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      // var data2
      // data2 = {
      //   token: this.tokenstatus,
      //   role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
      //   begin_search_type: 'component',
      //   begin_search_details: {
      //     custom_user_ID: '1',
      //     what_component: 'best_seller',
      //     component_id: ''
      //   },
      //   user_detail: {
      //     company_id: companyID
      //   }
      // }
      // data2 = {
      //   role_user: this.dataRole,
      //   company_id: companyID,
      //   limit: 18,
      //   page: 1,
      //   category: '',
      //   seller_shop_id: -1,
      //   orderBy: '',
      //   status_product: 'best-seller'
      // }
      var data3 = {
        role_user: this.dataRole,
        company_id: companyID,
        status_product: 'best-seller'
      }
      await this.$store.dispatch('actionsProductCardLandingPage', data3)
      var response2 = await this.$store.state.ModuleHompage.stateGetProductCardLandingPage
      // await this.$store.dispatch('actionsSelectCategoryShopList', data2)
      // var response2 = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      // console.log('data', response2)
      if (response2.ok === 'y') {
        this.bestSeller = []
        if (response2.query_result.length !== 0) {
          this.bestSeller = await [...response2.query_result]
          this.status = true
          this.StatusAPIBestProductSuccess = true
          this.StatusHomeBestProduct = true
        } else {
          this.bestSeller = 'ยังไม่มีรายการสินค้าขายดี'
          this.status = true
          this.StatusAPIBestProductSuccess = true
          this.StatusHomeBestProduct = true
        }
        await this.getNewProduct()
      } else {
        this.bestSeller = []
        this.status = true
        this.StatusHomeBestProduct = false
        this.StatusAPIBestProductSuccess = false
        await this.getNewProduct()
      }
    },
    async getRecommentProductExt () {
      this.StatusAPIRecomentSuccess = false
      var companyID = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        if (localStorage.getItem('oneData') !== null) {
          this.tokenstatus = this.oneData.user.access_token
        } else {
          this.tokenstatus = ''
        }
      } else {
        this.dataRole = 'ext_buyer'
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var data3
      // data3 = {
      //   token: this.tokenstatus,
      //   role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
      //   begin_search_type: 'component',
      //   begin_search_details: {
      //     custom_user_ID: '1',
      //     what_component: 'recommended_product',
      //     component_id: ''
      //   },
      //   user_detail: {
      //     company_id: companyID
      //   }
      // }
      data3 = {
        role_user: this.dataRole,
        company_id: companyID,
        category: -1,
        seller_shop_id: -1,
        orderBy: -1,
        status_product: 'recommend',
        limit: 30,
        page: 1
      }
      await this.$store.dispatch('actionsFlashSaleProductSystem', data3)
      var response3 = await this.$store.state.ModuleHompage.stateFlashSaleProductSystem
      if (response3.ok === 'y') {
        this.recommend = []
        if (response3.query_result.length !== 0) {
          this.recommend = await [...response3.query_result]
          this.status = true
          this.StatusHomeRecoment = true
          this.StatusAPIRecomentSuccess = true
        } else {
          this.recommend = 'ยังไม่มีรายการสินค้าแนะนำ'
          this.status = true
          this.StatusHomeRecoment = true
          this.StatusAPIRecomentSuccess = true
        }
        this.$store.commit('closeLoader')
      } else {
        this.recommend = []
        this.status = true
        this.StatusHomeRecoment = false
        this.StatusAPIRecomentSuccess = false
        this.$store.commit('closeLoader')
      }
    },
    async checkRemoveDataGracz () {
      // console.log('12345')
      // เวลาใช้งานใน Gracz menu ทุกครั้งที่กดออกเว็บไป แล้วกลับเข้ามาใหม่จะให้ทำการ clear localStorage แล้ว emit OnlinePurchase เพื่อทำการสลับ menu ขวามือเป็นของ panit
      if (this.$router.currentRoute.name === 'HomeProduct') {
        await localStorage.removeItem('OnlinePurchase')
        await localStorage.removeItem('CheckSalesOrAdminGracz')
        await localStorage.removeItem('UserDetailSelling')
        await localStorage.removeItem('categoryType')
        await localStorage.removeItem('GraczSelectCustomer')
        await localStorage.removeItem('GraczProductPromotionSelected')
        await localStorage.removeItem('GraczPromotionSelected')
        this.$EventBus.$emit('OnlinePurchase')
        this.$EventBus.$emit('changeAppBarPanit')
      }
    }
    // async getHomepageItems () {
    //   this.$store.commit('openLoader')
    //   var companyID = ''
    //   if (localStorage.getItem('roleUser') !== null) {
    //     this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //     if (localStorage.getItem('oneData') !== null) {
    //       this.tokenstatus = this.oneData.user.access_token
    //     } else {
    //       this.tokenstatus = ''
    //     }
    //   } else {
    //     this.dataRole = ''
    //     this.tokenstatus = ''
    //   }
    //   if (localStorage.getItem('SetRowCompany') !== null) {
    //     var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //     companyID = companyDataSet.company.company_id
    //   } else {
    //     companyID = '-1'
    //   }
    //   var data = ''
    //   var data2 = ''
    //   var data3 = ''
    //   data = {
    //     token: this.tokenstatus,
    //     role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
    //     begin_search_type: 'component',
    //     begin_search_details: {
    //       custom_user_ID: '1',
    //       what_component: 'new_product',
    //       component_id: ''
    //     },
    //     user_detail: {
    //       company_id: companyID
    //     }
    //   }
    //   data2 = {
    //     token: this.tokenstatus,
    //     role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
    //     begin_search_type: 'component',
    //     begin_search_details: {
    //       custom_user_ID: '1',
    //       what_component: 'best_seller',
    //       component_id: ''
    //     },
    //     user_detail: {
    //       company_id: companyID
    //     }
    //   }
    //   data3 = {
    //     token: this.tokenstatus,
    //     role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
    //     begin_search_type: 'component',
    //     begin_search_details: {
    //       custom_user_ID: '1',
    //       what_component: 'recommended_product',
    //       component_id: ''
    //     },
    //     user_detail: {
    //       company_id: companyID
    //     }
    //   }
    //   await this.$store.dispatch('actionsProduct', data)
    //   var response = await this.$store.state.ModuleProductNode.stateProducts
    //   await this.$store.dispatch('actionsProduct', data2)
    //   var response2 = await this.$store.state.ModuleProductNode.stateProducts
    //   await this.$store.dispatch('actionsProduct', data3)
    //   var response3 = await this.$store.state.ModuleProductNode.stateProducts
    //   this.newArrivals = []
    //   bestSeller = []
    //   this.recommend = []
    //   if (response.ok === 'y') {
    //     if (response.query_result.length !== 0 || response2.query_result.length !== 0 || response3.query_result.length !== 0) {
    //       if (response.query_result.length !== 0) {
    //         this.newArrivals = response.query_result
    //         this.status = true
    //         this.StatusHomeNewProduct = true
    //       } else {
    //         this.newArrivals = 'ยังไม่มีรายการสินค้ามาใหม่'
    //         this.status = true
    //         this.StatusHomeNewProduct = false
    //       }
    //       if (response2.query_result.length !== 0) {
    //         bestSeller = response2.query_result
    //         this.status = true
    //         this.StatusHomeBestProduct = true
    //       } else {
    //         bestSeller = 'ยังไม่มีรายการสินค้าขายดี'
    //         this.status = true
    //         this.StatusHomeBestProduct = false
    //       }
    //       if (response3.query_result.length !== 0) {
    //         this.recommend = response3.query_result
    //         this.status = true
    //         this.StatusHomeRecoment = true
    //       } else {
    //         this.recommend = 'ยังไม่มีรายการสินค้าแนะนำ'
    //         this.status = true
    //         this.StatusHomeRecoment = false
    //       }
    //       this.$store.commit('closeLoader')
    //     } else {
    //       this.newArrivals = []
    //       bestSeller = []
    //       this.recommend = []
    //       this.status = true
    //       this.StatusHomeNewProduct = false
    //       this.StatusHomeBestProduct = false
    //       this.StatusHomeRecoment = false
    //       this.$store.commit('closeLoader')
    //     }
    //   } else {
    //     this.$store.commit('closeLoader')
    //     const Toast = this.$swal.mixin({
    //       toast: true,
    //       showConfirmButton: false,
    //       timer: 1500,
    //       timerProgressBar: true
    //     })
    //     Toast.fire({
    //       icon: 'error',
    //       title: 'ไม่มีสินค้าในหน้าเพจ',
    //       text: 'โปรดติดต่อแอดมิน'
    //     })
    //   }
    // }
  }
}
</script>
