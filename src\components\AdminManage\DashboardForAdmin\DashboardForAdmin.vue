<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">แดชบอร์ดแอดมิน</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>แดชบอร์ดแอดมิน</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาชื่อร้านค้า" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 12" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="shopDataList.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อร้านค้าทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="shopDataList.length !== 0 && (MobileSize || IpadSize)">รายชื่อร้านค้าทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                  :headers="headers"
                  :items="shopDataList"
                  :search="search"
                  style="width:100%;"
                  height="100%"
                  :page.sync="page"
                  @pagination="countRequest"
                  no-results-text="ไม่พบรายชื่อร้านค้า"
                  no-data-text="ไม่มีรายชื่อรา้นค้า"
                  :update:items-per-page="itemsPerPage"
                  :items-per-page="10"
                  class="elevation-1 mt-4"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.name_th`]="{ item }">
                    <span>{{ item.name_th }}</span>
                  </template>

                  <template v-slot:[`item.shop_status`]="{ item }">
                    <v-chip v-if="item.shop_status === 'active'" color="#F0F9EE" text-color="#1AB759">
                      <v-icon size="12" color="#1AB759" class="pr-1">mdi-checkbox-blank-circle</v-icon>
                      <span>ร้านเปิด</span>
                    </v-chip>
                    <v-chip v-else-if="item.shop_status === 'inactive'" color="#F7D9D9" text-color="#D1392B">
                      <v-icon size="12" color="#D1392B" class="pr-1">mdi-checkbox-blank-circle</v-icon>
                      <span>ร้านปิด</span>
                    </v-chip>
                  </template>

                  <template v-slot:[`item.actions`]="{ item }">
                    <v-row dense>
                      <span
                        outlined
                        color="#27AB9C"
                        @click="openSellerDashboard(item)"
                        cclass="pt-4 pb-4"
                        :style="{'color': '#27AB9C'}"
                      >
                        แดชบอร์ดร้านค้า <v-icon :style="{'color': isSuperAdmin === true ? '#27AB9C' : '#BDBDBD'}">mdi-chevron-right</v-icon>
                      </span>
                    </v-row>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
// import { Decode } from '@/services'
export default {
  components: {
  },
  data () {
    return {
      search: '',
      shopDataList: [], // ค่าจากหลังบ้านที่แสดงในตาราง //
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'name_th', width: '190', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะร้านค้า', value: 'shop_status', width: '100', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', filterable: false, width: '150', sortable: false, class: 'backgroundTable fontTable--text align-end' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardForAdminMobile' }).catch(() => {})
      } else {
        localStorage.setItem('pathAdmin', 'dashboardForAdmin')
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      }
    }
  },
  created () {
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
      this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    // backtoPage () {
    //   this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    // },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async AuthorityUser () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.message === 'Get user detail success') {
        if (response.data.current_role_user.super_admin_platform === true) {
          this.isSuperAdmin = true
        } else {
          this.isSuperAdmin = false
        }
      }
    },
    async getShopData () {
      await this.$store.dispatch('actionListAllShopData')
      var response = await this.$store.state.ModuleDashBoardForAdmin.stateListAllShopData
      if (response.ok === 'y') {
        // console.log('actionListAllShopData', response)
        this.shopDataList = response.query_result
        if (this.shopDataList.length !== 0) {
          this.shopDataList.forEach(element => {
            element.username = element.first_name_th + ' ' + element.last_name_th
          })
          for (var i = 0; i < this.shopDataList.length; i++) {
            this.shopDataList[i].indexOfUser = i + 1
          }
        }
      }
    },
    openSellerDashboard (item) {
      var shopID = item.id
      this.$router.push({ path: '/dashboardShopAdmin', name: 'dashboardShopAdmin', params: { id: shopID } }).catch(() => {})
      // console.log(shopID, 'testtest')
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(4) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(4) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.v-data-table /deep/ .v-data-table-header-mobile__wrapper {
    display: flex;
    justify-content: end;
  }
</style>
