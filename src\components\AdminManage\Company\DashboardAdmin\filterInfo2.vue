<template>
<div>
  <v-row>
     <v-col class="mt-3 ml-5">
      <span style="font-size: 18px; color: #333333; font-weight: 700;">PANIT Summary Dashboard</span>
     </v-col>
    </v-row>
    <v-row dense justify="end" class="pb-0">
      <v-col cols="6" md="7" align="end">
        <v-row dense>
          <v-col cols="12" md="3" align="center">
            <div class="mt-4">
              วันที่เริ่ม - สิ้นสุด
            </div>
          </v-col>
          <v-col
            cols="12"
            md="4"
            class="pr-6"
            v-show="numChange === '1'"
          >
            <!-- <v-menu
              ref="menu1"
              v-model="menu1"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="auto"
            > -->
            <v-dialog
              ref="menu1"
              v-model="menu1"
              persistent
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="dateFormatted"
                  label=""
                  hint=""
                  persistent-hint
                  append-icon="mdi-calendar"
                  dense
                  outlined
                  class="mt-2"
                  v-bind="attrs"
                  v-on="on"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="dateStart"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                @input="menu1 = false, dateEnd = ''"
              ></v-date-picker>
            </v-dialog>
            <!-- </v-menu> -->
          </v-col>
          <v-col cols="1" class="pt-5 pr-6"  align="center">-</v-col>
          <v-col
            cols="12"
            md="4"
            class="pr-6"
            v-show="numChange === '1'"
          >
            <v-menu
              v-model="menu2"
              :close-on-content-click="false"
              transition="scale-transition"
              offset-y
              max-width="290px"
              min-width="auto"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="dateFormatted2"
                  label=""
                  hint=""
                  persistent-hint
                  append-icon="mdi-calendar"
                  dense
                  outlined
                  v-bind="attrs"
                  v-on="on"
                  class="mt-2"
                ></v-text-field>
              </template>
              <v-date-picker
                v-model="dateEnd"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                :min="dateStart"
                @input="menu2 = false"
                @change="filterDate"
              ></v-date-picker>
            </v-menu>
          </v-col>
        </v-row>
      </v-col>
     <!--  <v-col cols="12" md="3">
         <v-select
        v-show="numChange === '1'"
         style="position: 'absolute'; elevation: 50; zIndex:50 ;"
          v-model="weekday"
          :items="weekdays"
          item-value="id"
          item-text="name"
          return-object
          @change="filterOther"
          dense
          outlined
          hide-details
          label="เลือก"
          class="ma-2"
        ></v-select>
      </v-col> -->
</v-row>
<v-row>
    <div class="container">
      <table id="example" class="display nowrap" style="display: none" width="100%">
        <thead>
          <tr>
            <th></th>
            <th></th>
            <th></th>
            <th>การเข้าใช้งานระบบของผู้ใช้งาน</th>
            <th></th>
            <th></th>
            <th></th>
          </tr>
        </thead>

        <tfoot>
          <tr>
            <th></th>
            <th></th>
            <th></th>
            <th>การเข้าใช้งานระบบของผู้ใช้งาน</th>
            <th></th>
            <th></th>
            <th></th>
          </tr>
        </tfoot>
        <tbody>
          <tr>
            <td></td>
            <td></td>
            <td>ระหว่างวันที่</td>
            <td>{{formatDate(dateStart)}}-{{formatDate(dateEnd)}}</td>
            <td></td>
            <td></td>
            <td></td>
          </tr>
           <tr>
            <td></td>
            <td></td>
            <td>จำนวนการชำระเงินที่สำเร็จ</td>
            <td>{{transition}}</td>
            <td>รายการ</td>
            <td></td>
            <td></td>
          </tr>
        </tbody>
      </table>
      <table id="example2" class="display nowrap" style="display: none" width="100%">
        <thead>
          <tr>
            <th>ประเภท</th>
            <th>จำนวน(คน)</th>
          </tr>
        </thead>

        <tfoot>
          <tr>
            <th>ประเภท</th>
            <th>จำนวน(คน)</th>
          </tr>
        </tfoot>

        <tbody>
          <tr>
            <td>ผู้ใช้ทั้งหมด</td>
            <td>{{sellerAndUser.user_total}}</td>
          </tr>
          <tr>
            <td>ผู้ใช้ที่มีการใช้งาน</td>
            <td>{{sellerAndUser.user_active}}</td>
          </tr>
          <tr>
            <td>ผู้ใช้ใหม่</td>
            <td>{{sellerAndUser.user_new}}</td>
          </tr>
       </tbody>
      </table>
    <table id="example3" class="display nowrap" style="display: none" width="100%">
        <thead>
          <tr>
            <th>ประเภท</th>
            <th>จำนวน(คน)</th>
          </tr>
        </thead>

        <tfoot>
          <tr>
            <th>ประเภท</th>
            <th>จำนวน(ร้าน)</th>
          </tr>
        </tfoot>

        <tbody>
          <tr>
            <td>ร้านค้าทั้งหมด</td>
            <td>{{sellerAndUser.seller_total}}</td>
          </tr>
          <tr>
            <td>ร้านค้าที่มีการใช้งาน</td>
            <td>{{sellerAndUser.seller_active}}</td>
          </tr>
          <tr>
            <td>ร้านค้าใหม่</td>
            <td>{{sellerAndUser.seller_new}}</td>
          </tr>
       </tbody>
      </table>
          <table id="example4" class="display nowrap" style="display: none" width="100%">
        <thead>
          <tr>
            <th>ที่</th>
            <th>ชื่อร้าน</th>
            <th>"จำนวนรายการสั่งซื้อ"</th>
            <th>"เปอร์เซ็น (%)"</th>
          </tr>
        </thead>
        <tfoot>
          <tr>
            <th>ที่</th>
            <th>ชื่อร้าน</th>
            <th>"จำนวนรายการสั่งซื้อ"</th>
            <th>"เปอร์เซ็น (%)"</th>
          </tr>
        </tfoot>
        <tbody>
          <tr v-for="(item, index) in topOrder" :key="index">
            <td>{{item.number}}</td>
            <td>{{item.seller_name}}</td>
            <td>{{item.total_order}}</td>
            <td>{{item.percent}}</td>
          </tr>
       </tbody>
      </table>
          <table id="example5" class="display nowrap" style="display: none" width="100%">
        <thead>
          <tr>
            <th>ที่</th>
            <th>ชื่อร้าน</th>
            <th>"มูลค่า (บาท)"</th>
            <th>"เปอร์เซ็น (%)"</th>
          </tr>
        </thead>
        <tfoot>
          <tr>
            <th>ที่</th>
            <th>ชื่อร้าน</th>
            <th>"มูลค่า (บาท)"</th>
            <th>"เปอร์เซ็น (%)"</th>
          </tr>
        </tfoot>
        <tbody>
          <tr v-for="(item, index) in topValues" :key="index">
            <td>{{item.number}}</td>
            <td>{{item.seller_name}}</td>
            <td>{{item.total_price}}</td>
            <td>{{item.percent}}</td>
          </tr>
       </tbody>
      </table>
         <table id="example6" class="display nowrap" style="display: none" width="100%">
        <thead>
          <tr>
            <th  v-for="(item, index) in headerUser" :key="index">{{item !== undefined ? item : ''}}</th>
          </tr>
        </thead>
        <tfoot>
           <tr>
            <th  v-for="(item, index) in headerUser" :key="index">{{item !== undefined ? item : ''}}</th>
          </tr>
        </tfoot>
        <tbody>
          <tr v-for="(item, index) in comparedUser" :key="index">
            <td>{{item.type}}</td>
            <td>{{item.month1}}</td>
            <td>{{item.month2}}</td>
            <td>{{item.month3}}</td>
            <td>{{item.month4}}</td>
            <td>{{item.month5}}</td>
          </tr>
          <!-- <tr >
           <td>{{comparedUser[1].type}}</td>
            <td>{{comparedUser[1].month1}}</td>
            <td>{{comparedUser[1].month2}}</td>
            <td>{{comparedUser[1].month3}}</td>
            <td>{{comparedUser[1].month4}}</td>
            <td>{{comparedUser[1].month5}}</td>
          </tr>
          <tr >
            <td>{{comparedUser[2].type}}</td>
            <td>{{comparedUser[2].month1}}</td>
            <td>{{comparedUser[2].month2}}</td>
            <td>{{comparedUser[2].month3}}</td>
            <td>{{comparedUser[2].month4}}</td>
            <td>{{comparedUser[2].month5}}</td>
          </tr> -->
       </tbody>
      </table>
      <table id="example7" class="display nowrap" style="display: none" width="100%" >
        <thead>
          <tr>
            <th v-for="(item, index) in headerSeller" :key="index">{{item !== undefined ? item : ''}}</th>
          </tr>
        </thead>
        <tfoot >
           <tr>
            <th v-for="(item, index) in headerSeller" :key="index">{{item !== undefined ? item : ''}}</th>
          </tr>
        </tfoot>
        <tbody>
          <tr v-for="(item, index) in comparedSeller" :key="index">
            <td>{{item.type}}</td>
            <td>{{item.month1}}</td>
            <td>{{item.month2}}</td>
            <td>{{item.month3}}</td>
            <td>{{item.month4}}</td>
            <td>{{item.month5}}</td>
          </tr>
          <!-- <tr >
           <td>{{comparedSeller[1].type}}</td>
            <td>{{comparedSeller[1].month1}}</td>
            <td>{{comparedSeller[1].month2}}</td>
            <td>{{comparedSeller[1].month3}}</td>
            <td>{{comparedSeller[1].month4}}</td>
            <td>{{comparedSeller[1].month5}}</td>
          </tr>
          <tr >
            <td>{{comparedSeller[2].type}}</td>
            <td>{{comparedSeller[2].month1}}</td>
            <td>{{comparedSeller[2].month2}}</td>
            <td>{{comparedSeller[2].month3}}</td>
            <td>{{comparedSeller[2].month4}}</td>
            <td>{{comparedSeller[2].month5}}</td>
          </tr> -->
       </tbody>
      </table>
    </div>
</v-row>
</div>
</template>
<script>
import 'datatables.net'
import 'datatables.net-dt/css/jquery.dataTables.min.css'
import 'jszip'
import 'datatables.net-buttons-dt'
import 'datatables.net-buttons-dt/css/buttons.dataTables.min.css'
import 'datatables.net-buttons/js/buttons.colVis'
import 'datatables.net-buttons/js/buttons.flash'
import 'datatables.net-buttons/js/buttons.html5'
import 'datatables.net-buttons/js/buttons.print'
import 'datatables.net-buttons/js/dataTables.buttons'
import 'datatables.net-responsive-dt'
import $ from 'jquery'
window.JSZip = require('jszip')
// Based on example from:
// https://datatables.net/forums/discussion/49457
export default {
  data () {
    return {
      weekday: {},
      weekday2: {},
      weekdays: [
        {
          id: 2, name: '1 เดือน'
        },
        {
          id: 3, name: '6 เดือน'
        },
        {
          id: 4, name: '1 ปี'
        },
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      weekdays2: [
        {
          id: 5, name: 'ทั้งหมด'
        }
      ],
      exportStatus: false,
      mode: '',
      type: [],
      types: [],
      modes: [],
      dateStart: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateEnd: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateFormatted: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateFormatted2: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      menu1: false,
      menu2: false,
      numChange: '1',
      hideExport: false
    }
  },
  created () {
    this.init()
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
    // console.log('checReate', this.dateExport.user[6])
    // setTimeout(function () { this.init() }.bind(this), 1000)
  },
  updated () {
    if (this.exportStatus === true) {
      this.init()
    } else {
      this.init()
    }
  },
  destroyed () {
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    computedDateFormatted () {
      return this.formatDate(this.dateStart)
    },
    transition () {
      return this.$store.state.ModuleAdminManage.dashboardSummary.transaction
    },
    comparedSeller () {
      return this.$store.state.ModuleAdminManage.dashboardChart.comparedSeller.map((e, i) => {
        return {
          type: e.name === 'total' ? 'ผู้ใช้ทั้งหมด' : e.name === 'active' ? 'ผู้ใช้ที่มีการใช้งาน' : e.name === 'new' ? 'ผู้ใช้ใหม่' : '',
          month1: e.data[0] !== undefined ? e.data[0] : '',
          month2: e.data[1] !== undefined ? e.data[1] : '',
          month3: e.data[2] !== undefined ? e.data[2] : '',
          month4: e.data[3] !== undefined ? e.data[3] : '',
          month5: e.data[4] !== undefined ? e.data[4] : ''
        }
      })
    },
    comparedUser () {
      return this.$store.state.ModuleAdminManage.dashboardChart.comparedUser.map((e, i) => {
        return {
          type: e.name === 'total' ? 'ผู้ใช้ทั้งหมด' : e.name === 'active' ? 'ผู้ใช้ที่มีการใช้งาน' : e.name === 'new' ? 'ผู้ใช้ใหม่' : '',
          month1: e.data[0] !== undefined ? e.data[0] : '',
          month2: e.data[1] !== undefined ? e.data[1] : '',
          month3: e.data[2] !== undefined ? e.data[2] : '',
          month4: e.data[3] !== undefined ? e.data[3] : '',
          month5: e.data[4] !== undefined ? e.data[3] : ''
        }
      })
    },
    topOrder () {
      return this.$store.state.ModuleAdminManage.stateDashboardAdminAll.data.top_order.map((e, i) => {
        return {
          number: i + 1,
          seller_name: e.seller_name,
          total_order: e.total_order,
          percent: e.percent
        }
      })
    },
    topValues () {
      return this.$store.state.ModuleAdminManage.stateDashboardAdminAll.data.top_values.map((e, i) => {
        return {
          number: i + 1,
          seller_name: e.seller_name,
          total_price: e.total_price,
          percent: e.percent
        }
      })
    },
    sellerAndUser () {
      return this.$store.state.ModuleAdminManage.stateDashboardAdminAll.data
    },
    headerUser () {
      return [...['ประเภท'], ...this.$store.state.ModuleAdminManage.dashboardChart.dateUser.xaxis.categories]
    },
    headerSeller () {
      return [...['ประเภท'], ...this.$store.state.ModuleAdminManage.dashboardChart.dateSeller.xaxis.categories]
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    },
    dateStart (val) {
      this.dateFormatted = this.formatDate(this.dateStart)
    },
    dateEnd (val) {
      this.dateFormatted2 = this.formatDate(this.dateEnd)
    },
    comparedSeller () {
      // this.forceUpdate()
      // console.log('ดู reloadDataTable')
    }
  },
  methods: {
    async removeTable () {
      await $('#example').DataTable().destroy(true)
      await $('#example2').DataTable().destroy(true)
      await $('#example3').DataTable().destroy(true)
      await $('#example4').DataTable().destroy(true)
      await $('#example5').DataTable().destroy(true)
      await $('#example6').DataTable().destroy(true)
      await $('#example7').DataTable().destroy(true)
    },
    forceUpdate () {
      this.$forceUpdate()
    },
    async init () {
      setTimeout(function () {
        function getHeaderNames (table) {
          var header = $(table).DataTable().columns().header().toArray()
          var names = []
          header.forEach(function (th) {
            names.push($(th).html())
          })
          return names
        }
        function buildCols (data) {
          var cols = '<cols>'
          for (var i = 0; i < data.length; i++) {
            var colNum = i + 1
            cols += '<col min="' + colNum + '" max="' + colNum + '" width="20" customWidth="1"/>'
          }
          cols += '</cols>'
          return cols
        }
        function buildRow (data, rowNum, styleNum) {
          // console.log(data, rowNum, styleNum)
          var style = styleNum ? ' s="' + styleNum + '"' : ''
          var row = '<row r="' + rowNum + '">'
          for (var i = 0; i < data.length; i++) {
            var colNum = (i + 10).toString(36).toUpperCase()
            var cr = colNum + rowNum
            row += '<c t="inlineStr" r="' + cr + '"' + style + '>' +
              '<is>' +
                '<t>' + data[i] + '</t>' +
              '</is>' +
            '</c>'
          }
          row += '</row>'
          return row
        }
        function getTableData (table, title) {
          var header = getHeaderNames(table)
          var table2 = $(table).DataTable()
          var rowNum = 1
          var mergeCells = ''
          var ws = ''
          ws += buildCols(header)
          ws += '<sheetData>'
          if (title.length > 0) {
            ws += buildRow([title], rowNum, 51)
            rowNum++
            var mergeCol = ((header.length - 1) + 10).toString(36).toUpperCase()
            mergeCells = '<mergeCells count="1">' +
            '<mergeCell ref="A1:' + mergeCol + '1"/>' +
            '</mergeCells>'
          }
          ws += buildRow(header, rowNum, 2)
          rowNum++
          table2.rows().every(function (rowIdx, tableLoop, rowLoop) {
            var data = this.data()
            ws += buildRow(data, rowNum, '')
            rowNum++
          })
          ws += '</sheetData>' + mergeCells
          return ws
        }
        function setSheetName (xlsx, name) {
          if (name.length > 0) {
            var source = xlsx.xl['workbook.xml'].getElementsByTagName('sheet')[0]
            source.setAttribute('name', name)
          }
        }
        function addSheet (xlsx, table, title, name, sheetId) {
          var source = xlsx['[Content_Types].xml'].getElementsByTagName('Override')[1]
          var clone = source.cloneNode(true)
          clone.setAttribute('PartName', '/xl/worksheets/sheet' + sheetId + '.xml')
          xlsx['[Content_Types].xml'].getElementsByTagName('Types')[0].appendChild(clone)
          var source2 = xlsx.xl._rels['workbook.xml.rels'].getElementsByTagName('Relationship')[0]
          var clone2 = source2.cloneNode(true)
          clone2.setAttribute('Id', 'rId' + sheetId + 1)
          clone2.setAttribute('Target', 'worksheets/sheet' + sheetId + '.xml')
          xlsx.xl._rels['workbook.xml.rels'].getElementsByTagName('Relationships')[0].appendChild(clone2)
          var source3 = xlsx.xl['workbook.xml'].getElementsByTagName('sheet')[0]
          var clone3 = source3.cloneNode(true)
          clone3.setAttribute('name', name)
          clone3.setAttribute('sheetId', sheetId)
          clone3.setAttribute('r:id', 'rId' + sheetId + 1)
          xlsx.xl['workbook.xml'].getElementsByTagName('sheets')[0].appendChild(clone3)
          var newSheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>' +
      '<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac" mc:Ignorable="x14ac">' +
      getTableData(table, title) +
      '</worksheet>'
          xlsx.xl.worksheets['sheet' + sheetId + '.xml'] = $.parseXML(newSheet)
        }
        $('#example').DataTable({
          dom: 'Bftrip',
          destroy: true,
          buttons: [
            {
              extend: 'excelHtml5',
              text: ' <i class="fa fa-file-excel"></i> Excel',
              customize: function (xlsx) {
                setSheetName(xlsx, 'ภาพรวมระบบ PANIT')
                addSheet(xlsx, '#example2', 'การเข้าใช้งานระบบของผู้ใช้งาน', 'การเข้าใช้งานระบบของผู้ใช้งาน', '2')
                addSheet(xlsx, '#example3', 'การเข้าใช้งานระบบของร้านค้า', 'การเข้าใช้งานระบบของร้านค้า', '3')
                addSheet(xlsx, '#example4', '10 อันดับร้านค้าจำนวนรายการสั่งซื้อมากที่สุด', 'จำนวนรายการสั่งซื้อ', '4')
                addSheet(xlsx, '#example5', '10 อันดับร้านค้ามูลค่ารายการสั่งซื้อมากที่สุด', 'มูลค่ารายการสั่งซื้อ', '5')
                addSheet(xlsx, '#example6', 'เปรียบเทียบจำนวนผู้ใช้งานแบบรายเดือน', 'ผู้ใช้งานแบบรายเดือน', '6')
                addSheet(xlsx, '#example7', 'เปรียบเทียบจำนวนร้านค้าแบบรายเดือน', 'ร้านค้าแบบรายเดือน', '7')
              }
            }
            // {
            //   extend: 'csv',
            //   text: 'csv',
            //   charset: 'utf-8',
            //   extension: '.csv',
            //   filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
            //   customize: function (xlsx) {
            //     setSheetName(xlsx, 'Calls')
            //     addSheet(xlsx, '#example2', 'การเข้าใช้งานระบบของผู้ใช้งาน', 'การเข้าใช้งานระบบของผู้ใช้งาน', '2')
            //     addSheet(xlsx, '#example3', 'การเข้าใช้งานระบบของร้านค้า', 'การเข้าใช้งานระบบของร้านค้า', '3')
            //     addSheet(xlsx, '#example4', '10 อันดับร้านค้าจำนวนรายการสั่งซื้อมากที่สุด', 'จำนวนรายการสั่งซื้อ', '4')
            //     addSheet(xlsx, '#example5', '10 อันดับร้านค้ามูลค่ารายการสั่งซื้อมากที่สุด', 'มูลค่ารายการสั่งซื้อ', '5')
            //     addSheet(xlsx, '#example6', 'เปรียบเทียบจำนวนผู้ใช้งานแบบรายเดือน', 'ผู้ใช้งานแบบรายเดือน', '6')
            //     addSheet(xlsx, '#example7', 'เปรียบเทียบจำนวนร้านค้าแบบรายเดือน', 'ร้านค้าแบบรายเดือน', '7')
            //   }
            // },
            // 'print',
            // 'copy'
          ]
        })
      }, 500)
      // await $('.dt-buttons').remove()
      // await $('.dt-buttons').append('<button class="dt-button buttons-excel buttons-html5" tabindex="0" aria-controls="example" type="button"><span> <svg class="svg-inline--fa fa-file-excel fa-w-12" aria-hidden="true" focusable="false" data-prefix="fa" data-icon="file-excel" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm60.1 106.5L224 336l60.1 93.5c5.1 8-.6 18.5-10.1 18.5h-34.9c-4.4 0-8.5-2.4-10.6-6.3C208.9 405.5 192 373 192 373c-6.4 14.8-10 20-36.6 68.8-2.1 3.9-6.1 6.3-10.5 6.3H110c-9.5 0-15.2-10.5-10.1-18.5l60.3-93.5-60.3-93.5c-5.2-8 .6-18.5 10.1-18.5h34.8c4.4 0 8.5 2.4 10.6 6.3 26.1 48.8 20 33.6 36.6 68.5 0 0 6.1-11.7 36.6-68.5 2.1-3.9 6.2-6.3 10.6-6.3H274c9.5-.1 15.2 10.4 10.1 18.4zM384 121.9v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"></path></svg><!-- <i class="fa fa-file-excel"></i> Font Awesome fontawesome.com --> Excel</span></button>')
    },
    async reloadDataTable () {
      await setTimeout(function () {
        function getHeaderNames (table) {
          var header = $(table).DataTable().columns().header().toArray()
          var names = []
          header.forEach(function (th) {
            names.push($(th).html())
          })
          return names
        }
        function buildCols (data) {
          var cols = '<cols>'
          for (var i = 0; i < data.length; i++) {
            var colNum = i + 1
            cols += '<col min="' + colNum + '" max="' + colNum + '" width="20" customWidth="1"/>'
          }
          cols += '</cols>'
          return cols
        }
        function buildRow (data, rowNum, styleNum) {
          var style = styleNum ? ' s="' + styleNum + '"' : ''
          var row = '<row r="' + rowNum + '">'
          for (var i = 0; i < data.length; i++) {
            var colNum = (i + 10).toString(36).toUpperCase()
            var cr = colNum + rowNum
            row += '<c t="inlineStr" r="' + cr + '"' + style + '>' +
              '<is>' +
                '<t>' + data[i] + '</t>' +
              '</is>' +
            '</c>'
          }
          row += '</row>'
          return row
        }
        function getTableData (table, title) {
          var header = getHeaderNames(table)
          var table2 = $(table).DataTable()
          var rowNum = 1
          var mergeCells = ''
          var ws = ''
          ws += buildCols(header)
          ws += '<sheetData>'
          if (title.length > 0) {
            ws += buildRow([title], rowNum, 51)
            rowNum++
            var mergeCol = ((header.length - 1) + 10).toString(36).toUpperCase()
            mergeCells = '<mergeCells count="1">' +
            '<mergeCell ref="A1:' + mergeCol + '1"/>' +
            '</mergeCells>'
          }
          ws += buildRow(header, rowNum, 2)
          rowNum++
          table2.rows().every(function (rowIdx, tableLoop, rowLoop) {
            var data = this.data()
            ws += buildRow(data, rowNum, '')
            rowNum++
          })
          ws += '</sheetData>' + mergeCells
          return ws
        }
        function setSheetName (xlsx, name) {
          if (name.length > 0) {
            var source = xlsx.xl['workbook.xml'].getElementsByTagName('sheet')[0]
            source.setAttribute('name', name)
          }
        }
        function addSheet (xlsx, table, title, name, sheetId) {
          var source = xlsx['[Content_Types].xml'].getElementsByTagName('Override')[1]
          var clone = source.cloneNode(true)
          clone.setAttribute('PartName', '/xl/worksheets/sheet' + sheetId + '.xml')
          xlsx['[Content_Types].xml'].getElementsByTagName('Types')[0].appendChild(clone)
          var source2 = xlsx.xl._rels['workbook.xml.rels'].getElementsByTagName('Relationship')[0]
          var clone2 = source2.cloneNode(true)
          clone2.setAttribute('Id', 'rId' + sheetId + 1)
          clone2.setAttribute('Target', 'worksheets/sheet' + sheetId + '.xml')
          xlsx.xl._rels['workbook.xml.rels'].getElementsByTagName('Relationships')[0].appendChild(clone2)
          var source3 = xlsx.xl['workbook.xml'].getElementsByTagName('sheet')[0]
          var clone3 = source3.cloneNode(true)
          clone3.setAttribute('name', name)
          clone3.setAttribute('sheetId', sheetId)
          clone3.setAttribute('r:id', 'rId' + sheetId + 1)
          xlsx.xl['workbook.xml'].getElementsByTagName('sheets')[0].appendChild(clone3)
          var newSheet = '<?xml version="1.0" encoding="UTF-8" standalone="yes"?>' +
      '<worksheet xmlns="http://schemas.openxmlformats.org/spreadsheetml/2006/main" xmlns:r="http://schemas.openxmlformats.org/officeDocument/2006/relationships" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" xmlns:x14ac="http://schemas.microsoft.com/office/spreadsheetml/2009/9/ac" mc:Ignorable="x14ac">' +
      getTableData(table, title) +
      '</worksheet>'
          xlsx.xl.worksheets['sheet' + sheetId + '.xml'] = $.parseXML(newSheet)
        }
        $('#example').DataTable({
          dom: 'Bftrip',
          bDestroy: true,
          buttons: [
            {
              extend: 'excelHtml5',
              text: '<i class="fa fa-file-excel"></i> Excel',
              customize: function (xlsx) {
                setSheetName(xlsx, 'ภาพรวมระบบ PANIT')
                addSheet(xlsx, '#example2', 'การเข้าใช้งานระบบของผู้ใช้งาน', 'การเข้าใช้งานระบบของผู้ใช้งาน', '2')
                addSheet(xlsx, '#example3', 'การเข้าใช้งานระบบของร้านค้า', 'การเข้าใช้งานระบบของร้านค้า', '3')
                addSheet(xlsx, '#example4', '10 อันดับร้านค้าจำนวนรายการสั่งซื้อมากที่สุด', 'จำนวนรายการสั่งซื้อ', '4')
                addSheet(xlsx, '#example5', '10 อันดับร้านค้ามูลค่ารายการสั่งซื้อมากที่สุด', 'มูลค่ารายการสั่งซื้อ', '5')
                addSheet(xlsx, '#example6', 'เปรียบเทียบจำนวนผู้ใช้งานแบบรายเดือน', 'ผู้ใช้งานแบบรายเดือน', '6')
                addSheet(xlsx, '#example7', 'เปรียบเทียบจำนวนร้านค้าแบบรายเดือน', 'ร้านค้าแบบรายเดือน', '7')
              }
            }
          ]
        }).fnClearTable()
        $('#example2').DataTable().fnClearTable()
        $('#example3').DataTable().fnClearTable()
        $('#example4').DataTable().fnClearTable()
        $('#example5').DataTable().fnClearTable()
        $('#example6').DataTable().fnClearTable()
        $('#example7').DataTable().fnClearTable()
        // this.$forceUpdate()
      }, 500)
      // await $('.dt-buttons').remove()
      // await $('<button class="dt-button buttons-excel buttons-html5" tabindex="0" aria-controls="example" type="button"><span> <svg class="svg-inline--fa fa-file-excel fa-w-12" aria-hidden="true" focusable="false" data-prefix="fa" data-icon="file-excel" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm60.1 106.5L224 336l60.1 93.5c5.1 8-.6 18.5-10.1 18.5h-34.9c-4.4 0-8.5-2.4-10.6-6.3C208.9 405.5 192 373 192 373c-6.4 14.8-10 20-36.6 68.8-2.1 3.9-6.1 6.3-10.5 6.3H110c-9.5 0-15.2-10.5-10.1-18.5l60.3-93.5-60.3-93.5c-5.2-8 .6-18.5 10.1-18.5h34.8c4.4 0 8.5 2.4 10.6 6.3 26.1 48.8 20 33.6 36.6 68.5 0 0 6.1-11.7 36.6-68.5 2.1-3.9 6.2-6.3 10.6-6.3H274c9.5-.1 15.2 10.4 10.1 18.4zM384 121.9v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"></path></svg><!-- <i class="fa fa-file-excel"></i> Font Awesome fontawesome.com --> Excel</span></button>').appendTo('.dt-buttons')
      // setTimeout(function () { $('.dt-buttons').append('<button class="dt-button buttons-excel buttons-html5" tabindex="0" aria-controls="example" type="button"><span> <svg class="svg-inline--fa fa-file-excel fa-w-12" aria-hidden="true" focusable="false" data-prefix="fa" data-icon="file-excel" role="img" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 384 512" data-fa-i2svg=""><path fill="currentColor" d="M224 136V0H24C10.7 0 0 10.7 0 24v464c0 13.3 10.7 24 24 24h336c13.3 0 24-10.7 24-24V160H248c-13.2 0-24-10.8-24-24zm60.1 106.5L224 336l60.1 93.5c5.1 8-.6 18.5-10.1 18.5h-34.9c-4.4 0-8.5-2.4-10.6-6.3C208.9 405.5 192 373 192 373c-6.4 14.8-10 20-36.6 68.8-2.1 3.9-6.1 6.3-10.5 6.3H110c-9.5 0-15.2-10.5-10.1-18.5l60.3-93.5-60.3-93.5c-5.2-8 .6-18.5 10.1-18.5h34.8c4.4 0 8.5 2.4 10.6 6.3 26.1 48.8 20 33.6 36.6 68.5 0 0 6.1-11.7 36.6-68.5 2.1-3.9 6.2-6.3 10.6-6.3H274c9.5-.1 15.2 10.4 10.1 18.4zM384 121.9v6.1H256V0h6.1c6.4 0 12.5 2.5 17 7l97.9 98c4.5 4.5 7 10.6 7 16.9z"></path></svg><!-- <i class="fa fa-file-excel"></i> Font Awesome fontawesome.com --> Excel</span></button>') }, 1000)
    },
    backtoUserMenu () {
      this.$router.push({ path: '/sellerMobile' }).catch(() => {})
    },
    async numChangeData () {
      this.numChange = '0'
    },
    async filterDate () {
      // console.log('***********')
      // this.removeTable()
      this.exportStatus = await true
      var preStart = await this.afterDate(this.formatDate(this.dateStart))
      var preEnd = await this.afterDate(this.formatDate(this.dateEnd))
      // const start = await this.timeStamp(preStart)
      // const end = await this.timeStamp(preEnd)
      // const start = '31-2-2022'
      // const end = '31-2-2022'
      // const dataFilter = this.$store.getters.filterDate(start, end)
      // this.$store.state.ModuleShop.stateDashboard = []
      // this.$store.state.ModuleShop.stateDashboard = {
      //   data: dataFilter
      // }
      // const GetKey = dataFilter.map(name => {
      //   return {
      //     buyer_name: name.buyer_name
      //   }
      // })
      const data = await {
        start_date: preStart,
        end_date: preEnd,
        year: new Date().toISOString().slice(0, 4)
      }
      await this.$EventBus.$emit('age-changed', data)
      await this.reloadDataTable()
    },
    async filterOther () {
      const today = await new Date()
      // console.log('SDE', this.weekday.id)
      if (this.weekday.id === 2) {
        const data = await {
          text: 'one_month',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(today.setMonth(today.getMonth() - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 3) {
        var today2 = await new Date()
        var end = await ''
        if (((today2.getMonth() + 1) - 6) < 0) {
          await today2.setMonth((today2.getMonth() + 1) - 6)
          end = await today2
        } else {
          end = await today.setMonth(today.getMonth() - 6)
        }
        const data = await {
          text: 'six_months',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        await this.EventBusCall(data)
      } else if (this.weekday.id === 4) {
        var today3 = await new Date()
        var end3 = await ''
        if (((today3.getMonth() + 1) - 12) < 0) {
          await today3.setMonth((today3.getMonth()) - 12)
          end3 = await today3
        } else {
          end3 = await today.setMonth(today.getMonth() - 12)
        }
        const data = await {
          text: 'one_year',
          start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
          end: new Date(end3).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        }
        // const data = await {
        //   text: 'one_year',
        //   start: new Date(today.setFullYear(today.getFullYear())).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }),
        //   end: new Date(today.setFullYear((today.getFullYear() + 1) - 1)).toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' })
        // }
        await this.EventBusCall(data)
      } else {
        const data = await {
          text: 'all'
        }
        await this.EventBusCall(data)
        await this.filterOther2()
      }
      // console.log('filterOther', new Date().toLocaleDateString('default', { year: 'numeric', month: 'short', day: 'numeric' }))
    },
    async filltWeek2 () {
      // console.log('filltWeek2', this.weekday2.id)
      if (this.weekday2.id === 5) {
        const data = await {
          text: 'all'
        }
        this.numChange = await '1'
        await this.EventBusCall(data)
        await this.filterOther2()
      }
    },
    async EventBusCall (data) {
      await this.$EventBus.$emit('updateData', data)
    },
    async filterOther2 () {
      const num = await {
        number: '1'
      }
      await this.$EventBus.$emit('FuntionChange2', num)
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    parseDate (date) {
      // console.log('parseDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${day}-${month}-${year}`
    },
    afterDate (date) {
      // console.log('afterDate', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      return `${year}-${month}-${day}`
    },
    timeStamp (Dates) {
      var myDate = Dates.split('-')
      var newDate = new Date(myDate[2], myDate[1] - 1, myDate[0])
      return newDate.getTime()
    },
    randomColor () {
      const randomColor = Math.floor(Math.random() * 16777215).toString(16)
      return `#${randomColor}`
    }
  }
}
</script>
<style lang="css" scoped>

::v-deep #example_filter {
  display: none;
}
::v-deep #example_paginate {
  display: none;
}
::v-deep #example_info {
    display: none;
}
::v-deep #example_wrapper {
  margin-left: 90%;
}
::v-deep #example_wrapper > hr {
  display: nones
}
::v-deep #example2_filter {
  display: none;
}
::v-deep #example2_paginate {
  display: none;
}
::v-deep #example2_info {
    display: none;
}
::v-deep #example2_wrapper {
  display: none;
}
::v-deep #example3_filter {
  display: none;
}
::v-deep #example3_paginate {
  display: none;
}
::v-deep #example3_info {
    display: none;
}
::v-deep #example3_wrapper {
  display: none;
}
::v-deep #example4_filter {
  display: none;
}
::v-deep #example4_paginate {
  display: none;
}
::v-deep #example4_info {
    display: none;
}
::v-deep #example4_wrapper {
  display: none;
}
::v-deep #example5_filter {
  display: none;
}
::v-deep #example5_paginate {
  display: none;
}
::v-deep #example5_info {
    display: none;
}
::v-deep #example5_wrapper {
  display: none;
}
::v-deep #example6_filter {
  display: none;
}
::v-deep #example6_paginate {
  display: none;
}
::v-deep #example6_info {
    display: none;
}
::v-deep #example6_wrapper {
  display: none;
}
::v-deep #example7_filter {
  display: none;
}
::v-deep #example7_paginate {
  display: none;
}
::v-deep #example7_info {
    display: none;
}
::v-deep #example7_wrapper {
  display: none;
}

::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    border: 1px solid #27AB9C;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: #ffffff;
    color: #27AB9C;
}
</style>
