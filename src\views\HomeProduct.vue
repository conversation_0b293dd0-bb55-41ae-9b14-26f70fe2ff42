<template>
  <div v-if="status === true">
    <!-- <Carousel/> -->
    <HomeProducts :propsData='GetAllProduct.bestSeller' header='สินค้าขายดี' :check='status'/>
    <HomeProducts :propsData='GetAllProduct.newArrivals' header='สินค้าเข้าใหม่' :check='status'/>
    <!-- <NewArrivals :propsData='GetAllProduct.bestSeller'/> -->
  </div>
</template>

<script>
const newArrivals = []
for (let i = 0; i < 100; i++) {
  newArrivals.push({
    product_id: i,
    name: `Data Title newArrivals ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: 'https://picsum.photos/id/1039/600/600'
  })
}
const bestSeller = []
for (let i = 0; i < 50; i++) {
  bestSeller.push({
    product_id: i,
    name: `Data Title bestSeller ${i}`,
    price: ` ${i * 10}`,
    stock: `${i}`,
    image: 'https://picsum.photos/id/1039/600/600'
  })
}
export default {
  components: {
    // Carousel: () => import('@/components/Carousel/CarouselHome'),
    HomeProducts: () => import('@/components/Home/HomeProduct')
    // NewArrivals: () => import('@/components/Home/HomeProduct/NewArrivals')
  },
  data () {
    return {
      GetAllProduct: {
        newArrivals,
        bestSeller
      },
      status: false
    }
  },
  created () {
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    this.$EventBus.$on('getHomepageItems', this.getHomepageItems)
    this.getHomepageItems()
  },
  methods: {
    async getHomepageItems () {
      var data
      if (localStorage.getItem('roleUser') !== null) {
        data = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        data = {
          role: 'ext_buyer'
        }
      }
      await this.$store.dispatch('actionHompage', data)
      var response = await this.$store.state.ModuleHompage.statehomepagedata
      // console.log('response Homepage data ====>', response)
      if (response.result === 'SUCCESS') {
        if (response.data.length !== 0) {
          if (response.data.list_new_products !== 'ยังไม่มีรายการสินค้าขายดี') {
            this.GetAllProduct.newArrivals = response.data.list_new_products
            this.status = true
          } else {
            this.GetAllProduct.newArrivals = 'ยังไม่มีรายการสินค้าขายดี'
            this.status = true
          }
          this.GetAllProduct.bestSeller = response.data.list_best_sold_products
          this.status = true
        } else {
          this.GetAllProduct.newArrivals = []
          this.GetAllProduct.bestSeller = []
          this.status = true
        }
      } else {
        const Toast = this.$swal.mixin({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        Toast.fire({
          icon: 'error',
          title: 'ไม่มีสินค้าในหน้าเพจ',
          text: 'โปรดติดต่อแอดมิน'
        })
      }
    }
  }
}
</script>
