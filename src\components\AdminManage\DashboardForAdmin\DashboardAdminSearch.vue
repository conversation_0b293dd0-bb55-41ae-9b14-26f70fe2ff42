<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">แดชบอร์ด คำที่ค้นหาภายในระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>แดชบอร์ด คำที่ค้นหาภายในระบบ</v-card-title>

      <v-col cols="12">
        <v-row>
          <v-col cols="12" align="end">
            <v-btn rounded color="primary" @click="ExportDashboardSearch()">Export ข้อมูล</v-btn>
          </v-col>

          <v-col cols="12">
            <v-row>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>Role ผู้ใช้งาน</span>
                <v-select
                  v-model="selectedRole"
                  :items="items"
                  item-text="text"
                  item-value="value"
                  @change="detailSearchHistory"
                  solo
                  dense
                  label="กรองตามบทบาทของผู้ใช้"
                  hide-details
                ></v-select>
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>รหัสผู้ใช้ (User ID)</span>
                <v-text-field
                  v-model="userID"
                  @change="detailSearchHistory"
                  solo
                  dense
                  placeholder="กรองตามรหัสผู้ใช้"
                  style="font-size: 10px;"
                  hide-details
                >
                </v-text-field>
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>รหัสบริษัท (Company ID)</span>
                <v-text-field
                  v-model="companyID"
                  @change="detailSearchHistory"
                  solo
                  dense
                  placeholder="กรองตามรหัสบริษัท"
                  hide-details
                >
                </v-text-field>
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>รหัสร้านค้า (Seller Shop ID)</span>
                <v-text-field
                  v-model="sellerShopID"
                  @change="detailSearchHistory"
                  solo
                  dense
                  placeholder="กรองตามรหัสร้านค้า"
                  hide-details
                >
                </v-text-field>
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>ช่วงเวลา</span>
                <v-select
                  v-model="dateRange"
                  :items="presetOptions"
                  item-text="text"
                  item-value="value"
                  @change="detailSearchHistory"
                  solo
                  dense
                  hide-details
                />
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>เรียงข้อมูลตาม</span>
                <v-select
                  v-model="selectedSortBy"
                  :items="SortBy"
                  item-text="text"
                  item-value="value"
                  @change="detailSearchHistory"
                  solo
                  dense
                  label="ชนิดที่อยากให้เรียง"
                  hide-details
                >
                </v-select>
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>ลำดับข้อมูล</span>
                <v-select
                  v-model="orderDetail"
                  :items="OrderBy"
                  item-text="text"
                  item-value="value"
                  @change="detailSearchHistory"
                  solo
                  dense
                  label="ลำดับการเรียงข้อมูล"
                  hide-details
                ></v-select>
              </v-col>
              <v-col :cols="MobileSize || IpadSize ? 6 : 3">
                <span>แยกตามผู้ใช้</span>
                <v-switch
                  v-model="groupByUser"
                  @change="detailSearchHistory"
                  inset
                  :label="`${groupByUser ? 'ใช่' : 'ไม่ใช่'}`"
                  hide-details
                  style="margin-top: 8px; padding-top: 0px;"
                />
              </v-col>
            </v-row>
          </v-col>

          <v-col cols="12">
            <v-card>
              <v-data-table
                class="elevation-1"
                :headers="headers"
                :items="searchHistory"
                :page.sync="page"
                :footer-props="{'items-per-page-text':'จำนวนแถว', 'items-per-page-options': [10, 20, 30, 40, 50, 100]}"
                :items-per-page="options.itemsPerPage"
                :server-items-length="Number(totalMaxPage)"
                :options.sync="options"
                @update:options="updateOptionsSearchHistory"
              >
                <template v-slot:[`item.keyword_count`]="{ item }">
                  {{ Number(item.keyword_count).toLocaleString() }}
                </template>
                <template v-slot:[`item.first_searched_at`]="{ item }">
                  {{ formatDate(item.first_searched_at) }}
                </template>
                <template v-slot:[`item.last_searched_at`]="{ item }">
                  {{ formatDate(item.last_searched_at) }}
                </template>
              </v-data-table>
            </v-card>
          </v-col>
        </v-row>
      </v-col>

    </v-card>
  </v-container>
</template>

<script>
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import { Decode } from '@/services'
dayjs.extend(utc)
export default {
  data () {
    return {
      items: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'admin', value: 'admin' },
        { text: 'purchaser', value: 'purchaser' },
        { text: 'ext_buyer', value: 'ext_buyer' }
      ],
      SortBy: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'จำนวนการค้นหา', value: 'keyword' },
        { text: 'วันที่ค้นหาครั้งล่าสุด', value: 'last_search' }
      ],
      OrderBy: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'น้อยไปมาก', value: 'ASC' },
        { text: 'มากไปน้อย', value: 'DESC' }
      ],
      headers: [
        { text: 'คำที่ค้นหา', value: 'keyword', width: '150', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนการค้นหา', value: 'keyword_count', width: '150', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ค้นหาครั้งแรก', value: 'first_searched_at', width: '150', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ค้นหาครั้งล่าสุด', value: 'last_searched_at', width: '150', sortable: false, align: 'center', filterable: false, class: 'backgroundTable fontTable--text' }
      ],
      searchHistory: [],
      userID: '',
      selectedRole: '',
      companyID: '',
      sellerShopID: '',
      selectedSortBy: '',
      orderDetail: '',
      groupByUser: false,
      dateRange: '1d',
      presetOptions: [
        { text: '1 วัน', value: '1d' },
        { text: '3 วัน', value: '3d' },
        { text: '90 วัน', value: '90d' }
      ],
      customDate: {
        start: null,
        end: null
      },
      totalMaxPage: '',
      page: 1,
      options: {
        page: 1,
        itemsPerPage: 10
      }
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardShopAdminMobile' }).catch(() => { })
      } else {
        await localStorage.setItem('pathAdmin', 'BackToMobile')
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => { })
      }
    }
  },
  async created () {
    window.scrollTo(0, 0)
    this.$EventBus.$emit('changeNavAdmin')
    await this.detailSearchHistory()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {

  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    formatDate (date) {
      return dayjs.utc(date).format('DD/MM/YYYY HH:mm:ss')
    },
    updateOptionsSearchHistory (options) {
      this.options = options
      this.page = options.page
      this.detailSearchHistory()
    },
    async detailSearchHistory () {
      this.searchHistory = []
      this.totalMaxPage = ''
      var data = {
        group_by_user: this.groupByUser,
        date_range: this.dateRange,
        user_id: this.userID,
        role: this.selectedRole,
        seller_shop_id: this.sellerShopID,
        company_id: this.companyID,
        field_order: this.selectedSortBy,
        order: this.orderDetail,
        page: this.page,
        limit: this.options.itemsPerPage
      }

      await this.$store.dispatch('actionsSearchHistory', data)
      var response = await this.$store.state.ModuleAdminManage.stateSearchHistory
      console.log(response)

      if (response.result === 'success') {
        this.searchHistory = response.data.result
        this.totalMaxPage = response.data.pagination.pagination.total_search_result
      }
    },
    async ExportDashboardSearch () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        group_by_user: this.groupByUser,
        date_range: this.dateRange,
        user_id: this.userID,
        role: this.selectedRole,
        seller_shop_id: this.sellerShopID,
        company_id: this.companyID,
        field_order: this.selectedSortBy,
        order: this.orderDetail
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}dashboard/searchHistory/export`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob',
        data: data
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'ข้อมูลคำที่ค้นหาภายในระบบ.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    }
  }
}
</script>

<style>

</style>
