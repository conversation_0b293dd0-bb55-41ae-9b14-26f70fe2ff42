import AxiosBusiness from '../store_Business/axios_business'

const ModuleBusiness = {
  state: {
    stateCreateBusiness: [],
    stateDetailBusiness: [],
    stateFindTaxIDByRevenue: [],
    stateFindTaxIDByOneID: [],
    stateUploadApprover: [],
    stateUploadResultDocument: [],
    stateCheckeKYC: [],
    stateCreateBusinessOther: [],
    stateManageUser: [],
    stateManageCompany: [],
    stateManageShop: [],
    stateListPositions: [],
    stateAssignPositionsUser: [],
    stateSearchUser: [],
    stateOrderDetailCompany: [],
    stateListBusinessOrders: [],
    stateListCompany: [],
    stateListUsersCompany: [],
    stateListUsersShops: [],
    stateListCompanyPositions: [],
    stateListShopPositions: [],
    stateCreateRole: [],
    stateRoleUpdate: [],
    stateEditRole: [],
    stateCreateSellerShopNewBranch: [],
    stateUpdateOwnerPosition: [],
    stateCheckUser: [],
    stateGetPackage: [],
    stateCreatePackage: [],
    stateGetPartnerDetails: [],
    stateGetPartnerCode: [],
    stateRegisterBusiness: [],
    stateRegisterPartner: [],
    stateGetDetailPackage: [],
    stateGetPartnerList: [],
    stateEditPackage: [],
    stateCancelPackage: [],
    statePartnerService: [],
    statePartnerOrderList: [],
    statePartnerEdit: [],
    stateGetBillList: [],
    statePartnerOrderListV2: [],
    stateGetDetailPartnerOrderList: [],
    stateGetBillDetail: [],
    stateGetBillingApproval: [],
    stateCancelFunction: [],
    statechangeStatusPackage: [],
    stateRanksPackage: [],
    stateRanksShopBuying: [],
    stateListOrderPackage: [],
    stateGetDashboardPartnerDistributeIncome: [],
    stateGetDashboardPartnerOrderListIncome: [],
    stateGetTransfer: [],
    statePartnerEtaxDocumentInvoice: [],
    statePartnerEtaxDocumentReceipt: [],
    statePartnerChartIncome: [],
    stateCheckBusiness: [],
    stateRegisterShop: []
  },
  mutations: {
    mutationCreateBusiness (state, data) {
      state.stateCreateBusiness = data
    },
    mutationDetailBusiness (state, data) {
      state.stateDetailBusiness = data
    },
    mutationFindTaxIDByRevenue (state, data) {
      state.stateFindTaxIDByRevenue = data
    },
    mutationFindTaxIDByOneID (state, data) {
      state.stateFindTaxIDByOneID = data
    },
    mutationUploadApprover (state, data) {
      state.stateUploadApprover = data
    },
    mutationUploadResultDocument (state, data) {
      state.stateUploadResultDocument = data
    },
    mutationCheckeKYC (state, data) {
      state.stateCheckeKYC = data
    },
    mutationCreateBusinessOther (state, data) {
      state.stateCreateBusinessOther = data
    },
    mutationManageUser (state, data) {
      state.stateManageUser = data
    },
    mutationManageCompany (state, data) {
      state.stateManageCompany = data
    },
    mutationManageShop (state, data) {
      state.stateManageShop = data
    },
    mutationListPositions (state, data) {
      state.stateListPositions = data
    },
    mutationAssignPositionsUser (state, data) {
      state.stateAssignPositionsUser = data
    },
    mutationSearchUser (state, data) {
      state.stateSearchUser = data
    },
    mutationOrderDetailCompany (state, data) {
      state.stateOrderDetailCompany = data
    },
    mutationListBusinessOrders (state, data) {
      state.stateListBusinessOrders = data
    },
    mutationListCompany (state, data) {
      state.stateListCompany = data
    },
    mutationListUsersCompany (state, data) {
      state.stateListUsersCompany = data
    },
    mutationListUsersShops (state, data) {
      state.stateListUsersShops = data
    },
    mutationListCompanyPositions (state, data) {
      state.stateListCompanyPositions = data
    },
    mutationListShopPositions (state, data) {
      state.stateListShopPositions = data
    },
    mutationCreateRole (state, data) {
      state.stateCreateRole = data
    },
    mutationRoleUpdate (state, data) {
      state.stateRoleUpdate = data
    },
    mutationEditRole (state, data) {
      state.stateEditRole = data
    },
    mutationCreateSellerShopNewBranch (state, data) {
      state.stateCreateSellerShopNewBranch = data
    },
    mutationUpdateOwnerPosition (state, data) {
      state.stateUpdateOwnerPosition = data
    },
    mutationCheckUser (state, data) {
      state.stateCheckUser = data
    },
    mutationGetPackage (state, data) {
      state.stateGetPackage = data
    },
    mutationCreatePackage (state, data) {
      state.stateCreatePackage = data
    },
    mutationRegisterBusiness (state, data) {
      state.stateRegisterBusiness = data
    },
    mutationRegisterPartner (state, data) {
      state.stateRegisterPartner = data
    },
    mutationGetDetailPackage (state, data) {
      state.stateGetDetailPackage = data
    },
    mutationGetPartnerDetails (state, data) {
      state.stateGetPartnerDetails = data
    },
    mutationGetPartnerCode (state, data) {
      state.stateGetPartnerCode = data
    },
    mutationGetPartnerList (state, data) {
      state.stateGetPartnerList = data
    },
    mutationEditPackage (state, data) {
      state.stateEditPackage = data
    },
    mutationCancelPackage (state, data) {
      state.stateCancelPackage = data
    },
    mutationPartnerService (state, data) {
      state.statePartnerService = data
    },
    mutationPartnerOrderList (state, data) {
      state.statePartnerOrderList = data
    },
    mutationPartnerEdit (state, data) {
      state.statePartnerEdit = data
    },
    mutationGetBillList (state, data) {
      state.stateGetBillList = data
    },
    mutationPartnerOrderListV2 (state, data) {
      state.statePartnerOrderListV2 = data
    },
    mutationGetDetailPartnerOrderList (state, data) {
      state.stateGetDetailPartnerOrderList = data
    },
    mutationGetBillDetail (state, data) {
      state.stateGetBillDetail = data
    },
    mutationGetBillingApproval (state, data) {
      state.stateGetBillingApproval = data
    },
    mutationCancelFunction (state, data) {
      state.stateCancelFunction = data
    },
    mutationchangeStatusPackage (state, data) {
      state.statechangeStatusPackage = data
    },
    mutationRanksPackage (state, data) {
      state.stateRanksPackage = data
    },
    mutationRanksShopBuying (state, data) {
      state.stateRanksShopBuying = data
    },
    mutationListOrderPackage (state, data) {
      state.stateListOrderPackage = data
    },
    mutationGetDashboardPartnerDistributeIncome (state, data) {
      state.stateGetDashboardPartnerDistributeIncome = data
    },
    mutationGetDashboardPartnerOrderListIncome (state, data) {
      state.stateGetDashboardPartnerOrderListIncome = data
    },
    mutationGetTransfer (state, data) {
      state.stateGetTransfer = data
    },
    mutationPartnerEtaxDocumentInvoice (state, data) {
      state.statePartnerEtaxDocumentInvoice = data
    },
    mutationPartnerEtaxDocumentReceipt (state, data) {
      state.statePartnerEtaxDocumentReceipt = data
    },
    mutationPartnerChartIncome (state, data) {
      state.statePartnerChartIncome = data
    },
    mutationCheckBusiness (state, data) {
      state.stateCheckBusiness = data
    },
    mutationRegisterShop (state, data) {
      state.stateRegisterShop = data
    }
  },
  actions: {
    async actionCreateBusiness (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosCreateBusiness(access)
      await context.commit('mutationCreateBusiness', dataFromAxios)
    },
    async actionDetailBusiness (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosDetailBusiness(access)
      await context.commit('mutationDetailBusiness', dataFromAxios)
    },
    async actionFindTaxIDByRevenue (context, access) {
      // console.log(access)
      const dataFromAxios = await AxiosBusiness.axiosFindTaxIDByRevenue(access)
      await context.commit('mutationFindTaxIDByRevenue', dataFromAxios)
    },
    async actionFindTaxIDByOneID (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosFindTaxIDByOneID(access)
      await context.commit('mutationFindTaxIDByOneID', dataFromAxios)
    },
    async actionUploadApprover (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosUploadApprover(access)
      await context.commit('mutationUploadApprover', dataFromAxios)
    },
    async actionUploadResultDocument (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosUploadResultDocument(access)
      await context.commit('mutationUploadResultDocument', dataFromAxios)
    },
    async actionsCheckeKYC (context) {
      const dataFromAxios = await AxiosBusiness.axiosDetailBusinessOfUser()
      await context.commit('mutationCheckeKYC', dataFromAxios)
    },
    async actionsCreateBusinessOther (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosCreateBusinessOther(access)
      await context.commit('mutationCreateBusinessOther', dataFromAxios)
    },
    async actionsManageUser (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosManageUser(access)
      await context.commit('mutationManageUser', dataFromAxios)
    },
    async actionsManageCompany (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosManageCompany(access)
      await context.commit('mutationManageCompany', dataFromAxios)
    },
    async actionsManageShop (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosManageShop(access)
      await context.commit('mutationManageShop', dataFromAxios)
    },
    async actionsListPositions (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosListPositions(access)
      await context.commit('mutationListPositions', dataFromAxios)
    },
    async actionsAssignPositionsUser (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosAssignPositionsUser(access)
      await context.commit('mutationAssignPositionsUser', dataFromAxios)
    },
    async actionsSearchUser (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosSearchUser(access)
      await context.commit('mutationSearchUser', dataFromAxios)
    },
    async actionsOrderDetailCompany (context, access) {
      const dataFromAxios = await AxiosBusiness.orderDetailCompany(access)
      await context.commit('mutationOrderDetailCompany', dataFromAxios)
    },
    async actionsListBusinessOrders (context, access) {
      const dataFromAxios = await AxiosBusiness.listBusinessOrders(access)
      await context.commit('mutationListBusinessOrders', dataFromAxios)
    },
    async actionsListCompany (context, access) {
      const dataFromAxios = await AxiosBusiness.listCompany(access)
      await context.commit('mutationListCompany', dataFromAxios)
    },
    async actionsListUsersCompany (context, access) {
      const dataFromAxios = await AxiosBusiness.ListUsersCompany(access)
      await context.commit('mutationListUsersCompany', dataFromAxios)
    },
    async actionsListUsersShops (context, access) {
      const dataFromAxios = await AxiosBusiness.ListUsersShops(access)
      await context.commit('mutationListUsersShops', dataFromAxios)
    },
    async actionsListCompanyPositions (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosListCompany(access)
      await context.commit('mutationListCompanyPositions', dataFromAxios)
    },
    async actionsListShopPositions (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosListShop(access)
      await context.commit('mutationListShopPositions', dataFromAxios)
    },
    async actionsCreateRole (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosCreateRole(access)
      await context.commit('mutationCreateRole', dataFromAxios)
    },
    async actionsRoleUpdate (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosRoleUpdate(access)
      await context.commit('mutationRoleUpdate', dataFromAxios)
    },
    async actionsEditRole (context, access) {
      const dataFromAxios = await AxiosBusiness.axiosEditRole(access)
      await context.commit('mutationEditRole', dataFromAxios)
    },
    async actionsCreateSellerShopNewBranch (context, access) {
      const dataFromAxios = await AxiosBusiness.CreateSellerShopNewBranch(access)
      await context.commit('mutationCreateSellerShopNewBranch', dataFromAxios)
    },
    async actionsUpdateOwnerPosition (context, access) {
      const dataFromAxios = await AxiosBusiness.UpdateOwnerPosition(access)
      await context.commit('mutationUpdateOwnerPosition', dataFromAxios)
    },
    async actionsCheckUser (context, access) {
      const dataFromAxios = await AxiosBusiness.CheckUser(access)
      await context.commit('mutationCheckUser', dataFromAxios)
    },
    async actionsGetPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPackage(access)
      await context.commit('mutationGetPackage', dataFromAxios)
    },
    async actionsCreatePackage (context, access) {
      const dataFromAxios = await AxiosBusiness.CreatePackage(access)
      await context.commit('mutationCreatePackage', dataFromAxios)
    },
    async actionsRegisterBusiness (context, access) {
      const dataFromAxios = await AxiosBusiness.RegisterBusiness(access)
      await context.commit('mutationRegisterBusiness', dataFromAxios)
    },
    async actionsRegisterPartner (context, access) {
      const dataFromAxios = await AxiosBusiness.RegisterPartner(access)
      await context.commit('mutationRegisterPartner', dataFromAxios)
    },
    async actionsGetDetailPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.GetDetailPackage(access)
      await context.commit('mutationGetDetailPackage', dataFromAxios)
    },
    async actionsGetPartnerDetails (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPartnerDetails(access)
      await context.commit('mutationGetPartnerDetails', dataFromAxios)
    },
    async actionsGetPartnerCode (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPartnerCode(access)
      await context.commit('mutationGetPartnerCode', dataFromAxios)
    },
    async actionsGetPartnerList (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPartnerList(access)
      await context.commit('mutationGetPartnerList', dataFromAxios)
    },
    async actionsEditPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.EditPackage(access)
      await context.commit('mutationEditPackage', dataFromAxios)
    },
    async actionsCancelPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.CancelPackage(access)
      await context.commit('mutationCancelPackage', dataFromAxios)
    },
    async actionsPartnerService (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPartnerService(access)
      await context.commit('mutationPartnerService', dataFromAxios)
    },
    async actionsPartnerOrderList (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPartnerOrderList(access)
      await context.commit('mutationPartnerOrderList', dataFromAxios)
    },
    async actionsPartnerEdit (context, access) {
      const dataFromAxios = await AxiosBusiness.PartnerEdit(access)
      await context.commit('mutationPartnerEdit', dataFromAxios)
    },
    async actionsGetBillList (context, access) {
      const dataFromAxios = await AxiosBusiness.GetBillList(access)
      await context.commit('mutationGetBillList', dataFromAxios)
    },
    async actionsPartnerOrderListV2 (context, access) {
      const dataFromAxios = await AxiosBusiness.GetPartnerOrderListV2(access)
      await context.commit('mutationPartnerOrderListV2', dataFromAxios)
    },
    async actionsGetDetailPartnerOrderList (context, access) {
      const dataFromAxios = await AxiosBusiness.GetDetailPartnerOrderList(access)
      await context.commit('mutationGetDetailPartnerOrderList', dataFromAxios)
    },
    async actionsGetBillDetail (context, access) {
      const dataFromAxios = await AxiosBusiness.GetBillDetail(access)
      await context.commit('mutationGetBillDetail', dataFromAxios)
    },
    async actionsGetBillingApproval (context, access) {
      const dataFromAxios = await AxiosBusiness.GetBillingApproval(access)
      await context.commit('mutationGetBillingApproval', dataFromAxios)
    },
    async actionsCancelFunction (context, access) {
      const dataFromAxios = await AxiosBusiness.CancelFunction(access)
      await context.commit('mutationCancelFunction', dataFromAxios)
    },
    async actionschangeStatusPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.changeStatusPackage(access)
      await context.commit('mutationchangeStatusPackage', dataFromAxios)
    },
    async actionsRanksPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.RanksPackage(access)
      await context.commit('mutationRanksPackage', dataFromAxios)
    },
    async actionsRanksShopBuying (context, access) {
      const dataFromAxios = await AxiosBusiness.RanksShopBuying(access)
      await context.commit('mutationRanksShopBuying', dataFromAxios)
    },
    async actionsListOrderPackage (context, access) {
      const dataFromAxios = await AxiosBusiness.ListOrderPackage(access)
      await context.commit('mutationListOrderPackage', dataFromAxios)
    },
    async actionsGetDashboardPartnerDistributeIncome (context, access) {
      const dataFromAxios = await AxiosBusiness.GetDashboardPartnerDistributeIncome(access)
      await context.commit('mutationGetDashboardPartnerDistributeIncome', dataFromAxios)
    },
    async actionsGetDashboardPartnerOrderListIncome (context, access) {
      const dataFromAxios = await AxiosBusiness.GetDashboardPartnerOrderListIncome(access)
      await context.commit('mutationGetDashboardPartnerOrderListIncome', dataFromAxios)
    },
    async actionsGetTransfer (context, access) {
      const dataFromAxios = await AxiosBusiness.GetTransfer(access)
      await context.commit('mutationGetTransfer', dataFromAxios)
    },
    async actionsPartnerEtaxDocumentInvoice (context, access) {
      const dataFromAxios = await AxiosBusiness.PartnerEtaxDocumentInvoice(access)
      await context.commit('mutationPartnerEtaxDocumentInvoice', dataFromAxios)
    },
    async actionsPartnerEtaxDocumentReceipt (context, access) {
      const dataFromAxios = await AxiosBusiness.PartnerEtaxDocumentReceipt(access)
      await context.commit('mutationPartnerEtaxDocumentReceipt', dataFromAxios)
    },
    async actionsPartnerChartIncome (context, access) {
      const dataFromAxios = await AxiosBusiness.PartnerChartIncome(access)
      await context.commit('mutationPartnerChartIncome', dataFromAxios)
    },
    async actionsCheckBusiness (context, access) {
      const dataFromAxios = await AxiosBusiness.CheckBusiness(access)
      await context.commit('mutationCheckBusiness', dataFromAxios)
    },
    async actionsRegisterShop (context, access) {
      const dataFromAxios = await AxiosBusiness.RegisterShop(access)
      await context.commit('mutationRegisterShop', dataFromAxios)
    }
  }
}

export default ModuleBusiness
