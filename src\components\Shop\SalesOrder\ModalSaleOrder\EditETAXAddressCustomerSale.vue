<template>
  <div>
    <v-dialog v-model="EditaddressDialog" width="918" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" :class="MobileSize ? 'pt-6' : 'pt-6'">
                <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{title}}</b></span>
              </v-col>
              <v-btn v-if="actions === 'addAddress' || actions === 'edit'" fab small @click="reset()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
              <v-btn v-else fab small @click="cancel()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
        <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
          <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
            <v-col style="text-align: center;">
            </v-col>
          </v-row>
        </div>
        <div class="backgroundContent" style="position: relative;">
        <v-container class="pa-0">
          <!-- Address Desktop, IpadPro, Ipad -->
          <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
          <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
          <v-card-text v-if="!MobileSize" >
            <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="30" height="30"></v-img>
            <span style="font-size: 20px; font-weight: 700;">ข้อมูลเบื้องต้น</span>
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters dense>
                <v-col cols="12" v-if="title !== 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'">
                  <!-- <v-row class="pt-2" no-gutters v-if="SaleVendor === true">
                    <v-radio-group v-model="taxRoles" row>
                        <v-radio
                          color="#27AB9C"
                          label="นิติบุคคล"
                          value="Business"
                        ></v-radio>
                        <v-radio
                          color="#27AB9C"
                          label="บุคคลธรรมดา"
                          value="Personal"
                        ></v-radio>
                      </v-radio-group>
                  </v-row> -->
                  <v-row dense no-gutters class="pt-2">
                    <v-checkbox v-model="sameAddressCustomer" @change="sameAddressChange()" label="ใช้ที่อยู่เดียวกับที่อยู่ในการจัดส่งสินค้า"/>
                    <!-- <span>ใช้ที่อยู่เดียวกับที่อยู่ในการจัดส่งสินค้า</span> -->
                  </v-row>
                </v-col>
                <v-col cols="12" v-else>
                  <!-- <v-row class="pt-2" no-gutters v-if="SaleVendor === true">
                    <v-radio-group v-model="taxRoles" row>
                        <v-radio
                          color="#27AB9C"
                          label="นิติบุคคล"
                          value="Business"
                        ></v-radio>
                        <v-radio
                          color="#27AB9C"
                          label="บุคคลธรรมดา"
                          value="Personal"
                        ></v-radio>
                      </v-radio-group>
                  </v-row> -->
                </v-col>
                <v-col cols="12" class="mt-6">
                  <span>ชื่อที่ใช้ในการออกใบกำกับภาษี<span style="color: red;">*</span></span>
                  <!-- <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="name" :rules="Rules.name" counter="50" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^A-Za-zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="name" :rules="Rules.name" counter="100" ></v-text-field>
                </v-col>
                <!-- <v-col cols="6 mt-6" >
                  <span>นามสกุล<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="ระบุนามสกุล" outlined dense v-model="last_name" :rules="Rules.last_name" counter="20" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col> -->
                <v-col cols="6" class="pr-2">
                  <span>เลขประจำตัวผู้เสียภาษี<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="เลขประจำตัวผู้เสียภาษี" outlined dense v-model="taxID" :rules="Rules.taxID" @input="validateTaxID()" counter="13" maxlength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="6" class="mb-6">
                  <span>เบอร์โทรศัพท์<span style="color: red;">*</span></span>
                  <v-text-field v-if="SaleVendor === false" class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.tel" counter="10" maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  <!-- <v-text-field v-else class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.telVendor" maxlength="40" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  <v-text-field v-else class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.telVendor" maxlength="40"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                  <span style="font-size: 20px; font-weight: 700;">ข้อมูลในการออกใบกำกับภาษี</span>
                </v-col>
                <!-- <v-col cols="4" class="pr-5 mt-6">
                  <span>บัตรประชาชน<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="ระบุเลขบัตรประชาชน" outlined dense v-model="idCard" :rules="Rules.idCard" maxlength="13" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col> -->
                <v-col cols="4" class="pr-5 mt-6">
                  <span>email<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="ระบุ email" outlined dense v-model="email" :rules="Rules.email" oninput="this.value = this.value.replace(/[^0-9a-zA-Z@-.-.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5 mt-6">
                  <span>เลขที่<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="ระบุเลขที่อยู่" outlined dense v-model="house_no" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5 mt-6">
                  <span>ห้องเลขที่</span>
                  <v-text-field class="input_text" placeholder="ระบุเลขห้อง" outlined dense v-model="room_no" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12"></v-col>
                <v-col cols="4" class="pr-5">
                  <span>ชั้นที่</span>
                  <v-text-field class="input_text" placeholder="ระบุชั้น" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>อาคาร</span>
                  <v-text-field class="input_text" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" outlined dense v-model="building_name" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>หมู่บ้าน</span>
                  <v-text-field class="input_text" placeholder="ชื่อหมู่บ้าน" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense v-model="moo_ban" :rules="Rules.maxText"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>หมู่ที่</span>
                  <v-text-field class="input_text" placeholder="ระบุหมู่" outlined dense v-model="moo_no" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>ตรอก/ซอย</span>
                  <v-text-field class="input_text" placeholder="ระบุตรอก,ซอย" outlined dense v-model="soi" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>แยก</span>
                  <v-text-field class="input_text" placeholder="ระบุแยก" outlined dense v-model="yaek" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>ถนน</span>
                  <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12"></v-col>
                <v-col cols="6" class="pr-5">
                  <span>แขวง/ตำบล<span style="color: red;">*</span></span>
                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <span>เขต/อำเภอ<span style="color: red;">*</span></span>
                  <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <!-- <v-col cols="3">
                  <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street" :rules="Rules.maxText"></v-text-field>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col> -->
                <v-col cols="6" class="pr-5">
                  <span>จังหวัด<span style="color: red;">*</span></span>
                  <addressinput-province label="" v-model="province" placeholder="ระบุจังหวัด" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <span>รหัสไปรษณีย์<span style="color: red;">*</span></span>
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <!-- <v-col cols="6" class="pr-5">
                  <addressinput-province label="" v-model="province" placeholder="ระบุจังหวัด" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="6">
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col> -->
              </v-row>
              <!-- <v-row v-if="page === 'checkout' && title === 'เพิ่มที่อยู่จัดส่งสินค้า'">
                <v-col>
                  <v-checkbox dense v-model="defaultAdress" label="ตั้งค่าเป็นที่อยู่เริ่มต้น" value="Y" ></v-checkbox>
                </v-col>
              </v-row> -->
            </v-form>
          </v-card-text>
          <!-- Address Mobile -->
          <v-card-text v-if="MobileSize" class="pa-0">
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="12" class="mb-2">
                  <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="24" height="24"></v-img>
                  <span style="font-size: 18px; font-weight: 700;">ข้อมูลเบื้องต้น</span>
                </v-col>
                <v-col cols="12" v-if="title !== 'แก้ไขที่อยู่ในการออกใบกำกับภาษี' && this.actions !== 'addAddress'">
                  <!-- <v-row class="pt-2" no-gutters v-if="SaleVendor === true">
                    <v-radio-group v-model="taxRoles" row>
                        <v-radio
                          color="#27AB9C"
                          label="นิติบุคคล"
                          value="Business"
                        ></v-radio>
                        <v-radio
                          color="#27AB9C"
                          label="บุคคลธรรมดา"
                          value="Personal"
                        ></v-radio>
                      </v-radio-group>
                  </v-row> -->
                  <v-row no-gutters>
                  <v-checkbox v-model="sameAddressCustomer" @change="sameAddressChange()"/>
                  <span class="pt-5">ใช้ที่อยู่เดียวกับที่อยู่ในการจัดส่งสินค้า</span>
                  </v-row>
                </v-col>
                <v-col cols="12">
                  <span>ชื่อที่ใช้ในการออกใบกำกับภาษี<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="name" :rules="Rules.name" counter="100"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>เลขประจำตัวผู้เสียภาษี<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="เลขประจำตัวผู้เสียภาษี" outlined dense v-model="taxID" :rules="Rules.taxID" counter="13" maxlength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>เบอร์โทรศัพท์<span style="color: red;">*</span></span>
                  <v-text-field v-if="SaleVendor === true" class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.tel" maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  <v-text-field v-else class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.telVendor" maxlength="40"></v-text-field>
                </v-col>
                <v-col cols="12" class="mb-4">
                  <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                  <span style="font-size: 18px; font-weight: 700;">ข้อมูลในการออกใบกำกับภาษี</span>
                </v-col>
                <v-col cols="12">
                  <span>email<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" placeholder="ระบุ email" outlined dense v-model="email" :rules="Rules.email" oninput="this.value = this.value.replace(/[^0-9a-zA-Z@-.-.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>เลขที่<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุเลขที่อยู่" outlined dense v-model="house_no" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12" >
                  <span>ห้องเลขที่</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุเลขห้อง" outlined dense v-model="room_no" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>ชั้นที่</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุชั้น" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12"></v-col>
                <v-col cols="12">
                  <span>อาคาร</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" outlined dense v-model="building_name" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>หมู่บ้าน</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ชื่อหมู่บ้าน" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense v-model="moo_ban" :rules="Rules.maxText"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>หมู่ที่</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุหมู่" outlined dense v-model="moo_no" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>ตรอก/ซอย</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุตรอก,ซอย" outlined dense v-model="soi" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>แยก</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ระบุแยก" outlined dense v-model="yaek" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>ถนน</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>แขวง/ตำบล<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12">
                  <span>เขต/อำเภอ<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12">
                  <span>จังหวัด<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-province label="" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                  <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12">
                  <span>รหัสไปรษณีย์<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-zipcode label="" v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                  <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
              <!-- <v-row v-if="page === 'checkout' && title === 'เพิ่มที่อยู่จัดส่งสินค้า'">
                <v-col>
                  <v-checkbox dense v-model="defaultAdress" label="ตั้งค่าเป็นที่อยู่เริ่มต้น" value="Y" ></v-checkbox>
                </v-col>
              </v-row> -->
            </v-form>
          </v-card-text>
          </div>
          </v-card>
        </v-container>
        </div>
        </v-card-text>
          <v-card-actions v-if="!MobileSize" class="px-12" style="height: 88px; background-color: #F5FCFB;">
            <!-- <v-row :justify="!MobileSize ? 'end' : 'center'">
              <v-btn class="px-5 mr-2" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'addressProfile' ? createAddresss() : title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'checkout' ? updateAddress() : updateAddress()">บันทึก</v-btn>
            </v-row> -->
          <v-btn v-if="actions === 'addAddress' || actions === 'edit'" class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="reset()">ยกเลิก</v-btn>
          <v-btn v-else class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">ย้อนกลับ</v-btn>
          <v-spacer></v-spacer>
          <v-btn class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">บันทึก</v-btn>
          </v-card-actions>
          <v-card-actions v-if="MobileSize" class="px-4" style="height: 88px; background-color: #F5FCFB;">
            <!-- <v-row :justify="!MobileSize ? 'end' : 'center'">
              <v-btn class="px-5 mr-2" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'addressProfile' ? createAddresss() : title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'checkout' ? updateAddress() : updateAddress()">บันทึก</v-btn>
            </v-row> -->
          <v-btn v-if="actions !== 'addAddress'" class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="reset()">ยกเลิก</v-btn>
          <v-btn v-else class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">ย้อนกลับ</v-btn>
          <!-- <v-btn class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">ย้อนกลับ</v-btn> -->
          <v-spacer></v-spacer>
          <v-btn class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">บันทึก</v-btn>
          </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? 'เพิ่มข้อมูลลูกค้า' : 'แก้ไขข้อมูลลูกค้า' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text v-if="!MobileSize">
            <v-row  dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? createAddresss() : updateAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
          <v-card-text v-if="MobileSize">
            <v-row dense justify="space-between">
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogAwaitConfirm = !dialogAwaitConfirm">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? createAddresss() : updateAddress()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogSuccess = !dialogSuccess"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? 'เพิ่มข้อมูลลูกค้าเสร็จสิ้น' : 'แก้ไขข้อมูลลูกค้าเสร็จสิ้น' }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' ? 'คุณได้ทำการเพิ่มข้อมูลลูกค้าเรียบร้อย' : 'คุณได้ทำการแก้ไขข้อมูลลูกค้าเรียบร้อย' }}</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogSuccess = !dialogSuccess">ตกลง</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogSuccess = !dialogSuccess">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode, Encode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  // props: [],
  data () {
    return {
      dataIsVendor: false,
      SaleVendor: false,
      taxRoles: 'Personal',
      cusID: '',
      cusCode: '',
      addAddressForSameAddress: '',
      phone: '',
      sameAddress: 'N',
      title: '',
      itemCustomer: [],
      customerData: [],
      actions: '',
      email: '',
      idCard: '',
      sameAddressCustomer: false,
      userDetail: [],
      dialogSuccess: false,
      dialogAwaitConfirm: false,
      defaultAdress: false,
      dataEditAddress: [],
      data: [],
      lazy: false,
      name: '',
      last_name: '',
      first_name: '',
      taxID: '',
      detail: '',
      EditaddressDialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      house_no: '',
      room_no: '',
      floor: '',
      building_name: '',
      moo_ban: '',
      moo_no: '',
      soi: '',
      yaek: '',
      street: '',
      id: '',
      default_address: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        telVendor: [
          v => !!v || 'กรุณาเบอร์โทรศัพท์',
          v => v.length >= 9 || 'กรุณากรอกเลขมากกว่า 9 ตัว'
        ],
        idCard: [
          v => !!v || 'กรุณากรอกเลขบัตรประชาชน',
          v => v.length <= 13 || 'กรอกเลขได้ไม่เกิน 13 ตัว'
        ],
        name: [
          v => !!v || 'กรุณากรอกชื่อ',
          v => v.length <= 100 || 'กรอกได้ไม่เกิน 100 ตัวอักษร'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => v.charAt(0) === '0' || 'เบอร์โทรศัพท์ควรขึ้นต้นด้วยเลข 0',
          v => v.charAt(1) !== '0' || 'กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง',
          v => v.length === 10 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 10 หลัก'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ',
          v => v.length <= 20 || 'กรอกได้ไม่เกิน 20 ตัวอักษร'
        ],
        taxID: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 หลัก',
          v => this.validNationalID(v) || 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
        ],
        email: [
          v => !!v || 'กรุณากรอกemail',
          // v => (/^[a-zA-Zd0-9.-.]+@[a-zA-Z.-.d-]+\.[a-zA-Z-]{2,}$/.test(v) || v.length === 0) || 'กรุณากรอก email ให้ถูกต้อง'
          v => (/^\w+([.-]?\w+)@\w+([.-]?\w+)+\.(.\w{2,3})*\w+([.-]?\w{2,3})+$/.test(v) || v.length === 0) || 'กรุณากรอก email ให้ถูกต้อง'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่',
          v => v.charAt(0) !== '-' || 'กรุณากรอกข้อมูลให้ถูกต้อง',
          v => (v.split('').filter(char => char === '-').length <= 1) || 'ระบุข้อมูลไม่ถูกต้อง',
          v => (/^[-0-9,-/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9,-/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        maxText: [
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร'
        ]
        // room_no: [
        //   v => !!v || 'กรุณาระบุห้องเลขที่'
        // ],
        // floor: [
        //   v => !!v || 'กรุณาระบุชั้นที่'
        // ],
        // building_name: [
        //   v => !!v || 'กรุณาระบุอาคาร'
        // ],
        // moo_ban: [
        //   v => !!v || 'กรุณาระบุหมู่บ้าน'
        // ],
        // soi: [
        //   v => !!v || 'กรุณาระบุตรอก/ซอย'
        // ],
        // yaek: [
        //   v => !!v || 'กรุณาระบุแยก'
        // ],
        // street: [
        //   v => !!v || 'กรุณาระบุถนน'
        // ]
      }
    }
  },
  watch: {
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  mounted () {
    this.$EventBus.$on('itemEdit', this.itemEdit)
    this.$EventBus.$on('EditModalEtaxAddress', this.getAddressData)
    this.$EventBus.$on('actionSale', this.actionSale)
    this.$EventBus.$on('clearData', this.clearData)
    this.$EventBus.$on('getCustomDetail', this.getCustomDetail)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('ChackRowUser')
      this.$EventBus.$off('itemEdit')
      this.$EventBus.$off('getCustomDetail')
      this.$EventBus.$off('clearData')
      this.$EventBus.$off('EditModalEtaxAddress')
      this.$EventBus.$off('actionSale')
    })
  },
  // beforeDestroy () {
  //   this.$EventBus.$off('itemEdit')
  // },
  // created () {
  //   this.$EventBus.$on('getCustomDetail', (cusID) => {
  //     this.getCustomDetail(cusID)
  //   })
  // },
  // destroyed () {
  //   this.$EventBus.$off('getCustomDetail')
  // },
  // beforeDestroy () {
  //   this.$EventBus.$off('EditModalEtaxAddress')
  // },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    itemEdit (data) {
      this.dataIsVendor = data.is_vendor === 'yes'
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateTaxID () {
      if (this.validNationalID(this.taxID)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    clearData () {
      this.sameAddressCustomer = false
      this.addAddressForSameAddress = ''
      this.email = ''
      this.first_name = ''
      this.last_name = ''
      this.name = ''
      this.phone = ''
      this.taxID = ''
      this.house_no = ''
      this.room_no = ''
      this.floor = ''
      this.building_name = ''
      this.moo_ban = ''
      this.moo_no = ''
      this.soi = ''
      this.yaek = ''
      this.street = ''
      this.subdistrict = ''
      this.district = ''
      this.taxRoles = ''
      this.province = ''
      this.zipcode = ''
    },
    reset () {
      // console.log('reste')
      // this.sameAddressCustomer = false
      // this.email = ''
      // this.first_name = ''
      // this.last_name = ''
      // this.name = ''
      // this.phone = ''
      // this.taxID = ''
      // this.house_no = ''
      // this.room_no = ''
      // this.floor = ''
      // this.building_name = ''
      // this.moo_ban = ''
      // this.moo_no = ''
      // this.soi = ''
      // this.yaek = ''
      // this.street = ''
      // this.subdistrict = ''
      // this.district = ''
      // this.province = ''
      // this.zipcode = ''
      this.clearData()
      if (this.actions === 'addAddress' || this.actions === 'edit') {
        this.EditaddressDialog = false
      }
    },
    cancel () {
      var val = {
        email: this.email,
        first_name: this.first_name === null ? '' : this.first_name,
        last_name: this.last_name === null ? '' : this.last_name,
        name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
        phone: this.phone,
        tax_id: this.taxID,
        house_no: this.house_no,
        room_no: this.room_no,
        floor: this.floor,
        building: this.building_name,
        moo_ban: this.moo_ban,
        moo_no: this.moo_no,
        soi: this.soi,
        yaek: this.yaek,
        street: this.street,
        sub_district: this.subdistrict,
        // tax_type: this.taxRoles,
        district: this.district,
        province: this.province,
        postcode: this.zipcode
      }
      // console.log('cancel', val)
      localStorage.setItem('invoiceBackward', Encode.encode(val))
      this.EditaddressDialog = false
      // this.sameAddressCustomer = false
      // this.email = ''
      // this.first_name = ''
      // this.last_name = ''
      // this.name = ''
      // this.phone = ''
      // this.taxID = ''
      // this.house_no = ''
      // this.room_no = ''
      // this.floor = ''
      // this.building_name = ''
      // this.moo_ban = ''
      // this.moo_no = ''
      // this.soi = ''
      // this.yaek = ''
      // this.street = ''
      // this.subdistrict = ''
      // this.district = ''
      // this.province = ''
      this.zipcode = ''
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    actionSale (action) {
      this.actions = action
    },
    async open (val, title, page, actions, saleVendor) {
      // console.log('page', page)
      // console.log('val', val)
      // console.log('saleVendoropentex', saleVendor)
      if (saleVendor !== undefined) {
        if (this.dataIsVendor === true) {
          this.SaleVendor = this.dataIsVendor
        } else {
          this.SaleVendor = saleVendor
        }
      } else {
        this.SaleVendor = false
      }
      // console.log('SaleVendor222', this.SaleVendor)
      this.itemCustomer = val
      this.title = title
      this.page = page
      this.actions = actions
      if (val) {
        this.customerData = val
      }
      if (this.title !== undefined && this.title !== '') {
        var dataInvoice
        if (this.title === 'เพิ่มที่อยู่ในการออกใบกำกับภาษี' && this.sameAddressCustomer === false) {
          if (localStorage.getItem('invoiceBackward') !== null) {
            // console.log('กรณีกดย้อนกลับ')
            dataInvoice = JSON.parse(Decode.decode(localStorage.getItem('invoiceBackward')))
            this.name = dataInvoice.name
            this.email = dataInvoice.email
            this.house_no = dataInvoice.house_no
            this.moo_ban = dataInvoice.moo_ban
            this.building_name = dataInvoice.building
            this.street = dataInvoice.street
            this.soi = dataInvoice.soi
            this.room_no = dataInvoice.room_no
            this.floor = dataInvoice.floor
            this.moo_no = dataInvoice.moo_no
            this.yaek = dataInvoice.yaek
            this.subdistrict = dataInvoice.sub_district
            this.district = dataInvoice.district
            this.province = dataInvoice.province
            this.phone = dataInvoice.phone
            this.zipcode = dataInvoice.postcode
            this.taxID = dataInvoice.tax_id
            // this.taxRoles = dataInvoice.tax_type
            this.default_address = dataInvoice.default_address
            this.EditaddressDialog = true
          } else {
            // console.log('กรณีไม่ใช้ที่อยู่เดียวกับที่อยู่จัดส่งสินค้า')
            // this.$refs.FormAddress.resetValidation()
            // this.name = ''
            this.last_name = ''
            this.house_no = ''
            this.moo_ban = ''
            this.building_name = ''
            this.street = ''
            this.soi = ''
            this.room_no = ''
            this.floor = ''
            this.moo_no = ''
            this.yaek = ''
            this.subdistrict = ''
            this.district = ''
            this.province = ''
            this.phone = ''
            this.zipcode = ''
            this.id = ''
            this.default_address = 'Y'
            this.EditaddressDialog = true
            this.$refs.FormAddress.resetValidation()
          }
        } else if (this.title === 'แก้ไขที่อยู่ในการออกใบกำกับภาษี' && this.sameAddressCustomer === false) {
          if (this.SaleVendor === true) {
            dataInvoice = val
            this.name = dataInvoice.name
            this.email = dataInvoice.email
            this.house_no = dataInvoice.house_no
            this.moo_ban = dataInvoice.moo_ban
            this.building_name = dataInvoice.building
            this.street = dataInvoice.street
            this.soi = dataInvoice.soi
            this.room_no = dataInvoice.room_no
            this.floor = dataInvoice.floor
            this.moo_no = dataInvoice.moo_no
            this.yaek = dataInvoice.yaek
            this.subdistrict = dataInvoice.sub_district
            this.district = dataInvoice.district
            this.province = dataInvoice.province
            this.phone = dataInvoice.phone
            this.zipcode = dataInvoice.postcode
            this.taxID = dataInvoice.tax_id
            this.default_address = dataInvoice.default_address
            this.EditaddressDialog = true
            this.taxRoles = dataInvoice.tax_type
          } else {
            dataInvoice = val
            this.name = dataInvoice.name
            this.email = dataInvoice.email
            this.house_no = dataInvoice.house_no
            this.moo_ban = dataInvoice.moo_ban
            this.building_name = dataInvoice.building
            this.street = dataInvoice.street
            this.soi = dataInvoice.soi
            this.room_no = dataInvoice.room_no
            this.floor = dataInvoice.floor
            this.moo_no = dataInvoice.moo_no
            this.yaek = dataInvoice.yaek
            this.subdistrict = dataInvoice.sub_district
            this.district = dataInvoice.district
            this.province = dataInvoice.province
            this.phone = dataInvoice.phone
            this.zipcode = dataInvoice.postcode
            this.taxID = dataInvoice.tax_id
            this.default_address = dataInvoice.default_address
            this.EditaddressDialog = true
          }
        } else {
          // console.log('กรณีใช้ที่อยู่เดียวกัน')
          if (localStorage.getItem('invoiceBackward') !== null && this.sameAddress === 'N') {
            // console.log('กดย้อนกลับ')
            dataInvoice = JSON.parse(Decode.decode(localStorage.getItem('invoiceBackward')))
            this.name = dataInvoice.name
            this.email = dataInvoice.email
            this.house_no = dataInvoice.house_no
            this.moo_ban = dataInvoice.moo_ban
            this.building_name = dataInvoice.building
            this.street = dataInvoice.street
            this.soi = dataInvoice.soi
            this.room_no = dataInvoice.room_no
            this.floor = dataInvoice.floor
            this.moo_no = dataInvoice.moo_no
            this.yaek = dataInvoice.yaek
            this.subdistrict = dataInvoice.sub_district
            this.district = dataInvoice.district
            this.province = dataInvoice.province
            this.phone = dataInvoice.phone
            this.zipcode = dataInvoice.postcode
            this.taxID = dataInvoice.tax_id
            // this.taxRoles = dataInvoice.tax_type
            this.default_address = dataInvoice.default_address
            this.EditaddressDialog = true
          } else if (this.addAddressForSameAddress !== '') {
            // console.log('this.addAddressForSameAddress เพิ่มที่อยู่ในการออกใบกำกับภาษี', this.addAddressForSameAddress)
            dataInvoice = this.addAddressForSameAddress
            this.name = dataInvoice.name
            this.email = dataInvoice.email
            this.house_no = dataInvoice.house_no
            this.moo_ban = dataInvoice.moo_ban
            this.building_name = dataInvoice.building
            this.street = dataInvoice.street
            this.soi = dataInvoice.soi
            this.room_no = dataInvoice.room_no
            this.floor = dataInvoice.floor
            this.moo_no = dataInvoice.moo_no
            this.yaek = dataInvoice.yaek
            this.subdistrict = dataInvoice.sub_district
            this.district = dataInvoice.district
            this.province = dataInvoice.province
            this.phone = dataInvoice.phone
            this.zipcode = dataInvoice.postcode
            this.taxID = dataInvoice.tax_id
            // this.taxRoles = dataInvoice.tax_type
            this.default_address = dataInvoice.default_address
            this.EditaddressDialog = true
          } else {
            dataInvoice = val.cus_address
            // console.log('dataInvoice', dataInvoice)
            // this.name = ''
            // this.email = ''
            // this.last_name = dataInvoice.last_name
            this.house_no = dataInvoice.house_no === '' ? '' : dataInvoice.house_no
            this.moo_ban = dataInvoice.moo_ban === '' ? '-' : dataInvoice.moo_ban
            this.building_name = dataInvoice.building === '' ? '-' : dataInvoice.building
            this.street = dataInvoice.street === '' ? '-' : dataInvoice.street
            this.soi = dataInvoice.soi === '' ? '-' : dataInvoice.soi
            this.room_no = dataInvoice.room_no === '' ? '-' : dataInvoice.room_no
            this.floor = dataInvoice.floor === '' ? '-' : dataInvoice.floor
            this.moo_no = dataInvoice.moo_no === '' ? '-' : dataInvoice.moo_no
            this.yaek = dataInvoice.yaek === '' ? '-' : dataInvoice.yaek
            this.subdistrict = dataInvoice.sub_district
            this.district = dataInvoice.district
            this.province = dataInvoice.province
            this.phone = dataInvoice.phone
            this.zipcode = dataInvoice.postcode
            this.id = dataInvoice.id
            // this.taxRoles = dataInvoice.tax_type
            this.default_address = dataInvoice.default_address
            this.EditaddressDialog = true
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    async createAddresss () {
      // console.log('createAddresss', this.SaleVendor)
      // this.$store.commit('openLoader')
      if (this.$refs.FormAddress.validate(true)) {
        if (this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode)) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            // var sellerShopID = localStorage.getItem('ShopID')
            var roleSale = JSON.parse(localStorage.getItem('roleUser')).role
            if (roleSale !== 'sale_order_no_JV') {
              roleSale = 'seller'
            }
            // var roleSale = 'sale_order_no_JV'
            // ฟิกกก
            // var roleSale = { role: 'sale_order_no_JV' }.role
            var roleCumtomerSale = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
            // var AddressCustomerDetail = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerDetail')))
            // console.log('AddressCustomerDetail', AddressCustomerDetail)
            var addData
            var data
            var val
            const check = this.checkAddress()
            var userAddAddress
            if (check.length !== 0) {
              // console.log('page', this.page, roleSale)
              // console.log('AddressCustomerSale', localStorage.getItem('AddressCustomerSale'))
              if (roleCumtomerSale !== 'general' && this.SaleVendor === false) {
                if (this.actions !== 'addAddress') {
                  // console.log('เพิ่มที่อยู่ใบกำกับลูกค้าบริษัท ** เพิ่มลูกค้า')
                  data = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerBussinessSale')))
                  val = {
                    email: this.email,
                    name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                    phone: this.phone,
                    tax_id: this.taxID,
                    house_no: this.house_no,
                    room_no: this.room_no,
                    floor: this.floor,
                    building: this.building_name,
                    moo_ban: this.moo_ban,
                    moo_no: this.moo_no,
                    soi: this.soi,
                    yaek: this.yaek,
                    street: this.street,
                    sub_district: this.subdistrict,
                    district: this.district,
                    province: this.province,
                    postcode: this.zipcode
                  }
                  data.invoice_address = val
                  addData = data
                  // console.log('addDataบริษัท', addData)
                  // userAddAddress = {
                  //   message: 'Create customer successfully.'
                  // }
                  // console.log('add', addData)
                  await this.$store.dispatch('actionsCreateCustomer', addData)
                  userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomer
                  // console.log('userAddAddressเพิ่มที่อยู่ใบกำกับลูกค้าบริษัท', userAddAddress)
                } else {
                  // เพิ่มที่แค่อยู่ใบกำกับภาษี ** บอ
                  addData = {
                    seller_shop_id: roleSale === 'seller' ? parseInt(localStorage.getItem('shopSellerID')) : parseInt(localStorage.getItem('ShopID')),
                    role: roleSale,
                    cus_id: this.customerData.cus_id,
                    email: this.email,
                    name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                    phone: this.phone,
                    tax_id: this.taxID,
                    house_no: this.house_no,
                    room_no: this.room_no,
                    floor: this.floor,
                    building: this.building_name,
                    moo_ban: this.moo_ban,
                    moo_no: this.moo_no,
                    soi: this.soi,
                    yaek: this.yaek,
                    street: this.street,
                    sub_district: this.subdistrict,
                    district: this.district,
                    province: this.province,
                    // tax_type: this.taxRoles,
                    postcode: this.zipcode
                  }
                  await this.$store.dispatch('actionsCreateCustomerInvAddress', addData)
                  userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomerInvAddress
                  // console.log('val====================>', addData)
                }
              } else if (roleCumtomerSale !== 'general' && this.SaleVendor === true) {
                if (this.actions !== 'addAddress') {
                  // console.log('เพิ่มที่อยู่ใบกำกับลูกค้าvendor ** เพิ่มลูกค้า')
                  data = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerBussinessSale')))
                  val = {
                    email: this.email,
                    name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                    phone: this.phone,
                    tax_id: this.taxID,
                    house_no: this.house_no,
                    room_no: this.room_no,
                    floor: this.floor,
                    building: this.building_name,
                    moo_ban: this.moo_ban,
                    moo_no: this.moo_no,
                    soi: this.soi,
                    yaek: this.yaek,
                    street: this.street,
                    sub_district: this.subdistrict,
                    district: this.district,
                    province: this.province,
                    postcode: this.zipcode
                    // tax_type: this.taxRoles
                  }
                  data.invoice_address = val
                  addData = data
                  // console.log('addDataบริษัท', addData)
                  // userAddAddress = {
                  //   message: 'Create customer successfully.'
                  // }
                  await this.$store.dispatch('actionsCreateCustomer', addData)
                  userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomer
                  // console.log('userAddAddressเพิ่มที่อยู่ใบกำกับลูกค้าบริษัท', userAddAddress)
                } else {
                  // เพิ่มที่แค่อยู่ใบกำกับภาษี ** vendor
                  addData = {
                    seller_shop_id: roleSale === 'seller' ? parseInt(localStorage.getItem('shopSellerID')) : parseInt(localStorage.getItem('ShopID')),
                    role: roleSale,
                    cus_id: this.customerData.cus_id,
                    email: this.email,
                    name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                    phone: this.phone,
                    tax_id: this.taxID,
                    house_no: this.house_no,
                    room_no: this.room_no,
                    floor: this.floor,
                    building: this.building_name,
                    moo_ban: this.moo_ban,
                    moo_no: this.moo_no,
                    soi: this.soi,
                    yaek: this.yaek,
                    street: this.street,
                    sub_district: this.subdistrict,
                    district: this.district,
                    province: this.province,
                    postcode: this.zipcode
                    // tax_type: this.taxRoles
                  }
                  await this.$store.dispatch('actionsCreateCustomerInvAddress', addData)
                  userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomerInvAddress
                  // console.log('val====================>', addData)
                }
              } else {
                // console.log('เพิ่มที่อยู่ใบกำกับลูกค้าทั่วไป')
                if (this.actions === 'addAddress') {
                  // console.log('1')
                  // console.log('เพิ่มแค่****ที่อยู่ใบกำกับลูกค้าทั่วไป', this.SaleVendor)
                  // data = JSON.parse(Decode.decode(localStorage.getItem('AddAddressCustomerSale')))
                  // console.log('data', Decode.decode(localStorage.getItem('AddAddressCustomerSale')))
                  if (this.SaleVendor === true) {
                    addData = {
                      seller_shop_id: roleSale === 'seller' ? parseInt(localStorage.getItem('shopSellerID')) : parseInt(localStorage.getItem('ShopID')),
                      role: roleSale,
                      cus_id: this.customerData.cus_id,
                      email: this.email,
                      name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                      tax_id: this.taxID,
                      phone: this.phone,
                      house_no: this.house_no,
                      room_no: this.room_no,
                      floor: this.floor,
                      building: this.building_name,
                      moo_ban: this.moo_ban,
                      moo_no: this.moo_no,
                      soi: this.soi,
                      yaek: this.yaek,
                      street: this.street,
                      sub_district: this.subdistrict,
                      district: this.district,
                      province: this.province,
                      postcode: this.zipcode
                    }
                  } else {
                    addData = {
                      seller_shop_id: roleSale === 'seller' ? parseInt(localStorage.getItem('shopSellerID')) : parseInt(localStorage.getItem('ShopID')),
                      role: roleSale,
                      cus_id: this.customerData.cus_id,
                      email: this.email,
                      name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                      tax_id: this.taxID,
                      phone: this.phone,
                      house_no: this.house_no,
                      room_no: this.room_no,
                      floor: this.floor,
                      building: this.building_name,
                      moo_ban: this.moo_ban,
                      moo_no: this.moo_no,
                      soi: this.soi,
                      yaek: this.yaek,
                      street: this.street,
                      sub_district: this.subdistrict,
                      district: this.district,
                      province: this.province,
                      postcode: this.zipcode
                    }
                  }
                  // userAddAddress = {
                  //   message: 'Create customer successfully.'
                  // }
                  await this.$store.dispatch('actionsCreateCustomerInvAddress', addData)
                  userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomerInvAddress
                  // console.log('userAddAddress', userAddAddress)
                } else {
                  // console.log('เพิ่มลูกค้าทั่วไป')
                  data = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerSale')))
                  // console.log('32222SaleVendor', this.SaleVendor)
                  if (this.SaleVendor === true) {
                    val = {
                      email: this.email,
                      first_name: '',
                      last_name: '',
                      name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                      phone: this.phone,
                      tax_id: this.taxID,
                      house_no: this.house_no,
                      room_no: this.room_no,
                      floor: this.floor,
                      building: this.building_name,
                      moo_ban: this.moo_ban,
                      moo_no: this.moo_no,
                      soi: this.soi,
                      yaek: this.yaek,
                      street: this.street,
                      sub_district: this.subdistrict,
                      district: this.district,
                      province: this.province,
                      postcode: this.zipcode
                      // tax_type: this.taxRoles
                    }
                  } else {
                    val = {
                      email: this.email,
                      first_name: '',
                      last_name: '',
                      name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                      phone: this.phone,
                      tax_id: this.taxID,
                      house_no: this.house_no,
                      room_no: this.room_no,
                      floor: this.floor,
                      building: this.building_name,
                      moo_ban: this.moo_ban,
                      moo_no: this.moo_no,
                      soi: this.soi,
                      yaek: this.yaek,
                      street: this.street,
                      sub_district: this.subdistrict,
                      district: this.district,
                      province: this.province,
                      postcode: this.zipcode
                    }
                  }
                  // console.log('AddressCustomerDetail=====>>', AddressCustomerDetail)
                  data.invoice_address = val
                  addData = data
                  // console.log('data=====>>', data)
                  // console.log('addData=====>>02', addData)
                  // console.log('AddressCustomerDetail02', AddressCustomerDetail)
                  // userAddAddress = {
                  //   message: 'Create customer address successfully.'
                  // }
                  // console.log('addData===>', addData)
                  await this.$store.dispatch('actionsCreateCustomer', addData)
                  userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomer
                  // console.log('userAddAddress', userAddAddress)
                }
              }
              // console.log('aaddData==================>', addData)
              // console.log('cus_id', userAddAddress.data.cus_id)
              if (userAddAddress.message === 'Create customer successfully.' || userAddAddress.message === 'Create customer address successfully.' || userAddAddress.message === 'Create customer invoice address success.') {
                if (this.actions === 'addAddress' || this.actions === 'edit') {
                  // console.log('this.actions', this.actions, addData.cus_id)
                  await this.$EventBus.$emit('EditComplete', addData.cus_id)
                  this.cusID = addData.cus_id
                  await this.getCustomDetail()
                } else {
                  if (this.page === 'shoppage') {
                    await this.$EventBus.$emit('getListCouponsistNGC')
                  }
                  await this.$EventBus.$emit('EditComplete', userAddAddress.data.cus_id, addData)
                  this.cusID = userAddAddress.data.cus_id
                  // console.log('this.cusID', this.cusID)
                  await this.getCustomDetail()
                  // console.log('serAddAddress.data.cus_id', serAddAddress.data.cus_id)
                }
                this.dialog = false
                this.EditaddressDialog = false
                this.dialogAwaitConfirm = false
                // this.$EventBus.$emit('SentGetCart', this.SentGetCart)
                localStorage.removeItem('AddressBackward')
                localStorage.removeItem('AddAddressCustomer')
                // localStorage.removeItem('AddressCustomerBussinessSale')
                // localStorage.removeItem('AddressCustomerSale')
                localStorage.removeItem('invoiceBackward')
                localStorage.setItem('partner_id', userAddAddress.data.cus_id)
                await this.$EventBus.$emit('EditAddressCustomerComplete', userAddAddress.data)
                await this.$EventBus.$emit('getListCustomerOfSales')
                await this.$EventBus.$emit('listInvoiceAddress')
                this.$store.commit('closeLoader')
                this.dialogSuccess = true
                this.$EventBus.$emit('clearData')
                // this.clearData()
                this.reset()
                // this.$store.commit('openLoader')
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
              } else if (userAddAddress.message === 'Customer code is already exists.') {
                this.$swal.fire({ icon: 'warning', title: '<h5>รหัสลูกค้านี้ถูกใช้งานซ้ำ กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
              } else {
                this.$swal.fire({ icon: 'warning', title: `${userAddAddress.message}`, showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    async updateAddress () {
      // console.log('editupdateAddress')
      this.$store.commit('openLoader')
      if (this.$refs.FormAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkAddress()
            if (check.length !== 0) {
              // ฟิก
              var role = JSON.parse(localStorage.getItem('roleUser')).role
              if (role !== 'sale_order_no_JV') {
                role = 'seller'
              }
              // var role = 'sale_order_no_JV'
              if (this.SaleVendor === true) {
                var data = {
                  seller_shop_id: role === 'seller' ? parseInt(localStorage.getItem('shopSellerID')) : parseInt(localStorage.getItem('ShopID')),
                  cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
                  name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                  invoice_address_id: this.customerData.invoice_address_id,
                  email: this.email,
                  first_name: '',
                  last_name: '',
                  house_no: this.house_no,
                  moo_ban: this.moo_ban,
                  building: this.building_name,
                  street: this.street,
                  soi: this.soi,
                  room_no: this.room_no,
                  floor: this.floor,
                  moo_no: this.moo_no,
                  yaek: this.yaek,
                  tax_id: this.taxID,
                  sub_district: this.subdistrict,
                  district: this.district,
                  province: this.province,
                  phone: this.phone,
                  postcode: this.zipcode,
                  default_address: this.default_address,
                  role: role
                  // tax_type: this.taxRoles
                }
              } else {
                data = {
                  seller_shop_id: role === 'seller' ? parseInt(localStorage.getItem('shopSellerID')) : parseInt(localStorage.getItem('ShopID')),
                  cus_id: this.customerData.cus_id === undefined ? this.customerData.id : this.customerData.cus_id,
                  name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                  invoice_address_id: this.customerData.invoice_address_id,
                  email: this.email,
                  first_name: '',
                  last_name: '',
                  house_no: this.house_no,
                  moo_ban: this.moo_ban,
                  building: this.building_name,
                  street: this.street,
                  soi: this.soi,
                  room_no: this.room_no,
                  floor: this.floor,
                  moo_no: this.moo_no,
                  yaek: this.yaek,
                  tax_id: this.taxID,
                  sub_district: this.subdistrict,
                  district: this.district,
                  province: this.province,
                  phone: this.phone,
                  postcode: this.zipcode,
                  default_address: this.default_address,
                  role: role
                }
              }
              // console.log('data', data)
              await this.$store.dispatch('actionsUpdateCustomerInvAddress', data)
              const userAddress = await this.$store.state.ModuleUser.stateUpdateCustomerInvAddress
              if (userAddress.message === 'Update customer invoice address success.') {
                // this.$swal.fire({ icon: 'success', title: 'แก้ไขที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
                // this.$EventBus.$emit('SentGetCart', this.SentGetCart)
                // console.log('userAddress', data.cus_id)
                this.cusID = data.cus_id
                await this.getCustomDetail()
                this.dialog = false
                this.EditaddressDialog = false
                await this.$EventBus.$emit('getListCustomerOfSales')
                await this.$EventBus.$emit('listInvoiceAddress')
                await this.$EventBus.$emit('EditComplete', data.cus_id)
                await this.$EventBus.$emit('EditAddressCustomerComplete', this.dataEditAddress)
                localStorage.removeItem('AddressData')
                this.$store.commit('closeLoader')
                this.dialogSuccess = true
                // this.$store.commit('openLoader')
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
              } else {
                this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    async getCustomDetail (id, sellershopID) {
      // console.log('this.actions', this.actions)
      if (id !== undefined && id !== null) {
        this.cusID = id
      }
      // console.log('this.cusID', this.cusID)
      var shopID
      // console.log('this.page', this.page)
      if (this.page !== undefined) {
        if (this.page === 'checkoutSaleOrder' || this.page === 'shoppage') {
          shopID = localStorage.getItem('ShopID')
        } else {
          shopID = localStorage.getItem('shopSellerID')
        }
      } else {
        shopID = sellershopID
      }
      // console.log('idgetCustomDetail', shopID)
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: shopID,
        cus_id: this.cusID === undefined || this.cusID === null || this.cusID === '' ? localStorage.getItem('partner_id') : this.cusID
      }
      var roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      // console.log('data', data)
      await this.$store.dispatch('actionsGetDetailCustomer', data)
      var res = await this.$store.state.ModuleSaleOrder.stateGetDetailCustomer
      if (res.message === 'Get detail customer success.') {
        var dataCustomer = res.data[0]
        // console.log('roleCustomer', roleCustomer)
        // console.log('dataCustomer1', dataCustomer)
        this.cusCode = dataCustomer.cus_code
        localStorage.setItem('cusCode', this.cusCode)
        var customerAddress = dataCustomer.customer_address
        var customerInvAddress = dataCustomer.customer_inv_address
        var addressDefault = []
        var addressInvAddressDefault = []
        if (customerAddress.length > 1 || customerInvAddress.length > 1) {
          customerAddress.forEach((e, i) => {
            if (e.default_address === 'Y') {
              addressDefault.push(e)
            }
          })
          customerInvAddress.forEach((e, i) => {
            if (e.default_address === 'Y') {
              addressInvAddressDefault.push(e)
            }
          })
          dataCustomer.customer_address = addressDefault
          dataCustomer.customer_inv_address = addressInvAddressDefault
        }
        // console.log('dataCustomer2', dataCustomer)
        if (roleCustomer === 'general') {
          localStorage.removeItem('AddressCustomerSale')
          localStorage.setItem('AddressCustomerSale', Encode.encode(dataCustomer))
          // console.log('1', localStorage.getItem('AddressCustomerSale') !== null)
        } else {
          localStorage.removeItem('AddressCustomerBussinessSale')
          localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(dataCustomer))
          // console.log('2')
        }
        // console.log('test')
        if (this.actions === 'addAddress') {
          // console.log('dataCustomerพี่นนท์ใจร้าย', dataCustomer.customer_address[0])
          this.addAddressForSameAddress = dataCustomer.customer_address[0]
        } else {
          this.addAddressForSameAddress = ''
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง', showConfirmButton: false, timer: 2500 })
      }
    },
    sameAddressChange () {
      if (this.sameAddressCustomer === false) {
        this.sameAddress = 'N'
        localStorage.removeItem('invoiceBackward')
        // console.log('เข้าไหม')
        // this.email = ''
        this.first_name = ''
        this.last_name = ''
        // this.name = ''
        this.phone = ''
        // this.taxID = ''
        this.house_no = ''
        this.room_no = ''
        this.floor = ''
        this.building_name = ''
        this.moo_ban = ''
        this.moo_no = ''
        this.soi = ''
        this.yaek = ''
        this.street = ''
        this.subdistrict = ''
        this.district = ''
        this.province = ''
        this.zipcode = ''
      } else {
        if (this.actions === 'add') {
          this.addAddressForSameAddress = ''
        }
        this.sameAddress = 'Y'
        // if (localStorage.getItem('partner_id') !== null) {
        //   const ID = localStorage.getItem('partner_id')
        //   console.log('ID', ID)
        //   this.getCustomDetail(ID)
        // }
        // console.log('this.sameAddress', this.sameAddress)
      }
      this.open(this.itemCustomer, this.title, this.page, this.actions, this.SaleVendor)
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipcode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    }
  }
}
</script>
<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}
</style>
