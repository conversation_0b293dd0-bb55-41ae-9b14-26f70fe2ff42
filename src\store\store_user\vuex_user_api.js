import AxiosUser from './axios_user_api'

const ModuleUser = {
  state: {
    // User Detail Page
    stateUserDetailPage: [],
    stateBindAccount: [],
    stateChangePasswordMarket: [],
    stateListUserAddress: [],
    stateUpdateUserAddress: [],
    stateCreateUserAddress: [],
    stateDeleteUserAddress: [],
    stateSetDefaultUserAddress: [],
    stateEditUserProfile: [],
    // Created AddressTaxinvoice Shoppingcart And Confirm order
    stateUPSCreatedAddressTaxinvoice: [],
    // Get AddressTaxinvoice Shoppingcart And Confirm order
    stateUPSGetAddressTaxinvoice: [],
    stateUPSGetAddressTaxinvoicePurchase: [],
    stateUPSListUserAddressTex: [],
    stateAuthorityUser: [],
    // getInvoice
    stategetInvoice: [],
    // upsertInvoice
    stateupsertInvoice: [],
    // get business
    stateGetBusiness: [],
    // add Invoice
    stateAddInvoice: [],
    // Get Invoice Address
    stateGetInvoiceAddress: [],
    stateGetAllInvoiceAddress: [],
    stateSetDefaultInvoice: [],
    stateDeleteInvoice: [],
    stateAcceptConsent: [],
    stateApprovedConsent: [],
    stateGetTextConsent: [],
    stateCreateCustomerAddress: [],
    stateUpdateCustomerInvAddress: [],
    stateCreateCustomerInvAddress: [],
    stateCreateCustomer: [],
    stateGetTokenWLC: [],
    stateGetCoupon: [],
    stateListShopJV: [],
    stateRefreshToken: [],
    stateChangeShippingAddress: [],
    stateListAccountBankUser: [],
    stateCreateAccountBankUser: [],
    stateUpdateAccountBankUser: [],
    stateDeleteAccountBankUser: [],
    stateChangeMainAccountBankUser: [],
    stateGetDataInviteCode: []
  },
  getters: {
    getDataAuthorityUser (state) {
      return state.stateAuthorityUser
    }
  },
  mutations: {
    // User Detail Page
    mutationsUserDetailPage (state, data) {
      state.stateUserDetailPage = data
    },
    // Bind Account and Auto Add OneChat Bot + Create Shop
    mutationsBindAccount (state, data) {
      state.stateBindAccount = data
    },
    // Change Password Market
    mutationsChangePasswordMarket (state, data) {
      state.stateChangePasswordMarket = data
    },
    mutationListUserAddress (state, data) {
      state.stateListUserAddress = data
    },
    mutationUpdateUserAddress (state, data) {
      state.stateUpdateUserAddress = data
    },
    mutationCreateUserAddress (state, data) {
      state.stateCreateUserAddress = data
    },
    mutationDeleteUserAddress (state, data) {
      state.stateDeleteUserAddress = data
    },
    mutationSetDefaultUserAddress (state, data) {
      state.stateSetDefaultUserAddress = data
    },
    // Created AddressTaxinvoice Shoppingcart And Confirm order
    mutationsUPSCreatedAddressTaxinvoice (state, data) {
      state.stateUPSCreatedAddressTaxinvoice = data
    },
    // Get AddressTaxinvoice Shoppingcart And Confirm order
    mutationsUPSGetAddressTaxinvoice (state, data) {
      state.stateUPSGetAddressTaxinvoice = data
    },
    mutationsUPSGetAddressTaxinvoicePurchase (state, data) {
      state.stateUPSGetAddressTaxinvoicePurchase = data
    },
    mutationUPSListUserAddressTex (state, data) {
      state.stateUPSListUserAddressTex = data
    },
    // Edit User Profile
    mutationsEditUserProfile (state, data) {
      state.stateEditUserProfile = data
    },
    // Authority User
    mutationsAuthorityUser (state, data) {
      state.stateAuthorityUser = data
    },
    // getInvoice
    mutationsGetInvoice (state, data) {
      state.stategetInvoice = data
    },
    // upsertInvoice
    mutationsUpsertInvoice (state, data) {
      state.stateupsertInvoice = data
    },
    mutationsGetBusiness (state, data) {
      state.stateGetBusiness = data
    },
    // Add Invoice
    mutationsAddInvoice (state, data) {
      state.stateAddInvoice = data
    },
    // Get Invoice Address
    mutationsGetInvoiceAddress (state, data) {
      state.stateGetInvoiceAddress = data
    },
    mutationsGetAllInvoiceAddress (state, data) {
      state.stateGetAllInvoiceAddress = data
    },
    mutationsSetDefaultInvoice (state, data) {
      state.stateSetDefaultInvoice = data
    },
    mutationsDeleteInvoice (state, data) {
      state.stateDeleteInvoice = data
    },
    mutationsAcceptConsent (state, data) {
      state.stateAcceptConsent = data
    },
    mutationsApprovedConsent (state, data) {
      state.stateApprovedConsent = data
    },
    mutationsGetTextConsent (state, data) {
      state.stateGetTextConsent = data
    },
    mutationCreateCustomer (state, data) {
      state.stateCreateCustomer = data
    },
    mutationCreateCustomerAddress (state, data) {
      state.stateCreateCustomerAddress = data
    },
    mutationUpdateCustomerInvAddress (state, data) {
      state.stateUpdateCustomerInvAddress = data
    },
    mutationCreateCustomerInvAddress (state, data) {
      state.stateCreateCustomerInvAddress = data
    },
    mutationsGetTokenWLC (state, data) {
      state.stateGetTokenWLC = data
    },
    mutationsGetCoupon (state, data) {
      state.stateGetCoupon = data
    },
    mutationsListShopJV (state, data) {
      state.stateListShopJV = data
    },
    mutationsRefreshToken (state, data) {
      state.stateRefreshToken = data
    },
    mutationsChangeShippingAddress (state, data) {
      state.stateChangeShippingAddress = data
    },
    mutationsListAccountBankUser (state, data) {
      state.stateListAccountBankUser = data
    },
    mutationsCreateAccountBankUser (state, data) {
      state.stateCreateAccountBankUser = data
    },
    mutationsUpdateAccountBankUser (state, data) {
      state.stateUpdateAccountBankUser = data
    },
    mutationsDeleteAccountBankUser (state, data) {
      state.stateDeleteAccountBankUser = data
    },
    mutationsChangeMainAccountBankUser (state, data) {
      state.stateChangeMainAccountBankUser = data
    },
    mutationsGetDataInviteCode (state, data) {
      state.stateGetDataInviteCode = data
    }
  },
  actions: {
    // User Detail Page
    async actionsUserDetailPage (context, access) {
      const responseData = await AxiosUser.GetUserDetailPage(access)
      // console.log('response', responseData)
      await context.commit('mutationsUserDetailPage', responseData)
    },
    // Bind Account and Auto Add OneChat Bot + Create Shop
    async actionBindAccount (context, access) {
      const responseData = await AxiosUser.SetBindAccount(access)
      await context.commit('mutationsBindAccount', responseData)
    },
    // Change Password Market
    async actionChangePasswordMarket (context, access) {
      const responseData = await AxiosUser.ChangePasswordMarket(access)
      await context.commit('mutationsChangePasswordMarket', responseData)
    },
    async actionListUserAddress (context, access) {
      const dataFromAxios = await AxiosUser.axiosListUserAddress(access)
      await context.commit('mutationListUserAddress', dataFromAxios)
    },
    async actionUpdateUserAddress (context, access) {
      const dataFromAxios = await AxiosUser.UpdateUserAddress(access)
      await context.commit('mutationUpdateUserAddress', dataFromAxios)
    },
    async actionCreateUserAddress (context, access) {
      const dataFromAxios = await AxiosUser.CreateUserAddress(access)
      await context.commit('mutationCreateUserAddress', dataFromAxios)
    },
    async actionDeleteUserAddress (context, access) {
      const dataFromAxios = await AxiosUser.DeleteUserAddress(access)
      await context.commit('mutationDeleteUserAddress', dataFromAxios)
    },
    async actionDefaultUserAddress (context, access) {
      const dataFromAxios = await AxiosUser.DefaultUserAddress(access)
      await context.commit('mutationSetDefaultUserAddress', dataFromAxios)
    },
    // Created AddressTaxinvoice Shoppingcart And Confirm order
    async actionUPSCreatedAddressTaxinvoice (context, access) {
      const responseData = await AxiosUser.CreatedAddressTaxinvoice(access)
      await context.commit('mutationsUPSCreatedAddressTaxinvoice', responseData)
    },
    // Get AddressTaxinvoice Shoppingcart And Confirm order
    async actionUPSGetAddressTaxinvoice (context, access) {
      const responseData = await AxiosUser.GetAddressTaxinvoice(access)
      await context.commit('mutationsUPSGetAddressTaxinvoice', responseData)
    },
    async actionUPSGetAddressTaxinvoicePurchase (context) {
      const responseData = await AxiosUser.GetAddressTaxinvoicePurchase()
      await context.commit('mutationsUPSGetAddressTaxinvoicePurchase', responseData)
    },
    async actionUPSListUserAddressTex (context, access) {
      const dataFromAxios = await AxiosUser.axiosUPSListUserAddressTex(access)
      await context.commit('mutationUPSListUserAddressTex', dataFromAxios)
    },
    async actionsEditUserProfile (context, access) {
      const dataResponse = await AxiosUser.UpdateUserProfile(access)
      await context.commit('mutationsEditUserProfile', dataResponse)
    },
    // เช็คสิทธิการแสดงแถบเมนู
    async actionsAuthorityUser (context, access) {
      const dataResponse = await AxiosUser.AuthorityUserAccount(access)
      await context.commit('mutationsAuthorityUser', dataResponse)
    },
    // getInvoice
    async actionsGetInvoice (context, access) {
      const dataResponse = await AxiosUser.GetInvoice(access)
      await context.commit('mutationsGetInvoice', dataResponse)
    },
    // upsertInvoice
    async actionsUpsertInvoice (context, access) {
      const dataResponse = await AxiosUser.UpsertInvoice(access)
      await context.commit('mutationsUpsertInvoice', dataResponse)
    },
    async actionsGetBusinessData (context) {
      const response = await AxiosUser.GetBusiness()
      await context.commit('mutationsGetBusiness', response)
    },
    // upsertInvoice
    async actionsAddInvoice (context, access) {
      const dataResponse = await AxiosUser.AddInvoice(access)
      await context.commit('mutationsAddInvoice', dataResponse)
    },
    // Get Invoice Address
    async actionsGetInvoiceAddress (context, access) {
      const dataResponse = await AxiosUser.GetInvoiceAddress(access)
      await context.commit('mutationsGetInvoiceAddress', dataResponse)
    },
    async actionsGetAllInvoiceAddress (context, access) {
      const dataResponse = await AxiosUser.GetAllInvoiceAddress(access)
      await context.commit('mutationsGetAllInvoiceAddress', dataResponse)
    },
    async actionsSetDefaultInvoice (context, access) {
      const dataResponse = await AxiosUser.SetDefaultInvoice(access)
      await context.commit('mutationsSetDefaultInvoice', dataResponse)
    },
    async actionsDeleteInvoice (context, access) {
      const dataResponse = await AxiosUser.DeleteInvoice(access)
      await context.commit('mutationsDeleteInvoice', dataResponse)
    },
    async actionsAcceptConsent (context, access) {
      const dataResponse = await AxiosUser.AcceptConsent(access)
      await context.commit('mutationsAcceptConsent', dataResponse)
    },
    async actionsApprovedConsent (context, access) {
      const dataResponse = await AxiosUser.ApprovedConsent(access)
      await context.commit('mutationsApprovedConsent', dataResponse)
    },
    async actionsGetTextConsent (context, access) {
      const dataResponse = await AxiosUser.GetTextConsent(access)
      await context.commit('mutationsGetTextConsent', dataResponse)
    },
    async actionsCreateCustomerAddress (context, access) {
      var responseData = await AxiosUser.CreateCustomerAddress(access)
      await context.commit('mutationCreateCustomerAddress', responseData)
    },
    async actionsUpdateCustomerInvAddress (context, access) {
      var responseData = await AxiosUser.UpdateCustomerInvAddress(access)
      await context.commit('mutationUpdateCustomerInvAddress', responseData)
    },
    async actionsCreateCustomerInvAddress (context, access) {
      var responseData = await AxiosUser.CreateCustomerInvAddress(access)
      await context.commit('mutationCreateCustomerInvAddress', responseData)
    },
    async actionsCreateCustomer (context, access) {
      var responseData = await AxiosUser.CreateCustomer(access)
      await context.commit('mutationCreateCustomer', responseData)
    },
    async actionsGetTokenWLC (context, access) {
      const dataResponse = await AxiosUser.GetTokenWLC(access)
      await context.commit('mutationsGetTokenWLC', dataResponse)
    },
    async actionsGetCoupon (context, access) {
      const dataResponse = await AxiosUser.GetCoupon(access)
      await context.commit('mutationsGetCoupon', dataResponse)
    },
    async actionsListShopJV (context, access) {
      const dataResponse = await AxiosUser.ListShopJV(access)
      await context.commit('mutationsListShopJV', dataResponse)
    },
    async actionsRefreshToken (context, access) {
      const response = await AxiosUser.RefreshToken(access)
      await context.commit('mutationsRefreshToken', response)
    },
    async actionsChangeShippingAddress (context, access) {
      const response = await AxiosUser.ChangeShippingAddress(access)
      await context.commit('mutationsChangeShippingAddress', response)
    },
    async actionsListAccountBankUser (context, access) {
      const response = await AxiosUser.axiosListAccountBankUser(access)
      await context.commit('mutationsListAccountBankUser', response)
    },
    async actionsCreateAccountBankUser (context, access) {
      const response = await AxiosUser.axiosCreateAccountBankUser(access)
      await context.commit('mutationsCreateAccountBankUser', response)
    },
    async actionsUpdateAccountBankUser (context, access) {
      const response = await AxiosUser.axiosUpdateAccountBankUser(access)
      await context.commit('mutationsUpdateAccountBankUser', response)
    },
    async actionsDeleteAccountBankUser (context, access) {
      const response = await AxiosUser.axiosDeleteAccountBankUser(access)
      await context.commit('mutationsDeleteAccountBankUser', response)
    },
    async actionsChangeMainAccountBankUser (context, access) {
      const response = await AxiosUser.axiosChangeMainAccountBankUser(access)
      await context.commit('mutationsChangeMainAccountBankUser', response)
    },
    async actionsGetDataInviteCode (context, access) {
      const response = await AxiosUser.GetDataInviteCode(access)
      await context.commit('mutationsGetDataInviteCode', response)
    }
  }
}
export default ModuleUser
