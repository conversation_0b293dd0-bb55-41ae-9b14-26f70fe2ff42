<template>
  <div class="ma-5">
    <!-- <v-container> -->
      <v-row justify="center">
        <h1 class="mt-4 ml-4">ผู้ใช้งาน</h1>
        <v-spacer></v-spacer>
        <v-btn dense rounded class="mt-4" color="info" @click="goToCreateUser()"><v-icon>mdi-plus</v-icon> เพิ่มผู้ใช้งาน</v-btn>
      </v-row>
      <v-card
      class="mt-2"
      outlined
      width="100%"
      >
        <v-container>
          <v-row justify="center">
            <v-card
            class="my-4"
            outlined
            width="80%"
            >
              <v-card-title>
                รายละเอียดผู้ใช้งาน
                <v-spacer></v-spacer>
                <v-btn icon tile dark color="success" @click="EditUser()"><v-icon dark>mdi-square-edit-outline</v-icon></v-btn>
              </v-card-title>
              <v-divider></v-divider>
              <v-card-text>
                <v-row justify="center" align="center" dense class="mt-2">
                  <v-col cols="12" md="2" sm="2" xs="12">
                    <p>ชื่อ</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12">
                    <p>{{ dataUser.name }}</p>
                  </v-col>
                  <v-col cols="12" md="2" sm="2" xs="12">
                    <p>เข้าใช้งานล่าสุด</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12">
                    <p>{{ dataUser.arrivalTime }}</p>
                  </v-col>
                </v-row>
                <v-row justify="center" align="center" dense>
                  <v-col cols="12" md="2" sm="2" xs="12">
                    <p>อีเมล</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12">
                    <p>{{ dataUser.email }}</p>
                  </v-col>
                  <v-col cols="12" md="2" sm="2" xs="12">
                    <p>การยืนยันอีเมล</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12" class="pb-5">
                    <v-icon v-if="dataUser.verify === true" small color="success">mdi-check</v-icon>
                    <v-icon v-else-if="dataUser.verify === false" small color="error">mdi-close</v-icon>
                  </v-col>
                </v-row>
                <v-row justify="center" align="center" dense>
                  <v-col cols="12" md="2" sm="2" xs="12">
                    <p>หมายเลขโทรศัพท์</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12">
                    <p>{{ dataUser.mobile }}</p>
                  </v-col>
                  <v-col cols="12" md="5" sm="3" xs="12">
                  </v-col>
                </v-row>
                <v-row justify="center" align="center" dense>
                  <v-col cols="12" md="2" sm="2" xs="12" align-self="start">
                    <p>สิทธิ์การเข้าใช้งาน</p>
                  </v-col>
                  <v-col cols="12" md="3" sm="3" xs="12">
                    <v-row dense>
                      <v-col cols="12" md="8">
                        <p>ผู้ดูแลระบบ</p>
                      </v-col>
                      <v-col cols="12" md="1">
                        <v-icon v-if="dataUser.admin === true" small color="success">mdi-check</v-icon>
                        <v-icon v-else-if="dataUser.admin === false" small color="error">mdi-close</v-icon>
                      </v-col>
                      <v-col cols="12" md="8">
                        <p>ผู้ช่วยผู้ดูแลระบบ</p>
                      </v-col>
                      <v-col cols="12" md="1">
                        <v-icon v-if="dataUser.assistant === true" small color="success">mdi-check</v-icon>
                        <v-icon v-else-if="dataUser.assistant === false" small color="error">mdi-close</v-icon>
                      </v-col>
                      <v-col cols="12" md="8">
                        <p>ผู้อนุมัติ</p>
                      </v-col>
                      <v-col cols="12" md="1">
                        <v-icon v-if="dataUser.appover === true" small color="success">mdi-check</v-icon>
                        <v-icon v-else-if="dataUser.appover === false" small color="error">mdi-close</v-icon>
                      </v-col>
                      <v-col cols="12" md="8">
                        <p>ผู้สั่งซื้อ</p>
                      </v-col>
                      <v-col cols="12" md="1">
                        <v-icon v-if="dataUser.buyer === true" small color="success">mdi-check</v-icon>
                        <v-icon v-else-if="dataUser.buyer === false" small color="error">mdi-close</v-icon>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="5" sm="3" xs="12" v-if="dataUser.appover === true">
                    <v-col cols="12" md="6" class="mt-12">
                      <p>ระยะเวลารอการอนุมัติ {{ dataUser.appoverDate }} วัน</p>
                    </v-col>
                  </v-col>
                  <v-col cols="12" md="5" sm="3" xs="12" v-else>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-row>
          <ManageBuyer v-if="dataUser.buyer === true"/>
        </v-container>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined color="success" dense @click="back()">ย้อนกลับ</v-btn>
        </v-card-actions>
      </v-card>
    <!-- </v-container> -->
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    ManageBuyer: () => import('@/components/User/ManageBuyer.vue')
  },
  data () {
    return {
      dataUser: []
    }
  },
  created () {
    this.dataUser = JSON.parse(Decode.decode(localStorage.getItem('DetailUser')))
  },
  methods: {
    goToCreateUser () {
      this.$router.push('/createuser').catch(() => {})
    },
    EditUser () {
      this.$router.push('/edituser').catch(() => {})
    },
    back () {
      this.$router.push('/user').catch(() => {})
    }
  }
}
</script>
