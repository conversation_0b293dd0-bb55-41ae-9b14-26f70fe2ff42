<template>
  <v-container>
    <v-row>
      <h2 class="pt-1 ml-2 mt-2 mb-4" style="font-weight: 700;">สินค้าขายดี</h2>
      <v-spacer style="border-top: 2px solid #bbb; margin-top: 28px; margin-left: 12px; margin-right: 12px;"></v-spacer>
    </v-row>
    <v-row class="ml-1 mr-1">
      <v-card v-for="(item, index) in bestCategory" :key="index" style="cursor: pointer" tile height="275" width="199" elevation="0" class="mt-4 mb-4">
        <v-card-title class="pr-4">
          <v-row justify="center" dense>
            <v-img
             :src="item.image"
             height="163"
             width="163"
            >
            </v-img>
          </v-row>
        </v-card-title>
        <v-card-text>
          <v-row justify="center" align-content="center">
            <span style="font-size: 16px; font-weight: bold; color: #333333" class="mt-4">{{ item.name|truncate(25, '...') }}</span><br>
            <span style="font-size: 14px; color: #333333" class="mt-2">฿ {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          </v-row>
        </v-card-text>
      </v-card>
    </v-row>
    <v-row>
      <h2 class="pt-1 ml-2 mt-4 mb-4" style="font-weight: 700;">{{ categoryName }}</h2>
      <v-spacer style="border-top: 2px solid #bbb; margin-top: 36px; margin-left: 12px; margin-right: 12px;"></v-spacer>
    </v-row>
    <v-row>
      <span class="pt-1 ml-2 mt-2 mb-4" style="font-weight: 400; color: #636363; font-size: 16px;">ค้นพบสินค้า {{ productLenght }} รายการ</span>
      <v-spacer></v-spacer>
      <v-col cols="12" md="3" class="pt-0">
        <v-select
         v-model="selectPrice"
         :items="itemPrice"
         item-text="text"
         item-value="value"
         placeholder="ราคา"
         dense
         outlined
        ></v-select>
      </v-col>
      <v-col cols="12" md="1" class="pt-0 mb-4 mr-4">
        <v-btn-toggle
          v-model="toggle_exclusive"
          mandatory
          text
          group
          dense
        >
          <v-btn>
            <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
            <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
          </v-btn>
          <v-btn>
            <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-list-outline</v-icon>
            <v-icon v-else color="#27AB9C">mdi-view-list</v-icon>
          </v-btn>
        </v-btn-toggle>
      </v-col>
    </v-row>
    <v-row justify="center" v-if="toggle_exclusive === 0">
      <v-col cols="12" md="2" sm="3" xs="4" v-for="(item, index) in productCategory" :key="index">
        <CardProducts :itemProduct='item'/>
      </v-col>
    </v-row>
    <v-row justify="center" v-if="toggle_exclusive === 1">
      <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in productCategory" :key="index">
        <CardProductsList :itemProduct='item' />
      </v-col>
    </v-row>
    <v-row justify="center" class="my-6">
      <v-pagination
       color="#27AB9C"
       v-model="pageNumber"
       :length="15"
       :total-visible="7"
       @change="pageChange()"
      ></v-pagination>
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: ['bestCategory', 'categoryName', 'productCategory'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    CardProducts: () => import(/* webpackPrefetch: true */ '@/components/Card/ProductCardUI'),
    CardProductsList: () => import(/* webpackPrefetch: true */ '@/components/Card/ProductCardListUI')
  },
  data () {
    return {
      productLenght: '',
      selectPrice: '',
      itemPrice: [
        { text: 'ราคา: จากน้อยไปมาก', value: 'lowToHigh' },
        { text: 'ราคา: จากมากไปน้อย', value: 'HighToLow' }
      ],
      toggle_exclusive: 0
    }
  },
  created () {
    this.productLenght = this.productCategory.length
  },
  computed: {
    pageNumber: {
      get () {
        return parseInt(this.$route.query.pageNumber) || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.$router.push(`/CategoryPage/?pageNumber=${newPage}`).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.v-card:hover {
  transform:scale(1.02) ;
}
.container {
  max-width: 1250px;
}
.v-btn-toggle--group > .v-btn.v-btn {
  background-color: transparent !important;
  border-color: transparent;
  margin: 0px !important;
  min-width: auto;
}
</style>
