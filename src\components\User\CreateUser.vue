<template>
  <div class="ma-5">
    <!-- <v-container> -->
      <v-row justify="center">
        <h1 class="mt-4">เพิ่มผู้ใช้งาน</h1>
        <v-spacer></v-spacer>
        <v-dialog
          v-model="dialog"
          persistent
          max-width="600"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-btn dense rounded class="mt-4" v-bind="attrs" v-on="on" color="info"><v-icon>mdi-plus</v-icon> เพิ่มด้วยอีเมลที่มีอยู่แล้ว</v-btn>
          </template>
          <v-card>
            <v-card-title>
              กรุณากรอกอีเมลเพื่อค้นหาอีเมลที่มีอยู่แล้ว
              <v-spacer></v-spacer>
              <v-btn icon small @click="dialog = false"><v-icon>mdi-close-circle</v-icon></v-btn>
            </v-card-title>
            <v-card-text>
              <v-row justify="center" align="center">
                <v-col cols="12" md="10" class="my-6">
                  <v-text-field
                  v-model="emailUser"
                  outlined
                  placeholder="<EMAIL>"
                  dense
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-card-text>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn color="success" dense>ค้นหา</v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-row>
      <v-card
      class="mt-2"
      outlined
      width="100%"
      >
        <v-form
        ref="form"
        :lazy-validation="lazy"
        >
          <v-row justify="center" align="center" dense class="mt-2">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>ชื่อ (ภาษาไทย)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="firstnameTH"
              :rules="Rules.firstnameTH"
              outlined
              dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>นามสกุล (ภาษาไทย)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="lastnameTH"
              :rules="Rules.lastnameTH"
              outlined
              dense
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" align="center" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>ชื่อ (ภาษาอังกฤษ)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="firstnameEN"
              :rules="Rules.firstnameEN"
              outlined
              dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>นามสกุล (ภาษาอังกฤษ)<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="lastnameEN"
              :rules="Rules.lastnameEN"
              outlined
              dense
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" align="center" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>อีเมลผู้ใช้งาน<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="email"
              outlined
              :rules="Rules.emailRules"
              placeholder="<EMAIL>"
              dense
              ></v-text-field>
            </v-col>
            <v-col cols="12" md="2" sm="2" xs="12">
              <p>หมายเลขโทรศัพท์<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12" class="mt-2">
              <v-text-field
              v-model="mobile"
              v-mask="'###-###-####'"
              :maxlength="maxPhone"
              :rules="Rules.tel"
              outlined
              dense
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" align="start" dense class="mt-0">
            <v-col cols="12" md="2" sm="2" xs="12" class="mt-4">
              <p>สิทธิ์การเข้าใช้งาน<span style="color: red;"> *</span></p>
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="12">
              <v-checkbox
              v-model="checkbox1"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้ช่วยผู้ดูแลระบบ"
              ></v-checkbox>
              <v-checkbox
              v-model="checkbox2"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้อนุมัติ"
              ></v-checkbox>
              <v-checkbox
              v-model="checkbox3"
              :rules="[v => !!v || 'กรุณาเลือก สิทธิ์การเข้าใช้งาน']"
              label="ผู้สั่งซื้อ"
              ></v-checkbox>
            </v-col>
            <v-col cols="5" md="5" sm="2" xs="12" v-if="checkbox2 === true" class="mt-16">
              <v-row dense>
                <v-col cols="12" md="5" sm="2" xs="12" class="mt-4">
                  <p>จำนวนวันรอการอนุมัติ<span style="color: red;"> *</span></p>
                </v-col>
                <v-col cols="12" md="7" sm="3" xs="12" class="mt-2">
                  <v-text-field
                  v-model="limitdate"
                  outlined
                  dense
                  ></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="5" sm="2" xs="12" v-else>
            </v-col>
          </v-row>
        </v-form>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn outlined color="success" dense @click="back()">ย้อนกลับ</v-btn>
          <v-btn color="success" @click="submit()" dense>บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    <!-- </v-container> -->
  </div>
</template>

<script>
import Vue from 'vue'
import VueMask from 'v-mask'
Vue.use(VueMask)
export default {
  data () {
    return {
      lazy: false,
      firstnameTH: '',
      lastnameTH: '',
      firstnameEN: '',
      lastnameEN: '',
      email: '',
      mobile: '',
      checkbox1: false,
      checkbox2: false,
      checkbox3: false,
      limitdate: '',
      dialog: false,
      emailUser: '',
      Rules: {
        emailRules: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ],
        firstnameTH: [
          v => !!v || 'กรุณากรอก ชื่อ (ภาษาไทย)'
        ],
        lastnameTH: [
          v => !!v || 'กรุณากรอก นามสกุล (ภาษาไทย)'
        ],
        firstnameEN: [
          v => !!v || 'กรุณากรอก ชื่อ (ภาษาอังกฤษ)'
        ],
        lastnameEN: [
          v => !!v || 'กรุณากรอก นามสกุล (ภาษาอังกฤษ)'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ]
      },
      maxPhone: 12
    }
  },
  methods: {
    back () {
      this.$router.push('/user').catch(() => {})
    },
    submit () {
      if (this.$refs.form.validate(true)) {
        alert('บันทึกสำเร็จ')
      } else {
        alert('กรุณากรอกทุกช่องให้ครบ')
      }
    }
  }
}
</script>
