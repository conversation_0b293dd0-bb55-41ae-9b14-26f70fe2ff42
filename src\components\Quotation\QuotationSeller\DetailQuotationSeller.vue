<template>
  <v-container>
    <v-dialog v-model="ModalPaymentQU" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 4px;">
        <v-toolbar color="#BDE7D9" dark dense elevation="0">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"
                      :class="MobileSize ? 'title-mobile' : ''"><b>วิธีการชำระเงิน</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="ModalPaymentQU = !ModalPaymentQU" icon>
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text style="text-align: center;">
            <v-row dense justify="center">
              <v-col cols="12">
                <p style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;">
                    เลือกวิธีการชำระเงิน</p>
              </v-col>
              <v-col cols="12">
                <v-row dense justify="center">
                  <v-col cols="12" md="6" align="center">
                    <v-card outlined width="124" height="148"
                        style="border: 1px solid #D8EFE4; border-radius: 10px;">
                      <v-card-text class="pt-8">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/money.png" contain
                            max-height="40" max-width="40"></v-img>
                        <p style="font-weight: 500; font-size: 10px; line-height: 14px; color: #333333;"
                            class="pt-4">ชำระเงินทันที</p>
                      </v-card-text>
                    </v-card>
                  </v-col>
                  <v-col cols="12" md="6" align="center">
                    <v-card outlined width="124" height="148"
                      style="border: 1px solid #D8EFE4; border-radius: 10px;">
                      <v-card-text class="pt-8">
                        <v-img src="@/assets/ImageINET-Marketplace/Shop/term-loan.png" contain
                            max-height="40" max-width="40"></v-img>
                        <p style="font-weight: 500; font-size: 10px; line-height: 14px; color: #333333;"
                            class="pt-4">ใช้เครดิตเทอม</p>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-card width="100%" height="100%" elevation="0" :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']">
      <v-card-title>
        <v-row dense>
          <v-col cols="12" md="6">
            <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoListPoCompany()">mdi-chevron-left
              </v-icon>ใบเสนอราคา
            </span>
          </v-col>
          <v-col cols="12" md="6" v-if="EditModeQT === false && !MobileSize">
            <v-row dense v-if="MobileSize" no-gutters class="px-0">
              <v-col cols="12" align="center" v-if="DetailQU.status === 'waiting' && !editQT">
                <!-- <v-btn outlined color="#27AB9C" style="margin-right: 6px;" class="px-2" @click="editQuotation">
                    <v-icon color="#27AB9C" class="pr-2">mdi-pencil</v-icon> แก้ไข
                </v-btn> -->
                <v-btn text color="#27AB9C" class="mr-1" @click="ChangeEditMode()" v-if="DetailQU.is_order_JV !== 'no'">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil-outline</v-icon> <span style="text-decoration: underline;">แก้ไข</span>
                </v-btn>
                <v-btn text color="#27AB9C" class="mr-1" @click="editQTCompany()" v-if="DetailQU.is_order_JV !== 'yes' && DetailQU.status === 'waiting'">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil-outline</v-icon> <span style="text-decoration: underline;">แก้ไข</span>
                </v-btn>
                <v-btn @click="OpenModal('reject')" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C">
                    ปฏิเสธ
                </v-btn>
                <v-btn @click="OpenModal('approve')" rounded color="#27AB9C" class="px-2" dark>
                    ยอมรับ
                </v-btn>
              </v-col>
              <v-col cols="12" align="center" v-if="editQT">
                <!-- <v-btn text color="#27AB9C" class="mr-1" @click="reviewQTCompany()">
                  <v-icon color="#27AB9C" class="pr-2">mdi-eye</v-icon> <span style="text-decoration: underline;">ดูตัวอย่าง</span>
                </v-btn> -->
                <v-btn @click="RejectQT()" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C">
                  ยกเลิก
                </v-btn>
                <v-btn @click="editOrderQT()" rounded color="#27AB9C" class="px-2" dark>
                  บันทึก
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense justify="end" v-else>
              <v-col cols="12" align="end" v-if="DetailQU.status === 'waiting' && !editQT">
                <!-- <v-btn outlined color="#27AB9C" class="mr-1" @click="editQuotation">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil</v-icon> แก้ไข
                </v-btn> -->
                <v-btn text color="#27AB9C" class="mr-1" @click="ChangeEditMode()" v-if="DetailQU.is_order_JV !== 'no'">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil-outline</v-icon> <span style="text-decoration: underline;">แก้ไข</span>
                </v-btn>
                <v-btn text color="#27AB9C" class="mr-1" @click="editQTCompany()" v-if="DetailQU.is_order_JV !== 'yes' && DetailQU.status === 'waiting'">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil-outline</v-icon> <span style="text-decoration: underline;">แก้ไข</span>
                </v-btn>
                <v-btn @click="OpenModal('reject')" width="125" height="40" rounded outlined color="#27AB9C" class="mr-1">
                  ปฏิเสธ
                </v-btn>
                <v-btn @click="OpenModal('approve')" width="125" height="40" rounded color="#27AB9C" dark elevation="0">
                  ยอมรับ
                </v-btn>
              </v-col>
              <v-col cols="12" align="end" v-if="editQT">

                <!-- <v-btn text color="#27AB9C" class="mr-1" @click="reviewQTCompany()">
                  <v-icon color="#27AB9C" class="pr-2">mdi-eye</v-icon> <span style="text-decoration: underline;">ดูตัวอย่าง</span>
                </v-btn> -->
                <v-btn @click="RejectQT()" width="125" height="40" rounded outlined color="#27AB9C" class="mr-1">
                  ยกเลิก
                </v-btn>
                <v-btn @click="editOrderQT()" width="125" height="40" rounded color="#27AB9C" dark elevation="0">
                  บันทึก
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="6" v-else-if="EditModeQT === true && !MobileSize">
            <v-row dense v-if="MobileSize" no-gutters class="px-0">
              <v-col cols="12" align="center" v-if="DetailQU.status === 'waiting'">
                <!-- <v-btn outlined color="#27AB9C" style="margin-right: 6px;" class="px-2" @click="editQuotation">
                    <v-icon color="#27AB9C" class="pr-2">mdi-pencil</v-icon> แก้ไข
                </v-btn> -->
                <!-- <v-btn text color="#27AB9C" class="mr-1">
                  <v-icon color="#27AB9C" class="pr-2">mdi-eye</v-icon> <span style="text-decoration: underline;">ดูตัวอย่าง</span>
                </v-btn> -->
                <v-btn @click="closeEditMode()" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C">
                  ยกเลิก
                </v-btn>
                <v-btn @click="OpenModalDescription()" rounded color="#27AB9C" class="px-2" dark>
                  บันทึก
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense justify="end" v-else>
              <v-col cols="12" align="end" v-if="DetailQU.status === 'waiting'">
                <!-- <v-btn outlined color="#27AB9C" class="mr-1" @click="editQuotation">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil</v-icon> แก้ไข
                </v-btn> -->
                <!-- <v-btn text color="#27AB9C" class="mr-1">
                  <v-icon color="#27AB9C" class="pr-2">mdi-eye</v-icon> <span style="text-decoration: underline;">ดูตัวอย่าง</span>
                </v-btn> -->
                <v-btn @click="closeEditMode()" width="125" height="40" rounded outlined color="#27AB9C" class="mr-1">
                  ยกเลิก
                </v-btn>
                <v-btn @click="OpenModalDescription()" width="125" height="40" rounded color="#27AB9C" dark elevation="0">
                  บันทึก
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <!-- <v-col cols="12" md="6" align="end" v-else-if="DetailQU.status === 'waiting_approve'">
              <v-btn outlined color="#F5222D" class="mr-0">
                  <v-icon color="#F5222D" class="pr-2">mdi-delete-outline</v-icon> ยกเลิก
              </v-btn>
          </v-col> -->
          <!-- <v-col cols="12" md="6" align="end"
              v-else-if="DetailQU.status === 'active' || DetailQU.status === 'success'">
              <v-btn color="#27AB9C" class="mr-0 px-6" dark @click="ShowModalSelect()">
                  <v-icon class="pr-2">mdi-cart</v-icon> สั่งซื้อ
              </v-btn>
          </v-col> -->
        </v-row>
      </v-card-title>
      <v-card-text>
        <v-row class="pt-3 px-2" dense style="background: #F9FAFD; border-radius: 8px 8px 0px 0px;">
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่สร้าง : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ new Date(DetailQU.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })}}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเลขใบเสนอราคา : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.QT_number }}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <!-- <span>สถานะ : <b>{{ returnStringStatus(DetailQU.status) }}</b></span> -->
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto pt-1' : ''">Pay Type : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''">
              <v-chip v-if="DetailQU.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
              <v-chip v-else-if="DetailQU.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
              <span v-else>-</span>
            </span>
          </v-col>
          <!-- <v-col cols="12" md="4" v-if="DetailQU.status === 'waiting'">
              <v-checkbox v-model="useDiscount" dense label="ใช้ส่วนลด"></v-checkbox>
            </v-col>
            <v-col cols="12" md="4" v-if="DetailQU.status === 'waiting'">
              <v-row dense>
                <v-col cols="3" class="pt-3">
                  <span>ส่วนลด : </span>
                </v-col>
                <v-col cols="9">
                  <v-select :disabled="useDiscount === false ? true : false" outlined dense style="border-radius: 8px;" hide-details></v-select>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" md="4" v-if="DetailQU.status === 'waiting'">
              <v-row dense>
                <v-col cols="4" class="pt-3">
                  <span>ยอดส่วนลด : </span>
                </v-col>
                <v-col cols="8">
                  <v-text-field outlined dense disabled style="border-radius: 8px;" hide-details></v-text-field>
                </v-col>
              </v-row>
            </v-col> -->
        </v-row>
        <v-row class="px-2" dense style="background: #F9FAFD; border-radius: 0px 0px 8px 8px;">
          <v-col cols="12" md="4" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่อัปเดตล่าสุด : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ new Date(DetailQU.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric'}) }}</b></span>
          </v-col>
          <v-col cols="12" md="4" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">ส่งคำขอโดย : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.buyer_name }}</b></span>
          </v-col>
          <v-col cols="12" md="4" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเหตุ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.remark === null ? '-' : DetailQU.remark }}</b></span>
          </v-col>
          <v-col cols="12" md="4" v-if="DetailQU.status === 'reject'" :class="MobileSize ? 'd-flex' : ''" class="pt-2">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">เหตุผลในการปฏิเสธ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.reason }}</b></span>
          </v-col>
          <v-col cols="12" md="12" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">สถานะ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><v-chip :text-color="textcolorChip(DetailQU.status)" :color="colorChip(DetailQU.status)">{{ returnStringStatus(DetailQU.status) }}</v-chip></span>
          </v-col>
          <v-col v-if="DetailQU.status === 'waiting' && DetailQU.is_order_JV !== 'no' && EditModeQT === true"  cols="12" class="d-flex justify-space-between align-center">
            <span style="color: #333333; font-size: 16px;">รายการสินค้า</span>
            <span style="color: red;">* สามารถกรอกทศนิยมสูงสุดได้ 5 ตำแหน่ง</span>
          </v-col>
          <v-col cols="12" v-if="DetailQU.status === 'waiting' && DetailQU.is_order_JV !== 'no' && EditModeQT === true" >
            <v-data-table
              dense
              :headers="headers"
              :items="itemProduct"
              item-key="id"
              class="elevation-1"
              hide-default-footer
              style="text-align: center; white-space: nowrap;"
            >
              คอลัมน์ลำดับ
              <template v-slot:[`item.number`]="{ item }">
                <span>{{ itemProduct.indexOf(item) + 1 }}</span>
              </template>

              <!-- คอลัมน์ชื่อสินค้า -->
              <template v-slot:[`item.product_name`]="{ item }">
                <span>{{ item.product_name|truncate(30) }} {{item.have_attribute === 'no' ? '' : '(' + item.attribute_priority_1 + ')'}}</span>
              </template>

              <!-- คอลัมน์รูปภาพสินค้า -->
              <!-- <template v-slot:[`item.images_URL`]="{ item }">
                <v-card class="pa-1" width="100px" height="100px" elevation="0" contain>
                  <v-img
                    width="100px"
                    height="90px"
                    v-if="item.images_URL"
                    :src="item.have_attribute === 'no' ? item.images_URL : item.color_image_path"
                    contain
                  ></v-img>
                  <v-img width="100px" v-else src="@/assets/NoImage.png" contain></v-img>
                </v-card>
              </template> -->

              <!-- คอลัมน์ราคาตั้งต้น -->
              <!-- <template v-slot:[`item.fake_price`]="{ item }">
                <span>{{ item.fake_price ? item.fake_price.toLocaleString(undefined, { minimumFractionDigits: 2 }) : 'N/A' }}</span>
              </template> -->

              <!-- คอลัมน์ราคาขาย -->
              <!-- <template v-slot:[`item.real_price`]="{ item }">
                <span>{{ item.real_price ? item.real_price.toLocaleString(undefined, { minimumFractionDigits: 2 }) : 'N/A' }}</span>
              </template> -->

              <!-- คอลัมน์ส่วนลด -->
              <!-- <template v-slot:[`item.discount_percent`]="{ item }">
                <span>{{ item.discount_percent }}</span>
              </template> -->

              <!-- คอลัมน์ราคาแฟลชเซลล์ใหม่ -->
              <!-- <template v-slot:[`item.price`]="{ item }">
                <v-col class="d-flex justify-center align-center">
                  <v-text-field
                    v-model="item.price"
                    placeholder="ระบุราคา"
                    outlined
                    dense
                    oninput="this.value = this.value.replace(/^[.]/, '').replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^0+(?=\d)/,'').replace(/(\.\d{2}).+/g, '$1')"
                  ></v-text-field>
                </v-col>
              </template> -->
              <template v-slot:[`item.revenue_default`]="{ item }">
                <v-col class="d-flex justify-center align-center">
                  <v-text-field
                    v-model="item.revenue_default"
                    placeholder="ระบุราคา"
                    outlined
                    dense
                    class="text-center"
                    style="max-width: 80%;"
                    :rules="[value => /^\d*\.?\d{0,5}$/.test(value) || 'กรุณากรอกตัวเลขที่ถูกต้อง']"
                    oninput="this.value = this.value.replace(/^[.]/, '').replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1').replace(/^0+(?=\d)/,'').replace(/(\.\d{5}).+/g, '$1')"
                    @change="changePrice(item)"
                  ></v-text-field>
                </v-col>
              </template>
              <!-- @change="realPriceNoGood(item, itemFlashSalePhase2.indexOf(item))"
                  :rules="rule.amountVoucherWhenEdit(item.flashsale_price, item.real_price)" -->

              <!-- คอลัมน์ลบสินค้า -->
              <!-- <template v-slot:[`item.action`]="{ item, index }">
                <v-btn
                  x-small elevation="0" class="pt-4 pb-4 ml-2 btn-tool-ipad" @click="DeleteFromTable(item, index)">
                  <v-icon color="#A1A1A1" small>mdi-delete-outline</v-icon>
                </v-btn>
              </template> -->
            </v-data-table>
          </v-col>
          <v-col v-if="DetailQU.status === 'waiting' && DetailQU.is_order_JV !== 'no' && EditModeQT === true" cols="12" md="4" class="pt-2">
            <div style="color: #333333; font-size: 16px;">เลือกรายละเอียด</div>
            <v-autocomplete v-model="descriptionQTChange" :items="descriptionItem" item-text="name" item-value="description" label="เลือกรายละเอียด" solo dense no-data-text="ไม่มีข้อมูลรายละเอียด"></v-autocomplete>
          </v-col>
          <v-col v-if="DetailQU.status === 'waiting' && DetailQU.is_order_JV !== 'no' && EditModeQT === true" cols="12" md="12" class="pt-2">
            <div style="color: #333333; font-size: 16px;">รายละเอียด</div>
            <ckeditor :editor="editor" :config="editorConfig" v-model="QT_description" style="border: 1px solid;height: 310px"></ckeditor>
          </v-col>
          <v-col cols="12">
            <div>
              <v-col class="pa-0">
                <v-col v-if="DetailQU.old_installment_method.length !== 0" cols="12" md="4" sm="4" class="px-0">
                  <span style="color: #333333; font-size: 16px;">งวดชำระเงิน : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.old_installment_method.length }} งวด</b></span>
                  <!-- <v-select v-model="selectinstallment" hide-details :items="installmentOptions" item-text="formattedMonth" item-value="month" style="border-radius: 8px;" append-icon="mdi-chevron-down" placeholder="กรุณาเลือกงวดชำระเงิน" outlined dense></v-select> -->
                </v-col>
                <v-col v-if="DetailQU.old_installment_method.length !== 0" cols="12" class="pa-0">
                  <v-card style="border-radius: 8px;" elevation="0">
                    <v-card-text>
                      <v-row>
                        <v-col class="text-start pb-2" cols="12">
                          <v-row dense>
                            <v-img class="mr-2" src="@/assets/Layer_1.png" style="max-height: 25px; max-width: 25px;"></v-img>
                            <span style="color: #333333; font-size: 16px;">ยอดเงินที่ต้องการชำระแต่ละงวด</span>
                          </v-row>
                        </v-col>
                        <v-col v-for="(amount, index) in DetailQU.old_installment_method" :key="index" class="text-start py-2 px-1" :cols="MobileSize ? '6' : '3'">
                          <span style="color: #333333; font-size: 16px;">เดือนที่ {{ index + 1 }}</span><br>
                          <span style="color: #333333; font-size: 16px;"><b>{{ Number(amount.price).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }} บาท</b></span>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
                <v-col v-if="DetailQU.credit_day !== ''" cols="12" md="4" sm="4" class="px-0">
                  <span style="color: #333333; font-size: 16px;">Credit term : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ DetailQU.credit_day }} วัน</b></span>
                  <!-- <v-autocomplete v-model="selectedCreditTerm" :items="creditTermOptions" item-text="label" item-value="value" style="border-radius: 8px;" hide-details append-icon="mdi-chevron-down" placeholder="เลือก Credit term" outlined dense></v-autocomplete> -->
                </v-col>
              </v-col>
            </div>
            <v-col class="py-6" v-if="!discountBahtB2B && discountPercentB2B || discountBahtB2B && !discountPercentB2B">
              <v-row style="align-items: center;">
                <span class="pr-2" style="color: #333333; font-size: 16px;">ขอใช้ส่วนลด : </span>
                <span v-if="discountBahtB2B" style="color: #333333; font-size: 16px;"><b>ส่วนลดรูปแบบระบุยอด</b></span>
                <span v-if="discountPercentB2B" style="color: #333333; font-size: 16px;"><b>ส่วนลดรูปแบบเปอร์เซ็นต์</b></span>
              </v-row>
            </v-col>
            <v-col v-if="!discountBahtB2B && discountPercentB2B || discountBahtB2B && !discountPercentB2B" class="px-0">
              <v-row>
                <v-col v-if="discountBahtB2B" cols="12">
                  <span style="color: #333333; font-size: 16px;">ส่วนลด : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ Number(DetailQU.total_b2b_discount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }} บาท</b></span>
                </v-col>
                <v-col v-if="discountPercentB2B" cols="4">
                  <span style="color: #333333; font-size: 16px;">ส่วนลด : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ Number(DetailQU.percent_b2b_discount).toFixed(2) }} %</b></span>
                </v-col>
                <v-col v-if="discountPercentB2B" cols="4">
                  <span style="color: #333333; font-size: 16px;">ยอดส่วนลด : </span>
                  <span style="color: #333333; font-size: 16px;"><b>{{ Number(DetailQU.total_b2b_discount).toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 }) }} บาท</b></span>
                </v-col>
              </v-row>
            </v-col>
          </v-col>
        </v-row>
        <v-row dense>
          <!-- ใส่ iframe -->
          <v-card v-if="!editQT" width="100%" height="100%" outlined style="background: #C4C4C4; border-radius: 8px;" class="mt-4">
            <v-card-text :class="MobileSize ? 'pa-0' : ''">
              <!-- <span v-if="DetailQU.QT_path !== '-'">
                <vue-pdf
                  :page="2"
                  @num-pages="pageCount = $event"{{ returnStringStatus(DetailQU.status) }}
                  @page-loaded="currentPage = $event"
                  width="100%"
                  :src="pdfPath"
                ></vue-pdf>
                <vue-pdf-embed :source="DetailQU.QT_path" />
              </span> -->
              <iframe v-if="DetailQU.QT_path !== '-'"  :src="DetailQU.QT_path" width="100%" :height="MobileSize ? '500' : IpadSize ? '700' : '1200'"></iframe>
            </v-card-text>
          </v-card>
          <QTCheckoutV2 v-else class="mt-4" ref="QTCheckoutV2" />
        </v-row>
        <v-row dense v-if="MobileSize" class="mt-8">
          <v-col cols="12" class="px-0" v-if="EditModeQT === false">
            <v-row dense class="px-0" v-if="DetailQU.status === 'waiting'">
              <v-col cols="3">
                <v-btn text color="#27AB9C" class="px-0" @click="ChangeEditMode()">
                  <v-icon color="#27AB9C" class="pr-2">mdi-pencil-outline</v-icon> <span style="text-decoration: underline;">แก้ไข</span>
                </v-btn>
              </v-col>
              <v-col cols="9" align="end">
                <v-btn @click="OpenModal('reject')" width="91" height="38" class="mr-2" rounded outlined color="#27AB9C">
                  ปฏิเสธ
                </v-btn>
                <v-btn @click="OpenModal('approve')" width="81" height="38" rounded color="#27AB9C" dark>
                  ยอมรับ
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="6" v-else-if="EditModeQT === true">
            <v-row dense class="px-0">
              <v-col cols="12" align="end" v-if="DetailQU.status === 'waiting'">
                <!-- <v-btn text color="#27AB9C" class="mr-1">
                  <v-icon color="#27AB9C" class="pr-2">mdi-eye</v-icon> <span style="text-decoration: underline;">ดูตัวอย่าง</span>
                </v-btn> -->
                <v-btn @click="closeEditMode()" width="81" height="38" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C">
                  ยกเลิก
                </v-btn>
                <v-btn @click="OpenModalDescription()" width="81" height="38" rounded color="#27AB9C" class="px-2" dark>
                  บันทึก
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- Start Dialog  Approve and NonApprove -->
    <v-dialog v-model="dialog" width="605px" persistent scrollable>
      <v-form ref='formData' :lazy-validation="lazy">
        <v-card class="rounded-lg">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font v-if="this.type === 'reject'" color="#27AB9C">ปฏิเสธใบเสนอราคา</font>
                    <font v-if="this.type === 'approve'" color="#27AB9C">อนุมัติใบเสนอราคา</font>
                </span>
                <v-btn icon dark @click="closeModal()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="mt-12">
                <v-row no-gutters>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">หมายเลขใบเสนอราคา :
                        </p>
                    </v-col>
                    <v-col cols="12" md="8">
                        <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.QT_number }}
                        </p>
                    </v-col>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">ส่งคำขอโดย :
                        </p>
                    </v-col>
                    <v-col cols="12" md="8">
                        <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.buyer_name }}
                        </p>
                    </v-col>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">สถานะ :
                        </p>
                    </v-col>
                    <v-col cols=" 12" md="8">
                      <p style="font-weight: 600; font-size: 16px; color: #333333">
                        <b>{{ returnStringStatus(DetailQU.status) }}</b>
                      </p>
                    </v-col>
                    <v-col v-if="this.type === 'reject'" cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">เหตุผลในการปฏิเสธ <span style="color: red;">*</span> :
                        </p>
                    </v-col>
                    <v-col v-if="this.type === 'reject'" cols=" 12" md="8">
                        <v-textarea v-model="ans_reject" height="90" counter-value="100" counter="100" placeholder="ระบุเหตุผล" maxLength="100" dense auto-grow outlined :rules="Rules.reason">
                        </v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-card-actions v-if="this.type === 'reject'">
                <v-container style=" display: flex; justify-content: flex-end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">
                        ยกเลิก
                    </v-btn>
                    <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="ModalApprove('reject')">
                        บันทึก
                    </v-btn>
                </v-container>
            </v-card-actions>
            <v-card-actions v-else-if="this.type === 'approve'">
                <v-container style=" display: flex; justify-content: flex-end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">
                        ยกเลิก
                    </v-btn>
                    <v-btn dense color="#27AB9C" style="color: #ffff" class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="ModalApprove('approve')" :disabled="disableButton">
                        บันทึก
                    </v-btn>
                </v-container>
            </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- End Dialog Approve and NonApprove -->
    <v-dialog v-model="dialogEditDescription" width="605px" persistent scrollable>
      <v-card class="rounded-lg" v-if="MobileSize">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font color="#27AB9C">แก้ไขรายละเอียด</font>
                </span>
                <v-btn icon dark @click="closeModal()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="" style="padding-top:35px; margin-bottom:-25px">
                  <p style="font-weight: 400; font-size: 16px; color: #333333; display:flex; justify-content:center;">
                    กรุณากดยืนยันเพื่อบันทึก</p>
                  <p style="font-weight: 400; font-size: 16px; color: #333333; display:flex; justify-content:center; margin-top:-10px">
                    การแก้ไขรายละเอียดใบเสนอราคา
                  </p>
            </v-card-text>
            <v-card-actions>
            <v-container style=" display: flex; justify-content: center">
                <v-btn dense dark outlined color="#27AB9C" class="pl-4 pr-4 mt-2" @click="closeModal()">
                    ยกเลิก
                </v-btn>
                <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-4 pr-4 white--text"
                    @click="editDescription()">
                    บันทึก
                </v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
        <v-card class="rounded-lg" v-else>
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font color="#27AB9C">แก้ไขรายละเอียด</font>
                </span>
                <v-btn icon dark @click="closeModal()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="" style="display: flex; justify-content: center; padding-top:35px; margin-bottom:-25px" >
                  <p style="font-weight: 400; font-size: 16px; color: #333333;">
                    กรุณากดยืนยัน เพื่อบันทึกการแก้ไขรายละเอียดใบเสนอราคา
                  </p>
            </v-card-text>
            <v-card-actions>
            <v-container style=" display: flex; justify-content: center">
                <v-btn dense dark outlined color="#27AB9C" class="pl-4 pr-4 mt-2" @click="closeModal()">
                    ยกเลิก
                </v-btn>
                <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-4 pr-4 white--text"
                    @click="editDescription()">
                    บันทึก
                </v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
    </v-dialog>
    <!-- Await Change QT -->
    <v-dialog v-model="dialogAwaitChangeQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogAwait()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคา</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '125' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogAwait()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '125' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editDescription()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Change OT -->
    <v-dialog v-model="dialogSuccessChangeQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคาเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- <ModalSuccess v-model="dialogSuccessChangeQT"/>
    <ModalAwaitConfirm v-model="dialogAwaitChangeQT"/> -->
  </v-container>
</template>

<script>
// import VuePdf from 'vue-pdf'
// import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
import { Decode, Encode } from '@/services'
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
// import ModalSuccess from '@/components/Modal/ModalSuccess'
// import ModalAwaitConfirm from '@/components/Modal/ModalAwaitConfirm'
export default {
  components: {
    QTCheckoutV2: () => import(/* webpackPrefetch: true */ '@/components/Quotation/QTCheckoutV2')
    // VuePdf
    // VuePdfEmbed
    // ModalSuccess,
    // ModalAwaitConfirm
  },
  filters: {
    truncate: function (value, limit) {
      if (!value) return ''
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discountBahtB2B: false,
      editQT: false,
      discountPercentB2B: false,
      itemProduct: [],
      itemProductChange: [],
      headers: [
        { text: 'ลำดับที่', value: 'number', sortable: false, width: '10%' },
        { text: 'ชื่อสินค้า', value: 'product_name', sortable: false, width: '50%' },
        { text: 'ราคา', value: 'revenue_default', sortable: false, align: 'center', width: '40%' }
      ],
      pdfPath: '',
      QUNumber: '',
      EditModeQT: false,
      dialog: false,
      disableButton: false,
      ID: '',
      comID: '',
      DetailQU: [],
      type: '',
      ModalPaymentQU: false,
      useDiscount: false,
      dialogEditDescription: false,
      dialogSuccessChangeQT: false,
      dialogAwaitChangeQT: false,
      onedata: '',
      ans_reject: '',
      lazy: false,
      seller_shop_id: '',
      rules: [v => v.length <= 1000 || 'ระบุตัวอักษรไม่เกิน 1000 ตัวอักษร'],
      Rules: {
        reason: [
          v => !!v || 'กรุณากรอกเหตุผลในการปฏิเสธ'
        ]
      },
      CompanyDataId: '',
      detailItemQu: '',
      QT_description: '',
      descriptionItem: [],
      descriptionQTChange: '',
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          // 'heading',
          // '|',
          'bold',
          'italic',
          'link',
          // 'alignment:left',
          // 'alignment:right',
          // 'alignment:center',
          // 'alignment:justify',
          // 'bulletedlist',
          // 'numberedlist',
          // '|',
          // 'blockquote',
          // 'inserttable',
          'undo',
          'redo'
        ]
        // image: {
        //   toolbar: [
        //     'imageStyle:block',
        //     'imageStyle:side'
        //   ]
        // },
        // table: {
        //   contentToolbar: [
        //     'tableColumn',
        //     'tableRow',
        //     'mergeTableCells'
        //   ]
        // }
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.QUNumber = this.$route.query.QUNumber
    this.ID = this.$route.query.id
    this.comID = this.$route.query.comID
    this.Detail_rejcrt = ''
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
    // this.detailItemQu = JSON.parse(Decode.decode(localStorage.getItem('detailItemQU')))
    await this.GetDetailQU()
    await this.GetListIncludeQT()
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  mounted () {
    this.$EventBus.$on('successEditQTShop', this.successEditQT)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('successEditQTShop')
    })
    window.scrollTo(0, 0)
  },
  watch: {
    descriptionQTChange (val) {
      // console.log(val)
      this.QT_description = val
    }
  },
  methods: {
    async editOrderQT  () {
      if (this.$refs.QTCheckoutV2.hasInstallmentError) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>กรุณากรอกยอดชำระในแต่ละเดือนให้ครบและต้องไม่เป็น 0.00</h3>'
        })
      } else {
        if (this.$refs.QTCheckoutV2.remainingAmount !== '0.00') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: '<h3>กรุณาระบุยอดเงินให้เป็น 0.00 ก่อนดำเนินการต่อ</h3>'
          })
        } else {
          this.$refs.QTCheckoutV2.confirm()
        }
      }
    },
    RejectQT () {
      this.editQT = false
    },
    reviewQTCompany () {
      window.alert('ดูตัวอย่างใบเสนอราคา')
    },
    editQTCompany () {
      this.editQT = true
    },
    successEditQT () {
      this.editQT = false
      // this.backtoListPoCompany()
      this.GetDetailQU()
    },
    changePrice (item) {
      // console.log('changePrice', item)

      const exists = this.itemProduct.some(e => {
        if (e.have_attribute === 'no') {
          return e.product_id === item.product_id
        } else {
          return (
            e.product_id === item.product_id &&
            e.product_attribute_detail.product_attribute_id === item.product_attribute_detail.product_attribute_id
          )
        }
      })

      if (!exists) {
        this.itemProduct.push({ ...item })
      }
    },
    async GetListIncludeQT () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsListIncludeQT', data)
      const response = await this.$store.state.ModuleShop.stateListIncludeQT
      // console.log('GetListIncludeQT=====>', response.data)
      if (response.message === 'Success.') {
        this.descriptionItem = response.data.list_include
        // console.log('descriptionItem', this.descriptionItem)
      }
    },
    textcolorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return '#FF710B'
      } else if (quStatus === 'waiting_dwf') {
        return '#FF710B'
      } else if (quStatus === 'waiting_approve') {
        return '#FF710B'
      } else if (quStatus === 'check_doc') {
        return '#FF710B'
      } else if (quStatus === 'approve') {
        return '#52C41A'
      } else if (quStatus === 'reject') {
        return '#F5222D'
      } else if (quStatus === 'success') {
        return '#52C41A'
      } else if (quStatus === 'not_paid') {
        return 'rgb(28, 61, 119)'
      } else {
        return '#636363'
      }
    },
    colorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return '#FEF6E6'
      } else if (quStatus === 'waiting_dwf') {
        return '#FBECE1'
      } else if (quStatus === 'waiting_approve') {
        return '#FBECE1'
      } else if (quStatus === 'check_doc') {
        return '#FBECE1'
      } else if (quStatus === 'approve') {
        return '#F0FEE8'
      } else if (quStatus === 'reject') {
        return 'rgba(245, 34, 45, 0.10)'
      } else if (quStatus === 'success') {
        return '#F0FEE8'
      } else if (quStatus === 'not_paid') {
        return 'rgb(215, 226, 246)'
      } else {
        return '#E6E6E6'
      }
    },
    returnStringStatus (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'waiting') {
        return 'รออนุมัติเอกสาร'
      } else if (quStatus === 'waiting_dwf') {
        return 'รออนุมัติ'
      } else if (quStatus === 'waiting_approve') {
        return 'รอผู้อนุมัติ'
      } else if (quStatus === 'check_doc') {
        return 'รอผู้ซื้ออนุมัติ'
      } else if (quStatus === 'approve') {
        return 'อนุมัติแล้ว'
      } else if (quStatus === 'reject') {
        return 'ไม่อนุมัติ'
      } else if (quStatus === 'success') {
        return 'สร้างรายการสั่งซื้อ'
      } else if (quStatus === 'not_paid') {
        return 'ยังไม่ชำระเงิน'
      } else {
        return 'ยกเลิก'
      }
    },
    async editQuotation () {
    //   console.log('เข้าeditQuotation')
      this.$store.commit('openLoader')
      const dataForm = await {
        role_user: 'seller',
        user_id: this.onedata.user.user_id,
        id: this.ID,
        seller_shop_id: this.ID,
        company_id: parseInt(this.comID),
        qu_id: this.QUNumber,
        coupon_id: ''
      }
      localStorage.setItem('dataForm', Encode.encode(dataForm))
      const pathBack = window.location.href.split('/')
      this.$store.state.ModuleAdminManage.stateURLQu = `/${pathBack[3]}`
      await this.$router.push({ path: `/QuotationEdit?role=seller&&qu_id=${this.QUNumber}` }).catch(() => { })
      // await this.$store.dispatch('actionsEditQU', dataForm)
      // const { result = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQU
      // if (result === 'SUCCESS') {
      //   this.$store.state.ModuleAdminManage.QuotationformData = await data
      //   await this.$router.push({ path: `/QuotationEdit?role=seller&&qu_id=${this.QUNumber}` }).catch(() => { })
      // }
    },
    ChangeEditMode () {
      this.EditModeQT = true
    },
    closeEditMode () {
      this.EditModeQT = false
    },
    OpenModalDescription () {
      // this.$store.commit('openLoader').timer = 1500
      this.dialogAwaitChangeQT = true
      // await this.$store.commit('closeLoader')
    },
    async editDescription () {
      this.$store.commit('openLoader')
      // this.dialogEditDescription = false
      this.dialogAwaitChangeQT = false
      var data = {
        order_number: this.DetailQU.order_number,
        description: this.QT_description,
        product_list: this.itemProduct
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsEditQTDescription', data)
      var responseEditDescription = await this.$store.state.ModuleAdminManage.stateEditQTDescription
      // console.log('tong2', responseEditDescription)
      if (responseEditDescription.code === 200) {
        // await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
        // this.QUNumber = responseEditDescription.data.order_document_id
        this.QUNumber = responseEditDescription.data.order_number
        this.EditModeQT = false
        this.dialogSuccessChangeQT = true
        // if (this.DetailQU.QT_path !== '-') {
        //   this.DetailQU.QT_path = responseEditDescription.data.path_pdf_qt
        // } else if (this.DetailQU.s3_pdf_path_qt !== '-') {
        //   this.s3_pdf_path_qt = responseEditDescription.data.path_pdf_qt
        // }
        // this.GetDetailQU()
        // if (this.MobileSize === false) {
        //   this.$router.push({ path: `/QuotationDetail?QUNumber=${QUNumber}&id=${id}&comID=${comID}` }).catch(() => { })
        // } else {
        //   this.$router.push({ path: `/QuotationDetailMobile?QUNumber=${QUNumber}&id=${id}&comID=${comID}` }).catch(() => { })
        // }
      } else {
        if (responseEditDescription.code === 401) {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: responseEditDescription.message })
        }
      }
      this.$store.commit('closeLoader')
    },
    closeModalSuccess () {
      this.dialogSuccessChangeQT = false
      this.GetDetailQU()
    },
    saveEditQuotation () {
      if (this.MobileSize) {
        this.$route.push({ path: '/QuotationAllMobile' }).catch(() => {})
      } else {
        this.$route.push({ path: '/QuotationAll' }).catch(() => {})
      }
    },
    async getPagePDF (path) {
      // var loadingTask = VuePdf.createLoadingTask(path)
      // this.pdfPath = loadingTask
      // await this.pdfPath.promise.then(pdf => {
      //   this.numPages = pdf.numPages
      // })
      // console.log(this.numPages)
    },
    async GetDetailQU () {
      this.$store.commit('openLoader')
      this.DetailQU = []
      var data = {
        seller_shop_id: this.ID,
        order_number: this.QUNumber
      }
      await this.$store.dispatch('actionsNewDetailQTSeller', data)
      var response = await this.$store.state.ModuleShop.stateNewDetailQTSeller
      if (response.result === 'Success') {
        this.DetailQU = await response.data
        if (this.DetailQU.type_b2b_discount === 'baht') {
          this.discountBahtB2B = true
          this.discountPercentB2B = false
        } else if (this.DetailQU.type_b2b_discount === 'percent') {
          this.discountBahtB2B = false
          this.discountPercentB2B = true
        } else {
          this.discountBahtB2B = false
          this.discountPercentB2B = false
        }
        this.QT_description = await response.data.description_QT
        this.itemProduct = this.DetailQU.product_list
        this.$store.commit('closeLoader')
        // this.getPagePDF(
        //   // this.DetailQU.dwf_pdf_path_qt
        // )
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.DetailQU = []
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่มีใบเสนอราคานี้ในบริษัท',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/QuotationAll' }).catch(() => { })
        }
      }
    },
    async ModalApprove (statusApprove) {
      this.disableButton = true
      if (this.$refs.formData.validate(true)) {
        this.$store.commit('openLoader')
        var ApproveAndNonApprove = {
          seller_shop_id: this.seller_shop_id.id,
          qu_id: parseInt(this.QUNumber),
          order_number: this.DetailQU.order_number,
          status: statusApprove,
          reason: this.ans_reject,
          company_id: this.DetailQU.company_id,
          com_perm_id: this.DetailQU.com_perm_id
        }
        await this.$store.dispatch('Approve_QT', ApproveAndNonApprove)
        var response = await this.$store.state.ModuleShop.stateAppoverQT
        if (response.code === 200) {
          this.$store.commit('closeLoader')
          this.disableButton = false
          await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
          if (this.MobileSize) {
            this.$router.push({ path: '/QuotationAllMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/QuotationAll' }).catch(() => {})
          }
        } else if (response.message === 'สินค้า ใหญ่ ไม่เพียงพอ (คงเหลือ 0 ชิ้น)') {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'warning', text: response.message })
        } else if (response.code === 400) {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: response.message })
        } else if (response.code === 401) {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    OpenModal (type) {
      this.dialog = true
      this.type = type
    },
    closeModal () {
      this.dialog = false
      this.dialogEditDescription = false
      this.type = ''
      this.ans_reject = ''
    },
    closeDialogAwait () {
      this.dialog = false
      this.dialogAwaitChangeQT = false
      this.type = ''
      this.ans_reject = ''
    },
    backtoListPoCompany () {
      if (this.MobileSize) {
        this.$router.push({ path: '/QuotationAllMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/QuotationAll' }).catch(() => { })
      }
    },
    ShowModalSelect () {
      this.ModalPaymentQU = !this.ModalPaymentQU
    }
  }
}
</script>

<style lang="css">
 .vue-pdf-embed canvas {
  display: initial;
  position: inherit!important;
}
</style>
