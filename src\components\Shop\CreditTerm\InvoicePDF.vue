<template>
  <v-card elevation="0" width="100%" height="100%">
    <v-card-text>
      <v-row no-gutters>
        <v-col cols="12">
          <v-row dense class="mb-2">
            <v-btn icon @click="goBack()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon></v-btn>
            <h2 class="pt-2" :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile': 'fontSizeTitle'">ใบแจ้งหนี้</h2>
          </v-row>
        </v-col>
        <v-col cols="12" class="mt-2">
          <v-row dense class="mb-2">
            <v-col cols="12" md="4">
              <span>วันที่สร้าง :</span>
              <span class="ml-4">
                <b>{{new Date(order_detail.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</b>
              </span>
            </v-col>
            <v-col cols="12" md="4" class="">
              <span>รหัสการสั่งซื้อ :</span>
              <span class="ml-4"><b>{{order_detail.payment_credit_term_number}}</b></span>
            </v-col>
            <v-col cols="12" md="4">
              <span>สถานะ : </span>
              <span v-if="order_detail.transaction_status === 'Success'">
                <v-chip color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Not Paid'">
                <v-chip color="#E5EFFF" text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Fail'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Cancel'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
              </span>
              <span v-else-if="order_detail.transaction_status === 'Overdue'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">เกินกำหนดชำระ</v-chip>
              </span>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12">
        <v-row dense class="mb-2">
          <v-col cols="12" md="4">
            <span>วันที่อัปเดตล่าสุด :</span>
            <span class="ml-4">
              <b>{{new Date(order_detail.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</b>
            </span>
          </v-col>
          <v-col cols="12" md="4">
            <span>ส่งคำขอโดย :</span>
            <span class="ml-4"><b>{{order_detail.buyer_name}}</b></span>
          </v-col>
        </v-row>
        </v-col>
        <v-row justify="end" dense class="mr-2 mb-4">
          <v-btn text outlined class="pr-8 pl-8 mr-4" dark color="#27AB9C" style="border: 1px solid #27AB9C;" @click="dialogDelete()">ลบ</v-btn>
          <v-btn dark color="#27AB9C" class="pr-8 pl-8" @click="openModalInputPDF()">แก้ไข</v-btn>
        </v-row>
        <v-card color="#C4C4C4" width="1057px" height="100%" elevation="1">
          <v-card-text align="center">
            <iframe
            :src="pdfShow"
            width="100%" :height="!MobileSize ? '950px' : '450px'"></iframe>
          </v-card-text>
        </v-card>
      </v-row>
    </v-card-text>
    <ModalPDF ref="ModalPDF"/>
  </v-card>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    ModalPDF: () => import('@/components/Shop/CreditTerm/ModalInsertPDF')
  },
  data () {
    return {
      order_number: '',
      order_detail: [],
      pdfShow: '',
      DataPDF: {
        order_number: '',
        seller_shop_id: '',
        credit_term: '',
        pdf: ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      var number = JSON.parse(localStorage.getItem('creditTermOrdernumber'))
      if (val === true) {
        this.$router.push({ path: '/sellerInvoicePDFMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: `/sellerInvoicePDF?order_number=${number}` }).catch(() => {})
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('checkpath')
    this.$EventBus.$on('start', this.start)
    this.order_number = this.$route.query.order_number
    await this.start()
  },
  methods: {
    getTime () {
      return (new Date()).getTime() + Math.round(Math.random())
    },
    async start () {
      this.pdfShow = ''
      this.order_detail = []
      this.order_detail = JSON.parse(Decode.decode(localStorage.getItem('creditTerm')))
      this.pdfShow = this.order_detail.pdf_path + '?nochace=' + this.getTime()
      this.DataPDF.order_number = this.order_number
      this.DataPDF.seller_shop_id = this.order_detail.seller_shop_id
      this.DataPDF.credit_term = this.order_detail.credit_term
      // console.log('get from local', this.order_detail)
      // console.log(this.DataPDF)
    },
    dialogDelete (item) {
      if (this.MobileSize) {
        this.$swal.fire({
          icon: 'warning',
          html: '<h3>คุณต้องการลบเอกสารหรือไม่</h3>',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.DataPDF.pdf = null
            this.confirm(item)
          }
        }).catch(() => {
        })
      } else {
        this.$swal.fire({
          icon: 'warning',
          title: 'คุณต้องการลบเอกสารหรือไม่',
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          confirmButtonColor: '#27AB9C',
          reverseButtons: true
        }).then((result) => {
          if (result.isConfirmed) {
            this.confirm(item)
          }
        }).catch(() => {
        })
      }
    },
    async confirm () {
      await this.$store.dispatch('actionsUploadInvoiceCreditTerm', this.DataPDF)
      var response = await this.$store.state.ModuleShop.stateUploadInvoiceCreditTerm
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: 'ลบเอกสารสำเร็จ', showConfirmButton: false, timer: 2000 })
        if (!this.MobileSize) {
          this.$router.push({ path: `/sellerListCreditTerm?order_number=${this.order_number}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/sellerListCreditTermMobile?order_number=${this.order_number}` }).catch(() => {})
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.result, showConfirmButton: false, timer: 2000 })
      }
      // console.log('data to delete', this.DataPDF)
      // console.log('res form API', response)
    },
    openModalInputPDF () {
      this.$refs.ModalPDF.open()
      this.start()
    },
    goBack () {
      if (!this.MobileSize) {
        this.$router.push({ path: `/sellerListCreditTerm?order_number=${this.order_number}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/sellerListCreditTermMobile?order_number=${this.order_number}` }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.fontSizeTitle {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 24px; color: #333333;
}
</style>
