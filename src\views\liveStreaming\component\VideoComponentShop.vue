<template>
  <div v-if="track">
    <v-row>
      <v-col class="d-flex">
        <h1>{{ participantIdentity + (local ? ' (You)' : '') }}</h1>
        <v-spacer></v-spacer>
        <v-btn color="primary" class="white--text">ปักหมุดสินค้า</v-btn>
      </v-col>
    </v-row>
    <v-row justify="center" style="min-height: 80vh">
      <v-col cols="12" v-if="liveTitle !== ''">
        <span style="font-size: 20px;">หัวข้อการไลฟ์: {{liveTitle}}</span>
      </v-col>
      <v-col cols="8" style="margin-top: 10px;" align="center">
        <v-icon small class="mr-1">mdi-eye</v-icon>{{ participantCount }}
        <video ref="videoElement" :id="track.sid" width="100%" style="transform: scaleX(-1)"></video>
        <audio ref="audioElement" autoplay></audio>
        <v-row dense no-gutters class="pa-1">
          <!-- <v-btn icon @click="controlsVideo('play')"> -->
          <!-- <v-btn icon @click="$emit('pauseVideo')">
            <v-icon v-if="play">mdi-pause</v-icon>
            <v-icon v-else>mdi-play</v-icon>
          </v-btn> -->
          <v-btn icon @click="muteMicrophone">
            <v-icon v-if="mute">mdi-microphone</v-icon>
            <v-icon v-else>mdi-microphone-off</v-icon>
          </v-btn>
          <v-btn icon @click="muteVideo">
            <v-icon v-if="openVideo">mdi-video-outline</v-icon>
            <v-icon v-else>mdi-video-off-outline</v-icon>
          </v-btn>

          <v-btn v-if="room" icon @click="controlsVideo('full')" class="ml-auto">
            <v-icon>mdi-fullscreen</v-icon>
          </v-btn>
        </v-row>
        <v-btn
          outlined
          @click="$emit('leaveRoom')"
          style="border: none;"
          class="pt-4 pb-4"
        >
          <v-icon color="red">mdi-phone-hangup</v-icon>
        </v-btn>
      </v-col>

      <v-col cols="4">
        <div style="background-attachment: scroll;">
          <v-card width="530px" height="80vh" rounded style="display: flex !important; flex-direction: column;">
            <v-card-title style="font-weight: 700; font-size: 18px; line-height: 30px; color:#333333;">
              <p>Chat Room</p>
            </v-card-title>
            <v-card-subtitle><v-divider></v-divider></v-card-subtitle>
            <v-card-text style="flex-grow: 1; overflow: scroll;" ref="messageContainer">
              <div style="height: 400px;">
                <v-card min-height="500px" width="100%" color="#FAFAFA" style="margin-bottom: 20px; border-radius: 10px; box-shadow:0 !important;" class="d-flex" elevation="0">
                  <v-card-text>
                    <div v-for="(item, index) in listMessage" :value="index" :key="index">
                      <div class="pb-2">
                        <span style="font-weight: 700px; font-size: 18px;">{{ item.from }}</span><br/>
                        <span>{{ item.message }}</span>
                      </div>
                    </div>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
            <v-card-text>
              <v-row style="margin-top: 10px;">
                <v-text-field
                  v-model="message"
                  dense
                  hide-details
                  outlined
                  style="max-width: 250px;"
                  @keyup.enter="sendMessage"
                ></v-text-field>
                  <v-btn icon @click="sendMessage">
                    <v-icon>mdi-send</v-icon>
                  </v-btn>
                <!-- <v-btn color="primary" @click="sendMessage" :disabled="message == ''" class="ml-2">Send</v-btn> -->
              </v-row>
            </v-card-text>
          </v-card>
        </div>
      </v-col>
    </v-row>
  </div>
</template>

<script>
import {
  // eslint-disable-next-line camelcase
  DataPacket_Kind,
  RoomEvent
  // LocalVideoTrack,
  // RemoteVideoTrack
} from 'livekit-client'

export default {
  props: {
    track: {
      type: [],
      required: true
    },
    participantIdentity: {
      type: String,
      required: true
    },
    local: {
      type: Boolean,
      default: false
    },
    message: {
      type: String,
      default: ''
    },
    token: {
      type: String,
      default: ''
    },
    room: {
      type: [],
      required: true
    },
    audioTrack: {
      type: Object,
      required: false
    },
    liveTitle: {
      type: String,
      required: false
    }
  },
  data () {
    return {
      videoElement: null,
      APPLICATION_SERVER_URL: '',
      LIVEKIT_URL: '',
      listMessage: [],
      participantCount: 0,
      play: false,
      mute: true,
      openVideo: true,
      RoleUser: '',
      ListproductData: [],
      shopId: '',
      limit: 10
    }
  },
  async mounted () {
    this.configureUrls()
    this.track.attach(this.$refs.videoElement)
    if (this.audioTrack) {
      this.audioTrack.attach(this.$refs.audioElement)
    }
    // console.log(this.track, 'this.track')
    document.addEventListener('click', () => {
      this.$refs.videoElement.muted = false
      this.$refs.videoElement.play().catch(e => console.error('🔇 ไม่สามารถเล่นเสียง:', e))
    }, { once: true })
    if (this.track.kind === 'audio') {
      const audioElement = new Audio()
      this.track.attach(audioElement)
      audioElement.play()
    }
    this.play = true
    if (this.room) {
      // console.log('numParticipants:', this.room.numParticipants, ' numPublishers:', this.room.numPublishers)
      this.participantCount = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
      this.room.on(
        RoomEvent.TrackSubscribed,
        (_track, publication, participant) => {
          this.remoteTracksMap.set(publication.trackSid, {
            trackPublication: publication,
            participantIdentity: participant.identity
          })
          this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
        }
      )

      this.room.on(RoomEvent.ParticipantConnected, (participant) => {
        this.participantCount = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
        // console.log('Video ParticipantConnected')
        // console.log('first', participant)
      })

      this.room.on(RoomEvent.DataReceived, (payload, participant, kind, topic) => {
        // console.log('📡 การรับข้อมูลเริ่มทำงาน')
        const message = new TextDecoder().decode(payload)
        // console.log(`📩 ได้รับข้อความ: ${message}`)
        // console.log(`👤 จาก: ${participant ? participant.identity : 'ไม่ทราบ'}`)
        // console.log(`📦 ประเภท: ${kind}`)
        // console.log(`📌 หัวข้อ: ${topic}`)
        this.listMessage.push({
          from: participant ? participant.identity : 'ไม่ทราบ',
          message: message
        })
      })

      this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        this.participantCount = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
        // console.log('live ParticipantDisconnected')
        // console.log('first', participant)
      })

      this.room.on(RoomEvent.TrackUnsubscribed, (_track, publication, participant) => {
        if (participant.identity === 'Host') {
          this.$EventBus.$emit('leaveRoom')
        }
        // this.remoteTracksMap.delete(publication.trackSid)
        this.$forceUpdate() // อัปเดต UI เมื่อ Map เปลี่ยนแปลง
      })
    }
    this.shopId = JSON.parse(localStorage.getItem('shopSellerID'))
    await this.getProductData(this.shopId)
  },
  beforeDestroy () {
    // this.track.detach()
  },
  watch: {
    track: {
      handler (newValue, oldValue) {
        // console.log('🛑 Props track เปลี่ยนแปลง')
        // console.log('🔹 ค่าเก่า:', oldValue)
        // console.log('🔸 ค่าใหม่:', newValue)
      },
      deep: true,
      immediate: true
    },
    room: {
      handler (newValue, oldValue) {
        // console.log('🔹 ค่าเก่า:', oldValue)
        // console.log('🔸 ค่าใหม่:', newValue)
      },
      deep: true,
      immediate: true
    },
    listMessage () {
      this.$nextTick(() => {
        const el = this.$refs.messageContainer
        // console.log('ได้มั้ย', el)
        if (el && el.scrollTop !== undefined) {
          // console.log('ทดสอบแชทไม่ลงมาล่างสุด')
          el.scrollTop = el.scrollHeight
        }
      })
    }
  },
  methods: {
    controlsVideo (action) {
      this.$refs.videoElement.style.transform = 'scaleX(-1)'
      if (action === 'play') {
        if (this.play) {
          this.play = false
          this.room.localParticipant.videoTracks.forEach((publication) => {
            if (publication.track) {
              publication.track.enabled = false
            }
          })
          document.getElementById(this.track.sid).pause()
        } else {
          this.play = true
          document.getElementById(this.track.sid).play()
        }
      } else {
        // video
        document.getElementById(this.track.sid).requestFullscreen()
        // document.getElementById(this.track.sid).click()
      }
    },
    async sendMessage () {
      try {
        // this.room = new Room()
        // await this.room.connect(this.LIVEKIT_URL, this.token)
        // console.log(this.room.state)
        const message = this.message
        const data = new TextEncoder().encode(message)

        // eslint-disable-next-line camelcase
        await this.room.localParticipant.publishData(data, DataPacket_Kind.RELIABLE, {
          destinationIdentities: [],
          topic: 'chat'
        })
        // console.log('✅ ส่งข้อความสำเร็จ')

        this.listMessage.push({
          from: 'You',
          message: this.message
        })
        this.message = ''
      } catch (error) {
        console.error('❌ ส่งข้อความไม่สำเร็จ:', error)
      }
    },
    configureUrls () {
      if (!this.APPLICATION_SERVER_URL) {
        if (window.location.hostname === 'localhost') {
          this.APPLICATION_SERVER_URL = 'http://localhost:6080/'
        } else {
          this.APPLICATION_SERVER_URL = `https://${window.location.hostname}:6443/`
        }
      }

      if (!this.LIVEKIT_URL) {
        if (window.location.hostname === 'localhost') {
          // this.LIVEKIT_URL = 'wss://helloworld-nt1b7zmh.livekit.cloud'
          this.LIVEKIT_URL = 'wss://meet-lab.one.th'
        } else {
          this.LIVEKIT_URL = `wss://${window.location.hostname}:7443/`
        }
      }
    },
    muteMicrophone () {
      const audioPublication = this.room.localParticipant.audioTrackPublications.values().next().value
      if (audioPublication) {
        if (this.mute) {
          audioPublication.track.mute()
          this.mute = !this.mute
          // console.log('🔇 ปิดไมค์เรียบร้อย')
        } else {
          audioPublication.track.unmute()
          this.mute = !this.mute
        }
      }
    },
    muteVideo () {
      const videoPublication = this.room.localParticipant.videoTrackPublications.values().next().value
      if (videoPublication) {
        if (this.openVideo) {
          videoPublication.track.mute()
          this.openVideo = !this.openVideo
          // console.log('📷 ปิดกล้องเรียบร้อย')
        } else {
          videoPublication.track.unmute()
          // console.log('📷 เปิดกล้องเรียบร้อย')
          this.openVideo = !this.openVideo
        }
      }
    },
    async getProductData (shopId) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
      var companyID = '-1'
      var dataBestSeller = {
        role_user: this.RoleUser,
        company_id: companyID === null ? '-1' : companyID,
        category: '',
        seller_shop_id: shopId,
        orderBy: '',
        status_product: '',
        limit: 10,
        page: -1
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', dataBestSeller)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      if (response.ok === 'y') {
        this.ListproductData = await [...response.query_result]
        console.log(this.ListproductData)
        this.isLoading = true
      } else if (response.code === 400) {
        this.ListproductData = []
      }
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative;
  background: #3b3b3b;
  aspect-ratio: 16/9;
  border-radius: 6px;
  overflow: hidden;
}

.video-container video {
  width: 100%;
  height: 100%;
}

.video-container .participant-data {
  position: absolute;
  top: 0;
  left: 0;
}

.participant-data p {
  background: #f8f8f8;
  margin: 0;
  padding: 0 5px;
  color: #777777;
  font-weight: bold;
  border-bottom-right-radius: 4px;
}

/* Media Queries */
@media screen and (max-width: 480px) {
  .video-container {
    aspect-ratio: 9/16;
  }
}
</style>
