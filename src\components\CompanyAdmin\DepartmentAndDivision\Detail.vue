<template>
  <div>
    <v-card outlined>
      <v-card-text>
        <v-subheader><h2>รายละเอียดบริษัท</h2></v-subheader>
      </v-card-text>
      <v-divider></v-divider>
      <div class="pa-5">
        <v-row>
          <v-col cols="12" md="12">
            <v-row>
              <v-col cols="3">
                ชื่อบริษัท (ไทย)
              </v-col>
              <v-col cols="3">
                บริษัท ลิเวอร์พูล จำกัด
              </v-col>
              <v-col cols="3">
                ชื่อบริษัท (อังกฤษ)
              </v-col>
              <v-col cols="3">
                liverpool
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col cols="3">
                รหัสบริษัท
              </v-col>
              <v-col cols="3">
                Ballllll
              </v-col>
              <v-col cols="3">
                ระยะเวลา
              </v-col>
              <v-col cols="3">
                1 ปี
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </div>
      <div class="ma-5">
        <v-card outlined color="grey">
          <v-tabs v-model="tab" background-color="transparent" color="white" grow dark>
            <v-tab v-for="item in items" :key="item">
              {{ item }}
            </v-tab>
          </v-tabs>
          <v-divider></v-divider>
          <v-tabs-items v-model="tab">
            <v-tab-item>
              <v-card>
                <div class="comtainer pa-10">
                  <v-row>
                    <v-col cols="12" md="12">
                      <v-row>
                        <v-col cols="3">
                          รหัสแผนก:
                        </v-col>
                        <v-col cols="3">
                          liver02
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12">
                      <v-row>
                        <v-col cols="3">
                          ชื่อแผนก (ภาษาไทย):
                        </v-col>
                        <v-col cols="3">
                          แผนกจัดซื้อลิเวอร์
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12">
                      <v-row>
                        <v-col cols="3">
                          ชื่อแผนก (ภาษาอังกฤษ):
                        </v-col>
                        <v-col cols="3">
                          liver
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12">
                      <v-row>
                        <v-col cols="3">
                          สาขา:
                        </v-col>
                        <v-col cols="3">
                          ลิเวอร์ย่อย
                        </v-col>
                      </v-row>
                    </v-col>
                  </v-row>
                </div>
              </v-card>
            </v-tab-item>
            <v-tab-item>
              <div class="container pa-10" align="center">
                <v-card width="400">
                  <v-data-table :headers="headersamount" :items="amount" class="elevation-0, rounded-lg" hide-default-footer/>
                </v-card>
              </div>
            </v-tab-item>
            <v-tab-item>
              <v-card>
                <v-card-text>testttt3</v-card-text>
              </v-card>
            </v-tab-item>
            <v-tab-item>
              <v-card>
                <v-card-text>testttt4</v-card-text>
              </v-card>
            </v-tab-item>
            <v-tab-item>
              <v-card>
                <v-card-text>testttt5</v-card-text>
              </v-card>
            </v-tab-item>
            <v-tab-item>
              <v-card>
                <v-card-text>testttt6</v-card-text>
              </v-card>
            </v-tab-item>
          </v-tabs-items>
        </v-card>
      </div>
      <v-card-actions>
        <v-btn color="primary" outlined rounded @click="goPage('/departmentanddivision')">ย้อนกลับ</v-btn>
      </v-card-actions>
    </v-card>
  </div>
</template>

<script>
export default {
  data () {
    return {
      tab: null,
      items: ['แผนก', 'วงเงิน', 'ที่อยู่ใบเสร็จ', 'ที่อยู่ที่จัดส่ง', 'ผู้ซื้อ', 'แค็ตตาล็อก'],
      text: 'Lorem ipsum dolor sit amet',
      amount: [
        {
          type: 'ปี',
          budget: 12000000
        },
        {
          type: 'เดือน',
          budget: 1200000
        }
      ],
      headersamount: [
        { text: 'ปี', value: 'type', sortable: false },
        { text: 'วงเงิน', value: 'budget', sortable: false }
      ]
    }
  },
  watch: {
    tab (val) {
      // console.log('vall tab ', val)
    }
  },
  created () {
  },
  methods: {
    goPage (str) {
      this.$router.push(str)
    }
  }
}
</script>

<style scoped>
</style>
