<template>
    <v-container width="100%" height="100%" style="background: #FFFFFF; border: 0px solid; border-radius: 8px;">
      <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" >
          <v-row no-gutters class="d-flex" align="center">
              <v-col cols="7">
                  <v-card-title class="pl-0" style="font-weight: 700; font-size: 22px; line-height: 32px;" v-if="!MobileSize">{{ $t('reportOrderAffiliate.PageTitle') }} </v-card-title>
                  <v-card-title class="px-0" style="font-weight: 700; font-size: 18px; line-height: 32px;" v-else>
                    <v-icon color="#1AB759" class="mr-2" @click="backToUsr()">mdi-chevron-left</v-icon>{{ $t('reportOrderAffiliate.PageTitle') }}
                  </v-card-title>
              </v-col>
          </v-row>
      </v-card>
      <!-- filter ข้อมูล -->
      <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-if="!MobileSize">
          <v-row justify="center" >
            <v-col :cols="IpadProSize ? '6' : IpadSize ? '9' : '4'" :class="IpadSize ? 'pt-0': ''">
              <v-row dense>
                <span style="font-size: 14px; font-weight: 500; color: #333333;" :class="IpadSize ? 'pt-2 pr-2' : 'pt-2 pr-2 '"> {{ $t('reportOrderAffiliate.OrderID') }} : </span>
                <v-text-field v-model="searchOrderId" dense outlined style="border-radius: 8px; width: 40%;" :placeholder="$t('reportOrderAffiliate.OrderID')">
                </v-text-field>
              </v-row>
            </v-col>
            <v-col :cols="IpadProSize ? '6' : IpadSize ? '9' : '5'" :class="IpadSize ? 'pt-0': ''" >
                <v-row dense>
                  <span style="font-size: 14px; font-weight: 500; color: #333333;" :class="IpadSize ? 'pt-2 pr-2 pl-5' :' pr-2 pt-2'"> {{ $t('reportOrderAffiliate.OrderDate') }} : </span>
                  <v-dialog v-model="modalDateSelect" class="d-inline-block" width="480px">
                    <template v-slot:activator="{ on, attrs }">
                      <v-text-field v-model="rangeDate" readonly dense outlined style="border-radius: 8px;" :placeholder="$t('reportOrderAffiliate.SearchOrderDate')" v-bind="attrs" v-on="on">
                        <v-spacer></v-spacer>
                        <v-icon slot="append">mdi-calendar-month</v-icon>
                      </v-text-field>
                    </template>
                    <v-date-picker
                      v-model="dates"
                      full-width
                      range
                      reactive
                      no-title
                      :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                      :min="minDate"
                      :max="
                        new Date(
                          Date.now() - new Date().getTimezoneOffset() * 60000
                        )
                          .toISOString()
                          .substr(0, 10)
                      ">
                      <v-row>
                        <v-col align="end">
                          <v-btn text color="primary" @click="closeDialog(dates)">{{ $t('reportOrderAffiliate.Cancel') }}</v-btn>
                          <v-btn text color="primary" @click="saveDialog(dates)">{{ $t('reportOrderAffiliate.Confirm') }}</v-btn>
                        </v-col>
                      </v-row>
                    </v-date-picker>
                  </v-dialog>
                </v-row>
            </v-col>
            <v-col :cols="IpadProSize ? '6' : IpadSize ? '9' : '3'" :class="IpadSize ? 'pt-0': ''">
                <v-row dense>
                  <span style="font-size: 14px; font-weight: 500; color: #333333;" :class="IpadSize ? 'pt-2 pr-2 pl-3' :'pr-2 pt-2 '"> {{ $t('reportOrderAffiliate.OrderStatus') }} :</span>
                  <v-select item-text="name" item-value="value" hide-details v-model="selected.value" :items="itemSelected" dense outlined style="border-radius: 8px; width: 10px;"></v-select>
                </v-row>
            </v-col>
          </v-row>
      </v-card>
      <!-- MobileSize -->
      <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-else>
          <!-- <v-row justify="center" dense no-gutters>
            <v-col cols="4" class="pt-1">
              <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pl-10"> ชื่อสินค้า :</span>
            </v-col>
            <v-col cols="7">
              <v-text-field v-model="searchName" dense outlined style="border-radius: 8px;" placeholder="ชื่อสินค้า">
              </v-text-field>
            </v-col>
          </v-row> -->
          <v-row justify="center" dense no-gutters>
            <v-col cols="4" class="pt-1">
              <span style="font-size: 14px; font-weight: 500; color: #333333;"> {{ $t('reportOrderAffiliate.OrderID') }} :</span>
            </v-col>
            <v-col cols="7">
              <v-text-field v-model="searchOrderId" dense outlined style="border-radius: 8px;" :placeholder="$t('reportOrderAffiliate.OrderID')">
              </v-text-field>
            </v-col>
          </v-row>
          <v-row justify="center" no-gutters>
            <v-col cols="4" class="pt-1">
              <span style="font-size: 14px; font-weight: 500; color: #333333;" class="pt-3 pl-7"> {{ $t('reportOrderAffiliate.OrderDate') }} : </span>
            </v-col>
            <v-dialog v-model="modalDateSelect" class="d-inline-block" :return-value.sync="date" width="480px">
            <template v-slot:activator="{ on, attrs }">
              <v-col cols="7">
                <v-text-field v-model="rangeDate" readonly dense outlined style="border-radius: 8px;" :placeholder="$t('reportOrderAffiliate.SearchOrderDate')" v-bind="attrs" v-on="on">
                <v-spacer></v-spacer>
                <v-icon slot="append">mdi-calendar-month</v-icon>
              </v-text-field>
              </v-col>
            </template>
            <v-date-picker
              v-model="dates"
              full-width
              range
              reactive
              no-title
              :locale="$i18n.locale === 'th' ? 'th' : 'en'"
              :min="minDate"
              :max="
                new Date(
                  Date.now() - new Date().getTimezoneOffset() * 60000
                )
                  .toISOString()
                  .substr(0, 10)
              ">
              <v-row>
                <v-col align="end">
                  <v-btn text color="primary" @click="closeDialog(dates)">{{ $t('reportOrderAffiliate.Cancel') }}</v-btn>
                  <v-btn text color="primary" @click="saveDialog(dates)">{{ $t('reportOrderAffiliate.Confirm') }}</v-btn>
                </v-col>
              </v-row>
            </v-date-picker>
            </v-dialog>
          </v-row>
          <v-row justify="center" no-gutters>
            <v-col cols="4" class="pt-1">
              <span style="font-size: 14px; font-weight: 500; color: #333333;" class="pl-4"> {{ $t('reportOrderAffiliate.OrderStatus') }} :</span>
            </v-col>
            <v-col cols="7">
              <v-select item-text="name" item-value="value" v-model="selected.value" :items="itemSelected" dense outlined style="border-radius: 8px;" @click="orderTable"></v-select>
            </v-col>
          </v-row>
          <!-- <v-row justify="center" dense >
            <span style="font-size: 16px; font-weight: 500; color: #333333;" class="pr-2 pt-3 pl-15"> ชื่อสินค้า :</span>
            <v-col cols="3">
              <v-text-field v-model="searchClickId" dense outlined style="border-radius: 8px;" placeholder="ค้นหาเวลารหัสคลิก"></v-text-field>
            </v-col>
          </v-row> -->
          <!-- <v-row dense justify="center" class="mt-1" >
            <v-btn dark style="border-radius: 40px; background: red;" height="32px" @click="clearSearch">
              <v-icon small class="mr-1">mdi-restart</v-icon>ล้างค่า</v-btn>
          </v-row> -->
      </v-card>
      <v-row justify="center" :class="IpadProSize ? 'mt-7 mb-6' : IpadSize ? 'mt-7 mb-6' : 'mb-6'" >
        <v-btn dark style="border-radius: 40px; background: red;" height="32px" width="100px" class="mr-5" @click="clearSearch(options.page = 1)">
          <v-icon small>mdi-restart</v-icon>{{ $t('reportOrderAffiliate.Clear') }}</v-btn>
        <v-btn dark style="border-radius: 40px; background: #27AB9C;" height="32px" width="100px" @click="orderTable(options.page = 1)">
          <v-icon small>mdi-magnify</v-icon>{{ $t('reportOrderAffiliate.Search') }}</v-btn>
      </v-row>
        <!-- ตารางข้อมูล -->
      <v-card class="mb-2" style="border-radius: 8px;" elevation="0">
        <v-row>
          <v-col cols="9">
            <span class="ml-2 mt-3" style=" font-size: 16px; font-style: normal; font-weight: 600; color: #27AB9C">{{ $t('reportOrderAffiliate.Total') }} ({{ totalItems }} {{ $t('reportOrderAffiliate.Items') }})</span>
          </v-col>
          <v-col class="d-flex justify-end" cols="3">
            <v-btn @click="getReportExcel()" style="border-radius: 40px; background: #27AB9C;" height="32px">
              <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
              <span class="ml-1" style="font-size: 14px; font-style: normal; font-weight: 500; color: #FFFFFF">Export</span>
            </v-btn>
          </v-col>
        </v-row>
      </v-card>
      <v-card outlined class="mb-4 mt-2" v-if="disableTable === false">
        <v-data-table :options.sync="options" :server-items-length="totalItems" :items-per-page="options.itemsPerPage" @update:options="updateOptions" :headers="header" :items="tableData" elevation="1" :no-data-text="$t('reportOrderAffiliate.NoData')" :height="MobileSize ? '350px':''">
            <template v-slot:[`item.order_row.date`]="{ item }">
                {{new Date(item.order_row.date).toLocaleDateString($i18n.locale === 'th' ? 'th-TH' : 'en-US', { year: 'numeric', month: 'long', day: 'numeric' })}}
            </template>
            <template v-slot:[`item.order_details`]="{ item }">
              <v-row dense >
                <v-btn
                  x-small
                  outlined
                  @click = "openDialogDetail(item)"
                  style="border: none; width: 100%;"
                  height="100%"
                  class="pt-4 pb-4">
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.order_row.transaction_status`]="{item}">
              <v-chip :color="changeColorBackgroundStatus(item.order_row.transaction_status)" :text-color="changeColorStatus(item.order_row.transaction_status)">
                 {{ getTextStatatus(item.order_row.transaction_status)}}
              </v-chip>
            </template>
        </v-data-table>
      </v-card>
      <v-row justify="center" align-content="center" v-if="disableTable === true">
        <v-col cols="12" align="center">
          <div class="my-5">
            <v-img
              src="@/assets/emptypo.png"
              max-height="500px"
              max-width="500px"
              height="100%"
              width="100%"
              contain
              aspect-ratio="2">
            </v-img>
          </div>
          <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c">
            <b>{{ $t('reportOrderAffiliate.NoOrders') }}</b>
          </h2>
        </v-col>
      </v-row>
      <v-dialog
        v-model = "dialog_detail"
        width="732"
        scrollable>
        <v-card height="100%">
          <v-toolbar flat color="#E6F5F3">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title>
                  <span style="color: #27AB9C;">
                    <b>
                     {{ $t('reportOrderAffiliate.OrderDetailTitle') }} ({{order_details.length}} {{ $t('reportOrderAffiliate.Items') }})
                    </b>
                  </span>
                </v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small icon @click="closeDialogDetail()"><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-container>
            <v-row justify="center" align-content="center" class="px-5">
              <v-col cols="12" v-for="(item, index) in order_details" :key="index">
                <v-card outlined style="background-color: #FFFFFF;" v-if="!MobileSize">
                  <v-card-text>
                    <div class="d-flex flex-no-wrap">
                        <v-avatar v-if="item.color_image_path === null || item.color_image_path === ''" class="ma-4" size="172" tile>
                      <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" />
                        </v-avatar>
                        <v-avatar v-else class="ma-4" size="172" tile>
                          <!-- ถ้าต้องการทำ api ดูใน DetailAdminPanitModal.vue -->
                          <v-img :src="item.color_image_path" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;"></v-img>
                        </v-avatar>
                        <div class="ma-4" >
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductName') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;">{{ item.name }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductType') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_1 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductAttribute') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_2 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductPrice') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.product_price }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.Quantity') }} :  </span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.quantity }}</span> </v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span v-if="item.commission_type === 'percent'">{{ $t('reportOrderAffiliate.CommissionPercent') }} :</span>
                            <span v-if="item.commission_type === 'baht'">{{ $t('reportOrderAffiliate.CommissionBaht') }} :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.commission_rate }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.CommissionValue') }} :  </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C">{{ item.commission_received }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.TaxDeducted') }} :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.influencer_tax }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.NetCommission') }} :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.influencer_payment }}</span></v-col>
                          </v-row>
                        </div>
                    </div>
                  </v-card-text>
                </v-card>
                <v-card outlined style="background-color: #FFFFFF;" v-else>
                  <v-card-text>
                    <v-row justify="center">
                      <v-col cols="12" class="d-flex justify-center">
                        <v-avatar v-if="item.color_image_path === null || item.color_image_path === ''" class="ma-4" size="172" tile>
                      <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" />
                        </v-avatar>
                        <v-avatar v-else class="ma-4" size="172" tile>
                          <!-- ถ้าต้องการทำ api ดูใน DetailAdminPanitModal.vue -->
                          <v-img :src="item.color_image_path" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row no-gutters>
                      <v-col cols="12">
                        <div class="ma-4" >
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductName') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;">{{ item.name }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductType') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_1 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductAttribute') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_2 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.ProductPrice') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.product_price }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.Quantity') }} :  </span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.quantity }}</span> </v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span v-if="item.commission_type === 'percent'">{{ $t('reportOrderAffiliate.CommissionPercent') }} :</span>
                            <span v-if="item.commission_type === 'baht'">{{ $t('reportOrderAffiliate.CommissionBaht') }} :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.commission_rate }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.CommissionValue') }} :  </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C">{{ item.commission_received }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('reportOrderAffiliate.TaxDeducted') }} :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.influencer_tax }}</span></v-col>
                          </v-row>
                          <v-row no-gutters>
                            <span>{{ $t('reportOrderAffiliate.NetCommission') }} :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.influencer_payment }}</span></v-col>
                          </v-row>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-dialog>
    </v-container>
  </template>
<script>

import { Decode } from '@/services'
export default {
  data () {
    return {
      // startDate: '',
      // endDate: '',
      disableTable: true,
      searchName: '',
      header: [
        {
          text: this.$t('reportOrderAffiliate.OrderDateTitle'),
          align: 'center',
          width: '150px',
          sortable: false,
          value: 'order_row.date',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('reportOrderAffiliate.OrderID'),
          sortable: false,
          align: 'center',
          value: 'order_row.order_number',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('reportOrderAffiliate.OrderStatus'),
          sortable: false,
          align: 'center',
          value: 'order_row.transaction_status',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('reportOrderAffiliate.TotalCommission'),
          sortable: false,
          align: 'center',
          value: 'order_row.total_commission',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: this.$t('reportOrderAffiliate.Detail'),
          sortable: false,
          align: 'center',
          value: 'order_details',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        }
      ],
      dialog_detail: false,
      selected: { name: this.$t('reportOrderAffiliate.All'), value: '' },
      itemSelected: [
        { name: this.$t('reportOrderAffiliate.All'), value: '' },
        { name: this.$t('reportOrderAffiliate.Success'), value: 'Success' },
        { name: this.$t('reportOrderAffiliate.NotPaid'), value: 'Not Paid' },
        { name: this.$t('reportOrderAffiliate.CancelStatus'), value: 'Cancel' },
        { name: this.$t('reportOrderAffiliate.Fail'), value: 'Fail' },
        { name: this.$t('reportOrderAffiliate.Pending'), value: 'Pending' },
        { name: this.$t('reportOrderAffiliate.Approve'), value: 'Approve' },
        { name: this.$t('reportOrderAffiliate.Credit'), value: 'Credit' },
        { name: this.$t('reportOrderAffiliate.DataIncomplete'), value: 'Data Incomplete' },
        { name: this.$t('reportOrderAffiliate.Refund'), value: 'Refund' }
      ],
      searchOrderId: '',
      searchRegion: '',
      searchSubId: '',
      searchRangeDate: [],
      dates: [],
      tableData: [],
      totalLists: 0,
      modalDateSelect: false,
      DataTables: [],
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31',
      order_details: [],
      options: {
        page: 1,
        itemsPerPage: 5
      },
      totalItems: 0,
      totalPages: 0
    }
  },
  created () {
    // var startDate = new Date(this.selectedYear, 0, 1)
    // var endDate = new Date(this.selectedYear, 11, 31)
    // this.startDate = new Date(startDate.getTime() - (startDate.getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    // this.endDate = new Date(endDate.getTime() - (endDate.getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    this.checkConsent()
    this.$EventBus.$emit('changeNavAccount')
    this.orderTable()
  },
  computed: {
    rangeDate () {
      return this.dates.map(date => new Date(date).toLocaleDateString(this.$i18n.locale === 'th' ? 'th-TH' : 'en-US')).join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // filterItems () {
    //   const filteredItems = this.tableData.filter(item => {
    //     const itemDate = new Date(item.order_row.date)
    //     const startDate = new Date(this.searchRangeDate[0])
    //     const endDate = new Date(this.searchRangeDate[1])
    //     // console.log(itemDate, startDate, endDate)
    //     // endDate.setHours(23, 59, 59, 999)
    //     if (this.searchRangeDate.length === 0) {
    //       return item.order_row.order_number.toLowerCase().trim().includes(this.searchOrderId.toLowerCase().trim()) &&
    //       (this.selected.value === '' || item.order_row.transaction_status === this.selected.value)
    //     }
    //     if (this.searchRangeDate.length === 1) {
    //       return item.order_row.order_number.toLowerCase().trim().includes(this.searchOrderId.toLowerCase().trim()) &&
    //         (this.selected.value === '' || item.order_row.transaction_status === this.selected.value) &&
    //         item.order_row.date.includes(this.searchRangeDate)
    //     } else {
    //       return item.order_row.order_number.toLowerCase().trim().includes(this.searchOrderId.toLowerCase().trim()) &&
    //         (this.selected.value === '' || item.order_row.transaction_status === this.selected.value) &&
    //         (itemDate >= startDate && itemDate <= endDate)
    //     }
    //   })
    //   return filteredItems.sort((a, b) => new Date(b.order_row.date) - new Date(a.order_row.date))
    // }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/orderedBuyerAffilateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderedBuyerAffilate' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    backToUsr () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async orderTable () {
      this.$store.commit('openLoader')
      // const currentYear = new Date().getFullYear()
      // const startDateString = `${currentYear}-01-01`
      // const endDateString = `${currentYear}-12-31`
      // console.log('Start Date: ', startDateString)
      // console.log('End Date: ', endDateString)
      var data = {
        start: this.dates.length === 0 ? '' : this.dates[0],
        end: this.dates.length === 0 ? '' : this.dates.length === 1 ? this.dates[0] : this.dates[1],
        filter: 'year',
        role_user: 'ext_buyer',
        pages: this.options.page,
        count: this.options.itemsPerPage,
        search: this.searchOrderId,
        transaction_status: this.selected.value
      }
      await this.$store.dispatch('actionOrderReport', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateOrderReport
      if (response.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        if (response.ok === 'y') {
          this.tableData = response.query_result.response
          this.totalItems = response.query_result.max_items
          this.totalPages = response.query_result.max_pages
          // console.log(this.tableData, 'table')
          if (this.tableData.length > 0) {
            this.disableTable = false
          }
        }
      }
      this.$store.commit('closeLoader')
    },
    updateOptions (options) {
      this.options = options
      this.orderTable()
    },
    // async getClickReportData () {
    // //   var userId = JSON.parse(Decode.decode(localStorage.getItem('oneData'))) ห้ามลบ!!!!!!
    //   var data = {
    //     // user_id: userId.user.user_id ห้ามลบ!!!!!!
    //     user_id: 119
    //   }
    //   await this.$store.dispatch('actionClickReportList', data)
    //   var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateClickReportList
    //   this.tableData = response
    //   console.log('can fetch :', response)
    //   // if (response.length === '' || response.length === 'null' || response.length === 'undefined') {
    //   //   this.totalLists = 0
    //   // } else {
    //   //   this.totalLists = this.filterItems.length
    //   // }
    //   console.log('totalLists :', this.totalLists)
    // },
    // async getReportExcel () {
    //   const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   var data = 119
    //   await this.axios({
    //     url: `${process.env.VUE_APP_BACK_END2}affiliate/buyer/clickReportExcel/${data}`,
    //     headers: { Authorization: `Bearer ${oneData.user.access_token}` },
    //     method: 'GET',
    //     responseType: 'blob'
    //   }).then((response) => {
    //     const fileURL = window.URL.createObjectURL(new Blob([response.data]))
    //     const fileLink = document.createElement('a')
    //     fileLink.href = fileURL
    //     fileLink.setAttribute('download', 'click-report.xlsx')
    //     document.body.appendChild(fileLink)
    //     fileLink.click()
    //   })
    // },
    openDialogDetail (item) {
      this.dialog_detail = true
      this.order_details = item.order_details
      // console.log('this.order_details', this.order_details)
    },
    closeDialogDetail () {
      this.dialog_detail = false
    },
    saveDialog (date) {
      date.sort((a, b) => a.localeCompare(b))
      this.dates = date
      this.searchRangeDate = this.dates
      this.modalDateSelect = false
    },
    closeDialog (date) {
      date = []
      this.searchRangeDate = date
      this.dates = date
      this.modalDateSelect = false
    },
    async getReportExcel () {
      if (this.totalItems > 0) {
        const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        var data = {
          start: this.dates.length === 0 ? '' : this.dates[0],
          end: this.dates.length === 0 ? '' : this.dates.length === 1 ? this.dates[0] : this.dates[1],
          role_user: 'ext_buyer',
          exportdashboard: 'report_buyer',
          seller_shop_id: -1,
          pages: this.options.page,
          count: this.totalItems,
          search: this.searchOrderId,
          transaction_status: this.selected.value
        }
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}affiliate_dashboard/exportAffiliateDashboard`,
          data,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          responseType: 'blob'
        }).then((response) => {
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'order-report.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        })
      } else {
        this.$swal.fire({ icon: 'warning', text: this.$t('reportOrderAffiliate.NoOrders'), showConfirmButton: false, timer: 2000 })
      }
    },
    async clearSearch () {
      this.dates = []
      this.searchOrderId = ''
      this.selected = { name: this.$t('reportOrderAffiliate.All'), value: '' }
      await this.orderTable()
    },
    getTextStatatus (item) {
      if (item === 'Success') {
        return this.$t('reportOrderAffiliate.Success')
      } else if (item === 'Not Paid') {
        return this.$t('reportOrderAffiliate.NotPaid')
      } else if (item === 'Cancel') {
        return this.$t('reportOrderAffiliate.CancelStatus')
      } else if (item === 'Fail') {
        return this.$t('reportOrderAffiliate.Fail')
      } else if (item === 'Pending') {
        return this.$t('reportOrderAffiliate.Pending')
      } else if (item === 'Approve') {
        return this.$t('reportOrderAffiliate.Approve')
      } else if (item === 'Credit') {
        return this.$t('reportOrderAffiliate.Credit')
      } else if (item === 'Data Incomplete') {
        return this.$t('reportOrderAffiliate.DataIncomplete')
      } else if (item === 'Refund') {
        return this.$t('reportOrderAffiliate.Refund')
      }
    },
    changeColorBackgroundStatus (status) {
      if (status === 'Success') {
        return '#f0f9ee'
      } else if (status === 'Pending' || status === 'Not Paid' || status === 'Cancel' || status === 'Fail' || status === 'Approve' || status === 'Credit' || status === 'Refund' || status === 'Data Incomplete') {
        return '#fcf0da'
      }
    },
    changeColorStatus (status) {
      if (status === 'Success') {
        return '#1ab759'
      } else if (status === 'Pending' || status === 'Not Paid' || status === 'Cancel' || status === 'Fail' || status === 'Approve' || status === 'Credit' || status === 'Refund' || status === 'Data Incomplete') {
        return '#e9a016'
      }
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
