<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="openDialogCancel()">mdi-chevron-left</v-icon> สร้างโปรไฟล์การสืบย้อนกลับข้อมูลสินค้า (Product Traceability Profile)</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="openDialogCancel()">mdi-chevron-left</v-icon> สร้างโปรไฟล์การสืบย้อนกลับข้อมูลสินค้า<br>(Product Traceability Profile)</v-card-title>

       <v-col cols="12">
        <v-row>
          <v-col cols="12">
            <v-card elevation="0">
              <v-card-title style="background-color: #F7FFFC; border-radius: 10px;">
                <v-col cols="6" class="pa-0">
                  <v-avatar rounded icon size="30" class="mr-2">
                    <v-img contain :src="require('@/assets/TraceabilityProfile/documentation.png')"></v-img>
                  </v-avatar>
                  <span style="color: #27AB9C;"><b>ข้อมูลทั่วไป</b></span>
                </v-col>
                <v-col cols="6" class="text-right pa-0">
                  <div>
                    <v-btn fab small color="#27AB9C" @click="show1 = !show1">
                      <v-icon color="white">{{ show1 ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
                    </v-btn>
                  </div>
                </v-col>
              </v-card-title>
              <v-card-text class="pt-5" v-show="show1">
                <v-row>
                  <v-col cols="12" md="6">
                    <span style="font-size: 16px; color: #333333;">หมวดหมู่</span>
                    <v-select
                      v-model="Detail.category"
                      :items="categories"
                      item-text="name"
                      item-value="name"
                      label="เลือกหมวดหมู่"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      ></v-select>
                  </v-col>
                  <v-col cols="12" md="6">
                    <span style="font-size: 16px; color: #333333;">ชื่อโปรไฟล์</span>
                    <v-text-field
                      v-model="Detail.profileName"
                      label="ระบุชื่อโปรไฟล์"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      ></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16px; color: #333333;">เพิ่มรูปภาพ</span>
                    <v-row dense>
                      <v-col cols="12" md="12" class="mt-2">
                        <v-row dense>
                          <draggable v-model="DataToShowImageProfile"  v-if="DataToShowImageProfile.length !== 0" :move="onMove" @start="drag=true" @end="drag=false" class="row fill-height align-center sortable-list">
                            <v-col cols="3" md="2" sm="4" v-for="(item, index) in DataToShowImageProfile" :key="index" :class="!MobileSize ? 'mt-2' : 'px-1'">
                              <v-card outlined class="pa-1" width="140px" height="140px" v-if="statusPage !== 'Edit' && DataToShowImageProfile.length !== 0" style="justify-content: center; display: flex; text-align: center;">
                                <v-img :src="item.media_path" :lazy-src="item.media_path" max-width="100%" max-height="100%">
                                  <v-btn icon x-small style="float: right; background-color: #9A9A9A;">
                                    <v-icon x-small color="white" dark @click="RemoveImageShow(index, item)" @TouchStart="RemoveImageShow(index, item)">mdi-close</v-icon>
                                  </v-btn>
                                </v-img>
                              </v-card>
                              <v-card outlined class="pa-1" width="80" height="80" v-else-if="statusPage === 'Edit' && DataToShowImageProfile.length !== 0">
                                <v-img :src="item.media_path" :lazy-src="item.media_path" max-width="64" max-height="64" style="justify-content: center; display: flex; text-align: center;">
                                  <v-btn icon x-small style="float: right; background-color: #9A9A9A;">
                                    <v-icon x-small color="white" dark @click="RemoveImageShow(index, item)" @TouchStart="RemoveImageShow(index, item)">mdi-close</v-icon>
                                  </v-btn>
                                </v-img>
                              </v-card>
                            </v-col>
                            <v-col cols="3" md="2" sm="4" v-if="DataToShowImageProfile.length !== 5 && DataToShowImageProfile.length !== 0" :class="MobileSize ? '' : 'mt-2'" slot="footer" key="footer">
                              <v-card width="140px" height="140px" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="openDialogUploadImage()" >
                                <v-card-text class="pt-13">
                                  <v-row no-gutters align="center" justify="center">
                                    <v-col cols="12" md="12">
                                      <v-row justify="center" align="center">
                                        <v-col cols="12" md="12" style="text-align: center;">
                                          <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br/>
                                          <span style="font-weight: 500; line-height: 24px; color: #1B5DD6;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'">เพิ่มรูป</span>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </draggable>
                          <v-col cols="12" v-if="DataToShowImageProfile.length === 0">
                            <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                              <v-card-text>
                                <v-row no-gutters align="center" justify="center">
                                  <v-col cols="12" align="center" class="mt-6">
                                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เพิ่มรูป</span>
                                  </v-col>
                                  <v-col cols="12" align="center" class="my-5">
                                    <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png')" max-width="167" max-height="100"></v-img>
                                  </v-col>
                                  <v-col cols="12" md="12">
                                    <v-row justify="center" align="center">
                                      <v-col cols="12" md="12" style="text-align: center;">
                                        <span style="line-height: 24px; font-weight: 400; color: #333333;" :style="IpadSize ? 'font-size: 14px;' : MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'">ไฟล์รูปควรมีขนาดไม่เกิน 2 MB และใส่ได้สูงสุด 5 รูป</span><br/>
                                        <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 12px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 555 x 555 px และรองรับไฟล์นามสกุล .JPG, .JPEG, .PNG)</span>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                  <v-col cols="12" align="center" class="mt-5 mb-6">
                                    <v-btn color="#1B5DD6" text @click="openUploadImage()"><v-icon>mdi-cloud-upload-outline</v-icon><span style="font-size: 16px; font-weight: 500; text-decoration: underline;" class="pl-1">อัปโหลดรูป</span></v-btn>
                                  </v-col>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16; color: #333333;">Storyteliing / เรื่องราวของสินค้า</span>
                    <v-textarea
                      v-model="Detail.story"
                      label="ระบุ Storyteliing / เรื่องราวของสินค้า"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      >
                    </v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12">
            <v-card elevation="0">
              <v-card-title style="background-color: #F7FFFC; border-radius: 10px;">
                <v-col cols="6" class="pa-0">
                  <v-avatar rounded icon size="30" class="mr-2">
                    <v-img contain :src="require('@/assets/TraceabilityProfile/list.png')"></v-img>
                  </v-avatar>
                  <span style="color: #27AB9C;"><b>มาตรฐาน และใบรับรองที่เกี่ยวข้อง</b></span>
                </v-col>
                <v-col cols="6" class="text-right pa-0">
                  <div>
                    <v-btn fab small color="#27AB9C" @click="show2 = !show2">
                      <v-icon color="white">{{ show2 ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
                    </v-btn>
                  </div>
                </v-col>
              </v-card-title>
              <v-card-text class="pt-5" v-show="show2">
                <v-row>
                  <v-col cols="12" md="6">
                    <span style="font-size: 16px; color: #333333;">ชื่อใบรับรอง</span>
                    <v-text-field
                      v-model="Detail.certificateName"
                      label="ระบุชื่อใบรับรอง"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <span style="font-size: 16px; color: #333333;">เลขที่ใบรับรอง</span>
                    <v-text-field
                      v-model="Detail.certificateNumber"
                      label="ระบุเลขที่ใบรับรอง"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      ></v-text-field>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16px; color: #333333;">เพิ่มรูปมาตราฐาน</span>
                    <v-row dense>
                      <v-col cols="12" md="12" class="mt-2">
                        <v-row dense>
                          <draggable v-model="DataToShowImageStandard"  v-if="DataToShowImageStandard.length !== 0" :move="onMove" @start="drag=true" @end="drag=false" class="row fill-height align-center sortable-list">
                            <v-col cols="3" md="2" sm="4" v-for="(item, index) in DataToShowImageStandard" :key="index" :class="!MobileSize ? 'mt-2' : 'px-1'">
                              <v-card outlined class="pa-1" width="140" height="196" v-if="statusPage !== 'Edit' && DataToShowImageStandard.length !== 0" style="justify-content: center; display: flex; text-align: center;">
                                <v-img :src="item.media_path" :lazy-src="item.media_path" max-width="100%" max-height="100%">
                                  <v-btn icon x-small style="float: right; background-color: #9A9A9A;">
                                    <v-icon x-small color="white" dark @click="RemoveImageShowStandard(index, item)" @TouchStart="RemoveImageShowStandard(index, item)">mdi-close</v-icon>
                                  </v-btn>
                                </v-img>
                              </v-card>
                              <v-card outlined class="pa-1" width="80" height="80" v-else-if="statusPage === 'Edit' && DataToShowImageStandard.length !== 0">
                                <v-img :src="item.media_path" :lazy-src="item.media_path" max-width="64" max-height="64" style="justify-content: center; display: flex; text-align: center;">
                                  <v-btn icon x-small style="float: right; background-color: #9A9A9A;">
                                    <v-icon x-small color="white" dark @click="RemoveImageShowStandard(index, item)" @TouchStart="RemoveImageShowStandard(index, item)">mdi-close</v-icon>
                                  </v-btn>
                                </v-img>
                              </v-card>
                            </v-col>
                            <v-col cols="3" md="2" sm="4" v-if="DataToShowImageStandard.length !== 1 && DataToShowImageStandard.length !== 0" :class="MobileSize ? '' : 'mt-2'" slot="footer" key="footer">
                              <v-card width="80" height="80" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="opendialogUploadImageStandard()" >
                                <v-card-text>
                                  <v-row no-gutters align="center" justify="center">
                                    <v-col cols="12" md="12">
                                      <v-row justify="center" align="center">
                                        <v-col cols="12" md="12" style="text-align: center;">
                                          <v-icon color="#27AB9C">mdi-plus-circle</v-icon><br/>
                                          <span style="font-weight: 500; line-height: 24px; color: #1B5DD6;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'">เพิ่มรูป</span>
                                        </v-col>
                                      </v-row>
                                    </v-col>
                                  </v-row>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </draggable>
                          <v-col cols="12" v-if="DataToShowImageStandard.length === 0">
                            <v-card elevation="0" outlined width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                              <v-card-text>
                                <v-row no-gutters align="center" justify="center">
                                  <v-col cols="12" align="center" class="mt-6">
                                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เพิ่มรูป</span>
                                  </v-col>
                                  <v-col cols="12" align="center" class="my-5">
                                    <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png')" max-width="167" max-height="100"></v-img>
                                  </v-col>
                                  <v-col cols="12" md="12">
                                    <v-row justify="center" align="center">
                                      <v-col cols="12" md="12" style="text-align: center;">
                                        <span style="line-height: 24px; font-weight: 400; color: #333333;" :style="IpadSize ? 'font-size: 14px;' : MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'">ไฟล์รูปควรมีขนาดไม่เกิน 2 MB และใส่ได้สูงสุด 1 รูป</span><br/>
                                        <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 12px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 595 x 842 px และรองรับไฟล์นามสกุล .JPG, .JPEG, .PNG)</span>
                                      </v-col>
                                    </v-row>
                                  </v-col>
                                  <v-col cols="12" align="center" class="mt-5 mb-6">
                                    <v-btn color="#1B5DD6" text @click="openUploadImageStandard()"><v-icon>mdi-cloud-upload-outline</v-icon><span style="font-size: 16px; font-weight: 500; text-decoration: underline;" class="pl-1">อัปโหลดรูป</span></v-btn>
                                  </v-col>
                                </v-row>
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16; color: #333333;">URL : Agrinno Smart Farm Platform</span>
                    <v-text-field
                      v-model="Detail.certificateURL"
                      label="ระบุ URL"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      ></v-text-field>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12">
            <v-card elevation="0">
              <v-card-title style="background-color: #F7FFFC; border-radius: 10px;">
                <v-col cols="6" class="pa-0">
                  <v-avatar rounded icon size="30" class="mr-2">
                    <v-img contain :src="require('@/assets/TraceabilityProfile/box1.png')"></v-img>
                  </v-avatar>
                  <span style="color: #27AB9C;"><b>ข้อมูลสินค้า</b></span>
                </v-col>
                <v-col cols="6" class="text-right pa-0">
                  <div>
                    <v-btn fab small color="#27AB9C" @click="show3 = !show3">
                      <v-icon color="white">{{ show3 ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
                    </v-btn>
                  </div>
                </v-col>
              </v-card-title>
              <v-card-text class="pt-5" v-show="show3">
                <v-row>
                  <v-col cols="12">
                    <span style="font-size: 16px; color: #333333;">หมวดหมู่สินค้า</span>
                    <v-text-field
                      v-model="Detail.category"
                      label="ระบุหมวดหมู่สินค้า"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      disabled
                      ></v-text-field>
                      <span style="font-size: 12px; color: #989898;">* หมวดหมู่สินค้าจะต้องตรงกับหมวดหมู่ Product Traceability Profile</span>
                  </v-col>
                  <v-col cols="12">
                    <span style="font-size: 16px; color: #333333;">เพิ่มสินค้า</span>
                    <v-select
                      v-model="Detail.selectedProducts"
                      :items="productsWithSelectAll"
                      item-text="name"
                      item-value="name"
                      label="เลือกสินค้า"
                      class="mt-2"
                      solo
                      dense
                      hide-details
                      multiple
                      return-object
                    >
                      <template v-slot:selection="{ item, index }">
                        <v-chip
                          v-if="index < 2"
                          small
                          class="ma-1"
                        >
                          {{ item.name }}
                        </v-chip>
                        <span v-if="index === 2" class="grey--text caption">+{{ Detail.selectedProducts.length - 2 }} รายการ</span>
                      </template>

                      <template v-slot:item="{ item }">
                        <v-list-item>
                          <v-checkbox
                            v-if="item.name === 'เลือกทั้งหมด'"
                            :input-value="isAllSelected"
                            @change="toggleSelectAll"
                            label="ทั้งหมด"
                            hide-details
                            class="checkbox-border"
                          />
                          <template v-else>
                            <v-checkbox
                              :input-value="isSelected(item)"
                              @change="toggleItem(item)"
                              class="mr-2 checkbox-border"
                              hide-details
                            />
                            <v-img
                              v-if="item.image && item.image !== '-'"
                              :src="item.image"
                              max-height="93"
                              max-width="93"
                              class="ma-2"
                              style="border-radius: 8px;"
                            />
                            <img
                              v-else
                              src="@/assets/NoImage.png"
                              style="max-width: 93px; max-height: 93px; border-radius: 8px;"
                              class="mr-2"
                            />
                            <v-list-item-content class="product-text">
                              <v-list-item-title class="mb-2">{{ item.name }}</v-list-item-title>
                              <v-list-item-subtitle>{{ item.description }}</v-list-item-subtitle>
                            </v-list-item-content>
                          </template>
                        </v-list-item>
                      </template>
                    </v-select>
                  </v-col>

                  <v-col cols="12">
                    <v-data-table
                      :headers="headers"
                      :items="Detail.selectedProducts"
                      class="elevation-1"
                      hide-default-footer
                      style="max-height: 400px; overflow-y: auto;"
                    >
                      <template v-slot:[`item.number`]="{ index }">
                        {{ index + 1 }}
                      </template>

                      <template v-slot:[`item.image`]="{ item }">
                        <div class="pt-2 pb-2">
                          <v-img v-if="item.image && item.image !== '-'" :src="item.image" max-height="40" max-width="40" />
                          <v-img v-else src="@/assets/NoImage.png" max-height="40" max-width="40" />
                        </div>
                      </template>

                      <template v-slot:[`item.name`]="{ item }">
                        <span>{{ item.name }}</span>
                      </template>

                      <template v-slot:[`item.manage`]="{ index }">
                        <v-btn small @click="removeProduct(index)">
                          <v-icon color="#27AB9C">mdi mdi-trash-can-outline</v-icon>
                        </v-btn>
                      </template>
                    </v-data-table>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
       </v-col>

       <v-col cols="12" style="display: flex; justify-content: space-between;">
        <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="openDialogCancel()">ยกเลิก</v-btn>
        <v-btn color="#27AB9C" class="white--text" rounded width="125" height="40" @click="openDialogSuccess()">บันทึก</v-btn>
       </v-col>

      </v-card>

      <v-dialog v-model="DialogUploadImageProfile" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัปโหลดรูป</b></span>
                </v-col>
                <v-btn fab small @click="cancelUploadImageProfile()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                <v-card-text style="text-align: center;" class="mt-6" v-if="Detail.profile_image.length === 0">
                  <span class="textUploadnameImage">(เพิ่มได้สูงสุด 5 รูปภาพ)</span>
                  <v-row justify="center" class="pt-6" style="cursor: pointer;">
                    <v-col cols="12" align="center">
                      <v-card @click="onPickFile()" @drop.prevent="DropImage($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                        <v-card-text>
                          <v-col cols="12" md="12" class="mb-6">
                            <v-row justify="center" class="pt-10">
                              <v-file-input
                                v-model="DataImage"
                                :items="DataImage"
                                accept="image/jpeg, image/jpg, image/png"
                                @change="UploadImage($event)"
                                id="file_input"
                                ref="fileInput"
                                multiple
                                :clearable="false"
                                style="display:none"
                              >
                              </v-file-input>
                              <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                width="143"
                                height="143"
                                contain
                              ></v-img>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12" class="mt-2">
                            <v-row justify="center" align="center">
                              <v-col cols="12" md="12" style="text-align: center;">
                                <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                                <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(รองรับไฟล์นามสกุล .JPG, .JPEG, .PNG (ขนาดรูปภาพ 555 x 555 px) และขนาดไฟล์: สูงสุด 2 MB)</span>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
                <v-card-text class="px-4" v-else>
                  <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                    <v-card-text style="text-align: center;" v-if="Detail.profile_image.length < 5">
                      <v-row justify="center" class="pt-6" style="cursor: pointer;">
                        <v-col cols="12" align="center">
                          <v-card @click="onPickFile()" @drop.prevent="DropImage($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" :width="MobileSize ? '100%' : '882'" :height="MobileSize ? '100%' : '83'" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                            <v-card-text>
                              <v-row>
                                <v-file-input
                                  v-model="DataImage"
                                  :items="DataImage"
                                  accept="image/jpeg, image/jpg, image/png"
                                  @change="UploadImage($event)"
                                  id="file_input"
                                  ref="fileInput"
                                  multiple
                                  :clearable="false"
                                  style="display:none"
                                >
                                </v-file-input>
                                <v-col cols="12" md="2" sm="2" align="center" class="mt-2">
                                  <v-img
                                    src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                    width="32"
                                    height="32"
                                    contain
                                  ></v-img>
                                </v-col>
                                <v-col cols="12" md="8" sm="8" :align="MobileSize ? 'center' : 'start'">
                                  <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(รองรับไฟล์นามสกุล .JPG, .JPEG, .PNG (ขนาดรูปภาพ 555 x 555 px) และขนาดไฟล์: สูงสุด 2 MB)</span>
                                </v-col>
                                <v-col cols="12" md="2" sm="2" :align="MobileSize ? 'center' : 'start'" :class="MobileSize ? '' : 'mt-3'">
                                  <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-card-text>
                    <v-card-text>
                      <v-row dense>
                        <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                        <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                        <v-spacer></v-spacer>
                        <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 5 รูปภาพ)</span>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" v-for="(item, index) in Detail.profile_image" :key="index">
                          <v-row dense class="pt-4" style="height: 60px;">
                            <v-col cols="1" align="center">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                            </v-col>
                            <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === false">
                              <span class="textUpload" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                              <span style="line-height: 14px; font-weight: 400;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'">{{ Math.round(item.size / 1000) }} KB</span>
                            </v-col>
                            <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === true">
                              <span class="textUpload" style="color: #F5222D;" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                              <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                            </v-col>
                            <v-col cols="7" md="4" sm="4" :class="MobileSize ? 'pt-4' : ''">
                              <v-row justify="end" dense>
                                <v-btn :class="MobileSize ? 'px-0' : ''" :height="MobileSize ? '20' : '40'" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImage('Banner', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">แสดงตัวอย่าง</span></v-btn>
                                <v-btn :class="MobileSize ? 'px-0' : ''" width="37" :height="MobileSize ? '20' : '40'"  text color="#636363"  @click="RemoveImage(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">ลบ</span></v-btn>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions style="height: 88px; background-color: #F5FCFB;">
            <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelUploadImageProfile()">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" rounded width="125" :disabled="disableUploadButton" height="40" class="white--text" @click="uploadToShow()">อัปโหลด</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogShowImage" width="100%" style="height: 90vh;" persistent>
        <v-card width="100%" height="90vh" elevation="0" style="background: rgba(0, 0, 0, 0.50);">
          <v-card-text>
            <v-row dense>
              <v-col cols="12">
                <v-row dense class="pt-4">
                  <v-icon color="#FFFFFF" class="mr-2" @click="bactToModalImage()">mdi-arrow-left</v-icon>
                  <v-img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" max-width="30" max-height="30" contain></v-img>
                  <span class="textBigImage pt-1">{{ imageToBig.name }}</span>
                </v-row>
              </v-col>
              <v-col cols="12" align="center" :style="{ 'max-height': setHeight }" style="max-width: 100%;">
                <v-img id="imgBig" :src="imageToBig.media_path" :width="setWidth" :height="setHeight" max-height="90vh"></v-img>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row justify="center" dense style="position:absolute; bottom: 20px; margin: auto; left: 47%;">
              <div style="width: 128px; height: 40px; border-radius: 25px; background: #333; text-align: center;">
                <v-btn :disabled="disableZoonOutButton" icon width="40" height="40" class="mr-2" @click="ZoomOut()"><v-icon color="#FFFFFF" size="24">mdi-minus</v-icon></v-btn>
                <v-icon color="#A1A1A1">mdi-magnify-minus-outline</v-icon>
                <v-btn :disabled="disableZoomInButton" icon width="40" height="40" class="ml-2" @click="ZoomIn()"><v-icon color="#FFFFFF" size="24">mdi-plus</v-icon></v-btn>
              </div>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogUploadImageStandard" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัปโหลดรูป</b></span>
                </v-col>
                <v-btn fab small @click="cancelUploadImageStandard()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                <v-card-text style="text-align: center;" class="mt-6" v-if="Detail.standard_image.length === 0">
                  <span class="textUploadnameImage">(เพิ่มได้สูงสุด 1 รูปภาพ)</span>
                  <v-row justify="center" class="pt-6" style="cursor: pointer;">
                    <v-col cols="12" align="center">
                      <v-card @click="onPickFileStandard()" @drop.prevent="DropImageStandard($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                        <v-card-text>
                          <v-col cols="12" md="12" class="mb-6">
                            <v-row justify="center" class="pt-10">
                              <v-file-input
                                v-model="DataImageStandard"
                                :items="DataImageStandard"
                                accept="image/jpeg, image/jpg, image/png"
                                @change="UploadImageStandard($event)"
                                id="file_input_standard"
                                ref="fileInputStandard"
                                multiple
                                :clearable="false"
                                style="display:none"
                              >
                              </v-file-input>
                              <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                width="143"
                                height="143"
                                contain
                              ></v-img>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12" class="mt-2">
                            <v-row justify="center" align="center">
                              <v-col cols="12" md="12" style="text-align: center;">
                                <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                                <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(รองรับไฟล์นามสกุล .JPG, .JPEG, .PNG (ขนาดรูปภาพ 595 x 842 px) และขนาดไฟล์: สูงสุด 2 MB)</span>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
                <v-card-text class="px-4" v-else>
                  <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                    <v-card-text style="text-align: center;" v-if="Detail.standard_image.length < 1">
                      <v-row justify="center" class="pt-6" style="cursor: pointer;">
                        <v-col cols="12" align="center">
                          <v-card @click="onPickFileStandard()" @drop.prevent="DropImageStandard($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" :width="MobileSize ? '100%' : '882'" :height="MobileSize ? '100%' : '83'" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                            <v-card-text>
                              <v-row>
                                <v-file-input
                                  v-model="DataImageStandard"
                                  :items="DataImageStandard"
                                  accept="image/jpeg, image/jpg, image/png"
                                  @change="UploadImageStandard($event)"
                                  id="file_input_standard"
                                  ref="fileInputStandard"
                                  multiple
                                  :clearable="false"
                                  style="display:none"
                                >
                                </v-file-input>
                                <v-col cols="12" md="2" sm="2" align="center" class="mt-2">
                                  <v-img
                                    src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                    width="32"
                                    height="32"
                                    contain
                                  ></v-img>
                                </v-col>
                                <v-col cols="12" md="8" sm="8" :align="MobileSize ? 'center' : 'start'">
                                  <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                  <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(รองรับไฟล์นามสกุล .JPG, .JPEG, .PNG (ขนาดรูปภาพ 595 x 842 px) และขนาดไฟล์: สูงสุด 2 MB)</span>
                                </v-col>
                                <v-col cols="12" md="2" sm="2" :align="MobileSize ? 'center' : 'start'" :class="MobileSize ? '' : 'mt-3'">
                                  <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-card-text>
                    <v-card-text>
                      <v-row dense>
                        <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                        <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                        <v-spacer></v-spacer>
                        <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 1 รูปภาพ)</span>
                      </v-row>
                      <v-row dense>
                        <v-col cols="12" v-for="(item, index) in Detail.standard_image" :key="index">
                          <v-row dense class="pt-4" style="height: 60px;">
                            <v-col cols="1" align="center">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                            </v-col>
                            <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === false">
                              <span class="textUpload" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                              <span style="line-height: 14px; font-weight: 400;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'">{{ Math.round(item.size / 1000) }} KB</span>
                            </v-col>
                            <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === true">
                              <span class="textUpload" style="color: #F5222D;" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                              <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                            </v-col>
                            <v-col cols="7" md="4" sm="4" :class="MobileSize ? 'pt-4' : ''">
                              <v-row justify="end" dense>
                                <v-btn :class="MobileSize ? 'px-0' : ''" :height="MobileSize ? '20' : '40'" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImageStandard('Banner', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">แสดงตัวอย่าง</span></v-btn>
                                <v-btn :class="MobileSize ? 'px-0' : ''" width="37" :height="MobileSize ? '20' : '40'"  text color="#636363"  @click="RemoveImageStandard(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">ลบ</span></v-btn>
                              </v-row>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions style="height: 88px; background-color: #F5FCFB;">
            <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelUploadImageStandard()">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" rounded width="125" :disabled="disableUploadButtonStandard" height="40" class="white--text" @click="uploadToShowStandard()">อัปโหลด</v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogShowImageStandard" width="100%" style="height: 90vh;" persistent>
        <v-card width="100%" height="90vh" elevation="0" style="background: rgba(0, 0, 0, 0.50);">
          <v-card-text>
            <v-row dense>
              <v-col cols="12">
                <v-row dense class="pt-4">
                  <v-icon color="#FFFFFF" class="mr-2" @click="bactToModalImageStandard()">mdi-arrow-left</v-icon>
                  <v-img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" max-width="30" max-height="30" contain></v-img>
                  <span class="textBigImage pt-1">{{ imageToBig.name }}</span>
                </v-row>
              </v-col>
              <v-col cols="12" align="center" :style="{ 'max-height': setHeight }" style="max-width: 100%;">
                <v-img id="imgBig" :src="imageToBig.media_path" :width="setWidth" :height="setHeight" max-height="90vh"></v-img>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row justify="center" dense style="position:absolute; bottom: 20px; margin: auto; left: 47%;">
              <div style="width: 128px; height: 40px; border-radius: 25px; background: #333; text-align: center;">
                <v-btn :disabled="disableZoonOutButton" icon width="40" height="40" class="mr-2" @click="ZoomOut()"><v-icon color="#FFFFFF" size="24">mdi-minus</v-icon></v-btn>
                <v-icon color="#A1A1A1">mdi-magnify-minus-outline</v-icon>
                <v-btn :disabled="disableZoomInButton" icon width="40" height="40" class="ml-2" @click="ZoomIn()"><v-icon color="#FFFFFF" size="24">mdi-plus</v-icon></v-btn>
              </div>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogSuccess" persistent max-width="420px">
        <v-card style="border-radius: 35px;">
          <v-btn icon class="ml-auto"  @click="dialogSuccess = false" style="position: absolute; top: 10px; right: 10px;">
            <v-icon color="grey">mdi-close</v-icon>
          </v-btn>
          <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #F8FFF5">
            <v-img
              src="@/assets/TraceabilityProfile/CF.png"
              contain
              max-width="150"
            ></v-img>
          </v-card-title>
          <br>
          <v-card-text style="padding-bottom: 0px;">
            <div class="text-center mb-2">
              <span style="font-size: 24px;"><b>บันทึกเสร็จสิ้น</b></span><br><br>
              <span style="font-size: 16px;">คุณได้ทำการเพิ่ม Traceability Profile เรียบร้อย</span>
            </div>
          </v-card-text>
          <v-card-actions class="justify-center">
            <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="sentToBN()">ตกลง</v-btn>
          </v-card-actions>
          <br>
        </v-card>
      </v-dialog>

      <v-dialog v-model="dialogCancel" persistent max-width="420px">
        <v-card style="border-radius: 35px;">
          <v-btn icon class="ml-auto" @click="dialogCancel = false" style="position: absolute; top: 10px; right: 10px;">
            <v-icon color="grey">mdi-close</v-icon>
          </v-btn>
          <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
            <v-img
              src="@/assets/TraceabilityProfile/CC.png"
              contain
              max-width="150"
            ></v-img>
          </v-card-title>
          <br>
          <v-card-text style="padding-bottom: 0px;">
            <div class="text-center mb-2">
              <span style="font-size: 24px;"><b>ยกเลิกข้อมูล</b></span><br><br>
              <span style="font-size: 16px;">หากคุณยกเลิกฟอร์มนี้ ข้อมูลที่คุณได้สร้างไว้จะหายไป<br>เนื่องจากยังไม่ถูกบันทึก คุณต้องการจะยกเลิกใช่หรือไม่</span>
            </div>
          </v-card-text>
          <v-card-actions class="justify-center">
            <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px; font-size: 16px;" @click="dialogCancel = false">ยกเลิก</v-btn>
            <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white; font-size: 16px;" @click="clearShippingData()">ตกลง</v-btn>
          </v-card-actions>
          <br>
        </v-card>
      </v-dialog>

    </v-container>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      shopID: '',
      show1: false,
      show2: false,
      show3: false,
      categories: [
        { id: 1, name: 'หมวดหมู่ 1' },
        { id: 2, name: 'หมวดหมู่ 2' },
        { id: 3, name: 'หมวดหมู่ 3' }
      ],
      products: [
        {
          id: 1,
          name: 'เวย์โปรตีน รสช็อกโกแลต',
          description: 'โปรตีนสูง ช่วยเสริมสร้างกล้ามเนื้อ เหมาะสำหรับสายฟิตเนส',
          image: 'data:image/jpeg;base64,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'
        },
        {
          id: 2,
          name: 'มัทฉะเขียวเข้มข้น',
          description: 'ผงชาเขียวมัทฉะแท้จากญี่ปุ่น เหมาะสำหรับชงดื่มหรือทำขนม',
          image: 'data:image/jpeg;base64,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'
        },
        {
          id: 3,
          name: 'กราโนล่า ข้าวโอ๊ตอบกรอบ',
          description: 'กราโนล่าสูตรหวานน้อย เหมาะกับอาหารเช้า หรือกินเล่น',
          image: 'data:image/jpeg;base64,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'
        },
        {
          id: 4,
          name: 'คอลลาเจน ไตรเปปไทด์',
          description: 'คอลลาเจนสกัดเข้มข้นจากปลา บำรุงผิว ผม และเล็บ',
          image: 'data:image/jpeg;base64,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'
        },
        {
          id: 5,
          name: 'วิตามินซี ชนิดเม็ดฟู่',
          description: 'ช่วยเสริมภูมิคุ้มกัน ละลายน้ำง่าย รสส้มอร่อย',
          image: 'data:image/jpeg;base64,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'
        }
      ],
      headers: [
        { text: 'ลำดับ', value: 'number', width: '30', align: 'start', sortable: false, class: 'backgroundTable fontTable--text text-header' },
        { text: 'รูปสินค้า', value: 'image', width: '30', align: 'start', sortable: false, class: 'backgroundTable fontTable--text text-header' },
        { text: 'ชื่อสินค้า', value: 'name', width: '280', align: 'start', sortable: false, class: 'backgroundTable fontTable--text text-header' },
        { text: 'จัดการ', value: 'manage', width: '10', align: 'start', sortable: false, class: 'backgroundTable fontTable--text text-header' }
      ],
      DataToShowImageProfile: [],
      statusPage: 'Create',
      drag: false,
      DialogUploadImageProfile: false,
      DataImage: [],
      disableUploadButton: true,
      Detail: {
        category: null,
        profileName: '',
        story: '',
        certificateName: '',
        certificateNumber: '',
        certificateURL: '',
        profile_image: [],
        standard_image: [],
        product: null,
        selectedProducts: []
      },
      disableZoonOutButton: true,
      disableZoomInButton: false,
      setHeight: 0,
      setWidth: 0,
      imageToBig: {
        media_path: '',
        name: ''
      },
      fromClick: '',
      dialogShowImage: false,
      dragover: false,
      DataToShowImageStandard: [],
      dialogUploadImageStandard: false,
      dialogShowImageStandard: false,
      DataImageStandard: [],
      disableUploadButtonStandard: true,
      dialogCancel: false,
      dialogSuccess: false
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    productsWithSelectAll () {
      return [{ name: 'เลือกทั้งหมด' }, ...this.products]
    },
    isAllSelected () {
      return this.products.length && this.Detail.selectedProducts.length === this.products.length
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/CreateProfileTraceabilityMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/CreateProfileTraceability' }).catch(() => { })
      }
    }
  },
  async created () {
    // this.$EventBus.$emit('changeNav')
    window.scrollTo(0, 0)
    var sellerShopID = localStorage.getItem('shopSellerID')
    this.shopID = sellerShopID
  },
  mounted () {

  },
  methods: {
    backToProfile () {
      if (this.MobileSize) {
        this.$router.push({ path: '/ProfileTraceabilityMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/ProfileTraceability' }).catch(() => { })
      }
    },
    openUploadImage () {
      this.DialogUploadImageProfile = true
    },
    openUploadImageStandard () {
      this.dialogUploadImageStandard = true
    },
    openDialogUploadImage () {
      this.Detail.profile_image = []
      this.Detail.profile_image = [...this.DataToShowImageProfile]
      this.DialogUploadImageProfile = true
    },
    opendialogUploadImageStandard () {
      this.Detail.standard_image = []
      this.Detail.standard_image = [...this.DataToShowImageStandard]
      this.dialogUploadImageStandard = true
    },
    uploadToShow () {
      this.DataToShowImageProfile = []
      this.DataToShowImageProfile = [...this.Detail.profile_image]
      this.DialogUploadImageProfile = false
    },
    uploadToShowStandard () {
      this.DataToShowImageStandard = []
      this.DataToShowImageStandard = [...this.Detail.standard_image]
      this.dialogUploadImageStandard = false
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    onPickFileStandard () {
      document.getElementById('file_input_standard').click()
    },
    DropImage (e) {
      if (this.Detail.profile_image.length < 5) {
        for (let i = 0; i < e.dataTransfer.files.length; i++) {
          const element = e.dataTransfer.files[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
            const imageSize = element.size / 1024 / 1024
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = async () => {
              const img = new Image()
              img.src = reader.result
              const imageDimensions = await new Promise((resolve) => {
                img.onload = () => {
                  const dimensions = {
                    height: img.height,
                    width: img.width
                  }
                  resolve(dimensions)
                }
              })
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (imageDimensions.height <= 555 && imageDimensions.width <= 555) {
                if (this.Detail.profile_image.length < 5) {
                  if (imageSize < 2) {
                    this.Detail.profile_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.profile_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.profile_image.every((key) => key.statusFail === false)) {
                    this.disableUploadButton = false
                  } else {
                    this.disableUploadButton = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 5 รูป',
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพขนาด 555 x 555', showConfirmButton: false, timer: 2500 })
              }
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปได้ไม่เกิน 5 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    DropImageStandard (e) {
      if (this.Detail.standard_image.length < 1) {
        for (let i = 0; i < e.dataTransfer.files.length; i++) {
          const element = e.dataTransfer.files[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
            const imageSize = element.size / 1024 / 1024
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = async () => {
              const img = new Image()
              img.src = reader.result
              const imageDimensions = await new Promise((resolve) => {
                img.onload = () => {
                  const dimensions = {
                    height: img.height,
                    width: img.width
                  }
                  resolve(dimensions)
                }
              })
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (imageDimensions.height <= 842 && imageDimensions.width <= 595) {
                if (this.Detail.standard_image.length < 1) {
                  if (imageSize < 2) {
                    this.Detail.standard_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.standard_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.standard_image.every((key) => key.statusFail === false)) {
                    this.disableUploadButtonStandard = false
                  } else {
                    this.disableUploadButtonStandard = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 1 รูป',
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพขนาด 595 x 842 px', showConfirmButton: false, timer: 2500 })
              }
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปได้ไม่เกิน 1 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    UploadImage () {
      if (this.Detail.profile_image.length < 5) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
            const imageSize = element.size / 1024 / 1024
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = async () => {
              const img = new Image()
              img.src = reader.result
              const imageDimensions = await new Promise((resolve) => {
                img.onload = () => {
                  const dimensions = {
                    height: img.height,
                    width: img.width
                  }
                  resolve(dimensions)
                }
              })
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (imageDimensions.height <= 555 && imageDimensions.width <= 555) {
                if (this.Detail.profile_image.length < 5) {
                  if (imageSize < 2) {
                    this.Detail.profile_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImage[i].name,
                      size: this.DataImage[i].size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.profile_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImage[i].name,
                      size: this.DataImage[i].size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.profile_image.every((key) => key.statusFail === false)) {
                    this.disableUploadButton = false
                  } else {
                    this.disableUploadButton = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 5 รูป',
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพขนาด 555 x 555', showConfirmButton: false, timer: 2500 })
              }
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปได้ไม่เกิน 5 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    UploadImageStandard () {
      if (this.Detail.standard_image.length < 1) {
        for (let i = 0; i < this.DataImageStandard.length; i++) {
          const element = this.DataImageStandard[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
            const imageSize = element.size / 1024 / 1024
            const reader = new FileReader()
            reader.readAsDataURL(element)
            reader.onload = async () => {
              const img = new Image()
              img.src = reader.result
              const imageDimensions = await new Promise((resolve) => {
                img.onload = () => {
                  const dimensions = {
                    height: img.height,
                    width: img.width
                  }
                  resolve(dimensions)
                }
              })
              var resultReader = reader.result
              var url = URL.createObjectURL(element)
              if (imageDimensions.height <= 842 && imageDimensions.width <= 595) {
                if (this.Detail.standard_image.length < 1) {
                  if (imageSize < 2) {
                    this.Detail.standard_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImageStandard[i].name,
                      size: this.DataImageStandard[i].size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.standard_image.push({
                      image_data: resultReader.split(',')[1],
                      media_path: url,
                      name: this.DataImageStandard[i].name,
                      size: this.DataImageStandard[i].size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.standard_image.every((key) => key.statusFail === false)) {
                    this.disableUploadButtonStandard = false
                  } else {
                    this.disableUploadButtonStandard = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 1 รูป',
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มรูปภาพขนาด 595 x 842 px', showConfirmButton: false, timer: 2500 })
              }
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปได้ไม่เกิน 1 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.profile_image_delete.push(val.id)
        }
        this.Detail.profile_image.splice(index, 1)
        // this.DataToShowImageProfile.splice(index, 1)
        this.DataImage = []
        if (this.Detail.profile_image.every((key) => key.statusFail === false)) {
          this.disableUploadButton = false
        } else {
          this.disableUploadButton = true
        }
      } else {
        this.DataImage = []
        this.Detail.profile_image.splice(index, 1)
        if (this.Detail.profile_image.every((key) => key.statusFail === false)) {
          this.disableUploadButton = false
        } else {
          this.disableUploadButton = true
        }
        // this.DataToShowImageProfile.splice(index, 1)
      }
    },
    RemoveImageStandard (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.standard_image_delete.push(val.id)
        }
        this.Detail.standard_image.splice(index, 1)
        // this.DataToShowImageProfile.splice(index, 1)
        this.DataImageStandard = []
        if (this.Detail.standard_image.every((key) => key.statusFail === false)) {
          this.disableUploadButtonStandard = false
        } else {
          this.disableUploadButtonStandard = true
        }
      } else {
        this.DataImageStandard = []
        this.Detail.standard_image.splice(index, 1)
        if (this.Detail.standard_image.every((key) => key.statusFail === false)) {
          this.disableUploadButtonStandard = false
        } else {
          this.disableUploadButtonStandard = true
        }
        // this.DataToShowImageProfile.splice(index, 1)
      }
    },
    RemoveImageShow (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.profile_image_delete.push(val.id)
        }
        this.DataToShowImageProfile.splice(index, 1)
        this.DataImage = []
      } else {
        this.DataImage = []
        this.DataToShowImageProfile.splice(index, 1)
      }
    },
    RemoveImageShowStandard (index, val) {
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.standard_image_delete.push(val.id)
        }
        this.DataToShowImageStandard.splice(index, 1)
        this.DataImageStandard = []
      } else {
        this.DataImageStandard = []
        this.DataToShowImageStandard.splice(index, 1)
      }
    },
    cancelUploadImageProfile () {
      this.Detail.profile_image = []
      this.Detail.profile_image = [...this.DataToShowImageProfile]
      this.DialogUploadImageProfile = false
    },
    cancelUploadImageStandard () {
      this.Detail.standard_image = []
      this.Detail.standard_image = [...this.DataToShowImageStandard]
      this.dialogUploadImageStandard = false
    },
    ZoomIn () {
      var myImg = document.getElementById('imgBig')
      var currWidth = myImg.clientWidth
      if (this.IpadSize) {
        if (currWidth > 750) {
          this.disableZoomInButton = true
          this.disableZoonOutButton = false
          // return false
        } else {
          myImg.style.width = (currWidth + 100) + 'px'
          this.disableZoomInButton = false
          this.disableZoonOutButton = false
        }
      } else {
        if (currWidth > 1000) {
          this.disableZoomInButton = true
          this.disableZoonOutButton = false
          return false
        } else {
          myImg.style.width = (currWidth + 100) + 'px'
          this.disableZoomInButton = false
          this.disableZoonOutButton = false
        }
      }
    },
    ZoomOut () {
      var myImg = document.getElementById('imgBig')
      var currWidth = myImg.clientWidth
      if (this.IpadSize) {
        if (currWidth < 500) {
          this.disableZoonOutButton = true
          this.disableZoomInButton = false
          return false
        } else {
          myImg.style.width = (currWidth - 100) + 'px'
          myImg.style.height = (currWidth - 100)
          this.disableZoonOutButton = false
          this.disableZoomInButton = false
        }
      } else {
        if (currWidth <= this.setWidth) {
          this.disableZoonOutButton = true
          this.disableZoomInButton = false
          return false
        } else {
          myImg.style.width = (currWidth - 100) + 'px'
          this.disableZoonOutButton = false
          this.disableZoomInButton = false
        }
      }
    },
    bactToModalImage () {
      this.DialogUploadImageProfile = true
      this.dialogShowImage = false
    },
    bactToModalImageStandard () {
      this.dialogUploadImageStandard = true
      this.dialogShowImageStandard = false
    },
    async ShowBigImage (from, image) {
      const img = new Image()
      img.src = image.media_path
      const imageDimensions = await new Promise((resolve) => {
        img.onload = () => {
          const dimensions = {
            height: img.height,
            width: img.width
          }
          resolve(dimensions)
        }
      })
      this.setHeight = imageDimensions.height
      this.setWidth = imageDimensions.width
      this.imageToBig = image
      this.fromClick = ''
      this.fromClick = from
      this.DialogUploadImageProfile = false
      this.dialogShowImage = true
    },
    async ShowBigImageStandard (from, image) {
      const img = new Image()
      img.src = image.media_path
      const imageDimensions = await new Promise((resolve) => {
        img.onload = () => {
          const dimensions = {
            height: img.height,
            width: img.width
          }
          resolve(dimensions)
        }
      })
      this.setHeight = imageDimensions.height
      this.setWidth = imageDimensions.width
      this.imageToBig = image
      this.fromClick = ''
      this.fromClick = from
      this.dialogUploadImageStandard = false
      this.dialogShowImageStandard = true
    },
    closeMenu () {
      this.$nextTick(() => {
        if (this.$refs.select) {
          this.$refs.select.blur()
        }
      })
    },
    isSelected (item) {
      return this.Detail.selectedProducts.includes(item)
    },
    toggleItem (item) {
      const index = this.Detail.selectedProducts.findIndex(p => p.name === item.name)
      if (index > -1) {
        this.Detail.selectedProducts.splice(index, 1)
      } else {
        this.Detail.selectedProducts.push(item)
      }
    },
    toggleSelectAll (value) {
      if (value) {
        this.Detail.selectedProducts = [...this.products]
      } else {
        this.Detail.selectedProducts = []
      }
    },
    removeProduct (index) {
      this.Detail.selectedProducts.splice(index, 1)
    },
    openDialogSuccess () {
      this.dialogSuccess = true
    },
    openDialogCancel () {
      this.dialogCancel = true
    },
    clearShippingData () {
      this.backToProfile()
    },
    sentToBN () {
      console.log('sentToBN', this.Detail)
      this.dialogSuccess = false
    }
  }
}
</script>

<style scoped>

::v-deep .text-header {
  color: #27AB9C !important;
}

.checkbox-border ::v-deep .v-input--selection-controls__input .theme--light.v-icon {
  color: #27AB9C !important;
}

.product-text {
  max-width: 390px;
  white-space: normal !important;
  word-break: break-word !important;
}

.product-text .v-list-item__title,
.product-text .v-list-item__subtitle {
  white-space: normal !important;
  line-height: 1.3 !important;
}

</style>
