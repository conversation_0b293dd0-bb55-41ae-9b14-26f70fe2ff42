<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <v-dialog v-model="ModalDetailApproveOrder" persistent width="747">
      <v-card style="background: #FFFFFF; border-radius: 4px;">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :style="MobileSize ? 'font-size: 16px' : 'font-size: 18px'" :class="MobileSize ? 'title-mobile' : ''"><b>รายละเอียดการอนุมัติรายการสั่งซื้อ</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="ModalDetailApproveOrder = !ModalDetailApproveOrder" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row dense justify="start">
              <v-col cols="12">
                <p style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">รายละเอียดการอนุมัติรายการสั่งซื้อ</p>
              </v-col>
            </v-row>
            <!-- {{ DetailApproveOrder }} -->
            <v-row dense>
              <v-col cols="12" md="12">
                <p>รหัสการสั่งซื้อ: {{ DetailApproveOrder.order_number }}</p>
                <p>รหัสเอกสาร: {{ DetailApproveOrder.document_id }}</p>
                <p>รูปแบบการอนุมัติ: {{ checkType(DetailApproveOrder.type_approve) }}</p>
                <p>สถานะเอกสาร: <v-chip small :color="colorChipStatus(DetailApproveOrder.document_status)" :text-color="textChipStatus(DetailApproveOrder.document_status)">{{ textStatusOrder(DetailApproveOrder.document_status) }}</v-chip></p>
                <p>ผู้ซื้อ: {{ DetailApproveOrderBy.name }}</p>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12" md="12">
                <p style="font-weight: 600; font-size: 16px; line-height: 22px; color: #333333;">ลำดับการอนุมัติ</p>
              </v-col>
              <v-col cols="12" md="12">
                <v-timeline dense>
                  <v-timeline-item
                    v-for="(item, index) in DetailApproveOrder.approver_list"
                    :key="index"
                    large
                  >
                    <template v-slot:icon>
                      <!-- <v-avatar :size="40">
                        <v-img :src="item.approver[0].img_path"></v-img>
                      </v-avatar> -->
                      <span style="color: white;">{{ index + 1 }}</span>
                    </template>
                    <v-card class="elevation-0" v-for="(items, index1) in item.approver" :key="index1">
                      <v-card-text>
                        <span>ชื่อ - นามสกุล: {{ items.name }}</span><br/>
                        <span>อีเมล: {{ items.email }}</span><br/>
                        <span>เบอร์โทรศัพท์: {{ items.phone }}</span><br/>
                        <span>สถานะ: <v-chip small :color="colorChipStatus(item.status)" :text-color="textChipStatus(item.status)">{{ textStatusOrder(item.status) }}</v-chip></span>
                      </v-card-text>
                    </v-card>
                  </v-timeline-item>
                </v-timeline>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;">รายการอนุมัติการสั่งซื้อ</v-card-title>
      <v-card-title  v-else class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> รายการอนุมัติการสั่งซื้อ</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" class="px-2 py-0">
            <a-tabs @change="SelectDetailOrder">
              <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
              <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab">อนุมัติแล้ว <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderApprove }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="2"><span slot="tab">รอดำเนินการ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countOrderWaiting }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="3"><span slot="tab">ปฏิเสธอนุมัติ <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderReject }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
            </a-tabs>
          </v-col>
          <v-col v-if="disableTable === false"  cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-2 pt-0' : 'pl-2 pr-2 mb-3'">
            <v-text-field v-model="search" dense hide-details rounded outlined placeholder="ค้นหาจากรหัสการสั่งซื้อ">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col v-if="disableTable === false"  cols="12" class="">
            <v-row dense>
              <v-col cols="12" md="6" :class="!MobileSize ? 'pl-3 pr-2 mb-2 mt-2' : 'pl-2 pr-2 mb-3 mt-3'">
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 0">รายการทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 1">รายการอนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 2">รายการรอดำเนินการทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 3">รายการปฏิเสธอนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 4">รายการยกเลิกทั้งหมด {{ showCountOrder }} รายการ</span>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" class="px-0">
            <v-card v-if="disableTable === false" outlined class="small-card mr-2 my-5" min-height="436">
              <v-data-table
               :headers="headerApproveOrder"
               :items="DataTable"
               :search="search"
               :page.sync="page"
               style="width:100%;"
               height="100%"
               no-results-text="ไม่พบรายการอนุมัติที่ค้นหา"
               no-data-text="ไม่มีรายการอนุมัติในตาราง"
               @pagination="countOrdarApprove"
               :items-per-page="10"
               class=""
               :footer-props="{'items-per-page-text':'จำนวนแถว'}"
               :update:items-per-page="itemsPerPage"
              >
                <template v-slot:[`item.created_at`]="{ item }">
                  {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })}}
                </template>
                <template v-slot:[`item.order_number`]="{ item }">
                  <a @click="quDetail(item)">{{item.order_number}}</a>
                </template>
                <template v-slot:[`item.pdf_qu`]="{ item }">
                  <v-btn x-small
                    @click="quDetail(item)"
                    style="border: 1px solid #F2F2F2; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                    :style="IpadProSize ? 'max-width: 24px; max-height: 24px;' : IpadSize ? 'max-width: 16px; max-height: 16px;' : 'max-width: 32px; max-height: 32px;'"
                    class="pt-4 pb-4">
                    <v-icon color="#27AB9C">mdi-file-document</v-icon>
                  </v-btn>
                </template>
                <template v-slot:[`item.status`]="{ item }">
                  <span>
                    <v-chip small :color="colorChipStatus(item.document_status)" :text-color="textChipStatus(item.document_status)">{{ textStatusOrder(item.document_status) }}</v-chip>
                  </span>
                </template>
                <template v-slot:[`item.detail`]="{ item }">
                  <v-btn text rounded color="#27AB9C" small @click="goToDetailApproveOrder(item)" class="ml-0">
                    <b>รายละเอียด</b>
                    <!-- <v-icon small>mdi-chevron-right</v-icon> -->
                  </v-btn>
                </template>
                <template v-slot:[`item.consent`]="{ item }">
                  <v-btn text rounded color="#27AB9C" small @click="goToIframePaperless(item)" class="ml-0">
                    <b v-if="item.status === 'Pending'">ยืนยันการอนุมัติ</b>
                    <b v-else>ข้อมูลการอนุมัติ</b>
                    <!-- <v-icon small>mdi-chevron-right</v-icon> -->
                  </v-btn>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
          <v-col cols="12" v-if="disableTable === true" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0"><b>คุณยังไม่มีรายการอนุมัติรายการสั่งซื้อ</b></h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1"><b>คุณยังไม่มีรายการอนุมัติรายการสั่งซื้อ</b></h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2"><b>คุณยังไม่มีรายการอนุมัติรายการสั่งซื้อที่รอดำเนินการ</b></h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 3"><b>คุณยังไม่มีรายการอนุมัติรายการสั่งซื้อที่ปฏิเสธ</b></h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 4"><b>คุณยังไม่มีรายการอนุมัติรายการสั่งซื้อที่ยกเลิก</b></h2>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      countOrderAll: 0,
      countOrderPending: 0,
      countOrderApprove: 0,
      countOrderProcess: 0,
      countOrderReject: 0,
      countOrderWaiting: 0,
      countOrderCancel: 0,
      disableTable: false,
      search: '',
      page: 1,
      ModalDetailApproveOrder: false,
      headerApproveOrder: [
        { text: 'วันที่', value: 'created_at', filterable: false, width: '200', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ใบเสนอราคา', value: 'pdf_qu', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รายละเอียด', value: 'detail', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: '', value: 'consent', filterable: false, align: 'center', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      showCountOrder: 0,
      itemsPerPage: 10,
      StateStatus: 0,
      DataTable: [],
      orderList: [],
      DetailApproveOrder: [],
      DetailApproveOrderBy: [],
      oneData: []
    }
  },
  mounted () {
    this.$EventBus.$on('getListPurchaserApproveOrder', this.getListPurchaserApproveOrder)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getListPurchaserApproveOrder')
    })
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      //   this.seller_shop_id = JSON.parse(localStorage.getItem('shopDetail'))
      this.getListPurchaserApproveOrder()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listApproveMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listApprove' }).catch(() => {})
      }
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 0) {
        this.DataTable = this.orderList.All
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 1) {
        this.DataTable = this.orderList.Approve
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 3) {
        this.DataTable = this.orderList.Reject
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 2) {
        this.DataTable = this.orderList.Waiting
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      } else if (val === 4) {
        this.DataTable = this.orderList.Cancel
        if (this.DataTable.length === 0) {
          this.disableTable = true
        } else {
          this.disableTable = false
        }
      }
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return 'ผู้ใช้ไม่ได้รับอนุญาตให้ใช้ระบบ'
      } else if (msg === 'The user was not found in the company.') {
        return 'คุณไม่ได้อยู่ในบริษัทใดเลย'
      } else if (msg === 'Company not found.') {
        return 'ไม่พบข้อมูลบริษัทในระบบ'
      } else if (msg === 'You are not in this Company.') {
        return 'คุณไม่ได้อยู่ในบริษัทนี้'
      } else if (msg === 'Data missing. Please check your [" company_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสบริษัท ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Company Permission not found.') {
        return 'ไม่พบสิทธิ์ผู้ใช้องค์กรนี้'
      } else if (msg === 'This is not you Company Permission.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ใช่ของคุณ'
      } else if (msg === 'This Permission not in your Company.') {
        return 'สิทธิ์ผู้ใช้องค์กรนี้ไม่ได้อยู่ในองค์กรของคุณ'
      } else if (msg === 'Data missing. Please check your [" com_perm_id "] and try again.') {
        return 'ข้อมูลขาดหาย โปรดเช็ค [ รหัสสิทธิ์ผู้ใช้องค์กร ] แล้วลองใหม่อีกครั้ง'
      } else if (msg === 'Purchaser not found.') {
        return 'ไม่พบข้อมูลผู้ซื้อองค์กร'
      } else if (msg === '') {
        return ''
      } else {
        return 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่'
      }
    },
    checkType (val) {
      let type = ''
      if (val === 'all') {
        type = 'อนุมัติทั้งหมด'
      } else if (val === 'one') {
        type = 'อนุมัติ 1 คน'
      } else if (val === 'many') {
        type = 'ระบุวงเงิน'
      } else {
        type = ''
      }
      return type
    },
    countOrdarApprove (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    SelectDetailOrder (item) {
      this.StateStatus = item
      this.page = 1
    },
    quDetail (item) {
      window.open(`${item.pdf_path_qu}`)
    },
    async goToDetailApproveOrder (item) {
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        company_id: dataCompany,
        com_perm_id: companyDataSet.position.com_perm_id,
        order_number: item.order_number
      }
      // console.log(data)
      await this.$store.dispatch('actionsDetailOrderApprove', data)
      var response = await this.$store.state.ModuleAdminPanit.stateDetailOrderApprove
      // console.log(response.data)
      if (response.result === 'SUCCESS') {
        this.DetailApproveOrder = response.data[0]
        this.DetailApproveOrderBy = this.DetailApproveOrder.created_by
        this.ModalDetailApproveOrder = !this.ModalDetailApproveOrder
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          text: 'ไม่มีรายการสั่งซื้อนี้'
        })
      }
    },
    async getListPurchaserApproveOrder () {
      this.$store.commit('openLoader')
      var msg = ''
      this.countOrderAll = 0
      this.countOrderPending = 0
      this.countOrderApprove = 0
      this.countOrderProcess = 0
      this.countOrderReject = 0
      this.countOrderWaiting = 0
      this.countOrderCancel = 0
      this.DataTable = []
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      var data = {
        company_id: dataCompany,
        com_perm_id: companyDataSet.position.com_perm_id
      }
      // console.log(data)
      await this.$store.dispatch('actionsListPurchaserApproveOrder', data)
      var response = await this.$store.state.ModuleAdminPanit.stateListPurchaserApproveOrder
      // console.log(response)
      if (response.result === 'SUCCESS' && response.message === 'Get List Purchaser Approve Order Success.') {
        this.$store.commit('closeLoader')
        this.disableTable = false
        if (response.data.length !== 0) {
          this.orderList = response.data
          this.countOrderAll = this.orderList.All.length
          this.countOrderApprove = this.orderList.Approve.length
          this.countOrderReject = this.orderList.Reject.length
          this.countOrderWaiting = this.orderList.Waiting.length
          this.countOrderCancel = this.orderList.Cancel.length
          if (this.StateStatus === 0) {
            this.DataTable = this.orderList.All
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 1) {
            this.DataTable = this.orderList.Approve
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 3) {
            this.DataTable = this.orderList.Reject
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 2) {
            this.DataTable = this.orderList.Waiting
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          } else if (this.StateStatus === 4) {
            this.DataTable = this.orderList.Cancel
            if (this.DataTable.length === 0) {
              this.disableTable = true
            } else {
              this.disableTable = false
            }
          }
        } else {
          this.DataTable = []
          this.orderList = []
          this.disableTable = true
        }
      } else if (response.result === 'FAILED') {
        if (response.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          // msg = this.getErrorMsg(response.message)
          // this.$swal.fire({
          // toast: true,
          // showConfirmButton: false,
          // timer: 1500,
          // timerProgressBar: true,
          // icon: 'error',
          // text: msg
          // })
          // localStorage.removeItem('oneData')
          this.$EventBus.$emit('refreshToken')
        } else {
          msg = this.getErrorMsg(response.message)
          this.$swal.fire({
            icon: 'error',
            text: msg,
            showConfirmButton: false,
            timer: 1500
          })
          this.$store.commit('closeLoader')
          this.disableTable = true
          this.DataTable = []
          this.orderList = []
        }
      } else {
        this.$store.commit('closeLoader')
        this.disableTable = true
        this.DataTable = []
        this.orderList = []
      }
    },
    async goToIframePaperless (item) {
      var dataCompany = ''
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        dataCompany = companyDataSet.company.company_id
      } else {
        dataCompany = ''
      }
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        company_id: dataCompany,
        com_perm_id: companyDataSet.position.com_perm_id,
        order_number: item.order_number,
        access_token: this.oneData.user.access_token
      }
      // console.log(data)
      await this.$store.dispatch('actionsIframeOrderApprove', data)
      var response = await this.$store.state.ModuleAdminPanit.stateIframeOrderApprove
      // console.log(response.data)
      if (response.result === 'SUCCESS') {
        await localStorage.setItem('dataPaperlessList', Encode.encode(response.data))
        if (!this.MobileSize) {
          this.$router.push({ path: '/DetailListApprove' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DetailListApproveMobile' }).catch(() => {})
        }
      }
    },
    textStatusOrder (val) {
      if (val === 'Pending') {
        return 'รออนุมัติ'
      } else if (val === 'Success') {
        return 'อนุมัติแล้ว'
      } else if (val === 'Approve') {
        return 'อนุมัติแล้ว'
      } else if (val === 'Process') {
        return 'กำลังดำเนินการ'
      } else if (val === 'Reject') {
        return 'ปฏิเสธอนุมัติ'
      } else if (val === 'Waiting') {
        return 'รอดำเนินการ'
      } else {
        return 'ยกเลิก'
      }
    },
    colorChipStatus (val) {
      if (val === 'Pending') {
        return '#fefdec'
      } else if (val === 'Success') {
        return '#F0F9EE'
      } else if (val === 'Approve') {
        return '#F0F9EE'
      } else if (val === 'Process') {
        return '#e8f6f9'
      } else if (val === 'Reject') {
        return '#f7c5ad'
      } else if (val === 'Waiting') {
        return '#fdf8ed'
      } else {
        return '#F7D9D9'
      }
    },
    textChipStatus (val) {
      if (val === 'Pending') {
        return '#cfc60b'
      } else if (val === 'Success') {
        return '#1AB759'
      } else if (val === 'Approve') {
        return '#1AB759'
      } else if (val === 'Process') {
        return '#6EC4D6'
      } else if (val === 'Reject') {
        return '#f50'
      } else if (val === 'Waiting') {
        return '#E9A016'
      } else {
        return '#D1392B'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(5) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 130px;
          z-index: 10;
          background: white;
        }
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(5) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 130px;
        }
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
