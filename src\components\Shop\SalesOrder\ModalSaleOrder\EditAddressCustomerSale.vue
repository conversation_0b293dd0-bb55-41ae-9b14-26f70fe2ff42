<template>
  <div>
    <v-dialog v-model="EditaddressDialog" width="918" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
          <v-row style="height: 120px;">
              <v-col style="text-align: center;" :class="MobileSize ? 'pt-6' : 'pt-6'">
                <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>{{ ChangeLang(title) }}</b></span>
              </v-col>
              <v-btn v-if="actions === 'addAddress' || actions === 'edit'" fab small @click="close()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
              <v-btn v-else fab small @click="cancel()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
        <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
          <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
            <v-col style="text-align: center;">
            </v-col>
          </v-row>
        </div>
        <div class="backgroundContent" style="position: relative;">
        <v-container class="pa-0">
          <!-- Address Desktop, IpadPro, Ipad -->
          <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
          <div :class="MobileSize ? 'py-6 px-4' : 'py-12 px-10'">
          <v-card-text v-if="!MobileSize" >
            <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="30" height="30"></v-img>
            <span style="font-size: 20px; font-weight: 700;">{{ $t('AddressProfilePage.ShippingAddressModal.RecipientInformation') }}</span>
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="12" class="mt-6" v-if="roleCustomer === 'business'">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.CompanyName') }}<span style="color: red;">*</span></span>
                  <!-- <v-text-field class="input_text" placeholder="ระบุชื่อ" outlined dense v-model="name" :rules="Rules.name" counter="50" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^A-Za-zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderFirstName')" outlined dense v-model="name" :rules="Rules.name" counter="100"></v-text-field>
                </v-col>
                <v-col cols="6" class="pr-5 mt-6" v-if="roleCustomer === 'general'">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.FirstName') }}<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderFirstName')" outlined dense v-model="first_name" :rules="Rules.first_name" counter="20" oninput="this.value = this.value.replace(/[^A-Za-zก-๏]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="6" class="mt-6" v-if="roleCustomer === 'general'">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.LastName') }}<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderLastName')" outlined dense v-model="last_name" :rules="Rules.last_name" counter="30" oninput="this.value = this.value.replace(/[^A-Za-zก-๏()]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12" >
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Phone') }}<span style="color: red;">*</span></span>
                  <!-- <v-text-field class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.tel" maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  <v-text-field v-if="SaleVendor === false" class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderPhone')" outlined dense v-model="phone" oninput="this.value = this.value.replace(/^\s+(?=\d)/,'').replace(/(\\s\d{2}).+/g, '$1')" :rules="Rules.telVendor" maxlength="40" ></v-text-field>
                  <!-- <v-text-field v-else class="input_text" placeholder="เบอร์โทรศัพท์" outlined dense v-model="phone" :rules="Rules.telVendor" maxlength="40" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field> -->
                  <v-text-field v-else class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderPhone')" outlined dense v-model="phone" oninput="this.value = this.value.replace(/^\s+(?=\d)/,'').replace(/(\\s\d{2}).+/g, '$1')" :rules="Rules.telVendor" maxlength="40" ></v-text-field>
                </v-col>
                <v-col cols="12" class="mt-3">
                  <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="30" height="30"></v-img>
                  <span style="font-size: 20px; font-weight: 700;">{{ $t('AddressProfilePage.ShippingAddressModal.RecipientAddressInformation') }}</span>
                </v-col>
                <v-col cols="4" class="pr-5 mt-6">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.HouseNo') }}<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderHouseNo')" outlined dense v-model="house_no" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5 mt-6">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.RoomNo') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderRoomNo')" outlined dense v-model="room_no" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4 mt-6">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Floor') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderFloor')" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12"></v-col>
                <v-col cols="4" class="pr-5">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.BuildingName') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderBuildingName')" outlined dense v-model="building_name" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Village') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderVillage')" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense v-model="moo_ban" :rules="Rules.maxText"></v-text-field>
                </v-col>
                <v-col cols="4">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.VillageNo') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderVillageNo')" outlined dense v-model="moo_no" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Alley') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderAlley')" outlined dense v-model="soi" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4" class="pr-5">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Junction') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderJunction')" outlined dense v-model="yaek" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="4">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Road') }}</span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderRoad')" outlined dense v-model="street" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s().]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.SubDistrict') }}<span style="color: red;">*</span></span>
                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label=""  v-model="subdistrict" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderSubDistrict')"/>
                  <div v-if="checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="6">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.District') }}<span style="color: red;">*</span></span>
                  <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderDistrict')" />
                  <div v-if="checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <!-- <v-col cols="3">
                  <v-text-field class="input_text" placeholder="ชื่อถนน" outlined dense v-model="street" :rules="Rules.maxText"></v-text-field>
                </v-col>
                <v-col cols="6" class="pr-5">
                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="6">
                  <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                  <div v-if="checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col> -->
                <v-col cols="6" class="pr-5">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Province') }}<span style="color: red;">*</span></span>
                  <addressinput-province label="" v-model="province" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderProvince')" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="6">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Zipcode') }}<span style="color: red;">*</span></span>
                  <addressinput-zipcode label="" v-model="zipcode" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderZipCode')" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <!-- <v-col cols="6" class="pr-5">
                  <addressinput-province label="" v-model="province" placeholder="ระบุจังหวัด" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="6">
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="ระบุรหัสไปรษณีย์" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" />
                  <div v-if="checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col> -->
              </v-row>
              <!-- <v-row v-if="page === 'checkout' && title === 'เพิ่มที่อยู่จัดส่งสินค้า'">
                <v-col>
                  <v-checkbox dense v-model="defaultAdress" label="ตั้งค่าเป็นที่อยู่เริ่มต้น" value="Y" ></v-checkbox>
                </v-col>
              </v-row> -->
            </v-form>
          </v-card-text>
          <!-- Address Mobile -->
          <v-card-text v-if="MobileSize" class="pa-0">
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <v-row no-gutters>
                <v-col cols="12" class="mb-4">
                  <v-img class="float-left mt-n2 mr-2" src="@/assets/Frame1.png" width="24" height="24"></v-img>
                  <span style="font-size: 18px; font-weight: 700;">{{ $t('AddressProfilePage.ShippingAddressModal.RecipientInformation') }}</span>
                </v-col>
                <v-col cols="12" class="mt-6" v-if="roleCustomer === 'business'">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.CompanyName') }}<span style="color: red;">*</span></span>
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderFirstName')" outlined dense v-model="name" :rules="Rules.name" counter="100" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^A-Za-zก-๏\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12" v-if="roleCustomer === 'general'">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.FirstName') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" v-if="roleCustomer === 'general'">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderFirstName')" outlined dense v-model="first_name" :rules="Rules.first_name" counter="20" oninput="this.value = this.value.replace(/[^A-Za-zก-๏]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12" v-if="roleCustomer === 'general'">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.LastName') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" v-if="roleCustomer === 'general'">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderLastName')" outlined dense v-model="last_name" :rules="Rules.last_name" counter="30" oninput="this.value = this.value.replace(/[^A-Za-zก-๏()]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Phone') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" class="mb-4">
                  <v-text-field v-if="SaleVendor === true" class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderPhone')" outlined dense v-model="phone" oninput="this.value = this.value.replace(/^\s+(?=\d)/,'').replace(/(\\s\d{2}).+/g, '$1')" :rules="Rules.telVendor" maxlength="40" ></v-text-field>
                  <v-text-field v-else class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderPhone')" outlined dense v-model="phone" oninput="this.value = this.value.replace(/^\s+(?=\d)/,'').replace(/(\\s\d{2}).+/g, '$1')" :rules="Rules.telVendor" maxlength="40" ></v-text-field>
                </v-col>
                <v-col cols="12" class="mb-4">
                  <v-img class="float-left mt-n2 mr-2" src="@/assets/map1.png" width="24" height="24"></v-img>
                  <span style="font-size: 18px; font-weight: 700;">{{ $t('AddressProfilePage.ShippingAddressModal.RecipientAddressInformation') }}</span>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.HouseNo') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderHouseNo')" outlined dense v-model="house_no" :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12" >
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.RoomNo') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderRoomNo')" outlined dense v-model="room_no" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Floor') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderFloor')" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12"></v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.BuildingName') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderBuildingName')" outlined dense v-model="floor" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Village') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderVillage')" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense v-model="moo_ban" :rules="Rules.maxText"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.VillageNo') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderVillageNo')" outlined dense v-model="moo_no" :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Alley') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderAlley')" outlined dense v-model="soi" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Junction') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderJunction')" outlined dense v-model="yaek" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Road') }}</span>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="input_text" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderRoad')" outlined dense v-model="street" :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s().]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.SubDistrict') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" label=""  v-model="subdistrict" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderSubDistrict')"/>
                  <div v-if="checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.District') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderDistrict')" />
                  <div v-if="checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Province') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-province label="" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderProvince')" />
                  <div v-if="checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
                <v-col cols="12">
                  <span>{{ $t('AddressProfilePage.ShippingAddressModal.Zipcode') }}<span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12">
                  <addressinput-zipcode label="" v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('AddressProfilePage.ShippingAddressModal.PlaceHolderZipCode')" />
                  <div v-if="checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                </v-col>
              </v-row>
              <!-- <v-row v-if="page === 'checkout' && title === 'เพิ่มที่อยู่จัดส่งสินค้า'">
                <v-col>
                  <v-checkbox dense v-model="defaultAdress" label="ตั้งค่าเป็นที่อยู่เริ่มต้น" value="Y" ></v-checkbox>
                </v-col>
              </v-row> -->
            </v-form>
          </v-card-text>
          </div>
          </v-card>
        </v-container>
        </div>
        </v-card-text>
          <v-card-actions v-if="!MobileSize" class="px-12" style="height: 88px; background-color: #F5FCFB;">
            <!-- <v-row :justify="!MobileSize ? 'end' : 'center'">
              <v-btn class="px-5 mr-2" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'addressProfile' ? createAddresss() : title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'checkout' ? updateAddress() : updateAddress()">บันทึก</v-btn>
            </v-row> -->
          <v-btn v-if="actions === 'addAddress' || actions === 'edit'" class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="close()">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
          <v-btn v-else class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">{{ $t('ModalAddCustomerSaleOrder.Button.Back') }}</v-btn>
          <v-spacer></v-spacer>
          <v-btn v-if="actions === 'addAddress' || actions === 'edit'" class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Save') }}</v-btn>
          <v-btn v-else class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="nextStep()">{{ $t('ModalAddCustomerSaleOrder.Button.Next') }}</v-btn>
          </v-card-actions>
          <v-card-actions v-if="MobileSize" class="px-4" style="height: 88px; background-color: #F5FCFB;">
            <!-- <v-row :justify="!MobileSize ? 'end' : 'center'">
              <v-btn class="px-5 mr-2" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'addressProfile' ? createAddresss() : title === 'เพิ่มที่อยู่จัดส่งสินค้า' && page === 'checkout' ? updateAddress() : updateAddress()">บันทึก</v-btn>
            </v-row> -->
          <v-btn v-if="actions === 'addAddress' || actions === 'edit'"  class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="close()">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
          <v-btn v-else  class="px-10 mr-2" style="border-radius: 40px;" outlined color="#27AB9C" @click="cancel()">{{ $t('ModalAddCustomerSaleOrder.Button.Back') }}</v-btn>
          <v-spacer></v-spacer>
          <v-btn v-if="actions === 'addAddress' || actions === 'edit'" class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Save') }}</v-btn>
          <v-btn v-else class="px-10 white--text" style="border-radius: 40px;" color="#27AB9C"  @click="nextStep()">{{ $t('ModalAddCustomerSaleOrder.Button.Next') }}</v-btn>
          </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitConfirm" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogAwaitConfirm = !dialogAwaitConfirm"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? $t('AddressProfilePage.ShippingAddressModal.awaitSuccess1') : $t('AddressProfilePage.ShippingAddressModal.awaitSuccess2') }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ $t('BankAccountPage.Proceed') }}</span>
          </v-card-text>
          <v-card-text v-if="!MobileSize">
            <v-row  dense justify="center">
              <v-btn width="156" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? createAddresss() : updateAddress()">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
          <v-card-text v-if="MobileSize">
            <v-row dense justify="space-between">
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="dialogAwaitConfirm = !dialogAwaitConfirm">{{ $t('userProfileNewUI.Cancel') }}</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? createAddresss() : updateAddress()">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="dialogSuccess = !dialogSuccess"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>{{ title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? $t('AddressProfilePage.ShippingAddressModal.Success1') : $t('AddressProfilePage.ShippingAddressModal.Success2') }}</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">{{ title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า' ? $t('AddressProfilePage.ShippingAddressModal.Success3') : $t('AddressProfilePage.ShippingAddressModal.Success4') }}</span><br/>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center" v-if="!MobileSize">
            <v-btn width="156" height="38" dark rounded color="#27AB9C" @click="dialogSuccess = !dialogSuccess">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
            <v-row dense justify="center" v-if="MobileSize">
            <v-btn height="38" dark rounded color="#27AB9C" :style="{ flex: '1' }" @click="dialogSuccess = !dialogSuccess">{{ $t('userProfileNewUI.Confirm') }}</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <ModalETaxCustomerSale ref="ModalETaxCustomerSale"/>
  </div>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import { Decode, Encode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  // props: ['EditAddressDetail', 'title', 'page', 'customerSaleData'],
  components: {
    ModalETaxCustomerSale: () => import('@/components/Shop/SalesOrder/ModalSaleOrder/EditETAXAddressCustomerSale')
  },
  data () {
    return {
      dataIsVendor: false,
      SaleVendor: false,
      title: '',
      actions: '',
      name: '',
      roleCustomer: '',
      titleAddress: '',
      userDetail: [],
      dialogSuccess: false,
      dialogAwaitConfirm: false,
      defaultAdress: false,
      dataEditAddress: [],
      data: [],
      lazy: false,
      first_name: '',
      last_name: '',
      phone: '',
      detail: '',
      EditaddressDialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      house_no: '',
      room_no: '',
      floor: '',
      building_name: '',
      moo_ban: '',
      moo_no: '',
      soi: '',
      yaek: '',
      street: '',
      id: '',
      default_address: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        telVendor: [
          v => !!v || this.$t('AddressProfilePage.ShippingAddressModal.error6'),
          v => v.length >= 9 || this.$t('AddressProfilePage.ShippingAddressModal.error23')
        ],
        name: [
          v => !!v || this.$t('AddressProfilePage.ShippingAddressModal.error24'),
          v => v.length <= 100 || this.$t('AddressProfilePage.ShippingAddressModal.error25')
        ],
        first_name: [
          v => !!v || this.$t('AddressProfilePage.ShippingAddressModal.error2'),
          v => v.length <= 20 || this.$t('AddressProfilePage.ShippingAddressModal.error3')
        ],
        last_name: [
          v => !!v || this.$t('AddressProfilePage.ShippingAddressModal.error4'),
          v => v.length <= 30 || this.$t('AddressProfilePage.ShippingAddressModal.error5')
        ],
        tel: [
          v => !!v || this.$t('AddressProfilePage.ShippingAddressModal.error6'),
          v => v.charAt(0) === '0' || this.$t('AddressProfilePage.ShippingAddressModal.error7'),
          v => v.charAt(1) !== '0' || 'กรุณากรอกเบอร์โทรศัพท์ให้ถูกต้อง',
          v => v.length === 10 || v === '' || this.$t('AddressProfilePage.ShippingAddressModal.error8')
        ],
        house_no: [
          v => !!v || this.$t('AddressProfilePage.ShippingAddressModal.error14'),
          v => v.charAt(0) !== '-' || this.$t('AddressProfilePage.ShippingAddressModal.error15'),
          v => (v.split('').filter(char => char === '-').length <= 1) || this.$t('AddressProfilePage.ShippingAddressModal.error16'),
          v => (/^[-0-9,-/]+$/.test(v) || v.length === 0) || this.$t('AddressProfilePage.ShippingAddressModal.error17'),
          v => v.length <= 120 || this.$t('AddressProfilePage.ShippingAddressModal.error18'),
          v => ((/^[0-9,-/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || this.$t('AddressProfilePage.ShippingAddressModal.error19')
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || this.$t('AddressProfilePage.ShippingAddressModal.error17'),
          v => v.length <= 120 || this.$t('AddressProfilePage.ShippingAddressModal.error18'),
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || this.$t('AddressProfilePage.ShippingAddressModal.error19')
        ],
        maxText: [
          v => v.length <= 120 || this.$t('AddressProfilePage.ShippingAddressModal.error18')
        ]
        // room_no: [
        //   v => !!v || 'กรุณาระบุห้องเลขที่'
        // ],
        // floor: [
        //   v => !!v || 'กรุณาระบุชั้นที่'
        // ],
        // building_name: [
        //   v => !!v || 'กรุณาระบุอาคาร'
        // ],
        // moo_ban: [
        //   v => !!v || 'กรุณาระบุหมู่บ้าน'
        // ],
        // soi: [
        //   v => !!v || 'กรุณาระบุตรอก/ซอย'
        // ],
        // yaek: [
        //   v => !!v || 'กรุณาระบุแยก'
        // ],
        // street: [
        //   v => !!v || 'กรุณาระบุถนน'
        // ]
      }
    }
  },
  watch: {
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  mounted () {
    this.$EventBus.$on('EditModalAddress', this.open)
    this.$EventBus.$on('itemEdit', this.itemEdit)
    this.$EventBus.$on('actionSale', this.actionSale)
    this.$EventBus.$on('EditAddressCustomerComplete', this.close)
    this.$EventBus.$on('clearData', this.clearData)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('ChackRowUser')
      this.$EventBus.$off('itemEdit')
      this.$EventBus.$off('actionSale')
      this.$EventBus.$off('EditModalAddress')
      this.$EventBus.$off('EditAddressCustomerComplete')
      this.$EventBus.$off('clearData')
    })
  },
  // beforeDestroy () {
  //   this.$EventBus.$off('itemEdit')
  // },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    ChangeLang (val) {
      if (val === 'เพิ่มที่อยู่ในการจัดส่งสินค้า') {
        if (this.$i18n.locale === 'th') {
          return val
        } else {
          return this.$t('AddressProfilePage.ShippingAddressModal.TitleAddAddress')
        }
      } else if (val === 'แก้ไขอยู่ในการจัดส่งสินค้า') {
        if (this.$i18n.locale === 'th') {
          return val
        } else {
          return this.$t('AddressProfilePage.ShippingAddressModal.TitleEditAddress')
        }
      }
    },
    itemEdit (data) {
      // console.log('11=====>')
      this.dataIsVendor = data.is_vendor === 'yes'
    },
    clearData () {
      this.name = ''
      this.first_name = ''
      this.last_name = ''
      this.phone = ''
      this.house_no = ''
      this.room_no = ''
      this.floor = ''
      this.building_name = ''
      this.moo_ban = ''
      this.moo_no = ''
      this.soi = ''
      this.yaek = ''
      this.street = ''
      this.subdistrict = ''
      this.district = ''
      this.province = ''
      this.zipcode = ''
    },
    close () {
      // console.log('close')
      this.EditaddressDialog = false
    },
    cancel () {
      var val = {
        name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
        email: null,
        first_name: this.first_name === null ? '' : this.first_name,
        last_name: this.last_name === null ? '' : this.last_name,
        phone: this.phone,
        house_no: this.house_no,
        room_no: this.room_no,
        floor: this.floor,
        building: this.building_name,
        moo_ban: this.moo_ban,
        moo_no: this.moo_no,
        soi: this.soi,
        yaek: this.yaek,
        street: this.street,
        sub_district: this.subdistrict,
        district: this.district,
        province: this.province,
        postcode: this.zipcode
      }
      // console.log('val', val)
      localStorage.setItem('AddressBackward', Encode.encode(val))
      this.EditaddressDialog = false
      // this.EditAddressDetail = []
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    async open (val, title, page, actions, saleVendor) {
      this.title = title
      this.actions = actions
      this.roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      // console.log('dataIsVendor2', this.dataIsVendor)
      this.page = page
      if (saleVendor !== undefined) {
        if (this.dataIsVendor === true) {
          this.SaleVendor = this.dataIsVendor
        } else {
          this.SaleVendor = saleVendor
        }
      } else {
        this.SaleVendor = false
      }
      // console.log('getAddressData หน้าeditaddress')
      // console.log('title', title)
      // console.log('this.tiltle', this.title)
      // console.log('val', val)
      // console.log('page', page)
      // console.log('actions', actions)
      if (this.title !== undefined && this.title !== '') {
        if (localStorage.getItem('AddressBackward') !== null) {
          var data = JSON.parse(Decode.decode(localStorage.getItem('AddressBackward')))
          // console.log('test', data)
          this.name = data.name
          this.first_name = data.first_name
          this.last_name = data.last_name
          this.house_no = data.house_no === '' ? '' : data.house_no
          this.moo_ban = data.moo_ban === '' ? '-' : data.moo_ban
          this.building_name = data.building_name === '' ? '-' : data.building
          this.street = data.street === '' ? '-' : data.street
          this.soi = data.soi === '' ? '-' : data.soi
          this.room_no = data.room_no === '' ? '-' : data.room_no
          this.floor = data.floor === '' ? '-' : data.floor
          this.moo_no = data.moo_no === '' ? '-' : data.moo_no
          this.yaek = data.yaek === '' ? '-' : data.yaek
          this.subdistrict = data.sub_district
          this.district = data.district
          this.province = data.province
          this.phone = data.phone
          this.zipcode = data.postcode
          this.id = data.id
          this.default_address = data.default_address
          this.EditaddressDialog = true
        } else {
          if (localStorage.getItem('AddAddressCustomer') || localStorage.getItem('AddressCustomerDetail') !== null) {
            // console.log('addressData', addressData)
            // console.log('1111')
            if (this.title === 'เพิ่มที่อยู่ในการจัดส่งสินค้า') {
              // console.log('222')
              // this.$refs.FormAddress.resetValidation()
              this.name = ''
              this.first_name = ''
              this.last_name = ''
              this.house_no = ''
              this.moo_ban = ''
              this.building_name = ''
              this.street = ''
              this.soi = ''
              this.room_no = ''
              this.floor = ''
              this.moo_no = ''
              this.yaek = ''
              this.subdistrict = ''
              this.district = ''
              this.province = ''
              this.phone = ''
              this.zipcode = ''
              this.id = ''
              this.default_address = 'Y'
              this.EditaddressDialog = true
              this.$refs.FormAddress.resetValidation()
            } else {
              // console.log('3333', val)
              // console.log('2', this.SaleVendor)
              // console.log('3', this.dataIsVendor)
              var NameSplit = []
              var Vendor = false
              if (this.SaleVendor === true || this.dataIsVendor === true) {
                NameSplit = val.name.split(' ')
                Vendor = true
              } else {
                Vendor = false
              }
              // console.log('NameSplit', NameSplit)
              // console.log('NameSplit[1]', NameSplit[1])
              // console.log('Vendor', Vendor)
              this.name = val.name
              this.first_name = Vendor === true ? NameSplit[0] : val.first_name
              this.last_name = Vendor === true && NameSplit[1] !== undefined ? NameSplit[1] : val.last_name
              this.house_no = val.house_no === '' ? '' : val.house_no
              this.moo_ban = val.moo_ban === '' ? '-' : val.moo_ban
              this.building_name = val.building_name === '' ? '-' : val.building
              this.street = val.street === '' ? '-' : val.street
              this.soi = val.soi === '' ? '-' : val.soi
              this.room_no = val.room_no === '' ? '-' : val.room_no
              this.floor = val.floor === '' ? '-' : val.floor
              this.moo_no = val.moo_no === '' ? '-' : val.moo_no
              this.yaek = val.yaek === '' ? '-' : val.yaek
              this.subdistrict = val.sub_district
              this.district = val.district
              this.province = val.province
              this.phone = val.phone
              this.zipcode = val.postcode
              this.id = val.id
              this.default_address = val.default_address
              this.EditaddressDialog = true
            }
          }
        }
        // console.log('sale', this.SaleVendor, val)
        this.$store.commit('closeLoader')
      }
    },
    actionSale (action, cusType) {
      this.actions = action
      this.cusType = cusType
      // console.log('actionชชชชชชชชชฬฬฬฬ', action)
    },
    nextStep () {
      var val
      var data = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerDetail')))
      var roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
      if (this.$refs.FormAddress.validate(true)) {
        if (this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode)) {
          // if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
          if (this.actions === 'add') {
            this.titleAddress = 'เพิ่มที่อยู่ในการออกใบกำกับภาษี'
            // this.page = 'shoppage'
            if (roleCustomer === 'general' || this.SaleVendor === true) {
              val = {
                name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                email: null,
                first_name: this.first_name,
                last_name: this.last_name,
                phone: this.phone,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                soi: this.soi,
                yaek: this.yaek,
                street: this.street,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                postcode: this.zipcode
              }
            } else {
              val = {
                name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                email: null,
                first_name: '',
                last_name: '',
                phone: this.phone,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                soi: this.soi,
                yaek: this.yaek,
                street: this.street,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                postcode: this.zipcode
              }
            }
            data.cus_address = val
            if (roleCustomer === 'general') {
              localStorage.setItem('AddressCustomerSale', Encode.encode(data))
            } else {
              localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(data))
            }
          } else {
            // console.log('editใบกำกับภาษี')
            this.titleAddress = 'แก้ไขที่อยู่ในการออกใบกำกับภาษี'
            // this.page = 'shoppage'
            if (roleCustomer === 'general') {
              val = {
                name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                email: null,
                first_name: this.first_name,
                last_name: this.last_name,
                phone: this.phone,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                soi: this.soi,
                yaek: this.yaek,
                street: this.street,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                postcode: this.zipcode
              }
            } else {
              val = {
                name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                email: null,
                first_name: '',
                last_name: '',
                phone: this.phone,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                soi: this.soi,
                yaek: this.yaek,
                street: this.street,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                postcode: this.zipcode
              }
            }
            data.cus_address = val
            if (roleCustomer === 'general') {
              localStorage.setItem('AddressCustomerSale', Encode.encode(data))
            } else {
              localStorage.setItem('AddressCustomerBussinessSale', Encode.encode(data))
            }
          }
          // console.log('datadatadatadatadata', data)
          // localStorage.setItem('AddressCustomerDetail', Encode.encode(addData))
          // console.log('nextStep', val)
          // console.log('titleAddress', this.titleAddress, this.page)
          // this.EditAddressDetail = data
          // console.log('nextStep')
          this.$refs.ModalETaxCustomerSale.open(data, this.titleAddress, this.page, this.actions, this.SaleVendor, this.SaleVendor)
          // this.$EventBus.$emit('EditModalEtaxAddress', data, this.titleAddress, this.page, this.actions)
          // } else {
          //   this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, text: this.$t('AddressProfilePage.ShippingAddressModal.error21'), showConfirmButton: false, timer: 2500 })
          // }
        } else {
          this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, text: this.$t('AddressProfilePage.ShippingAddressModal.error22'), showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
      }
    //   this.$refs.ModalETaxCustomerSale.open('เพิ่มที่อยู่ในการออกใบกำกับภาษี', this.titleAddress, this.SaleID)
    },
    async createAddresss () {
      // console.log('createAddresss')
      this.$store.commit('openLoader')
      if (this.$refs.FormAddress.validate(true)) {
        if (this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode)) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            // console.log('zipcodezipcode', this.zipcode)
            var roleSale = JSON.parse(localStorage.getItem('roleUser')).role
            if (roleSale !== 'sale_order_no_JV') {
              roleSale = 'seller'
            }
            // var roleSale = 'sale_order_no_JV'
            // ฟิก
            // var AddressCustomerDetail = JSON.parse(Decode.decode(localStorage.getItem('AddressCustomerDetail')))
            // console.log('AddressCustomerDetail===>', AddressCustomerDetail)
            var addData
            var data
            const check = this.checkAddress()
            var userAddAddress
            // กรณีเพิ่มแค่ที่อยู่ลูกค้า
            if (check.length !== 0) {
              if (this.roleCustomer === 'general') {
                // console.log('page', this.page, roleSale)
                // console.log('เพิ่มที่อยู่ลูกค้าทั่วไป')
                data = JSON.parse(Decode.decode(localStorage.getItem('AddAddressCustomer')))
                addData = {
                  seller_shop_id: data.seller_shop_id,
                  role: roleSale,
                  cus_id: data.cus_id,
                  name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                  first_name: this.first_name,
                  last_name: this.last_name,
                  phone: this.phone,
                  house_no: this.house_no,
                  room_no: this.room_no,
                  floor: this.floor,
                  building: this.building_name,
                  moo_ban: this.moo_ban,
                  moo_no: this.moo_no,
                  soi: this.soi,
                  yaek: this.yaek,
                  street: this.street,
                  sub_district: this.subdistrict,
                  district: this.district,
                  province: this.province,
                  postcode: this.zipcode
                }
                // AddressCustomerDetail.cus_address = addData
                // userAddAddress = {
                //   message: 'Create customer successfully.'
                // }
                // console.log('addData', addData)
                localStorage.setItem('AddAddressCustomerSale', Encode.encode(addData))
                await this.$store.dispatch('actionsCreateCustomerAddress', addData)
                userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomerAddress
              } else {
                // console.log('เพิ่มที่อยู่ลูกค้าบริษัท')
                data = JSON.parse(Decode.decode(localStorage.getItem('AddAddressCustomer')))
                // console.log('bussi', data)
                addData = {
                  seller_shop_id: data.seller_shop_id,
                  role: roleSale,
                  cus_id: data.cus_id,
                  name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                  first_name: '',
                  last_name: '',
                  phone: this.phone,
                  house_no: this.house_no,
                  room_no: this.room_no,
                  floor: this.floor,
                  building: this.building_name,
                  moo_ban: this.moo_ban,
                  moo_no: this.moo_no,
                  soi: this.soi,
                  yaek: this.yaek,
                  street: this.street,
                  sub_district: this.subdistrict,
                  district: this.district,
                  province: this.province,
                  postcode: this.zipcode
                }
                // console.log('addData', addData)
                // AddressCustomerDetail.cus_address = addData
                localStorage.setItem('AddAddressCustomerBussinessSale', Encode.encode(addData))
                // userAddAddress = {
                //   message: 'Create customer address successfully.'
                // }
                await this.$store.dispatch('actionsCreateCustomerAddress', addData)
                userAddAddress = await this.$store.state.ModuleUser.stateCreateCustomerAddress
              }
              // this.nextStep(AddressCustomerDetail)
              if (userAddAddress.message === 'Create customer successfully.' || userAddAddress.message === 'Create customer address successfully.') {
                this.dialog = false
                this.EditaddressDialog = false
                this.dialogAwaitConfirm = false
                this.$EventBus.$emit('EditComplete', data.cus_id)
                localStorage.removeItem('AddAddressCustomer')
                localStorage.removeItem('AddAddressCustomerSale')
                localStorage.removeItem('AddAddressCustomerBussinessSale')
                // this.$EventBus.$emit('SentGetCart', this.SentGetCart)
                // console.log('11')
                this.$EventBus.$emit('getListCustomerOfSales')
                this.$EventBus.$emit('listAddress', data)
                this.$EventBus.$emit('EditAddressComplete')
                // console.log('22')
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
              } else {
                this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, text: this.$t('AddressProfilePage.ShippingAddressModal.error21'), showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, text: this.$t('AddressProfilePage.ShippingAddressModal.error22'), showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    async updateAddress () {
      // console.log('updateAddress')
      this.$store.commit('openLoader')
      if (this.$refs.FormAddress.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkAddress()
            if (check.length !== 0) {
              // ฟิกกก
              var role = JSON.parse(localStorage.getItem('roleUser')).role
              if (role !== 'sale_order_no_JV') {
                role = 'seller'
              }
              var AddAddressCustomer = JSON.parse(Decode.decode(localStorage.getItem('AddAddressCustomer')))
              this.roleCustomer = JSON.parse(localStorage.getItem('sale_order_customer')).role_customer
              var data
              // console.log('roleCustomer', this.roleCustomer)
              data = {
                seller_shop_id: AddAddressCustomer.seller_shop_id,
                cus_id: AddAddressCustomer.cus_id === undefined ? AddAddressCustomer.id : AddAddressCustomer.cus_id,
                role: role,
                name: this.name === '' || this.name === null ? this.first_name + this.last_name : this.name,
                first_name: this.roleCustomer === 'business' ? '' : this.first_name,
                last_name: this.roleCustomer === 'business' ? '' : this.last_name,
                phone: this.phone,
                house_no: this.house_no,
                room_no: this.room_no,
                floor: this.floor,
                building: this.building_name,
                moo_ban: this.moo_ban,
                moo_no: this.moo_no,
                street: this.street,
                soi: this.soi,
                yaek: this.yaek,
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                postcode: this.zipcode,
                address_id: AddAddressCustomer.address_id,
                default_address: this.default_address
              }
              // console.log('data', data)
              await this.$store.dispatch('actionsUpdateCustomerAddress', data)
              const userAddress = await this.$store.state.ModuleSaleOrder.stateUpdateCustomerAddress
              if (userAddress.message === 'Update customer address successfully.') {
                // this.$swal.fire({ icon: 'success', title: 'แก้ไขที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
                // this.$EventBus.$emit('SentGetCart', this.SentGetCart)
                this.dialog = false
                this.EditaddressDialog = false
                this.$EventBus.$emit('getListCustomerOfSales')
                this.$EventBus.$emit('EditComplete', data.cus_id)
                this.$EventBus.$emit('listAddress', data)
                this.$EventBus.$emit('EditAddressComplete')
                this.$EventBus.$emit('getCustomDetail', data.cus_id, data.seller_shop_id)
                localStorage.removeItem('AddressData')
                this.dialogSuccess = true
                this.$store.commit('openLoader')
                setTimeout(() => { this.$store.commit('closeLoader') }, 2500)
              } else {
                this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, text: this.$t('AddressProfilePage.ShippingAddressModal.error21'), showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, text: this.$t('AddressProfilePage.ShippingAddressModal.error22'), showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: `<h5>${this.$t('AddressProfilePage.ShippingAddressModal.error20')}</h5>`, showConfirmButton: false, timer: 2500 })
      }
      this.dialogAwaitConfirm = false
      this.$store.commit('closeLoader')
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipcode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    async setDefaultAdress () {
      const defaultAdress = this.defaultAdress === null ? this.defaultAdress === 'N' : this.defaultAdress
      const data = {
        id: this.data.id,
        default_address: defaultAdress
      }
      await this.$store.dispatch('actionDefaultUserAddress', data)
      const res = await this.$store.state.ModuleUser.stateSetDefaultUserAddress
      if (res.message === 'Update default address success') {
        // console.log('set default adress success')
        // this.$swal.fire({ icon: 'success', title: 'ตั้งค่าเป็นที่อยู่เริ่มต้นแล้ว', showConfirmButton: false, timer: 1500 })
        // this.getAddress()
      } else {
        // console.log('set default adress fail')
        // this.$swal.fire({ icon: 'error', title: 'ดำเนินการตั้งค่าเป็นที่อยู่เริ่มต้นแล้วไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}
</style>
