@font-face {
  font-family: 'Noto Sans Thai';
  src: url('font/Noto_Sans_Thai/static/NotoSansThai-Regular.ttf') format('truetype');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'Noto Sans Thai';
  src: url('font/Noto_Sans_Thai/static/NotoSansThai-Bold.ttf') format('truetype');
  font-weight: 700;
  font-style: normal;
}

html,
body,
span,
div,
a,
h1,
h2,
h3,
h4,
b,
th,
td,
.topic,
.display-4,
.display-3,
.display-2,
.display-1,
.headline,
.title,
.subheading,
.body-2,
.body-1,
.caption {
  /* font-family: 'Prompt' !important; */
  font-family: 'Noto Sans Thai', sans-serif !important;
  /* color: #777777; */
}

.ant-btn-primary {
  background: #27ab9c;
  border-color: #27ab9c;
}

.ant-btn-primary:hover {
  background: #27ab9c;
  border-color: #27ab9c;
}

.ant-table-thead > tr > th {
  background-color: #fafafa !important;
  color: black;
}

.ant-checkbox-checked .ant-checkbox-inner {
  background-color: #27ab9c !important;
  border-color: #27ab9c !important;
}

.ant-checkbox-indeterminate .ant-checkbox-inner::after {
  background-color: #27ab9c !important;
  border-color: #27ab9c !important;
}

.ant-checkbox-wrapper:hover .ant-checkbox-inner,
.ant-checkbox:hover .ant-checkbox-inner,
.ant-checkbox-input:focus + .ant-checkbox-inner {
  border-color: #27ab9c !important;
}

.ant-pagination-item-active {
  color: white !important;
  font-weight: 500 !important;
  background: #27ab9c !important;
  border-color: #fafafa !important;
}

.ant-pagination-item a {
  color: #8c8c8c !important;
}

.ant-pagination-item a:hover {
  color: #8c8c8c !important;
  border-color: #d9d9d9 !important;
}

.ant-pagination-item:hover {
  color: #8c8c8c !important;
  border-color: #d9d9d9 !important;
}

.ant-pagination-item-active a,
.ant-pagination-item-active a:hover {
  color: white !important;
}

.ant-pagination-prev .ant-pagination-item-link,
.ant-pagination-next .ant-pagination-item-link {
  color: #8c8c8c !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-pagination-prev .ant-pagination-item-link:hover,
.ant-pagination-next .ant-pagination-item-link:hover {
  color: #8c8c8c !important;
  border: 1px solid #d9d9d9 !important;
}

.ant-tabs-nav .ant-tabs-tab {
  font-size: 16px;
}

.ant-tabs-nav .ant-tabs-tab-active {
  color: #27ab9c !important;
}

.ant-tabs-nav .ant-tabs-tab:hover {
  color: #27ab9c !important;
}

.ant-tabs-ink-bar {
  border-bottom: 2px solid #27ab9c;
}

.hide-background-hover::before {
  background-color: transparent !important;
}

.v-list .v-list-item--active {
  background-color: #e6f5f3 !important;
}

.v-list-item--link:before {
  background-color: #e6f5f3 !important;
}

.v-list .v-list-item--active .v-list-item__title {
  color: #27ab9c !important;
}

input.th-address-input {
  background-color: #fff;
  border: 1px solid #aaaaaa !important;
  box-sizing: border-box;
  color: #666 !important;
  display: inline-block;
  font-size: 16px;
  height: 40px;
  line-height: 38px;
  padding: 8px 6px;
  transition-property: border;
  transition: 0.2s ease-in-out;
  width: 100%;
}

input.th-address-input:focus {
  border: 2px solid #27ab9c !important;
}

.swal2-cancel {
  color: #27ab9c !important;
  background-color: #ffffff !important;
  border-radius: 2px !important;
  border: 1px solid #27ab9c !important;
  border-color: #27ab9c !important;
  border-left-color: #27ab9c !important;
  border-right-color: #27ab9c !important;
}

.v-application--is-ltr .v-data-footer__select .v-select {
  margin: 13px 0 13px 9px !important;
}

.v-data-footer {
  padding: 0px !important;
}

/* custom autocomplete solo */
.v-input__icon--clear .mdi-close::before {
  font-size: 16px;
}

.v-text-field.v-text-field--solo:not(.v-text-field--solo-flat)
  > .v-input__control
  > .v-input__slot {
  box-shadow: 0 0 0 0 !important;
  font-size: 14px;
  border: 1px solid rgba(0, 0, 0, 0.54);
}

.v-text-field.v-text-field--solo .v-label {
  font-size: 14px !important;
}

.v-list-item .v-list-item__title,
.v-list-item .v-list-item__subtitle {
  font-size: 14px !important;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
}

/* แก้ไขบัคกรณีที่มีการ select มีการซูมใน ios  */
.v-text-field:not(.v-text-field--single-line) input {
  margin-top: 0;
  font-size: inherit !important;
  line-height: 1.5rem !important;
}

.v-text-field.v-text-field--enclosed:not(.v-text-field--rounded)
  > .v-input__control
  > .v-input__slot {
  background: white;
  line-height: 1.5rem !important;
  padding: 0px 12px 0px 12px !important;
}

/* แก้กรณีที่ menu vuetify โดน menu option apexkChart ทับ */
.theme--light.v-sheet {
  z-index: 1 !important;
}

/* แก้ปัญหาเห็นสระข้างบนตัวอักษรไม่เต็ม */
.v-list-item__title {
  line-height: 1.5rem !important;
}

/* แก้ปัญหาเห็นสระข้างบนตัวอักษรไม่เต็ม ใน error massage */
.v-text-field.v-text-field--enclosed .v-text-field__details {
  padding: 3px 12px 3px 0px !important;
}

/* Global Max Width */
.container {
  max-width: 1440px !important;
}

/* Global responsive */
.vhl-btn-left {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  z-index: 2;
  opacity: 0.7;
}

.vhl-btn-right {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 24px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 12%), 0 1px 2px rgb(0 0 0 / 24%);
  z-index: 2;
  opacity: 0.7;
}

.vhl-btn-right {
  margin-left: auto;
}

.vhl-btn-left[data-v-8b923bbc] {
  margin-right: auto;
}

.swal-title {
  font-size: 16px !important;
}

.vue-treeselect__control {
  border-radius: 4px;
  height: 40px;
  border: 1px solid #757575;
  padding-top: 3px;
  font-size: 16px;
}
.vue-treeselect__placeholder {
  color: #c4c4c4;
}

.pd-link {
  padding-right: 10px;
}
