<template>
  <v-container>
    <v-row align="center">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-row justify="end">
          <v-col  align="end">
            <v-btn class="ml-2" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createUser()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มผู้ใช้งาน</v-btn>
          </v-col>
        </v-row>
      </v-col>
      <!-- รายการผู้ใช้งาน -->
      <v-col cols="12">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense justify="center">
            <v-col cols="12" md="12" sm="12" xs="12">
              <v-card-title style="font-weight: bold;">รายการผู้ใช้งาน</v-card-title>
            </v-col>
          </v-row>
          <v-col cols="12" class="py-0 px-4">
            <a-tabs @change="SelectedMenu">
              <a-tab-pane v-for="item in UserMenu" :key="item.key" :tab="item.name"></a-tab-pane>
            </a-tabs>
          </v-col>
          <!-- <TableUser :props="ListData" :type="stateStatus === 0 ? 'userList' : 'userRequest'"/> -->
          <TableUser :props="stateStatus === 0 ? this.UserData : this.RequsetUserData" :type="stateStatus === 0 ? 'userList' : 'userRequest'"/>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Tabs } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    TableUser: () => import(/* webpackPrefetch: true */ '@/components/AdminManage/User/UserTable')
  },
  data () {
    return {
      UserMenu: [
        { key: 0, name: 'ผู้ใช้งาน' },
        { key: 1, name: 'คำขอเป็นผู้ใช้งาน' }
      ],
      stateStatus: 0,
      ListData: [],
      UserData: [
        {
          id: 1,
          userID: '1',
          name_th: 'ทดสอบ ผู้ใช้งาน',
          email: '<EMAIL>',
          permission: { admin: false, adminAssistant: false, approver: false, buyer: true },
          verifyEmail: true,
          detail: {
            name_th: 'ทดสอบ ผู้ใช้งาน',
            email: '<EMAIL>',
            verifyEmail: true,
            tel: '0888233335',
            permission: { admin: true, adminAssistant: false, approver: false, buyer: true },
            userBuyerSetting: { group: '', department: '', approverType: '', approver: '' },
            userEmployeetSetting: { group: '', department: '', approverType: '', approver: '' }
          },
          userSetting: true,
          status: true
        }
      ],
      RequsetUserData: [
        {
          id: 1,
          oneId: '1',
          name_th: 'ทดสอบ ผู้ใช้งาน',
          email: '<EMAIL>',
          detail: {
            name_th: 'ทดสอบ ผู้ใช้งาน',
            email: '<EMAIL>',
            verifyEmail: true,
            tel: '0888233335',
            permission: { admin: true, adminAssistant: false, approver: false, buyer: true },
            userBuyerSetting: { group: '', department: '', approverType: '', approver: '' },
            userEmployeetSetting: { group: '', department: '', approverType: '', approver: '' }
          },
          created_at: '2022-03-21T04:46:36.000000Z',
          status: false
        }
      ]
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'ผู้ใช้งาน')
    this.$EventBus.$emit('changeNavAdminManage')
    this.getListUser()
  },
  methods: {
    async getListUser () {
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('companyData')))
      // console.log('companyData', companyData)
      const data = { company_id: companyData.id }
      // console.log('getListUser send data', data)
      await this.$store.dispatch('actionsListUserCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateListUserCompany
      // console.log('USERR', response.data)
      if (response.result === 'SUCCESS') {
        this.ListData = response.data
      }
    },
    createUser () {
      localStorage.setItem('backToPage', 'usersCompany')
      this.$router.push({ path: '/manageUserCompany?Status=Create' }).catch(() => {})
    },
    SelectedMenu (item) {
      this.stateStatus = item
    }
  }
}
</script>

<style scoped>
</style>
