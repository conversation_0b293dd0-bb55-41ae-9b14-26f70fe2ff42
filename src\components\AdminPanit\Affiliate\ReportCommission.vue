<template>
  <v-container :style="MobileSize ? 'background-color: white' : ''">
    <v-row class="my-2" v-if="MobileSize === false">
      <v-col cols="6">
        <span style="font-size: 24px; font-weight: bold;">รายงานค่าคอมมิชชัน Affiliate</span>
      </v-col>
      <v-col cols="6" class="d-flex justify-end align-center">
        <!-- <v-btn color="#38b2a4" rounded>
          <span class="white--text">EXPORT TRANSACTION</span>
        </v-btn> -->
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false">
      <v-col cols="6">
        <span style="font-size: 18px; font-weight: bold; color: #27ab9c;">ตารางค่าคอมมิชชันของร้านค้า {{totalShop}} รายการ</span>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn color="#38b2a4" rounded  @click="exportShopExcel">
            <span class="white--text">EXPORT EXCEL</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && commissionMenu === 'ตารางค่าคอมมิชชันร้านค้า'" class="mt-5">
      <v-col cols="6">
        <v-btn @click="backPages" icon>
          <v-icon>mdi-chevron-left</v-icon>
        </v-btn>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn color="#38b2a4" rounded  @click="exportShopExcel">
            <span class="white--text">EXPORT EXCEL</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && commissionMenu === 'ตารางค่าคอมมิชชันร้านค้า'" >
      <v-col>
        <v-select
          v-model="commissionMenu"
          :items="commissionMenuItem"
        >
          <template slot="selection">
            <span style="font-weight: 600; font-size: 18px;">{{commissionMenu}}</span>
          </template>
        </v-select>
      </v-col>
    </v-row>
    <v-row class="mb-2" v-if="MobileSize && commissionMenu === 'ตารางค่าคอมมิชชันร้านค้า'">
      <v-col>
        <v-card>
          <v-data-table
            :headers="shopCommissionsHeadersdata"
            :items="shopCommissionsdata.shop_data"
          >
            <template v-slot:[`item.index`]="{index}">
                <span>{{index + 1}}</span>
            </template>
            <template v-slot:[`item.users`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                    outlined
                    color="#27AB9C"
                    @click="onOpenDetailShop(item)"
                    class="pt-4 pb-4"
                >
                    <span>ดูรายละเอียด</span>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <v-row class="mb-2" v-if="MobileSize === false">
      <v-col>
        <v-card>
          <v-data-table
            :headers="shopCommissionsHeadersdata"
            :items="shopCommissionsdata.shop_data"
          >
            <template v-slot:[`item.index`]="{index}">
                <span>{{index + 1}}</span>
            </template>
            <template v-slot:[`item.path_logo`]="{item}">
              <v-avatar size="40">
                <v-img :src="item.path_logo" v-if="item.path_logo !== null"/>
                <v-img src="@/assets/NoImage.png" v-if="item.path_logo === null"/>
              </v-avatar>
            </template>
            <template v-slot:[`item.users`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                    outlined
                    color="#27AB9C"
                    @click="onOpenDetailShop(item)"
                    class="pt-4 pb-4"
                >
                    <span>ดูรายละเอียด</span>
                </v-btn>
              </v-row>
            </template>
          </v-data-table>
        </v-card>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && commissionMenu === 'ตารางค่าคอมมิชชันของผู้ใช้งาน'" class="mt-5">
      <v-col cols="6" class="d-flex align-center">
        <v-btn @click="backPages" icon>
          <v-icon>mdi-chevron-left</v-icon>
        </v-btn>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn color="#38b2a4" rounded @click="exportUserExcel">
            <span class="white--text">EXPORT EXCEL</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize === false" class="mt-5">
      <v-col cols="6" class="d-flex align-center">
        <span style="font-size: 18px; font-weight: bold; color: #27ab9c;">ตารางค่าคอมมิชชันของผู้ใช้ {{totalUser}} รายการ</span>
      </v-col>
      <v-col cols="6" class="d-flex justify-end">
        <v-btn color="#38b2a4" rounded @click="exportUserExcel">
            <span class="white--text">EXPORT EXCEL</span>
        </v-btn>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && commissionMenu === 'ตารางค่าคอมมิชชันของผู้ใช้งาน'" >
      <v-col>
        <v-select
          v-model="commissionMenu"
          :items="commissionMenuItem"
        >
          <template slot="selection">
            <span style="font-weight: 600; font-size: 18px;">{{commissionMenu}}</span>
          </template>
        </v-select>
      </v-col>
    </v-row>
    <v-row v-if="MobileSize && commissionMenu === 'ตารางค่าคอมมิชชันของผู้ใช้งาน'">
        <v-col>
          <v-card>
            <v-data-table
              :headers="userCommissionsHeaders"
              :items="userCommissionsdata.user_data"
            >
                <template v-slot:[`item.index`]="{index}">
                    <span>{{index + 1}}</span>
                </template>
                <template v-slot:[`item.seller_shops_commission`]="{ item }">
                <v-row dense justify="center">
                  <v-btn
                    outlined
                    color="#27AB9C"
                    @click="onOpenDetailUser(item)"
                    class="pt-4 pb-4"
                  >
                    <span>ดูรายละเอียด</span>
                  </v-btn>
                </v-row>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
    </v-row>
    <v-row v-if="MobileSize === false">
        <v-col>
          <v-card>
            <v-data-table
              :headers="userCommissionsHeaders"
              :items="userCommissionsdata.user_data"
            >
              <template v-slot:[`item.index`]="{index}">
                  <span>{{index + 1}}</span>
              </template>
              <template v-slot:[`item.seller_shops_commission`]="{ item }">
                <v-row dense justify="center">
                  <v-btn
                    outlined
                    color="#27AB9C"
                    @click="onOpenDetailUser(item)"
                    class="pt-4 pb-4"
                  >
                    <span>ดูรายละเอียด</span>
                  </v-btn>
                </v-row>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      shopDialogOpen: false,
      usersDialogOpen: false,
      seller_shop_id: localStorage.getItem('shopSellerID'),
      shopCommissionsdata: [],
      totalShop: 0,
      totalUser: 0,
      shopCommissionsHeadersdata: [
        { text: 'ลำดับ', value: 'index', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'โลโก้', value: 'path_logo', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'seller_shop_name_th', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชันทั้งหมด (บาท)', value: 'total_commission', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'users', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      userCommissionsdata: [],
      userCommissionsHeaders: [
        { text: 'ลำดับ', value: 'index', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อผู้ใช้ (ชื่อจริง - นามสกุล)', value: 'user_fullname', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', value: 'email', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทรศัพท์', value: 'phone', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนร้านค้า', value: 'count_shops', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าคอมมิชชันทั้งหมด (บาท)', value: 'total_commission', align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียดคำสั่งซื้อ', value: 'seller_shops_commission', align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      commissionMenu: 'ตารางค่าคอมมิชชันร้านค้า',
      commissionMenuItem: [
        'ตารางค่าคอมมิชชันร้านค้า',
        'ตารางค่าคอมมิชชันของผู้ใช้งาน'
      ],
      exportShopPath: '',
      exportUserPath: ''
    }
  },
  async created () {
    await this.fetchShopCommissions()
    await this.fetchUserCommissions()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/reportCommissionAffiliateAdminMobile' }).catch(() => {})
      } else {
        // localStorage.setItem('pathAdmin', 'reportCommissionAffiliateAdmin')
        this.$router.push({ path: '/reportCommissionAffiliateAdmin' }).catch(() => {})
        // this.$router.push({ path: '/sellerJoinAffiliate' }).catch(() => {})
      }
    },
    shopCommissionsdata (val) {
      // console.log(val)
    }
  },
  methods: {
    async onOpenDetailShop (val) {
      this.$store.commit('mutationsSetShopDetailUserCommissions', val.users)
      if (this.MobileSize) {
        this.$router.push({ path: `/reportCommissionAffiliateDetailAdminMobile?sellerShop_id=${val.seller_shop_id}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/reportCommissionAffiliateDetailAdmin?sellerShop_id=${val.seller_shop_id}` }).catch(() => {})
      }
      // this.$store.commit('mutationsSetShopDetailUserCommissions', this.shopCommissionsdata.users)
      // this.$router.push({ path: `/reportCommissionAffiliateDetailAdmin?sellerShop_id=${this.seller_shop_id}` }).catch(() => {})
    },
    async onOpenDetailUser (val) {
      this.$store.commit('mutationsSetUserDetailCommissionsAffiliate', val.seller_shops_commission)
      if (this.MobileSize) {
        this.$router.push({ path: `/reportCommissionAffiliateUserDetailAdminMobile?id=${val.user_id}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/reportCommissionAffiliateUserDetailAdmin?id=${val.user_id}` }).catch(() => {})
      }
    },
    async fetchShopCommissions () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsShopComissionAffiliateTable')
      var response = await this.$store.state.ModuleAdminManage.stateShopComissionAffiliateTable
      this.shopCommissionsdata = response.data
      this.totalShop = this.shopCommissionsdata.shop_data.length
      this.exportShopPath = response.data.export_path
      this.$store.commit('closeLoader')
    },
    async fetchUserCommissions () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsUserComissionAffiliateTable')
      var response = await this.$store.state.ModuleAdminManage.stateUserComissionAffiliateTable
      this.userCommissionsdata = response.data
      this.totalUser = this.userCommissionsdata.user_data.length
      this.exportUserPath = response.data.export_path
      this.$store.commit('closeLoader')
    },
    async backPages () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async exportShopExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: this.exportShopPath,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'report_shop_commission.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async exportUserExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: this.exportUserPath,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'report_user_commission.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    }
  }
}
</script>
