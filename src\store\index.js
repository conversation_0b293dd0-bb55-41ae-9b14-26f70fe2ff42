import Vue from 'vue'
import Vuex from 'vuex'
import ModuleGlobal from './store_global'
import ModuleHompage from './store_homepage_api/vuex_homepage_api'
import ModuleProduct from './store_product_api/vuex_product_api'
import ModuleShop from './store_shop/vuex_shop_api'
import ModuleCart from './store_Cart/vuex_cart_api'
import ModuleManageShop from './store_manage_shop/vuex_manage_api'
import ModuleUser from './store_user/vuex_user_api'
import ModuleOrder from './store_order/vuex_order_api'
import ModuleBusiness from './store_Business/vuex_business'
import ModuleRegister from './store_Register/vuex_register_api'
import ModuleInventory from './store_inventory/vuex_inventory_api'
import ModuleProductNode from './store_product_api_node/vuex_product_api_node'
import ModuleFavoriteProduct from './store_favorite_product/vuex_ups_favorite_product_api'
import ModuleAdminManage from './store_admin_manage/vuex_admin_manage'
import ModuleDepartment from './store_Departments/vuex_deparment'
import ModuleCurier from './store_Curier/vuex_curier'
import ModuleTacking from './store_Tacking/vuex_tacking_api'
import ModuleRegisMorpromt from './store_regis_morpromt/vuex_regis_morpromt'
import ModuleNotification from './store_notification/vuex_notification_api'
import ModulePartner from './store_Partner/vuex_partner_api'
import ModuleSettingQuotation from './store_setting_quotation/vuex_setting_quotation_api'
import ModuleSettingTier from './store_setting_tier/vuex_setting_tier_api'
import ModuleReviewBuyer from './store_review_buyer/vuex_review_buyer_api'
import ModuleETax from './store_Etax/vuex_Etax_api'
import ModuleMyCouponsPoints from './store_MyCuoponsAndPoints/vuex_mycoupnsandpoints'
import ModuleAdminPanit from './store_admin_panit/vuex_admin_panit'
import ModuleApprove from './store_approve/vuex_approve'
import ModuleDashBoard from './store_seller_dashboard/vuex_seller_dashboard_api'
import ModuleShippingReport from './store_ShippingReport/vuex_shipping_report'
import ModuleDashBoardForAdmin from './store_dashboardForAdmin/vuex_dashboardForAdmin'
import ModuleDashBoardSaleOrder from './store_dashboard_sale_order/vuex_dashboard_sale_order_api'
import ModuleSaleOrder from './store_sale_order/vuex_sale_order_api'
import ModuleManagePoint from './store_manage_point/vuex_manage_point_api'
import ModuleManageCoupon from './store_manage_coupon/vuex_manage_coupon_api'
import ModuleManageFlashSale from './store_manage_flashsale/vuex_manage_flashsale_api'
import ModuleDigitalSignature from './store_digital_signature/vuex_digital_signature'
import ModuleAffiliate from './store_Affiliate/vuex_affiliate'
import ModuleDashboardAffiliateShop from './store_dashboard_affiliate_shop/vuex_dashboard_affiliate_shop'
import ModuleDashboardAffiliateBuyer from './store_dashboard_affiliate_buyer/vuex_dashboard_affiliate_buyer'
import ModuleOrderList from './store_order_list/vuex_order_list'
import ModuleArticle from './store_Article/vuex_article'
import ModuleDashboardTransport from './store_dashboard_transport/vuex_dashboard_transport'
import ModuleWithdrawAffiliate from './store_withdraw_affiliate/vuex_withdraw_affiliate'
import ModuleLiveStream from './store_live_stream/vuex_live_stream_api'
import ModuleMember from './store_member/vuex_member'

Vue.use(Vuex)

export default new Vuex.Store({
  namespaced: true,
  getters: {
  },
  state: {
  },
  mutations: {
  },
  actions: {
  },
  modules: {
    ModuleGlobal,
    ModuleHompage,
    ModuleProduct,
    ModuleShop,
    ModuleCart,
    ModuleManageShop,
    ModuleUser,
    ModuleOrder,
    ModuleBusiness,
    ModuleRegister,
    ModuleInventory,
    ModuleProductNode,
    ModuleFavoriteProduct,
    ModuleAdminManage,
    ModuleDepartment,
    ModuleCurier,
    ModuleTacking,
    ModuleRegisMorpromt,
    ModuleNotification,
    ModulePartner,
    ModuleSettingQuotation,
    ModuleSettingTier,
    ModuleReviewBuyer,
    ModuleETax,
    ModuleMyCouponsPoints,
    ModuleAdminPanit,
    ModuleApprove,
    ModuleDashBoard,
    ModuleShippingReport,
    ModuleDashBoardForAdmin,
    ModuleSaleOrder,
    ModuleDashBoardSaleOrder,
    ModuleManagePoint,
    ModuleDigitalSignature,
    ModuleManageCoupon,
    ModuleManageFlashSale,
    ModuleAffiliate,
    ModuleDashboardAffiliateBuyer,
    ModuleDashboardAffiliateShop,
    ModuleOrderList,
    ModuleArticle,
    ModuleDashboardTransport,
    ModuleWithdrawAffiliate,
    ModuleLiveStream,
    ModuleMember
  }
})
