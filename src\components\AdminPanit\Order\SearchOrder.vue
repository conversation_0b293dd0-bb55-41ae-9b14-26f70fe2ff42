<template>
<v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
    <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">ระบุรายการสั่งซื้อ</v-card-title>
    <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> ระบุรายการสั่งซื้อ</v-card-title>
    <v-row dense>
        <v-col cols="10" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-row dense>
            <v-col cols="9">
            <v-text-field v-model="search" placeholder="ค้นหารายการสั่งซื้อ" @keyup.enter="FindData" outlined rounded dense hide-details>
            </v-text-field></v-col>
            <v-col cols="3">
            <v-btn class="white--text" rounded color="teal lighten-1" @click="FindData"><v-icon left>mdi-magnify</v-icon>ค้นหา</v-btn></v-col>
            </v-row>
        </v-col>
        <v-col cols="12" md="12"></v-col>
        <v-col cols="12" md="12">
            <v-data-table
                :headers="headers"
                :items="data"
                :search="search"
                style="width:100%; white-space: nowrap;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}">
                <template v-slot:no-data>
                  <div style="text-align: left; padding: 16px;">ไม่มีรายการสั่งซื้อ</div>
                </template>
                <template v-slot:[`item.buyer_name`]="{ item }">
                    <span class="nameBuyer" style="white-space: nowrap;">{{ item.buyer_name }}</span>
                </template>
                <template v-slot:[`item.order_number`]="{ item }">
                    <span style="white-space: nowrap;">{{ item.order_number }}</span>
                </template>
                <template v-slot:[`item.username_oneid`]="{ item }">
                    <span v-if="item.username_oneid" style="white-space: nowrap;">{{ item.username_oneid }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.first_name_en`]="{ item }">
                    <span v-if="item.first_name_en" style="white-space: nowrap;">{{ item.first_name_en }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.last_name_en`]="{ item }">
                    <span v-if="item.last_name_en" style="white-space: nowrap;">{{ item.last_name_en }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.pr_document_id`]="{ item }">
                    <span v-if="item.pr_document_id" style="white-space: nowrap;">{{ item.pr_document_id }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.po_document_id`]="{ item }">
                    <span v-if="item.po_document_id" style="white-space: nowrap;">{{ item.po_document_id }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.ref_callback_so_id`]="{ item }">
                    <span v-if="item.ref_callback_so_id" style="white-space: nowrap;">{{ item.ref_callback_so_id }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.data_response_pr`]="{ item }">
                    <v-btn v-if="item.data_response_pr" color="#27AB9C" outlined @click="OpenDialogShowData('data response pr', item.data_response_pr)">ดู response pr</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.send_pr_timestamp`]="{ item }">
                    <span v-if="item.send_pr_timestamp" color="#27AB9C" >{{new Date(item.send_pr_timestamp).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.data_send_pr`]="{ item }">
                    <v-btn v-if="item.data_send_pr" color="#27AB9C" outlined @click="OpenDialogShowData('data send pr', item.data_send_pr)">ดู data send pr</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.data_send_pdf_pr`]="{ item }">
                    <v-btn v-if="item.data_send_pdf_pr" color="#27AB9C" outlined @click="OpenDialogShowData('data send pdf pr', item.data_send_pdf_pr)">ดู data send pdf pr</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.data_response_pdf_pr`]="{ item }">
                    <span v-if="item.data_response_pdf_pr" color="#27AB9C" > {{ item.data_response_pdf_pr }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.send_pdf_pr_timestamp`]="{ item }">
                    <span v-if="item.send_pdf_pr_timestamp" color="#27AB9C" > {{ item.send_pdf_pr_timestamp }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_personal`]="{ item }">
                    <v-btn v-if="item.json_personal !== '[]'" color="#27AB9C" outlined @click="OpenDialogShowData('json personal', item.json_personal)">ดู json personal</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_buyer`]="{ item }">
                    <v-btn v-if="item.json_buyer !== '[]'" color="#27AB9C" outlined @click="OpenDialogShowData('json buyer', item.json_buyer)">ดู json buyer</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.data_send_so`]="{ item }">
                    <v-btn v-if="item.data_send_so" color="#27AB9C" outlined @click="OpenDialogShowData('data send so', item.data_send_so)">ดู data send so</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.data_response_so`]="{ item }">
                    <span v-if="item.data_response_so" color="#27AB9C" > {{ item.data_response_so }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.send_so_timestamp`]="{ item }">
                    <span v-if="item.send_so_timestamp" color="#27AB9C" > {{ item.send_so_timestamp }}</span>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_mobilyst_req`]="{ item }">
                    <v-btn v-if="item.json_mobilyst_req" color="#27AB9C" outlined @click="OpenDialogShowData('mobilyst req', item.json_mobilyst_req)">ดู mobilyst req</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_mobilyst_res`]="{ item }">
                    <v-btn v-if="item.json_mobilyst_res" color="#27AB9C" outlined @click="OpenDialogShowData('mobilyst res', item.json_mobilyst_res)">ดู mobilyst res</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_refshare_req`]="{ item }">
                    <v-btn v-if="item.json_refshare_req" color="#27AB9C" outlined @click="OpenDialogShowData('refshare req', item.json_refshare_req)">ดู refshare req</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_refshare_res`]="{ item }">
                    <v-btn v-if="item.json_refshare_res" color="#27AB9C" outlined @click="OpenDialogShowData('json refshare res', item.json_etax_req)">ดู refshare res</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_etax_req`]="{ item }">
                    <v-btn v-if="item.json_etax_req" color="#27AB9C" outlined @click="OpenDialogShowData('json etax req', item.json_etax_req)">ดู etax req</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.json_etax_res`]="{ item }">
                    <v-btn v-if="item.json_etax_res" color="#27AB9C" outlined @click="OpenDialogShowData('json etax res', item.json_etax_res)">ดู etax res</v-btn>
                    <span v-else>-</span>
                </template>
                <template v-slot:[`item.created_at`]="{ item }">
                    <span style="white-space: nowrap;">{{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                </template>
                <template v-slot:[`item.manage`]="{ item }">
                    <v-btn v-if="btn_manage ||  item.manage" plain @click="DetailSearchOrder(item.order_number)"><v-icon color="#27AB9C">mdi-dots-vertical</v-icon></v-btn>
                    <DetailSearchOrder ref="DetailSearchOrder" />
                </template>
            </v-data-table>
        </v-col>
    </v-row>
    </v-card>
    <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
        <v-card>
            <v-card-title class="text-h5 grey lighten-2">
              {{ dataHeader }}
              <v-btn text color="#b1afae" style="margin-left: auto" @click="handleClick()"><v-icon>mdi-content-copy</v-icon>คัดลอก</v-btn>
              <v-tooltip
                v-model="showText"
                top
              >
                <template v-slot:activator="{ on, attrs }">
                  <p
                    icon
                    v-bind="attrs"
                    v-on="on"
                  >
                  </p>
                </template>
                <span>คัดลอกสำเร็จ</span>
              </v-tooltip>
            </v-card-title>
            <v-card-text class="pt-4">
              <pre style="white-space: wrap;" v-html="dataToShow"></pre>
            </v-card-text>
            <v-divider></v-divider>
            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                text
                @click="modalShowData = false"
              >
                ปิด
              </v-btn>
            </v-card-actions>
        </v-card>
      </v-dialog>
  </v-container>
</template>
<script>
import { mapGetters, mapActions } from 'vuex'
export default {
  components: {
    DetailSearchOrder: () => import(/* webpackPrefetch: true */ '@/components/AdminPanit/Order/DetailSearchOrder')
  },
  data () {
    return {
      search: '',
      data: [],
      findData: [],
      modalShowData: false,
      modalShowDetailData: false,
      dataToShow: '',
      dataHeader: '',
      btn_manage: true,
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      OrderNumberID: '',
      isSuperAdmin: null,
      showText: false,
      headers: [
        { text: 'เลขรายการสั่งซื้อ', value: 'order_number', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อผู้สั่งซื้อ', value: 'buyer_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รหัสร้านค้า', value: 'seller_shop_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'username_oneid', value: 'username_oneid', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'first_name_en', value: 'first_name_en', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'last_name_en', value: 'last_name_en', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'pr_document_id', value: 'pr_document_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'po_document_id', value: 'po_document_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ref_callback_so_id', value: 'ref_callback_so_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_send_pr', value: 'data_send_pr', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_response_pr', value: 'data_response_pr', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'send_pr_timestamp', value: 'send_pr_timestamp', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_send_pdf_pr', value: 'data_send_pdf_pr', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_response_pdf_pr', value: 'data_response_pdf_pr', width: '250', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'send_pdf_pr_timestamp', value: 'send_pdf_pr_timestamp', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_send_so', value: 'data_send_so', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_response_so', value: 'data_response_so', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'send_so_timestamp', value: 'send_so_timestamp', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_personal', value: 'json_personal', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_buyer', value: 'json_buyer', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_mobilyst_req', value: 'json_mobilyst_req', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_mobilyst_res', value: 'json_mobilyst_res', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_refshare_req', value: 'json_refshare_req', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_refshare_res', value: 'json_refshare_res', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_etax_req', value: 'json_etax_req', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_etax_res', value: 'json_etax_res', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'created_by', value: 'created_by', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'created_at', value: 'created_at', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'manage', filterable: false, sortable: false, class: 'backgroundTable fontTable--text', fixed: true, right: true }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    ...mapGetters(['stateFindOrderList'])
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SearchOrderMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'SearchOrder')
        this.$router.push({ path: '/SearchOrder' }).catch(() => {})
      }
    }
  },
  methods: {
    ...mapActions(['actionsFindOrderList']),
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async FindData () {
      // console.log(this.search)
      this.$store.commit('openLoader')
      await this.actionsFindOrderList(this.search)
      this.data = this.$store.state.ModuleOrderList.stateFindOrderList.data
      this.$store.commit('closeLoader')
      // console.log(this.data)
    },
    async FindDataID () {
      // console.log(this.search)
      this.$store.commit('openLoader')
      await this.actionsFindOrderList(this.OrderNumberID)
      this.findData = this.$store.state.ModuleOrderList.stateFindOrderList.data
      this.$store.commit('closeLoader')
      // console.log(this.data)
    },
    OpenDialogShowData (type, data) {
      this.dataToShow = ''
      this.dataHeader = ''
      if (type === 'data send pr') {
        this.dataHeader = 'Data Send PR'
        this.dataToShow = data
      } else if (type === 'data response pr') {
        this.dataHeader = 'Data Response PR'
        this.dataToShow = data
      } else if (type === 'data send pdf pr') {
        this.dataHeader = 'Data Send PDF PR'
        this.dataToShow = data
      } else if (type === 'json personal') {
        this.dataHeader = 'json Personal'
        this.dataToShow = data
      } else if (type === 'json buyer') {
        this.dataHeader = 'json Buyer'
        this.dataToShow = data
      } else if (type === 'data send so') {
        this.dataHeader = 'Data Send So'
        this.dataToShow = data
      } else if (type === 'mobilyst req') {
        this.dataHeader = 'Mobilyst Req'
        this.dataToShow = data
      } else if (type === 'refshare res') {
        this.dataHeader = 'Refshare Res'
        this.dataToShow = data
      } else if (type === 'json refshare res') {
        this.dataHeader = 'Json Refshare Res'
        this.dataToShow = data
      } else if (type === 'json etax req') {
        this.dataHeader = 'Json Etax Req'
        this.dataToShow = data
      } else {
        this.dataHeader = 'Json Etax Res'
        this.dataToShow = data
      }
      this.modalShowData = true
    },
    async DetailSearchOrder (id) {
      this.OrderNumberID = id
      await this.FindDataID()
      // console.log('id', this.OrderNumberID)
      await this.$refs.DetailSearchOrder.open(this.findData)
    },
    handleClick () {
      this.copyText()
      this.showText = !this.showText
    },
    copyText () {
      const text = this.dataToShow
      if (text) {
        navigator.clipboard.writeText(text).then(() => {
          // console.log('Copy Text Success:', text)
        }).catch(err => {
          console.error('Copy Text Fail:', err)
        })
      }
    }
  }
}
</script>
<style lang="scss" scoped>
::v-deep table {
    tbody {
    tr {
        td:nth-child(29) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(29) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        }
    }
    }
}
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
font-size: 0.62rem;
}
</style>
