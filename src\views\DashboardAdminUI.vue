<template>
   <v-app class="backgroundPage" height="100%">
    <div  class="my-4">
      <filters />
    </div>
  <!-- <div class="my-4">
    <filters />
    <v-row>
      <v-col cols="12" md="6" :class="IpadSize ? 'mb-0 pb-0' : 'pa-2'">
        <summaryTransaction/>
        <listTop/>
      </v-col>
      <v-col cols="12" md="6" :class="IpadSize ? 'pa-3 mt-0 pt-0' : 'pa-2'">
        <div v-if="isload">
        <comparedChart/>
        </div>
        <div v-else >
        <div class="loading">
    <div class="loading-1"></div>
    <div class="loading-2"></div>
    <div class="loading-3"></div>
    <div class="loading-4"></div>
  </div>
  </div>
      </v-col>
    </v-row>
  </div> -->
</v-app>
</template>
<script>
// import FrequencyUI from '@/components/Dashboard/frequencyTable'
import dataTest from '@/components/library/makedata.json'
export default {
  data () {
    return {
      freq: false,
      isload: false,
      dataFreq: [],
      dataSum: [],
      headers2: [],
      SETT: [],
      toDay: new Date().toISOString().slice(0, 10),
      // Day: `${new Date().toISOString().slice(0, 7)}-01`,
      Day: `${new Date().toISOString().slice(0, 10)}`,
      month: `${new Date().toISOString().slice(0, 4)}`,
      dateStart: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dateEnd: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      numChange: '1',
      Response: dataTest,
      dataAdded: '',
      dateExport: {
        user: [],
        seller: []
      }
    }
  },
  components: {
    // comparedChart: () => import('@/components/AdminManage/Company/DashboardAdmin/comparedChart'),
    // listTop: () => import('@/components/AdminManage/Company/DashboardAdmin/listTop'),
    // summaryTransaction: () => import('@/components/AdminManage/Company/DashboardAdmin/summaryTransaction'),
    filters: () => import('@/components/AdminManage/Company/DashboardAdmin/filterInfo')
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    this.$EventBus.$on('age-changed', (data) => {
      // console.log('Data--', data)
      this.dataAdded = data
      this.ComparedChart()
      // this.$EventBus.$off('age-changed')
    })
    // window.addEventListener('storage', function (event) {
    //   if (event.key === 'oneData' && !event.newValue) {
    //     window.location.assign('/')
    //   }
    // })
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.init()
  },
  destroyed () {
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardAdminMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardAdmin' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    Sums () {
      if (Object.values(this.dataSum).length !== 0) {
        return this.dataSum
      } else {
        return ''
      }
    }
  },
  methods: {
    async ComparedChart () {
      // console.log('dataForm')
      var totalAll = []
      var activeAll = []
      var newAll = []
      var dateTime = []
      var totalAll2 = []
      var activeAll2 = []
      var newAll2 = []
      var dateTime2 = []
      await this.$store.dispatch('actionsDashboardAdminAll', this.dataAdded)
      const { data = {} } = await this.$store.state.ModuleAdminManage.stateDashboardAdminAll
      // var listUser = data.compared_user
      // var listSeller = data.compared_seller
      // const groupByCategory = list.groupByToMap(product => {
      //   return product.data
      // })
      // console.log('groupByCategory', groupByCategory)
      for (const i in data.compared_user) {
        if (data.compared_user[i].data.total) {
          totalAll[i] = await data.compared_user[i].data.total
        }
        if (data.compared_user[i].data.active) {
          activeAll[i] = await data.compared_user[i].data.active
        }
        if (data.compared_user[i].data.new) {
          newAll[i] = await data.compared_user[i].data.new
        }
        dateTime.push(new Date(data.compared_user[i].month).toLocaleDateString('th-TH', { month: 'long' }))
      }
      for (const i in data.compared_seller) {
        if (data.compared_seller[i].data.total) {
          totalAll2[i] = await data.compared_seller[i].data.total
        }
        if (data.compared_seller[i].data.active) {
          activeAll2[i] = await data.compared_seller[i].data.active
          // console.log('compared_seller', activeAll2)
        }
        if (data.compared_seller[i].data.new) {
          newAll2[i] = await data.compared_seller[i].data.new
        }
        dateTime2.push(new Date(data.compared_seller[i].month).toLocaleDateString('th-TH', { month: 'long' }))
      }
      this.$store.state.ModuleAdminManage.stateDateExport = await {
        user: dateTime,
        seller: dateTime2
      }
      // console.log('stateDateExport', dateTime, dateTime2)
      this.$store.state.ModuleAdminManage.dashboardChart.comparedUser = await [
        {
          name: 'total',
          data: totalAll
        },
        {
          name: 'active',
          data: activeAll
        },
        {
          name: 'new',
          data: newAll
        }
      ]
      this.$store.state.ModuleAdminManage.dashboardChart.dateUser = await {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        title: {
          text: 'Compared User By month',
          align: 'left',
          margin: this.MobileSize ? 60 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: dateTime
        },
        yaxis: {
          title: {
            text: ''
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val
            }
          }
        }
      }
      this.$store.state.ModuleAdminManage.dashboardChart.comparedSeller = await [
        {
          name: 'total',
          data: totalAll2
        },
        {
          name: 'active',
          data: activeAll2
        },
        {
          name: 'new',
          data: newAll2
        }
      ]
      this.$store.state.ModuleAdminManage.dashboardChart.dateSeller = await {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        title: {
          text: 'Compared Market By month',
          align: 'left',
          margin: this.MobileSize ? 60 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          categories: dateTime2
        },
        yaxis: {
          title: {
            text: ''
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val
            }
          }
        }
      }
      this.$store.state.ModuleAdminManage.dashboardSummary = await {
        transaction: data.success_transaction,
        total: data.seller_total,
        active: data.seller_active,
        new: data.seller_new,
        totalUser: data.user_total,
        activeUser: data.user_active,
        newUser: data.user_new
      }
      this.$store.state.ModuleAdminManage.dashboardListTop = await { order: data.top_order, values: data.top_values }
    },
    async init () {
      // await this.$store.commit('openLoader')
      const dataSent = await {
        start_date: this.toDay,
        end_date: this.toDay,
        year: this.month
      }
      var totalAll = []
      var activeAll = []
      var newAll = []
      var dateTime = []
      var totalAll2 = []
      var activeAll2 = []
      var newAll2 = []
      var dateTime2 = []
      await this.$store.dispatch('actionsDashboardAdminAll', dataSent)
      const { data = {} } = await this.$store.state.ModuleAdminManage.stateDashboardAdminAll
      // var listUser = data.compared_user.sort((a, b) => {
      //   return new Date(`${a.year}-${a.month}`) - new Date(`${b.year}-${b.month}`)
      // })
      // var listUser = data.compared_user
      // var listSeller = data.compared_seller
      // console.log('listUser', listUser, listSeller)
      // const groupByCategory = list.groupByToMap(product => {
      //   return product.data
      // })
      // console.log('groupByCategory', groupByCategory)
      for (const i in data.compared_user) {
        if (data.compared_user[i].data.total) {
          totalAll[i] = await data.compared_user[i].data.total
        }
        if (data.compared_user[i].data.active) {
          activeAll[i] = await data.compared_user[i].data.active
        }
        if (data.compared_user[i].data.new) {
          newAll[i] = await data.compared_user[i].data.new
        }
        dateTime.push(new Date(data.compared_user[i].month).toLocaleDateString('th-TH', { month: 'long' }))
      }
      for (const i in data.compared_seller) {
        if (data.compared_seller[i].data.total) {
          totalAll2[i] = await data.compared_seller[i].data.total
        }
        if (data.compared_seller[i].data.active) {
          activeAll2[i] = await data.compared_seller[i].data.active
          // console.log('compared_seller', activeAll2)
        }
        if (data.compared_seller[i].data.new) {
          newAll2[i] = await data.compared_seller[i].data.new
        }
        dateTime2.push(new Date(data.compared_seller[i].month).toLocaleDateString('th-TH', { month: 'long' }))
      }
      this.$store.state.ModuleAdminManage.dashboardChart.comparedUser = await [
        {
          name: 'total',
          data: totalAll
        },
        {
          name: 'active',
          data: activeAll
        },
        {
          name: 'new',
          data: newAll
        }
      ]
      this.$store.state.ModuleAdminManage.dashboardChart.dateUser = await {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        title: {
          text: 'Compared User By month',
          align: 'left',
          margin: this.MobileSize ? 60 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          tickPlacement: 'on',
          labels: {
            formatter: function (value) {
              if (value !== undefined) {
                return value
              } else {
                return ''
              }
            }
          },
          categories: dateTime
        },
        yaxis: {
          title: {
            text: ''
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val
            }
          }
        }
      }
      this.$store.state.ModuleAdminManage.dashboardChart.comparedSeller = await [
        {
          name: 'total',
          data: totalAll2
        },
        {
          name: 'active',
          data: activeAll2
        },
        {
          name: 'new',
          data: newAll2
        }
      ]
      this.$store.state.ModuleAdminManage.dashboardChart.dateSeller = await {
        chart: {
          type: 'bar',
          height: 350
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '55%',
            endingShape: 'rounded'
          }
        },
        dataLabels: {
          enabled: false
        },
        title: {
          text: 'Compared Market By month',
          align: 'left',
          margin: this.MobileSize ? 60 : 0,
          style: {
            fontSize: this.MobileSize ? '14px' : '16px',
            fontWeight: '600',
            color: '#333333'
          }
        },
        stroke: {
          show: true,
          width: 2,
          colors: ['transparent']
        },
        xaxis: {
          tickPlacement: 'on',
          labels: {
            formatter: function (value) {
              if (value !== undefined) {
                return value
              } else {
                return ''
              }
            }
          },
          categories: dateTime2
        },
        yaxis: {
          title: {
            text: ''
          }
        },
        fill: {
          opacity: 1
        },
        tooltip: {
          y: {
            formatter: function (val) {
              return val
            }
          }
        }
      }
      this.$store.state.ModuleAdminManage.dashboardSummary = await {
        transaction: data.success_transaction,
        total: data.seller_total,
        active: data.seller_active,
        new: data.seller_new,
        totalUser: data.user_total,
        activeUser: data.user_active,
        newUser: data.user_new
      }
      this.$store.state.ModuleAdminManage.headerDBadmin.seller = await dateTime2
      this.$store.state.ModuleAdminManage.headerDBadmin.user = await dateTime
      this.$store.state.ModuleAdminManage.dashboardListTop = await { order: data.top_order, values: data.top_values }
      setTimeout(async () => {
        await this.$store.commit('closeLoader')
        this.isload = await true
      }, 1000)
    }
  }
}
</script>
<style lang="scss" scoped>
.loading {
  position: fixed;
  float: left;
  height: 120px;
  padding: 0px;
  width: 160px;
  margin-top: -50px;
  margin-left: -70px;
  border-left:1px solid #fff;
  border-bottom:1px solid #fff;
  padding:10px;
  box-sizing:border-box;
}
@keyframes loading {
  0% { background-color: #cd0a00; }

  30% { background-color: #fa8a00; }
  50% { height: 100px; margin-top: 0px; }
  80% { background-color: #91d700;  }
  100% { background-color: #cd0a00; }
}
/*@-moz-keyframes loading {
  50% { height: 100px; margin-top: 0px; }
}
@-o-keyframes loading {
  50% { height: 100px; margin-top: 0px; }
}
@keyframes loading {
  50% { height: 100px; margin-top: 0px; }
}*/
@mixin inner() {
  height: 10px;
  width: 30px;
  background-color: #fff;
  display: inline-block;
  margin-top: 90px;
  -webkit-animation: loading 2.5s infinite;
  -moz-animation: loading 2.5s infinite;
  -o-animation: loading 2.5s infinite;
  animation: loading 2.5s infinite;
  border-top-left-radius: 2px;
  border-top-right-radius: 2px;
}
@mixin loading() {
  @for $i from 1 through 10 {
    .loading-#{$i} { @include inner(); -webkit-animation-delay: $i/4+s; animation-delay: $i/4+s; }
  }
}
.loading { @include loading(); }
@media only screen and (max-width: 650px) {
}
</style>
