<template>
  <v-container grid-list-xl>
    <!-- Approved Dialog -->
    <v-dialog v-model="ApprovedDialog" persistent width="464">
      <v-card>
        <v-toolbar
          color="#BDE7D9"
          dense
        >
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 16px; font-weight: 700;">อนุมัติคำสั่งซื้อ</span>
          </v-row>
          <v-btn fab x-small @click="ApprovedDialog = !ApprovedDialog" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" md="11" sm="11" class="mt-8 mb-8 ml-4">
            <v-row justify="center">
              <span style="font-weight: 400; font-size: 14px; color: #333333;">คุณต้องการที่จะอนุมัติคำสั่งซื้อเลขที่</span>
              <span style="font-weight: 400; font-size: 14px; color: #333333;">"<b>{{ orderDetail.payment_transaction }}</b>" ใช่ หรือ ไม่</span>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" sm="12">
            <v-row justify="center" align-content="center">
              <v-btn outlined color="#27AB9C" @click="ApprovedDialog = !ApprovedDialog" class="mr-4">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark class="px-5" @click="approved()">ตกลง</v-btn>
            </v-row>
          </v-col>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Not Approved Dialog -->
    <v-dialog v-model="NotApprovedDialog" persistent width="464">
      <v-card>
        <v-toolbar
          color="#BDE7D9"
          dense
        >
          <v-row justify="center" align-content="center">
            <span style="color: #27AB9C; font-size: 16px; font-weight: 700;">ไม่อนุมัติคำสั่งซื้อ</span>
          </v-row>
          <v-btn fab x-small @click="NotApprovedDialog = !NotApprovedDialog" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-col cols="12" md="11" sm="11" class="mt-8 mb-8 ml-4">
            <v-row justify="center">
              <span style="font-weight: 400; font-size: 14px; color: #333333;">คุณต้องการที่จะไม่อนุมัติคำสั่งซื้อเลขที่</span>
              <span style="font-weight: 400; font-size: 14px; color: #333333;">"<b>{{ orderDetail.payment_transaction }}</b>" ใช่ หรือ ไม่</span>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" sm="12">
            <v-row justify="center" align-content="center">
              <v-btn outlined color="#27AB9C" @click="NotApprovedDialog = !NotApprovedDialog" class="mr-4">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark class="px-5" @click="Notapproved()">ตกลง</v-btn>
            </v-row>
          </v-col>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-overlay :value="overlay">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay>
    <v-row justify="center" class="my-4">
      <h2 style="font-weight: 700; font-size: 24px;"> สถานะการอนุมัติการซื้อสินค้า </h2>
    </v-row>
    <v-row no-gutters class="mx-4">
      <!-- ข้อมูล Header -->
      <v-col cols="12">
        <span style="font-size: 16px; line-height: 24px;">รหัสการสั่งซื้อ : <span style="font-weight: bold;">{{ orderDetail.payment_transaction }}</span></span>
        <span style="color: #636363; font-size: 16px;" class="px-3">|</span>
        <v-chip v-if="orderDetail.transaction_status === 'Pending'" small color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
        <v-chip v-else-if="orderDetail.transaction_status === 'Approve'" color="#ECF8EA" small text-color="#1AB759">อนุมัติ</v-chip>
        <v-chip v-else-if="orderDetail.transaction_status === 'Cancel'" color="#FBE5E4" small text-color="#D1392B">ยกเลิกคำสั่งซื้อ</v-chip>
      </v-col>
      <v-col cols="12">
        <span style="font-size: 16px; line-height: 24px;">ผู้ขออนุมัติ : <span style="font-weight: bold;">{{ orderDetail.buyer_name }}</span></span>
      </v-col>
      <v-col cols="12">
        <span style="font-size: 16px; line-height: 24px;">วันที่ขออนุมัติ : <span style="font-weight: bold;">{{new Date(orderDetail.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span></span>
      </v-col>
      <!-- ที่อยู่ในการจัดส่ง & รายการสินค้า -->
      <v-col cols="12" class="mt-12">
        <v-card outlined>
          <v-col cols="12" class="pl-5 pt-4 pb-4">
            <span style="font-weight: bold; font-size: 24px; color: #333333; line-height: 32px;">ที่อยู่ในการจัดส่งสินค้า</span>
          </v-col>
          <v-col cols="12" class="pl-5 pt-0">
            <span style="font-size: 16px; line-height: 24px; color: #000000;">{{ orderDetail.address_data }}</span>
          </v-col>
          <v-col cols="12" class="pl-5 pt-4 pb-4">
            <span style="font-weight: bold; font-size: 24px; color: #333333; line-height: 32px;">รายการสั่งซื้อสินค้า</span>
          </v-col>
          <v-col cols="12" class="pl-5 pt-0">
            <v-container grid-list-xs>
              <a-table bordered v-for="(item, index) in orderDetail.data_list" :key="index" :data-source="item.product_list" :rowKey="record => record.sku" :columns="headers">
                <template slot="title">
                  <p class="text-left">ผู้ขาย:<b> {{item.shop_name}}</b></p>
                </template>
                <template slot="productdetails" slot-scope="text, record">
                  <v-row>
                    <v-col cols="12" md="4" class="pr-0 py-1">
                      <v-img :src="record.product_image.url" class="imageshow"/>
                    </v-col>
                    <v-col cols="12" md="8">
                      <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                    </v-col>
                  </v-row>
                </template>
                <template slot="price" slot-scope="text, record">
                  <v-col cols="12">
                    <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </v-col>
                </template>
                <template slot="net_price" slot-scope="text, record">
                  <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                </template>
              </a-table>
            </v-container>
          </v-col>
          <v-col cols="12" md="12" class="mt-1 ml-4">
            <v-container grid-list-xs>
              <v-row>
                <v-col cols="12" md="10">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>ส่วนลด :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>ภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>ราคารวมภาษีมูลค่าเพิ่ม :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>ค่าจัดส่ง :</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span style="font-size: 20px; color: #333333; font-weight: bold;">ราคารวมทั้งหมด :</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" md="2">
                  <v-row dense class="pr-4">
                    <v-col cols="12" class="text-right">
                      <span>{{ Number(orderDetail.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>{{ Number(orderDetail.total_price_discount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>{{ Number(orderDetail.total_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>{{ Number(orderDetail.total_price_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span>{{ Number(orderDetail.total_shipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                    <v-col cols="12" class="text-right">
                      <span style="font-size: 20px; color: #333333; font-weight: bold;">{{ Number(orderDetail.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </v-col>
        </v-card>
      </v-col>
      <!-- ปุ่ม Approve & สถานะการอนุมัติ -->
      <v-row justify="end" class="mt-4 mb-4" v-if="orderDetail.status_approve === 'Pending'">
        <v-col cols="12" align="right">
          <v-btn color="#27AB9C" outlined class="mr-4" @click="NotApprovedDialog = !NotApprovedDialog">ไม่อนุมัติ</v-btn>
          <v-btn color="#27AB9C" dark class="px-7" @click="ApprovedDialog = !ApprovedDialog">อนุมัติ</v-btn>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col cols="12" class="mt-4">
            <h2 style="font-weight: 700;">สถานะผู้อนุมัติ</h2>
        </v-col>
        <v-col cols="12" class="mt-4">
            <h4 style="font-weight: 700;">ลำดับการอนุมัติ</h4>
        </v-col>
        <v-col cols="12" align="left">
          <v-timeline dense>
            <v-timeline-item v-for="(item,index) in orderDetail.approver_list" :key="index" fill-dot class="white--text mb-12" color="#27AB9C" small>
              <template v-slot:icon>
                <span>{{ index+1 }}</span>
              </template>
              <v-row no-gutters>
                <v-col cols="12">
                  <span style="color: #27AB9C;"><b>ผู้อนุมัติ</b></span>
                </v-col>
                <v-col cols="12">
                  <span style="color: black;">สถานะ :</span>
                  <v-chip v-if="item.status === 'pending'" class="ma-2" color="#FCF0DA" small text-color="#E9A016">รออนุมัติ</v-chip>
                  <v-chip v-else-if="item.status === 'approve'" class="ma-2" color="#ECF8EA" small text-color="#1AB759">อนุมัติ</v-chip>
                  <v-chip v-else-if="item.status === 'cancel'" class="ma-2" color="#FBE5E4" small text-color="#D1392B">ยกเลิก</v-chip>
                </v-col>
                <v-col cols="12">
                  <span style="color: black;">ผู้อนุมัติ : {{ item.approver_name }}({{ item.email }})</span>
                </v-col>
                <v-col v-if="item.time_approve === '-'" cols="12" class="mt-3">
                  <span style="color: black;">วันที่อนุมัติ : {{ item.time_approve }}</span>
                </v-col>
                <v-col v-else cols="12" class="mt-3">
                  <span style="color: black;">วันที่อนุมัติ : {{new Date(item.time_approve).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                </v-col>
              </v-row>
            </v-timeline-item>
          </v-timeline>
        </v-col>
      </v-row>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data () {
    return {
      overlay: false,
      orderDetail: [],
      paymentNumber: '',
      ApprovedDialog: false,
      NotApprovedDialog: false
    }
  },
  created () {
    this.paymentNumber = JSON.parse(Decode.decode(localStorage.getItem('orderNumber')))
    this.getOederDetailApprove()
  },
  methods: {
    async getOederDetailApprove () {
      this.overlay = true
      await this.$store.dispatch('actionOrderDetail', this.paymentNumber)
      var res = await this.$store.state.ModuleOrder.stateDetailOrder
      if (res.message === 'Get detail order success') {
        this.orderDetail = res.data
        this.overlay = false
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    async approved () {
      var data = {
        payment_transaction_number: this.orderDetail.payment_transaction,
        status: 'approve'
      }
      await this.$store.dispatch('actionApprove', data)
      var res = await this.$store.state.ModuleOrder.stateApprove
      if (res.message === 'Approves Order success') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'อนุมัติคำสั่งซื้อสำเร็จ'
        })
        this.ApprovedDialog = false
        this.$router.push({ path: '/approved' }).catch(() => {})
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: res.message
        })
        this.ApprovedDialog = false
      }
    },
    async Notapproved () {
      var data = {
        payment_transaction_number: this.orderDetail.payment_transaction,
        status: 'cancel'
      }
      await this.$store.dispatch('actionApprove', data)
      var res = await this.$store.state.ModuleOrder.stateApprove
      if (res.message === 'Approves Order success') {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ไม่อนุมัติคำสั่งซื้อสำเร็จ'
        })
        this.NotApprovedDialog = false
        this.$router.push({ path: '/approved' }).catch(() => {})
      } else {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: res.message
        })
        this.NotApprovedDialog = false
      }
    }
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          align: 'center',
          width: '20%'
        }
      ]
      return headers
    }
  },
  watch: {
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    }
  }
}
</script>
