<template>
  <v-container class="backgroundSeller">
    <v-row v-if="!MobileSize" class="mx-0">
      <!-- Website -->
      <v-col cols="12" md="3" sm="4" xs="12" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1400px" style="border-radius: 8px;" data-v-step="0">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-home</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; font-size: 18px !important; line-height: 30px;">กลับไปหน้าการซื้อขายผ</v-list-item-title>
              </v-list-item-content>
            </v-list-item>

            <!-- จัดการร้านค้า -->
            <div v-if="this.itemsShop[0].items.length !== 0">
              <v-list-group v-for="item in itemsShop" :key="item.key" v-model="item.active"
                no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                    {{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>
                <v-list-item-group v-model="SelectManageShop"
                  :mandatory="defaultSelect === 15 ? false : defaultSelect === 16 ? false : defaultSelect === 17 ? false : defaultSelect === 18 ? false : true">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- คำสั่งซื้อสินค้า -->
            <div v-if="this.itemsPurchaseOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsPurchaseOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectPurchaseOrder"
                  :mandatory="defaultSelect === 15">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- จัดการผู้ใช้งาน -->
            <div v-if="this.itemsManageUser[0].items.length !== 0">
              <v-list-group v-for="item in itemsManageUser" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectManageUser"
                  :mandatory="defaultSelect === 15">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- บริษัทคู้ค้า -->
            <div v-if="this.itemsCompany[0].items.length !== 0">
              <v-list-group v-for="item in itemsCompany" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectCompany"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : defaultSelect === 17 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ใบสั่งขาย -->
            <div v-if="this.itemsSalesOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSalesOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSalesOrder"
                  :mandatory="defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ตั้งค่าระบบใบสั่งขาย -->
            <div v-if="this.itemsSettingOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSettingOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSettingOrder"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- โปรโม -->
            <div v-if="this.itemsPromotion[0].items.length !== 0">
              <v-list-group v-for="item in itemsPromotion" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 18px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSalesOrder"
                  :mandatory="defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : defaultSelect === 21 ? true : defaultSelect === 22 ? true : defaultSelect === 23 ? true : defaultSelect === 24 ? true : defaultSelect === 25 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 18px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD PRO -->
      <v-col cols="12" md="3" sm="4" xs="12" v-else-if="!MobileSize && !IpadSize && IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1400" style="border-radius: 8px;">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-home</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; font-size: 16px !important; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>

            <!-- จัดการร้านค้า -->
            <div v-if="this.itemsShop[0].items.length !== 0">
              <v-list-group v-for="item in itemsShop" :key="item.key" v-model="item.active"
                no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                    {{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>
                <v-list-item-group v-model="SelectManageShop"
                  :mandatory="defaultSelect === 15 ? false : defaultSelect === 16 ? false : defaultSelect === 17 ? false : defaultSelect === 18 ? false : true">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 16px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- คำสั่งซื้อสินค้า -->
            <div v-if="this.itemsPurchaseOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsPurchaseOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectPurchaseOrder"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 16px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- จัดการผู้ใช้งาน -->
            <div v-if="this.itemsManageUser[0].items.length !== 0">
              <v-list-group v-for="item in itemsManageUser" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectManageUser"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 16px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- บริษัทคู้ค้า -->
            <div v-if="this.itemsCompany[0].items.length !== 0">
              <v-list-group v-for="item in itemsCompany" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectCompany"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : defaultSelect === 17 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 16px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ใบสั่งขาย -->
            <div v-if="this.itemsSalesOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSalesOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSalesOrder"
                  :mandatory="defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 16px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ตั้งค่าระบบใบสั่งขาย -->
            <div v-if="this.itemsSettingOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSettingOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSettingOrder"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 16px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>

          </v-list>
        </v-card>
      </v-col>
      <!-- IPAD -->
      <v-col cols="12" md="3" sm="4" xs="12"  v-else-if="!MobileSize && IpadSize && !IpadProSize">
        <v-card class="mt-6" max-height="100%" height="1400px" style="border-radius: 8px;">
          <v-list nav>
            <v-list-item style="cursor: pointer;">
              <v-list-item-icon>
                <v-icon color="">mdi-home</v-icon>
              </v-list-item-icon>
              <v-list-item-content>
                <v-list-item-title @click="changePage('/')" style="white-space: normal; font-weight: bold; font-size: 16px !important; line-height: 30px;">กลับไปหน้าการซื้อขาย</v-list-item-title>
              </v-list-item-content>
            </v-list-item>

            <!-- จัดการร้านค้า -->
            <div v-if="this.itemsShop[0].items.length !== 0">
              <v-list-group v-for="item in itemsShop" :key="item.key" v-model="item.active"
                no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                    {{ item.title }}</v-list-item-title>
                  </v-list-item-content>
                </template>
                <v-list-item-group v-model="SelectManageShop"
                  :mandatory="defaultSelect === 15 ? false : defaultSelect === 16 ? false : defaultSelect === 17 ? false : defaultSelect === 18 ? false : true">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 14px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- คำสั่งซื้อสินค้า -->
            <div v-if="this.itemsPurchaseOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsPurchaseOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectPurchaseOrder"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 14px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- จัดการผู้ใช้งาน -->
            <div v-if="this.itemsManageUser[0].items.length !== 0">
              <v-list-group v-for="item in itemsManageUser" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectManageUser"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 14px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- บริษัทคู้ค้า -->
            <div v-if="this.itemsCompany[0].items.length !== 0">
              <v-list-group v-for="item in itemsCompany" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectCompany"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : defaultSelect === 17 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 14px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ใบสั่งขาย -->
            <div v-if="this.itemsSalesOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSalesOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSalesOrder"
                  :mandatory="defaultSelect === 17 ? true : defaultSelect === 18 ? true : defaultSelect === 19 ? true : defaultSelect === 20 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 14px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>
            <!-- ตั้งค่าระบบใบสั่งขาย -->
            <div v-if="this.itemsSettingOrder[0].items.length !== 0">
              <v-list-group v-for="item in itemsSettingOrder" :key="item.key" v-show="checkCreateShop === false"
                v-model="item.active" :prepend-icon="item.action" no-action color="#27AB9C">
                <template v-slot:activator>
                  <v-list-item-content>
                    <v-list-item-title
                      style="font-weight: bold; font-size: 16px !important; line-height: 30px;">
                      {{ item.title }}
                    </v-list-item-title>
                  </v-list-item-content>
                </template>

                <v-list-item-group v-model="SelectSettingOrder"
                  :mandatory="defaultSelect === 15 ? true : defaultSelect === 16 ? true : false">
                  <v-list-item v-for="child in item.items" :key="child.key" color="#27AB9C" dense class="pl-16"
                    style="font-size: 14px; line-height: 26px;">
                    <v-list-item-content @click="Gopage(child)">
                      <v-list-item-action>{{ child.title }}</v-list-item-action>
                    </v-list-item-content>
                  </v-list-item>
                </v-list-item-group>
              </v-list-group>
            </div>

          </v-list>
        </v-card>
      </v-col>
      <!-- </v-navigation-drawer> -->
      <v-col cols="12" md="9" sm="8" xs="12" class="pl-0 pr-0 mt-3">
        <v-main style="padding: 0px;">
          <v-container>
            <v-row dense class="mt-0" v-if="checkCreateShop">
              <v-col cols="12" md="12" class="mt-0 pt-0">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.png" max-height="468"
                  max-width="100%" contain></v-img>
              </v-col>
            </v-row>
            <div
              v-if="this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditPicturesShop' || this.$router.currentRoute.name === 'EditShop'"
              max-height="100%" height="100%" width="100%">
              <router-view></router-view>
            </div>
            <v-card elevation="0" v-else max-height="100%" height="100%" width="100%" style="overflow-y: hidden;" data-v-step="1">
              <router-view></router-view>
            </v-card>
          </v-container>
        </v-main>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      isJV: '',
      pathData: '',
      shopname: '',
      roleUser: '',
      checkCreateShop: false,
      activeMenu: true,
      dataDetail: '',

      defaultSelect: 0,
      SelectManageShop: 0,
      SelectPurchaseOrder: -1,
      SelectManageUser: -1,
      SelectCompany: -1,
      SelectSalesOrder: -1,
      SelectSettingOrder: -1,

      itemsShop: [
        {
          key: 1,
          action: 'mdi-storefront',
          active: true,
          title: 'จัดการร้านค้า',
          items: []
        }
      ],
      itemsCreateShop: [
        {
          key: 1,
          action: 'mdi-storefront',
          active: true,
          title: 'จัดการร้านค้า',
          items: [
            { key: 1, title: 'สร้างร้านค้า', path: 'createShop' }
          ]
        }
      ],
      items: [
        {
          key: 1,
          action: 'mdi-storefront',
          active: true,
          title: 'จัดการร้านค้า',
          items: []
        }
      ],
      itemsCompany: [
        {
          key: 2,
          action: 'mdi-domain',
          active: false,
          title: 'บริษัทคู่ค้า',
          items: []
        }
      ],
      itemsSalesOrder: [
        {
          key: 3,
          action: 'mdi-briefcase-account',
          active: false,
          title: 'ใบสั่งขาย',
          items: []
        }
      ],
      itemsPurchaseOrder: [
        {
          key: 4,
          // action: 'mdi-briefcase-account',
          active: false,
          title: 'คำสั่งซื้อสินค้า',
          items: []
        }
      ],
      itemsManageUser: [
        {
          key: 5,
          // action: 'mdi-briefcase-account',
          active: false,
          title: 'จัดการผู้ใช้งาน',
          items: []
        }
      ],
      itemsSettingOrder: [
        {
          key: 6,
          // action: 'mdi-briefcase-account',
          active: false,
          title: 'ตั้งค่าระบบใบสั่งขาย',
          items: []
        }
      ],
      itemsPromotion: [
        {
          key: 4,
          action: 'mdi-ticket-percent',
          active: false,
          title: 'จัดการโปรโมชัน',
          items: []
        }
      ]
    }
  },
  async created () {
    var path
    this.dataRole()
    this.$EventBus.$emit('closeModalLogin')
    this.$EventBus.$emit('closeModalRegister')
    this.$EventBus.$emit('closeModalSuccess')
    this.$EventBus.$emit('resetSearch')
    this.$EventBus.$on('AuthorityUsers', this.AuthorityUsers)
    if (localStorage.getItem('pathShopSale') !== null) {
      path = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale')))
      this.pathData = path
    } else {
      path = '/'
      this.pathData = path
    }
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      if (localStorage.getItem('list_shop_detail') !== null) {
        this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
      }
      this.$EventBus.$emit('getPath')
      this.$EventBus.$emit('CheckFooter')
      this.$EventBus.$on('changeNav', this.SelectPath)
      this.$EventBus.$on('ChangeActiveMenu', this.ChangeActiveMenu)
      await this.checkJVShop()
      await this.checkPath2()
    }
    window.addEventListener('scroll', this.closeModal)
  },
  watch: {
    MobileSize (val) {
      if (this.$router.currentRoute.name === 'sellerUI') {
        var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
        if (val === true) {
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
        } else {
          this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
        }
      } else if (this.$router.currentRoute.name === 'POSellerDetailUI' || this.$router.currentRoute.name === 'posellerDetailMobileUI') {
        var orderData = JSON.parse(Decode.decode(localStorage.getItem('orderNumberSeller')))
        if (val === true) {
          this.$router.push({ path: `/posellerDetailMobile?orderNumber=${orderData.order_number}&tranNumber=${orderData.payment_transaction_number}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/POSellerDetail?orderNumber=${orderData.order_number}&tranNumber=${orderData.payment_transaction_number}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'createShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/createShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/createShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DownloadFiles' || this.$router.currentRoute.name === 'DownloadFilesMobile') {
        if (val === true) {
          this.$router.push({ path: '/DownloadFilesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DownloadFiles' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'Curier' || this.$router.currentRoute.name === 'CurierMobile') {
        if (val === true) {
          this.$router.push({ path: '/CurierMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Curier' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'Tackingorder' || this.$router.currentRoute.name === 'TackingorderMobile') {
        if (val === true) {
          this.$router.push({ path: '/TackingorderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/Tackingorder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'designShopMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/designShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/designShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'inventoryUI' || this.$router.currentRoute.name === 'inventoryMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/inventoryMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/inventory' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'posellerUI' || this.$router.currentRoute.name === 'posellerMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/posellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/poseller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'RevenueUI' || this.$router.currentRoute.name === 'RevenueMobileUI') {
        if (val === true) {
          this.$router.push({ path: '/revenueMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/revenue' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequest' || this.$router.currentRoute.name === 'SettingPartnerRequestMobile') {
        if (val === true) {
          this.$router.push({ path: '/SettingPartnerRequestMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/SettingPartnerRequest' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'SettingTier' || this.$router.currentRoute.name === 'SettingTierMobile') {
        if (val === true) {
          this.$router.push({ path: '/SettingTierMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/SettingTier' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QuotationSettingSeller' || this.$router.currentRoute.name === 'QuotationSettingSellerMobile') {
        if (val === true) {
          this.$router.push({ path: '/QuotationSettingSellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/QuotationSettingSeller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QuotationAll' || this.$router.currentRoute.name === 'QuotationDetail') {
        if (this.$router.currentRoute.name === 'QuotationAll') {
          if (val === true) {
            this.$router.push({ path: '/QuotationAllMobile' }).catch(() => { })
          } else {
            this.$router.push({ path: '/QuotationAll' }).catch(() => { })
          }
        } else {
          if (val === true) {
            this.$router.push({ path: '/QuotationDetailMobile' }).catch(() => { })
          } else {
            this.$router.push({ path: '/QuotationDetail' }).catch(() => { })
          }
        }
      } else if (this.$router.currentRoute.name === 'EtaxCredentail' || this.$router.currentRoute.name === 'EtaxCredentailMobile') {
        if (val === true) {
          this.$router.push({ path: '/EtaxCredentailMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/EtaxCredentail' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'QuotationSetting' || this.$router.currentRoute.name === 'QuotationSettingMobile') {
        if (val === true) {
          this.$router.push({ path: '/QuotationSettingMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/QuotationSetting' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'MerchantShop' || this.$router.currentRoute.name === 'MerchantShopMobile') {
        if (val === true) {
          this.$router.push({ path: '/MerchantShopMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/MerchantShop' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ManagaCupon' || this.$router.currentRoute.name === 'ManagaCuponMobile') {
        if (val === true) {
          this.$router.push({ path: '/ManagaCuponMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ManagaCupon' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerlistCreditOrder' || this.$router.currentRoute.name === 'sellerlistCreditOrderMobile') {
        if (val === true) {
          this.$router.push({ path: '/sellerlistCreditOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerlistCreditOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'creditTerm' || this.$router.currentRoute.name === 'creditTermMobile') {
        var number = JSON.parse(localStorage.getItem('creditTermOrdernumber'))
        if (val === true) {
          this.$router.push({ path: `/sellerlistCreditTermMobile?order_number=${number}` }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerlistCreditTerm' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerInvoicePDF' || this.$router.currentRoute.name === 'sellerInvoicePDFMobile') {
        number = JSON.parse(localStorage.getItem('creditTermOrdernumber'))
        if (val === true) {
          this.$router.push({ path: `/sellerInvoicePDFMobile?order_number=${number}` }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerInvoicePDF' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'manageComments' || this.$router.currentRoute.name === 'manageCommentsMobile') {
        if (val === true) {
          this.$router.push({ path: '/manageCommentsMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/manageComments' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'specialPrice' || this.$router.currentRoute.name === 'specialPriceMobile') {
        if (val === true) {
          this.$router.push({ path: '/specialPriceMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/specialPrice' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'partnerSeller' || this.$router.currentRoute.name === 'partnerSellerMobile') {
        if (val === true) {
          this.$router.push({ path: '/partnerSellerMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/partnerSeller' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listShopPosition' || this.$router.currentRoute.name === 'listShopPositionMobile') {
        if (val === true) {
          this.$router.push({ path: '/listShopPositionMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listShopPosition' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listShopUser' || this.$router.currentRoute.name === 'listShopUserMobile') {
        if (val === true) {
          this.$router.push({ path: '/listShopUserMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listShopUser' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ListRequestNewTerm' || this.$router.currentRoute.name === 'ListRequestNewTermMobile') {
        if (val === true) {
          this.$router.push({ path: '/ListRequestNewTermMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ListRequestNewTerm' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'dashboardMobile' || this.$router.currentRoute.name === 'dashboardUI') {
        if (val === true) {
          this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboard' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'sellerdashboardMobile' || this.$router.currentRoute.name === 'sellerdashboard') {
        if (val === true) {
          this.$router.push({ path: '/sellerdashboardMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/sellerdashboard' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrderMobile' || this.$router.currentRoute.name === 'DashboardSaleOrder') {
        if (val === true) {
          this.$router.push({ path: '/DashboardSaleOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/DashboardSaleOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'ShippingReportMobile' || this.$router.currentRoute.name === 'ShippingReport') {
        if (val === true) {
          this.$router.push({ path: '/ShippingReportMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/ShippingReport' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listSales' || this.$router.currentRoute.name === 'listSalesMobile') {
        if (val === true) {
          this.$router.push({ path: '/listSalesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listSales' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listCustomerGroupSalesMobile' || this.$router.currentRoute.name === 'listCustomerGroupSales') {
        if (val === true) {
          this.$router.push({ path: '/listCustomerGroupSalesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listCustomerGroupSales' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listCustomerSaleOrderMobile' || this.$router.currentRoute.name === 'listCustomerSaleOrder') {
        if (val === true) {
          this.$router.push({ path: '/listCustomerSaleOrderMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listCustomerSaleOrder' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listCustomerSalesMobile' || this.$router.currentRoute.name === 'listCustomerSales') {
        var dataOfSale = JSON.parse(Decode.decode(localStorage.getItem('Detail_sales')))
        if (val === true) {
          this.$router.push({ path: `/listCustomerSalesMobile?sale_code=${dataOfSale.sale_code}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/listCustomerSales?sale_code=${dataOfSale.sale_code}` }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'listQuotationSalesMobile' || this.$router.currentRoute.name === 'listQuotationSales') {
        if (val === true) {
          this.$router.push({ path: '/listQuotationSalesMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/listQuotationSales' }).catch(() => {})
        }
      } else if (this.$router.currentRoute.name === 'DetailQuotationSales' || this.$router.currentRoute.name === 'DetailQuotationSalesMobile') {
        var QTDetail = JSON.parse(Decode.decode(localStorage.getItem('detailItemQUSale')))
        var sellerShopId = ''
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (dataRole.role !== 'sale_order') {
          sellerShopId = JSON.parse(localStorage.getItem('shopDetail'))
        } else {
          sellerShopId = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
        }
        if (val === true) {
          this.$router.push({ path: `/DetailQuotationSalesMobile?QUNumber=${QTDetail.order_number}&id=${dataRole.role !== 'sale_order' ? sellerShopId.id : sellerShopId.seller_shop_id}` }).catch(() => {})
        } else {
          this.$router.push({ path: `/DetailQuotationSales?QUNumber=${QTDetail.order_number}&id=${dataRole.role !== 'sale_order' ? sellerShopId.id : sellerShopId.seller_shop_id}` }).catch(() => {})
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.checkJVShop()
    this.checkpath()
    // this.$tours['myTour.ShopPage'].start()
    this.$EventBus.$on('checkJVShop', this.checkJVShop)
    this.$EventBus.$on('checkpath', this.checkpath)
    this.$on('hook:beforeDestroy', () => {
      // this.$EventBus.$off('AuthorityUsers')
      this.$EventBus.$off('checkJVShop')
      this.$EventBus.$off('checkpath')
    })
  },
  methods: {
    dataRole () {
      var data = JSON.parse(localStorage.getItem('roleUser'))
      this.roleUser = data
    },
    closeModal () {
      this.$EventBus.$emit('closeModalLogin')
      this.$EventBus.$emit('closeModalRegister')
      this.$EventBus.$emit('closeModalSuccess')
      this.$EventBus.$emit('closeModalCartNoLogin')
      this.$EventBus.$emit('closeModalCart')
      this.$EventBus.$emit('OpenNotification')
      this.$EventBus.$emit('OpenChatAll')
      this.$EventBus.$emit('Open_No_Notification')
    },
    changePage (val) {
      this.$EventBus.$emit('resetAdminShop')
      if (this.roleUser.role === 'sale_order' || this.roleUser.role === 'sale_order_no_JV') {
        this.$router.push({ path: this.pathData.path }).catch(() => {})
      } else {
        this.$router.push({ path: `${val}` }).catch(() => {})
      }
    },
    async checkJVShop () {
      this.isJV = ''
      var shopDetail = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: shopDetail,
        role: 'seller'
      }
      await this.$store.dispatch('actionDetailShop', data)
      var response = await this.$store.state.ModuleShop.stateDatailShop
      if (response.result === 'SUCCESS') {
        if (response.data[0].is_JV === 'yes') {
          this.isJV = 'yes'
        } else {
          this.isJV = 'no'
        }
      }
      await this.AuthorityUsers()
    },

    async checkPath2 () {
      this.AuthorityUsers()
      for (let i = 0; i < this.items[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.items[0].items[i].path) {
          this.SelectManageShop = i
        }
      }
      for (let i = 0; i < this.itemsCompany[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsCompany[0].items[i].path) {
          this.SelectCompany = i
        }
      }
      for (let i = 0; i < this.itemsSalesOrder[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsSalesOrder[0].items[i].path) {
          this.SelectSalesOrder = i
        }
      }
      for (let i = 0; i < this.itemsPromotion[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsPromotion[0].items[i].path) {
          this.SelectProMo = i
        }
      }
    },
    AuthorityUsers () {
      this.dataDetail = []
      if (localStorage.getItem('list_shop_detail') !== null) {
        this.dataDetail = JSON.parse(Decode.decode(localStorage.getItem('list_shop_detail')))
        // console.log();
      } else {
        this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์การใช้งานภายในร้านนี้', showConfirmButton: false, timer: 2000 })
        this.$router.push({ path: '/' }).catch(() => {})
      }
      // console.log('123', this.dataDetail)
      var item1 = []
      var item2 = []
      var item3 = []
      var item4 = []
      if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'stepCreateShop') {
        item1 = this.itemsCreateShop
      } else {
        // item1.push({ key: 27, title: '', path: '' })
        if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1') {
          item1.push({ key: 25, title: 'แดชบอร์ดร้านค้า', path: 'sellerdashboard' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_product === '1') {
          item1.push({ key: 4, title: 'รายการสินค้า', path: 'seller' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
          item1.push({ key: 19, title: 'จัดการความคิดเห็น', path: 'manageComments' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 20, title: 'รายการเอกสารขอเป็นคู่ค้า', path: 'SettingPartnerRequest' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 21, title: 'ตั้งค่ากลุ่มคู่ค้า (Tier)', path: 'SettingTier' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          item1.push({ key: 23, title: 'e-Tax Credential', path: 'EtaxCredentail' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_dashboard === '1') {
          item1.push({ key: 25, title: 'แดชบอร์ดร้านค้า', path: 'sellerdashboard' })
        }
        if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
          // item1.push({ key: 26, title: 'ตั้งค่าใบเสนอราคา', path: 'QuotationSetting' })
        }
      }
      this.items[0].items = []
      this.items[0].items = item1
      this.itemsShop[0].items = []
      this.itemsShop[0].items = item1
      if (this.dataDetail.can_use_function_in_shop.manage_order === '1') {
        item2.push({ key: 28, title: 'ใบเสนอราคา', path: 'QuotationAll' })
      }
      this.itemsPurchaseOrder[0].items = []
      this.itemsPurchaseOrder[0].items = item2
      if (this.itemsPurchaseOrder[0].items.length === 0) {
        this.itemsPurchaseOrder[0].active = false
        this.items[0].active = true
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 37, title: 'แดชบอร์ดฝ่ายขาย', path: 'DashboardSaleOrder' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 29, title: 'รายชื่อลูกค้า', path: 'listCustomerSaleOrder' })
      }
      // if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
      //   item3.push({ key: 30, title: 'กลุ่มลูกค้า', path: 'listCustomerGroupSales' })
      // }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 31, title: 'ใบเสนอราคา', path: 'listQuotationSales' })
      }
      if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
        item3.push({ key: 32, title: 'รายการสั่งซื้อฝ่ายขาย', path: 'orderSales' })
      }
      // if (this.dataDetail.can_use_function_in_shop.sale_order_no_jv === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
      //   item3.push({ key: 33, title: 'รายชื่อฝ่ายขาย', path: 'listSales' })
      // }
      // if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
      //   item3.push({ key: 34, title: 'จัดการรูปแบบการอนุมัติฝ่ายขาย', path: 'ManageSalesApproval' })
      // }
      // if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
      //   item3.push({ key: 35, title: 'จัดการลำดับการอนุมัติฝ่ายขาย', path: 'manageSaleApprove' })
      // }
      // if (this.dataDetail.can_use_function_in_shop.manage_approve_order === '1' || this.dataDetail.can_use_function_in_shop.manage_sale_order === '1') {
      //   item3.push({ key: 36, title: 'รายการอนุมัติฝ่ายขาย', path: 'listApproveSales' })
      // }
      this.itemsSalesOrder[0].items = []
      this.itemsSalesOrder[0].items = item3
      if (this.itemsSalesOrder[0].items.length === 0) {
        this.itemsSalesOrder[0].active = false
        this.items[0].active = true
      }
      // if (this.dataDetail.can_use_function_in_shop.manage_product !== '1') {
      //   this.$router.push({ path: this.items[0].items[0].path }).catch(() => {})
      // }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item4.push({ key: 38, title: 'จัดการคูปอง', path: 'manageCoupon' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item4.push({ key: 39, title: 'จัดการแต้ม', path: 'setPoint' })
      }
      if (this.dataDetail.can_use_function_in_shop.manage_setting_shop === '1') {
        item4.push({ key: 40, title: 'รายชื่อลูกค้าสะสมแต้ม', path: 'allPointFromCostomer' })
      }
      this.itemsPromotion[0].items = []
      this.itemsPromotion[0].items = item4
      if (this.itemsPromotion[0].items.length === 0) {
        this.itemsPromotion[0].active = false
        this.items[0].active = true
      }
    },
    checkpath () {
      if (this.$router.currentRoute.name === 'sellerUI' || this.$router.currentRoute.name === 'manageproductUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'posellerUI' || this.$router.currentRoute.name === 'POSellerDetailUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ShippingReport') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'Curier') { // inventoryUI'
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'Tackingorder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'inventoryUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'returnUI' || this.$router.currentRoute.name === 'returndetailUI') { // supplier_seller
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'dashboardUI') { // promotion_seller
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'DownloadFiles') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'RevenueUI') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'SettingShop' || this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'partnerSeller' || this.$router.currentRoute.name === 'partnerSellerDetail') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'createShop' || this.$router.currentRoute.name === 'stepCreateShop') {
        this.itemsShop = this.itemsCreateShop
        this.checkCreateShop = true
      } else if (this.$router.currentRoute.name === 'createShopMobile' || this.$router.currentRoute.name === 'stepCreateShopMobile') {
        this.itemsShop = this.items
        this.checkCreateShop = true
      } else if (this.$router.currentRoute.name === 'QuotationSetting') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'partnerSeller') {
        this.itemsShop = this.items
        this.SelectCompany = 0
        this.defaultSelect = 15
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequest') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectCompany = 1
        this.defaultSelect = 16
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'SettingTier') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectCompany = 2
        this.defaultSelect = 17
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
      } else if (this.$router.currentRoute.name === 'sellerdashboard') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'QuotationSettingSeller') {
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'partnerSeller') {
        this.itemsShop = this.items
        this.Select = 0
        this.defaultSelect = 15
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'QuotationAll') {
        this.itemsShop = this.items
        this.SelectPurchaseOrder = 0
        this.defaultSelect = 15
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'listCustomerSaleOrder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectSalesOrder = 0
        this.defaultSelect = 17
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'listCustomerGroupSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectSalesOrder = 1
        this.defaultSelect = 18
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'listQuotationSales' || this.$router.currentRoute.name === 'DetailQuotationSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectSalesOrder = 2
        this.defaultSelect = 19
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'orderSales' || this.$router.currentRoute.name === 'DetailOrderSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectSalesOrder = 3
        this.defaultSelect = 20
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'listSales' || this.$router.currentRoute.name === 'listCustomerSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 6
        this.SelectSalesOrder = 4
        this.defaultSelect = 21
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'ManageSalesApproval' || this.$router.currentRoute.name === 'ManageSalesApprovalDetail') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectSettingOrder = 0
        this.defaultSelect = 15
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'manageSaleApprove') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.SelectSettingOrder = 1
        this.defaultSelect = 16
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'listApproveSales' || this.$router.currentRoute.name === 'DetailListApproveSales') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 9
        this.SelectSalesOrder = 7
        this.defaultSelect = 24
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 10
        this.SelectSalesOrder = 8
        this.defaultSelect = 25
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPromotion[0].active = false
      } else if (this.$router.currentRoute.name === 'manageCoupon') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 11
        this.SelectSalesOrder = 9
        this.SelectProMo = 0
        this.defaultSelect = 26
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
      } else if (this.$router.currentRoute.name === 'setPoint') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 12
        this.SelectSalesOrder = 10
        this.SelectProMo = 1
        this.defaultSelect = 27
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
      } else if (this.$router.currentRoute.name === 'allpointfromcostomer') {
        this.itemsShop = this.items
        this.checkCreateShop = false
        this.Select = 13
        this.SelectSalesOrder = 11
        this.SelectProMo = 2
        this.defaultSelect = 28
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
      }
    },
    async Gopage (val) {
      if (val.path !== 'seller') {
        this.$router.push(val.path).catch(() => {})
      } else {
        var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
        if (this.MobileSize === true) {
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
        } else {
          this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
        }
      }
    },
    ChangeActiveMenu (val) {
      this.activeMenu = val
    },
    SelectPath () {
      // console.log('----->.', this.items, this.$router.currentRoute.name)
      if (this.$router.currentRoute.name === 'sellerUI' || this.$router.currentRoute.name === 'manageproductUI') {
        // รายการสินค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 1
        this.SelectManageShop = 1
        this.checkCreateShop = false
        this.itemsShop[0].active = true
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('ManageShop')
      } else if (this.$router.currentRoute.name === 'posellerUI' || this.$router.currentRoute.name === 'POSellerDetailUI') {
        // รายการสั่งซื้อสินค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 1
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'ShippingReport' || this.$router.currentRoute.name === 'ShippingReportMobile') {
        // รายงานการสั่งซื้อ
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 2
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'Reportseller' || this.$router.currentRoute.name === 'ReportsellerMobile') {
        // Reportseller
        this.defaultSelect = 3
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.itemsShop = this.items
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'designShopUI' || this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
        // จัดการร้านค้า
        this.itemsShop = this.items
        if (this.$router.currentRoute.name === 'EditShop' || this.$router.currentRoute.name === 'EditPicturesShop') {
          this.defaultSelect = this.items[0].items.findIndex((result) => {
            return '/' + result.path === '/designShop'
          })
        } else {
          this.defaultSelect = this.items[0].items.findIndex((result) => {
            return '/' + result.path === this.$router.currentRoute.path
          })
        }
        this.defaultSelect = 4
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'dashboardUI') {
        // แดชบอร์ด
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 5
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'listShopPosition') {
        // จัดการตำแหน่งและสิทธิ์การใช้งาน
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.checkCreateShop = false
        this.SelectManageUser = 0
        this.defaultSelect = 15
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = true
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('ManageUser')
      } else if (this.$router.currentRoute.name === 'listShopUser') {
        // จัดการผู้ใช้งาน
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 7
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'manageComments') {
        // จัดการความคิดเห็น
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 2
        this.SelectManageShop = 2
        this.checkCreateShop = false
        this.itemsShop[0].active = true
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('ManageShop')
      } else if (this.$router.currentRoute.name === 'partnerSeller') {
        //  รายชื่อคู่ค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 15
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.ResetSelected('Company')
      } else if (this.$router.currentRoute.name === 'SettingPartnerRequest') {
        // รายการเอกสารขอเป็นคู่ค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 16
        this.SelectCompany = 1
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.ResetSelected('Company')
      } else if (this.$router.currentRoute.name === 'SettingTier') {
        // ตั้งค่ากลุ่มคู่ค้า (Tier)
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 17
        this.SelectCompany = 2
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = true
        this.itemsSalesOrder[0].active = false
        this.ResetSelected('Company')
      } else if (this.$router.currentRoute.name === 'EtaxCredentail') {
        // e-Tax Credential
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 11
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'sellerdashboard') {
        // แดชบอร์ดร้านค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 0
        this.SelectManageShop = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = true
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('ManageShop')
      } else if (this.$router.currentRoute.name === 'QuotationSetting') {
        // ตั้งค่าใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.defaultSelect = 13
        // this.Select = false
        this.checkCreateShop = false
      } else if (this.$router.currentRoute.name === 'QuotationSettingSeller') {
        // ตั้งค่าใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = 14
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.checkCreateShop = false
        // End of Menu Seller
      } else if (this.$router.currentRoute.name === 'QuotationAll') {
        // ใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectPurchaseOrder = 0
        this.defaultSelect = 15
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = true
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('PurchaseOrder')
        // End of menu บริษัท
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 0
        this.defaultSelect = 17
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.ResetSelected('SalesOrder')
      } else if (this.$router.currentRoute.name === 'listCustomerSaleOrder') {
        // รายชื่อลูกค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 1
        this.defaultSelect = 18
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.ResetSelected('SalesOrder')
      } else if (this.$router.currentRoute.name === 'listQuotationSales' || this.$router.currentRoute.name === 'DetailQuotationSales') {
        // ใบเสนอราคา
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 2
        this.defaultSelect = 19
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('SalesOrder')
      } else if (this.$router.currentRoute.name === 'orderSales' || this.$router.currentRoute.name === 'DetailOrderSales') {
        // รายการสั่งซื้อ Sales Order
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 3
        this.defaultSelect = 20
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = false
        this.ResetSelected('SalesOrder')
      } else if (this.$router.currentRoute.name === 'listCustomerGroupSales') {
        // console.log('listCustomerGroupSales')
        // กลุ่มลูกค้า
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        // this.defaultSelect = 18
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'listSales' || this.$router.currentRoute.name === 'listCustomerSales') {
        // รายชื่อ Sales
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 4
        this.defaultSelect = 21
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'ManageSalesApproval' || this.$router.currentRoute.name === 'ManageSalesApprovalDetail') {
        // จัดการรูปแบบการอนุมัติฝ่ายขาย
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSettingOrder = 0
        this.defaultSelect = 15
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = true
        this.ResetSelected('SettingOrder')
      } else if (this.$router.currentRoute.name === 'manageSaleApprove') {
        // รายชื่อ Sales
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSettingOrder = 1
        this.defaultSelect = 16
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPurchaseOrder[0].active = false
        this.itemsManageUser[0].active = false
        this.itemsSettingOrder[0].active = true
        this.ResetSelected('SettingOrder')
      } else if (this.$router.currentRoute.name === 'listApproveSales') {
        // รายการอนุมัติฝ่ายขาย
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 7
        this.Select = 9
        this.defaultSelect = 24
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'DashboardSaleOrder') {
        // รายการ Dashboard Sale Order
        this.itemsShop = this.items
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 8
        this.Select = 10
        this.defaultSelect = 25
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = true
      } else if (this.$router.currentRoute.name === 'manageCoupon') {
        // รายการ Dashboard Sale Order
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 9
        this.Select = 11
        this.defaultSelect = 26
        this.defaultSelect = 0
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
      } else if (this.$router.currentRoute.name === 'setPoint') {
        // รายการ Dashboard Sale Order
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 10
        this.Select = 12
        this.defaultSelect = 27
        this.defaultSelect = 1
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
      } else if (this.$router.currentRoute.name === 'allPointFromCostomer') {
        // รายการ Dashboard Sale Order
        this.defaultSelect = this.items[0].items.findIndex((result) => {
          return '/' + result.path === this.$router.currentRoute.path
        })
        this.SelectSalesOrder = 11
        this.Select = 13
        this.defaultSelect = 28
        this.defaultSelect = 2
        this.checkCreateShop = false
        this.itemsShop[0].active = false
        this.itemsCompany[0].active = false
        this.itemsSalesOrder[0].active = false
        this.itemsPromotion[0].active = true
      }
      // this.AuthorityUsers()
      for (let i = 0; i < this.itemsShop[0].items.length; i++) {
        if (this.$router.currentRoute.name === this.itemsShop[0].items[i].path) {
          this.defaultSelect = i
          this.Select = false
          this.SelectSalesOrder = false
        }
      }
    },
    ResetSelected (type) {
      if (type === 'ManageShop') {
        // this.SelectManageShop = -1
        this.SelectPurchaseOrder = -1
        this.SelectManageUser = -1
        this.SelectCompany = -1
        this.SelectSalesOrder = -1
        this.SelectSettingOrder = -1
      }
      if (type === 'PurchaseOrder') {
        this.SelectManageShop = -1
        // this.SelectPurchaseOrder = -1
        this.SelectManageUser = -1
        this.SelectCompany = -1
        this.SelectSalesOrder = -1
        this.SelectSettingOrder = -1
      }
      if (type === 'ManageUser') {
        this.SelectManageShop = -1
        this.SelectPurchaseOrder = -1
        // this.SelectManageUser = -1
        this.SelectCompany = -1
        this.SelectSalesOrder = -1
        this.SelectSettingOrder = -1
      }
      if (type === 'Company') {
        this.SelectManageShop = -1
        this.SelectPurchaseOrder = -1
        this.SelectManageUser = -1
        // this.SelectCompany = -1
        this.SelectSalesOrder = -1
        this.SelectSettingOrder = -1
      }
      if (type === 'SalesOrder') {
        this.SelectManageShop = -1
        this.SelectPurchaseOrder = -1
        this.SelectManageUser = -1
        this.SelectCompany = -1
        // this.SelectSalesOrder = -1
        this.SelectSettingOrder = -1
      }
      if (type === 'SettingOrder') {
        this.SelectManageShop = -1
        this.SelectPurchaseOrder = -1
        this.SelectManageUser = -1
        this.SelectCompany = -1
        this.SelectSalesOrder = -1
        // this.SelectSettingOrder = -1
      }
    }
  }
}
</script>

<style scoped>
.backgroundSeller {
  max-width: 100% !important;
  background: #F7FCFC;
}
.v-application ul, .v-application ol {
    padding: 0px 0px !important;
}
.v-application ol, .v-application ul {
    padding: 0px 0px !important;
}
</style>
