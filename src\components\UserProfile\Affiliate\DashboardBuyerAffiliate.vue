<template>
  <v-container width="100%" height="100%" style="background: #FFFFFF; border: 0px solid; border-radius: 8px;">
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0">
      <v-row no-gutters class="d-flex" align="center">
        <v-col :cols="MobileSize? '7':'3'">
          <v-card-title class="pl-0" style="font-weight: 700; font-size: 22px; line-height: 32px;" v-if="!MobileSize">{{ $t('DashboardAffiliate.titleWeb') }}</v-card-title>
          <v-card-title class="px-0" style="font-weight: 700; font-size: 18px; line-height: 32px;" v-else>
            <v-icon color="#1AB759" class="mr-2" @click="backToUsr()">mdi-chevron-left</v-icon>{{ $t('DashboardAffiliate.titleMobile') }}
          </v-card-title>
        </v-col>

        <v-col :cols="IpadProSize ? '12':'9'" align="end" v-if="!MobileSize && !IpadSize">
          <!-- แสดงผล -->
          <span style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.display') }} :</span>
          <v-menu offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
               <span style="font-size: 16px; font-weight: 400; color: #333333">{{selectedDropdown || $t('DashboardAffiliate.yearly')}}</span>
               <v-spacer></v-spacer>
               <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
          </template>
          <v-list>
            <v-list-item @click="onDropdownSelected($t('DashboardAffiliate.yearly'))">{{ $t('DashboardAffiliate.yearly') }}</v-list-item>
            <v-list-item @click="onDropdownSelected($t('DashboardAffiliate.monthly'))">{{ $t('DashboardAffiliate.monthly') }}</v-list-item>
            <v-list-item @click="onDropdownSelected($t('DashboardAffiliate.daily'))">{{ $t('DashboardAffiliate.daily') }}</v-list-item>
          </v-list>
          </v-menu>
          <!-- เมนูปี -->
          <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.year') }} :</span>
          <v-menu v-if="showYearDropdown" offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
               <span style="font-size: 16px; font-weight: 400; color: #333333">{{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}</span>
               <v-spacer></v-spacer>
               <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
          </template>
          <v-list>
            <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year.text)">{{ (year.text) + 543 }}</v-list-item>
          </v-list>
          </v-menu>

          <!-- เมนูรายเดือน -->
          <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.month') }} :</span>
          <v-menu v-if="showMonthDropdown" offset-y>
            <template v-slot:activator="{ on, attrs }">
              <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
               <span style="font-size: 16px; font-weight: 400; color: #333333">{{ selectedMonth || $t('DashboardAffiliate.titleSelectMonth') }}</span>
               <v-spacer></v-spacer>
               <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
              </v-btn>
          </template>
          <v-list>
            <!-- desktop -->
            <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelected(month)">{{ month.month }}</v-list-item>
          </v-list>
          </v-menu>

          <!-- เมนูรายวัน -->
            <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.day') }} :</span>
            <v-dialog v-if="showDatePicker" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
              <template v-slot:activator="{on, attrs}">
                <v-text-field
                v-model = "rangeDate"
                :placeholder="$t('DashboardAffiliate.formatDate')"
                dense
                rounded
                readonly
                style="border: 1px solid #EBEBEB; border-radius: 8px;"
                v-bind="attrs"
                v-on="on"
                class="d-inline-block ml-2 custom-text-field"
              >
              <v-spacer></v-spacer>
                <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                {{ rangeDate }}
              </v-text-field>
              </template>
              <v-date-picker
                style="font-size:29px !important; height: 480px !important"
                v-model="dates"
                scrollable
                reactive
                :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                range
                no-title
                full-width
                :min="minDate"
                :max="
                  new Date(
                    Date.now() - new Date().getTimezoneOffset() * 60000
                  )
                    .toISOString()
                    .substr(0, 10)
                "
                >
                <v-row>
                  <v-col align="end">
                    <v-btn text color="primary" @click="closeDialog(dates)">{{ $t('DashboardAffiliate.btnCancel') }}</v-btn>
                    <v-btn text color="primary" @click="saveDialog(dates)">{{ $t('DashboardAffiliate.btnConfirm') }}</v-btn>
                  </v-col>
                </v-row>
              </v-date-picker>
            </v-dialog>
        </v-col>
        <!-- IpadSize -->
         <v-col cols="12" align="end" v-else-if="IpadSize">
          <v-row>
            <v-col cols="12" class="pt-0">
              <span style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.display') }} :</span>
                <v-menu offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                    <span style="font-size: 16px; font-weight: 400; color: #333333">{{selectedDropdown || $t('DashboardAffiliate.yearly')}}</span>
                    <v-spacer></v-spacer>
                    <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                    </v-btn>
                </template>
                <v-list>
                  <v-list-item @click="onDropdownSelected($t('DashboardAffiliate.yearly'))">{{ $t('DashboardAffiliate.yearly') }}</v-list-item>
                  <v-list-item @click="onDropdownSelected($t('DashboardAffiliate.monthly'))">{{ $t('DashboardAffiliate.monthly') }}</v-list-item>
                  <v-list-item @click="onDropdownSelected($t('DashboardAffiliate.daily'))">{{ $t('DashboardAffiliate.daily') }}</v-list-item>
                </v-list>
                </v-menu>
            </v-col>

            <v-col v-if="!showDatePicker" cols="12" class="pt-0">
              <span v-if="showYearDropdown" style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.year') }} :</span>
              <v-menu v-if="showYearDropdown" offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">{{ selectedYear === null ? 'เลือกปี' : selectedYear + 543 }}</span>
                  <v-spacer></v-spacer>
                  <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelected(year.text)">{{ (year.text) + 543 }}</v-list-item>
              </v-list>
              </v-menu>
            </v-col>

            <v-col v-if="!showDatePicker" cols="12" class="pt-0">
              <span v-if="showMonthDropdown" style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.month') }} :</span>
              <v-menu v-if="showMonthDropdown" offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn color="#FFF" class="ml-2 mr-2" style="border: 1px solid #EBEBEB; border-radius: 8px;" elevation="0" width="130px" dark v-bind="attrs" v-on="on">
                  <span style="font-size: 16px; font-weight: 400; color: #333333">{{ selectedMonth || $t('DashboardAffiliate.titleSelectMonth') }}</span>
                  <v-spacer></v-spacer>
                  <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                  </v-btn>
              </template>
              <v-list>
                <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelected(month)">{{ month.month }}</v-list-item>
              </v-list>
              </v-menu>
            </v-col>

            <v-col cols="12" class="pt-0">
              <span v-if="showDatePicker" style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.day') }} :</span>
              <v-dialog v-if="showDatePicker" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
                <template v-slot:activator="{on, attrs}">
                    <v-text-field
                      v-model = "rangeDate"
                      :placeholder="$t('DashboardAffiliate.formatDate')"
                      dense
                      rounded
                      readonly
                      style="border: 1px solid #EBEBEB; border-radius: 8px;"
                      v-bind="attrs"
                      v-on="on"
                      class="d-inline-block ml-2 custom-text-field"
                    >
                    <v-spacer></v-spacer>
                      <v-icon slot="append" class="mt-2" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                      {{ rangeDate }}
                    </v-text-field>
                </template>
                <v-date-picker
                  style="font-size:29px !important; height: 480px !important"
                  v-model="dates"
                  scrollable
                  reactive
                  :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                  range
                  no-title
                  full-width
                  :min="minDate"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                  >
                  <v-row>
                    <v-col align="end">
                      <v-btn text color="primary" @click="closeDialog(dates)">{{ $t('DashboardAffiliate.btnCancel') }}</v-btn>
                      <v-btn text color="primary" @click="saveDialog(dates)">{{ $t('DashboardAffiliate.btnConfirm') }}</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
         </v-col>
        <!-- MobileSize -->
        <v-col align="end" v-else-if="MobileSize">
          <v-btn outlined style="height: 32px; border-radius: 40px; border-color: rgb(39, 171, 156);" elevation="0" @click="openDialogMobile = true">
            <v-icon style="color:#27AB9C; font-size: 16px">
              mdi-filter-outline
            </v-icon>
            <span style="color: rgb(39, 171, 156)">
              {{ $t('DashboardAffiliate.filter') }}
            </span>
          </v-btn>
          <v-dialog v-model="openDialogMobile" content-class="elevation-0" width="300px" persistent>
            <v-card class="rounded-xl" style="height: 300px;" align="center">
              <v-toolbar align="center" color="#FFF" dark dense elevation="0">
                <span
                  class="flex text-center ml-5"
                  style="font-weight: bold; font-size: 16px;">
                  <font color="#333">
                    {{ $t('DashboardAffiliate.filter') }}
                  </font>
                </span>
                <v-btn icon dark @click="closeDialogMobile()">
                  <v-icon color="#333">
                    mdi-close
                  </v-icon>
                </v-btn>
              </v-toolbar>
              <v-card class="mt-6 elevation-0" style="height: 46px; width: 90%; background-color: #F3F5F7; border-radius: 12px;">
                <v-row>
                  <v-col cols="4" class="pr-0 pt-1">
                    <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onDropdownSelected($t('DashboardAffiliate.yearly'))">
                      <span style="font-size: 16px; color: #27AB9C;">{{ $t('DashboardAffiliate.yearly') }}</span>
                    </v-card>
                  </v-col>
                  <v-col cols="4" class="pl-0 pr-0 pt-1">
                    <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onDropdownSelected($t('DashboardAffiliate.monthly'))">
                      <span style="font-size: 16px; color: #27AB9C">{{ $t('DashboardAffiliate.monthly') }}</span>
                    </v-card>
                  </v-col>
                  <v-col cols="4" class="pl-0 pt-1">
                    <v-card style="border-radius: 12px;" width="90%" height="38px" elevation="0" class="align-content-center" @click="onDropdownSelected($t('DashboardAffiliate.daily'))">
                      <span style="font-size: 16px; color: #27AB9C">{{ $t('DashboardAffiliate.daily') }}</span>
                    </v-card>
                  </v-col>
                </v-row>
                <!-- year -->
                <v-row style="height: 120px;" v-if="showYearDropdown">
                  <v-row v-if="showYearDropdown" no-gutters>
                    <v-col  cols="3" class="d-flex align-start mt-2 pl-9">
                    <span style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.year') }} :</span>
                    </v-col>
                    <v-col v-if="showYearDropdown" cols="5" class="pl-3">
                      <v-menu  offset-y>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn outlined v-bind="attrs" v-on="on" width="200px" class="rounded-xl" style="border: 1px solid grey">
                            <span style="font-size: 16px; font-weight: 400; color: #333333">{{ selectedYearMobile === null ? 'เลือกปี' : selectedYearMobile + 543 }}</span>
                            <v-spacer></v-spacer>
                            <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                          </v-btn>
                        </template>
                      <v-list style="max-height: 200px; overflow-y: auto;">
                        <v-list-item v-for="(year, index) in years" :key="index" @click="onYearSelectedMobile(year.text)">{{ (year.text) + 543 }}</v-list-item>
                      </v-list>
                      </v-menu>
                    </v-col>
                  </v-row>
                  <v-row v-if="showMonthDropdown" no-gutters>
                      <!-- month -->
                    <v-col cols="3" class="d-flex align-start mt-2 pl-3">
                      <span style="font-size: 16px; font-weight: 500;">{{ $t('DashboardAffiliate.month') }} :</span>
                    </v-col>
                    <v-col v-if="showMonthDropdown" cols="5" class="pl-3">
                      <v-menu offset-y>
                        <template v-slot:activator="{ on, attrs }">
                          <v-btn outlined v-bind="attrs" v-on="on" width="200px" class="rounded-xl" style="caret-color:rgb(39, 171, 156); border: 1px solid grey">
                            <span style="font-size: 16px; font-weight: 400; color: #333333">{{ selectedMonthMobile || $t('DashboardAffiliate.titleSelectMonth') }}</span>
                            <v-spacer></v-spacer>
                            <v-icon class="mdi-rotate-180 mb-2" color="#CCCCCC">mdi-apple-keyboard-control</v-icon>
                          </v-btn>
                        </template>
                        <v-list style="max-height: 200px; overflow-y: auto;">
                          <v-list-item v-for="(month, index) in months" :key="index" @click="onMonthSelectedMobile(month)">{{ month.month }}</v-list-item>
                        </v-list>
                    </v-menu>
                    </v-col>
                  </v-row>
                </v-row>
                 <!-- date -->
                  <v-row v-if="showDatePicker" justify="center" style="height: 120px;" no-gutters>
                    <v-col cols="2" class="pr-0 mt-6">
                      <span style="font-size: 16px;">{{ $t('DashboardAffiliate.day') }} :</span>
                    </v-col>
                    <v-col cols="10" class="mt-4">
                      <v-dialog v-if="showDatePicker" v-model="modalDateSelect" class="d-inline-block" persistent width="480px">
                      <template v-slot:activator="{on, attrs}">
                        <v-text-field
                        v-model = "rangeDate"
                        :placeholder="$t('DashboardAffiliate.formatDate')"
                        dense
                        rounded
                        readonly
                        outlined
                        style="border-radius: 8px;"
                        v-bind="attrs"
                        v-on="on"
                        class="d-inline-block custom-text-field my-input rounded-xl"
                      >
                      <v-spacer></v-spacer>
                        <v-icon slot="append" class="mt-1" :style="{ fontSize: '20px' }">mdi-calendar-month</v-icon>
                        {{ rangeDate }}
                      </v-text-field>
                      </template>
                      <v-date-picker
                        style="font-size:29px !important; height: 480px !important"
                        v-model="dates"
                        scrollable
                        reactive
                        :locale="$i18n.locale === 'th' ? 'th' : 'en'"
                        range
                        no-title
                        full-width
                        :min="minDate"
                        :max="
                          new Date(
                            Date.now() - new Date().getTimezoneOffset() * 60000
                          )
                            .toISOString()
                            .substr(0, 10)
                        "
                        >
                        <v-row>
                          <v-col align="end">
                            <v-btn text color="primary" @click="closeDialogDatesMobile(dates)">{{ $t('DashboardAffiliate.btnCancel') }}</v-btn>
                            <v-btn text color="primary" @click="saveDialogMobile(dates)">{{ $t('DashboardAffiliate.btnConfirm') }}</v-btn>
                          </v-col>
                        </v-row>
                      </v-date-picker>
                    </v-dialog>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col>
                      <v-btn @click="clearDialog" elevation="0" style="border-radius: 40px; width: 100px;">
                        <span style="font-size: 16px; color: #27AB9C;">{{ $t('DashboardAffiliate.clsValue') }}</span>
                      </v-btn>
                    </v-col>
                    <v-col>
                      <v-btn @click="submitFilter" style="border-radius: 40px; background: #27AB9C; width: 100px">
                        <span style="font-size: 16px; color: #FFFFFF">{{ $t('DashboardAffiliate.btnSave') }}</span>
                      </v-btn>
                    </v-col>
                  </v-row>
              </v-card>
            </v-card>
          </v-dialog>
        </v-col>

        <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.allLinks') && tableData.length !== 0" class="d-flex justify-end pt-2 pr-2">
          <v-btn @click="exportExcel()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-row>

        <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.commission') && tableDataCom.length !== 0" class="d-flex justify-end pt-2 pr-2">
          <v-btn @click="exportExcelCommission()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-row>

        <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.topClick') && topItem.length !== 0" class="d-flex justify-end pt-2 pr-2">
          <v-btn @click="exportExcelTop()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-row>

        <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.topSeller') && topSold.length !== 0" class="d-flex justify-end pt-2 pr-2">
          <v-btn @click="exportExcelSold()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-row>

        <!-- MobileSizePageType -->
        <v-row v-if="MobileSize" >
          <v-row justify="center" class="mt-3">
            <v-col cols="11" sm="6">
              <v-select item-text="name" item-value="id" :label="$t('DashboardAffiliate.selectShop')" dense outlined v-model="selectShopType" @change="handleSelectChange" :items="selectShop" hide-details style="border-radius: 80px;"></v-select>
            </v-col>
          </v-row>
          <v-row justify="center" class="mt-3">
            <v-col cols="11" sm="6">
              <v-select outlined v-model="selectedType" :items="selectPageType" dense class="subTitleText" height="22px" :menu-props="{ offsetY: true, offsetOverflowAuto: true }"></v-select>
            </v-col>
          </v-row>
        </v-row>
      </v-row>
    </v-card>
    <!-- กราฟแสดงรายได้ -->
    <v-card style="border-radius: 8px;" elevation="0" v-if="MobileSize && selectedType === $t('DashboardAffiliate.incomeInfo')">
        <v-btn outlined :class="{'grey-tabButton' : selectTab === $t('DashboardAffiliate.clickInfo'), 'subTitleTextTabButton' : selectTab === $t('DashboardAffiliate.incomeInfo')}" @click="selectTabClick($t('DashboardAffiliate.incomeInfo'))">
          <v-avatar rounded size="24">
            <v-img contain :src="passiveIncomeIconPath"></v-img>
          </v-avatar>
          <span :class="{'grey-tab ml-2' : selectTab === $t('DashboardAffiliate.clickInfo'), 'subTitleTextTab ml-2' : selectTab === $t('DashboardAffiliate.incomeInfo')}">{{ $t('DashboardAffiliate.incomeInfo') }}</span>
        </v-btn>
        <v-btn outlined :class="{'grey-tabButton' : selectTab === $t('DashboardAffiliate.incomeInfo'), 'subTitleTextTabButton' : selectTab === $t('DashboardAffiliate.clickInfo')}" @click="selectTabClick($t('DashboardAffiliate.clickInfo'))">
          <v-avatar rounded size="24">
            <v-img contain :src="clickIcon"></v-img>
          </v-avatar>
          <span :class="{'grey-tab ml-2' : selectTab === $t('DashboardAffiliate.incomeInfo'), 'subTitleTextTab ml-2' : selectTab === $t('DashboardAffiliate.clickInfo')}">{{ $t('DashboardAffiliate.clickInfo') }}</span>
        </v-btn>
      <!-- <ul class="nav nav-tabs mt-5 pl-0" style="display: flex; flex-wrap: nowrap;">
      </ul> -->
    </v-card>
    <v-card class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-if="MobileSize && selectedType === $t('DashboardAffiliate.incomeInfo')">
        <v-card outlined style="border-color: #dee2e6" elevation="0" v-if="selectTab === $t('DashboardAffiliate.incomeInfo')">
          <v-card-title>
            <v-row>
              <v-col cols="6">
                <v-avatar rounded size="20">
                  <v-img contain :src="statisticsIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 15px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.incomeGraph') }}</span>
              </v-col>
              <v-col cols="6" align="end">
                <v-avatar rounded size="27" class="mt-2">
                  <v-img contain :src="graphLineIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.theCommission') }}</span>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text><apexchart height="400" type="line" :options="chartOptions" :series="series"></apexchart></v-card-text>
        </v-card>
        <v-card outlined style="border-color: #dee2e6" elevation="0" v-if="selectTab === $t('DashboardAffiliate.clickInfo')">
          <v-card-title>
            <v-row>
              <v-col cols="6">
                <v-avatar rounded size="20">
                  <v-img contain :src="statisticsIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 15px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.clickGraph') }}</span>
              </v-col>
              <v-col cols="6" align="end">
                <v-avatar rounded size="27" class="mt-2">
                  <v-img contain :src="graphLineIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.clicking') }}</span>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text><apexchart height="400" type="line" :options="chartOptionsClick" :series="seriesClick"></apexchart></v-card-text>
        </v-card>
      </v-card>

      <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-if="MobileSize && selectedType === $t('DashboardAffiliate.incomeInfo')">
        <v-row>
            <!-- รายได้ -->
            <v-col cols="12" align="center" justify="center">
                <v-card style="border-radius: 8px;background: #F9FAFD;" elevation="0">
                    <v-row >
                        <v-col cols="12">
                            <v-avatar rounded size="80">
                                <v-img contain :src="moneyIconPath"></v-img>
                            </v-avatar>
                        </v-col>
                        <v-col cols="12" v-if="this.totalCommissions === null || this.totalCommissions === ''">
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">0.00</span>
                        </v-col>
                        <v-col cols="12" v-else>
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">{{ this.totalCommissions }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">{{ $t('DashboardAffiliate.totalCommission') }}<br/> ({{ $t('DashboardAffiliate.unit') }})</span>
                        </v-col>
                    </v-row>
                </v-card>
            </v-col>
            <!-- ยอดแชร์ -->
            <v-col cols="12" align="center" justify="center" class="mt-3">
                <v-card style="border-radius: 8px;background: #F9FAFD;" elevation="0">
                    <v-row >
                        <v-col cols="12" >
                            <v-avatar rounded size="80">
                                <v-img contain :src="clickIcon"></v-img>
                            </v-avatar>
                        </v-col>
                        <v-col cols="12" v-if="totalQuantity === 0">
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">0</span>
                        </v-col>
                        <v-col cols="12" v-else>
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">{{ parseInt(this.totalQuantity) }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">{{ $t('DashboardAffiliate.totalClicks') }} <br/> ({{ $t('DashboardAffiliate.unitClick') }})</span>
                        </v-col>
                    </v-row>
                </v-card>
            </v-col>
        </v-row>
      </v-card>
      <!-- ตารางข้อมูล link -->
      <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.allLinks')" class="pl-4 pt-3 pb-4">
        <span style="font-size: 14px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
          ({{ tableData.length }} {{ $t('DashboardAffiliate.unitOrder') }})
        </span>
      </v-row>
      <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.allLinks') && tableData.length === 0" class="d-flex justify-center">
        <v-card  elevation="0" class="mt-2 align-content-center" height="200" width="390">
          <v-row no-gutters class="d-flex justify-center">
            <v-col cols="5" align="center">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
                {{ $t('DashboardAffiliate.noClick') }}
              </h1>
            </v-col>
          </v-row>
        </v-card>
      </v-row>
      <v-card v-if="MobileSize && selectedType === $t('DashboardAffiliate.allLinks') && tableData.length !== 0">
        <v-data-table :headers="header" :items="tableData" :items-per-page="5" height="390px">
          <template v-slot:[`item.short_link`]="{item}">
          <v-row>
            <v-col cols="10">
              <span v-if="item.status === 'In Active'" style="color: #D3D3D3;">{{ item.short_link }}</span>
              <a v-if="item.status === 'Active'" :href="item.short_link" target="_blank">{{ item.short_link }}</a>
            </v-col>
            <v-col cols="2" class="d-flex align-center">
              <v-btn v-if="item.status === 'In Active'" @click="copyLink(item.short_link)" icon disabled><v-icon small>
                mdi-content-copy
              </v-icon></v-btn>
              <v-btn v-if="item.status === 'Active'" @click="copyLink(item.short_link)" icon><v-icon small>
                mdi-content-copy
              </v-icon></v-btn>
            </v-col>
          </v-row>
        </template>
        <template v-slot:[`item.status`]="{item}">
          <!-- 4444444444444 -->
          <v-chip :color="backgroundStatus(item.status)" :text-color="statusColor(item.status)">
            {{ linkStatus(item.status)}}
          </v-chip>
        </template>
        <template v-slot:[`header.total_click`]>
          <v-tooltip top>
            <template v-slot:activator="{on, attrs}">
              <span v-bind="attrs" v-on="on">
                {{ $t('DashboardAffiliate.numberClicks') }} ({{ $t('DashboardAffiliate.unitClick') }})
              </span>
            </template>
            <span>{{ $t('DashboardAffiliate.totalPeriods') }}</span>
          </v-tooltip>
        </template>
          <template v-slot:[`item.attribute_list`]="{ item }">
              <v-row dense >
                <v-btn
                  x-small
                  outlined
                  @click = "openDialogDetail(item)"
                  style="border: none; width: 100%;"
                  height="100%"
                  class="pt-4 pb-4">
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.commission_rate`]="{item}">
              <span v-if="item.commission_type === 'percent'">
                {{ item.commission_rate }} %
              </span>
              <span v-if="item.commission_type === 'baht'">
                {{ item.commission_rate }} {{ $t('DashboardAffiliate.unit') }}
              </span>
              <span v-if="item.commission_type === null">
                {{ item.commission_rate }}
              </span>
            </template>
            <!-- <template v-slot:[`item.short_link`]="{item}">
              <v-row justify="end">
                <v-col cols="6"><span>{{ item.short_link }}</span></v-col>
              </v-row>
            </template> -->
        </v-data-table>
      </v-card>
      <!-- ตารางข้อมูล commission -->
      <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.commission')" class="pl-4 pt-3 pb-4">
        <span style="font-size: 14px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
          ({{ tableDataCom.length }} {{ $t('DashboardAffiliate.unitOrder') }})
        </span>
      </v-row>
      <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.commission') && tableDataCom.length === 0" class="d-flex justify-center">
        <v-card  elevation="0" class="mt-2 align-content-center" height="200" width="390">
          <v-row no-gutters class="d-flex justify-center">
            <v-col cols="6" align="center">
              <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
                {{ $t('DashboardAffiliate.noPurchase') }}
              </h1>
            </v-col>
          </v-row>
        </v-card>
      </v-row>
      <v-card v-if="MobileSize && selectedType === $t('DashboardAffiliate.commission') && tableDataCom.length !== 0" class="mt-2 mb-4">
        <v-data-table :headers="headerCom" :items="tableDataCom" :items-per-page="5" height="390px">
          <template v-slot:[`item.short_link`]="{item}">
          <v-row>
            <v-col cols="10">
              <!-- <a v-if="item.status === 'In Active'" :href="item.short_link" target="_blank"><span style="color: red;">{{ item.short_link }}</span></a> -->
              <a :href="item.short_link" target="_blank">{{ item.short_link }}</a>
            </v-col>
            <v-col cols="2" class="d-flex align-center">
              <v-btn @click="copyLink(item.short_link)" icon><v-icon small>
                mdi-content-copy
              </v-icon></v-btn>
            </v-col>
          </v-row>
          </template>
          <template v-slot:[`item.attribute_list`]="{ item }">
            <v-row dense >
              <v-btn
                x-small
                outlined
                @click = "openDialogCommission(item)"
                style="border: none; width: 100%;"
                height="100%"
                class="pt-4 pb-4">
                <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
              </v-btn>
            </v-row>
          </template>
        </v-data-table>
      </v-card>
      <!-- top 10 -->
      <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.topClick') && topItem.length === 0" class="d-flex justify-center">
      <v-card  elevation="0" class="mt-2 align-content-center" height="200" width="390">
        <v-row no-gutters class="d-flex justify-center">
          <v-col cols="5" align="center">
            <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
              {{ $t('DashboardAffiliate.noClick') }}
            </h1>
          </v-col>
        </v-row>
      </v-card>
    </v-row>
      <v-card class="mt-2" style="border-radius: 8px;" elevation="0" v-if="MobileSize && selectedType === $t('DashboardAffiliate.topClick') && topItem.length !== 0">
        <v-row>
          <!-- first -->
          <v-col cols="12">
            <v-row no-gutters>
              <v-col v-for="(item, index) in topItem.slice(0, 5)" :key="index" cols="12" class="mb-4">
                <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                  <v-row class="pa-1">
                    <v-col cols="2" class="d-flex align-center">
                      <!-- place 1 -->
                      <v-avatar v-if="index === 0" rounded size="43">
                        <v-img contain :src="goldMedalIconPath">
                          <span
                            class="display-1 font-weight-bold"
                            style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                            <font style="font-size: 20px;">
                              1
                            </font>
                        </span>
                        </v-img>
                      </v-avatar>
                      <!-- place 2 -->
                      <v-avatar v-else-if="index === 1" rounded size="43">
                        <v-img contain :src="silverMedalIconPath">
                          <span
                            class="display-1 font-weight-bold"
                            style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;">
                            <font style="font-size: 20px;">
                              2
                            </font>
                        </span>
                        </v-img>
                      </v-avatar>
                      <!-- place 3 -->
                      <v-avatar v-else-if="index === 2" rounded size="43">
                        <v-img contain :src="bronzeMedalIconPath">
                          <span
                            class="display-1 font-weight-bold"
                            style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                            <font style="font-size: 20px;">
                              3
                            </font>
                        </span>
                        </v-img>
                      </v-avatar>
                      <!-- other palce -->
                      <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                        {{ index+1 }}
                      </v-avatar>
                    </v-col>
                    <v-col cols="2" class="d-flex align-center pl-0">
                      <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                    </v-avatar>
                      <v-avatar v-else rounded size="44" color="#FFF">
                        <v-img contain :src="item.media_path"></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                      <v-col>
                        <span class="one-lines">{{item.name}}</span>
                      </v-col>
                      <v-chip color="#D668030D" style="border-radius: 40px;">
                        <span class="vchipFontSize" style="color: #27AB9C">{{ item.total_click }} {{ $t('DashboardAffiliate.unitClick') }}</span>
                      </v-chip>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
        <!-- second -->
        <v-col cols="12" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in topItem.slice(5, 10)" :key="index" cols="12" class="mb-4">
              <v-card  height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2" class="d-flex align-center">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index+6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="d-flex align-center pl-0">
                    <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <v-col>
                      <span class="one-lines">{{item.name}}</span>
                    </v-col>
                    <v-chip color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">{{ item.total_click }} {{ $t('DashboardAffiliate.unitClick') }}</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-row v-if="MobileSize && selectedType === $t('DashboardAffiliate.topSeller') && topSold.length === 0" class="d-flex justify-center">
      <v-card  elevation="0" class="mt-2 align-content-center" height="200" width="390">
        <v-row no-gutters class="d-flex justify-center">
          <v-col cols="6" align="center">
            <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
              {{ $t('DashboardAffiliate.noPurchase') }}
            </h1>
          </v-col>
        </v-row>
      </v-card>
    </v-row>
    <v-card class="mt-2" style="border-radius: 8px;" elevation="0" v-if="MobileSize && selectedType === $t('DashboardAffiliate.topSeller') && topSold.length !== 0">
        <v-row>
          <!-- first -->
          <v-col cols="12">
            <v-row no-gutters>
              <v-col v-for="(item, index) in topSold.slice(0, 5)" :key="index" cols="12" class="mb-4">
                <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                  <v-row class="pa-1">
                    <v-col cols="2" class="d-flex align-center">
                      <!-- place 1 -->
                      <v-avatar v-if="index === 0" rounded size="43">
                        <v-img contain :src="goldMedalIconPath">
                          <span
                            class="display-1 font-weight-bold"
                            style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                            <font style="font-size: 20px;">
                              1
                            </font>
                        </span>
                        </v-img>
                      </v-avatar>
                      <!-- place 2 -->
                      <v-avatar v-else-if="index === 1" rounded size="43">
                        <v-img contain :src="silverMedalIconPath">
                          <span
                            class="display-1 font-weight-bold"
                            style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;">
                            <font style="font-size: 20px;">
                              2
                            </font>
                        </span>
                        </v-img>
                      </v-avatar>
                      <!-- place 3 -->
                      <v-avatar v-else-if="index === 2" rounded size="43">
                        <v-img contain :src="bronzeMedalIconPath">
                          <span
                            class="display-1 font-weight-bold"
                            style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                            <font style="font-size: 20px;">
                              3
                            </font>
                        </span>
                        </v-img>
                      </v-avatar>
                      <!-- other palce -->
                      <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                        {{ index+1 }}
                      </v-avatar>
                    </v-col>
                    <v-col cols="2" class="d-flex align-center pl-0">
                      <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                      <v-avatar v-else rounded size="44" color="#FFF">
                        <v-img contain :src="item.media_path"></v-img>
                      </v-avatar>
                    </v-col>
                    <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                      <v-col>
                        <span class="one-lines">{{item.name}}</span>
                      </v-col>
                      <v-chip color="#D668030D" style="border-radius: 40px;">
                        <span class="vchipFontSize" style="color: #FE6F07">{{ item.quantity }} {{ $t('DashboardAffiliate.items') }}</span>
                      </v-chip>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-col>
        <!-- second -->
        <v-col cols="12" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in topSold.slice(5, 10)" :key="index" cols="12" class="mb-4">
              <v-card  height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2" class="d-flex align-center">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index+6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="d-flex align-center pl-0">
                    <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <v-col>
                      <span class="one-lines">{{item.name}}</span>
                    </v-col>
                    <v-chip color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">{{ item.quantity }} {{ $t('DashboardAffiliate.items') }}</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <!-- ---------------------------------------------->
    <v-card class="mb-4" style="border-radius: 8px;" elevation="0" v-if="!MobileSize">
      <v-row>
        <v-spacer></v-spacer>
        <v-col :cols=" IpadProSize ? '5' : IpadSize ? '6' : '4'" class="pt-4">
            <v-select item-text="name" item-value="id" :label="$t('DashboardAffiliate.selectShop')" dense outlined v-model="selectShopType" @change="handleSelectChange" :items="selectShop" hide-details style="border-radius: 80px;"></v-select>
        </v-col>
      </v-row>
    </v-card>
    <!-- เลือกกราฟ -->
    <v-card style="border-radius: 8px;" elevation="0" v-if="!MobileSize">
        <v-btn outlined :class="{'grey-tabButton' : selectTab === $t('DashboardAffiliate.clickInfo'), 'subTitleTextTabButton' : selectTab === $t('DashboardAffiliate.incomeInfo')}" @click="selectTabClick($t('DashboardAffiliate.incomeInfo'))">
          <v-avatar rounded size="24">
            <v-img contain :src="passiveIncomeIconPath"></v-img>
          </v-avatar>
          <span :class="{'grey-tab ml-2' : selectTab === $t('DashboardAffiliate.clickInfo'), 'subTitleTextTab ml-2' : selectTab === $t('DashboardAffiliate.incomeInfo')}">{{ $t('DashboardAffiliate.incomeInfo') }}</span>
        </v-btn>
        <v-btn outlined :class="{'grey-tabButton' : selectTab === $t('DashboardAffiliate.incomeInfo'), 'subTitleTextTabButton' : selectTab === $t('DashboardAffiliate.clickInfo')}" @click="selectTabClick($t('DashboardAffiliate.clickInfo'))">
          <v-avatar rounded size="24">
            <v-img contain :src="clickIcon"></v-img>
          </v-avatar>
          <span :class="{'grey-tab ml-2' : selectTab === $t('DashboardAffiliate.incomeInfo'), 'subTitleTextTab ml-2' : selectTab === $t('DashboardAffiliate.clickInfo')}">{{ $t('DashboardAffiliate.clickInfo') }}</span>
        </v-btn>
      <!-- <ul class="nav nav-tabs mt-5 pl-0" style="display: flex; flex-wrap: nowrap;">
      </ul> -->
    </v-card>
    <v-card class="mb-10" width="100%" height="50%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-if="!MobileSize">
        <v-card outlined style="border-color: #dee2e6" elevation="0" v-if="selectTab === $t('DashboardAffiliate.incomeInfo')">
          <v-card-title>
            <v-row>
              <v-col cols="6">
                <v-avatar rounded size="20">
                  <v-img contain :src="statisticsIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.incomeGraph') }}</span>
              </v-col>
              <v-col cols="6" align="end">
                <v-avatar rounded size="27" class="mt-2">
                  <v-img contain :src="graphLineIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.commissionFee') }}</span>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text><apexchart height="400" type="line" :options="chartOptions" :series="series"></apexchart></v-card-text>
        </v-card>
        <v-card outlined style="border-color: #dee2e6" elevation="0" v-if="selectTab === $t('DashboardAffiliate.clickInfo')">
          <v-card-title>
            <v-row>
              <v-col cols="6">
                <v-avatar rounded size="20">
                  <v-img contain :src="statisticsIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 18px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.clickGraph') }}</span>
              </v-col>
              <v-col cols="6" align="end">
                <v-avatar rounded size="27" class="mt-2">
                  <v-img contain :src="graphLineIconPath"></v-img>
                </v-avatar>
                <span class="ml-2" style="font-size: 12px; font-style: normal; font-weight: 400;">{{ $t('DashboardAffiliate.clicking') }}</span>
              </v-col>
            </v-row>
          </v-card-title>
          <v-card-text><apexchart height="400" type="line" :options="chartOptionsClick" :series="seriesClick"></apexchart></v-card-text>
        </v-card>
      </v-card>
    <v-card class="mb-3" width="100%" height="100%" style="background: #FFFFFF; border: 0px solid;" elevation="0" v-if="!MobileSize">
        <v-row>
            <!-- รายได้ -->
            <v-col cols="6" align="center" justify="center">
                <v-card style="border-radius: 8px;background: #F9FAFD;" elevation="0">
                    <v-row >
                        <v-col cols="12">
                            <v-avatar rounded size="80">
                                <v-img contain :src="moneyIconPath"></v-img>
                            </v-avatar>
                        </v-col>
                        <v-col cols="12" v-if="this.totalCommissions === null || this.totalCommissions === ''">
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">0.00</span>
                        </v-col>
                        <v-col cols="12" v-else>
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">{{ this.totalCommissions }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">{{ $t('DashboardAffiliate.totalCommission') }}<br/> ({{ ($t('DashboardAffiliate.unit')) }})</span>
                        </v-col>
                    </v-row>
                </v-card>
            </v-col>
            <!-- ยอดแชร์ -->
            <v-col cols="6" align="center" justify="center">
                <v-card style="border-radius: 8px;background: #F9FAFD;" elevation="0">
                    <v-row >
                        <v-col cols="12" >
                            <v-avatar rounded size="80">
                                <v-img contain :src="clickIcon"></v-img>
                            </v-avatar>
                        </v-col>
                        <v-col cols="12" v-if="totalQuantity === 0">
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">0</span>
                        </v-col>
                        <v-col cols="12" v-else>
                            <span  style="font-size: 32px; font-style: normal; font-weight: 700; color:#186DEB">{{ parseInt(this.totalQuantity) }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">{{ $t('DashboardAffiliate.totalClicks') }} <br/> ({{ $t('DashboardAffiliate.unitClick') }})</span>
                        </v-col>
                    </v-row>
                </v-card>
            </v-col>
        </v-row>
    </v-card>
    <v-card class="mb-2" style="border-radius: 8px;" elevation="0" v-if="!MobileSize">
      <v-row>
        <v-col class="mt-6" cols="9">
          <v-avatar rounded size="24">
            <v-img contain :src="dataModelIconPath"></v-img>
          </v-avatar>
          <!-- <span v-if="tableData.length === 0" class="ml-2 subTitleText">รายการลิงก์ทั้งหมด (0 รายการ)</span> -->
          <span class="ml-2 subTitleText">{{ $t('DashboardAffiliate.allLinks') }} ({{ tableData.length }} {{ $t('DashboardAffiliate.unitOrder') }})</span>
        </v-col>
        <v-col class="mt-5 d-flex justify-end" cols="3" v-if="tableData.length !== 0">
          <v-btn @click="exportExcel()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <v-card v-if="!MobileSize && tableData.length === 0" elevation="0">
      <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">{{ $t('DashboardAffiliate.noClick') }}</h1>
    </v-card>
    <v-card outlined class="mt-2 mb-4" v-if="!MobileSize && tableData.length !== 0">
      <!-- <v-col cols="8" v-if="checkZerolength === 0">
        <span>
          ไม่พบข้อมูล
        </span>
      </v-col> -->
      <v-data-table :headers="header" :items="tableData" :items-per-page="5">
        <template v-slot:[`item.short_link`]="{item}">
          <v-row dense>
            <v-col cols="10">
              <span v-if="item.status === 'In Active'" style="color: #D3D3D3;">{{ item.short_link }}</span>
              <a v-if="item.status === 'Active'" :href="item.short_link" target="_blank">{{ item.short_link }}</a>
            </v-col>
            <v-col cols="2" class="d-flex align-center">
              <v-btn v-if="item.status === 'In Active'" @click="copyLink(item.short_link)" icon disabled><v-icon small>
                mdi-content-copy
              </v-icon></v-btn>
              <v-btn v-if="item.status === 'Active'" @click="copyLink(item.short_link)" icon><v-icon small>
                mdi-content-copy
              </v-icon></v-btn>
            </v-col>
          </v-row>
        </template>
        <template v-slot:[`item.status`]="{item}">
          <!-- 4444444444444 -->
          <v-chip :color="backgroundStatus(item.status)" :text-color="statusColor(item.status)">
            {{ linkStatus(item.status)}}
          </v-chip>
        </template>
        <template v-slot:[`header.total_click`]>
          <v-tooltip top>
            <template v-slot:activator="{on, attrs}">
              <span v-bind="attrs" v-on="on">
                {{ $t('DashboardAffiliate.numberClicks') }} ({{ $t('DashboardAffiliate.unitClick') }})
              </span>
            </template>
            <span>{{ $t('DashboardAffiliate.totalPeriods') }}</span>
          </v-tooltip>
        </template>
        <template v-slot:[`item.attribute_list`]="{ item }">
          <v-row dense >
            <v-btn
              x-small
              outlined
              @click = "openDialogDetail(item)"
              style="border: none; width: 100%;"
              height="100%"
              class="pt-4 pb-4">
              <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
            </v-btn>
          </v-row>
        </template>
        <template v-slot:[`item.commission_rate`]="{item}">
          <span v-if="item.commission_type === 'percent'">
            {{ item.commission_rate }} %
          </span>
          <span v-if="item.commission_type === 'baht'">
            {{ item.commission_rate }} {{ $t('DashboardAffiliate.unit') }}
          </span>
          <span v-if="item.commission_type === null">
            {{ item.commission_rate }}
          </span>
        </template>
      </v-data-table>
    </v-card>
    <!-- ตารางแสดงค่า commission แต่ละลิงค์  -->
    <v-card class="mb-2" style="border-radius: 8px;" elevation="0" v-if="!MobileSize">
      <v-row>
        <v-col class="mt-6" cols="9">
          <v-avatar rounded size="24">
            <v-img contain :src="dataModelIconPath"></v-img>
          </v-avatar>
          <span class="ml-2 subTitleText">{{ $t('DashboardAffiliate.commission') }} ({{ tableDataCom.length }} {{ $t('DashboardAffiliate.unitOrder') }})</span>
        </v-col>
        <v-col v-if="tableDataCom.length !== 0" class="mt-5 d-flex justify-end" cols="3">
          <v-btn @click="exportExcelCommission()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <v-card v-if="!MobileSize && tableDataCom.length === 0" elevation="0">
      <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">{{ $t('DashboardAffiliate.noPurchase') }}</h1>
    </v-card>
    <v-card v-if="!MobileSize && tableDataCom.length !== 0" outlined class="mt-2 mb-4">
      <v-data-table :headers="headerCom" :items="tableDataCom" :items-per-page="5" >
        <template v-slot:[`item.short_link`]="{item}">
          <v-row>
            <v-col cols="10">
              <!-- <a v-if="item.status === 'In Active'" :href="item.short_link" target="_blank"><span style="color: red;">{{ item.short_link }}</span></a> -->
              <a :href="item.short_link" target="_blank">{{ item.short_link }}</a>
            </v-col>
            <v-col cols="2" class="d-flex align-center">
              <v-btn @click="copyLink(item.short_link)" icon><v-icon small>
                mdi-content-copy
              </v-icon></v-btn>
            </v-col>
          </v-row>
        </template>
        <template v-slot:[`item.attribute_list`]="{ item }">
              <v-row dense >
                <v-btn
                  x-small
                  outlined
                  @click = "openDialogCommission(item)"
                  style="border: none; width: 100%;"
                  height="100%"
                  class="pt-4 pb-4">
                  <v-icon color="#A1A1A1" small>mdi-eye</v-icon>
                </v-btn>
              </v-row>
            </template>
      </v-data-table>
    </v-card>
    <v-card style="border-radius: 8px;" elevation="0" v-if="!MobileSize">
      <v-row>
        <v-col class="mt-6" cols="9">
          <v-avatar rounded size="24">
            <v-img contain :src="uniqueIconPath"></v-img>
          </v-avatar>
          <span class="ml-2 subTitleText">{{ $t('DashboardAffiliate.topClick') }}</span>
        </v-col>
        <v-col class="mt-5 d-flex justify-end" cols="3" v-if="topItem.length !== 0">
          <v-btn @click="exportExcelTop()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <v-card v-if="!MobileSize && topItem.length === 0" elevation="0" class="mt-2">
      <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
        {{ $t('DashboardAffiliate.noClick') }}
      </h1>
    </v-card>
    <v-card class="mt-2" style="border-radius: 8px;" elevation="0" v-if="!MobileSize && topItem.length !== 0">
      <v-row>
        <!-- first -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in topItem.slice(0, 5)" :key="index" cols="12" class="mb-4">
              <v-card  height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2" class="d-flex align-center">
                    <!-- place 1 -->
                    <v-avatar v-if="index === 0" rounded size="43">
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                          <font style="font-size: 20px;">
                            1
                          </font>
                      </span>
                      </v-img>
                    </v-avatar>
                    <!-- place 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;">
                          <font style="font-size: 20px;">
                            2
                          </font>
                      </span>
                      </v-img>
                    </v-avatar>
                    <!-- place 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                          <font style="font-size: 20px;">
                            3
                          </font>
                      </span>
                      </v-img>
                    </v-avatar>
                    <!-- other palce -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index+1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="d-flex align-center pl-0">
                    <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <v-col>
                      <span class="one-lines">{{item.name}}</span>
                    </v-col>
                    <v-chip color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">{{ item.total_click }} {{ $t('DashboardAffiliate.unitClick') }}</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- second -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in topItem.slice(5, 10)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2" class="d-flex align-center">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index+6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="d-flex align-center pl-0">
                    <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <v-col>
                      <span class="one-lines">{{item.name}}</span>
                    </v-col>
                    <v-chip color="#27AB9C0D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #27AB9C">{{ item.total_click }} {{ $t('DashboardAffiliate.unitClick') }}</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-card style="border-radius: 8px;" elevation="0" v-if="!MobileSize">
      <v-row>
        <v-col class="mt-6" cols="9">
          <v-avatar rounded size="24">
            <v-img contain :src="commissionIconPath"></v-img>
          </v-avatar>
          <span class="ml-2 subTitleText">{{ $t('DashboardAffiliate.topSeller') }}</span>
        </v-col>
        <v-col class="mt-5 d-flex justify-end" cols="3" v-if="topSold.length !== 0">
          <v-btn @click="exportExcelSold()" style="border-radius: 40px; background: #27AB9C;" height="32px">
            <v-img height="20px" width="20px" contain :src="require('@/assets/icons/SellerDashboard/file-export-solid 1.png')"></v-img>
            <span class="ml-1 exportButtonText">Export</span>
          </v-btn>
        </v-col>
      </v-row>
    </v-card>
    <v-card v-if="!MobileSize && topSold.length === 0" elevation="0" class="mt-2">
      <h1 style="font-size: 18px; font-style: normal; font-weight: 400; color: rgb(196, 196, 196);">
        {{ $t('DashboardAffiliate.noPurchase') }}
      </h1>
    </v-card>
    <v-card class="mt-2" style="border-radius: 8px;" elevation="0" v-if="!MobileSize && topSold.length !== 0">
      <v-row>
        <!-- first -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in topSold.slice(0, 5)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2" class="d-flex align-center">
                    <!-- place 1 -->
                    <v-avatar v-if="index === 0" rounded size="43">
                      <v-img contain :src="goldMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                          <font style="font-size: 20px;">
                            1
                          </font>
                      </span>
                      </v-img>
                    </v-avatar>
                    <!-- place 2 -->
                    <v-avatar v-else-if="index === 1" rounded size="43">
                      <v-img contain :src="silverMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: #E39162; font-size: 10px;">
                          <font style="font-size: 20px;">
                            2
                          </font>
                      </span>
                      </v-img>
                    </v-avatar>
                    <!-- place 3 -->
                    <v-avatar v-else-if="index === 2" rounded size="43">
                      <v-img contain :src="bronzeMedalIconPath">
                        <span
                          class="display-1 font-weight-bold"
                          style="position: absolute; top: 30%; left: 50%; transform: translate(-50%, -50%); color: white; font-size: 10px;">
                          <font style="font-size: 20px;">
                            3
                          </font>
                      </span>
                      </v-img>
                    </v-avatar>
                    <!-- other palce -->
                    <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index+1 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="d-flex align-center pl-0">
                    <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                    </v-avatar >
                    <v-avatar v-else size="44" color="#FFF" rounded>
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <v-col>
                      <span class="one-lines">{{item.name}}</span>
                    </v-col>
                    <v-chip color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">{{ item.quantity }} {{ $t('DashboardAffiliate.items') }}</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
        <!-- second -->
        <v-col cols="6" md="6" sm="12" xs="12">
          <v-row no-gutters>
            <v-col v-for="(item, index) in topSold.slice(5, 10)" :key="index" cols="12" class="mb-4">
              <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                <v-row class="pa-1">
                  <v-col cols="2" class="d-flex align-center">
                    <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                      {{ index+6 }}
                    </v-avatar>
                  </v-col>
                  <v-col cols="2" class="d-flex align-center pl-0">
                    <v-avatar v-if="item.media_path === null" rounded size="44" color="#FFF">
                      <!-- <span>No image</span> -->
                      <img src="@/assets/NoImage.png" alt="No Image" class="avatar-image" width="50" />
                    </v-avatar>
                    <v-avatar v-else rounded size="44" color="#FFF">
                      <v-img contain :src="item.media_path"></v-img>
                    </v-avatar>
                  </v-col>
                  <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                    <v-col>
                      <span class="one-lines">{{item.name}}</span>
                    </v-col>
                    <v-chip color="#D668030D" style="border-radius: 40px;">
                      <span class="vchipFontSize" style="color: #FE6F07">{{ item.quantity }} {{ $t('DashboardAffiliate.items') }}</span>
                    </v-chip>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog
      v-model="openDialog"
      width="640px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
      scrollable
    >
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span
            class="flex text-center ml-5"
            style="font-weight: bold"
            :style="MobileSize ? 'font-size: 16px' : 'font-size:20px'"
          >
            <font color="#27AB9C">{{ $t('DashboardAffiliate.productList') }}</font>
          </span>
          <v-btn icon dark @click="closeDialogDetail()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row no-gutters>
              <v-col class="pl-3 pt-3">
                <span style="color:#333333; font-size: 16px; font-weight: 600;">{{ $t('DashboardAffiliate.AllProductsList') }} {{ order_Detail.length }}  {{ $t('DashboardAffiliate.items') }}</span>
              </v-col>
            </v-row>
        </v-card-text>
        <v-card-text v-bind:style="{'height' : '400px'}">
          <v-container>
            <v-row v-for="(item, index) in order_Detail" :key="index">
              <v-col cols="12">
                <v-card class="mt-4">
                  <v-row>
                    <v-col :cols="MobileSize ? '12' : '4'" align="center">
                      <v-avatar tile size="160">
                        <div v-if="item.color_image_path !== null">
                          <img :src="item.color_image_path" alt="No Image" class="avatar-image" />
                        </div>
                        <div v-else>
                          <img src="@/assets/NoImage.png" alt="No Iamge" class="avatar-image" width="50" />
                        </div>
                      </v-avatar>
                    </v-col>
                    <v-col :cols="MobileSize ? '12' : '8'">
                      <v-card-title :class="MobileSize ? 'no-word-break pb-0' : 'no-word-break'"><span style="color: #27AB9C; font-weight: bold; font-size: 18px;">{{ item.name }}</span></v-card-title>
                      <v-card-text>
                        <v-col :class="MobileSize ? 'pl-0 pb-0' : 'pl-0 pb-0 pt-0'" :cols="MobileSize ? '12' : ''"><div><b>{{ $t('DashboardAffiliate.productType') }}:</b> {{ item.attribute_priority_1 }}</div></v-col>
                        <v-col :class="MobileSize ? 'pl-0 pb-0' : 'pl-0 pb-0 pt-1'" :cols="MobileSize ? '12' : ''"><div><b>{{ $t('DashboardAffiliate.attribute') }}:</b> {{ item.attribute_priority_2 }}</div></v-col>
                        <v-col :class="MobileSize ? 'pl-0 pb-0' : 'pl-0 pb-0 pt-1'" :cols="MobileSize ? '12' : ''"><div><b>{{ $t('DashboardAffiliate.price') }}:</b> {{ item.product_price }} {{ $t('DashboardAffiliate.unit') }}</div></v-col>
                        <v-col :class="MobileSize ? 'pl-0 pb-0' : 'pl-0 pb-0 pt-1'" :cols="MobileSize ? '12' : ''"><div><b>{{ $t('DashboardAffiliate.commissionIncome') }}:</b> {{ item.commission_received }} {{ $t('DashboardAffiliate.unit') }}</div></v-col>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- dialog commision -->
    <v-dialog
        v-model = "openDialogCom"
        width="732"
        scrollable>
        <v-card height="100%">
          <v-toolbar flat color="#E6F5F3">
            <v-row>
              <v-col class="d-flex justify-space-around">
                <v-toolbar-title>
                  <span style="color: #27AB9C;">
                    <b>
                     {{ $t('DashboardAffiliate.orderDetail') }} ({{order_DetailCom.length}} {{ $t('DashboardAffiliate.unitOrder') }})
                    </b>
                  </span>
                </v-toolbar-title>
              </v-col>
            </v-row>
            <v-btn fab small icon @click="closeDialogCommision()"><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
          </v-toolbar>
          <v-container>
            <v-row justify="center" align-content="center" class="px-5">
              <v-col cols="12" v-for="(item, index) in order_DetailCom" :key="index">
                <v-card outlined style="background-color: #FFFFFF;" v-if="!MobileSize">
                  <v-card-text>
                    <div class="d-flex flex-no-wrap">
                        <v-avatar v-if="item.color_image_path === null || item.color_image_path === ''" class="ma-4" size="172" tile>
                      <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" />
                        </v-avatar>
                        <v-avatar v-else class="ma-4" size="172" tile>
                          <!-- ถ้าต้องการทำ api ดูใน DetailAdminPanitModal.vue -->
                          <v-img :src="item.color_image_path" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;"></v-img>
                        </v-avatar>
                        <div class="ma-4" >
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.productName') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;">{{ item.name }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.productType') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_1 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.attribute') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_2 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.price') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.product_price }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.quantity') }}({{ $t('DashboardAffiliate.items') }}) :  </span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.total_quantity }}</span> </v-col>
                          </v-row>
                          <!-- <v-row no-gutters class="mb-2">
                            <span>เปอร์เซ็นคอมมิชชัน(%/ชิ้น) :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.commission_rate }}</span></v-col>
                          </v-row> -->
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.commissionFee') }}({{ $t('DashboardAffiliate.unit') }}) :  </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C">{{ item.total_estimate_commission }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.taxDeduction') }}({{ $t('DashboardAffiliate.unit') }}) :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.total_influencer_tax }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.commissionIncome') }}({{ $t('DashboardAffiliate.unit') }}) :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.total_influencer_payment }}</span></v-col>
                          </v-row>
                        </div>
                    </div>
                  </v-card-text>
                </v-card>
                <v-card outlined style="background-color: #FFFFFF;" v-else>
                  <v-card-text>
                    <v-row justify="center">
                      <v-col cols="12" class="d-flex justify-center">
                        <v-avatar v-if="item.color_image_path === null || item.color_image_path === ''" class="ma-4" size="172" tile>
                      <!-- <span>No image</span> -->
                          <img src="@/assets/NoImage.png" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;" />
                        </v-avatar>
                        <v-avatar v-else class="ma-4" size="172" tile>
                          <!-- ถ้าต้องการทำ api ดูใน DetailAdminPanitModal.vue -->
                          <v-img :src="item.color_image_path" contain style="border-radius: 8px; border: 1px #E0E0E0 solid;"></v-img>
                        </v-avatar>
                      </v-col>
                    </v-row>
                    <v-row no-gutters>
                      <v-col cols="12">
                        <div class="ma-4" >
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.productName') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;">{{ item.name }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.productType') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_1 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.attribute') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.attribute_priority_2 }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.price') }} : </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C;"> {{ item.product_price }} </span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.quantity') }}({{ $t('DashboardAffiliate.items') }}) :  </span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.total_quantity }}</span> </v-col>
                          </v-row>
                          <!-- <v-row no-gutters class="mb-2">
                            <span>เปอร์เซ็นคอมมิชชัน(%/ชิ้น) :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.commission_rate }}</span></v-col>
                          </v-row> -->
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.commissionFee') }}({{ $t('DashboardAffiliate.unit') }}) :  </span>
                            <v-col> <span class="pl-2" style="color: #27AB9C">{{ item.total_estimate_commission }}</span></v-col>
                          </v-row>
                          <v-row no-gutters class="mb-2">
                            <span>{{ $t('DashboardAffiliate.taxDeduction') }}({{ $t('DashboardAffiliate.unit') }}) :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.total_influencer_tax }}</span></v-col>
                          </v-row>
                          <v-row no-gutters>
                            <span>{{ $t('DashboardAffiliate.commissionIncome') }}({{ $t('DashboardAffiliate.unit') }}) :</span>
                            <v-col><span class="pl-2" style="color: #27AB9C">{{ item.total_influencer_payment }}</span></v-col>
                          </v-row>
                        </div>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import VueApexCharts from 'vue-apexcharts'
export default {
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      zeroCount: [],
      checkZerolength: null,
      selectTab: `${this.$t('DashboardAffiliate.incomeInfo')}`,
      topSold: [],
      order_Detail: [],
      openDialog: false,
      openDialogCom: false,
      order_DetailCom: [],
      totalQuantity: null,
      totalCommissions: null,
      chartData: [],
      startDate: '',
      endDate: '',
      // ยังไม่ใช้-----------
      selectShop: [],
      selectShopType: -1,
      selectPageType: [`${this.$t('DashboardAffiliate.incomeInfo')}`, `${this.$t('DashboardAffiliate.allLinks')}`, `${this.$t('DashboardAffiliate.commission')}`, `${this.$t('DashboardAffiliate.topClick')}`, `${this.$t('DashboardAffiliate.topSeller')}`],
      selectedType: `${this.$t('DashboardAffiliate.incomeInfo')}`,
      // ยังไม่ใช้-----------
      showGraphMobile: false,
      showTableMobile: false,
      showTopTen: false,
      // -------------------
      openDialogMobile: false,
      query: {},
      minDate: '2022-01-01', // Set your minimum date here
      maxDate: '2025-12-31', // Set your maximum date here
      datesMobile: [],
      dates: [],
      // startDate: new Date().getFullYear(),
      // endDate: new Date().getFullYear(),
      showYearDropdown: true,
      showMonthDropdown: false,
      showDatePicker: false,
      modalDateSelect: false,
      selectedDropdown: `${this.$t('DashboardAffiliate.yearly')}`,
      selectFilter: 'year',
      shopID: -1,
      selectedYearMobile: new Date().getFullYear(),
      selectedYear: new Date().getFullYear(),
      selectedMonthMobile: null,
      selectedMonthValueMobile: null,
      selectedMonth: null,
      selectedMonthValue: null,
      yearStart: null,
      yearEnd: null,
      selectedMonthValueStart: null,
      selectedMonthValueEnd: null,
      startMonth: null,
      endMonth: null,
      startDay: null,
      endDay: null,
      months: [
        {
          month: `${this.$t('DashboardAffiliate.Jan')}`,
          value: '01'
        },
        {
          month: `${this.$t('DashboardAffiliate.Feb')}`,
          value: '02'
        },
        {
          month: `${this.$t('DashboardAffiliate.Mar')}`,
          value: '03'
        },
        {
          month: `${this.$t('DashboardAffiliate.Apr')}`,
          value: '04'
        },
        {
          month: `${this.$t('DashboardAffiliate.May')}`,
          value: '05'
        },
        {
          month: `${this.$t('DashboardAffiliate.Jun')}`,
          value: '06'
        },
        {
          month: `${this.$t('DashboardAffiliate.Jul')}`,
          value: '07'
        },
        {
          month: `${this.$t('DashboardAffiliate.Aug')}`,
          value: '08'
        },
        {
          month: `${this.$t('DashboardAffiliate.Sep')}`,
          value: '09'
        },
        {
          month: `${this.$t('DashboardAffiliate.Oct')}`,
          value: '10'
        },
        {
          month: `${this.$t('DashboardAffiliate.Nov')}`,
          value: '11'
        },
        {
          month: `${this.$t('DashboardAffiliate.Dec')}`,
          value: '12'
        }
      ],
      years: [],
      header: [
        {
          text: `${this.$t('DashboardAffiliate.productName')}`,
          align: 'center',
          sortable: false,
          width: '180',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          value: 'name'
        },
        {
          text: `${this.$t('DashboardAffiliate.link')}`,
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          value: 'short_link'
        },
        {
          text: `${this.$t('DashboardAffiliate.status')}`,
          sortable: false,
          align: 'center',
          width: '150',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          value: 'status'
        },
        {
          text: `${this.$t('DashboardAffiliate.numberClicks')}`,
          sortable: false,
          width: '150',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          value: 'total_click'
        },
        {
          text: `${this.$t('DashboardAffiliate.thPrice')}`,
          width: '150',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'price_range'
        },
        {
          text: `${this.$t('DashboardAffiliate.thComProduct')}`,
          width: '180',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'commission_rate'
        },
        {
          text: `${this.$t('DashboardAffiliate.thComPrice')}`,
          width: '150',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'commission_received_range'
        },
        {
          text: `${this.$t('DashboardAffiliate.attributeList')}`,
          width: '150',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'attribute_list'
        }
      ],
      headerCom: [
        {
          text: `${this.$t('DashboardAffiliate.productName')}`,
          align: 'center',
          sortable: false,
          width: '180',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          value: 'name'
        },
        {
          text: `${this.$t('DashboardAffiliate.link')}`,
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          value: 'short_link'
        },
        {
          text: `${this.$t('DashboardAffiliate.quantitySold')}`,
          width: '150',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'quantity_sold'
        },
        {
          text: `${this.$t('DashboardAffiliate.influPayment')}`,
          width: '180',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'influencer_payment'
        },
        {
          text: `${this.$t('DashboardAffiliate.attributeList')}`,
          width: '150',
          align: 'center',
          class: 'backgroundTable fontTable--text fontSizeDetail',
          sortable: false,
          value: 'attribute_list'
        }
      ],
      tableData: [],
      tableDataCom: [],
      topItem: [],
      series: [],
      seriesClick: [],
      chartOptions: {
        chart: {
          toolbar: {
            show: false
          }
        },
        stroke: {
          show: true
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        colors: ['#0096FF']
      },
      chartOptionsClick: {
        chart: {
          toolbar: {
            show: false
          }
        },
        stroke: {
          show: true
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          tickPlacement: 'between',
          categories: ['0']
        },
        colors: ['#FF0000']
      },
      commissionIconPath: require('@/assets/icons/SellerDashboard/commission 1.png'),
      passiveIncomeIconPath: require('@/assets/icons/SellerDashboard/passive-income 1.png'),
      moneyIconPath: require('@/assets/icons/SellerDashboard/money (1) 1.png'),
      boxIconPath: require('@/assets/icons/SellerDashboard/box 1.png'),
      statisticsIconPath: require('@/assets/icons/SellerDashboard/statistics 1.png'),
      dataModelIconPath: require('@/assets/icons/SellerDashboard/data-model 1.png'),
      uniqueIconPath: require('@/assets/icons/SellerDashboard/unique 1.png'),
      goldMedalIconPath: require('@/assets/icons/SellerDashboard/gold-medal.png'),
      silverMedalIconPath: require('@/assets/icons/SellerDashboard/silver-medal.png'),
      bronzeMedalIconPath: require('@/assets/icons/SellerDashboard/bronze-medal.png'),
      graphLineIconPath: require('@/assets/icons/SellerDashboard/graph-line 1.png'),
      clickIcon: require('@/assets/icons/click.png')
    }
  },
  created () {
    this.checkConsent()
    const currentYear = new Date().getFullYear()
    var MonthOnSelected = new Date().getMonth() + 1
    var DayOnSelected = new Date().getDate()
    for (let year = 2022; year <= currentYear; year++) {
      this.years.push({ text: year })
    }
    this.startDate = `${currentYear}-01-01`
    this.endDate = `${currentYear}-12-31`
    this.startMonth = `${currentYear}-${MonthOnSelected}-01`
    this.endMonth = `${currentYear}-${MonthOnSelected}-31`
    this.startDay = `${currentYear}-${MonthOnSelected}-${DayOnSelected}`
    this.endDay = `${currentYear}-${MonthOnSelected}-${DayOnSelected}`
    this.$EventBus.$emit('changeNavAccount')
    // this.getPageData()
    this.getGraphData(this.startDate, this.endDate, this.shopID, this.selectFilter)
    this.getDataTable(this.startDate, this.endDate, this.shopID, this.selectFilter)
    this.getTopSold(this.startDate, this.endDate, this.shopID, this.selectFilter)
    this.getDataTableCommission(this.startDate, this.endDate, this.shopID, this.selectFilter)
    this.getTopten(this.startDate, this.endDate, this.shopID, this.selectFilter)
    this.getShopData()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SharedBuyerAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/SharedBuyerAffiliate' }).catch(() => {})
      }
    }
  },
  computed: {
    rangeDate () {
      return this.dates.map(date => new Date(date).toLocaleDateString(`${this.$t('DashboardAffiliate.typeLanguage')}`)).join(' - ')
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    backToUsr () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async handleSelectChange () {
      this.shopID = this.selectShopType
      if (this.selectFilter === 'year') {
        if (this.selectedYear === null) {
          await this.getDataTable(this.startDate, this.endDate, this.shopID, 'year')
          await this.getGraphData(this.startDate, this.endDate, this.shopID, 'year')
          await this.getTopSold(this.startDate, this.endDate, this.shopID, 'year')
          await this.getDataTableCommission(this.startDate, this.endDate, this.shopID, 'year')
          await this.getTopten(this.startDate, this.endDate, this.shopID, 'year')
        } else {
          await this.getDataTable(`${this.selectedYear}-01-01`, `${this.selectedYear}-12-31`, this.shopID, 'year')
          await this.getGraphData(`${this.selectedYear}-01-01`, `${this.selectedYear}-12-31`, this.shopID, 'year')
          await this.getTopSold(`${this.selectedYear}-01-01`, `${this.selectedYear}-12-31`, this.shopID, 'year')
          await this.getDataTableCommission(`${this.selectedYear}-01-01`, `${this.selectedYear}-12-31`, this.shopID, 'year')
          await this.getTopten(`${this.selectedYear}-01-01`, `${this.selectedYear}-12-31`, this.shopID, 'year')
        }
      } else if (this.selectFilter === 'month') {
        if (this.selectedMonthValue === null) {
          this.selectFilter = 'month'
          var MonthOnSelected = new Date().getMonth() + 1
          this.selectedMonthValueStart = `${this.selectedYear}-${MonthOnSelected}-01`
          this.selectedMonthValueEnd = `${this.selectedYear}-${MonthOnSelected}-31`
          await this.getDataTable(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getGraphData(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopSold(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopten(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
        } else {
          this.selectedMonthValueStart = `${this.selectedYear}-${this.selectedMonthValue}-01`
          this.selectedMonthValueEnd = `${this.selectedYear}-${this.selectedMonthValue}-31`
          this.selectFilter = 'month'
          await this.getDataTable(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getGraphData(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopSold(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopten(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
        }
      } else if (this.selectFilter === 'day') {
        if (this.dates.length === 0) {
          this.selectFilter = 'day'
          await this.getDataTable(this.startDay, this.endDay, this.shopID, this.selectFilter)
          await this.getGraphData(this.startDay, this.endDay, this.shopID, this.selectFilter)
          await this.getTopSold(this.startDay, this.endDay, this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.startDay, this.endDay, this.shopID, this.selectFilter)
          await this.getTopten(this.startDay, this.endDay, this.shopID, this.selectFilter)
        } else if (this.dates.length === 1) {
          this.selectFilter = 'day'
          await this.getDataTable(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
          await this.getGraphData(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
          await this.getTopSold(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
          await this.getTopten(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        } else if (this.dates.length === 2) {
          this.selectFilter = 'day'
          await this.getDataTable(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
          await this.getGraphData(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
          await this.getTopSold(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
          await this.getTopten(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        }
      }
    },
    changeChartsOptions (val) {
      this.chartOptions = {
        chart: {
          toolbar: {
            show: false
          }
        },
        stroke: {
          show: this.series[0].data.length > 1
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          tickPlacement: 'between',
          categories: this.showMonthDropdown ? val : this.showDatePicker ? val : [`${this.$t('DashboardAffiliate.Jan')}`, `${this.$t('DashboardAffiliate.Feb')}`, `${this.$t('DashboardAffiliate.Mar')}`, `${this.$t('DashboardAffiliate.Apr')}`, `${this.$t('DashboardAffiliate.May')}`, `${this.$t('DashboardAffiliate.Jun')}`, `${this.$t('DashboardAffiliate.Jul')}`, `${this.$t('DashboardAffiliate.Aug')}`, `${this.$t('DashboardAffiliate.Sep')}`, `${this.$t('DashboardAffiliate.Oct')}`, `${this.$t('DashboardAffiliate.Nov')}`, `${this.$t('DashboardAffiliate.Dec')}`]
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              return value.toFixed(2)
            }
          }
        },
        colors: ['#0096FF'],
        tooltip: {
          y: {
            formatter: function (value) {
              return value.toFixed(2)
            }
          }
        }
      }
      this.chartOptionsClick = {
        chart: {
          toolbar: {
            show: false
          }
        },
        stroke: {
          show: this.seriesClick[0].data.length > 1
        },
        markers: {
          size: 5,
          colors: '#AE8FF7'
        },
        xaxis: {
          tickPlacement: 'between',
          categories: this.showMonthDropdown ? val : this.showDatePicker ? val : [`${this.$t('DashboardAffiliate.Jan')}`, `${this.$t('DashboardAffiliate.Feb')}`, `${this.$t('DashboardAffiliate.Mar')}`, `${this.$t('DashboardAffiliate.Apr')}`, `${this.$t('DashboardAffiliate.May')}`, `${this.$t('DashboardAffiliate.Jun')}`, `${this.$t('DashboardAffiliate.Jul')}`, `${this.$t('DashboardAffiliate.Aug')}`, `${this.$t('DashboardAffiliate.Sep')}`, `${this.$t('DashboardAffiliate.Oct')}`, `${this.$t('DashboardAffiliate.Nov')}`, `${this.$t('DashboardAffiliate.Dec')}`]
        },
        yaxis: {
          labels: {
            formatter: function (value) {
              return value.toFixed(0)
            }
          }
        },
        colors: ['#FF0000'],
        tooltip: {
          y: {
            formatter: function (value) {
              return value.toFixed(0)
            }
          }
        }
      }
    },
    // changeChartsOptionsClick (val) {
    //   this.chartOptionsClick = {
    //     chart: {
    //       toolbar: {
    //         show: false
    //       }
    //     },
    //     markers: {
    //       size: 5,
    //       colors: '#AE8FF7'
    //     },
    //     xaxis: {
    //       tickPlacement: 'between',
    //       categories: this.showMonthDropdown ? val : this.showDatePicker ? val : ['มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน', 'กรกฏาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม']
    //     },
    //     colors: ['#FF0000']
    //   }
    // },
    async getGraphData (startDate, endDate, shopID, selectFilter) {
      this.$store.commit('openLoader')
      var data = {
        start: startDate,
        end: endDate,
        filter: selectFilter,
        role_user: 'ext_buyer',
        seller_shop_id: shopID
      }
      await this.$store.dispatch('actionGraph', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateGraph
      if (response.message === 'This user is Unauthorized') {
        this.$EventBus.$emit('refreshToken')
      } else {
        if (response.ok === 'y') {
          this.series = []
          this.seriesClick = []
          this.series = await response.query_result.response
          this.totalQuantity = response.query_result.total_click
          this.totalCommissions = response.query_result.total_commission_receive
          var newSeries = []
          var newSeriesClick = []
          for (let i = 0; i < this.series.length; i++) {
            newSeries.push(this.series[i].revenue.influencer_commission)
            newSeriesClick.push(this.series[i].revenue.total_click)
          }
          var dayInMonth = []
          for (let i = 0; i < this.series.length; i++) {
            dayInMonth.push(new Date(response.query_result.response[i].date).toLocaleDateString(`${this.$t('DashboardAffiliate.typeLanguage')}`, { month: 'long', day: 'numeric' }))
          }
          var dateToDate = []
          for (let i = 0; i < this.series.length; i++) {
            dateToDate.push(new Date(response.query_result.response[i].date).toLocaleDateString(`${this.$t('DashboardAffiliate.typeLanguage')}`, { year: 'numeric', month: 'long', day: 'numeric' }))
          }
          this.series = [{ name: `${this.$t('DashboardAffiliate.textSeries')}`, data: newSeries }]
          this.seriesClick = [{ name: `${this.$t('DashboardAffiliate.textSeriesClick')}`, data: newSeriesClick }]
          if (this.showMonthDropdown) {
            this.changeChartsOptions(dayInMonth)
          } else if (this.showDatePicker) {
            this.changeChartsOptions(dateToDate)
          } else {
            this.changeChartsOptions(false)
          }
          this.$store.commit('closeLoader')
        } else {
          this.series = []
          this.seriesClick = []
          newSeries = []
          newSeriesClick = []
          dateToDate = []
          dateToDate = []
          this.$store.commit('closeLoader')
        }
      }
    },
    async getShopData () {
      await this.$store.dispatch('actionShopList')
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateShoplist
      if (response.ok === 'y') {
        var statAllShop = [{ name: `${this.$t('DashboardAffiliate.allShop')}`, id: -1 }]
        this.selectShop = response.query_result.shops
        this.selectShop = statAllShop.concat(this.selectShop)
      }
    },
    async getDataTable (startDate, endDate, shopID, selectFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: selectFilter,
        role_user: 'ext_buyer',
        seller_shop_id: shopID,
        pages: 1,
        count: -1,
        search: ''
      }
      await this.$store.dispatch('actionDataTable', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateDataTable
      if (response.ok === 'y') {
        this.tableData = response.query_result.affiliateList
        this.tableData.sort((a, b) => {
          if (a.status === 'Active' && b.status !== 'Active') return -1
          if (a.status !== 'Active' && b.status === 'Active') return 1
          return 0
        })
      }
    },
    async getDataTableCommission (startDate, endDate, shopID, selectFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: selectFilter,
        role_user: 'ext_buyer',
        seller_shop_id: shopID,
        pages: 1,
        count: -1,
        search: ''
      }
      await this.$store.dispatch('actionCommissionTable', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateCommissionTable
      if (response.ok === 'y') {
        this.tableDataCom = response.query_result.affiliateList
      }
    },
    async getTopten (startDate, endDate, shopID, selectFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: selectFilter,
        role_user: 'ext_buyer',
        seller_shop_id: shopID
      }
      await this.$store.dispatch('actionTopTen', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateTopTen
      if (response.ok === 'y') {
        this.topItem = response.query_result.topclicked
      }
    },
    async getTopSold (startDate, endDate, shopID, selectFilter) {
      var data = {
        start: startDate,
        end: endDate,
        filter: selectFilter,
        role_user: 'ext_buyer',
        seller_shop_id: shopID
      }
      await this.$store.dispatch('actionTopSold', data)
      var response = await this.$store.state.ModuleDashboardAffiliateBuyer.stateTopSold
      if (response.ok === 'y') {
        this.topSold = response.query_result.topclicked
      }
    },
    async onDropdownSelected (choice) {
      // this.rangeDate = this.startDay
      // console.log(this.rangeDate, 'rangeDate')
      // console.log(this.startDay, 'startDay')
      const monthNames = [
        `${this.$t('DashboardAffiliate.Jan')}`, `${this.$t('DashboardAffiliate.Feb')}`,
        `${this.$t('DashboardAffiliate.Mar')}`, `${this.$t('DashboardAffiliate.Apr')}`,
        `${this.$t('DashboardAffiliate.May')}`, `${this.$t('DashboardAffiliate.Jun')}`,
        `${this.$t('DashboardAffiliate.Jul')}`, `${this.$t('DashboardAffiliate.Aug')}`,
        `${this.$t('DashboardAffiliate.Sep')}`, `${this.$t('DashboardAffiliate.Oct')}`,
        `${this.$t('DashboardAffiliate.Nov')}`, `${this.$t('DashboardAffiliate.Dec')}`
      ]
      var MonthOnSelected = new Date().getMonth() + 1
      this.dates = [this.startDay]
      this.selectedYear = new Date().getFullYear()
      this.selectedYearMobile = new Date().getFullYear()
      this.selectedMonthValueMobile = null
      this.selectedMonthMobile = `${monthNames[MonthOnSelected - 1]}`
      this.selectedMonth = `${monthNames[MonthOnSelected - 1]}`
      this.selectedMonthValue = null
      this.selectedDropdown = choice
      var start = `${this.selectedYear}-01-01`
      var end = `${this.selectedYear}-12-31`
      if (choice === `${this.$t('DashboardAffiliate.yearly')}`) {
        this.selectFilter = 'year'
        this.showYearDropdown = true
        this.showMonthDropdown = false
        this.showDatePicker = false
        await this.getDataTable(start, end, this.shopID, this.selectFilter)
        await this.getGraphData(start, end, this.shopID, this.selectFilter)
        await this.getTopSold(start, end, this.shopID, this.selectFilter)
        await this.getDataTableCommission(start, end, this.shopID, this.selectFilter)
        await this.getTopten(start, end, this.shopID, this.selectFilter)
      } else if (choice === `${this.$t('DashboardAffiliate.monthly')}`) {
        this.selectFilter = 'month'
        this.showYearDropdown = true
        this.showMonthDropdown = true
        this.showDatePicker = false
        await this.getDataTable(this.startMonth, this.endMonth, this.shopID, this.selectFilter)
        await this.getGraphData(this.startMonth, this.endMonth, this.shopID, this.selectFilter)
        await this.getTopSold(this.startMonth, this.endMonth, this.shopID, this.selectFilter)
        await this.getDataTableCommission(this.startMonth, this.endMonth, this.shopID, this.selectFilter)
        await this.getTopten(this.startMonth, this.endMonth, this.shopID, this.selectFilter)
      } else {
        this.selectFilter = 'day'
        this.showYearDropdown = false
        this.showMonthDropdown = false
        this.showDatePicker = true
        await this.getDataTable(this.startDay, this.endDay, this.shopID, this.selectFilter)
        await this.getGraphData(this.startDay, this.endDay, this.shopID, this.selectFilter)
        await this.getTopSold(this.startDay, this.endDay, this.shopID, this.selectFilter)
        await this.getDataTableCommission(this.startDay, this.endDay, this.shopID, this.selectFilter)
        await this.getTopten(this.startDay, this.endDay, this.shopID, this.selectFilter)
      }
    },
    async onMonthSelected (month) {
      this.selectedMonth = month.month
      this.selectedMonthValue = month.value
      this.selectedMonthValueStart = `${this.selectedYear}-${month.value}-01`
      this.selectedMonthValueEnd = `${this.selectedYear}-${month.value}-31`
      this.selectFilter = 'month'
      await this.getDataTable(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
      await this.getGraphData(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
      await this.getTopSold(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
      await this.getDataTableCommission(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
      await this.getTopten(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
    },
    async onYearSelected (year) {
      this.selectedYear = year
      if (this.selectFilter === 'year') {
        const start = `${this.selectedYear}-01-01`
        const end = `${this.selectedYear}-12-31`
        this.yearStart = start
        this.yearEnd = end
        this.selectFilter = 'year'
        await this.getDataTable(start, end, this.shopID, this.selectFilter)
        await this.getGraphData(start, end, this.shopID, this.selectFilter)
        await this.getTopSold(start, end, this.shopID, this.selectFilter)
        await this.getDataTableCommission(start, end, this.shopID, this.selectFilter)
        await this.getTopten(start, end, this.shopID, this.selectFilter)
      }
      if (this.selectFilter === 'month') {
        if (this.selectedMonthValue === null) {
          this.selectFilter = 'month'
          var MonthOnSelected = new Date().getMonth() + 1
          this.selectedMonthValueStart = `${this.selectedYear}-${MonthOnSelected}-01`
          this.selectedMonthValueEnd = `${this.selectedYear}-${MonthOnSelected}-31`
          await this.getDataTable(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getGraphData(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopSold(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopten(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
        } else {
          this.selectedMonthValueStart = `${this.selectedYear}-${this.selectedMonthValue}-01`
          this.selectedMonthValueEnd = `${this.selectedYear}-${this.selectedMonthValue}-31`
          // เรียกใช้งาน getDataTable และ getGraphData โดยใช้ selectFilter เป็น 'month'
          this.selectFilter = 'month'
          await this.getDataTable(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getGraphData(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopSold(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getDataTableCommission(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
          await this.getTopten(this.selectedMonthValueStart, this.selectedMonthValueEnd, this.shopID, this.selectFilter)
        }
      }
    },
    async saveDialog (date) {
      date.sort((a, b) => a.localeCompare(b))
      // console.log(date)
      this.dates = date
      if (this.dates.length === 1 && this.datesMobile.length === 0) {
        this.modalDateSelect = false
        await this.getDataTable(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getGraphData(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getTopSold(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getDataTableCommission(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getTopten(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      } else if (this.dates.length === 2 && this.datesMobile.length === 0) {
        this.modalDateSelect = false
        await this.getDataTable(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getGraphData(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getTopSold(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getDataTableCommission(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getTopten(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
      } else if (this.datesMobile.length === 1) {
        await this.getDataTable(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getGraphData(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getTopSold(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getDataTableCommission(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
        await this.getTopten(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      } else if (this.datesMobile.length === 2) {
        await this.getDataTable(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getGraphData(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getTopSold(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getDataTableCommission(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
        await this.getTopten(this.dates[0], this.dates[1], this.shopID, this.selectFilter)
      }
    },
    async closeDialog (date) {
      date = [this.startDay]
      this.dates = date
      this.modalDateSelect = false
      await this.getDataTable(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      await this.getGraphData(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      await this.getTopSold(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      await this.getDataTableCommission(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      await this.getTopten(this.dates[0], this.dates[0], this.shopID, this.selectFilter)
      // console.log('this date = ', this.dates)
    },
    closeDialogDatesMobile (date) {
      date = []
      this.datesMobile = date
      this.modalDateSelect = false
    },
    closeDialogMobile () {
      this.openDialogMobile = false
    },
    clearDialog () {
      const monthNames = [
        `${this.$t('DashboardAffiliate.Jan')}`, `${this.$t('DashboardAffiliate.Feb')}`,
        `${this.$t('DashboardAffiliate.Mar')}`, `${this.$t('DashboardAffiliate.Apr')}`,
        `${this.$t('DashboardAffiliate.May')}`, `${this.$t('DashboardAffiliate.Jun')}`,
        `${this.$t('DashboardAffiliate.Jul')}`, `${this.$t('DashboardAffiliate.Aug')}`,
        `${this.$t('DashboardAffiliate.Sep')}`, `${this.$t('DashboardAffiliate.Oct')}`,
        `${this.$t('DashboardAffiliate.Nov')}`, `${this.$t('DashboardAffiliate.Dec')}`
      ]
      var MonthOnSelected = new Date().getMonth() + 1
      this.dates = [this.startDay]
      this.selectedYearMobile = new Date().getFullYear()
      this.selectedMonthMobile = `${monthNames[MonthOnSelected - 1]}`
      this.selectedMonthValueMobile = null
    },
    exportExcelCommission () {
      var start = this.startDate
      var end = this.endDate
      if (this.selectFilter === 'year') {
        if (this.selectedYear === null) {
          this.getReportExcel(start, end, this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        } else {
          var startDate = `${this.selectedYear}-01-01`
          var endDate = `${this.selectedYear}-12-31`
          this.getReportExcel(startDate, endDate, this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        }
      }
      if (this.selectFilter === 'month') {
        if (this.selectedMonthValue === null) {
          var MonthOnSelected = new Date().getMonth() + 1
          var startMonth = `${this.selectedYear}-${MonthOnSelected}-01`
          var endMonth = `${this.selectedYear}-${MonthOnSelected}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        } else {
          startMonth = `${this.selectedYear}-${this.selectedMonthValue}-01`
          endMonth = `${this.selectedYear}-${this.selectedMonthValue}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        }
      }
      if (this.selectFilter === 'day') {
        if (this.dates.length === 0) {
          this.getReportExcel(this.startDay, this.endDay, this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        }
        if (this.dates.length === 1) {
          this.getReportExcel(this.dates[0], this.dates[0], this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        } else if (this.dates.length === 2) {
          this.getReportExcel(this.dates[0], this.dates[1], this.shopID, this.selectFilter, 'commission_buyer', `${this.$t('DashboardAffiliate.excelCommission')}`)
        }
      }
    },
    exportExcelTop () {
      var start = this.startDate
      var end = this.endDate
      if (this.selectFilter === 'year') {
        if (this.selectedYear === null) {
          this.getReportExcel(start, end, this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        } else {
          var startDate = `${this.selectedYear}-01-01`
          var endDate = `${this.selectedYear}-12-31`
          this.getReportExcel(startDate, endDate, this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        }
      }
      if (this.selectFilter === 'month') {
        if (this.selectedMonthValue === null) {
          var MonthOnSelected = new Date().getMonth() + 1
          var startMonth = `${this.selectedYear}-${MonthOnSelected}-01`
          var endMonth = `${this.selectedYear}-${MonthOnSelected}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        } else {
          startMonth = `${this.selectedYear}-${this.selectedMonthValue}-01`
          endMonth = `${this.selectedYear}-${this.selectedMonthValue}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        }
      }
      if (this.selectFilter === 'day') {
        if (this.dates.length === 0) {
          this.getReportExcel(this.startDay, this.endDay, this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        }
        if (this.dates.length === 1) {
          this.getReportExcel(this.dates[0], this.dates[0], this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        } else if (this.dates.length === 2) {
          this.getReportExcel(this.dates[0], this.dates[1], this.shopID, this.selectFilter, 'topten_buyer', `${this.$t('DashboardAffiliate.excelTopProducts')}`)
        }
      }
    },
    exportExcel () {
      var start = this.startDate
      var end = this.endDate
      if (this.selectFilter === 'year') {
        if (this.selectedYear === null) {
          this.getReportExcel(start, end, this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        } else {
          var startDate = `${this.selectedYear}-01-01`
          var endDate = `${this.selectedYear}-12-31`
          this.getReportExcel(startDate, endDate, this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        }
      }
      if (this.selectFilter === 'month') {
        if (this.selectedMonthValue === null) {
          var MonthOnSelected = new Date().getMonth() + 1
          var startMonth = `${this.selectedYear}-${MonthOnSelected}-01`
          var endMonth = `${this.selectedYear}-${MonthOnSelected}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        } else {
          startMonth = `${this.selectedYear}-${this.selectedMonthValue}-01`
          endMonth = `${this.selectedYear}-${this.selectedMonthValue}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        }
      }
      if (this.selectFilter === 'day') {
        if (this.dates.length === 0) {
          this.getReportExcel(this.startDay, this.endDay, this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        }
        if (this.dates.length === 1) {
          this.getReportExcel(this.dates[0], this.dates[0], this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        } else if (this.dates.length === 2) {
          this.getReportExcel(this.dates[0], this.dates[1], this.shopID, this.selectFilter, 'productlist_buyer', `${this.$t('DashboardAffiliate.allLinks')}`)
        }
      }
    },
    exportExcelSold () {
      var start = this.startDate
      var end = this.endDate
      if (this.selectFilter === 'year') {
        if (this.selectedYear === null) {
          this.getReportExcel(start, end, this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        } else {
          var startDate = `${this.selectedYear}-01-01`
          var endDate = `${this.selectedYear}-12-31`
          this.getReportExcel(startDate, endDate, this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        }
      }
      if (this.selectFilter === 'month') {
        if (this.selectedMonthValue === null) {
          var MonthOnSelected = new Date().getMonth() + 1
          var startMonth = `${this.selectedYear}-${MonthOnSelected}-01`
          var endMonth = `${this.selectedYear}-${MonthOnSelected}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        } else {
          startMonth = `${this.selectedYear}-${this.selectedMonthValue}-01`
          endMonth = `${this.selectedYear}-${this.selectedMonthValue}-31`
          this.getReportExcel(startMonth, endMonth, this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        }
      }
      if (this.selectFilter === 'day') {
        if (this.dates.length === 0) {
          this.getReportExcel(this.startDay, this.endDay, this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        }
        if (this.dates.length === 1) {
          this.getReportExcel(this.dates[0], this.dates[0], this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        } else if (this.dates.length === 2) {
          this.getReportExcel(this.dates[0], this.dates[1], this.shopID, this.selectFilter, 'toptensolds_buyer', `${this.$t('DashboardAffiliate.topSeller')}`)
        }
      }
    },
    async getReportExcel (startDate, endDate, shopID, selectFilter, exportDashboard, fileName) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        start: startDate,
        end: endDate,
        filter: selectFilter,
        role_user: 'ext_buyer',
        exportdashboard: exportDashboard,
        seller_shop_id: shopID,
        pages: 1,
        count: -1,
        search: '',
        transaction_status: ''
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}affiliate_dashboard/exportAffiliateDashboard`,
        data,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        responseType: 'blob'
      }).then((response) => {
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', fileName + '.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    openDialogDetail (item) {
      this.openDialog = true
      this.order_Detail = item.attribute_list
      // console.log('this.order_details', this.order_Detail)
    },
    closeDialogDetail () {
      this.openDialog = false
    },
    openDialogCommission (item) {
      this.openDialogCom = true
      this.order_DetailCom = item.attribute_list
    },
    closeDialogCommision () {
      this.openDialogCom = false
    },
    selectTabClick (item) {
      this.selectTab = item
      // console.log('this.selectTab', this.selectTab)
    },
    linkStatus (status) {
      if (status === 'Active') {
        return `${this.$t('DashboardAffiliate.linkStatusAC')}`
      } else if (status === 'In Active') {
        return `${this.$t('DashboardAffiliate.linkStatusIn')}`
      }
    },
    backgroundStatus (status) {
      if (status === 'Active') {
        return '#f0f9ee'
      } else if (status === 'In Active') {
        return '#f9eeee'
      }
    },
    statusColor (status) {
      if (status === 'Active') {
        return '#4CAF50'
      } else if (status === 'In Active') {
        return '#F44336'
      }
    },
    copyLink (link) {
      navigator.clipboard.writeText(link).then(() => {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: `${this.$t('DashboardAffiliate.textLinkCopy')}`
        })
      })
    },
    onYearSelectedMobile (year) {
      this.selectedYearMobile = year
    },
    onMonthSelectedMobile (month) {
      this.selectedMonthMobile = month.month
      this.selectedMonthValueMobile = month
    },
    saveDialogMobile (date) {
      this.datesMobile = date
      this.modalDateSelect = false
    },
    async submitFilter () {
      if (this.selectFilter === 'year') {
        this.onYearSelected(this.selectedYearMobile)
        this.openDialogMobile = false
      }
      if (this.selectFilter === 'month') {
        if (this.selectedMonthValueMobile === null) {
          var MonthOnSelected = new Date().getMonth() + 1
          var startMonth = `0${MonthOnSelected}`
          for (let i = 0; i < this.months.length; i++) {
            if (this.months[i].value === startMonth) {
              this.selectedMonthValueMobile = this.months[i]
              break
            }
          }
          this.selectedYear = this.selectedYearMobile
          this.onMonthSelected(this.selectedMonthValueMobile)
          this.openDialogMobile = false
        } else if (this.selectedMonthValueMobile) {
          this.selectedYear = this.selectedYearMobile
          this.onMonthSelected(this.selectedMonthValueMobile)
          this.openDialogMobile = false
        }
      }
      if (this.selectFilter === 'day') {
        if (this.datesMobile.length === 0) {
          this.datesMobile = [this.startDay]
          this.saveDialog(this.datesMobile)
          this.openDialogMobile = false
        } else {
          // console.log('here')
          // console.log(this.datesMobile, 'datesMobile')
          this.saveDialog(this.datesMobile)
          this.openDialogMobile = false
        }
      }
      // this.onMonthSelected(this.selectedMonthValueMobile)
    },
    async checkConsent () {
      this.$store.commit('openLoader')
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const data = {
        user_id: onedata.user.user_id
      }
      await this.$store.dispatch('actionsAffiliateConsent', data)
      var response = await this.$store.state.ModuleAffiliate.stateAffiliateConsent
      if (response) {
        if (response.isBuyer === '0') {
          if (this.MobileSize) {
            this.$router.push({ path: '/consentAffiliateMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/consentAffiliate' }).catch(() => {})
          }
        }
      }
      this.$store.commit('closeLoader')
    }
  }
}
</script>

<style scoped>
.custom-table {
  text-align: center;
}
.one-lines {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C
}
.exportButtonText {
  font-size: 14px;
  font-style: normal;
  font-weight: 500;
  color: #FFFFFF
}
.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}
.custom-text-field {
  height: 37px;
  width: 280px;
}
.my-input.v-input .v-input__slot {
  padding: 0 10px;
}
.subTitleText {
  font-size: 16px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C;
}
.avatar-image {
  max-width: 100%;
  max-height: 100%;
  width: auto;
  height: auto;
}
.no-word-break {
  word-break: normal;
}
.nav-link:focus {
  outline: none !important;
}
.nav-tabs .nav-link {
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
}
.grey-tab {
  color: #989898; /* Set the text color to grey */
  /* border-color: #dee2e6; Set the border color to grey */
  font-size: 16px;
  font-weight: 500;
}
.subTitleTextTab {
  /* background-color: transparent; */
  font-size: 16px;
  /* border-color: #dee2e6; */
  font-weight: 500;
  color: #27AB9C
}
.subTitleTextTabButton {
  border-color: #dee2e6;
  border-bottom: none;
}
.grey-tabButton {
  background-color: #F9FAFD;
  border-color: transparent; /* Set the border color to grey */
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
