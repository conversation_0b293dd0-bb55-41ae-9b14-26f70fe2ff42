<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการ ERP</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการ ERP</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col v-if="showDataERP" cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="searchERP" placeholder="ค้นหารายการ ERP" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col v-if="showDataShop" cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="searchShop" placeholder="ค้นหาร้านค้า" outlined rounded dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- เพิ่มรายการ ERP -->
      <div v-if="showDataERP">
        <v-row v-if="!MobileSize">
            <v-col cols="6">
            <span style="font-weight: 600; font-size: 18px; line-height: 16px; color: #333333;">รายการ ERP</span>
            </v-col>
            <v-col cols="6" style="display: flex; justify-content: flex-end;">
            <v-btn color="primary" small @click="openDivCreate()" ><span style="font-size: 15px !important;">Add Partner ERP</span></v-btn>
            </v-col>
        </v-row>
        <v-row v-else class="mx-1">
            <v-col cols="6">
            <span style="font-weight: 600; color: #333333;">รายการ ERP</span>
            </v-col>
            <v-col cols="6" style="display: flex; justify-content: flex-end;">
            <v-btn color="primary" small @click="openDivCreate()" ><span style="font-size: 12px !important;">Add Partner ERP</span></v-btn>
            </v-col>
        </v-row>
        <v-row class="mx-2 mt-1" v-if="!MobileSize && !IpadSize">
          <v-col cols="12" v-if="divCreate" style="border: 1px solid rgba(0, 0, 0, 0.12); border-radius: 10px; margin: 2% 0;">
            <v-row class="ml-3">
              <v-col cols="1"></v-col>
              <v-col cols="10" style="margin-left: -12px; margin-bottom: -12px;">
              <v-card-title style="margin-left: -12px; margin-bottom: -7px; font-size: 100%;">ชื่อพาร์ทเนอร์</v-card-title>
              <v-text-field v-model="service_name" outlined @keyup.enter="addDataERP" placeholder="กรอกข้อมูล" dense :rules="[validateService]"></v-text-field>
              </v-col>
              <v-col cols="1"></v-col>
            </v-row>
            <v-row class="ml-3">
              <v-col cols="1"></v-col>
              <v-col cols="10" style="display: flex; justify-content: space-between; margin: -12px;">
              <div style="width: 45%;">
                <v-card-title style="margin-left: -12px; margin-bottom: -7px; font-size: 100%;">Code</v-card-title>
                <v-text-field v-model="service_code" outlined @keyup.enter="addDataERP" placeholder="กรอกข้อมูล" dense :rules="[validateService]"></v-text-field>
              </div>
              <div style="width: 45%;">
                <v-card-title style="margin-left: -12px; margin-bottom: -7px; font-size: 100%;">เลขประจำตัวผู้เสียภาษี</v-card-title>
                <v-text-field v-model="tax_id" outlined @keyup.enter="addDataERP" maxlength="13" placeholder="กรอกข้อมูล" :rules="[validateTaxID]"
                @input="validateInput" dense></v-text-field>
              </div>
              </v-col>
              <v-col cols="1"></v-col>
            </v-row>
            <v-row class="mt-2">
              <v-col cols="12" style="display: flex; justify-content: center; gap: 5%;">
              <v-btn @click="divCreate = false" color="primary" text small><span>Cancel</span></v-btn>
              <v-btn @click="addDataERP()" color="primary" small><span>Save</span></v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row class="mx-2 mt-2" v-else>
          <v-col cols="12" v-if="divCreate" style="border: 1px solid rgba(0, 0, 0, 0.12); border-radius: 10px; margin: 2% 0;">
            <v-row class="ml-3">
              <v-col cols="12" style="margin: -7px 0 -12px -12px;">
              <v-card-title style="margin: -12px 0 -7px -12px; font-size: 100%;">ชื่อพาร์ทเนอร์</v-card-title>
              <v-text-field v-model="service_name" outlined @keyup.enter="addDataERP" placeholder="กรอกข้อมูล" dense :rules="[validateService]"></v-text-field>
              </v-col>
            </v-row>
            <v-row class="ml-3">
              <v-col cols="12" style="margin: -12px 0 -12px -12px;">
              <v-card-title style="margin: -12px 0 -7px -12px; font-size: 100%;">Code</v-card-title>
              <v-text-field v-model="service_code" outlined @keyup.enter="addDataERP" placeholder="กรอกข้อมูล" dense :rules="[validateService]"></v-text-field>
              </v-col>
            </v-row>
            <v-row class="ml-3">
              <v-col cols="12" style="margin: -12px 0 -12px -12px;">
                <v-card-title style="margin: -12px 0 -7px -12px; font-size: 100%;">เลขประจำตัวผู้เสียภาษี</v-card-title>
                <v-text-field v-model="tax_id" outlined @keyup.enter="addDataERP" maxlength="13" placeholder="กรอกข้อมูล" :rules="[validateTaxID]"
                @input="validateInput" dense></v-text-field>
              </v-col>
            </v-row>
            <v-row class="mt-2">
              <v-col cols="12" style="display: flex; justify-content: center; gap: 5%;">
              <v-btn @click="divCreate = false" color="primary" text small><span>Cancel</span></v-btn>
              <v-btn @click="addDataERP()" color="primary" small><span>Save</span></v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <!-- ตารางรายการ ERP -->
        <v-row dense>
          <v-col cols="12">
              <v-data-table
              :headers="headers"
              :search="searchERP"
              :items="showData"
              style="width:100%; text-align: center; white-space: nowrap;"
              height="100%"
              :page.sync="page"
              @pagination="countRequest"
              no-results-text="ไม่พบรายการ ERP"
              no-data-text="ไม่พบรายการ ERP"
              @update:items-per-page="itemsPerPage"
              :items-per-page="10"
              class="elevation-1 mt-4 erp-table"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              >
              <template v-slot:[`item.service_tax_id`]="{ item }">
                  <span v-if="item.service_tax_id" style="white-space: nowrap;">{{ item.service_tax_id }}</span>
                  <span v-else>-</span>
              </template>
              <template v-slot:[`item.service_access_token`]="{ item }">
                  <!-- <v-tooltip v-if="item.service_access_token" top :max-width="200" small> -->
                  <!-- <template v-slot:activator="{ on, attrs }"> -->
                      <v-btn color="primary" small @click="copyToClipboard(item.service_access_token)"><span style="font-size: 12px !important;">คัดลอก</span></v-btn>
                  <!-- </template> -->
                  <!-- <span style="font-size: 10px; cursor: pointer; white-space: normal; word-break: break-word;">{{ item.service_access_token }}</span> -->
                  <!-- </v-tooltip> -->
                  <!-- <span v-else>-</span> -->
              </template>
              <template v-slot:[`item.manage`]="{ item }">
                      <v-btn v-if="item.service_id && (item.service_shop_list && item.service_shop_list.length > 0)" color="primary" small @click="detailServiceShopList(item)"><span style="font-size: 12px !important;">เข้าร่วม</span></v-btn>
              </template>
              <template v-slot:[`item.service_inuse`]="{ item }">
                <div v-if="MobileSize">
                  <v-btn v-if="!item.service_inuse" color="primary" small @click="openEditDialog(item)">
                    <span style="font-size: 12px !important;">จัดการ</span>
                  </v-btn>
                </div>
                <div v-else style="display: flex; justify-content: flex-start;">
                  <v-btn v-if="!item.service_inuse" color="primary" icon small @click="openEditDialog(item)">
                    <v-icon small>mdi-dots-vertical</v-icon>
                  </v-btn>
                </div>
              </template>
              </v-data-table>
          </v-col>
        </v-row>
      </div>
      <!-- ตารางรายการร้านค้าที่เชื่อมกับ ERP -->
      <v-row dense v-if="showDataShop">
        <div style="display: flex; align-items: center;">
          <v-icon color="#27AB9C" class="mr-2" @click="toggle()">mdi-chevron-left</v-icon>
          <span style="font-weight: 600; font-size: 18px; line-height: 16px; color: #333333;" v-if="!MobileSize">รายการร้านค้า</span>
          <span style="font-weight: 600; color: #333333;" v-else>รายการร้านค้า</span>
        </div>
        <v-col cols="12">
          <v-data-table
            :headers="headerShop"
            :search="searchShop"
            :items="this.showDetail"
            style="width:100%; text-align: center;"
            height="100%"
            :page.sync="page"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการร้านค้า"
            no-data-text="ไม่พบรายการร้านค้า"
            @update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 mt-4 shop-table"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          >
            <template v-slot:[`item.shop_name`]="{ item }">
              <span v-if="item.shop_name" style="white-space: nowrap;">{{ item.shop_name }}</span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.shop_name_en`]="{ item }">
              <span v-if="item.shop_name_en" style="white-space: nowrap;">{{ item.shop_name_en }}</span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.shop_phones`]="{ item }">
              <div v-if="item.shop_phones">
                <div v-for="(items,index) in item.shop_phones" :key="index">
                <span style="white-space: nowrap;">{{ items.phone_type }} : </span>
                <span style="white-space: nowrap;">{{ items.phone_number }}</span>
                </div>
              </div>
              <span v-else>-</span>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
      <!-- btn save (ERP) -->
      <v-dialog v-model='openSuccess' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openSuccess = false">
        <v-card min-height='100%'>
          <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='openSuccess = false' icon><v-icon color='#BABABA'>mdi-close</v-icon></v-btn>
          </v-app-bar>
          </v-img>
          <v-container>
            <v-card-text style="text-align: center;">
                บันทึกสำเร็จ
            </v-card-text>
            <v-card-actions>
              <v-row dense class='d-flex justify-center' style="padding: 0 25%;">
                <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openSuccess = !openSuccess">ตกลง</v-btn>
              </v-row>
            </v-card-actions>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- edit ERP -->
      <v-dialog v-model='openEdit' :width="MobileSize ? '70%' : '40%'" persistent>
        <v-card min-height='100%'>
          <v-app-bar flat color="primary">
            <v-toolbar-title v-if="!MobileSize" style="font-size: 20px; color: white;">แก้ไขรายการ ERP</v-toolbar-title>
            <v-toolbar-title v-else style="font-size: 16px; color: white;">แก้ไขรายการ ERP</v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn plain fab small @click='openEdit = false' icon><v-icon color='white'>mdi-close</v-icon></v-btn>
          </v-app-bar>
          <v-container>
              <v-row dense class="mx-2 mt-1" width="100%">
                <v-col cols="12">
                  <v-text-field v-model="selectedItem.service_name" outlined label="ชื่อพาร์ทเนอร์" @keyup.enter="editERP" :rules="[validateService]"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field v-model="selectedItem.service_code" outlined label="Code" @keyup.enter="editERP" :rules="[validateService]"></v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-text-field v-model="selectedItem.service_tax_id" outlined label="เลขประจำตัวผู้เสียภาษี" maxlength="13" @keyup.enter="editERP" :rules="[validateTaxID]"></v-text-field>
                </v-col>
              </v-row>
            <v-card-actions>
              <v-row v-if="!MobileSize" dense class='d-flex justify-center mt-1' style="padding: 0 20%; gap: 10%;" width="100%">
                <v-btn class="ml-2" text color="primary" small :style="{ flex: '1' }" @click="openEdit = false"><span style="font-size: 12px;">Cancel</span></v-btn>
                <v-btn class="white--text ml-2" color="primary" small :style="{ flex: '1' }" @click="editERP()"><span style="font-size: 12px;">Save</span></v-btn>
              </v-row>
              <v-row v-else dense class='d-flex justify-center mt-1' style="padding: 0 10%; gap: 10%;" width="100%">
                <v-btn class="ml-2" text color="primary" small :style="{ flex: '1' }" @click="openEdit = false"><span style="font-size: 12px;">Cancel</span></v-btn>
                <v-btn class="white--text ml-2" color="primary" small :style="{ flex: '1' }" @click="editERP()"><span style="font-size: 12px;">Save</span></v-btn>
              </v-row>
            </v-card-actions>
          </v-container>
        </v-card>
      </v-dialog>
      <!-- dialog Copy -->
      <!-- <v-dialog v-model='dialogCopy' :width="MobileSize ? '50%' : '25%'" persistent @keydown.esc="dialogCopy = false">
        <v-card min-height='100%' style="background-color: #27AB9C;">
          <v-card-actions>
            <v-card-text style="text-align: center; color: white;">
              {{ dialogCopyText }}
            </v-card-text>
            <v-spacer></v-spacer>
            <v-btn plain fab x-small @click='dialogCopy = false' icon><v-icon color='white'>mdi-close</v-icon></v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog> -->
      <!-- dialog Error -->
      <v-dialog v-model='dialogError' :width="MobileSize ? '84%' : '40%'" persistent @keydown.esc="dialogError = false">
        <v-card min-height='100%' style="background-color: #ff5252;">
          <v-card-actions>
            <v-card-text style="text-align: center; color: white;">
              {{ dialogErrorText }}
            </v-card-text>
            <v-spacer></v-spacer>
            <v-btn plain fab x-small @click='dialogError = false' icon><v-icon color='white'>mdi-close</v-icon></v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      showData: [],
      showDetail: [],
      searchERP: '',
      searchShop: '',
      // dialogCopy: false,
      // dialogCopyText: '',
      dialogError: false,
      dialogErrorText: '',
      valTaxID: false,
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      showDataERP: true,
      showDataShop: false,
      divCreate: false,
      service_name: '',
      service_code: '',
      tax_id: '',
      access_token: '',
      btnDetail: false,
      openSuccess: false,
      openEdit: false,
      selectedItem: [],
      headers: [
        { text: 'ID', value: 'service_id', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อพาร์ทเนอร์', value: 'service_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'Code', value: 'service_code', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขประจำตัวผู้เสียภาษี', value: 'service_tax_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'Access Token', value: 'service_access_token', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ร้านค้าที่เข้าร่วม', value: 'manage', align: 'center', sortable: false, class: 'backgroundTable fontTable--text ' },
        { text: 'จัดการ', value: 'service_inuse', sortable: false, class: 'backgroundTable fontTable--text', fixed: true, right: true }
      ],
      headerShop: [
        { text: 'ID', value: 'shop_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'shop_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่ออังกฤษ', value: 'shop_name_en', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อไทย', value: 'shop_name_th', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เจ้าของ', value: 'shop_owner', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทร', value: 'shop_phones', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'shop_status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขประจำตัวผู้เสียภาษี', value: 'shop_tax_id', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageERPMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'ManageERP')
        this.$router.push({ path: '/ManageERP' }).catch(() => {})
      }
    }
  },
  created () {
    this.showDataERP = true
    this.showDataShop = false
    this.divCreate = false
    this.getData()
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    toggle () {
      this.showDataERP = !this.showDataERP
      this.showDataShop = !this.showDataShop
      // console.log('this.showDataERP', this.showDataERP)
      // console.log('this.showDataShop', this.showDataShop)
    },
    detailServiceShopList (data) {
      this.divCreate = false
      this.showDetail = data.service_shop_list || []
      // console.log('showDetail : ', this.showDetail)
      // console.log('this.showDetail.length : ', this.showDetail.length)
      this.toggle()
    },
    async copyToClipboard (text) {
      navigator.clipboard.writeText(text)
      await this.$swal.fire({
        showConfirmButton: false,
        timer: 2000,
        timerProgressBar: true,
        icon: 'success',
        html: '<h3>คัดลอกสำเร็จ</h3>'
      })
      // this.dialogCopyText = 'คัดลอกสำเร็จ'
      // this.dialogCopy = true
    },
    openDivCreate () {
      this.service_name = ''
      this.service_code = ''
      this.tax_id = ''
      this.divCreate = true
    },
    openEditDialog (item) {
      this.selectedItem = { ...item }
      this.openEdit = true
    },
    validateInput () {
      this.tax_id = this.tax_id.replace(/[^0-9]/g, '')
    },
    validateTaxID (value) {
      if (!value) {
        this.valTaxID = false
        return true
      }
      // ถ้ากรอก ต้องมีความยาวเท่ากับ 13 หลัก
      const regex = /^[0-9]*$/
      if (value === '-') {
        this.valTaxID = false
        return true
      }
      if (value && !regex.test(value)) {
        this.valTaxID = true
        return 'กรุณากรอกเฉพาะตัวเลข'
      }
      if (value.length !== 13) {
        this.valTaxID = true
        return 'กรุณากรอกเลข 13 หลัก'
      }
      if (!this.validNationalID(value)) {
        this.valTaxID = true
        return 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
      }
      this.valTaxID = false
      return true
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      let sum = 0
      for (let i = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      const mod = sum % 11
      const check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    validateService (value) {
      if (!value) {
        return 'กรุณากรอกข้อมูล'
      }
      return true
    },
    async getData () {
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END}api/admin_platform/erp/list_service_erp`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then(response => {
        this.showData = response.data.data.service_data
      })
      this.$store.commit('closeLoader')
      // console.log('showData---->', this.showData)
    },
    async addDataERP () {
      if (!this.service_name || !this.service_code) {
        this.dialogError = true
        this.dialogErrorText = 'กรุณากรอกข้อมูล Name และ Code'
        return
      }
      if (this.valTaxID === true) {
        this.dialogError = true
        this.dialogErrorText = 'กรุณากรอกข้อมูลเลขประจำตัวผู้เสียภาษีให้ถูกต้อง'
        return
      }
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.tax_id === '' || this.tax_id === null) {
        this.tax_id = '-'
      }
      const data = {
        payload: {
          service_name: this.service_name,
          service_code: this.service_code,
          tax_id: this.tax_id
        }
      }
      // console.log('data', data)
      try {
        const responseToken = await this.axios({
          url: `${process.env.VUE_APP_BACK_END}api/admin_platform/generate_jwt_token`,
          data: data,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST'
        })
        this.access_token = responseToken.data.data.access_token
        // console.log('access_token', this.access_token)
        const dataCreate = {
          service_name: this.service_name,
          service_code: this.service_code,
          tax_id: this.tax_id,
          access_token: this.access_token
        }
        // console.log('dataCreate', dataCreate)
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END}api/admin_platform/erp/create_service_erp`,
          data: dataCreate,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST'
        })
        this.getData()
        this.$store.commit('closeLoader')
        this.divCreate = false
        this.openSuccess = true
      } catch (error) {
        // console.log('error.response.status', error.response.status)
        if (error.response.status === 400) {
          this.$store.commit('closeLoader')
          this.divCreate = false
          this.dialogError = true
          this.dialogErrorText = 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'
        } else if (error.response.status === 409) {
          this.$store.commit('closeLoader')
          this.dialogError = true
          this.dialogErrorText = 'ข้อมูล Code ซ้ำ กรุณาลองใหม่อีกครั้ง'
        } else if (error.response.status === 500) {
          this.$store.commit('closeLoader')
          this.divCreate = false
          this.dialogError = true
          this.dialogErrorText = 'ขออภัยระบบขัดข้อง'
        }
      }
    },
    async editERP () {
      if (!this.selectedItem.service_name || !this.selectedItem.service_code) {
        this.dialogError = true
        this.dialogErrorText = 'กรุณากรอกข้อมูล Name และ Code'
        return
      }
      if (this.valTaxID === true) {
        this.dialogError = true
        this.dialogErrorText = 'กรุณากรอกข้อมูลเลขประจำตัวผู้เสียภาษีให้ถูกต้อง'
        return
      }
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.selectedItem.service_tax_id === '' || this.selectedItem.service_tax_id === null) {
        this.selectedItem.service_tax_id = '-'
      }
      const data = {
        payload: {
          service_name: this.selectedItem.service_name,
          service_code: this.selectedItem.service_code,
          tax_id: this.selectedItem.service_tax_id
        }
      }
      // console.log('data', data)
      try {
        const responseToken = await this.axios({
          url: `${process.env.VUE_APP_BACK_END}api/admin_platform/generate_jwt_token`,
          data: data,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST'
        })
        this.access_token = responseToken.data.data.access_token
        // console.log('access_token', this.access_token)
        const dataEdit = {
          service_name: this.selectedItem.service_name,
          service_code: this.selectedItem.service_code,
          tax_id: this.selectedItem.service_tax_id,
          access_token: this.access_token
        }
        // console.log('dataEdit', dataEdit)
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END}api/admin_platform/update_service_erp/${this.selectedItem.service_id}`,
          data: dataEdit,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST'
        })
        this.getData()
        this.$store.commit('closeLoader')
        this.openEdit = false
        this.openSuccess = true
      } catch (error) {
        if (error.response.status === 400) {
          this.$store.commit('closeLoader')
          this.openEdit = false
          this.dialogError = true
          this.dialogErrorText = 'เกิดข้อผิดพลาด กรุณาลองใหม่อีกครั้ง'
        } else if (error.response.status === 409) {
          this.$store.commit('closeLoader')
          this.dialogError = true
          this.dialogErrorText = 'ข้อมูล Code ซ้ำ กรุณาลองใหม่อีกครั้ง'
        } else if (error.response.status === 500) {
          this.$store.commit('closeLoader')
          this.openEdit = false
          this.dialogError = true
          this.dialogErrorText = 'ขออภัยระบบขัดข้อง'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.erp-table {
::v-deep table {
  tbody {
  tr {
      td:nth-child(7) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 21;
      background: white;
      }
  }
  }
  thead {
  tr {
      th {
        white-space: nowrap;
      }
      th:nth-child(1) {
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      z-index: 10;
      background: white;
      }
  }
  }
  thead {
  tr {
      th:nth-child(7) {
      text-align: center;
      z-index: 16;
      background: white;
      position: sticky !important;
      position: -webkit-sticky !important;
      right: 0;
      }
  }
  }
}}
.shop-table {
::v-deep table {
  thead {
  tr {
      th {
        white-space: nowrap;
      }
  }
  }
}}
</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
font-size: 0.62rem;
}
</style>
