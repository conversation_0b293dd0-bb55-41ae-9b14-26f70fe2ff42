<template>
  <div>
    <!-- Website -->
    <v-app-bar
     v-if="!MobileSize && !IpadSize"
     class="backgroundAppBar"
     app
     height="100px"
     color="#FFFFFF"
     elevate-on-scroll
     elevation="1"
     style="z-index: 12 !important; position: fixed;"
     :style="{'max-width': MobileSize ? '100vw' : '100%'}"
    >
      <v-col cols="12">
        <v-toolbar-title :style="IpadProSize ? '' : 'padding-left: 94px;'" class="d-flex justify-space-between">
          <!-- logo -->
          <router-link v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :to="{ pathShop }">
            <v-img :class="IpadProSize ? 'pr-4' : ''" v-lazyload :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
          />
          </router-link>
          <router-link v-else :to="{ path: pathHome }">
            <v-img v-lazyload :class="IpadProSize ? 'pr-4' : ''" :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
              @mousedown.right="goHomeRightClick()" />
          </router-link>
          <div class="d-flex" style="max-width: 694px; width: 100%; align-items: center; gap: 12px;">
            <v-text-field v-model="searchProductText" v-if="checkPath === 'shoppage' || checkPath === 'searchProduct'" rounded class="custom-placeholer-color textFieldStyle" :placeholder="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV' ? $t('AppBar.SearchNoJV'): $t('AppBar.SearchJV')" hide-details @keyup.enter="searchProduct()">
              <template v-slot:append>
                <v-btn class="search-btn" height="48" @click="searchProduct()" elevation="0">
                  <v-icon color="white" size="28">mdi-magnify</v-icon>
                </v-btn>
              </template>
            </v-text-field>
            <v-menu
              v-model="menuSearch"
              :close-on-content-click="false"
              offset-y
              v-else
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field v-model="searchtext" v-bind="attrs" v-on="on" rounded class="custom-placeholer-color textFieldStyle" :placeholder="$t('AppBar.Search')" hide-details @keydown.down.prevent="moveDown" @keydown.up.prevent="moveUp" @keydown.enter.prevent="selectHighlighted" @keyup="handleSearchAI">
                  <template v-slot:append>
                    <v-btn class="search-btn" height="48" @click="searchdata()" elevation="0">
                      <v-icon color="white" size="28">mdi-magnify</v-icon>
                    </v-btn>
                  </template>
                </v-text-field>
              </template>

              <v-list v-if="menuSearch && (results.length !== 0)"
                style="width: 100%; height: auto;" dense nav>
                <v-list-item-group>
                  <v-list-item
                    v-for="(item, index) in results"
                    :key="index"
                    @mousedown.prevent="selectItem(item)"
                    link
                    :ref="'resultItem' + index"
                    :class="{ 'v-list-item--highlighted': highlightedIndex === index }"
                  >
                    <v-list-item-content>
                      <v-list-item-title>{{ item }}</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <!-- <v-list-item v-if="results.length === 0">
                    <v-list-item-title>ไม่พบข้อมูล</v-list-item-title>
                  </v-list-item> -->
                </v-list-item-group>
              </v-list>
            </v-menu>
            <CartPopover v-if="path"/>
            <Notification />
            <!-- <a-popover v-if="statusLogin" v-model="visibleChatAll" :placement="MobileSize ? 'bottomRight' :'bottom'" trigger="click">
              <v-btn @click="ChatMeAll()" icon top bordered overlap>
                <v-badge class="mt-1" :content="0" :value="0" color="red" top bordered overlap>
                  <v-icon color="white">mdi-chat-outline</v-icon>
                </v-badge>
              </v-btn>
              <template class="wrapper" slot="content">
                <v-list :style="MobileSize ? 'max-width: 300px; width: 300px;' : 'max-width: 300px; width: 300px; max-hei'">
                    <iframe id='iframechatbotAll' frameborder='0' style='background-color: transparent; width: 100%; height: 490px;' :src="urlChatMe"></iframe>
                  </v-list>
              </template>
            </a-popover> -->
          </div>
          <div class="d-flex" style="max-width: 242px; width: 100%; align-items: center; gap: 12px;">
            <Login v-if="statusLogin === false" />
            <LoginSuccess class="mt-0 ml-2" v-else/>
          </div>
          <!-- <v-row dense class="appBarResponsive d-flex justify-space-between"> -->
            <!-- <v-col style="max-width: 100px; max-height: 70px; width: 100%; height: 100%;">
              <router-link v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :to="{ pathShop }">
                <v-img v-lazyload :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
                  />
              </router-link>
              <router-link v-else :to="{ path: pathHome }">
                <v-img v-lazyload :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
                  @mousedown.right="goHomeRightClick()" />
              </router-link>
            </v-col> -->
            <!-- Search สินค้า -->
            <!-- <v-col> -->
              <!-- <v-row no-gutters class="row-app-bar pa-0 setBorder" style="max-height: 40px; margin-top: 0%; margin-left: 0%; background-color: #DAF1E933; max-width: 100%;" :style="IpadProSize ? 'width: 100%' : IpadSize ? 'width: 100%' : MobileSize ? 'width: 100%' : 'width: 100%'">
                <v-col cols="3" align="start">
                  <div id="select-category">
                    <v-select
                      v-model="selectCategory"
                      :items="items"
                      item-text="category_name"
                      item-value="id"
                      color="#27AB9C"
                      item-color="green"
                      class="pa-0 setCustomSelect custom-placeholer-color"
                      :menu-props='{ overflowY: true, offsetY: true }'
                      style="max-width: 100%;"
                      height="40"
                      append-icon="mdi-chevron-down"
                      flat
                      solo
                      dense
                      background-color="transparent"
                      hide-selected
                    >
                      <template v-slot:label>
                        <span style="color: #333333; font-size: 16px;">หมวดหมู่ทั้งหมด</span>
                      </template>
                    </v-select>
                  </div>
                </v-col>
                <v-col cols="9">
                  <div class="d-flex" id="search-product">
                    <v-text-field
                      color="#27AB9C" style="max-width: 100vw; max-height: 40px;" v-model="searchtext" background-color="transparent" :placeholder="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV' ? 'ค้นหาสินค้า': 'ค้นหาสินค้าและร้านค้า'" flat solo dense hide-details
                      @keyup.enter="searchdata()"
                      class="d-inline-block" append-icon="mdi-image-search" @click:append="triggerFileUpload"
                    ></v-text-field>
                    <input
                    type="file"
                    ref="fileInput"
                    style="display: none;"
                    accept="image/*"
                    @change="handleFileUpload"
                  />
                    <v-btn class="d-inline rounded-e-xl mr-auto" style="border-radius: 0px 7px 7px 0px !important;" width="116" height="39" dark elevation="0" @click="searchdata()" color="#27AB9C">
                      ค้นหา
                    </v-btn>
                  </div>
                </v-col>
              </v-row> -->
            <!-- </v-col> -->
            <!-- <v-col class="mt-3">
              <v-row dense>
                <v-col cols="12" md="1" class="pr-0 mt-1" v-if="IpadProSize && Cart_on_off && statusLogin === true">
                  <CartPopover v-if="path" />
                </v-col>
                <v-col cols="12" md="1" class="pr-0" v-if="!IpadProSize && Cart_on_off && statusLogin === true">
                  <CartPopover class="mt-1" v-if="path" />
                </v-col>
                <v-col cols="12" md="1" v-if="Notification_on_off && statusLogin === true">
                  <Notification class="hidden-sm-and-down mt-1 pl-1" />
                </v-col>
                <v-col cols="12" md="10" justify="start" class="pl-4">
                  <v-row class="mt-1">
                    <a-popover v-if="statusLogin" class="px-0 mx-0" v-model="visibleChatAll" :placement="MobileSize ? 'bottomRight' :'bottom'" trigger="click">
                      <v-btn @click="ChatMeAll()" icon top bordered overlap>
                        <v-badge class="mt-1" :content="0" :value="0" color="red" top bordered overlap>
                          <v-icon color="#27AB9C">mdi-chat-outline</v-icon>
                        </v-badge>
                      </v-btn>
                      <template class="wrapper" slot="content">
                        <v-list :style="MobileSize ? 'max-width: 300px; width: 300px;' : 'max-width: 300px; width: 300px; max-hei'">
                            <iframe id='iframechatbotAll' frameborder='0' style='background-color: transparent; width: 100%; height: 490px;' :src="urlChatMe"></iframe>
                          </v-list>
                      </template>
                    </a-popover>
                    <v-row dense v-if="statusLogin === false && !IpadProSize" class="mt-0">
                      <Login v-if="statusLogin === false && !IpadSize" :class="MacBookSize ? 'mt-2 pl-0' : 'mt-2 pl-4'" />
                      <LoginSuccess class="mt-0 ml-2" v-else />
                    </v-row>
                    <v-row dense v-else-if="statusLogin === false && IpadProSize" class="mt-1">
                      <Login v-if="statusLogin === false && !IpadSize" class="mt-0" />
                      <LoginSuccess class="mt-0 ml-2" v-else />
                    </v-row>
                    <div v-else>
                      <LoginSuccess class="mt-0 ml-2" />
                    </div>
                  </v-row>
                </v-col>
              </v-row>
            </v-col> -->
          <!-- </v-row> -->
        </v-toolbar-title>
      </v-col>
    </v-app-bar>
    <!-- Ipad -->
    <v-app-bar app class="backgroundAppBar" v-if="IpadSize && !MobileSize" width="100vw" height="100px" color="#FFFFFF" elevate-on-scroll elevation="1" style="z-index: 12 !important; width: 100%; max-width: 100%; position: fixed;">
      <v-col cols="12" class="px-0">
        <v-toolbar-title class="d-flex justify-space-between">
          <!-- logo -->
          <router-link v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :to="{ pathShop }">
            <v-img v-lazyload class="pr-2" :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
          />
          </router-link>
          <router-link v-else :to="{ path: pathHome }">
            <v-img v-lazyload class="pr-2" :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain max-width="100" max-height="70" width="100%" height="100%" style="cursor: pointer;" @mousedown.left="goHome()"
              @mousedown.right="goHomeRightClick()" />
          </router-link>
          <div class="d-flex" style="max-width: 435px; width: 100%; align-items: center; gap: 12px;">
            <v-text-field v-model="searchProductText" v-if="checkPath === 'shoppage' || checkPath === 'searchProduct'" rounded class="custom-placeholer-color textFieldStyle" :placeholder="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV' ? $t('AppBar.SearchNoJV'): $t('AppBar.SearchJV')" hide-details @keyup.enter="searchProduct()">
              <template v-slot:append>
                <v-btn class="search-btn" height="48" @click="searchProduct()" elevation="0">
                  <v-icon color="white" size="28">mdi-magnify</v-icon>
                </v-btn>
              </template>
            </v-text-field>
            <v-menu
              v-model="menuSearch"
              :close-on-content-click="false"
              offset-y
              v-else
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field v-model="searchtext" v-bind="attrs" v-on="on" rounded class="custom-placeholer-color textFieldStyle" :placeholder="$t('AppBar.Search')" hide-details @keydown.down.prevent="moveDown" @keydown.up.prevent="moveUp" @keydown.enter.prevent="selectHighlighted" @keyup="handleSearchAI">
                  <template v-slot:append>
                    <v-btn class="search-btn" height="48" @click="searchdata()" elevation="0">
                      <v-icon color="white" size="28">mdi-magnify</v-icon>
                    </v-btn>
                  </template>
                </v-text-field>
              </template>
              <v-list v-if="menuSearch && (results.length !== 0)"
                style="width: 100%; height: auto;" dense nav>
                <v-list-item-group>
                  <v-list-item
                    v-for="(item, index) in results"
                    :key="index"
                    @mousedown.prevent="selectItem(item)"
                    link
                    :ref="'resultItem' + index"
                    :class="{ 'v-list-item--highlighted': highlightedIndex === index }"
                  >
                    <v-list-item-content>
                      <v-list-item-title>{{ item }}</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                  <!-- <v-list-item v-if="results.length === 0">
                    <v-list-item-title>ไม่พบข้อมูล</v-list-item-title>
                  </v-list-item> -->
                </v-list-item-group>
              </v-list>
            </v-menu>
            <CartPopover v-if="path"/>
            <Notification />
            <!-- <a-popover v-if="statusLogin" v-model="visibleChatAll" :placement="MobileSize ? 'bottomRight' :'bottom'" trigger="click">
              <v-btn @click="ChatMeAll()" icon top bordered overlap>
                <v-badge class="mt-1" :content="0" :value="0" color="red" top bordered overlap>
                  <v-icon color="white">mdi-chat-outline</v-icon>
                </v-badge>
              </v-btn>
              <template class="wrapper" slot="content">
                <v-list :style="MobileSize ? 'max-width: 300px; width: 300px;' : 'max-width: 300px; width: 300px; max-hei'">
                    <iframe id='iframechatbotAll' frameborder='0' style='background-color: transparent; width: 100%; height: 490px;' :src="urlChatMe"></iframe>
                  </v-list>
              </template>
            </a-popover> -->
          </div>
          <div class="d-flex" style="max-width: 200px; width: 100%; align-items: center; gap: 12px;">
            <Login v-if="statusLogin === false" />
            <LoginSuccess class="mt-0 ml-2" v-else/>
          </div>
        </v-toolbar-title>
      </v-col>
      <!-- <v-toolbar-title class="mb-4" style="padding-left: 0px;">
        <v-img v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :src="require('@/assets/ngc_logo.webp')" fetchpriority="high" contain :width="MobileSize ? 82 : IpadSize || IpadProSize ? 57 : 57" :height="MobileSize ? 82 : IpadSize || IpadProSize ? 57 : 57"
          style="cursor: pointer; float: right;" @click="goHome()" />
        <v-img v-else :src="require('@/assets/ngc_logo.webp')" fetchpriority="high" contain :width="MobileSize ? 82 : IpadSize || IpadProSize ? 57 : 57" :height="MobileSize ? 82 : IpadSize || IpadProSize ? 57 : 57"
          @click="goHome()" />
      </v-toolbar-title>
      <div class="pl-2 mb-4" :style="statusLogin === false ? 'max-width: 100%;' : 'max-width: 100%;'">
        <v-row no-gutters  dense class="row-app-bar pa-0 mt-0 setBorder" v-bind:style="{'height':'40px', 'width':'100%', 'margin-Top':'0%', 'margin-left':'0%', 'background-color':'#F3F9FB'}">
          <v-col cols="4" align="start">
            <div id="select-category">
              <v-select
                v-model="selectCategory"
                :items="items"
                item-text="category_name"
                item-value="hierachy"
                color="#27AB9C"
                item-color="green"
                class="pa-0 setCustomSelect custom-placeholer-color"
                :menu-props='{ overflowY: true, offsetY: true }'
                :style="IpadProSize ? 'max-width: 100%' : IpadSize ? 'max-width: 100%' : 'max-width: 205px'" style="font-size: 16px !important;"
                height="40"
                append-icon="mdi-chevron-down"
                flat
                solo
                dense
                background-color="transparent"
                hide-selected
              >
                <template v-slot:label>
                  <span style="color: #333333; font-size: 16px;">หมวดหมู่ทั้งหมด</span>
                </template>
              </v-select>
            </div>
          </v-col>
          <v-col cols="8" align="end">
            <div class="d-flex" id="search-product">
              <v-text-field
                color="#27AB9C" style="max-width: 65%; max-height: 40px;" v-model="searchtext" background-color="transparent" :placeholder="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'? 'ค้นหาสินค้า': 'ค้นหาสินค้าและร้านค้า'" flat solo dense hide-details
                @keyup.enter="searchdata()"
                class="d-inline-block" append-icon="mdi-image-search" @click:append="triggerFileUpload"
              ></v-text-field>
              <input
                type="file"
                ref="fileInput"
                style="display: none;"
                accept="image/*"
                @change="handleFileUpload"
              />
              <v-btn class="d-inline mr-auto" style="border-radius: 0px 7px 7px 0px !important; border-top-left-radius: 0px; border-bottom-left-radius: 0px;transform: translatey(-1.5px);" width="35%" height="40" dark elevation="0" @click="searchdata()" color="#27AB9C">
                ค้นหา
              </v-btn>
            </div>
          </v-col>
        </v-row>
      </div>
      <CartPopover class="mb-4" v-if="path && (MobileSize || IpadSize)"/>
      <Notification class="hidden-md-and-up mb-4" v-if="Notification_on_off && statusLogin === true" />
      <v-icon style="width: 38px; height: 38px;" color="#333333" large
        v-if="statusLogin === false && !IpadSize" @click="modalLogin = !modalLogin" class="hidden-sm-and-up mr-8 mb-4">
        mdi-account-circle-outline</v-icon>
      <Login v-if="statusLogin === false && IpadSize" class="mt-0 mr-4 mb-4" />
      <LoginSuccess  v-if="(MobileSize || IpadSize) && statusLogin === true" class="mt-0 mb-4" :class="MobileSize ? 'pr-0' : ''" /> -->
    </v-app-bar>
    <!-- Mobile -->
    <v-app-bar app class="backgroundAppBar contentAppBar" v-if="MobileSize && !IpadSize" width="100vw" height="115px" max-height="200px" :style="{'max-width': MobileSize ? '100%' : '100%'}"  color="#FFFFFF" elevate-on-scroll elevation="1" style="z-index: 12 !important; width: 100%; max-width: 100%; position: fixed;">
      <v-col cols="12" class="pa-0" style="margin: auto;">
        <v-toolbar-title class="pa-0">
          <v-row dense justify="center" class="px-0">
            <v-col :cols="statusLogin === false ? '3' : '4'">
              <v-img :class="statusLogin === true ? 'image-logged-in' : 'image-logged-out'" v-if="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV'" :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain :width="MobileSize ? 59 : IpadSize || IpadProSize ? 47 : 57" :height="MobileSize ? 42 : IpadSize || IpadProSize ? 47 : 57" style="cursor: pointer;" @click="goHome()" />
              <v-img :class="statusLogin === true ? 'image-logged-in' : 'image-logged-out'" v-else :src="require('@/assets/new_logo_nexgen.webp')" fetchpriority="high" contain :width="MobileSize ? 59 : IpadSize || IpadProSize ? 47 : 57" :height="MobileSize ? 42 : IpadSize || IpadProSize ? 47 : 57" @click="goHome()"/>
            </v-col>
            <v-col :cols="statusLogin === false ? '8' : '7'" style="display: flex;">
              <v-row dense justify="end">
                <div style="display: flex; align-items: center;" v-if="statusLogin === false">
                <v-btn v-if="$i18n.locale === 'th'" elevation="0" class="mr-2" rounded icon width="24"><v-img @click="changeLang('en')" :src="require('@/assets/eng.png')" height="24" max-width="24" style="border-radius: 100%;"></v-img></v-btn>
                <v-btn v-if="$i18n.locale === 'en'" elevation="0" class="mr-2" rounded icon width="24"><v-img @click="changeLang('th')" :src="require('@/assets/thai.png')" height="24" max-width="24" style="border-radius: 100%;"></v-img></v-btn>
                <v-btn @click="GoToRegister()" elevation="0" class="mr-2" outlined rounded color="#fff" style="max-height: 32px; max-width: 113px;">{{ $t('AppBar.Register') }}</v-btn>
                <v-btn @click="GoToLogin()" elevation="0" rounded color="#fff" style="color: #27AB9C; max-height: 32px; max-width: 113px;">{{ $t('AppBar.Login') }}</v-btn>
                </div>
                <CartPopover v-if="path && (MobileSize || IpadSize) && statusLogin === true"/>
                <Notification class="hidden-md-and-up" v-if="Notification_on_off && statusLogin === true" />
                <!-- <a-popover v-if="statusLogin" v-model="visibleChatAll" :placement="MobileSize ? 'bottomRight' :'bottom'" trigger="click">
                  <v-btn @click="ChatMeAll()" icon top bordered overlap>
                    <v-badge class="mt-1" :content="0" :value="0" color="red" top bordered overlap>
                      <v-icon color="white">mdi-chat-outline</v-icon>
                    </v-badge>
                  </v-btn>
                  <template class="wrapper" slot="content">
                    <v-list :style="MobileSize ? 'max-width: 300px; width: 300px;' : 'max-width: 300px; width: 300px; max-hei'">
                        <iframe id='iframechatbotAll' frameborder='0' style='background-color: transparent; width: 100%; height: 490px;' :src="urlChatMe"></iframe>
                      </v-list>
                  </template>
                </a-popover> -->
                <LoginSuccess v-if="(MobileSize || IpadSize) && statusLogin === true" :class="MobileSize ? 'pt-1' : ''" />
              </v-row>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col cols="12">
              <v-text-field v-model="searchProductText" v-if="checkPath === 'shoppage' || checkPath === 'searchProduct'" rounded class="custom-placeholer-color textFieldStyle" :placeholder="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV' ? $t('AppBar.SearchNoJV'): $t('AppBar.SearchJV')" hide-details @keyup.enter="searchProduct()">
              <template v-slot:append>
                <v-btn class="search-btn" height="48" @click="searchProduct()" elevation="0">
                  <v-icon color="white" size="28">mdi-magnify</v-icon>
                </v-btn>
              </template>
            </v-text-field>
              <v-menu
                v-model="menuSearch"
                :close-on-content-click="false"
                offset-y
                v-else
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field v-model="searchtext" v-bind="attrs" v-on="on" rounded class="custom-placeholer-color textFieldStyle" :placeholder="$t('AppBar.Search')"  hide-details @keydown.down.prevent="moveDown" @keydown.up.prevent="moveUp" @keydown.enter.prevent="selectHighlighted" @keyup="handleSearchAI">
                    <template v-slot:append>
                      <v-btn class="search-btn" height="48" @click="searchdata()" elevation="0">
                        <v-icon color="white" size="28">mdi-magnify</v-icon>
                      </v-btn>
                    </template>
                  </v-text-field>
                </template>
                <v-list v-if="menuSearch && (results.length !== 0)"
                  style="width: 100%; height: auto;" dense nav>
                  <v-list-item-group
                    v-model="selectedItemSearch"
                    color="#27AB9C"
                  >
                    <v-list-item
                      v-for="(item, index) in results"
                      :key="index"
                      @mousedown.prevent="selectItem(item)"
                      link
                      :ref="'resultItem' + index"
                      :class="{ 'v-list-item--highlighted': highlightedIndex === index }"
                    >
                      <v-list-item-content>
                        <v-list-item-title>{{ item }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                    <!-- <v-list-item v-if="results.length === 0">
                      <v-list-item-title>ไม่พบข้อมูล</v-list-item-title>
                    </v-list-item> -->
                  </v-list-item-group>
                </v-list>
              </v-menu>
            </v-col>
          </v-row>
          <!-- <div class="d-flex" style="max-width: 100%; width: 100%; align-items: center;"> -->
            <!-- <v-row no-gutters class="row-app-bar pa-0 setBorder" v-bind:style="{'height':'42px','width':'100%','margin-Top':'0%','margin-left':'0%','background-color':'#F3F9FB'}">
              <v-col cols="7" align="start">
                <div id="search-product">
                  <v-text-field color="#27AB9C" v-model="searchtext" background-color="#F3F9FB" :placeholder="dataRole === 'sale_order' || dataRole === 'sale_order_no_JV' ? 'ค้นหาสินค้า': 'ค้นหาสินค้าและร้านค้า'" dense hide-details flat solo
                    @keyup.enter="searchdata()" class="textSearch" style="background: #FFFFFF; font-size: 16px !important; transform: translatey(0px); border-radius: 8px; max-height: 38px;"
                    append-icon="mdi-image-search" @click:append="triggerFileUpload">
                  </v-text-field>
                  <input
                    type="file"
                    ref="fileInput"
                    style="display: none;"
                    accept="image/*"
                    @change="handleFileUpload"
                  />
                </div>
              </v-col>
              <v-col cols="5" align="end">
                <div id="select-category">
                  <v-select
                    v-model="selectCategory"
                    :items="items"
                    item-text="category_name"
                    item-value="id"
                    color="#27AB9C"
                    item-color="green"
                    class="d-inline-block custom-placeholer-color setCustomSelect"
                    :menu-props='{ overflowY: true, offsetY: true }'
                    :style="MobileSize ? 'max-width: 5px' : IpadSize ? '' : 'max-width: 205px'" style="font-size: 16px !important; transform: translateX(-35px);"
                    height="40"
                    append-icon="mdi-chevron-down"
                    flat
                    solo
                    dense
                    background-color="transparent"
                    hide-selected
                  >
                  </v-select>
                  <v-btn color="#27AB9C"  style="max-width: 36px; min-height: 41px; border-top-left-radius: 0px; border-bottom-left-radius: 0px; transform: translatey(-3px);" small class="d-inline-block white--text " @click="searchdata()"><b>ค้นหา</b></v-btn>
                </div>
              </v-col>
            </v-row> -->
            <!-- <CartPopover v-if="path && (MobileSize || IpadSize)"/>
            <Notification class="hidden-md-and-up" v-if="Notification_on_off && statusLogin === true" />
            <v-icon style="width: 38px; height: 38px;" color="#333333" large
            v-if="statusLogin === false && !IpadSize" @click="modalLogin = !modalLogin">
            mdi-account-circle-outline</v-icon>
            <Login  v-if="statusLogin === false && IpadSize"/>
            <LoginSuccess  v-if="(MobileSize || IpadSize) && statusLogin === true" :class="MobileSize ? 'pr-0' : ''" />
          </div> -->
        </v-toolbar-title>
      </v-col>
    </v-app-bar>
    <!-- v-navigation-drawer -->
    <v-dialog v-model="modalLogin" width="343">
      <v-card>
        <v-card-title>
          <!-- ปุ่มเข้าสู่ระบบ -->
          <v-col cols="12" md="12" sm="12">
            <v-btn block rounded color="#27AB9C" dark elevation="6" style="border-radius: 32px;"
              @click="gotoRegisterMobile()">{{ $t('AppBar.Register') }}</v-btn>
          </v-col>
          <!-- แถบ เข้าสู่ระบบด้วย -->
          <v-row dense justify="center" align-content="center" class="mt-6 mb-6">
            <v-divider style="margin-top: 10px;" class="mx-2"></v-divider>
            <span style="font-size: 14px; line-height: 22px; color: #000000;">{{ $t('AppBar.or') }}</span>
            <v-divider style="margin-top: 10px;" class="mx-2"></v-divider>
          </v-row>
          <!-- ปุ่มเข้าสู่ระบบแบบ ONE ID -->
          <v-col cols="12" md="12" sm="12">
            <v-btn @click="gotoLoginMobile()" color="#27AB9C" block outlined rounded elevation="4"
              style="border: 1px solid #27AB9C; border-radius: 32px;">{{ $t('AppBar.Login') }}</v-btn>
          </v-col>
        </v-card-title>
      </v-card>
    </v-dialog>
    <!-- </v-app> -->
  </div>
</template>
<script>
import { createChat } from '../../components/library/CallChatMe/callChatMe'
import { Decode, Encode } from '@/services'
// import { Popover } from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import debounce from 'lodash.debounce'
export default {
  components: {
    // 'a-popover': Popover,
    CartPopover: () => import('@/components/PopOver/PopoverCartAppBarUI'),
    Login: () => import('@/components/PopOver/LoginUI'),
    LoginSuccess: () => import('@/components/PopOver/LoginSuccessUI'),
    Notification: () => import('@/components/Notification/Notification')
  },
  data: () => ({
    visibleChatAll: false,
    urlChatMe: '',
    selectCategory: '',
    shopName: '',
    detailShop: '',
    dataRole: 'ext_buyer',
    select_lish_category: '',
    lish_Category: [],
    autosearch: [],
    drawer: false,
    searchtext: '',
    searchProductText: '',
    selectedItem: '',
    selectItemCategory: '',
    selectItemBrand: '',
    selectItemIndustry: '',
    menu: '',
    Notification_on_off: true,
    Cart_on_off: true,
    header: '',
    items: [],
    listItemCategory: [],
    listItemBrand: [],
    listItemIndrustry: [],
    countCart: 1,
    statusLogin: false,
    onedata: [],
    ListCategory: [],
    path: false,
    modalLogin: false,
    pathServe: process.env.VUE_APP_DOMAIN,
    loginpath: '',
    registerpath: '',
    appbarPath: '',
    listproductPath: '',
    pathHome: '/',
    pathShop: '',
    toggle_exclusive: null,
    IDCategory: '',
    lish_SubCategory: [],
    select_lish_subcategory: '',
    res: [],
    datas: [],
    selected: [],
    lastLoginDate: null,
    show: false,
    showUpdateSystem: true,
    checkPath: '',
    results: [],
    shopId: '',
    menuSearch: false,
    timer: null,
    lastQueryId: 0,
    selectedItemSearch: null,
    highlightedIndex: -1
  }),
  created () {
    this.$EventBus.$on('OpenChatAll', this.OpenChatAll)
    caches.keys().then(cacheNames => {
      cacheNames.forEach(cacheName => {
        caches.delete(cacheName)
      })
    })
    if (localStorage.getItem('oneData') === undefined) {
      localStorage.removeItem('oneData')
    }
    this.statusLogin = false
    // this.GetCategory()
    this.getPath()
    this.shopId = localStorage.getItem('shopID')
    if (localStorage.getItem('lang') !== null) {
      var lang = localStorage.getItem('lang')
      this.changeLang(lang)
    } else {
      localStorage.setItem('lang', 'th')
      this.changeLang('th')
    }
    // this.loginUser()
    // this.getAllCategory()
    // this.dataShop()
    // window.addEventListener('storage', function (event) {
    //   if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    //     window.location.reload()
    //   }
    // })
  },
  mounted () {
    // this.AuthorityUser()
    // window.addEventListener('storage', function (event) {
    //   if (localStorage.getItem('oneData') === null) {
    //     window.location.reload()
    //   }
    // })
    this.loginUser()
    this.dataShop()
    if (this.$route.name !== 'HomeProduct') {
      this.$EventBus.$emit('ChangeShow')
    }
    this.$EventBus.$on('LoginUser', this.loginUser)
    this.$EventBus.$on('closeModalSearchDataAI', this.closeModalSearchDataAI)
    this.$EventBus.$on('role', this.dataShop)
    this.$EventBus.$on('searchdata', this.searchdata)
    this.$EventBus.$on('getPath', this.getPath)
    this.$EventBus.$on('resetSearch', this.resetSearch)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('searchdata')
      this.$EventBus.$off('getPath')
      this.$EventBus.$off('role')
      this.$EventBus.$off('resetSearch')
    })
    // Check if last login date is stored in localStorage
    const storedLoginDate = localStorage.getItem('lastLoginDate')

    if (storedLoginDate) {
      this.lastLoginDate = new Date(storedLoginDate)
      this.checkIfShouldResetLocalStorage()
    } else {
      // If last login date is not stored, treat it as a fresh login
      // this.updateLastLoginDate()
    }
    window.addEventListener('mousemove', (event) => {
      if (!this.show) {
        this.getAllCategory()
      }
      this.show = true
    })
    window.addEventListener('mouseup', (event) => {
      if (!this.show) {
        this.getAllCategory()
      }
      this.show = true
    })
  },
  computed: {
    // main () {
    //   return this.$route.name === 'Home'
    // },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    MacBookSize () {
      const { lg } = this.$vuetify.breakpoint
      return !!lg
    }
  },
  watch: {
    async $route (to, from) {
      if (to.params.data !== '' && to.name === 'search') {
        this.searchtext = to.params.data
      }
    }
  },
  methods: {
    changeLang (value) {
      // console.log('lang =====>', value)
      localStorage.setItem('lang', value)
      this.$i18n.locale = value
      this.$forceUpdate()
    },
    closeSystemBar () {
      this.showUpdateSystem = false
    },
    triggerFileUpload () {
      this.$refs.fileInput.value = ''
      this.$refs.fileInput.click() // Trigger click event on the hidden input
    },
    handleFileUpload (event) {
      const file = event.target.files[0]
      if (file && (file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png')) {
        const imageSize = file.size / 1024 / 1024
        if (imageSize < 5) {
          const reader = new FileReader()
          reader.onload = () => {
            const base64String = reader.result
            localStorage.setItem('FileSearchImage', JSON.stringify(base64String)) // Store as Base64 string
            this.$EventBus.$emit('rePage', this.rePage)
            this.$EventBus.$emit('getResultSearchImage', base64String)
          }
          reader.readAsDataURL(file)
          localStorage.setItem('FileSearchImage', JSON.stringify(file))
          if (this.selectCategory !== '') {
            this.$router.push({ path: `/searchImage?idcat=${this.selectCategory}` }).catch(() => {})
          } else {
            this.$router.push({ path: '/searchImage' }).catch(() => {})
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
        }
      } else {
        if (file !== undefined) {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2500
          })
        }
      }
    },
    loginUser () {
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        this.checkButton = true
        this.onedata = []
        this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        // console.log(this.onedata)
        if (this.onedata.user === undefined && this.onedata.user === '') {
          localStorage.removeItem('oneData')
        }
        if (this.onedata.user !== undefined) {
          // this.username = onedata.user[0].first_name_th + ' ' + onedata.user[0].last_name_th
          this.statusLogin = true
          // this.AuthorityUser()
        } else {
          this.statusLogin = false
        }
      } else {
        this.statusLogin = false
        this.checkButton = false
      }
    },
    OpenChatAll () {
      this.visibleChatAll = false
    },
    async ChatMeAll () {
      // var x = document.getElementById('iframechatbot')
      const sharetoken = await createChat.sharetoken()
      // console.log('https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin/authen?type=user&sharetoken=' + sharetoken.data.shared_token, 'xxxxxx')
      // document.getElementById('iframechatbot').src = await 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=' + item.botID + '&sharetoken=' + sharetoken.data.shared_token
      this.urlChatMe = await 'https://chat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin/authen?type=user&sharetoken=' + sharetoken.data.shared_token
      setTimeout(() => {
        var iframe = document.getElementById('iframechatbotAll')
        window.postMessage('message', '*')
        var iframewindow = iframe.contentWindow || iframe.contentDocument.defaultView
        var doc = iframewindow.document
        // var csstag = doc.createElement('style')
        // var scripttag = doc.createElement('script')
        var body = doc.getElementsByTagName('body')[0]
        if (process.env.NODE_ENV !== 'production') {
          console.log(body, 'body')
        }
        // document.querySelectorAll('iframe').forEach(item => console.log('Beetest', item.contentWindow.document.getElementsByTagName('main')))
        // const data = document.querySelector('#iframechatbotAll')
        // console.log('datafff', zz)
      }, 2500)
    },
    checkIfShouldResetLocalStorage () {
      // Check if 7 days have passed since the last login
      const sevenDaysAgo = new Date(this.lastLoginDate)
      sevenDaysAgo.setDate(sevenDaysAgo.getDate() + 7)
      // sevenDaysAgo.setDate(sevenDaysAgo.getDate() + 7)

      if ((this.lastLoginDate > sevenDaysAgo) && this.lastLoginDate !== null) {
        // Clear localStorage
        localStorage.clear()
        window.location.reload()
        this.lastLoginDate = null
        // Update last login date
        // this.updateLastLoginDate()
      }
    },
    updateLastLoginDate () {
      this.$router.push({ path: '/Login' }).catch(() => {})
    },
    async getAllCategory () {
      var data = 'all'
      this.items = []
      await this.$store.dispatch('actionsGetCategory', data)
      var response = await this.$store.state.ModuleHompage.stateGetCategory
      if (response.message === 'Get all category success.') {
        this.items = response.data
        this.items.unshift({
          category_logo_path: '',
          category_name: 'หมวดหมู่ทั้งหมด',
          hierachy: '',
          id: ''
        })
      }
    },
    dataShop (Role) {
      // console.log('dataShop')
      if (Role === undefined) {
        if (localStorage.getItem('roleUser') !== null) {
          this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
        } else {
          this.dataRole = 'ext_buyer'
        }
      } else {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser')).role
      }
      if (localStorage.getItem('ShopDetailSale') !== null) {
        this.detailShop = JSON.parse(Decode.decode(localStorage.getItem('ShopDetailSale')))
        // console.log('detailShop', this.detailShop)
        var shopCleaned = ''
        if (this.detailShop.name_th !== undefined) {
          this.shopName = this.detailShop.name_th.replace(/\s/g, '-')
          shopCleaned = encodeURIComponent(this.shopName)
          this.pathShop = `/shoppage/${shopCleaned}-${this.detailShop.id}`
        } else {
          this.shopName = this.detailShop.shop_name_th.replace(/\s/g, '-')
          shopCleaned = encodeURIComponent(this.shopName)
          this.pathShop = `/shoppage/${shopCleaned}-${this.detailShop.seller_shop_id}`
        }
        this.$EventBus.$emit('pathShopSale', this.pathShop)
        if (this.dataRole === 'sale_order' || this.dataRole === 'sale_order_no_JV') {
          localStorage.setItem('pathShopSale', Encode.encode({ path: this.pathShop }))
        }
      }
    },
    // async AuthorityUser () {
    //   await this.$store.dispatch('actionsAuthorityUser')
    //   var response = await this.$store.state.ModuleUser.stateAuthorityUser
    //   console.log('Authority', response.data.current_role_user)
    // },
    gotoSeller () {
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        if (this.onedata.user.type_user === 'oneID_user') {
          this.$router.push({ path: '/seller' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาผูกบัญชีกับ One ID ก่อน!', showConfirmButton: false, timer: 1500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบหรือสมัครสมาชิกก่อน!', showConfirmButton: false, timer: 1500 })
        this.$EventBus.$emit('openModalLogin')
      }
    },
    async getPath () {
      var currentRoutepath = this.$router.currentRoute.path
      var parts = currentRoutepath.split('/')
      this.checkPath = parts[1]
      localStorage.setItem('path', parts[1])
      if (currentRoutepath === '/shoppingcart' || currentRoutepath === '/checkout' || currentRoutepath === '/checkoutSaleOrder') {
        this.path = false
      } else {
        this.path = true
      }
      this.$forceUpdate()
    },
    async GetCategory () {
      await this.$store.dispatch('GetDefaultCagegory')
      this.ListCategory = await this.$store.state.ModuleManageShop.Category
    },
    goHome () {
      var val
      var data
      if (this.MobileSize) {
        this.$EventBus.$emit('GetLink')
      }
      localStorage.removeItem('pathAdmin')
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        this.$EventBus.$emit('resetAdminShop')
        if (this.onedata.user !== undefined) {
          if (localStorage.getItem('roleUserApprove') !== null) {
            // console.log('เข้าเงื่อนไขขขขขขขขข')
            val = JSON.parse(localStorage.getItem('roleUser'))
            data = {
              role: val.role === 'purchaser' ? 'purchaser' : 'ext_buyer'
            }
            val.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
            localStorage.setItem('roleUser', JSON.stringify(data))
            this.$EventBus.$emit('LinkPage', val.role)
            this.searchtext = ''
            this.selectCategory = ''
            this.$router.push({ path: '/' }).catch(() => {})
          } else if (this.dataRole === 'sale_order' || this.dataRole === 'sale_order_no_JV') {
            this.searchtext = ''
            this.selectCategory = ''
            this.$router.push({ path: this.pathShop }).catch(() => {})
          } else if (localStorage.getItem('roleUserAdmin') !== null) {
            val = JSON.parse(localStorage.getItem('roleUser'))
            data = {
              role: val.role === 'purchaser' ? 'purchaser' : 'ext_buyer'
            }
            val.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
            localStorage.setItem('roleUser', JSON.stringify(data))
            this.$EventBus.$emit('LinkPage', val.role)
            this.searchtext = ''
            this.selectCategory = ''
            localStorage.removeItem('roleUserAdmin')
            this.$router.push({ path: '/' }).catch(() => {})
          } else {
            this.searchtext = ''
            this.selectCategory = ''
            this.$router.push({ path: '/' }).catch(() => {})
          }
        } else {
          localStorage.removeItem('oneData')
          this.$router.push({ path: '/' }).catch(() => {})
        }
      } else {
        this.searchtext = ''
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    resetSearch () {
      this.searchtext = ''
    },
    gotoCreateEpro () {
      this.$router.push({ path: '/createbusinesssid' }).catch(() => {})
    },
    gotoLoginMobile () {
      this.modalLogin = false
      this.$router.push({ path: '/Login' }).catch(() => {})
    },
    gotoRegisterMobile () {
      this.modalLogin = false
      this.$router.push({ path: '/Register' }).catch(() => {})
    },
    searchdata () {
      // console.log('first to check search', this.searchtext)
      this.searchtext = this.searchtext.trim().replace(/  +/g, ' ')
      localStorage.setItem('shopId', this.shopId)
      if (this.searchtext === '') {
        this.$swal.fire({
          icon: 'warning',
          title: this.$t('TextHome.textSearch'),
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        this.$EventBus.$emit('resetAdminShop')
        if (this.$router.currentRoute.name === 'search') {
          if (this.$router.currentRoute.params.data === this.searchtext) {
            // console.log('condition 1')
            if (this.$route.query.idcat === this.selectCategory) {
              this.$EventBus.$emit('getResultSearch')
            } else if (this.$route.query.idcat !== this.selectCategory) {
              if (this.selectCategory !== '') {
                this.menuSearch = false
                this.results = []
                this.$router.push({ path: `${encodeURIComponent(this.searchtext)}?idcat=${this.selectCategory}` }).catch(() => {})
              } else {
                this.menuSearch = false
                this.results = []
                this.$router.push({ path: `${encodeURIComponent(this.searchtext)}` }).catch(() => {})
              }
              this.$EventBus.$emit('getResultSearch')
            } else if (this.selectCategory === '') {
              this.menuSearch = false
              this.results = []
              this.$router.push({ path: `${encodeURIComponent(this.searchtext)}` }).catch(() => {})
              this.$EventBus.$emit('getResultSearch')
            } else {
              this.$EventBus.$emit('getResultSearch')
            }
          } else {
            // console.log('condition 2')
            this.menuSearch = false
            this.results = []
            if (this.selectCategory !== '') {
              this.$router.push({ path: `${encodeURIComponent(this.searchtext)}?idcat=${this.selectCategory}` }).catch(() => {})
            } else {
              this.$router.push({ path: `${encodeURIComponent(this.searchtext)}` }).catch(() => {})
            }
            this.$EventBus.$emit('getResultSearch')
          }
        } else {
          // console.log('condition 3')
          this.menuSearch = false
          this.results = []
          if (this.selectCategory !== '') {
            this.$router.push({ path: `/search/${encodeURIComponent(this.searchtext)}?idcat=${this.selectCategory}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/search/${encodeURIComponent(this.searchtext)}` }).catch(() => {})
          }
          // this.$router.push({ path: `/DetailProduct/${this.searchtext}` }).catch(() => {})
          this.$EventBus.$emit('getResultSearch')
        }
      }
    },
    searchProduct () {
      this.menuSearch = false
      this.results = []
      // console.log(this.searchProductText, '55555')
      // console.log('first to check search', this.searchtext)
      this.searchProductText = this.searchProductText.trim().replace(/  +/g, ' ')
      if (this.searchProductText === '') {
        this.$swal.fire({
          icon: 'warning',
          title: this.$t('TextHome.textSearch'),
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        this.$EventBus.$emit('resetAdminShop')
        if (this.$router.currentRoute.name === 'searchProduct') {
          if (this.$router.currentRoute.params.data === this.searchProductText) {
            // console.log(this.$router.currentRoute.params.data, 'kkkkk')
            localStorage.setItem('shopId', this.shopId)
            this.$router.push({ path: `${encodeURIComponent(this.searchProductText)}` }).catch(() => {})
            this.$EventBus.$emit('getResultSearch')
          } else {
            this.$router.push({ path: `${encodeURIComponent(this.searchProductText)}` }).catch(() => {})
            this.$EventBus.$emit('getResultSearch')
          }
        } else {
          if (this.selectCategory !== '') {
            this.$router.push({ path: `/searchProduct/${encodeURIComponent(this.searchProductText)}?idcat=${this.selectCategory}` }).catch(() => {})
          } else {
            this.$router.push({ path: `/searchProduct/${encodeURIComponent(this.searchProductText)}` }).catch(() => {})
          }
          // this.$router.push({ path: `/DetailProduct/${this.searchtext}` }).catch(() => {})
          this.$EventBus.$emit('getResultSearch')
        }
      }
    },
    GoToLogin () {
      // console.log('this.$router.currentRoute.path', this.$router.currentRoute.fullPath)
      sessionStorage.setItem('pathRedirect', this.$router.currentRoute.fullPath)
      this.$router.push({ path: '/Login' }).catch(() => {})
    },
    GoToRegister () {
      this.$router.push({ path: '/Register' }).catch(() => {})
    },
    closeModalSearchDataAI () {
      this.menuSearch = false
      this.results = []
    },
    moveDown () {
      if (!this.menuSearch) return
      if (this.highlightedIndex < this.results.length - 1) {
        this.highlightedIndex++
        this.scrollToHighlighted()
      }
    },
    moveUp () {
      if (!this.menuSearch) return
      if (this.highlightedIndex > 0) {
        this.highlightedIndex--
        this.scrollToHighlighted()
      }
    },
    scrollToHighlighted () {
      this.$nextTick(() => {
        const ref = this.$refs['resultItem' + this.highlightedIndex]
        if (ref && ref.$el) {
          ref.$el.scrollIntoView({ block: 'nearest' })
        }
      })
    },
    selectHighlighted () {
      if (this.highlightedIndex >= 0 && this.highlightedIndex < this.results.length) {
        // ✅ มีรายการถูกเลือก → ใช้รายการนั้น
        this.selectItem(this.results[this.highlightedIndex])
      } else {
        // ✅ ไม่มีรายการถูกเลือก → ใช้ searchtext ตรงๆ
        this.selectItem(this.searchtext)
      }
    },
    debouncedFetch: debounce(function () {
      this.fetchData()
    }, 150),
    handleSearchAI (event) {
      if (this.searchtext !== '') {
        const ignoreKeys = ['ArrowDown', 'ArrowUp', 'Enter']
        if (ignoreKeys.includes(event.key)) return
        this.debouncedFetch()
      } else {
        this.menuSearch = false
        this.results = []
        this.lastQueryId++
      }
    },
    async fetchData () {
      this.results = [] // เคลียร์ผลลัพธ์เก่าทันที
      const currentQueryId = ++this.lastQueryId
      var data = {
        keyword: this.searchtext,
        limit: 10,
        page: 1
      }
      try {
        // this.$store.dispatch('actionsSearchBarListAI', data)
        // const response = await this.$store.state.ModuleHompage.stateSearchBarListAI
        const response = await this.$store.dispatch('actionsSearchBarListAI', data)
        // ตรวจสอบว่าผลลัพธ์นี้เป็นของคำขอสุดท้ายจริงๆ
        if (currentQueryId === this.lastQueryId) {
          if (response && response.ok === 'y') {
            if (this.searchtext !== '') {
              this.results = response.query_result
              this.menuSearch = true
            } else {
              this.results = []
              this.menuSearch = false
            }
          } else {
            this.results = []
            this.menuSearch = false
          }
        }
      } catch (e) {
        this.results = []
        this.menuSearch = false
      }
    },
    async selectItem (item) {
      this.searchtext = item
      this.menuSearch = false
      this.lastQueryId++
      this.highlightedIndex = -1
      await this.searchdata()
    }
  }
}
</script>
<style lang="scss">
::v-deep .v-main > .v-main__wrap > .v-card > .layout-listchat[data-v-292a3094] {
  height: 100vh !important;
}
</style>
<style lang="css" scoped>
::v-deep(.v-list-item.v-list-item--highlighted) {
  background-color: #E0F7FA !important;
  color: #006064 !important;
}
::v-deep(.v-list-item.v-list-item--highlighted) {
  background-color: #E0F7FA !important;
  color: #006064 !important;
}
.search-btn {
  background: linear-gradient(180deg, #FF7534 0%, #FF631A 50%, #FF5100 100%);
  border-radius: 0 16px 16px 0;
  width: 78px;
}
::v-deep .v-input__append-inner {
  margin-top: 0px !important;
}
::v-deep .v-input__slot {
  padding-right: 0px !important;
}
.textFieldStyle {
  background-color: #FFFFFF;
  max-width: 578px;
  height: 48px;
  justify-content: space-between;
  border-radius: 16px;
  /* border: 1px solid #F5F5F5; */
  display: flex;
  align-items: center;
  padding-right: 0px !important;
}
.backgroundAppBar {
  background: linear-gradient(90deg, #2D95FF -4%, #54ECB5 103.33%);
  /* border-bottom: 1px solid #F5F5F5; */
  box-shadow: 0px 0px 1px 0px #C3CDD517;
  box-shadow: 0px 0px 1px 0px #C3CDD50D;
  box-shadow: 0px 0px 1px 0px #C3CDD503;
  box-shadow: 1px 0px 1px 0px #C3CDD500;
}
::v-deep .layout-listchat[data-v-292a3094] {
  height: 100vh !important;
}
::v-deep .v-main > .v-main__wrap > .v-card >.layout-listchat {
  height: 100vh !important;
}
::v-deep .theme--light.v-application {
  padding-bottom: 0px;
}
/deep/.ant-popover-title {
  padding: 0px !important;
}
.row-app-bar {
  width: 100%;
  margin: 0%;
}
.contentAppBar /deep/ .v-toolbar__content, .v-toolbar__extension {
  padding: 4px 4px !important;
}
.SetFontSize /deep/ .v-text-field__slot input {
  font-size: 16px !important;
}
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.custom-placeholer-color /deep/ input::placeholder {
  color: #58758E !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  margin: auto;
}
.bg-color {
  background-color: #27AB9C !important
}
@media screen and (min-width: 768px) {
  .responsive_menu {
    margin-left: 2.5%;
  }
  .appBarResponsive {
    margin-left: 5%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1290px) {
  .responsive_menu {
    margin-left: 2.2%;
  }
  .appBarResponsive {
    margin-left: 0%;
  }
}
@media screen and (min-width: 1290px) and (max-width: 1600px) {
  .responsive_menu {
    margin-left: 5.2%;
  }
  .appBarResponsive {
    margin-left: 6%;
  }
}
@media screen and (min-width: 1600px) {
  .responsive_menu {
    margin-left: 7.7%;
  }
  .appBarResponsive {
    margin-left: 7.4%;
  }
}
@media only screen and (min-width: 640px) {
  .responsive_navbar_2 {
    display: none !important;
  }
}
@media only screen and (max-width: 640px) {
  .responsive_navbar {
    display: none !important;
  }
  .Setting_down {
    display: none;
  }
  .shopping-cart-logo {
    display: none;
  }
  .profile_name {
    display: none;
  }
  /* .e-pro-front {
    display: none;
  } */
  .e-pro-back {
    display: flex;
  }
  .vdivider {
    display: none;
  }
  .logo-login-pass {
    display: none;
  }
  .logo-login-pass-mobile {
    display: flex;
  }
}
/* For Responsive mobile, Ipad, Website */
@media screen and (min-width: 360px) {
  .displayMobile {
    display: inline;
  }
  .displayIPAD {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 768px) and (max-width: 1280px) {
  .displayIPAD {
    display: inline;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: none;
  }
}
@media screen and (min-width: 1280px) {
  .displayIPAD {
    display: none;
  }
  .displayMobile {
    display: none;
  }
  .diaplayWeb {
    display: inline;
  }
}
.custom-text-field {
  height: 36px;
}
</style>

<style scoped>
::v-deep #search-product > .v-text-field--outlined > .v-input__control > .v-input__slot {
  height: 40px;
  min-height: 40px;
}
::v-deep #select-category > .v-text-field--outlined > .v-input__control > .v-input__slot {
  height: 40px;
  min-height: 40px;
}
.v-text-field.v-text-field--solo.v-input--dense>.v-input__control {
  min-height: 40px;
}
@media only screen and (max-width: 640px) {
  ::v-deep .image-logged-in .v-image__image {
    left: -0px !important;
  }
  ::v-deep .image-logged-out .v-image__image {
    left: -0px !important;
  }
}
</style>
