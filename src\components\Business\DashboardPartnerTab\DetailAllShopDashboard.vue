<template>
  <v-container class="pt-0">
    <v-row v-if="MobileSize">
      <v-col cols="12">
        <span style="font-size: 18px; font-weight: bold;">ประเภทการแสดงข้อมูล</span>
      </v-col>
      <v-col cols="12">
        <v-select
          v-model="selectedTypeData"
          :items="TypeDataItems"
          outlined
        ></v-select>
      </v-col>
    </v-row>
    <div v-if="!MobileSize || MobileSize && selectedTypeData === 'Top 5 Package ขายดี'">
      <v-card elevation="0" style="background-color: #F6FAFF;">
        <v-row>
          <v-col cols="12" md="6" style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
            <span class="pb-5" style="color: #27AB9C; font-size: 18px;"><b>TOP 5 Package ขายดี</b></span>
            <v-avatar rounded size="300">
              <v-img contain src="@/assets/Marketplace_partner/Group.png"></v-img>
            </v-avatar>
          </v-col>
          <v-col cols="12" md="6">
            <v-card v-if="filledRanksPackage.length !==0" class="mt-4" style="border-radius: 8px; background: transparent;" elevation="0">
              <v-row>
                <v-col cols="12" md="12" sm="12" xs="12">
                  <v-row no-gutters>
                    <v-col v-for="(item, index) in filledRanksPackage" :key="index" cols="12" class="mb-4 pa-2">
                      <v-card height="100%" style="border: 1px solid #E6FCD6;" elevation="0">
                        <v-row class="pa-2">
                          <v-col cols="2">
                            <v-avatar v-if="index === 0" rounded size="43">
                              <v-img contain src="@/assets/Marketplace_partner/win1.png">
                              </v-img>
                            </v-avatar>
                            <v-avatar v-else-if="index === 1" rounded size="43">
                              <v-img contain src="@/assets/Marketplace_partner/win2.png">
                              </v-img>
                            </v-avatar>
                            <v-avatar v-else-if="index === 2" rounded size="43">
                              <v-img contain src="@/assets/Marketplace_partner/win3.png">
                              </v-img>
                            </v-avatar>
                            <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                              {{ index + 1 }}
                            </v-avatar>
                          </v-col>
                          <v-col cols="10" class="d-flex justify-space-between align-center">
                            <span class="two-lines">Package : {{ item.package_name }}</span>
                            <v-chip class="custom-chip" color="#27AB9C0D" style="border-radius: 40px;">
                              <span class="vchipFontSize" style="color: #27AB9C">
                                <b>{{ item.transactions }} รายการ</b>
                              </span>
                            </v-chip>
                          </v-col>
                        </v-row>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card>
            <v-card v-else class="mt-4" style="border-radius: 8px;  background: transparent;" elevation="0">
              <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการ Package</h1>
            </v-card>
          </v-col>
        </v-row>
      </v-card>
    </div>
    <div v-if="!MobileSize || MobileSize && selectedTypeData === 'Top 10 ร้านค้าที่ซื้อมูลค่าสะสมมากที่สุด'">
      <v-row :class="MobileSize ? '' : 'pt-8'">
        <v-col cols="12">
          <span class="subTitleText">Top 10 ร้านค้าที่ซื้อมูลค่าสะสมมากที่สุด</span>
        </v-col>
        <v-col cols="12">
          <v-card v-if="filledRanksShopBuying.length !== 0" class="mt-2" style="border-radius: 8px;" elevation="0">
            <v-row>
              <v-col cols="12" md="6" sm="12" xs="12">
                <v-row no-gutters>
                  <v-col v-for="(item, index) in filledRanksShopBuying.slice(0, 5)" :key="index" cols="12" class="mb-4">
                    <v-card height="100%" style="border: 1px solid #FDF9EC;" elevation="0">
                      <v-row class="pa-1">
                        <v-col cols="2">
                          <v-avatar v-if="index === 0" rounded size="43">
                            <v-img contain src="@/assets/Marketplace_partner/win1.png">
                            </v-img>
                          </v-avatar>
                          <v-avatar v-else-if="index === 1" rounded size="43">
                            <v-img contain src="@/assets/Marketplace_partner/win2.png">
                            </v-img>
                          </v-avatar>
                          <v-avatar v-else-if="index === 2" rounded size="43">
                            <v-img contain src="@/assets/Marketplace_partner/win3.png">
                            </v-img>
                          </v-avatar>
                          <v-avatar v-else size="43" color="#F9FAFD" class="listOrderNum">
                            {{ index + 1 }}
                          </v-avatar>
                        </v-col>
                        <v-col cols="2" class="pl-0">
                          <v-avatar v-if="item.shop_logo === null || item.shop_logo === ''" rounded size="44" color="#FFF">
                            <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                          </v-avatar>
                          <v-avatar v-else rounded size="44" color="#FFF">
                            <v-img contain :src="item.shop_logo"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="8" class="d-flex justify-space-between align-center">
                          <span class="two-lines">{{ item.name_th }}</span>
                          <v-chip class="custom-chip" color="#F2FDFF" style="border-radius: 40px;">
                            <span class="vchipFontSize" style="color: #177FA5">
                              <b>{{ Number( item.total_spending ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} บาท</b>
                            </span>
                          </v-chip>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
              <v-col cols="12" md="6" sm="12" xs="12">
                <v-row no-gutters>
                  <v-col v-for="(item, index) in filledRanksShopBuying.slice(5, 10)" :key="index" cols="12" class="mb-4">
                    <v-card height="100%" style="border: 1px solid #FDF9EC;" elevation="0">
                      <v-row class="pa-1">
                        <v-col cols="2">
                          <v-avatar size="43" color="#F9FAFD" class="listOrderNum">
                            {{ index + 6 }}
                          </v-avatar>
                        </v-col>
                        <v-col cols="2" class="pl-0">
                          <v-avatar v-if="item.shop_logo === null || item.shop_logo === ''" rounded size="44" color="#FFF">
                            <img src="@/assets/NoImage.png" alt="Product Image" class="avatar-image" width="50" />
                          </v-avatar>
                          <v-avatar v-else rounded size="44" color="#FFF">
                            <v-img contain :src="item.shop_logo"></v-img>
                          </v-avatar>
                        </v-col>
                        <v-col cols="7" class="pl-0 pr-0 d-flex justify-space-between align-center">
                          <span class="two-lines">{{ item.name_th }}</span>
                          <v-chip class="custom-chip" color="#F2FDFF" style="border-radius: 40px;">
                            <span class="vchipFontSize" style="color: #177FA5">
                              <b>{{ Number( item.total_spending ).toLocaleString(undefined, {minimumFractionDigits: 0}) }} บาท</b>
                            </span>
                          </v-chip>
                        </v-col>
                      </v-row>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card>
          <v-card class="mt-1" v-else elevation="0">
            <h1 style="font-size: 18px;font-style: normal;font-weight: 400;color: #C4C4C4">ไม่มีรายการร้านค้าที่ซื้อ</h1>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <div v-if="!MobileSize || MobileSize && selectedTypeData === 'รายละเอียดข้อมูลการใช้งาน'">
      <v-row :class="MobileSize ? '' : 'pt-5'" v-if="listGroupStatus.cancel === 0">
        <v-col align="center" justify="center" cols="12" sm="4" md="4" lg="4">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/shop.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้าที่เชื่อมต่อทั้งหมด</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 30px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.all }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col align="center" justify="center" cols="12" sm="4" md="4" lg="4">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/connect.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้ากำลังใช้งาน</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 30px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.active }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col align="center" justify="center" cols="12" sm="4" md="4" lg="4">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/cancel.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้าที่ยกเลิกการใช้งาน</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.deleted }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <div v-if="!MobileSize || MobileSize && selectedTypeData === 'รายละเอียดข้อมูลการใช้งาน'">
      <v-row :class="MobileSize ? '' : 'pt-5'" v-if="listGroupStatus.cancel > 0">
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/shop.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้าที่เชื่อมต่อทั้งหมด</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 30px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.all }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/connect.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้ากำลังใช้งาน</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 30px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.active }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/suspended.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้าที่ระงับการใช้งาน</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 30px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.cancel }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
        <v-col align="center" justify="center" cols="12" sm="6" md="6" lg="3">
          <v-card style="border-radius: 8px; border: 2px solid #BDE7D9; background: transparent;" elevation="0" class="pa-2">
            <v-row>
              <v-col cols="12">
                <v-avatar rounded size="50">
                  <v-img contain src="@/assets/Marketplace_partner/cancel.png"></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">ร้านค้าที่ยกเลิกการใช้งาน</span>
              </v-col>
              <v-col cols="12">
                <span style="font-size: 32px; font-style: normal; font-weight: 700; color:#38C9B9">{{ listGroupStatus.deleted }}</span>
              </v-col>
              <v-col cols="12">
              <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">ร้านค้า</span>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <div v-if="!MobileSize || MobileSize && selectedTypeData === 'รายละเอียดข้อมูลการใช้งาน'">
      <v-row :class="MobileSize ? '' : 'pt-5'">
        <v-col align="center" justify="center" cols="12">
          <!-- <span>จำนวน Transactions ที่ใช้งานจริงทั้งหมด</span> -->
          <v-card style="border-radius: 8px;background: #F6FAFFCC;" elevation="0" class="pa-2">
            <v-row align="center" justify="center">
              <!-- <v-col cols="12" md="2">
              </v-col> -->
              <v-col cols="auto">
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#333333">จำนวน Transactions ที่ใช้งานจริงทั้งหมด</span><br>
                <span style="font-size: 40px; font-style: normal; font-weight: 700; color:#1B5DD6">{{ totalTransaction.toLocaleString() }}</span><br>
                <span style="font-size: 18px; font-style: normal; font-weight: 400; color:#989898">รายการ</span>
              </v-col>
              <v-col cols="auto">
                <v-avatar rounded size="100">
                  <v-img contain src="@/assets/Marketplace_partner/transactions.png"></v-img>
                </v-avatar>
              </v-col>
              <!-- <v-col cols="12" md="2">
              </v-col> -->
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </div>
    <div v-if="!MobileSize || MobileSize && selectedTypeData === 'รายการสั่งซื้อสินค้าทั้งหมด'">
      <v-row :class="MobileSize ? '' : 'pt-5'">
        <v-col cols="12">
          <v-avatar rounded size="24">
            <v-img contain src="@/assets/Marketplace_partner/data-model.png"></v-img>
          </v-avatar>
          <span class="subTitleText ml-2">รายการสั่งซื้อสินค้าทั้งหมด ( {{ this.totalOrder }} รายการ)</span>
        </v-col>
        <v-col cols="12">
          <v-card elevation="0">
            <v-data-table
              :headers="headers"
              :items="listOrderPackage"
              :items-per-page="10"
              class="elevation-1"
              item-key="i"
              no-data-text="ไม่มีรายการสั่งซื้อสินค้า"
              no-results-text="ไม่พบรายการสั่งซื้อสินค้า"
            >
              <template v-slot:[`item.service_connect`]="{ item }">
                {{ new Date(item.service_connect).toLocaleDateString('th-TH', {
                  timeZone: "UTC",
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric' })
                }}
              </template>
              <template v-slot:[`item.contract_start`]="{ item }">
                {{ new Date(item.contract_start).toLocaleDateString('th-TH', {
                  timeZone: "UTC",
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric' })
                }}
              </template>
              <template v-slot:[`item.contract_end`]="{ item }">
                {{ new Date(item.contract_end).toLocaleDateString('th-TH', {
                  timeZone: "UTC",
                  year: 'numeric',
                  month: 'numeric',
                  day: 'numeric' })
                }}
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                <span>{{ Number(item.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
              <template v-slot:[`item.service_fee`]="{ item }">
                <span>{{ Number(item.service_fee).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
              <template v-slot:[`item.service_payment_period`]="{ item }">
                <span>{{ Number(item.service_payment_period).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script>
export default {
  props: {
    payload: {
      type: Object
    }
  },
  data () {
    return {
      selectedTypeData: 'Top 5 Package ขายดี',
      TypeDataItems: [
        'Top 5 Package ขายดี',
        'Top 10 ร้านค้าที่ซื้อมูลค่าสะสมมากที่สุด',
        'รายละเอียดข้อมูลการใช้งาน',
        'รายการสั่งซื้อสินค้าทั้งหมด'
      ],
      taxId: '',
      partnerCode: '',
      ranksPackage: [],
      ranksShopBuying: [],
      listOrderPackage: [],
      listGroupStatus: [],
      totalTransaction: '',
      totalOrder: '',
      headers: [
        { text: 'ชื่อร้านค้า', width: '150', align: 'center', sortable: false, value: 'shop_name', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', width: '200', align: 'center', sortable: false, value: 'order_number', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ - นามสกุล ผู้เข้าร่วม', width: '200', sortable: false, align: 'center', value: 'full_name', class: 'backgroundTable fontTable--text' },
        { text: 'อีเมล', width: '100', sortable: false, align: 'center', value: 'seller_email', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทรศัพท์', width: '200', sortable: false, align: 'center', value: 'phone', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อ Package', width: '150', sortable: false, align: 'center', value: 'package_name', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่เข้าร่วมบริการ', width: '150', align: 'center', sortable: false, value: 'service_connect', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่เริ่มสัญญา', width: '150', align: 'center', sortable: false, value: 'contract_start', class: 'backgroundTable fontTable--text' },
        { text: 'วันที่สิ้นสุดสัญญา', width: '150', sortable: false, align: 'center', value: 'contract_end', class: 'backgroundTable fontTable--text' },
        { text: 'ยอดที่ชำระเงิน', width: '120', sortable: false, align: 'center', value: 'total_amount', class: 'backgroundTable fontTable--text' },
        { text: 'ค่าธรรมเนียม', width: '120', sortable: false, align: 'center', value: 'service_fee', class: 'backgroundTable fontTable--text' },
        // { text: 'คูปอง/ส่วนลด', width: '120', sortable: false, align: 'center', value: 'service_coupon', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนงวดชำระเงิน', width: '150', sortable: false, align: 'center', value: 'service_payment_period', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filledRanksPackage () {
      if (this.ranksPackage.length === 0) {
        // console.log(this.ranksPackage)
        return this.ranksPackage
      } else {
        var items = [...this.ranksPackage]
        // console.log(items)
        while (items.length < 5) {
          items.push({ package_code: '', package_name: '-', transactions: 0 })
        }
        return items
      }
    },
    filledRanksShopBuying () {
      if (this.ranksShopBuying.length === 0) {
        // console.log(this.ranksShopBuying)
        return this.ranksShopBuying
      } else {
        var items = [...this.ranksShopBuying]
        // console.log(items)
        while (items.length < 10) {
          items.push({ name_th: '-', seller_shop_id: '', shop_logo: '', total_spending: 0 })
        }
        return items
      }
    }
  },
  watch: {
    payload: {
      async handler (newValue, oldValue) {
        await this.DataRanksPackage()
        await this.DataListOrderPackage()
        await this.DataRanksShopBuying()
      },
      deep: true
    }
  },
  async created () {
    await this.DataRanksPackage()
    await this.DataListOrderPackage()
    await this.DataRanksShopBuying()
  },
  methods: {
    async DataRanksPackage () {
      // var data = {
      //   partner_code: this.partnerCode,
      //   date_start: '2025-01-01',
      //   date_end: '2025-12-31'
      // }

      await this.$store.dispatch('actionsRanksPackage', this.payload)
      var response = await this.$store.state.ModuleBusiness.stateRanksPackage

      if (response.message === 'list ranks package success.') {
        this.ranksPackage = response.data
        // console.log(this.ranksPackage)
      }
    },
    async DataRanksShopBuying () {
      // var data = {
      //   partner_code: this.partnerCode,
      //   date_start: '2025-01-01',
      //   date_end: '2025-12-31'
      // }

      await this.$store.dispatch('actionsRanksShopBuying', this.payload)
      var response = await this.$store.state.ModuleBusiness.stateRanksShopBuying

      if (response.message === 'list ranks spending success.') {
        this.ranksShopBuying = response.data.ranks_spending
        this.listGroupStatus = response.data.group_status.status
        this.totalTransaction = response.data.total_transaction
        // console.log(this.ranksShopBuying)
        // console.log(this.listGroupStatus)
        // console.log(this.totalTransaction)
      }
    },
    async DataListOrderPackage () {
      // var data = {
      //   partner_code: this.partnerCode,
      //   date_start: '2025-01-01',
      //   date_end: '2025-12-31'
      // }

      await this.$store.dispatch('actionsListOrderPackage', this.payload)
      var response = await this.$store.state.ModuleBusiness.stateListOrderPackage

      if (response.message === 'list order success.') {
        this.listOrderPackage = response.data.list_order_package
        this.totalOrder = response.data.total_order
        // console.log(this.listOrderPackage)
        // console.log(this.totalOrder)
      }
    }
  }
}
</script>

<style scoped>

.subTitleText {
  font-size: 18px;
  font-style: normal;
  font-weight: 600;
  color: #27AB9C
}

.listOrderNum {
  font-size: 18px;
  font-style: normal;
  font-weight: 700;
}

.vchipFontSize {
  font-size: 14px;
  font-style: normal;
  font-weight: 600;
}

</style>
