<template>
  <v-container style="background-color: #fff;" :class="MobileSize ? 'mt-6' : ''">
    <HeaderUI />
  <!-- <ContentUI /> -->
  </v-container>
</template>
<script>
export default {
  data () {
    return {
      freq: false,
      dataFreq: [],
      dataSum: [],
      headers2: [],
      SETT: []
    }
  },
  components: {
    HeaderUI: () => import('@/components/Revenue/header')
    // ContentUI: () => import('@/components/Revenue/content')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/revenueMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/revenue' }).catch(() => {})
      }
    }
  }
}
</script>
