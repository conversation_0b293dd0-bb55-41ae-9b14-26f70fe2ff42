<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-4">
      <v-row>
        <!-- หัวข้อเรื่อง -->
        <v-col cols="12" style="display: flex;">
          <v-row>
          <v-col :cols="MobileSize ? 12 : 6">
            <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายละเอียดร้านค้า</v-card-title>
            <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายละเอียดร้านค้า</v-card-title>
          </v-col>
          <v-col :cols="MobileSize ? 6 : 3" class="pl-6" v-if="itemStatus !== 'ยกเลิกคำสั่งซื้อ' && itemStatus !== 'เกินกำหนดชำระ'">
            <v-btn outlined color="#38b2a4" rounded block @click="openCancelDialog" class="pl-3">
              <span>ยกเลิกคำสั่งซื้อ</span>
            </v-btn>
          </v-col>
          <v-spacer v-else-if="MobileSize === false"></v-spacer>
          <!-- <v-col cols="12" sm="3" v-if="showDetailOrder[0].transaction_status !== 'Cancel' && MobileSize === true">
            <v-btn outlined color="#38b2a4" rounded block @click="openCancelDialog">
              <span>ยกเลิกคำสั่งซื้อ</span>
            </v-btn>
          </v-col> -->
          <v-col :cols="MobileSize ? 6 : 3" :class="transactionStatus === 'Cancel' ? 'pl-6' : 'pr-6'" v-if="trackingNumber === '-'">
            <v-btn color="#38b2a4" rounded block @click="openDeliveryDialog">
              <span class="white--text">จัดส่งสินค้า</span>
            </v-btn>
          </v-col>
          <!-- <v-col cols="3" v-else-if="showDetailOrder[0].tracking_number !== '-' && MobileSize === true">
            <v-btn color="#38b2a4" rounded block @click="openDeliveryDialog">
              <span class="white--text">จัดส่งสินค้า</span>
            </v-btn>
          </v-col> -->
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
            <v-card-text>
              <v-row dense style="display: flex; gap: 1vw;">
                <!-- ข้อมูลส่วน box แรก -->
                <v-col cols="12" md="6" sm="12" :style="MobileSize ? 'background: #ECEFF4; padding: 6vw; border-radius: 10px;' : 'background: #F9FAFD; padding: 3vw; border-radius: 20px;'">
                  <v-row>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ : </span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ paymentTransaction }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ : </span>
                      <span v-if="BuyerName !== null" style="font-size: 16px; font-weight: 400; color: #333333;">{{ BuyerName }}</span>
                      <span v-else>-</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ : </span>
                      <span v-if="createAt !== '-'" style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(createAt).toLocaleDateString('th-TH', {  timeZone: 'UTC', year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                      <span style="font-size: 16px; font-weight: 400; color: #333333;" v-else>{{ createAt }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ชำระเงิน : </span>
                      <span v-if="paidDatetime !== '-'" style="font-size: 16px; font-weight: 400; color: #333333;">{{new Date(paidDatetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                      <span v-else>{{ paidDatetime }}</span>
                    </v-col>
                    <v-col cols="12">
                      <span style="font-size: 16px; font-weight: 600; color: #333333;">ใบกำกับภาษี : </span>
                      <span v-if="transactionCode === '-'" style="font-size: 16px; font-weight: 400; color: #333333;">ขอใบกำกับภาษี</span>
                      <!-- <v-icon color="#27ab9c" size="35px" v-else-if="showDetailOrder[0].transactionCode !== '-'" @click="GetETax(showDetailOrder[0].transactionCode)">mdi-file-document</v-icon> -->
                      <a v-else-if="transactionCode !== '-'" @click="GetETax(transactionCode)">
                        <span style="font-size: 16px; font-weight: 400; color: #27ab9c; border-bottom: 1px solid;">{{ paymentTransaction }}</span>
                      </a>
                      <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">ไม่มีใบกำกับภาษี</span>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- ข้อมูลส่วน box สอง -->
                <v-col style="background: #F9FAFD; padding: 2vw; border-radius: 20px;">
                  <v-card elevation="0" :width="!IpadSize && !MobileSize ? '100%' : '100%'" height="100%" style="border-radius: 8px; background: #FFFFFF; padding: 2vw;">
                    <v-card-text>
                      <v-row :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">
                        <v-col cols="12">
                          <span style="font-weight: 600;">สถานะการสั่งซื้อ : </span>
                          <!-- <span v-if="showDetailOrder[0].buyer_received_status === 'received' " style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                          <span v-else-if="showDetailOrder[0].buyer_received_status === 'not_received' " style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span>
                          <span v-else style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ showDetailOrder[0].transportation_status }}</span> -->
                          <span v-if="deliveryStatus === 9">
                            <!-- <span v-if="showDetailOrder[0].buyer_received_status === 'received'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                            <span v-else-if="showDetailOrder[0].buyer_received_status === 'not_received'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ orderStatus }}</span> -->
                            <span v-if="orderStatus === 'ชำระเงินสำเร็จ'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                            <span v-else-if="orderStatus === 'ยังไม่ชำระเงิน'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                            <span v-else-if="orderStatus === 'ยกเลิกคำสั่งซื้อ'" style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                            <span v-else>{{ orderStatus }}</span>
                          </span>
                          <span v-else-if="deliveryStatus === 0" style="color: #D1392B; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                          <span v-if="orderStatus === 'ชำระเงินสำเร็จ'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                          <span v-else-if="orderStatus === 'ยังไม่ชำระเงิน'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                          <span v-else-if="orderStatus === 'ยกเลิกคำสั่งซื้อ'" style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                          <!-- <span v-else-if="!deliveryStatus" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                          <span v-else-if="deliveryStatus !== 9 && deliveryStatus !== 1 && deliveryStatus !== 0" style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ orderStatus }}</span>
                          <span v-else style="color: #D1392B; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ orderStatus }}</span> -->
                        </v-col>
                        <v-col cols="12">
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">วัน-เวลาส่งสินค้า : </span>
                          <span v-if="deliveryStatus !== 1 && tracking.send_date !== '-'">{{new Date(tracking.send_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                          <span v-else-if="deliveryStatus !== 1">{{ tracking.send_date }}</span>
                        </v-col>
                        <v-col cols="12">
                          <span style="font-size: 16px; font-weight: 600; color: #333333;">วัน-เวลารับสินค้า : </span>
                          <span v-if="tracking.received_date !== '-'">{{new Date(tracking.received_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                          <span v-else-if="deliveryStatus === 9">{{ tracking.received_date }}</span>
                          <span v-else>{{ tracking.received_date }}</span>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              </v-row>
            </v-card-text>
            <v-col cols="12" class="pt-6" v-if="MobileSize">
              <v-divider></v-divider>
            </v-col>
            <!-- รายละเอียดเอกสาร -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 7.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row style="display: flex;">
                <v-col cols="12" style="display: flex; justify-content: space-between; align-items: center;">
                  <div style="display: flex; align-items: center;">
                    <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/docCompany.png')" max-height="30" max-width="30"></v-img>
                    <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                      รายละเอียดเอกสาร
                    </span>
                  </div>
                  <v-btn outlined color="#38b2a4" rounded @click="openPdfUploadDialog">
                    <span>เพิ่มรายละเอียดเอกสาร</span>
                  </v-btn>
                </v-col>
              </v-row>
              <v-row>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสนอราคา : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ paymentTransaction }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบขอซื้อ (PR) : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ prDocumentId }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบสั่งซื้อ (PO) : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ poDocumentId }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ Sale Order : </span>
                  <span style="font-size: 16px; font-weight: 400; color: #333333;">{{ refCallbackSoId }}</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span style="font-size: 16px; font-weight: 600; color: #333333;">เลขที่ใบเสร็จ : </span>
                  <span v-if="receipt.orderIDRef !== null">{{ receipt.orderIDRef }}</span>
                  <span v-else style="font-size: 16px; font-weight: 400; color: #333333;">-</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- ที่อยู่ในการจัดส่งสินค้า -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/mapCompany.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  ที่อยู่ในการจัดส่งสินค้า
                </span>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="12" sm="12">
                  <v-row class="pt-6">
                    <v-col cols="12" style="font-size: 16px; font-weight: 400; color: #333333;">
                      <span v-if="userAddress !== ''">
                        <span v-if="invoiceAddress === [] || invoiceAddress === '-'">
                          {{ userAddress }}
                        </span>
                        <span v-else>
                          {{ invoiceAddress[0].first_name + ' ' + invoiceAddress[0].last_name }} {{ userAddress }}
                        </span>
                      </span>
                      <span v-else style="color: #7777;">ไม่มีที่อยู่ในการจัดส่งสินค้า</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- ที่อยู่ในการจัดส่งใบกำกับภาษี -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/mapCom2.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  ที่อยู่ในการจัดส่งใบกำกับภาษี
                </span>
              </v-row>
              <v-row dense>
                <v-col cols="12" md="12" sm="12">
                  <v-row class="pt-6">
                    <v-col cols="12" style="font-size: 16px; font-weight: 400; color: #333333;">
                      <span v-if="invoiceAddress.length !== 0 && invoiceAddress !== '-'">
                        <span v-if="invoiceAddress.length !== 0 && invoiceAddress !== null">
                          {{ invoiceAddress[0].first_name + ' ' + invoiceAddress[0].last_name }}
                          {{ invoiceAddress[0].detail }} หมู่บ้าน {{ invoiceAddress[0].street }} แขวง/ตำบล {{ invoiceAddress[0].sub_district }}
                          เขต/อำเภอ {{ invoiceAddress[0].district }} จังหวัด {{ invoiceAddress[0].province }} รหัสไปรษณีย์ {{ invoiceAddress[0].zip_code }}
                          เบอร์มือถือ {{ invoiceAddress[0].phone }} เบอร์โทรศัพท์ {{ invoiceAddress[0].phone_ext === null ? '-' : invoiceAddress[0].phone_ext}}
                        </span>
                        <span v-if="invoiceAddress.phone_ext === null">-</span>
                      </span>
                      <span v-else style="color: #7777;">ไม่มีที่อยู่ในการจัดส่งใบกำกับภาษี</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- รายการสั่งซื้อสินค้า -->
            <v-col cols="12" :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/cart.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  รายการสั่งซื้อสินค้า
                </span>
              </v-row>
              <v-row>
                <v-col cols="12">
                  <span style="font-size: 16px; font-weight: 700;">สินค้าทั่วไป</span>
                  <v-data-table
                  :headers="productsTableHeader"
                  :items="productList"
                  style="width:100%; text-align: center !important;"
                  height="100%"
                  @pagination="countRequest"
                  :items-per-page="5"
                  class="elevation-1 mt-4"
                  :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                  >
                  <template v-slot:[`item.sku`]="{ item }">
                    <span style="white-space: nowrap;">{{ item.sku }}</span>
                  </template>
                  </v-data-table>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6" v-if="MobileSize">
              <v-divider></v-divider>
            </v-col>
            <!-- โปรโมชันและคูปองส่วนลด -->
            <v-col cols="12" :style="MobileSize ? 'padding: 1vw 2vw 2vw 5.5vw;' : 'padding: 1vw 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/coupon.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  โปรโมชันและคูปองส่วนลด
                </span>
              </v-row>
              <v-row>
                <v-col cols="12" style="background-color: #e6f5f3; padding: 1vw; font-size: medium;">
                  <span>ส่วนลดจากแต้ม {{ totalPriceDiscount }} บาท</span>
                  <!-- <span>ส่วนลดจากแต้ม 0 บาท</span> -->
                </v-col>
              </v-row>
              <v-row>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
                  <span>{{ Number(totalPriceNoVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ส่วนลด</span>
                  <span>{{ Number(totalPriceDiscount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ภาษีมูลค่าเพิ่ม</span>
                  <span>{{ Number(totalVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
                  <span>{{ Number(totalPriceVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span>ค่าจัดส่ง</span>
                  <span>{{ totalShipping }} บาท</span>
                </v-col>
                <v-col cols="12" :style="MobileSize ? 'display: flex; justify-content: space-between; margin-top: -6.5vw;' : 'display: flex; justify-content: space-between; margin-top: -1.5vw;'">
                  <span style="font-size: 12px; color: #bbb;">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไป <br> ขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่จัดส่งจะติดต่อคุณ</span>
                </v-col>
                <v-col cols="12" style="display: flex; justify-content: space-between;">
                  <span style="font-size: 18px; font-weight: 700;">ราคารวมทั้งหมด</span>
                  <span style="color: rgb(39, 171, 156); font-size: 18px; font-weight: 700;">{{ Number(netPrice).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- รายละเอียดรายการสั่งซื้อ -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense>
                <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/detailSell.png')" max-height="30" max-width="30"></v-img>
                <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                  รายละเอียดรายการสั่งซื้อ
                </span>
              </v-row>
              <v-row style="font-size: 16px; font-weight: 600;">
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>วันที่เริ่มสัญญา :</span>
                  <span v-if="startDateContract !== null" style="font-size: 16px; font-weight: 400; color: #333333;">{{ startDateContract }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>วันที่สิ้นสุดสัญญา :</span>
                  <span v-if="endDateContract !== null" style="font-size: 16px; font-weight: 400; color: #333333;">{{ endDateContract }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>Pay Type : </span>
                  <v-chip v-if="payType === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)"> One Time</v-chip>
                  <v-chip v-else-if="payType === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                  <v-chip v-else-if="payType === 'general'" text-color="#808c96" color="#eff2f2">general</v-chip>
                  <span v-else>-</span>
                  <!-- <v-chip class="ma-2" color="green" text-color="white">Green Chip</v-chip> -->
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>ส่วนลด : </span>
                  <span>{{ Number(totalPriceDiscount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>หมวดงบประมาณ : </span>
                  <span v-if="typeBudget !== null">{{ typeBudget }} บาท</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>หมวดตัดงบ : </span>
                  <span v-if="budgetCut !== null">{{ budgetCut }} บาท</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 4)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span>หมายเหตุ : </span>
                  <span>{{ remark }}</span>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-6">
              <v-divider></v-divider>
            </v-col>
            <!-- การชำระเงิน -->
            <v-col :style="MobileSize ? 'padding: 0 2vw 2vw 5.5vw;' : 'padding: 0 2vw 2vw 2.5vw;'">
              <v-row dense style="display: flex; justify-content: space-between;">
                <div class="d-flex">
                  <v-img :src="require('@/assets/ImageINET-Marketplace/OrderDetail/pay.png')" max-height="30" max-width="30"></v-img>
                  <span style="font-size: 18px; font-weight: 700; color: #333333;" class="pl-3">
                    การชำระเงิน
                  </span>
                  <span v-if="itemStatus === 'ชำระเงินแล้ว'" style="font-size: 16px; font-weight: 400; color: #16D2A5;"><v-icon color="#16D2A5">mdi-circle-medium</v-icon>{{ itemStatus }}</span>
                  <span v-if="itemStatus === 'รอชำระเงิน'" style="color: #FAAD14; font-size: 16px;"><v-icon color="#FAAD14">mdi-circle-medium</v-icon>{{ itemStatus }}</span>
                  <span v-if="itemStatus === 'ยกเลิกคำสั่งซื้อ' || itemStatus === 'เกินกำหนดชำระ'" style="color: #d1392b; font-size: 16px;"><v-icon color="#d1392b">mdi-circle-medium</v-icon>{{ itemStatus }}</span>
                  <!-- <span v-else style="color: #D1392B; font-size: 16px;"><v-icon color="#D1392B">mdi-circle-medium</v-icon>{{ showDetailOrder[0].item_status }}</span> -->
                </div>
                <!-- <div>
                  <v-chip small text-color="#E9A016" color="#FCF0DA" rounded>
                    <span class="pt-1">รอร้านค้าอนุมัติคำสั่งซื้อ</span>
                  </v-chip>
                </div> -->
              </v-row>
              <v-row v-if="itemStatus === 'ชำระเงินแล้ว'">
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการชำระเงิน : </span>
                  <span v-if="receipt.id !== null">{{ receipt.orderId }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">จำนวนเงิน : </span>
                  <span v-if="receipt.id !== null">{{ Number(receipt.TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">วันเวลาที่ชำระเงิน : </span>
                  <span>{{new Date(tracking.paid_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ref : </span>
                  <span v-if="receipt.id !== null">{{ receipt.orderIDRef }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รูปแบบการชำระเงิน : </span>
                  <span v-if="receipt.id !== null">{{ receipt.payType }}</span>
                  <span v-else>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ผลการชำระเงิน : </span>
                  <span v-if="receipt.id !== null">
                    <span v-if="receipt.rmsg === 'Success'">{{ itemStatus }}</span>
                    <span v-else-if="receipt.rmsg === 'Fail'">{{ itemStatus }}</span>
                    <span v-if="receipt.rmsg === 'Cancel'">{{ itemStatus }}</span>
                  </span>
                  <span v-else>-</span>
                </v-col>
              </v-row>
              <v-row v-else>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการชำระเงิน : </span>
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">จำนวนเงิน : </span>
                  <!-- <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ Number(showDetailOrder[0].receipt[0].TxnAmount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} บาท</span> -->
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">วันเวลาที่ชำระเงิน : </span>
                  <!-- <span>{{new Date(showDetailOrder[0].tracking.paid_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}} น.</span> -->
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ref : </span>
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">รูปแบบการชำระเงิน : </span>
                  <!-- <span v-if="showDetailOrder[0].receipt[0].id !== null">{{ showDetailOrder[0].receipt[0].payType }}</span> -->
                  <span>-</span>
                </v-col>
                <v-col :cols="MobileSize ? 12 : (IpadSize ? 12 : 6)" :style="MobileSize ? 'display: flex; justify-content: space-between;' : (IpadSize ? 'display: flex; justify-content: space-between;' : '' )">
                  <span class="mr-auto" style="font-size: 16px; font-weight: 600; color: #333333;">ผลการชำระเงิน : </span>
                  <span>-</span>
                </v-col>
              </v-row>
            </v-col>
          </v-card>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog
      v-model="cancelDialogOpen"
      persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'"
      content-class="elevation-0"
    >
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ยกเลิกคำสั่งซื้อ</b></span>
              </v-col>
              <v-btn fab small @click="closeCancelDialog()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
              <v-card-text class="pa-0">
                <!-- ข้อมูล order -->
                <v-row dense class="d-flex pa-4" style="background: #F9FAFD; border-radius: 8px;">
                  <v-col cols="12" md="12" sm="12" class="pt-3 pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">รหัสการสั่งซื้อ : {{paymentTransaction}}</span> <span style="font-size: 16px; font-weight: 400; color: #333333;"></span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">ผู้ซื้อ : {{BuyerName}}</span> <span style="font-size: 16px; font-weight: 400; color: #333333;"></span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">วันที่ทำรายการ : {{new Date(createAt).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric' })}}</span> <span style="font-size: 16px; font-weight: 400; color: #333333;">น.</span>
                  </v-col>
                </v-row>
                <v-row dense class="mt-5">
                  <v-col cols="12" md="12" sm="12" class="pb-4">
                    <span style="font-size: 16px; font-weight: 600; color: #333333;">เหตุผลยกเลิกคำสั่งซื้อ</span>
                  </v-col>
                  <v-col cols="12" md="12" sm="12">
                    <v-textarea v-model="reason" :counter="250" maxLength="250" outlined placeholder="กรุณาระบุเหตุผลยกเลิกคำสั่งซื้อ" style="border-radius: 8px; border: 1px solid #CCC;" height="160" hide-details></v-textarea>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="CloseModalInputReason()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" :disabled="reason !== '' ? false : true" height="40" class="white--text" @click="confirmCancelOrder()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="modalAwaitCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogConfirm()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกคำสั่งซื้อ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณแน่ใจหรือไม่ว่าต้องการยกเลิก</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คำสั่งซื้อรายการนี้</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogConfirmCancel()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="confirmCancel()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
     <v-dialog v-model="deliverDialogOpen" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '750'" content-class="elevation-0">
      <v-form ref="modalTrackingOwnform">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
          <v-card-text class="px-0 pt-0">
            <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 750px'" class="backgroundHead" style="position: absolute; height: 120px;">
              <v-row style="height: 120px;">
                <v-col style="text-align: center;" class="pt-4">
                  <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ข้อมูลการจัดส่ง</b></span>
                </v-col>
                <v-btn fab small @click="closeDeliveryDialog()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '750px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="!MobileSize ? 'padding: 40px 48px 10px 48px;' : 'padding: 40px 15px 10px 15px'">
                <v-card-text class="pa-0">
                  <v-row dense justify="center">
                    <v-col cols="12" align="center">
                      <v-img :src="require('@/assets/ImageINET-Marketplace/Shop/owntracking.png')" max-height="147" max-width="320" width="100%" height="100%"></v-img>
                    </v-col>
                  </v-row>
                  <v-row dense justify="center" class="mt-10">
                    <!-- รูปแบบการจัดส่ง -->
                    <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color: #333333; line-height: 22px;">รูปแบบการจัดส่ง <span style="color: red;">*</span></span>
                      <v-select v-model="ownShipping" outlined dense style="border-radius: 8px;" placeholder="เลือกรูปแบบการจัดส่ง" class="setCustomSelect" :items="dataShipping" item-text="ship_name" item-value="ship_name" :rules="itemRules.selectOwnShipping"></v-select>
                    </v-col>
                    <!-- Tracking Number -->
                    <v-col cols="12">
                      <span style="font-size: 16px; font-style: normal; font-weight: 400; color: #333333; line-height: 22px;">Tracking Number <span style="color: red;">*</span></span>
                      <v-text-field v-model="trackingNumOwn" outlined dense style="border-radius: 8px;" placeholder="กรอก Tracking Number" :rules="itemRules.trackingNumOwn"></v-text-field>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </div>
          </v-card-text>
          <v-card-actions style="height: 88px; background-color: #F5FCFB;">
            <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closeDeliveryDialog()">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <v-btn color="#27AB9C" rounded width="125" :disabled="(ownShipping !== '' && trackingNumOwn !== '') ? false : true" height="40" class="white--text" @click="confirmUpdateTracking()">ยืนยัน</v-btn>
          </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <v-dialog v-model="dialogUploadPDF" width="752px" persistent scrollable>
      <v-form ref="formUploadPDF">
        <v-card style="border-radius: 24px; background-color: #27AB9C;">
          <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
            <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">อัปโหลดเอกสาร
            </span>
            <v-btn icon dark @click="closePdfUploadDialog()">
              <v-icon color="#FFFFFF">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-card style="border-radius: 20px;">
            <v-card-text>
              <div>
                <v-col class="pl-3">
                  <span style="font-size: 16px; font-weight: 600; line-height: 22.4px; color: #333333;">เอกสารที่ต้องอัปโหลด <span style="font-size: 12px; font-weight: 400; line-height: 16.8px; color: #CCCCCC;">(รองรับไฟล์นามสกุล .pdf และขนาดไฟล์ไม่เกิน 5mb)</span></span>
                </v-col>
                <div>
                  <v-col class="pt-0" v-for=" (item, index) in itemsDoc" :key="index">
                    <span>เอกสาร {{item.text}}</span>
                     <v-card class="mt-4 rounded-lg" style="border: solid 1px #F3F5F7;" elevation="0" width="100%" height="100%">
                      <v-card-text class="pa-2">
                        <v-row class="pa-2">
                          <v-col cols="3" md="2" xs="2" sm="2">
                            <v-img
                              src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png"
                              width="55px"
                              height="55px"
                              contain>
                            </v-img>
                          </v-col>
                          <v-col cols="7" md="6" xs="12" sm="6">
                            <v-layout row wrap align-center style="margin-top: 10px">
                              <v-flex>
                                <span
                                  style="
                                    font-weight: 600;
                                    font-size: 16px;
                                    line-height: 22px;
                                    color: #333333;
                                  ">
                                  <v-text-field
                                    v-model="item.value"
                                    oninput="this.value = this.value.replace(/[^0-9a-zA-Z-]/g, '').replace(/(\..*)\./g, '$1')"
                                    style="border-radius: 8px;"
                                    dense
                                    outlined
                                    :rules="itemRules.documents"
                                    placeholder="กรุณากรอกชื่อเอกสาร"
                                    :maxLength="25"
                                    >
                                  </v-text-field>
                                </span>
                              </v-flex>
                            </v-layout>
                          </v-col>
                          <v-col
                            cols="12"
                            md="4"
                            sm="4"
                            style="text-align-last: end; margin: auto;"
                            class="rounded-lg"
                            @click="selectIndex(item.value, index , item.file)"
                          >
                            <v-row justify="center" v-if="item.file === ''">
                              <v-file-input
                                outlined
                                v-model="DataFile"
                                :items="DataFile"
                                accept=".pdf"
                                @change="UploadFile($event, item.value, index)"
                                id="file_input"
                                multiple
                                :clearable="false"
                                style="display: none">
                              </v-file-input>
                              <v-btn
                                :disabled="!itemsDoc[index].value"
                                rounded
                                dense
                                outlined
                                color="#27AB9C"
                                style="height: 40px; margin-top: 5px; width: 149px;"
                                @click="onPickFile()"
                              >
                                <v-img src="@/assets/ImageINET-Marketplace/Shop/iconShop/upload.png"></v-img>
                                <span class="ml-2">อัปโหลดไฟล์</span>
                              </v-btn>
                            </v-row>
                            <v-row justify="center" v-else>
                              <v-btn color="#1B5DD6" @click="delete_file(index)" rounded outlined>
                                <span v-if="typeof(item.file) === 'string'">{{item.file|truncate(15, '...')}}</span>
                                <span v-else>{{item.file.name|truncate(15, '...')}}</span>
                                <v-icon>mdi-close</v-icon>
                              </v-btn>
                            </v-row>
                            <v-row
                              class="d-flex justify-end"
                              :class="MobileSize ? 'pr-4':'pr-4'"
                            >
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </div>
              </div>
            </v-card-text>
            <v-card-actions>
              <v-container style="display: flex; justify-content: flex-end">
                <v-col cols="6" class="pa-0">
                  <v-btn
                    rounded
                    dense
                    dark
                    outlined
                    color="#27AB9C"
                    class="pl-7 pr-7 mt-2"
                    @click="closePdfUploadDialog()"
                  >
                    ยกเลิก
                  </v-btn>
                </v-col>
                <v-col cols="6" style="text-align: end;" class="pa-0">
                  <v-btn
                    rounded
                    dense
                    color="#27AB9C"
                    class="ml-4 mt-2 pl-8 pr-8 white--text"
                    @click="confirm()"
                  >
                    บันทึก
                  </v-btn>
                </v-col>
              </v-container>
            </v-card-actions>
          </v-card>
        </v-card>
      </v-form>
    </v-dialog>
    <v-dialog v-model="modalSuccessCancelOrder" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeSuccessCancelOrder()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ยกเลิกคำสั่งซื้อเรียบร้อย</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการยกเลิกคำสั่งซื้อ</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeSuccessCancelOrder()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      showDetailOrder: [],
      // order_number: { order_number: 'OEM-20240801549' },
      productsTableHeader: [
        { text: 'รหัส SKU', value: 'sku', class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียดสินค้า', value: 'product_name', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'revenue_default', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'total_revenue_default', class: 'backgroundTable fontTable--text' }
      ],
      cancelDialogOpen: false,
      deliverDialogOpen: false,
      dialogUploadPDF: false,
      dataShipping: [
        { id: 1, ship_link: 'https://track.thailandpost.co.th/', ship_name: 'THAILAND POST' },
        { id: 2, ship_link: 'https://www.jtexpress.co.th/', ship_name: 'J&T EXPRESS' },
        { id: 3, ship_link: 'https://th.kerryexpress.com/th/track/', ship_name: 'KERRY EXPRESS' },
        { id: 4, ship_link: 'https://www.scgexpress.co.th/tracking/', ship_name: 'SCG EXPRESS' },
        { id: 5, ship_link: 'https://www.dhl.com/th-th/home/<USER>', ship_name: 'DHL EXPRESS' },
        { id: 6, ship_link: 'https://www.best-inc.co.th/track', ship_name: 'BEST EXPRESS' },
        { id: 7, ship_link: 'https://www.ninjavan.co/th-th/tracking', ship_name: 'NINJA VAN' },
        { id: 8, ship_link: 'https://www.flashexpress.co.th/fle/tracking', ship_name: 'FLASH EXPRESS' },
        { id: 9, ship_link: '', ship_name: 'อื่นๆ' }
      ],
      ownShipping: '',
      trackingNumOwn: '',
      itemRules: {
        documents: [
          v => !!v || 'กรุณาระบุชื่อเอกสาร'],
        dateSent: [
          v => !!v || 'กรุณาระบุวันที่ส่ง'],
        time: [
          v => !!v || 'กรุณาระบุเวลา'],
        dateReceived: [
          v => !!v || 'กรุณาระบุวันที่รับ'],
        selectOwnShipping: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการจัดส่ง'
        ],
        trackingNumOwn: [
          v => !!v || 'กรุณากรอก Tracking Number'
        ],
        reason: false
      },
      itemsDoc: [
        { text: 'PO', value: '', file: '' },
        { text: 'PR', value: '', file: '' },
        { text: 'SO', value: '', file: '' }
      ],
      name: '',
      i: 0,
      DataFile: [],
      Detail: {
        product_file: [],
        shop_name_th: '',
        shop_name_en: '',
        shop_description: '',
        path_logo: ''
      },
      shopID: '',
      reason: '',
      getStatusOrder: '',
      modalInputReason: false,
      modalAwaitCancelOrder: false,
      modalSuccessCancelOrder: false,
      showCountRequest: '',
      trackingNumber: '',
      transactionStatus: '',
      paymentTransaction: '',
      BuyerName: '',
      createAt: '',
      paidDatetime: '',
      transactionCode: '',
      deliveryStatus: '',
      transportationStatus: '',
      tracking: {},
      prDocumentId: '',
      poDocumentId: '',
      refCallbackSoId: '',
      receipt: {},
      userAddress: '',
      invoiceAddress: {},
      productList: [],
      totalPriceDiscount: '',
      totalPriceNoVat: '',
      totalVat: '',
      totalPriceVat: '',
      totalShipping: '',
      netPrice: '',
      startDateContract: '',
      endDateContract: '',
      payType: '',
      typeBudget: '',
      budgetCut: '',
      remark: '',
      itemStatus: '',
      orderStatus: ''
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/detailOrderCompanyMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathBusiness', 'detailOrderCompany')
        this.$router.push({ path: '/detailOrderCompany' }).catch(() => {})
      }
    }
    // itemsDoc (val) {
    //   console.log(val)
    // },
    // DataFile (val) {
    //   console.log(val)
    // }
  },
  async created () {
    this.$EventBus.$emit('changeNavAdmin')
    // var id = Number(this.$route.query.seller_shop_id)
    if (localStorage.getItem('oneData') !== null) {
      this.orderDetailCompany()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    await this.useParamsOrderNumber()
  },
  methods: {
    async openCancelDialog () {
      this.cancelDialogOpen = true
    },
    async closeCancelDialog () {
      this.cancelDialogOpen = false
    },
    async openDeliveryDialog () {
      this.deliverDialogOpen = true
    },
    async closeDeliveryDialog () {
      this.$refs.modalTrackingOwnform.resetValidation()
      this.deliverDialogOpen = false
    },
    async openPdfUploadDialog () {
      this.dialogUploadPDF = true
    },
    async closePdfUploadDialog () {
      this.dialogUploadPDF = false
    },
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/listOrderShop' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listOrderShopMobile' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async orderDetailCompany () {
      this.$store.commit('openLoader')
      var body = { order_number: this.$route.query.order_number }
      // console.log('body---->', body)
      await this.$store.dispatch('actionsOrderDetailCompany', body)
      var response = await this.$store.state.ModuleBusiness.stateOrderDetailCompany
      this.showDetailOrder = response.query_result.orderJV_details
      this.transactionStatus = this.showDetailOrder[0].transaction_status
      this.paymentTransaction = this.showDetailOrder[0].payment_transaction
      this.BuyerName = this.showDetailOrder[0].buyer_name
      this.createAt = this.showDetailOrder[0].created_at
      this.paidDatetime = this.showDetailOrder[0].paid_datetime
      this.transactionCode = this.showDetailOrder[0].transactionCode
      this.deliveryStatus = this.showDetailOrder[0].delivery_status
      this.transportationStatus = this.showDetailOrder[0].transportation_status
      this.tracking = this.showDetailOrder[0].tracking
      this.prDocumentId = this.showDetailOrder[0].pr_document_id
      this.poDocumentId = this.showDetailOrder[0].po_document_id
      this.refCallbackSoId = this.showDetailOrder[0].ref_callback_so_id
      this.receipt = this.showDetailOrder[0].receipt[0]
      this.userAddress = this.showDetailOrder[0].user_address
      this.productList = this.showDetailOrder[0].product_list
      this.totalPriceDiscount = this.showDetailOrder[0].total_price_discount
      this.totalPriceNoVat = this.showDetailOrder[0].total_price_no_vat
      this.totalVat = this.showDetailOrder[0].total_vat
      this.totalPriceVat = this.showDetailOrder[0].total_price_vat
      this.totalShipping = this.showDetailOrder[0].total_shipping
      this.netPrice = this.showDetailOrder[0].net_price
      this.startDateContract = this.showDetailOrder[0].start_date_contract
      this.endDateContract = this.showDetailOrder[0].end_date_contract
      this.payType = this.showDetailOrder[0].pay_type
      this.typeBudget = this.showDetailOrder[0].type_budget
      this.budgetCut = this.showDetailOrder[0].budget_cut
      this.remark = this.showDetailOrder[0].remark
      this.orderStatus = this.showDetailOrder[0].order_status
      this.itemStatus = this.showDetailOrder[0].item_status
      if (this.showDetailOrder[0].invoice_address !== '-') {
        this.invoiceAddress = this.showDetailOrder[0].invoice_address
      } else {
        this.invoiceAddress = '-'
      }
      // console.log(this.invoiceAddress)
      this.$store.commit('closeLoader')
    },
    checkDis (val) {
      if (val[0].file || val[1].file || val[2].file) {
        return false
      } else {
        return true
      }
    },
    selectIndex (name, index, file) {
      this.i = index
      this.name = name
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadFile (event, name, index) {
      // console.log('this.itemsDoc11', this.itemsDoc)
      // console.log(event)
      for (let i = 0; i < this.DataFile.length; i++) {
        const element = this.DataFile[i]
        const imageSize = element.size / 1024 / 1024
        if (imageSize < 2) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = () => {
            var resultReader = reader.result
            // console.log(resultReader, 'resultReader')
            this.Detail.product_file[this.i] = {
              name_document: this.name,
              file: resultReader.split(',')[1]
            }
            // console.log('this.itemsDocevent', '555555')
            this.DataFile = []
            this.itemsDoc[this.i].file = event[0]
            // console.log('check', this.checkDisConfirm)
            // console.log('this.itemsDocevent', this.itemsDoc)
          }
        } else {
          this.Detail.product_file = []
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่ไฟล์ไม่เกิน 5 mb',
            showConfirmButton: false,
            timer: 1500
          })
        }
      }
    },
    delete_file (i) {
      this.itemsDoc[i].file = ''
      this.Detail.product_file[i] = ''
      this.$refs.formUploadPDF.resetValidation()
    },
    async confirm () {
      // console.log('itemsDoc===>', this.itemsDoc)
      this.$store.commit('openLoader')
      // if (this.$refs.formUploadPDF.validate()) {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.ordernumber = this.$route.query.order_number
      this.shopID = this.$route.query.seller_shop_id
      const formData = new FormData()
      formData.append('order_Number', this.ordernumber)
      formData.append('sellerShopId', this.shopID)
      formData.append('userId', onedata.user.user_id)
      formData.append('poFile', this.itemsDoc[0].file)
      formData.append('prFile', this.itemsDoc[1].file)
      formData.append('soFile', this.itemsDoc[2].file)
      formData.append('poNumber', this.itemsDoc[0].value === null ? this.itemsDoc[0].value = '' : this.itemsDoc[0].value)
      formData.append('prNumber', this.itemsDoc[1].value === null ? this.itemsDoc[1].value = '' : this.itemsDoc[1].value)
      formData.append('soNumber', this.itemsDoc[2].value === null ? this.itemsDoc[2].value = '' : this.itemsDoc[2].value)
      await this.$store.dispatch('actionUploadOrderDocument', formData)
      var res = await this.$store.state.ModuleOrder.stateUploadOrderDocument
      if (res.message === 'File uploaded successfully') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'บันทึกสำเร็จ'
        })
        this.dialogUploadPDF = false
        await this.orderDetailCompany()
        await this.getOrderDocument()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    async getOrderDocument () {
      this.$store.commit('openLoader')
      var data = {
        orderNumber: this.ordernumber
      }
      // console.log('data', data)
      await this.$store.dispatch('actionGetOrderDocument', data)
      var res = await this.$store.state.ModuleOrder.stateGetOrderDocument
      if (res.message === 'Get order document successfully') {
        this.documents = res.data
        this.itemsDoc[0].value = this.documents.PoNumber
        this.itemsDoc[1].value = this.documents.PrNumber
        this.itemsDoc[2].value = this.documents.SoNumber
        this.itemsDoc[0].file = this.documents.poFile
        this.itemsDoc[1].file = this.documents.prFile
        this.itemsDoc[2].file = this.documents.soFile
        // console.log('itemsDoc', this.itemsDoc)
      } else {
        this.documents = []
      }
      this.$store.commit('closeLoader')
      // console.log(' this.documents', this.documents)
    },
    async useParamsOrderNumber () {
      this.ordernumber = this.$route.query.order_number
      this.trackingNumber = this.$route.query.tracking_number
      // console.log(this.trackingNumber)
      await this.getOrderDocument()
    },
    CloseModalInputReason () {
      this.reason = ''
      this.modalInputReason = false
    },
    confirmCancelOrder () {
      this.modalInputReason = false
      this.modalAwaitCancelOrder = true
    },
    async confirmCancel () {
      this.modalAwaitCancelOrder = false
      // const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      var data = ''
      this.shopID = this.$route.query.seller_shop_id
      this.ordernumber = this.$route.query.order_number
      data = {
        payment_transaction_number: this.ordernumber,
        order_number: this.ordernumber,
        seller_shop_id: this.shopID,
        role_user: 'seller',
        status: 'cancel',
        reason: this.reason
      }
      await this.$store.dispatch('actionsAccecptProduct', data)
      const resposnse = await this.$store.state.ModuleShop.stateAccecptProduct
      if (resposnse.result === 'SUCCESS') {
        this.modalSuccessCancelOrder = true
        this.cancelDialogOpen = false
      } else {
        this.$swal.fire({
          icon: 'error',
          text: resposnse.message,
          showConfirmButton: false,
          timer: 1500
        })
      }
      this.modalAwaitCancelOrder = false
    },
    closeDialogConfirmCancel () {
      this.modalAwaitCancelOrder = true
    },
    async closeSuccessCancelOrder () {
      this.modalSuccessCancelOrder = false
      // await this.getItemProductB2C()
      // await this.getOrderDocument()
      await this.orderDetailCompany()
    },
    async confirmUpdateTracking () {
      this.$store.commit('openLoader')
      // const shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.shopID = this.$route.query.seller_shop_id
      this.ordernumber = this.$route.query.order_number
      const data = {
        seller_shop_id: this.shopID,
        tracking_number: this.trackingNumOwn,
        ship_name: this.ownShipping,
        order_number: this.ordernumber
      }
      await this.$store.dispatch('actionsUpdateTrackingNumber', data)
      const response = await this.$store.state.ModuleShop.stateUpdateTrackingNumber
      if (response.query_result.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.modalAddTrackingTransport = false
        this.$swal.fire({
          icon: 'success',
          text: 'อัพเดต Tracking Number สำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.orderDetailCompany()
        this.deliverDialogOpen = false
      } else {
        this.$store.commit('closeLoader')
        this.modalAddTrackingTransport = false
        this.$swal.fire({
          icon: 'error',
          text: this.messageErrorTrackingnumber(response.query_result.message),
          showConfirmButton: false,
          timer: 1500
        })
      }
    }
  }
}
</script>

<style lang="css" scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
/* .v-application .mb-12 {
    margin-bottom: 12px !important;
} */
::v-deep .ant-table-pagination {
  display: none;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
.bgShippingUPS {
  background-color: #F3F5F7;
}
.fontActive {
  color: #27AB9C;
}
.fontInactive {
  color: #A6A6A6;
}
.fontSizeStepOrder {
  font-size: 11px;
}
.fontSizeTotalPrice {
  font-size: 18px;
}
.fontSizeTotalPriceMobile {
  font-size: 16px;
}
.fontSizeAddressDetail {
  font-size: 16px;
}
.buttonFontSize {
  font-size: 14px;
  font-weight: normal;
}
.captionSku {
  font-size: 12px;
}
.fontSizeTitle {
  font-size: 21px;
}
.fontSizeTitleMobile {
  font-size: 18px;
}
.fontSizeDetail {
  font-size: 14px;
}
.fontSizeDetailMobile {
  font-size: 12px;
}
.fontSizeTotalPrice {
  font-size: 16px;
}
.fontSizeTotalPriceMobile {
  font-size: 14px;
}
.DetailsProductFrontMobile {
  font-size: 12px;
}
.ant-card-bordered {
  border: 0px solid #e8e8e8;
}
</style>

<style scoped>
.text-16 {
  font-size: 16px;
  font-weight: bold;
}

.text-18 {
  font-size: 18px;
}

.text-18-bold {
  font-size: 18px;
  font-weight: bold;
}

.v-application--is-ltr .v-data-table > .v-data-table__wrapper > table > thead > tr > th {
  text-align: center !important;
}

.v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
  text-align: center !important;
}
</style>
