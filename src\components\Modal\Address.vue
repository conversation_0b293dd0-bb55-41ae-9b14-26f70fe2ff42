<template>
  <div class="text-center">
    <v-dialog v-model="dialog" width="500" persistent>
      <v-card>
        <v-container grid-list-xs>
          <v-card-title class="headline">เพิ่มที่อยู่ใหม่</v-card-title>
          <v-card-text>
            <v-form ref="FormAddress" :lazy-validation="lazy">
              <a-row type="flex">
                <a-col :span='24'>
                  <v-text-field placeholder="ชื่อจริง" v-model="first_name" :rules="Rules.first_name" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="นามสกุล" v-model="last_name" :rules="Rules.last_name" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="หมายเลขโทรศัพท์" v-model="phone" :rules="Rules.tel" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="เลขที่อยู่" v-model="house_no" :rules="Rules.house_no" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="ห้องเลขที่" v-model="room_no" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="ชั้นที่" v-model="floor" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="อาคาร" v-model="building_name" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="หมู่บ้าน" v-model="moo_ban" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="หมู่ที่" v-model="moo_no" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="ตรอก / ซอย" v-model="soi" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="แยก" v-model="yaek" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <v-text-field placeholder="ถนน" v-model="street" outlined dense class="input_text"></v-text-field>
                </a-col>
                <a-col :span='24'>
                  <addressinput-subdistrict :rules="Rules.empty" label=""  v-model="subdistrict" placeholder="เเขวง / ตำบล"/>
                </a-col>
                <a-col :span='24' class="mt-5">
                  <addressinput-district label="" v-model="district"  placeholder="เขต / อำเภอ" />
                </a-col>
                <a-col :span='24' class="mt-5">
                  <addressinput-province label="" v-model="province" placeholder="จังหวัด" />
                </a-col>
                <a-col :span='24' class="mt-5">
                  <addressinput-zipcode label="" v-model="zipcode" placeholder="รหัสไปรษณีย์" />
                </a-col>
              </a-row>
            </v-form>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <a-button class="mr-3" @click="cancel">ยกเลิก</a-button>
            <a-button class="border_confirm mr-2" @click="CreateAddress">ยืนยัน</a-button>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
  <!-- <v-form ref="FormAddress" :lazy-validation="lazy">
  <a-row type="flex">
  <a-col :span='24' class="mt-3">
  <v-text-field placeholder="ชื่อ-นามสกุล" v-model="name" :rules="Rules.name" outlined dense class="input_text"></v-text-field>
</a-col>
<a-col :span='24' class="mt-3">
<v-text-field placeholder="หมายเลขโทรศัพท์" v-model="phone" :rules="Rules.tel" outlined dense class="input_text"></v-text-field>
</a-col>
<a-col :span='24' class="mt-3">
<v-text-field placeholder="อาคาร, ถนน, เเละอื่นๆ" v-model="detail" outlined dense class="input_text"></v-text-field>
</a-col>
<a-col :span='24' class="mt-3">
<addressinput-subdistrict label=""  v-model="subdistrict" placeholder="เเขวง / ตำบล" />
</a-col>
<a-col :span='24' class="mt-3">
<addressinput-district label="" v-model="district"  placeholder="เขต / อำเภอ" />
</a-col>
<a-col :span='24' class="mt-3">
<addressinput-province label="" v-model="province" placeholder="จังหวัด" />
</a-col>
<a-col :span='24' class="mt-3">
<addressinput-zipcode label="" v-model="zipcode" placeholder="รหัสไปรษณีย์" />
</a-col>
</a-row>
</v-form> -->
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import { Decode } from '@/services'
import { Button, Col, Row } from 'ant-design-vue'
Vue.use(VueThailandAddress)
export default {
  props: ['checkAddress'],
  components: {
    'a-row': Row,
    'a-col': Col,
    'a-button': Button
  },
  data () {
    return {
      data: [],
      lazy: false,
      first_name: '',
      last_name: '',
      phone: '',
      detail: '',
      dialog: false,
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      house_no: '',
      room_no: '',
      floor: '',
      building_name: '',
      moo_ban: '',
      moo_no: '',
      soi: '',
      yaek: '',
      street: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        first_name: [
          v => !!v || 'กรุณากรอกชื่อจริงผู้รับ'
        ],
        last_name: [
          v => !!v || 'กรุณากรอกนามสกุลผู้รับ'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่อยู่'
        ],
        room_no: [
          v => !!v || 'กรุณาระบุห้องเลขที่'
        ],
        floor: [
          v => !!v || 'กรุณาระบุชั้นที่'
        ],
        building_name: [
          v => !!v || 'กรุณาระบุอาคาร'
        ],
        moo_ban: [
          v => !!v || 'กรุณาระบุหมู่บ้าน'
        ],
        moo_no: [
          v => !!v || 'กรุณาระบุหมู่ที่'
        ],
        soi: [
          v => !!v || 'กรุณาระบุตรอก/ซอย'
        ],
        yaek: [
          v => !!v || 'กรุณาระบุแยก'
        ],
        street: [
          v => !!v || 'กรุณาระบุถนน'
        ]
      }
    }
  },
  watch: {
    checkAddress (val) {
      this.dialog = val
    }
  },
  created () {
    this.dialog = this.checkAddress
  },
  mounted () {
    this.getAddressData()
  },
  methods: {
    cancel () {
      localStorage.removeItem('AddressData')
      this.$router.push({ path: '/shoppingcart' })
    },
    async getAddressData () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      if (localStorage.getItem('AddressData') !== null) {
        this.data = JSON.parse(Decode.decode(localStorage.getItem('AddressData')))
        // console.log('AddressData', this.data)
        this.first_name = this.data[0].first_name
        this.last_name = this.data[0].last_name
        this.house_no = this.data[0].house_no
        this.moo_ban = this.data[0].moo_ban
        this.building_name = this.data[0].building_name
        this.street = this.data[0].street
        this.soi = this.data[0].soi
        this.room_no = this.data[0].room_no
        this.floor = this.data[0].floor
        this.moo_no = this.data[0].moo_no
        this.yaek = this.data[0].yaek
        this.subdistrict = this.data[0].sub_district
        this.district = this.data[0].district
        this.province = this.data[0].province
        this.phone = this.data[0].phone
        this.zipcode = this.data[0].zipcode
      } else {
        this.getAddressData()
      }
    },
    async CreateAddress () {
      if (this.$refs.FormAddress.validate(true)) {
        var data = {
          first_name: this.first_name,
          last_name: this.last_name,
          house_no: this.house_no,
          moo_ban: this.moo_ban,
          building_name: this.building_name,
          street: this.street,
          soi: this.soi,
          room_no: this.room_no,
          floor: this.floor,
          moo_no: this.moo_no,
          yaek: this.yaek,
          sub_district: this.subdistrict,
          district: this.district,
          province: this.province,
          phone: this.phone,
          zipcode: this.zipcode
        }
        // console.log('data ที่จะสร้างที่อยู่ร้าน', data)
        await this.$store.dispatch('CreateAddressUser', data)
        var res = this.$store.state.ModuleManageShop.CreateAddressUser
        var updateAddress = {
          address_id: res.data
        }
        // console.log('ข้อมูล หลังจาก create user =', res)
        if (res.message === 'Create user address success') {
          this.$swal.fire({ icon: 'success', title: 'เพิ่มที่อยู่สำเร็จ', showConfirmButton: false, timer: 1500 })
          this.dialog = false
          localStorage.removeItem('AddressData')
          await this.$store.dispatch('UpdateAddressCart', updateAddress)
          await this.$EventBus.$emit('SentGetCart')
        } else if (res.message === 'Parameter is missing') {
          this.$swal.fire({ icon: 'warning', title: 'กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
      }
    }
  }
}
</script>

<style>
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
</style>
