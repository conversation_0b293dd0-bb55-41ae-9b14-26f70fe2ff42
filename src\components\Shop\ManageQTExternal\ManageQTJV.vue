<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการใบเสนอราคา JV</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backTomenu()">mdi-chevron-left</v-icon>จัดการใบเสนอราคา JV</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="6" class="pt-0">
            <span class="detail1">policy 1</span>
            <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="policy1" @ready="onReady"></ckeditor>
          </v-col>
          <v-col cols="6" class="pt-0">
            <span class="detail1">policy 2</span>
            <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="policy2" @ready="onReady"></ckeditor>
          </v-col>
        </v-row>
        <v-btn @click="PreviewSettingQT()">Preview</v-btn>
            <!-- <iframe
            v-if="pdfBase64"
            :src="pdfBase64"
            width="100%"
            height="600px"
            style="border: none;"
          ></iframe> -->
      </v-card-text>
    </v-card>
    <v-dialog v-model="dialogPreview" max-width="800px" persistent>
      <v-card>
        <v-card-title class="headline">PDF Preview</v-card-title>
        <v-card-text style="height: 600px; padding: 0;">
          <iframe
            v-if="pdfBase64"
            :src="pdfBase64"
            width="100%"
            height="100%"
            style="border: none;"
          ></iframe>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn color="primary" text @click="dialogPreview = false">Close</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import ClassicEditor from '@ckeditor/ckeditor5-build-decoupled-document'
export default {
  components: {
  },
  data () {
    return {
      dialogPreview: false,
      pdfBase64: null,
      policy1: '',
      policy2: '',
      editor: ClassicEditor,
      editorConfig: {
        toolbar: [
          'heading',
          '|',
          'bold',
          'italic',
          'link',
          'alignment:left',
          'alignment:right',
          'alignment:center',
          'alignment:justify',
          'bulletedlist',
          'numberedlist',
          '|',
          'blockquote',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ManageQTExternalMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageQTExternal' }).catch(() => {})
      }
    }
  },
  mounted () {
  },
  destroy () {
  },
  beforeDestroy () {
    if (this.pdfUrl) {
      URL.revokeObjectURL(this.pdfUrl)
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.oneData = []
    this.shopID = parseInt(localStorage.getItem('shopSellerID'))
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    if (localStorage.getItem('oneData') !== null) {
      // await this.GetDetailQT()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  methods: {
    async PreviewSettingQT () {
      this.$store.commit('openLoader')
      var data = {
        policy_1: this.policy1,
        policy_2: this.policy2
      }
      await this.$store.dispatch('actionPreviewSettingQT', data)
      const response = this.$store.state.ModuleShop.statePreviewSettingQT
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.PreviewData = response.data
        this.pdfBase64 = `data:application/pdf;base64,${this.PreviewData.base_64}`
        this.dialogPreview = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: '<h5>เกิดข้อผิดพลาด กรุณาลองใหม่ภายหลัง</h5>', showConfirmButton: false, timer: 2000 })
      }
    },
    onReady (editor) {
      editor.execute('heading', { value: 'heading2' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    backTomenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    }
  }
}
</script>

<style>
.v-radio .v-icon {
  color: #27AB9C;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 16px;
  border-radius: 8px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 8px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px;
  color: #212121;
  border-radius: 8px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.v-input .v-input__slot {
  border-radius: 8px !important;
}
.v-input--selection-controls {
  margin-top: 0px;
  padding-top: 0px;
}
li::marker {
  color: #27AB9C;
}
</style>
