<template>
  <v-app class="backgroundPage OverflowX" id="app">
    <AppBarRegis :step="1" />
    <v-main>
      <div class="d-flex justify-center">
        <!-- <v-container :class="MobileSize || IpadSize || IpadProSize ? '' : 'ma-16 mt-3'"> -->
        <v-container
          :style="(MobileSize || IpadSize || IpadProSize)
              ? ''
              : (selectAccount === 'selectFirst'
                  ? 'padding-top: 30px; padding-left: 90px; padding-right: 90px;'
                  : 'padding-top: 30px; padding-left: 190px; padding-right: 190px;')"
          >
          <v-card style="background-color: #ffffff" :class="MobileSize ? 'pa-3' : 'pa-10 pt-6'">
            <v-form ref="Registerform" :lazy-validation="lazy">
              <v-col cols="12" v-if="selectAccount === 'selectFirst'">
                <span :style="MobileSize ? 'font-size: 24px; font-weight: bold;' : 'font-size: 24px; font-weight: bold;'">ข้อมูลสำหรับผู้เข้าใช้งานระบบ Nex Gen Commerce</span>
              </v-col>
              <v-col cols="12" v-else-if="selectAccount === 'selectSecond'" style="display: flex; justify-content: center;">
                <span :style="MobileSize ? 'font-size: 24px; font-weight: bold;' : 'font-size: 24px; font-weight: bold;'">ข้อมูลสำหรับผู้เข้าใช้งานระบบ Nex Gen Commerce</span>
              </v-col>
              <v-col cols="12" v-if="selectAccount === 'phoneRegister' && !sentOTP">
                <v-row align="center">
                  <v-col cols="2" class="d-flex justify-start">
                    <v-btn icon @click="goBack" color="primary">
                      <v-icon>mdi-arrow-left</v-icon>
                    </v-btn>
                  </v-col>

                  <v-col :cols="MobileSize || IpadSize ? 10 : 8" class="d-flex justify-center">
                    <span :style="MobileSize ? 'font-size: 24px; font-weight: bold;' : 'font-size: 24px; font-weight: bold;'">
                      ข้อมูลสำหรับผู้เข้าใช้งานระบบ Nex Gen Commerce
                    </span>
                  </v-col>

                  <v-col :cols="MobileSize || IpadSize ? 0 : 2"></v-col>
                </v-row>
              </v-col>

              <v-col cols="12" v-else-if="selectAccount === 'phoneRegister' && sentOTP">
                <v-btn icon @click="goBack" color="primary">
                  <v-icon>mdi-arrow-left</v-icon>
                </v-btn>
              </v-col>
              <div :class="MobileSize ? 'pa-3' : 'pa-4 pt-0 pb-0'">
                <v-col :class="MobileSize || IpadSize || IpadProSize ? '' : 'd-flex align-center'">
                  <v-row dense>
                    <v-col cols="12" v-if="selectAccount === 'selectFirst'">
                      <v-radio-group v-model="selectAccount" row>
                        <v-radio
                          label="ยังไม่มีบัญชีผู้ใช้งาน One Platform"
                          value="selectFirst"
                          :class="MobileSize || IpadSize ? 'custom-radio-mobile' : 'custom-radio'"
                        />
                        <v-radio
                          label="มีบัญชีผู้ใช้งาน One Platform"
                          value="selectSecond"
                          :class="MobileSize || IpadSize ? 'custom-radio-mobile' : 'custom-radio'"
                        />
                      </v-radio-group>
                    </v-col>
                    <v-col cols="12" v-else-if="selectAccount === 'selectSecond'">
                      <v-radio-group v-model="selectAccount" row class="selectSecond">
                        <v-radio
                          label="ยังไม่มีบัญชีผู้ใชงาน One Platform"
                          value="selectFirst"
                          :class="MobileSize || IpadSize ? 'custom-radio-mobile' : 'custom-radio'"
                        />
                        <v-radio
                          label="มีบัญชีผู้ใชงาน One Platform"
                          value="selectSecond"
                          :class="MobileSize || IpadSize ? 'custom-radio-mobile' : 'custom-radio'"
                        />
                      </v-radio-group>
                    </v-col>

                    <v-col cols="12" v-if="selectAccount === 'selectFirst'">
                      <v-row>
                        <v-col cols="12" md="2" sm="4" class="pb-0">
                          <span style="font-size: 16px;">คำนำหน้า (ไทย) <span style="color: #FF0105;">*</span> :</span>
                          <v-select
                            v-model="selectedNameTH"
                            :items="selectNameTHOptions"
                            item-text="text"
                            item-value="value"
                            outlined
                            dense
                            :rules="Rules.selectNameTH"
                          />
                        </v-col>
                        <v-col cols="12" md="5" sm="4" class="pb-0">
                          <span style="font-size: 16px;">ชื่อ (ไทย) <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="nameTH" :rules="Rules.nameTH"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="5" sm="4" class="pb-0">
                          <span style="font-size: 16px;">นามสกุล (ไทย) <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="surnameTH" :rules="Rules.surnameTH"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="2" sm="4" class="pb-0">
                          <span style="font-size: 16px;">คำนำหน้า (อังกฤษ) <span style="color: #FF0105;">*</span> :</span>
                          <v-select
                            v-model="selectedNameEN"
                            :items="selectNameENOptions"
                            item-text="text"
                            item-value="value"
                            outlined
                            dense
                            :rules="Rules.selectNameEN"
                          />
                        </v-col>
                        <v-col cols="12" md="5" sm="4" class="pb-0">
                          <span style="font-size: 16px;">ชื่อ (อังกฤษ) <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="nameEN" :rules="Rules.nameEN"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="5" sm="4" class="pb-0">
                          <span style="font-size: 16px;">นามสกุล (อังกฤษ) <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="surnameEN" :rules="Rules.surnameEN"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" class="pb-0">
                          <span style="font-size: 16px;">เลขประจำตัวประชาชน <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="cardNumber" :maxLength="13" @keypress="CheckSpacebar($event)" @input="validateTaxID()" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" :rules="Rules.cardNumber"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" class="pb-0">
                          <span style="font-size: 16px;">เบอร์มือถือ <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="phoneNumber" :rules="Rules.tel"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" class="pb-0">
                          <span style="font-size: 16px;">อีเมล <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="email" :rules="Rules.email" oninput="this.value = this.value.replace(/[^a-zA-Z0-9@_.]/g, '')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" class="pb-0">
                          <span style="font-size: 16px;">ชื่อผู้ใช้งาน <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="Username" :rules="Rules.username"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" class="pb-0">
                          <span style="font-size: 16px;">รหัสผ่าน <span style="color: #FF0105;">*</span> :</span>
                           <v-text-field
                            outlined
                            dense
                            :type="showPassword ? 'text' : 'password'"
                            v-model="Password"
                            :append-icon="showPassword ? 'mdi-eye-off' : 'mdi-eye'"
                            @click:append="showPassword = !showPassword"
                            :rules="Rules.password"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="4" class="pb-0">
                          <span style="font-size: 16px;">ยืนยันรหัสผ่าน <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field
                            outlined
                            dense
                            :type="showCFPassword ? 'text' : 'password'"
                            v-model="cfPassword"
                            :append-icon="showCFPassword ? 'mdi-eye-off' : 'mdi-eye'"
                            @click:append="showCFPassword = !showCFPassword"
                            :rules="Rules.cfPassword"
                          ></v-text-field>
                        </v-col>
                        <v-col cols="12" class="pb-10" style="display: flex; align-items: center;">
                          <v-checkbox
                            v-model="ConCheck1"
                            :ripple="false"
                            color="#27AB9C"
                            hide-details
                            class="ma-0 pa-0"
                          />
                          <span :style="MobileSize || IpadSize ? 'font-size: 16px;' :'font-size: 20px;'">
                            ยอมรับ
                            <a class="text-decoration-underline" style="color: #27AB9C;" @click="consent('A')">
                              <span>ข้อกำหนดการใช้บริการ ONE ID</span>
                            </a>
                            <span> และ </span>
                            <a class="text-decoration-underline" style="color: #27AB9C;" @click="consent('B')">
                              <span>นโยบายความคุ้มครองข้อมูลส่วนบุคคล</span>
                            </a>
                          </span>
                        </v-col>
                        <v-col cols="12" class="pl-0 pr-0" style="display: flex; justify-content: space-between;">
                          <v-btn depressed color="#D9D9D9" width="140" height="40" :style="MobileSize ? 'border-radius: 15px; font-size: 16px;' : 'border-radius: 15px; font-size: 16px;'" @click="goHome()">กลับสู่หน้าหลัก</v-btn>
                          <v-btn depressed color="#27AB9C" width="140" height="40" :style="MobileSize ? 'color: #FFFFFF; border-radius: 15px; font-size: 16px;' : 'color: #FFFFFF; border-radius: 15px; font-size: 16px;'" :disabled="!isFormValidSelectFirst" @click="Register()">ถัดไป</v-btn>
                        </v-col>
                      </v-row>
                    </v-col>

                    <v-col cols="12" v-else-if="selectAccount === 'selectSecond'">
                      <v-row :style="MobileSize ? 'padding: 20px; padding-top: 0px; padding-bottom: 0px' : 'padding: 95px; padding-top: 0px; padding-bottom: 0px'">
                        <v-col cols="12" style="padding-left: 60px; padding-right: 60px; padding-top: 10px; padding-bottom: 0px;" v-if="showError">
                          <v-card elevation="0" class="pa-4 mx-8" style="border: 1px solid #ebccd1; background-color: #f2dede;">
                            <ul class="ma-0 text-start">
                              <li style="color: #a94442; font-size: 14px;">{{texterror}}</li>
                            </ul>
                          </v-card>
                        </v-col>
                        <v-col cols="12" style="padding-left: 60px; padding-right: 60px; padding-top: 10px; padding-bottom: 0px;">
                          <span style="font-size: 16px;">Username <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="usernameOnePlatform" :rules="Rules.usernameOnePlatform"></v-text-field>
                        </v-col>
                        <v-col cols="12" style="padding-left: 60px; padding-right: 60px; padding-top: 10px; padding-bottom: 0px;">
                          <span style="font-size: 16px;">Password <span style="color: #FF0105;">*</span> :</span>
                          <v-text-field outlined dense v-model="passwordOnePlatform" :type="showPassword2 ? 'text' : 'password'" :append-icon="showPassword2 ? 'mdi-eye-off' : 'mdi-eye'" @click:append="showPassword2 = !showPassword2" @copy.prevent @keydown.space.prevent :rules="Rules.passwordOnePlatform"></v-text-field>
                        </v-col>
                        <v-col cols="12" class="pb-10" style="display: flex; align-items: center;">
                          <v-checkbox
                            v-model="ConCheck2"
                            :ripple="false"
                            color="#27AB9C"
                            hide-details
                            class="ma-0 pa-0"
                          />
                          <span :style="MobileSize || IpadSize ? 'font-size: 16px;' :'font-size: 20px;'">
                            ยอมรับ
                            <a class="text-decoration-underline" style="color: #27AB9C;" @click="consent('A')">
                              <span>ข้อกำหนดการใช้บริการ ONE ID</span>
                            </a>
                            <span> และ </span>
                            <a class="text-decoration-underline" style="color: #27AB9C;" @click="consent('B')">
                              <span>นโยบายความคุ้มครองข้อมูลส่วนบุคคล</span>
                            </a>
                          </span>
                        </v-col>
                        <v-col cols="12" style="display: flex; justify-content: center; padding: 30px;">
                          <span :style="MobileSize || IpadSize ? 'font-size: 16px;' :'font-size: 20px;'">หรือ</span>
                        </v-col>
                        <v-col cols="12" style="display: flex; justify-content: center;">
                          <v-btn outlined rounded color="#27AB9C" :width="MobileSize || IpadSize ? '350' : '500'" :style="MobileSize || IpadSize ? 'font-size: 16px;' :'font-size: 20px;'" @click="selectAccount = 'phoneRegister'">เข้าสู่ระบบด้วยหมายเลขโทรศัพท์</v-btn>
                        </v-col>
                        <v-col cols="12" class="pl-0 pr-0" style="display: flex; justify-content: space-between; padding-top: 60px;">
                          <v-btn depressed color="#D9D9D9" width="150" height="55" :style="MobileSize ? 'border-radius: 15px; font-size: 18px;' : 'border-radius: 15px; font-size: 20px;'" @click="goHome()">กลับสู่หน้าหลัก</v-btn>
                          <v-btn depressed color="#27AB9C" width="150" height="55" :disabled="!isFormValid2" :style="MobileSize ? 'color: #FFFFFF; border-radius: 15px; font-size: 18px;' : 'color: #FFFFFF; border-radius: 15px; font-size: 20px;'" @click="Login('UsernameOne')">ถัดไป</v-btn>
                        </v-col>
                      </v-row>
                    </v-col>

                    <v-col cols="12" v-else-if="selectAccount === 'phoneRegister'">
                      <v-row :style="MobileSize ? 'padding: 40px; padding-top: 0px; padding-bottom: 0px' : (IpadSize ? 'padding: 110px; padding-top: 0px; padding-bottom: 0px' : 'padding: 250px; padding-top: 0px; padding-bottom: 0px')">
                        <v-col cols="12" class="d-flex flex-column align-center">
                          <v-img
                            src="@/assets/phoneRegister.png"
                            max-width="200"
                            contain
                          />
                          <span :style="MobileSize || IpadSize ? 'margin-top: 8px; font-size: 16px;' : 'margin-top: 8px; font-size: 22px;'">
                            เข้าสู่ระบบด้วยเบอร์โทรศัพท์
                          </span>
                        </v-col>

                        <template v-if="!sentOTP">
                          <v-col cols="12" class="pt-8 pb-8 d-flex justify-center">
                            <span v-if="MobileSize" style="font-size: 16px;">
                              ระบบจะทำการส่งเลข OTP <br> ไปยังเบอร์โทรศัพท์ของคุณ
                            </span>
                            <span v-else style="font-size: 16px;">
                              ระบบจะทำการส่งเลข OTP ไปยังเบอร์โทรศัพท์ของคุณ
                            </span>
                          </v-col>
                          <v-col cols="12">
                            <span :style="MobileSize || IpadSize ? 'font-size: 16px;' :'font-size: 20px;'">เบอร์โทรศัพท์</span>
                            <v-text-field
                              v-model="phoneNumberOTP"
                              outlined
                              dense
                              :rules="Rules.telOTP"
                            />
                          </v-col>
                          <v-col cols="12" class="d-flex justify-center">
                            <v-btn
                              depressed
                              color="#27AB9C"
                              width="190"
                              :style="MobileSize || IpadSize ? 'border-radius: 20px; font-size: 16px; color: #FFFFFF;' : 'border-radius: 20px; font-size: 20px; color: #FFFFFF;'"
                              @click="confirmOTP()"
                              :disabled="phoneNumberOTP.length !== 10"
                            >
                              ส่ง OTP
                            </v-btn>
                          </v-col>
                        </template>

                        <template v-else>
                          <v-col cols="12" class="d-flex justify-center" v-if="showErrorPhone">
                            <span style="font-weight: normal; font-size: 14px; line-height: 32px; color: red;">รหัส OTP ไม่ถูกต้อง กรุณาลองใหม่อีกครั้ง</span>
                          </v-col>
                          <v-col cols="12" class="d-flex justify-center">
                            <v-otp-input
                              v-model="otp"
                              :length="length"
                              :error="showErrorPhone"
                              type="number"
                              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                            ></v-otp-input>
                          </v-col>
                          <v-col cols="12" class="pb-0" style="display: flex; justify-content: center;">
                            <span style="font-size: 16px;">ระบบได้ส่งรหัส OTP ไปยังเบอร์ {{ maskPhoneNumber(mobile) }}</span>
                          </v-col>
                          <v-col cols="12" class="pb-0" style="display: flex; justify-content: center;">
                            <span style="font-size: 16px;">รหัสอ้างอิง: <b>{{ otpRefCode }}</b></span><v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon>mdi-refresh</v-icon></v-btn><br />
                          </v-col>
                          <v-col cols="12" class="pb-0" style="display: flex; justify-content: center;">
                            <span style="font-size: 16px;">สามารถขอรหัส OTP อีกครั้งภายใน {{ resendCountdown }}</span>
                          </v-col>
                          <v-col cols="12" class="pa-7 pb-2" style="display: flex; justify-content: center;">
                            <v-btn
                              depressed
                              color="#27AB9C"
                              width="190"
                              :style="MobileSize || IpadSize ? 'border-radius: 20px; font-size: 16px; color: #FFFFFF;' : 'border-radius: 20px; font-size: 20px; color: #FFFFFF;'"
                              @click="checkOTP()"
                              :disabled="!isActivePhone"
                            >
                              ยืนยัน OTP
                            </v-btn>
                          </v-col>
                        </template>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </div>
            </v-form>
          </v-card>
        </v-container>
      </div>
      <v-dialog
        v-model="consentA"
        scrollable
        max-width="800px"
        :style="MobileSize ? 'z-index: 16000004' : ''"
      >
        <v-card style="overflow-x: hidden;">
          <v-card-title style="font-size: 18px; text-align:center;" class="d-flex justify-center">ข้อกำหนดการใช้บริการ ONE ID</v-card-title>
          <v-divider></v-divider>
          <v-card-text style="height: 100%;">
            <div v-html="detailA" style="padding: 15px 0;"></div>
          </v-card-text>
          <v-divider></v-divider>
        </v-card>
      </v-dialog>
      <v-dialog
        v-model="consentB"
        scrollable
        max-width="800px"
        :style="MobileSize ? 'z-index: 16000004' : ''"
      >
        <v-card style="overflow-x: hidden;">
          <v-card-title style="font-size: 18px; text-align:center;" class="d-flex justify-center">นโยบายความคุ้มครองข้อมูลส่วนบุคคล</v-card-title>
          <v-divider></v-divider>
          <v-card-text style="height: 100%;">
            <div v-html="detailB" style="padding: 15px 0;"></div>
          </v-card-text>
          <v-divider></v-divider>
        </v-card>
      </v-dialog>
    </v-main>
  </v-app>
</template>

<script>
import axios from 'axios'
import { Encode, Decode } from '@/services'
export default {
  components: {
    AppBarRegis: () => import(/* webpackPrefetch: true */ '@/components/Home/AppBarRegisUI')
  },
  data () {
    return {
      showError: false,
      showErrorPhone: false,
      pathHome: '/',
      lazy: false,
      selectAccount: 'selectFirst',
      ConCheck1: false,
      ConCheck2: false,
      detailA: '',
      detailB: '',
      consentA: false,
      consentB: false,
      Rules: {
        selectNameTH: [
          v => !!v || 'กรุณาเลือกคำนำหน้า (ไทย)'
        ],
        nameTH: [
          v => !!v || 'กรุณากรอกชื่อ (ไทย)',
          v => /^[ก-๙\s]+$/.test(v) || 'กรุณากรอกเป็นภาษาไทยเท่านั้น'
        ],
        surnameTH: [
          v => !!v || 'กรุณากรอกนามสกุล (ไทย)',
          v => /^[ก-๙\s]+$/.test(v) || 'กรุณากรอกเป็นภาษาไทยเท่านั้น'
        ],
        selectNameEN: [
          v => !!v || 'กรุณาเลือกคำนำหน้า (อังกฤษ)'
        ],
        nameEN: [
          v => !!v || 'กรุณากรอกชื่อ (อังกฤษ)',
          v => /^[A-Za-z\s]+$/.test(v) || 'กรุณากรอกเป็นภาษาอังกฤษเท่านั้น'
        ],
        surnameEN: [
          v => !!v || 'กรุณากรอกนามสกุล (อังกฤษ)',
          v => /^[A-Za-z\s]+$/.test(v) || 'กรุณากรอกเป็นภาษาอังกฤษเท่านั้น'
        ],
        cardNumber: [
          v => !!v || 'กรุณากรอกเลขประจำตัวประชาชน',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวประชาชนให้ครบ 13 หลัก',
          v => this.validNationalID(v) || 'เลขประจำตัวประชาชนไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์มือถือ',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขมือถือ 9 หรือ 10 หลัก'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        username: [
          v => !!v || 'กรุณากรอกชื่อผู้ใช้งาน',
          v => /^[A-Za-z0-9\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษและตัวเลขเท่านั้น',
          v => v.length >= 6 || 'กรุณากรอกให้ครบ 6 อักขระหรือมากกว่านั้น'
        ],
        password: [
          v => !!v || 'กรุณาระบุรหัสผ่าน',
          v => /[A-Za-z0-9!@#$%^&*(),.?":{}|<>_]/.test(v) || 'ต้องประกอบด้วยตัวอักษรภาษาอังกฤษผสมตัวเลขอย่างน้อยหนึ่งหลัก โดยใช้อักขระพิเศษได้',
          v => /(?=.*?[A-Za-z]).+/.test(v) || 'ต้องประกอบด้วยตัวอักษรภาษาอังกฤษน้อยหนึ่งหลัก',
          v => /(?=.*\d).+/.test(v) || 'ต้องมีตัวเลขอย่างน้อยหนึ่งหลัก',
          v => v.length >= 8 || 'ความยาวต้องไม่น้อยกว่า 8 ตัวอักษร'
        ],
        cfPassword: [
          v => !!v || 'กรุณากรอกรหัสผ่านเพื่อยืนยันอีกครั้ง',
          v => v === this.Password || 'รหัสผ่านไม่ตรงกัน'
        ],
        usernameOnePlatform: [
          v => !!v || 'กรุณาระบุชื่อผู้ใช้'
        ],
        passwordOnePlatform: [
          v => !!v || 'กรุณาป้อนรหัสผ่าน'
        ],
        telOTP: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ]
      },
      selectedNameTH: '',
      selectedNameEN: '',
      selectNameTHOptions: [
        { text: 'นาย', value: 'นาย' },
        { text: 'นางสาว', value: 'นางสาว' },
        { text: 'นาง', value: 'นาง' }
      ],
      selectNameENOptions: [
        { text: 'Mr', value: 'Mr' },
        { text: 'Mrs', value: 'Mrs' },
        { text: 'Miss', value: 'Miss' }
      ],
      nameTH: '',
      nameEN: '',
      surnameTH: '',
      surnameEN: '',
      phoneNumber: '',
      cardNumber: '',
      email: '',
      Username: '',
      Password: '',
      showPassword: false,
      showPassword2: false,
      showCFPassword: false,
      cfPassword: '',
      usernameOnePlatform: '',
      passwordOnePlatform: '',
      sentOTP: false,
      phoneNumberOTP: '',
      otpRefCode: '',
      resendCountdown: '00:00',
      otp: '',
      otpCode: '',
      mobile: '',
      length: 6,
      disableRefreshOTP: false,
      pageSelect: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    isFormValidSelectFirst () {
      return (
        this.selectedNameTH &&
        this.nameTH &&
        this.surnameTH &&
        this.selectedNameEN &&
        this.nameEN &&
        this.surnameEN &&
        this.cardNumber &&
        this.phoneNumber &&
        this.email &&
        this.Username &&
        this.Password &&
        this.cfPassword &&
        this.ConCheck1 === true &&
        this.Password === this.cfPassword // รหัสผ่านตรงกัน
      )
    },
    isFormValid2 () {
      return this.usernameOnePlatform &&
            this.passwordOnePlatform &&
            this.ConCheck2
    },
    isActivePhone () {
      return this.otp.length === this.length
    }
  },
  mounted () {
    const query = this.$route.query

    if (Object.keys(query).length === 0) {
      this.$router.replace({
        path: '/shopRegister',
        query: { NoRegisterThroughOnePlatform: null }
      })
    }

    this.$nextTick(() => {
      this.setupFromRoute()
    })
  },
  watch: {
    '$route.query': {
      handler () {
        this.setupFromRoute()
      },
      deep: true,
      immediate: true
    },
    '$route.query.RegisterPhoneNumber' (newVal) {
      if (newVal === 'page2') {
        this.loadOTPData()
      }
    },
    // MobileSize (val) {
    //   if (val) {
    //     this.$router.push({ path: '/shopRegisterMobile' }).catch(() => {})
    //   } else {
    //     this.$router.push({ path: '/shopRegister' }).catch(() => {})
    //   }
    // },
    MobileSize (val) {
      if (val) {
        this.replaceRoute(this.$route.query)
      } else {
        this.replaceRoute(this.$route.query)
      }
    },
    selectAccount (newVal) {
      if (newVal === 'selectFirst') {
        this.goToSelectFirst()
      } else if (newVal === 'selectSecond') {
        this.goToSelectSecond()
      } else if (newVal === 'phoneRegister') {
        if (this.sentOTP) {
          this.goToPhonePage2()
        } else {
          this.goToPhonePage1()
        }
      } else {
        this.replaceRoute({})
      }
    },
    sentOTP (newVal) {
      if (this.selectAccount === 'phoneRegister') {
        if (newVal) {
          this.goToPhonePage2()
        } else {
          this.goToPhonePage1()
        }
      }
    }
  },
  created () {
    this.GetPrivacyConsent()
    this.GetTermConsent()

    if (this.$route.query.RegisterPhoneNumber === 'page2') {
      this.loadOTPData()
    }
    var CurrentPath = this.$router.currentRoute.path
    localStorage.setItem('CurrentPath', CurrentPath)
    this.pageSelect = localStorage.getItem('page')
  },
  methods: {
    goHome () {
      this.$router.push(this.pathHome)
    },
    async encodePassword (password) {
      const CryptoJS = require('crypto-js')
      const phrase = 'tenmerucorpEDeveloper'
      const iv = CryptoJS.enc.Utf8.parse('CGNecremmocnegxe')

      const secretKey = CryptoJS.SHA256(phrase).toString(CryptoJS.enc.Hex)

      const encryptedData = CryptoJS.AES.encrypt(password, CryptoJS.enc.Hex.parse(secretKey), {
        iv: iv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      })

      const encryptedBase64 = encryptedData.toString()
      return encryptedBase64
    },
    async Login (dataType) {
      var onedata = {}
      if (dataType === 'UsernameOne') {
        this.$store.commit('openLoader')
        const passwordEncode = await this.encodePassword(this.passwordOnePlatform)
        var data = {
          username: this.usernameOnePlatform,
          password: passwordEncode
        }
        await this.$store.dispatch('actionsLoginUsername', data)
        var response = await this.$store.state.ModuleRegister.stateLoginUsername
        if (response.result === 'SUCCESS') {
          if (response.data.access_token !== '') {
            localStorage.setItem('LoginTime', Date.now())
            onedata.user = response.data
            localStorage.setItem('oneData', Encode.encode(onedata))
          }
          this.$router.push({ path: `/InfoRegisterShop?page=${this.pageSelect}` }).catch(() => {})
          // var PathRedirect = ''
          // if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
          //   PathRedirect = sessionStorage.getItem('pathRedirect')
          //   if (PathRedirect === '/Login' || PathRedirect === '/Register') {
          //     PathRedirect = '/'
          //   }
          // } else {
          //   PathRedirect = '/'
          // }
          // var dataRole = {
          //   role: 'ext_buyer'
          // }
          // localStorage.setItem('roleUser', JSON.stringify(dataRole))
          // this.$EventBus.$emit('LoginUser')
          // this.$EventBus.$emit('checkPDPA')
          // this.$EventBus.$emit('getCartPopOver')
          // this.$EventBus.$emit('getItemNoti')
          // if (PathRedirect !== null) {
          //   this.$router.push({ path: `${PathRedirect}` }).catch(() => {})
          // } else {
          //   this.$router.push({ path: '/' }).catch(() => {})
          // }
          await this.$store.dispatch('actionsConGetBizDetail')
          var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
          if (responseBiz.message === 'updated data from one id successful.') {
            localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
          }
          this.$store.commit('closeLoader')
        } else if (response.result === 'FAILED') {
          if (response.message === 'The user credentials were incorrect.') {
            this.showError = true
            this.texterror = 'ชื่อผู้ใช้งานและรหัสผ่านไม่ตรงกับในระบบ'
          } else if (response.message === 'username or password cannot be empty') {
            this.showError = true
            this.texterror = 'ชื่อผู้ใช้งานหรือรหัสผ่านไม่สามารถเป็นค่าว่างได้'
          } else {
            this.showError = true
            this.texterror = response.message
          }
          this.$store.commit('closeLoader')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            html: '<h3>ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่</h3>'
          })
        }
      } else {
        onedata = {}
        onedata.CurrentPath = this.$router.currentRoute.path
        localStorage.setItem('oneData', Encode.encode(onedata))
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      }
    },
    maskPhoneNumber (Number) {
      return Number.substring(0, 2) + 'xxxxxx' + Number.substring(8)
    },
    async confirmOTP () {
      this.$store.commit('openLoader')
      var data = {
        mobile_no: this.phoneNumberOTP
      }
      await this.$store.dispatch('actionsGetOTP', data)
      var res = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
      if (res.result === 'SUCCESS') {
        var dataOTP = {
          otp: res.data.otp,
          ref_code: res.data.ref_code,
          mobileNo: this.phoneNumberOTP
        }
        localStorage.setItem('OTPData', Encode.encode(dataOTP))
        this.$store.commit('closeLoader')
        this.goToPhonePage2()
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'Mobile_no not found') {
          this.$swal.fire({
            icon: 'warning',
            html: '<p>เบอร์โทรศัพท์นี้ยังไม่ได้ลงทะเบียน</p><span>คุณต้องการลงทะเบียน ใช่ หรือ ไม่</span>',
            showConfirmButton: true,
            confirmButtonText: 'ใช่',
            confirmButtonColor: '#27AB9C',
            showCancelButton: true,
            cancelButtonText: 'ไม่'
          }).then(async (result) => {
            if (result.isConfirmed) {
              this.$router.push({ path: '/Register' }).catch(() => {})
            }
          })
        } else {
          this.$swal.fire({ icon: 'warning', title: 'ไม่สามารถดำเนินการได้', showConfirmButton: false, timer: 1500 })
        }
      }
    },
    countdownCheck (second) {
      this.counter = second
      const interval = setInterval(() => {
        var minutes = Math.floor(this.counter / 60)
        var seconds = this.counter % 60
        seconds = seconds < 10 ? `0${seconds}` : seconds
        this.resendCountdown = `${minutes}:${seconds}`
        this.counter--
        if (this.counter < 0) {
          this.disableRefreshOTP = false
          clearInterval(interval)
        }
      }, 1000)
    },
    async RefreshOTP () {
      this.$store.commit('openLoader')
      this.otp = ''
      var mobile = this.mobile
      var dataReOTP = {
        mobile_no: this.mobile
      }
      await this.$store.dispatch('actionsGetOTP', dataReOTP)
      var resReOTP = await this.$store.state.ModuleRegisMorpromt.stateGetOTP
      if (resReOTP.result === 'SUCCESS') {
        this.otpRefCode = resReOTP.data.ref_code
        this.otpCode = resReOTP.data.otp
        localStorage.removeItem('OTPData')
        var dataOTP = {
          otp: resReOTP.data.otp,
          ref_code: resReOTP.data.ref_code,
          mobileNo: mobile
        }
        localStorage.setItem('OTPData', Encode.encode(dataOTP))
        this.disableRefreshOTP = true
        this.countdownCheck(300)
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', title: resReOTP.message, showConfirmButton: false, timer: 1500 })
      }
    },
    loadOTPData () {
      const otpDataRaw = localStorage.getItem('OTPData')
      if (otpDataRaw) {
        this.dataOTP = JSON.parse(Decode.decode(otpDataRaw))
        this.otpRefCode = this.dataOTP.ref_code
        this.otpCode = this.dataOTP.otp
        this.mobile = this.dataOTP.mobileNo
        this.counter = 0
        this.countdownCheck(300)
      }
    },
    async checkOTP () {
      if (this.otp === this.otpCode) {
        this.$store.commit('openLoader')
        var data = {
          mobile_no: this.mobile,
          otp: this.otp
        }
        await this.$store.dispatch('actionsConfirmOTPOneID', data)
        var res = await this.$store.state.ModuleRegister.stateConfirmOTPOneID
        // console.log(res)
        if (res.result === 'SUCCESS') {
          localStorage.setItem('AccessToken', Encode.encode(res.data))
          var dataRole = { role: 'ext_buyer' }
          localStorage.setItem('roleUser', JSON.stringify(dataRole))
          var onedata = {}
          onedata.user = res.data
          localStorage.setItem('LoginTime', Date.now())
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.$router.push({ path: `/InfoRegisterShop?page=${this.pageSelect}` }).catch(() => {})
          // if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
          //   PathRedirect = sessionStorage.getItem('pathRedirect')
          //   if (PathRedirect === '/Login' || PathRedirect === '/Register') {
          //     PathRedirect = '/'
          //   }
          // } else {
          //   PathRedirect = '/'
          // }
          // await this.$store.dispatch('actionsConGetBizDetail')
          // var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
          // // console.log('tong res', responseBiz)
          // if (responseBiz.message === 'updated data from one id successful.') {
          //   localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
          // }
          // this.$EventBus.$emit('checkPDPA', res.data.one_id)
          // this.$store.commit('closeLoader')
          // if (PathRedirect !== null) {
          //   window.location.assign(`${PathRedirect}`, '_blank')
          // } else {
          //   window.location.assign('/', '_blank')
          // }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'warning', title: res.message, showConfirmButton: false, timer: 1500 })
        }
      } else {
        this.showErrorPhone = true
      }
    },
    async GetPrivacyConsent () {
      // Privacy
      await axios({
        url: 'https://one.th/api/privacy-policy-for-service?client_id=784',
        method: 'GET'
      }).then(
        (response) => {
          // console.log('detailB---->', response.data)
          this.detailB = response.data
        }
      )
    },
    async GetTermConsent () {
      // Term
      await axios({
        url: 'https://one.th/api/term-of-use-for-service?client_id=784',
        method: 'GET'
      }).then((response) => {
        // console.log('detailA---->', response.data)
        this.detailA = response.data
      })
    },
    async Register () {
      if (this.$refs.Registerform.validate(true)) {
        this.$store.commit('openLoader')
        var checkonedata = Object.prototype.hasOwnProperty.call(localStorage, 'oneData')
        if (checkonedata) {
          var checkMail = {
            email: this.email
          }
          var ResponseCheckMail = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/check_email_one_id`, checkMail)
          // console.log('ResponseCheckMail =========>', ResponseCheckMail)
          if (ResponseCheckMail.data.code !== 400 && ResponseCheckMail.data.message !== 'email duplicate') {
            var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            onedata.CurrentPath = this.$router.currentRoute.path
            await this.CFRegister()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ text: 'อีเมลนี้มีอยู่ในระบบแล้ว กรุณาใช้อีเมลอื่น', icon: 'error', timer: 2500, showConfirmButton: false })
          }
        } else {
          var checkMail1 = {
            email: this.email
          }
          var ResponseCheckMail1 = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/check_email_one_id`, checkMail1)
          // console.log('ResponseCheckMail1 =========>', ResponseCheckMail1)
          if (ResponseCheckMail1.data.code !== 400 && ResponseCheckMail1.data.message !== 'email duplicate') {
            onedata = {}
            onedata.CurrentPath = this.$router.currentRoute.path
            localStorage.setItem('oneData', Encode.encode(onedata))
            await this.CFRegister()
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ text: 'อีเมลนี้มีอยู่ในระบบแล้ว กรุณาใช้อีเมลอื่น', icon: 'error', timer: 2500, showConfirmButton: false })
          }
        }
      }
    },
    async CFRegister () {
      this.$store.commit('openLoader')
      const encryptedToken = await this.encodePassword(this.Password)
      const encryptedTokenRePassword = await this.encodePassword(this.cfPassword)

      var data = {
        keyname_th: this.selectedNameTH,
        keyname_en: this.selectedNameEN,
        name_th: this.nameTH,
        surname_th: this.surnameTH,
        name_en: this.nameEN,
        surname_en: this.surnameEN,
        card_number: this.cardNumber,
        mobile_no: this.phoneNumber,
        email: this.email,
        username: this.Username,
        password: encryptedToken,
        re_password: encryptedTokenRePassword
      }

      await this.$store.dispatch('actionRegisterOnePlatformToRegisterShop', data)
      var res = await this.$store.state.ModuleShop.stateRegisterOnePlatformToRegisterShop

      if (res.message === 'Register and login success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          html: '<h3>การลงทะเบียน One Platform ของคุณเสร็จสมบูรณ์</h3>'
        })
        var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        onedata.user = res.data.data
        var dataRole = {
          role: 'ext_buyer'
        }
        localStorage.setItem('roleUser', JSON.stringify(dataRole))
        localStorage.setItem('oneData', Encode.encode(onedata))
        const auth = {
          headers: { Authorization: `Bearer ${res.data.access_token}` }
        }
        const responseUserDetail = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail_mp_v2`, '', auth)
        if (responseUserDetail.data.code !== 500) {
          onedata = {}
          onedata.user = responseUserDetail.data.data
          localStorage.setItem('oneData', Encode.encode(onedata))
        }
        this.$router.push({ path: `/InfoRegisterShop?page=${this.pageSelect}` }).catch(() => {})
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'warning',
          html: res.message,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    },
    consent (val) {
      if (val === 'A') {
        this.consentA = true
      } else if (val === 'B') {
        this.consentB = true
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    validateTaxID () {
      if (this.validNationalID(this.cardNumber)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    replaceRoute (query) {
      if (this.MobileSize) {
        this.$router.replace({ path: '/shopRegisterMobile', query }).catch(() => {})
      } else {
        this.$router.replace({ path: '/shopRegister', query }).catch(() => {})
      }
    },
    goToPhonePage1 () {
      this.replaceRoute({ RegisterPhoneNumber: 'page1' })
    },
    goToPhonePage2 () {
      this.replaceRoute({ RegisterPhoneNumber: 'page2' })
    },
    goToSelectFirst () {
      this.replaceRoute({ NoRegisterThroughOnePlatform: null })
    },
    goToSelectSecond () {
      this.replaceRoute({ RegisterThroughOnePlatform: null })
    },
    setupFromRoute () {
      const query = this.$route.query

      if (query.NoRegisterThroughOnePlatform !== undefined) {
        this.selectAccount = 'selectFirst'
        this.sentOTP = false
      } else if (query.RegisterThroughOnePlatform !== undefined) {
        this.selectAccount = 'selectSecond'
        this.sentOTP = false
      } else if (query.RegisterPhoneNumber === 'page1') {
        this.selectAccount = 'phoneRegister'
        this.sentOTP = false
      } else if (query.RegisterPhoneNumber === 'page2') {
        this.selectAccount = 'phoneRegister'
        this.sentOTP = true
      } else {
        this.selectAccount = ''
        this.sentOTP = false
      }
    },
    goBack () {
      const query = this.$route.query

      if (query.RegisterPhoneNumber === 'page2') {
        this.goToPhonePage1() // กลับไปหน้าใส่เบอร์
      } else if (query.RegisterPhoneNumber === 'page1') {
        this.goToSelectSecond() // กลับไปหน้าเลือกแพลตฟอร์ม
      } else if (query.RegisterThroughOnePlatform !== undefined) {
        // หากต้องการกรณีพิเศษ
        this.goToSelectSecond()
      } else {
        this.$router.go(-1) // fallback
      }
    }
  }
}
</script>

<style>

</style>

<style scoped>

.backgroundPage{
  background-color: #FAFFFF;
}

.selectSecond /deep/ .v-input--radio-group__input {
  display: flex;
  justify-content: space-evenly;
}

.custom-radio /deep/ .v-label {
  font-size: 20px;
}

.custom-radio-mobile /deep/ .v-label {
  font-size: 16px;
}

</style>
