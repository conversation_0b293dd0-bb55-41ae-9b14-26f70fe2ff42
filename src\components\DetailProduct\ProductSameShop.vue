<template>
  <v-container>
    <v-row>
      <h2 class="pt-1 ml-2 mt-2 mb-4" style="font-weight: 900;">{{ header }}</h2>
      <v-spacer style="border-top: 2px solid #bbb; margin-top: 28px; margin-left: 10px;"></v-spacer>
      <v-btn text @click="GetAllProduct" color="#27AB9C" class="mt-2" style="text-decoration: underline;">ดูทั้งหมด</v-btn>
    </v-row>
    <!-- <pre>{{propsData}}</pre> -->
    <v-row dense justify="start">
      <v-col cols="6" md="2" sm="4" xs="6" v-for="(item, index) in cleandata" :key="index">
        <!-- <pre>{{item}}</pre> -->
        <CardProducts :itemProduct='item' />
      </v-col>
      <!-- <pre>{{ item }}</pre> -->
      <!-- <vue-horizontal-list :items='propsData' :options='options'> -->
        <!-- <template v-slot:default='{ item }'> -->
          <!-- <a-skeleton :loading="check === true ? !loading : loading"> -->
            <!-- <CardProducts :itemProduct='item' /> -->
          <!-- </a-skeleton> -->
        <!-- </template> -->
      <!-- </vue-horizontal-list> -->
    </v-row>
  </v-container>
</template>

<script>
export default {
  props: ['propsData', 'header', 'check'],
  components: {
    CardProducts: () => import('@/components/Card/ProductCardUI')
  },
  data () {
    return {
      options: {
        responsive: [
          { end: 576, size: 1 },
          { start: 576, end: 768, size: 2 },
          { start: 768, end: 992, size: 3 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        }
      },
      loading: true,
      cleandata: []
    }
  },
  created () {
    this.cleanData()
  },
  methods: {
    GetAllProduct () {
      this.$router.push(`/ListProduct/${this.header}?page=1`).catch(() => {})
    },
    cleanData () {
      // console.log(this.propsData)
      var array1 = this.propsData
      var i
      for (i = 0; i < 6; i++) {
        this.cleandata.push(array1[i])
      }
      // console.log(this.cleandata)
    }
  }
}
</script>

<style scoped>

</style>
