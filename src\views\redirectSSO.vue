<template>
  <div></div>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      gotoPath: '',
      dataGetFormRedirectSSO: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  async created () {
    // this.gotoPath = ''
    // this.gotoPath = this.$router.currentRoute.query.sent_to
    var data = {
      code: await this.$router.currentRoute.query.code
    }
    var response = ''
    response = await this.axios.post(`${process.env.VUE_APP_REDIRECT_SSO}`, data)
    if (response.data.code !== 500) {
      if (response.data.code === 200) {
        this.dataGetFormRedirectSSO = response.data.data
        const auth = {
          headers: { Authorization: `Bearer ${this.dataGetFormRedirectSSO.access_token}` }
        }
        var responseData = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/loginSharedTHC`, '', auth)
        if (responseData.data.result === 'SUCCESS') {
          var onedata = {}
          // var permissionsInShop = ''
          onedata.user = responseData.data.data
          var shopList = responseData.data.data
          localStorage.setItem('oneData', Encode.encode(onedata))
          if (this.dataGetFormRedirectSSO.sent_to === 'po_seller') {
            localStorage.setItem('shopSellerID', this.dataGetFormRedirectSSO.seller_shop_id)
            for (let i = 0; i < shopList.list_shop_detail.length; i++) {
              if (this.dataGetFormRedirectSSO.seller_shop_id === shopList.list_shop_detail[i].seller_shop_id) {
                // permissionsInShop = responseData.data.list_shop_detail[i].can_use_function_in_shop
                var dataShop = {
                  id: shopList.list_shop_detail[i].seller_shop_id,
                  name: shopList.list_shop_detail[i].shop_name
                }
                localStorage.setItem('shopDetail', JSON.stringify(dataShop))
                localStorage.setItem('list_shop_detail', Encode.encode(shopList.list_shop_detail[i]))
              }
            }
            this.$EventBus.$emit('CheckShop')
            this.$EventBus.$emit('checkpath')
            this.$EventBus.$emit('getCountInTable')
            this.$EventBus.$emit('checkJVShop')
            this.$EventBus.$emit('AuthorityUsers')
            this.$EventBus.$emit('checkPDPA', responseData.data.data.one_id)
            if (this.MobileSize) {
              this.$router.push({ path: '/posellerMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/poseller' }).catch(() => {})
            }
          } else {
            this.$EventBus.$emit('checkPDPA', responseData.data.data.one_id)
            if (this.MobileSize) {
              this.$router.push({ path: '/pobuyerProfileMobile' }).catch(() => {})
            } else {
              this.$router.push({ path: '/pobuyerProfile' }).catch(() => {})
            }
          }
        } else {
          localStorage.removeItem('oneData')
          window.location.assign('/')
        }
      } else {
        if (response.data.code === 410) {
          this.$swal.fire({ icon: 'error', text: 'Authorization code has expired.', showConfirmButton: false, timer: 2000 })
        } else if (response.data.code === 404) {
          this.$swal.fire({ icon: 'error', text: 'Invalid authorization code.', showConfirmButton: false, timer: 2000 })
        } else {
          this.$swal.fire({ icon: 'error', text: 'Authorization code has unauthorized.', showConfirmButton: false, timer: 2000 })
        }
        localStorage.removeItem('oneData')
        window.location.assign('/')
      }
    } else {
      localStorage.removeItem('oneData')
      window.location.assign('/')
    }
  },
  methods: {
  }
}
</script>
