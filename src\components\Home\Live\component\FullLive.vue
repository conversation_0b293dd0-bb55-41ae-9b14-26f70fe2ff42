<template>
  <v-container  height="100%" hide-delimiters>
    <div style="position: fixed; left: 0; top: 50%; transform: translateY(-60%); z-index: 1000;">
      <v-btn
        icon
        id="prev"
        :v-on="getTrackSuccess? on : ''"
        :disabled="currentIndex == 0"
        @click="NextLive(on, 'prev')"
      ><v-icon color="#27AB9C" large>mdi-chevron-left</v-icon></v-btn>
    </div>
    <div style="position: fixed; right: 0; top: 50%; transform: translateY(-60%); z-index: 1000;">
      <v-btn
        icon
        id="next"
        :v-on="getTrackSuccess? on : ''"
        :disabled="(listLiving.length - 1) == currentIndex"
        @click="NextLive(on, 'next')"
      >
      <v-icon color="#27AB9C" large>mdi-chevron-right</v-icon>
      </v-btn>
    </div>
      <div v-for="(item, index) in listLiving" :key="index">
        <v-card v-if="currentIndex === index" class="mb-5">
          <v-row>
            <v-col cols="12" sm="8" class="pa-2">
              <v-row class="pa-2">
                <v-col cols="6" align-self="center">
                  <v-avatar class="mx-2" v-if="logoShopPath" rounded size="40">
                    <v-img
                    :src="logoShopPath"
                    height="100%"
                    width="100%"
                    style="width: 100%;"
                    v-lazyload
                    contain
                    />
                  </v-avatar>
                  <v-avatar v-else rounded size="40" class="mx-2">
                    <v-img :src="require('@/assets/ImageINET-Marketplace/Shop/Store.png')" />
                  </v-avatar>
                  <span class="ml-2"><b>{{ sellerShopName }}</b></span><br>
                  <span class="ml-2">{{liveTitle}}</span>
                  <span class="ml-2"><v-icon small class="mr-1">mdi-eye</v-icon>{{ updateViewers > 0 ? updateViewers : item.numParticipants }}</span>
                  <span class="ml-2"><v-icon small class="mr-1">mdi-heart</v-icon>{{ 0 }} K</span>
                </v-col>
                <v-spacer></v-spacer>
                <v-col cols="3" align="end" align-self="center">
                  <!-- <v-btn class="ml-2" x-small width="34px" height="34px"><v-icon>mdi-share</v-icon></v-btn> -->
                  <!-- <v-btn class="ml-2" x-small width="34px" height="34px"><v-icon>mdi-dots-horizontal</v-icon></v-btn>
                  <v-btn class="ml-2" x-small width="34px" height="34px">
                    <v-icon v-if="true">mdi-account-check</v-icon>
                    <v-icon v-else>mdi-account-plus</v-icon>
                  </v-btn> -->
                </v-col>
              </v-row>
              <v-divider></v-divider>
              <v-col cols="12">
                <!-- <div :class="MobileSize ? 'd-flex align-center justify-center' : 'mt-2 ml-3 d-flex align-center justify-center'"> -->
                <div class="d-flex justify-center align-center" style="aspect-ratio: 16 / 9; width: 100%; background-color: #000;" :style="MobileSize ? 'max-height: 190px;' : IpadSize ? 'max-height: 300px;' : IpadProSize ? 'max-height: 279px;' : 'max-height: 360px;'">
                  <video
                    :id="currentIndex" width="100%" style="width: 100%; height: 100%; object-fit: contain; transform: scaleX(-1);"
                  ></video>
                  <audio ref="audioElement" :id="'audioElement'+currentIndex" v-if="connect"></audio>
                </div>

                <v-row dense no-gutters class="pa-1">
                  <!-- <v-btn v-if="!play" icon @click="controlsVideo('play')">
                    <v-icon>mdi-play</v-icon>
                  </v-btn>
                  <v-btn v-else icon @click="controlsVideo('pause')">
                    <v-icon>mdi-pause</v-icon>
                  </v-btn> -->
                  <!-- <v-btn @click="dialogProductCard = true">ดูสินค้าภายในร้าน</v-btn> -->
                  <!-- <v-btn @click="dialogProduct = true"></v-btn> -->
                  <!-- <v-btn icon @click="controlsVideo('full')" class="ml-auto">
                    <v-icon>mdi-fullscreen</v-icon>
                  </v-btn> -->
                </v-row>
              </v-col>
              <!-- <v-col>
                <ListShopProduct
                  :propsData="ListproductData"
                  typeProduct="best_seller"
                  header="ms"
                  :isCheck="true"
                />
              </v-col> -->
            </v-col>

            <v-col cols="12" sm="4">
              <div style="background-attachment: scroll;" class="pa-2">
                <v-card width="100%" height="100%" rounded style="display: flex !important; flex-direction: column;">
                  <v-card-title style="font-weight: 700; font-size: 18px; line-height: 30px; color:#333333;">
                    <p>แชทใน Live</p>
                  </v-card-title>
                  <v-card-subtitle><v-divider></v-divider></v-card-subtitle>
                  <v-card-text style="flex-grow: 1; overflow: scroll;" ref="messageContainer">
                    <div style="height: 500px;">
                      <v-card min-height="100%" width="100%" class="d-flex" elevation="0">
                        <v-card-text color="#FAFAFA" class="pa-0">
                          <div v-for="(item, index) in listMessage" :value="index" :key="index" class="pb-1" style="background-color: #FAFAFA">
                            <div class="pb-2">
                              <span><b>{{ item.from }}</b></span><br/>
                              <span>{{ item.message }}</span>
                            </div>
                          </div>
                        </v-card-text>
                      </v-card>
                    </div>
                  </v-card-text>
                  <v-card-text>
                    <v-row style="margin-top: 10px;" class="pb-3">
                      <v-col class="d-flex">
                        <v-text-field
                          class="px-3"
                          v-model="message"
                          placeholder="แสดงความคิดเห็นอะไรดีๆ"
                          dense
                          hide-details
                          outlined
                          rounded
                          @keyup.enter="message !== '' ? sendMessage() : null"
                        ></v-text-field>
                        <v-btn icon @click="sendMessage" :disabled="message == ''" class="mr-2"><v-icon>mdi-telegram</v-icon></v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-col>
          </v-row>
          <v-row>
            <v-col class="px-10">
              <vue-horizontal-list :class="MobileSize || IpadSize ? '' : ''" :items='ListproductData' :options='optionsArticle' v-if="isLoading">
                <template v-slot:nav-prev>
                  <div class="chevron left"><v-icon color="#269AFD" size="32">mdi-chevron-left</v-icon></div>
                </template>
                <template v-slot:nav-next>
                  <div class="chevron right"><v-icon color="#269AFD" size="32">mdi-chevron-right</v-icon></div>
                </template>
                <template v-slot:default="{ item }">
                  <v-col class="px-0" :class="MobileSize ? 'py-0' : 'py-0'">
                    <component v-if="isDataReady" :is="MobileSize || IpadSize ? 'CardProductsMobile' : 'CardProducts'" :itemProduct="item"/>
                  </v-col>
                </template>
              </vue-horizontal-list>
            </v-col>
          </v-row>
        </v-card>
        <v-dialog
          v-model="dialogProduct"
        >
          <v-card>
            <v-toolbar height="50px" style="background-color: #27AB9C;">
              <v-row dense class="ma-2">
                <v-col style="display: flex; align-items: center; justify-content: center;">
                  <span style="font-size: 20px; font-weight: 700; color: #fff">เพิ่มคูปอง</span>
                </v-col>
              </v-row>
            </v-toolbar>
            <v-row>
              <v-col cols="4" v-for="(item, index) in ListproductData" :key="index">
                <v-card>
                  <img :src="item.mediaPath"/>
                </v-card>
              </v-col>
            </v-row>
          </v-card>
        </v-dialog>
      </div>
      <v-dialog v-model="dialogProductCard" persistent width="379">
        <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
            <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
              จัดการหมายเหตุ
            </span>
              <v-btn icon dark @click="closeDialogProductCard()">
              <v-icon color="#27AB9C">mdi-close</v-icon>
            </v-btn>
          </v-toolbar>
          <v-col>
            <ListShopProduct
              :propsData="ListproductData"
              typeProduct="best_seller"
              header="ms"
              :isCheck="true"
            />
          </v-col>
          <v-card-actions>
            <v-row dense justify="center" class="pb-4">
              <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogProductCard()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-card>
      </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
import {
  // eslint-disable-next-line camelcase
  DataPacket_Kind,
  Room,
  RoomEvent
} from 'livekit-client'
// import { Encode } from '@/services'
import VueHorizontalList from 'vue-horizontal-list'
export default {
  props: {
    listLiving: {
      type: Array,
      required: true
    }
    // logoShopPath: {
    //   type: String,
    //   required: true
    // },
    // sellerShopName: {
    //   type: String,
    //   required: true
    // }
  },
  components: {
    ListShopProduct: () => import('../../../Shop/ListShopProduct'),
    VueHorizontalList,
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsMobile: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      room: null,
      remoteTracksMap: new Map(),
      track: [],
      audio: [],
      connect: false,
      getTrackSuccess: false,

      // arrow
      currentIndex: 0,

      userName: '',
      updateViewers: 0,
      // message: 'im watching now good stream' + Math.floor(Math.random() * 100),
      message: '',
      listMessage: [],
      viewers: 0,

      play: false,
      // LIVEKIT_URL: 'wss://helloworld-nt1b7zmh.livekit.cloud',
      LIVEKIT_URL: 'wss://meet-lab.one.th',
      hostName: '',
      logoShopPath: '',
      sellerShopName: '',
      liveTitle: '',
      ListproductData: [],
      dialogProduct: false,
      limit: 36,
      dialogProductCard: false,
      propsData: [],
      isCheck: true,
      isLoading: false
    }
  },
  async beforeDestroy () {
    this.$EventBus.$off('ChangeHostNameLiveStream')
  },
  async mounted () {
    // console.log('first')
    var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.userName = onedata.user.username
    // await this.checkPath()
    // console.log(this.hostName, 'hostname')
    if (this.hostName === '') {
      this.currentIndex = 0
    } else {
      this.currentIndex = this.listLiving.findIndex(e => e.name === this.hostName)
    }
    // console.log(this.logoShopPath, 'logoShopPath', this.listLiving, this.currentIndex)
    if (this.listLiving) {
      this.logoShopPath = this.listLiving[this.currentIndex].logoShopPath
      this.sellerShopName = this.listLiving[this.currentIndex].sellerShopName
      this.liveTitle = this.listLiving[this.currentIndex].title
    }
    // this.newRoom(this.listLiving[0])
    // console.log(this.currentIndex, 'index')
    // console.log(this.hostName !== '' ? this.listLiving.find(e => e.name === this.hostName) : this.listLiving[0], 'ข้อมูลที่จะส่งไป')
    this.newRoom(this.hostName !== '' ? this.listLiving.find(e => e.name === this.hostName) : this.listLiving[0])
    // console.log(this.listLiving.find(e => e.name === this.hostName), 'เมื่อไรจะเห็น log', this.hostName)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    isDataReady () {
      return this.ListproductData && this.ListproductData.length > 0
    },
    cleanData () {
      var array1 = this.ListproductData
      var lengthData = this.ListproductData.length
      var i
      var cleandata = []
      if (lengthData < 12) {
        for (i = 0; i < lengthData; i++) {
          cleandata.push(array1[i])
        }
      } else {
        for (i = 0; i < 12; i++) {
          cleandata.push(array1[i])
        }
      }
      return cleandata
    },
    optionsArticle () {
      return {
        responsive: [
          { end: 576, size: 2 },
          { start: 576, end: 768, size: 3 },
          { start: 768, end: 992, size: 4 },
          { start: 992, end: 1200, size: 5 },
          { size: 5 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: this.IpadSize || this.IpadProSize ? '8' : this.MobileSize ? '4' : '24'
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      }
    }
  },
  async created () {
    this.$EventBus.$on('ChangeHostNameLiveStream', this.changeHostName)
  },
  watch: {
    listMessage () {
      // console.log('ทดสอบแชทไม่ลงมาล่างสุด')
      this.$nextTick(() => {
        const el = this.$refs.messageContainer[0]
        if (el && el.scrollTop !== undefined) {
          el.scrollTop = el.scrollHeight
          // console.log('ได้มั้ย', el)
        }
      })
    }
  },
  methods: {
    async newRoom (item) {
      // ครั้งแรกจะไม่มี item เวลากด next จะมี item เดิมอยู่ต้องลบก่อน
      if (item !== undefined) {
        await this.leaveRoom()
      }
      this.room = new Room()
      // console.log('room', this.room)
      this.remoteTracksMap = new Map()
      await this.room.on(
        RoomEvent.TrackSubscribed,
        (_track, publication, participant) => {
          this.updateViewers = this.room.numParticipants
          // console.log('TrackSubscribed')
          this.remoteTracksMap.set(publication.trackSid, {
            trackPublication: publication,
            participantIdentity: participant.identity
          })
        }
      )

      this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
        this.updateViewers = this.room.numParticipants
        // console.log('Host ออกจากห้อง')
        // console.log('Live: first', participant)
        if (participant.identity.includes('Host')) {
          this.listLiving = this.listLiving.filter(obj => obj.name !== participant.identity)
          // console.log(this.listLiving, 'Live: first')
          if (this.listLiving.length > 1) {
            if (this.currentIndex === this.listLiving.length - 1) { // ถ้าอยู่หน้าสุดท้ายของสไลด์
              this.currentIndex = 0
              document.getElementById('prev').click()
            } else {
              document.getElementById('next').click()
            }
          }
        }
      })

      this.room.on(RoomEvent.DataReceived, (payload, participant, kind, topic) => {
        const message = new TextDecoder().decode(payload)
        // console.log(`📩 ได้รับข้อความ: ${message}`)
        this.listMessage.push({
          from: participant ? participant.identity : 'ไม่ทราบ',
          message: message
        })
      })
      // console.log(item, 'new Room')
      this.getRoom(item.name, item.sellerShopId)
    },
    async getRoom (roomName, shopId) {
      // console.log('getRoom')
      if (roomName) {
        this.$store.commit('openLoader')
        this.token = await this.getToken(roomName, this.userName)
        try {
          await this.room.connect(this.LIVEKIT_URL, this.token)
          // console.log('✅ Connected to LiveKit!')
        } catch (error) {
          console.error('❌ LiveKit Connection Error:', error)
          // this.$swal.fire({
          //   showConfirmButton: false,
          //   timer: 5000,
          //   timerProgressBar: true,
          //   icon: 'error',
          //   text: 'ระบบขัดข้องกรุณาลองอีกครั้ง'
          // })
          // this.$store.commit('closeLoader')
          // console.log('เข้านี้')
        }
        this.updateViewers = this.room.numParticipants
        // console.log('room', this.room)

        for (const remoteTrack of this.remoteTracksMap.values()) {
          if (remoteTrack.trackPublication.kind === 'video') {
            this.track = remoteTrack.trackPublication.videoTrack
          } else {
            this.audio = remoteTrack.trackPublication.audioTrack
          }
        }
        this.play = true
        // console.log('id', this.currentIndex)
        // console.log('connect', this.connect)
        // console.log(this.track, 'track')
        if (this.track.length !== 0) {
          await this.track.attach(document.getElementById(this.currentIndex))
          // await this.track.attach(document.getElementById(this.currentIndex))
        }
        if (this.audio.length !== 0) {
          await this.audio.attach(document.getElementById('audioElement' + this.currentIndex))
        }
        this.connect = true
        // this.audio.attach(this.$refs.audioElement)
        // var data = {
        //   shopId: shopId
        // }
        // await this.$store.dispatch('actionsLiveListProduct', data)
        // var response = await this.$store.state.ModuleLiveStream.stateLiveListProduct
        // if (response.code === 200) {
        //   console.log(response, 'response')
        //   this.ListproductData = response.data
        // } else if (response.code === 400) {
        //   this.ListproductData = []
        // }
        // console.log(this.ListproductData, 'this.ListproductData')
        await this.getProductData(shopId)
        this.$store.commit('closeLoader')
      }
    },
    async getToken (roomName, participantName) {
      // console.log('mamie')
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ roomName, participantName })
      })

      if (!response.ok) {
        const error = await response.json()
        throw new Error(`Failed to get token: ${error.errorMessage}`)
      } else if (response.code === 404) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาลองอีกครั้ง'
        })
        this.$store.commit('closeLoader')
      }

      const data = await response.json()
      return data.token
    },

    async controlsVideo (action) {
      if (action === 'play') {
        this.play = true
        this.pause = false
        await this.track.attach(document.getElementById(this.currentIndex))
        await this.track.attach(document.getElementById(this.currentIndex))
      } else {
        this.play = false
        this.pause = true
        document.getElementById(this.currentIndex).pause()
      }
    },
    async roomOnEverything () {
      this.room.on(RoomEvent.ParticipantConnected, (participant) => {
        // this.viewers = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
        // console.log('Video ParticipantConnected')
        // console.log('first', participant)
        // this.viewers = this.room.roomInfo.numParticipants
      })

      // this.room.on(RoomEvent.DataReceived, (payload, participant, kind, topic) => {
      //   const message = new TextDecoder().decode(payload)
      //   console.log(`📩 ได้รับข้อความ: ${message}`)
      //   this.listMessage.push({
      //     from: participant ? participant.identity : 'ไม่ทราบ',
      //     message: message
      //   })
      // })

      // this.room.on(RoomEvent.ParticipantDisconnected, (participant) => {
      //   // this.viewers = this.room.numParticipants === 0 ? this.room.numPublishers : this.room.numParticipants
      //   console.log('live ParticipantDisconnected')
      //   console.log('first', participant)
      // })
    },
    async leaveRoom () {
      // this.getTrackSuccess = false
      // console.log('leaveRoom', this.room)
      if (this.room) {
        await this.room.disconnect()
        this.connected = false
      }
    },
    async NextLive (on, status) {
      // console.log('nextLive')
      if (status === 'next') {
        this.currentIndex += 1
      } else {
        this.currentIndex -= 1
      }
      this.listMessage = []
      this.getTrackSuccess = true
      // console.log('this.getTrackSuccess', this.getTrackSuccess)
      this.logoShopPath = this.listLiving[this.currentIndex].logoShopPath
      this.sellerShopName = this.listLiving[this.currentIndex].sellerShopName
      this.liveTitle = this.listLiving[this.currentIndex].title
      await this.newRoom(this.listLiving[this.currentIndex])
    },

    async sendMessage () {
      try {
        // console.log(this.room.state)
        const message = this.message
        const data = new TextEncoder().encode(message)

        // eslint-disable-next-line camelcase
        await this.room.localParticipant.publishData(data, DataPacket_Kind.RELIABLE, {
          destinationIdentities: [],
          topic: 'chat'
        })
        // console.log('✅ ส่งข้อความสำเร็จ')

        this.listMessage.push({
          from: 'You',
          message: this.message
        })
        // const chatBox = document.getElementById('chatBox')
        // chatBox.scrollTop = chatBox.scrollHeight
        // this.$nextTick(() => {
        //   const el = this.$refs.chatBox
        //   el.scrollTop = el.scrollHeight
        // })
        // console.log(chatBox)
        this.message = ''
      } catch (error) {
        console.error('❌ ส่งข้อความไม่สำเร็จ:', error)
      }
    },
    async checkPath () {
      if (this.hostName === '') {
        var path = await this.$route.query.name
      }
      // console.log(path, 'as45')
      if (path && this.hostName !== '') {
        // this.show = 1
        this.hostName = path
      }
    },
    async changeHostName (hostName) {
      // console.log(hostName, 'host name Fulllive')
      // console.log('ตรงนี้ยิงกี่รอบ')
      this.hostName = hostName
      // console.log('changeHostName')
      if (this.hostName === '') {
        this.currentIndex = 0
      } else {
        this.currentIndex = this.listLiving.findIndex(e => e.name === this.hostName)
      }
      if (this.listLiving) {
        this.logoShopPath = this.listLiving[this.currentIndex].logoShopPath
        this.sellerShopName = this.listLiving[this.currentIndex].sellerShopName
        this.liveTitle = this.listLiving[this.currentIndex].title
      }
      this.listMessage = []
      // console.log('มามั้ย', this.hostName)
      await this.newRoom(this.hostName !== '' ? this.listLiving.find(e => e.name === this.hostName) : this.listLiving[0])
      // this.$EventBus.$off('ChangeHostNameLiveStream')
      // this.$EventBus.$on('ChangeHostNameLiveStream')
    },
    async getProductData (shopId) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
      var companyID = '-1'
      var dataBestSeller = {
        role_user: this.RoleUser,
        company_id: companyID === null ? '-1' : companyID,
        category: '',
        seller_shop_id: shopId,
        orderBy: '',
        status_product: 'best-seller',
        limit: this.limit,
        page: 1
      }
      await this.$store.dispatch('actionsSelectCategoryShopList', dataBestSeller)
      var response = await this.$store.state.ModuleShop.stateSelectCategoryShopList
      if (response.ok === 'y') {
        this.ListproductData = await [...response.query_result]
        // console.log(this.ListproductData)
        this.isLoading = true
      } else if (response.code === 400) {
        this.ListproductData = []
      }
    },
    async closeDialogProductCard () {
      this.dialogProductCard = false
    }
  }
}
</script>

<style scoped>
.video-container {
  position: relative; /* Needed for absolute positioning of controls */
  overflow: hidden; /* Prevents the mirrored video from overflowing */
}

.video-container video {
  width: 50%;
  height: 50%;
  background-color: #000;
}

.controls {
  position: absolute; /* Or relative, depending on your layout */
  bottom: 10px; /* Adjust as needed */
  left: 10px; /* Adjust as needed */
  /* Style your controls here */
  display: flex; /* or inline-flex */
}
.controls button {
    margin-right: 5px;
}
.chevron-left {
  top: 160px;
  left: -14px;
}
@media (max-width: 600px) {
  ::v-deep(.v-window__next) {
    top: 160px;
    right: -15px;
  }
  ::v-deep(.v-window__prev) {
    top: 160px;
    left: -14px;
  }
}

.chevron.left {
  left: 10px;
}

.chevron.right {
  right: 10px;
}
</style>
