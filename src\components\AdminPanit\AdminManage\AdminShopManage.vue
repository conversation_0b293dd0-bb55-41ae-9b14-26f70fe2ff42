<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0" v-if="divAdminShopManage">
      <v-row v-if="!MobileSize && !IpadSize">
        <v-col cols="8">
          <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;">รายชื่อร้านค้าของระบบ</v-card-title>
        </v-col>
        <v-col cols="4" style="display: flex; justify-content: end; align-items: center;">
          <v-btn color="#38b2a4" class="mr-4" rounded @click="exportExcel()">
            <span class="white--text">EXPORT ร้านค้าที่ใช้คูปองระบบ</span>
          </v-btn>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col cols="12">
          <v-card-title style="font-weight: 700;"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายชื่อร้านค้าของระบบ</v-card-title>
        </v-col>
        <v-col cols="12" style="display: flex; justify-content: center; align-items: center; margin-top: -20px;">
          <v-btn color="#38b2a4" width="90%" rounded @click="exportExcel()">
            <span class="white--text">EXPORT ร้านค้าที่ใช้คูปองระบบ</span>
          </v-btn>
        </v-col>
      </v-row>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อร้านค้าของระบบ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="adminShopList.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อร้านค้าของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="adminShopList.length !== 0 && (MobileSize || IpadSize)">รายชื่อร้านค้าของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="adminShopList"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อร้านค้าของระบบ"
                no-data-text="ไม่มีชื่อร้านค้าของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ index }">
                    {{ index + 1 }}
                  </template>
                  <template v-slot:[`item.shipping`]="{ item }">
                    {{ (item.shipping_method.length !== 0 && item.shipping_method !== '[]' && item.shipping_method !== null) ? 'ใช้ขนส่งภายในระบบ' : 'ไม่ใช้ขนส่งภายในระบบ' }}
                  </template>
                  <template v-slot:[`item.merchant_mobilyst`]="{ item }">
                    <v-btn v-if="(item.shipping_method.length !== 0 && item.shipping_method !== '[]' && item.shipping_method !== null) && item.merchant_mobilyst_key === null" color="#27AB9C" outlined @click="OpenModalWallet(item, 'add')">เพิ่ม Wallet Mobilyst</v-btn>
                    <span v-else-if="(item.shipping_method.length !== 0 && item.shipping_method !== '[]' && item.shipping_method !== null) && item.merchant_mobilyst_key !== null">{{ item.merchant_mobilyst_key }} <v-btn icon  @click="OpenModalWallet(item, 'edit')"><v-icon small color="#27AB9C">mdi-pencil</v-icon></v-btn></span>
                    <span v-else> - </span>
                  </template>
                  <template v-slot:[`item.is_gp`]="{ item }">
                    <span>{{ item.is_gp === 'Y' ? 'คิด GP' : 'ไม่คิด GP' }} <v-btn icon  @click="OpenModalGP(item)"><v-icon small color="#27AB9C">mdi-pencil</v-icon></v-btn></span>
                  </template>
                  <template v-slot:[`item.shop_pdf`]="{ item }">
                    <v-btn
                      width="24"
                      height="24"
                      v-if="item.documentFDA.length !== 0"
                      style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                      outlined icon small @click="goDetailPDF(item.id)">
                      <v-icon color="#27AB9C" class="" size="18">mdi-file-document-outline</v-icon>
                    </v-btn>
                  </template>
                  <template v-slot:[`item.actions`]="{ item }">
                    <v-btn @click="gotoSittingShop(item)" text color="#27AB9C">เข้าสู่ร้านค้า</v-btn>
                  </template>
                  <!-- <template v-slot:[`item.syncERP`]="{ item }">
                    <v-switch
                      v-model="item.syncERP"
                      inset
                    ></v-switch>
                  </template> -->
                  <template v-slot:[`item.ERPService`]="{ item }">
                    <v-btn outlined @click="serviceMenageOpen(item.ERPService)" :disabled="item.ERPService.length === 0 ? true : false" color="#27AB9C">
                      <span>จัดการ service</span>
                    </v-btn>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-dialog v-model="modalWallet" persistent width="450">
      <v-card>
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'">
                <b>เพิ่ม Wallet Mobilyst</b>
              </span>
            </v-col>
            <v-btn fab small @click="closeModal()" icon class="mt-2">
              <v-icon color="white">mdi-close</v-icon>
            </v-btn>
          </v-row>
        </v-card-title>

        <v-card-text class="pt-4">
          <v-row dense>
            <v-col cols="12">
              <span style="font-size: 16px; font-weight: 600;">Key Wallet Mobilyst :</span>
            </v-col>
            <v-col cols="12">
              <v-text-field v-model="keyWallet" outlined style="border-radius: 8px;" dense placeholder="กรอก Key Wallet Mobilyst"></v-text-field>
            </v-col>
          </v-row>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions class="pa-4">
          <v-btn
            color="primary"
            text
            rounded
            @click="closeModal()"
          >
            ปิด
          </v-btn>
          <v-spacer></v-spacer>
          <v-btn
            color="primary"
            rounded
            @click="saveWalletKey(dataShop)"
          >
            บันทึก
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-card width="100%" height="100%" elevation="0" :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']" v-if="divShopManagePDF">
      <v-card-title>
        <v-row dense>
          <v-col cols="12" md="6">
            <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoPageAdminShopManage()">mdi-chevron-left
              </v-icon>เอกสาร อย.ร้านค้า
            </span>
          </v-col>
          <v-col cols="12" md="6" sm="12" align="end">
            <v-row dense v-if="MobileSize" no-gutters class="px-0">
              <v-col cols="12" align="center" v-if="statusPDF !== 'Approve' && statusPDF !== 'Reject'">
                <v-btn @click="openConfirmReject = true" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C">ไม่อนุมัติ</v-btn>
                <v-btn @click="openConfirmApprove = true" rounded color="#27AB9C" class="px-2" dark>อนุมัติ</v-btn>
              </v-col>
            </v-row>
            <v-row dense justify="end" v-else>
              <v-col cols="12" align="end" v-if="statusPDF !== 'Approve' && statusPDF !== 'Reject'">
                <v-btn @click="openConfirmReject = true" width="125" height="40" rounded outlined color="#27AB9C" class="mr-1">ไม่อนุมัติ</v-btn>
                <v-btn @click="openConfirmApprove = true" width="125" height="40" rounded color="#27AB9C" dark elevation="0">อนุมัติ</v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-title>
      <v-row dense justify="center" align="center">
        <!-- ใส่ iframe -->
        <v-card width="90%" height="50%" outlined style="background: #C4C4C4; border-radius: 8px;" class="mt-4 mb-5">
          <v-card-text :class="MobileSize ? 'pa-0' : ''">
            <iframe width="100%" :src="PDF_path" :height="MobileSize ? '500' : IpadSize ? '700' : '1200'"></iframe>
          </v-card-text>
        </v-card>
      </v-row>
    </v-card>

    <v-dialog v-model='openConfirmApprove' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openConfirmApprove = false">
      <v-card min-height='100%'>
          <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
          >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn plain fab small @click='openConfirmApprove = false' icon
              ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
              >
          </v-app-bar>
          </v-img>
          <v-container>
          <v-card-text style="text-align: center;">
              คุณต้องการ อนุมัติเอกสาร ใช่หรือไม่
          </v-card-text>
          <v-card-actions>
          <v-row dense class='d-flex justify-space-between' style="gap: 20px;">
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="openConfirmApprove = false">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="confirmApprove('Approve', docID)">ตกลง</v-btn>
          </v-row>
          </v-card-actions>
          </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model='openConfirmReject' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openConfirmReject = false">
      <v-card min-height='100%'>
          <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
          >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn plain fab small @click='openConfirmReject = false' icon
              ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
              >
          </v-app-bar>
          </v-img>
          <v-container>
          <v-card-text style="text-align: center;">
              คุณต้องการ ไม่อนุมัติเอกสาร ใช่หรือไม่
          </v-card-text>
          <v-card-actions>
          <v-row dense class='d-flex justify-space-between' style="gap: 20px;">
              <v-btn outlined rounded color="#27AB9C" class="mr-2" :style="{ flex: '1' }" @click="openConfirmReject = false">ยกเลิก</v-btn>
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="confirmReject('Reject', docID)">ตกลง</v-btn>
          </v-row>
          </v-card-actions>
          </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model='openSuccess' :width="MobileSize ? '60%' : '30%'" persistent @keydown.esc="openSuccess = false">
      <v-card min-height='100%'>
          <v-img height="100%" :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')">
          <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
          >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn plain fab small @click='openSuccess =false' icon
              ><v-icon color='#BABABA'>mdi-close</v-icon></v-btn
              >
          </v-app-bar>
          </v-img>
          <v-container>
          <v-card-text style="text-align: center;">
              คุณได้ทำรายการนี้เรียบร้อย
          </v-card-text>
          <v-card-actions>
          <v-row dense class='d-flex justify-center' style="padding: 0 25%;">
              <v-btn class="white--text ml-2" rounded color="#27AB9C" :style="{ flex: '1' }" @click="openSuccess =false">ตกลง</v-btn>
          </v-row>
          </v-card-actions>
          </v-container>
      </v-card>
    </v-dialog>

    <v-dialog v-model="DialogListService" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : '900'" content-class="elevation-0">
      <v-card elevation="0" style="background: #ffffff; border-radius: 24px; overflow-x: hidden; overflow-y: hidden">
      <v-card-text class="px-0 pt-0">
        <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 900px'" class="backgroundHead" style="position: absolute; height: 120px">
          <v-row style="height: 120px">
            <v-col style="text-align: center" class="pt-4">
              <span :class=" MobileSize ? 'title-mobile white--text' : 'title white--text'" :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>รายการ Service</b></span>
            </v-col>
            <v-btn fab small @click="DialogListService = !DialogListService" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </div>
        <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
          <v-row :width="MobileSize ? '100%' : '650px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #ffffff;">
            <v-col style="text-align: center"> </v-col>
          </v-row>
        </div>
        <v-card elevation="0" width="100%" height="100%" style="background: #ffffff; border-radius: 20px 20px 0px 0px" :style="MobileSize ? 'padding: 20px 20px 10px 20px;' : 'padding: 40px 48px 10px 48px;'">
          <v-card-text class="pa-0">
            <v-row :class="MobileSize ? 'mt-5' :'mt-1'">
              <v-col cols="12" md="6" sm="8">
                <v-text-field v-model="searchService" dense hide-details outlined placeholder="ค้นหาจากชื่อร้านค้า" style="border-radius: 8px;">
                  <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                </v-text-field>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-row>
                  <v-col>
                    <v-card>
                      <v-data-table
                      :headers="headersERPService"
                      :items="DataERPService"
                      :items-per-page="5"
                      :search="searchService"
                      :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                    >
                      <template v-slot:[`item.service_sync`]="{ item }">
                        <v-row>
                          <v-col class="d-flex justify-center">
                            <v-switch
                            v-model="item.service_sync"
                            inset
                            @change="editSyncERP(item)"
                          ></v-switch>
                          </v-col>
                        </v-row>
                      </template>
                    </v-data-table>
                    </v-card>
                  </v-col>
                </v-row>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-card-text>
      </v-card>
    </v-dialog>

    <v-dialog v-model="modalGP" persistent width="450">
      <v-card>
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'">
                <b>แก้ไข GP</b>
              </span>
            </v-col>
            <v-btn fab small @click="closeModal()" icon class="mt-2">
              <v-icon color="white">mdi-close</v-icon>
            </v-btn>
          </v-row>
        </v-card-title>

        <v-card-text class="pt-4">
          <v-row dense align="center" justify="center">
            <v-col cols="auto" class="pa-0">
              <span style="font-size: 16px; font-weight: 600;">เลือก GP : </span>
            </v-col>
            <v-col cols="auto" class="pa-0">
              <v-radio-group
                v-model="statusGP"
                :row="true"
                style="margin-left: 5px;"
              >
                <v-radio label="คิด GP" value="Y"></v-radio>
                <v-radio label="ไม่คิด GP" value="N"></v-radio>
              </v-radio-group>
            </v-col>
          </v-row>
        </v-card-text>

        <v-divider></v-divider>

        <v-card-actions class="pa-4">
          <v-btn color="primary" text rounded @click="closeModal()">ปิด</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="primary" rounded @click="saveEditGP(dataShop)">บันทึก</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      search: '',
      adminShopList: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      keyWallet: '',
      page: 1,
      isSuperAdmin: null,
      statusAction: '',
      modalWallet: false,
      statusGP: '',
      modalGP: false,
      divAdminShopManage: true,
      divShopManagePDF: false,
      PDF_path: '',
      statusPDF: false,
      docID: '',
      openConfirmApprove: false,
      openConfirmReject: false,
      openSuccess: false,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขประจำตัวผู้เสียภาษี', value: 'tax_id', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_th', width: '180', align: 'center', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้าน', value: 'shop_name', width: '180', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ขนส่งในระบบ', value: 'shipping', width: '180', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'wallet mobilyst', value: 'merchant_mobilyst', align: 'center', width: '200', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'GP', value: 'is_gp', width: '130', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'อย.ร้านค้า', value: 'shop_pdf', width: '100', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'sync ERP', value: 'ERPService', width: '100', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', filterable: false, align: 'center', width: '150', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      dataShop: [],
      headersERPService: [
        { text: 'ชื่อ service', value: 'service_name', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' },
        { text: 'สถานะเปิด-ปิดของ service', value: 'service_sync', width: '100', sortable: false, class: 'backgroundTable fontTable--text', align: 'center' }
      ],
      DataERPService: [],
      DialogListService: false,
      searchService: '',
      syncService: {
        shop_id: '',
        action: '',
        service_id: ''
      },
      typePartner: ''
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/adminShopManageMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'adminShopManage')
        this.$router.push({ path: '/adminShopManage' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
  },
  methods: {
    async saveWalletKey (shopData) {
      var data = {
        seller_id: shopData.id,
        merchant_mobilyst_key: this.keyWallet
      }
      await this.$store.dispatch('actionsMerchantEdit', data)
      var response = await this.$store.state.ModuleAdminManage.stateMerchantEdit
      if (response.result === 'SUCCESS') {
        if (this.statusAction === 'add') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: 'เพิ่ม Wallet Mobilyst สำเร็จ'
          })
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            text: 'แก้ไข Wallet Mobilyst สำเร็จ'
          })
        }
        this.closeModal()
        this.getShopData()
      } else {
        if (this.statusAction === 'add') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'เพิ่ม Wallet Mobilyst ไม่สำเร็จสำเร็จ'
          })
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'แก้ไข Wallet Mobilyst ไม่สำเร็จสำเร็จ'
          })
        }
      }
    },
    OpenModalWallet (item, status) {
      this.dataShop = item
      this.statusAction = status
      if (item.merchant_mobilyst_key === null) {
        this.keyWallet = ''
      } else {
        this.keyWallet = item.merchant_mobilyst_key
      }
      this.modalWallet = true
    },
    async saveEditGP (shopData) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: shopData.id,
        status_gp: this.statusGP
      }
      // console.log(data)
      await this.$store.dispatch('actionsEditGP', data)
      var response = await this.$store.state.ModuleAdminManage.stateEditGP
      if (response.message === 'Update Data Success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          text: 'แก้ไข GP สำเร็จ'
        })
        this.closeModal()
        this.getShopData()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    OpenModalGP (item) {
      this.dataShop = item
      this.statusGP = item.is_gp
      // console.log(this.statusGP)
      this.modalGP = true
    },
    closeModal () {
      this.dataShop = []
      this.keyWallet = ''
      this.modalWallet = false
      this.statusGP = ''
      this.modalGP = false
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    backtoPageAdminShopManage () {
      this.divShopManagePDF = false
      if (!this.MobileSize) {
        this.$router.push({ path: '/adminShopManage' }).catch(() => { })
      } else {
        this.$router.push({ path: '/adminShopManageMobile' }).catch(() => { })
      }
      this.divAdminShopManage = true
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetShopDataAdmin')
      var response = await this.$store.state.ModuleAdminManage.stateGetShopDataAdmin
      if (response.message === 'List Seller Shop Success.') {
        this.$store.commit('closeLoader')
        this.adminShopList = [...response.data]
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    async gotoSittingShop (shopDetail) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: shopDetail.id
      }
      await this.$store.dispatch('actionsAddAdminShop', data)
      var response = await this.$store.state.ModuleAdminManage.stateAddAdminShop
      if (response.result === 'SUCCESS') {
        await this.$store.dispatch('actionsAuthorityUser')
        var responseAuth = await this.$store.state.ModuleUser.stateAuthorityUser
        var listSeller = await responseAuth.data.list_shop_detail
        for (let i = 0; i < listSeller.length; i++) {
          if (shopDetail.id === listSeller[i].seller_shop_id) {
            localStorage.setItem('list_shop_detail', Encode.encode(listSeller[i]))
          }
        }
        const sellershopDetail = {
          id: shopDetail.id,
          name: shopDetail.shop_name
        }
        this.$store.commit('closeLoader')
        var shopname = shopDetail.shop_name
        localStorage.setItem('shopDetail', JSON.stringify(sellershopDetail))
        localStorage.setItem('shopSellerID', shopDetail.id)
        await this.$EventBus.$emit('getUserDetailMP')
        if (!this.MobileSize) {
          this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopname })
        } else {
          this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopname })
        }
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async exportExcel () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}coupon/export/seller`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET',
        responseType: 'blob'
      }).then((response) => {
        // console.log(response)
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'SellerCoupon.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
    },
    async goDetailPDF (item) {
      this.divAdminShopManage = false
      this.divShopManagePDF = true
      window.scrollTo(0, 0)
      this.$store.commit('openLoader')
      const payload = {
        seller_shop_id: item
      }
      // console.log('data item', item)
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END}api/admin_platform/admin_list_documents_seller_shop`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: payload
      }).then((response) => {
        // console.log('data ====>', response.data.data[0])
        this.PDF_path = response.data.data[0].path
        // console.log('this.PDF_path', this.PDF_path)
        this.statusPDF = response.data.data[0].doc_status
        this.docID = response.data.data[0].id
        this.$store.commit('closeLoader')
      })
    },
    async confirmReject (status, id) {
      if (status === 'Reject') {
        this.$store.commit('openLoader')
        await this.sendStatus(status, id)
        this.openConfirmReject = false
        this.statusPDF = 'Reject'
        this.$store.commit('closeLoader')
        this.openSuccess = true
      }
    },
    async confirmApprove (status, id) {
      if (status === 'Approve') {
        this.$store.commit('openLoader')
        await this.sendStatus(status, id)
        this.openConfirmApprove = false
        this.statusPDF = 'Approve'
        this.$store.commit('closeLoader')
        this.openSuccess = true
      }
    },
    async sendStatus (status, id) {
      const payload = {
        document_id: id,
        status: status
      }
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END}api/admin_platform/approve_fda`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: payload
      }).then((response) => {
        this.getShopData()
        // console.log('response', response)
      })
    },
    async serviceMenageOpen (listService) {
      // console.log(listService[0])
      this.DialogListService = true
      this.DataERPService = listService
      // console.log(this.DataERPService)
    },
    async editSyncERP (val) {
      // เปิดจรงนี้ด้วยถ้าหลังบ้านส่ง key มาแล้ว
      var dataSyncShop = ''
      var res = ''
      if (val.service_sync === true) {
        this.syncService = {
          action: 'sync',
          shop_id: val.seller_shop_id,
          service_id: val.service_id
        }
        // รบกวนให้หลังบ้านเพิ่ม key ที่ไม่มีนะ จะได้เชื่อมกับของหน้าพี่ยอดได้
        dataSyncShop = {
          provider: val.service_provider,
          service_key_id: val.service_key_id,
          seller_shop_id: val.seller_shop_id,
          status: 'active'
        }
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsSetSyncShopErp', this.syncService)
        var response = await this.$store.state.ModuleShop.stateSetSyncShopErp
        if (response.result === 'SUCCESS') {
          await this.getShopData()
          // เปิดส่วนนี้เมื่อหลังบ้านส่ง key มาแล้ว
          await this.$store.dispatch('actionsUpdateStatusServiceKeyERP', dataSyncShop)
          res = await this.$store.state.ModuleShop.stateUpdateStatusServiceKeyERP
          if (res.result === 'SUCCESS') {
            this.$swal.fire({
              icon: 'success',
              text: 'เปิดการใช้งานสำเร็จ',
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 1500
            })
            await this.getShopData()
          } else if (res.result === 'FAILED') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'error',
              text: res.message,
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 2000
            })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'error',
              text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 1500
            })
          }
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: `${response.message}`
          })
        }
        this.$store.commit('closeLoader')
      } else if (val.service_sync === false) {
        this.syncService = {
          action: 'unsync',
          shop_id: val.seller_shop_id,
          service_id: val.service_id
        }
        // รบกวนให้หลังบ้านเพิ่ม key ที่ไม่มีนะ จะได้เชื่อมกับของหน้าพี่ยอดได้
        dataSyncShop = {
          provider: val.service_provider,
          service_key_id: val.service_key_id,
          seller_shop_id: val.seller_shop_id,
          status: 'inactive'
        }
        this.$store.commit('openLoader')
        await this.$store.dispatch('actionsSetSyncShopErp', this.syncService)
        var responseFail = await this.$store.state.ModuleShop.stateSetSyncShopErp
        if (responseFail.result === 'SUCCESS') {
          await this.getShopData()
          // เปิดส่วนนี้เมื่อหลังบ้านส่ง key มาแล้ว
          await this.$store.dispatch('actionsUpdateStatusServiceKeyERP', dataSyncShop)
          res = await this.$store.state.ModuleShop.stateUpdateStatusServiceKeyERP
          if (res.result === 'SUCCESS') {
            this.$swal.fire({
              icon: 'success',
              text: 'ปิดการใช้งานสำเร็จ',
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 1500
            })
            await this.getShopData()
          } else if (res.result === 'FAILED') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'error',
              text: res.message,
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 2000
            })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              icon: 'error',
              text: 'ระบบขัดข้อง โปรดติดต่อเจ้าหน้าที่',
              showConfirmButton: false,
              timerProgressBar: true,
              timer: 1500
            })
          }
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: `${responseFail.message}`
          })
        }
        this.$store.commit('closeLoader')
      }
    },
    async setSyncPayload () {
      // this.syncService.shop_id =
      // this.syncService.service_id
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(10) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(10) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
