<template>
      <div>
        <div  id='chart' class="mt-5">
        <apexchart type='line' height='350' :options='chartOptions' :series='$store.getters.drawChart'></apexchart>
        </div>
      </div>
</template>
<script>
// import ApexCharts from 'apexcharts'
// import dataTest from '../library/dataTest.json'
// import eventBus from '@/components/eventBus'
import VueApexCharts from 'vue-apexcharts'
export default {
  name: 'ApexChart',
  components: {
    apexchart: VueApexCharts
  },
  data () {
    return {
      toDay: new Date().toISOString().slice(0, 10),
      Day: `${new Date().toISOString().slice(0, 7)}-01`,
      series: [],
      chartOptions: {
        chart: {
          height: 350,
          type: 'line',
          dropShadow: {
            enabled: true,
            color: '#000',
            top: 18,
            left: 7,
            blur: 10,
            opacity: 0.2
          },
          toolbar: {
            show: true
          }
        },
        dataLabels: {
          enabled: false
        },
        stroke: {
          curve: 'smooth'
        },
        title: {
          text: '',
          align: 'left'
        },
        grid: {
          borderColor: '#e7e7e7',
          row: {
            colors: ['#f3f3f3', 'transparent'], // takes an array which will be repeated on columns
            opacity: 0.5
          }
        },
        markers: {
          size: 1
        },
        xaxis: {
          type: 'category',
          labels: {
            formatter: function (val) {
              return new Date(val).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })
            }
          }
        },
        yaxis: {
          title: {
            text: ''
          },
          min: 0,
          max: 1000
        },
        legend: {
          position: 'top',
          horizontalAlign: 'right',
          floating: true,
          offsetY: -25,
          offsetX: -5
        }
      }
    }
  },
  created () {
    // console.log('today', this.toDay, this.Day)
    // this.init()
    this.$EventBus.$on('appendData', this.appendData)
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
    // this.init()
    // console.log(planet)
    // document.getElementById('planet-chart').setAttribute('data-id', planet)
  },
  computed: {
  },
  watch: {
    '$store.getters.chartOption' (e) {
      // console.log(e)
    }
  },
  methods: {
    // async init () {
    //   const data = await {
    //     start_date: this.Day,
    //     end_date: this.toDay
    //   }
    //   await this.$store.dispatch('actionsDashboard', data)
    //   var response = await this.$store.state.ModuleShop.stateDashboard
    //   var dataFilter = await []
    //   dataFilter = await response.data
    //   this.chartOptions.yaxis.max = await Math.max.apply(Math, dataFilter.map(o => o.value))
    // },
    async init () {
      // const data = await {
      //   start_date: this.Day,
      //   end_date: this.toDay
      // }
      // await this.$store.dispatch('actionsDashboard', data)
      // var response = await this.$store.state.ModuleShop.stateDashboard
      // var dataFilter = await []
      // dataFilter = await response.data
      // this.chartOptions.yaxis.max = await Math.max.apply(Math, dataFilter.map(o => o.value))
      // var grouped = await {}
      // for (const i in dataFilter) {
      //   grouped[dataFilter[i].date] = await grouped[dataFilter[i].buyer_name] || []
      //   await grouped[dataFilter[i].date].push(dataFilter[i].value)
      // }
      // const statusObj = await []
      // for (const [k, v] of Object.entries(grouped)) {
      //   await statusObj.push({
      //     name: k,
      //     data: v
      //   })
      // }
      // console.log('categories--', statusObj)
      // this.series = await []
      // this.series = await statusObj
      // this.chartOptions = await {
      //   xaxis: {
      //     categories: this.$store.state.ModuleShop.stateDateTime
      //   }
      // }
      // console.log('categories++', this.chartOptions.xaxis.categories)
    },
    async appendData () {
      // console.log('appendData***', this.series)
      this.series = await this.$store.state.ModuleShop.stateSeries
      // console.log('LineChar', this.series)
      // this.series = await this.$store.state.ModuleShop.sateSeries.map(el => {
      //   return {
      //     name: Object.values(el.name),
      //     data: el.data
      //   }
      // })
      // this.chartOptions = await {
      //   colors: colors
      // }
      this.chartOptions = await {
        xaxis: {
          categories: this.$store.state.ModuleShop.stateDateTime
        },
        yaxis: {
          max: this.$store.getters.chartOption
        },
        title: {
          text: `ข้อมูลการซื้อสินค้าของ : ${this.series[0].name}`,
          align: 'left',
          offsetX: 12,
          offsetY: 10
        }
      }
      // console.log('DataSeries', this.$store.state.ModuleShop.stateSeries, '==', this.$store.getters.chartColor)
    }
  }
}
</script>
<style scoped>
 ::v-deep .apexcharts-legend{
  display: none;
}
</style>
