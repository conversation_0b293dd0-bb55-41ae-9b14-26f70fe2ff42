<template>
  <v-container>
    <v-row dense justify="center">
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-row class="mb-2 ml-1">
          <v-col cols="12" align="start" class="pl-0">
            <span style="font-weight: bold; font-size: 28px;">{{ settingAction }}</span>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card elevation="0" width="100%" height="100%">
          <v-row justify="center" dense class="mx-5">
            <v-col cols="12" md="11" sm="12" xs="12">
              <v-form ref="form" v-model="valid" lazy-validation>
                <v-row justify="center" dense class="mt-5">
                  <!-- ฝ่าย  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>ฝ่าย<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-autocomplete class="custom-autocomplete" dense solo clearable v-model="group" :items="selectGroup" item-text="name" item-value="name" label="กรุณาเลือกฝ่าย"></v-autocomplete>
                    </v-row>
                  </v-col>
                  <!-- แผนก  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>แผนก<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-autocomplete class="custom-autocomplete" dense solo clearable v-model="department" :items="selectDepartment" item-text="name" item-value="name" label="กรุณาเลือกแผนก"></v-autocomplete>
                    </v-row>
                  </v-col>
                  <!-- ประเภทการอนุมัติ  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>ประเภทการอนุมัติ<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-autocomplete class="custom-autocomplete" dense solo clearable v-model="typeApprove" :items="selectTypeApprove" item-text="name" item-value="name" label="กรุณาเลือกประเภทการอนุมัติ"></v-autocomplete>
                    </v-row>
                  </v-col>
                  <!-- ผู้อนุมัติ  -->
                  <v-col cols="2" md="2" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1 pt-3">
                      <span>ผู้อนุมัติ<span style="color: red;">*</span></span>
                    </v-row>
                  </v-col>
                  <v-col cols="4" md="4" sm="6" xs="12">
                    <v-row dense justify="start" class="ml-1">
                      <v-autocomplete class="custom-autocomplete" dense solo clearable v-model="approver" :items="selectApprover" item-text="name" item-value="name" label="กรุณาเลือกผู้อนุมัติ"></v-autocomplete>
                    </v-row>
                  </v-col>
                </v-row>
              </v-form>
            </v-col>
          </v-row>
        </v-card>
      </v-col>
      <!-- ผู้อนุมัติ -->
      <v-col cols="12" class="mt-2">
        <v-card elevation="0" width="100%" height="100%" min-height="280px">
          <v-toolbar elevation="0" color="#E6F5F3">
            <v-row>
              <v-col align="start" class="mt-3 text-title">ผู้อนุมัติ</v-col>
            </v-row>
          </v-toolbar>
          <v-card-text>
            <v-form ref="form" v-model="valid" lazy-validation>
              <v-row justify="start" dense class="ma-2">
                test
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
      <!-- ปุ่มยืนยันยกเลิก -->
      <v-col cols="12" class="mt-5">
        <v-row justify="end" dense>
          <v-btn text color="#27AB9C" @click="backToDetailUser()" class="mr-2">ย้อนกลับ</v-btn>
          <v-btn color="primary" @click="saveSettingUser()">บันทึก</v-btn>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
// import Address2021 from '@/Thailand_Address/address2021'
export default {
  data () {
    return {
      backpage: '',
      settingAction: '',
      group: '',
      department: '',
      approver: '',
      typeApprove: '',
      selectDepartment: [
        { name: 'แผนก1', value: 'แผนก1' },
        { name: 'แผนก2', value: 'แผนก2' },
        { name: 'แผนก3', value: 'แผนก3' }
      ],
      selectGroup: [
        { name: 'ฝ่าย1', value: 'ฝ่าย1' },
        { name: 'ฝ่าย2', value: 'ฝ่าย2' },
        { name: 'ฝ่าย3', value: 'ฝ่าย3' }
      ],
      selectApprover: [
        { name: 'ผู้อนุมัติ1', value: 'ผู้อนุมัติ1' },
        { name: 'ผู้อนุมัติ2', value: 'ผู้อนุมัติ2' }
      ],
      selectTypeApprove: [
        { name: 'ไม่มีผู้อนุมัติ', value: 'ไม่มีผู้อนุมัติ' },
        { name: 'ผู้อนุมัติแบบหนึ่งคน', value: 'ผู้อนุมัติแบบหนึ่งคน' },
        { name: 'ผู้อนุมัติแบบหลายคน', value: 'ผู้อนุมัติแบบหลายคน' },
        { name: 'ผู้อนุมัติแบบทั้งหมด', value: 'ผู้อนุมัติแบบทั้งหมด' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  created () {
    if (this.$route.query.Status === 'Create') {
      this.settingAction = 'ตั้งค่าผู้ซื้อ'
    } else {
      this.settingAction = 'แก้ไขผู้ซื้อ'
      this.getEditUser()
    }
    this.$EventBus.$emit('changeTitle', 'ผู้ใช้งาน')
    this.$EventBus.$emit('changeNavAdminManage')
  },
  methods: {
    saveSettingUser () {
      // console.log('Save Setting User')
    },
    backToDetailUser () {
      this.$router.push({ path: '/detailUserCompany' }).catch(() => {})
    }
  }
}
</script>

<style scoped>
.text-title {
  font-size: 16px;
  color: #27AB9C;
  font-weight: bold;
}
</style>

<style>
.custom-autocomplete {
  font-size: 10px;
}
</style>
