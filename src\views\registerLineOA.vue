<template>
  <v-container style="margin: auto; text-align: center;">
    <!-- website -->
    <v-container v-if="!MobileSize && !IpadSize">
      <v-row justify="center" class="my-0">
        <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text>
            <v-container>
              <v-form ref="Registerform" :lazy="lazy">
                <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                  <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
                </v-row>
                <v-row dense justify="center" align-content="center" class="mt-4 mb-6">
                  <span class="textRegister">ลงทะเบียนเชื่อมต่อ line oa แจ้งเตือนรายการสั่งซื้อระบบ NEX GEN</span>
                </v-row>
                <v-row no-gutters dense class="mx-8">
                  <!-- uuid -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">uuid</h3>
                    <v-text-field color="#269AFD" v-model="uuid" disabled placeholder="ระบุ uuid" outlined dense required @keydown="noSpace" :rules="Rules.uuid" @keypress="checkCopyPaste($event)"></v-text-field>
                  </v-col>
                  <!-- Tax ID -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">Tax ID</h3>
                    <v-text-field color="#269AFD" v-model="taxID" placeholder="ระบุ Tax ID" outlined dense required @keydown="noSpace" :rules="Rules.taxid" @keypress="checkCopyPaste($event)" maxlength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- ชื่อร้านค้า -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">ชื่อร้านค้า</h3>
                    <v-text-field color="#269AFD" v-model="Shop" placeholder="ระบุชื่อร้านค้า" outlined dense required :rules="Rules.shop"></v-text-field>
                  </v-col>
                  <!-- ชื่อสาขา -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">ชื่อสาขา</h3>
                    <v-text-field color="#269AFD" v-model="Branch" placeholder="ระบุชื่อสาขา" outlined dense required :rules="Rules.branch"></v-text-field>
                  </v-col>
                  <!-- อีเมล -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">อีเมล</h3>
                    <v-text-field color="#269AFD" v-model="email" placeholder="ระบุอีเมล" outlined dense required @keydown="noSpace" :rules="Rules.email" oninput="this.value = this.value.replace(/[\u0E00-\u0E7F]/g, '')"></v-text-field>
                  </v-col>
                  <!-- เบอร์โทรศัพท์ -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">เบอร์โทรศัพท์</h3>
                    <v-text-field color="#269AFD" v-model="phone" placeholder="ระบุเบอร์โทรศัพท์" outlined dense required @keydown="noSpace" :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- ปุ่มสมัครสมาชิก -->
                  <v-col cols="12" md="12" sm="12" class="mt-3">
                    <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Register()" >ลงทะเบียน</v-btn>
                  </v-col>
                </v-row>
              </v-form>
            </v-container>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
    <!-- Ipad -->
    <v-container v-if="!MobileSize && IpadSize" class="mt-8">
      <v-row justify="center" class="my-14">
        <v-card width="467px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text>
            <v-container>
              <v-form ref="Registerform" :lazy="lazy">
                <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                  <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
                </v-row>
                <!-- <v-row dense justify="center" align-content="center" class="mt-4 mb-6">
                  <span class="textRegister">สมัครสมาชิก One Platform</span>
                </v-row> -->
                <v-row no-gutters dense class="mx-8">
                  <!-- uuid -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">uuid</h3>
                    <v-text-field color="#269AFD" v-model="uuid" disabled placeholder="ระบุ uuid" outlined dense required @keydown="noSpace" :rules="Rules.uuid" @keypress="checkCopyPaste($event)"></v-text-field>
                  </v-col>
                  <!-- Tax ID -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">Tax ID</h3>
                    <v-text-field color="#269AFD" @keydown="noSpace" v-model="taxID" placeholder="ระบุ Tax ID" outlined dense required :rules="Rules.taxid" @keypress="checkCopyPaste($event)" maxlength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- ชื่อร้านค้า -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">ชื่อร้านค้า</h3>
                    <v-text-field color="#269AFD" v-model="Shop" placeholder="ระบุชื่อร้านค้า" outlined dense required :rules="Rules.shop"></v-text-field>
                  </v-col>
                  <!-- ชื่อสาขา -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">ชื่อสาขา</h3>
                    <v-text-field color="#269AFD" v-model="Branch" placeholder="ระบุชื่อสาขา" outlined dense required :rules="Rules.branch"></v-text-field>
                  </v-col>
                  <!-- อีเมล -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">อีเมล</h3>
                    <v-text-field color="#269AFD" @keydown="noSpace" v-model="email" placeholder="ระบุอีเมล" outlined dense required  :rules="Rules.email" oninput="this.value = this.value.replace(/[\u0E00-\u0E7F]/g, '')"></v-text-field>
                  </v-col>
                  <!-- เบอร์โทรศัพท์ -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">เบอร์โทรศัพท์</h3>
                    <v-text-field color="#269AFD" @keydown="noSpace" v-model="phone" placeholder="ระบุเบอร์โทรศัพท์" outlined dense required :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- ปุ่มลงทะเบียน -->
                  <v-col cols="12" md="12" sm="12" class="mt-3">
                    <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Register()"  >ลงทะเบียน</v-btn>
                  </v-col>
                </v-row>
              </v-form>
            </v-container>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
    <!-- App -->
    <v-container v-if="MobileSize">
      <v-row justify="center" class="my-6">
        <v-card width="480px" height="100%" style="background: #FFFFFF; box-shadow: 0px 8px 24px rgba(0, 0, 0, 0.1); border-radius: 16px;">
          <v-card-text>
            <v-container>
              <v-form ref="Registerform" :lazy="lazy">
                <v-row dense justify="center" align-content="center" class="mt-8 mb-8">
                  <v-img :src="require('@/assets/ngc_logo_1.png')" max-height="50%" max-width="60%" contain/>
                </v-row>
                <!-- <v-row dense justify="center" align-content="center" class="mt-4 mb-6">
                  <span class="textRegister">สมัครสมาชิก One Platform</span>
                </v-row> -->
                <v-row no-gutters dense class="mx-8">
                  <!-- uuid -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">uuid</h3>
                    <v-text-field color="#269AFD" disabled v-model="uuid" placeholder="ระบุ uuid" outlined dense required @keydown="noSpace" :rules="Rules.uuid" @keypress="checkCopyPaste($event)"></v-text-field>
                  </v-col>
                  <!-- Tax ID -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">Tax ID</h3>
                    <v-text-field color="#269AFD" @keydown="noSpace" v-model="taxID" placeholder="ระบุ Tax ID" outlined dense required :rules="Rules.taxid" @keypress="checkCopyPaste($event)" maxlength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- ชื่อร้านค้า -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">ชื่อร้านค้า</h3>
                    <v-text-field color="#269AFD" v-model="Shop" placeholder="ระบุชื่อร้านค้า" outlined dense required :rules="Rules.shop"></v-text-field>
                  </v-col>
                  <!-- ชื่อสาขา -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">ชื่อสาขา</h3>
                    <v-text-field color="#269AFD" v-model="Branch" placeholder="ระบุชื่อสาขา" outlined dense required :rules="Rules.branch"></v-text-field>
                  </v-col>
                  <!-- อีเมล -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">อีเมล</h3>
                    <v-text-field color="#269AFD" @keydown="noSpace" v-model="email" placeholder="ระบุอีเมล" outlined dense required  :rules="Rules.email" oninput="this.value = this.value.replace(/[\u0E00-\u0E7F]/g, '')"></v-text-field>
                  </v-col>
                  <!-- เบอร์โทรศัพท์ -->
                  <v-col cols="12" md="12" sm="12">
                    <h3 class="mb-0" style="text-align: left; font-size: 15px;">เบอร์โทรศัพท์</h3>
                    <v-text-field color="#269AFD" @keydown="noSpace" v-model="phone" placeholder="ระบุเบอร์โทรศัพท์" outlined dense required :rules="Rules.tel"  maxlength="10" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                  </v-col>
                  <!-- ปุ่มลงทะเบียน -->
                  <v-col cols="12" md="12" sm="12" class="mt-3">
                    <v-btn block rounded class="textbtnRegister" color="#269AFD" style="color:white;" elevation="6" @click="Register()" >ลงทะเบียน</v-btn>
                  </v-col>
                </v-row>
              </v-form>
            </v-container>
          </v-card-text>
        </v-card>
      </v-row>
    </v-container>
  </v-container>
</template>

<script>
// import liff from '@line/liff'
export default {
  data () {
    return {
      ProfileLine: {},
      uuid: '',
      Branch: '',
      taxID: '',
      lazy: false,
      email: '',
      phone: '',
      Shop: '',
      Rules: {
        uuid: [
          v => !!v || 'กรุณากรอก uuid'
        ],
        shop: [
          v => !!v || 'กรุณากรอกชื่อร้านค้า'
        ],
        branch: [
          v => !!v || 'กรุณากรอกชื่อสาขา'
        ],
        taxid: [
          v => !!v || 'กรุณากรอก Tax ID'
        ],
        email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ]
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  created () {
    // this.initLiff()
    // console.log('pathname', window.location.pathname)
  },
  mounted () {
    this.initLiff()
  },
  methods: {
    initLiff () {
      // console.log(1)
      this.$liff.init({ liffId: '2006460110-Vyjv3Zx4' })
        .then(() => {
          // console.log('LIFF initialized')
          if (this.$liff.isLoggedIn()) {
            this.$store.commit('openLoader')
            this.getUserProfile()
          } else {
            this.$liff.login()
          }
        })
        .catch(error => {
          console.error('LIFF Initialization failed', error)
        })
    },
    async getUserProfile () {
      try {
        const profile = await this.$liff.getProfile()
        this.ProfileLine = profile
        this.uuid = profile.userId
        this.$store.commit('closeLoader')
      } catch (error) {
        this.$store.commit('closeLoader')
        console.error('Failed to get user profile', error)
      }
    },
    loginWithLiff () {
      if (!this.$liff.isLoggedIn()) {
        this.$liff.login()
      }
    },
    noSpace (e) {
      if (e.which === 32) {
        e.preventDefault()
      }
    },
    async Register () {
      if (this.$refs.Registerform.validate(true)) {
        this.$store.commit('openLoader')
        var uuid = this.uuid
        var data = {
          taxID: this.taxID,
          shopName: this.Shop,
          branch: this.Branch,
          email: this.email,
          phone: this.phone
        }
        await this.$store.dispatch('actionsRegisterLineOA', { access: data, uuid })
        var res = await this.$store.state.ModuleRegister.stateRegisterLineOA
        if (res.message === 'Success') {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'success',
            html: '<h3>การลงทะเบียนของคุณเสร็จสมบูรณ์</h3>'
          })
          this.taxID = ''
          this.Shop = ''
          this.Branch = ''
          this.email = ''
          this.phone = ''
          this.$refs.Registerform.resetValidation()
          setTimeout(() => (this.$liff.closeWindow()), 2500)
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${res.message === 'ไม่พบผู้ขายที่มีเลขประจำตัวผู้เสียภาษีนี้' ? 'เลขประจำตัวผู้เสียภาษีหรือ<br>ชื่อร้านค้า/สาขาไม่ตรงกัน' : res.message}</h3>`
          })
        }
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: '<h3>ข้อมูลไม่ถูกต้อง กรุณาตวจสอบข้อมูลใหม่อีกครั้ง</h3>'
        })
      }
    }
  }
}
</script>

<style scoped>
.borderButtomText {
  margin-bottom: -30px
}
</style>
