import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async GraczListPromotion (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}onlineOrder/promotion/listPromotion`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GraczPromotionDetail (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}onlineOrder/promotion/promotionDetail`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GraczCreatePromotion (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}onlineOrder/promotion/createPromotion`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GraczEditPromotion (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}onlineOrder/promotion/editPromotion`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GraczDeletePromotion (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}onlineOrder/promotion/deletePromotion`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
