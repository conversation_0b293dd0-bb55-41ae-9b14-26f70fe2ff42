<template>
  <v-container :style="MobileSize ? 'background-color: white;' : ''">
    <!-- Dialog to Show Image Big -->
    <v-dialog v-model="dialogShowImage" width="100%" style="height: 90vh;" persistent>
      <v-card width="100%" height="90vh" elevation="0" style="background: rgba(0, 0, 0, 0.50);">
        <v-card-text>
          <v-row dense>
            <v-col cols="12">
              <v-row dense class="pt-4">
                <v-icon color="#FFFFFF" class="mr-2" @click="bactToModalImage()">mdi-arrow-left</v-icon>
                <v-img class="mr-2" src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" max-width="30" max-height="30" contain></v-img>
                <span class="textBigImage pt-1">{{ imagetoBig.name }}</span>
              </v-row>
            </v-col>
            <v-col cols="12" align="center">
              <v-img id="imgBig" :src="imagetoBig.path" :width="setWidth" :height="setHeight"></v-img>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row justify="center" dense>
            <div style="width: 128px; height: 40px; border-radius: 25px; background: #333; text-align: center;">
              <v-btn icon width="40" height="40" class="mr-2" @click="ZoomOut()"><v-icon color="#FFFFFF" size="24">mdi-minus</v-icon></v-btn>
              <v-icon color="#A1A1A1">mdi-magnify-minus-outline</v-icon>
              <v-btn icon width="40" height="40" class="ml-2" @click="ZoomIn()"><v-icon color="#FFFFFF" size="24">mdi-plus</v-icon></v-btn>
            </div>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="modalConfirmPartnerCreate"
      width="424"
      persistent
    >
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-col class="d-flex justify-center">
          <v-img
            height="300px"
            width="200px"
            :src="require('@/assets/Coorperation/modalConfirmEditDialog.png')"
          >
            <v-app-bar
              flat
              color="rgba(0, 0, 0, 0)"
            >
              <v-toolbar-title></v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn
                color="#CCCCCC"
                icon
                @click="closeConfirmCreatePartner()"
              >
                <v-icon>mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
        </v-col>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะแก้ไขข้อมูลร้านค้าใช่ไหม</span><br/>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">กับทาง Marketplace ใช่ไหม</span> -->
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeConfirmCreatePartner()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="createPartner('no')">ยืนยัน</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogAwaitChooseAddpackage" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="closeChooseAddPackage()"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/Coorperation/modalchoosepackage.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เพิ่มข้อมูลสินค้าบริการ</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการลงทะเบียน Partner แล้ว</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">สามารถเพิ่มข้อมูลสินค้าบริการได้</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeChooseAddPackage()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="createPartner()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccessCreateShop" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeCreateShop()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ทำการแก้ไขข้อมูล Partner</b></p>
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เรียบร้อย</b></p>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeCreateShop()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Dialog Upload Banner -->
    <v-dialog v-model="dialogOpenDialogUploadBanner" :width="MobileSize ? '100%' : IpadSize ? '100%' : '918'" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 918px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>อัปโหลดหน้าปกร้านค้า</b></span>
              </v-col>
              <v-btn fab small @click="cancelUploadBanner()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '918px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
              <v-card-text style="text-align: center;" class="mt-6" v-if="partnerPayload.media.length === 0">
                <span class="textUploadnameImage">(เพิ่มได้สูงสุด 6 รูปภาพ)</span>
                <v-row justify="center" class="pt-6" style="cursor: pointer;">
                  <v-col cols="12" align="center">
                    <v-card @click="onPickFile()" @drop.prevent="DropImage($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" width="700" height="363" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                      <v-card-text>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-10">
                            <v-file-input
                              v-model="DataImage"
                              :items="DataImage"
                              accept="image/jpeg, image/jpg, image/png"
                              @change="UploadImage()"
                              id="file_input"
                              multiple
                              :clearable="false"
                              style="display:none"
                            >
                            </v-file-input>
                            <v-img
                              src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                              width="143"
                              height="143"
                              contain
                            ></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center" align="center">
                            <v-col cols="12" md="12" style="text-align: center;">
                              <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                              <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-text class="px-4" v-else>
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <v-card-text style="text-align: center;" v-if="partnerPayload.media.length < 6">
                    <v-row justify="center" class="pt-6" style="cursor: pointer;">
                      <v-col cols="12" align="center">
                        <v-card @click="onPickFile()" @drop.prevent="DropImage($event)" @dragover.prevent="dragover = true" @dragenter.prevent="dragover = true" @dragleave.prevent="dragover = false" :width="MobileSize ? '100%' : '882'" :height="MobileSize ? '100%' : '83'" elevation="0" style="border: 2px dashed rgba(0, 0, 0, 0.25); border-radius: 10px; box-sizing: border-box;">
                          <v-card-text>
                            <v-row>
                              <v-file-input
                                v-model="DataImage"
                                :items="DataImage"
                                accept="image/jpeg, image/jpg, image/png"
                                @change="UploadImage()"
                                id="file_input"
                                multiple
                                :clearable="false"
                                style="display:none"
                              >
                              </v-file-input>
                              <v-col cols="12" md="2" sm="2" align="center" class="mt-2">
                                <v-img
                                  src="@/assets/ImageINET-Marketplace/ICONShop/cloud-upload.png"
                                  width="32"
                                  height="32"
                                  contain
                                ></v-img>
                              </v-col>
                              <v-col cols="12" md="8" sm="8" :align="MobileSize ? 'center' : 'start'">
                                <span class="textUpload">ลากรูปภาพของคุณมาที่นี่ เพื่ออัปโหลด</span><br />
                                <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, .PNG, .JPG ขนาดไฟล์: สูงสุด 1 MB)</span>
                              </v-col>
                              <v-col cols="12" md="2" sm="2" :align="MobileSize ? 'center' : 'start'" :class="MobileSize ? '' : 'mt-3'">
                                <span class="textUploadnameImage">หรือ </span><span class="textUploadSelect">เลือกรูป</span>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-card-text>
                  <v-card-text>
                    <v-row dense>
                      <v-icon color="#636363" class="px-2">mdi-file-plus-outline</v-icon>
                      <span class="textUploadNotComplete pt-1">เพิ่มไฟล์แล้ว</span>
                      <v-spacer></v-spacer>
                      <span class="textUploadlimitComplete pt-2">(เพิ่มได้สูงสุด 6 รูปภาพ)</span>
                    </v-row>
                    <v-row dense>
                      <v-col cols="12" v-for="(item, index) in partnerPayload.media" :key="index">
                        <v-row dense class="pt-4" style="height: 60px;">
                          <v-col cols="1" align="center">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                          </v-col>
                          <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === false">
                            <span class="textUpload" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                            <span style="line-height: 14px; font-weight: 400;" :style="MobileSize ? 'font-size: 10px;' : 'font-size: 12px;'">{{ Math.round(item.size / 1000) }} KB</span>
                          </v-col>
                          <v-col cols="4" md="7" sm="7" align="start" v-if="item.statusFail === true">
                            <span class="textUpload" style="color: #F5222D;" :style="MobileSize ? 'font-size: 12px !important;' : ''">{{ MobileSize ? item.name.substring(0, 10) : item.name.substring(0, 50) }}<span v-if="item.name.length > 50 && !MobileSize">...</span><span v-if="item.name.length > 10 && MobileSize">...</span></span><br />
                            <span style="font-size: 10px; line-height: 14px; font-weight: 400; color: #F5222D;">ไฟล์มีขนาดใหญ่เกินไป</span>
                          </v-col>
                          <v-col cols="7" md="4" sm="4" :class="MobileSize ? 'pt-4' : ''">
                            <v-row justify="end" dense>
                              <v-btn :class="MobileSize ? 'px-0' : ''" :height="MobileSize ? '20' : '40'" text color="#27AB9C" v-if="item.statusFail === false" @click="ShowBigImage('Banner', item)"><v-icon class="pr-2" size="20">mdi-eye-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">แสดงตัวอย่าง</span></v-btn>
                              <v-btn :class="MobileSize ? 'px-0' : ''" width="37" :height="MobileSize ? '20' : '40'" text color="#636363"  @click="RemoveImage(index, item)"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;" :style="MobileSize ? 'font-size: 12px;' : ''">ลบ</span></v-btn>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <v-card-actions style="height: 88px; background-color: #F5FCFB;">
          <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="cancelUploadBanner()">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn color="#27AB9C" rounded width="125" :disabled="disableUploadButton" height="40" class="white--text" @click="uploadToShow()">อัปโหลด</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-row>
        <v-col class="ma-3 mt-5 mb-0 pb-0" cols="12">
            <span style="font-size: 24px; font-weight: bold;"><v-icon v-if="MobileSize" color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>แก้ไขข้อมูล Partner</span>
        </v-col>
        <v-col cols="12" v-if="stepper === 1">
            <v-card class="elevation-0 pl-5 py-3" style="background-color: #f7fffc; color: #27AB9C">
                <v-row>
                    <v-col cols="2" sm="1" >
                        <v-img
                        src="@/assets/Coorperation/shoplogo.png"
                        width="40"
                        contain
                        ></v-img>
                    </v-col>
                    <v-col class="d-flex align-center">
                        <span style="font-size: 18px; font-weight: bold;">ข้อมูล Partner</span>
                    </v-col>
                </v-row>
            </v-card>
        </v-col>
    </v-row>
    <!-- <v-row dense class="mt-3" v-if="MobileSize">
      <v-col cols="12" md="12" class="mt-0 pt-0">
        <v-img src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.png" lazy-src="@/assets/ImageINET-Marketplace/ICONShop/BannerCreateShop.jpg" max-height="394" max-width="100%" contain></v-img>
      </v-col>
    </v-row> -->
    <v-row justify="center" dense no-gutters v-if="!MobileSize && !IpadSize">
      <v-img src="@/assets/Coorperation/createPartnerStep1.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-if="stepper === 1"></v-img>
      <v-img src="@/assets/Coorperation/createPartnerStep2.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
    </v-row>
    <v-row justify="center" dense no-gutters v-else-if="!MobileSize && IpadSize">
       <v-img src="@/assets/Coorperation/createPartnerStep1.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-if="stepper === 1"></v-img>
      <v-img src="@/assets/Coorperation/createPartnerStep2.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
    </v-row>
    <v-row justify="center" dense no-gutters v-else>
       <v-img src="@/assets/Coorperation/createPartnerStep1.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-if="stepper === 1"></v-img>
      <v-img src="@/assets/Coorperation/createPartnerStep2.png" contain max-height="139" max-width="725" class="mt-12 mb-10" v-else-if="stepper === 2"></v-img>
    </v-row>
    <!-- <v-row>
      <v-col class="d-flex align-center">
        <span style="font-size: 16px;">รูปภาพร้านค้า Partner </span>
        <span style="font-size: 12px; color: #A1A1A1;" class="ml-5">ขนาดไฟล์: สูงสุด 1 MB</span>
      </v-col>
    </v-row> -->
    <v-card elevation="0" width="100%" height="100%" style="border-radius: 8px;">
      <!--step 1  -->
      <v-form ref="formOne" :lazy-validation="lazyOne" v-if="stepper === 1" class="pt-2">
        <v-card-text>
          <span class="textStepOne pr-4">รูปภาพร้านค้า</span><span class="subtextStepOne">ขนาดไฟล์: สูงสุด 1 MB</span>
          <!-- เพิ่มรูปภาพ -->
          <v-row dense class="my-2">
            <v-col cols="12" md="3" sm="12">
              <v-card elevation="0" outlined width="100%" :height="IpadSize || MobileSize ? '100%' : '321'" class="cardImageStyle">
                <v-card-text style="text-align: center;" class="px-2">
                  <v-row justify="center">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/NoImageShop.png" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-if="Detail.shop_logo_image.length === 0 || showError === true"></v-img>
                    <v-img :src="Detail.shop_logo_image[0].path" width="134" height="134" max-width="134" max-height="134" contain class="mb-6" v-else></v-img>
                  </v-row>
                  <span class="textStepOne">โปรไฟล์ร้านค้า</span><br/>
                  <span class="subtextStepOne" v-if="Detail.shop_logo_image.length === 0">( ขนาดรูปภาพ 244 x 244 pxและ ไฟล์นามสกุล .JPEG, .PNG, .JPG )</span><br/>
                  <v-btn width="125" height="40" text rounded color="#1B5DD6" class="mt-4" @click="uploadImageShop()" v-if="Detail.shop_logo_image.length === 0"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                  <v-file-input
                    v-model="DataImageShop"
                    :items="DataImageShop"
                    accept="image/*"
                    @change="UploadImageShop()"
                    @click="event => event.target.value = null"
                    id="imageShop"
                    :clearable="false"
                    style="display:none"
                  />
                  <!-- wait upload -->
                  <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-4">
                    <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== null">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}<span v-if="Detail.shop_logo_image[0].name.length > 10">...</span></span>
                    <!-- <v-spacer></v-spacer> -->
                    <!-- <span class="textUploadsizeImage pt-1">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span> -->
                  </v-row>
                  <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                    <v-progress-linear :value="percentUpload" height="7" rounded :active="show"></v-progress-linear>
                  </v-row>
                  <v-row v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-1">
                    <span class="textUploadsizeImage">กำลังอัปโหลด...</span>
                    <v-spacer></v-spacer>
                    <span class="textUploadpercentImage">{{ percentUpload }} %</span>
                  </v-row>
                  <v-row justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === true" class="pt-2">
                    <v-btn text color="#27AB9C" style="text-decoration-line: underline; font-size: 14px;" @click="cancelImageShop()">ยกเลิก</v-btn>
                  </v-row>
                  <!-- upload success -->
                  <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === false" class="pt-0">
                    <v-col cols="12">
                      <span class="textUploadnameImage" v-if="Detail.shop_logo_image[0].name !== ''">{{ Detail.shop_logo_image[0].name.substring(0, 20) }}</span><span v-if="Detail.shop_logo_image[0].name.length > 20">...</span><br/>
                      <!-- <span class="textUploadsizeImage">{{ Math.round(Detail.shop_logo_image[0].size / 1000) }} KB</span> -->
                    </v-col>
                    <v-col cols="12">
                      <v-btn width="50" height="40" text color="#27AB9C" @click="uploadImageShop()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                    </v-col>
                  </v-row>
                  <!-- upload fail -->
                  <v-row dense justify="center" v-if="Detail.shop_logo_image.length !== 0 && show === false && showError === true" class="pt-4">
                    <v-col cols="12">
                      <span class="textUploadnameImageFail" v-if="Detail.shop_logo_image[0].name !== ''">{{ Detail.shop_logo_image[0].name.substring(0, 10) }}</span><span v-if="Detail.shop_logo_image[0].name.length > 10">...</span><br/>
                      <span class="textFailUpload">{{ showErrorText }}</span>
                    </v-col>
                    <v-col cols="12">
                      <v-btn width="50" height="40" text color="#636363" @click="cancelImageShop()"><v-icon class="pr-2" size="20">mdi-delete-outline</v-icon> <span style="text-decoration-line: underline;">ลบ</span></v-btn>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="12" md="9" sm="12">
              <v-card elevation="0" outlined width="100%" :height="IpadSize || MobileSize ? '100%' : '321'" class="cardImageStyle">
                <v-card-text style="text-align: center;" class="pt-0" v-if="DataToShowImageBanner.length === 0">
                  <span class="textStepOne">หน้าปกร้านค้า</span><br/>
                  <v-row justify="center" class="py-12">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png" width="167" height="100" max-width="167" max-height="100" contain></v-img>
                  </v-row>
                  <span class="subtextStepOne">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG, PNG, .JPG เพิ่มได้สูงสุด 6 รูปภาพ)</span><br/>
                  <v-btn width="125" height="40" text rounded color="#1B5DD6" class="mt-4" @click="openModalUploadBanner()"><v-icon class="pr-2">mdi-cloud-upload-outline</v-icon> <span style="text-decoration: underline;">อัปโหลดรูป</span></v-btn>
                </v-card-text>
                <v-card-text style="text-align: center;" class="pt-0" v-else>
                  <v-row dense>
                    <span class="textStepOne">หน้าปกร้านค้า</span>
                    <v-spacer></v-spacer>
                    <v-btn width="50" height="20" text color="#27AB9C" @click="openModalUploadBanner()"><v-icon class="pr-2" size="20">mdi-pencil-outline</v-icon> <span style="text-decoration-line: underline;">แก้ไข</span></v-btn>
                  </v-row>
                  <v-row justify="start">
                    <v-col cols="12" md="6" sm="6" v-for="(item, index) in DataToShowImageBanner" :key="index">
                      <v-card elevation="0" outlined width="100%" height="50" style="border-radius: 8px;">
                        <v-card-text class="py-1">
                          <v-row dense>
                            <v-col cols="2" align="center" class="pt-2">
                              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/gallery.png" width="30" height="30" contain></v-img>
                            </v-col>
                            <v-col cols="8" align="start">
                              <span class="textUpload">{{ item.name.substring(0, 15) + '...' }}</span><span v-if="item.name.length > 15">...</span><br />
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{ Math.round(item.size / 1000) }} KB</span>
                            </v-col>
                            <v-col cols="2" class="pt-3">
                              <v-btn width="20" height="20" icon color="#9A9A9A"  @click="RemoveImageBanner(index, item)"><v-icon size="20">mdi-delete-outline</v-icon></v-btn>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                    <v-col cols="12" md="6" sm="6" v-if="DataToShowImageBanner.length < 6">
                      <v-card @click="openModalUploadBanner()" elevation="0" outlined width="100%" height="50" style="border-radius: 8px; border-color: #27AB9C;">
                        <v-card-text class="py-1">
                          <v-row dense>
                            <v-col cols="2" align="center" class="pt-3">
                              <v-icon size="24" color="#27AB9C">mdi-plus</v-icon>
                            </v-col>
                            <v-col cols="10" align="start" class="pt-3">
                              <span class="textUploadImage">อัปโหลดรูปเพิ่ม</span>
                            </v-col>
                          </v-row>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                  <v-row justify="end" dense class="pt-4">
                    <span style="subtextbanner">(ขนาดรูปภาพ 1376 x 380 px และไฟล์นามสกุล .JPEG,PNG เพิ่มได้สูงสุด 6 รูปภาพ)</span>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row no-gutters>
            <!-- ประเภทธุรกิจ -->
            <!-- <v-col cols="12" md="12" class="pt-4 pb-4">
              <span class="f-left textStepOne">ประเภทธุรกิจ</span>
            </v-col>
            <v-col cols="12" md="12" class="px-0 pt-2 pb-3">
              <v-radio-group
                v-model="bussinessType"
                :rules="Rules.bussinessType"
                row
              >
                <v-radio
                  v-if="haveCitizen"
                  label="นิติบุคคลอื่นๆ"
                  style="color: #333333;"
                  value="citizen"
                ></v-radio>
                <v-radio
                  v-if="haveBusiness"
                  label="นิติบุคคล"
                  style="color: #333333;"
                  value="business"
                ></v-radio>
              </v-radio-group>
            </v-col> -->
            <v-col  cols="12" md="12" sm="12" class="px-0 py-0">
              <v-row dense no-gutters>
                <!-- ชื่อร้านค้า -->
                <v-col cols="12" md="12" sm="12">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">ชื่อร้านค้า <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="shopName"
                        outlined
                        dense
                        :rules="Rules.shop_name"
                        :maxLength="255"
                        placeholder="ระบุชื่อร้านค้า"
                        style="border-radius: 8px;"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- ประเภทการให้บริการ -->
                <v-col cols="12" md="12" sm="12">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">ประเภทการให้บริการ <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12">
                    <v-select
                      v-model="selectedserviceType"
                      :items="serviceTypeList"
                      multiple
                      chips
                      persistent-hint
                      style="border-radius: 8px;"
                      outlined
                      dense
                      :rules="Rules.ItemServiceType"
                      placeholder="เลือกประเภทการให้บริการ"
                    >
                    </v-select>
                    </v-col>
                  </v-row>
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">ระบุชื่อย่อร้านค้า <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : ''">
                      <v-tooltip top>
                        <template v-slot:activator="{ on, attrs }">
                          <v-text-field v-model="shortName" v-bind="attrs" v-on="on" outlined dense oninput="this.value = this.value.replace(/[^a-zA-Z0-9]/g, '')" :rules="Rules.short_name" :maxLength="5" placeholder="ระบุชื่อย่อร้านค้า ระบุได้ 1-5 ตัวอักษร"></v-text-field>
                        </template>
                        <span>ตัวย่อที่จะเอาไปใส่ run number ของ Cost Sheet และ PO ของแต่ละ JV</span>
                      </v-tooltip>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- เลขประจำตัวผู้เสียภาษีอากร -->
                <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">เลขประจำตัวผู้เสียภาษีอากร <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                      <v-text-field
                        v-model="taxId"
                        outlined
                        dense
                        :maxLength="13"
                        :rules="Rules.tax_id"
                        disabled
                        style="border-radius: 8px;"
                        oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                        @keypress="CheckSpacebar($event)"
                        placeholder="ระบุเลขประจำตัวผู้เสียภาษี 13 หลัก"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- URL ร้านค้า -->
                <v-col cols="12" md="6" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">URL ร้านค้า</span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="shopURL"
                        outlined
                        dense
                        readonly
                        style="border-radius: 8px;"
                        placeholder="ระบุ url ร้านค้า"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
            <!-- เกี่ยวกับร้านค้า -->
            <v-col cols="12" md="12" sm="12" class="pa-0">
              <span class="textFieldStepOne">เกี่ยวกับ Partner</span>
            </v-col>
            <v-col cols="12" md="12" sm="12" class="pt-2">
              <v-textarea
                v-model="descriptionShop"
                placeholder="อธิบายเกี่ยวกับร้านค้าของคุณ"
                background-color="#FFFFFF"
                outlined
                height="136"
                style="border-radius: 8px;"
                no-resize
                :rules="Rules.spaceRule"
              ></v-textarea>
            </v-col>
            <!-- เบอร์โทรศัพท์, ข้อมูลฝ่ายขาย, ข้อมูลการ เปิด - ปิด ร้านค้า -->
            <v-col cols="12" md="12" sm="12" class="px-0 py-0">
              <v-row dense no-gutters>
                <!-- เบอร์โทรศัพท์ -->
                <v-col cols="12" md="4" sm="12">
                  <v-row dense>
                    <v-col cols="12">
                        <span class="textFieldStepOne">เบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ <span style="color: red;">*</span></span>
                    </v-col>
                    <v-col cols="12" class="pr-4">
                        <v-text-field
                        v-model="mobile"
                        outlined
                        dense
                        :maxLength="10"
                        style="border-radius: 8px;"
                        :rules="Rules.telShop"
                        @keypress="CheckSpacebarMobile($event)"
                        placeholder="ระบุเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ"
                        oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                        ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- Facebook -->
                <v-col cols="12" md="4" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">Url Facebook</span>
                    </v-col>
                    <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                      <v-text-field
                        v-model="facebook"
                        outlined
                        dense
                        style="border-radius: 8px;"
                        placeholder="ระบุ Facebook"
                        @blur="checkFackbook"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
                <!-- Line -->
                <v-col cols="12" md="4" sm="6">
                  <v-row dense>
                    <v-col cols="12">
                      <span class="textFieldStepOne">ID Line</span>
                    </v-col>
                    <v-col cols="12">
                      <v-text-field
                        v-model="line"
                        outlined
                        style="border-radius: 8px;"
                        dense
                        placeholder="ระบุ ID LINE"
                        oninput="this.value = event.target.value.replace(/[^a-z0-9_.-]/g, '')"
                        @blur="checkLine"
                      ></v-text-field>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
          <v-row dense class="mt-4">
            <v-btn rounded color="#27AB9C" width="125" height="40" outlined @click="backtoHome()">ยกเลิก</v-btn>
            <v-spacer></v-spacer>
            <!-- <v-btn text color="#27AB9C" width="81" height="40" @click="clearStepOne()">ล้างค่า</v-btn> -->
            <v-btn rounded color="#27AB9C" width="125" height="40" class="white--text" :class="MobileSize ? '' : ''" @click="nextStep(2)">ถัดไป</v-btn>
          </v-row>
        </v-card-text>
      </v-form>
      <v-form ref="formTwo" :lazy-validation="lazyTwo" v-else-if="stepper === 2">
        <v-col cols="12">
            <v-card class="elevation-0 pl-5 py-3" style="background-color: #f7fffc; color: #27AB9C">
                <v-row>
                    <v-col cols="2" sm="1">
                        <v-img
                        src="@/assets/Coorperation/paymentData.png"
                        width="40"
                        contain
                        ></v-img>
                    </v-col>
                    <v-col class="d-flex align-center">
                      <span style="font-size: 18px; font-weight: bold;">ข้อมูลบัญชี</span>
                    </v-col>
                </v-row>
            </v-card>
        </v-col>
        <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="6" class="pa-0 pr-3">
            <v-row>
              <v-col cols="12">
                <span class="textFieldStepOne">ประเภทการชำระเงิน<span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                <v-select
                  placeholder="เลือกประเภทการชำระเงิน"
                  :items="paymentTypeList"
                  v-model="selectedPaymentType"
                  chips
                  dense
                  outlined
                  style="border-radius: 8px;"
                  :rules="Rules.ItemPaymentType"
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="6" sm="6" class="pa-0 pr-3">
            <v-row>
              <v-col cols="12">
                <span class="textFieldStepOne">ช่องทางการชำระเงิน<span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" md="12" sm="12" :class="IpadSize ? 'pt-6' : ''">
                <v-select v-model="SelectPaymentType" :menu-props="{ offsetY: true }" :items="itemPayment" item-text="text" item-value="value" :rules="Rules.ItemPayment" multiple chips outlined dense placeholder="เลือกช่องทางการชำระเงิน" style="border-radius: 8px;">
                  <template #selection="{ item }">
                    <v-chip
                      close
                      @click:close="removePayment(item)" :color="item.value === 'qrcode' ? 'rgba(27, 93, 214, 0.10)' : item.value === 'installment' ? '#F8FFF5' : 'rgba(255, 113, 11, 0.10)'" :style="item.value === 'qrcode' ? 'color: #1B5DD6;' : item.value === 'installment' ? 'color: #8BC34A;' : 'color: #FF710B;'"
                    >
                      {{ item.text }}
                    </v-chip>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row dense no-gutters>
          <v-col cols="12" md="6" sm="6">
            <v-row dense>
              <v-col cols="12">
                <span class="textFieldStepOne">ชื่อบัญชีธนาคาร <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" class="pr-4">
                <v-text-field
                v-model="accountName"
                outlined
                dense
                :rules="Rules.emptyText"
                style="border-radius: 8px;"
                @keypress="CheckSpacebarOne($event)"
                oninput="this.value = this.value.replace(/[^a-zA-Zก-๏.()\s]/g, '')"
                placeholder="ระบุชื่อบัญชีธนาคาร"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="6" sm="6">
            <v-row dense>
                <v-col cols="12">
                  <span class="textFieldStepOne">ชื่อธนาคาร <span style="color: red;">*</span></span>
                </v-col>
                <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                  <v-select @change="handleChange" v-model="bankCode" :menu-props="{ offsetY: true }" :items="itemsBank" item-text="name" item-value="code" :rules="Rules.ItemBank" outlined dense placeholder="เลือกธนาคาร" style="border-radius: 8px;" class="setCustomSelect"></v-select>
                </v-col>
              </v-row>
          </v-col>
          <v-col cols="12" md="6" sm="6">
            <v-row dense>
              <v-col>
                <span class="textFieldStepOne">ชื่อสาขาธนาคาร <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                <v-text-field
                  v-model="bankBranchName"
                  outlined
                  dense
                  :rules="Rules.emptyText"
                  @keypress="CheckSpacebarOne($event)"
                  style="border-radius: 8px;"
                  oninput="this.value = this.value.replace(/[^0-9a-zA-Zก-๏.()\s]/g, '')"
                  placeholder="ชื่อสาขาธนาคาร"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <!-- เลขบัญชีธนาคาร -->
          <v-col cols="12" md="6" sm="6">
            <v-row dense>
              <v-col cols="12">
                <span class="textFieldStepOne">เลขบัญชีธนาคาร <span style="color: red;">*</span></span>
              </v-col>
              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? 'pr-4' : 'pr-4'">
                <v-text-field
                v-model="accountNo"
                outlined
                dense
                :rules="Rules.bookBankNo"
                @keypress="CheckSpacebarOne($event)"
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 15)"
                placeholder="ระบุเลขบัญชีธนาคาร"
                :maxLength="checkLengthValid"
                ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row dense no-gutters>
          <v-col cols="12" md="12" sm="6">
            <v-row dense>
              <v-col cols="12">
                <span class="textFieldStepOne">รูปหน้าบัญชีธนาคาร<span style="color: red;">*</span></span>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row>
          <v-col>
            <v-card
              v-if="DataImageBookBank.length === 0"
              class="mt-3"
              elevation="0"
              :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px;'"
              height="400px%"
              @click="onPickFileBookBank()"
            >
              <v-card-text>
                <v-row dense align="center" justify="center" style="cursor: pointer;">
                    <v-file-input
                    v-model="DataImageBookBank"
                    :items="DataImageBookBank"
                    :rules="Rules.bookBank"
                    accept="image/jpeg, image/jpg, image/png"
                    @change="UploadImageBookBank()"
                    id="file_input_book_bank"
                    multiple :clearable="false"
                    style="display:none"
                    >
                    </v-file-input>
                    <v-col cols="12" md="12">
                    <v-row justify="center" align="center">
                        <v-col cols="12" md="12" align="center">
                        <v-img
                            src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                            width="280.34"
                            height="154.87"
                            contain
                        ></v-img>
                        </v-col>
                        <v-col cols="12" md="12" style="text-align: center;">
                        <span style="line-height: 24px; font-weight: 400;"
                            :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                        <span style="line-height: 24px; font-weight: 400;"
                            :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                        <span style="line-height: 16px; font-weight: 400;"
                            :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                        </v-col>
                    </v-row>
                    </v-col>
                </v-row>
                </v-card-text>
            </v-card>
            <v-card
              v-if="DataImageBookBank.length !== 0"
              class="mt-3"
              elevation="0"
              style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
              height="400px%"
            >
              <v-card-text>
                <div class="mt-4">
                  <!-- <draggable
                    v-model="FlashsTest"
                    :move="onMove"
                    @start="drag = true"
                    @end="drag = false"
                    class="pl-5 pr-5 row  fill-height align-center sortable-list"
                  > -->
                    <v-col cols="12" class="ma-3">
                      <div class="d-flex justify-center">
                      <v-card max-height="400px" class="elevation-0">
                          <v-btn icon x-small style="float: right; background-color: #ff5252; margin-top: 12px; margin-right: 12px">
                            <v-icon x-small color="white" dark
                              @click="RemoveImageBookBank()">mdi-close
                            </v-icon>
                          </v-btn>
                          <img :src="bookBankShowImage" style="width: -webkit-fill-available; height: 380px;"/>
                          <!-- <v-img :src="bookBankShowImage" :style="MobileSize ? '340px' : IpadSize ? '430px' : IpadProSize ? '660px' : '800px'" max-height="400px" contain>
                            <v-btn icon x-small style="float: right; background-color: #ff5252; margin-top: 12px; margin-right: 12px">
                              <v-icon x-small color="white" dark
                                @click="RemoveImageBookBank()">mdi-close
                              </v-icon>
                            </v-btn>
                          </v-img> -->
                        </v-card>
                      </div>
                    </v-col>
                  <!-- </draggable> -->
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
        </v-card-text>
        <v-card-text>
          <v-card class="elevation-0 pl-5 py-3" style="background-color: #f7fffc; color: #27AB9C">
              <v-row>
                <v-col cols="2" sm="1">
                  <v-img
                    src="@/assets/Coorperation/shoplogo.png"
                    width="40"
                    contain
                  ></v-img>
                </v-col>
                <v-col class="d-flex align-center">
                  <span style="font-size: 18px; font-weight: bold;">ข้อมูลภาษี</span>
                </v-col>
              </v-row>
          </v-card>
          <v-row dense no-gutters>
            <v-col cols="12" md="12" sm="6" class="pt-5">
              <span class="textFieldStepOne">หมายเลขประจำตัวผู้เสียภาษี <span style="color: red;">*</span></span>
            </v-col>
            <v-col cols="12" md="12" sm="6" class="pt-3">
              <v-text-field
                v-model="taxId"
                outlined
                dense
                :rules="Rules.emptyText"
                @keypress="CheckSpacebarOne($event)"
                disabled
                style="border-radius: 8px;"
                oninput="this.value = this.value.replace(/[^0-9/\s]/g, '').replace(/(\..*)\./g, '$1')"
                placeholder="ระบุหมายเลขประจำตัวผู้เสียภาษี"
                ></v-text-field>
            </v-col>
          </v-row>
          <v-card class="elevation-0 pl-5 py-3" style="background-color: #f7fffc; color: #27AB9C">
              <v-row>
                <v-col cols="2" sm="1">
                  <v-img
                    src="@/assets/Coorperation/shoplogo.png"
                    width="40"
                    contain
                  ></v-img>
                </v-col>
                <v-col class="d-flex align-center">
                  <span style="font-size: 18px; font-weight: bold;">ที่อยู่นิติบุคคล</span>
                </v-col>
              </v-row>
          </v-card>
          <v-row>
            <v-col cols="12" md="12" sm="4">
                <v-radio-group
                v-model="selectedAddressType"
                :rules="Rules.ItemContact"
                row
                >
                <v-radio
                    label="ใช้ตามข้อมูลนิติบุคคล"
                    value="original_address"
                ></v-radio>
                <v-radio
                    label="ใช้ที่อยู่ใหม่"
                    value="new_address"
                ></v-radio>
                </v-radio-group>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12" md="4" sm="4">
                <span>ที่อยู่ </span>
                <v-text-field @keypress="CheckSpacebarOne($event)" v-model="address" :readonly="selectedAddressType === 'original_address'" placeholder="ที่อยู่" outlined dense >
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>ห้องเลขที่ </span>
                <v-text-field  v-model="roomNo" oninput="this.value = this.value.replace(/[^0-9/-\s]/g, '')" :readonly="selectedAddressType === 'original_address'" placeholder="ห้องเลขที่" outlined dense >
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>ชั้นที่ </span>
                <v-text-field  v-model="floor" oninput="this.value = this.value.replace(/[^0-9/-\s]/g, '')" :readonly="selectedAddressType === 'original_address'" placeholder="ชั้นที่" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>อาคาร </span>
                <v-text-field  v-model="buildingName" :readonly="selectedAddressType === 'original_address'" placeholder="อาคาร" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>หมู่บ้าน </span>
                <v-text-field  v-model="houseName" :readonly="selectedAddressType === 'original_address'" placeholder="หมู่บ้าน" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>หมู่ที่ </span>
                <v-text-field  v-model="houseGroup" oninput="this.value = this.value.replace(/[^0-9/-\s]/g, '')" :readonly="selectedAddressType === 'original_address'" placeholder="หมู่ที่" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>ตรอก/ซอย </span>
                <v-text-field  v-model="alley" :readonly="selectedAddressType === 'original_address'" placeholder="ตรอก/ซอย" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>แยก </span>
            <v-text-field v-model="cross" :readonly="selectedAddressType === 'original_address'" placeholder="แยก" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="4" sm="4">
                <span>ถนน </span>
                <v-text-field v-model="road" :readonly="selectedAddressType === 'original_address'" placeholder="ถนน" outlined dense>
                </v-text-field>
            </v-col>
            <v-col cols="12" md="6" sm="4">
              <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
              <addressinput-subdistrict label="" :readonly="selectedAddressType === 'original_address'" style="border-radius: 8px !important;" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล" />
              <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col cols="12" md="6" sm="4">
              <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
              <addressinput-district label="" :readonly="selectedAddressType === 'original_address'" style="border-radius: 8px !important;" v-model="districtText" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุเขต/อำเภอ" />
              <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col cols="12" md="6" sm="4">
              <span>จังหวัด</span><span style="color: red;"> *</span>
              <addressinput-province label="" :readonly="selectedAddressType === 'original_address'" style="border-radius: 8px !important;" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="provinceText" placeholder="ระบุจังหวัด" />
              <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col cols="12" md="6" sm="4">
              <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
              <addressinput-zipcode numbered :readonly="selectedAddressType === 'original_address'" style="border-radius: 8px !important;" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').substring(0, 5)" label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์" />
              <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <!-- <v-col cols="12" md="6" sm="4">
                <span>ตำบล/แขวง<span style="color: red;"> *</span></span>
                <v-text-field outlined dense v-model="subdistricttext" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '')" :readonly="selectedAddressType === 'original_address'" placeholder="ตำบล/แขวง" :rules="Rules.empty"></v-text-field>
            </v-col>
            <v-col cols="12" md="6" sm="4">
                <span>อำเภอ/เขต</span><span style="color: red;"> *</span>
                <v-text-field outlined dense v-model="districtText" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '')" :readonly="selectedAddressType === 'original_address'" placeholder="อำเภอ/เขต" :rules="Rules.empty"></v-text-field>
            </v-col>
            <v-col cols="12" md="6" sm="4">
                <span>จังหวัด</span><span style="color: red;"> *</span>
                <v-text-field outlined dense v-model="provinceText" oninput="this.value = this.value.replace(/[^a-zA-Zก-๏\s]/g, '')"  placeholder="จังหวัด" :readonly="selectedAddressType === 'original_address'" :rules="Rules.empty"></v-text-field>
            </v-col>
            <v-col cols="12" md="6" sm="4">
                <span>รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                <v-text-field v-model="zipcodeText" placeholder="รหัสไปรษณีย์" :readonly="selectedAddressType === 'original_address'" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').substring(0, 5)" :rules="Rules.postCode" required outlined dense/>
            </v-col> -->
            </v-row>
          <!-- </v-row> -->
          <v-row dense class="mt-4">
            <v-col cols="12" class="d-flex">
              <v-btn rounded color="#27AB9C" width="125" height="40" outlined @click="backtoHome">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <!-- <v-btn text color="#27AB9C" width="81" height="40" @click="clearStepTwo()">ล้างค่า</v-btn> -->
              <v-btn text color="#27AB9C" width="81" height="40" @click="backStep(1)">ย้อนกลับ</v-btn>
              <v-btn color="#27AB9C" width="125" height="40" rounded class="white--text" :class="MobileSize ? '' : ''" @click="confirmCreatePartner">บันทึก</v-btn>
            </v-col>
          </v-row>
        </v-card-text>
      </v-form>
    </v-card>
  </v-container>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
// import draggable from 'vuedraggable'
import { Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
//   components: { draggable },
  data () {
    return {
      stepper: 1,
      businessId: '',
      taxId: '',
      dialogShowImage: false,
      dialogSuccessCreateShop: false,
      dialogFailCreateShop: false,
      dialogAwaitCreateShop: false,
      messageError: '',
      setWidth: 1247,
      setHeight: '70vh',
      imagetoBig: '',
      Detail: {
        shop_logo_image: [],
        image_banner: [],
        branch: '',
        image_news: [],
        shop_image: [],
        shop_image_banner: [],
        shop_advert: [],
        shop_name: '',
        shop_url: '',
        tax_id: '',
        business_id: '',
        line_id: '',
        use_ewht: '',
        shop_description: '',
        payment_method: [],
        shipping_method: [],
        installment_method: [],
        use_estimate: '',
        store_front: '',
        shop_status: '',
        public_show: '',
        partner_show: '',
        have_partner: '',
        facebook_url: '',
        receiver_account_name: '',
        receiver_account_no: '',
        receiver_bank_code: '',
        shop_type: '',
        merchant_key: '',
        first_name: '',
        last_name: '',
        shop_phone: [
          { phone: '' },
          { phone: '' }
        ],
        shop_address: [
          {
            default_address: '',
            house_no: '',
            detail: '',
            province: '',
            district: '',
            sub_district: '',
            zipcode: ''
          }
        ]
      },
      partnerPayload: {
        id_card_num: '',
        partner_name: '',
        service_type: '',
        phone_no: '',
        facebook: '',
        short_code: '',
        line: '',
        url_name: '',
        detail: '',
        transaction_limit: null,
        document_limit: null,
        status: '',
        business_phone_no: '',
        media: [],
        address: [],
        fax_no: '',
        type_address: 'business',
        short_name: '',
        payment_method: []
      },
      partner: false,
      publicshop: true,
      openshop: true,
      shippingPriceShop: false,
      allDay: false,
      monday: false,
      tuesday: false,
      wednesday: false,
      thursday: false,
      friday: false,
      saturday: false,
      sunday: false,
      time: null,
      MondaytimeStart: null,
      MondaytimeEnd: null,
      TuesdaytimeStart: null,
      TuesdaytimeEnd: null,
      WednesdaytimeStart: null,
      WednesdaytimeEnd: null,
      ThursdaytimeStart: null,
      ThursdaytimeEnd: null,
      FirdaytimeStart: null,
      FirdaytimeEnd: null,
      SaturdaytimeStart: null,
      SaturdaytimeEnd: null,
      SundaytimeStart: null,
      SundaytimeEnd: null,
      menuMondayStart: false,
      menuMondayEnd: false,
      menuTuesdayStart: false,
      menuTuesdayEnd: false,
      menuWednesdayStart: false,
      menuWednesdayEnd: false,
      menuThursdayStart: false,
      menuThursdayEnd: false,
      menuFirdayStart: false,
      menuFirdayEnd: false,
      menuSaturdayStart: false,
      menuSaturdayEnd: false,
      menuSundayStart: false,
      menuSundayEnd: false,
      haveCitizen: false,
      haveBusiness: false,
      DataImageShop: [],
      lazyOne: false,
      lazyTwo: false,
      lazyThree: false,
      DataToShowImageBanner: [],
      show: false,
      showError: false,
      showErrorText: '',
      percentUpload: 0,
      dialogOpenDialogUploadBanner: false,
      disableUploadButton: false,
      DataImage: [],
      BranchName: '',
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        seller_name: [
          v => !!v || 'กรุณากรอกชื่อฝ่ายขาย',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        shop_name: [
          v => !!v || 'กรุณากรอกชื่อร้านค้า',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        short_name: [
          v => !!v || 'กรุณากรอกชื่อย่อร้านค้า',
          v => v.length <= 5 || 'กรุณากรอกชื่อย่อร้านค้าไม่เกิน 5 ตัวอักษร',
          v => /^[A-Za-z0-9\s]+$/.test(v) || 'กรุณากรอกแค่ตัวอักษรภาษาอังกฤษและตัวเลข'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 ตัว',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        firstname: [
          v => !!v || 'กรุณากรอกชื่อ',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        lastname: [
          v => !!v || 'กรุณากรอกนามสกุล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ',
          v => (v.length > 8 && v.length <= 20) || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        email: [
          v => !!v || 'กรุณาระบุอีเมล',
          v => !v || /^\w+([.-]?\w+)*@[a-zA-Z0-9]+([.-]?[a-zA-Z]+)*(\.[a-zA-Z]{2,3})+$/.test(v) || 'กรุณากรอกอีเมลให้ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        Merchant: [
          v => !!v || 'กรุณาระบุรหัสการจ่ายเงิน',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        bussinessType: [
          v => !!v || 'กรุณาเลือกประเภทธุรกิจ'
        ],
        contactType: [
          v => !!v || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemContact: [
          v => !!v || 'กรุณาเลือกประเภทสัญญา'
        ],
        ItempayType: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบชำระเงิน'
        ],
        ItemPayment: [
          v => v.length !== 0 || 'กรุณาเลือกช่องทางการชำระเงิน'
        ],
        ItemShipping: [
          v => v.length !== 0 || 'กรุณาเลือกขนส่ง'
        ],
        ItemBank: [
          v => v.length !== 0 || 'กรุณาเลือกธนาคาร'
        ],
        installment: [
          v => v.length !== 0 || 'กรุณาเลือกรูปแบบการผ่อนชำระ'
        ],
        BranchName: [
          v => v.length !== 0 || 'กรุณากรอกชื่อสาขาร้านค้า'
        ],
        ItemServiceType: [
          v => v.length !== 0 || 'กรุณาเลือกประเภทการให้บริการ'
        ],
        ItemPaymentType: [
          v => v.length !== 0 || 'กรุณาเลือกประเภทการชำระเงิน'
        ],
        bookBank: [
          v => v.length !== 0
        ],
        postCode: [
          v => v.length === 5 || 'กรุณากรอกรหัสไปรษณีย์'
        ],
        bookBankNo: [
          v => v.length > 9 || 'กรุณากรอกเลขบัญชีธนาคาร'
        ]
      },
      shopName: '',
      taxNumber: '',
      shopURL: '',
      descriptionShop: '',
      mobile: '',
      facebook: '',
      line: '',
      switchShipping: true,
      switchStoreFront: false,
      itemPayment: [
        { text: 'QR Code', value: 'qrcode' },
        { text: 'Credit Card/Debit Card', value: 'creditcard' }
      ],
      SelectPaymentType: '',
      SelectType: '',
      showTextEWHT: '',
      itemsBank: [],
      bankCode: '',
      accountNo: '',
      accountName: '',
      shortName: '',
      switchEWHT: '',
      itemShipping: [],
      selectInstallmentType: [],
      bussinessType: '',
      itemPayType: [
        { text: 'One Time', value: 'onetime' },
        { text: 'Recurring', value: 'recurring' }
      ],
      SelectTypePay: '',
      SelectShipping: '',
      checkDistrictError: '',
      checkSubDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      mobileNumber: '',
      panel: [0, 1, 2],
      dragover: false,
      dragoverAdvert: false,
      itemPaymentCondition: [
        { header: 'QR Code', content: 'ระบบมีการเรียกเก็บ <span style="color: #27AB9C; font-weight: 700; font-size: 16px;">" ค่าธรรมเนียมครั้งละ 8 บาท / 1 Transaction "</span>' },
        { header: 'Credit Card/Debit Card', content: 'ระบบรองรับ บัตรเครดิต/เดบิต ทุกธนาคาร โดยมี <span style="color: #27AB9C; font-weight: 700; font-size: 16px;">" ค่าธรรมเนียม 2.5% และ VAT 7% ของค่าธรรมเนียม / 1 Transaction "</span>' }
        // { header: 'การผ่อนชำระผ่านบัตรเครดิต', content: 'ระบบรองรับเฉพาะบัตรเครดิตกรุงไทย (KTC) เท่านั้น โดยมี <span style="color: #27AB9C; font-weight: 700; font-size: 16px;">" ค่าธรรมเนียม 3%, ดอกเบี้ย 0.89% และ VAT 7% ของค่าธรรมเนียมและดอกเบี้ย / 1 Transaction "</span>' }
      ],
      setDefault: true,
      DataToShowImageAdvert: [],
      dialogOpenDialogUploadAdvert: false,
      DataImageAdvert: [],
      dialogPaymentCondition: false,
      disableUploadButtonAdvert: true,
      countShop: 0,
      serviceTypeList: [
        'ERP',
        'POS',
        'OMS',
        'Web Development',
        'Marketing'
      ],
      selectedserviceType: '',
      paymentTypeList: [
        { text: 'ออมทรัพย์', value: 'savings' },
        { text: 'ฝากประจำ', value: 'current' }
      ],
      selectedPaymentType: '',
      bankBranchName: '',
      selectedAddressType: 'original_address',
      modalConfirmPartnerCreate: false,
      dialogAwaitChooseAddpackage: false,
      DataImageBookBank: [],
      addressCodeOther: '',
      houseNoOther: '',
      roomNoOther: '',
      floorOther: '',
      buildingNameOther: '',
      houseNameOther: '',
      houseGroupOther: '',
      alleyOther: '',
      crossOther: '',
      roadOther: '',
      typeTaxIDOther: '',
      subdistrictTextOther: '',
      subdistricttext: '',
      districtText: '',
      districtTextOther: '',
      provinceTextOther: '',
      provinceText: '',
      zipcodeTextOther: '',
      zipcodeText: '',
      theRedI: false,
      bookBankShowImage: '',
      DetailBusiness: '',
      businessType: '',
      orinalAddress: {},
      isLoading: true
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    await this.checkCountShop()
    await this.getListBank()
    await this.GetBizData()
    await this.getTaxId()
    await this.getListShipping()
    await this.getDetailBusinessData()
    await this.getPartnerList()
    this.isLoading = false
    localStorage.setItem('step', this.stepper)
  },
  computed: {
    showText () {
      if (this.switchShipping === false) {
        return 'ไม่ใช้ขนส่งของระบบ'
      } else {
        return 'ใช้ขนส่งของระบบ'
      }
    },
    showTextStoreFront () {
      if (this.switchStoreFront === false) {
        return 'ไม่มีรับหน้าร้าน'
      } else {
        return 'มีรับหน้าร้าน'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkLengthValid () {
      if (this.bankCode === '030') {
        return 15
      } else if (this.bankCode === '033' || this.bankCode === '034') {
        return 12
      } else {
        return 10
      }
    }
  },
  watch: {
    SelectPaymentType (val) {
      if (val.includes('installment') === false) {
        this.selectInstallmentType = []
      }
    },
    // switchEWHT (val) {
    //   if (val === false) {
    //     this.bankCode = ''
    //     this.accountNo = ''
    //     this.accountName = ''
    //   }
    // },
    switchShipping (val) {
      if (val === true) {
        this.switchEstimate = false
      }
    },
    switchEstimate (val) {
      if (val === true) {
        this.switchShipping = false
      }
    },
    shopName (val) {
      if (val !== '') {
        const shopCleaned = val.replace(/\s/g, '-')
        this.shopURL = `${process.env.VUE_APP_DOMAIN}shoppage/${shopCleaned}`
      } else {
        this.shopURL = ''
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/editPartnerMobile?step=${this.stepper}` }).catch(() => {})
        localStorage.setItem('step', this.stepper)
      } else {
        this.$router.push({ path: `/editPartner?step=${this.stepper}` }).catch(() => {})
        localStorage.setItem('step', this.stepper)
      }
    },
    subdistricttext (val) {
      if (/\s/g.test(val)) {
        this.subdistricttext = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcodeText = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    districtText (val) {
      if (/\s/g.test(val)) {
        this.districtText = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.provinceText = ''
        }
      }
    },
    provinceText (val) {
      if (/\s/g.test(val)) {
        this.provinceText = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.districtText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.districtText = ''
        }
      }
    },
    zipcodeText (val) {
      if (/\s/g.test(val)) {
        this.zipcodeText = val.replace(/\s/g, '').substring(0, 5)
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            // this.checkAdressError('checkZipcodeError')
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistricttext = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.subdistricttext = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    selectedAddressType (val) {
      if (val === 'original_address') {
        this.useOriginalAddress()
      } else if (val === 'new_address') {
        if (this.isLoading === false) {
          this.clearAddress()
        }
      }
    },
    partnerPayload (val) {
      // console.log(val, 'partnerPayload')
    }
  },
  methods: {
    handleChange () {
      this.accountNo = ''
    },
    async checkCountShop () {
      this.countShop = this.$route.query.countShop
    },
    async getListShipping () {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      const auth = {
        headers: { Authorization: `Bearer ${oneData.user.access_token}` }
      }
      const data = {
        seller_shop_id: -1
      }
      const response = await this.axios.post(`${process.env.VUE_APP_BACK_END2}iship/iship_courier_list`, data, auth)
      if (response.data.ok === 'y') {
        this.itemShipping = [...response.data.query_result]
      }
    },
    UploadImageShop () {
      this.show = true
      this.showError = false
      this.showErrorText = ''
      this.percentUpload = 0
      var data = {}
      const element = this.DataImageShop
      const imageSize = element.size / 1024 / 1024
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
        this.Detail.shop_logo_image = []
        if (imageSize < 1) {
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = async () => {
            var resultReader = reader.result
            // var url = URL.createObjectURL(element)
            data = {
              image: [resultReader.split(',')[1]],
              type: 'shop',
              seller_shop_id: 'all'
            }
            await this.$store.dispatch('actionsUploadToS3', data)
            var response = await this.$store.state.ModuleShop.stateUploadToS3
            if (response.message === 'List Success.') {
              this.Detail.shop_logo_image.push({
                path: response.data.list_path[0].path,
                image_data_lazy: response.data.list_path[0].path_lazy,
                name: this.DataImageShop.name,
                size: this.DataImageShop.size
              })
              setInterval(() => {
                if (this.percentUpload === 100) {
                  this.show = false
                }
                this.percentUpload += 25
              }, 1000)
            }
          }
        } else {
          this.show = false
          this.showError = true
          this.showErrorText = 'ไฟล์มีขนาดใหญ่เกินไป'
          this.Detail.shop_logo_image = ''
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 1 MB',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    UploadImageBookBank () {
      this.show = true
      this.showError = false
      this.showErrorText = ''
      this.percentUpload = 0
      var data = {}
      const element = this.DataImageBookBank[0]
      // console.log(this.DataImageBookBank, 'this.DataImageBookBank')
      const imageSize = element.size / 1024 / 1024
      // console.log(element, 'element')
      if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
        this.partnerPayload.bookbank_image_url = ''
        if (imageSize < 1) {
          // console.log('come')
          const reader = new FileReader()
          reader.readAsDataURL(element)
          reader.onload = async () => {
            var resultReader = reader.result
            // var url = URL.createObjectURL(element)
            data = {
              image: [resultReader.split(',')[1]],
              type: 'shop_banner',
              seller_shop_id: 'all'
            }
            this.$store.commit('openLoader')
            await this.$store.dispatch('actionsUploadToS3', data)
            var response = await this.$store.state.ModuleShop.stateUploadToS3
            // console.log('upload to s3 ====>', response)
            if (response.message === 'List Success.') {
              this.bookBankShowImage = response.data.list_path[0].path
              this.DataImageBookBank[0].path = response.data.list_path[0].path
              this.partnerPayload.bookbank_image_url = response.data.list_path[0].path
              // console.log(this.partnerPayload, 'DataImageBookBank')
              this.$store.commit('closeLoader')
              // this.Detail.shop_logo_image.push({
              //   image_data: response.data.list_path[0].path,
              //   image_data_lazy: response.data.list_path[0].path_lazy,
              //   path: url,
              //   name: this.DataImageShop.name,
              //   size: this.DataImageShop.size
              // })
            }
            // console.log('logo images ===>', this.Detail.shop_logo_image)
          }
        } else {
          this.show = false
          this.showError = true
          this.showErrorText = 'ไฟล์มีขนาดใหญ่เกินไป'
          this.partnerPayload.bookbank_image_url = ''
          this.DataImageBookBank = []
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 1 MB',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    DropImage (e) {
      if (this.Detail.shop_image_banner.length < 6) {
        if (e.dataTransfer.files !== undefined || e.dataTransfer.files.length < 6) {
          for (let i = 0; i < e.dataTransfer.files.length; i++) {
            const element = e.dataTransfer.files[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = () => {
                var resultReader = reader.result
                var url = URL.createObjectURL(element)
                if (this.Detail.shop_image_banner.length < 6) {
                  if (imageSize < 1) {
                    this.Detail.shop_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: false
                    })
                  } else {
                    this.Detail.shop_image_banner.push({
                      image_data: resultReader.split(',')[1],
                      path: url,
                      name: element.name,
                      size: element.size,
                      statusFail: true
                    })
                  }
                  if (this.Detail.shop_image_banner.every((key) => key.statusFail === false)) {
                    this.disableUploadButton = false
                  } else {
                    this.disableUploadButton = true
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 6 รูปภาพ',
                    showConfirmButton: false,
                    timer: 1500,
                    status: false
                  })
                }
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    RemoveImageBanner (index, val) {
      this.DataToShowImageBanner.splice(index, 1)
      this.partnerPayload.media.splice(index, 1)
    },
    RemoveImage (index, val) {
      // console.log(this.partnerPayload.media, 'ก่อนลบ')
      if (this.$route.query.Status === 'Edit') {
        if (val.id !== undefined) {
          this.Detail.remove_img.push({
            id: val.id
          })
        }
        this.partnerPayload.media.splice(index, 1)
      } else {
        this.DataImage.splice(index, 1)
        this.partnerPayload.media.splice(index, 1)
      }
      if (this.partnerPayload.media.every((key) => key.statusFail === false)) {
        this.disableUploadButton = false
      } else {
        this.disableUploadButton = true
      }
    },
    RemoveImageBookBank (index, val) {
      this.DataImageBookBank = []
      this.bookBankShowImage = ''
      this.DataImageBookBank[0].path = ''
      this.partnerPayload.bookbank_image_url = ''
    },
    openModalUploadBanner () {
      this.partnerPayload.media = [...this.DataToShowImageBanner]
      this.dialogOpenDialogUploadBanner = true
    },
    cancelImageShop () {
    },
    uploadImageShop () {
      document.getElementById('imageShop').click()
    },
    onPickFileAdvert () {
      document.getElementById('file_input_Advert').click()
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    onPickFileBookBank () {
      document.getElementById('file_input_book_bank').click()
    },
    uploadToShow () {
      this.DataToShowImageBanner = []
      this.DataToShowImageBanner = [...this.partnerPayload.media]
      this.dialogOpenDialogUploadBanner = false
    },
    UploadImage () {
      if (this.partnerPayload.media.length < 6) {
        if (this.DataImage !== undefined || this.DataImage.length < 6) {
          for (let i = 0; i < this.DataImage.length; i++) {
            const element = this.DataImage[i]
            const imageSize = element.size / 1024 / 1024
            if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = async () => {
                var resultReader = reader.result
                const img = new Image()
                img.src = reader.result
                const imageDimensions = await new Promise((resolve) => {
                  img.onload = () => {
                    const dimensions = {
                      height: img.height,
                      width: img.width
                    }
                    resolve(dimensions)
                  }
                })
                var url = URL.createObjectURL(element)
                if (this.partnerPayload.media.length < 6) {
                  if (imageDimensions.height <= 380 && imageDimensions.width <= 1376) {
                    if (imageSize < 1) {
                      this.partnerPayload.media.push({
                        image_data: resultReader.split(',')[1],
                        path: url,
                        name: this.DataImage[i].name,
                        size: this.DataImage[i].size,
                        // width: width,
                        // height: height,
                        statusFail: false
                      })
                    } else {
                      this.partnerPayload.media.push({
                        image_data: resultReader.split(',')[1],
                        path: url,
                        name: this.DataImage[i].name,
                        size: this.DataImage[i].size,
                        statusFail: true
                      })
                    }
                    if (this.partnerPayload.media.every((key) => key.statusFail === false)) {
                      this.disableUploadButton = false
                    } else {
                      this.disableUploadButton = true
                    }
                  } else {
                    this.$swal.fire({
                      icon: 'warning',
                      text: 'กรุณาใส่รูปขนาด 1376 x 380',
                      showConfirmButton: false,
                      timer: 1500,
                      status: false
                    })
                  }
                } else {
                  this.$swal.fire({
                    icon: 'warning',
                    text: 'กรุณาใส่รูปไม่เกิน 6 รูปภาพ',
                    showConfirmButton: false,
                    timer: 1500,
                    status: false
                  })
                }
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 6 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    cancelUploadBanner () {
      this.partnerPayload.media = []
      this.partnerPayload.media = [...this.DataToShowImageBanner]
      this.dialogOpenDialogUploadBanner = false
    },
    async closeCreateShop () {
      this.dialogSuccessCreateShop = false
      await this.updateOwnerPosition()
      if (this.MobileSize === true) {
        this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
      }
    },
    CheckSpacebarMobile (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    checkFackbook () {
      const spaceRegex = /^\s/
      if (spaceRegex.test(this.facebook)) {
        this.facebook = this.facebook.replace(/\s/g, '')
      }
    },
    checkLine () {
      const spaceRegex = /^\s/
      if (spaceRegex.test(this.line)) {
        this.line = this.line.replace(/\s/g, '')
      }
    },
    remove (item) {
      const index = this.SelectTypePay.indexOf(item.value)
      if (index >= 0) this.SelectTypePay.splice(index, 1)
    },
    async getListBank () {
      await this.$store.dispatch('actionsListBank')
      const response = await this.$store.state.ModuleShop.stateListBank
      if (response.result === 'SUCCESS') {
        this.itemsBank = await [...response.data]
      }
    },
    closeDialogConfirm () {
      this.Detail.image_news = []
      this.dialogAwaitCreateShop = !this.dialogAwaitCreateShop
    },
    bactToModalImage () {
      if (this.fromClick === 'Advert') {
        this.dialogOpenDialogUploadAdvert = true
        this.dialogShowImage = false
      } else {
        this.dialogOpenDialogUploadBanner = true
        this.dialogShowImage = false
      }
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    removePayment (item) {
      const index = this.SelectPaymentType.indexOf(item.value)
      if (index >= 0) this.SelectPaymentType.splice(index, 1)
    },
    backtoHome () {
      if (this.MobileSize) {
        this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
      }
    },
    removeShipping (item) {
      const index = this.SelectShipping.indexOf(item.code)
      if (index >= 0) this.SelectShipping.splice(index, 1)
    },
    ZoomOut () {
      var myImg = document.getElementById('imgBig')
      var currWidth = myImg.clientWidth
      if (this.IpadSize) {
        if (currWidth < 500) {
          this.disableZoonOutButton = true
          return false
        } else {
          myImg.style.width = (currWidth - 100) + 'px'
          this.disableZoonOutButton = false
        }
      } else {
        if (currWidth < 800) {
          this.disableZoonOutButton = true
          return false
        } else {
          myImg.style.width = (currWidth - 100) + 'px'
          this.disableZoonOutButton = false
        }
      }
    },
    ZoomIn () {
      var myImg = document.getElementById('imgBig')
      var currWidth = myImg.clientWidth
      if (this.IpadSize) {
        if (currWidth > 750) {
          this.disableZoomInButton = true
          // return false
        } else {
          myImg.style.width = (currWidth + 100) + 'px'
          this.disableZoomInButton = false
        }
      } else {
        if (currWidth > 1200) {
          this.disableZoomInButton = true
          return false
        } else {
          myImg.style.width = (currWidth + 100) + 'px'
          this.disableZoomInButton = false
        }
      }
    },
    clearStepOne () {
      this.Detail.shop_logo_image = []
      this.Detail.shop_image = []
      this.DataImageShop = []
      this.show = false
      this.showError = false
      this.DataToShowImageBanner = []
      this.Detail.shop_image_banner = []
      this.Detail.image_banner = []
      this.shopName = ''
      this.taxNumber = ''
      this.shopURL = ''
      this.descriptionShop = ''
      this.SelectPaymentType = ''
      this.switchShipping = true
      this.SelectShipping = ''
      this.selectInstallmentType = []
      this.shortName = ''
      this.mobile = ''
      this.facebook = ''
      this.line = ''
      this.openshop = false
      this.partner = false
      // this.MerchantKey = ''
      this.SelectType = 'no_contact'
      this.SelectTypePay = []
    },
    async nextStep (val) {
      this.Detail.payment_costs = []
      this.$store.commit('openLoader')
      if (this.$refs.formOne.validate(true)) {
        await this.uploadBannerToS3(this.partnerPayload.media)
        this.Detail.shop_name = this.shopName
        this.Detail.shop_url = this.shopURL
        if (this.Detail.shop_logo_image.length !== 0) {
          this.Detail.shop_image.push({
            name: this.Detail.shop_logo_image[0].name,
            path: this.Detail.shop_logo_image[0].image_data,
            path_lazy: this.Detail.shop_logo_image[0].image_data_lazy,
            href: ''
          })
        }
        this.Detail.tax_id = this.taxId
        this.Detail.payment_costs = this.SelectType === 'no_contact' ? [] : this.SelectTypePay
        this.Detail.shipping_method = [
          { service: '', courier: [] }
        ]
        this.partnerPayload.partner_name = this.shopName
        this.partnerPayload.service_type = this.selectedserviceType
        this.partnerPayload.url_name = this.shopURL
        this.partnerPayload.detail = this.descriptionShop
        this.partnerPayload.partner_phone_no = this.mobile
        this.partnerPayload.facebook = this.facebook
        this.partnerPayload.line = this.line
        // this.partnerPayload.bank_code = this.bankCode
        // this.partnerPayload.book_bank_type = this.selectedPaymentType
        // this.partnerPayload.bank_username = this.accountName
        // this.partnerPayload.bank_branch = this.bankBranchName
        // this.partnerPayload.bank_no = this.accountNo
        this.stepper = val
        if (!this.MobileSize) {
          window.scrollTo(0, 0)
          this.$store.commit('closeLoader')
          this.$router.push({ path: `/editPartner?step=${this.stepper}` }).catch(() => {})
          localStorage.setItem('step', this.stepper)
        } else {
          window.scrollTo(0, 0)
          this.$store.commit('closeLoader')
          this.$router.push({ path: `/editPartnerMobile?step=${this.stepper}` }).catch(() => {})
          localStorage.setItem('step', this.stepper)
        }
      } else {
        this.$store.commit('closeLoader')
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async getTaxId () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        var bizid = localStorage.getItem('business_id')
        var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
        // this.taxId = response.data.array_business[0].owner_tax_id
        if (ownerBusiness.length === 0) {
          this.$swal.fire({
            icon: 'error',
            text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
            showConfirmButton: false,
            timer: 2500
          })
          if (!this.MobileSize) {
            this.$router.push('/detailbusinesssid')
          } else {
            this.$router.push('/detailbusinesssidMobile')
          }
        } else {
          this.taxId = ownerBusiness[0].owner_tax_id
          this.Detail.business_id = bizid
        }
      }
      this.$store.commit('closeLoader')
    },
    async backtoMenu () {
      if (!this.MobileSize) {
        window.scrollTo(0, 0)
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
      } else {
        window.scrollTo(0, 0)
        this.$store.commit('closeLoader')
        this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
      }
    },
    async uploadBannerToS3 (imageBanner) {
      var data = {
        image: [],
        type: '',
        seller_shop_id: ''
      }
      var backupBanner = []
      // this.partnerPayload.media = []
      for (let h = 0; h < this.partnerPayload.media.length; h++) {
        if (this.partnerPayload.media[h].image_type !== undefined) {
          if (this.partnerPayload.media[h].image_type === 'banner') {
            backupBanner.push(this.partnerPayload.media[h])
          }
        }
        // console.log(this.partnerPayload.media[j])
      }
      // console.log('backupBanner old', backupBanner)
      var backupBannerName = []
      for (let i = 0; i < imageBanner.length; i++) {
        if (imageBanner[i].image_data !== undefined) {
          backupBannerName.push(imageBanner[i].name)
          data.image.push(imageBanner[i].image_data)
        }
      }
      this.Detail.shop_image_banner = []
      data.type = 'shop_banner'
      data.seller_shop_id = 'all'
      await this.$store.dispatch('actionsUploadToS3', data)
      var response = await this.$store.state.ModuleShop.stateUploadToS3
      if (response.message === 'List Success.') {
        for (let j = 0; j < response.data.list_path.length; j++) {
          this.Detail.shop_image_banner.push({
            image_data: response.data.list_path[j].path,
            name: backupBannerName[j]
          })
          // this.Detail.shop_image_banner[j].image_data_lazy = response.data.list_path[j].path_lazy
        }
        this.partnerPayload.media = []
        this.Detail.image_banner = []
        for (let k = 0; k < this.Detail.shop_image_banner.length; k++) {
          this.Detail.image_banner.push({
            name: this.Detail.shop_image_banner[k].name,
            media_path: this.Detail.shop_image_banner[k].image_data,
            media_type: 'image',
            image_type: 'banner',
            status: 'active',
            statusFail: false
          })
        }
        for (let l = 0; l < this.Detail.image_banner.length; l++) {
          backupBanner.push(this.Detail.image_banner[l])
        }
        // console.log(backupBanner, 'backupBanner')
        this.partnerPayload.media = backupBanner
        // var media = response.data.list_path.map(e => {
        //   return {
        //     media_path: e.path,
        //     media_type: 'image',
        //     image_type: 'banner',
        //     name: e.name
        //     status: 'active'
        //   }
        // })
        // var mediaBackUp = backupBanner.map(e => {
        //   return {
        //     media_path: e.path,
        //     media_type: 'image',
        //     image_type: 'banner',
        //     status: 'active'
        //   }
        // })
        // this.partnerPayload.media = []
        // this.partnerPayload.media = media
        // var result = this.partnerPayload.
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
    },
    functionCheckDuplicateValue (arr) {
      const ids = arr.map(({ courier }) => courier)
      const filtered = arr.filter(({ courier }, index) => !ids.includes(courier, index + 1))
      return filtered
    },
    async GetBizData () {
      var room = ''
      var floor = ''
      var buildingName = ''
      var mooBan = ''
      var mooNo = ''
      var soi = ''
      var yaek = ''
      var street = ''
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      // var BusinessID = response.data.list_business[0].id
      var data = {
        business_id: localStorage.getItem('business_id')
      }
      await this.$store.dispatch('actionDetailBusiness', data)
      var responseBizDetail = await this.$store.state.ModuleBusiness.stateDetailBusiness
      // console.log('responseBizDetail', responseBizDetail)
      if (responseBizDetail.result === 'SUCCESS') {
        this.DataBiz = responseBizDetail.data
        // this.shopName = this.DataBiz.first_name_th
        this.shopName = ''
        this.taxNumber = this.DataBiz.id_card_num
        this.mobile = this.DataBiz.tel_no !== null ? this.DataBiz.tel_no : ''
        this.DataAddressBiz = responseBizDetail.data.address
        this.name = response.data.first_name_th
        this.surname = response.data.last_name_th
        this.email = this.DataBiz.email
        this.mobileNumber = this.DataBiz.mobile_no
        if (this.DataAddressBiz.room_no !== null) {
          room = 'ห้องเลขที่ ' + this.DataAddressBiz.room_no + ' '
        }
        if (this.DataAddressBiz.floor !== null) {
          floor = 'ชั้น ' + this.DataAddressBiz.floor + ' '
        }
        if (this.DataAddressBiz.building_name !== null) {
          buildingName = this.DataAddressBiz.building_name + ' '
        }
        if (this.DataAddressBiz.moo_ban !== null) {
          mooBan = 'หมู่บ้าน ' + this.DataAddressBiz.moo_ban + ' '
        }
        if (this.DataAddressBiz.moo_no !== null) {
          mooNo = 'หมู่ที่ ' + this.DataAddressBiz.moo_no + ' '
        }
        if (this.DataAddressBiz.soi !== null) {
          soi = 'ซอย ' + this.DataAddressBiz.soi + ' '
        }
        if (this.DataAddressBiz.yaek !== null) {
          yaek = 'แยก ' + this.DataAddressBiz.yaek + ' '
        }
        if (this.DataAddressBiz.street !== null) {
          street = 'ถนน ' + this.DataAddressBiz.street
        }
        this.addressDetail = room + floor + buildingName + mooBan + mooNo + soi + yaek + street
        this.houseNo = this.DataAddressBiz.house_no
        this.subdistrict = this.DataAddressBiz.tambon
        this.district = this.DataAddressBiz.amphoe
        this.province = this.DataAddressBiz.province
        this.zipcode = this.DataAddressBiz.zipcode
        this.partnerPayload.branch = this.DataBiz.branch_name
        this.partnerPayload.branch_no = this.DataBiz.branch_no
        this.partnerPayload.business_name_en = this.DataBiz.name_on_document_eng
        this.partnerPayload.business_name_th = this.DataBiz.name_on_document_th
        this.partnerPayload.id_card_num = this.taxNumber
        this.partnerPayload.company_name_th = this.DataBiz.name_on_document_th
        this.partnerPayload.company_name_en = this.DataBiz.name_on_document_eng
        // this.partnerPayload.company_name_th = this.DataBiz.name_on_document_th
        // this.partnerPayload.company_name_en = this.DataBiz.name_on_document_eng
        this.partnerPayload.email = this.DataBiz.email
        this.partnerPayload.business_short_code = '' // this.DataBiz.branch_no
        // if (this.DetailBusiness.account_title_th === 'ห้างหุ้นส่วนสามัญ') {
        //   this.businessType = 1
        // } else if (this.DetailBusiness.account_title_th === 'ห้างหุ้นส่วนจำกัด') {
        //   this.businessType = 2
        // } else if (this.DetailBusiness.account_title_th === 'บริษัทจำกัด') {
        //   this.businessType = 3
        // } else if (this.DetailBusiness.account_title_th === 'บริษัทมหาชนจำกัด') {
        //   this.businessType = 4
        // } else {
        //   this.businessType = 5
        // }
        this.$store.commit('closeLoader')
      }
    },
    async uploadAdvertToS3 (imageAdvert) {
      var data = {
        image: [],
        type: '',
        seller_shop_id: ''
      }
      for (let i = 0; i < imageAdvert.length; i++) {
        data.image.push(imageAdvert[i].image_data)
      }
      data.type = 'shop_banner'
      data.seller_shop_id = 'all'
      await this.$store.dispatch('actionsUploadToS3', data)
      var response = await this.$store.state.ModuleShop.stateUploadToS3
      if (response.message === 'List Success.') {
        for (let j = 0; j < response.data.list_path.length; j++) {
          this.Detail.shop_advert[j].image_data = response.data.list_path[j].path
          this.Detail.shop_advert[j].image_data_lazy = response.data.list_path[j].path_lazy
        }
        for (let k = 0; k < this.Detail.shop_advert.length; k++) {
          this.Detail.image_news.push({
            name: this.Detail.shop_advert[k].name,
            path: this.Detail.shop_advert[k].image_data,
            path_lazy: this.Detail.shop_advert[k].image_data_lazy,
            href: ''
          })
        }
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async checkCreateShop () {
      // this.Detail.shop_email = []
      this.$store.commit('openLoader')
      if (this.$refs.formThree.validate(true)) {
        if (this.DataToShowImageAdvert.length !== 0) {
          await this.uploadAdvertToS3(this.DataToShowImageAdvert)
        } else {
          this.Detail.image_news = []
          this.$store.commit('closeLoader')
        }
        this.dialogAwaitCreateShop = true
      } else {
        this.$store.commit('closeLoader')
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    checkTextError (error) {
      this.messageError = ''
      if (error === 'Maximum image files upload are 6 files.') {
        this.messageError = 'สามารถอัปโหลดรูปภาพสูงสุด 6 รูป'
      } else if (error === 'tax_id invalid.') {
        this.messageError = 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
      } else if (error === 'Shop address data is invalid.') {
        this.messageError = 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
      } else if (error === 'This shop name has already used.') {
        this.messageError = 'ชื่อร้านค้านี้ถูกใช้งานแล้ว'
      } else if (error === 'This url name has already used.') {
        this.messageError = 'ชื่อ URL นี้ถูกใช้งานแล้ว'
      } else if (error === 'This short name has already used.') {
        this.messageError = 'ชื่อย่อร้านค้านี้ถูกใช้งานแล้ว'
      } else {
        this.messageError = error
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistrict)
      this.checkdistrictConfirm(this.district)
      this.checkprovinceConfirm(this.province)
      this.checkzipcodeConfirm(this.zipcode)
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    RemoveImageAdvertOuter (index, val) {
      this.DataToShowImageAdvert.splice(index, 1)
    },
    openModalUploadAdvert () {
      this.Detail.shop_advert = [...this.DataToShowImageAdvert]
      this.dialogOpenDialogUploadAdvert = true
    },
    uploadAdvert () {
      this.DataToShowImageAdvert = []
      this.DataToShowImageAdvert = [...this.Detail.shop_advert]
      this.dialogOpenDialogUploadAdvert = false
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    ShowBigImage (from, image) {
      this.imagetoBig = image
      this.fromClick = ''
      if (from === 'Advert') {
        this.fromClick = from
        this.dialogOpenDialogUploadAdvert = false
        this.dialogShowImage = true
      } else {
        this.fromClick = from
        this.dialogOpenDialogUploadBanner = false
        this.dialogShowImage = true
      }
    },
    backStep (val) {
      this.stepper = val
      if (!this.MobileSize) {
        this.$router.push({ path: `/editPartner?step=${this.stepper}` }).catch(() => {})
        localStorage.setItem('step', this.stepper)
      } else {
        this.$router.push({ path: `/editPartnerMobile?step=${this.stepper}` }).catch(() => {})
        localStorage.setItem('step', this.stepper)
      }
    },
    cancelUploadAdvert () {
      this.Detail.shop_advert = []
      this.Detail.shop_advert = [...this.DataToShowImageAdvert]
      this.dialogOpenDialogUploadAdvert = false
    },
    clearStepThree () {
      this.DataToShowImageAdvert = []
      this.Detail.image_news = []
      this.Detail.shop_advert = []
    },
    clearStepTwo () {
      this.name = ''
      this.surname = ''
      this.email = ''
      this.mobileNumber = ''
      this.addressDetail = ''
      this.houseNo = ''
      this.subdistrict = ''
      this.checkSubDistrictError = false
      this.district = ''
      this.checkDistrictError = false
      this.province = ''
      this.checkProvinceError = false
      this.zipcode = ''
      this.checkZipcodeError = false
      this.setDefault = true
    },
    async updateOwnerPosition () {
      var data = {
        tax_id: this.taxId
      }
      await this.$store.dispatch('actionsUpdateOwnerPosition', data)
    },
    confirmCreatePartner () {
      if (this.$refs.formTwo.validate(true) && this.subdistricttext !== '' && this.districtText !== '' && this.provinceText !== '' && this.zipcodeText !== '') {
        this.modalConfirmPartnerCreate = true
      } else {
        window.scrollTo(0, 0)
      }
    },
    closeConfirmCreatePartner () {
      this.modalConfirmPartnerCreate = false
    },
    chooseAddPackage () {
      this.modalConfirmPartnerCreate = false
      this.dialogAwaitChooseAddpackage = true
    },
    async closeChooseAddPackage () {
      this.dialogAwaitChooseAddpackage = false
      await this.createPartner('no')
    },
    async createPartner (val) {
      // this.dialogAwaitChooseAddpackage = false
      this.modalConfirmPartnerCreate = false
      this.partnerPayload.bank_code = this.bankCode
      this.partnerPayload.book_bank_type = this.selectedPaymentType
      this.partnerPayload.bank_username = this.accountName
      this.partnerPayload.bank_branch = this.bankBranchName
      this.partnerPayload.bank_no = this.accountNo
      this.partnerPayload.payment_method = this.SelectPaymentType
      this.partnerPayload.short_name = this.shortName
      if (this.selectedAddressType === 'new_address') {
        this.partnerPayload.address.push(this.orinalAddress)
        this.partnerPayload.new_address.push({
          house_code: this.address,
          house_no: this.address,
          room_no: this.roomNo,
          moo_ban: this.houseName,
          moo_no: this.houseGroup,
          building_name: this.buildingName,
          floor: this.floor,
          yaek: this.cross,
          street: this.road,
          soi: this.alley,
          province: this.provinceText,
          tambon: this.subdistricttext,
          amphoe: this.districtText,
          zipcode: this.zipcodeText
        })
        this.partnerPayload.type_address = 'partner'
      } else {
        this.partnerPayload.address.push({
          house_code: this.address,
          house_no: this.address,
          room_no: this.roomNo,
          moo_ban: this.houseName,
          moo_no: this.houseGroup,
          building_name: this.buildingName,
          floor: this.floor,
          yaek: this.cross,
          street: this.road,
          soi: this.alley,
          province: this.provinceText,
          tambon: this.subdistricttext,
          amphoe: this.districtText,
          zipcode: this.zipcodeText
        })
        this.partnerPayload.type_address = 'business'
      }
      // console.log(this.partnerPayload.media, 'data')
      var shopImage = []
      // this.Detail.shop_image
      if (this.Detail.shop_logo_image.length !== 0) {
        shopImage = this.Detail.shop_logo_image.map(e => {
          return {
            image_type: 'main',
            name: e.name,
            media_path: e.path,
            media_type: 'image',
            status: 'active'
          }
        })
      }
      var formatBanner = this.partnerPayload.media.map(e => {
        if (e.media_path === undefined) {
          return {
            image_type: 'banner',
            name: e.name,
            media_path: e.path,
            media_type: 'image',
            status: 'active'
          }
        } else {
          return {
            image_type: 'banner',
            name: e.name,
            media_path: e.media_path,
            media_type: 'image',
            status: 'active'
          }
        }
      })
      this.partnerPayload.media = []
      this.partnerPayload.media = formatBanner
      if (shopImage.length !== 0) {
        this.partnerPayload.media.push(shopImage[0])
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsPartnerEdit', this.partnerPayload)
      var response = await this.$store.state.ModuleBusiness.statePartnerEdit
      if (response.code === 200) {
        this.$EventBus.$emit('CheckPartnerCode', this.taxId, 'no')
        if (val === 'no') {
          this.dialogSuccessCreateShop = true
        } else {
          if (this.MobileSize) {
            this.$router.push({ path: '/serviceProductPartnerMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/serviceProductPartner' }).catch(() => {})
          }
        }
      } else {
        await this.$swal.fire({
          icon: 'warning',
          text: `status ${response.code} ${response.message}`,
          showConfirmButton: false
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/partnerShopInfoMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/partnerShopInfo' }).catch(() => {})
        }
      }
      this.dialogSuccessCreateShop = true
      this.$store.commit('closeLoader')
    },
    async getDetailBusinessData () {
      this.$store.commit('openLoader')
      var bizid = ''
      bizid = localStorage.getItem('business_id')
      if (bizid === null) {
        await this.$store.dispatch('actionsAuthorityUser')
        var res = await this.$store.state.ModuleUser.stateAuthorityUser
        this.BusinessID = res.data.list_business[0].id
      } else {
        this.BusinessID = bizid
      }
      var data = {
        business_id: this.BusinessID
      }
      await this.$store.dispatch('actionDetailBusiness', data)
      var response = await this.$store.state.ModuleBusiness.stateDetailBusiness
      // console.log('response for detail business ===>', response.data)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.showForm = true
        this.selectType = 'none'
        this.DetailBusiness = await response.data
        this.tel = this.DetailBusiness.tel_no !== null ? this.DetailBusiness.tel_no : ''
        // this.partnerPayload.partner_phone_no = this.tel
        this.phone = this.DetailBusiness.mobile_no !== null ? this.DetailBusiness.mobile_no : ''
        this.partnerPayload.business_phone_no = this.phone
        this.partnerPayload.telephone = this.tel
        this.partnerPayload.fax_no = ''
        this.partnerPayload.new_address = []
        this.partnerPayload.bookbank_image_url = ''
        this.fax = this.DetailBusiness.address.fax_number !== null ? this.DetailBusiness.address.fax_number : ''
        this.addressCode = this.DetailBusiness.address.house_code !== null ? this.DetailBusiness.address.house_code : '-'
        this.address = this.DetailBusiness.address.house_no !== null ? this.DetailBusiness.address.house_no : '-'
        this.roomNo = this.DetailBusiness.address.room_no !== null ? this.DetailBusiness.address.room_no : '-'
        this.buildingName = this.DetailBusiness.address.building_name !== null ? this.DetailBusiness.address.building_name : '-'
        this.floor = this.DetailBusiness.address.floor !== null ? this.DetailBusiness.address.floor : '-'
        this.houseName = this.DetailBusiness.address.moo_ban !== null ? this.DetailBusiness.address.moo_ban : '-'
        this.houseGroup = this.DetailBusiness.address.moo_no !== null ? this.DetailBusiness.address.moo_no : '-'
        this.alley = this.DetailBusiness.address.soi !== null ? this.DetailBusiness.address.soi : '-'
        this.road = this.DetailBusiness.address.street !== null ? this.DetailBusiness.address.street : '-'
        this.cross = this.DetailBusiness.address.yaek !== null ? this.DetailBusiness.address.yaek : '-'
        this.province = this.DetailBusiness.address.province
        this.district = this.DetailBusiness.address.amphoe
        this.subdistricttext = this.DetailBusiness.address.tambon
        this.districtText = this.DetailBusiness.address.amphoe
        this.provinceText = this.DetailBusiness.address.province
        this.zipcodeText = this.DetailBusiness.address.zipcode
        this.orinalAddress = {
          house_code: this.address,
          house_no: this.address,
          room_no: this.roomNo,
          moo_ban: this.houseName,
          moo_no: this.houseGroup,
          building_name: this.buildingName,
          floor: this.floor,
          yaek: this.cross,
          street: this.road,
          soi: this.alley,
          province: this.provinceText,
          tambon: this.subdistricttext,
          amphoe: this.districtText,
          zipcode: this.zipcodeText
        }
        if (this.DetailBusiness.account_title_th === 'ห้างหุ้นส่วนสามัญ') {
          this.businessType = 1
        } else if (this.DetailBusiness.account_title_th === 'ห้างหุ้นส่วนจำกัด') {
          this.businessType = 2
        } else if (this.DetailBusiness.account_title_th === 'บริษัทจำกัด') {
          this.businessType = 3
        } else if (this.DetailBusiness.account_title_th === 'บริษัทมหาชนจำกัด') {
          this.businessType = 4
        } else {
          this.businessType = 5
        }
      } else {
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 3000 })
        this.$router.push({ path: '/' }).catch(() => {})
      }
    },
    clearAddress () {
      this.orinalAddress = {
        house_code: this.address,
        house_no: this.address,
        room_no: this.roomNo,
        moo_ban: this.houseName,
        moo_no: this.houseGroup,
        building_name: this.buildingName,
        floor: this.floor,
        yaek: this.cross,
        street: this.road,
        soi: this.alley,
        province: this.provinceText,
        tambon: this.subdistricttext,
        amphoe: this.districtText,
        zipcode: this.zipcodeText
      }
      this.tel = ''
      this.phone = ''
      this.fax = ''
      this.addressCode = ''
      this.address = ''
      this.roomNo = ''
      this.buildingName = ''
      this.floor = ''
      this.houseName = ''
      this.houseGroup = ''
      this.alley = ''
      this.road = ''
      this.cross = ''
      this.province = ''
      this.district = ''
      this.subdistricttext = ''
      this.districtText = ''
      this.provinceText = ''
      this.zipcodeText = ''
    },
    useOriginalAddress () {
      this.tel = this.DetailBusiness.tel_no !== null ? this.DetailBusiness.tel_no : '-'
      this.phone = this.DetailBusiness.mobile_no !== null ? this.DetailBusiness.mobile_no : '-'
      this.fax = this.DetailBusiness.address.fax_number !== null ? this.DetailBusiness.address.fax_number : '-'
      this.addressCode = this.DetailBusiness.address.house_code !== null ? this.DetailBusiness.address.house_code : '-'
      this.address = this.DetailBusiness.address.house_no !== null ? this.DetailBusiness.address.house_no : '-'
      this.roomNo = this.DetailBusiness.address.room_no !== null ? this.DetailBusiness.address.room_no : '-'
      this.buildingName = this.DetailBusiness.address.building_name !== null ? this.DetailBusiness.address.building_name : '-'
      this.floor = this.DetailBusiness.address.floor !== null ? this.DetailBusiness.address.floor : '-'
      this.houseName = this.DetailBusiness.address.moo_ban !== null ? this.DetailBusiness.address.moo_ban : '-'
      this.houseGroup = this.DetailBusiness.address.moo_no !== null ? this.DetailBusiness.address.moo_no : '-'
      this.alley = this.DetailBusiness.address.soi !== null ? this.DetailBusiness.address.soi : '-'
      this.road = this.DetailBusiness.address.street !== null ? this.DetailBusiness.address.street : '-'
      this.cross = this.DetailBusiness.address.yaek !== null ? this.DetailBusiness.address.yaek : '-'
      this.province = this.DetailBusiness.address.province
      this.district = this.DetailBusiness.address.amphoe
      this.subdistricttext = this.DetailBusiness.address.tambon
      this.districtText = this.DetailBusiness.address.amphoe
      this.provinceText = this.DetailBusiness.address.province
      this.zipcodeText = this.DetailBusiness.address.zipcode
      this.partnerPayload.type_address = 'business'
    },
    async getPartnerList () {
      var data = {
        id_card_num: this.taxId
      }
      await this.$store.dispatch('actionsGetPartnerList', data)
      var response = await this.$store.state.ModuleBusiness.stateGetPartnerList
      if (response.code === 200) {
        this.selectedAddressType = response.data[0].type_address === 'partner' ? 'new_address' : 'original_address'
        if (this.selectedAddressType === 'new_address') {
          // console.log(response.data[0].new_address[0])
          this.fax = response.data[0].new_address[0].fax_number !== null ? response.data[0].new_address[0].fax_number : '-'
          this.addressCode = response.data[0].new_address[0].house_code !== null ? response.data[0].new_address[0].house_code : '-'
          this.address = response.data[0].new_address[0].house_no !== null ? response.data[0].new_address[0].house_no : '-'
          this.roomNo = response.data[0].new_address[0].room_no !== null ? response.data[0].new_address[0].room_no : '-'
          this.buildingName = response.data[0].new_address[0].building_name !== null ? response.data[0].new_address[0].building_name : '-'
          this.floor = response.data[0].new_address[0].floor !== null ? response.data[0].new_address[0].floor : '-'
          this.houseName = response.data[0].new_address[0].moo_ban !== null ? response.data[0].new_address[0].moo_ban : '-'
          this.houseGroup = response.data[0].new_address[0].moo_no !== null ? response.data[0].new_address[0].moo_no : '-'
          this.alley = response.data[0].new_address[0].soi !== null ? response.data[0].new_address[0].soi : '-'
          this.road = response.data[0].new_address[0].street !== null ? response.data[0].new_address[0].street : '-'
          this.cross = response.data[0].new_address[0].yaek !== null ? response.data[0].new_address[0].yaek : '-'
          this.province = response.data[0].new_address[0].province
          this.district = response.data[0].new_address[0].amphoe
          this.subdistricttext = response.data[0].new_address[0].tambon
          this.districtText = response.data[0].new_address[0].amphoe
          this.provinceText = response.data[0].new_address[0].province
          this.zipcodeText = response.data[0].new_address[0].zipcode
        }
        this.shopName = response.data[0].partner_name
        this.descriptionShop = response.data[0].detail !== null ? response.data[0].detail : ''
        this.mobile = response.data[0].phone_no
        this.facebook = response.data[0].facebook !== null ? response.data[0].facebook : ''
        this.line = response.data[0].line !== null ? response.data[0].line : ''
        this.accountName = response.data[0].bank_username
        this.bankCode = response.data[0].branch_no
        this.selectedPaymentType = response.data[0].book_bank_type
        this.accountNo = response.data[0].bank_no
        this.bankBranchName = response.data[0].bank_branch
        this.selectedserviceType = response.data[0].service_type
        this.bankCode = response.data[0].bank_code
        this.bookBankShowImage = response.data[0].bookbank_image_url
        this.partnerPayload.bookbank_image_url = response.data[0].bookbank_image_url
        this.mobile = response.data[0].partner_phone_no
        this.shortName = response.data[0].short_name
        this.SelectPaymentType = response.data[0].payment_method
        if (response.data[0].type_address === 'business') {
          this.partnerPayload.type_address = 'business'
        } else {
          this.partnerPayload.type_address = 'partner'
        }
        this.DataImageBookBank[0] = { path: this.bookBankShowImage }
        for (var i = 0; i < response.data[0].media.length; i++) {
          if (response.data[0].media[i].image_type === 'main') {
            this.Detail.shop_logo_image[0] = {
              path: response.data[0].media[i].media_path,
              name: response.data[0].media[i].name !== null ? response.data[0].media[i].name : '',
              image_type: response.data[0].media[i].image_type,
              media_type: response.data[0].media[i].media_type,
              size: '',
              statusFail: false
            }
          } else {
            this.partnerPayload.media.push({
              path: response.data[0].media[i].media_path,
              name: response.data[0].media[i].name !== null ? response.data[0].media[i].name : '',
              image_type: response.data[0].media[i].image_type,
              media_type: response.data[0].media[i].media_type,
              size: '',
              statusFail: false
            })
          }
        }
        // this.partnerPayload.media = response.data[0].media.map(e => {
        //   if (e.image_type !== 'main') {
        //     return {
        //       path: e.media_path,
        //       name: e.name !== null ? e.name : '',
        //       image_type: e.image_type,
        //       media_type: e.media_type,
        //       size: '',
        //       statusFail: false
        //     }
        //   }
        // })
        this.DataToShowImageBanner = [...this.partnerPayload.media]
        // this.getImageFileSizeByDownload(response.data[0].media[0].media_path)
        // console.log(img, 'img')
      }
      // console.log(this.shopName)
    }
  }
}
</script>

<style scoped>
::v-deep(.v-chip) {
  background-color: #DAF1E9 !important;
  color: #27AB9C !important;
  border-radius: 16px;
}
</style>

<style>
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  border-radius: 8px !important;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  color: #212121;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
