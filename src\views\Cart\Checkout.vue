<template>
  <div class="container">
    <h1>รายการสั่งซื้อ</h1>
    <div v-if="checkLogin === true">
      <CheckoutLogin />
      <ModalAddress :checkAddress="check" />
    </div>
    <div v-else>
      <CheckoutLocalStorage />
      <ModalAddressLocal :checkAddressLocal="check" />
    </div>
  </div>
</template>

<script>
import { Encode } from '@/services'
export default {
  components: {
    CheckoutLogin: () => import('@/components/Cart/CheckoutLogin'),
    CheckoutLocalStorage: () => import('@/components/Cart/CheckoutLocalStorage'),
    ModalAddress: () => import('@/components/Modal/Address'),
    ModalAddressLocal: () => import('@/components/Modal/AddressLocal')
  },
  data () {
    return {
      checkLogin: false,
      propsAddress: [],
      check: false
    }
  },
  created () {
    this.$EventBus.$emit('getPath')
    if (localStorage.getItem('oneData') !== null) {
      this.checkLogin = true
      this.checkAddress()
    } else {
      this.checkLogin = false
      this.check = true
    }
  },
  methods: {
    async checkAddress () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      // console.log('this.propsAddress[0].address_data.length', this.propsAddress[0].address_data[0])
      localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
      if (this.propsAddress[0].address_data[0].status === 'N') {
        this.check = true
      }
    },
    async checkAddressLocal () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role
      }
      await this.$store.dispatch('GetUserAddress', data)
      this.propsAddress = this.$store.state.ModuleManageShop.GetUserAddress
      // console.log('this.propsAddress[0].address_data.length', this.propsAddress[0].address_data.length)
      if (this.propsAddress[0].address_data.length === 0) {
        this.check = true
      }
    }
  }
}
</script>
