<template>
  <v-container>
    <div v-if="itemsCart.length === 0">
      <h1>ไม่มีสินค้าในรถเข็น</h1>
      <v-btn color="primary" outlined @click="goHomepage()">go home</v-btn>
    </div>
    <div v-else>
      <v-row dense>
        <v-overlay :value="overlay">
          <v-progress-circular indeterminate size="64"></v-progress-circular>
        </v-overlay>
        <v-col cols="12" md="12" v-if="falseAlert">
          <v-row dense justify="center">
            <v-col cols="4">
              <v-alert dense text type="info">
                กรุณาเลือกรายการสินค้าที่ต้องการชำระเงิน
              </v-alert>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="12">
          <v-card outlined class="elevation-0">
            <v-container>
              <div v-for="(item,index) in itemsCart" :key="index">
                <!-- <pre>{{item}}</pre> -->
                <a-table bordered :data-source="item.product_list" :rowKey="record =>  record.sku" :columns="headers" :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }" v-if="item.checkStatus === false">
                  <template slot="title">
                    <p class="text-left">{{item.shop_name}}</p>
                  </template>
                  <template slot="actions" slot-scope="text, record">
                    <v-icon small @click="changeQuantitySwal(record, 'DELETE')">
                      mdi-delete
                    </v-icon>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" class="pr-0 py-1">
                        <v-img :src="`${record.product_image.url}`" class="imageshow" v-if="record.product_image" @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="12" md="8">
                        <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <v-col cols="12" class="py-0 px-0">
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity--, changeQuantitySwal(record, 'UPDATE')" :disabled="checkQuantity">
                        <v-icon x-small>mdi-minus</v-icon>
                      </v-btn>
                      <input v-model="record.quantity" @change="changeQuantitySwal(record, 'UPDATE')" class="AddNumberProduct" size="4" maxlength="4" type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"/>
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity++, changeQuantitySwal(record, 'UPDATE')" :disabled="checkQuantity">
                        <v-icon x-small> mdi-plus</v-icon>
                      </v-btn>
                    </v-col>
                  </template>
                  <template slot="price" slot-scope="text, record">
                    <v-col cols="12">
                      <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </template>
                  <template slot="net_price" slot-scope="text, record">
                    <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                </a-table>
                <a-table bordered :data-source="item.product_list" :rowKey="record =>  record.sku" :columns="headers" v-else>
                  <template slot="title">
                    <p class="text-left">{{item.shop_name}}</p>
                  </template>
                  <template slot="actions" slot-scope="text, record">
                    <v-icon small @click="changeQuantitySwal(record, 'DELETE')">
                      mdi-delete
                    </v-icon>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" class="pr-0 py-1">
                        <v-img :src="`${record.product_image.url}`" class="imageshow" v-if="record.product_image" @click="goProductDetail(record)" />
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else @click="goProductDetail(record)" />
                      </v-col>
                      <v-col cols="12" md="8">
                        <p class="mb-0 caption">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="quantity" slot-scope="text, record">
                    <v-col cols="12" class="py-0 px-0">
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity--, changeQuantitySwal(record, 'UPDATE')" :disabled="checkQuantity">
                        <v-icon x-small>mdi-minus</v-icon>
                      </v-btn>
                      <input v-model="record.quantity" @change="changeQuantitySwal(record, 'UPDATE')" class="AddNumberProduct" size="4" maxlength="4" type="text" oninput="this.value = this.value.replace(/[^0-9.]/g, '').replace(/(\..*)\./g, '$1')"/>
                      <v-btn elevation="1" x-small icon outlined class="mx-1" @click="record.quantity++, changeQuantitySwal(record, 'UPDATE')" :disabled="checkQuantity">
                        <v-icon x-small> mdi-plus</v-icon>
                      </v-btn>
                    </v-col>
                  </template>
                  <template slot="price" slot-scope="text, record">
                    <v-col cols="12">
                      <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </template>
                  <template slot="net_price" slot-scope="text, record">
                    <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                </a-table>
              </div>
            </v-container>
          </v-card>
        </v-col>
        <v-col cols="12" md="12">
          <v-card outlined class="elevation-0">
            <v-container grid-list-xs>
              <v-row>
                <v-col cols="12" md="10">
                  <v-row dense>
                    <v-col cols="12" class="text-right">
                      <span class="subheader">ราคารวมสินค้าที่เลือก</span>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="12" md="2">
                  <v-row dense>
                    <v-col cols="12" class="text-left">
                      <span>{{ Number(CartData.total_price_no_vat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-container>
          </v-card>
        </v-col>
      </v-row>
      <v-row>
        <v-col cols="12" class="text-right pt-5">
          <v-btn color="primary" outlined @click="nextstep()" :disabled="selectProduct === false">
            ยืนยันรายการสั่งซื้อ
          </v-btn>
        </v-col>
      </v-row>
    </div>
  </v-container>
</template>

<script>
import { mapMutations } from 'vuex'
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data () {
    return {
      itemsCart: [],
      CartData: [],
      overlay: false,
      selectedRowKeys: [],
      shopNameList: {
        data: []
      },
      selectProduct: false,
      falseAlert: true,
      checkQuantity: false
    }
  },
  computed: {
    localStorageLanguage () {
      return localStorage.getItem('StorageLanguage')
    },
    step () {
      return this.$store.state.ModuleCart.state_step
    },
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '35%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          width: '15%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          scopedSlots: { customRender: 'price' },
          key: 'price',
          width: '20%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          key: 'net_price',
          width: '20%'
        },
        {
          title: 'แอคชั่น',
          key: 'actions',
          scopedSlots: { customRender: 'actions' },
          width: '10%'
        }
      ]
      return headers
    }
  },
  watch: {
    // itemsCart (val) {
    //   console.log('watch  itemsCart', val)
    // }
  },
  async created () {
    this.$EventBus.$on('getCart', this.getDetailCart)
    this.getDetailCart()
  },
  methods: {
    ...mapMutations(['setCountCart']),
    async goProductDetail (item) {
      const nameCleaned = item.product_name.replace(/\s/g, '-')
      // this.goPage(`/DetailProduct/${nameCleaned}-${item.product_id}`)
      const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${item.id}` } })
      window.location.assign(routeData.href, '_blank')
    },
    goPage (link) {
      this.$router.push(link).catch(() => {})
    },
    async getDetailCart () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.checkQuantity = true
      this.overlay = true
      var data = {
        role_user: dataRole.role,
        shop_to_cal: this.shopNameList.data,
        product_to_cal: this.selectedRowKeys
      }
      await this.$store.dispatch('ActionDetailCart', data)
      const res = await this.$store.state.ModuleCart.stateDetailCart
      // console.log('res cart detail in shopping cart', res)
      this.itemsCart = []
      // console.log('log shopNameList==', this.shopNameList)
      if (res.message === 'Get detail cart success') {
        this.checkQuantity = false
        res.data.shop_list.forEach(list => {
          if (this.shopNameList.data.length !== 0) {
            this.falseAlert = false
            if (list.shop_name !== this.shopNameList.data[0]) {
              list.checkStatus = true
              this.itemsCart.push(list)
            } else {
              list.checkStatus = false
              this.itemsCart.push(list)
            }
          } else {
            this.falseAlert = true
            list.checkStatus = false
            this.itemsCart.push(list)
          }
        })
        // console.log('log list cart Ja', this.itemsCart)
        // เปลี่ยนไปใช้ watch
        // this.overlay = false
        // this.itemsCart = res.data.shop_list
        this.CartData = res.data
        if (res.data.shop_list.length === 0) {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'warning',
            title: 'รบกวนเพิ่มสินค้าลงในรถเข็น'
          })
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        // เปลี่ยนไปใช้ watch
        // this.overlay = false
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 3000,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      }
    },
    goHomepage () {
      this.$router.push('/')
    },
    async onSelectChange (selectedRowKeys) {
      // set sku select
      // console.log('ทดสอบ selectedRowKeys', selectedRowKeys)
      this.selectedRowKeys = selectedRowKeys
      // console.log('product sku select', this.selectedRowKeys)
      if (selectedRowKeys.length === 0) {
        this.selectProduct = false
      } else {
        this.selectProduct = true
      }
      //
      // set shop select
      this.itemsCart.forEach(element => {
        element.product_list.forEach(check1 => {
          selectedRowKeys.forEach(final => {
            if (check1.sku === final) {
              element.selectData.push(check1)
            }
          })
        })
      })
      const setShopNameList = []
      for (let index = 0; index < this.itemsCart.length; index++) {
        const element = this.itemsCart[index]
        if (element.selectData.length !== 0) {
          for (let index = 0; index < element.selectData.length; index++) {
            const element2 = element.selectData[index]
            setShopNameList.push(element2.shop_name)
          }
        }
      }
      const dataShopNameList = [...new Set(setShopNameList)]
      this.shopNameList.data = [...dataShopNameList]
      // console.log('shop name select', this.shopNameList)
      await this.getDetailCart()
    },
    changeQuantitySwal (item, strkey) {
      // const ToastDelete = this.$swal.mixin({
      //   toast: true,
      //   showCancelButton: true,
      //   confirmButtonText: 'ยืนยัน',
      //   cancelButtonText: 'ยกเลิก',
      //   cancelButtonColor: '#d33'
      // })
      if (strkey === 'DELETE') {
        this.checkQuantity = true
        item.quantity = 0
        this.$swal.fire({
          toast: true,
          showCancelButton: true,
          confirmButtonText: 'ยืนยัน',
          cancelButtonText: 'ยกเลิก',
          cancelButtonColor: '#d33',
          icon: 'warning',
          title: 'คุนต้องการลบสินค้าหรือไม่'
        }).then((result) => {
          if (result.isConfirmed) {
            this.updateCartItem(item)
          } else if (result.isDismissed) {
            this.checkQuantity = true
            this.getDetailCart()
          }
        }).catch(() => {
        })
      } else if (strkey === 'UPDATE') {
        if (item.quantity === 0) {
          this.checkQuantity = true
          this.$swal.fire({
            toast: true,
            showCancelButton: true,
            confirmButtonText: 'ยืนยัน',
            cancelButtonText: 'ยกเลิก',
            cancelButtonColor: '#d33',
            icon: 'warning',
            title: 'คุณต้องการลบสินค้าหรือไม่'
          }).then((result) => {
            if (result.isConfirmed) {
              this.updateCartItem(item)
            } else if (result.isDismissed) {
              this.checkQuantity = false
              this.getDetailCart()
            }
          }).catch(() => {
          })
        } else if (parseInt(item.quantity) < 0 || item.quantity === '') {
          item.quantity = 1
          this.checkQuantity = false
        } else {
          this.checkQuantity = false
          this.updateCartItem(item)
        }
      }
    },
    async updateCartItem (val) {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      // this.overlay = true
      var data = {
        seller_shop_id: val.shop_id,
        role_user: dataRole.role,
        sku: val.sku,
        quantity: val.quantity
      }
      // console.log('data in update', data)
      await this.$store.dispatch('ActionUpdateCart', data)
      const res = await this.$store.state.ModuleCart.stateUpdateCart
      if (res.message === 'Update Cart Success') {
        // this.overlay = false
        this.checkQuantity = false
        if (val.quantity === 0) {
          this.shopNameList.data = []
          this.selectedRowKeys = []
          this.$EventBus.$emit('getCartPopOver')
          this.getDetailCart()
        } else {
          this.$EventBus.$emit('getCartPopOver')
          this.getDetailCart()
        }
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'อัปเดตจำนวนสินค้าในรถเข็นเรียบร้อย'
        })
      } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'ใส่ข้อมูลไม่ครบ'
        })
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 1500,
        //   timerProgressBar: true
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR update'
        })
      }
    },
    async nextstep () {
      var dataRole = JSON.parse(localStorage.getItem('roleUser'))
      var data = {
        role_user: dataRole.role,
        shop_to_cal: this.shopNameList.data,
        product_to_cal: this.selectedRowKeys,
        address_id: 1
      }
      // console.log('data before checkout', data)
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData = data
      localStorage.setItem('oneData', Encode.encode(onedata))
      await this.$router.push('/checkout')
    }
  }
}
</script>

<style scoped>
input {
  width: 100%;
  margin: 8px 0;
  box-sizing: border-box;
  border: 1px solid rgb(139, 136, 136);
  background-color: lightblue;
}
.AddNumberProduct {
  height: 25px;
  width: 40px;
  box-shadow: inset 0 1px 3px 0 rgba(232, 232, 232, 0.5);
  background-color: #ffffff;
  text-align: center;
}
.imageshow {
  width: 50px;
  height: 50px;
  cursor: pointer;
}
</style>
