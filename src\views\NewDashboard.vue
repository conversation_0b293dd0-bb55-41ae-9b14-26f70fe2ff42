<template>
  <v-container style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;">
    <v-card width="100%" height="100%" elevation="0">
      <v-card-title class="pl-0" style="font-weight: 700; font-size: 24px; line-height: 32px;"><v-icon color="#27AB9C" class="mr-2" v-if="MobileSize" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> แดชบอร์ด</v-card-title>
      <v-card-text class="pa-0">
        <v-row justify="center" dense class="mb-4">
          <v-col cols="12">
            <DashBoard />
          </v-col>
        </v-row>
      <!--  <v-row justify="center" dense class="mb-4">
          <v-col cols="12">
            <ProductTopTen />
          </v-col>
        </v-row> -->
        <!-- <v-row justify="center" dense class="mb-4">
          <v-col cols="12">
            <UserTopTen />
          </v-col>
        </v-row> -->
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  components: {
    // DashBoard: () => import('@/components/NewDashboard/NewDashboard')
    DashBoard: () => import('@/components/NewDashboard/Dashboard')
    // ProductTopTen: () => import('@/components/NewDashboard/ProductTopTen'),
    // UserTopTen: () => import('@/components/NewDashboard/UserTopTen')
  },
  data () {
    return {
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    // this.$EventBus.$emit('changeNavAdmin')
    this.$EventBus.$emit('changeNav')
    // await this.getDataInDashboard()
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    }
  }
}
</script>
