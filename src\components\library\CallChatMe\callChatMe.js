import axios from 'axios'
import { Decode } from '@/services'
// import '@fortawesome/fontawesome-free/css/all.css'
// import '@fortawesome/fontawesome-free/js/all.js'
// import './chatMe.scss'
// var number = null
// var tempHTML = ''
const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // getAS(oneData.user.current_role_user)
    const auth = {
      headers: {
        Authorization: `Bearer ${oneData.user.access_token}`,
        CacheControl: 'no-cache',
        Pragma: 'no-cache',
        Expires: '0'
      }
    }
    return auth
  }
  // } else {
  //   const text = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6IjQ4MWVmNjNiNjVjNzJlZTJiNDFlZmI3ZGVkNjBhMThlY2QyZmRlNTY0ZTU2OTJjNTQ1Y2UzMDJlOWNkMTVkZmJiMmFlYzNlMWI0YzE3MTBmIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.CbSI5tzoyHNn660uE98AbQQT8cv1-X8sv7gD3zJfuEy510xmdI4Jz_hM8mqFyAQHX25RNfmV7PNdOTLWZCfoanNyN2K0hyMYbzsrFW4vjXwZM5iK_TlpWqX-69e601fuDE7Ui2qJpTushc0IlWviRKUZQvSYk-xUofV1efs3ZL3LduW8r6eFEvD1FpPHaz8YJw4h13jmRWPLDD5hVgFHszzZcmKlGtUQw5q91glH48TGXFOCfh8Fzsyn6KtmAHlPXgd-DxGTqIyF8y2a78_E0_lSSiBFaxfIlhzkZMzG4OfYXg546BbvUgESumPEVHoMsyDZXnav9CfYn0VUG8QRe1e02feiy8OnMIB4Wu87ODWyZVp6VkhRBTq3AZFl_on7j9QzWgJNZCwdFzv9L6yt10qLsxZfHOxMS09bGw31MUOs8IsZNkoyB-ywmhtU6IsWrFixbkc619JKEvl3LPqXO5tU3hijVm1oEq7SLZZjygQjlZ7RotGnb1UwxMG8cPrZKfK9z1_KT4YWxe__z9I1HF6h0ZHdEVTMBzGSoP2dOhymWSAIvt05G0rn-IJb9rHR8p4NPiJ2hzpfymQ-FcHKeRhGu9LrM6yGTHG3Z8b_2Em4c0N8hX77sf6RDRQngkSyR9Nt5gR0mYf4E-J1qo1_LMnYrRMg6BGAtsamCzV8yxc'
  //   const auth = {
  //     headers: { Authorization: `Bearer ${text}` }
  //   }
  //   return auth
  // }
  // const text = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJSUzI1NiIsImp0aSI6ImY5ZmFjYWY2YjIwNTMxY2RjMDY5NjQwZTA1MGQ1ZjhhODQ0Y2NjOWMzNzIyMTc1ZjEzNWI4MzMyMmYwNzEyYjZhMTZhMjg0YmY4NmEyYzAzIn0.***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.cZJ98RI4hMszjS-8u23qAEdsUMY_MHp2X-GPoKg4jYhti04YZ-DlrYexy-QLMP_cM83u1jyf_Wi8BcLrhjJtIRhy91095Dtt4-tO7m84xDxFmMvI2o8x43qazl7v056qHyjyfKJF1qTJgn1OzI65T7v4SOcMb9XD7r7QirpbtgK9CrTiDhFQM5VIKdAgH6yoDYyBqGkAT2Pl3RRgbvqJpfrbfryf8LF-Zr9TqDL3981Kb2m2kT6xI28HGvytesbQem6ZM9PiCr0iFfbORzYnft5hHzYTuImTWyR9L6F7ykKBpexmdDCYJHpbpm61prcK_KC2LpNnQdIUfGayldU4JChN_12D36PLD2gCJLgyF8QcvM7_8FuDiHBuYG2sPepZk2syaFRngrLDDfZl6fdfFHnU_sG5-phdbORhtndRznpHkQGOZx7jR7FX8iy9rrmpbuLhQERiPetbrPTFkqiiOHxJG7nh8cyj2SieslB6LQOcJGJSo7mhVl_xhUtI95DeYQIN6AipvWiZ3wfAHPJ5DPWzla1B76Ud9ZR_t7uskWcra1R3Zxg6gViGBQ2Bdn6KaVyrabPMIV_Uf2dPXmRTI5Q8qHpUAOpwUMi4wfwUGA585KqXBcl-QYHPxP5JmeNnLRGkAE7-1p0oCyKvAjHZbxVTPOnfu45d9x8rQNlhjlU'
  // const auth = {
  //   headers: { Authorization: `Bearer ${text}` }
  // }
  // return auth
}
// const getAS = (val) => {
//   var temp = []
//   for (const [key, value] of Object.entries(val)) {
//     console.log('ASS', key, value)
//     if (value) {
//       temp.push(key)
//     }
//   }
//   return temp
// }
export const createChat = {
  async sharetoken () {
    const auth = await GetToken()
    var response = ''
    // console.log('fffg', auth)
    try {
      // var response = await axios.get('https://one.th/api/v2/service/shared-token?shared-token', auth)
      response = await axios.get('https://testoneid.inet.co.th/api/v2/service/shared-token', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  tack (x) {
    return new Promise(resolve => setTimeout(resolve, x))
  },
  async chatMe (item, status = true) {
    // if (!status) {
    //   localStorage.setItem('shopIdForChat', item.shopNGSID)
    // }
    // const sharetoken = await this.sharetoken()
    // var sharetoken = ''
    // console.log('sharetoken', sharetoken)
    var divChatbot = document.createElement('div')
    document.getElementsByTagName('body')[0].appendChild(divChatbot)
    // var imgminchat = 'https://nexgenshop.co.th/_nuxt/img/icon.950fb42.png' // url ของรูป plugin ที่จะใช้ตอนที่ปิดหน้าแชทลง
    // var sharetoken = 'eyJhbGciOiJSUzI1NiIsImp0aSI6IjM1OGQ0ZWIwYWJiNThhMGRiNGJiZDY4MzFiZmY0MjY2MDM4Y2ZmZjA4OGI4ZThiMGYxM2UxY2VjNDZjMmExMmIxOGQyNzdhYTgwNzY0YjhlIiwia2lkIjoiIiwidHlwIjoiSldUIn0.eyJhdWQiOiI2NjAiLCJleHAiOjE3MTExMDY3OTgsImp0aSI6IjM1OGQ0ZWIwYWJiNThhMGRiNGJiZDY4MzFiZmY0MjY2MDM4Y2ZmZjA4OGI4ZThiMGYxM2UxY2VjNDZjMmExMmIxOGQyNzdhYTgwNzY0YjhlIiwiaWF0IjoxNzExMDk0NjYwLCJuYmYiOjE3MTEwOTQ2NjAsInN1YiI6IjY0MTM0MDUwNDgiLCJzY29wZXMiOlsiQml6IiwidGl0bGUiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwiaWRfY2FyZCIsImJpcnRoX2RhdGUiLCJlbWFpbCIsInRlbCIsInBpYyJdLCJ1c2VybmFtZSI6ImphY2tzb24xOTEifQ.ozM54WkKAxm0XLxboICq33F2tqkYAbX6K5echQMyUBsxlMCx2V9Nvt0JSMz8knf0CuVF3NzAlaVdzibb3NDYm3EHw_ajKoeBS0M7MyOIe_0m-8X1DghbuaOv1Pz9KDylwkOsNX0ieuxmRYxirJ6qvmXVFuUxqyRPPMWs66RAO-lCe9qA_7b-fgYgbZbnfaCkTkr1jw5xa7bArLkLGRQmDPRZK1KfWIvrHJgWuz4Ch9RO9qkGpvyObtawLWzSpjLEjc6mN8wAzcwgrn6sa7IsN35S3YJ8GDYw4vvlSjVYnKnN4YBCJUMTGqJbj6MH5gQJ4qt7ovILKVWai1xNpSt0Ww'
    var link = ''
    var botId = item.botID // bot id ที่ได้จากการสร้างบอท
    var tokenservice = item.botToken
    function encodeToken (data) {
      data = data + randomString(5)
      return reverse(btoa(unescape(data))) + randomString(10)
    }
    function reverse (s) {
      return s.split('').reverse().join('')
    }
    function randomString (num) {
      var text = ''
      var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      for (var i = 0; i < num; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length))
      }
      return text
    }
    // ขนาดของหน้าจอสามารถปรับเช็คตามความเหมาะสมได้
    if (screen.width >= 700) {
      // chat plugin ขนาดสำหรับใช้บนเว็บ
      divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 30px; bottom: 30px;display: none; width: 535px; height: 520px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 25px; width: 535px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <img id='botCloseplugin' src='https://chat-plugin.one.th/web-admin/img/icon_close.png' style='float: right;margin-top: 10px;color:white;margin-right: 10px; width: 20px;'/></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div>"
    } else {
      // chat plugin ขนาดสำหรับใช้บนมือถือ
      divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 0px; bottom: 0px;display: none; width: 100vw; height: 80vh; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 25px; width: 100vw; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <img id='botCloseplugin' src='https://chat-plugin.one.th/web-admin/img/icon_close.png' style='float: right;margin-top: 10px;color:white;margin-right: 10px; width: 20px;'/></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div>"
    }
    link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=auto&bot_id=' + botId + '&tokenservice=' + encodeToken(tokenservice)
    document.getElementById('iframechatbot').src = link
    document.getElementById('botChat').style.display = 'block'
    document.querySelector('body').addEventListener('click', function (e) {
      e.target.matches = e.target.matches || e.target.msMatchesSelector
      if (e.target.matches('#botCloseplugin')) {
        link = ''
        document.getElementById('iframechatbot').src = link
        document.getElementById('botChat').style.display = 'none'
        // document.getElementById('minBotChat').style.display = 'block'
      } else if (e.target.matches('#minBotChatTitle')) {
        // link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=' + botId + '&sharetoken=' + sharetoken
        link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=auto&bot_id=' + botId + '&tokenservice=' + encodeToken(tokenservice)

        // link = "https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=" + bot_id + "&sharetoken=" + sharetoken
        document.getElementById('iframechatbot').src = link
        document.getElementById('botChat').style.display = 'block'
        // document.getElementById('minBotChat').style.display = 'none'
      }
    })
    dragElement(document.getElementById('botChat'))
    function dragElement (elmnt) {
      var pos1 = 0
      var pos2 = 0
      var pos3 = 0
      var pos4 = 0
      if (document.getElementById('botTitleBar')) {
        document.getElementById('botTitleBar').onmousedown = dragMouseDown
      } else {
        elmnt.onmousedown = dragMouseDown
      }
      function dragMouseDown (e) {
        e = e || window.event
        e.preventDefault()
        // get the mouse cursor position at startup:
        pos3 = e.clientX
        pos4 = e.clientY
        document.onmouseup = closeDragElement
        // call a function whenever the cursor moves:
        document.onmousemove = elementDrag
      }

      function elementDrag (e) {
        e = e || window.event
        e.preventDefault()
        // calculate the new cursor position:
        pos1 = pos3 - e.clientX
        pos2 = pos4 - e.clientY
        pos3 = e.clientX
        pos4 = e.clientY
        // set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + 'px'
        elmnt.style.left = (elmnt.offsetLeft - pos1) + 'px'
      }
      function closeDragElement () {
        document.onmouseup = null
        document.onmousemove = null
      }
    }
  },
  async chatMeAdmin () {
    // const sharetoken = await this.sharetoken()
    // console.log('sharetoken', JSON.stringify(sharetoken), sharetoken)
    var divChatbot = document.createElement('div')
    document.getElementsByTagName('body')[0].appendChild(divChatbot)
    var botId = ''
    var tokenservice = ''
    var linkAdmin = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=auto&bot_id=' + botId + '&tokenservice=' + encodeToken(tokenservice)
    var imgAdmin = 'https://s3gw.inet.co.th:8082/e-pro-b2b-uat/shop/27/banner202312052157160.webp?=1712019225872'
    if (screen.width >= 700) {
      divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 30px; bottom: 30px;display: none; width: 535px; height: 520px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 25px; width: 535px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <img id='botCloseplugin' src='https://chat-plugin.one.th/web-admin/img/icon_close.png' style='float: right;margin-top: 10px;color:white;margin-right: 10px; width: 20px;'/></div><iframe id='iframechatbotseller' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + linkAdmin + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 90px; height: 90px; right: 10px; bottom: 15px;'><div id='minBotChatTitle' style='z-index: 16000003;height: 90px; width: 90px; position:fixed; cursor: pointer;'></div><img src='" + imgAdmin + "' style='width: 70px;height: 70px;border-radius: 6px;'/></div>"
    } else {
      divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 0px; bottom: 0px;display: none; width: 100vw; height: 80vh; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 25px; width: 100vw; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <img id='botCloseplugin' src='https://chat-plugin.one.th/web-admin/img/icon_close.png' style='float: right;margin-top: 10px;color:white;margin-right: 10px; width: 20px;'/></div><iframe id='iframechatbotseller' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + linkAdmin + "'></iframe></div><div id='minBotChat' style='border-radius: 10px;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent;position: fixed; z-index: 16000002; width: 70px; height: 70px; right: 0px; bottom: 10px;'><div id='minBotChatTitle' style='z-index: 16000003;height: 70px; width: 70px; position:fixed; cursor: pointer;'></div><img src='" + imgAdmin + "' style='width: 60px; height: 60px;border-radius: 6px;'/></div>"
    }
    function encodeToken (data) {
      data = data + randomString(5)
      return reverse(btoa(unescape(data))) + randomString(10)
    }
    function reverse (s) {
      return s.split('').reverse().join('')
    }
    function randomString (num) {
      var text = ''
      var possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      for (var i = 0; i < num; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length))
      }
      return text
    }
    document.querySelector('body').addEventListener('click', function (e) {
      e.target.matches = e.target.matches || e.target.msMatchesSelector
      if (e.target.matches('#botCloseplugin')) {
        linkAdmin = ''
        document.getElementById('iframechatbotseller').src = linkAdmin
        document.getElementById('botChat').style.display = 'none'
        document.getElementById('minBotChat').style.display = 'block'
      } else if (e.target.matches('#minBotChatTitle')) {
        // link = `https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&botId="${botId}"&sharetoken="${sharetoken}`
        // const urlParams = new URLSearchParams(link)
        window.open(linkAdmin)
        // console.log('linkAdmin', linkAdmin)
        document.getElementById('iframechatbotseller').src = linkAdmin
        document.getElementById('botChat').style.display = 'block'
        document.getElementById('minBotChat').style.display = 'none'
      }
    })
    dragElement(document.getElementById('botChat'))
    function dragElement (elmnt) {
      var pos1 = 0
      var pos2 = 0
      var pos3 = 0
      var pos4 = 0
      if (document.getElementById('botTitleBar')) {
      /* if present, the header is where you move the DIV from: */
        document.getElementById('botTitleBar').onmousedown = dragMouseDown
      } else {
        /* otherwise, move the DIV from anywhere inside the DIV: */
        elmnt.onmousedown = dragMouseDown
      }
      function dragMouseDown (e) {
        e = e || window.event
        e.preventDefault()
        // get the mouse cursor position at startup:
        pos3 = e.clientX
        pos4 = e.clientY
        document.onmouseup = closeDragElement
        // call a function whenever the cursor moves:
        document.onmousemove = elementDrag
      }

      function elementDrag (e) {
        e = e || window.event
        e.preventDefault()
        // calculate the new cursor position:
        pos1 = pos3 - e.clientX
        pos2 = pos4 - e.clientY
        pos3 = e.clientX
        pos4 = e.clientY
        // set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + 'px'
        elmnt.style.left = (elmnt.offsetLeft - pos1) + 'px'
      }
      function closeDragElement () {
        /* stop moving when mouse button is released: */
        document.onmouseup = null
        document.onmousemove = null
      }
    }
  },
  async chatMe2 (item = '') {
    const sharetoken = await this.sharetoken()
    var botId = ''
    var link = ''
    var divChatbot = document.createElement('div')
    document.getElementsByTagName('body')[0].appendChild(divChatbot)
    // var imgminchat = 'https://nexgenshop.co.th/_nuxt/img/icon.950fb42.png' // url ของรูป plugin ที่จะใช้ตอนที่ปิดหน้าแชทลง
    // var sharetoken = 'eyJhbGciOiJSUzI1NiIsImp0aSI6IjM1OGQ0ZWIwYWJiNThhMGRiNGJiZDY4MzFiZmY0MjY2MDM4Y2ZmZjA4OGI4ZThiMGYxM2UxY2VjNDZjMmExMmIxOGQyNzdhYTgwNzY0YjhlIiwia2lkIjoiIiwidHlwIjoiSldUIn0.eyJhdWQiOiI2NjAiLCJleHAiOjE3MTExMDY3OTgsImp0aSI6IjM1OGQ0ZWIwYWJiNThhMGRiNGJiZDY4MzFiZmY0MjY2MDM4Y2ZmZjA4OGI4ZThiMGYxM2UxY2VjNDZjMmExMmIxOGQyNzdhYTgwNzY0YjhlIiwiaWF0IjoxNzExMDk0NjYwLCJuYmYiOjE3MTEwOTQ2NjAsInN1YiI6IjY0MTM0MDUwNDgiLCJzY29wZXMiOlsiQml6IiwidGl0bGUiLCJmaXJzdF9uYW1lIiwibGFzdF9uYW1lIiwiaWRfY2FyZCIsImJpcnRoX2RhdGUiLCJlbWFpbCIsInRlbCIsInBpYyJdLCJ1c2VybmFtZSI6ImphY2tzb24xOTEifQ.ozM54WkKAxm0XLxboICq33F2tqkYAbX6K5echQMyUBsxlMCx2V9Nvt0JSMz8knf0CuVF3NzAlaVdzibb3NDYm3EHw_ajKoeBS0M7MyOIe_0m-8X1DghbuaOv1Pz9KDylwkOsNX0ieuxmRYxirJ6qvmXVFuUxqyRPPMWs66RAO-lCe9qA_7b-fgYgbZbnfaCkTkr1jw5xa7bArLkLGRQmDPRZK1KfWIvrHJgWuz4Ch9RO9qkGpvyObtawLWzSpjLEjc6mN8wAzcwgrn6sa7IsN35S3YJ8GDYw4vvlSjVYnKnN4YBCJUMTGqJbj6MH5gQJ4qt7ovILKVWai1xNpSt0Ww'
    botId = item.botID // bot id ที่ได้จากการสร้างบอท
    link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=' + botId + '&sharetoken=' + sharetoken.data.shared_token
    // ขนาดของหน้าจอสามารถปรับเช็คตามความเหมาะสมได้
    if (screen.width >= 700) {
      // chat plugin ขนาดสำหรับใช้บนเว็บ
      divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 30px; bottom: 30px;display: block; width: 535px; height: 520px; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 25px; width: 535px; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <img id='botCloseplugin' src='https://chat-plugin.one.th/web-admin/img/icon_close.png' style='float: right;margin-top: 10px;color:white;margin-right: 10px; width: 20px;'/></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div>"
    } else {
      // chat plugin ขนาดสำหรับใช้บนมือถือ
      divChatbot.outerHTML = "<div id='botChat' style='border-radius: 10px;position: absolute;margin-top: 0px; margin-right: 0px; margin-bottom: 0px; padding: 0px; border: 0px; background: transparent; overflow: hidden; position: fixed; z-index: 16000004; right: 0px; bottom: 0px;display: block; width: 100vw; height: 80vh; box-shadow: rgba(0, 0, 0, 0.1) 0px 0px 3px 2px;'><div id='botTitleBar' style='z-index: 16000005; height: 25px; width: 100vw; position:fixed; cursor: pointer; border-top-left-radius: 10px; border-top-right-radius: 10px;'> <img id='botCloseplugin' src='https://chat-plugin.one.th/web-admin/img/icon_close.png' style='float: right;margin-top: 10px;color:white;margin-right: 10px; width: 20px;'/></div><iframe id='iframechatbot' frameborder='0' style='background-color: transparent; vertical-align: text-bottom; position: relative; width: 100%; height: 100%; min-width: 100%; min-height: 100%; max-width: 100%; max-height: 100%; margin: 0px; overflow: hidden; display: block;'src='" + link + "'></iframe></div>"
    }
    // document.getElementById('iframechatbot').src = link
    // document.getElementById('botChat').style.display = 'block'
    // document.getElementById('minBotChat').style.display = 'none'
    document.querySelector('body').addEventListener('click', function (e) {
      e.target.matches = e.target.matches || e.target.msMatchesSelector
      if (e.target.matches('#botCloseplugin')) {
        link = ''
        // document.getElementById('iframechatbot').src = link
        // document.getElementById('botChat').style.display = 'none'
        document.getElementById('botChat').remove()
        // document.getElementById('minBotChat').style.display = 'block'
      } else if (e.target.matches('#minBotChatTitle')) {
        link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=' + botId + '&sharetoken=' + sharetoken.data.shared_token
        // link = 'https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=auto&bot_id=' + botId + '&tokenservice=' + sharetoken
        // link = "https://uatchat-plugin.one.th/web-plugin/firstpage-chat/chat_plugin?type=user&bot_id=" + bot_id + "&sharetoken=" + sharetoken
        document.getElementById('iframechatbot').src = link
        document.getElementById('botChat').style.display = 'block'
        // document.getElementById('minBotChat').style.display = 'none'
      }
    })
    // console.log('sharetoken', sharetoken.data.shared_token)
    dragElement(document.getElementById('botChat'))
    function dragElement (elmnt) {
      var pos1 = 0
      var pos2 = 0
      var pos3 = 0
      var pos4 = 0
      if (document.getElementById('botTitleBar')) {
        document.getElementById('botTitleBar').onmousedown = dragMouseDown
      } else {
        elmnt.onmousedown = dragMouseDown
      }
      function dragMouseDown (e) {
        e = e || window.event
        e.preventDefault()
        // get the mouse cursor position at startup:
        pos3 = e.clientX
        pos4 = e.clientY
        document.onmouseup = closeDragElement
        // call a function whenever the cursor moves:
        document.onmousemove = elementDrag
      }

      function elementDrag (e) {
        e = e || window.event
        e.preventDefault()
        // calculate the new cursor position:
        pos1 = pos3 - e.clientX
        pos2 = pos4 - e.clientY
        pos3 = e.clientX
        pos4 = e.clientY
        // set the element's new position:
        elmnt.style.top = (elmnt.offsetTop - pos2) + 'px'
        elmnt.style.left = (elmnt.offsetLeft - pos1) + 'px'
      }
      function closeDragElement () {
        document.onmouseup = null
        document.onmousemove = null
      }
    }
  }
}
