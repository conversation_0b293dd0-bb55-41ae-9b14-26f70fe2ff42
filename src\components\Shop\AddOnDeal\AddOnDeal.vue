<template>
  <v-container :class="MobileSize ? 'background_productMobile my-4' : 'background_product pa-6'" style="background-color: #FFFFFF">
      <v-row>
        <v-col>
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">Add-on Deal</span>
        </v-col>
        <v-col style="display: flex; justify-content: flex-end;">
        <v-btn style="border: 1px solid #27AB9C" outlined rounded @click="createAddOnDeal()"><span style="font-size: 16px; font-weight: 700; color: #27AB9C; ">สร้าง</span></v-btn>
      </v-col>
      </v-row>
      <v-row>
        <v-col>
          <v-data-table :headers="headersMain" :items="dataPrime">
            <!-- <template v-slot:[`item.info`]="{ item }">
            <v-btn icon @click="openDialog1(item)">
              <v-icon>mdi-eye</v-icon>
            </v-btn>
          </template> -->
          </v-data-table>
        </v-col>
      </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      dataPrime: [],
      headersMain: [
        { text: 'ชื่อ Add-on Deal', value: '', width: '', sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทโปรโมชั่น', filterable: false, value: '', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'รายการสินค้า', filterable: false, value: '', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'ระยะเวลา', filterable: false, value: '', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'ดำเนินการ', filterable: false, sortable: false, value: '', align: 'start', class: 'backgroundTable fontTable--text' }
      ],
      seller_shop_id: ''
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
  },
  methods: {
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    createAddOnDeal () {
      if (this.MobileSize) {
        this.$router.push({ path: '/createAddOnDealMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createAddOnDeal' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
</style>
