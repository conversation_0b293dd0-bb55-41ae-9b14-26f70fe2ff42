<template>
  <v-container>
    <v-row >
      <v-col cols="6">
       <h1><B>เพิ่มฝ่ายและแผนก</B></h1>
      </v-col>
    </v-row>
    <v-card class="rounded-lg" >
      <v-container style="margin-left:90px;padding-right:220px">
        <v-row >
          <v-col cols="12" dense>
            <br/><h2>รายละเอียดบริษัท</h2><v-divider></v-divider><br/>
            <v-row>
             <v-col  cols="6" md="3">
              <span>ชื่อบริษัท (ไทย)</span>
             </v-col>
             <v-col  cols="6" md="3">
              <span>{{this.company.name_th}}</span>
             </v-col>
             <v-col  cols="6" md="3">
              <span>ช่วงเวลา</span>
             </v-col>
             <v-col  cols="6" md="3">
              <span>ปี</span>
             </v-col>
             <v-col  cols="6" md="3">
              <span>รหัสบริษัท</span>
             </v-col>
             <v-col cols="6" md="9">
              <span>{{this.company.customer_code}}</span>
             </v-col>
             <v-col cols="7" md="3">
              <span>เป็นบริษัทลูกหรือไม่ ? <font color="red">*</font></span>
             </v-col>
             <v-col cols="5" md="9" dense>
               <v-row dense style="margin-Top:-25px">
                <v-col cols="6" md="1">
                  <v-checkbox :readonly="subsidiary_company === 'Y' ? true : false " dense v-model="subsidiary_company" @click="Checkchildcompany(selected)" label="ใช่"   value="Y" ></v-checkbox>
                </v-col>
                <v-col cols="6" md="1">
                  <v-checkbox :readonly="subsidiary_company === 'N' ? true : false " dense v-model="subsidiary_company" @click="Checkchildcompany(selected)" label="ไม่"   value="N" ></v-checkbox>
                </v-col>
               </v-row>
             </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" v-if="this.company.type_level === 3" dense>
           <h2>เพิ่มฝ่าย </h2><v-divider></v-divider><br/>
            <v-row >
             <v-col cols="3">
              <span>ฝ่าย</span>
             </v-col>
             <v-col cols="9">
              <v-row>
                <v-col cols="6">
                  <v-select v-model="selected" :items="item_selected"   outlined dense ></v-select>
                </v-col>
                <v-col cols="6">
                  <v-btn width="1px" @click="Add_Division()" ><v-icon small> mdi-plus</v-icon></v-btn>
                </v-col>
              </v-row>
             </v-col>
            </v-row>
            <v-row  v-if="selected !== 'เลือกฝ่าย' || add_division">
              <v-col cols="6">
                <span>รหัสฝ่าย</span>
                <v-text-field width="100px" v-model="division_code" background-color="#F6F6F6" disabled outlined dense ></v-text-field>
              </v-col>
              <v-col cols="6">
                <span>ชื่อฝ่าย (ภาษาไทย)</span>
                <v-text-field width="100px" v-model="division_name_th" background-color="#F6F6F6" disabled outlined dense ></v-text-field>
              </v-col>
              <v-col cols="6">
                 <span>ชื่อฝ่าย (ภาษาอังกฤษ)</span>
                 <v-text-field width="100px" v-model="division_name_en" background-color="#F6F6F6" disabled outlined dense ></v-text-field>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" dense>
           <h2>เพิ่มแผนก</h2><v-divider></v-divider><br/>
            <v-row >
             <v-col cols="4" md="3">
              <span>รหัสแผนก<font color="red">*</font></span>
             </v-col>
             <v-col cols="8" md="9">
                <v-text-field v-model="department_code"    outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="3">
              <span>ชื่อแผนก (ภาษาไทย)<font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="9">
                <v-text-field v-model="department_name_th" :rules="Rules.CompanyNameTHRules" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="3">
              <span>ชื่อแผนก (ภาษาอังกฤษ)<font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="9">
                <v-text-field v-model="department_name_en" :rules="Rules.CompanyNameENRules"  outlined dense ></v-text-field>
             </v-col>
             <v-col cols="12" md="12">
              <span>การจัดส่ง</span>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">ชื่อผู้รับ <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="recipient_name" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">รายละเอียดของที่อยู่ <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="shipping_address1" :rules="Rules.address" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">แขวง/เขต หรือ ตำบล/อำเภอ<font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="shipping_address2" :rules="Rules.address2" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">จังหวัด <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="shipping_address3" :rules="Rules.province" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">รหัสไปรษณีย์  <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="shipping_zipcode" :rules="Rules.zipcode"  outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">หมายเลขโทรศัพท์ <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field v-model="shipping_tel" :rules="Rules.tel" maxlength="10" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">หมายเลขมือถือ </span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field v-model="shipping_phone" :rules="Rules.tel"  maxlength="10" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">แฟกซ์ </span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field v-model="shipping_fax"   outlined dense ></v-text-field>
             </v-col>
             <v-col cols="12">
              <span>การเรียกเก็บเงิน</span>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">รายละเอียดของที่อยู่ <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="billing_address1" :rules="Rules.address"  outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">แขวง/เขต หรือ ตำบล/อำเภอ<font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="billing_address2" :rules="Rules.address2" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">จังหวัด <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="billing_address3" :rules="Rules.province" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">รหัสไปรษณีย์  <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field  v-model="billing_zipcode" :rules="Rules.zipcode" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text">หมายเลขโทรศัพท์ <font color="red"> *</font></span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field v-model="billing_tel" maxlength="10" :rules="Rules.tel" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text"> หมายเลขมือถือ</span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field v-model="billing_phone" :rules="Rules.tel" maxlength="10" outlined dense ></v-text-field>
             </v-col>
             <v-col cols="4" md="4">
                <span class="margin-text"> แฟกซ์ </span>
             </v-col>
             <v-col cols="8" md="8">
                <v-text-field v-model="billing_fax"  outlined dense ></v-text-field>
             </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row justify="end" style="margin-Bottom:5px">
         <v-btn width="100px"  class="ma-2" outlined  color="#27AB9C" @click="GoToDepartment()">
          ย้อนกลับ
         </v-btn>
         <v-btn width="100px" class="ma-2"  color="#27AB9C" @click="CreateDepartment()">
            <font color="#FFFFFF"> บันทึก </font>
         </v-btn>
        </v-row>
      </v-container>
    </v-card>
    <v-dialog  v-model="dialog"  width="500">
      <v-card>
        <v-card-title class="text-h5  green accent-1">
            วงเงิน :ปี
        </v-card-title>

        <v-card-text>
          <br/>
          <v-row>
            <v-col cols="2">
              ปี 1
            </v-col>
            <v-col cols="6">
              <v-text-field type="number" placeholder="00.00" outlined dense></v-text-field>
            </v-col>
            <v-col cols="4">
              <font color="red">*วันที่เริ่ม : february</font>
            </v-col>
          </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn  color="green"    @click="dialog = false">ใช้</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog  v-model="dialog_division"  width="500">
      <v-card>
        <v-card-title class="text-h5  green accent-1">
             สร้างฝ่าย
        </v-card-title>

        <v-card-text>
          <br/>
           <v-row >
              <v-col cols="6">
                <span>รหัสฝ่าย</span>
                <v-text-field  width="100px" v-model="division_code_dialog" background-color="#F6F6F6"  outlined dense ></v-text-field>
              </v-col>
              <v-col cols="6">
                <span>ชื่อฝ่าย (ภาษาไทย)</span>
                <v-text-field width="100px" v-model="division_name_th_dialog" background-color="#F6F6F6" outlined dense ></v-text-field>
              </v-col>
              <v-col cols="6">
                 <span>ชื่อฝ่าย (ภาษาอังกฤษ)</span>
                 <v-text-field width="100px" v-model="division_name_en_dialog" background-color="#F6F6F6" outlined dense ></v-text-field>
              </v-col>
            </v-row>
        </v-card-text>
        <v-divider></v-divider>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn  color="green"    @click="dialog_division = false;Add_Division_Dialog()">เพิ่มฝ่าย</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      company: '',
      selected: 'เลือกฝ่าย',
      division_code_dialog: '',
      division_name_th_dialog: '',
      division_name_en_dialog: '',
      item_selected: ['เลือกฝ่าย'],
      add_division: false,
      dialog: false,
      dialog_division: false,
      company_id: '',
      subsidiary_company: '',
      branch: '',
      branch_name: '',
      company_name_th: '',
      company_name_en: '',
      customer_code: '',
      tax_id: '',
      division_code: '',
      division_name_th: '',
      division_name_en: '',
      department_code: '',
      department_name_th: '',
      department_name_en: '',
      recipient_name: '',
      shipping_same_company: 'N',
      shipping_address1: '',
      shipping_address2: '',
      shipping_address3: '',
      shipping_zipcode: '',
      shipping_tel: '',
      shipping_phone: '',
      shipping_fax: '',
      billing_same_company: 'N',
      billing_address1: '',
      billing_address2: '',
      billing_address3: '',
      billing_zipcode: '',
      billing_tel: '',
      billing_phone: '',
      billing_fax: '',
      Rules: {
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        company_code: [
          v => !!v || 'กรุณากรอกรหัสบริษัท'
        ],
        customer_code: [
          v => !!v || 'กรุณากรอกรหัสลูกค้า'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        TexID: [
          v => !!v || 'กรุณากรอกรหัสประจำตัวผู้เสียภาษี'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่'
        ],
        address2: [
          v => !!v || 'กรุณาระบุแขวง/เขต หรือ ตำบล/อำเภอ'
        ],
        CompanyNameTHRules: [
          v => !!v || 'กรุณากรอกชื่อบริษัท(ไทย)',
          v => /^[ก-๏\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        CompanyNameENRules: [
          v => !!v || 'กรุณากรอกชื่อบริษัท(อังกฤษ)',
          v => /^[A-Za-z_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        province: [
          v => !!v || 'กรุณาระบุจังหวัด'
        ],
        zipcode: [
          v => !!v || 'กรุณาระบุรหัสไปรษณีย์'
        ]
      }
    }
  },
  created () {
    this.company_id = JSON.parse(Decode.decode(localStorage.getItem('companyData'))).id
    this.company = JSON.parse(Decode.decode(localStorage.getItem('companyData')))
    // console.log('company', (JSON.parse(Decode.decode(localStorage.getItem('companyData')))))
    this.$EventBus.$emit('changeTitle', 'แผนก')
    this.$EventBus.$emit('changeNavAdminManage')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    GoToDepartment () {
      this.$router.push({ path: '/departmentsCompany' })
    },
    Checkchildcompany (selected) {
      this.selected = selected
    },
    ModalFinancialAmount () {
      this.dialog = true
    },
    Add_Division () {
      this.dialog_division = true
      this.add_division = true
    },
    Add_Division_Dialog () {
      this.division_code = this.division_code_dialog
      this.division_name_th = this.division_name_th_dialog
      this.division_name_en = this.division_name_en_dialog
    },
    async CreateDepartment () {
      var data = {
        company_id: this.company_id.toString(),
        subsidiary_company: this.subsidiary_company,
        branch: '',
        branch_name: '',
        company_name_th: this.company.name_th,
        company_name_en: this.company.name_en,
        customer_code: this.customer_code,
        tax_id: this.company.tax_id,
        division_code: '',
        division_name_th: '',
        division_name_en: '',
        department_code: this.department_code,
        department_name_th: this.department_name_th,
        department_name_en: this.department_name_en,
        recipient_name: this.recipient_name,
        shipping_same_company: this.shipping_same_company,
        shipping_address1: this.shipping_address1,
        shipping_address2: this.shipping_address2,
        shipping_address3: this.shipping_address3,
        shipping_zipcode: this.shipping_zipcode,
        shipping_tel: this.shipping_tel,
        shipping_phone: this.shipping_phone,
        shipping_fax: this.shipping_fax,
        billing_same_company: this.billing_same_company,
        billing_address1: this.billing_address1,
        billing_address2: this.billing_address2,
        billing_address3: this.billing_address3,
        billing_zipcode: this.billing_zipcode,
        billing_tel: this.billing_tel,
        billing_phone: this.billing_phone,
        billing_fax: this.billing_fax
      }
      await this.$store.dispatch('actionsCreateDepartment', data)
      var response = await this.$store.state.ModuleDepartment.stateCreateDepartment
      if (response.result === 'SUCCESS') {
        this.dataDepartment = response.data
        // console.log('CreateDepartment', response)
        await this.$swal.fire({ text: 'สร้างฝ่ายและแผนกสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.$router.push({ path: '/departmentsCompany' })
      } else {
        this.$swal.fire({ text: 'ไม่สามารถสร้างฝ่ายและแผนก', icon: 'error', timer: 2500, showConfirmButton: false })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.margin-text {
    margin-left: 50px;
}
.v-text-field .v-input__control .v-input__slot {
  min-height: 0 !important;
  padding: 0 8px !important;
  margin-bottom: 2px !important;
  display: flex !important;
  align-items: center !important;
}

.v-text-field .v-input__control .v-input__slot .v-input__append-inner {
  margin-top: 5px !important;
}

.v-text-field .v-input__control .v-input__slot label {
  margin-top: -12px !important;
}

.v-text-field .v-input__control .v-input__slot label.v-label--active {
  margin: 0 0 0 5px !important;
}

.v-text-field__details {
  margin: 2px !important;
}
.v-text-field{
      width: 300px;
}
</style>
