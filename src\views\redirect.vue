<template>
  <div>
  </div>
</template>
<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      lastDateLogin: null
    }
  },
  async created () {
    // var data = {
    //   fake_token: this.$router.currentRoute.params.data
    // }
    // var checkLogin = ''
    // if (localStorage.getItem('oneData') !== null) {
    //   checkLogin = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // } else {
    //   checkLogin = ''
    // }
    // var checkLine = ''
    // if (localStorage.getItem('lineUserId') !== null) {
    //   checkLine = localStorage.getItem('lineUserId')
    // } else {
    //   checkLine = null
    // }
    // console.log(checkLine)
    var data = {
      code: await this.$router.currentRoute.query.code
      // lineUserId: checkLine
    }
    var response = ''
    // if (checkLogin.typeLoginOne === 'OneID') {
    response = await this.axios.post(`${process.env.VUE_APP_CALLBACK}`, data)
    // } else {
    //   response = await this.axios.post(`${process.env.VUE_APP_CALLBACK_TEST_ONE}`, data)
    // }
    if (response.data.code !== 500) {
      if (response.data.data.access_token !== '') {
        if (localStorage.getItem('oneData') !== null) {
          // console.log('เข้าบนมี OneData')
          var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
          onedata.user = response.data.data
          localStorage.setItem('oneData', Encode.encode(onedata))
        } else {
          // console.log('เข้าล่างมี onedataShareToken')
          var onedataShareToken = {}
          onedataShareToken.CurrentPath = '/'
          onedataShareToken.user = response.data.data
          localStorage.setItem('oneData', Encode.encode(onedataShareToken))
        }
        var PathRedirect = ''
        if (sessionStorage.getItem('pathRedirect') !== '' && sessionStorage.getItem('pathRedirect') !== undefined) {
          PathRedirect = sessionStorage.getItem('pathRedirect')
          if (PathRedirect === '/Login' || PathRedirect === '/Register') {
            PathRedirect = '/'
          }
        } else {
          PathRedirect = '/'
        }
        var dataRole = {
          role: 'ext_buyer'
        }
        localStorage.setItem('roleUser', JSON.stringify(dataRole))
        this.$EventBus.$emit('checkPDPA', response.data.data.one_id)
        this.updateLastLoginDate()
        if (PathRedirect !== null) {
          this.$router.push({ path: `${PathRedirect}` }).catch(() => {})
        } else {
          this.$router.push({ path: '/' }).catch(() => {})
        }
        await this.$store.dispatch('actionsConGetBizDetail')
        var responseBiz = await this.$store.state.ModuleRegister.stateGetBizDetail
        if (responseBiz.message === 'updated data from one id successful.') {
          localStorage.setItem('BizDeartment', Encode.encode(responseBiz))
        }
      } else {
        localStorage.removeItem('oneData')
        window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
      }
    } else {
      localStorage.removeItem('oneData')
      window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
    }
  },
  methods: {
    updateLastLoginDate () {
      this.lastDateLogin = new Date()
      // console.log('lastDateLogin', this.lastDateLogin, this.lastDateLogin.toISOString())
      localStorage.setItem('lastLoginDate', this.lastDateLogin)
    }
  }
}
</script>
