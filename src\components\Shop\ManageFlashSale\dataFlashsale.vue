<template>
  <v-container grid-list-xs rounded :class="MobileSize ? 'background_color_Mobile pa-0' : 'background_color pa-0'">
    <!-- <v-card v-if="dataFlashSale[0].now.length === 0 || dataFlashSale[0].end.length === 0" class="pa-10">
        <span>ยังไม่มีรายการแฟลชเซลล์</span>
    </v-card> -->
    <!-- <v-card> -->
      <v-col cols="12" md="12" sm="12" class="pa-0">
        <v-col cols="12" align="start" class="pt-0">{{header}} {{dataFlashSale.length}} รายการ</v-col>
          <v-col cols="12" md="12" sm="12" align="end" class="py-0">
            <v-btn v-if="header === 'รายการแฟลชเซลล์ปัจจุบัน'"  class="CartBtn" rounded color="primary" @click="addFlashSale()"> <v-icon color="white" class="IconContainer">mdi-plus</v-icon> <span style="color: white;" class="text">เพิ่มแฟลชเซลล์ใหม่</span></v-btn>
            <v-btn v-else class="CartBtn" rounded color="#27AB9C" @click="exportFlashSale()">  <v-icon color="white" class="IconContainer">mdi-export</v-icon> <span style="color: white;" class="text">Export</span></v-btn>
          </v-col>
          <v-col cols="12" md="12" sm="12" v-if="dataFlashSale.length > 0" class="pa-0">
            <v-col cols="12" no-gutters v-for="(item, index) in dataFlashSale" :key="index">
              <v-card>
                <v-card-title>
                  <v-col cols="10" class="pa-0">
                    <span>วันเวลาเริ่มต้น - สิ้นสุด : {{formatDateToShow(item.start_date)}} น. - {{formatDateToShow(item.end_date)}} น.</span>
                  </v-col>
                  <v-col cols="2" class="pa-0" align="end">
                    <v-switch v-model="statusFlashsale" @change="updateStatusFlashsale(item)" color="#52C41A" inset class="my-0 ml-2 d-inline-block" hide-details></v-switch>
                    <span style="font-weight: 400; font-size: 16px; color: #333333;">เปิด-ปิด</span>
                  </v-col>
                </v-card-title>
                <v-card-subtitle>
                  <v-col cols="12" align="end" class="px-0">
                    <v-btn rounded color="primary" @click="editFlashsale(item)" class="mr-2">แก้ไข</v-btn>
                    <v-btn rounded outlined @click="deleteFlashsale(item)" color="red">ลบ</v-btn>
                  </v-col>
                  <v-col cols="12" class="pa-0">
                    <v-col class="pa-0">
                      <v-img src="https://as2.ftcdn.net/v2/jpg/04/87/46/61/1000_F_487466113_eOM9YdvG8YRjBcWWK1M4zb6lU92fh03Q.jpg"></v-img>
                    </v-col>
                  </v-col>
                  <v-col cols="12">
                    <v-row no-gutters>
                      <v-col cols="12" md="8" sm="9" class="d-flex">
                        <span class="mr-2 my-auto" style="font-weight: 700; font-size: 18px; color: #333333;">{{ item.type === 'starting' || item.type === 'end' ? 'จบภายใน : ' : 'เริ่มภายใน : ' }} </span>
                        <CountdownDateStart v-if="item.type === 'starting' || item.type === 'countdown'" :endDate="countDownDate(item)" page="list"/>
                        <CountdownDateEnd v-else :endDate="countDownDate(item)"/>
                      </v-col>
                      <v-col cols="12" md="4" sm="3" align="end" class="my-auto">
                        <v-btn text class="hide-background-hover px-0" :to="`manageFlashSaleDetail?flashSaleID=${item.id}`" :ripple="false" style="font-weight: 500; font-size: 16px; color: #2A70C3;">
                          ดูทั้งหมด
                          <v-btn style="width: 22px; height: 22px;" x-small fab color="#DBECFA" elevation="0" class="ml-1"><v-icon color="#2A70C3">mdi-chevron-right</v-icon></v-btn>
                        </v-btn>
                      </v-col>
                      <v-col align="start" v-for="(product, index) in item.product_list" :key="index" class="pt-4" :class="IpadSize ? 'cardProductIpad' :IpadProSize ? 'cardProductIpadPro' : 'cardProductDesk'">
                        <CardProducts v-if="item.product_list.length !== 0 && !MobileSize" :itemProduct='product' align="start"/>
                        <CardProductsMobile v-if="item.product_list.length !== 0 && MobileSize" :itemProduct='product' align="start"/>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-card-subtitle>
              </v-card>
            <!-- <CardProducts v-if="!MobileSize" :itemProduct='item' />
            <CardProductsMobile v-else :itemProduct='item' /> -->
            </v-col>
            <v-col cols="12">
              <v-pagination
                color="#27AB9C"
                v-model="pageNumber"
                :length="pageMax"
                circle
                @input="pageChange($event)"
              > </v-pagination>
            </v-col>
          </v-col>
          <v-col v-else align="center">
            <!-- <v-card> -->
              ไม่มีรายการแฟลชเซลล์
            <!-- </v-card> -->
          </v-col>
      </v-col>
      <!-- <v-col cols="12" md="12" sm="12" v-else>
        <v-col cols="12" align="start">{{header}} {{dataFlashSaleInactive.length}} รายการ</v-col>
          <v-col cols="12" md="12" sm="12" align="end">
            <v-btn class="CartBtn" rounded color="blue" @click="exportFlashSale()"> <v-icon class="IconContainer">mdi-export</v-icon> <span class="text">Export</span></v-btn>
          </v-col>
          <v-col cols="12" md="12" sm="12" v-if="dataFlashSaleInactive.length > 0">
              <v-card v-for="(item, index) in dataFlashSaleInactive" :key="index">
              </v-card>
          </v-col>
      </v-col> -->
    <!-- </v-card> -->
  </v-container>
</template>

<script>
// import { msgErr, statusErr } from '@/enum/GetError'
// import { Decode } from '@/services'
export default {
  props: ['propsData', 'header', 'check'],
  components: {
    CountdownDateStart: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CountdownDate'),
    CountdownDateEnd: () => import('@/components/Shop/ManageFlashSale/FlashSaleItem/CountdownDateEnd'),
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsMobile: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      // pageNumber: 1,
      statusFlashsale: false,
      dataFlashSaleActive: [],
      dataFlashSaleInactive: [],
      dataFlashSale: []
    }
  },
  computed: {
    checkWidth () {
      return window.screen.width
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    pageNumber () {
      return this.dataFlashSale
      // get () {
      //   return this.current || 1
      // },
      // set (newPage) {
      //   this.current = newPage
      //   window.scrollTo(0, 0)
      // }
    },
    indexStart () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return (this.current - 1) * pageSize
    },
    indexEnd () {
      var pageSize = this.MobileSize ? 16 : this.IpadSize ? 15 : this.IpadProSize ? 16 : 15
      return this.indexStart + pageSize
    },
    paginated () {
      return this.dataFlashSale
    },
    showNumber () {
      var number = this.IpadProSize ? 4 : this.IpadSize ? 3 : this.MobileSize ? 2 : 5
      return number
    }
  },
  watch: {
    overlay (val) {
      val && setTimeout(() => {
        this.overlay = false
      }, 500)
    }
  },
  mounted () {
    this.$EventBus.$on('changeTab', this.getData)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('changeTab')
    })
  },
  destroy () {
    clearInterval(this.timer)
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    // this.pageNumber = parseInt(this.$route.query.page)
    await this.getData()
  },
  methods: {
    pageChange (val) {
      this.$router.push(`${this.$route.path}?page=${val}`).catch(() => {})
      this.pageNumber = val
      this.$EventBus.$emit('pageChange', val)
      // this.getData(this.header)
      // console.log('val')
      // this.$router.push(`/ListProduct/${this.typeProduct}?page=${val}`).catch(() => {})
    },
    updateStatusFlashsale (item) {
      console.log('updateStatusFlashsale', this.statusFlashsale)
      if (this.statusFlashsale === true) {
        item.status = 'active'
      } else {
        item.status = 'inactive'
      }
      var shopID = localStorage.getItem('shopSellerID')
      var data = {
        seller_shop_id: parseInt(shopID),
        id: item.id,
        status: item.status
      }
      console.log('data', data)
    },
    editFlashsale (item) {
      console.log('editFlashsale', item.id)
    },
    deleteFlashsale (item) {
      console.log('deleteFlashsale', item.id)
    },
    countDownDate (item) {
      if (item.type === 'starting') {
        return Date.parse(item.end_date)
        // return Date.parse('2023-11-1 11:28:00')
      } else {
        return Date.parse(item.start_date)
      }
    },
    addFlashSale () {
      console.log('เพิ่ม====>')
      var id = 2
      this.$router.push({ path: '/manageFlashSaleCreate?flashSaleID=' + id }).catch(() => { })
    },
    exportFlashSale () {
      console.log('export')
    },
    async getData (headerChange) {
      // console.log('yesy', this.propsData)
      this.statusFlashsale = false
      this.dataFlashSaleActive = []
      this.dataFlashSaleInactive = []
      if (this.propsData !== undefined) {
        this.propsData.forEach(e => {
          if (e.status === 'active') {
            this.statusFlashsale = true
            this.dataFlashSaleActive.push(e)
          } else {
            this.statusFlashsale = false
            this.dataFlashSaleInactive.push(e)
          }
          // console.log('e', e.status)
        })
      }
      if (headerChange === undefined) {
        headerChange = this.header
      }
      if (headerChange === 'รายการแฟลชเซลล์ปัจจุบัน') {
        // console.log('q')
        this.dataFlashSale = this.dataFlashSaleActive
      } else {
        // console.log('t')
        this.dataFlashSale = this.dataFlashSaleInactive
      }
      this.pageMax = this.dataFlashSale.length
      console.log('this.this.dataFlashSale', this.dataFlashSale)
      // console.log('this.headerChange', headerChange)
      // var res = this.propsData
      // this.dataFlashSale = res
      // this.dataFlashSaleEnd = res[0].end
      // this.dataFlashSaleNow = res[0].now
    },
    formatDateToShow (data) {
      // console.log('data', data)
      const date = data.split(' ')
      if (!date) return null
      const [year, month, day] = date[0].split('-')
      // const [time] = date[1]
      // console.log('time.split', time.split(':'))
      const yearChange = parseInt(year) + 543
      // const [hour, min, second] = time.split(':')
      // return `${day}/${month}/${yearChange} ${hour}:${min}:${second}`
      return `${day}/${month}/${yearChange} ${date[1]}`
    }
  }
}
</script>
<style scoped>
.CartBtn {
  width: 180px;
  height: 40px;
  border-radius: 12px;
  border: none;
  /* background-color: rgb(255, 208, 0); */
  /* display: flex; */
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition-duration: .5s;
  overflow: hidden;
  box-shadow: 0px 5px 10px rgba(0, 0, 0, 0.103);
  position: relative;
}

.IconContainer {
  position: absolute;
  left: -50px;
  width: 30px;
  height: 30px;
  background-color: transparent;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  z-index: 2;
  transition-duration: .5s;
}

.icon {
  border-radius: 1px;
}

.text {
  height: 100%;
  width: fit-content;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgb(17, 17, 17);
  z-index: 1;
  transition-duration: .5s;
  font-size: 1.04em;
  font-weight: 600;
}

.CartBtn:hover .IconContainer {
  transform: translateX(40px);
  border-radius: 40px;
  transition-duration: .5s;
}

.CartBtn:hover .text {
  transform: translate(10px,0px);
  transition-duration: .5s;
}

.CartBtn:active {
  transform: scale(0.95);
  transition-duration: .5s;
}
</style>
