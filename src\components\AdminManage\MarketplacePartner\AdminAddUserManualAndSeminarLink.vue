<template>
  <v-container class="pa-4">
    <v-card width="100%" height="100%" elevation="0" :class="[ MobileSize ? 'mb-12 mt-4 px-0' : 'mb-4' ]">
      <v-col>
        <span v-if="MobileSize" class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">
          <v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการเอกสารคู่มือการใช้งานและสัมมนา</span>
        <span v-else class="f-left" style="line-height: 26px; color: #333333; font-weight: 700; font-size: 24px;">จัดการเอกสารคู่มือการใช้งานและสัมมนา</span>
      </v-col>

      <v-card-text class="px-2">
        <v-row>
          <v-col cols="12">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead" v-if="this.DataUserManualDetail.length === 0">อัปโหลดเอกสารคู่มือการใช้งาน</span>
                <span class="textManageShopHead" v-if="this.DataUserManualDetail.length !== 0">เอกสารคู่มือการใช้งาน</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <div v-if="this.DataUserManualDetail.length === 0">
                  <v-col cols="12" class="pa-0">
                    <v-card width="100%" height="100%" outlined elevation="0" @click="onPickFile()">
                      <v-card-text style="text-align: center;" class="px-2">
                        <input type="file" ref="filePDF" @change="handleFileUpload($event)" style="display: none;" accept=".pdf">
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-0">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="100" height="100" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center">
                            <v-col cols="12" md="4" style="text-align: center;">
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </div>

                <div v-if="this.DataUserManualDetail.length !== 0">
                  <v-col cols="12" md="12" sm="12" class="pa-0" v-for="(item, index) in DataUserManualDetail" :key="index">
                    <v-card outlined width="100%" height="80%" @click="openLink(item.path)" class="mt-2">
                      <v-row dense justify="end">
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click.stop="DeleteUserManual(item)">mdi-close</v-icon>
                        </v-btn>
                      </v-row>
                      <v-card-text>
                        <v-row dense>
                          <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                          </v-img>
                          <span style="text-align: center; align-content: center;" class="text-truncate pt-2">{{ item.name }}</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </div>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead" v-if="this.DataLinkSeminarDetail.length === 0">บันทึกลิงก์ในการเข้าร่วมสัมมนา</span>
                <span class="textManageShopHead" v-if="this.DataLinkSeminarDetail.length !== 0">ลิงก์ในการเข้าร่วมสัมมนา</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <div v-if="this.DataLinkSeminarDetail.length === 0">
                  <v-col cols="12" class="pa-0">
                    <v-text-field
                      dense
                      outlined
                      v-model="seminarLink"
                      label="ใส่ลิงก์ในการเข้าร่วมสัมมนา"
                      placeholder="https://example.com"
                      append-icon="mdi-content-save"
                      :rules="[validateURL]"
                      @click:append="saveSeminarLink()"
                    ></v-text-field>
                  </v-col>
                </div>

                <div v-if="this.DataLinkSeminarDetail.length !== 0">
                  <v-col cols="12" class="pa-0" v-for="(item, index) in DataLinkSeminarDetail" :key="index">
                    <v-row dense justify="end">
                      <v-btn
                        icon
                        x-small
                        style="float: right; background-color: #ff5252; z-index: 1;"
                        @click.stop="DeleteLinkSeminar(item)"
                      >
                        <v-icon x-small color="white">mdi-close</v-icon>
                      </v-btn>
                    </v-row>
                    <v-text-field
                      readonly
                      solo
                      dense
                      hide-details
                      v-model="item.links"
                      @click="openLinkSeminar(item.links)"
                      placeholder="ลิงก์ในการเข้าร่วมสัมมนา"
                    ></v-text-field>
                  </v-col>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>

    </v-card>

    <v-dialog v-model="dialogDelete" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDelete = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 16px;"><b>ลบเอกสารคู่มือการใช้งาน</b></span><br>
            <span>คุณยืนยันจะลบเอกสารคู่มือการใช้งาน ใช่ไหม</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogDelete = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="confirmDelete()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <v-dialog v-model="dialogDeleteLinkSeminar" max-width="350px">
      <v-card style="border-radius: 35px;">
        <v-btn icon class="ml-auto" @click="dialogDeleteLinkSeminar = false" style="position: absolute; top: 10px; right: 10px;">
          <v-icon color="grey">mdi-close</v-icon>
        </v-btn>
        <v-card-title class="justify-center" style="border-radius: 35px 35px 0 0; background: #fffef5">
          <v-img
            src="@/assets/Marketplace_partner/Group2.png"
            contain
            max-width="150"
          ></v-img>
        </v-card-title>
        <br>
        <v-card-text style="padding-bottom: 0px;">
          <div class="text-center mb-2">
            <span style="font-size: 16px;"><b>ลบลิงก์ในการเข้าร่วมสัมมนา</b></span><br>
            <span>คุณยืนยันจะลบลิงก์ในการเข้าร่วมสัมมนา ใช่ไหม</span>
          </div>
        </v-card-text>
        <v-card-actions class="justify-center">
          <v-btn rounded class="ma-1" outlined color="#27AB9C" style="border-color: #27AB9C; color: #27AB9C; width: 100px;" @click="dialogDeleteLinkSeminar = false">ยกเลิก</v-btn>
          <v-btn rounded color="#27AB9C" class="ma-1" style="width: 100px; color: white;" @click="confirmDeleteLinkSeminar()">ยืนยัน</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dialogDelete: false,
      dialogDeleteLinkSeminar: false,
      deleteCode: '',
      deleteId: '',
      seminarLink: '',
      DataUserManualDetail: [],
      DataLinkSeminarDetail: [],
      onechatToken: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminAddUserManualAndSeminarLinkMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'AdminAddUserManualAndSeminarLink')
        this.$router.push({ path: '/AdminAddUserManualAndSeminarLink' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user !== undefined) {
        this.isLogin = true
        if (oneData.user.shared_token !== undefined && oneData.user.shared_token !== null && oneData.user.shared_token !== '') {
          this.onechatToken = oneData.user.shared_token
        } else {
          this.onechatToken = ''
        }
      } else {
        localStorage.removeItem('oneData')
      }
    }
    await this.UserManualDetail()
    await this.LinkSeminarDetail()
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async UserManualDetail () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAdminUserManualDetail')
      const response = await this.$store.state.ModuleAdminPanit.stateAdminUserManualDetail
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DataUserManualDetail = response.data
        // console.log(this.DataUserManualDetail)
      }
    },
    openLink (link) {
      window.open(link, '_blank')
    },
    openLinkSeminar (link) {
      // var sharetoken = this.onechatToken
      // window.open(link + '?share_token=' + sharetoken, '_blank')
      window.open(link, '_blank')
    },
    onPickFile () {
      this.$refs.filePDF.click()
    },
    handleFileUpload (event) {
      this.$store.commit('openLoader')
      const file = event.target.files[0]

      if (file) {
        const reader = new FileReader()
        reader.readAsDataURL(file)
        reader.onload = () => {
          this.uploadPDF(file)
          this.$refs.filePDF.value = ''
        }
      }
    },
    async uploadPDF (file) {
      const formData = new FormData()
      const maxLength = 60

      if (file.name.length <= maxLength) {
        formData.append('name', file.name)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'info',
          text: `ชื่อไฟล์ยาวเกิน ${maxLength} ตัวอักษร`,
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        return
      }

      formData.append('file', file)

      await this.$store.dispatch('actionsAdminAddUserManual', formData)
      const response = await this.$store.state.ModuleAdminPanit.stateAdminAddUserManual

      if (response.message === 'File uploaded successfully') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'อัปโหลดเอกสารคู่มือการใช้งานสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        this.UserManualDetail()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    DeleteUserManual (item) {
      this.dialogDelete = true
      this.deleteCode = item.code
    },
    async confirmDelete () {
      this.$store.commit('openLoader')
      this.dialogDelete = false
      var data = {
        manual_code: this.deleteCode
      }
      await this.$store.dispatch('actionsAdminDeleteUserManual', data)
      const response = await this.$store.state.ModuleAdminPanit.stateAdminUserManualDetail
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'ลบเอกสารคู่มือการใช้งานสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        await this.UserManualDetail()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500,
          icon: 'error',
          text: `${response.message}`
        })
      }
    },
    async LinkSeminarDetail () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAdminLinkSeminar')
      const response = await this.$store.state.ModuleAdminPanit.stateAdminLinkSeminar
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DataLinkSeminarDetail = response.data
        // console.log(this.DataLinkSeminarDetail)
      }
    },
    validateURL (value) {
      const pattern = /^(https?:\/\/)?([\w-]+\.)+[\w-]{2,}(\/\S*)?$/
      return pattern.test(value) || 'กรุณาใส่ลิงก์ที่ถูกต้อง'
    },
    async saveSeminarLink () {
      if (this.validateURL(this.seminarLink) === true) {
        var data = {
          link: this.seminarLink
        }
        await this.$store.dispatch('actionsAdminAddLinkSeminar', data)
        const response = await this.$store.state.ModuleAdminPanit.stateAdminAddLinkSeminar
        if (response.result === 'SUCCESS') {
          this.$swal.fire({
            icon: 'success',
            text: 'บันทึกลิงก์ในการเข้าร่วมสัมมนาสำเร็จ',
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 1500
          })
          this.seminarLink = ''
          await this.LinkSeminarDetail()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timerProgressBar: true,
            timer: 2500,
            icon: 'error',
            text: `${response.message}`
          })
        }
      } else {
        this.$swal.fire({
          icon: 'info',
          text: 'กรุณาใส่ลิงก์ที่ถูกต้องก่อนบันทึก',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
      }
    },
    DeleteLinkSeminar (item) {
      this.dialogDeleteLinkSeminar = true
      this.deleteId = item.id
    },
    async confirmDeleteLinkSeminar () {
      this.$store.commit('openLoader')
      this.dialogDeleteLinkSeminar = false
      var data = {
        link_id: this.deleteId,
        status: 'inactive'
      }
      await this.$store.dispatch('actionsAdminDeleteLinkSeminar', data)
      const response = await this.$store.state.ModuleAdminPanit.stateAdminDeleteLinkSeminar
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'ลบลิงก์ในการเข้าร่วมสัมมนาสำเร็จ',
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 1500
        })
        await this.LinkSeminarDetail()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timerProgressBar: true,
          timer: 2500,
          icon: 'error',
          text: `${response.message}`
        })
      }
    }
  }
}
</script>

<style scoped>

</style>
