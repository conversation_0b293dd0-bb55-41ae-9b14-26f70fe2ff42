<template>
  <v-container grid-list-xs class="pr-5">
    <draggable class="dragArea" tag="ul" :list="dataList" :group="{ name: 'category' }" @change="handleMove" :animation="300" :fallbackOnBody="true" :forceFallback="true">
      <li v-for="el in dataList" :key="el.id">
        <div class="d-flex align-baseline">
          <p class="mr-1" style="margin-top: -5vw;">{{ el.name }}</p>
          <v-btn fab x-small color="#27AB9C" text @click="openDialogEdit(el)">
            <v-icon>mdi-square-edit-outline</v-icon>
          </v-btn>
          <v-btn fab x-small color="red" text @click="openDialogDelete(el)">
            <v-icon>mdi-delete-outline</v-icon>
          </v-btn>
        </div>
        <CategoryProduct :dataList="el.children" :parentCategory="el" />
      </li>
    </draggable>
    <v-dialog persistent content-class="elevation-0" v-model="dialogEditCategory" :width="MobileSize ? '100%' : IpadSize ? '86%' : IpadProSize ? '68%' : '46%'">
      <v-card width="100%" style="background: #FFFFFF; border-radius: 20px;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>แก้ไขหมวดหมู่สินค้า</b></span>
            </v-col>
            <v-btn fab small @click="cancelDialogEdit()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>
        <v-card-title>
          <v-tabs
            @change="handleMenuChange()"
            v-model="tabEdit"
          >
            <v-tabs-slider color="#27ac9c"></v-tabs-slider>

            <v-tab
              v-for="item in items"
              :key="item"
            >
              {{ item }}
            </v-tab>
          </v-tabs>
        </v-card-title>
        <v-form ref="editName" v-if="tabEdit === 0">
            <v-card-text class="pt-4">
            <span>ชื่อหมวดหมู่สินค้าเดิม</span>
            <v-text-field
              v-model="oldName"
              outlined
              dense
              disabled
            >
            </v-text-field>
          </v-card-text>
          <v-card-text :style="MobileSize ? 'margin-top: -7vw' : 'margin-top: -3vw'">
            <span>ชื่อหมวดหมู่สินค้าใหม่</span>
            <v-text-field
              v-model="newName"
              outlined
              dense
              placeholder="ระบุชื่อหมวดหมู่สินค้าใหม่"
              :rules="Rules.fixedKey"
              :validate-on-blur="true"
            >
            </v-text-field>
          </v-card-text>
          <v-card-text style="margin-top: -2vw">
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelDialogEdit()">ยกเลิก</v-btn>
              <v-btn :disabled="newName === ''" :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="dialogAwaitEdit = true">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-form>
        <v-form ref="editPosition" v-if="tabEdit === 1">
            <v-card-text class="pt-4">
            <span>ชื่อหมวดหมู่</span>
            <v-text-field
              v-model="oldName"
              outlined
              dense
              disabled
            >
            </v-text-field>
          </v-card-text>
          <v-card-text :style="MobileSize ? 'margin-top: -7vw' : 'margin-top: -3vw'">
            <span>เลือกหมวดหมู่ที่ต้องการย้าย</span>
            <treeselect
              :value="selectCategory || null"
              :options="dataEdit"
              :normalizer="normalizer"
              placeholder="เลือกหมวดหมู่"
              style="z-index: 999;"
              :append-to-body="true"
              @input="updateCategory"
            ></treeselect>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelDialogEdit()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="dialogAwaitChange = true">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-form>
        <v-form ref="editProduct" v-if="tabEdit === 2">
          <v-card-text class="pt-4">
          <span>หมวดหมู่สินค้า</span>
          <v-text-field
            v-model="oldName"
            outlined
            dense
            disabled
          >
          </v-text-field>
          </v-card-text>
          <v-card-text :style="MobileSize ? 'margin-top: -10vw;' : IpadSize || IpadProSize ? 'margin-top: -4vw;' : 'margin-top: -3vw;'">
            <span>เลือกรายกาารสินค้า</span>
          </v-card-text>
          <v-card-text :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -2vw;'">
            <v-text-field
            v-model="search"
            dense
            outlined
            placeholder="ค้นหาจากชื่อสินค้า, รหัส SKU"
            @change="searchProduct(search)"
            >
              <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
            </v-text-field>
          </v-card-text>
          <v-card-text v-if="detailProduct.length !== 0" :style="MobileSize ? 'margin-top: -5vw;' : 'margin-top: -1vw;'">
            <div class="d-flex flex-wrap " :class="MobileSize ? 'justify-center' : ''" style="gap: 1vw;">
              <v-col cols="12" class="d-flex pa-0" style="margin-top: -1vw;">
                <v-checkbox v-model="selectAll" @change="(val) => toggleSelectAll(val)"></v-checkbox>
                <span>เลือกทั้งหมด</span>
              </v-col>
              <v-card class="pa-3 d-flex flex-column mb-1" :width="MobileSize ? '120' : '150'" style="border-radius: .5vw; margin-top: -1vw;" v-for="(items, index) in detailProduct" :key="index">
                <v-checkbox
                  :input-value="isSelected(items.product_id)"
                  @change="toggleSelection(items.product_id, index)"
                ></v-checkbox>
                <div class="d-flex justify-center" style="height: 5vw;">
                  <v-avatar :size="MobileSize ? 35 : IpadSize ? 40 : 60" tile v-if="items.product_image !== null && items.product_image !== ''" width="80">
                    <img :src="items.product_image" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 1vw;">
                  </v-avatar>
                  <v-avatar :size="MobileSize ? 35 : IpadSize ? 40 : 60" tile v-else width="80">
                    <img src="@/assets/NoImage.png" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                  </v-avatar>
                </div>
                <v-tooltip top>
                  <template v-slot:activator="{ on, attrs }">
                    <span :class="MobileSize ? 'mt-5' : ''" v-bind="attrs" v-on="on"> {{ substring(items.product_name) }}</span>
                  </template>
                  <span>{{ items.product_name }}</span>
                </v-tooltip>
                <!-- <span style="height: 3vw;" :class="MobileSize ? 'mt-7' : IpadProSize ? 'mt-5' : ''">{{substring(items.product_name)}}</span> -->
                <span style="color: #7c7e81;" :class="MobileSize ? 'mt-2' : ''"><b>฿ {{Number(items.product_price).toLocaleString(undefined, {minimumFractionDigits: 2})}}</b></span>
              </v-card>
            </div>
            <div v-if="maxPage > 1" class="d-flex justify-center mt-2">
              <v-btn color="#27AB9C" fab small class="mr-1" style="color: #fff;" :disabled="option.page === 1" @click="previousPage()">
                <v-icon>mdi-arrow-left-bold-circle-outline</v-icon>
              </v-btn>
              <v-btn color="#27AB9C" fab small style="color: #fff;">{{option.page}}</v-btn>
              <v-btn color="#27AB9C" fab small class="ml-1" style="color: #fff;" @click="nextPage()" :disabled="option.page*option.offset > countProduct || detailProduct.length < 20">
                <v-icon>mdi-arrow-right-bold-circle-outline</v-icon>
              </v-btn>
            </div>
          </v-card-text>
          <v-card-text v-else :style="MobileSize ? 'margin-top: -10vw;' : 'margin-top: -3vw;'">
            <v-card :style="MobileSize ? 'margin-top: -1vw' : ''">
              <v-card-text class="d-flex align-center flex-column" style="padding: 3vw;">
                <v-img
                  src="@/assets/newarticle1.png"
                  width="350"
                  height="150"
                  contain
                  style="opacity: 80%;"
                ></v-img>
                <span class="mt-4" style="font-size: medium;'">ไม่พบสินค้าที่ค้นหา</span>
              </v-card-text>
            </v-card>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogAddProduct()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="opendialogAwaitAdd()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <!-- dialog chage category -->
    <v-dialog persistent content-class="elevation-0" v-model="dialogChangePosition" :width="MobileSize || IpadSize ? '100%' : IpadProSize ? '50%' : '30%'">
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="cancelMove()"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขตำแหน่งหมวดหมู่สินค้า</b></p>
            <span v-if="categoryMove !== ''" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะย้ายหมวดหมู่ <b>{{oldName}}</b> <br>ไปหมวดหมู่ <b>{{categoryMove}}</b> ใช่ไหม</span>
            <span v-else style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะย้ายหมวดหมู่ <b>{{oldName}}</b> <br>ไปหมวดหมู่นอกสุดใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelMove()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editCategory()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog confirm edit name -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitEdit" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="closeDialogAwaitEdit"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขชื่อหมวดหมู่สินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะแก้ไขชื่อหมวดหมู่สินค้าใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitEdit = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editCategory()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog confirm change position -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitChange" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogAwaitChange = false"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เปลี่ยนตำแหน่งหมวดหมู่สินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะเปลี่ยนตำแหน่งหมวดหมู่สินค้า</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitChange = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editCategory()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog confirm delete -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitDelete" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogAwaitDelete = false"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/delete.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ลบหมวดหมู่สินค้า</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะลบหมวดหมู่ <b>{{nameDelete}}</b> ใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitDelete = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="deleteCategory()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- dialog edit product -->
    <v-dialog persistent content-class="elevation-0" v-model="dialogEditProduct" :width="MobileSize || IpadSize ? '100%' : IpadProSize ? '50%' : '47%'">
      <v-card width="100%" style="background: #FFFFFF; border-radius: 20px;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายการสินค้าในหมวดหมู่</b></span>
            </v-col>
            <v-btn small @click="dialogEditProduct = false" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>
        <v-card-text class="mt-3">
          <div class="d-flex flex-wrap" :class="MobileSize ? 'justify-center' : ''" style="gap: 1vw;" v-if="listProduct.length !== 0">
            <v-card class="pa-3 d-flex flex-column" :width="MobileSize ? '120' : '150'" style="border-radius: .5vw;" v-for="(items, index) in listProduct" :key="index">
              <div>
                <v-btn fab small style="top: -.5vw; left: 6vw;" color="#27ab9c" feb text @click="openDialogMoveProduct(items)"><v-icon>mdi-playlist-edit</v-icon></v-btn>
              </div>
              <div class="d-flex justify-center" style="height: 5vw; margin-top: -1vw;">
                <v-avatar :size="MobileSize ? 35 : IpadSize ? 40 : 60" tile v-if="items.media_path !== null && items.media_path !== ''" width="80">
                  <img :src="items.media_path" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 1vw;">
                </v-avatar>
                <v-avatar :size="MobileSize ? 35 : IpadSize ? 40 : 60" tile v-else width="80">
                  <img src="@/assets/NoImage.png" alt="shippingImage" style="width: 100%; height: 100%; object-fit: contain; border-radius: 5vw;">
                </v-avatar>
              </div>
              <span style="height: 3vw;" :class="MobileSize ? 'mt-7' : IpadProSize ? 'mt-5' : ''">{{substring(items.name)}}</span>
              <span style="color: #7c7e81;" :class="MobileSize ? 'mt-8' : ''"><b>฿ {{Number(items.min_price).toLocaleString(undefined, {minimumFractionDigits: 2})}}</b></span>
            </v-card>
          </div>
          <div v-else>
            <v-card :style="MobileSize ? 'margin-top: -1vw' : ''">
              <v-card-text class="d-flex align-center flex-column" style="padding: 3vw;">
                <v-img
                  src="@/assets/newarticle1.png"
                  width="500"
                  height="300"
                  contain
                  style="opacity: 80%;"
                ></v-img>
                <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">ไม่มีสินค้าในหมวดหมู่นี้</span>
                <div class="d-flex align-baseline">
                  <span class="mt-4 mr-2" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">ต้องการเพิ่มสินค้า กดปุ่ม </span>
                  <v-btn @click="openDialogEdit()" style="color: #fff; border-radius: 10vw;" color="#27AB9C">เพิ่มสินค้า</v-btn>
                  <span class="mt-4 ml-2" :style="MobileSize ? 'font-size: medium;' : 'font-size: large;'">ที่นี่</span>
                </div>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
        <!-- <v-card-text>
          <v-row dense justify="center">
            <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogEditProduct = false">ยกเลิก</v-btn>
            <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="dialogAwaitEditProduct = true">ตกลง</v-btn>
          </v-row>
        </v-card-text> -->
      </v-card>
    </v-dialog>
    <!-- dialog select category remove -->
    <v-dialog persistent content-class="elevation-0" v-model="dialogMoveProduct" :width="MobileSize || IpadSize ? '100%' : IpadProSize ? '50%' : '30%'">
      <v-card width="100%" style="background: #FFFFFF; border-radius: 20px;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
              <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายการสินค้าในหมวดหมู่</b></span>
            </v-col>
            <v-btn small @click="cancelMoveProduct()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>
        <v-card-text class="pt-4">
          <span>ชื่อสินค้า</span>
          <v-text-field
            v-model="nameProduct"
            outlined
            dense
            disabled
          >
          </v-text-field>
        </v-card-text>
        <v-card-text :style="MobileSize ? 'margin-top: -7vw' : 'margin-top: -2vw'">
          <span>เลือกหมวดหมู่ที่ต้องการย้าย</span>
          <treeselect
            :value="selectForMoveProduct || null"
            :options="dataEdit"
            :normalizer="normalizerMove"
            placeholder="เลือกหมวดหมู่"
            style="z-index: 999;"
            :append-to-body="true"
          ></treeselect>
        </v-card-text>
        <v-card-text>
          <v-row dense justify="center">
            <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="cancelMoveProduct()">ยกเลิก</v-btn>
            <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="dialogAwaitMove = true">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- dialog confirm change position -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitEditProduct" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogAwaitEditProduct = false"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>เปลี่ยนตำแหน่งสินค้าในหมวดหมู่</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะเปลี่ยนตำแหน่งสินค้า <b>{{nameProduct}}</b> ใช่หรือไม่ </span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitEditProduct = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editProduct()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- diaog confirm add product -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitAdd" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogAwaitAdd = false"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขสินค้าในหมวดหมู่</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะแก้ไขสินค้าในหมวดหมู่ใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitAdd = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editProduct()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- diaog confirm move product -->
    <v-dialog content-class="elevation-0" v-model="dialogAwaitMove" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 20px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
          style="position: relative;"
        >
        <v-toolbar-title></v-toolbar-title>
        <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogAwaitMove = false"
        >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-col cols="12" class="d-flex justify-center">
            <img
            height="200px"
            :src="require('@/assets/ImageINET-Marketplace/ICONShop/noFlashsale.png')"
            />
        </v-col>
        <v-container>
          <v-card-text style="text-align: center; margin-top: -2vw">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>ย้ายสินค้าในหมวดหมู่</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณยืนยันจะย้าย <b>{{nameProduct}}</b> ในหมวดหมู่ใช่ไหม</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogAwaitMove = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="removeProduct()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import draggable from 'vuedraggable'
import Treeselect from '@riophae/vue-treeselect'
import '@riophae/vue-treeselect/dist/vue-treeselect.css'
export default {
  name: 'CategoryProduct',
  props: {
    dataList: {
      type: Array
      // disableDrag: Boolean
    },
    parentCategory: Object
  },
  components: {
    draggable,
    Treeselect
  },
  data () {
    return {
      dialogEditCategory: false,
      dialogAwaitEdit: false,
      dialogAwaitDelete: false,
      dialogAwaitChange: false,
      dialogChangePosition: false,
      dialogEditProduct: false,
      dialogAwaitEditProduct: false,
      dialogAwaitMove: false,
      dialogMoveProduct: '',
      oldName: '',
      newName: '',
      idDelete: '',
      idEdit: '',
      hierachyEdit: '',
      indexEdit: '',
      idShowProduct: '',
      tabEdit: null,
      items: ['แก้ไขชื่อหมวดหมู่สินค้า', 'เปลี่ยนตำแหน่งหมวดหมู่', 'แก้ไขรายการสินค้า'],
      selectCategory: '',
      selectForEditProduct: null,
      selectForMoveProduct: null,
      categoryMove: '',
      idMove: '',
      indexMove: '',
      nameDelete: '',
      listProduct: [],
      productForMove: [],
      oldProduct: [],
      listProductMove: [],
      idPreview: '',
      // dataEdit: [],
      normalizer (node) {
        const id = 'hierachy'
        const labelKey = 'name'
        const childrenKey = 'children'
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey] && node[childrenKey].length ? node[childrenKey] : undefined
        }
      },
      normalizerMove (node) {
        const id = 'id'
        const labelKey = 'name'
        const childrenKey = 'children'
        return {
          id: node[id],
          label: node[labelKey],
          children: node[childrenKey] && node[childrenKey].length ? node[childrenKey] : undefined
        }
      },
      selectedCategoryId: null,
      idEditProduct: '',
      detailProduct: [],
      nameProduct: '',
      countProduct: '',
      option: {
        page: 1,
        offset: 20
      },
      selectCategoryEdit: '',
      selectedItem: [],
      dialogAwaitAdd: false,
      hierarchyDepth: null,
      countHierachy: null,
      Rules: {
        fixedKey: [v => !!v || 'กรุณากรอกข้อมูล'],
        selectCategory: [v => !!v || 'กรุณาเลือกหมวดหมู่']
      },
      search: '',
      selectAll: false,
      maxPage: null,
      totalProduct: null
    }
  },
  watch: {
    selectCategory (val) {
      // console.log(val, '*****')
      this.hierachyEdit = val
      this.countHierachy = this.hierachyEdit.split('_').length
      // console.log(this.countHierachy, '*****')
    },
    hierachyEdit (val) {
      // console.log(val, 'hierachyEdit')
    },
    tabEdit (val) {
      // console.log(val, 888)
    },
    selectForMoveProduct (val) {
      // console.log(val, 5656)
    },
    selectAll (val) {
      // console.log(val, 5656)
    },
    // search (newVal) {
    //   this.option.page = 1
    //   this.valueSearch = newVal
    //   setTimeout(() => {
    //     this.getDetailProduct(this.option.offset, this.valueSearch)
    //   }, 2000)
    // },
    selectedItem (val) {
      if ((val.length === this.countProduct && val.length !== 0) || (this.detailProduct.length === this.totalProduct && val.length === this.totalProduct)) {
        this.selectAll = true
      } else {
        this.selectAll = false
      }
      // console.log(val)
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    dataEdit () {
      return this.$store.state.ModuleShop.stateListGategory &&
           this.$store.state.ModuleShop.stateListGategory.data &&
           this.$store.state.ModuleShop.stateListGategory.data.product_group_data
        ? this.$store.state.ModuleShop.stateListGategory.data.product_group_data
        : []
    }
    // checkBtn () {
    //   if (this.option.offset === 20) {
    //     return this.detailProduct.length > 20
    //   } else {
    //     return this.detailProduct.length !== this.countProduct
    //   }
    // }
  },
  mounted () {
    if (!this.dataEdit.length) {
      this.$store.dispatch('actionListGategory', { seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')) })
    }
  },
  methods: {
    searchProduct (newVal) {
      this.option.page = 1
      this.valueSearch = newVal
      this.getDetailProduct(this.option.offset, this.valueSearch)
    },
    updateCategory (value) {
      this.selectCategory = value
    },
    async handleMove (event) {
      // if (event.added) {
      //   const movedItem = event.added.element
      //   console.log(`ย้าย ${movedItem.name} ไปยัง ${this.parentCategory && this.parentCategory.hierachy ? this.parentCategory.hierachy : 'root'}`)
      // }
      if (event.added || event.moved) {
        const movedElement = event.added ? event.added.element : event.moved.element
        const filteredList = this.dataList.filter(item => item.id !== movedElement.id)
        const newParent = this.findParentById(this.dataEdit, filteredList)
        if (this.parentCategory !== undefined) {
          this.movedItem = {
            movedId: movedElement.id,
            newParentId: this.parentCategory ? this.parentCategory.hierachy : '',
            nameParent: this.parentCategory ? this.parentCategory.name : '',
            indexMove: this.parentCategory ? this.parentCategory.index : 999
          }
        } else {
          this.movedItem = {
            movedId: movedElement.id,
            newParentId: newParent ? newParent.hierachy : '',
            nameParent: newParent ? newParent.name : '',
            indexMove: newParent ? newParent.index_group : 999
          }
        }
        // console.log(this.parentCategory)
        this.oldName = movedElement.name
        this.hierarchyDepth = this.countHierarchyLevels(event.added ? event.added.element : event.moved ? event.moved.element : null)
        // console.log(this.hierarchyDepth, 'hierarchyDepth')
        this.categoryMove = this.movedItem.nameParent
        this.idMove = this.movedItem.movedId
        // หมวดหม่ที่จะย้ายไป
        this.hierachyEdit = this.movedItem.newParentId
        this.countHierachy = this.hierachyEdit.split('_').length
        // console.log(this.hierachyEdit, 'hierachyEdit')
        // console.log(this.countHierachy, 'countHierachy')
        // console.log(this.countHierachy, 'see countHierachy')
        this.dialogChangePosition = true
      }
    },
    findParentById (list, filteredList) {
      for (const item of list) {
        // ถ้ามีลูกให้ค้นหาจากลูกก่อน
        if (item.children) {
          const foundChild = item.children.find(child => filteredList.some(fItem => fItem.id === child.id))
          if (foundChild) return item
          const found = this.findParentById(item.children, filteredList)
          if (found) return found
        }
        // ถ้าไม่มีลูกให้คืนค่าหมวดแม่นี้เลย
        if (!item.children && filteredList.some(fItem => fItem.id === item.id)) {
          return item
        }
      }
      return null
    },
    cancelMove () {
      this.dialogChangePosition = false
      this.$root.$emit('updateCategory')
    },
    cancelDialogEdit () {
      this.tabEdit = null
      this.newName = ''
      this.dialogEditCategory = false
      this.tabEdit = 0
      this.option.page = 1
      this.selectCategory = ''
      this.selectedItem = []
      this.hierarchyDepth = null
      this.countHierachy = null
      this.search = ''
      this.selectAll = false
    },
    countHierarchyLevels (category) {
      if (!category.children || category.children.length === 0 || category.children === null || category.children === undefined) {
        return 0 // ไม่มีหมวดหมู่ย่อยเลย
      }
      return 1 + Math.max(...category.children.map(child => this.countHierarchyLevels(child)))
    },
    openDialogEdit (item) {
      if (this.dialogEditProduct === true) {
        this.tabEdit = 2
        this.getDetailProduct()
        this.getListProduct(this.idEdit)
      }
      this.dialogEditCategory = true
      this.oldName = item.name
      this.idEdit = item.id
      this.hierachyEdit = item.hierachy.split('_').slice(0, -1).join('_')
      // console.log('see hierachyEdit', item.hierachy)
      this.indexEdit = item.index
      this.hierarchyDepth = this.countHierarchyLevels(item)
      // console.log(`จำนวนขั้นของหมวดหมู่ย่อยทั้งหมด: ${this.hierarchyDepth}`)
    },
    openDialogDelete (item) {
      this.dialogAwaitDelete = true
      this.idDelete = item.id
      this.nameDelete = item.name
    },
    closeDialogAwaitEdit () {
      this.dialogAwaitEdit = false
      this.dialogEditCategory = false
    },
    openDialogPreview (item) {
      this.dialogEditProduct = true
      this.idPreview = item.id
      // ดึงค่าเพื่อที่จะคลิกไปยังหน้าเพิ่มสินค้า
      this.oldName = item.name
      this.idEdit = item.id
      this.hierachyEdit = item.hierachy.split('_').slice(0, -1).join('_')
      this.indexEdit = item.index
      this.getListProduct(this.idPreview)
    },
    openDialogMoveProduct (item) {
      this.dialogEditProduct = false
      this.dialogMoveProduct = true
      this.nameProduct = item.name
      this.productForMove = [{ product_id: item.product_id, index: 999 }]
      this.listProductMove = this.productForMove
    },
    cancelMoveProduct () {
      this.dialogMoveProduct = false
      this.dialogEditProduct = true
    },
    // openDialogEditProduct (item, list) {
    //   this.dialogEditProduct = true
    //   this.nameProduct = list.name
    //   this.idProduct = list.product_id
    // },
    // edit category
    async editCategory () {
      this.$store.commit('openLoader')
      // console.log(this.idEdit, this.hierachyEdit.split('_')[0])
      if (this.hierarchyDepth === 3) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `ไม่สามารถย้ายหมวดหมู่นี้ได้ <br> <span style="font-size: 14px;">เนื่องจากหมวดหมู่ <b>${this.oldName}</b> มีหมวดหมู่ย่อย 3 ขั้น</span>`
        })
        this.dialogAwaitChange = false
        this.dialogChangePosition = false
        this.hierarchyDepth = null
        this.countHierachy = null
        this.$root.$emit('updateCategory')
        this.$store.commit('closeLoader')
      } else if (this.hierarchyDepth === 2 && this.countHierachy > 1) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `ไม่สามารถย้ายหมวดหมู่นี้ได้ <br> <span style="font-size: 14px;">เนื่องจากหมวดหมู่ <b>${this.oldName}</b> มีหมวดหมู่ย่อย 2 ขั้น <br>ย้ายไปหมวดหมู่ที่อยู่ในขั้นที่มากกว่า 2 ไม่ได้</span>`
        })
        this.dialogAwaitChange = false
        this.dialogChangePosition = false
        this.hierarchyDepth = null
        this.countHierachy = null
        this.$root.$emit('updateCategory')
        this.$store.commit('closeLoader')
      } else if (this.hierarchyDepth === 1 && this.countHierachy > 2) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `ไม่สามารถย้ายหมวดหมู่นี้ได้ <br> <span style="font-size: 14px;">เนื่องจากหมวดหมู่ <b>${this.oldName}</b> มีหมวดหมู่ย่อย 1 ขั้น <br>ย้ายไปหมวดหมู่ที่อยู่ในขั้นที่มากกว่า 3 ไม่ได้</span>`
        })
        this.dialogAwaitChange = false
        this.dialogChangePosition = false
        this.hierarchyDepth = null
        this.countHierachy = null
        this.$root.$emit('updateCategory')
        this.$store.commit('closeLoader')
      } else if (this.hierarchyDepth === 0 && this.countHierachy > 3) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'warning',
          html: `ไม่สามารถย้ายหมวดหมู่นี้ได้ <br> <span style="font-size: 14px;">เนื่องจากหมวดหมู่ <b>${this.oldName}</b> <br>ย้ายไปหมวดหมู่ที่อยู่ในขั้นที่มากกว่า 4 ไม่ได้</span>`
        })
        this.dialogAwaitChange = false
        this.dialogChangePosition = false
        this.hierarchyDepth = null
        this.countHierachy = null
        this.$root.$emit('updateCategory')
        this.$store.commit('closeLoader')
      } else {
        var data = {
          seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')),
          product_group_id: this.dialogAwaitEdit || this.dialogAwaitChange ? this.idEdit : this.idMove,
          hierachy: this.selectCategory === '' ? '' : this.hierachyEdit,
          name: this.dialogAwaitEdit || this.dialogAwaitChange ? (this.tabEdit === 0 ? this.newName : this.oldName) : this.oldName,
          index: this.selectCategory === '' ? '' : this.dialogAwaitEdit || this.dialogAwaitChange ? (this.tabEdit === 0 ? this.indexEdit : 999) : this.indexMove
        }
        await this.$store.dispatch('actionEditCategory', data)
        var res = this.$store.state.ModuleShop.stateEditCategory
        if (res.code === 200) {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1000,
            timerProgressBar: true,
            icon: 'success',
            text: 'แก้ไขหมวดหมู่สินค้าสำเร็จ'
          })
          this.dialogAwaitEdit = false
          this.dialogEditCategory = false
          this.dialogAwaitChange = false
          this.dialogChangePosition = false
          this.selectForMoveProduct = ''
          this.newName = ''
          this.hierarchyDepth = null
          this.countHierachy = null
          this.$root.$emit('updateCategory')
          this.$store.commit('closeLoader')
        } else if (res.message === 'Cant Assign To Self.') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'warning',
            html: 'กรุณาเลือกหมวดหมู่ใหม่ <br> <span style="font-size: 12px;">(ไม่สามารถเลือกหมวดหมู่เดิม และไม่สามารถย้ายหมวดหมู่หลัก ไปหมวดหมู่ย่อยได้)</span>'
          })
          this.dialogAwaitChange = false
          this.dialogChangePosition = false
          this.hierarchyDepth = null
          this.countHierachy = null
          this.$root.$emit('updateCategory')
          this.$store.commit('closeLoader')
        } else if (res.message === 'Limit Children 3.') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'warning',
            html: 'กรุณาเลือกหมวดหมู่ใหม่ <br> <span style="font-size: 14px;">(ไม่สามารถเลือกหมวดหมู่ที่อยู่ขั้นที่ 3 ของหมวดหมู่หลักได้)</span>'
          })
          this.dialogAwaitChange = false
          this.dialogChangePosition = false
          this.hierarchyDepth = null
          this.countHierachy = null
          this.$root.$emit('updateCategory')
          this.$store.commit('closeLoader')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
          this.dialogAwaitChange = false
          this.dialogChangePosition = false
          this.hierarchyDepth = null
          this.countHierachy = null
          this.$root.$emit('updateCategory')
          this.$store.commit('closeLoader')
        }
      }
    },
    // delete category
    async deleteCategory () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')),
        product_group_id: this.idDelete
      }
      await this.$store.dispatch('actionDeleteCategory', data)
      var res = this.$store.state.ModuleShop.stateDeleteCategory
      if (res.code === 200) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
          icon: 'success',
          text: 'ลบหมวดหมู่สินค้าสำเร็จ'
        })
        this.dialogAwaitDelete = false
        this.$root.$emit('updateCategory')
        // this.disableDrag = true
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    // get product
    async getListProduct (id) {
      this.$store.commit('openLoader')
      this.selectedCategoryId = id
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')),
        product_group_id: id
      }
      await this.$store.dispatch('actionGetListProduct', data)
      var res = this.$store.state.ModuleShop.stateGetListProduct
      if (res.code === 200) {
        this.listProduct = res.data.list_product
        this.oldProduct = this.listProduct
        if (this.selectedItem.length === 0) {
          this.selectedItem = this.listProduct.map((item) => ({
            product_id: item.product_id,
            index: item.index
          }))
        }
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    handleMenuChange () {
      if (this.tabEdit === 2) {
        this.getDetailProduct()
        this.getListProduct(this.idEdit)
      } else {
        this.$store.commit('openLoader')
        setTimeout(() => {
          this.$store.commit('closeLoader')
        }, 500)
      }
    },
    async removeProduct () {
      await this.editProduct()
      this.listProductMove = (this.oldProduct.filter(product => !this.productForMove.some(p => p.product_id === product.product_id)))
      this.selectForMoveProduct = this.idPreview
      this.editProduct()
    },
    async editProduct () {
      var list = this.listProductMove
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')),
        product_group_id: this.dialogEditCategory === true ? this.idEdit : this.selectForMoveProduct,
        list_product: this.dialogEditCategory === true ? this.selectedItem : list
      }
      await this.$store.dispatch('actionEditProduct', data)
      var res = await this.$store.state.ModuleShop.stateEditProduct
      if (res.code === 200) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
          icon: 'success',
          text: 'แก้ไขรายการสินค้าสำเร็จ'
        })
        this.dialogAwaitAdd = false
        this.dialogEditCategory = false
        this.tabEdit = 0
        this.option.page = 1
        this.dialogMoveProduct = false
        this.dialogAwaitMove = false
        this.selectedItem = []
        this.selectCategory = ''
        this.search = ''
        this.valueSearch = ''
        this.selectAll = false
        this.getListProduct(this.idEdit)
        this.$root.$emit('updateCategory')
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    async getDetailProduct (offset, search) {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')),
        page: this.option.page,
        offset: offset === -1 ? offset : this.option.offset,
        search: search,
        product_type: '',
        message_status: '',
        stock_status: ''
      }
      await this.$store.dispatch('actionGetDetailProduct', data)
      var res = await this.$store.state.ModuleShop.stateGetDetailProduct
      if (res.code === 200) {
        this.detailProduct = res.data.list_product
        this.countProduct = res.data.total_products_count
        this.maxPage = res.data.max_page
        this.totalProduct = res.data.total_list_product
        if (this.selectedItem.length === this.countProduct && this.selectedItem.length !== 0) {
          this.selectAll = true
        } else {
          this.selectAll = false
        }
        // console.log(this.countProduct, 'sss')
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.$store.commit('closeLoader')
      }
    },
    nextPage () {
      // this.selectedItem = []
      this.option.page = this.option.page + 1
      var offset = this.option.offset
      this.getDetailProduct(offset, this.valueSearch)
    },
    previousPage () {
      this.option.page = this.option.page - 1
      var offset = this.option.offset
      this.getDetailProduct(offset, this.valueSearch)
    },
    async toggleSelectAll (val) {
      this.option.page = 1
      if (this.search === '') {
        var offset = -1
        await this.getDetailProduct(offset)
      } else {
        await this.getDetailProduct(this.option.offset, this.valueSearch)
      }
      if (val) {
        this.selectedItem = await this.detailProduct.map((item, index) => ({
          product_id: item.product_id,
          index
        }))
      } else {
        this.selectedItem = []
        // this.selectAll = false
      }
    },
    toggleSelection (id, index) {
      const foundIndex = this.selectedItem.findIndex(item => item.product_id === id)

      if (foundIndex === -1) {
        this.selectedItem.push({ product_id: id, index })
      } else {
        this.selectedItem.splice(foundIndex, 1)
      }

      // อัปเดตสถานะของ selectAll ให้ตรงกับ selectedItem
      this.selectAll = this.selectedItem.length === this.detailProduct.length
    },
    isSelected (id) {
      return this.selectedItem.some(item => item.product_id === id)
    },
    substring (data) {
      return data.length > 15 ? data.substring(0, 15) + '...' : data
    },
    closeDialogAddProduct () {
      this.dialogEditCategory = false
      this.tabEdit = 0
      this.option.page = 1
      this.selectedItem = []
      this.search = ''
      this.selectAll = false
      // if (this.selectedItem.length > 0) {
      //   this.selectedItem = this.listProduct.map((item) => ({
      //     product_id: item.product_id,
      //     index: item.index
      //   }))
      // }
    },
    opendialogAwaitAdd () {
      this.dialogAddProduct = false
      this.dialogAwaitAdd = true
    }
  }
}
</script>

<style>

</style>
<style scoped>
::v-deep .v-slide-group.v-item-group > .v-slide-group__prev,
::v-deep .v-slide-group.v-item-group > .v-slide-group__next {
  display: none !important;
}

.v-input--selection-controls {
  margin-top: 0 !important;
  padding-top: 0 !important;
}

.dragArea {
  /* min-height: 40px;
  outline: 1px solid #a0a0a0;
  border-radius: .5vw; */
}
::v-deep .dragArea li {
  cursor: pointer;
}
/* ::v-deep .v-slide-group {
  height:  !important;
} */
</style>
