<template>
  <v-container>
    <v-row dense justify="center">
      <v-col cols="12">
        <v-row class="mb-2 ml-1">
          <v-col cols="6" align="start" class="pl-0">
            <span style="font-weight: bold; font-size: 28px;">ผู้ใช้งาน</span>
          </v-col>
          <v-col cols="6" align="end">
            <v-btn class="ml-2" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createUser()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มผู้ใช้งาน</v-btn>
          </v-col>
        </v-row>
      </v-col>
      <!-- รายละเอียดผู้ใช้งาน -->
      <v-col cols="12" class="mt-2">
        <v-card elevation="0" width="100%" height="100%">
          <v-toolbar elevation="0" color="#E6F5F3">
            <v-row>
              <v-col cols="8" align="start" class="mt-3 text-title">รายละเอียดผู้ใช้งาน</v-col>
              <v-col cols="4" align="end">
                <v-btn icon color="#27AB9C" @click="editUser()" dark><v-icon>mdi-pencil-box-multiple</v-icon></v-btn>
              </v-col>
            </v-row>
          </v-toolbar>
          <v-card-text>
            <v-form ref="form" v-model="valid" lazy-validation>
              <v-row justify="start" dense class="ma-2">
                <!-- ชื่อ -->
                <v-col cols="3">
                  <span><b>ชื่อ</b></span>
                </v-col>
                <v-col cols="5">
                  <span>{{userData.userName}}</span>
                </v-col>
                <v-col cols="4"></v-col>
                <!-- อีเมล -->
                <v-col cols="3">
                  <span><b>อีเมล</b></span>
                </v-col>
                <v-col cols="5">
                  <span>{{userData.email}}</span>
                </v-col>
                <v-col cols="4"></v-col>
                <!-- การยืนยันอีเมล -->
                <v-col cols="3">
                  <span><b>การยืนยันอีเมล</b></span>
                </v-col>
                <v-col cols="5">
                  <v-icon v-if="userData.verifyEmail === true" color="#27ab9c">mdi-check-circle</v-icon>
                  <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                </v-col>
                <v-col cols="4"></v-col>
                <!-- หมายเลขโทรศัพท์ -->
                <v-col cols="3">
                  <span><b>หมายเลขโทรศัพท์</b></span>
                </v-col>
                <v-col cols="5">
                  <span>{{userData.tel}}</span>
                </v-col>
                <v-col cols="4"></v-col>
                <!-- สิทธิ์การเข้าใช้งาน -->
                <v-col cols="3">
                  <span><b>สิทธิ์การเข้าใช้งาน</b></span>
                </v-col>
                <v-col cols="5">
                  <v-row>
                    <v-col>ผู้ดูแลระบบ</v-col>
                    <v-col>
                      <v-icon v-if="permission.admin === true" color="#27ab9c">mdi-check-circle</v-icon>
                      <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col>ผู้ช่วยผู้ดูแลระบบ</v-col>
                    <v-col>
                      <v-icon v-if="permission.adminAssistant === true" color="#27ab9c">mdi-check-circle</v-icon>
                      <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                    </v-col>
                  </v-row>
                  <v-row>
                    <v-col>ผู้อนุมัติ</v-col>
                    <v-col>
                      <v-icon v-if="permission.approver === true" color="#27ab9c">mdi-check-circle</v-icon>
                      <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                    </v-col>
                  </v-row>
                  <v-row >
                    <v-col>ผู้สั่งซื้อ</v-col>
                    <v-col>
                      <v-icon v-if="permission.buyer === true" color="#27ab9c">mdi-check-circle</v-icon>
                      <v-icon v-else color="#E57373">mdi-close-circle</v-icon>
                    </v-col>
                  </v-row>
                </v-col>
                <v-col cols="4"></v-col>
              </v-row>
            </v-form>
          </v-card-text>
        </v-card>
      </v-col>
      <!-- ตั้งค่าผู้ซื้อ -->
      <v-col cols="12" class="mt-2">
        <v-card elevation="0" width="100%" height="100%">
          <v-toolbar elevation="0" color="#E6F5F3">
            <v-row>
              <v-col cols="8" align="start" class="mt-3 text-title">ตั้งค่าผู้ซื้อ</v-col>
              <v-col cols="4" align="end">
                <v-btn icon color="#27AB9C" @click="createSettingUser()" dark><v-icon>mdi-cog</v-icon></v-btn>
              </v-col>
            </v-row>
          </v-toolbar>
        </v-card>
      </v-col>
      <!-- แก้ไขการตั้งค่า -->
      <v-col cols="12" class="mt-2">
        <v-card elevation="0" width="100%" height="100%">
            <v-expansion-panels elevation="0" flat>
              <v-expansion-panel>
                <v-expansion-panel-header color="#E6F5F3">
                  <v-row>
                    <v-col cols="8" align="start" class="mt-3 text-title">ชื่อแผนก</v-col>
                    <v-col cols="4" align="end">
                      <v-btn icon color="#27AB9C" @click="editSettingUser()" dark><v-icon>mdi-pencil-box-multiple</v-icon></v-btn>
                    </v-col>
                  </v-row>
                </v-expansion-panel-header>
                <v-expansion-panel-content>
                  <v-row justify="start" dense class="ma-2 pt-3">
                    <!-- ประเภทผู้อนุมัติ -->
                    <v-col cols="3">
                      <span><b>ประเภทผู้อนุมัติ</b></span>
                    </v-col>
                    <v-col cols="5">
                      <span>ไม่มีผู้อนุมัติ</span>
                    </v-col>
                    <v-col cols="4"></v-col>
                  </v-row>
                </v-expansion-panel-content>
              </v-expansion-panel>
            </v-expansion-panels>
        </v-card>
      </v-col>
      <!-- ปุ่มย้อนกลับ -->
      <v-col cols="12" class="mt-3 mb-3">
        <v-row justify="end" dense>
          <v-btn text color="#27AB9C" @click="backToUserPage()" class="mr-2">ย้อนกลับ</v-btn>
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
// import Address2021 from '@/Thailand_Address/address2021'
export default {
  data () {
    return {
      dialog: false,
      stepper: 1,
      valid: true,
      valid1: true,
      checkbox: [],
      firstnameTH: '',
      firstnameEN: '',
      lastnameTH: '',
      lastnameEN: '',
      countDate: '',
      tel: '',
      email: '',
      createByEmail: '',
      backpage: '',
      Rules: {
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์'
        ],
        nameTHRules: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[ก-๏\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาไทย'
        ],
        nameENRules: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[A-Za-z_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ'
        ],
        emailRule: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[a-zA-Z0-9._-]+@[a-zA-Z]+([.]?[a-zA-Z])*(\.[a-zA-Z]{2,3})+$/.test(v) || v === '' || 'กรุณากรอกอีเมลให้ถูกต้อง'
        ]
      },
      userData: [],
      permission: '',
      data: [
        {
          id: 1,
          userID: '1',
          userName: 'ทดสอบ ผู้ใช้งาน',
          email: '<EMAIL>',
          permission: { admin: true, adminAssistant: false, approver: false, buyer: true },
          verifyEmail: true,
          detail: {
            userName: 'ทดสอบ ผู้ใช้งาน',
            email: '<EMAIL>',
            verifyEmail: true,
            tel: '0888233335',
            permission: { admin: true, adminAssistant: false, approver: false, buyer: true },
            userBuyerSetting: { department: '', approverType: '', approver: '' },
            userEmployeetSetting: { department: '', approverType: '', approver: '' }
          },
          userSetting: true,
          userStatus: true
        }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  created () {
    this.getUserDetail()
    this.$EventBus.$emit('changeTitle', 'ผู้ใช้งาน')
    this.$EventBus.$emit('changeNavAdminManage')
  },
  methods: {
    getUserDetail () {
      this.userData = this.data[0].detail
      this.permission = { admin: this.userData.permission.admin, adminAssistant: this.userData.permission.adminAssistant, approver: this.userData.permission.approver, buyer: this.userData.permission.buyer }
    },
    createUser () {
      localStorage.setItem('backToPage', 'detailUserCompany')
      this.$router.push({ path: '/manageUserCompany?Status=Create' }).catch(() => {})
    },
    editUser () {
      localStorage.setItem('backToPage', 'detailUserCompany')
      this.$router.push({ path: '/manageUserCompany?Status=Edit' }).catch(() => {})
    },
    createSettingUser () {
      this.$router.push({ path: '/settingUserCompany?Status=Create' }).catch(() => {})
    },
    editSettingUser () {
      this.$router.push({ path: '/settingUserCompany?Status=Edit' }).catch(() => {})
    },
    saveAddUser () {
      // console.log('Save Add User')
    },
    backToUserPage () {
      this.$router.push({ path: '/usersCompany' }).catch(() => {})
    },
    closeDialog () {
      this.dialog = !this.dialog
    }
  }
}
</script>

<style scoped>
.text-title {
  font-size: 16px;
  color: #27AB9C;
  font-weight: bold;
}
</style>
