import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('ONEDATA NAAAa', oneData)
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async GetOrderList () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END2}admin/order/all_order`, auth)
      // console.log('List Order Approve ', response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async FindOrderList (val) {
    const auth = await GetToken()
    try {
      const response = await axios.post(`${process.env.VUE_APP_BACK_END2}admin/order/find_order`, val, auth)
      if (response && response.data) {
        return response.data
      } else {
        throw new Error('Response or response.data is undefined')
      }
    } catch (error) {
      console.error('Error in FindOrderList:', error)
      return { error: error.message || 'Unknown error' }
    }
  },
  async EditOrderList (val) {
    const auth = await GetToken()
    try {
      const response = await axios.post(`${process.env.VUE_APP_BACK_END2}admin/order/update_order`, val, auth)
      if (response && response.data) {
        return response.data
      } else {
        throw new Error('Response or response.data is undefined')
      }
    } catch (error) {
      console.error('Error in EditOrderList:', error)
      return { error: error.message || 'Unknown error' }
    }
  }
}
