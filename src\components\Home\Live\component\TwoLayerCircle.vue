<template>
  <div>
    <div class="live-circle">
      <v-progress-circular
        :size="size"
        :value="value"
        :color="color"
      >
        <img :src="imgURL" v-if="imgURL" alt="Inner Circle Image" class="inner-image">
        <img :src="require('@/assets/ImageINET-Marketplace/Shop/Store.png')" v-else alt="Inner Circle Image" class="inner-image">
      </v-progress-circular>
      <span v-if="live" :class="smallSize ? 'live-text-small' : 'live-text'">LIVE</span>
    </div>
    <span v-if="name" :class="smallSize ? 'live-shop-name-small': 'live-shop-name'">{{ name }}</span>
  </div>
</template>

<script>
export default {
  props: {
    size: {
      type: Number,
      default: 130
    },
    color: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 60
    },
    value: {
      type: Number,
      default: 100
    },
    name: {
      type: String
    },
    imgURL: {
      type: String
    },
    live: {
      type: Boolean,
      default: false
    },
    smallSize: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style scoped>
.live-circle {
  position: relative;
  display: inline-block;
  text-align: center; /* จัดข้อความให้อยู่ตรงกลาง */
}
.live-text {
  position: absolute; /* กำหนดตำแหน่งแบบ absolute */
  bottom: 0; /* วางไว้ด้านล่าง */
  left: 50%; /* จัดให้อยู่ตรงกลางแนวนอน */
  transform: translateX(-50%); /* ชดเชยการจัดตรงกลาง */
  font-weight: bold; /* ทำให้ข้อความหนา */
  color: white; /* เปลี่ยนสีข้อความเป็นสีแดง */
  background-color: #27AB9C; /* เพิ่มพื้นหลังสีขาว */
  padding: 2px 5px; /* เพิ่ม padding เล็กน้อย */
  border-radius: 5px; /* ทำให้ขอบมน */
  font-size: 12px;
}
.live-text-small {
  position: absolute; /* กำหนดตำแหน่งแบบ absolute */
  bottom: 0; /* วางไว้ด้านล่าง */
  left: 50%; /* จัดให้อยู่ตรงกลางแนวนอน */
  transform: translateX(-50%); /* ชดเชยการจัดตรงกลาง */
  font-weight: bold; /* ทำให้ข้อความหนา */
  color: white; /* เปลี่ยนสีข้อความเป็นสีแดง */
  background-color: #27AB9C; /* เพิ่มพื้นหลังสีขาว */
  padding: 2px 5px; /* เพิ่ม padding เล็กน้อย */
  border-radius: 5px; /* ทำให้ขอบมน */
  font-size: 8px;
}
.inner-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 10px); /* Adjust size as needed */
  height: calc(100% - 10px); /* Adjust size as needed */
  border-radius: 50%; /* Make the image circular */
  object-fit: cover; /* Prevent image distortion */
  padding: 2px;
}
.live-shop-name {
  display: block; /* แสดงข้อความอยู่ใต้ block */
  margin-top: 10px; /* ปรับระยะห่างจากวงกลม */
  font-weight: bold; /* ทำให้ข้อความหนา */
  color: white; /* เปลี่ยนสีข้อความเป็นสีแดง */
}
.live-shop-name-small {
  display: block; /* แสดงข้อความอยู่ใต้ block */
  margin-top: 10px; /* ปรับระยะห่างจากวงกลม */
  font-weight: bold; /* ทำให้ข้อความหนา */
  color: white; /* เปลี่ยนสีข้อความเป็นสีแดง */
}
</style>
