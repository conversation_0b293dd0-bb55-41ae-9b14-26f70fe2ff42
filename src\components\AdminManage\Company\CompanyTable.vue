<template>
  <v-container>
    <v-row dense no-gutters justify="end">
      <v-col cols="12" md="4" sm="4" xs="12">
        <v-text-field
          v-model="search"
          rounded
          dense
          outlined
          placeholder="ค้นหาบริษัท"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify </v-icon>
        </v-text-field>
      </v-col>
    </v-row>
    <v-row v-if="disableTable === true">
      <v-col cols="12" class="py-0">
        <a-tabs @change="SelectDetailCompany">
          <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
          <a-tab-pane :key="0"
            ><span slot="tab"
              >กำลังใช้งาน
              <a-tag color="#27AB9C" style="border-radius: 8px;">{{
                countActiveCompany
              }}</a-tag></span
            ></a-tab-pane
          >
          <a-tab-pane :key="1"
            ><span slot="tab"
              >ยกเลิก
              <a-tag color="#f50" style="border-radius: 8px;">{{
                countInactiveCompany
              }}</a-tag></span
            ></a-tab-pane
          >
        </a-tabs>
      </v-col>
      <v-col cols="12" class="pl-4 pt-6">
        <span
          style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600"
          >แสดงรายการจำนวนสินค้า
          {{ itemsPerPage &lt; 0 ? props.length : itemsPerPage }} รายการ</span
        >
      </v-col>
      <v-col cols="12" md="12" sm="12" xs="12">
        <v-card outlined class="mb-4">
          <v-data-table
            :headers="
              keyCheckHead == 0 ? headersActiveCompany : headersInactiveCompany
            "
            :items="DataTable"
            :items-per-page="5"
            :page.sync="page"
            :search="search"
            @pagination="countCompany"
            no-results-text="ไม่พบบริษัทที่ค้นหา"
            no-data-text="ไม่มีบริษัทในตาราง"
            :update:items-per-page="getItemPerPage"
          >
            <template v-slot:[`item.id`]="{ item }">
              {{
                DataTable.map(function(x) {
                  return x.id;
                }).indexOf(item.id) + 1
              }}
            </template>
            <template v-slot:[`item.detail`]="{ item }">
              <v-row dense justify="center">
                <v-btn
                  outlined
                  color="#27AB9C"
                  style="border: 1px solid #27AB9C; box-sizing: border-box; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                  @click="gotoCompanyDetail(item)"
                  class="pt-4 pb-4"
                >
                  รายละเอียด
                </v-btn>
              </v-row>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              {{
                new Date(item.created_at).toLocaleDateString("th-TH", {
                  year: "numeric",
                  month: "short",
                  day: "numeric",
                  hour: "numeric",
                  minute: "numeric",
                  second: "numeric"
                })
              }}
            </template>
          </v-data-table>
        </v-card>
        <!-- <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div> -->
      </v-col>
    </v-row>
    <v-row justify="center" align-content="center" v-else>
      <v-col cols="12" md="12" align="center">
        <div class="my-5">
          <v-img
            src="@/assets/ImageINET-Marketplace/ICONShop/NotProductIcon.png"
            max-height="500px"
            max-width="500px"
            height="100%"
            width="100%"
            contain
            aspect-ratio="2"
          ></v-img>
        </div>
        <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;"
            >คุณยังไม่มีบริษัท</span
          ><br />
          <span style="font-weight: bold; font-size: 24px; line-height: 32px;"
            >กด
            <span style="font-size: 28px;">“เพิ่มข้อมูลบริษัท”</span>
            เพื่อเพิ่มบริษัทที่สมบูรณ์ของคุณ</span
          >
        </h2>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      pageCount: 5,
      page: 1,
      itemsPerPage: 5,
      search: '',
      StateStatus: 0,
      showCountOrder: 0,
      disableTable: false,
      keyCheckHead: 0,
      countActiveCompany: 0,
      countInactiveCompany: 0,
      DataTable: [],
      companyData: [],
      pathEditCompany: '',
      headersActiveCompany: [
        {
          text: 'ลำดับที่',
          value: 'id',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'วันที่สร้าง',
          value: 'created_at',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'รหัสบริษัท',
          value: 'code',
          sortable: false,
          align: 'center',
          width: '250',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'ชื่อบริษัท',
          value: 'name_th',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'รหัสลูกค้า',
          value: 'customer_code',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'ข้อมูล',
          value: 'detail',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        }
        // { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ],
      headersInactiveCompany: [
        {
          text: 'ลำดับที่',
          value: 'id',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'วันที่สร้าง',
          value: 'created_at',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'รหัสบริษัท',
          value: 'code',
          sortable: false,
          align: 'center',
          width: '250',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'ชื่อบริษัท',
          value: 'name_th',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'รหัสลูกค้า',
          value: 'customer_code',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        },
        {
          text: 'ข้อมูล',
          value: 'detail',
          sortable: false,
          align: 'center',
          class: 'backgroundTable fontTable--text'
        }
        // { text: 'จัดการ', value: 'actions', sortable: false, align: 'center', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  created () {
    this.getListCompany()
  },
  methods: {
    async getListCompany () {
      await this.$store.dispatch('actionsListCompany')
      var response = await this.$store.state.ModuleAdminManage.stateListCompany
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.companyData = response.data
        if (this.StateStatus === 0) {
        } else if (this.StateStatus === 1) {
        }
      }
    },
    countCompany (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    SelectDetailCompany (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
      this.page = 1
    },
    async gotoCompanyDetail (val) {
      var data = {
        company_id: val.id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage
        .stateDetailCompany
      // console.log(response)
      if (response.result === 'SUCCESS') {
        if (response.message === 'Show company detail success.') {
          localStorage.setItem('companyData', Encode.encode(response.data))
          this.$EventBus.$emit('getCompanyName')
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        } else {
          this.$swal.fire({
            icon: 'error',
            title: response.message,
            showConfirmButton: false,
            timer: 2000
          })
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          title: response.message,
          showConfirmButton: false,
          timer: 2000
        })
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    }
  }
}
</script>
