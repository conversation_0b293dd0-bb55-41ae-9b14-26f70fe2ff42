<template>
  <div class="body" style="margin: 12px 18px 12px 18px">
    <v-col style="margin-left: 15px">
      <v-row class="d-flex align-center mt-0" style="margin-left: -20px">
        <a href="/orderCompany">
          <v-icon style="color: #27ab9c">mdi-chevron-left</v-icon>
        </a>
        <h1 class="renew" style="margin-top: 10px; margin-left: 10px">
          Renew Order
        </h1>
      </v-row>
      <v-row style="margin-top: 20px">
        <p class="subtitle">เลขที่อ้างอิงรหัสสั่งซื้อ :</p>
        <p class="refNoB">{{ refNo }}</p>
        <p class="refNoB">|</p>
        <a @click="orderDetail()"
          ><v-icon style="color: #27ab9c; margin-left: 5px"
            >mdi-eye-outline</v-icon
          ></a
        >
        <a @click="orderDetail()" class="quotation_sample"
          ><u>ตัวอย่างใบเสนอราคา</u></a
        >
      </v-row>
      <!-- รายการสินค้าที่สั่งซื้อ -->
      <v-row style="margin-top: 20px">
        <h1 class="List_of_products">รายการสินค้าที่สั่งซื้อ</h1>
        <v-divider
          style="
            border-top: 2px solid #ebebeb;
            margin-left: 16px;
            margin-right: 25px;
            display: flex;
            align-self: center;
            margin-bottom: 10px;
          "
        ></v-divider>
      </v-row>
      <v-row style="margin-top: 20px">
        <p>{{ products_list.length }}</p>
        <p style="margin-left: 5px">รายการสินค้า</p>
      </v-row>
    </v-col>
    <v-col style="margin-top: -20px">
      <a-table
        :showHeader="true"
        :columns="headers"
        :data-source="products_list"
        :rowKey="record => checkRowKey(record)"
        :pagination="false">
        <template slot="productdetails" slot-scope="text, record">
          <v-row>
            <v-col cols="12" md="4" class="pr-0 py-1">
              <v-img :src="record.product_image" class="imageshow" v-if="record.product_image !== ''" contain/>
              <v-img src="@/assets/NoImage.png" class="imageshow" contain v-else/>
            </v-col>
            <v-col cols="12" md="8">
              <p class="mb-0">{{ record.product_name }}</p>
              <!-- <span v-if="record.product_attribute_detail.attribute_priority_1" class="mb-0 DetailsProductFront">{{record.key_1_value}}: <b>{{record.product_attribute_detail.attribute_priority_1}}</b></span>
              <span v-if="record.product_attribute_detail.attribute_priority_2" class="pl-2 mb-0 DetailsProductFront">{{record.key_2_value}}: <b>{{record.product_attribute_detail.attribute_priority_2}}</b></span> -->
            </v-col>
          </v-row>
        </template>
        <template slot="revenue_default" slot-scope="text, record">
          <v-col cols="12" class="py-0 px-0">
            <span>{{ Number(record.revenue_default).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
          </v-col>
        </template>
        <template slot="quantity" slot-scope="text, record">
          <v-col cols="12" class="py-0 px-0">
            <span>{{ record.quantity }}</span>
          </v-col>
        </template>
        <template slot="total_revenue_default" slot-scope="text, record">
          <v-col cols="12" class="py-0 px-0">
            <span>{{ Number(record.total_revenue_default).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span>
          </v-col>
        </template>
      </a-table>
    </v-col>
    <v-row class="mt-3">
      <v-col>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ราคาไม่รวมภาษีมูลค่าเพิ่ม</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <span class="cost"><span>{{ Number(total_price_no_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท</span
            >
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ส่วนลด</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost">0.00 บาท</p>
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ภาษีมูลค่าเพิ่ม</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost">
              <span>{{ Number(total_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท
            </p>
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end r">
          <v-col cols="4" class="d-flex justify-end">
            <p>ราคารวมภาษีมูลค่าเพิ่ม</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost">
              <span>{{ Number(total_price_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท
            </p>
          </v-col>
        </v-row>
        <v-row class="d-flex justify-end a">
          <v-col cols="4" class="d-flex justify-end">
            <p>ราคารวมทั้งหมด</p>
          </v-col>
          <v-col cols="2" class="d-flex justify-end">
            <p class="cost a">
              <span>{{ Number(total_price_vat).toLocaleString(undefined, { minimumFractionDigits: 2 }) }}</span> บาท
            </p>
          </v-col>
        </v-row>
      </v-col>
    </v-row>
    <!-- รายละเอียดรายการสั่งซื้อ -->
    <v-col style="margin-left: 24px; margin-right: 24px">
      <v-row style="margin-top: 20px">
        <h1 class="List_of_products">รายละเอียดรายการสั่งซื้อ</h1>
        <v-divider
          style="
            border-top: 2px solid #ebebeb;
            margin-left: 16px;
            margin-right: 38px;
            display: flex;
            align-self: center;
            margin-bottom: 10px;
          "
        ></v-divider>
      </v-row>
      <v-row style="margin-left: -12px; margin-top: 25px">
        <v-col cols="4">
          <v-row>
            <p style="color: #636363">วันที่เริ่มสัญญาเดิม :</p>
            <span>
              {{ formatDateToShow(dataStartDate) }}
              <!-- {{
                new Date(products_data.start_date_contract).toLocaleDateString(
                  'th-TH',
                  { year: 'numeric', month: 'long', day: 'numeric' }
                )
              }} -->
              </span
            >
          </v-row>
        </v-col>
        <v-col cols="4">
          <v-row>
            <p style="color: #636363">วันที่สิ้นสุดสัญญาเดิม :</p>
            <span>
              {{ formatDateToShow(this.dataEndDate) }}
              <!-- {{
                new Date(products_data.end_date_contract).toLocaleDateString(
                  'th-TH',
                  { year: 'numeric', month: 'long', day: 'numeric' }
                )
              }} -->
              </span
            >
          </v-row>
        </v-col>
      </v-row>
    </v-col>
    <v-col style="margin-left: 12px; margin-right: 24px">
      <v-row>
        <!-- <v-col cols="4">
          <label for="start"
            >วันที่เริ่มสัญญา <span style="color: #f5222d">*</span></label
          >
          <v-text-field
            dense
            outlined
            disabled
            v-model="FormattedstartDateShow"
          ><v-icon disabled slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
        </v-col> -->
        <v-col cols="12" md="4" sm="6">
          <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่เริ่มสัญญา <span style="color: red;">*</span></span>
          <v-dialog
          ref="dialog"
          v-model="modalContractStartDate"
          :close-on-content-click="false"
          :return-value.sync="date"
          transition="scale-transition"
          offset-y
          left
          width="290px"
          min-width="auto"
        >
          <template v-slot:activator="{ on, attrs }">
            <v-text-field
              v-model="dateFormattedShow"
              v-bind="attrs"
              v-on="on"
              dense
              disabled
              placeholder="วว/ดด/ปปปป"
              outlined
              hide-details
            ><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
          </template>
          <v-date-picker
            color="#27AB9C"
            v-model="dateFormatted"
            scrollable
            locale="th"
            :min="minStart"
          >
            <v-spacer></v-spacer>
            <v-btn text color="primary" @click="modalContractStartDate = false">ยกเลิก
            </v-btn>
            <v-btn text color="primary" @click="getSelectDateStart(dateFormatted)">ตกลง
            </v-btn>
          </v-date-picker>
        </v-dialog>
        </v-col>
        <v-col cols="12" md="4" sm="6">
          <span style="line-height: 24px; font-size: 16px; color: #333333;">วันที่สิ้นสุดสัญญา <span style="color: red;">*</span></span>
          <v-dialog
            ref="dialog1"
            v-model="modalContractEndDate"
            :close-on-content-click="false"
            :return-value.sync="date"
            transition="scale-transition"
            offset-y
            left
            width="290px"
            min-width="auto"
          >
            <template v-slot:activator="{ on, attrs }">
              <v-text-field
                v-model="dateFormatted2Show"
                v-bind="attrs"
                v-on="on"
                dense
                placeholder="วว/ดด/ปปปป"
                outlined
                hide-details
              ><v-icon slot="append" color="#27AB9C">mdi-calendar-multiselect</v-icon></v-text-field>
            </template>
            <v-date-picker
              color="#27AB9C"
              v-model="dateFormatted2"
              scrollable
              locale="Th-th"
              reactive
              :min="dateFormatted"
            >
              <v-spacer></v-spacer>
              <v-btn text color="primary" @click="closeModalContractEndDate()">ยกเลิก
              </v-btn>
              <v-btn text color="primary" @click="getSelectDateEnd(dateFormatted2)">ตกลง
              </v-btn>
            </v-date-picker>
          </v-dialog>
        </v-col>
        <v-col cols="4" style="display: flex; align-items: center">
          <span
            style="
              color: rgb(99, 99, 99);
              padding-left: 50px;
              padding-bottom: 20px;
            "
            >Pay Type :
          </span>
          <span
            style="
              padding-left: 5px;
              padding-bottom: 20px;
              font-size: 16px;
              /* font-family: Poppins; */
              font-weight: 500;
              line-height: 24px;
            "
            >{{ products_data.pay_type }}</span
          >
        </v-col>
      </v-row>
    </v-col>
    <v-col style="margin-left: 12px; margin-right: 24px; margin-top: -20px">
      <v-row style="margin-left: 6px">
        <v-col cols="4" style="align-self: center">
          <v-row>
            <v-switch
              disabled
              inset
              v-model="switchState"
              style="width: 42px; height: 24px"
            ></v-switch>
            <p
              style="margin-left: 20px; align-self: flex-end; margin-top: 20px"
              v-if="switchState"
            >
              เลือกใช้ส่วนลด
            </p>
            <p
              style="margin-left: 20px; align-self: flex-end; margin-top: 20px"
              v-else
            >
              ไม่ใช้ส่วนลด
            </p>
          </v-row>
        </v-col>
        <v-col cols="8" style="margin-left: -8px">
          <v-row>
            <v-col>
              <label for="">ส่วนลด</label>
              <v-select
                v-if="switchState"
                dense
                outlined
                placeholder="เลือกส่วนลด"
              ></v-select>
              <v-select
                v-else
                dense
                outlined
                disabled
                placeholder="เลือกส่วนลด"
              ></v-select>
            </v-col>
            <v-col>
              <label for="">ยอดส่วนลด</label>
              <v-text-field
                v-if="switchState"
                dense
                outlined
                placeholder="0.00"
                suffix="บาท"
              ></v-text-field>
              <v-text-field
                v-else
                dense
                outlined
                disabled
                placeholder="0.00"
                suffix="บาท"
              ></v-text-field>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" style="margin-top: -20px">
          <v-row>
            <v-checkbox v-model="checkbox"></v-checkbox>
            <v-row style="margin-top: 20px; margin-left: 6px">
              <p
                style="
                  font-size: 16px;
                  /* font-family: Poppins; */
                  font-weight: 500;
                  line-height: 24px;
                "
              >
                ต้องการระบุสัญญาบริการ
              </p>
            </v-row>
          </v-row>
        </v-col>
        <v-col style="margin-left: -15px">
          <p
            style="
              font-size: 16px;
              /* font-family: Poppins; */
              line-height: 24px;
              margin-bottom: 5px;
            "
          >
            หมายเหตุ
          </p>
          <v-textarea
            outlined
            placeholder="ระบุหมายเหตุ"
            v-model="notation"
            oninput="this.value = this.value.replace(/^[0-9!/*๑-๙฿@#$%&()_+{}:;<>,.?~]|^[\s]/, '')"
          ></v-textarea>
        </v-col>
      </v-row>
      <v-row class="d-flex justify-center" style="margin-bottom: 14px">
        <v-btn outlined color="#27AB9C" height="32px" width="146px" @click="dialog_Cancel = true"
          >ยกเลิก</v-btn
        >
        <v-btn
          :disabled="dateFormatted2Show === '' || dateFormatted2 === ''"
          color="#27AB9C"
          height="32px"
          width="146px"
          style="color: white; margin-left: 16px"
          @click="dialog_Confirm = true"
          >ยืนยันการขอซื้อ</v-btn
        >
      </v-row>
    </v-col>
    <v-dialog v-model="dialog_Confirm" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">บันทึก Renew Order</font>
          </span>
          <v-btn icon dark @click="dialog_Confirm = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  font-weight: 200;
                  font-size: 18px;
                  line-height: 26px;
                  color: #333333;
                "
              >
                ต้องการต่อสัญญาการสั่งซื้อสินค้าใช่หรือไม่
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn
              dense
              dark
              outlined
              color="#27AB9C"
              class="pl-7 pr-7 mt-1"
              @click="dialog_Confirm = false"
            >
              ยกเลิก
            </v-btn>
            <v-btn
              dense
              color="#27AB9C"
              class="ml-4 mt-1 pl-8 pr-8 white--text"
              @click="OrderRenew()"
            >
              ยืนยัน
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Confirm2" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">บันทึก Renew Order</font>
          </span>
          <v-btn icon dark @click="dialog_Confirm2 = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row style="display: contents">
              <v-icon x-large style="font-size: 85px; color: #27ab9c"
                >mdi-checkbox-marked-circle</v-icon
              >
            </v-row>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  color: var(--primary, #27ab9c);
                  text-align: center;
                  /* font-family: Poppins; */
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 24px;
                "
              >
                คุณได้ทำต่อสัญญาการสั่งซื้อสินค้า เรียบร้อย
                กรุณารอผู้ขายอนุมัติการสั่งซื้อ
              </span>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Cancel" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">ยกเลิก Renew Order</font>
          </span>
          <v-btn icon dark @click="dialog_Cancel = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  font-weight: 200;
                  font-size: 18px;
                  line-height: 26px;
                  color: #333333;
                "
              >
                ต้องการยกเลิกการ Renew Order ใช่หรือไม่
              </span>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions align="center">
          <v-container>
            <v-btn
              dense
              dark
              outlined
              color="#27AB9C"
              class="pl-7 pr-7 mt-1"
              @click="dialog_Cancel = false"
            >
              ยกเลิก
            </v-btn>
            <v-btn
              dense
              color="#27AB9C"
              class="ml-4 mt-1 pl-8 pr-8 white--text"
              @click="OpenCancelRenew ()"
            >
              ยืนยัน
            </v-btn>
          </v-container>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialog_Cancel2" width="436px" persistent scrollable>
      <v-card>
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">ยกเลิก Renew Order</font>
          </span>
          <v-btn icon dark @click="CancelRenew ()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text align="center">
          <v-container>
            <v-row style="display: contents">
              <v-icon x-large style="font-size: 85px; color: #27ab9c"
                >mdi-checkbox-marked-circle</v-icon
              >
            </v-row>
            <v-row
              class="pa-2 mt-4"
              style="display: flex; justify-content: center"
            >
              <span
                style="
                  color: var(--primary, #27ab9c);
                  text-align: center;
                  /* font-family: Poppins; */
                  font-size: 16px;
                  font-style: normal;
                  font-weight: 600;
                  line-height: 24px;
                "
              >
                คุณได้ยกเลิกการทำรายการดังกล่าวเรียบร้อย
              </span>
            </v-row>
          </v-container>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table
  },
  data () {
    return {
      data: '',
      dataStartDate: '',
      dataEndDate: '',
      dateFormattedShow: '',
      modalContractStartDate: false,
      dialog_Confirm: false,
      dialog_Confirm2: false,
      dialog_Cancel: false,
      dialog_Cancel2: false,
      pay_type: 'Onetime',
      start_date: '',
      end_date: '',
      endDateFormat: '',
      checkbox: false,
      switchState: false,
      // coupon: 'ใช้ส่วนลด',
      listOrderNo: 2,
      refNo: '',
      notation: '',
      products: [
        {
          id: 4242,
          name: 'สินค้าที่ 1',
          cost: 1000,
          count: 1,
          all: 1000,
          imgUrl:
            'https://support-legendsofruneterra.riotgames.com/hc/article_attachments/360046334854/image-0.jpeg'
        },
        {
          id: 4353,
          name: 'สินค้าที่ 2',
          cost: 2000,
          count: 1,
          all: 2000,
          imgUrl:
            'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Teemo_8.jpg'
        },
        {
          id: 1024,
          name: 'สินค้าที่ 3',
          cost: 3000,
          count: 1,
          all: 3000,
          imgUrl:
            'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Teemo_14.jpg'
        },
        {
          id: 4866,
          name: 'สินค้าที่ 4',
          cost: 4000,
          count: 1,
          all: 4000,
          imgUrl:
            'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Teemo_27.jpg'
        },
        {
          id: 5742,
          name: 'สินค้าที่ 5',
          cost: 5000,
          count: 1,
          all: 5000,
          imgUrl:
            'https://ddragon.leagueoflegends.com/cdn/img/champion/splash/Teemo_37.jpg'
        }
      ],
      products_data: [],
      products_list: [],
      companyData: [],
      // data: {
      //   role_user: 'purchaser',
      //   company_id: this.companyData.id,
      //   payment_transaction_number: this.refNo
      // },
      total: 0,
      total_price_no_vat: 0,
      total_vat: 0,
      total_price_vat: 0,
      pdf_path: '',
      FormattedstartDateShow: '',
      sendnewContract: '',
      sendnewEndContract: '',
      modalContractEndDate: false,
      dateFormatted2: '',
      minDate: '',
      dateFormatted2Show: ''
    }
  },
  mounted () {
    // เข้าถึงค่า orderNumber ผ่าน URL parameter
    // this.refNo = this.$route.query.orderNumber
    // this.refNo = orderNumber
    // console.log('------------>', orderNumber) // พิมพ์ค่า orderNumber ใน console
  },
  computed: {
    headers () {
      const headers = [
        {
          title: 'รหัสสินค้า',
          dataIndex: 'sku',
          key: 'sku',
          slots: { title: 'sku' },
          scopedSlots: { customRender: 'sku' },
          width: '20%'
        },
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          slots: { title: 'customTitleProductdetails' },
          scopedSlots: { customRender: 'productdetails' },
          width: '20%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'revenue_default',
          scopedSlots: { customRender: 'revenue_default' },
          key: 'revenue_default',
          align: 'center',
          width: '20%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          align: 'center',
          width: '15%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'total_revenue_default',
          scopedSlots: { customRender: 'total_revenue_default' },
          align: 'center',
          key: 'total_revenue_default',
          width: '10%'
        }
      ]
      return headers
    }
  },
  watch: {
    dialog_Confirm2 (val) {
      if (!val) return
      setTimeout(() => (this.dialog_Confirm2 = false), 2000)
    }
  },
  async created () {
    this.refNo = this.$route.query.orderNumber
    // console.log('------------>', this.refNo)
    if (localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(
        Decode.decode(localStorage.getItem('CompanyData'))
      )
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    // console.log('idPage----->', this.companyData.id)
    this.getListOrderDetail()
  },
  methods: {
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${parseInt(year) + 543}`
    },
    minStartDate () {
      var dateStart = this.checkDateToShow((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
      this.dateFormatted = dateStart
      this.dateFormattedShow = this.formatDateToShow(dateStart)
      this.minStart = dateStart
    },
    checkDateToShow (date) {
      if (!date) return null
      var yearEndDate = this.dataEndDate.split('-')[0]
      const oldDate = date.split('-')
      var newDate = ` ${yearEndDate}-${oldDate[1]}-${oldDate[2]}`
      if (parseInt(oldDate[2]) <= 15) {
        var d1 = '01'
        var current
        if (new Date(newDate).getMonth() === 11) {
          current = new Date(new Date(newDate).getFullYear() + 1, 0, 1)
        } else {
          current = new Date(new Date(newDate).getFullYear(), new Date(newDate).getMonth() + 1, 1)
        }
        var endyear = current.getFullYear()
        var endMonth = ('0' + (current.getMonth() + 1)).slice(-2)
        return `${endyear}-${endMonth}-${d1}`
      } else {
        var d2 = '01'
        var current2
        if (new Date(newDate).getMonth() + 1 === 11) {
          current2 = new Date(new Date(newDate).getFullYear() + 1, 0, 1)
        } else {
          current2 = new Date(new Date(newDate).getFullYear(), new Date(newDate).getMonth() + 2, 1)
        }
        var endyear2 = current2.getFullYear()
        var endMonth2 = ('0' + (current2.getMonth() + 1)).slice(-2)
        return `${endyear2}-${endMonth2}-${d2}`
      }
    },
    closeModalContractEndDate () {
      this.dateFormatted2 = ''
      this.dateFormatted2Show = ''
      this.modalContractEndDate = false
      // this.date1 = false
    },
    getSelectDateEnd (val) {
      if (val !== '') {
        this.$refs.dialog1.save(val)
        this.dateFormatted2 = this.formatDate(val)
        this.dateFormatted2Show = this.formatDateToShow(val)
        this.modalContractEndDate = false
      }
    },
    getSelectDateStart (val) {
      if (val !== '') {
        this.$refs.dialog.save(val)
        this.dateFormatted = this.formatDate(val)
        this.dateFormattedShow = this.formatDateToShow(val)
        this.dateFormatted2 = ''
        this.dateFormatted2Show = ''
        this.modalContractStartDate = false
      }
    },
    checkRowKey (record) {
      if (record.have_attribute === 'yes') {
        var newKey = record.product_attribute_detail.product_attribute_id
        return newKey
      } else {
        // console.log('record.sku', record.sku)
        return record.sku
      }
    },
    closeModal () {
      // this.dateFormatted2Show = this.formatDate(this.dateFormatted2)
      // this.dateFormatted2Show = this.sendnewContract
      this.modalContractEndDate = false
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    async getListOrderDetail () {
      this.$store.commit('openLoader')
      this.FormattedstartDateShow = ''
      this.sendnewContract = ''
      this.dateFormatted2Show = ''
      const companyId = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      var data = {
        role_user: 'purchaser',
        company_id: companyId.id,
        payment_transaction_number: this.$route.query.orderNumber
      }
      await this.$store.dispatch('actionListOrderSellerDetail', data)
      var res = await this.$store.state.ModuleOrder.stateOrderListSellerDetail
      if (res.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        // console.log('res', res.data)
        // this.products_data = res.data.data_list
        this.products_list = res.data.data_list[0].product_list
        this.products_data = res.data
        this.dataStartDate = res.data.start_date_contract
        this.dataEndDate = res.data.end_date_contract
        this.total_price_no_vat = this.products_data.total_price_no_vat
        this.total_vat = this.products_data.total_vat
        this.total_price_vat = this.products_data.total_price_vat
        if (parseFloat(this.total_price_vat) > parseFloat(5000)) {
          this.checkbox = true
        } else {
          this.checkbox = false
        }
        this.minStartDate()
        this.pdf_path = this.products_data.pdf_path
        this.end_date = this.products_data.end_date_contract
        var keepStartDate = new Date(this.products_data.end_date_contract)
        var keepDate = new Date(keepStartDate.setDate(keepStartDate.getDate() + 1))
        var getyear = keepDate.toLocaleDateString('default', { year: 'numeric' })
        var getmonth = keepDate.toLocaleDateString('default', { month: '2-digit' })
        var getday = keepDate.toLocaleDateString('default', { day: '2-digit' })
        // console.log(new Date(keepStartDate.setDate(keepStartDate.getDate() + 1)))
        this.FormattedstartDateShow = getday + '/' + getmonth + '/' + (parseInt(getyear) + 543)
        // this.sendnewContract = getmonth + '-' + getday + '-' + getyear
        // this.dateFormatted2Show = getmonth + '-' + getday + '-' + getyear
        // this.dateFormatted2 = getyear + '-' + getmonth + '-' + getday
        // this.dateFormatted2Show = this.formatDate(this.dateFormatted2)
        // this.minDate = getyear + '-' + getmonth + '-' + getday
      } else {
        this.$store.commit('closeLoader')
        console.error('listOrderDetailErr----------------->', res)
      }
    },
    orderDetail () {
      window.open(this.pdf_path)
    },
    async OrderRenew () {
      // this.dialog_Confirm = false
      // console.log('OrderRenew----------------->')
      // this.dialog_Confirm2 = true
      this.$store.commit('openLoader')
      const data = {
        order_number: this.refNo,
        renew_reason: this.notation,
        end_date_contract: this.dateFormatted2
      }
      await this.$store.dispatch('actionListOrderRenew', data)
      var res = await this.$store.state.ModuleOrder.stateOrderRenew
      if (res.result === 'Success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'สำเร็จ',
          showConfirmButton: false,
          timer: 2000
        })
        this.dialog_Confirm = false
        // console.log('OrderRenew----------------->', res)
        // this.dialog_Confirm2 = true
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      } else {
        this.$store.commit('closeLoader')
        console.error('OrderRenewErr----------------->', res)
        this.$swal.fire({
          icon: 'error',
          text: res.message,
          showConfirmButton: false,
          timer: 2000
        })
        this.dialog_Confirm = false
      }
    },
    OpenCancelRenew () {
      this.dialog_Cancel = false
      this.dialog_Cancel2 = true
    },
    CancelRenew () {
      this.dialog_Cancel = false
      this.$router.push({ path: '/orderCompany' }).catch(() => {})
    }
  }
}
</script>

<style>
* {
  margin: 0;
  padding: 0;
  outline: 1px solid rgba(9, 255, 0, 0);
}
.List_of_products {
  color: #333333;
  font-size: 20px;
  /* font-family: Poppins; */
  font-weight: 700;
  line-height: 30px;
}
/* td {
  height: 75px;
} */
.renew {
  font-size: 20px;
  font-family: Poppins;
  font-weight: 700;
  line-height: 30px;
}
.subtitle {
  color: #636363;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 400;
  line-height: 24px;
}
.refNoB {
  color: #333333;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 800;
  line-height: 24px;
  margin-left: 5px;
}
.quotation_sample {
  color: #27ab9c;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 400;
  line-height: 24px;
  margin-left: 5px;
}
.cost {
  text-align: right;
  font-size: 16px;
  /* font-family: Poppins; */
  font-weight: 600;
  line-height: 24px;
}
.r {
  margin-bottom: -35px;
  color: #333333;
}
.a {
  font-size: 20px;
  /* font-family: Poppins; */
  font-weight: 700;
  line-height: 30px;
}
.imageshow {
  width: 80px;
  height: 80px;
}
.imageshowMobile {
  width: 60px;
  height: 60px;
  /* cursor: pointer; */
}
</style>
<style>
.ant-table-thead > tr > th {
  color: rgba(0, 0, 0, 0.85);
  font-weight: 500;
  text-align: left;
  background: #D8EFE4 !important;
  border-bottom: 1px solid #e8e8e8;
  transition: background 0.3s ease;
}
.ant-table-column-title {
  color: #27AB9C !important;
  font-weight: 600;
  line-height: 24px;
  font-size: 16px;
}
</style>
