<template>
  <v-container style="height:650px" class="pl-0 py-0">
    <v-row dense class="mb-4 ml-0" style="overflow: hidden;">
      <v-col cols="12" md="12" class="px-0">
        <v-row>
         <h2 v-if="!MobileSize && !IpadSize" class="ml-0 pa-10" style="font-weight: 700; font-size:24px; line-height: 32px; color: #333333; margin-bottom:2%;"><B>ดาวน์โหลดเอกสารและคู่มือการใช้งาน</B></h2>
          <h4 v-if="IpadSize" class="ml-0 pa-10" style="font-size:20px;margin-bottom:2%"><B>ดาวน์โหลดเอกสารและคู่มือการใช้งาน</B></h4>
         <v-spacer  style="border-top: 1px solid #E6E6E6; margin-top: 60px; margin-left: 2px;" ></v-spacer>
         <v-btn  icon  outlined style="border: 1px solid #F2F2F2; box-sizing: border-box; border-radius: 999px;"   class="mt-11 mr-5" >
           <v-icon color="#27AB9C" @click="reloadpage()">mdi-file-document</v-icon>
         </v-btn>
        </v-row>
        <v-row>
          <v-card outlined width="376px" height="100px" style="border-radius: 8px; border: 1px solid #F2F2F2;" class="mr-4 ml-9" @click="DownloadForm('manual')" dense>
            <v-row dense>
              <v-col cols="4" md="3" class="mt-2 ml-2">
                <v-avatar
                  width="84px"
                  height="84px"
                  color="#EBEEF6"
                  style="border-radius: 8px;"
                >
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="55px" height="55px" contain></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="5" md="8" class="mt-6">
                <span style="font-weight: bold; font-size: 16px; line-height: 24px; color: #333333;">คู่มือการใช้ Panit (e-Pro).pdf</span><br/>
                <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 1.68 MB</span>
              </v-col>
            </v-row>
          </v-card>
          <!-- <v-card outlined width="386px" height="100px" style="border-radius: 8px; border: 1px solid #F2F2F2;" @click="DownloadForm('POAS')" dense>
              <v-row dense>
                <v-col cols="12" md="3" class="mt-2 ml-2">
                  <v-avatar
                    width="84px"
                    height="84px"
                    color="#EBEEF6"
                    style="border-radius: 8px;"
                  >
                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/word.png" width="60px" height="60px" contain></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="12" md="8" class="mt-6">
                  <span style="font-weight: bold; font-size: 16px; line-height: 24px; color: #333333;">หนังสือมอบอำนาจช่วง.docx</span><br/>
                  <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 17 KB</span>
                </v-col>
              </v-row>
          </v-card> -->
        </v-row>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      datas: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/DownloadFilesMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/DownloadFiles' }).catch(() => {})
      }
    }
  },
  methods: {
    reloadpage () {
      window.location.reload()
    },
    DownloadForm (val) {
      if (val === 'POA') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88.docx')
      } else if (val === 'POAS') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%87.docx')
      } else if (val === 'manual') {
        window.open('https://s3gw.inet.co.th:8082/bucket-test-3-9-2022/File/%E0%B8%84%E0%B8%B9%E0%B9%88%E0%B8%A1%E0%B8%B7%E0%B8%AD%E0%B8%81%E0%B8%B2%E0%B8%A3%E0%B9%83%E0%B8%8A%E0%B9%89%20Panit%20%28e-Pro%29.pdf')
      } else {
        window.open('https://docs.google.com/document/d/1p6-XO5bUdHuxCfJy1HnbhJwZnSEbkI9AbUK93Tfbhvo/edit?usp=sharing')
      }
    }
    // D () {
    // const link = document.createElement('a')
    // link.href = 'https://docs.google.com/document/d/1IOePzbIJg8QUQX9enNxznkgF5jpkyOQc/edit?usp=sharing&ouid=113422844464442335528&rtpof=true&sd=true'
    // link.setAttribute('download', 'คู่มือการใช้ Panit (e-Pro).docx')
    // link.click()
    // }
  }
}
</script>
