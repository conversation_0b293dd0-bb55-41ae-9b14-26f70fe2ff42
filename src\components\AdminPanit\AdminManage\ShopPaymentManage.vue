<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col :cols="MobileSize ? 12 : 6">
          <v-card-title style="font-weight: 700; font-size: 22px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการยกเลิกโอนเงินร้านค้า</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2 d-flex" @click="backtoPage()">mdi-chevron-left</v-icon>รายการยกเลิกโอนเงินร้านค้า</v-card-title>
        </v-col>
      </v-row>
      <v-row class="mx-1">
        <v-col :cols="MobileSize ? 12 : 6">
          <v-text-field rounded v-model="search" @keyup="searchText(search)" placeholder="ค้นหาจากรหัสคำสั่งสั่งซื้อ, ชื่อบัญชี หรือ ชื่อธนาคาร" outlined dense hide-details>
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col cols="12" class="mt-2">
          <span style="font-size: 17px; align-items: center; color: #333333; font-weight: 600;">
            รายการโอนเงินทั้งหมด {{ showCountOrder }} รายการ
          </span>
        </v-col>
        <v-col cols="12">
           <v-data-table
            class="elevation-1"
            height="100%"
            :options.sync="option"
            :items-per-page="option.itemsPerPage"
            @pagination="countOrdar"
            :server-items-length="maxItem"
            @update:options="updateOptions"
            :footer-props="{ 'items-per-page-options': [5, 10, 15, 50, 100], 'items-per-page-text': 'จำนวนแถว' }"
            :headers="headers"
            :items="DataTable"
            style="width:100%; text-align: center; white-space: nowrap;"
            no-results-text="ไม่พบรหัสคำสั่งซื้อ"
            no-data-text="ไม่พบรหัสคำสั่งซื้อ">
            <template v-slot:[`item.status`]="{ item }">
              <v-chip
                small
                :key="item.status"
                :style="{ backgroundColor: colorBg(item.status), color: colorText(item.status) }"
              >
                  <span style="font-weight: bold;">{{ thaiStatus(item.status) }}</span>
              </v-chip>
            </template>
            <template v-slot:[`item.status_transfer`]="{ item }">
              <v-chip
                small
                :key="item.status_transfer"
                :style="{ backgroundColor: colorBg(item.status_transfer), color: colorText(item.status_transfer) }"
              >
                  <span style="font-weight: bold;">{{ thaiStatusTrnsfer(item.status_transfer) }}</span>
              </v-chip>
            </template>
            <template v-slot:[`item.created_at`]="{ item }">
              <span v-if="item.created_at !== '' && item.created_at !== null">
                {{
                new Date(item.created_at).toLocaleDateString('th-TH', {
                  year: 'numeric',
                  month: 'long',
                  day: 'numeric'
                })
              }}
              </span>
              <span v-else>
                -
              </span>
            </template>
            <template v-slot:[`item.remark_buyer`]="{ item }">
              <v-tooltip bottom v-if="item.remark_buyer !== null && item.remark_buyer !== ''">
                <template v-slot:activator="{ on, attrs }">
                  <span v-bind="attrs" v-on="on">{{item.remark_buyer | truncate(20, '...')}}</span>
                </template>
                <div style="max-width: 300px; white-space: normal; word-break: break-word;">
                  {{ item.remark_buyer }}
                </div>
              </v-tooltip>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.remark_seller`]="{ item }">
              <v-tooltip bottom v-if="item.remark_seller !== null && item.remark_seller !== ''">
                <template v-slot:activator="{ on, attrs }">
                  <span v-bind="attrs" v-on="on">{{item.remark_seller | truncate(20, '...')}}</span>
                </template>
                <div style="max-width: 300px; white-space: normal; word-break: break-word;">
                  {{ item.remark_seller }}
                </div>
              </v-tooltip>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.index`] = "{ item }">
              <v-btn text rounded color="#27AB9C" small @click="openEditStatus(item)">
                <v-icon small>mdi-square-edit-outline</v-icon>
                <b class="pl-1">แก้ไขสถานะ</b>
              </v-btn>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog persistent content-class="elevation-0" v-model="modalEditStatus" :width="MobileSize ? '100%' : '35%'">
      <v-card style="border-radius: 1.5vw;">
        <v-card-title class="backgroundHead">
          <v-row>
            <v-col style="text-align: center;" class="pt-4">
            <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>แก้ไขสถานะการอนุมัติ/โอนเงิน</b></span>
            </v-col>
            <v-btn fab small @click="closeEditStatus()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
          </v-row>
        </v-card-title>
        <v-card-text class="pt-4">
          <div class="pb-5 pt-2">
            <span style="font-weight: bold; color: #333333; font-size: 16px;">รหัสคำสั่งซื้อ :</span><span style="font-size: 16px;"> voke-565623232</span>
          </div>
          <div class="px-2">
            <span style="font-weight: 700;">สถานะการอนุมัติ : </span>
            <v-select
              v-model="selectedStatus"
              :items="status"
              item-text="text"
              item-value="value"
              label="สถานะการอนุมัติ"
              solo
              chips
              style="border-radius: 8px; max-height: 100px; overflow-y: auto;"
              placeholder="เลือกสถานะการอนุมัติ"
              :menu-props="{ maxHeight: '230px' }"
            >
              <template v-slot:selection="data">
                <v-chip
                  :key="data.item.value"
                  :style="{ backgroundColor: colorBg(data.item.value), color: colorText(data.item.value) }"
                >
                    <span style="font-weight: bold;" >{{ data.item.text }}</span>
                </v-chip>
              </template>
            </v-select>
          </div>
          <div class="px-2">
            <span style="font-weight: 700;">สถานะการโอนเงิน : </span>
            <v-select
              v-model="selectedTransfer"
              :items="statusTransfer"
              item-text="text"
              item-value="value"
              label="สถานะการโอนเงิน"
              solo
              chips
              style="border-radius: 8px; max-height: 100px; overflow-y: auto;"
              placeholder="เลือกสถานะการโอนเงิน"
              :menu-props="{ maxHeight: '230px' }"
            >
              <template v-slot:selection="data">
                <v-chip
                  :key="data.item.value"
                  :style="{ backgroundColor: colorBg(data.item.value), color: colorText(data.item.value) }"
                >
                    <span style="font-weight: bold;" >{{ data.item.text }}</span>
                </v-chip>
              </template>
            </v-select>
          </div>
        </v-card-text>
          <v-card-actions style="display: flex; justify-content: center; gap: 1vw; padding-bottom: 2vw;">
          <v-btn
            color="primary"
            outlined
            rounded
            width="8vw"
            height="40"
            @click="closeEditStatus()"
        >
            ยกเลิก
          </v-btn>
          <v-btn
            color="primary"
            width="8vw"
            height="40"
            rounded
            @click="UpdateCancelOrder()"
        >
            ตกลง
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      maxItem: 0,
      showCountOrder: 0,
      option: {
        page: 1,
        itemsPerPage: 10
      },
      DataTable: [],
      orderNumber: '',
      selectedTransfer: '',
      selectedStatus: '',
      modalEditStatus: false,
      statusTransfer: [
        { text: 'รอการโอนเงิน', value: 'waiting' },
        { text: 'โอนเงินสำเร็จ', value: 'success' },
        { text: ' ปฏิเสธการโอนเงิน', value: 'reject' }
      ],
      status: [
        { text: 'รอการอนุมัติ', value: 'waiting' },
        { text: 'อนุมัติ', value: 'approve' },
        { text: 'ปฏิเสธการอนุมัติ', value: 'reject' }
      ],
      search: '',
      headers: [
        { text: 'รหัสการสั่งซื้อ', align: 'center', width: '200', value: 'order_number', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการอนุมัติ', align: 'center', width: '200', value: 'status', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการโอนเงิน', align: 'center', width: '200', value: 'status_transfer', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบัญชีธนาคาร', align: 'center', width: '200', value: 'account_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ธนาคาร', align: 'center', width: '200', value: 'bank_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขบัญชีธนาคาร', align: 'center', width: '200', value: 'account_no', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทรศัพท์', align: 'center', width: '200', value: 'phone', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่ทำรายการ', align: 'center', width: '200', value: 'created_at', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุผู้ซื้อ', align: 'center', width: '200', value: 'remark_buyer', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุร้านค้า', align: 'center', width: '200', value: 'remark_seller', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', align: 'center', width: '200', value: 'index', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/ShopPaymentManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ShopPaymentManage' }).catch(() => {})
      }
    }
  },
  created () {
    this.GetListOrder()
  },
  methods: {
    async searchText (text) {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(async () => {
        this.search = text
        this.$store.commit('openLoader')
        this.options.page = 1
        await this.GetListOrder()
      }, 500)
    },
    async updateOptions (options) {
      this.options = options
      await this.GetListOrder()
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async GetListOrder () {
      this.$store.commit('openLoader')
      var data = {
        limit: this.option.itemsPerPage,
        page: this.option.page,
        search: this.search
      }
      await this.$store.dispatch('actionsListsOrderCancelAdmin', data)
      var res = await this.$store.state.ModuleAdminManage.stateListsOrderCancelAdmin
      if (res.code === 200) {
        this.DataTable = res.data.list_all
        this.maxItem = res.data.count_all
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${res.message}` })
      }
    },
    async UpdateCancelOrder () {
      this.$store.commit('openLoader')
      var data = {
        order_number: this.orderNumber,
        status: this.selectedStatus,
        status_transfer: this.selectedTransfer
      }
      await this.$store.dispatch('actionsUpdateCancelOrderList', data)
      var res = await this.$store.state.ModuleAdminManage.stateUpdateCancelOrderList
      if (res.code === 200) {
        this.closeEditStatus()
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'success', text: `${res.message}` })
        this.GetListOrder()
      } else {
        this.closeEditStatus()
        this.$store.commit('closeLoader')
        await this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${res.message}` })
      }
    },
    openEditStatus (item) {
      this.orderNumber = item.order_number
      this.selectedStatus = item.status
      this.selectedTransfer = item.status_transfer
      this.modalEditStatus = true
    },
    closeEditStatus () {
      this.orderNumber = ''
      this.selectedStatus = ''
      this.selectedTransfer = ''
      this.modalEditStatus = false
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    thaiStatus (val) {
      if (val === 'waiting') {
        return 'รอการอนุมัติ'
      } else if (val === 'approve') {
        return 'อนุมัติ'
      } else if (val === 'reject') {
        return 'ปฏิเสธการอนุมัติ'
      }
    },
    thaiStatusTrnsfer (val) {
      if (val === 'waiting') {
        return 'รอการโอนเงิน'
      } else if (val === 'success') {
        return 'โอนเงินสำเร็จ'
      } else if (val === 'reject') {
        return 'ปฏิเสธการโอน'
      }
    },
    colorText (val) {
      if (val === 'waiting') {
        return '#FAAD14'
      } else if (val === 'success' || val === 'approve') {
        return '#00B500'
      } else if (val === 'reject') {
        return '#F5222D'
      }
    },
    colorBg (val) {
      if (val === 'waiting') {
        return '#fdf8ed'
      } else if (val === 'success' || val === 'approve') {
        return '#def9d1'
      } else if (val === 'reject') {
        return '#fdcbcd'
      }
    }
  }
}
</script>

<style>

</style>
