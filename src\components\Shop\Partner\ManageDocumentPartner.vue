<template>
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="overflow: hidden;">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รายการเอกสารขอเป็นคู่ค้า</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายการเอกสารขอเป็นคู่ค้า</v-card-title>
      <v-row dense class="mb-4">
        <v-col cols="12" class="mt-3" :class="MobileSize ? 'px-5' : 'px-4'">
          <v-row class="mx-0 pb-6" v-if="!MobileSize">
            <v-col cols="6" sm="8">
              <v-img class="float-left" src="@/assets/icons/File.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">รายการเอกสารที่ร้องขอ</v-card-title>
            </v-col>
            <v-col cols="6" sm="4" align="end">
              <v-btn @click="settingDocumentPartnerRequest()" color="#27AB9C" class="ml-4 pl-4 pr-4 white--text" :class="IpadSize ? 'mt-2' : ''">จัดการข้อมูล</v-btn>
            </v-col>
          </v-row>
          <v-row v-else>
            <v-col cols="12">
              <v-img class="float-left" src="@/assets/icons/File.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">รายการเอกสารที่ร้องขอ</v-card-title>
            </v-col>
            <v-col cols="12" align="end">
              <v-btn block @click="settingDocumentPartnerRequest()" color="#27AB9C" class="white--text">จัดการข้อมูล</v-btn>
            </v-col>
          </v-row>
        </v-col>
        <v-col v-for="(item, i) in documentPartnerList" :key="i" cols="12" :class="MobileSize ? 'px-5 pt-5' : 'px-6'">
          <v-card height="100" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-3 py-2" v-if="!MobileSize">
            <v-row class="mx-0">
              <v-col class="px-0">
                <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="84px" height="84px"></v-img>
                <v-card-title class="float-left pl-3 card-title">{{ item.name_document }}</v-card-title>
              </v-col>
            </v-row>
          </v-card>
          <v-card height="100%" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-2 py-2" v-else>
            <v-card-text class="py-0 px-0">
              <v-row dense class="mx-0">
                <v-col cols="2" class="px-0">
                  <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="84px" height="84px"></v-img>
                </v-col>
                <v-col cols="10">
                  <span class="float-left pl-2 card-title-mobile">{{ item.name_document }}</span>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="documentPartnerList.length === 0" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 :class="MobileSize ? 'title-mobil' : ''" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการเอกสารที่ร้องขอ</b></h2>
        </v-col>
      </v-row>
    </v-card>
    <!-- เอกสารที่ร้องขอ -->
    <v-dialog v-model="openDialogListData" width="800" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :style="{ 'font-size': MobileSize ? '18px' : '24' }"><b>เอกสารร้องขอ</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="openDialogListData = !openDialogListData" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-row class="px-5">
            <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? 'px-0' : 'px-5'">
              <v-img class="float-left" src="@/assets/icons/File.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">รายการเอกสารที่ร้องขอ</v-card-title>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6" align="end" :class="MobileSize ? 'px-0' : 'px-5'">
              <v-btn @click="addSettingDocumentPartnerRequest()" :block="MobileSize" color="#27AB9C" class="white--text" >
                <v-icon left> mdi-plus</v-icon>เพิ่มข้อมูล
              </v-btn>
            </v-col>
          </v-row>
          <v-card-text class="mt-3">
            <v-row>
              <v-col v-for="(item, index) in updateDocumentPartnerList" :key="index" cols="12" :class="MobileSize ? 'px-0' : 'px-6'">
                <v-card height="100" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-3 py-2" v-if="!MobileSize">
                  <v-row class="mx-0">
                    <v-col class="px-0">
                      <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="84px" height="84px"></v-img>
                      <v-card-title class="float-left pl-3 card-title">{{ item.name_document }}</v-card-title>
                      <v-btn class="float-right" style="transform: translateY(20%);" @click="deleteData(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                    </v-col>
                  </v-row>
                </v-card>
                <v-card height="100" elevation="0" style="border-radius: 8px; border: 1px solid #E6E6E6; box-sizing: border-box; box-shadow: none;" class="pa-2 py-2" v-else>
                  <v-card-text class="py-0 px-0">
                    <v-row dense class="mx-0 mt-2">
                      <v-col cols="2" class="px-0">
                        <v-img class="float-left" src="@/assets/icons/PDF.png" contain width="60px" height="60px"></v-img>
                      </v-col>
                      <v-col cols="8">
                        <span class="pl-1 card-title-mobile">{{ item.name_document }}</span>
                      </v-col>
                      <v-col cols="2">
                        <v-btn class="" style="transform: translateY(20%);" @click="deleteData(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text class="mt-3">
            <v-row v-if="documentPartnerList.length === 0">
              <v-col cols="12"  align="center">
                <div class="my-5">
                  <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 :class="MobileSize ? 'title-mobil' : ''" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการเอกสารที่ร้องขอ</b></h2>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- เพิ่มเอกสารที่ร้องขอ -->
    <v-dialog v-model="openDialog" width="800" persistent>
      <v-card>
        <v-toolbar dark dense elevation="0" color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :style="{ 'font-size': MobileSize ? '18px' : '24' }"><b>เพิ่มเอกสารร้องขอ</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="cancel()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container grid-list-xs>
          <v-row class="px-5">
            <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? 'px-0' : 'px-5'">
              <v-img class="float-left" src="@/assets/icons/File.png" contain width="60px" height="60px"></v-img>
              <v-card-title style="font-weight: bold; font-size: 18px; line-height: 26px; color: #333333;">เพิ่มเอกสารที่ร้องขอ</v-card-title>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 6" :class="MobileSize ? 'px-0' : 'px-5'" align="end">
              <v-btn outlined @click="addDocumentName()" color="#27AB9C" class="#27AB9C--text" :block="MobileSize">
                <v-icon left> mdi-plus</v-icon>เพิ่มเอกสารที่ร้องขอ
              </v-btn>
            </v-col>
          </v-row>
          <v-card-text>
            <v-form ref="Form1" :lazy-validation="lazy">
              <v-row :class="MobileSize ? 'mt-3 px-0' : 'mt-3 px-4'" v-for="(item, index) in updateDocumentPartnerList" :key="index">
                <v-col cols="12" class="py-0">
                  <span class="mb-0">เอกสาร</span>
                </v-col>
                <v-col :cols="MobileSize ? 10 : 11" class="py-0">
                  <v-text-field v-model="item.name_document" placeholder="ระบุชื่อเอกสาร" outlined dense :rules="Rules.empty"></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 2 : 1" class="pl-0 py-0">
                  <v-btn @click="deleteDocumentName(index)" elevation="0" icon><v-icon color="#757D8A">mdi-delete-outline</v-icon></v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-card-text>
          <v-card-text class="mt-3">
            <v-row v-if="updateDocumentPartnerList.length === 0">
              <v-col cols="12"  align="center">
                <div class="my-5">
                  <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
                </div>
                <h2 :class="MobileSize ? 'title-mobil' : ''" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>เพิ่มเอกสารที่ร้องขอ</b></h2>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions v-if="!MobileSize">
            <v-spacer></v-spacer>
            <v-btn class="px-5" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
            <v-btn class="px-5 white--text" color="#27AB9C" @click="saveData()">บันทึก</v-btn>
          </v-card-actions>
          <v-card-actions v-else>
            <v-row dense justify="center">
              <v-btn class="px-5 mr-3" outlined color="#27AB9C" @click="cancel()">ยกเลิก</v-btn>
              <v-btn class="px-5 white--text" color="#27AB9C" @click="saveData()">บันทึก</v-btn>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      lazy: false,
      listData: [],
      openDialog: false,
      openDialogListData: false,
      documentPartnerList: [],
      updateDocumentPartnerList: [],
      actions: '',
      Rules: {
        empty: [v => !!v || 'กรุณากรอกข้อมูล']
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.getMainDocumentList()
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/SettingPartnerRequestMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/SettingPartnerRequest' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    settingDocumentPartnerRequest () {
      this.openDialogListData = !this.openDialogListData
    },
    addSettingDocumentPartnerRequest () {
      this.openDialog = !this.openDialog
    },
    async getMainDocumentList () {
      this.updateDocumentPartnerList = []
      this.$store.commit('openLoader')
      const shopId = localStorage.getItem('shopSellerID')
      const data = { seller_shop_id: shopId }
      await this.$store.dispatch('actionsDocumentPartnerList', data)
      const res = await this.$store.state.ModulePartner.stateDocumentPartnerList
      if (res.message === 'List main document partner success') {
        this.$store.commit('closeLoader')
        this.documentPartnerList = res.data.list_main_document
        this.updateDocumentPartnerList = res.data.list_main_document
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      } else if (res.message === 'Not found data') {
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (res.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 7000,
            timerProgressBar: true,
            icon: 'error',
            title: 'ดำเนินการไม่สำเร็จ',
            text: `${res.message}`
          })
        }
      }
    },
    addDocumentName () {
      const data = { name_document: '' }
      this.updateDocumentPartnerList.push(data)
    },
    deleteData (index) {
      this.$swal.fire({
        icon: 'warning',
        html: '<h3>คุณได้ทำการลบเอกสารร้องขอ<br>คุณต้องการทำรายการนี้ ใช่หรือไม่</h3>',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.actions = 'delete'
          this.updateDocumentPartnerList.splice(index, 1)
          this.updateData()
        } else if (result.dismiss === this.$swal.DismissReason.cancel) {
        }
      }).catch(() => {
      })
    },
    saveData () {
      this.$swal.fire({
        icon: 'warning',
        html: this.MobileSize ? '<h4>คุณได้ทำการเพิ่มเอกสารร้องขอ<br>คุณต้องการทำรายการนี้ ใช่หรือไม่</h4>' : '<h3>คุณได้ทำการเพิ่มเอกสารร้องขอ<br>คุณต้องการทำรายการนี้ ใช่หรือไม่</h3>',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.actions = 'create'
          this.save()
        } else if (result.dismiss === this.$swal.DismissReason.cancel) {
        }
      }).catch(() => {
      })
    },
    save () {
      if (this.$refs.Form1.validate(true)) {
        this.updateData()
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'warning', title: 'ตรวจสอบข้อมูลอีกครั้ง', text: 'กรุณากรอกข้อมูลให้ครบ' })
      }
    },
    async updateData () {
      const shopId = localStorage.getItem('shopSellerID')
      const data = {
        seller_shop_id: shopId,
        data_to_insert: this.updateDocumentPartnerList
      }
      await this.$store.dispatch('actionsSetDocumentPartner', data)
      const res = await this.$store.state.ModulePartner.stateSetDocumentPartner
      if (res.message === 'Update main document partner') {
        this.getMainDocumentList()
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'บันทึกข้อมูลสำเร็จ'
        })
        if (this.actions === 'delete') {
          if (this.documentPartnerList.length === 0) {
            this.openDialogListData = false
          } else {
            this.openDialogListData = true
          }
        } else {
          this.openDialog = false
        }
      } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ดำเนินการไม่สำเร็จ',
          text: `${res.message}`
        })
      }
    },
    deleteDocumentName (indexData) {
      this.updateDocumentPartnerList.splice(indexData, 1)
    },
    cancel () {
      this.$swal.fire({
        icon: 'warning',
        title: this.MobileSize ? '<h5>ยกเลิกการทำรายการนี้</h5>' : 'ยกเลิกการทำรายการนี้',
        html: this.MobileSize ? '<h5>คุณแน่ใจว่าต้องการออกจากหน้านี้</h5>' : '<h3>คุณแน่ใจว่าต้องการออกจากหน้านี้</h3>',
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
      }).then((result) => {
        if (result.isConfirmed) {
          this.getMainDocumentList()
          this.openDialog = !this.openDialog
        } else if (result.dismiss === this.$swal.DismissReason.cancel) {
        }
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.card-title {
  font-style: normal;
  font-weight: bold;
  font-size: 16px;
  line-height: 30px;
  color: #333333;
  background: #FFFFFF;
  padding: 14px 0px 0px 8px;
}
.card-title-mobile {
  font-style: normal;
  font-weight: bold;
  font-size: 14px;
  line-height: 30px;
  color: #333333;
  padding: 14px 0px 0px 8px;
  white-space: normal;
}
.title-mobil {
  font-size: 16px;
}
</style>
