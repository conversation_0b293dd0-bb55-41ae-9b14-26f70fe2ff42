<template>
  <v-container>
    <!-- Modal เลือกวิธีการชำระเงิน -->
    <v-dialog v-model="ModalPaymentQU" persistent width="600">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 600px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-6">
                <span :class="MobileSize ? 'white--text' : 'white--text'" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 22px;'"><b>วิธีการชำระเงิน</b></span>
              </v-col>
              <v-btn fab small @click="ModalPaymentQU = !ModalPaymentQU" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '600px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;"></v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 15px 20px 15px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense>
                  <v-col cols="12" md="12">
                    <v-row dense justify="center">
                      <span style="font-weight: 600; color: #333333;" :style="MobileSize ? 'font-size: 16px; line-height: 24px;' : 'font-size: 18px; line-height: 26px;'">เลือกวิธีการชำระเงิน</span>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" class="mt-4">
                    <v-row dense justify="center">
                      <v-col cols="12" md="4" sm="6" align="center">
                        <v-card outlined width="130" height="148" style="border: 1px solid #D8EFE4; border-radius: 10px;" @click="OpenModalSelectPaytype('payment')">
                          <v-card-text class="pt-8 px-0">
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/money.png" contain max-height="40" max-width="40"></v-img>
                            <p style="font-weight: 500; font-size: 14px; line-height: 14px; color: #333333;" class="pt-4">เลือกการชำระเงิน</p>
                          </v-card-text>
                        </v-card>
                      </v-col>
                      <v-col cols="12" md="4" sm="6" align="center">
                        <v-card outlined width="130" height="148" style="border: 1px solid #D8EFE4; border-radius: 10px;" @click="OpenModalSelectPaytype('cash')">
                          <v-card-text class="pt-8 px-0">
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/term-loan.png" contain max-height="40" max-width="40"></v-img>
                            <p style="font-weight: 500; font-size: 14px; line-height: 14px; color: #333333;" class="pt-4">ชำระด้วยเงินสด</p>
                          </v-card-text>
                        </v-card>
                      </v-col>
                      <v-col cols="12" md="4" sm="6" align="center">
                        <v-card outlined width="130" height="148" style="border: 1px solid #D8EFE4; border-radius: 10px;" @click="OpenModalSelectPaytype('creditTerm')">
                          <v-card-text class="pt-8 px-0">
                            <v-img src="@/assets/ImageINET-Marketplace/Shop/term-loan.png" contain max-height="40" max-width="40"></v-img>
                            <p style="font-weight: 500; font-size: 14px; line-height: 14px; color: #333333;" class="pt-4">ชำระด้วยเครดิตเทอม</p>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal เลือกแบบ Payment -->
    <v-dialog v-model="dialogChooesPayType" persistent width="550">
      <v-card :height=" MobileSize || IpadSize ? '':''" :width="MobileSize || IpadSize ? '':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="55px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="white" icon
              @click="closeDialogPayment()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-card-title style="place-content: center;" class="px-0">
          <span style="font-size: 20px;font-weight: 700; color: #FAAD14;">วิธีการชำระเงิน</span>
        </v-card-title>
        <v-card-text style="place-content: center;">
          <v-row no-gutters justify="center">
            <v-radio-group v-model="radioPayment" row class="ma-0 pa-0">
              <v-radio v-if="DetailQU.payment_method[0] === 'qrcode' || DetailQU.payment_method[1] === 'qrcode' || DetailQU.payment_method[2] === 'qrcode'" value="radio-qr" @click="setRadioCreditTermNo()"><template v-slot:label >
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio v-if="DetailQU.payment_method[0] === 'creditcard' || DetailQU.payment_method[1] === 'creditcard' || DetailQU.payment_method[2] === 'creditcard'" value="radio-credit" @click="setRadioCreditTermNo()"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              <v-radio v-if="DetailQU.payment_method[0] === 'installment' || DetailQU.payment_method[1] === 'installment' || DetailQU.payment_method[2] === 'installment'" value="radio-installment"><template v-slot:label>
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio>
            </v-radio-group>
          </v-row>
          <div v-if="radioPayment === 'radio-installment'" class="mt-0 mb-4 mx-3">
            <v-row align="center">
              <v-col :cols="MobileSize ? 6 : 4" class="pt-0 pl-8">
                <span style="font-size: 16px;">ระยะเวลาผ่อนชำระ</span>
              </v-col>
              <v-col :cols="MobileSize ? 6 : 8" class="pb-0">
                <v-select outlined dense label="เลือกระยะเวลาผ่อนชำระ" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" style="border-radius: 8px;">
                  <template v-slot:append>
                    <v-icon>mdi-chevron-down</v-icon>
                  </template>
                  <template v-slot:no-data>
                    <v-list-item>
                      <v-list-item-content class="text-center">
                        <v-list-item-title>ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง <span style="color: #27AB9C;">'ขั้นต่ำ'</span> ที่กำหนดไว้</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </template>
                </v-select>
              </v-col>
            </v-row>
          </div>
        </v-card-text>
        <v-card-text >
          <v-row dense justify="center">
            <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4"
              @click="closeDialogPayment()">ยกเลิก</v-btn>
            <v-btn :disabled="radioPayment === 'no' || radioPayment === 'radio-installment' && radioCreditTerm === 'No'" :width="MobileSize? '100':'156'" height="38" class="white--text " rounded color="#27AB9C"
              @click="confirmPayment()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal เลือกแบบ eCashier -->
    <v-dialog v-model="dialogChooeseCashier" persistent width="550">
      <v-card :height=" MobileSize || IpadSize ? '':''" :width="MobileSize || IpadSize ? '':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="55px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-row dense>
              <v-col style="text-align: center;">
                <span :class="MobileSize ? 'white--text' : 'white--text'" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 22px;'"><b>E-mail / ใบแจ้งหนี้</b></span>
              </v-col>
              <v-btn fab small @click="dialogChooeseCashier = !dialogChooeseCashier" icon><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </v-app-bar>
        </v-img>
        <v-card-title style="place-content: center;" class="px-0">
          <span style="font-size: 20px;font-weight: 700; color: #FAAD14;">วิธีการชำระเงิน</span>
        </v-card-title>
        <v-card-text style="place-content: center;">
          <v-row no-gutters justify="center">
            <v-radio-group v-model="radioPayment" row class="ma-0 pa-0">
              <v-radio v-if="DetailQU.payment_method[0] === 'qrcode' || DetailQU.payment_method[1] === 'qrcode' || DetailQU.payment_method[2] === 'qrcode'" value="radio-qr" @click="setRadioCreditTermNo()"><template v-slot:label >
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio v-if="DetailQU.payment_method[0] === 'creditcard' || DetailQU.payment_method[1] === 'creditcard' || DetailQU.payment_method[2] === 'creditcard'" value="radio-credit" @click="setRadioCreditTermNo()"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
            </v-radio-group>
          </v-row>
        </v-card-text>
        <v-card-text >
          <v-row dense justify="center">
            <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4"
              @click="closeDialogPayment()">ยกเลิก</v-btn>
            <v-btn :disabled="radioPayment === 'no' || radioPayment === 'radio-installment' && radioCreditTerm === 'No'" :width="MobileSize? '100':'156'" height="38" class="white--text " rounded color="#27AB9C"
              @click="confirmPayment()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Modal Confirm Payment -->
    <v-dialog v-model="dialogConfirm" width="424" persistent>
      <v-card :height=" MobileSize || IpadSize ? '400':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn @click="dialogConfirm = false" color="#CCCCCC" icon>
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b></b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่
              หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogConfirm = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize? '100':'156'" height="38" class="white--text" rounded color="#27AB9C" @click="radioPayment === 'radio-qr' ? GetQRCode('cashPayment'): GetCC('cashPayment') ">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Dialog QR -->
    <v-dialog v-model="DialogQR" persistent :width="MobileSize ? '100%' : '640'">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
              style="position: absolute; height: 120px; ">
              <v-row style="height: 120px; ">
                <v-col style="text-align: center;" class="pt-4">
                  <span style="margin-left: 47px"
                    :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>สแกน QR Code
                      ชำระเงิน</b></span>
                </v-col>
                <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                    color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '640px'"
                style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
                style="background: #FFFFFF; border-radius: 20px;">
                <div style="text-align: center;">
                  <!-- <v-img height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"></v-img>
                  <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn> -->
                  <v-col class="py-0">
                    <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"/>
                  </v-col>
                  <v-col class="py-0">
                    <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                  </v-col>
                  <div>
                    <v-col>
                      <span style="font-size: 20px; font-weight: 700;">ยอดชำระเงินจำนวน : {{ Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
                        บาท</span>
                    </v-col>
                    <v-col>
                      <span style="font-size: 14px; font-weight: 400;">รหัสอ้างอิง {{Ref1}}</span>
                    </v-col>
                    <v-col class="py-0">
                      <span style="font-size: 14px; font-weight: 600; color: #A1A1A1;">สามารถชำระเงินได้ตามขั้นตอนนี้
                        (กรณีชำระเงินผ่านมือถือ)</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">2. เปิดแอพธนาคารของท่าน
                        และเลือกเมนูสแกน QR Code</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                        Code</span>
                    </v-col>
                  </div>
                </div>
              </v-card>
            </div>
          </v-card-text>
        </v-card>
    </v-dialog>
    <v-card width="100%" height="100%" elevation="0" :class="[MobileSize ? 'mb-12 mt-4' : 'mb-4']">
      <v-card-title>
        <v-row dense>
          <v-col cols="12" md="6">
            <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;">
              <v-icon color="#27AB9C" class="mr-2" @click="backtoListPoCompany()">mdi-chevron-left
              </v-icon>ใบเสนอราคา
            </span>
          </v-col>
          <v-col cols="12" md="6" sm="12" align="end">
            <v-row dense v-if="MobileSize" no-gutters class="px-0">
              <v-col cols="12" align="center" >
                <v-btn @click="editQTSaleOrder(DetailQU.QT_number)" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C" v-if="DetailQU.status !== 'Approve' && DetailQU.can_edit === 'yes' && DetailQU.is_vendor === 'no'">
                  แก้ไขใบเสนอราคา
                </v-btn>
                <v-btn @click="OpenModal('reject')" rounded style="margin-right: 6px;" class="px-2" outlined color="#27AB9C" v-if="DetailQU.status === 'Pending'">
                    ปฏิเสธ
                </v-btn>
                <v-btn @click="OpenModal('approve')" rounded color="#27AB9C" class="px-2" dark v-if="DetailQU.status === 'Pending'">
                    ยอมรับ
                </v-btn>
                <v-btn @click="OpenModalDWF('approve')" rounded color="#27AB9C" class="px-2" dark v-if="DetailQU.status_dwf === 'waiting' && DetailQU.status === 'Approve'">
                  ส่งเอกสาร
                </v-btn>
                <v-btn @click="OpenEmailDialog()" rounded color="#27AB9C" class="mx-2 white--text" v-if="(DetailQU.status_dwf === 'waiting_dwf' || DetailQU.status_dwf === 'approve') && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'yes'">
                  ส่งเมล
                </v-btn>
                <v-btn @click="OpenPdfUploadDialog()" rounded color="#27AB9C" class="mx-2 white--text" v-if="(DetailQU.status_dwf === 'waiting_dwf' || DetailQU.status_dwf === 'approve') && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'yes'">
                  แนบไฟล์
                </v-btn>
                <v-btn @click="OpenModalSO('approve')" rounded color="#27AB9C" :disabled="DetailQU.status_dwf !== 'approve'" class="mx-2 white--text" v-if="(DetailQU.status_dwf === 'waiting_dwf' || DetailQU.status_dwf === 'approve') && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'yes' && DetailQU.data_response_so === null">
                  ส่ง SO
                </v-btn>
              </v-col>
              <v-col cols="12" align="end" v-if="DetailQU.can_payment === 'yes' && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'no'">
                <v-btn rounded @click="openModalPayment()" color="#27AB9C" width="125" height="40" dark>
                    จ่ายเงิน
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense justify="end" v-else>
              <v-col cols="12" align="end">
                <v-btn @click="editQTSaleOrder(DetailQU.QT_number)" rounded class="mr-1" outlined color="#27AB9C" v-if="DetailQU.status !== 'Approve' && DetailQU.can_edit === 'yes' && DetailQU.is_vendor === 'no'">
                  แก้ไขใบเสนอราคา
                </v-btn>
                <v-btn @click="OpenModal('reject')" width="125" height="40" rounded outlined color="#27AB9C" class="mr-1" v-if="DetailQU.status === 'Pending'">
                  ปฏิเสธ
                </v-btn>
                <v-btn @click="OpenModal('approve')" width="125" height="40" rounded color="#27AB9C" dark elevation="0" v-if="DetailQU.status === 'Pending'">
                  ยอมรับ
                </v-btn>
                <v-btn @click="OpenModalDWF('approve')" width="125" height="40" rounded color="#27AB9C" dark elevation="0" v-if="DetailQU.status_dwf === 'waiting' && DetailQU.status === 'Approve'">
                  ส่งเอกสาร
                </v-btn>
                <v-btn @click="OpenEmailDialog()" width="125" height="40" class="white--text mr-1" rounded color="#27AB9C" elevation="0"
                  v-if="(DetailQU.status_dwf === 'waiting_dwf' || DetailQU.status_dwf === 'approve') && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'yes'">
                  ส่งเมล
                </v-btn>
                <v-btn @click="OpenPdfUploadDialog()" width="125" height="40" class="white--text mr-1" rounded color="#27AB9C" elevation="0"
                  v-if="(DetailQU.status_dwf === 'waiting_dwf' || DetailQU.status_dwf === 'approve') && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'yes'">
                  แนบไฟล์
                </v-btn>
                <v-btn @click="OpenModalSO('approve')" width="125" height="40" class="white--text mr-1" rounded color="#27AB9C" elevation="0" :disabled="DetailQU.status_dwf !== 'approve'"
                  v-if="(DetailQU.status_dwf === 'waiting_dwf' || DetailQU.status_dwf === 'approve') && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'yes' && DetailQU.data_response_so === null">
                  ส่ง SO
                </v-btn>
              </v-col>
              <v-col cols="12" align="end" v-if="DetailQU.can_payment === 'yes' && DetailQU.status === 'Approve' && DetailQU.is_vendor === 'no'">
                <v-btn @click="openModalPayment()" rounded color="#27AB9C" width="125" height="40" dark>
                    จ่ายเงิน
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-title>
      <v-card-text>
        <v-row class="pt-3 px-2" dense style="background: #F9FAFD; border-radius: 8px 8px 0px 0px;">
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่สร้าง : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ new Date(DetailQU.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })}}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเลขใบเสนอราคา : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.QT_number }}</b></span>
          </v-col>
          <v-col cols="12" md="4" :class="MobileSize ? 'd-flex' : ''">
            <!-- <span>สถานะ : <b>{{ returnStringStatus(DetailQU.status) }}</b></span> -->
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto pt-1' : ''">Pay Type : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''">
              <v-chip v-if="DetailQU.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
              <v-chip v-else-if="DetailQU.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
              <span v-else>-</span>
            </span>
          </v-col>
        </v-row>
        <v-row class="pb-3 px-2" dense style="background: #F9FAFD; border-radius: 0px 0px 8px 8px;">
          <v-col cols="12" md="4" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">วันที่อัปเดตล่าสุด : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ new Date(DetailQU.updated_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric'}) }}</b></span>
          </v-col>
          <v-col cols="12" md="4" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">ส่งคำขอโดย : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.sale_name }}</b></span>
          </v-col>
          <v-col cols="12" md="4" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">หมายเหตุ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.remark === null ? '-' : DetailQU.remark }}</b></span>
          </v-col>
          <v-col cols="12" md="4" v-if="DetailQU.status === 'reject'" :class="MobileSize ? 'd-flex' : ''" class="pt-2">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">เหตุผลในการปฏิเสธ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><b>{{ DetailQU.reason }}</b></span>
          </v-col>
          <v-col cols="12" md="12" class="pt-2" :class="MobileSize ? 'd-flex' : ''">
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'mr-auto' : ''">สถานะ : </span>
            <span style="color: #333333; font-size: 16px;" :class="MobileSize ? 'ml-auto' : ''"><v-chip :text-color="textcolorChip(DetailQU.status)" :color="colorChip(DetailQU.status)">{{ returnStringStatus(DetailQU.status) }}</v-chip></span>
          </v-col>
        </v-row>
        <v-row dense>
          <!-- ใส่ iframe -->
          <v-card width="100%" height="100%" outlined style="background: #C4C4C4; border-radius: 8px;" class="mt-4">
            <v-card-text :class="MobileSize ? 'pa-0' : ''">
              <iframe v-if="DetailQU.QT_path !== '-'"  :src="DetailQU.QT_path" width="100%" :height="MobileSize ? '500' : IpadSize ? '700' : '1200'"></iframe>
            </v-card-text>
          </v-card>
        </v-row>
        <!-- <v-row dense v-if="MobileSize" class="mt-8">
          <v-col cols="12" class="px-0" v-if="EditModeQT === false">
            <v-row dense class="px-0" v-if="DetailQU.status === 'waiting_approve'">
              <v-col cols="12" align="end">
                <v-btn @click="OpenModal('reject')" width="91" height="38" class="mr-2" rounded outlined color="#27AB9C">
                  ปฏิเสธ
                </v-btn>
                <v-btn @click="OpenModal('approve')" width="81" height="38" rounded color="#27AB9C" dark>
                  ยอมรับ
                </v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row> -->
      </v-card-text>
    </v-card>

    <!-- Start Dialog  Approve and NonApprove -->
    <v-dialog v-model="dialog" width="605px" persistent scrollable>
      <v-form ref='formData' :lazy-validation="lazy">
        <v-card class="rounded-lg">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font v-if="this.type === 'reject'" color="#27AB9C">ปฏิเสธใบเสนอราคา</font>
                    <font v-if="this.type === 'approve'" color="#27AB9C">อนุมัติใบเสนอราคา</font>
                </span>
                <v-btn icon dark @click="closeModal()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="mt-12">
                <v-row no-gutters>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">หมายเลขใบเสนอราคา :
                        </p>
                    </v-col>
                    <v-col cols="12" md="8">
                        <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.QT_number }}
                        </p>
                    </v-col>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">ส่งคำขอโดย :
                        </p>
                    </v-col>
                    <v-col cols="12" md="8">
                        <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.sale_name }}
                        </p>
                    </v-col>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">สถานะ :
                        </p>
                    </v-col>
                    <v-col cols=" 12" md="8">
                      <p style="font-weight: 600; font-size: 16px; color: #333333">
                        <b>{{ returnStringStatus(DetailQU.status) }}</b>
                      </p>
                    </v-col>
                    <v-col v-if="this.type === 'reject'" cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">เหตุผลในการปฏิเสธ <span style="color: red;">*</span> :
                        </p>
                    </v-col>
                    <v-col v-if="this.type === 'reject'" cols=" 12" md="8">
                        <v-textarea v-model="ans_reject" height="90" counter-value="100" counter="100" placeholder="ระบุเหตุผล" maxLength="100" dense auto-grow outlined :rules="Rules.reason">
                        </v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-card-actions v-if="this.type === 'reject'">
                <v-container style=" display: flex; justify-content: flex-end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">
                        ยกเลิก
                    </v-btn>
                    <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="ModalApprove('reject')">
                        บันทึก
                    </v-btn>
                </v-container>
            </v-card-actions>
            <v-card-actions v-else-if="this.type === 'approve'">
                <v-container style=" display: flex; justify-content: flex-end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModal()">
                        ยกเลิก
                    </v-btn>
                    <v-btn dense color="#27AB9C" style="color: #ffff" class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="ModalApprove('approve')" :disabled="disableButton">
                        บันทึก
                    </v-btn>
                </v-container>
            </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- End Dialog Approve and NonApprove -->

    <!-- Start Dialog  ApproveDWF and NonApproveDWF -->
    <v-dialog v-model="dialogDWF" width="605px" persistent scrollable>
      <v-form ref='formData' :lazy-validation="lazy">
        <v-card class="rounded-lg">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font v-if="this.type === 'reject'" color="#27AB9C">ปฏิเสธใบเสนอราคา</font>
                    <font v-if="this.type === 'approve'" color="#27AB9C">อนุมัติใบเสนอราคา</font>
                </span>
                <v-btn icon dark @click="closeModalDWF()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="mt-12">
                <v-row no-gutters>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">หมายเลขใบเสนอราคา :
                        </p>
                    </v-col>
                    <v-col cols="12" md="8">
                        <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.QT_number }}
                        </p>
                    </v-col>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">ส่งคำขอโดย :
                        </p>
                    </v-col>
                    <v-col cols="12" md="8">
                        <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.sale_name }}
                        </p>
                    </v-col>
                    <v-col cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">สถานะ :
                        </p>
                    </v-col>
                    <v-col cols=" 12" md="8">
                      <p style="font-weight: 600; font-size: 16px; color: #333333">
                        <b>{{ returnStringStatus(DetailQU.status) }}</b>
                      </p>
                    </v-col>
                    <v-col v-if="this.type === 'reject'" cols="12" md="4">
                        <p style="font-weight: 400; font-size: 16px; color: #333333">เหตุผลในการปฏิเสธ <span style="color: red;">*</span> :
                        </p>
                    </v-col>
                    <v-col v-if="this.type === 'reject'" cols=" 12" md="8">
                        <v-textarea v-model="ans_reject" height="90" counter-value="100" counter="100" placeholder="ระบุเหตุผล" maxLength="100" dense auto-grow outlined :rules="Rules.reason">
                        </v-textarea>
                    </v-col>
                </v-row>
            </v-card-text>
            <v-card-actions v-if="this.type === 'reject'">
                <v-container style=" display: flex; justify-content: flex-end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModalDWF()">
                        ยกเลิก
                    </v-btn>
                    <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="ModalApproveDWF('reject')">
                        บันทึก
                    </v-btn>
                </v-container>
            </v-card-actions>
            <v-card-actions v-else-if="this.type === 'approve'">
                <v-container style=" display: flex; justify-content: flex-end">
                    <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModalDWF()">
                        ยกเลิก
                    </v-btn>
                    <v-btn dense color="#27AB9C" style="color: #ffff" class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="ModalApproveDWF('approve')" :disabled="disableButton">
                        บันทึก
                    </v-btn>
                </v-container>
            </v-card-actions>
        </v-card>
      </v-form>
    </v-dialog>
    <!-- End Dialog  ApproveDWF and NonApproveDWF -->

    <v-dialog v-model="dialogEditDescription" width="605px" persistent scrollable>
      <v-card class="rounded-lg" v-if="MobileSize">
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font color="#27AB9C">แก้ไขรายละเอียด</font>
                </span>
                <v-btn icon dark @click="closeModal()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="" style="padding-top:35px; margin-bottom:-25px">
                  <p style="font-weight: 400; font-size: 16px; color: #333333; display:flex; justify-content:center;">
                    กรุณากดยืนยันเพื่อบันทึก
                  </p>
                  <p style="font-weight: 400; font-size: 16px; color: #333333; display:flex; justify-content:center; margin-top:-10px">
                    การแก้ไขรายละเอียดใบเสนอราคา
                  </p>
            </v-card-text>
            <v-card-actions>
            <v-container style=" display: flex; justify-content: center">
                <v-btn dense dark outlined color="#27AB9C" class="pl-4 pr-4 mt-2" @click="closeModal()">
                    ยกเลิก
                </v-btn>
                <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-4 pr-4 white--text"
                    @click="editDescription()">
                    บันทึก
                </v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
        <v-card class="rounded-lg" v-else>
            <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                <span class="flex text-center ml-5" style="font-size:20px">
                    <font color="#27AB9C">แก้ไขรายละเอียด</font>
                </span>
                <v-btn icon dark @click="closeModal()">
                    <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
            </v-toolbar>
            <v-card-text class="" style="display: flex; justify-content: center; padding-top:35px; margin-bottom:-25px" >
                  <p style="font-weight: 400; font-size: 16px; color: #333333;">
                    กรุณากดยืนยัน เพื่อบันทึกการแก้ไขรายละเอียดใบเสนอราคา
                  </p>
            </v-card-text>
            <v-card-actions>
            <v-container style=" display: flex; justify-content: center">
                <v-btn dense dark outlined color="#27AB9C" class="pl-4 pr-4 mt-2" @click="closeModal()">
                    ยกเลิก
                </v-btn>
                <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-4 pr-4 white--text"
                    @click="editDescription()">
                    บันทึก
                </v-btn>
            </v-container>
          </v-card-actions>
        </v-card>
    </v-dialog>
    <!-- Await Change QT -->
    <v-dialog v-model="dialogAwaitChangeQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeDialogAwait()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคา</span><br/>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '125' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeDialogAwait()">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '125' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="editDescription()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- Success Change OT -->
    <v-dialog v-model="dialogSuccessChangeQT" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-img
          height="240px"
          :src="require('@/assets/ImageINET-Marketplace/ICONShop/successShop.png')"
        >
          <v-app-bar
            flat
            color="rgba(0, 0, 0, 0)"
          >
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn
              color="#CCCCCC"
              icon
              @click="closeModalSuccess()"
            >
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกเสร็จสิ้น</b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณได้ทำการแก้ไขข้อมูลใบเสนอราคาเรียบร้อย</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn width="156" height="38" class="white--text" rounded color="#27AB9C" @click="closeModalSuccess()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <!-- <ModalSuccess v-model="dialogSuccessChangeQT"/>
    <ModalAwaitConfirm v-model="dialogAwaitChangeQT"/> -->
        <!-- Start Dialog  ApproveDWF and NonApproveDWF -->
        <v-dialog v-model="dialogSO" width="605px" persistent scrollable>
          <v-form ref='formData' :lazy-validation="lazy">
            <v-card class="rounded-lg">
                <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
                    <span class="flex text-center ml-5" style="font-size:20px">
                        <font v-if="this.type === 'reject'" color="#27AB9C">ปฏิเสธใบเสนอราคา</font>
                        <font v-if="this.type === 'approve'" color="#27AB9C">อนุมัติใบเสนอราคา</font>
                    </span>
                    <v-btn icon dark @click="closeModalSO()">
                        <v-icon color="#27AB9C">mdi-close</v-icon>
                    </v-btn>
                </v-toolbar>
                <v-card-text class="mt-12">
                    <v-row no-gutters>
                        <v-col cols="12" md="4">
                            <p style="font-weight: 400; font-size: 16px; color: #333333">หมายเลขใบเสนอราคา :
                            </p>
                        </v-col>
                        <v-col cols="12" md="8">
                            <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.order_number }}
                            </p>
                        </v-col>
                        <v-col cols="12" md="4">
                            <p style="font-weight: 400; font-size: 16px; color: #333333">ส่งคำขอโดย :
                            </p>
                        </v-col>
                        <v-col cols="12" md="8">
                            <p style="font-weight: 600; font-size: 16px; color: #333333"> {{ DetailQU.sale_name }}
                            </p>
                        </v-col>
                        <v-col cols="12" md="4">
                            <p style="font-weight: 400; font-size: 16px; color: #333333">สถานะ :
                            </p>
                        </v-col>
                        <v-col cols=" 12" md="8">
                          <p style="font-weight: 600; font-size: 16px; color: #333333">
                            <b>{{ returnStringStatus(DetailQU.status) }}</b>
                          </p>
                        </v-col>
                        <v-col v-if="this.type === 'reject'" cols="12" md="4">
                            <p style="font-weight: 400; font-size: 16px; color: #333333">เหตุผลในการปฏิเสธ <span style="color: red;">*</span> :
                            </p>
                        </v-col>
                        <v-col v-if="this.type === 'reject'" cols=" 12" md="8">
                            <v-textarea v-model="ans_reject" height="90" counter-value="100" counter="100" placeholder="ระบุเหตุผล" maxLength="100" dense auto-grow outlined :rules="Rules.reason">
                            </v-textarea>
                        </v-col>
                    </v-row>
                </v-card-text>
                <v-card-actions v-if="this.type === 'reject'">
                    <v-container style=" display: flex; justify-content: flex-end">
                        <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModalSO()">
                            ยกเลิก
                        </v-btn>
                        <v-btn dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text"
                            @click="OpenSO('reject')">
                            บันทึก
                        </v-btn>
                    </v-container>
                </v-card-actions>
                <v-card-actions v-else-if="this.type === 'approve'">
                    <v-container style=" display: flex; justify-content: flex-end">
                        <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="closeModalSO()">
                            ยกเลิก
                        </v-btn>
                        <v-btn dense color="#27AB9C" style="color: #ffff" class="ml-4 mt-2 pl-8 pr-8 white--text"
                            @click="OpenSO('approve')" :disabled="disableButton">
                            บันทึก
                        </v-btn>
                    </v-container>
                </v-card-actions>
            </v-card>
          </v-form>
        </v-dialog>
        <v-dialog v-model="dialogUploadPDF" width="752px" persistent scrollable>
          <v-card style="border-radius: 24px; background-color: #27AB9C;">
            <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
              <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">อัปโหลดเอกสาร
              </span>
              <v-btn icon dark @click="closePdfUploadDialog()">
                <v-icon color="#FFFFFF">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-card style="border-radius: 20px;">
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <v-row dense v-if="FileSO.length !== 0">
                  <v-col cols="12" md="12" sm="12">
                    <v-card v-for="(file, index) in FileSO" :key="index" outlined width="100%" height="105" class="mt-2">
                      <v-card-text>
                      <v-row dense>
                        <v-col class="py-3" cols="11">
                          <v-row>
                            <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                            </v-img>
                            <span text style="text-align: center; align-content: center; color: #1976D2; cursor: pointer;" class="text-truncate pt-2 text-decoration-underline" @click="openPath(file.pdf_path, file)">Document_{{ index+ 1 }}</span>
                          </v-row>
                        </v-col>
                        <v-col class="py-3 align-content-center" cols="1">
                          <v-btn icon small style="float: right; background-color: #ff5252;">
                            <v-icon small color="white" dark @click.stop="Removefile(index)">mdi-close</v-icon>
                          </v-btn>
                        </v-col>
                      </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row dense v-if="FileSO.length !== 3">
                  <v-col cols="12">
                    <v-card width="100%" height="100%" outlined elevation="0" class="cardImageStyle"  @click="FileSO.length < 3 && onPickFile()">
                      <v-card-text style="text-align: center;" class="px-2">
                        <input type="file" ref="fileFDA" @change="handleFileUpload($event)" style="display: none;" accept=".pdf" multiple>
                        <v-col cols="12" md="12" class="mb-6">
                          <v-row justify="center" class="pt-0">
                            <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="100" height="100" contain></v-img>
                          </v-row>
                        </v-col>
                        <v-col cols="12" md="12" class="mt-2">
                          <v-row justify="center">
                            <v-col cols="12" md="4" style="text-align: center;">
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                              <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                              <span style="font-size: 12px; line-height: 16px; font-weight: 400;">คุณสามารถเพิ่มไฟล์ได้สูงสุด 3 ไฟล์</span><br/>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
              </v-card-text>
              <v-card-actions>
                <v-container style="display: flex; justify-content: flex-end">
                  <v-col cols="6" class="pa-0">
                    <v-btn
                      rounded
                      dense
                      dark
                      outlined
                      color="#27AB9C"
                      class="pl-7 pr-7 mt-2"
                      @click="closeModalConfirm()"
                    >
                      ยกเลิก
                    </v-btn>
                  </v-col>
                  <v-col cols="6" style="text-align: end;" class="pa-0">
                    <v-btn
                      rounded
                      dense
                      color="#27AB9C"
                      class="ml-4 mt-2 pl-8 pr-8 white--text"
                      @click="uploadSO(FileSO)"
                    >
                      บันทึก
                    </v-btn>
                  </v-col>
                </v-container>
              </v-card-actions>
            </v-card>
          </v-card>
      </v-dialog>
        <v-dialog v-model="dialogEmail" width="500px" persistent scrollable>
          <v-form ref="formEmail" :lazy-validation="lazy">
            <v-card style="border-radius: 24px; background-color: #27AB9C;">
              <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
                <span class="flex text-center ml-5" style="font-size: 24px; font-weight: 700; color: #FFFFFF;">ส่งเมล
                </span>
                <v-btn icon dark @click="closeEmailDialog()">
                  <v-icon color="#FFFFFF">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card style="border-radius: 20px;">
                <v-card-text :style="MobileSize ? 'padding: 20px 16px 0px 16px;' : 'padding: 20px 20px 0px 20px;'">
                  <v-row dense>
                    <v-col cols="12">
                      <v-card-text class="py-0">
                        <v-row dense style="justify-content: center;">
                          <v-card class="my-6" elevation="0" style="background: #EBEBEB; border: 1px solid #EBEBEB; border-radius: 8px;" width="416" height="100%">
                            <v-card-text>
                              <v-row dense style="justify-content: center;">
                                <v-col v-for="(item, index) in EmailSO" :key="index" cols="12" align="center">
                                    <v-row dense>
                                      <v-col cols="12" align="start" class="">
                                        <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Email: <span style="color: red;">*</span></span>
                                      </v-col>
                                      <v-col cols="12" align="start" class="">
                                        <v-row dense>
                                          <v-col :cols="index !== 0 ? 10 : 12" align="start" class="">
                                          <v-text-field @keydown.enter.prevent class="input_text" v-model="EmailSO[index]" placeholder="ระบุ Email" outlined dense :rules="Rules.email" oninput="this.value = this.value.replace(/[^0-9a-zA-Z@-.-.]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                                          </v-col>
                                          <v-col v-if="index !== 0" cols="2" class="mt-1 text-center">
                                            <v-icon class="my-auto" color="red" size="32" @click="deleteTextField(index)">
                                              mdi-close
                                            </v-icon>
                                          </v-col>
                                        </v-row>
                                      </v-col>
                                    </v-row>
                                    <v-card :disabled="EmailSO.some(email => email === '')" v-if="index === EmailSO.length - 1" class="align-content-center" elevation="0" height="50"
                                      :style="EmailSO.some(email => email === '') ? 'border-radius: 8px; border: 2px solid #9E9E9E;' : 'border-radius: 8px; border: 2px solid var(--primary-27-ab-9-c, #27AB9C);'" @click="addTextField()">
                                        <v-card-text class="">
                                          <v-row class="px-3">
                                            <v-icon :color="EmailSO.some(email => email === '') ? '#9E9E9E' : '#27AB9C'">mdi-plus-circle</v-icon><br />
                                            <span class="pl-2"
                                            :style="EmailSO.some(email => email === '') ? 'font-weight: 500; font-size: 14px; color: #9E9E9E;' : 'font-weight: 500; font-size: 14px; color: #1B5DD6;'">เพิ่ม Email</span>
                                          </v-row>
                                        </v-card-text>
                                      </v-card>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-row>
                      </v-card-text>
                    </v-col>
                  </v-row>
                </v-card-text>
                <v-card-actions>
                  <v-container style="display: flex; justify-content: flex-end">
                    <v-col cols="6" class="pa-0">
                      <v-btn
                        rounded
                        dense
                        dark
                        outlined
                        color="#27AB9C"
                        class="pl-7 pr-7 mt-2"
                        @click="closeEmailDialog()"
                      >
                        ยกเลิก
                      </v-btn>
                    </v-col>
                    <v-col cols="6" style="text-align: end;" class="pa-0">
                      <v-btn
                      :disabled="EmailSO.some(email => email === '')"
                        rounded
                        dense
                        color="#27AB9C"
                        class="ml-4 mt-2 pl-8 pr-8 white--text"
                        @click="SendEmail()"
                      >
                        ตกลง
                      </v-btn>
                    </v-col>
                  </v-container>
                </v-card-actions>
              </v-card>
            </v-card>
            </v-form>
        </v-dialog>
        <v-dialog v-model="ModalConfirm" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
          <v-card style="background: #FFFFFF; border-radius: 24px;">
            <v-img
              height="240px"
              :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
            >
              <v-app-bar
                flat
                color="rgba(0, 0, 0, 0)"
              >
                <v-toolbar-title></v-toolbar-title>
                <v-spacer></v-spacer>
                <v-btn
                  color="#CCCCCC"
                  icon
                  @click="CancelSubmit()"
                >
                  <v-icon>mdi-close</v-icon>
                </v-btn>
              </v-app-bar>
            </v-img>
            <v-container>
              <v-card-text style="text-align: center;">
                <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>บันทึกข้อมูล</b></p>
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการ บันทึก หรือ ไม่</span>
              </v-card-text>
              <v-card-text>
                <v-row dense justify="center">
                  <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeModalConfirm()">ยกเลิก</v-btn>
                  <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="uploadSO(FileSO)">บันทึก</v-btn>
                </v-row>
              </v-card-text>
            </v-container>
          </v-card>
        </v-dialog>
        <v-dialog v-model="ModalConfirmSO" width="424" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
          <v-card style="background: #FFFFFF; border-radius: 24px;">
            <v-img
              height="240px"
              :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')"
            >
              <v-app-bar
                flat
                color="rgba(0, 0, 0, 0)"
              >
                <v-toolbar-title></v-toolbar-title>
                <v-spacer></v-spacer>
              </v-app-bar>
            </v-img>
            <v-container>
              <v-card-text style="text-align: center;">
                <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b>คุณยังไม่ได้แนบไฟล์</b></p>
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการต่อ ใช่ หรือ ไม่</span>
              </v-card-text>
              <v-card-text>
                <v-row dense justify="center">
                  <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="closeModalConfirmSO()">ยกเลิก</v-btn>
                  <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="OpenSO2()">ยืนยัน</v-btn>
                </v-row>
              </v-card-text>
            </v-container>
          </v-card>
        </v-dialog>
  </v-container>
</template>

<script>
// import VuePdf from 'vue-pdf'
// import VuePdfEmbed from 'vue-pdf-embed/dist/vue2-pdf-embed'
import { Decode, Encode } from '@/services'
// import ModalSuccess from '@/components/Modal/ModalSuccess'
// import ModalAwaitConfirm from '@/components/Modal/ModalAwaitConfirm'
export default {
  components: {
    // VuePdf
    // VuePdfEmbed
    // ModalSuccess,
    // ModalAwaitConfirm
  },
  data () {
    return {
      ModalConfirm: false,
      ModalConfirmSO: false,
      EmailSO: [],
      dialogEmail: false,
      FileSO: [],
      PathDocSO: [],
      dialogUploadPDF: false,
      dialogSO: false,
      TypeOS: '',
      CheckSaleOrder: 'saleorder',
      items: [],
      pdfPath: '',
      QUNumber: '',
      EditModeQT: false,
      dialog: false,
      disableButton: false,
      ID: '',
      comID: '',
      DetailQU: [],
      type: '',
      ModalPaymentQU: false,
      useDiscount: false,
      dialogEditDescription: false,
      dialogSuccessChangeQT: false,
      dialogAwaitChangeQT: false,
      onedata: '',
      ans_reject: '',
      lazy: false,
      seller_shop_id: '',
      rules: [v => v.length <= 1000 || 'ระบุตัวอักษรไม่เกิน 1000 ตัวอักษร'],
      Rules: {
        reason: [
          v => !!v || 'กรุณากรอกเหตุผลในการปฏิเสธ'
        ],
        email: [
          v => !!v || 'กรุณากรอกอีเมล',
          v => /.+@.+\..+/.test(v) || 'กรอกอีเมลให้ถูกต้อง',
          v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรอกได้เฉพาะภาษาอังกฤษ',
          v => /^\S*$/.test(v) || 'ห้ามใส่ช่องว่างในอีเมล'
        ]
      },
      CompanyDataId: '',
      detailItemQu: '',
      QT_description: '',
      descriptionItem: [],
      descriptionQTChange: '',
      dialogChooesPayType: false,
      dialogChooeseCashier: false,
      radioCreditTerm: 'No',
      itemsCreditTerm: [
        { value: '3', label: '3 เดือน' },
        { value: '6', label: '6 เดือน' },
        { value: '10', label: '10 เดือน' }
      ],
      radioPayment: 'no',
      dialogConfirm: false,
      CloseDialog: false,
      DialogQR: false,
      netPrice: '',
      Ref1: '',
      tRef2: '',
      ImageQR: '',
      imageBase64: '',
      dialogDWF: false
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    this.TypeOS = this.detectOS()
    this.QUNumber = this.$route.query.QUNumber
    this.ID = this.$route.query.id
    // this.comID = this.$route.query.comID
    this.Detail_rejcrt = ''
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    // this.detailItemQu = JSON.parse(Decode.decode(localStorage.getItem('detailItemQUSale')))
    this.GetDetailQU()
    window.scrollTo(0, 0)
  },
  computed: {
    filteredCreditTerms () {
      return this.itemsCreditTerm.map(term => {
        // console.log('this.items', this.items)
        const correspondingInstallment = this.items.installment_method.find(installment => installment.month === term.value)
        // เพิ่ม property displayText เพื่อแสดงราคาในตัวเลือก
        return {
          ...term,
          displayText: correspondingInstallment ? `${term.label} - ${Number(correspondingInstallment.price).toLocaleString(undefined, {
                  minimumFractionDigits: 2,
                  maximumFractionDigits: 2
                })} บาท/เดือน` : term.label
        }
      }).filter(term => {
        // กรองตามเงื่อนไขที่ต้องการ
        const correspondingInstallment = this.items.installment_method.find(installment => installment.month === term.value)
        return correspondingInstallment && correspondingInstallment.price >= 500
      })
    },
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `DetailQuotationSalesMobile?QUNumber=${this.QUNumber}&id=${this.ID}` }).catch(() => {})
      } else {
        this.$router.push({ path: `DetailQuotationSales?QUNumber=${this.QUNumber}&id=${this.ID}` }).catch(() => {})
      }
    },
    descriptionQTChange (val) {
      // console.log(val)
      this.QT_description = val
    }
  },
  methods: {
    closeModalConfirmSO () {
      this.ModalConfirmSO = false
    },
    CancelSubmit () {
      this.ModalConfirm = false
    },
    openPath (item, item2) {
      if (item) {
        window.open(item)
      } else {
        const fileURL = URL.createObjectURL(item2)
        window.open(fileURL)
        setTimeout(() => URL.revokeObjectURL(fileURL), 1000)
      }
    },
    async SendEmail () {
      if (this.$refs.formEmail.validate(true)) {
        this.$store.commit('openLoader')
        var SameEmail = this.EmailSO.some((item, index) => this.EmailSO.indexOf(item) !== index)
        if (SameEmail === false) {
          var data = {
            order_number: this.DetailQU.order_number,
            email: this.EmailSO
          }
          await this.$store.dispatch('actionsSendEmailSO', data)
          const response = await this.$store.state.ModuleSaleOrder.stateSendEmailSO
          if (response.result === 'SUCCESS') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'success',
              text: 'ส่งอีเมลสำเร็จ'
            })
            this.EmailSO = []
            if (this.DetailQU.customer_email !== null) {
              this.EmailSO.push(this.DetailQU.customer_email)
            } else {
              this.EmailSO.push('')
            }
            this.dialogEmail = false
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: 'ส่งอีเมลไม่สำเร็จ'
            })
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            text: 'พบอีเมลซ้ำกัน'
          })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
    },
    deleteTextField (index) {
      this.EmailSO.splice(index, 1)
    },
    addTextField () {
      this.EmailSO.push('')
    },
    closeEmailDialog () {
      // this.$refs.formEmail.resetValidation()
      this.EmailSO = []
      if (this.DetailQU.customer_email !== null) {
        this.EmailSO.push(this.DetailQU.customer_email)
      } else {
        this.EmailSO.push('')
      }
      this.dialogEmail = false
    },
    OpenEmailDialog () {
      this.dialogEmail = true
      this.$refs.formEmail.resetValidation()
    },
    Removefile (index) {
      this.FileSO.splice(index, 1)
    },
    OpenPdfUploadDialog () {
      this.GetDetailQU()
      this.dialogUploadPDF = true
    },
    closeModalConfirm () {
      this.PathDocSO = []
      this.FileSO = []
      this.dialogUploadPDF = false
      this.ModalConfirm = false
    },
    closePdfUploadDialog () {
      this.ModalConfirm = true
    },
    onPickFile () {
      this.$refs.fileFDA.click()
    },
    handleFileUpload (event) {
      const files = event.target.files
      const filelength = files.length
      const maxFiles = 3
      if ((this.FileSO.length + filelength) <= maxFiles) {
        // ดึงเฉพาะไฟล์ PDF
        const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf')
        if (pdfFiles.length !== files.length) {
          this.$swal.fire({
            icon: 'warning',
            text: 'กรุณาอัปโหลดเฉพาะไฟล์ PDF เท่านั้น',
            showConfirmButton: false,
            timer: 3000
          })
          return
        }
        this.$store.commit('openLoader')
        pdfFiles.forEach(file => {
          const reader = new FileReader()
          reader.readAsDataURL(file)
          reader.onload = () => {
            // คุณสามารถทำอะไรเพิ่มที่นี่ได้
          }
          this.FileSO.push(file)
        })
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'warning',
          text: 'คุณสามารถเพิ่มไฟล์ได้สูงสุด 3 ไฟล์',
          showConfirmButton: false,
          timer: 3000
        })
      }
    },
    async uploadSO (file) {
      this.$store.commit('openLoader')
      this.PathDocSO = []
      const validFiles = Array.from(file).filter(f => f instanceof File)
      const invalidFiles = Array.from(file).filter(f => !(f instanceof File))
      invalidFiles.forEach(invalidFile => {
        if (invalidFile.pdf_path) {
          this.PathDocSO.push(invalidFile.pdf_path)
        }
      })
      for (let i = 0; i < validFiles.length; i++) {
        const formData = new FormData()
        formData.append('file', validFiles[i])
        formData.append('type', 'Doc_SO')
        formData.append('seller_shop_id', localStorage.getItem('shopSellerID'))
        await this.$store.dispatch('actionsUploadDocumentSO', formData)
        const response = await this.$store.state.ModuleSaleOrder.stateUploadDocumentSO
        if (response.message === 'File uploaded successfully.') {
          this.PathDocSO.push(response.data.pdf_path)
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: `${response.message}`
          })
        }
      }
      this.ConfirmSO()
    },
    async ConfirmSO () {
      // this.$store.commit('openLoader')
      var data = {
        order_number: this.DetailQU.order_number,
        pdf_path: this.PathDocSO,
        type: 'Doc_SO',
        seller_shop_id: localStorage.getItem('shopSellerID')
      }
      await this.$store.dispatch('actionsAttachDocumentSO', data)
      const response = await this.$store.state.ModuleSaleOrder.stateAttachDocumentSO
      if (response.message === 'Success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกเอกสารสำเร็จ',
          showConfirmButton: false,
          timer: 2000
        })
        this.FileSO = []
        this.dialogUploadPDF = false
        this.GetDetailQU()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          text: `${response.message}`
        })
      }
      this.ModalConfirm = false
    },
    async OpenSO () {
      if (this.FileSO.length === 0) {
        this.ModalConfirmSO = true
      } else {
        this.$store.commit('openLoader')
        var data = {
          order_number: this.DetailQU.order_number
        }
        await this.$store.dispatch('actionsCreateSODocument', data)
        const response = await this.$store.state.ModuleSaleOrder.stateCreateSODocument
        if (response.code === 200) {
          this.$store.commit('closeLoader')
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'success', text: response.message })
          this.dialogSO = false
          this.ModalConfirmSO = false
          this.GetDetailQU()
        } else {
          this.$store.commit('closeLoader')
          this.dialogSO = false
          this.ModalConfirmSO = false
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: response.message })
        }
      }
    },
    async OpenSO2 () {
      this.$store.commit('openLoader')
      var data = {
        order_number: this.DetailQU.order_number
      }
      await this.$store.dispatch('actionsCreateSODocument', data)
      const response = await this.$store.state.ModuleSaleOrder.stateCreateSODocument
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'success', text: response.message })
        this.dialogSO = false
        this.ModalConfirmSO = false
        this.GetDetailQU()
      } else {
        this.$store.commit('closeLoader')
        this.dialogSO = false
        this.ModalConfirmSO = false
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: response.message })
      }
    },
    OpenModalSO (type) {
      this.dialogSO = true
      this.type = type
    },
    closeModalSO () {
      this.dialogSO = false
      this.type = ''
    },
    detectOS () {
      const userAgent = window.navigator.userAgent || window.navigator.vendor || window.opera
      if (/iPad|iPhone|iPod/.test(userAgent) && !window.MSStream) {
        return 'iOS'
      }
      if (/android/i.test(userAgent)) {
        return 'Android'
      }
      return 'PC'
    },
    editQTSaleOrder (val) {
      if (this.MobileSize) {
        this.$router.push({ path: `/QuotationSale?order_number=${val}&status=edit&from=DetailQuotationSalesMobile&id=${this.ID}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/QuotationSale?order_number=${val}&status=edit&from=DetailQuotationSales&id=${this.ID}` }).catch(() => {})
      }
    },
    setRadioCreditTermNo () {
      this.radioCreditTerm = 'No'
    },
    async OpenModalSelectPaytype (val) {
      if (val === 'payment') {
        this.dialogChooesPayType = true
      } else if (val === 'cash') {
        this.$store.commit('openLoader')
        const data = {
          payment_transaction_number: this.DetailQU.order_number
        }
        await this.$store.dispatch('actionsPayCashPayment', data)
        const response = await this.$store.state.ModuleSaleOrder.statePayCashPayment
        if (response.message === 'Pay Cash Success') {
          this.ModalPaymentQU = false
          await this.CreateMobi()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', html: '<h3>ชำระเงินไม่สำเร็จ</h3>' })
        }
      } else {
        this.$store.commit('openLoader')
        const data = {
          payment_transaction_number: this.DetailQU.order_number,
          cus_id: this.DetailQU.customer_id
        }
        await this.$store.dispatch('actionsCreateOrderCreditTerm', data)
        const response = await this.$store.state.ModuleSaleOrder.stateCreateOrderCreditTerm
        if (response.message === 'Create Order Credit Term Success') {
          this.ModalPaymentQU = false
          await this.CreateMobi()
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', html: '<h3>ชำระเงินไม่สำเร็จ</h3>' })
        }
      }
    },
    async CreateMobi () {
      const data = {
        order_number: this.DetailQU.order_number
      }
      await this.$store.dispatch('actionsCreateMobiCashPayment', data)
      const response = await this.$store.state.ModuleSaleOrder.stateCreateMobiCashPayment
      if (response.message === 'สร้างขนส่งสำเร็จ') {
        this.$store.commit('closeLoader')
        await this.GetDetailQU()
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'success', html: '<h3>ชำระเงินสำเร็จ</h3>' })
        if (this.MobileSize) {
          this.$router.push({ path: `DetailOrderSalesMobile?orderNumber=${this.DetailQU.order_number}&tranNumber=${this.DetailQU.order_number}` }).catch(() => {})
        } else {
          this.$router.push({ path: `DetailOrderSales?orderNumber=${this.DetailQU.order_number}&tranNumber=${this.DetailQU.order_number}` }).catch(() => {})
        }
      } else if (response.message === 'ร้านนี้ไม่มีขนส่งเข้าร่วม') {
        this.$store.commit('closeLoader')
        await this.GetDetailQU()
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'success', html: '<h3>ชำระเงินสำเร็จ</h3>' })
        if (this.MobileSize) {
          this.$router.push({ path: `DetailOrderSalesMobile?orderNumber=${this.DetailQU.order_number}&tranNumber=${this.DetailQU.order_number}` }).catch(() => {})
        } else {
          this.$router.push({ path: `DetailOrderSales?orderNumber=${this.DetailQU.order_number}&tranNumber=${this.DetailQU.order_number}` }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', html: '<h3>ชำระเงินไม่สำเร็จ</h3>' })
      }
    },
    async GetListIncludeQT () {
      var data = {
        seller_shop_id: this.seller_shop_id
      }
      await this.$store.dispatch('actionsListIncludeQT', data)
      const response = await this.$store.state.ModuleShop.stateListIncludeQT
      // console.log('GetListIncludeQT=====>', response.data)
      if (response.message === 'Success.') {
        this.descriptionItem = response.data.list_include
        // console.log('descriptionItem', this.descriptionItem)
      }
    },
    textcolorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'Waiting') {
        return '#FF710B'
      } else if (quStatus === 'Pending') {
        return '#FF710B'
      } else if (quStatus === 'Approve') {
        if (this.DetailQU.status_dwf === 'waiting_dwf') {
          return '#FF710B'
        } else {
          return '#52C41A'
        }
      } else if (quStatus === 'Reject') {
        return '#F5222D'
      } else if (quStatus === 'Process') {
        return '#6EC4D6'
      } else {
        return '#636363'
      }
    },
    colorChip (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'Waiting') {
        return '#FEF6E6'
      } else if (quStatus === 'Pending') {
        return '#FBECE1'
      } else if (quStatus === 'Approve') {
        if (this.DetailQU.status_dwf === 'waiting_dwf') {
          return '#FBECE1'
        } else {
          return '#F0FEE8'
        }
      } else if (quStatus === 'Reject') {
        return 'rgba(245, 34, 45, 0.10)'
      } else if (quStatus === 'Process') {
        return '#e8f6f9'
      } else {
        return '#E6E6E6'
      }
    },
    returnStringStatus (quStatus) {
      // console.log(quStatus, statusApprove)
      if (quStatus === 'Waiting') {
        return 'รออนุมัติเอกสาร'
      } else if (quStatus === 'Pending') {
        return 'รออนุมัติ'
      } else if (quStatus === 'Approve') {
        if (this.DetailQU.status_dwf === 'waiting_dwf') {
          return 'รออนุมัติใบเสนอราคา'
        } else {
          return 'อนุมัติแล้ว'
        }
      } else if (quStatus === 'Reject') {
        return 'ปฏิเสธอนุมัติ'
      } else if (quStatus === 'Process') {
        return 'กำลังดำเนินการ'
      } else {
        return 'ยกเลิก'
      }
    },
    async editQuotation () {
    //   console.log('เข้าeditQuotation')
      this.$store.commit('openLoader')
      const dataForm = await {
        role_user: 'seller',
        user_id: this.onedata.user.user_id,
        id: this.ID,
        seller_shop_id: this.ID,
        company_id: parseInt(this.comID),
        qu_id: this.QUNumber,
        coupon_id: this.detailItemQu.coupon_id ? this.detailItemQu.coupon_id : ''
      }
      localStorage.setItem('dataForm', Encode.encode(dataForm))
      const pathBack = window.location.href.split('/')
      this.$store.state.ModuleAdminManage.stateURLQu = `/${pathBack[3]}`
      await this.$router.push({ path: `/QuotationEdit?role=seller&&qu_id=${this.QUNumber}` }).catch(() => { })
      // await this.$store.dispatch('actionsEditQU', dataForm)
      // const { result = '', data = {} } = await this.$store.state.ModuleAdminManage.stateEditQU
      // if (result === 'SUCCESS') {
      //   this.$store.state.ModuleAdminManage.QuotationformData = await data
      //   await this.$router.push({ path: `/QuotationEdit?role=seller&&qu_id=${this.QUNumber}` }).catch(() => { })
      // }
    },
    ChangeEditMode () {
      this.EditModeQT = true
    },
    closeEditMode () {
      this.EditModeQT = false
    },
    OpenModalDescription () {
      // this.$store.commit('openLoader').timer = 1500
      this.dialogAwaitChangeQT = true
      // await this.$store.commit('closeLoader')
    },
    async editDescription () {
      this.$store.commit('openLoader')
      // this.dialogEditDescription = false
      this.dialogAwaitChangeQT = false
      var data = {
        order_number: this.DetailQU.order_number,
        description: this.QT_description
      }
      await this.$store.dispatch('actionsEditQTDescription', data)
      var responseEditDescription = await this.$store.state.ModuleAdminManage.stateEditQTDescription
      // console.log('tong2', responseEditDescription)
      if (responseEditDescription.code === 200) {
        // await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
        // this.QUNumber = responseEditDescription.data.order_document_id
        this.QUNumber = responseEditDescription.data.order_number
        this.EditModeQT = false
        this.dialogSuccessChangeQT = true
        // if (this.DetailQU.QT_path !== '-') {
        //   this.DetailQU.QT_path = responseEditDescription.data.path_pdf_qt
        // } else if (this.DetailQU.s3_pdf_path_qt !== '-') {
        //   this.s3_pdf_path_qt = responseEditDescription.data.path_pdf_qt
        // }
        // this.GetDetailQU()
        // if (this.MobileSize === false) {
        //   this.$router.push({ path: `/QuotationDetail?QUNumber=${QUNumber}&id=${id}&comID=${comID}` }).catch(() => { })
        // } else {
        //   this.$router.push({ path: `/QuotationDetailMobile?QUNumber=${QUNumber}&id=${id}&comID=${comID}` }).catch(() => { })
        // }
      } else {
        this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: responseEditDescription.message })
      }
      this.$store.commit('closeLoader')
    },
    closeModalSuccess () {
      this.dialogSuccessChangeQT = false
      this.GetDetailQU()
    },
    saveEditQuotation () {
      if (this.MobileSize) {
        this.$route.push({ path: '/listQuotationSalesMobile' }).catch(() => {})
      } else {
        this.$route.push({ path: '/listQuotationSales' }).catch(() => {})
      }
    },
    closeDialogPayment () {
      this.dialogChooesPayType = false
      this.radioPayment = 'no'
    },
    confirmPayment () {
      this.dialogChooesPayType = false
      this.ModalPaymentQU = false
      this.dialogConfirm = true
    },
    async openModalPayment () {
      var messageCheckError = ''
      var i
      var data = {
        payment_transaction_number: this.DetailQU.order_number
      }
      await this.$store.dispatch('actionsCheckStockBeforePayment', data)
      const response = await this.$store.state.ModuleCart.stateCheckStockBeforePayment
      if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
        this.ModalPaymentQU = !this.ModalPaymentQU
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
        for (i = 0; i < response.data.product_free.length; i++) {
          messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      }
    },
    async GetDetailQU () {
      this.$store.commit('openLoader')
      this.DetailQU = ''
      var data = {
        seller_shop_id: this.ID,
        order_number: this.QUNumber
      }
      await this.$store.dispatch('actionsDetailQTSale', data)
      var response = await this.$store.state.ModuleSaleOrder.stateDetailQTSale
      // console.log('', response)
      if (response.result === 'Success') {
        this.DetailQU = await response.data
        this.FileSO = this.DetailQU.Doc_SO
        this.EmailSO = []
        if (this.DetailQU.customer_email !== null) {
          this.EmailSO.push(this.DetailQU.customer_email)
        } else {
          this.EmailSO.push('')
        }
        this.$store.commit('closeLoader')
        // this.getPagePDF(
        //   // this.DetailQU.dwf_pdf_path_qt
        // )
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.DetailQU = []
          this.$swal.fire({
            icon: 'error',
            text: 'ไม่มีใบเสนอราคานี้',
            showConfirmButton: false,
            timer: 1500
          })
          this.$router.push({ path: '/listQuotationSales' }).catch(() => { })
        }
      }
    },
    async ModalApprove (statusApprove) {
      this.disableButton = true
      if (this.$refs.formData.validate(true)) {
        this.$store.commit('openLoader')
        var ApproveAndNonApprove = {
          seller_shop_id: this.ID,
          order_number: this.DetailQU.order_number,
          status: statusApprove
        }
        await this.$store.dispatch('actionsApproveSale', ApproveAndNonApprove)
        var response = await this.$store.state.ModuleSaleOrder.stateApproveSale
        if (response.result === 'SUCCESS') {
          this.$store.commit('closeLoader')
          this.disableButton = false
          await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
          this.dialog = false
          await this.GetDetailQU()
        } else if (response.message === 'สินค้า ใหญ่ ไม่เพียงพอ (คงเหลือ 0 ชิ้น)') {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'warning', text: response.message })
        } else if (response.code === 400) {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: response.message })
        } else if (response.message === 'This user is unauthorized.') {
          this.$EventBus.$emit('refreshToken')
        }
      }
    },
    OpenModal (type) {
      this.dialog = true
      this.type = type
    },
    closeModal () {
      this.dialog = false
      this.dialogEditDescription = false
      this.type = ''
      this.ans_reject = ''
    },
    async ModalApproveDWF (statusApprove) {
      this.disableButton = true
      if (this.$refs.formData.validate(true)) {
        this.$store.commit('openLoader')
        var ApproveAndNonApprove = {
          seller_shop_id: this.seller_shop_id.id,
          qu_id: parseInt(this.QUNumber),
          order_number: this.DetailQU.order_number,
          status: statusApprove,
          reason: this.ans_reject,
          company_id: this.DetailQU.company_id,
          com_perm_id: this.DetailQU.com_perm_id
        }
        await this.$store.dispatch('Approve_QT', ApproveAndNonApprove)
        var response = await this.$store.state.ModuleShop.stateAppoverQT
        if (response.code === 200) {
          this.$store.commit('closeLoader')
          this.disableButton = false
          await this.$swal.fire({ showConfirmButton: false, timer: 1500, timerProgressBar: true, icon: 'success', html: '<h3>ดำเนินการสำเร็จ</h3>' })
          if (this.MobileSize) {
            this.$router.push({ path: '/listQuotationSalesMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/listQuotationSales' }).catch(() => {})
          }
        } else if (response.message === 'สินค้า ใหญ่ ไม่เพียงพอ (คงเหลือ 0 ชิ้น)') {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'warning', text: response.message })
        } else if (response.code === 400) {
          this.$store.commit('closeLoader')
          this.disableButton = false
          this.closeModal()
          this.$swal.fire({ showConfirmButton: false, timer: 2000, timerProgressBar: true, icon: 'error', text: response.message })
        }
      }
    },
    OpenModalDWF (type) {
      this.dialogDWF = true
      this.type = type
    },
    closeModalDWF () {
      this.dialogDWF = false
      this.type = ''
    },
    closeDialogAwait () {
      this.dialog = false
      this.dialogAwaitChangeQT = false
      this.type = ''
      this.ans_reject = ''
    },
    backtoListPoCompany () {
      if (this.MobileSize) {
        this.$router.push({ path: '/listQuotationSalesMobile' }).catch(() => { })
      } else {
        this.$router.push({ path: '/listQuotationSales' }).catch(() => { })
      }
    },
    ShowModalSelect () {
      this.ModalPaymentQU = !this.ModalPaymentQU
    },
    async GetCC (paymentTypeData) {
      localStorage.setItem('sale_order', this.CheckSaleOrder)
      // console.log('GetCC', this.radioCreditTerm)
      this.$store.commit('openLoader')
      this.dialogConfirm = false
      var data
      var resCC
      var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
      if (this.radioPayment !== 'radio-installment') {
        this.radioCreditTerm = ''
      }
      data = {
        go_local: goLocalValue,
        payment_transaction_number: this.DetailQU.order_number,
        term: this.radioCreditTerm ? this.radioCreditTerm : ''
      }
      // console.log('data', data)
      await this.$store.dispatch('actionsGetCCV2', data)
      resCC = await this.$store.state.ModuleCart.stateGetCCV2
      if (resCC.result === 'SUCCESS') {
        localStorage.setItem('PaymentData', Encode.encode(resCC.data))
        this.$router.push('/RedirectPaymentPage').catch(() => {})
      } else if (resCC.message === 'ERROR ระบบ Payment มีปัญหาไม่สามารถส่งหรือรับข้อมูลได้') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ระบบ Payment มีปัญหา',
          text: 'ไม่สามารถชำระเงินได้'
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่สามารถชำระเงินได้'
        })
      }
      this.$store.commit('closeLoader')
    },
    GetQRCode (paymentTypeData) {
      localStorage.setItem('sale_order', this.CheckSaleOrder)
      // console.log('GetQRCode', paymentTypeData)
      const paymentType = paymentTypeData
      if (paymentType === 'cashPayment') {
        this.openDialogQR()
      }
    },
    closeDialogQR () {
      // this.checkOrderResult(this.items.payment_transaction)
      this.dialogConfirm = false
      this.CloseDialog = true
      this.DialogQR = false
      this.$store.commit('closeLoader')
    },
    async openDialogQR () {
      // console.log('openDialogQR')
      this.$store.commit('openLoader')
      let resQR = ''
      var data
      data = {
        payment_transaction_number: this.DetailQU.order_number
      }
      await this.$store.dispatch('actionsGetQRCodeV2', data)
      resQR = await this.$store.state.ModuleCart.stateGetQRCodeV2
      if (resQR.result === 'SUCCESS') {
        this.netPrice = await resQR.data.net_price
        this.Ref1 = await resQR.data.ref1
        this.Ref2 = await resQR.data.ref2
        this.imageBase64 = 'data:image/png;base64,' + resQR.data.img_base
        var QRCode = await resQR.data.img_base64
        setTimeout(() => {
          this.ImageQR = QRCode
          this.DialogQR = true
        }, 1000)
        this.$store.commit('closeLoader')
        this.checkOrderResult(this.DetailQU.order_number)
      } else if (resQR.result === 'FAILED') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ERROR ไม่สามารถชำระเงินได้'
        })
        this.DialogQR = false
        this.dialogConfirm = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
        })
        this.DialogQR = false
        this.dialogConfirm = false
        // }
        // this.$store.commit('closeLoader')
      }
    },
    async checkOrderResult (orderNumber) {
      const data = {
        payment_transaction_number: this.DetailQU.order_number
      }
      var value = orderNumber
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'การชำระเงินไม่เสร็จสมบูรณ์'
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }
}
</script>

<style lang="css">
 .vue-pdf-embed canvas {
  display: initial;
  position: inherit!important;
}
</style>
