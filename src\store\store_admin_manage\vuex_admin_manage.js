import AxiosAdminManage from '../store_admin_manage/axios_admin_manage'

const ModuleAdminManage = {
  state: {
    // Create Company
    stateCreateCompany: [],
    // Edit Company
    stateEditCompany: [],
    // Detail Company
    stateDetailCompany: [],
    // List Company
    stateListCompany: [],
    // List User Company
    stateListUserCompany: [],
    // List PO For Buyer
    stateListPOBuyer: [],
    // Detail QU for buyer
    stateDetailQUBuyer: [],
    // update_status_buyer
    stateUpdateStatusBuyer: [],
    // list_status_buyer
    stateListOrderPurchaser: [],
    stateTerminate: [],
    // detail_status_buyer
    stateDetailOrderPurchaser: [],
    // edit QU for buyer
    stateEditQU: [],
    QuotationformData: {},
    // List Refund Product Company
    stateListRefundPurchaser: [],
    // Detail Refund Product Company
    stateDetailRefundPurchaser: [],
    // (YaPalm) Credit Term
    stateListOrderCreditTerm: [],
    stateListCreditTerm: [],
    stateCalculateRequestCreditTerm: [],
    stateRequestChangeTerm: [],
    stateCheckCalculate: [],
    stateCancelRequestChangeTerm: [],
    stateListSpecialPriceBuyer: [],
    stateDetailSpecialPrice: [],
    stateListProductAttribute: [],
    stateUpdateListDataTableSpecialPrice: [],
    stateUpdateStatusSpecialPriceBuyer: [],
    stateRequestSpecialPrice: [],
    stateEditSpecialPriceBuyer: [],
    ListProductOfShop: [],
    stateEditQuotation: [],
    stateImgQu: '',
    stateeditPositionOfCompany: [],
    statecreatePositionOfCompany: [],
    statedetailPositionOfCompany: [],
    statelistPositionOfCompany: [],
    stateListPositionSellerUser: [],
    stateDetailPositionSellerUser: [],
    stateSearchPositionSellerUser: [],
    stateEditPositionSellerUser: [],
    stateAddPositionSellerUser: [],
    stateListPositionShops: [],
    stateEstimateQu: [],
    stateGetPayment: [],
    stateURLQu: '',
    createQuCart: '',
    stateCreateCompanyByBiz: [],
    stateDashboardAdminAll: [],
    dashboardSummary: '',
    dashboardChart: {
      comparedSeller: '',
      dateSeller: '',
      comparedUser: '',
      dateUser: ''
    },
    stateDateExport: '',
    dashboardListTop: '',
    stateAllGP: [],
    stateGpChart: {
      value: [],
      date: []
    },
    stateGPTable: [],
    stateGetListAdminPlatform: [],
    stateCreateAdminPlatform: [],
    stateEditAdminPlatform: [],
    stateDetailAdminPlatform: [],
    stateDeleteAdminPlatform: [],
    stateSearchAdminPlatform: [],
    stateshopList: [],
    stateGetDataQt: [],
    stateCreateQt: [],
    stateCreateQtV2: [],
    headerDBadmin: {
      seller: [],
      user: []
    },
    statePersonalCompany: [],
    stateEditPersonalCompany: [],
    stateEditQtV2: [],
    stateEditQTDescription: [],
    stateGetShopDataAdmin: [],
    stateAddAdminShop: [],
    stateListQTBuyer: [],
    stateDetailQTBuyer: [],
    stateListBusinessAdmin: [],
    stateAddAdminCompany: [],
    // Get Detail Cart in PO Company
    stateDetailCartInCompony: [],
    stateCompanyApproveOrder: [],
    stateDetailLandingPage: [],
    stateMangeLandingPage: [],
    stateUserInSystem: [],
    stateGetAddressCompany: [],
    stateAddAddressCompany: [],
    stateUpdateAddressCompany: [],
    stateDeleteAddressCompany: [],
    stateSetDefaultAddressCompany: [],
    stateeTaxAdmin: [],
    stateMerchantEdit: [],
    stateStockProduct: [],
    stateImportStock: [],
    stateStatusPvNo: [],
    // e-Withholding Tax
    stateAddDataEwth: [],
    stateEditDataEwth: [],
    stateGetDataEwth: [],
    // Vender
    stateGetVender: [],
    stateUpdateVender: [],
    // Affiliate
    stateGetUserJoinAffiliate: [],
    stateGetSellerJoinAffiliate: [],
    stateGetUserJoinSellerJAffiliate: [],
    stateGetToken: [],
    stateShopComissionAffiliateTable: [],
    stateUserComissionAffiliateTable: [],
    shopDetailUserCommissions: [],
    stateGetUserBySeller: [],
    stateUserActive: [],
    UserDetailCommissionsAffiliate: [],
    orderCommissionDetail: [],
    stateGetProductSellerAffiliate: [],
    stateListAllCourier: [],
    stateEditCourier: [],
    stateEditSellerShopShipping: [],
    stateApproveQuotation: [],
    stateSearchUserShop: [],
    stateManageListPosition: [],
    stateAddUserShop: [],
    GetCompanyData: [],
    stateGetUserComapany: [],
    stateSearchUserCompany: [],
    stateEditUserShop: [],
    stateListPositionCompany: [],
    stateAddUserCompany: [],
    stateEditUserCompany: [],
    stateDeleteUserCompany: [],
    stateDeleteUserShop: [],
    stateSetFlashSale: [],
    stateStatusFlashSale: [],
    stateShopPartnerAdmin: [],
    stateDetailShopPartnerAdmin: [],
    stateListPackagePartners: [],
    stateCreateCouponPlatform: [],
    stateGetListCoupon: [],
    stateChangeStatusCoupon: [],
    stateDeleteCoupon: [],
    stateGetDetailCouponPlatform: [],
    stateEditCouponPlatform: [],
    stateEditCouponPlatformIdPath: '',
    stateEditGP: [],
    stateGetListServicePartner: [],
    stateGetJoinServicePartnerWithAdmin: [],
    stateListCostCenter: [],
    stateGetDataReach: [],
    stateCreateDataReach: [],
    stateOrderReach: [],
    stateAdminApproveShop: [],
    stateAdminListPosition: [],
    stateCreatePositionShop: [],
    stateSendMail: [],
    stateGetDetailPurchaserV2: [],
    stateGetListCouponUser: [],
    stateSearchCoupon: [],
    stateAddCouponUserAdmin: [],
    stateEditIcon: [],
    stategetDataIcon: [],
    stateOrderDeliveryCompany: [],
    stateConfirmOrderDeliveryCompany: [],
    stateSearchListUser: [],
    stateGetDataListNoti: [],
    stateCreateNoti: [],
    stateStoreRegisList: [],
    stateStoreRegisDetail: [],
    stateCreateNotiNow: [],
    stateDeleteNoti: [],
    stateEditNoti: [],
    stateSearchNoti: [],
    stateGetListType: [],
    stateSearchHistory: [],
    stateEditIconMobile: [],
    stategetDataIconMobile: [],
    stategetListCancelOrderAdmin: [],
    stateGetListSortProduct: [],
    stateUpdateSortProduct: [],
    stateListPaymentTransfer: [],
    stateUpdatePaymentTransfer: [],
    stateListRegisterInfoPartner: [],
    stateDetailRegisterInfoPartner: [],
    stateListsOrderCancelAdmin: [],
    stateUpdateCancelOrderList: [],
    stateListRegisterInfoShop: [],
    stateDetailRegisterInfoShop: [],
    statePrescription: [],
    stateTransfer: [],
    stateListMyQTCompany: [],
    stateManageConsent: [],
    stateEditConsent: []
  },
  getters: {
    dateGraph (state) {
      return state.stateAllGP.data.date_graph
    },
    dataGraph (state) {
      const series = [{ name: 'รายได้ทั้งหมด', data: state.stateAllGP.data.data_graph }]
      return series
    },
    CurrentEditCouponPlatformIdPath (state) {
      return state.stateEditCouponPlatformIdPath
    }
  },
  mutations: {
    // Create Company
    mutationsCreateCompany (state, data) {
      state.stateCreateCompany = data
    },
    // Edit Company
    mutationsEditCompany (state, data) {
      state.stateEditCompany = data
    },
    // Detail Company
    mutationsDetailCompany (state, data) {
      state.stateDetailCompany = data
    },
    // List Company
    mutationsListCompany (state, data) {
      state.stateListCompany = data
    },
    // List User Company
    mutationsListUserCompany (state, data) {
      state.stateListUserCompany = data
    },
    // List PO For Buyer
    mutationsListPOBuyer (state, data) {
      state.stateListPOBuyer = data
    },
    // Detail QU for buyer
    mutationsDetailQUBuyer (state, data) {
      state.stateDetailQUBuyer = data
    },
    // update_status_buyer
    mutationsUpdateStatusBuyer (state, data) {
      state.stateUpdateStatusBuyer = data
    },
    // list_status_buyer
    mutationsListOrderPurchaser (state, data) {
      state.stateListOrderPurchaser = data
    },
    mutationsTerminate (state, data) {
      state.stateTerminate = data
    },
    // detail_status_buyer
    mutationsDetailOrderPurchaser (state, data) {
      state.stateDetailOrderPurchaser = data
    },
    // edit QU for buyer
    mutationsEditQU (state, data) {
      state.stateEditQU = data
    },
    // List Refund Product Company
    mutationsListRefundPurchaser (state, data) {
      state.stateListRefundPurchaser = data
    },
    // Detail Refund Product Company
    mutationsDetailRefundPurchaser (state, data) {
      state.stateDetailRefundPurchaser = data
    },
    // (YaPalm) List Order Credit Term
    mutationsListOrderCreditTerm (state, data) {
      state.stateListOrderCreditTerm = data
    },
    // (YaPalm) List Credit Term
    mutationsListCreditTerm (state, data) {
      state.stateListCreditTerm = data
    },
    mutationsCalculateRequestCreditTerm (state, data) {
      state.stateCalculateRequestCreditTerm = data
    },
    mutationsCheckCalculate (state, data) {
      state.stateCheckCalculate = data
    },
    mutationsRequestChangeTerm (state, data) {
      state.stateRequestChangeTerm = data
    },
    mutationsCancelRequestChangeTerm (state, data) {
      state.stateCancelRequestChangeTerm = data
    },
    mutationsListSpecialPriceBuyer (state, data) {
      state.stateListSpecialPriceBuyer = data
    },
    mutationsDetailSpecialPrice (state, data) {
      state.stateDetailSpecialPrice = data
    },
    mutationsListProductAttribute (state, data) {
      state.stateListProductAttribute = data
    },
    mutationsUpdateListDataTableSpecialPrice (state, data) {
      state.stateUpdateListDataTableSpecialPrice = data
    },
    mutationsUpdateStatusSpecialPriceBuyer (state, data) {
      state.stateUpdateStatusSpecialPriceBuyer = data
    },
    mutationsRequestSpecialPrice (state, data) {
      state.stateRequestSpecialPrice = data
    },
    mutationsEditSpecialPriceBuyer (state, data) {
      state.stateEditSpecialPriceBuyer = data
    },
    // Edit Quotation
    mutationsEditQuotation (state, data) {
      state.stateEditQuotation = data
    },
    mutationslistPositionOfCompany (state, data) {
      state.statelistPositionOfCompany = data
    },
    mutationsdetailPositionOfCompany (state, data) {
      state.statedetailPositionOfCompany = data
    },
    mutationscreatePositionOfCompany (state, data) {
      state.statecreatePositionOfCompany = data
    },
    mutationseditPositionOfCompany (state, data) {
      state.stateeditPositionOfCompany = data
    },
    mutatonListPositionSellerUser (state, data) {
      state.stateListPositionSellerUser = data
    },
    mutationsDetailPositionSellerUser (state, data) {
      state.stateDetailPositionSellerUser = data
    },
    mutationsSearchPositionSellerUser (state, data) {
      state.stateSearchPositionSellerUser = data
    },
    mutationsEditPositionSellerUser (state, data) {
      state.stateEditPositionSellerUser = data
    },
    mutationsAddPositionSellerUser (state, data) {
      state.stateAddPositionSellerUser = data
    },
    mutationsListPositionShop (state, data) {
      state.stateListPositionShops = data
    },
    mutationsEstimateQu (state, data) {
      state.stateEstimateQu = data
    },
    mutationsGetPayment (state, data) {
      state.stateGetPayment = data
    },
    mutationsCreateQUCart (state, data) {
      state.createQuCart = data
    },
    mutationsCreateCompanyByBiz (state, data) {
      state.stateCreateCompanyByBiz = data
    },
    mutationsDashboardAdminAll (state, data) {
      state.stateDashboardAdminAll = data
    },
    mutationsAllGP (state, data) {
      state.stateAllGP = data
    },
    // start admin panit manage
    mutationsGetListAdminPlatform (state, data) {
      state.stateGetListAdminPlatform = data
    },
    mutationsCreateAdminPlatform (state, data) {
      state.stateCreateAdminPlatform = data
    },
    mutationsEditAdminPlatform (state, data) {
      state.stateEditAdminPlatform = data
    },
    mutationsDetailAdminPlatform (state, data) {
      state.stateDetailAdminPlatform = data
    },
    mutationsDeleteAdminPlatform (state, data) {
      state.stateDeleteAdminPlatform = data
    },
    mutationsSearchAdminPlatform (state, data) {
      state.stateSearchAdminPlatform = data
    },
    mutationsGetDataQt (state, data) {
      state.stateGetDataQt = data
    },
    mutationsCreateQt (state, data) {
      state.stateCreateQt = data
    },
    mutationsCreateQtV2 (state, data) {
      state.stateCreateQtV2 = data
    },
    mutationsPersonalCompany (state, data) {
      state.statePersonalCompany = data
    },
    mutationsEditPersonalCompany (state, data) {
      state.stateEditPersonalCompany = data
    },
    mutationsEditQtV2 (state, data) {
      state.stateEditQtV2 = data
    },
    mutationsEditQTDescription (state, data) {
      state.stateEditQTDescription = data
    },
    mutationsGetShopData (state, data) {
      state.stateGetShopDataAdmin = data
    },
    mutationsAddAdminShop (state, data) {
      state.stateAddAdminShop = data
    },
    mutationsListQTBuyer (state, data) {
      state.stateListQTBuyer = data
    },
    mutationsDetailQTBuyer (state, data) {
      state.stateDetailQTBuyer = data
    },
    mutationsListBusinessAdmin (state, data) {
      state.stateListBusinessAdmin = data
    },
    mutationsAddAdminCompany (state, data) {
      state.stateAddAdminCompany = data
    },
    // Get Detail Cart in PO Company
    mutationsDetailCartInCompony (state, data) {
      state.stateDetailCartInCompony = data
    },
    mutationsCompanyApproveOrder (state, data) {
      state.stateCompanyApproveOrder = data
    },
    mutationsDetailLandingPage (state, data) {
      state.stateDetailLandingPage = data
    },
    mutationsMangeLandingPage (state, data) {
      state.stateMangeLandingPage = data
    },
    mutationsUserInSystem (state, data) {
      state.stateUserInSystem = data
    },
    mutationsGetAddressCompany (state, data) {
      state.stateGetAddressCompany = data
    },
    mutationsAddAddressCompany (state, data) {
      state.stateAddAddressCompany = data
    },
    mutationsUpdateAddressCompany (state, data) {
      state.stateUpdateAddressCompany = data
    },
    mutationsDeleteAddressCompany (state, data) {
      state.stateDeleteAddressCompany = data
    },
    mutationsSetDefaultAddressCompany (state, data) {
      state.stateSetDefaultAddressCompany = data
    },
    mutationseTaxAdmin (state, data) {
      state.stateeTaxAdmin = data
    },
    mutationsMerchantEdit (state, data) {
      state.stateMerchantEdit = data
    },
    mutationsStockProduct (state, data) {
      state.stateStockProduct = data
    },
    mutationsImportStock (state, data) {
      state.stateImportStock = data
    },
    mutationsStatusPvNo (state, data) {
      state.stateStatusPvNo = data
    },
    mutationsAddDataEwth (state, data) {
      state.stateAddDataEwth = data
    },
    mutationsEditDataEwth (state, data) {
      state.stateEditDataEwth = data
    },
    mutationsGetDataEwth (state, data) {
      state.stateGetDataEwth = data
    },
    mutationsGetVender (state, data) {
      state.stateGetVender = data
    },
    mutationsUpdateVender (state, data) {
      state.stateUpdateVender = data
    },
    mutationsGetSellerJoinAffiliate (state, data) {
      state.stateGetSellerJoinAffiliate = data
    },
    mutationsGetUserJoinSellerJAffiliate (state, data) {
      state.stateGetUserJoinSellerJAffiliate = data
    },
    mutationsGetToken (state, data) {
      state.stateGetToken = data
    },
    mutationsGetUserBySeller (state, data) {
      state.stateGetUserBySeller = data
    },
    // end admin panit manage
    // Affiliate
    mutationsGetUserJoinAffiliate (state, data) {
      state.stateGetUserJoinAffiliate = data
    },
    mutationsGetShopComissionAffiliateTable (state, data) {
      state.stateShopComissionAffiliateTable = data
    },
    mutationsGetUserComissionAffiliateTable (state, data) {
      state.stateUserComissionAffiliateTable = data
    },
    mutationsSetShopDetailUserCommissions (state, data) {
      state.shopDetailUserCommissions = data
    },
    mutationsUserActive (state, data) {
      state.stateUserActive = data
    },
    mutationsSetUserDetailCommissionsAffiliate (state, data) {
      state.UserDetailCommissionsAffiliate = data
    },
    mutationsGetOrderCommissionDetail (state, data) {
      state.orderCommissionDetail = data
    },
    mutationsGetProductSellerAffiliate (state, data) {
      state.stateGetProductSellerAffiliate = data
    },
    mutationsListAllCourier (state, data) {
      state.stateListAllCourier = data
    },
    mutationsEditCourier (state, data) {
      state.stateEditCourier = data
    },
    mutationsEditSellerShopShipping (state, data) {
      state.stateEditSellerShopShipping = data
    },
    mutationsApproveQuotation (state, data) {
      state.stateApproveQuotation = data
    },
    mutationsSearchUserShop (state, data) {
      state.stateSearchUserShop = data
    },
    mutationsManageListPosition (state, data) {
      state.stateManageListPosition = data
    },
    mutationsAddUserShop (state, data) {
      state.stateAddUserShop = data
    },
    mutationsGetCompanyData (state, data) {
      state.stateGetCompanyData = data
    },
    mutationsGetUserComapany (state, data) {
      state.stateGetUserComapany = data
    },
    mutationsSearchUserCompany (state, data) {
      state.stateSearchUserCompany = data
    },
    mutationsEditUserShop (state, data) {
      state.stateEditUserShop = data
    },
    mutationsListPositionCompany (state, data) {
      state.stateListPositionCompany = data
    },
    mutationsAddUserCompany (state, data) {
      state.stateAddUserCompany = data
    },
    mutationsEditUserCompany (state, data) {
      state.stateEditUserCompany = data
    },
    mutationsDeleteUserCompany (state, data) {
      state.stateDeleteUserCompany = data
    },
    mutationsDeleteUserShop (state, data) {
      state.stateDeleteUserShop = data
    },
    mutationsSetFlashSale (state, data) {
      state.stateSetFlashSale = data
    },
    mutationsStatusFlashSale (state, data) {
      state.stateStatusFlashSale = data
    },
    mutationsShopPartnerAdmin (state, data) {
      state.stateShopPartnerAdmin = data
    },
    mutationsDetailShopPartnerAdmin (state, data) {
      state.stateDetailShopPartnerAdmin = data
    },
    mutationsListPackagePartners (state, data) {
      state.stateListPackagePartners = data
    },
    mutationsCreateCouponPlatform (state, data) {
      state.stateCreateCouponPlatform = data
    },
    mutationsGetListCoupon (state, data) {
      state.stateGetListCoupon = data
    },
    mutationsChangeStatusCoupon (state, data) {
      state.stateChangeStatusCoupon = data
    },
    mutationsGetDetailCoupon (state, data) {
      state.stateGetDetailCoupon = data
    },
    mutationsEditCouponPlatform (state, data) {
      state.stateEditCouponPlatform = data
    },
    mutationsGetDetailCouponPlatform (state, data) {
      state.stateGetDetailCouponPlatform = data
    },
    mutationsDeleteCoupon (state, data) {
      state.stateDeleteCoupon = data
    },
    mutationsCurrentEditCouponPlatformIdPath (state, data) {
      state.stateEditCouponPlatformIdPath = data
    },
    mutationsEditGP (state, data) {
      state.stateEditGP = data
    },
    mutationsGetListServicePartner (state, data) {
      state.stateGetListServicePartner = data
    },
    mutationsGetJoinServicePartnerWithAdmin (state, data) {
      state.stateGetJoinServicePartnerWithAdmin = data
    },
    mutationsListCostCenter (state, data) {
      state.stateListCostCenter = data
    },
    mutationsGetDataReach (state, data) {
      state.stateGetDataReach = data
    },
    mutationsCreateDataReach (state, data) {
      state.stateCreateDataReach = data
    },
    mutationsOrderReach (state, data) {
      state.stateOrderReach = data
    },
    mutationsAdminApproveShop (state, data) {
      state.stateAdminApproveShop = data
    },
    mutationsAdminListPosition (state, data) {
      state.stateAdminListPosition = data
    },
    mutationsCreatePositionShop (state, data) {
      state.stateCreatePositionShop = data
    },
    mutationsSendMail (state, data) {
      state.stateSendMail = data
    },
    mutationsEditIcon (state, data) {
      state.stateEditIcon = data
    },
    mutationsgetDataIcon (state, data) {
      state.stategetDataIcon = data
    },
    mutationsGetDetailPurchaserV2 (state, data) {
      state.stateGetDetailPurchaserV2 = data
    },
    mutationsGetListCouponUser (state, data) {
      state.stateGetListCouponUser = data
    },
    mutationsSearchCoupon (state, data) {
      state.stateSearchCoupon = data
    },
    mutationsAddCouponUserAdmin (state, data) {
      state.stateAddCouponUserAdmin = data
    },
    mutationsOrderDeliveryCompany (state, data) {
      state.stateOrderDeliveryCompany = data
    },
    mutationsConfirmOrderDeliveryCompany (state, data) {
      state.stateConfirmOrderDeliveryCompany = data
    },
    mutationsSearchListUser (state, data) {
      state.stateSearchListUser = data
    },
    mutationsGetDataListNoti (state, data) {
      state.stateGetDataListNoti = data
    },
    mutationsCreateNoti (state, data) {
      state.stateCreateNoti = data
    },
    mutationsStoreRegisList (state, data) {
      state.stateStoreRegisList = data
    },
    mutationsStoreRegisDetail (state, data) {
      state.stateStoreRegisDetail = data
    },
    mutationsCreateNotiNow (state, data) {
      state.stateCreateNotiNow = data
    },
    mutationsDeleteNoti (state, data) {
      state.stateDeleteNoti = data
    },
    mutationsEditNoti (state, data) {
      state.stateEditNoti = data
    },
    mutationsSearchNoti (state, data) {
      state.stateSearchNoti = data
    },
    mutationsGetListType (state, data) {
      state.stateGetListType = data
    },
    mutationsSearchHistory (state, data) {
      state.stateSearchHistory = data
    },
    mutationsEditIconMobile (state, data) {
      state.stateEditIconMobile = data
    },
    mutationsgetDataIconMobile (state, data) {
      state.stategetDataIconMobile = data
    },
    mutationgetListCancelOrderAdmin (state, data) {
      state.stategetListCancelOrderAdmin = data
    },
    mutationGetListSortProduct (state, data) {
      state.stateGetListSortProduct = data
    },
    mutationUpdateSortProduct (state, data) {
      state.stateUpdateSortProduct = data
    },
    mutationListPaymentTransfer (state, data) {
      state.stateListPaymentTransfer = data
    },
    mutationUpdatePaymentTransfer (state, data) {
      state.stateUpdatePaymentTransfer = data
    },
    mutationsListRegisterInfoPartner (state, data) {
      state.stateListRegisterInfoPartner = data
    },
    mutationsDetailRegisterInfoPartner (state, data) {
      state.stateDetailRegisterInfoPartner = data
    },
    mutationsListsOrderCancelAdmin (state, data) {
      state.stateListsOrderCancelAdmin = data
    },
    mutationsUpdateCancelOrderList (state, data) {
      state.stateUpdateCancelOrderList = data
    },
    mutationsListRegisterInfoShop (state, data) {
      state.stateListRegisterInfoShop = data
    },
    mutationsDetailRegisterInfoShop (state, data) {
      state.stateDetailRegisterInfoShop = data
    },
    mutationsPrescription (state, data) {
      state.statePrescription = data
    },
    mutationsTransfer (state, data) {
      state.stateTransfer = data
    },
    mutationsListMyQTCompany (state, data) {
      state.stateListMyQTCompany = data
    },
    mutationsManageConsent (state, data) {
      state.stateManageConsent = data
    },
    mutationsEditConsent (state, data) {
      state.stateEditConsent = data
    }
  },
  actions: {
    // Create Company
    async actionsCreateCompany (context, access) {
      const response = await AxiosAdminManage.createCompany(access)
      await context.commit('mutationsCreateCompany', response)
    },
    // Edit Company
    async actionsEditCompany (context, access) {
      const response = await AxiosAdminManage.editCompany(access)
      await context.commit('mutationsEditCompany', response)
    },
    // Detail Company
    async actionsDetailCompany (context, access) {
      const response = await AxiosAdminManage.detailCompany(access)
      await context.commit('mutationsDetailCompany', response)
    },
    async actionsPersonalCompany (context, access) {
      const response = await AxiosAdminManage.detailPersonalCompany(access)
      await context.commit('mutationsPersonalCompany', response)
    },
    // async actionsEditPersonalCompany (context, access) {
    //   const response = await AxiosAdminManage.detailEditPersonalCompany(access)
    //   await context.commit('mutationsEditPersonalCompany', response)
    // },
    // List Company
    async actionslistCompany (context) {
      const response = await AxiosAdminManage.listCompany()
      await context.commit('mutationsListCompany', response)
    },
    // List User Company
    async actionsListUserCompany (context) {
      const response = await AxiosAdminManage.listUserCompany()
      await context.commit('mutationsListUserCompany', response)
    },
    // List PO For Buyer
    async actionsListPOBuyer (context, access) {
      const response = await AxiosAdminManage.listPOBuyer(access)
      await context.commit('mutationsListPOBuyer', response)
    },
    // Detail QU for buyer
    async actionsDetailQUBuyer (context, access) {
      const response = await AxiosAdminManage.DetailQUBuyer(access)
      await context.commit('mutationsDetailQUBuyer', response)
    },
    // Description QT Edit
    async actionsEditQTDescription (context, access) {
      const response = await AxiosAdminManage.EditQTDescription(access)
      await context.commit('mutationsEditQTDescription', response)
    },
    // update_status_buyer
    async actionsUpdateStatusBuyer (context, access) {
      const response = await AxiosAdminManage.UpdateStatusBuyer(access)
      await context.commit('mutationsUpdateStatusBuyer', response)
    },
    // list_order_purchaser
    async actionsListOrderPurchaser (context, access) {
      const response = await AxiosAdminManage.listOrderPurchaser(access)
      await context.commit('mutationsListOrderPurchaser', response)
    },
    async actionsTerminate (context, access) {
      const response = await AxiosAdminManage.Terminate(access)
      await context.commit('mutationsTerminate', response)
    },
    // detail_order_purchaser
    async actionsDetailOrderPurchaser (context, access) {
      const response = await AxiosAdminManage.detailOrderPurchaser(access)
      await context.commit('mutationsDetailOrderPurchaser', response)
    },
    // edit QU for buyer
    async actionsEditQU (context, access) {
      const response = await AxiosAdminManage.EditQU(access)
      await context.commit('mutationsEditQU', response)
    },
    async actionsGetDataQt (context, access) {
      const response = await AxiosAdminManage.GetDataQt(access)
      await context.commit('mutationsGetDataQt', response)
    },
    async actionsCreateQt (context, access) {
      const response = await AxiosAdminManage.CreateQt(access)
      await context.commit('mutationsCreateQt', response)
    },
    async actionsCreateQt_v2 (context, access) {
      const response = await AxiosAdminManage.CreateQtV2(access)
      await context.commit('mutationsCreateQtV2', response)
    },
    async actionsEditQt_V2 (context, access) {
      const response = await AxiosAdminManage.EditQtV2(access)
      await context.commit('mutationsEditQtV2', response)
    },
    // List Refund Product Company
    async actionsListRefundPurchaser (context, access) {
      const response = await AxiosAdminManage.ListRefundPurchaser(access)
      await context.commit('mutationsListRefundPurchaser', response)
    },
    // Detail Refund Product Company
    async actionsDetailRefundPurchaser (context, access) {
      const response = await AxiosAdminManage.DetailRefundPurchaser(access)
      await context.commit('mutationsDetailRefundPurchaser', response)
    },
    // (yaPalm) Credit Term
    async actionsListOrderCreditTerm (context, access) {
      const response = await AxiosAdminManage.listOrderCreditTerm(access)
      await context.commit('mutationsListOrderCreditTerm', response)
    },
    async actionsListCreditTerm (context, access) {
      const response = await AxiosAdminManage.listCreditTerm(access)
      await context.commit('mutationsListCreditTerm', response)
    },
    async actionsCalculateRequestCreditTerm (context, access) {
      const response = await AxiosAdminManage.calculateRequestCreditTerm(access)
      await context.commit('mutationsCalculateRequestCreditTerm', response)
    },
    async actionsCheckCalculate (context, access) {
      const response = await AxiosAdminManage.checkCalculate(access)
      await context.commit('mutationsCheckCalculate', response)
    },
    async actionsRequestChangeTerm (context, access) {
      const response = await AxiosAdminManage.requestChangeTerm(access)
      await context.commit('mutationsRequestChangeTerm', response)
    },
    async actionsCancelRequestChangeTerm (context, access) {
      const response = await AxiosAdminManage.cancelRequestChangeTerm(access)
      await context.commit('mutationsCancelRequestChangeTerm', response)
    },
    async actionsListSpecialPriceBuyer (context, access) {
      const response = await AxiosAdminManage.ListSpecialPriceBuyer(access)
      await context.commit('mutationsListSpecialPriceBuyer', response)
    },
    async actionsDetailSpecialPrice (context, access) {
      const response = await AxiosAdminManage.DetailSpecialPrice(access)
      await context.commit('mutationsDetailSpecialPrice', response)
    },
    // search product in modal special request buyer
    async actionsListProductAttribute (context, access) {
      const response = await AxiosAdminManage.ListProductAttribute(access)
      await context.commit('mutationsListProductAttribute', response)
    },
    // update ข้อมูลในตาราง
    async actionsUpdateListDataTableSpecialPrice (context, access) {
      const response = await AxiosAdminManage.UpdateListDataTableSpecialPrice(access)
      await context.commit('mutationsUpdateListDataTableSpecialPrice', response)
    },
    // อัปเดตสถานะกการร้องขอราคาพิเศษของบริษัท
    async actionsUpdateStatusSpecialPriceBuyer (context, access) {
      const response = await AxiosAdminManage.UpdateStatusSpecialPriceBuyer(access)
      await context.commit('mutationsUpdateStatusSpecialPriceBuyer', response)
    },
    async actionsRequestSpecialPrice (context, access) {
      const response = await AxiosAdminManage.RequestSpecialPrice(access)
      await context.commit('mutationsRequestSpecialPrice', response)
    },
    async actionsEditSpecialPriceBuyer (context, access) {
      const response = await AxiosAdminManage.EditSpecialPriceBuyer(access)
      await context.commit('mutationsEditSpecialPriceBuyer', response)
    },
    // Edit Quotation
    async actionsEditQuotation (context, access) {
      const response = await AxiosAdminManage.EditQuotation(access)
      await context.commit('mutationsEditQuotation', response)
    },
    async actionslistPositionOfCompany (context, access) {
      const response = await AxiosAdminManage.listPositionOfCompany(access)
      await context.commit('mutationslistPositionOfCompany', response)
    },
    async actionsdetailPositionOfCompany (context, access) {
      const response = await AxiosAdminManage.detailPositionOfCompany(access)
      await context.commit('mutationsdetailPositionOfCompany', response)
    },
    async actionscreatePositionOfCompany (context, access) {
      const response = await AxiosAdminManage.createPositionOfCompany(access)
      await context.commit('mutationscreatePositionOfCompany', response)
    },
    async actionseditPositionOfCompany (context, access) {
      const response = await AxiosAdminManage.editPositionOfCompany(access)
      await context.commit('mutationseditPositionOfCompany', response)
    },
    // List User In Seller
    async actionsListPositionSellerUser (context, access) {
      var responseData = await AxiosAdminManage.ListPositionUserSeller(access)
      await context.commit('mutatonListPositionSellerUser', responseData)
    },
    async actionsDetailUserPositionSeller (context, access) {
      var responseData = await AxiosAdminManage.DetailPositionUserSeller(access)
      await context.commit('mutationsDetailPositionSellerUser', responseData)
    },
    async actionsSearchUserPositionSeller (context, access) {
      var responseData = await AxiosAdminManage.SearchPositionUserSeller(access)
      await context.commit('mutationsSearchPositionSellerUser', responseData)
    },
    async actionsEditUserPositionSeller (context, access) {
      var responseData = await AxiosAdminManage.EditPositionUserSeller(access)
      await context.commit('mutationsEditPositionSellerUser', responseData)
    },
    async actionsAddUserPositionSeller (context, access) {
      var responseData = await AxiosAdminManage.AddPositionUserSeller(access)
      await context.commit('mutationsAddPositionSellerUser', responseData)
    },
    async actionsListPositionShop (context, access) {
      var responseData = await AxiosAdminManage.ListPositionShop(access)
      await context.commit('mutationsListPositionShop', responseData)
    },
    async actionsEstimateQu (context, access) {
      var responseData = await AxiosAdminManage.estimateQu(access)
      await context.commit('mutationsEstimateQu', responseData)
    },
    async actionsGetPayment (context, access) {
      var responseData = await AxiosAdminManage.GetPayment(access)
      await context.commit('mutationsGetPayment', responseData)
    },
    async actionsCreateQuCart (context, access) {
      var responseData = await AxiosAdminManage.CreateQUCart(access)
      await context.commit('mutationsCreateQUCart', responseData)
    },
    async actionsCreateCompanyByBiz (context) {
      var responseData = await AxiosAdminManage.CreateCompanyByBiz()
      await context.commit('mutationsCreateCompanyByBiz', responseData)
    },
    async actionsDashboardAdminAll (context, access) {
      // console.log('เข้า 1', access)
      var responseData = await AxiosAdminManage.DashBorardAdmin(access)
      await context.commit('mutationsDashboardAdminAll', responseData)
    },
    async actionsAllGP (context, access) {
      // console.log('เข้า 1', access)
      var responseData = await AxiosAdminManage.allGpOfsystem(access)
      await context.commit('mutationsAllGP', responseData)
    },
    // start admin panit manage
    async actionsGetListAdminPlatform (context) {
      var responseData = await AxiosAdminManage.GetListAdminPlatform()
      await context.commit('mutationsGetListAdminPlatform', responseData)
    },
    async actionsCreateAdminPlatform (context, access) {
      var responseData = await AxiosAdminManage.CreateAdminPlatform(access)
      await context.commit('mutationsCreateAdminPlatform', responseData)
    },
    async actionsEditAdminPlatform (context, access) {
      var responseData = await AxiosAdminManage.EditAdminPlatform(access)
      await context.commit('mutationsEditAdminPlatform', responseData)
    },
    async actionsDetailAdminPlatform (context, access) {
      var responseData = await AxiosAdminManage.DetailAdminPlatform(access)
      await context.commit('mutationsDetailAdminPlatform', responseData)
    },
    async actionsDeleteAdminPlatform (context, access) {
      var responseData = await AxiosAdminManage.DeleteAdminPlatform(access)
      await context.commit('mutationsDeleteAdminPlatform', responseData)
    },
    async actionsSearchAdminPlatform (context, access) {
      var responseData = await AxiosAdminManage.SearchAdminPlatform(access)
      await context.commit('mutationsSearchAdminPlatform', responseData)
    },
    async actionsGetShopDataAdmin (context) {
      var responseData = await AxiosAdminManage.GetShopDataAdmin()
      await context.commit('mutationsGetShopData', responseData)
    },
    async actionsAddAdminShop (context, access) {
      var responseData = await AxiosAdminManage.AddAdminShop(access)
      await context.commit('mutationsAddAdminShop', responseData)
    },
    async actionsListQTBuyer (context, access) {
      var responseData = await AxiosAdminManage.ListQTBuyer(access)
      await context.commit('mutationsListQTBuyer', responseData)
    },
    async actionsDetailQTBuyer (context, access) {
      var responseData = await AxiosAdminManage.DetailQTBuyer(access)
      await context.commit('mutationsDetailQTBuyer', responseData)
    },
    async actionsListBusinessAdmin (context) {
      var responseData = await AxiosAdminManage.ListBusinessAdmin()
      await context.commit('mutationsListBusinessAdmin', responseData)
    },
    async actionsAddAdminCompany (context, access) {
      var responseData = await AxiosAdminManage.AddAdminCompany(access)
      await context.commit('mutationsAddAdminCompany', responseData)
    },
    // Get Detail Cart in PO Company
    async actionsDetailCartInCompony (context, access) {
      var responseData = await AxiosAdminManage.GetDetailCartInCompany(access)
      await context.commit('mutationsDetailCartInCompony', responseData)
    },
    async actionsCompanyApproveOrder (context, access) {
      var responseData = await AxiosAdminManage.CompanyApproveOrder(access)
      await context.commit('mutationsCompanyApproveOrder', responseData)
    },
    async actionsDetailLandingPage (context, access) {
      var responseData = await AxiosAdminManage.DetailLandingPage(access)
      await context.commit('mutationsDetailLandingPage', responseData)
    },
    async actionsMangeLandingPage (context, access) {
      var responseData = await AxiosAdminManage.MangeLandingPage(access)
      await context.commit('mutationsMangeLandingPage', responseData)
    },
    async actionsUserInSystem (context) {
      var responseData = await AxiosAdminManage.UserInSystem()
      await context.commit('mutationsUserInSystem', responseData)
    },
    async actionsGetAddressCompany (context, access) {
      var responseData = await AxiosAdminManage.GetAddressCompany(access)
      await context.commit('mutationsGetAddressCompany', responseData)
    },
    async actionsAddAddressCompany (context, access) {
      var responseData = await AxiosAdminManage.AddAddressCompany(access)
      await context.commit('mutationsAddAddressCompany', responseData)
    },
    async actionsUpdateAddressCompany (context, access) {
      var responseData = await AxiosAdminManage.UpdateAddressCompany(access)
      await context.commit('mutationsUpdateAddressCompany', responseData)
    },
    async actionsDeleteAddressCompany (context, access) {
      var responseData = await AxiosAdminManage.DeleteAddressCompany(access)
      await context.commit('mutationsDeleteAddressCompany', responseData)
    },
    async actionsSetDefaultAddressCompany (context, access) {
      var responseData = await AxiosAdminManage.SetDefaultAddressCompany(access)
      await context.commit('mutationsSetDefaultAddressCompany', responseData)
    },
    async actionseTaxAdmin (context) {
      var responseData = await AxiosAdminManage.eTaxAdmin()
      await context.commit('mutationseTaxAdmin', responseData)
    },
    async actionsMerchantEdit (context, access) {
      var response = await AxiosAdminManage.MerchantEdit(access)
      await context.commit('mutationsMerchantEdit', response)
    },
    async actionsStockProduct (context, access) {
      var response = await AxiosAdminManage.StockProduct(access)
      await context.commit('mutationsStockProduct', response)
    },
    async actionsImportStock (context, access) {
      var response = await AxiosAdminManage.ImportStock(access)
      await context.commit('mutationsImportStock', response)
    },
    async actionsStatusPvNo (context, access) {
      var response = await AxiosAdminManage.StatusPvNo(access)
      await context.commit('mutationsStatusPvNo', response)
    },
    async actionsAddDataEwth (context, access) {
      var response = await AxiosAdminManage.AddDataEwth(access)
      await context.commit('mutationsAddDataEwth', response)
    },
    async actionsEditDataEwth (context, access) {
      var response = await AxiosAdminManage.EditDataEwth(access)
      await context.commit('mutationsEditDataEwth', response)
    },
    async actionsGetDataEwth (context, access) {
      var response = await AxiosAdminManage.GetDataEwth(access)
      await context.commit('mutationsGetDataEwth', response)
    },
    async actionsGetVender (context, access) {
      var response = await AxiosAdminManage.GetVender(access)
      await context.commit('mutationsGetVender', response)
    },
    async actionsUpdateVender (context, access) {
      var response = await AxiosAdminManage.UpdateVender(access)
      await context.commit('mutationsUpdateVender', response)
    },
    async actionsGetSellerJoinAffiliate (context, access) {
      var response = await AxiosAdminManage.GetSellerJoinAffiliate(access)
      await context.commit('mutationsGetSellerJoinAffiliate', response)
    },
    async actionsGetUserJoinSellerJAffiliate (context, access) {
      var response = await AxiosAdminManage.GetUserJoinSellerJAffiliate(access)
      await context.commit('mutationsGetUserJoinSellerJAffiliate', response)
    },
    async actionsGetToken (context, access) {
      var response = await AxiosAdminManage.GetTokenEmail(access)
      await context.commit('mutationsGetToken', response)
    },
    async actionsGetUserBySeller (context, access) {
      var response = await AxiosAdminManage.GetUserBySeller(access)
      await context.commit('mutationsGetUserBySeller', response)
    },
    // end admin panit manage
    // Affailiate
    async actionsGetUserJoinAffiliate (context, access) {
      var response = await AxiosAdminManage.GetUserJoinAffiliate(access)
      await context.commit('mutationsGetUserJoinAffiliate', response)
    },
    async actionsShopComissionAffiliateTable (context, access) {
      var response = await AxiosAdminManage.GetShopComissionAffiliateTable(access)
      await context.commit('mutationsGetShopComissionAffiliateTable', response)
    },
    async actionsUserComissionAffiliateTable (context, access) {
      var response = await AxiosAdminManage.GetUserComissionAffiliateTable(access)
      await context.commit('mutationsGetUserComissionAffiliateTable', response)
    },
    async actionsUserActive (context, access) {
      var response = await AxiosAdminManage.GetUserActive()
      await context.commit('mutationsUserActive', response)
    },
    async actionsGetOrderCommissionDetail (context, access) {
      var response = await AxiosAdminManage.GetOrderCommissionDetail(access)
      await context.commit('mutationsGetOrderCommissionDetail', response)
    },
    async actionsGetProductSellerAffiliate (context, access) {
      var response = await AxiosAdminManage.GetProductSellerAffiliate(access)
      await context.commit('mutationsGetProductSellerAffiliate', response)
    },
    async actionsListAllCourier (context, access) {
      var response = await AxiosAdminManage.ListAllCourier(access)
      await context.commit('mutationsListAllCourier', response)
    },
    async actionsEditCourier (context, access) {
      var response = await AxiosAdminManage.EditCourier(access)
      await context.commit('mutationsEditCourier', response)
    },
    async actionsEditSellerShopShipping (context, access) {
      var response = await AxiosAdminManage.EditSellerShopShipping(access)
      await context.commit('mutationsEditSellerShopShipping', response)
    },
    async actionsApproveQuotation (context, { access, token }) {
      var response = await AxiosAdminManage.ApproveQuotation(access, token)
      await context.commit('mutationsApproveQuotation', response)
    },
    async actionsSearchUserShop (context, access) {
      var response = await AxiosAdminManage.SearchUserShop(access)
      await context.commit('mutationsSearchUserShop', response)
    },
    async actionsManageListPosition (context, access) {
      var response = await AxiosAdminManage.ManageListPosition(access)
      await context.commit('mutationsManageListPosition', response)
    },
    async actionsAddUserShop (context, access) {
      var response = await AxiosAdminManage.AddUserShop(access)
      await context.commit('mutationsAddUserShop', response)
    },
    async actionGetCompanyData (context, access) {
      var response = await AxiosAdminManage.GetCompanyDataAdmin(access)
      await context.commit('mutationsGetCompanyData', response)
    },
    async actionsGetUserComapany (context, access) {
      var response = await AxiosAdminManage.GetUserComapany(access)
      await context.commit('mutationsGetUserComapany', response)
    },
    async actionsSearchUserCompany (context, access) {
      var response = await AxiosAdminManage.SearchUserCompany(access)
      await context.commit('mutationsSearchUserCompany', response)
    },
    async actionsEditUserShop (context, access) {
      var response = await AxiosAdminManage.EditUserShop(access)
      await context.commit('mutationsEditUserShop', response)
    },
    async actionsListPositionCompany (context, access) {
      var response = await AxiosAdminManage.ListPositionCompany(access)
      await context.commit('mutationsListPositionCompany', response)
    },
    async actionsAddUserCompany (context, access) {
      var response = await AxiosAdminManage.AddUserCompany(access)
      await context.commit('mutationsAddUserCompany', response)
    },
    async actionsEditUserCompany (context, access) {
      var response = await AxiosAdminManage.EditUserCompany(access)
      await context.commit('mutationsEditUserCompany', response)
    },
    async actionsDeleteUserCompany (context, access) {
      var response = await AxiosAdminManage.DeleteUserCompany(access)
      await context.commit('mutationsDeleteUserCompany', response)
    },
    async actionsDeleteUserShop (context, access) {
      var response = await AxiosAdminManage.DeleteUserShop(access)
      await context.commit('mutationsDeleteUserShop', response)
    },
    async actionsSetFlashSale (context, access) {
      var response = await AxiosAdminManage.SetFlashSale(access)
      await context.commit('mutationsSetFlashSale', response)
    },
    async actionsStatusFlashSale (context, access) {
      var response = await AxiosAdminManage.StatusFlashSale(access)
      await context.commit('mutationsStatusFlashSale', response)
    },
    async actionsShopPartnerAdmin (context, access) {
      var response = await AxiosAdminManage.ShopPartnerAdmin(access)
      await context.commit('mutationsShopPartnerAdmin', response)
    },
    async actionsDetailShopPartnerAdmin (context, access) {
      var response = await AxiosAdminManage.DetailShopPartnerAdmin(access)
      await context.commit('mutationsDetailShopPartnerAdmin', response)
    },
    async actionsListPackagePartners (context, access) {
      var response = await AxiosAdminManage.ListPackagePartners(access)
      await context.commit('mutationsListPackagePartners', response)
    },
    async actionsCreateCouponPlatform (context, access) {
      var response = await AxiosAdminManage.CreateCouponPlatform(access)
      await context.commit('mutationsCreateCouponPlatform', response)
    },
    async actionsGetListCoupon (context, access) {
      var response = await AxiosAdminManage.GetListCoupon(access)
      await context.commit('mutationsGetListCoupon', response)
    },
    async actionsChangeStatusCoupon (context, access) {
      var response = await AxiosAdminManage.ChangeStatusCoupon(access)
      await context.commit('mutationsChangeStatusCoupon', response)
    },
    async actionsDeleteCoupon (context, access) {
      var response = await AxiosAdminManage.DeleteCoupon(access)
      await context.commit('mutationsDeleteCoupon', response)
    },
    async actionsGetDetailCouponPlatform (context, access) {
      var response = await AxiosAdminManage.GetDetailCoupon(access)
      await context.commit('mutationsGetDetailCouponPlatform', response)
    },
    async actionsEditCouponPlatform (context, access) {
      var response = await AxiosAdminManage.EditCouponPlatform(access)
      await context.commit('mutationsEditCouponPlatform', response)
    },
    async actionsEditGP (context, access) {
      var response = await AxiosAdminManage.EditGP(access)
      await context.commit('mutationsEditGP', response)
    },
    async actionsGetListServicePartner (context, access) {
      var response = await AxiosAdminManage.GetListServicePartner(access)
      await context.commit('mutationsGetListServicePartner', response)
    },
    async actionsGetJoinServicePartnerWithAdmin (context, access) {
      var response = await AxiosAdminManage.GetJoinServicePartnerWithAdmin(access)
      await context.commit('mutationsGetJoinServicePartnerWithAdmin', response)
    },
    async actionsListCostCenter (context, access) {
      var response = await AxiosAdminManage.ListCostCenter(access)
      await context.commit('mutationsListCostCenter', response)
    },
    async actionsGetDataReach (context, access) {
      var response = await AxiosAdminManage.GetDataReach(access)
      await context.commit('mutationsGetDataReach', response)
    },
    async actionsCreateDataReach (context, access) {
      var response = await AxiosAdminManage.CreateDataReach(access)
      await context.commit('mutationsCreateDataReach', response)
    },
    async actionsOrderReach (context, access) {
      var response = await AxiosAdminManage.OrderReach(access)
      await context.commit('mutationsOrderReach', response)
    },
    async actionsAdminApproveShop (context, access) {
      var response = await AxiosAdminManage.AdminApproveShop(access)
      await context.commit('mutationsAdminApproveShop', response)
    },
    async actionsAdminListPosition (context, access) {
      var response = await AxiosAdminManage.AdminListPosition(access)
      await context.commit('mutationsAdminListPosition', response)
    },
    async actionsCreatePositionShop (context, access) {
      var response = await AxiosAdminManage.CreatePositionShop(access)
      await context.commit('mutationsCreatePositionShop', response)
    },
    async actionsSendMail (context, access) {
      var response = await AxiosAdminManage.SendMail(access)
      await context.commit('mutationsSendMail', response)
    },
    async actionsEditIcon (context, access) {
      var response = await AxiosAdminManage.EditIcon(access)
      await context.commit('mutationsEditIcon', response)
    },
    async actionsgetDataIcon (context, access) {
      var response = await AxiosAdminManage.getDataIcon(access)
      await context.commit('mutationsgetDataIcon', response)
    },
    async actionsGetDetailPurchaserV2 (context, access) {
      var response = await AxiosAdminManage.GetDetailPurchaserV2(access)
      await context.commit('mutationsGetDetailPurchaserV2', response)
    },
    async actionsGetListCouponUser (context, access) {
      var response = await AxiosAdminManage.GetListCouponUser(access)
      await context.commit('mutationsGetListCouponUser', response)
    },
    async actionsSearchCouponAdmin (context, access) {
      var response = await AxiosAdminManage.SearchCoupon(access)
      await context.commit('mutationsSearchCoupon', response)
    },
    async actionsAddCouponUserAdmin (context, access) {
      var response = await AxiosAdminManage.AddCouponUserAdmin(access)
      await context.commit('mutationsAddCouponUserAdmin', response)
    },
    async actionsOrderDeliveryCompany (context, access) {
      var response = await AxiosAdminManage.OrderDeliveryCompany(access)
      await context.commit('mutationsOrderDeliveryCompany', response)
    },
    async actionsConfirmOrderDeliveryCompany (context, access) {
      var response = await AxiosAdminManage.ConfirmOrderDeliveryCompany(access)
      await context.commit('mutationsConfirmOrderDeliveryCompany', response)
    },
    async actionsSearchListUser (context, access) {
      var response = await AxiosAdminManage.SearchListUser(access)
      await context.commit('mutationsSearchListUser', response)
    },
    async actionsGetDataListNoti (context, access) {
      var response = await AxiosAdminManage.GetDataListNoti(access)
      await context.commit('mutationsGetDataListNoti', response)
    },
    async actionsCreateNoti (context, access) {
      var response = await AxiosAdminManage.CreateNoti(access)
      await context.commit('mutationsCreateNoti', response)
    },
    async actionsStoreRegisList (context, access) {
      var response = await AxiosAdminManage.StoreRegisList(access)
      await context.commit('mutationsStoreRegisList', response)
    },
    async actionsStoreRegisDetail (context, access) {
      var response = await AxiosAdminManage.StoreRegisDetail(access)
      await context.commit('mutationsStoreRegisDetail', response)
    },
    async actionsCreateNotiNow (context, access) {
      var response = await AxiosAdminManage.CreateNotiNow(access)
      await context.commit('mutationsCreateNotiNow', response)
    },
    async actionsDeleteNoti (context, access) {
      var response = await AxiosAdminManage.DeleteNoti(access)
      await context.commit('mutationsDeleteNoti', response)
    },
    async actionsEditNoti (context, access) {
      var response = await AxiosAdminManage.EditNoti(access)
      await context.commit('mutationsEditNoti', response)
    },
    async actionsSearchNoti (context, access) {
      var response = await AxiosAdminManage.SearchNoti(access)
      await context.commit('mutationsSearchNoti', response)
    },
    async actionsGetListType (context, access) {
      var response = await AxiosAdminManage.GetListType(access)
      await context.commit('mutationsGetListType', response)
    },
    async actionsSearchHistory (context, access) {
      var response = await AxiosAdminManage.SearchHistory(access)
      await context.commit('mutationsSearchHistory', response)
    },
    async actionsEditIconMobile (context, access) {
      var response = await AxiosAdminManage.EditIconMobile(access)
      await context.commit('mutationsEditIconMobile', response)
    },
    async actionsgetDataIconMobile (context, access) {
      var response = await AxiosAdminManage.getDataIconMobile(access)
      await context.commit('mutationsgetDataIconMobile', response)
    },
    async actionsgetListCancelOrderAdmin (context, access) {
      var response = await AxiosAdminManage.getListCancelOrderAdmin(access)
      await context.commit('mutationgetListCancelOrderAdmin', response)
    },
    async actionsGetListSortProduct (context, access) {
      var response = await AxiosAdminManage.GetListSortProduct(access)
      await context.commit('mutationGetListSortProduct', response)
    },
    async actionsUpdateSortProduct (context, access) {
      var response = await AxiosAdminManage.UpdateSortProduct(access)
      await context.commit('mutationUpdateSortProduct', response)
    },
    async actionsListPaymentTransfer (context, access) {
      var response = await AxiosAdminManage.ListPaymentTransfer(access)
      await context.commit('mutationListPaymentTransfer', response)
    },
    async actionsUpdatePaymentTransfer (context, access) {
      var response = await AxiosAdminManage.UpdatePaymentTransfer(access)
      await context.commit('mutationUpdatePaymentTransfer', response)
    },
    async actionsListRegisterInfoPartner (context, access) {
      var response = await AxiosAdminManage.ListRegisterInfoPartner(access)
      await context.commit('mutationsListRegisterInfoPartner', response)
    },
    async actionsDetailRegisterInfoPartner (context, access) {
      var response = await AxiosAdminManage.DetailRegisterInfoPartner(access)
      await context.commit('mutationsDetailRegisterInfoPartner', response)
    },
    async actionsListsOrderCancelAdmin (context, access) {
      var response = await AxiosAdminManage.ListsOrderCancelAdmin(access)
      await context.commit('mutationsListsOrderCancelAdmin', response)
    },
    async actionsUpdateCancelOrderList (context, access) {
      var response = await AxiosAdminManage.UpdateCancelOrderList(access)
      await context.commit('mutationsUpdateCancelOrderList', response)
    },
    async actionsListRegisterInfoShop (context, access) {
      var responseData = await AxiosAdminManage.ListRegisterInfoShop(access)
      await context.commit('mutationsListRegisterInfoShop', responseData)
    },
    async actionsDetailRegisterInfoShop (context, access) {
      var responseData = await AxiosAdminManage.DetailRegisterInfoShop(access)
      await context.commit('mutationsDetailRegisterInfoShop', responseData)
    },
    async actionsPrescription (context, access) {
      var responseData = await AxiosAdminManage.Prescription(access)
      await context.commit('mutationsPrescription', responseData)
    },
    async actionsTransfer (context, access) {
      var responseData = await AxiosAdminManage.Transfer(access)
      await context.commit('mutationsTransfer', responseData)
    },
    async actionsListMyQTCompany (context, access) {
      var responseData = await AxiosAdminManage.ListMyQTCompany(access)
      await context.commit('mutationsListMyQTCompany', responseData)
    },
    async actionsManageConsent (context, access) {
      var responseData = await AxiosAdminManage.ManageConsent(access)
      await context.commit('mutationsManageConsent', responseData)
    },
    async actionsEditConsent (context, access) {
      var responseData = await AxiosAdminManage.EditConsents(access)
      await context.commit('mutationsEditConsent', responseData)
    }
  }
}

export default ModuleAdminManage
