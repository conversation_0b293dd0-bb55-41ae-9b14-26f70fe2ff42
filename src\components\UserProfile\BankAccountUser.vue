<template>
  <v-container class="pa-2">
    <v-card width="100%" min-height="700" class="mb-4" elevation="0">
      <v-card-text>
        <div>
          <v-row dense>
            <v-col class="d-flex" cols="12">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="mr-auto pt-2" v-if="!MobileSize">{{ $t('BankAccountPage.header') }}</span>
              <span style="font-weight: bold; font-size: 16px; line-height: 32px; color: #333333;" class="mr-auto pt-2" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUser()">mdi-chevron-left</v-icon> {{ $t('BankAccountPage.header') }}</span>
              <v-btn v-if="AccountData.length !== 10" @click="openDialogEdit('add')" color="#27AB9C" rounded :width="$i18n.locale === 'th' ? 126 : 155" height="40" dark class="ml-auto"><v-icon left>mdi-plus-circle-outline</v-icon>{{ $t('BankAccountPage.addAccount') }}</v-btn>
              <span v-else class="pt-1" style="font-size: 16px; font-weight: 500; color: #989898; line-height: 32px;"><v-icon size="20" color="#989898" class="pr-2">mdi-information-outline</v-icon>{{ $t('BankAccountPage.overTenAccount') }}</span>
            </v-col>
          </v-row>
          <v-row dense class="mt-9" v-if="AccountData.length !== 0">
            <v-col cols="12" class="pt-2" v-for="(item, index) in AccountData" :key="index">
              <v-card min-height="149" elevation="0" outlined :style="item.main_account === '1' ? 'border-color: #27AB9C; border-radius: 8px;' : 'border-color: #C4C4C4; border-radius: 8px;'">
                <v-row class="pa-4">
                  <v-col cols="12" md="12">
                    <v-row dense class="pb-3">
                      <v-col cols="12" md="12" class="d-flex">
                        <div class="mr-auto">
                          <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/account.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; font-size: 18px;">{{ $t('BankAccountPage.index') }}</span><span style="font-weight: 700; font-size: 18px; color:#27AB9C ;"> {{ index + 1 }}</span>
                        </div>
                        <div class="ml-auto">
                          <div v-if="!MobileSize">
                            <v-btn :class="$i18n.locale === 'th' ? '' : 'mr-3'" color="#27AB9C" height="20" width="56" text elevation="0" @click="openDialogEdit('edit', item)"><v-icon color="#27AB9C" size="20">mdi-pencil-outline</v-icon><span v-if="!MobileSize">{{ $t('BankAccountPage.editAccount') }}</span></v-btn>
                            <v-btn @click="openDialogEdit('delete', item)" v-if="AccountData.length !== 1" height="20" width="41" text :disabled="item.main_account === '1'" color="#ff0303" elevation="0"><v-icon size="20" color="#ff0303">mdi-delete-outline</v-icon><span v-if="!MobileSize">{{ $t('BankAccountPage.deleteAccount') }}</span></v-btn>
                          </div>
                          <v-row dense v-else class="d-flex">
                            <v-col cols="6" :class="$i18n.locale === 'th' ? '' : 'mr-3'">
                              <v-btn outlined color="#27AB9C" height="30" :width="!MobileSize ? '56' : '30'" :icon="!MobileSize ? false : true" elevation="0" @click="openDialogEdit('edit', item)"><v-icon color="#27AB9C" size="20">mdi-pencil-outline</v-icon><span v-if="!MobileSize">{{ $t('BankAccountPage.editAccount') }}</span></v-btn>
                            </v-col>
                            <v-col cols="6">
                              <v-btn outlined v-if="AccountData.length !== 1" :icon="!MobileSize ? false : true" height="30" :width="!MobileSize ? '41' : '30'" :disabled="item.main_account === '1'" color="#ff0303" elevation="0" @click="openDialogEdit('delete', item)"><v-icon size="20" color="#ff0303">mdi-delete-outline</v-icon><span v-if="!MobileSize">{{ $t('BankAccountPage.deleteAccount') }}</span></v-btn>
                            </v-col>
                          </v-row>
                        </div>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="IpadSize || MobileSize ? '12' : '6'" class="d-flex">
                    <v-row dense class="pl-2">
                      <v-col :cols="IpadSize || MobileSize || IpadProSize ? '3' : '2'" class="d-flex align-center">
                        <span style="color: #333333; font-weight: bold; font-size: 17px; line-height: 32px;">{{ $t('BankAccountPage.accountName') }}: </span>
                      </v-col>
                      <v-col cols="8">
                        <v-text-field v-model="item.bank_user_name" dense hide-details outlined readonly></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="IpadSize || MobileSize ? '12' : '6'" class="d-flex">
                    <v-row dense :class="IpadSize || MobileSize ? 'pl-2' : ''">
                      <v-col :cols="IpadSize || MobileSize || IpadProSize ? '3' : '2'" class="d-flex align-center">
                        <span style="color: #333333; font-weight: bold; font-size: 17px; line-height: 32px;">{{ $t('BankAccountPage.bankName') }}: </span>
                      </v-col>
                      <v-col cols="8">
                        <v-text-field v-model="item.bank_name" dense hide-details outlined readonly></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="IpadSize || MobileSize ? '12' : '6'" class="d-flex">
                    <v-row dense :class="MobileSize || IpadSize || IpadProSize ? 'pl-2' : 'pl-3'">
                      <v-col :cols="IpadSize || MobileSize ? '3' : IpadProSize ? '3' : '2'" class="d-flex align-center">
                        <span style="color: #333333; font-weight: bold; font-size: 17px; line-height: 32px;">{{ $t('BankAccountPage.branch') }}: </span>
                      </v-col>
                      <v-col cols="8">
                        <v-text-field v-model="item.bank_branch" dense hide-details outlined readonly></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col :cols="IpadSize || MobileSize ? '12' : '6'" class="d-flex">
                    <v-row dense  >
                      <v-col :cols="IpadSize || MobileSize ? '3' : IpadProSize ? '3' : '2'" class="d-flex align-center">
                        <span style="color: #333333; font-weight: bold; font-size: 17px; line-height: 32px;">{{ $t('BankAccountPage.accountNumber') }}: </span>
                      </v-col>
                      <v-col cols="8">
                        <v-text-field v-model="item.bank_no" dense hide-details outlined readonly></v-text-field>
                      </v-col>
                    </v-row>
                  </v-col>
                  <v-col cols="12" md="12" class="pb-0 pt-1">
                    <v-radio-group v-model="item.main_account">
                      <v-radio
                        color="#27AB9C"
                        :label="$t('BankAccountPage.accountDefault')"
                        value="1"
                        :disabled="item.main_account === '1' ? true : false"
                        @click="changeMainAccount(item.id)"
                        style="color: #333333"
                        ></v-radio>
                    </v-radio-group>
                  </v-col>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row justify="center" dense class="pt-12" v-else>
            <v-col cols="12" class="pt-12" align="center">
              <v-img contain :src="require('@/assets/Marketplace_partner/noinfoPaymentPartner.png')" height="100%" width="100%" max-height="250" max-width="318" class="mb-6"></v-img>
              <p style="font-size: 18px; font-weight: 600; line-height: 26px; color: #636363;">{{ $t('BankAccountPage.NoFound') }}</p>
              <span style="font-size: 16px; font-weight: 400; line-height: 26px; color: #9A9A9A;">{{ $t('BankAccountPage.Press') }}</span><span style="font-size: 18px; font-weight: 600; line-height: 26px; color: #1B5DD6; text-decoration: underline;">{{ $t('BankAccountPage.addAccount') }}</span><span style="font-size: 16px; font-weight: 400; line-height: 26px; color: #9A9A9A;">เพื่อเพิ่มบัญชีธนาคารของคุณ</span>
            </v-col>
          </v-row>
        </div>
      </v-card-text>
    </v-card>
    <v-dialog v-model="dialogEdit" persistent :width="MobileSize ? '350' : '600'" style="border-radius: 24px;">
      <v-card style="background: #FFFFFF; border-top-left-radius: 24px; border-top-right-radius: 24px; z-index: 1;" width="100%" height="100%">
          <v-img height="120px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')" style="opacity: 0.93;">
            <v-app-bar flat color="rgba(0, 0, 0, 0)" style="margin-top: 20px;" class="mx-4">
              <v-spacer></v-spacer>
              <v-toolbar-title style="color: #FFFFFF; font-size: 24px; font-weight: 700; text-align: center;">{{ type === 'add' ? this.$t('BankAccountPage.addAccount') : this.$t('BankAccountPage.editAccountModal') }}</v-toolbar-title>
              <v-spacer></v-spacer>
              <v-btn style="" width="24" height="24" color="#fff" icon
                @click="closeDialogEdit">
                <v-icon size="16">mdi-close</v-icon>
              </v-btn>
            </v-app-bar>
          </v-img>
      </v-card>
      <v-card style="background: #FFFFFF; border-top-left-radius: 24px; border-top-right-radius: 24px; z-index: 3;" width="100%" height="100%">
        <v-card-text style="border-radius: 24px; margin-top: -20px;">
          <v-form ref="form" v-model="valid">
            <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center">
              <!-- ส่วน บัญชีของฉัน -->
              <v-col cols="12" style="display: flex; align-items: center;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <img class="mr-3" src="@/assets/ImageINET-Marketplace/ICONProfile/account.png" style="width: 24px; height: 24px;" /><span style="font-weight: 700; color: #333333;" :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'">{{ $t('BankAccountPage.headerModal') }}</span>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('BankAccountPage.accountNameModal') }}</span>
                <v-text-field lazy-rules @keypress="CheckSpacebarOne($event)" class="mb-3" dense v-model="bankUsername" outlined :placeholder="$t('BankAccountPage.PlaceHolderAccountName')" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankusername"></v-text-field>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('BankAccountPage.bankNameModal') }}</span>
                <v-select lazy-rules @change="handleBankName" class="mb-3" dense v-model="bankCode" :items="listBank" item-value="code" item-text="name" outlined :placeholder="$t('BankAccountPage.PlaceHolderBankName')" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankname"></v-select>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px;">{{ $t('BankAccountPage.branchModal') }}</span>
                <v-text-field @keypress="CheckSpacebarOne($event)" lazy-rules class="mb-3" dense v-model="bankBranch" outlined :placeholder="$t('BankAccountPage.PlaceHolderBranch')" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankbranch"></v-text-field>
              </v-col>
              <v-col cols="12" style="display: flex; flex-direction: column; justify-content: start;" :class="MobileSize ? 'pa-0 py-2 px-1' : ''">
                <span style="text-align: left; color: #333333; font-weight: 400; font-size: 16px; d">{{ $t('BankAccountPage.accountNumberModal') }}</span>
                <v-text-field :maxLength="checkLengthValid" lazy-rules class="mb-3" dense v-model="bankNo" outlined :placeholder="$t('BankAccountPage.PlaceHolderAccountNumber')" style="height: 42px; border-radius: 8px; border: 1px solid #EBEEF2; color: #333333; font-size: 14px;" :rules="Rules.bankno" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1').substring(0, 15)"></v-text-field>
              </v-col>
            </v-col>
          </v-row>
          </v-form>
        </v-card-text>
      </v-card>
      <v-card class="pt-5" style="background: #FFFFFF; border-bottom-left-radius: 24px; border-bottom-right-radius: 24px; z-index: 1; margin-top: -10px; background-color: #F5FCFB;" elevation="0" width="100%" height="88px">
        <v-card-actions>
          <v-row dense class="pb-4 mx-2">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogEdit">{{ $t('ShopPage.Cancel') }}</v-btn>
            <v-spacer></v-spacer>
            <v-btn :disabled="(bankUsername === '' || bankNo === '' || bankCode === '') || !valid" width="110" height="40" rounded color="#27AB9C" class="white--text" @click="dialogConfirm = true">{{ $t('ListCoupon.Save') }}</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirm" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar color="#FFFBF5" height="240" dark dense elevation="0" width="100%">
          <div style="width: 100%;">
            <div class="d-flex justify-end">
              <v-btn width="32" icon dark @click="dialogConfirm = false">
              <v-icon size="20" color="#CCCCCC">mdi-close</v-icon>
            </v-btn>
            </div>
            <div class="d-flex justify-center" style="margin-top: -20px;">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONProfile/warning.png')" style="max-width: 196px;" />
            </div>
          </div>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-2">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: 24px; font-weight: 700; line-height: 3; color: #333333;">{{ type === 'add' ? this.$t('BankAccountPage.addAwait') : type === 'delete' ? this.$t('BankAccountPage.deleteAwait') : this.$t('BankAccountPage.editAwait') }}{{ $t('BankAccountPage.MyAccount') }}</span><br>
              <span style="font-size: 16px; font-weight: 400;"> {{ type === 'delete' ? this.$t('BankAccountPage.Proceed') : this.$t('BankAccountPage.Review')  }}</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4 mb-2">
            <v-btn width="150" height="40" outlined rounded color="#27AB9C" @click="dialogConfirm = false" class="mr-2">{{ $t('ShopPage.Cancel') }}</v-btn>
            <v-btn width="150" height="40" rounded color="#27AB9C" class="white--text" @click="type === 'add' ? createAccount() : type === 'delete' ? deleteAccount() : EditAccount()">{{ $t('ShopPage.Confirm') }}</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog Success -->
    <v-dialog v-model="dialogSuccess" persistent width="424">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar color="#F8FFF5" height="240" dark dense elevation="0" width="100%">
          <div style="width: 100%;">
            <div class="d-flex justify-end">
              <v-btn width="32" icon dark @click="closeDialogSuccess()">
              <v-icon size="20" color="#CCCCCC">mdi-close</v-icon>
            </v-btn>
            </div>
            <div class="d-flex justify-center" style="margin-top: -20px;">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONProfile/success.png')" style="max-width: 196px;" />
            </div>
          </div>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-2">
            <v-col cols="12" align="center" class="pb-0">
              <span style="font-size: 24px; font-weight: 700; line-height: 3; color: #333333;">{{ type === 'add' ? $t('BankAccountPage.addAwait') : type === 'delete' ? $t('BankAccountPage.deleteAwait') : $t('BankAccountPage.editAwait') }}{{ $t('BankAccountPage.Success') }}</span><br>
              <span style="font-size: 16px; font-weight: 400;">{{ $t('BankAccountPage.DetailSuccess1') }}{{ type === 'add' ? this.$t('BankAccountPage.addAwait') : type === 'delete' ? this.$t('BankAccountPage.deleteAwait') : this.$t('BankAccountPage.editAwait') }}{{ $t('BankAccountPage.DetailSuccess2') }}</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4 mb-2">
            <v-btn width="320" height="40" rounded color="#27AB9C" class="white--text" @click="closeDialogSuccess()">{{ $t('ShopPage.Confirm') }}</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      valid: true,
      id: null,
      type: '',
      listBank: [],
      dialogSuccess: false,
      dialogConfirm: false,
      dialogEdit: false,
      bankBranch: '',
      bankUsername: '',
      bankNo: '',
      bankCode: '',
      bankName: '',
      AccountData: [],
      Rules: {
        bankusername: [
          v => !!v || this.$t('BankAccountPage.validateAccountName1'),
          v => /[^฿@#$%&*()_+{}:;<>,.?~]+$/.test(v) || this.$t('BankAccountPage.validateAccountName2')
        ],
        bankname: [
          v => !!v || this.$t('BankAccountPage.validateBankName1')
        ],
        bankno: [
          v => !!v || this.$t('BankAccountPage.validateAccountNumber1'),
          v => v.length > 9 || this.$t('BankAccountPage.validateAccountNumber2')
        ],
        bankbranch: [
          v => !!v || this.$t('BankAccountPage.validateBranch1')
        ]
      }
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavAccount')
    if (localStorage.getItem('oneData') === null) {
      localStorage.removeItem('oneData')
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (onedata.user !== undefined) {
        this.getListAccountBank()
      } else {
        localStorage.removeItem('oneData')
        this.$router.push({ path: '/' }).catch(() => {})
      }
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/BankAccountUserMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/BankAccountUser' }).catch(() => {})
      }
    },
    dialogEdit () {
      this.$refs.form.resetValidation()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    checkLengthValid () {
      if (this.bankCode === '030') {
        return 15
      } else if (this.bankCode === '033' || this.bankCode === '034') {
        return 12
      } else {
        return 10
      }
    }
  },
  methods: {
    async getListAccountBank () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListAccountBankUser')
      var response = await this.$store.state.ModuleUser.stateListAccountBankUser
      if (response.result === 'SUCCESS') {
        this.AccountData = response.data
        this.$store.commit('closeLoader')
      } else {
        await this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    backtoUser () {
      this.$router.push({ path: '/userprofileMobile' }).catch(() => {})
    },
    async openDialogEdit (type, data) {
      this.type = type
      if (type === 'add') {
        this.bankUsername = ''
        this.bankNo = ''
        this.bankCode = ''
        this.bankName = ''
        this.bankBranch = ''
        await this.getListBank()
        this.dialogEdit = true
      } else if (type === 'edit') {
        this.bankUsername = data.bank_user_name
        this.bankNo = data.bank_no
        this.bankCode = data.bank_code
        this.bankName = data.bank_name
        this.bankBranch = data.bank_branch
        this.id = data.id
        await this.getListBank()
        this.dialogEdit = true
      } else if (type === 'delete') {
        this.id = data.id
        this.dialogConfirm = true
      }
    },
    closeDialogEdit () {
      this.dialogEdit = false
      this.type = ''
      this.bankUsername = ''
      this.bankNo = ''
      this.bankCode = ''
      this.bankName = ''
      this.bankBranch = ''
      this.id = null
    },
    async getListBank () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListBank')
      var response = await this.$store.state.ModuleShop.stateListBank
      // console.log(response)
      if (response.code === 200) {
        this.listBank = response.data
      }
      this.$store.commit('closeLoader')
    },
    async EditAccount () {
      this.dialogEdit = false
      this.dialogConfirm = false
      this.$store.commit('openLoader')
      var data = {
        id: this.id,
        bank_user_name: this.bankUsername,
        bank_no: this.bankNo,
        bank_name: this.bankName,
        bank_code: this.bankCode,
        bank_branch: this.bankBranch
      }
      await this.$store.dispatch('actionsUpdateAccountBankUser', data)
      var response = await this.$store.state.ModuleUser.stateUpdateAccountBankUser
      if (response.result === 'SUCCESS') {
        await this.$store.commit('closeLoader')
        this.dialogSuccess = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async createAccount () {
      this.dialogEdit = false
      this.dialogConfirm = false
      this.$store.commit('openLoader')
      var data = {
        bank_user_name: this.bankUsername,
        bank_no: this.bankNo,
        bank_name: this.bankName,
        bank_code: this.bankCode,
        bank_branch: this.bankBranch
      }
      await this.$store.dispatch('actionsCreateAccountBankUser', data)
      var response = await this.$store.state.ModuleUser.stateCreateAccountBankUser
      if (response.result === 'SUCCESS') {
        await this.$store.commit('closeLoader')
        this.dialogSuccess = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async deleteAccount () {
      this.dialogConfirm = false
      this.$store.commit('openLoader')
      var data = {
        id: this.id
      }
      await this.$store.dispatch('actionsDeleteAccountBankUser', data)
      var response = await this.$store.state.ModuleUser.stateDeleteAccountBankUser
      if (response.result === 'SUCCESS') {
        await this.$store.commit('closeLoader')
        this.dialogSuccess = true
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    handleBankName (val) {
      this.bankCode = val
      this.bankNo = ''
      const selected = this.listBank.find(b => b.code === this.bankCode)
      this.bankName = selected ? selected.name : ''
      // console.log('bankCode', this.bankCode)
      // console.log('bankName', this.bankName)
    },
    closeDialogSuccess () {
      this.dialogSuccess = false
      this.getListAccountBank()
    },
    async changeMainAccount (id) {
      this.$store.commit('openLoader')
      var data = {
        id: id
      }
      await this.$store.dispatch('actionsChangeMainAccountBankUser', data)
      var response = await this.$store.state.ModuleUser.stateChangeMainAccountBankUser
      if (response.result === 'SUCCESS') {
        // await this.$store.commit('closeLoader')
        this.getListAccountBank()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    CheckSpacebarOne (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    }
  }

}
</script>

<style scoped>
::v-deep .v-btn {
  text-transform: none;
}
</style>
