<template>
  <div class="mt-4">
    <div class="ml-3 mb-6">
      <span style="font-size: 16px; color: #333333; font-weight: 600;">ตารางแสดงข้อมูลยอดขายทั้งหมด</span>
    </div>
    <v-row>
     <!--  <v-col cols="12" >
    <v-list-item>
      <v-list-item-title ><v-row justify="center" ><v-col cols="3"></v-col> <v-col>
        <ldsFacebook />
        </v-col><v-col cols="3"></v-col></v-row></v-list-item-title>
      </v-list-item>
    </v-col> -->
  <v-col cols="12">
   <table class="table-striped" id="example" >
     <thead>
        <tr>
          <th class="fontData">
            รหัสผู้ซื้อ
          </th>
          <th class="fontData">
            ชื่อผู้ซื้อ
          </th>
          <th class="fontData">
            รหัสบริษัท
          </th>
          <th class="fontData">
            ชื่อบริษัท
          </th>
          <th class="fontData">
            วันที่ทำรายการ
          </th>
          <th class="fontData">
            เลขที่ทำรายการ
          </th>
          <th class="fontData">
            รหัสสินค้า
          </th>
          <th class="fontData">
            รหัสร้านค้า
          </th>
          <th class="fontData">
            เลข SKU
          </th>
          <th class="fontData">
            หน่วย
          </th>
          <th class="fontData">
            มูลค่า
          </th>
        </tr>
      </thead>
    <tbody>
      <tr v-for="item in dataRes" :key="item.name">
        <td><span class="dot">{{item.buyer_ID}}</span></td>
        <td ><span class="dot2">{{item.buyer_name}}</span></td>
        <td >{{item.buyer_org_ID === -1 ? '-' : item.buyer_org_ID === 0 ? '-' : item.buyer_org_ID === null ? '-' : item.buyer_org_ID }}</td>
        <td >{{item.buyer_org_name === null ? '-' : item.buyer_org_name}}</td>
        <td >{{item.date}}</td>
        <td ><span class="dot2">{{item.order_number}}</span></td>
        <td >{{item.product_id}}</td>
        <td >{{item.shop_ID }}</td>
        <td >{{item.sku}}</td>
        <td >{{item.unit }}</td>
        <td >{{item.value }}</td>
      </tr>
    </tbody>
  </table>
  </v-col>
  </v-row>
 <!--  <v-row class="mobile ma-0 pb-0">
  <v-col cols="7" class="pb-0 mb-0">
   <v-text-field
            v-model="search"
            label=""
            outlined
            dense
            class="pt-2 mt-0 "
            placeholder="ค้นหา"
             clearable
             append-icon="mdi-magnify"
             @click:append="onTest()"
          >
          </v-text-field>
  </v-col>
  <v-col cols="5">
    <div >
    <v-tooltip bottom color="rgba(255, 255, 255, .1)">
        <template v-slot:activator="{ on, attrs }">
        <v-icon
          color="success"
          dark
          v-bind="attrs"
          v-on="on"
          class="pt-4"
          style="margin-left: -18px;"
        >
          mdi-information-outline
        </v-icon>
      </template>
      <img
         src="../../assets/icons/tooltip.png"
         alt="John"
         width="241px"
         height="360px"
        >
    </v-tooltip>
  </div>
</v-col>
</v-row> -->
</div>
</template>
<script>
// import 'jquery/dist/jquery.min.js'
// import { jquery, jqDatatable, dataTableBTN, jszip, pdf, vfsFonts, html5, print } from '../library/dataTableNet.js'
import dataMap from '../library/TestTable.json'
import 'datatables.net'
import 'datatables.net-dt/css/jquery.dataTables.min.css'
import 'jszip'
import 'datatables.net-buttons-dt'
import 'datatables.net-buttons-dt/css/buttons.dataTables.min.css'
import 'datatables.net-buttons/js/buttons.colVis'
import 'datatables.net-buttons/js/buttons.flash'
import 'datatables.net-buttons/js/buttons.html5'
import 'datatables.net-buttons/js/buttons.print'
import 'datatables.net-buttons/js/dataTables.buttons'
import 'datatables.net-responsive-dt'
import $ from 'jquery'
window.JSZip = require('jszip')
export default {
  components: {
    // ldsFacebook: () => import('@/components/loading/lds-facebook.vue')
  },
  data: () => ({
    dataRes: [],
    dataMobile: [],
    toDay: new Date().toISOString().slice(0, 10),
    Day: `${new Date().toISOString().slice(0, 7)}-01`,
    dataMain: dataMap,
    search: '',
    headerProps: {
      sortByText: 'เรียงตาม'
    },
    isMobile: false,
    loading: false
  }),
  created () {
    this.init()
    this.loading = true
  },
  mounted () {
    this.init()
  },
  computed: {
    // chkSession () {
    //   if (Object.values(sessionStorage.getItem('dataRes')) !== 0) {
    //     return JSON.parse(sessionStorage.getItem('dataRes'))
    //   } else {
    //     return this.dataRes
    //   }
    // }
  },
  methods: {
    onResize () {
      if (window.innerWidth < 650) {
        this.isMobile = true
      } else {
        this.isMobile = false
      }
    },
    async init () {
      const data = {
        start_date: '',
        end_date: '',
        search_type: 'all'
      }
      await this.$store.dispatch('actionsDashboard2', data)
      const response = await this.$store.state.ModuleShop.stateDashboard2
      this.dataRes = await response.data
      this.dataMobile = await response.data
      // await window.sessionStorage.setItem('dataRes', JSON.stringify(this.dataRes))
      // console.log('dataRes', this.dataRes)
      await setTimeout(function () {
        $('#example').DataTable({
          dom: 'Bfrtip',
          destroy: true,
          responsive: true,
          language: {
            Search: '',
            searchPlaceholder: 'ค้นหา'
          },
          oLanguage: {
            sSearch: ''
          },
          columnDefs: [
            {
              targets: [4],
              visible: false,
              searchable: false
            }
          ],
          buttons: [
            {
              extend: 'csv',
              text: 'csv',
              charset: 'utf-8',
              extension: '.csv',
              filename: 'INET-Marketplace Platform | ตลาดออนไลน์สำหรับคุณ ง่าย รวดเร็ว ตอบโจทย์',
              bom: true
            },
            'copy', 'excel', 'print'
          ]
        }
        )
      },
      0
      )
      this.loading = await false
      // await setTimeout(() => { this.loading = false }, 2000)
      // console.log('SesstionRes', this.dataRes)
      // console.log('Sesstion', JSON.parse(sessionStorage.getItem('dataRes')))
    }
  }
}
</script>
<style scoped="css">
::v-deep .loading_manage[data-v-7e5bf2db]{
  min-height: 20vh;
}
@media only screen and (max-width: 650px) {
  .table-striped {
    display: none;
  }
  ::v-deep .dataTables_paginate.paging_simple_numbers {
    display: none;
  }
  ::v-deep #example_filter.dataTables_filter {
    display: none;
  }
}

@media only screen and (min-width: 750px) {
  ::v-deep .mobile {
    display: none;
  }
}
::v-deep #example_info {
  display: none;
}
::v-deep .dataTables_filter {
  margin-bottom: 1em;
}
::v-deep .dataTables_wrapper .dataTables_filter input {
    padding: 8px;
    width: 600px;
    background-color: transparent;
    margin-left: 8px;
    background-image: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABIAAAASCAYAAABWzo5XAAAABmJLR0QA/wD/AP+gvaeTAAABs0lEQVQ4ja3TTWsTURjF8f+ZTMUwLV10VSgVVBhDt4KrutB2aYVgBqSC4LQ7v4IbBf0KlmYErYJMJpZ+h4KLrgOm1IVvq+IqaqPNzOMiddFwk2L1LO9z+XEuPBf+UzRsEG4lE6XDfDYv9KPN5EeiKP8rKMySy5g9BBYB/+h436CuM3rSvhl3XJB3DGkky5htA188z7vy81up7BtTSPdl3LBf9rbSXJse2eioyTYobtfiV4MXZ9K0HNDZBBtv1+J5JHM3MnsEbLgQgM9RdCDfX0bMhVl9yfm0cCuZABZk9tSF/Mm76t2vgoag6oRKh/ks4He/+61RUL84LUPnnVBP/gHA2UkvOAmSFMis64R28/EPwL4VvesnNsKuFdKOEyKKckkJhT2YSdPyMKSSri8CV8eMZ24IsDEeS3gBnc1Lb55PuZBCeg2WtqKVvcH5sc2uNNemi7zUQMyZlIqiJVNgaAFsHiwF1ZDF7VurL4dC/WqmMKsvCapmuoDoStopFZa0opW9MFu/gykZxIZ+2lEJsyTCbMPQ6m4tfnFqqI/1m/V63sX3t+99Oq0DQKVZP/dPgCu/AanyqVoXnFHTAAAAAElFTkSuQmCC) !important;
    background-position: 570px 10px !important;
    outline: transparent;
    border: 1px solid #cacaca;
    border-radius: 6px;
  }
  ::v-deep .input {
    display: inline-block;
    white-space: nowrap;
  }
  ::v-deep .dt-button.buttons-print{
    align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;
  }
  ::v-deep .dt-button.buttons-copy.buttons-html5{
    align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;
  }
::v-deep .dt-button.buttons-csv.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;

}
::v-deep .dt-button.buttons-excel.buttons-html5 {
  align-items: center;
    border-radius: 4px;
    display: inline-flex;
    flex: 0 0 auto;
    font-weight: 500;
    letter-spacing: 0.0892857143em;
    justify-content: center;
    outline: 0;
    position: relative;
    text-decoration: none;
    text-indent: 0.0892857143em;
    text-transform: uppercase;
    transition-duration: 0.28s;
    transition-property: box-shadow, transform, opacity;
    transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    vertical-align: middle;
    white-space: nowrap;
    background-color: rgb(39, 171, 156);
    color: #ffffff;
}
::v-deep #example {
  padding-top: 12px;
}
::v-deep .sorting {
  color: #e6f5f3;
  background-color: #e6f5f3;
}
::v-deep table.dataTable tbody td.sorting_1 {
  text-align: center;
}
::v-deep #example > thead > tr > th {
  background-color: #e6f5f3;
  color: #27AB9C;
  font-size: 12px;
}
::v-deep table.dataTable th, table.dataTable td  {
  border-bottom: 1px solid #e7e7e7;
}
::v-deep div.dt-buttons {
  float: right;
 }
 ::v-deep .dataTables_wrapper .dataTables_filter  {
  float: left;
 }
  .fontData {
    font-size: 14px;
  }
  .tableLong {
    width: 200px;
  }
  .container {
    margin-right: 25px;
  }
  .sec {
    width: 30vw !important;
  }
  .dot {
    display: inline-block;
    width: 80px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
/* . hiDe {
  display: none;
} */
.dot2 {
    display: inline-block;
    width: 120px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}

</style>
