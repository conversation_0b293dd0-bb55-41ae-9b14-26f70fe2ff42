<template>
  <v-container :class="MobileSize ? 'background_color_Mobile' : 'background_color'" grid-list-xs rounded>
    <v-row dense class="pt-3 pb-3 pl-3">
      <v-col cols="12" align="end">
        <v-btn @click="ExportInternalShipping()" :block="MobileSize ? true : false" width="220" rounded color="#27AB9C" class="white--text" height="40" :class="MobileSize ? 'mb-2' : 'mr-2'">Export รายการขนส่งในระบบ</v-btn>
        <v-btn @click="ExportOutsourceShipping()" :block="MobileSize ? true : false" width="222" rounded outlined color="#27AB9C" height="40" :class="MobileSize ? 'mb-2' : ''">Export รายการขนส่งนอกระบบ</v-btn>
      </v-col>
      <v-col cols="12" md="6" sm="6">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; line-height: 32px; font-weight: bold;'">
          <v-icon v-if="MobileSize" @click="backtoSellerMenu()" color="#27AB9C" size="30">mdi-chevron-left</v-icon>
          จัดการรายการขนส่งระบบ
        </span>
      </v-col>
      <v-col cols="12" md="6" sm="6">
        <v-autocomplete v-model="selectShop" :items="itemsShop" item-text="name_th" item-value="id" outlined dense placeholder="เลือกร้านค้าภายในระบบ" :menu-props="{ offsetY: true, offsetOverflowAuto: true }" @change="handleSelectChange"></v-autocomplete>
      </v-col>
    </v-row>
    <!-- Button Action -->
    <v-row dense class="px-3" v-if="!MobileSize && !IpadSize">
      <v-col cols="12" md="9" sm="6" align="start">
        <v-btn rounded outlined height="40" :disabled="selected.length === 0 || statusShipping ? true : false" class="mr-2" color="#27AB9C" @click="OpenDialog('requestCourier')">เรียกขนส่งเข้ารับ(คูเรียร์)</v-btn>
        <v-btn @click="OpenDialog('print')" :disabled="selected.length === 0 ? true : false" rounded outlined width="160" height="40" class="mr-2" color="#27AB9C"><v-icon color="#2A70C3" class="pr-1">mdi-printer</v-icon> ปริ้นใบแปะหน้า</v-btn>
        <v-btn @click="GoToUploadReceiveOrderIMG()" rounded outlined width="160" height="40" class="mr-2" color="#27AB9C" style="font-size: 12px;"> แนบรูปยืนยันเข้ารับพัสดุ <br/>{{proofImgCount}} รายการ</v-btn>
        <!-- <v-btn rounded outlined width="160" height="40" color="#2A70C3">แจ้งเลขแทร็ก</v-btn> -->
      </v-col>
      <v-col cols="12" md="3" sm="6" align="end">
        <!-- <v-btn @click="getListIship(statusCode, dateStartToSent, dateEndToSent, courierCode, seleteFilterDate, search, 'export')" rounded width="160" height="40" style="font-weight: 700;" class="mr-2 white--text" color="#27AB9C">Export Excel</v-btn> -->
        <v-btn @click="openDialogDateLengthExport" rounded width="160" height="40" style="font-weight: 700;" class="mr-2 white--text" color="#27AB9C">Export Excel</v-btn>
        <!-- <v-btn @click="OpenDialog('create')" rounded width="160" height="40" style="font-weight: 700;" class="white--text" color="#27AB9C"><v-icon color="white" class="pr-1">mdi-plus-circle-outline</v-icon> สร้างรายการ</v-btn> -->
      </v-col>
    </v-row>
    <!-- Button Action -->
    <v-row dense class="px-3" v-if="!MobileSize && IpadSize">
      <v-col cols="12" md="7" sm="12">
        <v-row dense>
          <v-col cols="6">
            <v-btn block @click="OpenDialog('print')" :disabled="selected.length === 0 ? true : false" rounded outlined width="160" height="48" class="mr-2" color="#27AB9C"><v-icon color="#27AB9C" class="pr-1">mdi-printer</v-icon> ปริ้นใบแปะหน้า</v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn block rounded outlined height="48" :disabled="selected.length === 0 || statusShipping ? true : false" class="mr-2" color="#27AB9C" @click="OpenDialog('requestCourier')">เรียกขนส่งเข้ารับ(คูเรียร์)</v-btn>
          </v-col>
          <v-col cols="12" md="12" sm="12">
            <v-btn block @click="GoToUploadReceiveOrderIMG()" rounded outlined width="160" height="40" class="mr-2" color="#27AB9C" style="font-size: 12px;"> แนบรูปยืนยันเข้ารับพัสดุ <br/>{{proofImgCount}} รายการ</v-btn>
          </v-col>
          <!-- <v-btn rounded outlined width="160" height="40" color="#27AB9C">แจ้งเลขแทร็ก</v-btn> -->
        </v-row>
      </v-col>
      <v-col cols="12" md="3" sm="6">
        <!-- <v-btn block @click="getListIship(statusCode, dateStartToSent, dateEndToSent, courierCode, seleteFilterDate, search, 'export')" rounded width="160" height="40" style="font-weight: 700;" class="mr-2 white--text" color="#27AB9C">Export Excel</v-btn> -->
        <v-btn @click="openDialogDateLengthExport" rounded block width="160" height="48" style="font-weight: 700;" class="mr-2 white--text" color="#27AB9C">Export Excel</v-btn>
        <!-- <v-btn @click="OpenDialog('create')" rounded width="160" height="40" style="font-weight: 700;" class="white--text" color="#27AB9C"><v-icon color="white" class="pr-1">mdi-plus-circle-outline</v-icon> สร้างรายการ</v-btn> -->
      </v-col>
      <!-- <v-col cols="12" md="5" sm="12">
        <v-row dense class="mt-2">
          <v-col cols="6">
            <v-btn block @click="OpenDialog('create')" style="font-weight: 700;" rounded width="160" height="48" class="white--text" color="#27AB9C"><v-icon color="white" class="pr-1">mdi-plus-circle-outline</v-icon> สร้างรายการ</v-btn>
          </v-col>
          <v-col cols="12">
            <v-btn block @click="OpenDialog('setting')" style="font-weight: 700;" rounded width="160" height="48" class="mr-2 white--text" color="#27AB9C">ตั้งค่าขนส่ง</v-btn>
          </v-col>
        </v-row>
      </v-col> -->
    </v-row>
    <!-- Button Action -->
    <v-row dense v-if="MobileSize && !IpadSize">
      <v-col cols="12" md="7" sm="12">
        <v-row dense>
          <v-col cols="6">
            <v-btn block rounded style="font-size: 13px;" :disabled="selected.length === 0 || statusShipping ? true : false" outlined height="48" color="#27AB9C" @click="OpenDialog('requestCourier')">เรียกขนส่งเข้ารับ<br/>(คูเรียร์)</v-btn>
          </v-col>
          <v-col cols="6">
            <v-btn block @click="OpenDialog('print')" :disabled="selected.length === 0 ? true : false" rounded outlined  height="48" color="#27AB9C" class="px-1 mr-2"><v-icon color="#27AB9C" class="pr-1">mdi-printer</v-icon> ปริ้นใบแปะหน้า</v-btn>
          </v-col>
          <!-- <v-btn rounded outlined width="160" height="40" color="#27AB9C">แจ้งเลขแทร็ก</v-btn> -->
        </v-row>
      </v-col>
      <v-col cols="12" md="12" sm="12">
        <v-btn block @click="GoToUploadReceiveOrderIMG()" rounded outlined width="160" height="40" class="mr-2" color="#27AB9C"> แนบรูปยืนยันเข้ารับพัสดุ {{proofImgCount}} รายการ</v-btn>
      </v-col>
      <v-col cols="12" md="5" sm="12">
        <v-row dense class="mt-2 ">
          <v-col cols="12">
            <!-- <v-btn block @click="getListIship(statusCode, dateStartToSent, dateEndToSent, courierCode, seleteFilterDate, search, 'export')" rounded width="160" height="48" style="font-weight: 700;" class="mr-2 white--text" color="#27AB9C">Export Excel</v-btn> -->
            <v-btn @click="openDialogDateLengthExport" block rounded width="160" height="48" style="font-weight: 700;" class="mr-2 white--text" color="#27AB9C">Export Excel</v-btn>
          </v-col>
          <!-- <v-col cols="6" class="">
            <v-btn block @click="OpenDialog('create')" style="font-weight: 700;" rounded height="48" class="white--text" color="#27AB9C"><v-icon color="white" class="pr-1">mdi-plus-circle-outline</v-icon> สร้างรายการ</v-btn>
          </v-col> -->
        </v-row>
      </v-col>
    </v-row>
    <!-- Card Action -->
    <!--PC(1)-->
    <!-- <v-row dense justify="center" class="px-3 mb-2" v-if="!MobileSize && !IpadSize && !IpadProSize">
      <div v-for="(item, index) in dataToSelectPage" :key="index">
        <v-card width="185" height="110" class="my-4" :class="index !== 4 ? 'mr-2' : ''" style="box-shadow: 0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16); border-radius: 10px; cursor: pointer;" :style="selectItem === index ? 'background-color: #DAF1E9' : ''">
          <v-card-text @click="changeTab(index, item.value)">
            <v-row justify="center">
              <v-col cols="12" align="center">
                <v-img :src="item.icon" max-height="33" max-width="33" contain></v-img>
              </v-col>
            </v-row>
            <v-row justify="center" class="pb-2">
              <v-col cols="12" align="center" class="px-0">
                <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;" class="pt-1">{{ item.text }}</span><v-chip small class="ml-1" :color="item.color" style="color: white;"><b>{{ item.count }}</b></v-chip>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </div>
    </v-row> -->
    <!-- IPAD(1) -->
    <!-- <div class="px-3 mb-2 scrolling-wrapper" v-if="!MobileSize && IpadSize || IpadProSize">
      <div v-for="(item, index) in dataToSelectPage" :key="index" class="card">
        <v-card width="200" height="110" class="my-4" :class="index !== 4 ? 'mr-2' : ''" style="box-shadow: 0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16); border-radius: 10px; cursor: pointer;" :style="selectItem === index ? 'background-color: #DAF1E9' : ''">
          <v-card-text @click="changeTab(index, item.value)">
            <v-row justify="center">
              <v-col cols="12" align="center">
                <v-img :src="item.icon" max-height="33" max-width="33" contain></v-img>
              </v-col>
            </v-row>
            <v-row justify="center" class="pb-2">
              <v-col cols="12" align="center">
                <span style="font-weight: 600; font-size: 14px; line-height: 22px; color: #333333;" class="pt-1">{{ item.text }}</span><v-chip small class="ml-2" :color="item.color" style="color: white;"><b>{{ item.count }}</b></v-chip>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </div>
    </div> -->
    <!-- MOBILE(1) -->
    <!-- <div class="px-3 scrolling-wrapper" v-if="MobileSize && !IpadSize">
      <div v-for="(item, index) in dataToSelectPage" :key="index" class="card">
        <v-card width="167" height="79" class="my-4" :class="index !== 4 ? 'mr-2' : ''" style="box-shadow: 0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16); border-radius: 10px; cursor: pointer;" :style="selectItem === index ? 'background-color: #DAF1E9' : ''">
          <v-card-text @click="changeTab(index, item.value)">
            <v-row justify="center">
              <v-col cols="12" align="center" class="py-0">
                <v-img :src="item.icon" max-height="33" max-width="33" contain></v-img>
              </v-col>
            </v-row>
            <v-row justify="center" class="pb-2 px-0">
              <v-col cols="12" align="center" class="px-0">
                <span style="font-weight: 600; font-size: 12px; line-height: 16px; color: #333333;" class="pt-1">{{ item.text }}</span><v-chip small class="ml-2" :color="item.color" style="color: white;"><b>{{ item.count }}</b></v-chip>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </div>
    </div> -->
    <!-- PC(2) -->
    <v-row dense class="pt-3 pb-6">
      <v-col>
        <v-sheet elevation="0">
        <v-tabs
          bg-color="indigo"
          next-icon="mdi-arrow-right-bold-box-outline"
          prev-icon="mdi-arrow-left-bold-box-outline"
          show-arrows
        >
          <v-tab
            v-for="(item, index) in dataToSelectPage"
            :key="index"
            @click="changeTab(index, item.value)"
          >
          <!-- {{item.icon}} -->
          <v-icon>{{item.icon}}</v-icon>
          <span style="font-weight: 600; font-size: 14px; line-height: 16px;" class="pt-1 pl-2">
            {{ item.text }}
            <v-chip
              small
              label
              class="px-2 ml-1"
              :color="item.color"
              style="color: white;"
            >
              <b>{{ item.count }}</b>
            </v-chip>
          </span>
          </v-tab>
        </v-tabs>
      </v-sheet>
      </v-col>
    </v-row>
    <!-- Select Action -->
    <v-row dense :justify="IpadSize ? 'center' : 'start'" class="px-3" v-if="!MobileSize">
      <!-- วันนี้ -->
       <v-col cols="3">
        <div @click="changeFilterDate('day')">
          <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px;" :style="seleteFilterDate === 'day' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'day' ? 'color: #333333;' : 'color: #27AB9C;'">วันนี้</span>
          </div>
        </div>
       </v-col>
      <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" class="mr-2 ml-2" @click="changeFilterDate('day')">
        <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px;" :style="seleteFilterDate === 'day' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
          <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'day' ? 'color: #333333;' : 'color: #27AB9C;'">วันนี้</span>
        </div>
      </div> -->
      <!-- เมื่อวาน -->
      <v-col cols="3">
        <div @click="changeFilterDate('yesterday')">
          <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #ffffff00;" :style="seleteFilterDate === 'yesterday' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'yesterday' ? 'color: #333333;' : 'color: #27AB9C;'">เมื่อวาน</span>
          </div>
        </div>
      </v-col>
      <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" class="mr-2" @click="changeFilterDate('yesterday')">
        <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #ffffff00;" :style="seleteFilterDate === 'yesterday' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
          <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'yesterday' ? 'color: #333333;' : 'color: #27AB9C;'">เมื่อวาน</span>
        </div>
      </div> -->
      <!-- สัปดาห์นี้ -->
      <v-col cols="3">
        <div @click="changeFilterDate('week')">
          <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'week' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'week' ? 'color: #333333;' : 'color: #27AB9C;'">สัปดาห์นี้</span>
          </div>
        </div>
      </v-col>
      <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" class="mr-2" @click="changeFilterDate('week')">
        <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'week' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
          <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'week' ? 'color: #333333;' : 'color: #27AB9C;'">สัปดาห์นี้</span>
        </div>
      </div> -->
      <!-- เดือนนี้ -->
      <v-col cols="3">
        <div @click="changeFilterDate('month')">
          <div :class="IpadSize ? '' : ''" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'month' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'month' ? 'color: #333333;' : 'color: #27AB9C;'">เดือนนี้</span>
          </div>
        </div>
      </v-col>
      <!-- <div :style="IpadSize ? 'width: 154px; height: 38px;' : 'width: 200px; height: 40px;'" @click="changeFilterDate('month')">
        <div :class="IpadSize ? 'widthCardIpad' : 'widthCardDesktop'" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'month' ? 'background-color: #FFFFFF; border: 2px solid #27AB9C;' : 'background-color: #DAF1E9; border: 1px solid #ffffff00;'">
          <span style="font-weight: 600; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'month' ? 'color: #333333;' : 'color: #27AB9C;'">เดือนนี้</span>
        </div>
      </div> -->
    </v-row>
    <!-- Select filter date Action -->
    <v-row :justify="IpadSize ? 'center' : 'center'" class="mt-3" v-if="!MobileSize">
      <v-container class="mx-4">
        <v-row dense>
          <!-- start date -->
          <v-col cols="3" :style="IpadSize ? '' : ''" class="">
            <v-dialog
              ref="dialogStartDate"
              v-model="dialogStartDate"
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentStartDate"
                  v-bind="attrs"
                  placeholder="เริ่มต้น"
                  outlined
                  readonly
                  dense
                  v-on="on"
                >
                  <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-card>
                <v-card-title>
                  <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันเริ่มต้น</span>
                  <v-spacer></v-spacer>
                  <v-btn text @click="dialogStartDate = false" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                </v-card-title>
                <v-date-picker
                v-model="date"
                scrollable
                reactive
                locale="TH-th"
                @change="sentStartDate === '' ? sentStartDate = formatDate(date)  : '', $refs.dialogStartDate.save(date);"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
              >
                <!-- <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="dialogStartDate = false"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="sentStartDate === '' ? sentStartDate = formatDate(date)  : '', $refs.dialogStartDate.save(date)"
                >
                  ตกลง
                </v-btn> -->
                <v-btn
                  text
                  color="primary"
                  @click="cancelChooseStartDate"
                >
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="clearChooseStartDate"
                >
                  ล้างค่า
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  :disabled="date === ''"
                  @click="setValueStartDate(date); dialogStartDate = false"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
              </v-card>
            </v-dialog>
          </v-col>
          <!-- end date -->
          <v-col cols="3" :style="IpadSize ? '' : ''" class="">
            <v-dialog
              ref="dialogEndDate"
              v-model="dialogEndDate"
              width="290px"
              persistent
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentEndDate"
                  v-bind="attrs"
                  placeholder="สิ้นสุด"
                  outlined
                  readonly
                  dense
                  v-on="on"
                >
                  <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-card>
                <v-card-title>
                <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันสิ้นสุด</span>
                <v-spacer></v-spacer>
                <v-btn text @click="dialogEndDate = false" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
              </v-card-title>
              <v-date-picker
                v-model="date1"
                scrollable
                reactive
                no-title
                locale="TH-th"
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                :min="date"
              >
                <!-- <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="dialogEndDate = false"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="$refs.dialogEndDate.save(date1), setValueEndDate(date1)"
                >
                  ตกลง
                </v-btn> -->
                <v-btn
                  text
                  color="primary"
                  @click="cancelChooseEndDate"
                >
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="clearChooseEndDate"
                >
                  ล้างค่า
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  :disabled="date1 === ''"
                  @click="setValueEndDate(date1); dialogEndDate = false"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
              </v-card>
            </v-dialog>
          </v-col>
          <!-- select type shipping -->
          <v-col cols="3" :style="IpadSize ? '' : ''" class="">
            <v-select
              v-model="courierCode"
              :items="listCourier"
              item-text="courier_name"
              item-value="courier_code"
              placeholder="ขนส่งทั้งหมด"
              dense
              :menu-props="{ overflowY: true, offsetY: true }"
              outlined
              class="vSelectLineHeight"
            ></v-select>
          </v-col>
          <v-col cols="3" :style="IpadSize ? '' : ''" class="">
            <v-select
              v-model="selectedProvider"
              :items="serviceProviderItem"
              placeholder="ประเภทขนส่ง"
              dense
              :menu-props="{ overflowY: true, offsetY: true }"
              outlined
              class="vSelectLineHeight"
            ></v-select>
          </v-col>
          <!-- clear -->
          <!-- <v-col cols="3" :style="IpadSize ? '' : ''" class="">
            <v-btn outlined rounded color="primary"  @click="reset()" style="font-Weight: 700">ล้างการค้นหา</v-btn>
          </v-col> -->
        </v-row>
      </v-container>
      <!-- select status -->
      <!-- <div style="width: 200px; height: 40px;">
        <v-select
          :items="itemsStatus"
          label="สถานะทั้งหมด"
          dense
          :menu-props="{overflowY: true, offsetY: true }"
          outlined
        ></v-select>
      </div> -->
    </v-row>
    <v-row dense :class="MobileSize ? 'px-3' : 'px-3 pt-0'">
      <v-col cols="12" md="7" sm="7">
      <v-text-field
          v-model="searchh"
          dense
          style="border-radius: 4px;"
          hide-details
          outlined
          placeholder="ค้นหารหัสการสั่งซื้อ, เลขพัสดุ"
          @keyup="checkSearch"
        >
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="3" :style="IpadSize ? '' : ''" class="">
        <v-btn outlined rounded color="primary"  @click="reset()" style="font-Weight: 700">ล้างการค้นหา</v-btn>
      </v-col>
    </v-row>
    <!-- Mobile action -->
    <v-row dense v-if="MobileSize">
      <v-col cols="5" class="mt-6">
        <v-row dense>
          <!-- วันนี้ -->
          <div style="width: 124px; height: 34px;" class="ml-5 mb-4" @click="changeFilterDate('day')">
            <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'day' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
              <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'day' ? 'color: #27AB9C;' : 'color: #27AB9C;'">วันนี้</span>
            </div>
          </div>
          <!-- เมื่อวาน -->
          <div style="width: 124px; height: 34px;" class="ml-5 mb-4" @click="changeFilterDate('yesterday')">
            <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'yesterday' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
              <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'yesterday' ? 'color: #27AB9C;' : 'color: #27AB9C;'">เมื่อวาน</span>
            </div>
          </div>
          <!-- สัปดาห์นี้ -->
          <div style="width: 124px; height: 34px;" class="ml-5 mb-4" @click="changeFilterDate('week')">
            <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'week' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
              <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'week' ? 'color: #27AB9C;' : 'color: #27AB9C;'">สัปดาห์นี้</span>
            </div>
          </div>
          <!-- เดือนนี้ -->
          <div style="width: 124px; height: 34px;" class="ml-5" @click="changeFilterDate('month')">
            <div class="widthCardMobile" style="cursor: pointer; height: 38px; text-align: center; border-radius: 4px; border: 1px solid #E6E6E6;" :style="seleteFilterDate === 'month' || seleteFilterDate === '' ? 'background-color: #DAF1E9;' : 'background-color: #FFFFFF;'">
              <span style="font-weight: 500; font-size: 16px; line-height: 24px; display: block; padding-top: 8px;" :style="seleteFilterDate === 'month' ? 'color: #27AB9C;' : 'color: #27AB9C;'">เดือนนี้</span>
            </div>
          </div>
        </v-row>
      </v-col>
      <v-col cols="7" class="pt-0 mt-6">
        <!-- <v-row dense justify="start"> -->
          <!-- start date -->
          <div style="width: 175px; height: 40px;" class="ml-4 mb-3">
            <v-dialog
              ref="dialogStartDate"
              v-model="dialogStartDate"
              :return-value.sync="date"
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentStartDate"
                  v-bind="attrs"
                  readonly
                  placeholder="เริ่มต้น"
                  outlined
                  dense
                  v-on="on"
                >
                  <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                </v-text-field>
              </template>
                <v-date-picker
                  v-model="date"
                  scrollable
                  reactive
                  locale="TH-th"
                  @change="setValueStartDate(date)"
                  no-title
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="dialogStartDate = false"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="sentStartDate === '' ? sentStartDate = formatDate(date)  : '', $refs.dialogStartDate.save(date)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
            </v-dialog>
          </div>
          <!-- end date -->
          <div style="width: 175px; height: 40px;" class="ml-4 mb-2">
            <v-dialog
              ref="dialogEndDate"
              v-model="dialogEndDate"
              :return-value.sync="date1"
              width="290px"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-text-field
                  v-model="sentEndDate"
                  v-bind="attrs"
                  readonly
                  placeholder="สิ้นสุด"
                  outlined
                  dense
                  v-on="on"
                >
                  <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                </v-text-field>
              </template>
              <v-date-picker
                v-model="date1"
                scrollable
                reactive
                no-title
                locale="TH-th"
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                :min="date"
              >
                <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="dialogEndDate = false"
                >
                  ยกเลิก
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="$refs.dialogEndDate.save(date1), setValueEndDate(date1)"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-dialog>
          </div>
          <!-- select type shipping -->
          <div style="width: 175px; height: 40px;" class="ml-4">
            <v-select
              v-model="courierCode"
              :items="listCourier"
              item-text="courier_name"
              item-value="courier_code"
              placeholder="ขนส่งทั้งหมด"
              dense
              :menu-props="{ overflowY: true, offsetY: true }"
              outlined
              class="vSelectLineHeight"
            ></v-select>
          </div>
          <div :style="MobileSize ? 'width: 200px; height: 40px; margin: 10px 0px 0px 7px;' : 'width: 200px; height: 40px;'" class="ml-4 mr-2">
            <v-btn class="white--text" color="#27AB9C" :style="MobileSize ? 'min-width: 175px;' : 'min-width: 200px;'" @click="reset()"><v-icon small>mdi-delete-outline</v-icon>ล้างการค้นหา</v-btn>
          </div>
          <!-- select status -->
          <!-- <div style="width: 200px; height: 40px;">
            <v-select
              :items="itemsStatus"
              label="สถานะทั้งหมด"
              dense
              :menu-props="{overflowY: true, offsetY: true }"
              outlined
            ></v-select>
          </div> -->
        <!-- </v-row> -->
      </v-col>
    </v-row>
    <!-- table Shipping order -->
    <v-row dense justify="center" :class="MobileSize ? 'mt-6' : 'px-3 mt-6'">
      <v-col cols="12" :class="MobileSize ? 'px-0' : ''">
        <v-data-table
          v-model="selected"
          :headers="MobileSize ? headersMobile : headers"
          :items="itemTransportsOrder"
          :search="searchh"
          show-select
          :hide-default-header="MobileSize ? true : false"
          :style="MobileSize ? '' : 'filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16)); border-radius: 8px;'"
          no-data-text="ไม่มีข้อมูลขนส่งในตาราง"
          no-results-text="ไม่พบข้อมูลขนส่งในตาราง"
          :footer-props="{ itemsPerPageText: 'จำนวนแถว' , itemsPerPageOptions: [5, 10, 15, 25, 50] }"
          :server-items-length="totalItems"
          :items-per-page="options.itemsPerPage"
          @update:options="updateOptions"
          @toggle-select-all="selectAllToggle"
          :class="MobileSize ? 'px-0' : 'elevation-1'"
        >
          <template v-slot:[`header.data-table-select`]="{ on , props }">
              <v-simple-checkbox
                v-model="checkboxAll"
                :indeterminate="checkboxIndeterminate"
                :ripple="false"
                v-bind="props"
                v-on="on"
                disabled
              ></v-simple-checkbox>
          </template>
          <template v-slot:[`item.data-table-select`]="{ isSelected, select, item }">
            <v-simple-checkbox :ripple="false" :value="(item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน') || item.service_provider === null ? null : isSelected" @input="select($event)" :disabled="(item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน' || selectShop === -3) || item.service_provider === null ? true : false" v-if="!MobileSize"></v-simple-checkbox>
            <v-card elevation="0" class="d-flex pb-0" v-if="MobileSize" style="max-width: 100%; border-top: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 8px 8px 0px 0px;">
              <v-card-text class="d-flex pb-0">
                <v-simple-checkbox :ripple="false" :value="item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน' ? null : isSelected" @input="select($event)" :disabled="item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน' || selectShop === -3 ? true : false ? true : false" class="pt-1 mr-auto"></v-simple-checkbox>
                <v-menu offset left bottom nudge-bottom="20">
                  <template v-slot:activator="{ attrs, on }">
                    <v-btn
                      v-if="item" class="ml-3" v-bind="attrs" v-on="on"
                      small outlined icon tile
                      style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                    >
                      <v-icon color="primary">mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item>
                      <v-list-item-title style="cursor: pointer;"  @click="openDialogUpdateRemarkAdmin(item)">
                        <span style="font-size: small;">จัดการหมายเหตุแอดมิน</span>
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title style="cursor: pointer;" @click="item.service_provider === null || item.service_provider === 'OUT_SOURCE' ? openDialogUpdateDataStatus(item, 'out') : openDialogUpdateDataStatus(item, 'in')">
                        <span style="font-size: small;">จัดการสถานะขนส่ง</span>
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title style="cursor: pointer;" @click="item.business_type !== 'own_shipping' || selectShop === -3 ? ConfirmdeleteOrderCourier(item) : null">
                        <span style="font-size: small;" :style="item.business_type !== 'own_shipping' || selectShop === -3 ? 'color: inherit;' : 'color: #A1A1A1;'">ยกเลิกรายการขนส่ง</span>
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title style="cursor: pointer;"  @click="openDialogTimeline(item)">
                        <span style="font-size: small;">ติดตามสถานะขนส่ง</span>
                      </v-list-item-title>
                    </v-list-item>
                    <v-list-item>
                      <v-list-item-title style="cursor: pointer;"  @click="openModalB2BDeliveryList(item.order_number)">
                        <span style="font-size: small;">รายละเอียดรอบจัดส่ง</span>
                      </v-list-item-title>
                    </v-list-item>
                  </v-list>
                </v-menu>
                <!-- <v-btn class="ml-1" small @click="openDialogUpdateDataStatus(item)" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                  <v-icon  color="primary">mdi-pencil</v-icon>
                </v-btn>
                <v-btn class="ml-1" small @click="ConfirmdeleteOrderCourier(item)" :disabled="item.delivery_status !== 'รอเข้ารับพัสดุ' && item.delivery_status !== 'รอเรียกพนักงาน'  || selectShop === -3 ? true : false ? true : false" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                  <v-icon color="#A1A1A1">mdi-delete</v-icon>
                </v-btn> -->
              </v-card-text>
            </v-card>
          </template>
        <!-- </template> -->
          <!-- Mobile -->
          <template v-slot:[`item.Detail`]="{ item }" v-if="MobileSize">
            <v-card class="pa-3 mb-4" elevation="0" style="border-bottom: 2px solid #E6E6E6; border-right: 2px solid #E6E6E6; border-left: 2px solid #E6E6E6; border-radius: 0px 0px 8px 8px;">
              <v-row dense>
                <!-- icon -->
                <!-- <v-col cols="6" align="start">
                  <v-simple-checkbox :value="isSelected" @input="select($event)"></v-simple-checkbox>
                </v-col> -->
                <!-- <v-col cols="6" align="end">
                  <v-btn @click="ConfirmdeleteOrderCourier(item)" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
                    <v-icon color="#A1A1A1">mdi-delete</v-icon>
                  </v-btn>
                </v-col> -->
                <!-- รูป -->
                <v-col cols="6" align="start">
                  <v-avatar rounded>
                    <!-- <v-img :src="item.courier_image_path" max-width="60" max-height="60" width="100%" height="100%" contain></v-img> -->
                    <v-img :src="item.courier_image_path !== '' && item.courier_image_path !== null ? item.courier_image_path : noIMG " max-width="60" max-height="60" width="100%" height="100%" contain></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="6" align="end" class="pt-4">
                  <span style="font-weight: 700; font-size: 14px; line-height: 24px; color: #000000;">{{ item.order_no }}</span>
                </v-col>
                <!-- วันที่ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">วันที่</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ new Date(item.paid_datetime).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span>
                </v-col>
                <!-- รหัสสั่งซื้อสินค้า -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">รหัสสั่งซื้อสินค้า</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.order_number }}</span>
                </v-col>
                <!-- พัสดุ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">พัสดุ</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.tracking_number }}</span>
                </v-col>
                <!-- ประเภทขนส่ง -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">ประเภทขนส่ง</span>
                </v-col>
                <v-col cols="6" align="end">
                  <v-chip v-if="item.transport_ngc === 'N'" color="#fcf5bd" style="color: #bcae47;" small>ขนส่งนอก</v-chip>
                  <v-chip v-else-if="item.transport_ngc === 'Y'" color="#cdf3dc" style="color: #4ab273;" small>ขนส่งใน</v-chip>
                </v-col>
                <!-- สถานะ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">สถานะ</span>
                </v-col>
                <v-col cols="6" align="end">
                  <v-chip @click="getStatusIship(item.order_no, item.service_provider)" :color="getColorAlter(item.delivery_status)" :text-color="getTextColorAlter(item.delivery_status)" small>{{ item.delivery_status === 'ขนส่งนอก' ? 'ขนส่งนอก' : item.delivery_status }}</v-chip>
                </v-col>
                <!-- ผู้รับ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">ผู้รับ</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span v-if="item.dst_address !== null" style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.dst_address.dst_name }}</span>
                </v-col>
                <!-- เบอร์โทร -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">เบอร์โทร</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.dst_address !== null && item.dst_address !== undefined ? addDashes(item.dst_address.dst_phone) : '-' }}</span>
                </v-col>
                <!-- ที่อยู่ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">ที่อยู่</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.dst_address !== null ? item.dst_address.dst_address + ' ' + item.dst_address.dst_city_name + ' ' + item.dst_address.dst_district_name + ' '+ item.dst_address.dst_province_name + ' ' + item.dst_address.dst_postal_code : '-' }}</span>
                </v-col>
                <!-- พิมพ์ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">พิมพ์</span>
                </v-col>
                <v-col cols="6" align="end">
                  <v-chip :color="getPrintColor(item.print_status)" :text-color="getTextPrintColor(item.print_status)" small>{{ getTextPrint(item.print_status) }}</v-chip>
                </v-col>
                <!-- COD -->
                <!-- <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">COD</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.cod_amount }}</span>
                </v-col> -->
                <!-- หมายเหตุ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">หมายเหตุร้านค้า</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.remark_shop ? item.remark_shop : '-' }}</span>
                </v-col>
                <!-- หมายเหตุ -->
                <v-col cols="6" align="start">
                  <span style="font-weight: 700; font-size: 14px; line-height: 22px; color: #636363;">หมายเหตุแอดมิน</span>
                </v-col>
                <v-col cols="6" align="end">
                  <span style="font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;">{{ item.remark_admin ? item.remark_admin : '-' }}</span>
                </v-col>
              </v-row>
            </v-card>
          </template>
          <template v-slot:[`item.paid_datetime`]="{ item }">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ new Date(item.paid_datetime).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric" }) }}</span>
          </template>
          <template v-slot:[`item.order_number`]="{ item }">
            <span v-if="item.delivery_status !== 'ยกเลิก'" style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><a @click="dialogShipping(item)">{{ item.order_number }}</a></span>
            <span v-else style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.order_number }}</span>
          </template>
          <template v-slot:[`item.order_no`]="{ item }">
            <v-row no-gutters>
              <v-col cols="12" align="center">
                <v-avatar>
                  <v-img :src="item.courier_image_path !== '' && item.courier_image_path !== null ? item.courier_image_path : noIMG " max-width="100%" max-height="100%" width="58" height="57" contain></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12" align="center">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">{{ item.order_no === item.order_number || item.order_no === null ? '-' : item.order_no }}</span>
              </v-col>
            </v-row>
            <!-- <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.order_no }}</span> -->
          </template>
          <template v-slot:[`item.dst_name`]="{ item }">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.dst_address !== null ? item.dst_address.dst_name : '-' }}</span>
          </template>
          <template v-slot:[`item.dst_phone`]="{ item }">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.dst_address !== null && item.dst_address !== undefined ? addDashes(item.dst_address.dst_phone) : '-' }}</span>
          </template>
          <template v-slot:[`item.dst_address`]="{ item }">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.dst_address !== null ? item.dst_address.dst_address + ' ' + item.dst_address.dst_city_name + ' ' + item.dst_address.dst_district_name + ' '+ item.dst_address.dst_province_name + ' ' + item.dst_address.dst_postal_code : '-' }}</span>
          </template>
          <template v-slot:[`item.delivery_status`]="{ item }">
            <v-chip :color="getColorAlter(item.delivery_status, item.transport_ngc)" :text-color="getTextColorAlter(item.delivery_status)" small>{{ item.delivery_status === 'ขนส่งนอก' ? 'ขนส่งนอก' : item.delivery_status }}</v-chip>
            <!-- <v-chip @click="getStatusIship(item.order_no, item.service_provider)" :color="getColorAlter(item.delivery_status)" :text-color="getTextColorAlter(item.delivery_status)" small>{{ item.delivery_status === 'ขนส่งนอก' ? 'ขนส่งนอก' : item.delivery_status }}</v-chip> -->
          </template>
          <template v-slot:[`item.transport_type`]="{ item }">
            <v-chip v-if="item.transport_ngc === 'N'" color="#fcf5bd" style="color: #bcae47;" small>ขนส่งนอก</v-chip>
            <v-chip v-else-if="item.transport_ngc === 'Y'" color="#cdf3dc" style="color: #4ab273;" small>ขนส่งใน</v-chip>
          </template>
          <template v-slot:[`item.remark_shop`]="{ item }">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.remark_shop === '' ? '-' : item.remark_shop }}</span>
          </template>
          <template v-slot:[`item.remark_admin`]="{ item }">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">{{ item.remark_admin === '' ? '-' : item.remark_admin }}</span>
          </template>
          <template v-slot:[`item.print_label_status`]="{ item }">
            <v-chip :color="getPrintColor(item.print_status)" :text-color="getTextPrintColor(item.print_status)" small>{{ getTextPrint(item.print_status) }}</v-chip>
          </template>
          <!-- <template v-slot:[`item.tracking_number`]="{ item }">
            <v-row dense justify="center">
              <v-col cols="12" align="center">
                <v-avatar>
                  <v-img :src="item.courier_image_path" max-width="100%" max-height="100%" width="58" height="57" contain></v-img>
                </v-avatar>
              </v-col>
              <v-col cols="12" align="center">
                <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">{{ item.tracking_number }}</span>
              </v-col>
            </v-row>
          </template> -->
          <template v-slot:[`item.action`]="{ item }">
            <!-- <v-btn @click="EditOrder(item)" :disabled="item.delivery_status !== 'รอเรียกพนักงาน' ? true : false ? true : false" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
              <v-icon color="#A1A1A1">mdi-pencil</v-icon>
            </v-btn> -->
            <v-menu offset left bottom nudge-bottom="20">
              <template v-slot:activator="{ attrs, on }">
                <v-btn
                  v-if="item" class="ml-3" v-bind="attrs" v-on="on"
                  small outlined icon tile
                  style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;"
                >
                  <v-icon color="primary">mdi-dots-vertical</v-icon>
                </v-btn>
              </template>
              <v-list>
                <v-list-item>
                  <v-list-item-title style="cursor: pointer;"  @click="openDialogUpdateRemarkAdmin(item)">
                    <span style="font-size: small;">จัดการหมายเหตุแอดมิน</span>
                  </v-list-item-title>
                </v-list-item>
                <v-list-item  :disabled="item.is_b2b === 'Y'">
                  <v-list-item-title style="cursor: pointer;" @click="item.service_provider === null || item.service_provider === 'OUT_SOURCE' ? openDialogUpdateDataStatus(item, 'out') : openDialogUpdateDataStatus(item, 'in')">
                    <span style="font-size: small;">จัดการสถานะขนส่ง</span>
                  </v-list-item-title>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title style="cursor: pointer;" @click="item.business_type !== 'own_shipping' || selectShop === -3 ? ConfirmdeleteOrderCourier(item) : null">
                    <span style="font-size: small;" :style="item.business_type !== 'own_shipping' || selectShop === -3 ? 'color: inherit;' : 'color: #A1A1A1;'">ยกเลิกรายการขนส่ง</span>
                  </v-list-item-title>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title style="cursor: pointer;"  @click="openDialogTimeline(item)">
                    <span style="font-size: small;">ติดตามสถานะขนส่ง</span>
                  </v-list-item-title>
                </v-list-item>
                <v-list-item>
                  <v-list-item-title style="cursor: pointer;"  @click="openModalB2BDeliveryList(item.order_number)">
                    <span style="font-size: small;">รายละเอียดรอบจัดส่ง</span>
                  </v-list-item-title>
                </v-list-item>
              </v-list>
            </v-menu>
            <!-- <v-btn class="ml-3" small @click="openDialogUpdateDataStatus(item)" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
              <v-icon  color="primary">mdi-pencil</v-icon>
            </v-btn>
            <v-btn class="ml-3" small @click="ConfirmdeleteOrderCourier(item)" :disabled="item.business_type !== 'own_shipping' || selectShop === -3 ? true : false ? true : false" outlined icon tile style="border: 1px solid #F2F2F2; box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04); border-radius: 4px;">
              <v-icon color="#A1A1A1">mdi-delete</v-icon>
            </v-btn> -->
          </template>
          <template v-slot:[`item.datetime_status`]="{ item }">
            <span>{{item.datetime_status !== '-' ? new Date(item.datetime_status.substring(0, 10)).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric" }) : item.datetime_status}}</span>
          </template>
        </v-data-table>
      </v-col>
    </v-row>
    <!-- Dialog ลบ order ขนส่ง -->
    <v-dialog v-model="dialogDeleteOrder" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการลบรายการขนส่ง
          </span>
           <v-btn icon dark @click="dialogDeleteOrder = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span>ท่านต้องการลบรายการขนส่ง รหัส <b style="color: #333333;">{{ dataDeleteOrder.order_number }} </b></span><br>
              <span>ใช่หรือไม่</span>
            </v-col>

          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="dialogDeleteOrder = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmOrderDelete(dataDeleteOrder)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- Dialog ยืนยันลบ order ขนส่ง  -->
    <v-dialog v-model="dialogConfirmDeleteOrder" persistent width="379">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ยืนยันการลบรายการขนส่ง
          </span>
           <v-btn icon dark @click="closeDialogConfirmDeleteOrder()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <!-- <v-col cols="12" align="start" class="pb-0">
              <v-card width="328" max-width="100%" max-height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text>
                  <v-avatar :size="80">
                    <v-img></v-img>
                  </v-avatar>
                </v-card-text>
              </v-card>
            </v-col> -->
            <v-col cols="12" align="start" class="mt-2">
              <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รหัสรายการขนส่ง : {{ confirmDataDelete.order_number }}</span>
            </v-col>
            <v-col cols="12" align="start">
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">หมายเหตุ</span>
              <v-textarea v-model="remarkCancel" outlined hide-details height="100"></v-textarea>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogConfirmDeleteOrder()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="comfirmDelete(confirmDataDelete, remarkCancel)">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <!-- ปริ้นใบปะหน้า -->
    <v-dialog v-model="dialogPrint" :width="MobileSize ? '100%' : '590px'" persistent>
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '590px'">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ปริ้นใบปะหน้า
          </span>
           <v-btn icon dark @click="dialogPrint = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" class="pt-4" dense>
            <v-col cols="6">
              <v-card :width="MobileSize ? '100%' : '100%'" :height="MobileSize ? '100%' : '169px'" class="card_pdf" @click="printPDF()">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center" class="pt-4">
                      <v-img :src="require('@/assets/iShip/pdf.png')" max-width="75" max-height="75" contain></v-img>
                    </v-col>
                    <v-col cols="12" align="center" class="pt-4">
                      <span class="card_text_pdf">ใบปะหน้า</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
            <v-col cols="6">
              <v-card :width="MobileSize ? '100%' : '100%'" :height="MobileSize ? '100%' : '169px'" class="card_pdf" @click="PrintShippingOrder()">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center" class="pt-4">
                      <v-img :src="require('@/assets/iShip/pdf.png')" max-width="75" max-height="75" contain></v-img>
                    </v-col>
                    <v-col cols="12" align="center" class="pt-4">
                      <span class="card_text_pdf">ใบรับพัสดุ</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- ตั้งค่าขนส่ง -->
    <v-dialog v-model="dialogSettingCourier" persistent :width="MobileSize ? '100%' : '554px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '554px'">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ตั้งค่าขนส่ง
          </span>
           <v-btn icon dark @click="CloseDialogSettingCourier()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense>
            <v-col cols="12">
              <v-checkbox v-model="checkAllCourier" dense label="เลือกทั้งหมด" hide-details @change="CheckAllCourier(dataCourier, checkAllCourier)"></v-checkbox>
            </v-col>
            <v-col cols="12" class="pl-4" v-for="(itemCourier, index) in dataCourier" :key="index" @click="Checkbutton(dataCourier)">
              <v-row dense>
                <v-col cols="1" class="pt-4">
                  <v-checkbox v-model="itemCourier.status" dense hide-details></v-checkbox>
                </v-col>
                <v-col cols="4" md="2" sm="2">
                  <v-avatar size="64">
                    <v-img :src="`${itemCourier.media_path}`" contain max-height="100%" max-width="100%" height="64" width="64"></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="7" md="9" sm="9" class="pt-6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">{{ itemCourier.name }}</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn outlined rounded color="#27AB9C" width="110" height="40" class="mr-2" @click="CloseDialogSettingCourier()">ย้อนกลับ</v-btn>
            <v-btn color="#27AB9C" rounded class="white--text" width="110" height="40" :disabled="disableButton" @click="updateCourierList(dataCourier)">บันทึก</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogShippingDetail" persistent :width="MobileSize ? '100%' : '822px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '822px'">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            รายละเอียดการสั่งซื้อ
          </span>
           <v-btn icon dark @click="ClosedialogShippingDetail()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <!-- <pre>
            {{ orderSelect }}
          </pre> -->
          <v-row class="pt-5" dense>
            <v-col cols="12" md="6" sm="6" class="px-0">
              <div> <span class="titles1 pr-1">รหัสคำสั่งซื้อ</span>:<span class="titles2 pl-1">{{ orderSelect.order_number }}</span></div>
            </v-col>
            <v-col cols="12" md="6" sm="6" class="px-0">
              <div class="d-flex justify-end"><span class="titles1 pr-1">วันที่สั่งซื้อ</span>:<span class="titles2 pl-1"> {{ new Date(orderSelect.paid_datetime).toLocaleDateString("th-TH", { timeZone: 'utc', year: "numeric", month: "long", day: "numeric", hour:"numeric", minute:"numeric"}) }} น.</span></div>
            </v-col>
          </v-row>
          <v-row class="pt-2" dense>
            <div class="titles1_5">ที่อยู่ในการจัดส่งสินค้า</div>
          </v-row>
          <v-row class="pt-3" dense>
            <div class="titles2" v-if="orderSelect.dst_address !== undefined">
              <!-- {{ orderSelect.dst_address }} -->
              {{ orderSelect.dst_address === null || orderSelect.dst_address === '' || orderSelect.dst_address === undefined ? '-' : orderSelect.dst_address.dst_address + ' ' + orderSelect.dst_address.dst_city_name + ' ' + orderSelect.dst_address.dst_province_name + ' ' + orderSelect.dst_address.dst_postal_code }}
            </div>
          </v-row>
          <!-- <v-row class="pt-3" dense>
            <v-card width="100%" class="elevation-0" color="#FAFAFA">
              <v-card-item >
                <v-card-title class="py-2 titles1_5">เอกสารที่ต้องเพิ่มลงกล่อง</v-card-title>
              </v-card-item>
              <v-card-text class="pb-2 pt-3 titles2">
                ใบเสร็จรับเงิน : <a @click="downloadPDF(dataprime.bill_pdf)">คลิกที่นี่</a>
                {{ this.dataprime.bill_pdf }}
                <span v-if="this.dataprime.bill_pdf != ''" style="color:#1B5DD6; cursor: pointer;" @click="downloadPDF(this.dataprime.bill_pdf)">คลิกที่นี่</span>
                <span v-else> - </span>
              </v-card-text>
              <div class="mb-3 pl-4 d-flex grow flex-wrap" style="column-gap: 3px;align-items: center;">
                  <span>เอกสารที่ผู้ซื้อร้องขอ :</span>
                  <span v-if="countPro == 0"> - </span>
                    <div v-else v-for="(item, index) in this.dataprime.document_buyer" :key="index" class="d-flex flex-wrap mr-2" style="align-items: center;border: solid 0.25px;border-color: #EFEFEE;border-radius: 8px;background-color: white;color: #333333;padding: 4px;">
                          <v-img
                          max-height="20"
                          max-width="20"
                          src="@/assets/iShip/pdf.svg"></v-img>
                          <span class="pl-2">{{item}}</span>
                    </div>
              </div>
              <v-card-text class="pb-2 pt-3 titles2">
                ความต้องการของผู้ซื้อ : <span v-if="!dataprime.remark"> - </span>
                <span v-else class="titles2Color">{{ dataprime.remark }}</span>
              </v-card-text>
            </v-card>
          </v-row> -->
          <v-row class="pt-3" dense>
            <v-col>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><b>Standard Delivery</b> : <br v-if="MobileSize"/> {{ orderSelect.business_type }}</span><br>
            </v-col>
          </v-row>
          <v-row dense>
            <v-col>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">Tracking Number : {{ orderSelect.order_number }}</span>
            </v-col>
          </v-row>
          <v-row dense v-if="orderSelect.length !== 0">
            <v-col>
              <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">สถานะเรียกรถ : {{ orderSelect.data_call_courier.msg_req_courier }}</span>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="12">
              <v-data-table
                 :headers="dataTableheader"
                 :hide-default-header="MobileSize ? true : false"
                 :items="toggleshow === true ? data1 : NotToggle"
                 :items-per-page="50"
                 hide-default-footer
                 style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));"
                >
                  <template v-slot:[`item.product_attribute_detail`]="{ item }">
                      <v-row dense>
                        <v-col cols="2" class="mt-3">
                          <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                        </v-col>
                        <!-- {{ item }} -->
                        <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                          <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                          <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                          <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.product_attribute_detail.key_1_value">{{ item.product_attribute_detail.key_1_value }} : {{ item.product_attribute_detail.attribute_priority_1 }}</span><span v-if="item.product_attribute_detail.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.product_attribute_detail.key_2_value }} : {{ item.product_attribute_detail.attribute_priority_2 }}</span></span>
                        </v-col>
                      </v-row>
                  </template>
                    <template v-slot:[`item.price`]="{ item }">
                      <!-- item.vat_default === 'yes' ? Number(parseFloat(item.revenue_default) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) :  Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) -->
                      <!-- {{ item.vat_default === 'yes' ? Number(parseFloat(item.revenue_default) + parseFloat(item.vat_include)).toLocaleString(undefined, {minimumFractionDigits: 2}) :  Number(item.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }} -->
                      {{ item.vat_default === 'yes' ? item.revenue_default_with_vat : item.revenue_default }}
                    </template>
                    <template v-slot:[`item.total_price`]="{ item }">
                      <!-- {{ Number(item.total_revenue_default).toLocaleString(undefined, {minimumFractionDigits: 2}) }} -->
                      {{ item.vat_default === 'yes' ? item.revenue_default_with_vat_total : item.total_revenue_default }}
                  </template>
              </v-data-table>
              <v-row dense justify="center" class="mt-0" v-if="data1.length > 1">
                  <v-col cols="12" align="center">
                    <v-btn text block @click="ShowExpand()">
                      <span :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'" style="font-weight: 400; line-height: 22px; text-decoration-line: underline; color: #27AB9C;" v-if="toggleshow === false">ดูเพิ่มเติม</span><v-icon color="#27AB9C" v-if="toggleshow === false">mdi-chevron-down</v-icon>
                      <span :style="MobileSize ? 'font-size: 10px;' : 'font-size: 14px;'" style="font-weight: 400; line-height: 22px; text-decoration-line: underline; color: #27AB9C;" v-if="toggleshow === true">ย่อขนาด</span><v-icon color="#27AB9C" v-if="toggleshow === true">mdi-chevron-up</v-icon>
                    </v-btn>
                  </v-col>
                </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- เรียกแก้ไข -->
    <v-dialog v-model="dialogSetting" persistent :width="MobileSize ? '100%' : '444px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0" height="40px">
          <span class="flex text-center pl-9" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 700; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แก้ไขรายการขนส่ง
          </span>
           <v-btn icon dark @click="closeDialogSetting()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
          <v-card-text class="pt-4">
            <v-row class="pa-7">
              <v-col cols="6" class="pa-0 pr-3">
                <v-btn block @click="ShowDialog2()" height="180px" width="116px" elevation="0" outlined color="#DAF1E9">
                  <v-col>
                    <v-img :src="require('@/assets/iShip/trucksan.png')" ></v-img>
                    <div class="pt-1" style="font-size: 18px; color: rgb(0, 0, 0)"><b>เปลี่ยนขนส่ง</b></div>
                  </v-col>
                </v-btn>
              </v-col>
              <v-col cols="6" class="pa-0 pl-3">
                <v-btn block @click="sendToSplit()" height="180px" width="116px" elevation="0" outlined color="#DAF1E9" :disabled="sumQuantity === 1 ? true : false">
                  <v-col>
                    <v-img :class="sumQuantity === 1 ? 'grayscale' : ''" :src="require('@/assets/iShip/datadeli.png')" ></v-img>
                    <div class="pt-1" :style="sumQuantity === 1 ? 'font-size: 18px; color: crgb(83, 81, 81)' : 'font-size: 18px; color: rgb(0, 0, 0)'"><b>แบ่งออเดอร์</b></div>
                  </v-col>
                </v-btn>
              </v-col>
            </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- เรียกขนส่งเข้ารับ (คูเรียร์) -->
    <v-dialog v-model="dialogListRequestCourier" persistent :width="MobileSize ? '100%' : '554px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '554px'">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            เรียกขนส่งเข้ารับ (คูเรียร์)
          </span>
           <v-btn icon dark @click="dialogListRequestCourier = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="12" class="pl-4" v-for="(itemListCourier, index) in ListRequestCourier" :key="index" @click="Checkbutton(dataCourier)">
              <v-row justify="center" v-if="itemListCourier.parcel !== 0" dense class="HoverToClickCourier" style="cursor: pointer;" @click="SelectRequestCourier(itemListCourier)">
                <v-col cols="8" md="6" sm="6" align="start">
                  <v-avatar size="64">
                    <v-img :src="`${itemListCourier.logo}`" contain max-height="100%" max-width="100%" height="64" width="64"></v-img>
                  </v-avatar>
                  <span class="pl-4" style="font-weight: 500; font-size: 16px; line-height: 24px;">{{ itemListCourier.name }}</span>
                </v-col>
                <v-col cols="4" md="6" sm="6" class="pt-6" align="end">
                  <span style="font-weight: 700; font-size: 16px; line-height: 24px;">{{ itemListCourier.count }} คำสั่งซื้อ</span>
                </v-col>
              </v-row>
              <v-row justify="center" v-else>
                <v-col cols="8" md="6" sm="6" align="start">
                  <v-avatar size="64">
                    <v-img :src="`${itemListCourier.logo}`" contain max-height="100%" max-width="100%" height="64" width="64"></v-img>
                  </v-avatar>
                  <span class="pl-4" style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">{{ itemListCourier.name }}</span>
                </v-col>
                <v-col cols="4" md="6" sm="6" class="pt-6" align="end">
                  <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">{{ itemListCourier.count }} คำสั่งซื้อ</span>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSelectCourier" width="580" persistent :scrollable="dataChangeCourier.length !== 0 ? true : false">
      <v-card style="border-radius: 12px;">
        <v-toolbar dense elevation="0" color="#DAF1E9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>เปลี่ยนขนส่ง</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialogSelect()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text class="pb-0" :style="dataChangeCourier.length === 0 ? 'overflow-y: hidden !important;' : ''">
          <v-row dense class="mt-4" v-if="dataChangeCourier.length !== 0">
            <v-col cols="12" v-for="(item, index) in dataChangeCourier" :key="index" class="mb-4">
              <v-card elevation="0" :width="MobileSize ? '100%' : '774'" :height="MobileSize ? '100%' : '100%'" outlined style="border: 1px solid #DAF1E9; border-radius: 8px;">
                <v-card-text style="display: block; margin: auto;">
                  <v-row>
                    <v-col cols="1" md="1" :align="MobileSize ? '' : 'center'" :class="MobileSize ? 'px-1' : ''" style="display: block; margin: auto;">
                      <v-radio-group v-model="selectCourier">
                        <v-radio :value="item.courier_code" @click="saveSelectCourier(item.courier_code)"></v-radio>
                      </v-radio-group>
                    </v-col>
                    <v-col cols="5" md="5" align="start" style="display: block; margin: auto;">
                      <v-img :src="item.img_path" max-width="90" max-height="90" style="filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16)); border-radius: 50%;"></v-img>
                    </v-col>
                    <v-col cols="6" md="6" align="end" :class="MobileSize ? 'px-1' : ''" style="display: block; margin: auto;">
                      <span :style="MobileSize ? 'font-size: 16px;' : 'font-size: 18px;'" style="font-weight: 400; line-height: 26px; color: #333333;">{{ item.courier_name }}</span><br/>
                      <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'" style="font-weight: 400; line-height: 24px; color: #1B5DD6;">ค่าส่งเริ่มต้น {{ item.total_price }} บาท</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
          <v-row justify="center" v-else>
            <v-col cols="12" align="center" class="my-4">
              <v-card elevation="0">
                <v-card-text>
                  <v-row dense justify="center">
                    <v-col cols="12" align="center">
                      <v-img class="mb-4" :src="require('@/assets/iShip/car.png')" :width="MobileSize ? '100%' : '460'" height='228' contain></v-img>
                      <span style="font-weight: 700; line-height: 32px; color: #333333;" :style="!MobileSize ? 'font-size: 24px;' : 'font-size: 16px;'">ไม่มีขนส่งรองรับ</span>
                    </v-col>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- เลือกเรียกขนส่งเข้ารับ (คูเรียร์) -->
    <v-dialog v-model="dialogSelectListRequestCourier" persistent :width="MobileSize ? '100%' : '822px'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" :width="MobileSize ? '100%' : '822px'">
        <v-form ref="formOne" :lazy-validation="lazyOne">
          <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            เรียกขนส่งเข้ารับ (คูเรียร์)
          </span>
           <v-btn icon dark @click="ClosedialogSelect()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="12">
              <v-card max-width="100%" max-height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text>
                  <v-avatar size="80">
                    <v-img :src="`${SelectRequestCourierItem.logo}`" contain max-height="100%" max-width="100%" height="80" width="78"></v-img>
                  </v-avatar>
                  <span class="pl-4" style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">{{ SelectRequestCourierItem.name }}</span>
                </v-card-text>
              </v-card>
            </v-col>
            <!-- ชื่อ-นามสกุลและเบอร์โทร -->
            <!-- <v-col cols="12">
              <v-row dense>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ชื่อ - นามสกุล</span><span style="color: red;"> *</span>
                  <v-text-field v-model="nameRequest" dense outlined></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">เบอร์โทรศัพท์</span><span style="color: red;"> *</span>
                  <v-text-field v-model="phoneRequest" dense outlined></v-text-field>
                </v-col>
              </v-row>
            </v-col> -->
            <!-- ที่อยู่เข้ารับและจำนวนพัสดุ -->
            <v-col cols="12">
              <v-row dense>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ที่อยู่เข้ารับ</span><span style="color: red;"> *</span>
                  <v-text-field v-model="addressRequest" dense outlined placeholder="กรอกที่อยู่เข้ารับ" @keypress="CheckSpacebar($event)"></v-text-field>
                </v-col>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">จำนวนพัสดุ</span>
                  <v-text-field v-model="SelectRequestCourierItem.count" disabled dense outlined></v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-2">
              <v-row dense>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ตำบล/แขวง</span><span style="color: red;"> *</span>
                  <!-- <addressinput-subdistrict label="" style="border-radius: 8px !important;" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div> -->
                  <addressinput-subdistrict label="" dense outlined :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">อำเภอ/เขต</span><span style="color: red;"> *</span>
                  <addressinput-district label="" style="border-radius: 8px !important;" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="districtText" placeholder="ระบุเขต/อำเภอ"/>
                  <div v-if="checkDistrictError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-2">
              <v-row dense>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">จังหวัด</span><span style="color: red;"> *</span>
                  <!-- <addressinput-subdistrict label="" style="border-radius: 8px !important;" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล"/>
                  <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div> -->
                  <addressinput-province label="" style="border-radius: 8px !important;" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="provinceText" placeholder="ระบุจังหวัด"/>
                  <div v-if="checkProvinceError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
                  <!-- <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div> -->
                </v-col>
                <v-col cols="12" md="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">รหัสไปรษณีย์</span><span style="color: red;"> *</span>
                  <addressinput-zipcode numbered style="border-radius: 8px !important;" label="" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์"/>
                  <div v-if="checkZipcodeError" class="text-error pb-3">ข้อมูลไม่ถูกต้อง</div>
                </v-col>
              </v-row>
            </v-col>
            <v-col cols="12" class="pt-2 pb-0 mb-0">
              <v-row dense>
                <v-col cols="12" md="6" class="pb-0 mb-0">
                  <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">หมายเลขโทรศัพท์</span><span style="color: red;"> *</span>
                  <v-text-field
                  v-model="phoneShop"
                  style="font-size: 14px !important;"
                  dense
                  outlined
                  placeholder="หมายเลขโทรศัพท์"
                  oninput="this.value = this.value.replace(/[^0-9]/g, '')"
                  :rules="Rules.tel"
                  @keypress="CheckSpacebar($event)">
                </v-text-field>
                </v-col>
              </v-row>
            </v-col>
            <!-- หมายเหตุ -->
            <v-col cols="12">
              <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">หมายเหตุ</span>
              <v-textarea v-model="remarkRequest" max-height="107" outlined dense auto-grow placeholder="หมายเหตุ ตัวอย่าง เช่น โปรดอย่าโยน"></v-textarea>
            </v-col>
            <v-col cols="12">
              <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><span style="color: red;">*</span>  หากไม่ได้รับการติดต่อกลับจากทางพนักงานขนส่ง หรือ ไม่มีข้อมูลการเข้ารับใน 2 ชั่วโมง, กรุณาติดต่อแอดมิน</p>
              <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;"><span style="color: red;">*</span>  หากเรียกรถหลัง 14.00 น. จะต้องทำการเรียกรถอีกครั้งในวันถัดไป, เนื่องจากการจองรถอาจถูกยกเลิกโดนบริษัทขนส่งได้</p>
            </v-col>
            <v-col cols="12" align="end">
              <!-- <v-btn @click="RequestCourier(SelectRequestCourierItem)" max-width="140px" max-height="40px" rounded color="#27AB9C" class="white--text" style="font-size: 14px; line-height: 22px; font-weight: 400;" :disabled="addressRequest !== '' ? false : true"><v-icon color="white">mdi-checkbox-marked-circle-outline</v-icon> ยืนยันการเข้ารับ</v-btn> -->
              <v-btn @click="confirmSentRequestShopData(SelectRequestCourierItem)" max-width="140px" max-height="40px" rounded color="#27AB9C" class="white--text" style="font-size: 14px; line-height: 22px; font-weight: 400;" :disabled="addressRequest !== '' ? false : true"><v-icon color="white">mdi-checkbox-marked-circle-outline</v-icon> ยืนยันการเข้ารับ</v-btn>
            </v-col>
          </v-row>
        </v-card-text>
        </v-form>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ShowDialog4" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แบ่งออร์เดอร์
          </span>
           <v-btn icon dark @click="ShowDialog4 = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center" class="pb-0">
              <span>คุณได้ทำการแบ่งออเดอร์สินค้า</span><br>
              <span><b>"รหัส {{ formDetail.orders_number}}"</b></span><br>
              <span>คุณต้องการที่จะแบ่งออเดอร์สินค้า ใช่ หรือ ไม่ ?</span>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="ShowDialog4 = false" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmData()">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSplitOrder" persistent width="822">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แบ่งออร์เดอร์
          </span>
           <v-btn icon dark @click="closeCutOrder()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="start" dense class="pt-6">
            <v-col cols="12" md="6" align="start" class="pb-0">
              <v-card style="width: 313px; height: 40px;" class="d-flex align-center rounded-pill pl-4">
                <v-row class="d-flex align-center">
                  <v-img :src="require('@/assets/iShip/bag.png')" max-height="32" max-width="32"></v-img> <span class="ml-2" style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333;">รหัสการสั่งซื้อ : {{ formDetail.orders_number}}</span>
                </v-row>
              </v-card>
            </v-col>
          </v-row>
          <v-row justify="start" dense class="pt-6">
            <v-col cols="12" md="12" align="start" class="pb-0">
              <span style="color: #333333;">กล่องที่ 1</span>
              <!-- <span class="pl-4">{{ showCountOrder }} / {{ lengthProduct }}</span> -->
            </v-col>
          </v-row>
          <v-row>
          <v-col cols="12" md="6" sm="6">
            <!-- <v-select :items="items" outlined dense label="กรุณาเลือกสินค้า">
              <template v-slot:selection="{ item }">
              <img dense :src="item.image">{{ item.name }}
             </template>
            <template v-slot:item="{ item }">
           <img dense :src="item.image" >{{ item.name }}
         </template>
        </v-select> -->
          <v-select
          v-model="selectOrder"
          :items="selectItemOrder"
          @change="selectedOrder(0)"
          item-text="product_name"
          item-value="index"
          placeholder="กรุณาเลือกสินค้า"
          outlined
          return-object
          hide-details
          dense
          no-data-text="ไม่พบข้อมูล"
          label="กรุณาเลือกสินค้า"
        >
      </v-select>
      </v-col>
      <v-col cols="12" md="6" sm="6" align="end">
        <div class="">
        <v-btn
      rounded
      color="primary"
      :block="MobileSize ? true : false"
      outlined
      :disabled="checkDisableBotton(selectItemOrder.length, indexAddBox)"
      @click="addBox()"
    >
        <!-- <v-btn
      rounded
      color="primary"
      :block="MobileSize ? true : false"
      outlined
      @click="addBox()"
    > -->
    <v-icon
      x-small
      color="primary"
    >
      mdi-plus-circle-outline
    </v-icon>
    เพิ่มกล่องสินค้า
    </v-btn>
  </div>
      </v-col>
          </v-row>
    <v-row>
      <v-col cols="12">
        <v-card width="100%" height="100%" outlined style="border-radius: 8px; border-color: #B9DAF6;">
          <div class="text-center " :class="MobileSize ? 'my-4 mx-4' : 'my-6 mx-6'">
            <div v-if="itemInboxAll.length !== 0">
              <v-data-table
                 :headers="MobileSize ? dataTableheader2Mobile : dataTableheader2"
                 :items="itemInboxAll[[0]]"
                 hide-default-footer
                 :hide-default-header="MobileSize ? true : false"
                 @pagination="countMember"
                 no-data-text="ยังไม่มีสินค้า"
                 :style="MobileSize ? '' : 'filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));'"
                >
                  <template v-slot:[`item.product_detailMobile`]="{ item }">
                    <v-row dense>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                        <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <!-- <v-row dense v-if="items.expand_table === false">
                      <v-col cols="12" v-if="(index === items.product_list_not_expand.length - 1)" class="pt-6 pb-4">
                        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="12" v-else class="pt-4">
                      </v-col>
                    </v-row>
                    <v-row dense v-else-if="items.expand_table === true">
                      <v-col cols="12" v-if="(index === items.productInbox.length - 1)" class="pt-6 pb-4">
                        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="12" v-else class="pt-4">
                      </v-col>
                    </v-row> -->
                    <v-row dense>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                  <template v-slot:[`item.total_price`]="{ item }">
                    {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                </v-data-table>
            </div>
            <div v-else >
            ยังไม่มีสินค้า
          </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  <div v-for="(item, index) in formCut.product_split" :key="index">
    <!---index 2-->
    <v-row justify="start" dense class="pt-6">
            <v-col cols="12" md="12" align="start" class="pb-0">
              <span style="color: #333333;">กล่องที่ {{ index + 2 }}</span>
              <!-- <span class="pl-4">{{ showCountOrderOther }} / {{ lengthProduct }}</span> -->
            </v-col>
          </v-row>
          <v-row>
          <v-col cols="12" md="6" sm="6">
            <!-- <v-select :items="items" outlined dense label="กรุณาเลือกสินค้า">
              <template v-slot:selection="{ item }">
              <img dense :src="item.image">{{ item.name }}
             </template>
            <template v-slot:item="{ item }">
           <img dense :src="item.image" >{{ item.name }}
         </template>
        </v-select> -->
          <v-select
          v-model="selectOrder"
          :items="selectItemOrder"
          @change="selectedOrder(item.indexBox)"
          item-text="product_name"
          item-value="index"
          placeholder="กรุณาเลือกสินค้า"
          outlined
          hide-details
          return-object
          dense
          no-data-text="ไม่พบข้อมูล"
          label="กรุณาเลือกสินค้า"
        >
      </v-select>
      </v-col>
      <v-col cols="12" md="6">
      </v-col>
          </v-row>
    <v-row>
      <v-col cols="12">
        <v-card width="100%" height="100%" outlined style="border-radius: 8px; border: 1px solid var(--primary-b-9-daf-6, #B9DAF6);">
          <div class="text-center" :class="MobileSize ? 'my-4 mx-4' : 'my-6 mx-6'">
            <div v-if="itemInboxAll.length !== 0">
              <v-data-table
                 :headers="MobileSize ? dataTableheader2Mobile : dataTableheader2"
                 :hide-default-header="MobileSize ? true : false"
                 :items="itemInboxAll[[index + 1]]"
                 hide-default-footer
                 @pagination="countMemberOther"
                 no-data-text="ยังไม่มีสินค้า"
                 :style="MobileSize ? '' : 'filter: drop-shadow(0px 0px 1px rgba(40, 41, 61, 0.08)) drop-shadow(0px 0.5px 2px rgba(96, 97, 112, 0.16));'"
                >
                  <template v-slot:[`item.product_detailMobile`]="{ item }">
                    <v-row dense>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 12px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                        <span style="font-weight: 700; font-size: 12px; line-height: 26px; color: #333333; text-align: start; display: block;">฿ {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.product_detail`]="{ item }">
                    <!-- <v-row dense v-if="items.expand_table === false">
                      <v-col cols="12" v-if="(index === items.product_list_not_expand.length - 1)" class="pt-6 pb-4">
                        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="12" v-else class="pt-4">
                      </v-col>
                    </v-row>
                    <v-row dense v-else-if="items.expand_table === true">
                      <v-col cols="12" v-if="(index === items.productInbox.length - 1)" class="pt-6 pb-4">
                        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">สินค้าโปรโมชัน</span>
                      </v-col>
                      <v-col cols="12" v-else class="pt-4">
                      </v-col>
                    </v-row> -->
                    <v-row dense>
                      <v-col cols="2" class="mt-3">
                        <v-img :src="item.product_image" max-height="100" max-width="100" contain></v-img>
                      </v-col>
                      <v-col cols="10" align="center" style="margin: 0em auto 0em auto;">
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">รหัสสินค้า: {{ item.sku }}</span>
                        <span style="font-weight: 600; font-size: 10px; line-height: 26px; color: #333333; text-align: start; display: block;">{{ item.product_name }}</span>
                        <span v-if="item.have_attribute === 'yes'" style="font-weight: 600; font-size: 10px; line-height: 20px; color: #333333; text-align: start; display: block;"><span v-if="item.key_1_value">{{ item.key_1_value }} : {{ item.attribute_priority_1 }}</span><span v-if="item.key_2_value">&nbsp; &nbsp; &nbsp; &nbsp; {{ item.key_2_value }} : {{ item.attribute_priority_2 }}</span></span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.price`]="{ item }">
                    {{ Number(item.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                  <template v-slot:[`item.total_price`]="{ item }">
                    {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
                  </template>
                </v-data-table>
            </div>
            <div v-else >
            ยังไม่มีสินค้า
          </div>
          </div>
        </v-card>
      </v-col>
    </v-row>
  </div>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeCutOrder()" class="mr-2">ยกเลิก</v-btn>
            <v-btn v-if="boxproof2" width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmDataAreUsure()">บันทึก</v-btn>
            <v-btn v-else width="110" height="40" rounded color="#27AB9C" disabled class="white--text" @click="confirmDataAreUsure()">บันทึก</v-btn>
            <!-- <v-btn width="110" height="40" rounded color="#27AB9C" :disabled="boxproof2" class="white--text" @click="confirmDataAreUsure()">บันทึก</v-btn> -->
            <!-- {{ boxproof2 }} -->
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogSuccess" width="472" persistent>
    <v-card style="background: #FFFFFF; border-radius: 4px;" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center ml-9" style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">
          แบ่งออเดอร์
        </span>
        <v-btn icon dark @click="closeDialog()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row dense justify="center">
          <v-col cols="12" align="center" class="py-4">
            <!-- <v-icon color="#27AB9C" size="70px">mdi-check-circle</v-icon> -->
            <v-img  :src="require('@/assets/iShip/FrameSusses.png')" loading="lazy"  width="164px"  height="139px" ></v-img>
            <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #012A73;" class="pt-4">เสร็จสิ้น</p>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
    <v-dialog v-model="dialogSuccessChange" width="472" persistent>
    <v-card style="background: #FFFFFF; border-radius: 4px;" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center ml-9" style="font-weight: 700; font-size: 16px; line-height: 24px; color: #27AB9C;">
          เปลี่ยนขนส่ง
        </span>
      </v-toolbar>
      <v-card-text>
        <v-row dense justify="center">
          <v-col cols="12" align="center" class="py-4">
            <!-- <v-icon color="#27AB9C" size="70px">mdi-check-circle</v-icon> -->
            <v-img  :src="require('@/assets/iShip/FrameSusses.png')" loading="lazy"  width="164px"  height="139px" ></v-img>
            <p style="font-weight: 600; font-size: 16px; line-height: 24px; color: #012A73;" class="pt-4">เสร็จสิ้น</p>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-dialog>
  <!-- Dialog แก้สถานะการจัดส่ง  -->
  <v-dialog v-model="dialogUpdateStatus" persistent width="420">
    <v-form ref="formUpdateStatus" :lazy-validation="false">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ปรับสถานะรายการขนส่ง
          </span>
            <v-btn icon dark @click="closeDialogUpdateDataStatus(); date4 = ''; date4Show = ''">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row justify="center" dense class="pt-6">
            <v-col cols="12" align="center">
              <div style="display: flex; align-items: center; justify-content: center;" class="mb-2">
                <v-avatar width="100" height="100">
                  <v-img :src="courierImageUpdate !== '' ? courierImageUpdate : noIMG " max-width="100%" max-height="100%" contain></v-img>
                </v-avatar>
              </div>
              <v-card-title style="display: flex; justify-content: center; font-size: small;">{{ inShipping }}</v-card-title>
              <v-select v-if="checkStatusShipping" dense outlined v-model="shippingIn" @change="handleSelectChangeShippingIn" label="ขนส่งในระบบ" :items="dataShippingIn" item-text="text" item-value="status"></v-select>
              <v-select dense outlined :disabled="statusCourier" label="ขนส่งนอก" v-model="ownShipping" :items="courierItem" item-text="name" item-value="code"></v-select>
              <!-- <v-select dense outlined :disabled="statusCourier" label="ขนส่งนอก" v-model="ownShipping" :items="dataShipping" item-text="ship_name" item-value="ship_name"></v-select> -->
              <v-text-field v-model="trackingNumberUpdate" :disabled="statusTrackingNumber" minlength="1" :rules="ownShipping === 'SELLER OWN FEET' ? [] : [v => /^[a-zA-Z0-9_-]*$/.test(v) || 'กรุณากรอกเฉพาะอักษรภาษาอังกฤษ ตัวเลข - หรือ _', v => !!v || 'กรุณากรอกเลขพัสดุ']" dense outlined label="เลขพัสดุ"></v-text-field>
              <v-select dense outlined label="สถานะ" v-model="statusUpdate" @change="handleSelectChangeUpdate" :items="dataToSelectStatus" item-text="text" item-value="value" :rules="[v => !!v || 'กรุณาเลือกสถานะ']"></v-select>
              <v-text-field readonly v-if="statusUpdate === 'shipped'" :rules="[v => !!v || 'กรุณาระบุวันที่จัดส่ง']" outlined dense v-model="date4Show" placeholder="วันที่จัดส่ง" @click="openSendDatePicker"></v-text-field>
              <!-- <v-textarea v-model="remarkUpdate" dense outlined label="หมายเหตุ" row="1" minlength="1" maxlength="100" counter :rules="[v => !!v || 'กรุณากรอกหมายเหตุ']"></v-textarea> -->
            </v-col>
            <!-- <v-col cols="12" v-if="isOutSource">
              <v-card
                class="mt-3"
                elevation="0"
                @click="onPickFileByOrder()"
                @drop.prevent="DropImageByOrder($event)"
                :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
              >
                <v-file-input
                  v-model="DataImage"
                  :items="DataImage"
                  accept="image/jpeg, image/jpg, image/png"
                  @change="onFileSelectedByOrder(DataImage, index)"
                  id="file_input"
                  multiple
                  :clearable="false"
                  style="display:none"
                ></v-file-input>
                <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                    <v-col cols="12" md="12" align="center">
                      <v-img
                        src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                        width="280.34"
                        height="154.87"
                        contain
                        v-if="orderImagePath.length === 0"
                      ></v-img>
                      <v-row v-if="orderImagePath.length !== 0">
                        <v-col v-for="(items, index2) in orderImagePath" :key="index2" cols="12" md="4" sm="4">
                          <v-card outlined class="pa-1" width="146" height="146">
                            <v-img :src="items" width="130" height="130" contain>
                              <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                <v-icon x-small color="white" dark @click.prevent.stop="RemoveImageMultiByOrder(index2)">mdi-close</v-icon>
                              </v-btn>
                            </v-img>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-col>
                    <v-col cols="12" md="12" style="text-align: center;" v-if="orderImagePath.length === 0">
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                    </v-col>
                  </v-row>
                </v-col>
              </v-card>
            </v-col> -->
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogUpdateDataStatus(); date4 = ''; date4Show = ''">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="dialogUpdateStatus = false; dialogConfirmUpdateStatus = true;" :disabled="(!statusUpdate || !/^[a-zA-Z0-9_-]*$/.test(trackingNumberUpdate)) || (date4 === '' && statusUpdate === 'shipped') || (trackingNumberUpdate === '' && ownShipping !== 'SELLER OWN FEET')">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-form>
  </v-dialog>
  <!-- Dialog ยืนยันแก้สถานะการจัดส่ง  -->
  <v-dialog v-model="dialogConfirmUpdateStatus" persistent width="420">
    <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
          ปรับสถานะรายการขนส่ง
        </span>
          <v-btn icon dark @click="dialogConfirmUpdateStatus = false">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row justify="center" dense class="pt-6">
          <v-col cols="12" align="center">
            <v-card-title :style="{ fontSize: MobileSize ? 'small' : 'large', fontWeight: '600' }">คุณต้องการปรับสถานะรายการขนส่ง ใช่หรือไม่</v-card-title>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="dialogUpdateStatus = true; dialogConfirmUpdateStatus = false;">ยกเลิก</v-btn>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="editDataStatus(selectedUpdate)">ตกลง</v-btn>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <!-- Dialog แสดงสถานะการจัดส่ง  -->
  <v-dialog v-model="dialogStatusIship" persistent width="560">
    <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
          สถานะพัสดุ
        </span>
          <v-btn icon dark @click="dialogStatusIship = false">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row justify="center" dense class="pt-6">
          <v-col cols="12" align="center">
            <v-timeline align-top dense class="mt-2 mb-2">
              <v-timeline-item small class="text-left" v-for="(item, index) in itemsStatusIship" :key="index">{{ item.message }}</v-timeline-item>
            </v-timeline>
          </v-col>
        </v-row>
      </v-card-text>
      <v-card-actions>
        <!-- <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="dialogStatusIship = false">ยกเลิก</v-btn>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="dialogStatusIship = false" :disabled="!statusUpdate || !remarkUpdate || !/^[a-zA-Z0-9]*$/.test(trackingNumberUpdate)">ตกลง</v-btn>
        </v-row> -->
      </v-card-actions>
    </v-card>
  </v-dialog>
  <!-- Dialog แก้หมายเหตุ  -->
  <v-dialog v-model="dialogUpdateRemarkAdmin" persistent width="379">
    <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
      <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
        <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
          จัดการหมายเหตุ
        </span>
          <v-btn icon dark @click="closeDialogUpdateRemarkAdmin()">
          <v-icon color="#27AB9C">mdi-close</v-icon>
        </v-btn>
      </v-toolbar>
      <v-card-text>
        <v-row justify="center" dense class="pt-6">
          <v-col cols="12" align="start" class="mt-2">
            <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รหัสรายการขนส่ง : {{ orderNumber }}</span>
          </v-col>
          <v-col cols="12" align="start">
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;">หมายเหตุ</span>
            <v-textarea v-model="remarkAdmin" dense outlined row="1" minlength="1" maxlength="100" counter :rules="[v => !!v || 'กรุณากรอกหมายเหตุ']"></v-textarea>
          </v-col>
          <!-- <v-col cols="12" align="center">
            <v-card-title style="font-size: large; font-weight: 600;">{{ orderNumber }}</v-card-title>
            <v-textarea v-model="remarkAdmin" dense outlined label="หมายเหตุ" row="1" minlength="1" maxlength="100" counter :rules="[v => !!v || 'กรุณากรอกหมายเหตุ']"></v-textarea>
          </v-col> -->
        </v-row>
      </v-card-text>
      <v-card-actions>
        <v-row dense justify="center" class="pb-4">
          <v-btn width="110" height="40" outlined rounded color="#27AB9C" class="mr-2"  @click="closeDialogUpdateRemarkAdmin()">ยกเลิก</v-btn>
          <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="updateRemarkAdmin()">ตกลง</v-btn>
        </v-row>
      </v-card-actions>
    </v-card>
  </v-dialog>
  <v-dialog
      ref="dialogStartDate"
      v-model="dialogDateRangeExport"
      :return-value.sync="date3"
      width="290px"
    >
      <v-date-picker
        v-model="date3"
        scrollable
        reactive
        range
        locale="TH-th"
        no-title
        :max="date3.length === 0 ? (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10) : startExportLength"
        :min="endExportLength"
      >
        <v-btn
          text
          color="primary"
          @click="cancelChooseExportExcelDate"
        >
          ยกเลิก
        </v-btn>
        <v-spacer></v-spacer>
        <v-btn
          text
          color="primary"
          @click="clearChooseExportExcelDate"
        >
          ล้างค่า
        </v-btn>
        <v-btn
          text
          color="primary"
          :disabled="date3.length === 0"
          @click="confirmExportRangeDate(statusCode, dateStartToSent, dateEndToSent, courierCode, seleteFilterDate, search, 'export')"
        >
          ตกลง
        </v-btn>
      </v-date-picker>
    </v-dialog>
    <v-dialog
      ref="dialogStartDate"
      v-model="sendDateModal"
      width="290px"
    >
      <v-date-picker
        v-model="date4"
        scrollable
        reactive
        locale="TH-th"
        no-title
        :max="(new Date(Date.now())).toISOString().substr(0, 10)"
      >
        <v-spacer></v-spacer>
        <v-btn
          text
          color="primary"
          @click="closeSendDatePicker"
        >
          ยกเลิก
        </v-btn>
        <v-btn
          text
          color="primary"
          :disabled="date4 === ''"
          @click="confirmSendDatePicker(date4)"
        >
          ตกลง
        </v-btn>
      </v-date-picker>
    </v-dialog>
    <!-- Dialog Timeline  -->
    <v-dialog v-model="dialogTimeline" persistent :width="MobileSize ? '100%' : IpadSize ? '75%' : IpadProSize ? '60%' : '40%'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            ติดตามสถานะพัสดุ
          </span>
            <v-btn icon dark @click="dialogTimeline = false">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text v-if="listTimeline.length !== 0">
          <div class="py-8 d-flex justify-center">
            <span>หมายเลขออร์เดอร์: <b>{{orderNum}}</b></span>
          </div>
          <v-timeline v-for="(item, index) in listTimeline" :key="index">
            <v-timeline-item side="end">
              <template v-slot:opposite>
                <span :style="MobileSize ? 'font-size: 12px' : ''">
                  {{new Date(item.timestamp).toLocaleDateString("th-TH", { timeZone: 'Asia/Bangkok', year: "numeric", month: "long", day: "numeric", hour:"numeric", minute:"numeric"})}} น.
                </span>
              </template>
              <div>
                <!-- <div class="text-h6">Content title</div> -->
                <p :style="MobileSize ? 'font-size: small' : ''">
                 <b>{{item.status_desc}}</b> - {{item.location}}
                </p>
              </div>
            </v-timeline-item>
          </v-timeline>
        </v-card-text>
        <v-card-text v-else class="d-flex flex-column align-center pa-10">
          <v-img
            src="@/assets/emptycart.png"
            width="250"
            height="146"
            contain
          ></v-img>
          <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'">พัสดุยังไม่เข้าสู่ระบบ</span>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogB2BDeliveryList" persistent :width="MobileSize ? '100%' : IpadSize ? '75%' : IpadProSize ? '60%' : '50%'">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            รายละเอียดรอบจัดส่ง
          </span>
            <v-btn icon dark @click="closeDialogGetB2BDeliveryList">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text v-if="b2bDeliveryList.length !== 0">
          <v-row>
            <v-col class="pt-5">
              <v-card v-for="(item, index) in b2bDeliveryList" :key="index" class="elevation-0 my-3" outlined>
                <v-card-text>
                  <v-row>
                    <v-col cols="12">
                      <span style="font-size: 18px; font-weight: 700; color: #000000;">รหัสใบส่งสินค้า : <span style="font-size: 18px; font-weight: 400; color: #a0a0a0;">{{item.order_delivery_number}}</span></span>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <span class="ml-2" style="font-weight: bold;">สถานะใบส่งสินค้า : <v-chip :color="ChipColorDeliveryB2B(item.status)" :text-color="textColorDeliveryB2B(item.status)" small><span>{{textDeliveryB2B(item.status)}}</span></v-chip></span>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <span style="font-weight: bold;">จำนวนสินค้า : <span style="font-weight: 400; color: #a0a0a0">{{item.total_shipping_amount}}</span></span>
                    </v-col>
                    <v-col cols="12" md="6" sm="12">
                      <span class="ml-2" style="font-weight: bold;">วันที่จัดส่งสินค้า : <span style="font-weight: 400; color: #a0a0a0">{{changeDeliveryDateFormat(item.date_shipping)}}</span></span>
                    </v-col>
                    <v-col cols="12"  md="6" sm="12">
                      <span style="font-weight: bold;">เวลาที่จัดส่งสินค้า : <span style="font-weight: 400; color: #a0a0a0"></span>{{changeDeliveryDateFormat(item.end_shipping)}}</span>
                    </v-col>
                    <v-col cols="12">
                      <v-btn color="#27AB9C" class="white--text" @click="detailToggle(index)">ดูรายละเอียดสินค้า</v-btn>
                    </v-col>
                    <v-col cols="12">
                      <v-expand-transition>
                        <div v-show="item.show">
                          <v-data-table
                            :headers="b2bDeliveryDetailHeaders"
                            :items="item.details"
                            hide-default-footer
                          >
                            <template v-slot:[`item.image`]="{ item }">
                              <v-avatar tile>
                                <v-img :src="item.image"></v-img>
                              </v-avatar>
                            </template>
                            <template v-slot:[`item.product_name`]="{ item }">
                              <v-row>
                                <v-col>
                                  <span>{{item.product_name}} {{item.attribute !== '-' ? item.attribute : ''}}</span>
                                </v-col>
                              </v-row>
                            </template>
                            <template v-slot:[`item.status`]="{ item }">
                              <v-chip :color="ChipColorDeliveryB2B(item.status)" :text-color="textColorDeliveryB2B(item.status)" small><span>{{textDeliveryB2B(item.status)}}</span></v-chip>
                            </template>
                          </v-data-table>
                          <!-- <v-card v-for="(items, index) in item.details" :key="index">
                            <v-avatar tile>
                              <v-img :src="items.image"></v-img>
                            </v-avatar>
                          </v-card> -->
                        </div>
                      </v-expand-transition>
                    </v-col>
                  </v-row>
                </v-card-text>
                <!-- <v-data-table
                  :headers="b2bDeliveryListHeaders"
                  :items="b2bDeliveryList"
                >
                  <template v-slot:[`item.details`]="{ item }">
                    <v-btn color="#27AB9C" class="white--text" @click="openModalB2BDeliveryDetail(item.details)">รายละเอียดสินค้า</v-btn>
                    <v-card @click="detailToggle" elevation="0">
                      <v-avatar tile>
                        <v-img :src="item.details[0].image"></v-img>
                      </v-avatar>
                    </v-card>
                    <v-expand-transition>
                      <div v-show="show">
                        <v-divider></v-divider>
                        <v-row>
                          <v-col cols="12">
                            <span>ชื่อสินค้า : {{item.details[0].product_name}}</span><br/>
                            <span>ชื่อสินค้า : {{item.details[0].sku}} {{item.details[0].attribute !== null ? item.details[0].attribute : ''}}</span><br/>
                            <span>ราคาสินค้า : {{item.details[0].real_price}}</span><br/>
                            <span>จำนวนสินค้า : {{item.details[0].shipping_amount}}</span><br/>
                            <span>สถานะ : <v-chip :color="ChipColorDeliveryB2B(item.details[0].status)" :text-color="textColorDeliveryB2B(item.details[0].status)" small><span>{{textDeliveryB2B(item.details[0].status)}}</span></v-chip></span><br/>
                          </v-col>
                        </v-row>
                      </div>
                    </v-expand-transition>
                  </template>
                </v-data-table> -->
              </v-card>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-text v-else class="d-flex flex-column align-center pa-10">
          <v-img
            src="@/assets/emptycart.png"
            width="250"
            height="146"
            contain
          ></v-img>
          <span class="mt-4" :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'">ไม่รายละเอียดรอบจัดส่ง</span>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog
      v-model="dialogB2BDeliveryDetail"
    >
      <v-row>
        <v-col cols="12"  v-for="(item, index) in b2bDeliveryDetail" :key="index">
          <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
            <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
              <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
                รายละเอียดรอบจัดส่ง
              </span>
                <v-btn icon dark @click="closeDialogGetB2BDeliveryDetail">
                <v-icon color="#27AB9C">mdi-close</v-icon>
              </v-btn>
            </v-toolbar>
            <v-avatar tile>
              <v-img :src="item.image"></v-img>
            </v-avatar>
            <v-row>
              <v-col>
                <span>ชื่อสินค้า : {{item.product_name}}</span><br/>
                <span>ชื่อสินค้า : {{item.sku}} {{item.attribute !== null ? item.attribute : ''}}</span><br/>
                <span>ราคาสินค้า : {{item.real_price}}</span><br/>
                <span>จำนวนสินค้า : {{item.shipping_amount}}</span><br/>
                <span>สถานะ : <v-chip :color="ChipColorDeliveryB2B(item.status)" :text-color="textColorDeliveryB2B(item.status)" small><span>{{textDeliveryB2B(item.status)}}</span></v-chip></span><br/>
              </v-col>
            </v-row>
          </v-card>
        </v-col>
      </v-row>
    </v-dialog>
  </v-container>
</template>

<script>
import axios from 'axios'
import { Decode } from '@/services'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      selectShop: -3,
      itemsShop: [],
      dataTableheader: [
        { text: 'รายละเอียดสินค้า', value: 'product_attribute_detail', sortable: false, align: 'start', width: '50%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', sortable: false, align: 'center', width: '10%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'total_price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheaderPromotion: [
        { text: '', value: 'product_detail', sortable: false, align: 'start', width: '50%', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'quantity', sortable: false, align: 'center', width: '10%', class: 'backgroundTable fontTable--text' },
        { text: '', value: 'net_price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' }
      ],
      dataToSelectPage: [
        { icon: 'mdi-package-variant-closed', text: 'ทั้งหมด', value: '', count: 0, color: '#27AB9C' },
        { icon: 'mdi-clock-outline', text: 'รอเตรียมจัดส่ง', value: 'waiting_shipping', count: 0, color: '#49C1D4' },
        { icon: 'mdi-account-clock', text: 'รอเรียกพนักงาน', value: 'waiting', count: 0, color: '#FAAD14' },
        { icon: 'mdi-calendar-clock', text: 'รอเข้ารับพัสดุ', value: 'waiting_picked_up', count: 0, color: '#FAAD14' },
        { icon: 'mdi-exit-to-app', text: 'พัสดุเข้าระบบ', value: 'picked_up', count: 0, color: '#49C1D4' },
        { icon: 'mdi-truck-fast', text: 'ระหว่างขนส่ง', value: 'shipping', count: 0, color: '#AC6BF1' },
        { icon: 'mdi-truck-delivery', text: 'กำลังนำส่ง', value: 'progressing', count: 0, color: '#85BEEF' },
        { icon: 'mdi-checkbox-marked-circle-outline', text: 'จัดส่งสำเร็จ', value: 'shipped', count: 0, color: '#52C41A' },
        { icon: 'mdi-phone-missed', text: 'ติดต่อผู้รับไม่ได้', value: 'issue', count: 0, color: '#FAAD14' },
        { icon: 'mdi-restore', text: 'พัสดุตีกลับ', value: 'return', count: 0, color: '#FA1414' },
        { icon: 'mdi-clipboard-arrow-left', text: 'ส่งคืนสำเร็จ', value: 'return_success', count: 0, color: '#52C41A' },
        { icon: 'mdi-cash-check', text: 'ชำระเงินสำเร็จ', value: 'payment_success', count: 0, color: '#52C41A' },
        { icon: 'mdi-cancel', text: 'ยกเลิก', value: 'cancel', count: 0, color: '#F5222D' },
        { icon: 'mdi-cube-off-outline', text: 'พัสดุสูญหาย', value: 'lost', count: 0, color: '#F5222D' },
        { icon: 'mdi-truck', text: 'ขนส่งนอก', value: 'no_status', count: 0, color: '#b6b6b6' }
      ],
      activeTab: 'tab1',
      selectItem: 0,
      statusCode: 'all',
      seleteFilterDate: '',
      searchh: '',
      date: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      date1: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      dialogStartDate: false,
      dialogEndDate: false,
      dialogPrint: false,
      ShowDialog4: false,
      dialogSuccess: false,
      headers: [
        // { text: 'วันที่', value: 'paid_datetime', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text' }
        { text: 'วันที่', value: 'paid_datetime', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'shop_name', sortable: false, align: 'start', width: '180', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อสินค้า', value: 'order_number', sortable: false, align: 'start', width: '200', class: 'backgroundTable fontTable--text' },
        { text: 'พัสดุ', value: 'order_no', sortable: false, align: 'start', width: '170', class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทขนส่ง', value: 'transport_type', sortable: false, align: 'center', width: '120', class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'delivery_status', sortable: false, align: 'start', width: '150', class: 'backgroundTable fontTable--text' },
        { text: 'ผู้รับ', value: 'dst_name', sortable: false, align: 'start', width: '150', class: 'backgroundTable fontTable--text' },
        { text: 'เบอร์โทร', value: 'dst_phone', sortable: false, align: 'start', width: '140', class: 'backgroundTable fontTable--text' },
        { text: 'ที่อยู่', value: 'dst_address', sortable: false, align: 'start', width: '300', class: 'backgroundTable fontTable--text' },
        { text: 'พิมพ์', value: 'print_label_status', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        // { text: 'COD', value: 'cod_amount', sortable: false, align: 'start', width: '123', class: 'backgroundTable fontTable--text' },
        { text: 'วันจัดส่งสำเร็จ', value: 'datetime_status', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุร้านค้า', value: 'remark_shop', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุแอดมิน', value: 'remark_admin', sortable: false, align: 'center', width: '250', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', sortable: false, align: 'start', width: '120', class: 'backgroundTable fontTable--text' }
      ],
      headersMobile: [
        { text: 'รายละเอียด', value: 'Detail', sortable: false, width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      itemTransportsOrder: [],
      dataTableheader2Mobile: [
        { text: 'รายละเอียดสินค้า', value: 'product_detailMobile', sortable: false, align: 'start', width: '100%', class: 'backgroundTable fontTable--text' }
      ],
      dataTableheader2: [
        { text: 'รายละเอียดสินค้า', value: 'product_detail', sortable: false, align: 'start', width: '50%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' },
        { text: 'จำนวน', value: 'quantity', sortable: false, align: 'center', width: '10%', class: 'backgroundTable fontTable--text' },
        { text: 'ราคารวม', value: 'total_price', sortable: false, align: 'center', width: '20%', class: 'backgroundTable fontTable--text' }
      ],
      selected: [],
      dataChangeCourier: [],
      dataCourier: [],
      itemInboxAll: [],
      productInbox: [],
      formCut: {
        order_number: '',
        seller_shop_id: '',
        product_split: []
      },
      indexAddBox: 0,
      toggleshow: false,
      selectOrder: [],
      NotToggle: [],
      countPro: 0,
      dialogSettingCourier: false,
      dialogShippingDetail: false,
      dialogSetting: false,
      checkAllCourier: false,
      itemsStatus: [],
      orderSelect: [],
      splitOrderSelect: [],
      lengthProduct: '',
      selectItemOrder: [],
      formDetail: {
        orders_number: ''
      },
      orderSelectEdit: [],
      dialogSelectCourier: false,
      itemsShipping: [],
      disableButton: true,
      dateStartToSent: '',
      dateEndToSent: '',
      courierCode: '',
      search: '',
      page: 1,
      dataprime: [],
      data1: [],
      limit: 100,
      isSelect: [],
      ListRequestCourier: [],
      dialogListRequestCourier: false,
      SelectRequestCourierItem: [],
      dialogSelectListRequestCourier: false,
      nameRequest: '',
      addressRequest: '',
      dialogSuccessChange: false,
      phoneRequest: '',
      remarkRequest: '',
      userDetail: [],
      sentStartDate: '',
      sentEndDate: '',
      dialogDeleteOrder: false,
      dataDeleteOrder: [],
      confirmDataDelete: [],
      dialogConfirmDeleteOrder: false,
      remarkCancel: '',
      failforsafe: true,
      disabledCount: 0,
      checkboxAll: false,
      selectCourier: '',
      checkboxIndeterminate: false,
      dialogSplitOrder: false,
      boxproof: null,
      sumQuantity: 0,
      shopID: 0,
      listCourier: [],
      isSelected: false,
      noIMG: require('@/assets/NoImage.png'),
      count: -1,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      totalItems: 0,
      dateType: '',
      dataDateType: '',
      dialogUpdateStatus: false,
      dialogConfirmUpdateStatus: false,
      dataToSelectStatus: [
        { text: 'รอเรียกพนักงาน', value: 'waiting' },
        { text: 'รอเข้ารับพัสดุ', value: 'waiting_picked_up' },
        { text: 'พัสดุเข้าระบบ', value: 'picked_up' },
        { text: 'ระหว่างขนส่ง', value: 'shipping' },
        { text: 'กำลังนำส่ง', value: 'progressing' },
        { text: 'จัดส่งสำเร็จ', value: 'shipped' },
        { text: 'ติดต่อผู้รับไม่ได้', value: 'issue' },
        { text: 'พัสดุตีกลับ', value: 'return' },
        { text: 'ส่งคืนสำเร็จ', value: 'return_success' },
        { text: 'ชำระเงินสำเร็จ', value: 'payment_success' },
        { text: 'ยกเลิก', value: 'cancel' },
        { text: 'พัสดุสูญหาย', value: 'lost' }
      ],
      courierImageUpdate: '',
      trackingNumberUpdate: '',
      orderNumberUpdate: '',
      remarkUpdate: '',
      statusUpdate: '',
      courierUpdate: '',
      dataShipping: [
        { id: 1, ship_link: 'https://track.thailandpost.co.th/', ship_name: 'THAILAND POST' },
        { id: 2, ship_link: 'https://www.jtexpress.co.th/', ship_name: 'J&T EXPRESS' },
        { id: 3, ship_link: 'https://th.kerryexpress.com/th/track/', ship_name: 'KERRY EXPRESS' },
        { id: 4, ship_link: 'https://www.scgexpress.co.th/tracking/', ship_name: 'SCG EXPRESS' },
        { id: 5, ship_link: 'https://www.dhl.com/th-th/home/<USER>', ship_name: 'DHL EXPRESS' },
        { id: 6, ship_link: 'https://www.best-inc.co.th/track', ship_name: 'BEST EXPRESS' },
        { id: 7, ship_link: 'https://www.ninjavan.co/th-th/tracking', ship_name: 'NINJA VAN' },
        { id: 8, ship_link: 'https://www.flashexpress.co.th/fle/tracking', ship_name: 'FLASH EXPRESS' },
        { id: 9, ship_link: 'https://www.nimexpress.com/web/p/tracking', ship_name: 'NiM EXPRESS' },
        { id: 10, ship_link: '', ship_name: 'อื่นๆ' }
      ],
      ownShipping: '',
      inShipping: '',
      selectedUpdate: '',
      statusShipping: false,
      statusTrackingNumber: true,
      statusCourier: true,
      trackingNumberUpdateAPI: '',
      ownShippingAPI: '',
      dialogStatusIship: false,
      itemsStatusIship: [],
      disabledShipping: false,
      orderNumber: '',
      remarkAdmin: '',
      dialogUpdateRemarkAdmin: false,
      checkStatusShipping: false,
      shippingIn: '',
      statusShippingIn: '',
      dataShippingIn: [
        { text: 'ขนส่งในระบบ', status: 'in' },
        { text: 'ขนส่งนอกระบบ', status: 'out' }
      ],
      serviceProviderUpdate: '',
      courier: '',
      serviceProviderItem: [
        { text: 'ทั้งหมด', value: 'all' },
        { text: 'ขนส่งใน', value: 'Y' },
        { text: 'ขนส่งนอก', value: 'N' }
      ],
      selectedProvider: '',
      dialogDateRangeExport: false,
      date2: [],
      startExportLength: '',
      endExportLength: '',
      date3: [],
      courierItem: [],
      date4: '',
      date4Show: '',
      sendDateModal: false,
      subdistricttext: '',
      checkSubDistrictError: '',
      districtText: '',
      checkDistrictError: '',
      provinceText: '',
      checkProvinceError: '',
      zipcodeText: '',
      checkZipcodeError: '',
      phoneShop: '',
      lazyOne: false,
      Rules: {
        spaceRule: [
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        emptyText: [v => !!v || 'กรุณากรอกข้อมูล'],
        empty: [v => !!v || 'กรุณาเลือกข้อมูล'],
        seller_name: [
          v => !!v || 'กรุณากรอกชื่อฝ่ายขาย',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        house_Num: [
          v => !!v || 'กรุณากรอกข้อมูล',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tax_id: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษี',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 ตัว',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        tel: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์',
          v => /^[?0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => v.length >= 9 || v === '' || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        contactTel: [
          v => !v || /^[0]{1}/.test(v) || 'กรุณากรอก 0 ตัวแรก',
          v => !v || (v.length >= 9 && v.length <= 10) || 'กรุณากรอกหมายเลขโทรศัพท์ 9 หรือ 10 หลัก'
        ],
        telShop: [
          v => !!v || 'กรุณากรอกเบอร์โทรศัพท์/เบอร์โทรศัพท์มือถือ',
          v => (v.length > 8 && v.length <= 20) || 'กรอกหมายเลขโทรศัพท์ไม่ถูกต้อง',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        address: [
          v => !!v || 'กรุณาระบุที่อยู่',
          v => /^[^\s].*/.test(v) || /^\S.*/.test(v) || (v === '') || 'ข้อมูลไม่ถูกต้อง'
        ],
        postCode: [
          v => v.length === 5 || 'กรุณากรอกรหัสไปรษณีย์'
        ]
      },
      currentShopId: 0,
      listTimeline: [],
      dialogTimeline: false,
      orderNum: '',
      dialogB2BDeliveryList: false,
      b2bDeliveryList: [],
      b2bDeliveryDetail: [],
      b2bDeliveryListHeaders: [
        { text: 'รหัสใบส่งสินค้า', value: 'order_delivery_number', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะรายการ', value: 'status', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'รายละเอียด', value: 'details', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      show: false,
      dialogB2BDeliveryDetail: false,
      b2bDeliveryDetailHeaders: [
        { text: 'รูปสินค้า', value: 'image', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อสินค้า', value: 'product_name', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะ', value: 'status', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จำนวนสินค้า', value: 'shipping_amount', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ราคาต่อชิ้น', value: 'real_price', width: '150', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      onedata: [],
      isOutSource: true,
      orderImagePath: [],
      theRedI: true,
      DataImage: [],
      proofImgCount: 0
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
      this.getListIship()
      this.getCourierType(-3)
      this.getAllOutSourceCourier()
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      if (this.onedata.user.current_role_user.super_admin === false && this.onedata.user.current_role_user.super_admin_platform === false && this.onedata.user.current_role_user.admin_platform === false) {
        this.$router.push({ path: '/' }).catch(() => {})
      }
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  computed: {
    boxproof2 () {
      return this.boxproof
    },
    dateStart () {
      return this.formatDate(this.date)
    },
    dateEnd () {
      return this.formatDate(this.date1)
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    minDate () {
      return this.changeRange(this.date2)
    }
  },
  watch: {
    courierCode (val) {
      this.options.page = 1
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, val, this.seleteFilterDate, this.searchh)
    },
    itemInboxAll (val) {
      if (val.length !== 0) {
        this.boxproof = val.every(e => e.length !== 0 && e.length !== null)
      } else {
        this.boxproof = false
      }
    },
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageOrderShippingMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'manageOrderShipping')
        this.$router.push({ path: '/manageOrderShipping' }).catch(() => {})
      }
    },
    async selectedProvider () {
      await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    date3 (val) {
      if (val.length === 1) {
        this.changeRange(val)
      }
    },
    date (val) {
      // console.log(val, 'val')
    },
    subdistricttext (val) {
      if (/\s/g.test(val)) {
        this.subdistricttext = val.replace(/\s/g, '')
      } else {
        this.checkSubDistrictError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.district === val
          })
          if (result.length !== 0) {
            this.checkSubdistrict = result[0].district
            // this.checkAdressError('checkSubDistrictError')
          } else {
            this.checkAdressError('checkSubDistrictError')
            this.checkSubdistrict = ''
            this.zipcodeText = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    },
    districtText (val) {
      if (/\s/g.test(val)) {
        this.districtText = val.replace(/\s/g, '')
      } else {
        this.checkDistrictError = false
        this.statusError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.amphoe === val
          })
          if (result.length !== 0) {
            this.checkDistrict = result[0].amphoe
            // this.checkAdressError('checkDistrictError')
          } else {
            this.checkAdressError('checkDistrictError')
            this.checkDistrict = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.provinceText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.provinceText = ''
        }
      }
    },
    provinceText (val) {
      if (/\s/g.test(val)) {
        this.provinceText = val.replace(/\s/g, '')
      } else {
        this.checkProvinceError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.province === val
          })
          if (result.length !== 0) {
            this.checkProvince = result[0].province
            // this.checkAdressError('checkProvinceError')
          } else {
            this.checkAdressError('checkProvinceError')
            this.checkProvince = ''
            this.zipcodeText = ''
            this.subdistricttext = ''
            this.districtText = ''
          }
        } else {
          this.zipcodeText = ''
          this.subdistricttext = ''
          this.districtText = ''
        }
      }
    },
    zipcodeText (val) {
      if (/\s/g.test(val)) {
        this.zipcodeText = val.replace(/\s/g, '').substring(0, 5)
      } else {
        this.checkZipcodeError = false
        if (val !== '') {
          const result = Address2021.filter((data) => {
            return data.zipcode === parseInt(val)
          })
          if (result.length !== 0) {
            this.checkZipcode = result[0].zipcode.toString()
            // this.checkAdressError('checkZipcodeError')
          } else {
            this.checkAdressError('checkZipcodeError')
            this.checkZipcode = ''
            this.subdistricttext = ''
            this.districtText = ''
            this.provinceText = ''
          }
        } else {
          this.subdistricttext = ''
          this.districtText = ''
          this.provinceText = ''
        }
      }
    }
  },
  beforeDestroy () {
    this.listOrder = true
  },
  mounted () {
    window.scrollTo(0, 0)
    window.setInterval(() => {
      if (this.listOrder === false) {
        this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      }
    }, 5000)
  },
  methods: {
    async ExportInternalShipping () {
      this.$store.commit('openLoader')
      // var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_internal_shipping`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'รายงานรายการขนส่งในระบบ.csv')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function (error) {
        // handle error
        if (error.response.status === 404 || error.response.status === 500) {
          this.$store.commit('closeLoader')
        }
      })
    },
    async ExportOutsourceShipping () {
      this.$store.commit('openLoader')
      // var dateCurrent = new Date().toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' })
      const auth = {
        Authorization: `Bearer ${this.onedata.user.access_token}`
      }
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_outsource_shipping`,
        method: 'GET',
        responseType: 'blob',
        headers: auth
      }).then((response) => {
        this.$store.commit('closeLoader')
        var fileURL = window.URL.createObjectURL(new Blob([response.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'รายงานรายการขนส่งนอกระบบ.csv')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch(function (error) {
        // handle error
        // console.log(error)
        if (error.response.status === 404 || error.response.status === 500) {
          this.$store.commit('closeLoader')
        }
      })
    },
    async openDialogTimeline (item) {
      this.orderNum = item.order_number
      this.$store.commit('openLoader')
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(item)
      var data = item.order_no
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}v1/iship/shipping-timeline?tracking=${data}`,
        // data: this.exportExcelBody,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'GET'
      }).then((response) => {
        this.listTimeline = response.data.data
        this.$store.commit('closeLoader')
      })
      this.dialogTimeline = true
    },
    checkSearch () {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      this.timer = setTimeout(() => {
        this.options.page = 1
        this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      }, 500)
    },
    async handleSelectChange () {
      this.$store.commit('openLoader')
      this.shopID = this.selectShop
      this.options.page = 1
      await this.getCourierType()
      await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      this.$store.commit('closeLoader')
      // if (this.selectShop !== -3) {
      //   this.shopID = this.selectShop
      //   this.options.page = 1
      //   // await this.getCourierListII()
      //   await this.getCourierType()
      //   // await this.getStatusIship()
      //   await this.getListIship()
      // } else {
      //   this.$store.commit('closeLoader')
      //   // this.$router.go()
      // }
    },
    async getShopData () {
      // await this.$store.dispatch('actiongetIshiplistAllShop')
      // var response = await this.$store.state.ModuleDashBoardForAdmin.stategetIshiplistAllShop
      // if (response.ok === 'y') {
      //   var statAllShop = [{ name_th: 'ทั้งหมด', id: -3 }]
      //   this.itemsShop = response.query_result
      //   this.itemsShop = statAllShop.concat(this.itemsShop)
      // }
      await this.$store.dispatch('actionsgetIshiplistAllShop')
      var response = await this.$store.state.ModuleDashboardTransport.stategetIshiplistAllShop
      if (response.code === 200) {
        var statAllShop = [{ name_th: 'ทั้งหมด', id: -3 }]
        this.itemsShop = response.data
        this.itemsShop = statAllShop.concat(this.itemsShop)
      }
    },
    async updateCourierList (val) {
      this.$store.commit('openLoader')
      var data1 = []
      val.forEach(e => {
        if (e.status === true) {
          data1.push(e.code)
        }
      })
      var data = {
        seller_shop_id: this.shopID,
        shipping_method: [
          {
            service: 'ISHIP',
            courier: data1
          }
        ]
      }
      await this.$store.dispatch('ActionsUpdateCourierList', data)
      const response = await this.$store.state.ModuleManageShop.UpdateCourierList
      if (response.data.message === 'Edit seller shop data success.') {
        this.$swal.fire({ icon: 'success', text: 'บันทึกสำเร็จ', showConfirmButton: false, timer: 2000 })
        this.getCourierListII()
        this.dialogSettingCourier = false
      } else {
        this.$swal.fire({ icon: 'error', text: 'บันทึกไม่สำเร็จ', showConfirmButton: false, timer: 2000 })
      }
      this.$store.commit('closeLoader')
    },
    async getCourierList () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierList', data)
      const response = await this.$store.state.ModuleManageShop.GetCourierList
      if (response.data.ok === 'y') {
        // this.dataCourier = [...response.data.query_result]
        this.dataCourier = this.dataCourier.map(item => {
          const match = response.data.query_result.some(e => e.code === item.code)
          return {
            ...item,
            status: match
          }
        })
        if (this.dataCourier.every((key) => key.status === false)) {
          this.disableButton = true
        } else {
          this.disableButton = false
        }
        // this.dataCourier.every((key) => key.status === false)
        this.$store.commit('closeLoader')
        this.dialogSettingCourier = true
      }
      // this.dataCourier = response.data.query_result
    },
    async getCourierListII () {
      this.$store.commit('openLoader')
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierListII', data)
      const response = await this.$store.state.ModuleManageShop.GetCourierListII
      if (response.data.ok === 'y') {
        this.dataCourier = response.data.query_result
      }
      this.$store.commit('closeLoader')
    },
    async printPDF () {
      var listSelectOrder = []
      for (var i = 0; i < this.selected.length; i++) {
        listSelectOrder.push(this.selected[i].order_no)
      }
      const date = new Date()
      const day = String(date.getDate()).padStart(2, '0')
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const year = date.getFullYear() + 543
      const fileName = `label_shipment_${day}_${month}_${year}.pdf`
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        seller_shop_id: this.shopID,
        role_user: 'admin',
        tracking_number_list: listSelectOrder
      }
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}iship/print_label_pdf_V2`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: data,
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', fileName)
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch((error) => {
        this.$store.commit('closeLoader')
        console.log(error)
        if (error.response && error.response.status === 500) {
          this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2000 })
        }
      })
      // var data = {
      //   seller_shop_id: this.shopID,
      //   role_user: 'admin',
      //   tracking_number_list: listSelectOrder
      // }
      // await this.$store.dispatch('ActionsPrintLabelPDFIship', data)
      // const response = await this.$store.state.ModuleManageShop.PrintLabelPDFIship
      // if (response.data.ok === 'y') {
      //   window.open(`${response.data.query_result}`, '_blank')
      //   this.dialogPrint = false
      //   await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      // }
      // if (response.data.ok === 'y') {
      // this.dialogPrint = false
      // this.$swal.fire({ icon: 'success', text: 'พิมพ์สำเร็จ', showConfirmButton: false, timer: 2000 })
      // } else {
      // this.$swal.fire({ icon: 'error', text: 'พิมพ์ไม่สำเร็จ', showConfirmButton: false, timer: 2000 })
      // }
    },
    // async getUserShopDetail () {
    //   var data = {
    //     seller_shop_id: this.shopID
    //   }
    //   await this.$store.dispatch('actionDetailShop', data)
    //   const response = await this.$store.state.ModuleShop.stateDatailShop
    //   // this.userDetail = response.data
    // },
    async getListCourierIship () {
      var listSelectOrder = []
      for (var i = 0; i < this.selected.length; i++) {
        listSelectOrder.push(this.selected[i].order_no)
      }
      var data = {
        seller_shop_id: this.shopID,
        role_user: 'admin',
        tracking_number_list: listSelectOrder
      }
      // await this.$store.dispatch('ActionsGetListCourierIship', data)
      // const response = await this.$store.state.ModuleManageShop.GetListCourierIship
      // if (response.data.ok === 'y') {
      //   this.ListRequestCourier = [...response.data.query_result]
      // } else {
      //   // if (response.message === 'Not access this function') {
      //   //   await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
      //   //   this.$router.push({ path: '/userInfo' }).catch(() => { })
      //   // } else {
      //   // this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
      //   // }
      // }
      await this.$store.dispatch('ActionsGetListCourierIshipV2', data)
      const response = await this.$store.state.ModuleManageShop.stateGetListCourierIshipV2
      if (response.data.code === 200) {
        // console.log(response)
        this.ListRequestCourier = [...response.data.data]
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
      }
    },
    select ($event) {
      // this.selectItem = item
    },
    // async getStatusIship () {
    //   var data = {
    //     seller_shop_id: this.shopID
    //   }
    //   await this.$store.dispatch('ActionsGetStatusIship', data)
    //   const response = await this.$store.state.ModuleManageShop.GetStatusIship
    //   var itemsStatus = response.data.transports_status
    //   this.dataToSelectPage[0].count = await itemsStatus[0].total
    //   this.dataToSelectPage[1].count = await itemsStatus[1].total
    //   this.dataToSelectPage[2].count = await itemsStatus[2].total
    //   this.dataToSelectPage[3].count = await itemsStatus[3].total
    //   this.dataToSelectPage[4].count = await itemsStatus[4].total
    //   this.dataToSelectPage[5].count = await itemsStatus[5].total
    //   this.dataToSelectPage[6].count = await itemsStatus[6].total
    //   this.dataToSelectPage[7].count = await itemsStatus[7].total
    //   this.dataToSelectPage[8].count = await itemsStatus[8].total
    //   this.dataToSelectPage[9].count = await itemsStatus[9].total
    //   this.dataToSelectPage[10].count = await itemsStatus[10].total
    // },
    // async exportUserExcel () {
    //   this.$store.commit('openLoader')
    //   const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    //   await this.axios({
    //     url: ${process.env.VUE_APP_BACK_END2}affiliate/export/buyer,
    //     headers: { Authorization: Bearer ${oneData.user.access_token} },
    //     method: 'GET',
    //     responseType: 'blob'
    //   }).then((response) => {
    //     this.$store.commit('closeLoader')
    //     const fileURL = window.URL.createObjectURL(new Blob([response.data]))
    //     const fileLink = document.createElement('a')
    //     fileLink.href = fileURL
    //     fileLink.setAttribute('download', 'reportUserJoinAffiliate.xlsx')
    //     document.body.appendChild(fileLink)
    //     fileLink.click()
    //   }).catch(function (error) {
    //     console.log(error)
    //     this.$store.commit('closeLoader')
    //   })
    // },
    async getCourierType (val) {
      var data = {
        seller_shop_id: val === -3 ? -3 : this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierType', data)
      const response = await this.$store.state.ModuleManageShop.GetCourierType
      this.listCourier = response.data.courier_list
    },
    async getListIship (code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword, type) {
      this.$store.commit('openLoader')
      // console.log('code----->', code)
      // console.log('startDate----->', startDate)
      // console.log('endDate----->', endDate)
      // console.log('courierCode----->', courierCode)
      // console.log('seleteFilterDate----->', seleteFilterDate)
      // console.log('searchkeyword----->', searchkeyword)
      // console.log('type----->', type)
      // var data = {
      //   role_user: 'admin',
      //   seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
      //   status_code: code !== undefined && code !== 'all' ? code : '',
      //   start_date: startDate !== undefined ? startDate : '',
      //   end_date: endDate !== undefined ? endDate : '',
      //   courier_code: courierCode !== undefined ? courierCode : '',
      //   search_keyword: searchkeyword !== undefined ? searchkeyword : '',
      //   date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
      //   page: this.options.page,
      //   count: this.options.itemsPerPage,
      //   transport_ngc: this.selectedProvider === '' ? '' : this.selectedProvider === 'Y' ? '1' : '0'
      //   // date_type: 'day' 'yesterday' 'week' 'month',
      // }
      var transportNgc = ''
      if (this.selectedProvider === 'all') {
        transportNgc = ''
      } else {
        transportNgc = this.selectedProvider
      }
      if (type === 'export') {
        const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
        var data = {
          role_user: 'admin',
          seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
          status_code: code !== undefined && code !== 'all' ? code : '',
          start_date: startDate !== undefined ? (new Date(Date.now() - (30 * 24 * 60 * 60 * 1000) - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10) : '',
          end_date: endDate !== undefined ? (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10) : '',
          courier_code: courierCode !== undefined ? courierCode : '',
          search_keyword: searchkeyword !== undefined ? searchkeyword : '',
          date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
          transport_ngc: transportNgc === '' ? '' : transportNgc === 'Y' ? '1' : '0'
          // date_type: 'day' 'yesterday' 'week' 'month',
        }
        await this.axios({
          url: `${process.env.VUE_APP_BACK_END2}iship/exportOrderList`,
          headers: { Authorization: `Bearer ${oneData.user.access_token}` },
          method: 'POST',
          data: data,
          responseType: 'blob'
        }).then((response) => {
          this.$store.commit('closeLoader')
          const fileURL = window.URL.createObjectURL(new Blob([response.data]))
          const fileLink = document.createElement('a')
          fileLink.href = fileURL
          fileLink.setAttribute('download', 'Report_Delivery.xlsx')
          document.body.appendChild(fileLink)
          fileLink.click()
        }).catch((error) => {
          this.$store.commit('closeLoader')
          console.log(error)
          if (error.response && error.response.status === 500) {
            this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2000 })
          }
        })
        // console.log('data', data)
        // await this.$store.dispatch('actionsExportDeliveryReport', data)
        // const response = await this.$store.state.ModuleAdminManage.stateExportDeliveryReport
        // // console.log('response----->', response)
        // const fileName = 'Export_Delivery_Report'
        // const fileURL = window.URL.createObjectURL(new Blob([response]))
        // console.log('fileURL----->', fileURL)
        // const fileLink = document.createElement('a')
        // console.log('fileLink----->', fileLink)
        // fileLink.href = fileURL
        // fileLink.setAttribute('download', fileName + '.xlsx')
        // document.body.appendChild(fileLink)
        // fileLink.click()
        // this.$store.commit('closeLoader')
      } else {
        data = {
          role_user: 'admin',
          seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
          status_code: code !== undefined && code !== 'all' ? code : '',
          start_date: startDate !== undefined ? startDate : '',
          end_date: endDate !== undefined ? endDate : '',
          courier_code: courierCode !== undefined ? courierCode : '',
          search_keyword: searchkeyword !== undefined ? searchkeyword : '',
          date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
          page: this.options.page,
          count: this.options.itemsPerPage,
          transport_ngc: transportNgc === '' ? '' : transportNgc === 'Y' ? '1' : '0'
          // date_type: 'day' 'yesterday' 'week' 'month',
        }
        this.itemTransportsOrder = []
        this.listOrder = await false
        await this.$store.dispatch('ActionsGetListIshipIII', data)
        const response = await this.$store.state.ModuleManageShop.GetListIshipIII
        if (response.data.statusCode === 200) {
          // this.$store.commit('closeLoader')
          var itemsStatus = response.data.data.transports_status
          this.dataToSelectPage[0].count = await itemsStatus[0].total
          this.dataToSelectPage[1].count = await itemsStatus[1].total
          this.dataToSelectPage[2].count = await itemsStatus[2].total
          this.dataToSelectPage[3].count = await itemsStatus[3].total
          this.dataToSelectPage[4].count = await itemsStatus[4].total
          this.dataToSelectPage[5].count = await itemsStatus[5].total
          this.dataToSelectPage[6].count = await itemsStatus[6].total
          this.dataToSelectPage[7].count = await itemsStatus[7].total
          this.dataToSelectPage[8].count = await itemsStatus[8].total
          this.dataToSelectPage[9].count = await itemsStatus[9].total
          this.dataToSelectPage[10].count = await itemsStatus[10].total
          this.dataToSelectPage[11].count = await itemsStatus[11].total
          this.dataToSelectPage[12].count = await itemsStatus[12].total
          this.dataToSelectPage[13].count = await itemsStatus[13].total
          this.dataToSelectPage[14].count = await itemsStatus[14].total
          this.listOrder = await true
          this.proofImgCount = response.data.data.proofImageMaxItems
          this.itemTransportsOrder = [...response.data.data.data]
          this.totalItems = response.data.data.max_items
          this.checkboxAll = false
          this.selected = []
          this.itemTransportsOrder.forEach(item => {
            if (item.delivery_status === 'รอเข้ารับพัสดุ') {
              this.statusShipping = false
            } else { this.statusShipping = false }
          })
          if (response.data.data.data.length === 0) {
            this.totalItems = 0
          }
          window.scrollTo(0, 0)
          this.$store.commit('closeLoader')
        } else if (response.message === 'This user does not have permission') {
          this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์เข้าถึงรายงานการจัดส่ง', showConfirmButton: false, timer: 2000 })
        } else {
          this.$store.commit('closeLoader')
          if (response.data.message === 'Not access this function') {
            await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
            this.$router.push({ path: '/userInfo' }).catch(() => { })
          } else {
            this.listOrder = await false
            this.$swal.fire({ icon: 'error', text: response.data.message, showConfirmButton: false, timer: 2000 })
          }
        }
      }
      // this.$store.commit('openLoader')
      // this.itemTransportsOrder = response.data.data
    },
    updateOptions (options) {
      this.options = options
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    // async updateOrderIship () {
    //   var data = {
    //     // key required
    //     order_number: 'NGS-***********',
    //     order_no: 'TH0117DTHB5A0',
    //     business_type: 'FlashExpress', // need default {PACEL, FLASH, SCG, OUT_SOURCE, THAILAND_POST}
    //     // need default {waiting, picked_up, shipping, progressing, shipped, issue, return, return_success, payment_success, cancel}
    //     delivery_status: 'waiting',
    //     // optional
    //     src_address: '',
    //     dst_address: ''
    //   }
    //   await this.$store.dispatch('ActionsUpdateOrderIship', data)
    //   const response = await this.$store.state.ModuleManageShop.UpdateOrderIship
    // },
    // async cancelOrderIship (val) {
    //   var data = {
    //     order_number: ''
    //   }
    //   await this.$store.dispatch('ActionsCancelOrderIship', data)
    //   const response = await this.$store.state.ModuleManageShop.CancelOrderIship
    // },
    sendToSplit () {
      // this.$route.name
      localStorage.setItem('routeNameBefore', this.$route.name)
      this.$router.push(`/divideorder/${this.orderSelectEdit.id}`).catch(() => {})
    },
    downloadPDF (e) {
      window.open(e)
    },
    backtoSellerMenu () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    async ShowDialog2 () {
      // this.$store.commit('openLoader')
      // this.orderSelect = []
      // this.orderSelect = dataSelect
      // this.dialogCallShipping = true
      await this.getCourierPrice(this.dataprime)
      this.$store.commit('closeLoader')
      this.dialogSelectCourier = true
      this.closeDialogSetting()
    },
    async saveSelectCourier (courier) {
      // this.$store.commit('openLoader')
      await this.ChangeCourier(courier)
      this.$store.commit('closeLoader')
      this.dialogSelectCourier = false
    },
    async ChangeCourier (courier) {
      if (this.dataprime.remark === null || this.dataprime.remark === undefined) {
        this.dataprime.remark = ''
      }
      var data = {
        prepare_order_id: [this.dataprime.prepare_order_id],
        order_number: this.dataprime.order_number,
        courier_code: courier,
        shop_id: 1,
        remark: this.dataprime.remark,
        type: 'change_transport'
      }
      await this.$store.dispatch('actionsChangeCourier', data)
      const response = await this.$store.state.NSGModuleIship.stateChangeCourier
      if (response.result === 'Success') {
        this.dialogSuccessChange = true
        setTimeout(() => {
          this.dialogSuccessChange = false
        }, 2500)
        this.selectCourier = ''
        this.dialogSelectCourier = false
        await this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.searchh, this.page, this.limit)
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          if (response.message === 'เปลี่ยนขนส่งไม่สำเร็จ') {
            this.$swal.fire({ icon: 'error', title: response.message, text: response.data, showConfirmButton: false, timer: 3000 })
          } else {
            this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
          }
        }
        this.selectCourier = ''
        this.dialogSelectCourier = false
      }
    },
    closeDialogSelect () {
      // if (this.selectCourier === '' || this.checkSelectCourier === false) {
      this.selectCourier = ''
      //   this.dialogSelectCourier = false
      // } else {
      //   this.dialogSelectCourier = false
      // }
      this.dialogSelectCourier = false
    },
    async dialogShipping (dataSelect) {
      this.orderSelect = []
      this.orderSelect = dataSelect
      this.data1 = []
      this.NotToggle = []
      this.data1 = this.orderSelect.product_list
      this.NotToggle.push(this.orderSelect.product_list[0])
      this.dialogShippingDetail = true
      // await this.getData1(dataSelect.id)
      // if (this.failforsafe) {
      //   document.getElementsByTagName('body')[0].style.position = 'fixed'
      //   document.getElementsByTagName('body')[0].style.minHeight = '100%'
      // }
    },
    async EditOrder (dataSelect) {
      this.orderSelectEdit = []
      this.orderSelectEdit = dataSelect
      await this.getData1(this.orderSelectEdit.id)
      if (this.failforsafe) {
        this.dialogSetting = true
      }
    },
    closeDialogSetting () {
      this.dialogSetting = false
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    async comfirmDelete (dataDelete, remarkCancel) {
      // 112233
      // console.log('remark', remark)
      this.$store.commit('openLoader')
      var data = {
        service_provider: dataDelete.service_provider,
        order_number: [dataDelete.order_number],
        remark_cancel: remarkCancel,
        courier: dataDelete.business_type
      }
      await this.$store.dispatch('ActionsCancelOrderIship', data)
      const response = await this.$store.state.ModuleManageShop.CancelOrderIship
      if (response.data.message === 'cancel order successfully') {
        this.dialogConfirmDeleteOrder = false
        this.$swal.fire({ icon: 'success', text: 'ลบรายการขนส่งสำเร็จ', showConfirmButton: false, timer: 2000 })
        this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      } else {
        this.dialogConfirmDeleteOrder = false
        this.$swal.fire({ icon: 'error', text: 'ลบรายการขนส่งไม่สำเร็จ', showConfirmButton: false, timer: 2000 })
      }
      this.remarkCancel = ''
      this.$store.commit('closeLoader')
      // var data = {
      //   courier_code: dataDelete.courier_code,
      //   ref_code: dataDelete.ref_code,
      //   track_no: dataDelete.tracking_number,
      //   reason: remark
      // }
      // await this.$store.dispatch('actionsCancelOrderIship', data)
      // const response = await this.$store.state.NSGModuleIship.stateCancelOrderIship
      // if (response.result === 'Success') {
      //   this.dialogConfirmDeleteOrder = false
      //   this.remark = ''
      //   this.$swal.fire({ icon: 'success', text: 'ลบรายการขนส่งสำเร็จ', showConfirmButton: false, timer: 1500 })
      //   await this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
      // } else {
      //   this.remark = ''
      //   this.dialogConfirmDeleteOrder = false
      //   if (response.message === 'Not access this function') {
      //     await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
      //     this.$router.push({ path: '/userInfo' }).catch(() => { })
      //   } else {
      //     if (response.message === 'การยกเลิก order ไม่สำเร็จ') {
      //       this.$swal.fire({ icon: 'error', text: 'ลบรายการขนส่งไม่สำเร็จ', showConfirmButton: false, timer: 1500 })
      //     } else {
      //       this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      //     }
      //   }
      // }
      // this.$store.commit('closeLoader')
    },
    ConfirmdeleteOrderCourier (dataDelete) {
      this.dataDeleteOrder = []
      this.dataDeleteOrder = dataDelete
      this.dialogDeleteOrder = true
    },
    confirmOrderDelete (data) {
      this.dialogDeleteOrder = false
      this.confirmDataDelete = []
      this.confirmDataDelete = data
      this.dialogConfirmDeleteOrder = true
    },
    closeDialogConfirmDeleteOrder () {
      this.remark = ''
      this.confirmDataDelete = []
      this.dialogConfirmDeleteOrder = false
    },
    addDashes (f) {
      if (f === undefined || f === null) {
        return '-'
      } else {
        var number = f.slice(0, 3) + '-' + f.slice(3, 11)
        return number
      }
    },
    changeTab (val, value) {
      this.selectItem = val
      this.statusCode = value
      this.options.page = 1
      // this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
      // code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    setValueStartDate (val) {
      this.sentStartDate = this.formatDate(val)
      this.sentEndDate = ''
      this.dateEndToSent = ''
      this.dateStartToSent = val
    },
    setValueEndDate (val) {
      this.sentEndDate = this.formatDate(val)
      this.dateEndToSent = val
      this.seleteFilterDate = undefined
      // code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
      this.compareDates()
    },
    compareDates () {
      var date1 = new Date(this.dateStartToSent)
      var date2 = new Date(this.dateEndToSent)
      var now = new Date(this.formatDateToSent(new Date().toISOString().substr(0, 10)))
      var diffTime = Math.abs(date2 - date1)
      var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

      if (isNaN(diffDays) || parseInt(diffDays) === 0) {
        var toDayORYester = Math.ceil(Math.abs(now - date1) / (1000 * 60 * 60 * 24))
        this.seleteFilterDate = isNaN(toDayORYester) || parseInt(toDayORYester) === 0 ? 'day' : parseInt(toDayORYester) === 1 ? 'yesterday' : ''
      } else {
        if (parseInt(diffDays) >= 1 && parseInt(diffDays) <= 7) {
          var minOfWeek = new Date()
          minOfWeek.setDate(minOfWeek.getDate() - (minOfWeek.getDay()))
          var maxOfWeek = new Date()
          maxOfWeek.setDate(maxOfWeek.getDate() + (6 - maxOfWeek.getDay()))
          var min = new Date(this.formatDateToSent(new Date(minOfWeek).toISOString().substr(0, 10)))
          var max = new Date(this.formatDateToSent(new Date(maxOfWeek).toISOString().substr(0, 10)))
          if (Math.abs(date1) >= Math.abs(min) && Math.abs(max) >= Math.abs(date2)) {
            this.seleteFilterDate = 'week'
          }
        } else if (parseInt(diffDays) >= 1 && parseInt(diffDays) <= 30) {
          if ((new Date(this.dateStartToSent)).getMonth() === (new Date(this.dateEndToSent)).getMonth() && (new Date(this.dateStartToSent)).getMonth() === (new Date()).getMonth()) {
            this.seleteFilterDate = 'month'
          }
        }
      }
    },
    changeFilterDate (filter) {
      this.seleteFilterDate = filter
      this.options.page = 1
      // var dateStart = ''
      // var dateEnd = ''
      // var date = new Date()
      // var date1 = new Date()
      // if (filter === 'day') {
      //   dateStart = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      //   dateEnd = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      //   this.dateStartToSent = this.formatDateToSent(dateStart)
      //   this.dateEndToSent = this.formatDateToSent(dateEnd)
      // } else if (filter === 'yesterday') {
      //   this.dateStartToSent = new Date(date.setDate(date.getDate() - 1)).toISOString().substr(0, 10)
      //   this.dateEndToSent = new Date(date1.setDate(date1.getDate() - 1)).toISOString().substr(0, 10)
      // } else if (filter === 'week') {
      //   var first = date.getDate() - date.getDay() + 1 // First day is the day of the month - the day of the week
      //   // var last = first + 6 // last day is the first day + 6
      //   this.dateStartToSent = new Date(date.setDate(first)).toISOString().substr(0, 10)
      //   this.dateEndToSent = new Date(date.setDate(date.getDate() + 6)).toISOString().substr(0, 10)
      // } else if (filter === 'month') {
      //   var month = new Date((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
      //   this.dateStartToSent = new Date(new Date(month.getFullYear(), month.getMonth(), 1) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
      //   this.dateEndToSent = new Date(new Date(month.getFullYear(), month.getMonth() + 1, 0) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
      // }
      this.dateStartToSent = undefined
      this.sentStartDate = ''
      this.dateEndToSent = undefined
      this.sentEndDate = ''
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    // async getListOrder (code, startDate, endDate, courierCode, keyword, page, limit) {
    //   // await this.$store.commit('openLoader')
    //   this.listOrder = await false
    //   const data = await {
    //     seller_shop_id: 1,
    //     status_code: code,
    //     start_date: startDate,
    //     end_date: endDate,
    //     courier_code: courierCode,
    //     search_keyword: keyword,
    //     page: page,
    //     limit: limit,
    //     response_type: 'all'
    //   }
    //   await this.$store.dispatch('actionsListOrder', data)
    //   const responseListOrder = await this.$store.state.NSGModuleIship.stateListOrder
    //   if (responseListOrder.result === 'Success') {
    //     this.listOrder = await true
    //     this.itemTransportsOrder = await responseListOrder.data.transports_order
    //     var status = await responseListOrder.data.transports_status
    //     this.dataToSelectPage[0].count = await status[0].total
    //     this.dataToSelectPage[1].count = await status[1].total
    //     this.dataToSelectPage[2].count = await status[2].total
    //     this.dataToSelectPage[3].count = await status[3].total
    //     this.dataToSelectPage[4].count = await status[4].total
    //     this.dataToSelectPage[5].count = await status[5].total
    //     this.dataToSelectPage[6].count = await status[6].total
    //     this.dataToSelectPage[7].count = await status[7].total
    //     this.dataToSelectPage[8].count = await status[8].total
    //     this.dataToSelectPage[9].count = await status[9].total
    //     this.dataToSelectPage[10].count = await status[10].total
    //     await this.$store.commit('closeLoader')
    //   } else {
    //     await this.$store.commit('closeLoader')
    //     if (responseListOrder !== 'CORS') {
    //       var msg = await responseListOrder.message !== undefined ? responseListOrder.message : 'ระบบขัดข้อง'
    //       await this.$swal.fire({ text: `${msg}`, icon: 'error', timer: 2500, showConfirmButton: false })
    //     } else {
    //       this.listOrder = await false
    //       // this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
    //     }
    //   }
    //   await this.$store.commit('closeLoader')
    // },
    async getCourierPrice (dataPri) {
      const courierCodeArray = dataPri.current_courier
      var data = {
        seller_shop_id: 1,
        src_zipcode: dataPri.src_zipcode,
        src_province: dataPri.src_province,
        src_amphure: dataPri.src_amphure,
        src_district: dataPri.src_district,
        dst_zipcode: dataPri.dstZipcode,
        dst_province: dataPri.dstProvince,
        dst_amphure: dataPri.dstAmphure,
        dst_district: dataPri.dstDistrict,
        weight: dataPri.weight,
        width: dataPri.width,
        length: dataPri.length,
        height: dataPri.height
      }
      await this.$store.dispatch('actionsGetCourierForBuyer', data)
      const response = await this.$store.state.NSGModuleIship.stateGetCourierForBuyer
      const resp = response.data
      const differenceCouriers = resp.couriers.filter((element) => element.courier_code !== courierCodeArray)
      if (response.result === 'Success') {
        this.dataChangeCourier = []
        this.dataChangeCourier = differenceCouriers
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    dateFormat (inputDate, format) {
      // parse the input date
      const date = new Date(inputDate)
      const day = date.getDate()
      const month = date.getMonth() + 1
      const year = date.getFullYear()
      // replace the month
      format = format.replace('MM', month.toString().padStart(2, '0'))
      // replace the year
      if (format.indexOf('yyyy') > -1) {
        format = format.replace('yyyy', year.toString())
      } else if (format.indexOf('yy') > -1) {
        format = format.replace('yy', year.toString().substr(2, 2))
      }
      // replace the day
      format = format.replace('dd', day.toString().padStart(2, '0'))
      return format
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    formatDateToSent (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    },
    async OpenDialog (value) {
      if (value === 'print') {
        this.dialogPrint = true
      } else if (value === 'create') {
        this.$router.push({ path: '/createShipping' }).catch(() => {})
      } else if (value === 'setting') {
        // if (this.dataCourier.every((key) => key.status === true)) {
        //   this.checkAllCourier = true
        // } else {
        //   this.checkAllCourier = false
        // }
        this.getCourierList()
      } else if (value === 'requestCourier') {
        await this.getListCourierIship()
        this.dialogListRequestCourier = true
      } else if (value === 'export') {
        this.ExportPDF()
      }
    },
    async CloseDialogSettingCourier () {
      this.dialogSettingCourier = false
      this.checkAllCourier = false
      // await this.getCourier()
    },
    async ClosedialogShippingDetail () {
      this.toggleshow = false
      this.dialogShippingDetail = false
      document.getElementsByTagName('body')[0].style.position = 'initial'
      document.getElementsByTagName('body')[0].style.minHeight = 'initial'
    },
    selectAllToggle (props) {
      if (this.searchh === '') {
        if (this.selected.length !== this.itemTransportsOrder.length - this.disabledCount && this.checkboxAll) {
          // if (this.a) {
          //   this.selected = []
          //   this.a = false
          // } else {
          //   // this.selected = []
          //   // this.a = true
          //   // const self = this
          //   // this.itemTransportsOrder.forEach(item => {
          //   //   if (item.status_desc === 'รอเรียกพนักงาน') {
          //   //     self.selected.push(item)
          //   //   }
          //   // })
          //   this.selected = []
          //   const self = this
          //   props.items.forEach(item => {
          //     if (item.status_desc === 'รอเรียกพนักงาน') {
          //       self.selected.push(item)
          //     }
          //   })
          // }
          this.selected = []
          const self = this
          this.itemTransportsOrder.forEach(item => {
            if (item.delivery_status === 'รอเรียกพนักงาน') {
              if (item.service_provider != null) {
                self.selected.push(item)
              }
            }
          })
          if (this.selected.length > 0 && this.itemTransportsOrder.length === this.selected.length) {
            this.checkboxAll = true
            this.checkboxIndeterminate = false
          } else if (this.selected.length > 0) {
            this.checkboxIndeterminate = true
          }
        } else {
          this.selected = []
          this.checkboxAll = false
          this.checkboxIndeterminate = false
        }
      }
    },
    // async getCourier () {
    //   this.itemsShipping = await []
    //   const data = await {
    //     seller_shop_id: 1
    //   }
    //   await this.$store.dispatch('actionsGetCourier', data)
    //   const response = await this.$store.state.NSGModuleIship.stateGetCourier
    //   if (response.result === 'Success') {
    //     this.dataCourier = await [...response.data.couriers]
    //     this.itemsShipping = await response.data.couriers
    //     var first = await {
    //       courier_code: 'all',
    //       courier_id: 0,
    //       courier_name: 'ขนส่งทั้งหมด',
    //       img_path: '',
    //       seller_shop_id: 1,
    //       status: true
    //     }
    //     await this.itemsShipping.splice(0, 0, first)
    //     // this.itemsShipping.push({
    //     //   courier_code: '',
    //     //   courier_id: 0,
    //     //   courier_name: 'ทั้งหมด',
    //     //   img_path: '',
    //     //   seller_shop_id: 1,
    //     //   status: true
    //     // })
    //     // for (let i = 0; i < response.data.couriers.length; i++) {
    //     //   this.itemsShipping.push(response.data.couriers[i])
    //     // }
    //     if (this.dataCourier.every((key) => key.status === false)) {
    //       this.disableButton = await true
    //     } else {
    //       this.disableButton = await false
    //     }
    //   } else {
    //     if (response.message === 'Not access this function') {
    //       await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
    //       this.$router.push({ path: '/userInfo' }).catch(() => { })
    //     } else {
    //       await this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
    //     }
    //   }
    // },
    confirmDataAreUsure () {
      this.ShowDialog4 = true
    },
    async ShowDialog3 () {
      this.dialogSetting = false
      this.selectOrder = []
      this.itemInboxAll = []
      this.productInbox = []
      this.formCut.product_split = []
      this.indexAddBox = 0

      this.dialogSplitOrder = true
      this.lengthProduct = this.data1.length
      this.selectItemOrder = this.data1.map((z, i) => {
        return {
          index: i + 1,
          net_price: z.net_price,
          price: z.price,
          product_id: z.product_id,
          product_image: z.product_image,
          product_name: z.product_name,
          quantity: z.quantity,
          sku: z.sku,
          have_attribute: z.have_attribute,
          key_1_value: z.key_1_value,
          key_2_value: z.key_2_value,
          attribute_priority_1: z.product_attribute_detail.attribute_priority_1,
          attribute_priority_2: z.product_attribute_detail.attribute_priority_2
        }
      })
      this.addIndex(0)
      this.formDetail.orders_number = this.dataprime.order_number
    },
    ShowExpand () {
      this.toggleshow = !this.toggleshow
    },
    // async getChangeCourier () {
    //   this.itemsShipping = await []
    //   const data = await {
    //     seller_shop_id: 1
    //   }
    //   await this.$store.dispatch('actionsGetCourier', data)
    //   const response = await this.$store.state.NSGModuleIship.stateGetCourier
    //   if (response.result === 'Success') {
    //     this.dataCourier = await [...response.data.couriers]
    //     this.itemsShipping = await response.data.couriers
    //     var first = await {
    //       courier_code: 'all',
    //       courier_id: 0,
    //       courier_name: 'ขนส่งทั้งหมด',
    //       img_path: '',
    //       seller_shop_id: 1,
    //       status: true
    //     }
    //     await this.itemsShipping.splice(0, 0, first)
    //     // this.itemsShipping.push({
    //     //   courier_code: '',
    //     //   courier_id: 0,
    //     //   courier_name: 'ทั้งหมด',
    //     //   img_path: '',
    //     //   seller_shop_id: 1,
    //     //   status: true
    //     // })
    //     // for (let i = 0; i < response.data.couriers.length; i++) {
    //     //   this.itemsShipping.push(response.data.couriers[i])
    //     // }
    //     if (this.dataCourier.every((key) => key.status === false)) {
    //       this.disableButton = await true
    //     } else {
    //       this.disableButton = await false
    //     }
    //   } else {
    //     await this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
    //   }
    // },
    addBox () {
      // this.boxproof = false
      var c = this.itemInboxAll
      this.itemInboxAll = []
      this.itemInboxAll = c
      this.indexAddBox = (this.indexAddBox + 1)
      this.formCut.product_split.push({
        indexBox: this.indexAddBox
      })
      this.addIndex(this.indexAddBox)
    },
    addIndex (index) {
      this.productInbox[[index]] = []
    },
    async selectedOrder (index, indexId) {
      this.itemInboxAll = []
      // this.productInbox[[index]].push(this.selectOrder)
      // if (this.productInbox.length <= index) {
      //   this.productInbox[[index]] = await [this.selectOrder]
      // } else {
      //   await this.productInbox[[index]].push(this.selectOrder)
      // }
      await this.productInbox[[index]].push(this.selectOrder)
      await setTimeout(() => {
        this.removeObjectWithId(this.selectItemOrder, this.selectOrder.index)
      }, 100)
      this.itemInboxAll = this.productInbox
    },
    removeObjectWithId (arr, index) {
      const objWithIdIndex = arr.findIndex((obj) => obj.index === index)
      if (objWithIdIndex > -1) {
        arr.splice(objWithIdIndex, 1)
        return arr
      }
    },
    checkDisableBotton (selectItemLength, itemBoxLength) {
      if (selectItemLength === 0 && (itemBoxLength + 1 !== this.lengthProduct)) {
        return true
      } else if (itemBoxLength + 1 === this.lengthProduct) {
        return true
      } else {
        return false
      }
    },
    closeCutOrder () {
      this.selectOrder = []
      this.itemInboxAll = []
      this.productInbox = []
      this.formCut.product_split = []
      this.dialogSplitOrder = false
      this.indexAddBox = 0
      // this.getOrderShipping()
    },
    async confirmData () {
      // const src = 'https://panit.sdi.inet.co.th/backend/img/news71/description/64465e7657061.jpeg'
      // var ter = this.imageToBase64(src)
      var split = this.productInbox.map(e => {
        return e.map(x => {
          return {
            product_id: x.product_id,
            product_name: x.product_name,
            main_sku: x.sku,
            sku: x.sku,
            product_image: x.product_image,
            quantity: x.quantity
          }
        })
      })
      var data = {
        order_number: this.formDetail.orders_number,
        transport_order_id: this.dataprime.transports_order_id,
        remark: this.dataprime.remark,
        seller_shop_id: 1,
        product_split: split,
        type: 'split_order'
      }
      setTimeout(this.dialogSuccess = false, 2000)
      await this.$store.dispatch('actionsSplitOrder', data)
      const response = await this.$store.state.NSGModuleIship.stateSplitOrder
      if (response.result === 'Success') {
        this.dialogSuccess = true
        setTimeout(() => {
          this.dialogSuccess = false
        }, 2000)
        this.productInbox = []
        this.indexAddBox = 0
        this.ShowDialog4 = false
        this.dialogSplitOrder = false
        await this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.searchh, this.page, this.limit)
      } else {
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
          this.productInbox = []
          this.indexAddBox = 0
          this.ShowDialog4 = false
          this.dialogSplitOrder = false
        }
      }
    },
    // async getListRequestCourier () {
    //   var listSelectOrder = []
    //   for (var i = 0; i < this.selected.length; i++) {
    //     listSelectOrder.push(this.selected[i].tracking_number)
    //   }
    //   const data = {
    //     seller_shop_id: 1,
    //     tracking_number_list: listSelectOrder
    //   }
    //   // await this.$store.dispatch('actionsListRequest', data)
    //   // const response = await this.$store.state.NSGModuleIship.stateListRequest
    //   if (response.result === 'Success') {
    //     this.ListRequestCourier = await [...response.data.courier]
    //   } else {
    //     if (response.message === 'Not access this function') {
    //       await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
    //       this.$router.push({ path: '/userInfo' }).catch(() => { })
    //     } else {
    //       this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
    //     }
    //   }
    // },
    CheckAllCourier (data, check) {
      if (check === true) {
        for (var i = 0; i < data.length; i++) {
          if (this.dataCourier[i].status === false) {
            data[i].status = true
          }
        }
        this.disableButton = false
      } else {
        for (var j = 0; j < data.length; j++) {
          data[j].status = false
        }
        this.disableButton = true
      }
    },
    Checkbutton (val) {
      if (val.every((key) => key.status === false)) {
        this.disableButton = true
      } else {
        this.disableButton = false
        if (val.every((key) => key.status === true)) {
          this.checkAllCourier = true
        } else {
          this.checkAllCourier = false
        }
      }
    },
    async SaveSettingCourier (dataCourierToSent) {
      // const data = []
      // for (var i = 0; i < dataCourierToSent.length; i++) {
      //   data.push({
      //     seller_shop_id: 1,
      //     courier_id: dataCourierToSent[i].courier_id,
      //     status: dataCourierToSent[i].status
      //   })
      // }
      // const dataTosent = {
      //   data_set_courier: data
      // }
      // await this.$store.dispatch('actionsSetCourier', dataTosent)
      // const responseSetting = await this.$store.state.NSGModuleIship.stateSetCourier
      // if (responseSetting.result === 'Success') {
      //   await this.getCourier()
      //   this.dialogSettingCourier = false
      //   this.$swal.fire({ text: `${responseSetting.message}`, icon: 'success', timer: 2500, showConfirmButton: false })
      // } else {
      //   if (responseSetting.message === 'Not access this function') {
      //     await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
      //     this.$router.push({ path: '/userInfo' }).catch(() => { })
      //   } else {
      //     this.$swal.fire({ text: `${responseSetting.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
      //   }
      // }
    },
    async PrintPDF () {
      let data = []
      const trackingNumber = []
      for (let i = 0; i < this.selected.length; i++) {
        trackingNumber.push(this.selected[i].tracking_number)
      }
      data = {
        tracking_number: trackingNumber,
        seller_shop_id: 1
      }
      await this.$store.dispatch('actionsPrintPDF', data)
      const responsePDF = await this.$store.state.NSGModuleIship.statePrintPDF
      if (responsePDF.result === 'Success') {
        this.dialogPrint = false
        await this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.searchh, this.page, this.limit)
        window.open(`${responsePDF.data.LabelA4}`, '_blank')
      } else {
        if (responsePDF.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          // this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 2000 })
          this.$swal.fire({ text: `${responsePDF.message}`, icon: 'error', timer: 1500, showConfirmButton: false })
        }
      }
    },
    async PrintShippingOrder () {
      var listSelectOrder = []
      for (var i = 0; i < this.selected.length; i++) {
        listSelectOrder.push(this.selected[i].order_no)
      }
      var data = {
        seller_shop_id: this.shopID,
        role_user: 'admin',
        tracking_number_list: listSelectOrder
      }
      await this.$store.dispatch('ActionsPrintLabelShippingPDFIship', data)
      const response = await this.$store.state.ModuleManageShop.PrintLabelShippingPDFIship
      if (response.data.ok === 'y') {
        window.open(`${response.data.query_result}`, '_blank')
        // await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.search)
      }
      // let data = []
      // const transportIshipID = []
      // for (let i = 0; i < this.selected.length; i++) {
      //   transportIshipID.push(this.selected[i].transports_iship_id)
      // }
      // data = {
      //   transport_iship_id_array: transportIshipID
      // }
      // await this.$store.dispatch('actionsPrintShipping', data)
      // const responsePDF = await this.$store.state.NSGModuleIship.statePrintShipping
      // if (responsePDF.result === 'Success') {
      //   this.dialogPrint = false
      //   await this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
      //   window.open(`${responsePDF.data.print_shipping_order_path}`, '_blank')
      // } else {
      //   if (responsePDF.message === 'Not access this function') {
      //     await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
      //     this.$router.push({ path: '/userInfo' }).catch(() => { })
      //   } else {
      //     // this.$swal.fire({ icon: 'warning', text: response.message, showConfirmButton: false, timer: 2000 })
      //     this.$swal.fire({ text: `${responsePDF.message}`, icon: 'error', timer: 1500, showConfirmButton: false })
      //   }
      // }
    },
    async SelectRequestCourier (itemCourier) {
      this.dialogListRequestCourier = false
      this.SelectRequestCourierItem = itemCourier
      // this.getUserShopDetail()
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('actionDetailShop', data)
      const response = await this.$store.state.ModuleShop.stateDatailShop
      // await this.$store.dispatch('actionsNSGUserDetailPage')
      // var response = await this.$store.state.UPSModuleUser.stateUPSUserDetailPage
      this.userDetail = response.data[0]
      if (this.userDetail.address_detail.length !== 0) {
        // var address = this.userDetail.address_detail[0].house_no + ' ' + this.userDetail.address_detail[0].detail + ' ' + this.userDetail.address_detail[0].sub_district + ' ' + this.userDetail.address_detail[0].district + ' ' + this.userDetail.address_detail[0].province + ' ' + this.userDetail.address_detail[0].zipcode
        var address = this.userDetail.address_detail[0].house_no + ' ' + this.userDetail.address_detail[0].detail
        this.subdistricttext = this.userDetail.address_detail[0].sub_district
        this.districtText = this.userDetail.address_detail[0].district
        this.provinceText = this.userDetail.address_detail[0].province
        this.zipcodeText = this.userDetail.address_detail[0].zipcode
        this.phoneShop = this.userDetail.shop_phone[0].phone
      }
      this.nameRequest = ''
      this.addressRequest = address
      this.phoneRequest = ''
      this.remarkRequest = ''
      this.dialogSelectListRequestCourier = true
    },
    ClosedialogSelect () {
      this.dialogSelectListRequestCourier = false
      this.SelectRequestCourierItem = ''
      this.nameRequest = ''
      this.addressRequest = ''
      this.phoneRequest = ''
      this.remarkRequest = ''
    },
    async RequestCourier (dataCourier) {
      // const data = {
      //   seller_shop_id: this.shopID,
      //   role_user: 'admin',
      //   tracking_list: dataCourier.tracking_numbers,
      //   courier_code: dataCourier.code,
      //   pickup_address: this.addressRequest,
      //   name: this.userDetail.shop_name,
      //   phone: this.userDetail.shop_phone.length !== 0 ? this.userDetail.shop_phone[0].phone : '',
      //   parcel: dataCourier.count,
      //   remark: this.remarkRequest
      // }
      const data = {
        seller_shop_id: this.shopID === 0 ? -3 : this.shopID,
        tracking_list: dataCourier.tracking_numbers,
        courier_code: dataCourier.code,
        name: this.userDetail.shop_name,
        address: this.addressRequest,
        mobile: this.phoneShop,
        county: this.subdistricttext,
        city: this.districtText,
        state: this.provinceText,
        postcode: this.zipcodeText,
        estimate_parcel: dataCourier.count,
        remark: this.remarkRequest,
        role_user: 'seller',
        service_provider: dataCourier.service_provider
      }
      // await this.$store.dispatch('actionsRequestCourier', data)
      // const response = await this.$store.state.NSGModuleIship.stateRequestCourier
      // await this.$store.dispatch('ActionsCreateRequestCourierIship', data)
      // const response = await this.$store.state.ModuleManageShop.CreateRequestCourierIship
      await this.$store.dispatch('ActionsCreateRequestCourierIshipV2', data)
      const response = await this.$store.state.ModuleManageShop.CreateRequestCourierIshipV2
      if (response.data.ok === 'y') {
        this.dialogSelectListRequestCourier = false
        if (response.data.query_result.status !== false) {
          await this.$swal.fire({ text: 'เรียกขนส่งเข้ารับสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
          await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
          this.$forceUpdate()
        } else {
          await this.$swal.fire({ text: 'ไม่สามารถเรียกขนส่งซ้ำได้', icon: 'error', timer: 2500, showConfirmButton: false })
          await this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
          this.$forceUpdate()
        }
        this.selected = []
      } else {
        this.dialogSelectListRequestCourier = false
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
        this.selected = []
      }
    },
    async getData1 (id) {
      // this.$store.commit('openLoader')
      this.data1 = await []
      this.dataprime = await []
      this.NotToggle = await []
      this.failforsafe = true
      const data = await {
        id: id,
        type: 'transports_order'

      }
      await this.$store.dispatch('actionsGetDaTa', data)
      const response = await this.$store.state.NSGModuleIship.stateDaTa
      if (response.result === 'Success') {
        this.dataprime = response.data
        this.data1 = response.data.products
        this.countPro = this.dataprime.document_buyer.length
        this.NotToggle.push(response.data.products[0])
        this.sumQuantity = this.data1.length === 1 ? this.data1[0].quantity : this.data1.reduce((sum, { quantity }) => sum + quantity, 0)
        this.$store.commit('closeLoader')
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'Not access this function') {
          await this.$swal.fire({ icon: 'warning', text: 'ไม่มีสิทธิ์เข้าใช้งานฟังก์ชันนี้', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/userInfo' }).catch(() => { })
        } else {
          await this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
        }
        this.failforsafe = false
      }
    },
    getColor (val) {
      if (val === 'รอเรียกพนักงาน' || val === 'พัสดุเข้าสู่ระบบ' || val === 'รอเลือกขนส่ง' || val === 'รับพัสดุเข้าระบบ') {
        return '#DAF1E9'
      } else if (val === 'อยู่ระหว่างขนส่ง' || val === 'อยู่ระหว่างจัดส่ง' || val === 'พัสดุถึงสถานีคัดแยก' || val === 'กำลังนำส่ง') {
        return '#FCF0DA'
      } else if (val === 'จัดส่งแล้ว' || val === 'ส่งคืนสำเร็จ' || val === 'ชำระเงินสำเร็จ' || val === 'จัดส่งสำเร็จ') {
        return '#F0F9EE'
      } else {
        return '#FBE5E4'
      }
    },
    getTextColorAlter (val) {
      if (val === 'รอเรียกพนักงาน' || val === 'รอเข้ารับพัสดุ' || val === 'รอเรียกพนักงาน') {
        return '#1c3d77'
      } else if (val === 'พัสดุเข้าระบบ') {
        return '#1c3d77'
      } else if (val === 'รอเตรียมจัดส่ง') {
        return '#ffffff'
      } else if (val === 'รอเลือกขนส่ง') {
        return '#1c3d77'
      } else if (val === 'รับพัสดุเข้าระบบ') {
        return '#497bd4'
      } else if (val === 'อยู่ระหว่างขนส่ง') {
        return '#497bd4'
      } else if (val === 'อยู่ระหว่างจัดส่ง') {
        return '#497bd4'
      } else if (val === 'พัสดุถึงสถานีคัดแยก') {
        return '#497bd4'
      } else if (val === 'กำลังนำส่ง') {
        return '#497bd4'
      } else if (val === 'จัดส่งแล้ว') {
        return '#52C41A'
      } else if (val === 'ส่งคืนสำเร็จ') {
        return '#52C41A'
      } else if (val === 'ชำระเงินสำเร็จ') {
        return '#52C41A'
      } else if (val === 'จัดส่งสำเร็จ') {
        return '#52C41A'
      } else if (val === 'ขนส่งนอก') {
        return ''
      } else {
        return '#F5222D'
      }
      // { icon: require('@/assets/NSG/shipping_icon/All.png'), text: 'ทั้งหมด', value: 'all', count: 0, color: '#27AB9C' },
      // { icon: require('@/assets/NSG/shipping_icon/wait_shipping.png'), text: 'รอเรียกพนักงาน', value: 'waiting', count: 0, color: '#FAAD14' },
      // { icon: require('@/assets/NSG/shipping_icon/wait_shipping.png'), text: 'พัสดุเข้าระบบ', value: 'picked_up', count: 0, color: '#49C1D4' },
      // { icon: require('@/assets/NSG/shipping_icon/shipping.png'), text: 'ระหว่างขนส่ง', value: 'shipping', count: 0, color: '#AC6BF1' },
      // { icon: require('@/assets/NSG/shipping_icon/shipping.png'), text: 'กำลังนำส่ง', value: 'progress', count: 0, color: '#85BEEF' },
      // { icon: require('@/assets/NSG/shipping_icon/success.png'), text: 'จัดส่งสำเร็จ', value: 'shipped', count: 0, color: '#52C41A' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ติดต่อผู้รับไม่ได้', value: 'issue', count: 0, color: '#FAAD14' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'พัสดุตีกลับ', value: 'return', count: 0, color: '#FA1414' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ส่งคืนสำเร็จ', value: 'return_success', count: 0, color: '#52C41A' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ชำระเงินสำเร็จ', value: 'payment_success', count: 0, color: '#52C41A' },
      // { icon: require('@/assets/NSG/shipping_icon/error.png'), text: 'ยกเลิก', value: 'cancel', count: 0, color: '#F5222D' }
    },
    getColorAlter (val, transportNgc) {
      if (val === 'รอเรียกพนักงาน' || val === 'รอเข้ารับพัสดุ' || val === 'รอเรียกพนักงาน') {
        return '#d7e2f6'
      } else if (val === 'พัสดุเข้าระบบ') {
        return '#d7e2f6'
      } else if (val === 'รอเตรียมจัดส่ง') {
        return transportNgc === 'N' ? '#b9b9b9' : '#49C1D4'
      } else if (val === 'รอเลือกขนส่ง') {
        return '#d7e2f6'
      } else if (val === 'รับพัสดุเข้าระบบ') {
        return '#b8ccee'
      } else if (val === 'อยู่ระหว่างขนส่ง') {
        return '#b8ccee'
      } else if (val === 'อยู่ระหว่างจัดส่ง') {
        return '#b8ccee'
      } else if (val === 'พัสดุถึงสถานีคัดแยก') {
        return '#b8ccee'
      } else if (val === 'กำลังนำส่ง') {
        return '#b8ccee'
      } else if (val === 'จัดส่งแล้ว') {
        return '#def9d1'
      } else if (val === 'ส่งคืนสำเร็จ') {
        return '#def9d1'
      } else if (val === 'ชำระเงินสำเร็จ') {
        return '#def9d1'
      } else if (val === 'จัดส่งสำเร็จ') {
        return '#def9d1'
      } else if (val === 'ขนส่งนอก') {
        return ''
      } else {
        return '#fdcbcd'
      }
    },
    getTextColor (val) {
      if (val === 'รอเข้ารับพัสดุ' || val === 'พัสดุเข้าสู่ระบบ' || val === 'รอเลือกขนส่ง' || val === 'รับพัสดุเข้าระบบ' || val === 'รอเรียกพนักงาน') {
        return '#27AB9C'
      } else if (val === 'อยู่ระหว่างขนส่ง' || val === 'อยู่ระหว่างจัดส่ง' || val === 'พัสดุถึงสถานีคัดแยก' || val === 'กำลังนำส่ง') {
        return '#FAAD14'
      } else if (val === 'จัดส่งแล้ว' || val === 'ส่งคืนสำเร็จ' || val === 'ชำระเงินสำเร็จ' || val === 'จัดส่งสำเร็จ') {
        return '#00B500'
      } else {
        return '#F5222D'
      }
    },
    getPrintColor (val) {
      if (val === 'N') {
        return '#FCF0DA'
      } else {
        return '#F0F9EE'
      }
    },
    getTextPrintColor (val) {
      if (val === 'N') {
        return '#FAAD14'
      } else {
        return '#00B500'
      }
    },
    getTextPrint (val) {
      if (val === 'N') {
        return 'รอพิมพ์'
      } else {
        return 'พิมพ์แล้ว'
      }
    },
    reset () {
      this.courierCode = ''
      this.selectItem = 0
      // this.statusCode = ''
      this.dateStartToSent = ''
      this.dateEndToSent = ''
      this.seleteFilterDate = ''
      this.sentEndDate = ''
      this.sentStartDate = ''
      this.search = ''
      this.searchh = ''
      this.selectedProvider = ''
      // this.getCourier()
      // this.getListOrder(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.search, this.page, this.limit)
      this.getListIship(this.statusCode, this.dateStartToSent, this.dateEndToSent, this.courierCode, this.seleteFilterDate, this.searchh)
    },
    async openDialogUpdateDataStatus (data, status) {
      // console.log(status)
      // console.log(this.formatDate(data.sent_date.substr(0, 10)), 'data')
      if (status === 'in') {
        this.statusCourier = true
        this.statusTrackingNumber = true
        this.checkStatusShipping = true
      } else if (status === 'out') {
        this.statusCourier = false
        this.statusTrackingNumber = false
        this.checkStatusShipping = false
      }
      if (data.delivery_status === 'รอเรียกพนักงาน') {
        this.statusUpdate = 'waiting'
      } else if (data.delivery_status === 'รอเข้ารับพัสดุ') {
        this.statusUpdate = 'waiting_picked_up'
      } else if (data.delivery_status === 'พัสดุเข้าระบบ') {
        this.statusUpdate = 'picked_up'
      } else if (data.delivery_status === 'ระหว่างขนส่ง') {
        this.statusUpdate = 'shipping'
      } else if (data.delivery_status === 'กำลังนำส่ง') {
        this.statusUpdate = 'progressing'
      } else if (data.delivery_status === 'จัดส่งสำเร็จ') {
        this.statusUpdate = 'shipped'
      } else if (data.delivery_status === 'ติดต่อผู้รับไม่ได้') {
        this.statusUpdate = 'issue'
      } else if (data.delivery_status === 'พัสดุตีกลับ') {
        this.statusUpdate = 'return'
      } else if (data.delivery_status === 'ส่งคืนสำเร็จ') {
        this.statusUpdate = 'return_success'
      } else if (data.delivery_status === 'ชำระเงินสำเร็จ') {
        this.statusUpdate = 'payment_success'
      } else if (data.delivery_status === 'ยกเลิก') {
        this.statusUpdate = 'cancel'
      } else if (data.delivery_status === 'พัสดุสูญหาย') {
        this.statusUpdate = 'lost'
      } else {
        this.statusUpdate = ''
      }
      this.courierImageUpdate = data.courier_image_path
      this.orderNumberUpdate = data.order_number
      this.trackingNumberUpdate = (data.order_no === data.order_number) || data.order_no === null ? '' : data.order_no
      this.trackingNumberUpdateAPI = this.trackingNumberUpdate
      this.remarkUpdate = data.remark_admin
      if (data.business_type !== 'OUT_SOURCE' || data.business_type !== 'own_shipping') {
        this.disabledShipping = true
        this.inShipping = data.business_type
      } else {
        this.disabledShipping = false
      }
      this.ownShipping = data.business_type !== 'OUT_SOURCE' || data.business_type !== 'own_shipping' ? data.business_type : ''
      // console.log(this.ownShipping)
      this.isOutSource = data.transport_ngc === 'N'
      this.ownShippingAPI = this.ownShipping
      this.serviceProviderUpdate = data.service_provider
      this.date4 = data.sent_date === '-' ? '' : data.sent_date.substr(0, 10)
      this.date4Show = this.formatDate(data.sent_date === '-' ? '' : data.sent_date.substr(0, 10))

      var data3 = [
        this.orderNumberUpdate
      ]

      await this.$store.dispatch('ActionsGetShipmentsImages', data3)
      const response = await this.$store.state.ModuleManageShop.GetShipmentsImages
      if (response.status === 200) {
        this.orderImagePath = response.data.data[0].media.map(e => e.media_path)
      }
      this.dialogUpdateStatus = true
      this.$refs.formUpdateStatus.resetValidation()
    },
    closeDialogUpdateDataStatus () {
      // console.log('123')
      this.courierImageUpdate = ''
      this.orderNumberUpdate = ''
      this.trackingNumberUpdate = ''
      this.trackingNumberUpdateAPI = ''
      this.remarkUpdate = ''
      this.statusUpdate = ''
      this.courierUpdate = ''
      this.ownShipping = ''
      this.ownShippingAPI = ''
      this.serviceProviderUpdate = ''
      this.dialogUpdateStatus = false
      this.checkStatusShipping = false
      this.shippingIn = ''
    },
    handleSelectChangeUpdate (value) {
      this.selectedUpdate = value
    },
    handleSelectChangeShippingIn (value) {
      if (value === 'out') {
        this.statusCourier = false
        this.statusTrackingNumber = false
      } else if (value === 'in') {
        this.statusCourier = true
        this.statusTrackingNumber = true
      }
      this.statusShippingIn = value
    },
    async editDataStatus (value) {
      // console.log('value', value)
      if (this.statusTrackingNumber === false || this.statusShippingIn === 'out') {
        if (this.ownShipping === 'SELLER OWN FEET') {
          this.trackingNumberUpdateAPI = this.trackingNumberUpdate === '' ? this.orderNumberUpdate : this.trackingNumberUpdate
        } else {
          this.trackingNumberUpdateAPI = this.trackingNumberUpdate
        }
        this.ownShippingAPI = this.ownShipping
      }
      if (this.statusShippingIn === 'out') {
        this.serviceProviderUpdate = 'OUT_SOURCE'
      }
      if (value === '') {
        value = this.statusUpdate
      }
      const data = {
        orders: [{
          order_number: this.orderNumberUpdate,
          courier: this.ownShippingAPI,
          tracking_no: this.trackingNumberUpdateAPI,
          status: value,
          service_provider: this.serviceProviderUpdate,
          images: this.orderImagePath
          // sent_date: this.date4
        }]
      }
      if (this.statusUpdate === 'shipped') {
        if (this.statusUpdate === 'shipped') {
          var data2 = {
            orders: [{
              order_number: this.orderNumberUpdate,
              courier: this.ownShippingAPI,
              tracking_no: this.trackingNumberUpdateAPI,
              status: value,
              service_provider: this.serviceProviderUpdate,
              sent_date: this.date4,
              images: this.orderImagePath
            }]
          }
        }
      }
      try {
        // await this.$store.dispatch('ActionsUpdateStatusShipping', this.statusUpdate === 'shipped' ? data2 : data)
        // const response = await this.$store.state.ModuleManageShop.UpdateStatusShipping
        await this.$store.dispatch('actionsUpdateOrderV2', this.statusUpdate === 'shipped' ? data2 : data)
        const response = await this.$store.state.ModuleDashboardTransport.stateUpdateOrderV2
        if (response.statusCode === 200) {
          this.statusUpdate = value
          this.getListIship()
          this.closeDialogUpdateDataStatus()
          this.dialogConfirmUpdateStatus = false
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2000,
            timerProgressBar: true,
            icon: 'success',
            text: 'อัปเดต สถานะรายการขนส่ง สำเร็จ'
          })
        }
      } catch (error) {
        this.closeDialogUpdateDataStatus()
        this.dialogConfirmUpdateStatus = false
        this.$swal.fire({
          icon: 'warning',
          text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่',
          showConfirmButton: false,
          timer: 2000
        })
      }
    },
    async getStatusIship (trackingNumber, serviceProvider) {
      if (serviceProvider === 'ISHIP') {
        if (trackingNumber) {
          this.dialogStatusIship = true
          try {
            const response = await fetch(`${process.env.VUE_APP_ISHIP_URL}tracking/${trackingNumber}`)
            // const response = await fetch(`${process.env.VUE_APP_ISHIP_URL}tracking/TH47016NDDJ20D0`)
            // console.log('response', response)
            const data = await response.json()
            // console.log('data', data)
            if (data.length === 0) {
              this.itemsStatusIship = [{ message: 'ยังไม่มีสถานะพัสดุ' }]
            } else {
              this.itemsStatusIship = data
            }
            // console.log(this.itemsStatusIship)
          } catch (error) {
            console.error('Error fetching tracking data:', error)
          }
        }
      }
    },
    openDialogUpdateRemarkAdmin (item) {
      this.orderNumber = item.order_number
      this.remarkAdmin = item.remark_admin
      this.courier = item.business_type
      this.dialogUpdateRemarkAdmin = true
    },
    closeDialogUpdateRemarkAdmin () {
      this.orderNumber = ''
      this.remarkAdmin = ''
      this.dialogUpdateRemarkAdmin = false
    },
    async updateRemarkAdmin () {
      var data = {
        order_number: this.orderNumber,
        remark: this.remarkAdmin,
        courier: this.courier
      }
      await this.$store.dispatch('ActionsUpdateRemarkAdmin', data)
      const response = await this.$store.state.ModuleManageShop.stateUpdateRemarkAdmin
      // console.log('response', response)
      if (response.code === 200) {
        this.getListIship()
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'ไม่สามารถทำรายการได้ กรุณาลองใหม่อีกครั้ง',
          showConfirmButton: false,
          timer: 2000
        })
      }
      this.dialogUpdateRemarkAdmin = false
    },
    async openDialogDateLengthExport () {
      this.dialogDateRangeExport = true
    },
    async confirmExportRangeDate (code, startDate, endDate, courierCode, seleteFilterDate, searchkeyword, type) {
      // console.log(this.date2, 'date2')
      await this.date3.sort((a, b) => {
        var startDay = new Date(a)
        var endDay = new Date(b)
        return startDay - endDay
      })
      // console.log(this.date2, 'date2', 1)
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var data = {
        role_user: 'admin',
        seller_shop_id: this.shopID !== 0 ? this.shopID : -3,
        status_code: code !== undefined && code !== 'all' ? code : '',
        start_date: this.date3[0],
        end_date: this.date3.length === 1 ? this.date3[0] : this.date3[1],
        courier_code: courierCode !== undefined ? courierCode : '',
        search_keyword: searchkeyword !== undefined ? searchkeyword : '',
        date_type: seleteFilterDate !== undefined ? seleteFilterDate : '',
        page: this.options.page,
        count: this.options.itemsPerPage,
        transport_ngc: this.selectedProvider === '' ? '' : this.selectedProvider === 'Y' ? '1' : '0'
        // date_type: 'day' 'yesterday' 'week' 'month',
      }
      this.$store.commit('openLoader')
      await this.axios({
        url: `${process.env.VUE_APP_BACK_END2}iship/exportOrderList`,
        headers: { Authorization: `Bearer ${oneData.user.access_token}` },
        method: 'POST',
        data: data,
        responseType: 'blob'
      }).then((response) => {
        this.$store.commit('closeLoader')
        const fileURL = window.URL.createObjectURL(new Blob([response.data]))
        const fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'Report_Delivery.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      }).catch((error) => {
        this.$store.commit('closeLoader')
        console.log(error)
        if (error.response && error.response.status === 500) {
          this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 2000 })
        }
      })
      this.date3 = []
      this.startExportLength = ''
      this.endExportLength = ''
      this.dialogDateRangeExport = false
    },
    async changeRange (date2) {
      this.startExportLength = ''
      this.endExportLength = ''
      // await date2.sort((a, b) => {
      //   var startDay = new Date(a)
      //   var endDay = new Date(b)
      //   return startDay - endDay
      // })
      // console.log(date2, 'มาแล้ว')
      this.endExportLength = (new Date(new Date(date2[0]) - (30 * 24 * 60 * 60 * 1000) - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      // console.log(new Date(new Date(Date.now()) + (30 * 24 * 60 * 60 * 1000)), 'date2[0]')
      // console.log(new Date(Date.now() + (30 * 24 * 60 * 60 * 1000)))
      // console.log(new Date(new Date(date2[0]).getTime() + (30 * 24 * 60 * 60 * 1000)).toISOString().substr(0, 10))
      // console.log((new Date(Date.now() + (30 * 24 * 60 * 60 * 1000) - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
      // console.log(new Date(date2[0]).getTime() > Date.now())
      if (new Date(date2[0]).getTime() + (30 * 24 * 60 * 60 * 1000) < Date.now()) {
        this.startExportLength = new Date(new Date(date2[0]).getTime() + (30 * 24 * 60 * 60 * 1000)).toISOString().substr(0, 10)
      } else {
        this.startExportLength = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      }
      // console.log(this.endExportLength, 456)
      // console.log(this.startExportLength, 789)
    },
    async cancelChooseExportExcelDate () {
      this.dialogDateRangeExport = false
      this.date3 = []
      this.startExportLength = ''
      this.endExportLength = ''
    },
    clearChooseExportExcelDate () {
      this.date3 = []
      this.startExportLength = ''
      this.endExportLength = ''
    },
    async getAllOutSourceCourier () {
      await this.$store.dispatch('actionsgetAllOutSourceCourierII')
      var response = await this.$store.state.ModuleDashboardTransport.stategetAllOutSourceCourierII
      if (response.statusCode === 200) {
        this.courierItem = response.data
      }
    },
    async openSendDatePicker () {
      this.sendDateModal = true
    },
    async closeSendDatePicker () {
      this.sendDateModal = false
      this.date4 = ''
      this.date4Show = ''
    },
    async confirmSendDatePicker (val) {
      this.sendDateModal = false
      this.date4Show = this.formatDate(val)
    },
    clearChooseStartDate () {
      this.date = ''
      this.sentStartDate = ''
    },
    cancelChooseStartDate () {
      this.date = ''
      this.sentStartDate = ''
      this.dialogStartDate = false
    },
    cancelChooseEndDate () {
      this.date1 = ''
      this.sentEndDate = ''
      this.dateEndToSent = ''
      this.dialogEndDate = false
    },
    clearChooseEndDate () {
      this.date1 = ''
      this.sentEndDate = ''
      this.dateEndToSent = ''
    },
    async confirmSentRequestShopData (items) {
      if (this.$refs.formOne.validate(true)) {
        if ((this.checksubdistrictConfirm(this.subdistricttext) || this.checkdistrictConfirm(this.districtText) || this.checkprovinceConfirm(this.provinceText) || this.checkzipcodeConfirm(this.zipcodeText))) {
          if (this.subdistricttext === this.checkSubdistrict && this.districtText === this.checkDistrict && this.provinceText === this.checkProvince && this.zipcodeText === this.checkZipcode) {
            const check = this.checkSendAddress()
            if (check.length !== 0) {
              this.RequestCourier(items)
            } else {
              this.$store.commit('closeLoader')
              this.callCheckAdress()
              this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.checkConfirmAddress()
            this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$store.commit('closeLoader')
          this.callCheckAdress()
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
        }
        // this.modalAwaitCancelOrder = true
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    callCheckAdress () {
      // เช็คเพื่อแสดงข้อความสีแดงกรณีที่ไม่ได้กรอก อำเภอ ตำบล จังหวัด รหัสไปรษณี
      this.checksubdistrictConfirm(this.subdistricttext)
      this.checkdistrictConfirm(this.districtText)
      this.checkprovinceConfirm(this.provinceText)
      this.checkzipcodeConfirm(this.zipcodeText)
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistricttext && data.amphoe === this.districtText && data.province === this.provinceText && data.zipcode === Number(this.zipcodeText)
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistricttext
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.districtText
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.provinceText
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcodeText)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    async openModalB2BDeliveryList (orderNumber) {
      await this.getB2BDeliveryList(orderNumber)
      this.dialogB2BDeliveryList = true
    },
    async openModalB2BDeliveryDetail (item) {
      // await this.getB2BDeliveryList(orderNumber)
      this.b2bDeliveryDetail = item
      this.dialogB2BDeliveryDetail = true
    },
    async getB2BDeliveryList (orderNumber) {
      this.$store.commit('openLoader')
      var data = {
        order_number: orderNumber
      }
      await this.$store.dispatch('ActionsGetB2BDeliveryList', data)
      const response = await this.$store.state.ModuleManageShop.stateGetB2BDeliveryList
      // console.log(response, 456)
      if (response.status === 200) {
        this.$store.commit('closeLoader')
        this.b2bDeliveryList = response.data.data.map(e => {
          return {
            ...e,
            show: false
          }
        })
        // console.log(this.b2bDeliveryList, 1234)
      } else {
        this.b2bDeliveryList = []
        this.$store.commit('closeLoader')
        // this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
      }
    },
    closeDialogGetB2BDeliveryList () {
      this.b2bDeliveryList = []
      this.dialogB2BDeliveryList = false
    },
    closeDialogGetB2BDeliveryDetail () {
      this.b2bDeliveryDetail = []
      this.dialogB2BDeliveryDetail = false
    },
    detailToggle (val) {
      // console.log(val)
      this.b2bDeliveryList[val].show = !this.b2bDeliveryList[val].show
      // this.show = !this.show
    },
    textDeliveryB2B (val) {
      if (val === 'waiting') {
        return 'รอเตรียมจัดส่ง'
      } else if (val === 'inprogress') {
        return 'ระหว่างขนส่ง'
      } else if (val === 'success') {
        return 'จัดส่งสำเร็จ'
      }
    },
    ChipColorDeliveryB2B (val) {
      if (val === 'waiting') {
        return '#d0dbf1'
      } else if (val === 'inprogress') {
        return '#fdc4c7'
      } else if (val === 'success') {
        return '#d8f7ca'
      }
    },
    textColorDeliveryB2B (val) {
      if (val === 'waiting') {
        return '#1c3d77'
      } else if (val === 'inprogress') {
        return '#f5222d'
      } else if (val === 'success') {
        return '#52c41a'
      }
    },
    changeDeliveryDateFormat (date) {
      if (date !== '-') {
        var formatDate = ''
        var time = date.split(' ')[1]
        formatDate = new Date(date).toLocaleDateString('th-TH', { timeZone: 'utc', year: 'numeric', month: 'long', day: 'numeric' }) + ' ' + time + ' น.'
        return formatDate
      } else {
        return '-'
      }
    },
    onPickFileByOrder () {
      document.getElementById('file_input').click()
    },
    async onFileSelectedByOrder (files) {
      if (files && files.length > 0) {
        const file = files
        var base64ImageList = []
        for (let i = 0; i < file.length; i++) {
          const element = file[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
            const base64 = await new Promise((resolve, reject) => {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = async () => {
                resolve(reader.result.split(',')[1])
              }
              reader.onerror = reject
            })

            base64ImageList.push(base64)
            // reader.readAsDataURL()
          }
        }
        // console.log(base64ImageList, 'base64ImageList')
        const Image = {
          image: base64ImageList,
          type: 'shipment',
          seller_shop_id: this.shopID
        }

        this.$store.commit('openLoader')

        await this.$store.dispatch('actionsUploadToS3', Image)
        const response = this.$store.state.ModuleShop.stateUploadToS3
        if (response.message === 'List Success.') {
          this.$store.commit('closeLoader')
          // for (var i = 0; i < response.data.list_path.length; i++) {
          //   this.updateOrderV2Payload.order[index].media.push(response.data.list_path[i].path)
          // }
          var imagePath = response.data.list_path.map(e => e.path)
          for (var i = 0; i < imagePath.length; i++) {
            if (this.orderImagePath.length < 3) {
              this.orderImagePath.push(imagePath[i])
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'ใส่ไม่ได้เกิน 3 ภาพ',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
          this.theRedI = true
        }
      }
    },
    RemoveImageMultiByOrder (index2) {
      this.orderImagePath.splice(index2, 1)
    },
    GoToUploadReceiveOrderIMG () {
      if (this.MobileSize) {
        this.$router.push({ path: '/UpLoadPickUpOrderIMGAdminMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/UpLoadPickUpOrderIMGAdmin' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.headerPromotion /deep/ .v-data-table__wrapper > table > thead > tr:last-child > th {
  color: #6f8fb9 !important;
  border-bottom: none !important;
  height: 0px !important;
}
.vSelectLineHeight /deep/ .v-select__selection--comma {
  line-height: 25px !important;
}
.scrolling-wrapper {
  -webkit-overflow-scrolling: touch;
  overflow-x: scroll;
  overflow-y: hidden;
  white-space: nowrap;
}
.scrolling-wrapper::-webkit-scrollbar {
  display: none;
}
.image-1 {
  filter: invert(100%);
  -webkit-filter: invert(100%);
  color: #27AB9C;
}
.v-tab {
  padding-left: 10px;
  padding-right: 10px;
}
.card {
  display: inline-block;
}
.widthCardMobile {
  width: 124px;
}
.widthCardIpad {
  width: 154px;
}
.widthCardDesktop {
  width: 200px;
}
.HoverToClickCourier {
  color: #333333;
}
.HoverToClickCourier:hover {
  background: rgba(219, 236, 250, 0.6);
  color: #27AB9C !important;
}
.background_color {
  background-color: #FFFFFF;
}
.background_color_Mobile {
  background-color: #FFFFFF;
  border: 1px solid #DAF1E9;
  border-radius: 8px;
}
.card_pdf {
  background: #F4FAFE !important;
  box-shadow: 0px 0px 1px rgba(40, 41, 61, 0.08), 0px 0.5px 2px rgba(96, 97, 112, 0.16) !important;
  border-radius: 16px !important;
}
.card_text_pdf {
  font-weight: 700;
  font-size: 18px;
  line-height: 24px;
  color: #333333;
}
@media only screen and (max-width: 768px) {
  /* For mobile phones: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    font-size: 0.6rem;
    padding: 0 0 0 8px;
  }
}
@media only screen and (min-width: 768px) {
  /* For desktop: */
  .v-data-table /deep/
  .v-data-footer {
    display: flex;
    flex-wrap: inherit !important;
    justify-content: flex-end;
    align-items: center;
    /* font-size: 14px; */
    padding: 0 0 0 8px;
  }
}
.v-data-table /deep/
.v-data-table__wrapper .v-data-table__mobile-row {
  border-bottom: 0px !important;
  padding-left: 4px;
  padding-right: 4px;
}
.v-data-table /deep/ .v-data-table__wrapper .v-data-table__mobile-row {
  height: initial;
  min-height: 48px;
  width: 100%;
  display: inline-block;
}
</style>
<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  ::v-deep .elevation-1 tr th:first-of-type, td:first-of-type {
    background-color: #E6F5F3;
    border-style: none !important;
  }
</style>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(11) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 40px;
          z-index: 10;
          background: white;
        }
        td:nth-child(15) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0px;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(11) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 40px;
        }
        th:nth-child(15) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0px;
        }
      }
    }
    tr:hover {
      td:nth-child(10) { background: #F2F2F2; }
      td:nth-child(13) { background: #F2F2F2; }
      background: #F2F2F2 !important;
    }
  }
</style>

<style>
  .grayscale {
    filter: grayscale(100%);
  }
  .titles0 {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
  }
  .titles1 {
    font-size: 16px;
    font-weight: 700;
    line-height: 24px;
  }
  .titles1_5 {
    font-size: 18px;
    font-weight: 700;
    line-height: 24px;
  }
  .titles2 {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
  }
  .titles2Color {
    font-size: 16px;
    font-weight: 400;
    line-height: 24px;
    color: #27AB9C;
  }
  .validate_error {
    opacity: 1.5;
  }
  .input_text-thai-address-error {
    height: 45px !important;
  }
  .input_text-thai-address {
    border-radius: 8px !important;
  }
  .input_text-thai-address-error input.th-address-input {
    opacity: 1;
    font-size: 14px;
    border-radius: 4px;
    padding-left: 10px;
    border: 2px solid #ff5252  !important;
  }
  .input_text-thai-address input.th-address-input {
    opacity: 1;
    font-size: 14px;
    color: #212121;
    border-radius: 4px;
    padding-left: 10px;
    /* border: 1px solid red !important; */
  }
  .text-error {
    color: #ff5252;
    font-size: 12px;
    line-height: 12px;
    padding: 3px 12px 2px 0px !important;
  }
</style>
