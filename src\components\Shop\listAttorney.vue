<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="border-radius: 8px;">
      <v-card-text>
        <v-row class="pa-0">
          <v-col cols="12" class="mt-2">
            <span v-if="!MobileSize" style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" class="ml-2" >รายการเอกสารมอบอำนาจ</span>
            <span v-else style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายการเอกสารมอบอำนาจ</span>
          </v-col>
        </v-row>
        <!-- <v-row dense>
          <v-col cols="12" class="py-0">
            <a-tabs @change="getAttorneyList" class="changeBorderbottom">
              <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <v-chip small text-color="#27AB9C" color="rgba(39, 171, 156, 0.10)">{{ countall }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab">รอผู้รับมอบยืนยัน <v-chip small text-color="rgb(233, 160, 22)" color="rgb(252, 240, 218)">{{ countwaiting }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="2"><span slot="tab">รออนุมัติ <v-chip small text-color="#FAAD14" color="#FEF6E6">{{ countwait }}</v-chip></span></a-tab-pane>
              <a-tab-pane :key="3"><span slot="tab">อนุมัติ <v-chip small text-color="#52C41A" color="#F0FEE8">{{ countsuccess }}</v-chip></span></a-tab-pane>
            </a-tabs>
          </v-col>
        </v-row> -->
        <v-row dense class="mt-4">
          <v-col cols="12" md="6" sm="6">
            <v-text-field v-model="search" dense hide-details outlined placeholder="ค้นหาจากชื่อ - นามสกุล" style="border-radius: 8px;">
              <v-icon slot="append">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" sm="6">
            <v-row dense class="pt-1">
              <span class="pr-2" style="padding-top: 10px; color: #333333;"><b>สถานะ</b></span>
              <v-select v-model="statusAttorney" :items="listStatus" item-text="text" item-value="status" dense outlined class="setCustomSelect"></v-select>
            </v-row>
          </v-col>
          <v-col cols="12" md="12" sm="12" class="py-4">
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 0">รายการเอกสารมอบอำนาจทั้งหมด {{ countListAttorney }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 1">รายการรอผู้รับมอบยืนยันทั้งหมด {{ countListAttorney }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 2">รายการรออนุมัติเอกสารมอบอำนาจทั้งหมด {{ countListAttorney }} รายการ</span>
            <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 3">รายการอนุมัติเอกสารมอบอำนาจทั้งหมด {{ countListAttorney }} รายการ</span>
          </v-col>
          <v-col cols="12" md="12" sm="12">
            <v-card outlined>
              <v-data-table
              :headers="dataHeader"
              :items="filterDataTable"
              :search="search"
              :page.sync="page"
              style="width:100%;"
              height="100%"
              :items-per-page="10"
              no-results-text="ไม่พบชื่อ - นามสกุลที่ค้นหา"
              no-data-text="ไม่มีข้อมูลในตาราง"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
              @pagination="countAttorney"
              >
                <template v-slot:[`item.id`]="{ item }">
                  {{ dataListAttorney.map(x => {return x.id }).indexOf(item.id) + 1 }}
                </template>
                <template v-slot:[`item.pdf_path`]="{ item }">
                  <v-chip small v-if="item.status_approve === 'Approve'" @click="openPDF(item.pdf_path)">ดูเอกสารมอบอำนาจ</v-chip>
                  <span v-else>-</span>
                </template>
                <template v-slot:[`item.status_approve`]="{ item }">
                  <v-chip small :color="getColorChip(item.status_approve)" :text-color="getTextColorStatus(item.status_approve)">{{ getTextStatus(item.status_approve) }}</v-chip>
                </template>
              </v-data-table>
            </v-card>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      countall: 0,
      countwaiting: 0,
      countwait: 0,
      countsuccess: 0,
      StateStatus: 0,
      search: '',
      dataHeader: [
        { text: 'ลำดับที่', value: 'id', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail', width: '25%' },
        { text: 'ชื่อ - นามสกุลผู้รับมอบ', value: 'receiver', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '25%' },
        { text: 'เอกสารมอบอำนาจ', value: 'pdf_path', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '25%' },
        { text: 'สถานะ', value: 'status_approve', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail', width: '25%' }
      ],
      dataListAttorney: [],
      page: 1,
      countListAttorney: 0,
      sellerShop: '',
      statusAttorney: '',
      listStatus: [
        { text: 'ทั้งหมด', status: '' },
        { text: 'รอผู้รับมอบยืนยัน', status: 'Waiting' },
        { text: 'อนุมัติเอกสารแล้ว', status: 'Approve' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    filterDataTable () {
      if (this.statusAttorney !== '') {
        return this.dataListAttorney.filter(item => {
          return item.status_approve.includes(this.statusAttorney)
        })
      } else {
        return this.dataListAttorney
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.sellerShop = localStorage.getItem('shopSellerID')
      this.getListAttorney()
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/listAttorneyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listAttorney' }).catch(() => {})
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    getAttorneyList (item) {
      this.StateStatus = item
    },
    countAttorney (pagination) {
      this.countListAttorney = pagination.itemsLength
    },
    async getListAttorney () {
      this.$store.commit('openLoader')
      this.dataListAttorney = []
      const data = {
        seller_shop_id: this.sellerShop
        // seller_shop_id: 46
      }
      await this.$store.dispatch('actionListAttorneyShop', data)
      var response = await this.$store.state.ModuleShop.stateListAttorneyShop
      if (response.ok === 'y') {
        this.dataListAttorney = [...response.query_result]
        this.$store.commit('closeLoader')
      } else {
        this.dataListAttorney = []
        this.$store.commit('closeLoader')
      }
    },
    getTextStatus (status) {
      if (status === 'Waiting') {
        return 'รอผู้รับมอบยืนยัน'
      } else if (status === 'Approve') {
        return 'อนุมัติเอกสารแล้ว'
      }
    },
    getTextColorStatus (status) {
      if (status === 'Waiting') {
        return '#E9A016'
      } else if (status === 'Approve') {
        return '#1AB759'
      }
    },
    getColorChip (status) {
      if (status === 'Waiting') {
        return '#FCF0DA'
      } else if (status === 'Approve') {
        return '#F0F9EE'
      }
    },
    openPDF (link) {
      window.open(link, '_blank')
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
</style>
