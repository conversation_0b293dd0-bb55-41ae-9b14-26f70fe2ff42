<template>
  <v-overlay opacity="0.02" :value="overlay">
    <v-row justify="center" v-if="listPopUp.length !== 0">
      <v-dialog v-model="dialog" width="auto">
        <div>
          <v-card flat color="transparent" class="mx-auto my-auto" elevation="0"
            :style="this.linkImage ? 'cursor: pointer;' : ''">
            <v-toolbar align="center" outlined dense elevation="0" color="transparent">
              <span class="flex text-center" :style="!MobileSize ? 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;' : ''">
              </span>
              <v-btn icon small style="float: right; background-color: #FFFFFF; z-index: 999;"
                @click="overlay = false, checkboxs()">
                <v-icon small color="#27AB9C" dark>mdi-window-close</v-icon>
              </v-btn>
            </v-toolbar>
            <!-- ของเก่า -->
            <!-- <v-card-text v-if="listPopUp.type === 'popup_image'" @click="openLink(listPopUp.additional_link)" class="pa-md-0 pa-0">
              <v-img
                :src="this.linkImage"
                :lazy-src="this.linkImage"
                contain
                :style="this.layout === 'vertical'
                  ? 'width: auto !important; height: 100%; background-size: cover; max-width: 420px; max-height: 540px;'
                  : 'max-width: 100%; height: 100%; width: 1480px; background-size: cover;'"
              ></v-img>
            </v-card-text> -->

            <!-- ของใหม่ -->
            <v-carousel cycle :show-arrows="false" hide-delimiter-background v-if="popupImage === true">
              <v-carousel-item v-for="(slide, i) in listPopUp" :key="i">
                <v-img
                  loading="lazy"
                  v-if="slide.status === 'active'"
                  :src="slide.link_media"
                  :lazy-src="slide.link_media"
                  contain
                  :class="slide.additional_link !== '' && slide.additional_link !== null && slide.additional_link !== '-' ? 'imageHaveLink' : ''"
                  :style="slide.layout === 'vertical'
                    ? 'width: auto !important; height: 100%; background-size: cover; max-width: 420px; max-height: 540px;'
                    : 'max-width: 100%; height: 100%; width: 1480px; background-size: cover;'"
                  @click="openLink(slide.additional_link)"
                ></v-img>
              </v-carousel-item>
            </v-carousel>
            <!-- v-if="listPopUp.type === 'popup_video'" -->
            <v-card-text v-else class="pa-md-0 pa-0" >
              <div v-if="this.layout === false">
                <Youtube
                  ref="youtube"
                  :video-id="youtube_parser(this.linkVDO)"
                  @playing="playing"
                  :player-vars="playerVars"
                  width="100%"
                  :height="iframeHeightStyle">
                </Youtube>
              </div>
              <div v-else>
                <Youtube
                  ref="youtube"
                  :video-id="youtube_parser(this.linkVDO)"
                  @playing="playing"
                  :player-vars="playerVars"
                  width="100%"
                  :height="iframeHeightStyle"
                >
                </Youtube>
              </div>
            </v-card-text>

            <v-card-text class="check_list ma-0 pa-0">
              <v-checkbox
                class="pb-0 ma-0"
                v-model="isChecked"
                @change="checkboxs"
                label="ไม่ต้องแสดงหน้านี้อีก"
              ></v-checkbox>
            </v-card-text>
          </v-card>
        </div>
      </v-dialog>
    </v-row>
  </v-overlay>
</template>

<script>
// import { Decode } from '@/services'
import { Youtube } from 'vue-youtube'
export default {
  components: {
    Youtube
  },
  data () {
    return {
      dialog: false,
      overlay: false,
      listPopUp: {},
      layout: '',
      isChecked: false,
      linkImage: '',
      linkVDO: '',
      width: '',
      height: '',
      playerVars: {
        autoplay: 0,
        controls: 1
      },
      countN: 0,
      popupImage: false,
      typePopup: ''
    }
  },
  computed: {
    iframeHeightStyle () {
      const aspectRatio = 16 / 9
      const width = window.innerWidth
      return width / aspectRatio
    },
    checkWidthScreen () {
      if (window.screen.width < 330) {
        return '100%'
      } else if (window.screen.width < 375) {
        return '100%'
      } else if (window.screen.width < 1025) {
        return '420px'
      } else {
        return '420px'
      }
    },
    checkWidthScreenVideo () {
      if (window.screen.width < 330) {
        this.$forceUpdate()
        return '420px'
      } else if (window.screen.width < 375) {
        this.$forceUpdate()
        return '420px'
      } else if (window.screen.width < 1025) {
        this.$forceUpdate()
        return '420px'
      } else {
        this.$forceUpdate()
        return '420px'
      }
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  created () {
    this.typePopup = ''
    this.$EventBus.$on('openPopup', this.openPopup)
    this.openPopup()
  },
  watch: {
    dialog (val) {
      if (val === false) {
        this.dialog = false
        this.overlay = false
        this.checkboxs()
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.sizeWidt = '100%'
        this.sizeHeight = '100%'
      } else {
        this.sizeWidt = '100%'
        this.sizeHeight = '100%'
      }
    }
  },
  methods: {
    async openPopup () {
      await this.$store.dispatch('actionsDetailLandingPage')
      const detail = this.$store.state.ModuleAdminManage.stateDetailLandingPage
      // console.log(detail)

      if (detail.result === 'SUCCESS') {
        if (detail.data.popup.length > 0) {
          // ของเก่า
          // const popupData = detail.data.popup[0]
          // ของใหม่ใช้แบบ Array
          const popupData = detail.data.popup
          this.listPopUp = popupData
          // ถ้า true = horizontal ทั้งหมด แต่ถ้า false จะเป็น vertical ทั้งหมด
          // horizontal = แนวนอน, vertical = แนวตั้ง
          // this.layout = popupData.layout
          this.layout = popupData.every(item => item.layout === 'horizontal')
          // เช็คว่าเป็น รูปภาพ หรือ วิดีโอ
          this.popupImage = popupData.every(item => item.type === 'popup_image')

          // ตรวจสอบว่า link_media เป็น YouTube URL ไหม
          if (this.popupImage === false) {
            const youtubeRegex = /(?:https?:\/\/)?(?:www\.)?(?:youtube\.com\/(?:[^/\n\s]+\/\S+|(?:v|e(?:mbed)?)\/([\w-]+)|.*v=([\w-]+)))|youtu\.be\/([\w-]+)/
            if (youtubeRegex.test(popupData[0].link_media)) {
              this.linkVDO = popupData[0].link_media
            } else {
              // this.linkImage = popupData.link_media
            }
            this.typePopup = 'video'
          } else {
            this.typePopup = 'image'
          }

          // ตรวจสอบค่า countHome จาก sessionStorage
          let n = parseInt(sessionStorage.getItem('countHome')) || 0 // เริ่มต้นที่ 0 ถ้าค่าเป็น null
          if (n < 5) {
            n++ // เพิ่มค่า countHome ถ้ายังไม่ถึง 5
          }

          // อัปเดตค่า countHome ใน sessionStorage
          this.countN = n
          sessionStorage.setItem('countHome', n)

          // ไม่ให้แสดง overlay เมื่อ countHome ถึง 5
          if (n < 5) {
            this.dialog = true
            this.overlay = true
          } else {
            this.overlay = false
          }
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          title: 'เกิดข้อผิดพลาด',
          text: detail.message || 'ไม่สามารถดึงข้อมูลได้',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
      }
    },
    async checkboxs () {
      if (this.isChecked === true) {
        sessionStorage.setItem('countHome', 5)
      } else {
        sessionStorage.setItem('countHome', this.countN)
      }
    },
    closePopup () {
      this.dialog = false
      this.overlay = false
    },
    openLink (url) {
      if (url.length === 0) {
        // console.log(url.length)
      } else {
        this.overlay = false
        this.dialog = false
        if (url !== '-' && url !== '' && url !== null) {
          window.location.href = url
        }
        // if (localStorage.getItem('AuthorityUser') !== null) {
        //   this.onedata = JSON.parse(Decode.decode(localStorage.getItem('AuthorityUser')))
        //   if (this.onedata.user !== undefined) {
        //     window.open(url)
        //     if (url.includes('NGC')) {
        //       this.$router.push({ path: url.split('NGC')[1] }).catch(() => {})
        //     } else {
        //       window.open(url)
        //     }
        //   } else {
        //     this.$router.push({ path: '/Login' }).catch(() => {})
        //   }
        // } else {
        //   if (url.includes('NGC')) {
        //     localStorage.setItem('CurrentPath', url.split('NGC')[1])
        //     if (url.split('NGC')[1] === '/AboutUs' || url.split('NGC')[1] === '/Payment' || url.split('NGC')[1] === '/Blog' || url.split('NGC')[1] === '/Contact') {
        //       this.$router.push({ path: url.split('NGC')[1] }).catch(() => {})
        //     } else {
        //       this.$router.push({ path: '/Login' }).catch(() => {})
        //     }
        //   } else {
        //     window.open(url)
        //   }
        // }
      }
    },
    youtube_parser (url) {
      // console.log('0', url)
      let ID = ''
      url = url.replace(/(>|<)/gi, '').split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
      if (url[2] !== undefined) {
        ID = url[2].split(/[^0-9a-z_-]/i)
        ID = ID[0]
      } else {
        // console.log('เข้ามาทำไม')
        ID = false
      }
      // console.log(ID)
      return ID
    },
    playing () {
      // console.log('we are watching!!!')
    }
  }
}
</script>

<style scoped>
.video-container iframe {
  display: block;
  width: 100%;
  height: 100%;
  border: none;
}

.imageHaveLink {
  cursor: pointer;
}
::v-deep .v-dialog {
  box-shadow: none;
  overflow-y: hidden !important;
}
::v-deep .bk-from {
  opacity: 0.89;
  background-color: rgb(33, 33, 33);
  border-color: rgb(33, 33, 33);
}
.home-popup__content {
  -webkit-box-flex: 0;
  flex: 0 1 auto;
  position: relative;
  width: 80%;
  max-width: 438px;
  max-height: 100%;
}
.centerImage
{
 text-align:center;
 display:block;
}
img-v {
  -moz-transform: scaleY(-1);
  -o-transform: scaleY(-1);
  -webkit-transform: scaleY(-1);
  transform: scaleY(-1);
  filter: FlipV;
  -ms-filter: "FlipV";
}
img-h {
  -moz-transform: scaleX(-1);
  -o-transform: scaleX(-1);
  -webkit-transform: scaleX(-1);
  transform: scaleX(-1);
  filter: FlipH;
  -ms-filter: "FlipH";
}
::v-deep .v-overlay {
  opacity: 0.89;
}
.check {
  /* position: relative; */
  display: inline-block;
  border-bottom: 1px dotted rgb(255, 255, 255);
}
.check_list {
  /* max-width: 438px; */
  background-color: rgb(255, 255, 255);
  color: #fff;
  text-align: left;
  /* padding-top: -60px; */
  /* margin-top: -23px; */
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
  /* Position the tooltip text - see examples below! */
  /* position: absolute; */
  z-index: 1;
}
.tooltip {
  position: relative;
  /* display: inline-block; */
  border-bottom: 1px dotted black; /* If you want dots under the hoverable text */
}
::v-deep .v-messages{
  display: none;
}

.tooltip .tooltiptext {
  visibility: hidden;
  width: 120px;
  background-color: rgb(0, 0, 0);
  color: #fff;
  text-align: center;
  padding: 5px 0;
  margin-top: 32%;
  margin-left: 80%;
  /* margin-top: 40hv; */
  border-radius: 6px;
  position: absolute;
  z-index: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.tooltip .tooltiptext::after {
  content: " ";
  position: absolute;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: transparent transparent black transparent;
}
.tooltip:hover .tooltiptext {
  visibility: visible;
}
</style>
