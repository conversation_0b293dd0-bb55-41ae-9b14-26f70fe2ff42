$blue: #74b9ff;
.container {
  margin: 60px auto;
  background: #fff;
  padding: 0;
  border-radius: 7px;
}
.profile-image-user {
  width: 50px;
  height: auto;
  border-radius: 40px;
}
.profile-image {
  width: 50px;
  height: 50px;
  border-radius: 40px;
}
.profile-image-h {
  width: 40px;
  height: 40px;
}
.settings-tray {
  background: #2562af;
  padding: 3px 15px;
//   border-radius: 7px;
  .no-gutters {
    padding: 0;
  }
  &--right {
    float: right;
    i {
      margin-top: 10px;
      font-size: 25px;
      color: grey;
      margin-left: 14px;
      transition: .3s;
      &:hover {
        color: $blue;
        cursor: pointer;
      }
    }
  }
}
.search-box {
  background: #fafafa;
  padding: 10px 13px;
  .input-wrapper {
    background: #fff;
    border-radius: 40px;
    i {
      color: grey;
      margin-left: 7px;
      vertical-align: middle;
    }
  }
}
input {
  border: none;
  border-radius: 30px;
  width: 80%;
  &::placeholder {
    color: #e3e3e3;
    font-weight: 300;
    margin-left: 20px;
  }
  &:focus {
    outline: none;
  }
}
.friend-drawer {
  padding: 10px 15px;
  display: flex;
  vertical-align: baseline;
  background: #fff;
  transition: .3s ease;
  &--grey {
    background: #eee;
  }
  .text {
    margin-left: 12px;
    width: 70%;
    h6 {
      margin-top: 6px;
      margin-bottom: 0;
    }
    p {
      margin: 0;
    }
  }
  .time {
    color: grey;
  }
  &--onhover:hover {
    background: $blue;
    cursor: pointer;
    p,
    h6,
    .time {
      color: #fff !important;
    }
  }
}
hr {
  margin: 1px auto;
  width: 100%;
}
.chat-bubble {
  padding: 10px 14px;
  background: #eee;
  margin: 10px 30px;
  border-radius: 9px;
  position: relative;
  animation: fadeIn 1s ease-in;
  &:after {
    content: '';
    position: absolute;
    top: 50%;
    width: 0;
    height: 0;
    border: 20px solid transparent;
    border-bottom: 0;
    margin-top: -10px;
  }
  &--left {
     &:after {
      left: 0;
      border-right-color: #eee;
      border-left: 0;
      margin-left: -20px;
      }
  }
  &--right {
    &:after {
      right: 0;
      border-left-color: $blue;
      border-right: 0;
      margin-right: -20px;
    }
  }
}
@keyframes fadeIn {
    0% {
        opacity: 0;
    }
    100% {
        opacity: 1;
    }
}
.offset-md-9 {
  .chat-bubble {
    background: $blue;
    color: #fff;
  }
}
.chat-box-tray {
  background: #eee;
  display: flex;
  align-items: baseline;
  padding: 10px 15px;
  align-items: center;
  margin-top: 19px;
  bottom: 0;
  input {
    margin: 0 10px;
    padding: 6px 2px;
  }
  i {
    color: grey;
    font-size: 30px;
    vertical-align: middle;
    &:last-of-type {
      margin-left: 25px;
    }
  }
}
