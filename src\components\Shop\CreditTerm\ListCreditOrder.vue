<template>
  <v-container :class="MobileSize ? 'mt-2' : 'mx-0'">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">รายการสั่งซื้อสินค้าแบบเครดิตเทอม</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoSellerMenu()">mdi-chevron-left</v-icon> รายการสั่งซื้อสินค้าแบบเครดิตเทอม</v-card-title>
      <v-card-text>
        <v-row no-gutters>
          <v-col cols="12" class="py-0 pr-2">
            <a-tabs @change="getRequest">
              <a-tab-pane :key="0"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countall }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="1"><span slot="tab">ชำระเงินสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countSuccess }}</a-tag></span></a-tab-pane>
              <a-tab-pane :key="2"><span slot="tab">ยังไม่ชำระเงิน <a-tag color="#1B5DD6" style="border-radius: 8px;">{{ countNotPaid }}</a-tag></span></a-tab-pane>
              <!-- <a-tab-pane :key="3"><span slot="tab">ชำระเงินไม่สำเร็จ <a-tag color="#F5222D" style="border-radius: 8px;">{{ countFailed }}</a-tag></span></a-tab-pane> -->
              <!-- <a-tab-pane :key="4"><span slot="tab">ยกเลิก <a-tag color="#F5222D" style="border-radius: 8px;">{{ countCancel }}</a-tag></span></a-tab-pane> -->
            </a-tabs>
          </v-col>
          <v-col v-if="disableTable === true" cols="12" md="6" sm="6" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3 pt-3' : 'pl-0 pr-2 mb-3 pt-3'">
            <v-text-field class=".rounded-lg" v-model="search" placeholder="ค้นหาจากรหัสการสั่งซื้อ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" class="pt-2 pb-2" v-if="disableTable === true">
            <v-row dense>
              <v-col cols="12" md="6" class="pt-0">
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'" v-if="StateStatus === 0">รายการเครดิตเทอมทั้งหมด {{ showCountRequest }} รายการ</span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'" v-else-if="StateStatus === 1">รายการเครดิตเทอมที่ชำระเงินสำเร็จ {{ showCountRequest }} รายการ</span>
                <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'" v-else-if="StateStatus === 2">รายการเครดิตเทอมที่ยังไม่ชำระเงิน {{ showCountRequest }} รายการ</span>
                <!-- <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'" v-else-if="StateStatus === 3">รายการเครดิตเทอมที่ชำระเงินไม่สำเร็จ {{ showCountRequest }} รายการ</span> -->
                <!-- <span :class="IpadSize || IpadProSize || MobileSize ? 'fontSizeTitleMobile2': 'fontSizeTitle2'" v-else-if="StateStatus === 4">รายการเครดิตเทอมที่ยกเลิก {{ showCountRequest }} รายการ</span> -->
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" v-if="disableTable === true">
            <v-data-table
            :headers="headers"
            :items="DataList"
            :search="search"
            style="width:100%;"
            height="100%"
            :page.sync="page"
            @pagination="countRequest"
            no-results-text="ไม่พบรายการสั่งซื้อ"
            no-data-text="ไม่มีรายการสั่งซื้อ"
            :update:items-per-page="itemsPerPage"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.date`]="{ item }">
                <span>{{ new Date(item.date).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' }) }}</span>
              </template>
              <template v-slot:[`item.paid_datetime`]="{ item }">
                <span>{{ item.paid_datetime === null ? '-' : new Date(item.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric'}) }}</span><br>
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                <span>{{ Number(item.total_amount).toLocaleString(undefined, {minimumFractionDigits: 2}) }} </span>
              </template>
              <template v-slot:[`item.transaction_status`]="{ item }">
                <span v-if="item.transaction_status === 'Success'">
                  <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Not Paid' || item.transaction_status === 'Overdue'">
                  <v-chip class="ma-2" color="#E5EFFF" text-color="#1B5DD6">ยังไม่ชำระเงิน</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Fail'">
                  <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
                </span>
                <span v-else-if="item.transaction_status === 'Cancel'">
                  <v-chip class="ma-2" color="#F7D9D9" text-color="#F5222D">ชำระเงินไม่สำเร็จ</v-chip>
                </span>
              </template>
              <template v-slot:[`item.pdf_path`] = "{ item }">
                <v-btn width="32px" height="32px" color="#F2F2F2" class="px-2 ml-4" small @click="goQU(item.pdf_path)" v-if="item.pdf_path !== null">
                  <v-icon color="#27AB9C" class="ma-1"> mdi-file-document </v-icon>
                </v-btn>
                <div style="color: #27AB9C;" class="px-12" v-else >
                  -
                </div>
              </template>
              <template v-slot:[`item.action`]="{ item }">
                <v-btn text rounded color="#27AB9C" class="px-2" small @click="goDetail(item.payment_number)">
                  <b>รายละเอียด</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-col>
          <v-col cols="12" v-if="disableTable === false" align="center">
            <div class="my-5">
              <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-if="StateStatus === 0"><b>คุณยังไม่มีรายการเครดิตเทอม</b></h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 1"><b>คุณยังไม่มีรายการเครดิตเทอมที่ชำระเงินสำเร็จ</b></h2>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;" v-else-if="StateStatus === 2"><b>คุณยังไม่มีรายการเครดิตเทอมที่ยังไม่ชำระเงิน</b></h2>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      shopDetail: '',
      // modal , dialog, table
      disableTable: false,

      // tab bar--------------------------------------------------------
      StateStatus: 0,
      countall: 0,
      showCountRequest: 0,
      countSuccess: 0,
      countNotPaid: 0,
      countFailed: 0,
      countCancel: 0,

      // table order list--------------------------------------------------------
      search: '',
      page: 1,
      pageCount: 0,
      itemsPerPage: 10,
      DataAll: [],
      DataList: [],
      headers: [
        { text: 'วันที่', value: 'date', filterable: false, sortable: false, align: 'start', width: '150px', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสการสั่งซื้อ', value: 'payment_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'ราคา', value: 'total_amount', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', filterable: false, align: 'start', width: '150px', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ใบเสนอราคา', value: 'pdf_path', filterable: false, sortable: false, width: '110px', class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  async created () {
    this.$EventBus.$emit('changeNav')
    this.$EventBus.$emit('checkpath')
    this.shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
    localStorage.removeItem('creditTermOrdernumber')
    localStorage.removeItem('creditTerm')
    this.GetCreditOrder()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/sellerlistCreditOrderMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/sellerlistCreditOrder' }).catch(() => {})
      }
    },
    StateStatus (val) {
      // console.log('state is', val)
      if (val === 0) {
        this.DataList = this.DataAll.all !== undefined ? this.DataAll.all : []
        // console.log('data', this.DataList)
        if (this.DataList.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 1) {
        this.DataList = this.DataAll.success !== undefined ? this.DataAll.success : []
        // console.log('data', this.DataList)
        if (this.DataList.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 2) {
        this.DataList = this.DataAll.not_paid !== undefined ? this.DataAll.not_paid : []
        // console.log('data', this.DataList)
        if (this.DataList.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 3) {
        this.DataList = this.DataAll.fail !== undefined ? this.DataAll.fail : []
        // console.log('data', this.DataList)
        if (this.DataList.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 4) {
        this.DataList = this.DataAll.cancel !== undefined ? this.DataAll.cancel : []
        // console.log('data', this.DataList)
        if (this.DataList.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    }
  },
  methods: {
    backtoSellerMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      if (this.MobileSize) {
        this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
      } else {
        this.$router.push({ path: '/seller?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name })
      }
    },
    async GetCreditOrder () {
      var shopData = {
        seller_shop_id: this.shopDetail.id
      }
      await this.$store.dispatch('actionsSellerListOrderCreditTerm', shopData)
      var response = await this.$store.state.ModuleShop.stateListOrderCreditTerm
      this.DataAll = response.data
      this.DataList = this.DataAll
      // console.log('datalist', this.DataList)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.disableTable = true
        this.countall = this.DataAll.all.length
        this.countSuccess = this.DataAll.success.length
        this.countNotPaid = this.DataAll.not_paid.length
        this.countFailed = this.DataAll.fail.length
        this.countCancel = this.DataAll.cancel.length
        if (this.StateStatus === 0) {
          this.DataList = this.DataAll.all
          // console.log('datalist', this.DataList)
          if (this.DataList.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 1) {
          this.DataList = this.DataAll.success
          if (this.DataList.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 2) {
          this.DataList = this.DataAll.not_paid
          if (this.DataList.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 3) {
          this.DataList = this.DataAll.fail
          if (this.DataList.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 4) {
          this.DataList = this.DataAll.cancel
          if (this.DataList.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
      } else {
        this.disableTable = false
        this.$store.commit('closeLoader')
      }
      // console.log('ShopData', shopData)
      // console.log('response', response)
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    getRequest (item) {
      this.StateStatus = item
      // i dnk
      this.page = 1
    },
    goDetail (item) {
      // console.log('go!!' + item)
      localStorage.setItem('creditTermOrdernumber', item)
      if (!this.MobileSize) {
        this.$router.push({ path: `/sellerListCreditTerm?order_number=${item}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/sellerListCreditTermMobile?order_number=${item}` }).catch(() => {})
      }
    },
    goQU (item) {
      window.open(`${item}`)
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(7) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(7) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>

<style>
.fontSizeTitle {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile {
  font-weight: 700; font-size: 14px; line-height: 48px; color: #333333;
}
.fontSizeTitle2 {
  font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;
}
.fontSizeTitleMobile2 {
  font-weight: 400; font-size: 14px; line-height: 24px; color: #333333;
}
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #636363 !important;
}
</style>
