<template>
    <v-container>
        <v-card>
          <v-col cols="12" md="12" sm="12">
             <span style="font-weight: bold; font-size: 20px;">จัดการFlashSale</span>
             <a-tabs @change="changePageProduct">
                <a-tab-pane :key="0" class="mt-6"><span slot="tab" :style="MobileSize? 'font-size: 15px;': 'font-weight: bold; font-size: 20px;'">แฟลชเซลล์ปัจจุบัน</span>
                </a-tab-pane>
                <a-tab-pane :key="1" class="mt-6"><span slot="tab" :style="MobileSize? 'font-size: 15px;': 'font-weight: bold; font-size: 20px;'">แฟลชเซลล์ที่จบแล้ว</span>
                </a-tab-pane>
             </a-tabs>
             <!-- <v-col cols="12" md="12" sm="12">
                <v-row no-gutters >
                    <v-col cols="7" md="7" sm="7" align="start">
                      {{timeFlashSale}} {{countFlashSale}} รายการ
                    </v-col>
                    <v-col cols="5" md="5" sm="5" align="end">
                        ค้นหา
                    </v-col>
                </v-row>
             </v-col> -->
             <v-col cols="12" md="12" sm="12" class="pa-0">
                <v-col cols="12" md="12" sm="12" class="pa-0">
                        <DataFlashSale
                        v-if="checkDetDetail"
                        :propsData="dataFlashSale"
                        :header="timeFlashSale"
                    />
                    </v-col>
             </v-col>
           </v-col>
        </v-card>
    </v-container>
</template>

<script>
// import { defineComponent } from '@vue/composition-api'

export default ({
  components: {
    DataFlashSale: () => import('@/components/Shop/ManageFlashSale/dataFlashsale')
  },
  data () {
    return {
      limit: 5,
      checkDetDetail: false,
      test: [],
      text: 'tt',
      itemsDes: 0,
      countFlashSale: 0,
      timeFlashSale: 'รายการแฟลชเซลล์ปัจจุบัน',
      dataFlashSale: []
    }
  },
  computed: {
    checkWidth () {
      return window.screen.width
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  async created () {
    await this.getDetail()
  },
  mounted () {
    this.$EventBus.$on('pageChange', this.getDetail)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('pageChange')
    })
  },
  methods: {
    async getDetail (val) {
      this.checkDetDetail = false
      var shopID = localStorage.getItem('shopSellerID')
      var page
      if (val !== undefined) {
        page = val
      } else {
        page = 1
      }
      var data = {
        seller_shop_id: parseInt(shopID),
        page: page,
        limit: this.limit
      }
      console.log('data', data)
      await this.$store.dispatch('actionsGetFlashSale', data)
      var res = await this.$store.state.ModuleManageFlashSale.stateGetFlashSale
      console.log('res', res)
      if (res.result === 'SUCCESS') {
        this.dataFlashSale = res.data.data
        this.dataFlashSale[0].product_list = [{
          id: 3368,
          seller_shop_id: 19,
          name: 'NGC ทดสอบสร้างสินค้า',
          sku: 'TNGC01',
          short_description: 'สำหรับทดสอบ',
          message_status: 'new',
          stock_status: 'in stock',
          stock_count: 55,
          inventory_stock: 55,
          images_URL: [
            'https://devinet-eprocurement.one.th/static/shop/19/products_3368/3368_1717056280_dl4IG.jpeg'
          ],
          real_price: 800,
          fake_price: 1000,
          special_price: '',
          discount_percent: '20%',
          stars: 0,
          isFavorite: false,
          values: 800,
          revenue_price: '800.00',
          vat_include: 0,
          vat_default: 'no',
          sold: 2
        }
        ]
      } else {
        this.dataFlashSale = []
      }
      this.checkDetDetail = true
      console.log('this.dataFlashSale', this.dataFlashSale)
    },
    changePageProduct (item) {
      console.log('item', item)
      this.itemsDes = item
      if (item === 1) {
        this.timeFlashSale = 'รายการแฟลชเซลล์ที่จบไปแล้ว'
        // this.selectCategory()
      } else {
        this.timeFlashSale = 'รายการแฟลชเซลล์ปัจจุบัน'
      }
      this.$EventBus.$emit('changeTab', this.timeFlashSale)
    }
  }
})
</script>
<style scoped>

</style>
