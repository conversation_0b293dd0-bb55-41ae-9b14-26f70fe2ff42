<template>
  <div class="div_overflow" v-if="statusPage === true">
     <v-row>
        <v-col cols="9" >
          <a-form id="components-form-demo-normal-login" :form="form" class="login-form" @submit="handleSubmit">
              <v-row class="ml-2 mr-2" no-gutters>
                <v-col cols="12">
                  <v-card outlined id="step-1">
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10">
                        <h2 >ข้อมูลทั่วไป</h2>
                      </v-col>
                      <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                      <!-- <v-col cols="5" md="2" class="pl-3"><span class="f-right">{{$t('seller.product.detail.enable_product')}}</span></v-col>
                      <v-col cols="7" md="9" class="pl-3">
                        <v-switch v-model="product_status" :label="`${message}`"></v-switch>
                      </v-col> -->
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* ชื่อสินค้า</span></v-col>
                      <v-col cols="12" md="9" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'product_name', { initialValue: product_name, rules: Rule.product_name }]" @change="ChangeInput('product_name')" placeholder="รหัสสินค้า" suffix=" " />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* รหัส SKU</span></v-col>
                      <v-col cols="12" md="9" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'product_sku', { initialValue: product_sku, rules: Rule.product_sku }]" @change="ChangeInput('product_sku')" placeholder="รหัสสินค้า" suffix=" " />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2"  class="pl-3 mb-5"><span class="f-right">* หมวดหมู่สินค้า</span></v-col>
                      <v-col cols="12" md="9" class="pl-3 mb-5">
                          <cool-select v-model="SelectCategory"  :items="ListCategory" placeholder="หมวดหมู่สินค้า" />
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* ตัวเเทนจำหน่าย</span></v-col>
                      <v-col cols="12" md="9" class="pl-3 mb-5">
                        <cool-select v-model="SelectSupplier" :items="ListSupplier" placeholder="ตัวเเทนจำหน่าย" >
                          <div slot="after-items-fixed">
                            <a-row type="flex" justify="center" class="py-3">
                              <span class="pt-1">ต้องการเพิ่มตัวเเทนจำหน่าย</span>
                              <a-col :span="12" class="pl-2 pr-2"><a-input v-model="AddSupplier" placeholder="ตัวเเทนจำหน่าย" suffix=" " /></a-col>
                              <a-button @click="AddNewSupplier()"><a-icon type="plus" /> เพิ่ม</a-button>
                            </a-row>
                          </div>
                        </cool-select>
                      </v-col>
                      <v-col cols="12" md="2"  class="pl-3 mb-5"><span class="f-right">* เเบรน</span></v-col>
                      <v-col cols="12" md="9" class="pl-3 mb-5">
                        <cool-select v-model="SelectBrand" :items="ListBrand" placeholder="เเบรน">
                          <div slot="after-items-fixed">
                            <a-row type="flex" justify="center" class="py-3">
                              <span class="pt-1">ต้องการเพิ่มเเบรน</span>
                              <a-col :span="12" class="pl-2 pr-2"><a-input v-model="AddBrand" placeholder="เเบรน" suffix=" " /></a-col>
                              <a-button @click="AddNewBrand()"><a-icon type="plus" /> เพิ่ม</a-button>
                            </a-row>
                          </div>
                        </cool-select>
                      </v-col>
                      <v-col cols="2" class="pl-3 mb-5"> <span class="f-right" style="padding-bottom:80px">รายละเอียดสินค้า</span></v-col>
                      <v-col cols="9"  class="pl-3 mb-5">
                        <a-textarea placeholder="รายละเอียดสินค้า" :rows="4" v-model="product_description" @change="ChangeInput('product_description')"  />
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>

                <!-- ------------------------------------ข้อมูลการขาย------------------------------------- -->
                <v-col cols="12">
                  <v-card outlined id="step-2" class="mt-5">
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10">
                        <h2 >ข้อมูลการขาย</h2>
                      </v-col>
                      <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* คลัง</span></v-col>
                       <v-col cols="12" md="9" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'stock', { initialValue: stock, rules: Rule.stock }]" @change="ChangeInput('stock')" placeholder="คลัง" suffix=" " />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* ราคา</span></v-col>
                      <v-col cols="12" md="9" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'price', { initialValue: price, rules: Rule.price }]" @change="ChangeInput('price')" placeholder="ราคา" suffix="บาท" />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* น้ำหนัก</span></v-col>
                      <v-col cols="12" md="9" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'weight', { initialValue: weight, rules: Rule.weight }]" @change="ChangeInput('weight')" placeholder="น้ำหนัก" suffix="กิโลกรัม" />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3 mb-5"><span class="f-right">* ขนาดพัสดุ</span></v-col>
                      <v-col cols="12" md="2" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'wide', { initialValue: wide, rules: Rule.wide }]" @change="ChangeInput('size')" placeholder="กว้าง" suffix="ซม." />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'long', { initialValue: long, rules: Rule.long }]" @change="ChangeInput('size')" placeholder="ยาว" suffix="ซม." />
                        </a-form-item>
                      </v-col>
                      <v-col cols="12" md="2" class="pl-3">
                        <a-form-item>
                          <a-input v-decorator="[ 'high', { initialValue: high, rules: Rule.high }]" @change="ChangeInput('size')" placeholder="สูง" suffix="ซม." />
                        </a-form-item>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>

                <!-- -----------------------------------------รูปภาพ ------------------------------------------->
                <v-col cols="12" class="mt-5">
                  <v-card outlined>
                    <v-row no-gutters align="center">
                      <v-col cols="11" class="mt-5 ml-10" id="step-3">
                        <h2>รูปภาพสินค้า</h2>
                      </v-col>
                      <v-col cols="12" class="mb-5"><v-divider></v-divider></v-col>

                      <v-col cols="12" md="12" class="pl-3 mb-5" @click="onPickFile()">
                        <v-row no-gutters align="center" justify="center">
                          <v-file-input
                            v-model="DataImage"
                            :items="DataImage"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImage()"
                            id="file_input"
                            multiple
                            :clearable="false"
                            style="display:none"></v-file-input>
                            <v-img max-width="50" src="@/assets/upload.png" class="mr-3"></v-img>
                            <span>เลือกรูปภาพ</span>
                        </v-row>
                      </v-col>
                      <v-col cols="12" md="12" v-if="product_image.length !== 0 && statusPage === true" class="mb-5" >
                        <draggable v-model="product_image"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                          <v-col v-for="(item, index) in product_image" :key="index" cols="3" md="3">
                            <v-card dense light class="pa-1">
                              <v-card-actions>
                                <v-spacer></v-spacer>
                                <v-icon small light @click="RemoveImage(index, item)">mdi-close</v-icon>
                              </v-card-actions>
                              <!-- <img width="100%"  :src="item.url" v-if="MethodProduct === 'Create'" />
                              <img width="100%" :src="`${PathImage}${item.url}`" v-else /> -->
                              <v-img :src="item.url"  aspect-ratio="1.8" contain v-if="MethodProduct === 'Create'"></v-img>
                              <v-img :src="`${item.url}`" aspect-ratio="1.8" contain v-else></v-img>
                              <v-card-text class="text-md-center">
                                <!-- <span class="subheading">{{item.name|truncate(20, '...') }}</span> -->
                              </v-card-text>
                            </v-card>
                          </v-col>
                        </draggable>
                      </v-col>
                    </v-row>
                  </v-card>
                </v-col>
              </v-row>
            <v-row no-gutters justify="center" align="center" class="mt-5 mb-5">
              <a-button @click="Cancle">ยกเลิก</a-button>
              <a-button type="primary" html-type="submit" class="login-form-button ml-5">ยืนยัน</a-button>
            </v-row>
          </a-form>
        </v-col>
        <v-col cols="3" style="position:fixed;right:-30px;padding-right:70px">
          <v-row no-getters>
            <v-col cols="12" v-if="statustour">
              <a-card  bordered>
                <v-row no-gutters>
                  <v-col cols="12" class="pb-1">เคล็ดลับ</v-col>
                  <v-col cols="12" class="pb-1">{{title}}</v-col>
                  <v-col cols="12" class="pl-2" v-for="item in content" :key="item">* {{item}}</v-col>
                </v-row>
              </a-card>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
  </div>
</template>
<script>
import { CoolSelect } from 'vue-cool-select'
import draggable from 'vuedraggable'
import dataMessage from './datatour.json'
export default {
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    draggable,
    CoolSelect
  },
  data () {
    return {
      PathImage: process.env.VUE_APP_IMAGE,
      Rule: {
        product_name: [{ required: true, message: 'กรุณากรอกชื่อ' }],
        product_sku: [{ required: true, message: 'กรุณากรอกรหัสสินค้า' }],
        stock: [{ required: true, message: 'กรุณากรอกจำนวนสินค้าในคลัง' }],
        price: [{ required: true, message: 'กรุณากรอกราคาสินค้า' }],
        weight: [{ required: true, message: 'กรุณากรอกน้ำหนัก' }],
        wide: [{ required: true, message: 'ความกว้าง' }],
        long: [{ required: true, message: 'ความยาว' }],
        high: [{ required: true, message: 'ความสูง' }],
        brand: [{ required: true, message: 'กรุณาเลือกเเบรน' }],
        category: [{ required: true, message: 'กรุณาเลือกหมวดหมู่สินค้า' }],
        supplier: [{ required: true, message: 'กรุณาเลือกตัวเเทนจำหน่าย' }]

      },
      statusPage: false,
      SelectSupplier: null,
      SelectCategory: null,
      SelectBrand: null,
      ListCategory: [],
      ListBrand: [],
      ListSupplier: [],
      AddSupplier: '',
      AddBrand: '',
      statustour: true,
      title: 'คำเเนะนำการใช้งาน',
      content: ['ทดสอบการเเนะนำ'],
      product_description: '',
      DataImage: [],
      product_image: [],
      product_name: '',
      product_sku: '',
      stock: '',
      price: '',
      weight: '',
      wide: '',
      long: '',
      high: '',
      MethodProduct: this.$router.currentRoute.query.Status,
      Remove_img: []
    }
  },
  beforeCreate () {
    this.form = this.$form.createForm(this, { name: 'normal_login' })
  },
  async created () {
    await this.$store.dispatch('GetCagegory')
    this.ListCategory = await this.$store.state.ModuleManageShop.Category
    await this.$store.dispatch('GetSupplier')
    this.ListSupplier = await this.$store.state.ModuleManageShop.Supplier
    await this.$store.dispatch('GetBrand')
    this.ListBrand = await this.$store.state.ModuleManageShop.Brand
    if (this.MethodProduct === 'Edit') {
      var CheckDataEdit = this.$store.state.ModuleManageShop.EditProduct
      // console.log(CheckDataEdit)
      if (CheckDataEdit !== '') {
        this.SetDataEdit()
      } else {
        this.$router.push({ path: '/seller' })
      }
    } else {
      this.statusPage = true
    }
  },
  methods: {
    ChangeInput (val) {
      if (val === 'product_name') {
        this.title = dataMessage.data[0].title
        this.content = dataMessage.data[0].content
      } else if (val === 'product_sku') {
        this.title = dataMessage.data[1].title
        this.content = dataMessage.data[1].content
      } else if (val === 'stock') {
        this.title = dataMessage.data[4].title
        this.content = dataMessage.data[4].content
      } else if (val === 'price') {
        this.title = dataMessage.data[3].title
        this.content = dataMessage.data[3].content
      } else if (val === 'weight') {
        this.title = dataMessage.data[5].title
        this.content = dataMessage.data[5].content
      } else if (val === 'product_description') {
        this.title = dataMessage.data[2].title
        this.content = dataMessage.data[2].content
      } else if (val === 'size') {
        this.title = dataMessage.data[6].title
        this.content = dataMessage.data[6].content
      }
    },
    async AddNewBrand () {
      var data = {
        manufacturer_name: this.AddBrand
      }
      await this.$store.dispatch('CreateBrand', data)
      await this.$store.dispatch('GetBrand')
      this.ListBrand = this.$store.state.ModuleManageShop.Brand
      this.AddBrand = ''
    },
    async AddNewSupplier () {
      var data = {
        supplier_name: this.AddSupplier
      }
      await this.$store.dispatch('CreateSupplier', data)
      await this.$store.dispatch('GetSupplier')
      this.ListSupplier = this.$store.state.ModuleManageShop.Supplier
      this.AddSupplier = ''
    },
    SetDataEdit () {
      var DataEdit = this.$store.state.ModuleManageShop.EditProduct
      this.product_name = DataEdit.product_name
      this.product_sku = DataEdit.product_sku
      this.stock = DataEdit.product_stock
      this.price = DataEdit.product_price
      this.weight = DataEdit.product_weight
      this.wide = JSON.parse(DataEdit.product_size)[0]
      this.long = JSON.parse(DataEdit.product_size)[1]
      this.high = JSON.parse(DataEdit.product_size)[2]
      this.product_image = DataEdit.product_image
      this.SelectBrand = DataEdit.manufacturer_name
      this.SelectSupplier = DataEdit.supplier_name
      this.product_description = DataEdit.product_description
      this.SelectCategory = DataEdit.product_category
      this.statusPage = true
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      for (let i = 0; i < this.DataImage.length; i++) {
        const element = this.DataImage[i]
        const reader = new FileReader()
        reader.readAsDataURL(element)
        reader.onload = () => {
          var resultReader = reader.result
          var url = URL.createObjectURL(element)
          this.product_image.push({
            image_data: resultReader,
            url: url,
            name: this.DataImage[i].name,
            id: '-1'
          })
        }
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage (index, item) {
      // console.log(index, item)
      if (item.id !== '-1') {
        this.Remove_img.push({
          id: item.id
        })
      }
      this.product_image.splice(index, 1)
    },
    Cancle () {
      this.$router.push({ path: '/seller' })
    },
    addItem () {
      this.MockListSupplier.push('New item 1')
    },
    handleSubmit (e) {
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          this.Confirm(values)
        }
      })
    },
    async Confirm (val) {
      if (this.MethodProduct === 'Edit') {
        this.EditProduct(val)
      } else {
        this.CreateProduct(val)
      }
    },
    async EditProduct (val) {
      var EditData = this.$store.state.ModuleManageShop.EditProduct
      var SetProductSize = []
      var DataImageEdit = []
      for (let index = 0; index < 3; index++) {
        if (index === 0) {
          SetProductSize.push(val.wide)
        } else if (index === 1) {
          SetProductSize.push(val.long)
        } else if (index === 2) {
          SetProductSize.push(val.high)
        }
      }
      this.product_image.forEach((item, index) => {
        if (item.id !== '-1') {
          DataImageEdit.push({
            id: item.id,
            index: index.toString(),
            image: item.url
          })
        } else {
          DataImageEdit.push({
            id: item.id,
            index: index.toString(),
            image: item.image_data
          })
        }
      })
      var data = {
        seller_shop_id: this.$router.currentRoute.query.ShopID,
        product_id: EditData.product_id,
        product_sku: val.product_sku,
        product_name: val.product_name,
        product_price: val.price,
        product_stock: val.stock,
        product_status: '1',
        product_description: this.product_description,
        product_short_description: '',
        product_shipping_rate: '0',
        product_weight: val.weight,
        product_size: SetProductSize,
        product_category: this.SelectCategory,
        manufacturer_name: this.SelectBrand,
        supplier_name: this.SelectSupplier,
        remove_img: this.Remove_img,
        new_index: DataImageEdit
      }
      // console.log(data)
      var res = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/edit_product`, data)
      if (res.data.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: res.data.message, showConfirmButton: false, timer: 1500 })
        this.$router.push({ path: '/seller' })
      } else {
        this.$swal.fire({ icon: 'warning', title: res.data.message, showConfirmButton: false, timer: 1500 })
      }
    },
    async CreateProduct (val) {
      var SetProductSize = []
      for (let index = 0; index < 3; index++) {
        if (index === 0) {
          SetProductSize.push(val.wide)
        } else if (index === 1) {
          SetProductSize.push(val.long)
        } else if (index === 2) {
          SetProductSize.push(val.high)
        }
      }
      var data = {
        seller_shop_id: this.$router.currentRoute.query.ShopID,
        product_status: '1',
        product_sku: val.product_sku,
        product_name: val.product_name,
        product_price: val.price,
        product_stock: val.stock,
        product_description: this.product_description,
        product_short_description: '',
        product_shipping_rate: 0,
        product_size: JSON.stringify(SetProductSize),
        product_weight: val.weight,
        product_category: this.SelectCategory,
        manufacturer_name: this.SelectBrand,
        supplier_name: this.SelectSupplier,
        product_image: this.product_image
      }
      var res = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/add_product`, data)
      if (res.data.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', title: res.data.message, showConfirmButton: false, timer: 1500 })
        this.$router.push({ path: '/seller' })
      } else {
        this.$swal.fire({ icon: 'warning', title: res.data.message, showConfirmButton: false, timer: 1500 })
      }
    }
  }
}
</script>

<style scoped>
.f-right {
  float: right;
}
.div_overflow {
  overflow: auto;
  width:100%;
  height:89vh
}
::-webkit-scrollbar {
    width: 0px;
    background: transparent;
}
</style>
