<template>
  <div class="pb-6">
    <v-container v-if="listLiving.length > 0" class="d-flex justify-center" :style="IpadSize || MobileSize || IpadProSize ? 'width: 100%;' : 'width: 1223px !important'">
      <v-col cols="12" class="p-0">
        <v-row>
          <v-col cols="12" md="12" style="position: relative" class="p-0 backIMG">
            <div class="mr-4 ChipHeadCoupons" style="pointer-events: none; position: absolute; font-size: 23px; color: #27AB9C; z-index: 3;">
              <b style="">Live Stream</b>
            </div>
            <v-row justify="end" no-gutters ><span @click="seeMore()" style="font-size: 16px; color: #27AB9C;" class="couponAll" >ดูทั้งหมด</span></v-row>
            <v-row dense no-gutters>
              <v-col>
                <vue-horizontal-list :class="MobileSize || IpadSize ? 'mx-4 py-0' : 'mx-10'"  :items='listLiving' :options='optionsItems' :key="listLiving.length">
                  <template v-slot:nav-prev>
                    <div><v-icon color="#008E00" size="32">mdi-chevron-left</v-icon></div>
                  </template>
                  <template v-slot:nav-next>
                    <div><v-icon color="#008E00" size="32">mdi-chevron-right</v-icon></div>
                  </template>
                  <template v-slot:default="{ item }">
                    <v-col style="display: flex;" :class="MobileSize ? 'py-0 pr-1' : 'py-0'">
                      <div @click="gotoLive(item)" style="cursor: pointer;">
                        <TwoLayerCircle
                        :name="item.shop_name"
                        :imgURL="item.logoShopPath"
                        :size="80"
                        :value="100"
                        color="#27AB9C"
                        :live="true"
                        />
                      </div>
                      <!-- <component v-if="isDataReady" :is="MobileSize || IpadSize ? 'CardProductsFlashSaleMobile' : 'CardProductsFlashSale'" :itemProduct="item"/> -->
                    </v-col>
                  </template>
                </vue-horizontal-list>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-col>
    </v-container >
  </div>
</template>

<script>
import VueHorizontalList from 'vue-horizontal-list'
import { Decode } from '@/services'
import TwoLayerCircle from './component/TwoLayerCircle.vue'
import { io } from 'socket.io-client'
// const socket = io('http://**********:3000')
export default {
  components: {
    VueHorizontalList,
    TwoLayerCircle
  },
  data () {
    return {
      settings: {
        infinite: false,
        slidesToShow: 1.2,
        speed: 900,
        rows: 1,
        slidesPerRow: 1,
        slidesToScroll: 1
      },
      productSearch: [],
      listLiving: [],
      optionsItems: {
        responsive: [
          { end: 300, size: 4 },
          { end: 576, size: 4 },
          { start: 576, end: 768, size: 6 },
          { start: 768, end: 1023, size: 6 },
          { start: 1024, end: 1200, size: 9 },
          { start: 1200, size: 9 }
        ],
        list: {
          windowed: 1300,
          padding: 0
        },
        item: {
          class: '',
          padding: 5
        }
      },
      // socket: io('http://**********:3000')
      // socket: io('http://192.168.73.74:3000')
      // socket: io('https://devinet-eprocurement.one.th/api/backend_2')
      socket: ''
    }
  },
  watch: {
    '$socket.disconnected' (newVal) {
      // console.log('WebSocket disconnected?', newVal)
    }
  },
  mounted () {
    // socket.on('update_live', (data) => {
    //   console.log('📩 update_live:', data)
    //   this.listLiving = data
    // })
    // console.log('ทำงาน')
    // console.log(this.socket)
    this.socket = io(process.env.VUE_APP_LIVE_STREAM.substring(0, 35), {
      path: '/api/live/socket.io'
    })
    this.socket.on('update_live', (data) => {
      // console.log(this.socket)
      console.log('📡 Live shops updated:', data)
      // alert('มาแล้ว', data)
      this.listLiving = data
      // this.getAllLive()
    })
    // this.socket.on("disconnect", () => {
    //   console.log("❌ Socket disconnected");
    //   this.isConnected = false;
    // })
    // this.requestLiveRooms()
  },
  unmounted () {
    // console.log(5555)
    if (this.socket) {
      this.socket.off('update_live')
      this.socket.disconnect()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  async beforeDestroy () {
    // this.socket.off('update_live')
    if (this.socket) {
      this.socket.off('update_live')
      this.socket.disconnect()
    }
  },
  async created () {
    this.getAllLive()
    this.getResultShop()
    this.requestLiveRooms()
  },
  beforeRouteLeave (to, from, next) {
    if (this.socket) {
      this.socket.off('update_live')
      this.socket.disconnect()
      next()
    }
  },
  methods: {
    async getResultShop () {
      this.$store.commit('openLoader')
      var dataAllShop
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId = ''
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        }
        dataAllShop = {
          role_user: dataRole.role,
          company_id: companyId !== '' ? companyId.company.company_id : '-1',
          page: this.pageNumber,
          offset: '48'
        }
      } else {
        dataAllShop = {
          role_user: 'ext_buyer',
          company_id: '-1',
          page: this.pageNumber,
          offset: '48'
        }
      }
      await this.$store.dispatch('actionListAllShop', dataAllShop)
      var response = await this.$store.state.ModuleShop.stateListAllShop
      if (response.code === 200) {
        this.$store.commit('closeLoader')
        this.overlay2 = false
        this.productSearch = response.data.list_shop
        this.productSearch.forEach(element => {
          if (element.shop_id !== undefined) {
            this.pathShopDetail = this.path + 'shoppage/' + encodeURIComponent(element.shop_name.replace(/\s/g, '-') + '-' + element.shop_id)
            element.path = this.pathShopDetail
          }
        })
        this.pageMax = parseInt(response.data.total_page)
        this.shopCount = response.data.total_shop
        this.showSkeletonLoader = true
        window.scrollTo(0, 0)
      } else {
        this.$store.commit('closeLoader')
        this.overlay2 = false
        this.productSearch = []
        this.NoDataInSearch = true
        this.showSkeletonLoader = true
      }
    },
    async getAllLive () {
      var data = null
      const response = await fetch(`${process.env.VUE_APP_BACK_END2}getAllRoom`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      })
      const res = await response.json()
      if (res.message === 'get list rooms success') {
        data = res.data
        this.listLiving = data
        // await this.getRoom(this.listLiving[0])
        // this.colLeft = this.listLiving.length % (12 / this.mdCol)
        // console.log('this.colLeft', this.colLeft)
      }
    },
    seeMore () {
      this.$router.push({ path: '/live' }).catch(() => {})
    },
    gotoLive (val) {
      this.$router.push({ path: `/live?name=${val.name}` }).catch(() => {})
    },
    requestLiveRooms () {
      // this.socket.emit('new_live')
    }
  }
}
</script>

<style scoped>
.couponAll {
  cursor: pointer;
}
.couponAll:hover {
  transform: scale(1.02) !important;
}
.progress-gradient {
width: 100%;
height: 100%;
border-radius: 48px;
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.vProgressLinearDesk {
  max-width: 4.5vw;
}
.vProgressLinearIped {
  max-width: 8vw;
}
.vProgressLinearMobile {
  max-width: 23vw;
}

.backIMG{
  /* background-image: url('../../../assets/ConponNGC/shopConpon/background.png'); */
  /* background-color: #FB8700; */
  border-radius: 10px;
  height: 100%;
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMG{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: cover;
  padding: 1%;
  /* width: 100%; */
}
.couponIMGMobile{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  /* padding: 1%; */
  /* width: 100%; */
}
.couponIMGDesk{
  background-image: url('../../../assets/ConponNGC/shopConpon/NewCoupon.png');
  /* object-fit: contain; */
  background-size: contain;
  padding: 1%;
  /* width: 100%; */
}

.live-circle {
  position: relative;
  display: inline-block;
  text-align: center; /* จัดข้อความให้อยู่ตรงกลาง */
}
.live-text {
  position: absolute; /* กำหนดตำแหน่งแบบ absolute */
  bottom: 0; /* วางไว้ด้านล่าง */
  left: 50%; /* จัดให้อยู่ตรงกลางแนวนอน */
  transform: translateX(-50%); /* ชดเชยการจัดตรงกลาง */
  font-weight: bold; /* ทำให้ข้อความหนา */
  color: red; /* เปลี่ยนสีข้อความเป็นสีแดง */
  background-color: white; /* เพิ่มพื้นหลังสีขาว */
  padding: 2px 5px; /* เพิ่ม padding เล็กน้อย */
  border-radius: 5px; /* ทำให้ขอบมน */
}
.inner-image {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: calc(100% - 10px); /* Adjust size as needed */
  height: calc(100% - 10px); /* Adjust size as needed */
  border-radius: 50%; /* Make the image circular */
  object-fit: cover; /* Prevent image distortion */
}
.live-shop-name {
  display: block; /* แสดงข้อความอยู่ใต้ block */
  margin-top: 10px; /* ปรับระยะห่างจากวงกลม */
  font-weight: bold; /* ทำให้ข้อความหนา */
  color: white; /* เปลี่ยนสีข้อความเป็นสีแดง */
}
</style>
