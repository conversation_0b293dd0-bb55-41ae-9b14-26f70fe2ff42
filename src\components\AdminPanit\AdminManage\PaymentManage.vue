<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col :cols="MobileSize ? 8 : 6">
          <v-card-title style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการรายการโอนเงิน</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2 d-flex" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการรายการโอนเงิน</v-card-title>
        </v-col>
      </v-row>
      <v-row class="mx-1" v-if="!MobileSize">
        <v-col :cols="IpadSize ? 6 : 3">
          <v-dialog
            ref="dialogStartDate"
            v-model="dialogStartDate"
            width="290px"
          >
            <template v-slot:activator="{ on, attrs }">
              <!-- <v-select
                v-model="sentStartDate"
                v-bind="attrs"
                placeholder="วันที่"
                outlined
                dense
                v-on="on"
                class="customSelect mr-1"
                hide-details
                rounded
              >
              </v-select> -->
              <v-text-field
                v-model="sentStartDate"
                v-bind="attrs"
                v-on="on"
                hide-details
                outlined
                dense
                rounded
                placeholder="วันที่"
                class="customSelect mr-1"
                readonly
              >
              </v-text-field>
            </template>
            <v-card>
              <v-card-title>
                <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันที่</span>
                <v-spacer></v-spacer>
                <v-btn text @click="cancelSelectDate" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
              </v-card-title>
              <v-date-picker
                v-model="date1"
                range
                locale="TH-th"
                no-title
                :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
              >
                <v-btn
                  text
                  color="primary"
                  @click="cancelPickDate"
                >
                  ยกเลิก
                </v-btn>
                <v-spacer></v-spacer>
                <v-btn
                  text
                  color="primary"
                  @click="clearSelectDate"
                >
                  ล้างค่า
                </v-btn>
                <v-btn
                  text
                  color="primary"
                  @click="changeformatdate()"
                  :disabled="date1.length === 0"
                >
                  ตกลง
                </v-btn>
              </v-date-picker>
            </v-card>
          </v-dialog>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="typePayment"
            :items="itemsTypePayment"
            placeholder="ประเภทการชำระเงิน"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="transferGP"
            :items="itemsTransfer"
            placeholder="สถานะโอน GP"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="transferAffiliate"
            :items="itemsTransfer"
            placeholder="สถานะโอน Affiliate"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="transferShipping"
            :items="itemsTransfer"
            placeholder="สถานะโอนค่าขนส่ง"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="transferShop"
            :items="itemsTransfer"
            placeholder="สถานะโอนให้ร้านค้า"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="isTransfer"
            :items="itemsIsTransfer"
            placeholder="วิธีการโอนเงิน"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="customerRefund"
            :items="itemsTransfer"
            placeholder="สถานะคืนเงินลูกค้า"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 6 : 3">
          <v-select
            v-model="sendEail"
            :items="itemsSendEail"
            placeholder="สถานะการส่งอีเมล"
            outlined
            dense
            class="customSelect mr-1"
            hide-details
            item-text="label"
            item-value="value"
            rounded
            @change="listPaymentTransfer()"
          >
          </v-select>
        </v-col>
        <v-col :cols="IpadSize ? 12 : IpadProSize ? 6 : 4" class="d-flex">
          <v-text-field class="mr-1" rounded v-model="search" placeholder="ค้นหาเลขสั่งซื้อ" outlined dense hide-details @change="listPaymentTransfer()">
            <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
          </v-text-field>
          <v-btn rounded color="#2faea0" style="color: #fff;" @click="clsValidate">ล้างการค้นหา</v-btn>
        </v-col>
      </v-row>
      <v-row v-else>
        <v-col cols="12" style="display: grid;">
          <v-btn color="primary" rounded outlined @click="filterMobileDialog = true">ตัวกรอง</v-btn>
          <v-dialog
            v-model="filterMobileDialog"
          >
            <v-card>
              <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
                <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
                  ตัวกรอง
                </span>
                <v-btn icon dark @click="filterMobileDialog = false">
                  <v-icon color="#27AB9C">mdi-close</v-icon>
                </v-btn>
              </v-toolbar>
              <v-card-text>
                <v-col cols="12">
                  <v-dialog
                    ref="dialogStartDate"
                    v-model="dialogStartDate"
                    width="290px"
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <!-- <v-select
                        v-model="sentStartDate"
                        v-bind="attrs"
                        placeholder="วันที่"
                        outlined
                        dense
                        v-on="on"
                        class="customSelect mr-1"
                        hide-details
                        rounded
                      >
                      </v-select> -->
                      <v-text-field
                        v-model="sentStartDate"
                        v-bind="attrs"
                        v-on="on"
                        hide-details
                        outlined
                        dense
                        placeholder="วันที่"
                        class="customSelect mr-1"
                        readonly
                      >
                      </v-text-field>
                    </template>
                    <v-card>
                      <v-card-title>
                        <span style="font-weight: 600; color: #2faea0; font-size: medium;">วันที่</span>
                        <v-spacer></v-spacer>
                        <v-btn text @click="cancelSelectDate" icon small style="margin-right: -5px;"><v-icon small>mdi-close</v-icon></v-btn>
                      </v-card-title>
                      <v-date-picker
                        v-model="date1"
                        range
                        locale="TH-th"
                        no-title
                        :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                      >
                        <v-btn
                          text
                          color="primary"
                          @click="cancelPickDate"
                        >
                          ยกเลิก
                        </v-btn>
                        <v-spacer></v-spacer>
                        <v-btn
                          text
                          color="primary"
                          @click="clearSelectDate"
                        >
                          ล้างค่า
                        </v-btn>
                        <v-btn
                          text
                          color="primary"
                          @click="changeformatdate()"
                          :disabled="date1.length === 0"
                        >
                          ตกลง
                        </v-btn>
                      </v-date-picker>
                    </v-card>
                  </v-dialog>
                </v-col>
                <v-col cols="12">
                  <v-text-field class="mr-1" v-model="search" placeholder="ค้นหาเลขสั่งซื้อ" outlined dense hide-details>
                    <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
                  </v-text-field>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="typePayment"
                    :items="itemsTypePayment"
                    placeholder="ประเภทการชำระเงิน"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="transferGP"
                    :items="itemsTransfer"
                    placeholder="สถานะโอน GP"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="transferAffiliate"
                    :items="itemsTransfer"
                    placeholder="สถานะโอน Affiliate"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="transferShipping"
                    :items="itemsTransfer"
                    placeholder="สถานะโอนค่าขนส่ง"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="transferShop"
                    :items="itemsTransfer"
                    placeholder="สถานะโอนให้ร้านค้า"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="isTransfer"
                    :items="itemsIsTransfer"
                    placeholder="วิธีการโอนเงิน"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="customerRefund"
                    :items="itemsTransfer"
                    placeholder="สถานะคืนเงินลูกค้า"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
                <v-col cols="12">
                  <v-select
                    v-model="sendEail"
                    :items="itemsSendEail"
                    placeholder="สถานะการส่งอีเมล"
                    outlined
                    dense
                    class="customSelect mr-1"
                    hide-details
                    item-text="label"
                    item-value="value"
                  >
                  </v-select>
                </v-col>
              </v-card-text>
              <v-card-actions class="d-flex">
                <v-btn color="#2faea0" outlined @click="clsValidate">ล้างค่า</v-btn>
                <v-spacer></v-spacer>
                <v-btn color="#2faea0" class="white--text" @click="listPaymentTransfer()">ค้นหา</v-btn>
              </v-card-actions>
            </v-card>
          </v-dialog>
        </v-col>
      </v-row>
      <v-row class="mx-1">
        <v-col cols="12">
            <!-- <v-data-table
            :headers="headers"
            :items="listPayment"
            :search="search"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            :page.sync="page"
            no-results-text="ไม่พบรายการโอนเงิน"
            no-data-text="ไม่พบรายการโอนเงิน"
            :server-items-length="maxPages"
            :update:items-per-page="itemsPerPage"
            class="elevation-1 mt-4 shop-table"
            :footer-props="{ 'items-per-page-options': [5, 10, 15, 50, 100], 'items-per-page-text': 'จำนวนแถว' }"
            :options.sync="optionShop"
            :items-per-page="optionShop.itemsPerPage"
            @update:options="updateOptions"
            item-key="shopId"
            :hide-default-footer="searchShop !== ''"
            >
            </v-data-table> -->
            <v-data-table
            :headers="headers"
            :items="listPayment"
            :search="search"
            style="width:100%; text-align: center; white-space: nowrap;"
            height="100%"
            :page.sync="page"
            no-results-text="ไม่พบรายการโอนเงิน"
            no-data-text="ไม่พบรายการโอนเงิน"
            :server-items-length="maxPages"
            :update:items-per-page="itemsPerPage"
            class="elevation-1 mt-4 shop-table"
            :footer-props="{ 'items-per-page-options': [5, 10, 15, 50, 100], 'items-per-page-text': 'จำนวนแถว' }"
            :options.sync="options"
            :items-per-page="options.itemsPerPage"
            @update:options="updateOptions"
            item-key="shopId"
            >
            <!-- @update:options="updateOptions" -->
            <!-- <template v-slot:selection="data">
                <v-chip
                  :key="data.item.value"
                  :style="{ backgroundColor: colorBg(data.item.value), color: colorText(data.item.value) }"
                >
                    <span style="font-weight: bold;" >{{ data.item.text }}</span>
                </v-chip>
              </template> -->
            <template v-slot:[`item.transfer_gp`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.transfer_gp), color: colorText(item.transfer_gp) }"
                >
                    <span>{{ item.transfer_gp }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.transfer_shipping`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.transfer_shipping), color: colorText(item.transfer_shipping) }"
                >
                    <span>{{ item.transfer_shipping }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.transfer_shop`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.transfer_shop), color: colorText(item.transfer_shop) }"
                >
                    <span>{{ item.transfer_shop }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.customer_refund`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.customer_refund), color: colorText(item.customer_refund) }"
                >
                    <span>{{ item.customer_refund }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.send_email`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.send_email), color: colorText(item.send_email) }"
                >
                    <span>{{ item.send_email }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.transfer_affiliate`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.transfer_affiliate), color: colorText(item.transfer_affiliate) }"
                >
                    <span>{{ item.transfer_affiliate }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.is_transfer`]="{ item }">
              <v-chip
                  :style="{ backgroundColor: colorBg(item.is_transfer), color: colorText(item.is_transfer) }"
                >
                    <span>{{ item.is_transfer }}</span>
                </v-chip>
            </template>
            <template v-slot:[`item.remark_gp`]="{ item }">
              <v-btn v-if="item.remark_gp !== ''" outlined rounded color="#27AB9C" @click="openDialogDetailRemark(item.remark_gp)">รายละเอียด</v-btn>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.remark_affiliate`]="{ item }">
              <v-btn v-if="item.remark_affiliate !== ''" outlined rounded color="#27AB9C" @click="openDialogDetailRemark(item.remark_affiliate)">รายละเอียด</v-btn>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.remark_shipping`]="{ item }">
              <v-btn v-if="item.remark_shipping !== ''" outlined rounded color="#27AB9C" @click="openDialogDetailRemark(item.remark_shipping)">รายละเอียด</v-btn>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.remark_shop`]="{ item }">
              <v-btn v-if="item.remark_shop !== ''" outlined rounded color="#27AB9C" @click="openDialogDetailRemark(item.remark_shop)">รายละเอียด</v-btn>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.remark_refund`]="{ item }">
              <v-btn v-if="item.remark_refund !== ''" outlined rounded color="#27AB9C" @click="openDialogDetailRemark(item.remark_refund)">รายละเอียด</v-btn>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.customer_refund_date`]="{ item }">
              <span v-if="item.customer_refund_date !== ''" style="white-space: nowrap;" :style="MobileSize ? 'font-size: smaller' : ''">{{ formatThaiDateTime(item.customer_refund_date) }}</span>
              <span v-else>-</span>
            </template>
            <template v-slot:[`item.action`]="{ item }">
              <v-btn plain><v-icon color="#27AB9C" @click="openDialogUpdate(item)">mdi-dots-vertical</v-icon></v-btn>
            </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="dialogUpdatePayment" width="600px" content-class="elevation-0">
      <v-card style="border-radius: 22px;" class="pb-2">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: x-large; font-weight: 700; color: #FFFFFF;">แก้ไขข้อมูลการโอนเงิน
          </span>
          <v-btn icon dark @click="dialogUpdatePayment = false">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="pt-2">
          <v-row>
            <v-col>
              <span>หมายเลขคำสั่งซื้อ: <b>{{ orderNumberUpdate }}</b></span>
            </v-col>
            <!-- <v-col cols="12">
              <span>ประเภทการชำระเงิน</span>
              <v-select
                v-model="typePaymentUpdate"
                :items="itemsTypePayment"
                placeholder="ประเภทการชำระเงิน"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col> -->
            <v-col cols="12">
              <span>สถานะการโอน GP</span>
              <v-select
                v-model="transferGPUpdate"
                :items="itemsTransfer"
                placeholder="สถานะการโอน GP"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
            <v-col cols="12">
              <span>หมายเหตุการโอน GP</span>
              <v-textarea
                v-model="remarkGP"
                outlined
                placeholder="ระบุหมายเหตุการโอน GP"
                :style="MobileSize ? 'height: 25vw;' : ''"
                :validate-on-blur="true"
              ></v-textarea>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: 50px;' : 'margin-top: -35px;'">
              <span>สถานะการโอน Affiliate</span>
              <v-select
                v-model="transferAffiliateUpdate"
                :items="itemsTransfer"
                placeholder="สถานะการโอน Affiliate"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
            <v-col cols="12">
              <span>หมายเหตุ Affiliate</span>
              <v-textarea
                v-model="remarkAffiliate"
                outlined
                placeholder="ระบุหมายเหตุ Affiliate"
                :style="MobileSize ? 'height: 25vw;' : ''"
                :validate-on-blur="true"
              ></v-textarea>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: 50px;' : 'margin-top: -35px;'">
              <span>สถานะการโอนค่าขนส่ง</span>
              <v-select
                v-model="transferShippingUpdate"
                :items="itemsTransfer"
                placeholder="สถานะการโอนค่าขนส่ง"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
            <v-col cols="12">
              <span>หมายเหตุโอนค่าขนส่ง</span>
              <v-textarea
                v-model="remarkShipping"
                outlined
                placeholder="ระบุหมายเหตุโอนค่าขนส่ง"
                :style="MobileSize ? 'height: 25vw;' : ''"
                :validate-on-blur="true"
              ></v-textarea>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: 50px;' : 'margin-top: -35px;'">
              <span>สถานะโอนให้ร้านค้า</span>
              <v-select
                v-model="transferShopUpdate"
                :items="itemsTransfer"
                placeholder="สถานะโอนให้ร้านค้า"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
            <v-col cols="12">
              <span>หมายเหตุโอนให้ร้านค้า</span>
              <v-textarea
                v-model="remarkShop"
                outlined
                placeholder="ระบุหมายเหตุโอนให้ร้านค้า"
                :style="MobileSize ? 'height: 25vw;' : ''"
                :validate-on-blur="true"
              ></v-textarea>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: 50px;' : 'margin-top: -35px;'">
              <span>วิธีการโอนเงิน</span>
              <v-select
                v-model="isTransferUpdate"
                :items="itemsIsTransfer"
                placeholder="วิธีการโอนเงิน"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
            <v-col cols="12">
              <span>สถานะคืนเงินลูกค้า</span>
              <v-select
                v-model="customRefundUpdate"
                :items="itemsTransfer"
                placeholder="สถานะคืนเงินลูกค้า"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
            <v-col cols="12">
              <span>หมายเหตุคืนเงินให้ลูกค้า</span>
              <v-textarea
                v-model="remarkRefund"
                outlined
                placeholder="ระบุหมายเหตุคืนเงินให้ลูกค้า"
                :style="MobileSize ? 'height: 25vw;' : ''"
                :validate-on-blur="true"
              ></v-textarea>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: 50px;' : 'margin-top: -35px;'">
              <span>วันที่คืนเงินลูกค้า</span>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: -25px;' : 'margin-top: -25px;'">
              <v-dialog
                ref="dialogUpdateDate"
                v-model="dialogUpdateDate"
                :return-value.sync="date2"
                width="290px"
                persistent
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="sentDate2"
                    v-bind="attrs"
                    placeholder="วว/ดด/ปป"
                    outlined
                    readonly
                    dense
                    v-on="on"
                  >
                    <v-icon slot="append" color="rgb(39, 171, 156)">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date2"
                  scrollable
                  reactive
                  locale="TH-th"
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                <v-row dense>
                    <v-col cols="12" class="pt-0">
                      <v-col cols="12" class="pt-0">
                        <a-time-picker
                            v-model="timeUpdate"
                            :bordered="false"
                            style="width: 100%;"
                            format="HH:mm:ss น."
                            valueFormat="HH:mm:ss"
                            size="large"
                            placeholder="00.00.00 น."
                            :disabled="date2 === ''"
                            :placement="'topLeft'"
                            :popupStyle="!this.MobileSize ? { position: 'fixed' } : { position: 'absolute'}"
                          />
                      </v-col>
                    </v-col>
                    <v-col cols="12" align="end">
                      <v-btn text color="primary" @click="dialogUpdateDate = false, date2 === '' ? timeUpdate = '' : ''" > ยกเลิก </v-btn>
                      <v-btn text color="primary" @click="clsDateTime" > ล้างค่า </v-btn>
                      <v-btn text color="primary" :disabled="date2 == '' || timeUpdate == ''" @click="setValueDate(date2, 'date2'), $refs.dialogUpdateDate.save(date2)"> บันทึก</v-btn>
                    </v-col>
                  </v-row>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12" :style="MobileSize ? 'margin-top: -40px;' : 'margin-top: -35px;'">
              <span>สถานะการส่งอีเมล</span>
              <v-select
                v-model="sendEmailUpdate"
                :items="itemsSendEail"
                placeholder="สถานะการส่งอีเมล"
                outlined
                dense
                class="customSelect mr-1"
                hide-details
                item-text="label"
                item-value="value"
              >
              </v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-btn outlined rounded color="#27AB9C" @click="dialogUpdatePayment = false">ยกเลิก</v-btn>
          <v-spacer></v-spacer>
          <v-btn rounded color="#27AB9C" style="color: #fff;" @click="dialogUpdate = true">ตกลง</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogUpdate" width="424" persistent>
      <v-card style="background: #FFFFFF; border-radius: 24px;">
        <v-app-bar
          flat
          color="rgba(0, 0, 0, 0)"
        >
          <v-toolbar-title></v-toolbar-title>
          <v-spacer></v-spacer>
          <v-btn
            color="#CCCCCC"
            icon
            @click="dialogUpdate = false"
          >
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-app-bar>
        <v-container>
          <div class="d-flex justify-center">
            <v-avatar :size="MobileSize ? 90 : 250" tile><img style="width: 100%; height: 100%; object-fit: contain;" src="@/assets/Coorperation/modalConfirmEditDialog.png" alt=""></v-avatar>
          </div>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: large; line-height: 24px; color: #333333;" class="my-4"><b>แก้ไขรายการโอนเงิน</b></p>
            <span style="font-weight: 400; font-size: small; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายแก้ไขรายการโอนเงิน ใช่ หรือ ไม่</span>
          </v-card-text>
          <v-card-text>
            <v-row dense justify="center">
              <v-btn :width="MobileSize ? '120' : '156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogUpdate = false">ยกเลิก</v-btn>
              <v-btn :width="MobileSize ? '120' : '156'" height="38" class="white--text" rounded color="#27AB9C" @click="updatePaymentTransfer()">ตกลง</v-btn>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogDetailRemark" width="400px" content-class="elevation-0">
      <v-card style="border-radius: 22px;">
        <v-toolbar src="@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png" style="toolbar-shaped-border-radius: 48px; box-shadow: none;" align="center" color="#27AB9C" height="80">
          <span class="flex text-center ml-5" style="font-size: large; font-weight: 700; color: #FFFFFF;">แก้ไขข้อมูลการโอนเงิน
          </span>
          <v-btn icon dark @click="dialogDetailRemark = false">
            <v-icon color="#FFFFFF">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="d-flex mt-2">
          <span class="mr-1"><b>หมายเหตุ: </b></span>
          <span> {{ remark }}</span>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog persistent v-model="dialogReqOTP" width="400px" content-class="elevation-0">
      <v-card style="border-radius: 22px;" class="d-flex justify-center pa-4">
        <v-row>
          <v-col cols="12" class="d-flex justify-end">
            <v-btn icon dark @click="backToMainPage">
              <v-icon color="#a0a0a0">mdi-close</v-icon>
            </v-btn>
          </v-col>
          <v-col class="d-flex flex-column align-center" style="gap: 5px;">
            <img src="@/assets/sendOTP.png" alt="" width="200" height="200">
            <!-- <v-img
              src="@/assets/sendOTP.png"
              max-width="500"
              max-height="500"
              contain
            /> -->
            <span :style="MobileSize || IpadSize ? 'margin-top: 8px; font-size: medium;' : 'margin-top: 8px; font-size: medium;'"><b>ยืนยันสิทธิ์จัดการการโอนเงิน</b></span>
            <span style="font-size: 16px;">ระบบได้ส่งรหัส OTP ไปยัง Email แล้ว</span>
            <v-otp-input
              v-model="otp"
              :length="6"
              type="number"
              dense
              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
            ></v-otp-input>
            <span v-if="message !== ''" style="font-size: 16px;">{{ message }} <b>{{ email }}</b></span>
            <span style="font-size: 16px;">สามารถขอรหัส OTP อีกครั้งภายใน {{ resendCountdown }} <v-btn icon @click="RefreshOTP()" :disabled="disableRefreshOTP" small class="ml-2"><v-icon>mdi-refresh</v-icon></v-btn></span>
            <!-- <v-btn
              rounded
              color="#27AB9C"
              width="190"
              style="color: #fff;"
              @click="sendOTP()"
              :disabled="this.otp.length !== 6"
            >
              ยืนยัน OTP
            </v-btn> -->
          </v-col>
        </v-row>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { TimePicker } from 'ant-design-vue'
import dayjs from 'dayjs'
export default {
  components: {
    'a-time-picker': TimePicker
    // 'a-time-range-picker': TimePicker.RangePicker
  },
  data () {
    return {
      defaultDate: dayjs('00:00:00', 'HH:mm:ss'),
      headers: [
        { text: 'หมายเลขคำสั่งซื้อ', value: 'order_number', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทการชำระเงิน', value: 'type_payment', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะโอน GP', value: 'transfer_gp', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุการโอน GP', value: 'remark_gp', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะโอนค่าขนส่ง', value: 'transfer_shipping', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุโอนค่าขนส่ง', value: 'remark_shipping', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะโอนให้ร้านค้า', value: 'transfer_shop', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุโอนให้ร้านค้า', value: 'remark_shop', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วิธีการโอนเงิน', value: 'is_transfer', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะคืนเงินลูกค้า', value: 'customer_refund', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุคืนเงินลูกค้า', value: 'remark_refund', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'วันที่คืนเงินลูกค้า', value: 'customer_refund_date', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะการส่งอีเมล', value: 'send_email', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะโอน Affiliate', value: 'transfer_affiliate', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'หมายเหตุ Affiliate', value: 'remark_affiliate', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ค่า GP', value: 'total_gp', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ค่าขนส่ง', value: 'total_shipping', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'action', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      itemsTypePayment: [
        { label: 'QR Code Payment', value: 'QR Code Payment' },
        { label: 'Credit Card', value: 'Credit Card' }
      ],
      itemsTransfer: [
        { label: 'สำเร็จ', value: 'Success' },
        { label: 'รอการดำเนินการ', value: 'Waiting' },
        { label: 'ไม่สำเร็จ', value: 'Fail' },
        { label: 'ยังไม่ชำระเงิน', value: 'Not Paid' },
        { label: 'ยกเลิก', value: 'Cancel' }
      ],
      itemsIsTransfer: [
        { label: 'โอนอัตโนมัติ', value: 'Auto' },
        { label: 'โอนด้วยตนเอง', value: 'Manual' },
        { label: 'ไม่มีการโอน', value: 'None' }
      ],
      itemsSendEail: [
        { label: 'สำเร็จ', value: 'Success' },
        { label: 'รอการดำเนินการ', value: 'Waiting' },
        { label: 'ยังไม่ส่ง', value: 'Not Sent' }
      ],
      listPayment: [],
      maxPages: null,
      search: '',
      itemsPerPage: 10,
      page: 1,
      options: {
        page: 1,
        itemsPerPage: 10
      },
      searchShop: '',
      orderNumber: '',
      typePayment: '',
      transferGP: '',
      transferAffiliate: '',
      transferShipping: '',
      transferShop: '',
      isTransfer: '',
      customerRefund: '',
      sendEail: '',
      createdDate: '',
      dialogStartDate: false,
      sentStartDate: [],
      date1: [],
      date: new Date(Date.now()),
      dialogUpdatePayment: false,
      orderNumberUpdate: '',
      // typePaymentUpdate: '',
      transferGPUpdate: '',
      remarkGP: '',
      transferAffiliateUpdate: '',
      remarkAffiliate: '',
      transferShippingUpdate: '',
      remarkShipping: '',
      transferShopUpdate: '',
      remarkShop: '',
      isTransferUpdate: '',
      customRefundUpdate: '',
      sendEmailUpdate: '',
      remarkRefund: '',
      dialogUpdateDate: false,
      sentDate2: '',
      date2: '',
      timeUpdate: '',
      dialogUpdate: false,
      filterMobileDialog: false,
      dialogDetailRemark: false,
      remark: '',
      idUpdate: '',
      dialogReqOTP: false,
      otp: '',
      resendCountdown: '00:00',
      disableRefreshOTP: false,
      nameEmail: '',
      message: '',
      email: ''
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.otp = ''
    this.resendCountdown = '00:00'
    this.intervalId = setInterval(() => {
      this.listPaymentTransfer()
    }, 180000)
  },
  beforeDestroy () {
    clearInterval(this.intervalId)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    checkValue () {
      if (this.sentStartDate === '' && this.typePayment === '' && this.transferGP === '' && this.transferAffiliate === '' &&
        this.transferShipping === '' && this.transferShop === '' && this.isTransfer === '' && this.customerRefund === '' &&
          this.sendEail === '' && this.search === '') {
        return true
      }
      return false
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/paymentManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/paymentManage' }).catch(() => {})
      }
    },
    otp (val) {
      if (val && val.length === 6) {
        this.sendOTP()
      }
    }
  },
  created () {
    // this.listPaymentTransfer()
  },
  methods: {
    colorText (val) {
      if (val === 'รอการดำเนินการ' || val === 'ยังไม่ชำระเงิน' || val === 'ไม่มีการโอน') {
        return '#FAAD14'
      } else if (val === 'สำเร็จ' || val === 'โอนด้วยตนเอง') {
        return '#00B500'
      } else if (val === 'ไม่สำเร็จ' || val === 'ยกเลิก') {
        return '#F5222D'
      } else if (val === 'ยังไม่ส่ง' || val === 'โอนอัตโนมัติ') {
        return '#2A70C3'
      }
    },
    colorBg (val) {
      if (val === 'รอการดำเนินการ' || val === 'ยังไม่ชำระเงิน' || val === 'ไม่มีการโอน') {
        return '#fdf8ed'
      } else if (val === 'สำเร็จ' || val === 'โอนด้วยตนเอง') {
        return '#def9d1'
      } else if (val === 'ไม่สำเร็จ' || val === 'ยกเลิก') {
        return '#fdcbcd'
      } else if (val === 'ยังไม่ส่ง' || val === 'โอนอัตโนมัติ') {
        return '#DBECFA'
      }
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    clsDateTime () {
      this.date2 = ''
      this.timeUpdate = ''
      this.sentDate2 = ''
    },
    openDialogDetailRemark (text) {
      this.remark = text
      this.dialogDetailRemark = true
    },
    async updateOptions (options) {
      this.options = options
      await this.listPaymentTransfer()
    },
    async listPaymentTransfer () {
      try {
        this.$store.commit('openLoader')
        var data = {
          page: this.options.page,
          limit: this.options.itemsPerPage,
          order_number: this.search,
          type_payment: this.typePayment,
          transfer_gp: this.transferGP,
          transfer_affiliate: this.transferAffiliate,
          transfer_shipping: this.transferShipping,
          transfer_shop: this.transferShop,
          is_transfer: this.isTransfer,
          customer_refund: this.customerRefund,
          send_email: this.sendEail,
          created_date: this.sentStartDate
        }

        await this.$store.dispatch('actionsListPaymentTransfer', data)
        var res = this.$store.state.ModuleAdminManage.stateListPaymentTransfer

        if (res.code === 200) {
          this.filterMobileDialog = false
          this.listPayment = res.data
          var max = res.pagination
          this.maxPages = this.checkValue === true ? res.count : max.total * max.totalPage
          this.$store.commit('closeLoader')
          this.checkVerify()
        } else if (res.code === 401) {
          this.$store.commit('closeLoader')
          await this.$swal.fire({
            icon: 'error',
            html: 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true
          })
          if (this.MobileSize) {
            this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
          } else {
            this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
          }
        } else {
          await this.$swal.fire({
            icon: 'error',
            html: `${res.message}`,
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true
          })
          this.$store.commit('closeLoader')
        }
      } catch (err) {
        console.error('❌ listPaymentTransfer ERROR:', err)
        this.$store.commit('closeLoader')
      }
    },
    async checkVerify () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionCheckVerify')
      var res = await this.$store.state.ModuleAdminManage.stateCheckVerify
      if (res.code === 200) {
        this.$store.commit('closeLoader')
      } else if (res.code === 400) {
        this.$store.commit('closeLoader')
        this.reqOTP()
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      }
    },
    async reqOTP () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionRegOTP')
      var res = await this.$store.state.ModuleAdminManage.stateRegOTP
      if (res.code === 200) {
        this.otp = ''
        this.countdownCheck(60)
        var nameEmailRes = res.message
        const parts = nameEmailRes.split('อีเมล ')
        this.message = parts[0].trim() + 'อีเมล'
        this.email = parts[1].trim()
        this.dialogReqOTP = true
        this.listPayment = []
        this.$store.commit('closeLoader')
      } else if (res.code === 401 || res.message === 'ไม่พบ Email ในระบบ') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: res.message === 'ไม่พบ Email ในระบบ' ? 'ไม่พบ Email ในระบบ' : 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else if (res.message === 'คุณสามารถขอ OTP ได้ทุก ๆ 1 นาที') {
        this.$store.commit('closeLoader')
        // await this.$swal.fire({
        //   icon: 'error',
        //   html: `${res.message}`,
        //   showConfirmButton: false,
        //   timer: 3000,
        //   timerProgressBar: true
        // })
        this.countdownCheck(60)
        this.dialogReqOTP = true
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    async sendOTP () {
      this.$store.commit('openLoader')
      var data = {
        otp: this.otp
      }
      await this.$store.dispatch('actionSendOTP', data)
      var res = await this.$store.state.ModuleAdminManage.stateSendOTP
      if (res.code === 200) {
        this.dialogReqOTP = false
        this.$store.commit('closeLoader')
        this.listPaymentTransfer()
      } else if (res.code === 401) {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.otp = ''
        this.$store.commit('closeLoader')
      }
    },
    async RefreshOTP () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionRegOTP')
      var res = await this.$store.state.ModuleAdminManage.stateRegOTP
      if (res.code === 200) {
        this.countdownCheck(60)
        var nameEmailRes = res.message
        const parts = nameEmailRes.split('อีเมล ')
        this.message = parts[0].trim() + 'อีเมล'
        this.email = parts[1].trim()
        this.$store.commit('closeLoader')
      } else if (res.code === 401 || res.message === 'ไม่พบ Email ในระบบ') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'error',
          html: res.message === 'ไม่พบ Email ในระบบ' ? 'ไม่พบ Email ในระบบ' : 'ไม่มีสิทธิ์เข้าถึง กรุณาติดต่อเจ้าหน้าที่เพื่อเพิ่มสิทธิ์',
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true
        })
        if (this.MobileSize) {
          this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
        } else {
          this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
        }
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    countdownCheck (second) {
      this.counter = second
      const interval = setInterval(() => {
        var minutes = Math.floor(this.counter / 60)
        var seconds = this.counter % 60
        seconds = seconds < 10 ? `0${seconds}` : seconds
        this.resendCountdown = `${minutes}:${seconds}`
        this.counter--
        if (this.counter < 0) {
          this.disableRefreshOTP = false
          clearInterval(interval)
        } else {
          this.disableRefreshOTP = true
        }
      }, 1000)
    },
    backToMainPage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboardShopAdmin' }).catch(() => {})
      }
    },
    cancelSelectDate () {
      this.sentStartDate = []
      this.date = []
      this.date1 = []
      // this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.dialogStartDate = false
    },
    cancelPickDate () {
      this.date1 = []
      this.dialogStartDate = false
    },
    clearSelectDate () {
      this.sentStartDate = []
      this.date1 = []
      this.listPaymentTransfer()
      // this.date = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
    },
    changeformatdate () {
      if (this.date1.length > 1) {
        const [d1, d2] = this.date1
        const date1 = new Date(d1)
        const date2 = new Date(d2)
        const [start, end] = date1 > date2 ? [d2, d1] : [d1, d2]
        this.sentStartDate = [this.formatDateToThai(start), this.formatDateToThai(end)]
      } else {
        this.sentStartDate = [this.formatDateToThai(this.date1[0])]
      }
      if (!this.MobileSize) {
        this.listPaymentTransfer()
      }
      this.dialogStartDate = false
    },

    formatDateToThai (dateString) {
      const date = new Date(dateString)
      if (isNaN(date)) return 'Invalid Date' // ป้องกัน NaN

      const day = date.getDate().toString().padStart(2, '0')
      const month = (date.getMonth() + 1).toString().padStart(2, '0')
      const yearBE = date.getFullYear() + 543
      return `${day}/${month}/${yearBE}`
    },
    async clsValidate () {
      this.date = []
      this.date1 = []
      this.sentStartDate = []
      this.search = ''
      this.typePayment = ''
      this.transferGP = ''
      this.transferAffiliate = ''
      this.transferShipping = ''
      this.transferShop = ''
      this.isTransfer = ''
      this.customerRefund = ''
      this.sendEail = ''
      this.listPaymentTransfer()
    },
    openDialogUpdate (item) {
      const now = new Date()
      const pad = n => n.toString().padStart(2, '0')
      const currentTime = `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`
      this.idUpdate = item.id
      this.orderNumberUpdate = item.order_number
      // this.typePaymentUpdate = item.type_payment
      this.transferGPUpdate = item.transfer_gp === 'สำเร็จ' ? 'Success' : item.transfer_gp === 'รอการดำเนินการ' ? 'Waiting' : item.transfer_gp === 'ไม่สำเร็จ' ? 'Fail' : item.transfer_gp === 'ยังไม่ชำระเงิน' ? 'Not Paid' : 'Cancel'
      this.remarkGP = item.remark_gp
      this.transferAffiliateUpdate = item.transfer_affiliate === 'สำเร็จ' ? 'Success' : item.transfer_affiliate === 'รอการดำเนินการ' ? 'Waiting' : item.transfer_affiliate === 'ไม่สำเร็จ' ? 'Fail' : item.transfer_affiliate === 'ยังไม่ชำระเงิน' ? 'Not Paid' : 'Cancel'
      this.remarkAffiliate = item.remark_affiliate
      this.transferShippingUpdate = item.transfer_shipping === 'สำเร็จ' ? 'Success' : item.transfer_shipping === 'รอการดำเนินการ' ? 'Waiting' : item.transfer_shipping === 'ไม่สำเร็จ' ? 'Fail' : item.transfer_shipping === 'ยังไม่ชำระเงิน' ? 'Not Paid' : 'Cancel'
      this.remarkShipping = item.remark_shipping
      this.transferShopUpdate = item.transfer_shop === 'สำเร็จ' ? 'Success' : item.transfer_shop === 'รอการดำเนินการ' ? 'Waiting' : item.transfer_shop === 'ไม่สำเร็จ' ? 'Fail' : item.transfer_shop === 'ยังไม่ชำระเงิน' ? 'Not Paid' : 'Cancel'
      this.remarkShop = item.remark_shop
      this.isTransferUpdate = item.is_transfer === 'โอนอัตโนมัติ' ? 'Auto' : item.is_transfer === 'โอนด้วยตนเอง' ? 'Manual' : 'None'
      this.customRefundUpdate = item.customer_refund === 'สำเร็จ' ? 'Success' : item.customer_refund === 'รอการดำเนินการ' ? 'Waiting' : item.customer_refund === 'ไม่สำเร็จ' ? 'Fail' : item.customer_refund === 'ยังไม่ชำระเงิน' ? 'Not Paid' : 'Cancel'
      this.remarkRefund = item.remark_refund
      this.sendEmailUpdate = item.send_email === 'สำเร็จ' ? 'Success' : item.send_email === 'รอการดำเนินการ' ? 'Waiting' : 'Not Sent'
      this.date2 = item.customer_refund_date !== '' ? this.formatDateToEdit(item.customer_refund_date) : (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
      this.timeUpdate = item.customer_refund_date !== '' ? this.formatTimeToEdit(item.customer_refund_date) : currentTime
      this.sentDate2 = item.customer_refund_date !== '' ? this.formatThaiDateTime(item.customer_refund_date) : ''
      this.dialogUpdatePayment = true
    },
    formatDateToEdit (dateEdit) {
      const dateObj = new Date(dateEdit)
      const date = dateObj.toISOString().slice(0, 10)
      return date
    },
    formatTimeToEdit (timeEdit) {
      const timePartRaw = timeEdit.split('T')[1]
      const timePart = timePartRaw.replace('.000Z', '')
      const [hour, minute, second] = timePart.split(':')

      return `${hour}.${minute}.${second}`
    },
    formatThaiDateTime (dateStr) {
      const [datePart, timePartRaw] = dateStr.split('T')
      const timePart = timePartRaw.replace('.000Z', '') // ตัด .000Z ออก

      const [year, month, day] = datePart.split('-')
      const thaiMonth = new Date(`${year}-${month}-${day}`).toLocaleString('th-TH', { month: 'long' })
      const buddhistYear = parseInt(year) + 543

      const [hour, minute, second] = timePart.split(':')

      return `${parseInt(day)} ${thaiMonth} ${buddhistYear} เวลา ${hour}.${minute}.${second} น.`
    },
    setValueDate (dateDay, proof) {
      if (!dateDay) return null
      const date = new Date(dateDay)
      const dateSplit = dateDay.split('-')
      const monthChange = date.toLocaleString('th-TH', { month: 'long' })
      const yearChange = parseInt(dateSplit[0]) + 543
      this.sentDate2 = `${dateSplit[2]} ${monthChange} ${yearChange}` + ' ' + this.timeUpdate
    },
    async updatePaymentTransfer () {
      this.$store.commit('openLoader')
      var sendDate = ''
      if (this.date2 !== '' && this.timeUpdate !== '') {
        sendDate = this.date2 + ' ' + this.timeUpdate
      } else {
        sendDate = ''
      }
      var data = {
        id: this.idUpdate,
        order_number: this.orderNumberUpdate,
        // type_payment: this.typePaymentUpdate,
        transfer_gp: this.transferGPUpdate,
        transfer_affiliate: this.transferAffiliateUpdate,
        transfer_shipping: this.transferShippingUpdate,
        transfer_shop: this.transferShopUpdate,
        is_transfer: this.isTransferUpdate,
        customer_refund: this.customRefundUpdate,
        customer_refund_date: sendDate,
        send_email: this.sendEmailUpdate,
        remark_affiliate: this.remarkAffiliate,
        remark_shipping: this.remarkShipping,
        remark_refund: this.remarkRefund,
        remark_shop: this.remarkShop,
        remark_gp: this.remarkGP
      }
      await this.$store.dispatch('actionsUpdatePaymentTransfer', data)
      var res = await this.$store.state.ModuleAdminManage.stateUpdatePaymentTransfer
      if (res.code === 200) {
        this.dialogUpdate = false
        this.dialogUpdatePayment = false
        await this.listPaymentTransfer()
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${res.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    }
    // formatDateToThai (dateStr) {
    //   const [year, month, day] = dateStr.split('-')
    //   const buddhistYear = parseInt(year) + 543
    //   return `${day}/${month}/${String(buddhistYear).substring(4, 2)}`
    // }
  }
}
</script>

<style>

</style>
<style lang="scss" scoped>
::v-deep .shop-table table {
    thead {
      tr th:nth-child(1) {
        background: #E6F5F3 !important;
        border-style: none !important;
      }
    }
    tbody {
      tr {
        td:nth-child(18) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
          th {
            white-space: nowrap;
          }
          th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
          }
      }
    }
    thead {
      tr {
          th:nth-child(18) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          }
      }
    }
  }
</style>
<style scoped>
.v-data-table /deep/ .v-data-footer {
font-size: 0.62rem;
}
</style>
