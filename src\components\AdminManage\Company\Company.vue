<template>
  <v-container>
    <v-row align="center">
      <v-col cols="12">
        <v-card width="100%" height="100%" elevation="0">
          <v-row dense justify="center">
            <v-col cols="12" md="12" sm="12" xs="12">
              <v-card-title style="font-weight: bold; font-size: 24px;">รายชื่อบริษัทของฉัน</v-card-title>
            </v-col>
            <v-col cols="12" md="12" sm="12" xs="12">
            <v-row justify="end" class="mr-2 mb-2">
              <!-- <v-col cols="12" md="6" sm="12" xs="12">
                <v-row dense> -->
                  <!-- <v-btn class="ml-2">นำเข้าไฟล์ excel</v-btn>
                  <v-btn class="ml-2">ดาว์นโหลดแบบฟอร์ม Company</v-btn> -->
                  <v-btn class="ml-2 mr-3" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;" @click="createCompany()" dark><v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มข้อมูลบริษัท</v-btn>
                  <!-- <v-menu
                    bottom
                    offset-y
                  >
                    <template v-slot:activator="{ on, attrs }">
                      <v-btn
                        icon
                        v-bind="attrs"
                        v-on="on"
                      >
                        <v-icon>mdi-dots-vertical</v-icon>
                      </v-btn>
                    </template>

                    <v-btn>
                      <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-pencil</v-icon>แก้ไขรายละเอียดบริษัท
                    </v-btn>
                  </v-menu> -->
                <!-- </v-row>
              </v-col> -->
            </v-row>
          </v-col>
          </v-row>
          <TableCompany />
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script>
export default {
  components: {
    TableCompany: () => import(/* webpackPrefetch: true */ '@/components/AdminManage/Company/CompanyTable')
  },
  data () {
    return {
      companyData: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
  },
  methods: {
    createCompany () {
      this.$router.push({ path: '/manageCompany?Status=Create' }).catch(() => {})
    }
  }
}
</script>
