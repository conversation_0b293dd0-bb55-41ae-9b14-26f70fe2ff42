<template>
  <div>
  <!-- <v-app id='inspire'> -->
    <!-- v-system-bar -->
    <v-system-bar
      dark
      app
      height="40"
      color="#F3F5F7"
      class="responsive_navbar"
    >
        <span style="color: #333333;" class="mr-4 ml-16"><v-icon color="#00B500" size="22" class="mr-2">mdi-phone</v-icon>โทร 02-591-4005</span>
        <span style="color: #333333;" class="mr-4"><v-icon color="#00B500" size="22" class="mr-2">mdi-clock</v-icon>จันทร์- ศุกร์ เวลา 08.00-17.00 น. หยุด เสาร์ อาทิตย์</span>
        <span style="color: #333333;"><v-icon color="#00B500" size="22" class="mr-2">mdi-email</v-icon><EMAIL></span>
        <v-spacer></v-spacer>
        <img src="@/assets/Line_icon.png" height="22" width="22" class="mr-1"/>
        <v-icon color="#4A7AFF" size="22">mdi-facebook</v-icon>
        <v-icon color="#55ACEE" size="22">mdi-twitter</v-icon>
        <img src="@/assets/instagram_icon.png" height="22" width="22" class="mr-1"/>
        <v-icon color="#FF0000" size="22" class="mr-16">mdi-youtube</v-icon>
    </v-system-bar>
    <!-- v-app-bar -->
    <v-app-bar
      app
      color="#FAFAFA"
      elevation="0"
      :clipped-left="$vuetify.breakpoint.lgAndUp"
    >
      <v-app-bar-nav-icon @click.stop="drawer = !drawer" class="responsive_navbar_2"></v-app-bar-nav-icon>
      <v-toolbar-title class="responsive_navbar_2"><v-img :src="require('@/assets/ICON/UPS.png')" contain class="mr-5" height="46" width="91" style="cursor: pointer; float: right;" @click="goHome()"/></v-toolbar-title>
      <v-col cols="12" class="responsive_navbar">
        <v-toolbar-title>
          <v-row dense class="pl-10">
            <v-col cols="12" md="1" sm="1" xs="1" class="mt-2" style="padding-left: 20px;">
              <v-img :src="require('@/assets/ICON/UPS.png')" contain class="mr-5" height="46" width="91" style="cursor: pointer; float: right;" @click="goHome()"/>
            </v-col>
            <v-col cols="12" md="8" sm="4" xs="4" class="mt-4">
              <!-- <v-toolbar-title> -->
                <v-text-field color="#FFFFFF" v-model="searchtext" placeholder="ค้นหาสินค้าและร้านค้า" dense solo hide-details @keyup.enter="searchdata()" rounded>
                  <v-icon slot="append" @click="searchdata()" color="#008E00">mdi-magnify </v-icon>
                </v-text-field>
              <!-- </v-toolbar-title> -->
            </v-col>
            <v-col cols="12" md="3" sm="3" xs="3" class="pl-4">
              <v-row>
                <v-col cols="12" md="2" class="pr-0">
                  <CartPopover v-if="path"/>
                </v-col>
                <v-col cols="12" md="10" justify="start" class="pl-0">
                  <v-row no-gutters class="mt-1">
                    <!-- <v-btn text class="px-1">THB</v-btn>
                    <v-divider vertical></v-divider>
                    <v-btn text class="px-1">ภาษาไทย</v-btn>
                    <v-divider vertical></v-divider>
                    <v-btn text class="px-1">สมัครสมาชิก</v-btn>
                    <v-divider vertical></v-divider>
                    <v-btn text class="px-1">เข้าสู่ระบบ</v-btn> -->
                    <Login v-if="statusLogin === false" class="mt-1" />
                    <LoginSuccess class="mt-4 ml-2" v-else/>
                  </v-row>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-toolbar-title>
      </v-col>
      <template v-slot:extension>
        <v-row dense no-gutters style="margin-left: 11%;" class="responsive_navbar">
          <div v-for="(item, index) in items" :key="index">
            <v-btn text class="px-2" v-if="item.title !== 'สินค้า'" color="#333333">{{ item.title }}</v-btn>
            <v-menu
              v-model="menu"
              :close-on-content-click="false"
              offset-y
              v-else-if="item.title === 'สินค้า'"
            >
              <template v-slot:activator="{ on, attrs }">
                <v-btn
                  text
                  v-bind="attrs"
                  v-on="on"
                  class="px-2"
                  color="#333333"
                >
                  {{ item.title }}
                </v-btn>
              </template>
              <v-card width="720" height="100%">
                <v-list dense>
                  <v-row dense>
                    <v-col cols="12" md="4" class="pt-4">
                      <span style="font-weight: bold; font-size: 24px; line-height: 32px;" class="pl-4">หมวดหมู่</span>
                      <v-list-item-group class="pt-4">
                        <v-list-item
                         v-for="(item, i) in listItemCategory"
                         :key="i"
                         color="#00B500"
                        >
                          <v-list-item-content v-if="i !== 12">
                            <v-list-item-title v-text="item.title"></v-list-item-title>
                          </v-list-item-content>
                          <v-list-item-content v-else>
                            <v-list-item-title v-text="item.title" style="color: #00B500;"></v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-item-group>
                    </v-col>
                    <v-divider vertical class="mt-12 mb-2"></v-divider>
                    <v-col cols="12" md="3" class="pt-4">
                      <span style="font-weight: bold; font-size: 24px; line-height: 32px;" class="pl-4">แบรนด์</span>
                      <v-list-item-group class="pt-4">
                        <v-list-item
                         v-for="(item, i) in listItemBrand"
                         :key="i"
                         color="#00B500"
                        >
                          <v-list-item-content v-if="i !== 12">
                            <v-list-item-title v-text="item.title" ></v-list-item-title>
                          </v-list-item-content>
                          <v-list-item-content v-else>
                            <v-list-item-title v-text="item.title" style="color: #00B500;"></v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-item-group>
                    </v-col>
                    <v-divider vertical class="mt-12 mb-2"></v-divider>
                    <v-col cols="12" md="4" class="pt-4">
                      <span style="font-weight: bold; font-size: 24px; line-height: 32px;" class="pl-4">ประเภทอุตสาหกรรม</span>
                      <v-list-item-group class="pt-4">
                        <v-list-item
                         v-for="(item, i) in listItemIndrustry"
                         :key="i"
                         color="#00B500"
                        >
                          <v-list-item-content v-if="i !== 11">
                            <v-list-item-title v-text="item.title" ></v-list-item-title>
                          </v-list-item-content>
                          <v-list-item-content v-else>
                            <v-list-item-title v-text="item.title" style="color: #00B500;"></v-list-item-title>
                          </v-list-item-content>
                        </v-list-item>
                      </v-list-item-group>
                    </v-col>
                  </v-row>
                </v-list>
              </v-card>
            </v-menu>
          </div>
        </v-row>
      </template>
    </v-app-bar>
    <!-- v-navigation-drawer -->
    <v-navigation-drawer
      v-model='drawer'
      app
      absolute
      left
      temporary
      :clipped="$vuetify.breakpoint.mdAndDown"
    >
      <v-list dense>
        <v-list-item link @click="link('/')">
          <v-list-item-action>
            <v-icon>mdi-view-dashboard</v-icon>
          </v-list-item-action>
          <v-list-item-content>
            <v-list-item-title>Dashboard</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item link @click="link('/about')">
          <v-list-item-action>
            <v-icon>mdi-file-multiple</v-icon>
          </v-list-item-action>
          <v-list-item-content>
            <v-list-item-title>Report</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
        <v-list-item link @click="link('/carousal')">
          <v-list-item-action>
            <v-icon>mdi-play-box-outline</v-icon>
          </v-list-item-action>
          <v-list-item-content>
            <v-list-item-title>Carousel</v-list-item-title>
          </v-list-item-content>
        </v-list-item>
      </v-list>
      <v-list dense>
        <v-list-item-group
          v-model="selectedItem"
          color="primary"
        >
          <v-list-item
            v-for="(item, i) in items"
            :key="i"
          >
            <v-list-item-icon>
              <v-icon v-text="item.icon"></v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title v-text="item.title"></v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list-item-group>
      </v-list>
    </v-navigation-drawer>
  <!-- </v-app> -->
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {
    CartPopover: () => import('@/components/PopOver/PopoverCartAppBarUI'),
    Login: () => import('@/components/PopOver/LoginUI'),
    LoginSuccess: () => import('@/components/PopOver/LoginSuccessUI')
  },
  data () {
    return {
      drawer: false,
      searchtext: '',
      selectedItem: '',
      menu: '',
      items: [
        { icon: 'mdi-home', title: 'หน้าหลัก' },
        { icon: 'mdi-information', title: 'เกี่ยวกับเรา' },
        { icon: 'mdi-manjaro', title: 'สินค้า' },
        { icon: 'mdi-professional-hexagon', title: 'โปรโมชัน' },
        { icon: 'mdi-basket', title: 'E-Catelog' },
        { icon: 'mdi-currency-usd', title: 'วิธีการชำระเงิน' },
        { icon: 'mdi-ticket', title: 'ข่าวและกิจกรรม' },
        { icon: 'mdi-file-document-edit', title: 'ติดต่อเรา' }
      ],
      listItemCategory: [
        { title: 'อุปกรณ์ป้องกันศรีษะ' },
        { title: 'อุปกรณ์ป้องกันใบหน้า' },
        { title: 'อุปกรณ์ป้องกันดวงตา' },
        { title: 'อุปกรณ์ป้องกันทางเดินหายใจ' },
        { title: 'อุปกรณ์ด้านกายภาพ' },
        { title: 'อุปกรณ์ป้องกันตก' },
        { title: 'อุปกรณ์จัดเก็บสารเคมี' },
        { title: 'อุปกรณ์ฝักบัวและล้างตา' },
        { title: 'อุปกรณ์ดูดซับน้ำมัน สารเคมี' },
        { title: 'เครื่องมือตรวจวัด' },
        { title: 'อุปกรณ์ป้องกันไฟฟ้า' },
        { title: 'อุปกรณ์ล็อคเอ้าท์' },
        { title: 'ดูหมวดหมู่สินค้าทั้งหมด' }
      ],
      listItemBrand: [
        { title: 'Dellta Pluss' },
        { title: 'Dupont' },
        { title: 'Haws' },
        { title: '3M' },
        { title: 'Krushers' },
        { title: 'Steel Blue' },
        { title: 'Showa' },
        { title: 'Bolle’' },
        { title: 'Howler' },
        { title: 'Delight' },
        { title: 'Dailove' },
        { title: 'Msa' },
        { title: 'ดูแบรนด์ทั้งหมด' }
      ],
      listItemIndrustry: [
        { title: 'อุตสาหกรรมอาหารและเครื่องดื่ม' },
        { title: 'อุตสาหกรรมยานยนต์' },
        { title: 'อุตสาหกรรมเครื่องจักร' },
        { title: 'อุตสาหกรรมบรรจุภัณฑ์' },
        { title: 'อุตสาหกรรมเปโตรเคมี' },
        { title: 'อุตสหกรรมเหล็ก' },
        { title: 'อุตสาหกรรมก่อสร้าง' },
        { title: 'อุตสาหกรรมพลังงาน' },
        { title: 'อุตสาหกรรมการแพทย์' },
        { title: 'อุตสาหกรรมอิเล็กทรอนิกส์' },
        { title: 'อุตสาหกรรมขนส่ง โลจิสติกส์' },
        { title: 'ดูประเภทอุตสาหกรรมทั้งหมด' }
      ],
      countCart: 1,
      statusLogin: false,
      onedata: [],
      ListCategory: [],
      path: false
    }
  },
  created () {
    this.GetCategory()
    this.getPath()
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.checkButton = true
      this.onedata = []
      this.onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      // console.log(this.onedata)
      if (this.onedata.user !== undefined) {
        // this.username = onedata.user[0].first_name_th + ' ' + onedata.user[0].last_name_th
        this.statusLogin = true
      } else {
        this.statusLogin = false
      }
    } else {
      this.statusLogin = false
      this.checkButton = false
    }
    window.addEventListener('storage', function (event) {
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        window.location.reload()
      }
    })
  },
  mounted () {
    this.$EventBus.$on('searchdata', this.searchdata)
    this.$EventBus.$on('getPath', this.getPath)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('searchdata')
      this.$EventBus.$off('getPath')
    })
  },
  computed: {
    main () {
      return this.$route.name === 'Home'
    }
  },
  methods: {
    link (val) {
      this.$router.push({ path: `${val}` }).catch(() => {})
    },
    gotoSeller () {
      if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
        if (this.onedata.user.type_user === 'oneID_user') {
          this.$router.push({ path: '/sellers' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาผูกบัญชีกับ One ID ก่อน!', showConfirmButton: false, timer: 1500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบหรือสมัครสมาชิกก่อน!', showConfirmButton: false, timer: 1500 })
        this.$EventBus.$emit('openModalLogin')
      }
    },
    async getPath () {
      var currentRoutepath = this.$router.currentRoute.path
      // console.log('getPath', currentRoutepath)
      if (currentRoutepath === '/shoppingcart' || currentRoutepath === '/checkout') {
        this.path = false
      } else {
        this.path = true
      }
    },
    async GetCategory () {
      await this.$store.dispatch('GetDefaultCagegory')
      this.ListCategory = await this.$store.state.ModuleManageShop.Category
      // console.log('ListCategory', this.ListCategory)
    },
    goHome () {
      if (localStorage.getItem('roleUserApprove') !== null) {
        // console.log('เข้าเงื่อนไขขขขขขขขข')
        var val = JSON.parse(localStorage.getItem('roleUser'))
        var data = {
          role: val.role === 'purchaser' ? 'purchaser' : 'ext_buyer'
        }
        val.role === 'ext_buyer' ? this.changeRole = 'ext_buyer' : this.changeRole = 'purchaser'
        localStorage.setItem('roleUser', JSON.stringify(data))
        this.$EventBus.$emit('LinkPage', val.role)
        this.searchtext = ''
        this.$router.push({ path: '/' }).catch(() => {})
      }
      this.searchtext = ''
      this.$router.push({ path: '/' }).catch(() => {})
    },
    gotoCreateEpro () {
      this.$router.push({ path: '/createbusinesssid' }).catch(() => {})
    },
    searchdata () {
      if (this.searchtext === '') {
        this.$swal.fire({
          icon: 'warning',
          title: 'โปรดใส่คำที่จะค้นหา',
          showConfirmButton: false,
          timer: 1500
        })
      } else {
        // localStorage.setItem('searchtext', this.searchtext)
        if (this.$router.currentRoute.name === 'search') {
          if (this.$router.currentRoute.params.data === this.searchtext) {
            // console.log('condition 1')
            this.$EventBus.$emit('getResultSearch')
          } else {
            // console.log('condition 2')
            this.$router.push({ path: `${this.searchtext}` }).catch(() => {})
            this.$EventBus.$emit('getResultSearch')
          }
        } else {
          // console.log('condition 3')
          this.$router.push({ path: `/search/${this.searchtext}` }).catch(() => {})
          this.$EventBus.$emit('getResultSearch')
        }
      }
    }
  }
}
</script>

<style scoped>
.instagram{
  background: radial-gradient(circle at 30% 107%, #fdf497 0%, #fdf497 5%, #fd5949 45%, #d6249f 60%, #285AEB 90%);
}
@media only screen and (min-width: 640px) {
  .responsive_navbar_2 {
    display: none !important;
  }
}
@media only screen and (max-width: 640px) {
  .responsive_navbar {
    display: none !important;
  }
  .Setting_down {
    display: none;
  }
  .shopping-cart-logo {
    display: none;
  }
  .profile_name {
    display: none;
  }
  /* .e-pro-front {
    display: none;
  } */
  .e-pro-back {
    display: flex;
  }
  .vdivider {
    display: none;
  }
  .logo-login-pass {
    display: none;
  }
  .logo-login-pass-mobile {
    display: flex;
  }
}
</style>
