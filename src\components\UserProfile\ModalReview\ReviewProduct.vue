<template>
  <div class="text-center">
    <v-dialog v-model="ModalReviewProduct" :style="MobileSize ? 'z-index: 16000004' : ''" width="730px" persistent>
      <v-card>
        <v-toolbar flat color="#BDE7D9">
          <v-btn v-show="reviewMultiProduct" fab small @click="backward()" icon><v-icon color="#27AB9C">mdi-arrow-left</v-icon></v-btn>
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>{{$t('ReviewListBuyer.title')}}</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="CancelMulti()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <!-- <v-container grid-list-xs> -->
          <!-- Desktop -->
          <!-- multiProduct -->
          <v-card-text v-if="reviewMultiProduct === false && !MobileSize">
            <v-row no-gutters class="pt-5" v-if="statusReview !== 'expired'">
              <v-col cols="12" class="pt-5">
                <h3>{{$t('ReviewListBuyer.textDialog')}}</h3>
              </v-col>
            </v-row>
            <v-row no-gutters v-for="(item, index) in productMultiList" :key="index">
              <v-col cols="12">
                <v-card outlined class="mt-8" @click="getSingleProduct(item)">
                  <v-container grid-list-xs>
                    <v-row no-gutters>
                      <v-col cols="12" sm="2" md="2" class="text-center">
                        <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''"/>
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else/>
                      </v-col>
                      <v-col cols="12" sm="8" md="7" class="text-left">
                        <p class="mb-0 captionSku">{{$t('ReviewListBuyer.titleSKU')}}: {{item.sku}}<br/>{{item.product_name}}</p>
                        <span class="mb-0 captionSku"><b>{{item.attribute_detail}}</b></span>
                        <v-rating v-if="item.status === 'can_edit' || item.status === 'success'" size="12" v-model="item.stars" background-color="#FFCB49" color="#FFCB49"></v-rating>
                      </v-col>
                      <v-col cols="12" sm="2" md="3" class="text-right">
                        <span v-if="item.status === 'can_edit'">
                          <v-chip small color="#E6F5F3" text-color="#27AB9C">{{$t('ReviewListBuyer.titleEdit')}}</v-chip>
                        </span>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="reviewMultiProduct === true && !MobileSize">
            <v-row no-gutters>
              <v-col cols="12">
                <v-card outlined class="mt-8">
                  <v-container grid-list-xs>
                    <v-row no-gutters>
                      <v-col cols="12" sm="3" md="2" class="text-center">
                        <v-img :src="productDetail.product_image" class="imageshow" v-if="productDetail.product_image !== ''"/>
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else/>
                      </v-col>
                      <v-col cols="12" sm="9" md="7" class="text-left">
                        <p class="mb-0 captionSku">{{$t('ReviewListBuyer.titleSKU')}}: {{productDetail.sku}}<br/>{{productDetail.product_name}}</p>
                        <span class="mb-0 captionSku"><b>{{productDetail.attribute_detail}}</b></span>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
              </v-col>
              <v-col cols="12" class="mt-2">
                <v-card outlined color="#F5F8FF" class="mt-2">
                  <v-container grid-list-xs>
                    <v-row no-gutters>
                      <v-col cols="12" md="12" class="text-center">
                        <v-rating :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" v-model="productDetail.stars" background-color="#FFCB49" color="#FFCB49"></v-rating>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
              </v-col>
              <v-row v-if="productDetail.stars !== 0 && productDetail.stars !== ''" no-gutters class="mt-3">
                <v-col cols="12" md="12" class="text-left mb-4" v-if="productDetail.date !== ''">
                  <span style="font-size: 16px;">
                    <v-icon small class="pr-1">mdi-clock-outline</v-icon>
                    {{ productDetail.status === 'success' ? titleDateSuccess : titleDateEdit }} {{ formatDateTime(productDetail.date) }} {{ this.$i18n.locale === 'th' ? new Date(productDetail.date).toLocaleTimeString('th-TH') + ' น.' : new Date(productDetail.date).toLocaleTimeString('en-EN')}}
                  </span>
                </v-col>
                <v-col cols="12" md="12" class="text-left mb-2">
                  <span style="font-size: 18px;">{{ productDetail.status === 'success' ? titleReviewSuccess : titleReviewEdit}}</span>
                </v-col>
                <v-col cols="12" md="12">
                  <v-textarea :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" v-model="productDetail.comment" outlined maxlength="250" :counter="250"></v-textarea>
                </v-col>
              </v-row>
              <!-- Upload image -->
              <v-col v-if="productDetail.stars !== 0 && productDetail.stars !== ''" cols="12" md="12">
                  <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                    <v-card-text v-if="productDetail.status === 'success' && (productDetail.image_upload === null || productDetail.image_upload.length === 0)">{{$t('ReviewListBuyer.noImage')}}</v-card-text>
                    <v-card-text>
                      <v-row>
                        <v-col cols="6">
                            <v-card v-if="productDetail.status !== 'success'" class="mb-4" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFileMulti()">
                              <v-card-text>
                                <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                  <v-file-input
                                  v-model="DataImage"
                                  :items="DataImage"
                                  accept="image/jpeg, image/jpg, image/png"
                                  @change="UploadImageMulti()"
                                  :id="'file_input'"
                                  multiple
                                  :clearable="false"
                                  style="display:none">
                                </v-file-input>
                                <v-col cols="12" md="12">
                                  <v-row justify="center" align="center">
                                    <v-col cols="12" md="12" class="mb-6">
                                      <v-row justify="center" class="pt-10">
                                        <v-img src="@/assets/ReviewUploadImage.png" width="144px" height="85px" contain/>
                                      </v-row>
                                    </v-col>
                                    <v-col cols="12" md="12" style="text-align: center;">
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 700;">{{$t('ReviewListBuyer.uploadImage')}}</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{$t('ReviewListBuyer.fixedSizeImage')}}</span><br/>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                        <v-col cols="6">
                            <v-card v-if="productDetail.status !== 'success'" class="mb-4" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFileVideo()">
                              <v-card-text>
                                <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                  <!-- <input
                                  type="file"
                                  accept="video/mp4"
                                  @change="UploadVideoMulti($event)"
                                  :id="'file_input_video'"
                                  > -->
                                  <v-file-input
                                    v-model="DataVideo"
                                    :items="DataVideo"
                                    type="file"
                                    accept="video/mp4"
                                    @change="UploadVideoMulti($event)"
                                    :id="'file_input_video'"
                                    style="display:none"
                                    >
                                  </v-file-input>
                                  <!-- <template v-if="videoPreviewUrl">
                                    <video :src="videoPreviewUrl" controls width="100%" style="border-radius: 8px;"/>
                                    <v-btn icon x-small style="float: right; background-color: #ff5252; left: 14vw; bottom: 42vw;" @click="clsVideo()">
                                      <v-icon x-small color="white" dark>mdi-close</v-icon>
                                    </v-btn>
                                  </template> -->
                                  <template>
                                    <v-row justify="center" align="center">
                                      <v-col cols="12" md="12" class="mb-6">
                                        <v-row justify="center" class="pt-10">
                                          <v-img src="@/assets/ReviewUploadVideo.png" width="144px" height="85px" contain/>
                                        </v-row>
                                      </v-col>
                                      <v-col cols="12" md="12" style="text-align: center;">
                                        <span style="font-size: 16px; line-height: 24px; font-weight: 700;">{{$t('ReviewListBuyer.uploadVideo')}}</span><br/>
                                        <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{$t('ReviewListBuyer.fixedSizeVideo')}}</span><br/>
                                      </v-col>
                                    </v-row>
                                  </template>
                                <!-- <v-col cols="12" md="12" >
                                  <v-row justify="center" align="center">
                                    <v-col cols="12" md="12" class="mb-6">
                                      <v-row justify="center" class="pt-10">
                                        <v-img src="@/assets/ReviewUploadVideo.png" width="144px" height="85px" contain/>
                                      </v-row>
                                    </v-col>
                                    <v-col cols="12" md="12" style="text-align: center;">
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 700;">เพิ่มวิดีโอของคุณที่นี่</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(อัปโหลดไม่เกิน 1 ไฟล์ ขนาดไม่เกิน 20 mb นามสกุลไฟล์ .mp4)</span><br/>
                                    </v-col>
                                  </v-row>
                                </v-col> -->
                              </v-row>
                            </v-card-text>
                          </v-card>
                        </v-col>
                      </v-row>
                      <!-- fixed image upload Not show -->
                      <pre v-show="false">{{ dataUploadImage }}</pre>
                      <!-- fixed image upload Not show -->
                      <v-row v-if="productDetail.length !== 0">
                        <v-col v-for="(items, index2) in productDetail.image_upload" :key="index2" cols="6" md="3" sm="4">
                          <v-card outlined class="pa-1" width="146" height="146" v-if="items.media_type === 'image'">
                            <v-img :src="items.media_path" :lazy-src="items.media_path" width="130" height="130" contain>
                              <v-btn v-if="productDetail.status !== 'success'" icon x-small style="float: right; background-color: #ff5252;">
                                <v-icon x-small color="white" dark @click="RemoveImageMulti(index2, items)">mdi-close</v-icon>
                              </v-btn>
                            </v-img>
                          </v-card>
                          <v-card outlined class="pa-1" width="146" height="146" v-else>
                            <video :src="items.media_path" controls width="130" height="135" style="border-radius: 8px;"/>
                            <v-btn v-if="productDetail.status !== 'success'" icon x-small style="float: right; background-color: #ff5252; bottom: 140px;" @click="RemoveImageMulti(index2, items)">
                              <v-icon x-small color="white" dark>mdi-close</v-icon>
                            </v-btn>
                          </v-card>
                        </v-col>
                      </v-row>
                    </v-card-text>
                  </v-card>
                </v-col>
              <v-col v-if="productDetail.stars !== 0 && productDetail.stars !== '' && productDetail.status === 'success'" cols="12" class="mt-3">
                <!-- <v-checkbox :readonly="productDetail.status === 'success'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? userName : 'ไม่แสดงตัวตน'" value="yes"></v-checkbox> -->
                <v-checkbox :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? `${this.$t('ReviewListBuyer.anonymous')}` + productDetail.username.substring(0, 1) + '****' + productDetail.username.substring(productDetail.username.length - 1) : `${this.$t('ReviewListBuyer.anonymous')}`" value="yes"></v-checkbox>
              </v-col>
              <v-col v-if="productDetail.stars !== 0 && productDetail.stars !== '' && productDetail.status === 'can_edit'" cols="12" class="mt-3">
                <!-- <v-checkbox :readonly="productDetail.status === 'success'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? userName : 'ไม่แสดงตัวตน'" value="yes"></v-checkbox> -->
                <v-checkbox :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? userName : `${this.$t('ReviewListBuyer.anonymous')}`" value="yes"></v-checkbox>
              </v-col>
              <v-col v-if="productDetail.stars !== 0 && productDetail.stars !== '' && productDetail.status === 'waiting_review'" cols="12" class="mt-3">
                <!-- <v-checkbox :readonly="productDetail.status === 'success'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? userName : 'ไม่แสดงตัวตน'" value="yes"></v-checkbox> -->
                <v-checkbox :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? userName : `${this.$t('ReviewListBuyer.anonymous')}`" value="yes"></v-checkbox>
              </v-col>
            <!-- Upload image -->
            <!-- Upload video-->
            <!-- <v-col v-if="rating !== 0" cols="12" md="12">
              <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                <v-card-text>
                  <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFileVideo()">
                    <v-card-text>
                      <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                        <v-file-input
                        v-model="DataVideo"
                        :items="DataVideo"
                        accept="video/mp4"
                        @change="UploadVideoMulti()"
                        :id="'file_input_video'"
                        multiple
                        :clearable="false"
                        style="display:none">
                      </v-file-input>
                      <v-col cols="12" md="12">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="12" class="mb-6">
                            <v-row justify="center" class="pt-10">
                              <v-img src="@/assets/ReviewUploadVideo.png" width="144px" height="85px" contain/>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12" style="text-align: center;">
                            <span style="font-size: 16px; line-height: 24px; font-weight: 700;">เพิ่มวิดีโอของคุณที่นี่</span><br/>
                            <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(อัปโหลดไม่เกิน 1 ไฟล์ ขนาดไม่เกิน 20 mb นามสกุลไฟล์ .mp4)</span><br/>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card> -->
                <!-- fixed image upload Not show -->
                <!-- <pre v-show="false">{{ dataUploadVideo }}</pre> -->
                <!-- fixed image upload Not show -->
                <!-- <div v-if="productDetail.review_video.length !== 0" class="mt-4">
                  <draggable v-model="productDetail.review_video"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                    <v-col v-for="(items, index2) in productDetail.review_video" :key="index2" cols="12" md="6">
                      <v-card outlined class="pa-1" width="146" height="146">
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click="RemoveVideoMulti(index2, items)">mdi-close</v-icon>
                        </v-btn>
                        <video width="100%" height="100%" controls autoplay>
                          <source :src="items.media_path">
                        </video>
                      </v-card>
                    </v-col>
                  </draggable>
                </div>
              </v-card-text>
            </v-card>
          </v-col> -->
          <!-- Upload video -->
            </v-row>
          </v-card-text>
          <v-card-actions class="pb-6" v-if="productDetail.stars !== 0 && productDetail.stars !== '' && reviewMultiProduct === true && !MobileSize && productDetail.status !== 'success'">
            <v-spacer></v-spacer>
            <v-btn class="px-10" outlined color="#27AB9C" @click="CancelMulti()">{{$t('ReviewListBuyer.btnCancel')}}</v-btn>
            <v-btn class="px-10 white--text" color="#27AB9C" @click="editReviewMulti()">{{$t('ReviewListBuyer.btnSuccess')}}</v-btn>
          </v-card-actions>
          <!-- Mobile -->
          <!-- multiProduct -->
          <v-card-text v-if="reviewMultiProduct === false && MobileSize">
            <v-row no-gutters class="pt-5" v-if="statusReview !== 'expired'">
              <v-col cols="12" class="pt-5">
                <h3>{{$t('ReviewListBuyer.textDialog')}}</h3>
              </v-col>
            </v-row>
            <v-row no-gutters v-for="(item, index) in productMultiList" :key="index">
              <v-col cols="12">
                <v-card outlined class="mt-8" @click="getSingleProduct(item)">
                  <v-container grid-list-xs>
                    <v-row no-gutters>
                      <v-col cols="5" sm="2" md="2" class="text-center">
                        <v-img :src="item.product_image" class="imageshow" v-if="item.product_image !== ''"/>
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else/>
                      </v-col>
                      <v-col cols="7" sm="8" md="7" class="text-left">
                        <p class="mb-0 captionSku">{{$t('ReviewListBuyer.titleSKU')}}: {{item.sku}}<br/>{{item.product_name}}</p>
                        <span class="mb-0 captionSku"><b>{{item.attribute_detail}}</b></span>
                        <v-rating v-if="item.status === 'can_edit' || item.status === 'success'" size="12" v-model="item.stars" background-color="#FFCB49" color="#FFCB49" readonly></v-rating>
                      </v-col>
                      <v-col cols="12" sm="2" md="3" class="text-right">
                        <span v-if="item.status === 'can_edit' && item.status !== 'expired'">
                          <v-chip small color="#E6F5F3" text-color="#27AB9C">แก้ไข</v-chip>
                        </span>
                        <span v-if="item.status === 'expired'">
                          <v-chip small color="#E6F5F3" text-color="#27AB9C">เกินระยะเวลาการประเมินความพึงพอใจ</v-chip>
                        </span>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-text v-if="reviewMultiProduct === true && MobileSize">
            <v-row no-gutters>
              <v-col cols="12">
                <v-card outlined class="mt-8">
                  <v-container grid-list-xs>
                    <v-row no-gutters>
                      <v-col cols="5" md="2" class="text-center">
                        <v-img :src="productDetail.product_image" class="imageshow" v-if="productDetail.product_image !== ''"/>
                        <v-img src="@/assets/NoImage.png" class="imageshow" v-else/>
                      </v-col>
                      <v-col cols="7" md="7" class="text-left">
                        <p class="mb-0 captionSku">{{$t('ReviewListBuyer.titleSKU')}}: {{productDetail.sku}}<br/>{{productDetail.product_name}}</p>
                        <span class="mb-0 captionSku"><b>{{productDetail.attribute_detail}}</b></span>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
              </v-col>
              <v-col cols="12" class="mt-2">
                <v-card outlined color="#F5F8FF" class="mt-2">
                  <v-container grid-list-xs>
                    <v-row no-gutters>
                      <v-col cols="12" md="12" class="text-center">
                        <v-rating :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" v-model="productDetail.stars" background-color="#FFCB49" color="#FFCB49"></v-rating>
                      </v-col>
                    </v-row>
                  </v-container>
                </v-card>
              </v-col>
              <v-row v-if="productDetail.stars !== 0 && productDetail.stars !== ''" no-gutters class="mt-3">
                <v-col cols="12" md="12" class="text-left mb-4" v-if="productDetail.date !== ''">
                  <span style="font-size: 16px;"><v-icon small class="pr-1">mdi-clock-outline</v-icon>
                  {{ productDetail.status === 'success' ? titleDateSuccess : titleDateEdit }}{{ formatDateTime(productDetail.date) }} {{ this.$i18n.locale === 'th' ? new Date(productDetail.date).toLocaleTimeString('th-TH') + ' น.' : new Date(productDetail.date).toLocaleTimeString('en-EN')}}</span>
                </v-col>
                <v-col cols="12" md="12" class="text-left mb-2">
                  <span style="font-size: 18px;">{{ productDetail.status === 'success' ? titleReviewSuccess : titleReviewEdit}}</span>
                </v-col>
                <v-col cols="12" md="12">
                  <v-textarea :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" v-model="productDetail.comment" outlined maxlength="250" :counter="250"></v-textarea>
                </v-col>
              </v-row>
              <!-- Upload image -->
              <v-col v-if="productDetail.stars !== 0 && productDetail.stars !== ''" cols="12" md="12">
                <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                  <v-card-text>
                    <v-row>
                      <v-col cols="12">
                          <v-card v-if="productDetail.status !== 'success'" class="mb-4" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFileMulti()">
                            <v-card-text>
                              <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                <v-file-input
                                v-model="DataImage"
                                :items="DataImage"
                                accept="image/jpeg, image/jpg, image/png"
                                @change="UploadImageMulti()"
                                :id="'file_input'"
                                multiple
                                :clearable="false"
                                style="display:none">
                              </v-file-input>
                              <v-col cols="12" md="12">
                                <v-row justify="center" align="center">
                                  <v-col cols="12" md="12" class="mb-6">
                                    <v-row justify="center" class="pt-10">
                                      <v-img src="@/assets/ReviewUploadImage.png" width="144px" height="85px" contain/>
                                    </v-row>
                                  </v-col>
                                  <v-col cols="12" md="12" style="text-align: center;">
                                    <span style="font-size: 16px; line-height: 24px; font-weight: 700;">{{$t('ReviewListBuyer.uploadImage')}}</span><br/>
                                    <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{$t('ReviewListBuyer.fixedSizeImage')}}</span><br/>
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                      <v-col cols="12">
                          <v-card v-if="productDetail.status !== 'success'" class="mb-4" elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFileVideo()">
                            <v-card-text>
                              <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                <!-- <input
                                type="file"
                                accept="video/mp4"
                                @change="UploadVideoMulti($event)"
                                :id="'file_input_video'"
                                > -->
                                <v-file-input
                                  v-model="DataVideo"
                                  :items="DataVideo"
                                  type="file"
                                  accept="video/mp4"
                                  @change="UploadVideoMulti($event)"
                                  :id="'file_input_video'"
                                  style="display:none"
                                  >
                                </v-file-input>
                                <!-- <template v-if="videoPreviewUrl">
                                  <video :src="videoPreviewUrl" controls width="100%" style="border-radius: 8px;"/>
                                  <v-btn icon x-small style="float: right; background-color: #ff5252; left: 14vw; bottom: 42vw;" @click="clsVideo()">
                                    <v-icon x-small color="white" dark>mdi-close</v-icon>
                                  </v-btn>
                                </template> -->
                                <template>
                                  <v-row justify="center" align="center">
                                    <v-col cols="12" md="12" class="mb-6">
                                      <v-row justify="center" class="pt-10">
                                        <v-img src="@/assets/ReviewUploadVideo.png" width="144px" height="85px" contain/>
                                      </v-row>
                                    </v-col>
                                    <v-col cols="12" md="12" style="text-align: center;">
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 700;">{{$t('ReviewListBuyer.uploadVideo')}}</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;">{{$t('ReviewListBuyer.fixedSizeVideo')}}</span><br/>
                                    </v-col>
                                  </v-row>
                                </template>
                              <!-- <v-col cols="12" md="12" >
                                <v-row justify="center" align="center">
                                  <v-col cols="12" md="12" class="mb-6">
                                    <v-row justify="center" class="pt-10">
                                      <v-img src="@/assets/ReviewUploadVideo.png" width="144px" height="85px" contain/>
                                    </v-row>
                                  </v-col>
                                  <v-col cols="12" md="12" style="text-align: center;">
                                    <span style="font-size: 16px; line-height: 24px; font-weight: 700;">เพิ่มวิดีโอของคุณที่นี่</span><br/>
                                    <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(อัปโหลดไม่เกิน 1 ไฟล์ ขนาดไม่เกิน 20 mb นามสกุลไฟล์ .mp4)</span><br/>
                                  </v-col>
                                </v-row>
                              </v-col> -->
                            </v-row>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  <!-- fixed image upload Not show -->
                  <pre v-show="false">{{ dataUploadImage }}</pre>
                  <!-- fixed image upload Not show -->
                  <!-- <v-row v-if="productDetail.image_upload !== null && productDetail.image_upload.length !== 0">
                    <v-col v-for="(items, index2) in productDetail.image_upload" :key="index2" cols="6" md="3" sm="4" align="center">
                      <v-card outlined class="pa-1" width="146" height="146" align="center">
                        <v-img :src="items.media_path" :lazy-src="items.media_path" width="130" height="130" contain>
                          <v-btn v-if="productDetail.status !== 'success'" icon x-small style="float: right; background-color: #ff5252;">
                            <v-icon x-small color="white" dark @click="RemoveImageMulti(index2, items)">mdi-close</v-icon>
                          </v-btn>
                        </v-img>
                      </v-card>
                    </v-col>
                  </v-row> -->
                  <v-row v-if="productDetail.length !== 0">
                    <v-col v-for="(items, index2) in productDetail.image_upload" :key="index2" cols="6" md="3" sm="4">
                      <v-card outlined class="pa-1" width="146" height="146" v-if="items.media_type === 'image'">
                        <v-img :src="items.media_path" :lazy-src="items.media_path" width="130" height="130" contain>
                          <v-btn v-if="productDetail.status !== 'success'" icon x-small style="float: right; background-color: #ff5252;">
                            <v-icon x-small color="white" dark @click="RemoveImageMulti(index2, items)">mdi-close</v-icon>
                          </v-btn>
                        </v-img>
                      </v-card>
                      <v-card outlined class="pa-1" width="146" height="146" v-else>
                        <video :src="items.media_path" controls width="130" height="135" style="border-radius: 8px;"/>
                        <v-btn v-if="productDetail.status !== 'success'" icon x-small style="float: right; background-color: #ff5252; bottom: 140px;" @click="RemoveImageMulti(index2, items)">
                          <v-icon x-small color="white" dark>mdi-close</v-icon>
                        </v-btn>
                      </v-card>
                    </v-col>
                  </v-row>
                  </v-card-text>
                </v-card>
              </v-col>
              <v-col v-if="productDetail.stars !== 0 && productDetail.stars !== ''" cols="12" class="mt-3">
                <v-checkbox :readonly="productDetail.status === 'success' || productDetail.status === 'expired'" class="float-left" dense v-model="productDetail.hide_username" :label="productDetail.hide_username === 'yes' ? userName : `${this.$t('ReviewListBuyer.anonymous')}`" value="yes"></v-checkbox>
              </v-col>
            <!-- Upload image -->
            <!-- Upload video-->
            <!-- <v-col v-if="rating !== 0" cols="12" md="12">
              <v-card outlined elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                <v-card-text>
                  <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFileVideo()">
                    <v-card-text>
                      <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                        <v-file-input
                        v-model="DataVideo"
                        :items="DataVideo"
                        accept="video/mp4"
                        @change="UploadVideoMulti()"
                        :id="'file_input_video'"
                        multiple
                        :clearable="false"
                        style="display:none">
                      </v-file-input>
                      <v-col cols="12" md="12">
                        <v-row justify="center" align="center">
                          <v-col cols="12" md="12" class="mb-6">
                            <v-row justify="center" class="pt-10">
                              <v-img src="@/assets/ReviewUploadVideo.png" width="144px" height="85px" contain/>
                            </v-row>
                          </v-col>
                          <v-col cols="12" md="12" style="text-align: center;">
                            <span style="font-size: 16px; line-height: 24px; font-weight: 700;">เพิ่มวิดีโอของคุณที่นี่</span><br/>
                            <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(อัปโหลดไม่เกิน 1 ไฟล์ ขนาดไม่เกิน 20 mb นามสกุลไฟล์ .mp4)</span><br/>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card> -->
                <!-- fixed image upload Not show -->
                <!-- <pre v-show="false">{{ dataUploadVideo }}</pre> -->
                <!-- fixed image upload Not show -->
                <!-- <div v-if="productDetail.review_video.length !== 0" class="mt-4">
                  <draggable v-model="productDetail.review_video"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                    <v-col v-for="(items, index2) in productDetail.review_video" :key="index2" cols="12" md="6" align="center">
                      <v-card outlined class="pa-1" width="146" height="146">
                        <v-btn icon x-small style="float: right; background-color: #ff5252;">
                          <v-icon x-small color="white" dark @click="RemoveVideoMulti(index2, items)">mdi-close</v-icon>
                        </v-btn>
                        <video width="100%" height="100%" controls autoplay>
                          <source :src="items.media_path">
                        </video>
                      </v-card>
                    </v-col>
                  </draggable>
                </div> -->
              <!-- </v-card-text>
            </v-card>
          </v-col> -->
          <!-- Upload video -->
            </v-row>
          </v-card-text>
          <v-card-actions class="pb-6" v-if="productDetail.stars !== 0 && productDetail.stars !== '' && reviewMultiProduct === true && MobileSize && productDetail.status !== 'success'">
            <v-spacer></v-spacer>
            <v-btn class="px-10" outlined color="#27AB9C" @click="CancelMulti()">{{$t('ReviewListBuyer.btnCancel')}}</v-btn>
            <v-btn class="px-10 white--text" color="#27AB9C" @click="editReviewMulti()">{{$t('ReviewListBuyer.btnSuccess')}}</v-btn>
          </v-card-actions>
          <!-- <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn class="px-10" outlined color="#27AB9C" @click="CancelMulti()">ยกเลิก</v-btn>
            <v-btn class="px-10 white--text" color="#27AB9C" @click="editReview()">บันทึก</v-btn>
          </v-card-actions> -->
        <!-- </v-container> -->
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode } from '@/services'
export default {
  components: {},
  data () {
    return {
      titleDateEdit: `${this.$t('ReviewListBuyer.titleDateEdit')}`,
      titleDateSuccess: `${this.$t('ReviewListBuyer.titleDateSuccess')}`,
      titleReviewEdit: `${this.$t('ReviewListBuyer.titleReviewEdit')}`,
      titleReviewSuccess: `${this.$t('ReviewListBuyer.titleReviewSuccess')}`,
      data: [],
      lazy: false,
      paymentNumber: {},
      ModalReviewProduct: false,
      reviewMultiProduct: false,
      productMultiList: [],
      productDetail: [

      ],
      arrayReview: [],
      fileImage: [],
      fileVideo: [],
      uploadFileImage: [],
      uploadFileVideo: [],
      dataUploadImageIndex: 0,
      dataUploadVideoIndex: 0,
      DataImage: [],
      DataVideo: [],
      uploadImageList: [],
      uploadVideoList: [],
      dataUploadImage: [],
      dataUploadVideo: [],
      video: '',
      orderNumber: '',
      actions: '',
      userdetail: '',
      userName: '',
      dataRole: '',
      statusReview: '',
      fileS3: '',
      fileVideoS3: '',
      videoPreviewUrl: null
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    DataVideo (val) {
      // console.log(val, 'val')
    }
  },
  methods: {
    formatDateTime (val) {
      if (!val) return null
      var date = val.split(' ')
      const [year, month, day] = date[0].split('-')
      if (this.$i18n.locale === 'th') {
        const monthName = new Date(`${year}-${month}-01`).toLocaleString(this.$i18n.locale, { month: 'long' })
        return `${day} ${monthName} ${parseInt(year) + 543}`
      } else {
        const monthName = new Date(`${year}-${month}-01`).toLocaleString(this.$i18n.locale === 'en', { month: 'long' })
        return `${day} ${monthName} ${year}`
      }
    },
    clsVideo () {
      this.videoPreviewUrl = null
      this.DataVideo = []
    },
    open (items, ordernumber, actions, role) {
      // console.log('itemsitems', items)
      this.orderNumber = ordernumber
      this.paymentNumber = ordernumber
      this.dataRole = role
      this.ModalReviewProduct = true
      this.statusReview = items.expired_review === '-' ? 'expired' : ''
      this.getReviewList(ordernumber)
      this.getUserDeatil()
    },
    getSingleProduct (items) {
      this.reviewMultiProduct = true
      this.productDetail = items
      this.dataUploadImage = []
      // console.log(items, '**+++++++++*')
      if (items.image_upload !== null && items.image_upload !== '') {
        items.image_upload.forEach(element => {
          this.dataUploadImage.push({ image_data: '', media_path: element.media_path, name: '', id: element.id, media_type: element.media_type })
        })
      }
      // console.log(this.productDetail, '**+++++++++*')
    },
    getUserDeatil () {
      var oneData
      if (localStorage.getItem('oneData') !== null) {
        oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      }
      // console.log('oneData', oneData)
      this.userdetail = oneData.user.username
      var userName = this.userdetail
      this.userName = `${this.$t('ReviewListBuyer.anonymous')} ` + userName.substring(0, 1) + '****' + userName.substring(userName.length - 1)
    },
    backward () {
      this.clearData()
      this.getReviewList(this.orderNumber)
      this.reviewMultiProduct = false
    },
    Cancel () {
      this.clearData()
      this.reviewMultiProduct = false
      this.ModalReviewProduct = false
    },
    CancelMulti () {
      this.clearData()
      this.reviewMultiProduct = false
      this.ModalReviewProduct = false
    },
    clearData () {
      this.productMultiList = []
      this.productDetail = []
      this.dataUploadImage = []
      this.DataImage = []
      this.fileImage = []
    },
    async getReviewList (items) {
      // purchaser
      var data = {
        role_user: this.dataRole,
        order_number: items
      }
      await this.$store.dispatch('actionsCheckLiistReviewProductBuyer', data)
      var res = this.$store.state.ModuleReviewBuyer.stateCheckLiistReviewProductBuyer
      if (res.message === 'Review and comment success.') {
        this.clearData()
        this.productMultiList = res.data.product
      }
    },
    onPickFileMulti () {
      document.getElementById('file_input').click()
    },
    onPickFileVideo () {
      document.getElementById('file_input_video').click()
    },
    async UploadImageMulti () {
      var lengthData = this.dataUploadImage.filter(item => item.media_type === 'image')
      if ((this.DataImage.length + lengthData.length) <= 6) {
        for (let i = 0; i < this.DataImage.length; i++) {
          const element = this.DataImage[i]
          // console.log(element)
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
            const imageSize = element.size / 1024 / 1024
            if (imageSize <= 6) {
              // const base64 = await this.readFileAsBase64(element)
              const url = URL.createObjectURL(element)

              // console.log(this.dataUploadImage, 99)

              var formData = new FormData()
              formData.append('image', element)
              formData.append('type', 'review_image')
              formData.append('order_number', this.orderNumber)

              // const dataImg = { image: [base64.split(',')[1]], type: 'review_image', order_number: this.orderNumber }

              this.$store.commit('openLoader')

              await this.$store.dispatch('actionsUploadToS3', formData)
              const response = await this.$store.state.ModuleShop.stateUploadToS3

              // console.log(response, '***')

              if (response.message === 'List Success.') {
                this.$store.commit('closeLoader')
                this.fileS3 = response.data.list_path[0].path
              }

              this.dataUploadImage.push({
                image_data: this.fileS3,
                media_path: url,
                name: element.name,
                id: '-1',
                media_type: 'image'
              })
              // console.log(this.dataUploadImage, 97978)
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 6 MB', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 1500 })
          }
          // this.productDetail.file_data = this.fileImage
          this.productDetail.image_upload = this.dataUploadImage
        }
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่รูปได้ 6 รูป', showConfirmButton: false, timer: 2500 })
      }
    },
    readFileAsBase64 (file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader()
        reader.onload = () => resolve(reader.result)
        reader.onerror = reject
        reader.readAsDataURL(file)
      })
    },
    async UploadVideoMulti (event) {
      var lengthData = this.dataUploadImage.filter(item => item.media_type === 'video')
      // console.log(lengthData.length)
      if (lengthData.length < 1) {
        // console.log(event)
        const file = event
        if (!file) return
        // console.log(file, 'file')
        if (file.type === 'video/mp4' && file.size <= 20 * 1024 * 1024) {
          // const base64 = await this.readFileAsBase64(file)
          // var sendBase64 = base64
          this.videoPreviewUrl = URL.createObjectURL(file)
          this.fileVideoS3 = file
          var formData = new FormData()
          formData.append('image', file)
          formData.append('type', 'review_video')
          formData.append('order_number', this.orderNumber)
          // console.log(formData, 'fromData')
          // this.fileVideoS3 = file
          // const dataVideo = { image: [sendBase64.split(',')[1]], type: 'review_video', order_number: this.orderNumber }

          this.$store.commit('openLoader')

          await this.$store.dispatch('actionsUploadToS3', formData)
          const response = await this.$store.state.ModuleShop.stateUploadToS3

          // console.log(response, '***')

          if (response.message === 'List Success.') {
            this.$store.commit('closeLoader')
            this.fileVideoS3 = response.data.list_path[0].path
          }
          this.dataUploadImage.push({
            image_data: this.fileVideoS3,
            media_path: this.videoPreviewUrl,
            name: file.name,
            id: '-1',
            media_type: 'video'
          })
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่วิดีโอที่มีขนาดน้อยกว่า 20 MB', showConfirmButton: false, timer: 1500 })
        }
        this.productDetail.image_upload = this.dataUploadImage
        // console.log(this.productDetail, 97978)
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาตรวจสอบอีกครั้ง ระบบสามารถใส่วิดีโอได้ 1 วิดีโอ', showConfirmButton: false, timer: 2500 })
      }
    },
    // UploadVideoMulti () {
    //   for (let i = 0; i < this.DataVideo.length; i++) {
    //     const element = this.DataVideo[i]
    //     if (element.type === 'video/mp4') {
    //       const videoSize = element.size / 1024 / 1024
    //       if (videoSize < 20) {
    //         const reader = new FileReader()
    //         reader.readAsDataURL(element)
    //         reader.onload = () => {
    //           var resultReader = reader.result
    //           var url = URL.createObjectURL(element)
    //           this.dataUploadVideo.push({
    //             image_data: resultReader,
    //             url: url,
    //             name: this.DataVideo[i].name
    //           })
    //           this.fileVideo.push({
    //             file_data: this.DataVideo[i]
    //           })
    //         }
    //       } else {
    //         this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่วิดีโอที่มีขนาดน้อยกว่า 20 MB', showConfirmButton: false, timer: 1500 })
    //       }
    //     } else {
    //       this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์วิดีโอ', showConfirmButton: false, timer: 1500 })
    //     }
    //   }
    //   this.productDetail.file_video = this.fileVideo
    //   this.productDetail.review_video = this.dataUploadVideo
    // },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImageMulti (index2, val) {
      // console.log(index2, val)
      // this.DataImage.splice(index2, 1)
      // this.dataUploadImage.splice(index2, 1)
      this.productDetail.image_upload.splice(index2, 1)
      this.dataUploadImage = this.productDetail.image_upload
      // console.log(this.productDetail)
    },
    // RemoveVideoMulti (index2, val) {
    //   this.productDetail.review_video.splice(index2, 1)
    //   this.productDetail.file_video.splice(index2, 1)
    // },
    editReviewMulti () {
      var imageArr = []
      var imgData
      var medieType
      // console.log(this.productDetail, 'this.productDetail')
      for (let i = 0; i < this.productDetail.image_upload.length; i++) {
        if ('image_data' in this.productDetail.image_upload[i]) {
          // console.log(111)
          imgData = this.productDetail.image_upload[i].image_data
          medieType = this.productDetail.image_upload[i].media_type
          imageArr.push({ id: this.productDetail.image_upload[i].id, image_data: imgData === undefined ? '' : imgData, media_type: medieType })
        } else {
          // console.log(222)
          imageArr.push({ id: this.productDetail.image_upload[i].id, image_data: '', media_type: medieType })
        }
      }
      if (this.productDetail.status === 'waiting_review') {
        const createData = {
          order_number: this.orderNumber,
          product_id: this.productDetail.product_id,
          stars: this.productDetail.stars,
          comment: this.productDetail.comment,
          image_upload: imageArr,
          role_user: this.dataRole,
          hide_username: this.productDetail.hide_username === 'yes' ? this.productDetail.hide_username : 'no',
          video_upload: ''
        }
        this.sendCreateApi(createData)
      } else {
        const editData = {
          feedback_id: this.productDetail.feedback_id,
          stars: this.productDetail.stars,
          comment: this.productDetail.comment,
          image_upload: imageArr,
          role_user: this.dataRole,
          hide_username: this.productDetail.hide_username === 'yes' ? this.productDetail.hide_username : 'no',
          video_upload: ''
        }
        this.sendEditApi(editData)
      }
    },
    async sendEditApi (data) {
      await this.$store.dispatch('actionsEditReviewProductBuyer', data)
      var res = await this.$store.state.ModuleReviewBuyer.stateEditReviewProductBuyer
      if (res.message === 'Edit review and comment success.') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'บันทึกการแก้ไขการประเมินความพึงพอใจสินค้าสำเร็จ'
        })
        this.$EventBus.$emit('SentGetReview')
        this.productMultiList = []
        this.productDetail = []
        this.rating = 0
        this.comment = ''
        this.reviewMultiProduct = false
        this.ModalReviewProduct = false
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      }
    },
    async sendCreateApi (data) {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsCreateReviewProductBuyer', data)
      var res = await this.$store.state.ModuleReviewBuyer.stateCreateReviewProductBuyer
      if (res.message === 'Review and comment success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'บันทึกการประเมินความพึงพอใจสินค้าสำเร็จ'
        })
        this.$EventBus.$emit('SentGetReview')
        this.$EventBus.$emit('getItemNoti')
        this.productMultiList = []
        this.productDetail = []
        this.rating = 0
        this.comment = ''
        this.reviewMultiProduct = false
        this.ModalReviewProduct = false
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR',
          text: `${res.message}`
        })
      }
    }

  }
}
</script>
<style scoped>
.imageshow {
  width: 80px;
  height: 80px;
  cursor: pointer;
}
.v-text-field input {
  font-size: 0.9em;
}
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: rgb(111,183,87)
}
.input_text {
  height: 60px;
  opacity: 1;
}
.captionSku {
  font-size: 12px;
}
.labelInputSize {
  font-size: 16px;
}
.bgRating {
  background-color: red;
}
::v-deep .v-btn {
  text-transform: none;
}
</style>
