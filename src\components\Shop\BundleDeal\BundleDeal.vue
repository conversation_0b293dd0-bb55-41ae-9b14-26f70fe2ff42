<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row>
        <v-col>
          <v-icon v-if="MobileSize" color="#27AB9C" class="mr-2 mr-auto" @click="backtoUserMenu()">mdi-chevron-left</v-icon><span class="pb-0" style="font-weight: 600; line-height: 32px;" :style="MobileSize ? 'font-size: 18px;' : 'font-size: 24px;'">Bundle Deal</span>
        </v-col>
        <v-col style="display: flex; justify-content: flex-end;">
        <v-btn style="border: 1px solid #27AB9C" outlined rounded @click="createBundle()"><span style="font-size: 16px; color: #27AB9C; ">สร้าง</span></v-btn>
      </v-col>
      </v-row>
      <v-row>
        <v-col cols="12">
          <!-- <a-tabs>
            <a-tab-pane>เร็ว ๆ นี้</a-tab-pane>
            <a-tab-pane>กำลังดำเนินการ</a-tab-pane>
            <a-tab-pane>หมดอายุ</a-tab-pane>
          </a-tabs> -->
          <a-tabs @change="SelectTabs">
            <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
            <a-tab-pane key="upcoming"><span slot="tab">เร็ว ๆ นี้ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countUpcoming.length }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="active"><span slot="tab">กำลังดำเนินการ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countActive.length }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="expired"><span slot="tab">หมดอายุ <a-tag color="#f44336" style="border-radius: 8px;">{{ countExpired.length }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col>
          <v-data-table
          :headers="headersMain"
          :items="listBundle"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          height="100%"
          style="white-space: nowrap; width: 100%;"
          >
            <template v-slot:[`item.date`]="{ item }">
              <span>{{ formatDateService(item.start_date) + ' - ' + formatDateService(item.end_date) }}</span>
          </template>
          </v-data-table>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      StateStatus: 'upcoming',
      headersMain: [
        { text: 'ชื่อ Bundle Deal', value: 'name', width: '', sortable: false, align: 'start', class: 'backgroundTable fontTable--text' },
        { text: 'ประเภทโปรโมชั่น', filterable: false, value: 'discount_type', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'รายการสินค้า', filterable: false, value: '', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'ระยะเวลา', filterable: false, value: 'date', sortable: false, align: 'start', width: '', class: 'backgroundTable fontTable--text' },
        { text: 'ดำเนินการ', filterable: false, sortable: false, value: '', align: 'start', class: 'backgroundTable fontTable--text' }
      ],
      seller_shop_id: '',
      countUpcoming: 0,
      countActive: 0,
      countExpired: 0,
      listBundle: []
    }
  },
  created () {
    this.$EventBus.$emit('changeNav')
    this.seller_shop_id = JSON.parse(localStorage.getItem('shopSellerID'))
    this.listBundleDeal()
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/bundleDealMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/bundleDeal' }).catch(() => {})
      }
    }
  },
  methods: {
    formatDateService (dateStr) {
      const [datePart, timePartRaw] = dateStr.split('T')
      const timePart = timePartRaw.replace('.000Z', '') // ตัด .000Z ออก

      const [year, month, day] = datePart.split('-')
      const thaiMonth = new Date(`${year}-${month}-${day}`).toLocaleString('th-TH', { month: 'long' })
      const buddhistYear = parseInt(year) + 543

      const [hour, minute, second] = timePart.split(':')

      return `${parseInt(day)} ${thaiMonth} ${buddhistYear} เวลา ${hour}.${minute}.${second} น.`
    },
    SelectTabs (item) {
      console.log(item, '*****')
      this.StateStatus = item
      this.listBundleDeal()
    },
    async listBundleDeal () {
      this.$store.commit('openLoader')
      var data = {
        // seller_shop_id: JSON.parse(localStorage.getItem('shopSellerID')),
        seller_shop_id: 186,
        status: this.StateStatus,
        limit: 10,
        page: 1
      }
      await this.$store.dispatch('actionListBundle', data)
      var res = await this.$store.state.ModuleShop.stateListBundle
      if (res.code === 200) {
        this.listBundle = res.data.bundle_lists
        this.countUpcoming = this.listBundle.filter(item => item.status === 'upcoming')
        this.countActive = this.listBundle.filter(item => item.status === 'active')
        this.countExpired = this.listBundle.filter(item => item.status === 'expired')
        console.log(this.countExpired)
        this.$store.commit('closeLoader')
      }
    },
    backtoUserMenu () {
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      this.$router.push({ path: '/sellerMobile?ShopID=' + shopDetail.id + '&ShopName=' + shopDetail.name }).catch(() => {})
    },
    createBundle () {
      if (this.MobileSize) {
        this.$router.push({ path: '/createBundleDealMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/createBundleDeal' }).catch(() => {})
      }
    }
  }
}
</script>

<style scoped>
.background_product {
  background-color:#FFFFFF;
}
.background_productMobile {
  background-color:#FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
</style>
