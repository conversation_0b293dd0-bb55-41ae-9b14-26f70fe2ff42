########## start env prd OLD #############
NODE_ENV = production
VUE_APP_DOMAIN = https://nexgencommerce.one.th/
VUE_APP_API_DASHBOARD_REPORT = http://http://**************:5000/api
VUE_APP_API = http://**************/api
VUE_APP_WEB_FILE = http://*************
VUE_APP_BACK_END = https://nexgencommerce.one.th/api/backend/
VUE_APP_IMAGE = https://nexgencommerce.one.th/backend/
VUE_APP_WEB_RESET_PASSWORD = http://http:///**************/user_reset_password
VUE_APP_WEB_EDIT_PROFILE = http://http:///**************/user_edit_profile
VUE_APP_WEB_LOGOUT = http://http:///**************/logout
VUE_APP_REDIRECT = https://nexgencommerce.one.th/api/backend/api/get_redirect?status=prd
VUE_APP_CALLBACK = https://nexgencommerce.one.th/api/backend/api/callback_oauth_prd
VUE_APP_BACK_END2 = https://nexgencommerce.one.th/api/backend_2/
VUE_APP_NEW_BACK_END2 = https://nexgencommerce.one.th/api/backend_2/
VUE_APP_BACK_END3 = https://nexgencommerce.one.th/backend_3/
VUE_APP_BACK_END4 = https://nexgencommerce.one.th/backend_4/
VUE_APP_IMAGE_PRODUCT_DETAIL = https://s3gw.inet.co.th:8082/e-pro-uat/
VUE_APP_LOGOUT = https://one.th/api/oauth/logout?redirect_url=https%3A%2F%2Fnexgencommerce.one.th%2F
VUE_APP_NEW_BACK_END = https://nexgencommerce.one.th/api/backend/
VUE_APP_API_DOCUMENT_TYPE = https://budgeting.inet.co.th/
VUE_APP_API_PR_PORTAL = https://budgeting.inet.co.th/
VUE_APP_API_PR_PORTAL_PRODUCTION = https://budgeting.inet.co.th/
VUE_APP_PR_PORTAL_CLIENT_ID = '1'
VUE_APP_PR_PORTAL_CLIENT_SECRET = 'tSOcvAkbBDKMbAN3JRKl8HtuPhq9qIvw'
VUE_APP_PR_PORTAL_CLIENT_ID_GET_DOC = '2'
VUE_APP_PR_PORTAL_CLIENT_SECRET_GET_DOC = 'z6mLjgQk4YjKYem1Qo6fYR9Us11uMQ8E'
VUE_APP_PR_PORTAL_SECRET_KEY = 'asd5sa4w6e46sd5aWERd56'
VUE_APP_WLC_ADMIN_UAT = https://uat-adminjobedu.one.th/adminRef?token
VUE_APP_WLC_ADMIN = https://adminworklifecycle.one.th/adminRef?token
VUE_APP_CHAT_PLUGIN = https://chat-plugin.one.th/backend/api/v1
VUE_APP_ISHIP_URL = https://app.iship.cloud/
VUE_APP_REDIRECT_SSO = https://nexgencommerce.one.th/api/backend/api/erp/oauth/verify_code
########## end env prd  #############

########## start env prd OLD #############
# NODE_ENV = production
# VUE_APP_DOMAIN = https://inet-b2b.one.th/
# VUE_APP_API_DASHBOARD_REPORT = http://http://**************:5000/api
# VUE_APP_API = http://**************/api
# VUE_APP_WEB_FILE = http://*************
# VUE_APP_BACK_END = https://inet-b2b.one.th/api/backend/
# VUE_APP_IMAGE = https://inet-b2b.one.th/backend/
# VUE_APP_WEB_RESET_PASSWORD = http://http:///**************/user_reset_password
# VUE_APP_WEB_EDIT_PROFILE = http://http:///**************/user_edit_profile
# VUE_APP_WEB_LOGOUT = http://http:///**************/logout
# VUE_APP_REDIRECT = https://inet-b2b.one.th/api/backend/api/get_redirect?status=prd
# VUE_APP_CALLBACK = https://inet-b2b.one.th/api/backend/api/callback_oauth_prd
# VUE_APP_BACK_END2 = https://inet-b2b.one.th/api/backend_2/
# VUE_APP_NEW_BACK_END2 = https://inet-b2b.one.th/api/backend_2/
# VUE_APP_BACK_END3 = https://internaldevsitepanit.one.th/backend_3/
# VUE_APP_BACK_END4 = https://internaldevsitepanit.one.th/backend_4/
# VUE_APP_IMAGE_PRODUCT_DETAIL = https://devinet-eprocurement.one.th/static/
# VUE_APP_LOGOUT = https://one.th/api/oauth/logout?redirect_url=https%3A%2F%2Finet-b2b.one.th%2F
# VUE_APP_NEW_BACK_END = https://inet-b2b.one.th/api/backend/
# VUE_APP_API_DOCUMENT_TYPE = https://budgeting.inet.co.th/
# VUE_APP_API_PR_PORTAL = https://budgeting.inet.co.th/
# VUE_APP_API_PR_PORTAL_PRODUCTION = https://budgeting.inet.co.th/
# VUE_APP_PR_PORTAL_CLIENT_ID = '1'
# VUE_APP_PR_PORTAL_CLIENT_SECRET = 'tSOcvAkbBDKMbAN3JRKl8HtuPhq9qIvw'
########## end env prd  #############

########## start env UAT #############
# NODE_ENV = production
# VUE_APP_DOMAIN = https://uatinet-eprocurement.one.th/
# VUE_APP_API_DASHBOARD_REPORT = http://http://**************:5000/api
# VUE_APP_API = http://**************/api
# VUE_APP_WEB_FILE = http://*************
# VUE_APP_BACK_END = https://uatinet-eprocurement.one.th/api/backend/
# VUE_APP_IMAGE = https://uatinet-eprocurement.one.th/backend/
# VUE_APP_WEB_RESET_PASSWORD = http://http:///**************/user_reset_password
# VUE_APP_WEB_EDIT_PROFILE = http://http:///**************/user_edit_profile
# VUE_APP_WEB_LOGOUT = http://http:///**************/logout
# VUE_APP_REDIRECT = https://uatinet-eprocurement.one.th/api/backend/api/get_redirect?status=prd
# VUE_APP_CALLBACK = https://uatinet-eprocurement.one.th/api/backend/api/callback_oauth_prd
# VUE_APP_BACK_END2 = https://uatinet-eprocurement.one.th/api/backend_2/
# VUE_APP_NEW_BACK_END2 = https://uatinet-eprocurement.one.th/api/backend_2/
# VUE_APP_BACK_END3 = https://internaldevsitepanit.one.th/backend_3/
# VUE_APP_BACK_END4 = https://internaldevsitepanit.one.th/backend_4/
# VUE_APP_IMAGE_PRODUCT_DETAIL = https://s3gw.inet.co.th:8082/e-pro-uat/
# VUE_APP_LOGOUT = https://one.th/api/oauth/logout?redirect_url=https%3A%2F%2Fuatinet-eprocurement.one.th%2F
# VUE_APP_NEW_BACK_END = https://uatinet-eprocurement.one.th/api/backend/
# VUE_APP_API_DOCUMENT_TYPE = https://pr-portal.inet.co.th/
# VUE_APP_API_PR_PORTAL = https://pr-portal.inet.co.th/
########## end env UAT  #############

########## start env UAT For test #############
# NODE_ENV = production
# VUE_APP_DOMAIN = https://uat-nexgencommerce.one.th/
# VUE_APP_API_DASHBOARD_REPORT = http://http://**************:5000/api
# VUE_APP_API = http://**************/api
# VUE_APP_WEB_FILE = http://*************
# VUE_APP_BACK_END = https://uat-nexgencommerce.one.th/api/backend/
# VUE_APP_IMAGE = https://uat-nexgencommerce.one.th/backend/
# VUE_APP_WEB_RESET_PASSWORD = http://http:///**************/user_reset_password
# VUE_APP_WEB_EDIT_PROFILE = http://http:///**************/user_edit_profile
# VUE_APP_WEB_LOGOUT = http://http:///**************/logout
# VUE_APP_REDIRECT = https://uat-nexgencommerce.one.th/api/backend/api/get_redirect?status=prd
# VUE_APP_CALLBACK = https://uat-nexgencommerce.one.th/api/backend/api/callback_oauth_prd
# VUE_APP_BACK_END2 = https://uat-nexgencommerce.one.th/api/backend_2/
# VUE_APP_NEW_BACK_END2 = https://uat-nexgencommerce.one.th/api/backend_2/
# VUE_APP_BACK_END3 = https://internaldevsitepanit.one.th/backend_3/
# VUE_APP_BACK_END4 = https://internaldevsitepanit.one.th/backend_4/
# VUE_APP_IMAGE_PRODUCT_DETAIL = https://devinet-eprocurement.one.th/static/
# VUE_APP_LOGOUT = https://one.th/api/oauth/logout?redirect_url=https%3A%2F%2Fdevinet-eprocurement.one.th%2F
# VUE_APP_NEW_BACK_END = https://uat-nexgencommerce.one.th/api/backend/
# VUE_APP_API_DOCUMENT_TYPE = https://budgeting.inet.co.th/
# VUE_APP_API_PR_PORTAL = https://budgeting.inet.co.th/
# VUE_APP_API_PR_PORTAL_PRODUCTION = https://budgeting.inet.co.th/
# VUE_APP_PR_PORTAL_CLIENT_ID = '1'
# VUE_APP_PR_PORTAL_CLIENT_SECRET = 'tSOcvAkbBDKMbAN3JRKl8HtuPhq9qIvw'
########## start env UAT For test #############
