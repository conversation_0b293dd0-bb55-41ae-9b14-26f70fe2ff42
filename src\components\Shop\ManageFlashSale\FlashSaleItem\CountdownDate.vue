<template>
  <div id="countdown">
    <ul>
      <li>วัน<span id="days">{{ days.toString().padStart(2, '0') }}</span></li>
      <li><span style="color: #FFCB49; padding: 0px; font-weight: 700;">:</span></li>
      <li>ชั่วโมง<span id="hours">{{ hours.toString().padStart(2, '0') }}</span></li>
      <li><span style="color: #FFCB49; padding: 0px; font-weight: 700;">:</span></li>
      <li>นาที<span id="minutes">{{ minutes.toString().padStart(2, '0') }}</span></li>
      <li><span style="color: #FFCB49; padding: 0px; font-weight: 700;">:</span></li>
      <li>วินาที<span id="seconds">{{ seconds.toString().padStart(2, '0') }}</span></li>
    </ul>
  </div>
</template>

<script>
export default {
  props: ['endDate', 'page'],
  components: {
  },
  data () {
    return {
      days: 0,
      hours: 0,
      minutes: 0,
      seconds: 0,
      isEnded: null,
      distance: 0
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    this.tick()
    this.timer = setInterval(this.tick.bind(this), 1000)
  },
  watch: {
    isEnded (val) {
      if (val === true) {
        this.tick()
        this.timer = setInterval(this.tick.bind(this), 1000)
      }
    }
  },
  destroy () {
    clearInterval(this.timer)
  },
  created () {
  },
  methods: {
    updateRemaining (distance) {
      this.days = Math.floor(distance / (1000 * 60 * 60 * 24))
      this.hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
      this.minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
      this.seconds = Math.floor((distance % (1000 * 60)) / 1000)
    },
    tick () {
      const currentTime = new Date()
      const distance = Math.max(this.endDate - currentTime, 0)
      this.distance = distance
      this.updateRemaining(distance)
      if (distance === 0) {
        clearInterval(this.timer)
        this.isEnded = true
        if (this.isEnded) {
          if (this.page === 'list') {
            this.$EventBus.$emit('getNewDataFlashSaleList')
          } else {
            this.$EventBus.$emit('getNewDataFlashSaleDetail')
          }
        }
      }
    }
  }
}
</script>
<style scoped>
#countdown {
  background: #333333;
  border-radius: 8px;
}
ul {
  padding: 2px;
  margin: 0px;
}
li {
  display: inline-block;
  font-size: 20px;
  list-style-type: none;
  padding: 7px;
  color: #FFFFFF;
  text-transform: uppercase;
}
li span {
  display: block;
  color: #FFFFFF;
  font-size: 20px;
}
</style>
