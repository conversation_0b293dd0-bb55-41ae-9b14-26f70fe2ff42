<template>
  <div>
    <a-row type="flex">
      <a-col :span="24">
        <ShopProduct />
      </a-col>
      <a-col :span="24">
        <ManageShop />
      </a-col>
    </a-row>
  </div>
</template>
<script>
export default {
  components: {
    ShopProduct: () => import('@/components/Shop/ShopProduct/ShopProduct'),
    ManageShop: () => import('@/components/Shop/ManageShop')
  },
  async created () {
    this.$EventBus.$emit('CheckFooter')
    this.$EventBus.$emit('getPath')
    var res = await this.axios.get('http://*************:8088/api/category/get_all_category')
    var MainCategory = ''
    var SubCategory
    var ResultCategory = []
    res.data.data.forEach(element => {
      MainCategory = element.main_category
      SubCategory = Object.values((Object.values(element.sub_category))[0])
      SubCategory.forEach(ForSubCate => {
        ForSubCate.forEach(ForSubSubCate => {
          ResultCategory.push({
            category: `${MainCategory}>${ForSubSubCate}`
          })
        })
      })
    })
  }
}
</script>
