import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async transaction (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}panit/dashboard/transection`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Top10Sellers (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}panit/dashboard/top10sellers`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Top10Buyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}panit/dashboard/top10buyers`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async Top10Purchaser (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END4}panit/dashboard/top10purchasers`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListOrderApprove (data) {
    const auth = await GetToken()
    // const auth = {
    //   headers: { Authorization: 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' }
    // }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver/list_approve_order`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async IframeOrderApprove (data) {
    const auth = await GetToken()
    // const auth = {
    //   headers: { Authorization: 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' }
    // }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver/iframe_approve_order`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailOrderApprove (data) {
    const auth = await GetToken()
    // const auth = {
    //   headers: { Authorization: 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' }
    // }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver/detail_list_approve_order`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListPurchaserApproveOrder (data) {
    const auth = await GetToken()
    // const auth = {
    //   headers: { Authorization: 'Bearer **********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************' }
    // }
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/approver/list_purchaser_approve_order`, data, auth)
      // console.log(response.data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // onlineShopping (gracz)
  async ListGSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_g_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListSelectNewGSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/list_select_new_g_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AddGAdminSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/add_g_admin_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteGSellerShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/delete_g_seller_shop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumDocAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/allSumaryDocument`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumTrendAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/allSummaryTrendQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumBarQTAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/allSummaryDocumentBarQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumBarSOAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/allSummaryDocumentBarSO`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumBarPRAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/allSummaryDocumentBarPR`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumBarPOAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashboard/allSummaryDocumentBarPO`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetAllSumTopFiveQTAdmin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}dashborad/allSummaryTopFiveQT`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async MediaUpload (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}flash_sale/media_upload`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async MediaDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}flash_sale/media`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPopUp (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_popup`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdatePopUp (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/admin_platform/update_popup`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetPopUpHomepage (data) {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_BACK_END}api/get_popup_homepage`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ServicePartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/admin/product_service_details`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateServicePartner (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/admin/update_status_service`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminAddUserManual (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/manualV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminDeleteUserManual (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/delete_manualV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminUserManualDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/manual_detailV2`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminAddLinkSeminar (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/add_meeting_link`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminLinkSeminar (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/meeting_links`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AdminDeleteLinkSeminar (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}partner/status_meeting_links`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
