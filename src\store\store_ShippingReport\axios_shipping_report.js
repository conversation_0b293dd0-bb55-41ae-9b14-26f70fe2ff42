import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  // จำนวนใน v-tabs
  async SummaryOrderByShopID (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobilyst/sumaryOrderByShopID`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AllOrderByShopID (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobilyst/allOrderByShopID`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async CallRider (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobilyst/callRider`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
