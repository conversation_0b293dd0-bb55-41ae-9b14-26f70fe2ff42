<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายชื่อนิติบุคคลของระบบ</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> รายชื่อนิติบุคคลของระบบ</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาจากชื่อนิติบุคคลของระบบ" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="listBusinessAdmin.length !== 0 && (!MobileSize && !IpadSize)">รายชื่อนิติบุคคลของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="listBusinessAdmin.length !== 0 && (MobileSize || IpadSize)">รายชื่อร้านค้านิติบุคคลของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="tab === 0 ? listBusinessAdminActive : tab === 1 ? listBusinessAdminPending : tab === 2 ? listBusinessAdminReject : listBusinessAdminReject"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อนิติบุคคลของระบบ"
                no-data-text="ไม่มีชื่อนิติบุคคลของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ index }">
                    {{ index + 1 }}
                  </template>
                  <template v-slot:[`item.first_name_th`]="{ item }">
                    {{ item.first_name_th !== null ? item.first_name_th : '-' }}
                  </template>
                  <template v-slot:[`item.company_name`]="{ item }">
                    {{ item.company_name !== null ? item.company_name : '-' }}
                  </template>
                  <template v-slot:[`item.shop_name`]="{ item }">
                    {{ item.shop_name !== null ? item.shop_name : '-' }}
                  </template>
                  <template v-slot:[`item.actions`]="{ item }" >
                    <v-btn @click="MyDetailCompany(item)" text color="#27AB9C">เข้าสู่นิติบุคคล</v-btn>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <!-- <CreateAdminModal ref="CreateAdminModal" />
      <DetailAdminModal ref="DetailAdminModal" /> -->
    </v-card>
  </v-container>
</template>

<script>
import { Encode } from '@/services'
export default {
  data () {
    return {
      search: '',
      listBusinessAdmin: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '20', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เลขประจำตัวผู้เสียภาษี', value: 'id_card_num', width: '80', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อนิติบุคคล', value: 'first_name_th', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'company_name', width: '200', sortable: false, filterable: false, class: 'backgroundTable fontTable--text' },
        { text: 'เจ้าของนิติบุคคล', value: 'owner_name', width: '120', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการ', value: 'actions', filterable: false, width: '120', sortable: false, class: 'backgroundTable fontTable--text' }
      ],
      tab: 0,
      listBusinessAdminActive: [],
      listBusinessAdminPending: [],
      listBusinessAdminReject: [],
      countApprove: 0,
      countPending: 0,
      countReject: 0
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/BusinessManageMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'BusinessManage')
        this.$router.push({ path: '/BusinessManage' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    // this.$EventBus.$on('createAdminPanitSuccess', this.getShopData)
    // this.$EventBus.$on('listBusinessAdmindeleteAdminPanitSuccess', this.getShopData)
    // this.$EventBus.$on('editAdminPanitSuccess', this.getShopData)
    if (localStorage.getItem('oneData') !== null) {
      this.getShopData()
    //   this.AuthorityUser()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  methods: {
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    // async AuthorityUser () {
    //   await this.$store.dispatch('actionsAuthorityUser')
    //   var response = await this.$store.state.ModuleUser.stateAuthorityUser
    //   if (response.message === 'Get user detail success') {
    //     if (response.data.current_role_user.super_admin_platform === true) {
    //       this.isSuperAdmin = true
    //     } else {
    //       this.isSuperAdmin = false
    //     }
    //   }
    // },
    async getShopData () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListBusinessAdmin')
      var response = await this.$store.state.ModuleAdminManage.stateListBusinessAdmin
      // console.log('list business ====>', response)
      if (response.message === 'List Success.') {
        this.$store.commit('closeLoader')
        this.listBusinessAdmin = [...response.data]
        this.listBusinessAdminActive = this.listBusinessAdmin.filter(status => status.status_approve === 'approve')
        this.countApprove = this.listBusinessAdminActive.length
        this.listBusinessAdminPending = this.listBusinessAdmin.filter(status => status.status_approve === 'pending')
        this.countPending = this.listBusinessAdminPending.length
        this.listBusinessAdminReject = this.listBusinessAdmin.filter(status => status.status_approve === 'reject')
        this.countReject = this.listBusinessAdminReject.length
        // console.log(this.listBusinessAdmin)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    async MyDetailCompany (val) {
      this.$store.commit('openLoader')
      var data = {
        company_id: val.company_id
      }
      // console.log(val.company_id)
      await this.$store.dispatch('actionsAddAdminCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateAddAdminCompany
      if (response.result === 'SUCCESS') {
        this.$EventBus.$emit('getUserDetailMP')
        this.$EventBus.$emit('getCompany')
        var Data = {
          company_id: val.company_id
        }
        await this.$store.dispatch('actionsDetailCompany', Data)
        var response1 = await this.$store.state.ModuleAdminManage.stateDetailCompany
        // console.log(response1.data)
        localStorage.setItem('CompanyData', Encode.encode(response1.data))
        localStorage.setItem('company_id', val.company_id)
        this.$store.commit('closeLoader')
        if (!this.MobileSize) {
          this.$router.push({ path: '/detailCompany' }).catch(() => {})
        } else {
          this.$router.push({ path: '/companyMobile' }).catch(() => {})
        }
      } else {
        this.$store.commit('closeLoader')
      }
    },
    selectTab (val) {
      this.search = ''
      this.tab = val
      // console.log(this.tab, '5555')
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
