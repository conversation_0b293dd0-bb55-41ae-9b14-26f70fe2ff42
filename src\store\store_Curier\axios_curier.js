import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    // console.log('ONEDATA NAAAa', oneData)
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  }
}

export default {
  async CallCurier (val) {
    const auth = await GetToken()
    // const data = val.dataShop
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END3}mobilyst/call_courier`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetCurierAll (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}mobilyst/getOrderByShopID`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async callMutiCourier (val) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END3}mobilyst/callMutiCourier`, val, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
