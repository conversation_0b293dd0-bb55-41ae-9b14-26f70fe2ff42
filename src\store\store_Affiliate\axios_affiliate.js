import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  // Consent ของโปรแกรม Affiliate
  async AffiliateConsent (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/consent`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateBuyerCreateDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/createBuyerDetail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ร้านค้าเข้าร่วมโปรแกรม Affiliate
  async SellerJoinAffiliate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/sellerJoinAffiliate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateConfirmJoin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/buyerJoinAffiliate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เลือกสินค้าเข้าร่วมโปรแกรม Affiliate
  async SelectProductAffiliate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/seller/selectProductAffiliate`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateUpdatePayment (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/updateBuyerPayment`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เพิ่มสินค้า affiliate
  async AddProductAffiliate (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/addProduct`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateUpdateSocail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/updateBuyerSocial`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateShowDetailBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/listBuyerDetail`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateShowProduct (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/offer/Product`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateGenerateShortUrl (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/generateShortUrl`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // โชว์สินค้า affiliate ร้านค้า
  async SeletedProductList (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/seller/seletedProductList`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // ลิสต์รายชื่อผู้เข้าร่วม
  async ListUserJoin (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/seller/listBuyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateListShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/buyer/listShop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateSearchShop (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/searchShop`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // อนุมัติผู้เข้าร่วม
  async ApproveBuyer (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/seller/approveBuyer`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateBuyerJoinSeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/buyer/joinSeller`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateProductBySeller (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}affiliate/offer/ProductBySeller `, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateListBank () {
    try {
      var response = await axios.get(`${process.env.VUE_APP_NEW_BACK_END}api/list_category_bank `)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiliateAutoApprove (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}affiliate/seller/autoApprove`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DetailUserEKYC () {
    const auth = await GetToken()
    try {
      var response = await axios.get(`${process.env.VUE_APP_NEW_BACK_END2}users/account`, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AffiListProductByCategory (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}affiliate/seller/list_product_by_category`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchProductAffiliate (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}search/product/affiliate`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SearchShopProductAffiliate (data) {
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}search/product/affiliate_shop`, data)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async AutoJoin () {
    const auth = await GetToken()
    console.log(auth)
    try {
      var response = await axios.post(`${process.env.VUE_APP_NEW_BACK_END2}affiliate/buyer/autoApprove`, '', auth)
      console.log(response)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
