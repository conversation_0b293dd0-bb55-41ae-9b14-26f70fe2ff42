<template>
  <v-container :class="MobileSize ? 'mt-2' : ''">
    <v-card width="100%" height="100%" elevation="0" class="mb-4 mt-3">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 32px; color: #333333;" class="pb-0" v-if="!MobileSize">จัดการบริษัท & ร้านค้า</v-card-title>
      <v-card-title style="font-weight: 700; font-size: 18px; line-height: 32px; color: #333333;" class="pb-0" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon> จัดการบริษัท & ร้านค้า</v-card-title>

      <v-divider class="my-4"></v-divider>

      <v-card-text class="px-2">
        <!-- <v-row dense style="padding-bottom: 10px;">
          <v-col cols="12" md="6" sm="8">
            <v-text-field v-model="searchCompany" dense hide-details outlined placeholder="ค้นหาจากชื่อบริษัทหรือสาขา" style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="6" sm="4" xs="12" align="end" class="py-0">
            <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
              @click="createPositionCompanyOpen()" dark>
              <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มสาขาบริษัท
            </v-btn>
          </v-col>
        </v-row> -->
        <v-row dense style="padding-bottom: 10px;">
          <v-col cols="12" md="6" sm="8">
            <v-text-field v-model="searchShop" dense hide-details outlined placeholder="ค้นหาจากชื่อร้านค้า" style="border-radius: 8px;">
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <!-- <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
              @click="createPositionOpen()" dark>
            <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>เพิ่มสาขาร้านค้า
          </v-btn> -->
          <v-col cols="12" md="6" sm="4" xs="12" align="end" class="py-0" v-if="manageBusiness === 'yes' && shopList.length < 3">
            <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
              @click="createPositionShopOpen()" dark>
              <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>สร้างสาขาร้านค้า
            </v-btn>
          </v-col>
          <v-col cols="12" md="6" sm="4" xs="12" align="end" class="align-center" v-else-if="manageBusiness === 'yes' && shopList.length >= 3">
            <v-row>
              <v-col cols="12">
                <v-btn :block="MobileSize" color="#27AB9C" style="font-weight: bold; font-size: 16px; line-height: 30px;"
                  @click="createPositionShopOpen()" disabled>
                  <v-icon left small style="width: 13.33px; height: 13.33px;">mdi-plus-circle-outline</v-icon>สร้างสาขาร้านค้า
                </v-btn>
              </v-col>
            </v-row>
            <v-row>
              <v-col cols="12" class="py-0">
                <span style="color: red;">* คุณไม่สามารถสร้างสาขาร้านค้าได้เนื่องจากมีสาขาร้านค้าเกิน 2 สาขา</span>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
        <v-row dense no-gutters class="mb-2">
          <v-col cols="12" class="py-0 mb-0">
            <a-tabs v-model="activeTab" @change="SelectDetailUser" :show-arrows="IpadSize || IpadProSize">
              <a-tab-pane :key="0">
                <span slot="tab">บริษัท <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countCompany }}</a-tag></span>
              </a-tab-pane>
              <a-tab-pane :key="1">
                <span slot="tab">ร้านค้า <a-tag color="#1AB759" style="border-radius: 8px;">{{ countShop }}</a-tag></span>
              </a-tab-pane>
            </a-tabs>
          </v-col>
        </v-row>

        <div v-if="activeTab === 0 && companyList.length > 0">
          <h4 v-if="!MobileSize" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">
            {{ pageHeaders }} {{ showCountStatusUser }} รายการ
          </h4>

          <v-data-table
            :headers="headerManageCompany"
            :items="companyList"
            :search="searchCompany"
            no-results-text="ไม่พบรายชื่อผู้ใช้งานภายในนิติบุคคล"
            no-data-text="ไม่มีรายชื่อผู้ใช้งานภายในนิติบุคคล"
            @pagination="countStatusUser"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          >
            <template v-slot:[`item.indexOfUser`]="{ index }">
              <span>{{ index + 1 }}</span>
            </template>
            <template v-slot:[`item.name_th`]="{ item }">
              <v-row align="center">
                <v-col cols="auto">
                  <v-img width="50" height="70" src="@/assets/NoImage.png" v-if="item.img_path === '' || item.img_path === null" contain></v-img>
                  <v-img width="50" height="70" :src="`${item.img_path}`" v-else contain></v-img>
                </v-col>
                <v-col style="text-align: start;">
                  <span>{{ item.name_th }}</span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.branch`]="{ item }">
              <span>
                {{ item.branch }}
              </span>
            </template>
             <template v-slot:[`item.status`]="{ item }">
              <span v-if="item.status === 'active'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ใช้งาน</v-chip>
              </span>
              <span v-else-if="item.status === 'inactive'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
              </span>
            </template>
            <template v-slot:[`item.total_users`]="{ item }">
              <span>
                {{ item.total_users }}
              </span>
            </template>
            <template v-slot:[`item.action`]="{ item }">
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    v-on="on"
                    class="pt-4 pb-4"
                    x-small
                    outlined
                    style="
                      border-radius: 4px;
                      border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                      background: var(--neutral-ffffff, #fff);
                      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                    "
                  >
                    <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="(menuItem, index) in actionsCompany"
                    :key="index"
                    :value="index"
                    @click="handleCompany(menuItem, item)"
                  >
                    <v-list-item-title>{{ menuItem.title }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </template>
          </v-data-table>
        </div>

        <div v-if="activeTab === 1 && shopList.length > 0">
          <h4 v-if="!MobileSize" style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600">
            {{ pageHeaders }} {{ showCountStatusUser }} รายการ
          </h4>

          <v-data-table
            :headers="headerManageShop"
            :items="shopList"
            :search="searchShop"
            no-results-text="ไม่พบรายชื่อผู้ใช้งานภายในนิติบุคคล"
            no-data-text="ไม่มีรายชื่อผู้ใช้งานภายในนิติบุคคล"
            @pagination="countStatusUser"
            :items-per-page="10"
            class="elevation-1 mt-4"
            :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          >
            <template v-slot:[`item.indexOfUser`]="{ index }">
              <span>{{ index + 1 }}</span>
            </template>
            <template v-slot:[`item.name_th`]="{ item }">
              <v-row align="center">
                <v-col cols="auto">
                  <v-img width="50" height="70" src="@/assets/NoImage.png" v-if="item.path_logo === '' || item.path_logo === null" contain></v-img>
                  <v-img width="50" height="70" :src="`${item.path_logo}`" v-else contain></v-img>
                </v-col>
                <v-col style="text-align: start;">
                  <span>{{ item.name_th }}</span>
                </v-col>
              </v-row>
            </template>
            <template v-slot:[`item.branch`]="{ item }">
              <span>
                {{ item.branch }}
              </span>
            </template>
            <template v-slot:[`item.shop_status`]="{ item }">
              <span v-if="item.shop_status === 'active'">
                <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เปิดร้าน</v-chip>
              </span>
              <span v-else-if="item.shop_status === 'inactive'">
                <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ปิดร้าน</v-chip>
              </span>
            </template>
            <template v-slot:[`item.total_users`]="{ item }">
              <span>
                {{ item.total_users }}
              </span>
            </template>
            <template v-slot:[`item.action`]="{ item }">
              <v-menu offset-y>
                <template v-slot:activator="{ on, attrs }">
                  <v-btn
                    v-bind="attrs"
                    v-on="on"
                    class="pt-4 pb-4"
                    x-small
                    outlined
                    style="
                      border-radius: 4px;
                      border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                      background: var(--neutral-ffffff, #fff);
                      box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                    "
                  >
                    <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                  </v-btn>
                </template>
                <v-list>
                  <v-list-item
                    v-for="(menuItem, index) in actionsShop"
                    :key="index"
                    :value="index"
                    @click="handleShop(menuItem, item)"
                  >
                    <v-list-item-title>{{ menuItem.title }}</v-list-item-title>
                  </v-list-item>
                </v-list>
              </v-menu>
            </template>
          </v-data-table>
        </div>
      </v-card-text>
      <!-- <v-container v-if="companyList.length === 0">
              <v-row justify="center" align-content="center" >
                <v-col cols="12" md="12" align="center" style="min-height: 636px;">
                  <div style="padding-top: 90px;">
                    <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px"
                      height="100%" width="100%" contain aspect-ratio="2"></v-img>
                  </div>
                  <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
                    <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการ{{ emptyPageText }}</span><br />
                  </h2>
                </v-col>
              </v-row>
      </v-container> -->
      <v-container v-if="(companyList.length === 0 && activeTab === 0) || (shopList.length === 0 && activeTab === 1)">
        <v-row justify="center" align-content="center">
          <v-col cols="12" md="12" align="center" style="min-height: 636px;">
            <div style="padding-top: 90px;">
              <v-img src="@/assets/ImageINET-Marketplace/ICONShop/None-Order.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
            </div>
            <h2 style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;">
              <span style="font-weight: bold; font-size: 24px; line-height: 32px;">คุณยังไม่มีรายการ{{ emptyPageText }}</span><br />
            </h2>
          </v-col>
        </v-row>
      </v-container>
      <DetailUserModal ref="DetailUserModal" />
    </v-card>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag,
    DetailUserModal: () => import(/* webpackPrefetch: true */ '@/components/Business/dialogDetailUser.vue')
  },
  data () {
    return {
      manageBusiness: '',
      activeTab: 0,
      searchCompany: '',
      searchShop: '',
      taxID: '',
      detailBusiness: '',
      companyList: [],
      shopList: [],
      lazy: false,
      countCompany: 0,
      countShop: 0,
      disableTable: false,
      showCountStatusUser: '',
      itemsPerPage: 10,
      pageHeaders: '',
      emptyPageText: '',
      headerManageCompany: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_th', align: 'center', sortable: false, width: '200px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สาขา', value: 'branch', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนผู้ใช้งาน', value: 'total_users', align: 'center', filterable: false, sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', align: 'center', filterable: false, sortable: false, width: '50px', class: 'backgroundTable fontTable--text fontSizeDetail' }

      ],
      headerManageShop: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'name_th', align: 'center', sortable: false, width: '200px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สาขา', value: 'branch', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'shop_status', align: 'center', sortable: false, width: '150px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จำนวนผู้ใช้งาน', value: 'total_users', align: 'center', filterable: false, sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'จัดการ', value: 'action', align: 'center', filterable: false, sortable: false, width: '50px', class: 'backgroundTable fontTable--text fontSizeDetail' }

      ],
      dialogAddBranchCompany: false,
      dialogAddBranchShop: false,
      textField: '',
      sellerShopNameTH: '',
      sellerShopNameEN: '',
      BussinessCode: '',
      urlName: '',
      bankAccount: '',
      actionsShop: [
        {
          title: 'รายละเอียดร้านค้า'
        },
        {
          title: 'รายละเอียดผู้ใช้งาน'
        },
        {
          title: 'จัดการตำแหน่ง'
        }
      ],
      actionsCompany: [
        {
          title: 'รายละเอียดบริษัท'
        },
        {
          title: 'รายละเอียดผู้ใช้งาน'
        },
        {
          title: 'จัดการตำแหน่ง'
        }
      ],
      accessShopUser: '0',
      assignShopPermission: 0
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavBusiness')
    this.$EventBus.$on('refreshCreateCompany', this.getBusinessTaxIDtoCompany)
    await this.getBusinessTaxIDtoCompany()
    await this.getBusinessTaxIDtoShop()
    // await this.getListBank()
    await this.listPositionTable()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/manageCompanyShopMobile' }).catch(() => {})
      } else {
        localStorage.setItem('pathBusiness', 'manageCompanyShop')
        this.$router.push({ path: '/manageCompanyShop' }).catch(() => {})
      }
    }
  },
  mounted () {
    this.SelectDetailUser(this.activeTab)
  },
  methods: {
    showPositionCompany (val) {
      var data = {
        id: val.company_id
      }
      localStorage.setItem('companyID', JSON.stringify(data))
      if (this.MobileSize) {
        if (val.assign_company_permission === '1' || val.assign_company_permission === 1) {
          localStorage.setItem('paramID', val.company_id)
          this.$router.push(`/managePositionCompanyMobile?companyID=${val.company_id}`)
        } else if (val.assign_company_permission === '0' || val.assign_company_permission === 0) {
          this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุณไม่มีสิทธิ์จัดการตำแหน่งร้านค้า' })
        }
      } else {
        if (val.assign_company_permission === '1' || val.assign_company_permission === 1) {
          localStorage.setItem('paramID', val.company_id)
          this.$router.push(`/managePositionCompany?companyID=${val.company_id}`)
        } else if (val.assign_company_permission === '0' || val.assign_company_permission === 0) {
          this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุณไม่มีสิทธิ์จัดการตำแหน่งบริษัท' })
        }
      }
    },
    showDetailCompany (item) {
      if (this.MobileSize) {
        localStorage.setItem('paramID', item.company_id)
        localStorage.setItem('paramName', item.name_th)
        this.$router.push(`/showDetailCompanyMobile?companyID=${item.company_id}&name=${item.name_th}`)
      } else {
        localStorage.setItem('paramID', item.company_id)
        localStorage.setItem('paramName', item.name_th)
        this.$router.push(`/showDetailCompany?companyID=${item.company_id}&name=${item.name_th}`)
      }
    },
    showDetailUserCompany (item) {
      if (this.MobileSize) {
        if (item.access_company_member === 1 || item.access_company_member === '1') {
          localStorage.setItem('paramTaxId', item.tax_id)
          localStorage.setItem('paramID', item.company_id)
          localStorage.setItem('paramName', item.name_th)
          this.$router.push(`/detailUsersCompanyMobile?taxID=${item.tax_id}&companyID=${item.company_id}&nameTH=${item.name_th}`)
        } else if (item.access_company_member === 0 || item.access_company_member === '0') {
          this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุณไม่มีสิทธิ์ดูรายละเอียดบุคลากรบริษัท' })
        }
      } else {
        if (item.access_company_member === 1 || item.access_company_member === '1') {
          localStorage.setItem('paramTaxId', item.tax_id)
          localStorage.setItem('paramID', item.company_id)
          localStorage.setItem('paramName', item.name_th)
          this.$router.push(`/detailUsersCompany?taxID=${item.tax_id}&companyID=${item.company_id}&nameTH=${item.name_th}`)
        } else if (item.access_company_member === 0 || item.access_company_member === '0') {
          console.log('here 2')
          this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'คุณไม่มีสิทธิ์ดูรายละเอียดบุคลากรบริษัท' })
        }
      }
    },
    showDetailShop (item) {
      if (this.MobileSize) {
        localStorage.setItem('paramID', item.seller_shop_id)
        localStorage.setItem('paramName', item.name_th)
        this.$router.push(`/detailShopMobile?shopID=${item.seller_shop_id}&name=${item.name_th}`)
      } else {
        localStorage.setItem('paramID', item.seller_shop_id)
        localStorage.setItem('paramName', item.name_th)
        this.$router.push(`/detailShop?shopID=${item.seller_shop_id}&name=${item.name_th}`)
      }
    },
    showDetailUserShop (item) {
      this.accessShopUser = item.access_shop_user
      if (this.MobileSize) {
        if (this.accessShopUser === '1') {
          localStorage.setItem('paramTaxId', item.tax_id)
          localStorage.setItem('paramID', item.seller_shop_id)
          localStorage.setItem('paramName', item.name_th)
          this.$router.push(`/detailUsersShopsMobile?taxID=${item.tax_id}&shopID=${item.seller_shop_id}&name=${item.name_th}`)
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'คุณไม่มีสิทธิ์ดูรายละเอียดบุคลากรร้านค้า'
          })
        }
      } else {
        if (this.accessShopUser === '1') {
          localStorage.setItem('paramTaxId', item.tax_id)
          localStorage.setItem('paramID', item.seller_shop_id)
          localStorage.setItem('paramName', item.name_th)
          this.$router.push(`/detailUsersShops?taxID=${item.tax_id}&shopID=${item.seller_shop_id}&name=${item.name_th}`)
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'คุณไม่มีสิทธิ์ดูรายละเอียดบุคลากรร้านค้า'
          })
        }
      }
    },
    showManagePosition (item) {
      this.assignShopPermission = item.assign_shop_permission
      if (this.MobileSize) {
        if (this.assignShopPermission === '1') {
          localStorage.setItem('shopSellerID', item.seller_shop_id)
          this.$router.push(`/managePositionShopMobile?seller_shop_id=${item.seller_shop_id}`)
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'คุณไม่มีสิทธิ์จัดการตำแหน่งบริษัท'
          })
        }
      } else {
        if (this.assignShopPermission === '1') {
          localStorage.setItem('shopSellerID', item.seller_shop_id)
          this.$router.push(`/managePositionShop?seller_shop_id=${item.seller_shop_id}`)
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'error',
            text: 'คุณไม่มีสิทธิ์จัดการตำแหน่งบริษัท'
          })
        }
      }
    },
    backtoUserMenu () {
      this.$router.push({ path: '/detailbusinesssidMobileMenu' }).catch(() => {})
    },
    async SelectDetailUser (item) {
      if (item === 0) {
        this.pageHeaders = 'รายการบริษัทในนิติบุคคลทั้งหมด'
        this.emptyPageText = 'บริษัท'
      } else if (item === 1) {
        this.pageHeaders = 'รายการร้านค้าในนิติบุคคลทั้งหมด'
        this.emptyPageText = 'ร้านค้า'
      }
    },
    countStatusUser (pagination) {
      this.showCountStatusUser = pagination.itemsLength
    },
    async listPositionTable () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsAuthorityUser')
      // var taxID = await this.$store.state.ModuleUser.stateAuthorityUser
      // var bizid = localStorage.getItem('business_id')
      // var ownerBusiness = taxID.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
      // this.taxID = ownerBusiness[0].owner_tax_id
      var data = {
        tax_id: this.taxID
      }
      await this.$store.dispatch('actionsListPositions', data)
      var response = await this.$store.state.ModuleBusiness.stateListPositions
      if (response.code === 200) {
        this.manageBusiness = response.data.manage_business
      }
      this.$store.commit('closeLoader')
    },
    async getBusinessTaxIDtoCompany () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      this.detailBusiness = this.taxID.data
      var bizid = localStorage.getItem('business_id')
      var ownerBusiness = response.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
      // this.taxID = taxID.data.array_business[0].owner_tax_id
      if (ownerBusiness.length === 0) {
        this.$swal.fire({
          icon: 'error',
          text: 'คุณไม่สิทธิ์จัดการนิติบุคคล',
          showConfirmButton: false,
          timer: 2500
        })
        if (!this.MobileSize) {
          this.$router.push('/detailbusinesssid')
        } else {
          this.$router.push('/detailbusinesssidMobile')
        }
      } else {
        this.taxID = ownerBusiness[0].owner_tax_id
        const data = {
          tax_id: this.taxID
        }

        await this.$store.dispatch('actionsManageCompany', data)
        const detailCompany = await this.$store.state.ModuleBusiness.stateManageCompany
        if (detailCompany.result === 'SUCCESS') {
          this.$EventBus.$emit('changeNav')
          this.companyList = detailCompany.data.companies
          this.countCompany = detailCompany.data.total_company
          this.$store.commit('closeLoader')
        }
      }
      this.$store.commit('closeLoader')
    },
    async getBusinessTaxIDtoShop () {
      this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsAuthorityUser')
      // const taxID = await this.$store.state.ModuleUser.stateAuthorityUser
      // this.detailBusiness = taxID.data
      // var bizid = localStorage.getItem('business_id')
      // if (taxID.data.array_business.lenght !== 0) {
      //   var ownerBusiness = taxID.data.array_business.filter(taxId => taxId.business_id === parseInt(bizid))
      //   this.taxID = ownerBusiness[0].owner_tax_id
      // } else {
      //   this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: 'นิติบุคคลยังไม่อนุมัติ' })
      // }
      const data = {
        tax_id: this.taxID
      }

      await this.$store.dispatch('actionsManageShop', data)
      const detailShop = await this.$store.state.ModuleBusiness.stateManageShop
      if (detailShop.result === 'SUCCESS') {
        this.$EventBus.$emit('changeNav')
        this.shopList = detailShop.data.shops
        this.countShop = detailShop.data.total_shop
        this.$store.commit('closeLoader')
      }
    },
    // async createPositionCompanyOpen () {
    //   if (this.MobileSize) {
    //     this.$router.push('/createBussinessBranchCompanyMobile')
    //   } else {
    //     this.$router.push('/createBussinessBranchCompany')
    //   }
    // },
    // async createPositionCompanyClose () {
    //   this.dialogAddBranchCompany = false
    // },
    async createPositionShopOpen () {
      if (this.MobileSize) {
        this.$router.push(`/createBussinessBranchShopMobile?step=1&countShop=${this.countShop}`)
      } else {
        this.$router.push(`/createBussinessBranchShop?step=1&countShop=${this.countShop}`)
      }
    },
    async createPositionShopClose () {
      this.dialogAddBranchShop = false
    },
    async getListBank () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsListBank')
      const response = await this.$store.state.ModuleShop.stateListBank
      if (response.result === 'SUCCESS') {
        this.itemsBank = [...response.data]
      }
      this.$store.commit('closeLoader')
    },
    handleShop (menuItem, item) {
      switch (menuItem.title) {
        case 'รายละเอียดร้านค้า':
          this.showDetailShop(item)
          break
        case 'รายละเอียดผู้ใช้งาน':
          this.showDetailUserShop(item)
          break
        case 'จัดการตำแหน่ง':
          this.showManagePosition(item)
          break
      }
    },
    handleCompany (menuItem, item) {
      switch (menuItem.title) {
        case 'รายละเอียดบริษัท':
          this.showDetailCompany(item)
          break
        case 'รายละเอียดผู้ใช้งาน':
          this.showDetailUserCompany(item)
          break
        case 'จัดการตำแหน่ง':
          this.showPositionCompany(item)
          break
      }
    }
  }
}
</script>

<style scoped>
.custom-btn-style {
  width: 50px;
  height: 50px;
  border: 1px solid #F2F2F2;
  box-sizing: border-box;
  box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
  border-radius: 4px;
}
.text-center {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
}

.text-break {
  white-space: normal;
  display: inline-block;
  word-break: break-word;
  max-width: 600px;
}
/* .v-data-table /deep/ .v-data-table-header-mobile__wrapper {
  display: flex;
  justify-content: end;
}
.v-data-table /deep/ .v-simple-checkbox {
  padding-left: 25px;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
.whiteNumber {
  color: #FFF;
}
.v-data-table /deep/ .v-data-table__wrapper .v-data-table__mobile-row {
  border-bottom: 0px !important;
  padding-left: 4px;
  padding-right: 4px;
} */
</style>

<style lang="scss" scoped>
  ::v-deep .elevation-1 th:first-of-type {
    background-color: #E6F5F3;
  }
  ::v-deep .elevation-1 tr th:first-of-type, td:first-of-type {
    background-color: #E6F5F3;
    border-style: none !important;
  }
  .v-dialog > .v-card > .v-card__text {
    padding: 0px 29px 0px;
  }
  ::v-deep table {
    tbody {
    tr {
        td:nth-child(6) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
    }
    thead {
    tr {
        th:nth-child(6) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        }
    }
    }
  }
</style>
