server {
    listen 80;
    #listen 443 ssl;
#    ssl on;
#    ssl_certificate /etc/ssl/one.th/one.th.crt;
#    ssl_certificate_key /etc/ssl/one.th/one.th.key;
    server_name panit.sdi.inet.co.th;
    access_log off;
    error_log off;
    large_client_header_buffers 4 32k;
    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html =404;
    }
}
