<template>
  <v-hover
   v-slot="{ hover }"
  >
    <v-card class="rounded-lg custom-card"  :elevation="hover ? 4 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" :href="itemProduct.link ? itemProduct.link : pathProductDetail" @click.prevent="DetailProduct(itemProduct)" max-width="100%" max-height="100%">
      <v-row>
        <v-col cols="12" md="12" sm="12" xs="12" class="pt-4">
          <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
          <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
          <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
          <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
          <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
          <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
          <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
          <!-- <v-img src="@/assets/Tag/E-Receipt.png" height="100" width="100" style="margin-left: 0px; margin-top: -10px; position:absolute; z-index:1;" v-else-if="itemProduct.message_status === 'e-receipt'"></v-img> -->
        </v-col>
        <v-col cols="6" md="3" sm="4">
          <v-card-title class="pt-0 mb-2">
            <v-img v-lazyload loading='lazy' :src="itemProduct.images_URL[0]" height="260" width="260" v-if="itemProduct.images_URL.length !== 0" contain>
              <!-- <v-chip
                v-if="itemProduct.stock_count === 0"
                class="ma-2"
                text-color="#D1392B"
                color="rgba(255, 255, 255)"
                small
              >
                <v-avatar
                  left
                  color="#D1392B"
                  size="10"
                >
                  <v-icon small color="white">mdi-close</v-icon>
                </v-avatar>
                สินค้าหมด
              </v-chip> -->
                 <v-row dense>
                  <!-- <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
                    <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
                    <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
                    <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
                    <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
                    <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
                    <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
                    <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
                    <v-img src="@/assets/Tag/E-Receipt.png" height="80" width="70" style="margin-left: -10px; margin-top: -10px; position:absolute; z-index:1;" v-else-if="itemProduct.message_status === 'e-receipt'"></v-img>
                  </v-col> -->
                  <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && !IpadSize && !MobileSize">
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 47%;">
                      <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 16px; display: block; line-height: 16px;" class="pt-2" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                      <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 16px; display: block; line-height: 16px;" class="pt-2" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 16px; display: block; line-height: 18px;">ลด</span>
                    </v-img>
                  </v-col>
                  <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize && !IpadSize && !MobileSize">
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
                    </v-img>
                  </v-col>
                  <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadSize && !IpadProSize && !MobileSize">
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
                    </v-img>
                  </v-col> -->
                  <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else>
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 60%;">
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 20px; line-height: 16px; display: block;" class="pt-2">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 20px; line-height: 18px; display: block;">ลด</span>
                    </v-img>
                  </v-col> -->
                </v-row>
            </v-img>
            <v-img v-lazyload src="@/assets/NoImage.png" height="260" width="260" v-else contain>
              <!-- <v-chip
                v-if="itemProduct.stock_count === 0"
                class="ma-2"
                text-color="#D1392B"
                color="rgba(255, 255, 255)"
                small
              >
                <v-avatar
                  left
                  color="#D1392B"
                  size="10"
                >
                  <v-icon small color="white">mdi-close</v-icon>
                </v-avatar>
                สินค้าหมด
              </v-chip> -->
              <v-row dense>
                <!-- <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
                  <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
                  <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
                  <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
                  <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
                  <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
                  <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
                  <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
                  <v-img src="@/assets/Tag/Event1.png" height="75" width="60" contain style="margin-left: 10px; margin-top: -20px;" v-else-if="itemProduct.message_status === 'e-receipt'"></v-img>
                </v-col> -->
                <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && !IpadSize && !MobileSize">
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 47%;">
                      <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 16px; display: block; line-height: 16px;" class="pt-2" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                      <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 16px; display: block; line-height: 16px;" class="pt-2" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 16px; display: block; line-height: 18px;">ลด</span>
                    </v-img>
                  </v-col>
                  <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize && !IpadSize && !MobileSize">
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
                    </v-img>
                  </v-col>
                  <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadSize && !IpadProSize && !MobileSize">
                    <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-2" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                      <span style="color: #FF0000; font-size: 16px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
                    </v-img>
                  </v-col> -->
              </v-row>
            </v-img>
          </v-card-title>
        </v-col>
        <v-col cols="6" md="9" sm="8" xs="12">
          <v-tooltip bottom>
            <template v-slot:activator="{ attrs }">
              <v-card-text v-bind="attrs"  :style="{'font-size': MobileSize ? '14px' : '20px', 'font-weight': MobileSize ? 'bold' : 'bold'}" class="pl-0">{{ itemProduct.name }}</v-card-text>
            </template>
            <!-- <span>{{ itemProduct.product_name }}</span> -->
          </v-tooltip>
          <span class="ml-0 mr-2">{{ $t('AppBar.sku') }}: {{ itemProduct.sku }}</span><br>
          <div v-if="!MobileSize">
            <span style="color: #a0a0a0">{{ itemProduct.short_description }}</span><br>
          </div>
          <div v-else>
            <span style="color: #a0a0a0">{{ substring(itemProduct.short_description) }}</span>
          </div>
          <v-chip x-small v-if="itemProduct.fda_number !== null && itemProduct.fda_number !== ''" color="#F3F5F7" class="square-chip" text-color="#636363"><v-img style="border-radius: 999px;" max-height="16px" max-width="16px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">เครื่องหมาย อย.</span></v-chip><br/>
          <!-- <span class="ml-0">ขายแล้ว 100 ชิ้น</span><br/> -->
          <!-- <span>{{ Number(itemProduct.revenue_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
          <span v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
            <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
            <span class="specialPrice" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
             <v-chip class="ml-2" color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
          </div>
          <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
            <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
            <span class="specialPrice" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
          </div>
          <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
            ติดต่อสอบถามเจ้าหน้าที่
          </span>
          <!-- <span class="ml-0 pt-4" v-if="itemProduct.give_tier !== 'y'">฿ {{ Number(itemProduct.product_tier_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <div v-else>
            <span style="font-weight: 300" class="priceDecrese">฿ {{ Number(itemProduct.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
            <span class="specialPrice">฿ {{ Number(itemProduct.product_tier_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          </div> -->
        </v-col>
      </v-row>
    </v-card>
  </v-hover>
</template>

<script>
// import { Decode } from '@/services'
import Vue from 'vue'
export default {
  props: ['itemProduct'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN
    }
  },
  created () {
    this.formatSold()
    if (this.itemProduct !== undefined) {
      if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
        if (this.itemProduct.link) {
          // console.log('tt1')
          this.pathProductDetail = this.itemProduct.link
        } else {
          // console.log('els')
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.id)
        }
      } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
        if (this.itemProduct.link) {
          this.pathProductDetail = this.itemProduct.link
        } else {
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.product_id)
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    formatSold () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'พัน'
        }
        return value.toString()
      })
    },
    DetailProduct (val) {
    // console.log(val)
      const nameCleaned = val.name.replace(/\s/g, '-')
      // this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.product_id}` } }).catch(() => {})
      const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } })
      window.location.assign(routeData.href, '_blank')
      // this.$router.push({ path: routeData.href })
    },
    substring (data) {
      return data.length > 50 ? data.substring(0, 50) + '...' : data
    }
  }
}
</script>

<style scoped>
.square-chip {
  padding: 1px 0px 0px 1px;
  width: 120px; /* กำหนดความกว้าง */
  height: 16px; /* กำหนดความสูง */
  font-size: 14px; /* ขนาดตัวอักษร */
}
.custom-card {
  border: 1px solid #BDE7D9 !important; /* สีขอบของการ์ด */
  border-color: #BDE7D9 !important; /* สีขอบของการ์ดเมื่อไม่ได้โฮเวอร์ */
}
.priceDecrese {
  font-size: 14px;
  text-decoration: line-through;
  color: #929292;
  margin-right: 0px;
}
.specialPrice {
  font-size: 16px;
  color: red;
}
.discount {
  color: #FF0000;
  background-color: #FBE5E4;
  border-radius: 4px;
}
</style>
