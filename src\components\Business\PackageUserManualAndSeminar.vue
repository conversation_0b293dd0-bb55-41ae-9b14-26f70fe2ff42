<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" class="pl-3" v-if="!MobileSize">เอกสารคู่มือการใช้งานและสัมมนา</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoUserMenu()">mdi-chevron-left</v-icon>เอกสารคู่มือการใช้งานและสัมมนา</v-card-title>

      <div class="pl-3 pa-3">
        <v-row>
          <v-col cols="12">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead">เอกสารคู่มือการใช้งาน</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <div v-if="this.DataUserManualDetail.length === 0">
                  <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
                    <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                      <span>ไม่มีเอกสารคู่มือการใช้งาน</span>
                    </v-card-text>
                  </v-card>
                </div>

                <div v-if="this.DataUserManualDetail.length !== 0">
                  <v-col cols="12" md="12" sm="12" class="pa-0" v-for="(item, index) in DataUserManualDetail" :key="index">
                    <v-card outlined width="100%" height="80%" @click="openLink(item.path)" class="mt-2">
                      <v-card-text>
                        <v-row dense>
                          <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" max-width="80" max-height="80" contain>
                          </v-img>
                          <span style="text-align: center; align-content: center;" class="text-truncate pt-2">{{ item.name }}</span>
                        </v-row>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </div>
              </v-card-text>
            </v-card>
          </v-col>

          <v-col cols="12">
            <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
              <v-card-title style="padding: 16px 20px; background: #F3F5F7; height: 57px;">
                <span class="textManageShopHead">ลิงก์ในการเข้าร่วมสัมมนา</span>
              </v-card-title>
              <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                <div  v-if="this.DataLinkSeminarDetail.length === 0">
                  <v-col cols="12" class="pa-0">
                    <v-card elevation="0" outlined width="100%" height="100%" style="border-radius: 8px;">
                    <v-card-text :style="MobileSize ? 'padding: 20px 16px 16px 16px;' : 'padding: 20px 20px 16px 20px;'">
                      <span>ไม่มีลิงก์ในการเข้าร่วมสัมมนา</span>
                    </v-card-text>
                  </v-card>
                  </v-col>
                </div>

                <div  v-if="this.DataLinkSeminarDetail.length !== 0">
                  <v-col cols="12" class="pa-0" v-for="(item, index) in DataLinkSeminarDetail" :key="index">
                    <v-text-field
                      readonly
                      solo
                      dense
                      hide-details
                      v-model="item.links"
                      @click="openLinkSeminar(item.links)"
                      placeholder="ลิงก์ในการเข้าร่วมสัมมนา"
                    ></v-text-field>
                  </v-col>
                </div>
              </v-card-text>
            </v-card>
          </v-col>
        </v-row>
      </div>

    </v-card>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      onechatToken: '',
      DataUserManualDetail: [],
      DataLinkSeminarDetail: []
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/PackageUserManualAndSeminarMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/PackageUserManualAndSeminar' }).catch(() => {})
      }
    }
  },
  async created () {
    window.scrollTo(0, 0)
    if (localStorage.getItem('oneData') !== null) {
      const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      this.onedata = oneData
      if (oneData.user !== undefined) {
        this.isLogin = true
        if (oneData.user.shared_token !== undefined && oneData.user.shared_token !== null && oneData.user.shared_token !== '') {
          this.onechatToken = oneData.user.shared_token
        } else {
          this.onechatToken = ''
        }
      } else {
        localStorage.removeItem('oneData')
      }
    }
    await this.UserManualDetail()
    await this.LinkSeminarDetail()
  },
  methods: {
    backtoUserMenu () {
      this.$router.push('/detailbusinesssidMobileMenu')
    },
    async UserManualDetail () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAdminUserManualDetail')
      const response = await this.$store.state.ModuleAdminPanit.stateAdminUserManualDetail
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DataUserManualDetail = response.data
        // console.log(this.DataUserManualDetail)
      }
    },
    async LinkSeminarDetail () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsAdminLinkSeminar')
      const response = await this.$store.state.ModuleAdminPanit.stateAdminLinkSeminar
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        this.DataLinkSeminarDetail = response.data
        // console.log(this.DataLinkSeminarDetail)
      }
    },
    openLink (link) {
      window.open(link, '_blank')
    },
    openLinkSeminar (link) {
      // var sharetoken = this.onechatToken
      // window.open(link + '?share_token=' + sharetoken, '_blank')
      window.open(link, '_blank')
    }
  }
}
</script>

<style>

</style>
