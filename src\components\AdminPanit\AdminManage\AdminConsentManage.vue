<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-row class="d-flex align-center">
        <v-col :cols="MobileSize ? 8 : 6">
          <v-card-title style="font-weight: 700; font-size: x-large; line-height: 22px; color: #333333;" v-if="!MobileSize">จัดการ Consents</v-card-title>
          <v-card-title style="font-size: medium; font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2 d-flex" @click="backtoPage()">mdi-chevron-left</v-icon>จัดการ Consents</v-card-title>
        </v-col>
      </v-row>
      <v-row class="d-flex justify-center mt-6">
        <v-card v-for="(item, index) in listConsent" :key="index" width="90%" style="background: #FFFFFF; border-radius: 8px; margin-bottom: 20px;" outlined>
          <v-card-title>
            <v-card width="100%" height="60px" style="background: linear-gradient(90deg, #2D95FF -4%, #54ECB5 103.33%); border-radius: 8px;" class="d-flex">
              <v-card-title :style="MobileSize ? 'font-weight: 700; font-size: large; color: #FFFFFF;' : 'font-weight: 700; font-size: x-large; color: #FFFFFF;'" class="pa-0 ma-0"><v-icon color="#FFFFFF" large>mdi-circle-small</v-icon>{{ ConsentName(item.name) }}</v-card-title>
              <v-spacer></v-spacer>
              <v-card-title class="pa-0 ma-0 pr-3" style="gap: 1vw;" v-if="item.edit === false">
                <!-- add -->
                <v-btn @click="item.edit = !item.edit" v-if="item.data_consent === null" color="primary" style="background: #FFFFFF;" icon><v-icon>mdi-plus</v-icon></v-btn>
                <!-- edit -->
                <v-btn @click="openEdit(index)" v-if="item.data_consent !== null"  color="primary" style="background: #FFFFFF;" icon><v-icon>mdi-pencil</v-icon></v-btn>
                <!-- example -->
                <v-btn @click="openDialog(index)" v-if="item.data_consent !== null" color="primary" style="background: #FFFFFF;" icon><v-icon>mdi-eye-outline</v-icon></v-btn>
              </v-card-title>
              <v-card-title class="pa-0 ma-0 pr-3" style="gap: 1vw;" v-if="item.edit === true">
                <!-- cancel -->
                <v-btn @click="closeEdit(index)" color="red" style="background: #FFFFFF;" icon><v-icon>mdi-close</v-icon></v-btn>
                <!-- save -->
                <v-btn :disabled="item.ckModel === null || item.ckModel === ''" @click="saveConsent(index)" color="primary" style="background: #FFFFFF;" icon><v-icon>mdi-check</v-icon></v-btn>
              </v-card-title>
            </v-card>
          </v-card-title>
          <v-card-text v-if="item.edit === false">
            <v-card width="100%" outlined style="border: 1px dashed #ccc;" class="d-flex align-center flex-column" v-if="item.data_consent === null">
              <v-img
                class="mt-2 mb-5"
                src="@/assets/consent.png"
                width="250"
                height="150"
                contain
              ></v-img>
              <span :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'" class="pb-2">คุณยังไม่มี{{ item.name === 'Terms Of Use' ? ' Terms Of Use' : ConsentName(item.name) }}</span>
              <span :style="MobileSize ? 'font-size: medium;' : 'font-size: medium;'" class="pb-2">สามารถกด <v-btn small readonly color="primary" style="background: #FFFFFF; pointer-events: none;" icon><v-icon>mdi-plus</v-icon></v-btn> เพื่อเพิ่ม{{ item.name === 'Terms Of Use' ? ' Terms Of Use' : ConsentName(item.name) }}</span>
            </v-card>
            <v-card width="100%" outlined style="border: 1px dashed #ccc;" class="d-flex align-center flex-column" v-else>
              <v-col cols="12">
                <div v-html="truncateHtml(item.data_consent, 1000)"></div>
              </v-col>
              <v-col v-if="isTruncatedHtml(item.data_consent, 1000)" cols="12" class="d-flex justify-center mt-0 pt-0">
                <span class="pr-1" style="color: #27AB9C; text-decoration: underline;">กดดู</span>
                <v-btn class="pt-1" x-small readonly color="primary" style="background: #FFFFFF; pointer-events: none;" icon><v-icon>mdi-eye-outline</v-icon></v-btn>
                <span class="pl-1" style="color: #27AB9C; text-decoration: underline;">เพื่อดูเพิ่มเติม</span>
              </v-col>
            </v-card>
          </v-card-text>
          <v-card-text v-else>
            <v-card width="100%" outlined style="border: 1px dashed #ccc;" class="d-flex align-center flex-column">
              <v-col cols="12">
                <div id="toolbar-container"></div>
                <ckeditor style="border: 1px #A0A0A0 solid" :editor="editor" :config="editorConfig" v-model="item.ckModel" @ready="onReady"></ckeditor>
              </v-col>
            </v-card>
          </v-card-text>
        </v-card>
      </v-row>
    </v-card>
    <v-dialog v-dialog v-model="modalDetails" style="border-radius: 24px;" persistent :width="MobileSize ? '100%' : IpadSize ? '100%' : IpadProSize ? '90%': '1100'">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : IpadProSize ? 'width: 100%' : 'width: 1100px'" class="backgroundHeadPartner" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>{{ ConsentName(nameDetail) }}</b></span>
              </v-col>
              <v-btn fab small @click="closeDialog()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '1100px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div>
            <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
              <div class="pa-5" v-html="this.detail"></div>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import htmlTruncate from 'html-truncate'
import DecoupledEditor from '@ckeditor/ckeditor5-build-decoupled-document'
export default {
  data () {
    return {
      modalDetails: false,
      detail: '',
      nameDetail: '',
      description: '',
      editor: DecoupledEditor,
      editorConfig: {
        toolbar: [
          'heading',
          'fontFamily',
          'fontSize',
          '|',
          'bold',
          'italic',
          'link',
          '|',
          'alignment:left',
          'alignment:right', 
          'alignment:center',
          'alignment:justify',
          '|',
          'bulletedList',
          'numberedList',
          'outdent',
          'indent',
          '|',
          'imageUpload',
          'blockquote',
          'insertTable',
          '|',
          'undo',
          'redo'
        ],
        image: {
          toolbar: [
            'imageStyle:block',
            'imageStyle:side'
          ]
        },
        table: {
          contentToolbar: [
            'tableColumn',
            'tableRow',
            'mergeTableCells'
          ]
        }
      },
      listConsent: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AdminConsentManageMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/AdminConsentManage' }).catch(() => {})
      }
    }
  },
  created () {
    console.log(DecoupledEditor.builtinPlugins.map(p => p.pluginName))
    this.getListConsent()
  },
  methods: {
    truncateHtml (html, limit = 1000) {
      return htmlTruncate(html, limit, { ellipsis: '...' })
    },
    isTruncatedHtml (html, limit = 1000) {
      const truncated = htmlTruncate(html, limit, { ellipsis: '...' })
      return truncated !== html
    },
    onReady (editor) {
      // const toolbarContainer = document.querySelector('#toolbar-container')
      // toolbarContainer.appendChild(editor.ui.view.toolbar.element)
      editor.execute('heading', { value: 'paragraph' })
      editor.editing.view.document.on('enter', (evt, data) => {
        if (data.isSoft) {
          editor.execute('enter')
        } else {
          editor.execute('shiftEnter')
        }
        data.preventDefault()
        evt.stop()
        editor.editing.view.scrollToTheSelection()
      }, { priority: 'high' })
    },
    async getListConsent () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsManageConsent')
      var response = await this.$store.state.ModuleAdminManage.stateManageConsent
      if (response.result === 'SUCCESS') {
        this.listConsent = response.data.map(item => ({
          ...item,
          edit: false,
          ckModel: item.data_consent
        }))
        // console.log(this.listConsent)
        this.$store.commit('closeLoader')
      } else {
        this.$swal.fire({
          icon: 'error',
          html: `${response.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
        this.$store.commit('closeLoader')
      }
    },
    openEdit (index) {
      this.listConsent[index].ckModel = this.listConsent[index].data_consent
      this.listConsent[index].edit = true
    },
    closeEdit (index) {
      this.listConsent[index].ckModel = null
      this.listConsent[index].edit = false
    },
    async saveConsent (index) {
      this.$store.commit('openLoader')
      var type = ''
      if (this.listConsent[index].name === 'Terms Of Use') {
        type = 'terms_of_use'
      } else if (this.listConsent[index].name === 'Policy') {
        type = 'policy'
      } else if (this.listConsent[index].name === 'Deleting User Data') {
        type = 'deleting_user_data'
      }
      var data = {
        type: type,
        data_consent: this.listConsent[index].ckModel
      }
      await this.$store.dispatch('actionsEditConsent', data)
      var response = await this.$store.state.ModuleAdminManage.stateEditConsent
      console.log(response.result)
      if (response.result === 'SUCCESS') {
        this.$store.commit('closeLoader')
        await this.$swal.fire({
          icon: 'success',
          html: 'แก้ไขข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true
        })
        this.listConsent[index].edit = false
        this.getListConsent()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          icon: 'error',
          html: `${response.message}`,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true
        })
      }
    },
    openDialog (index) {
      this.nameDetail = this.listConsent[index].name
      this.detail = this.listConsent[index].data_consent
      this.modalDetails = true
    },
    closeDialog () {
      this.modalDetails = false
      this.detail = ''
      this.nameDetail = ''
    },
    ConsentName (val) {
      if (val === 'Policy') {
        return 'นโยบาย'
      } else if (val === 'Terms Of Use') {
        return 'Terms Of Use'
      } else if (val === 'Deleting User Data') {
        return 'การลบข้อมูลผู้ใช้'
      }
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    }
  }
}
</script>

<style>

</style>
