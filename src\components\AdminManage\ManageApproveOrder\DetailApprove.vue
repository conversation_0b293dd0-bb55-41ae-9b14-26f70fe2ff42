<template>
  <v-card elevation="0" width="100%" height="100%">
    <v-card-title>
      <v-row dense>
        <v-col cols="12" md="12">
          <span style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;"><v-icon color="#27AB9C" class="mr-2" @click="backtoListApproveOrder()">mdi-chevron-left</v-icon>รายการอนุมัติ</span>
        </v-col>
      </v-row>
    </v-card-title>
    <v-card-text>
      <v-row dense>
        <!-- ใส่ iframe -->
        <iframe :src="DetailDataPaperless.path_iframe" width="100%" height="800"></iframe>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      DetailDataPaperless: []
    }
  },
  async created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    if (localStorage.getItem('dataPaperlessList') !== null) {
      this.DetailDataPaperless = await JSON.parse(Decode.decode(localStorage.getItem('dataPaperlessList')))
    } else {
      await this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        icon: 'error',
        text: 'ไม่มีรายการสั่งซื้อนี้'
      })
      if (!this.MobileSize) {
        this.$router.push({ path: '/listApprove' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listApproveMobile' }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  methods: {
    backtoListApproveOrder () {
      this.$EventBus.$emit('getListPurchaserApproveOrder')
      if (!this.MobileSize) {
        this.$router.push({ path: '/listApprove' }).catch(() => {})
      } else {
        this.$router.push({ path: '/listApproveMobile' }).catch(() => {})
      }
    }
  }
}
</script>
