<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF', 'border-bottom': '1px solid #F2F2F2' }">
      <template v-slot:divider>
        <v-icon color="#3EC6B6">mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item
        :href="item.href"
        :disabled="item.disabled"
        >
          <span :style="{ color: item.color, cursor: item.disabled !== true ? 'pointer' : 'none', fontSize: '16px' }">{{ item.text }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container>
      <v-row justify="center" class="my-4">
        <h2 :style="!MobileSize ? 'font-weight: 700; font-size: 24px;' : 'font-weight: 700; font-size: 18px;' "> {{ $t('Business.BusinessInfo') }} </h2>
      </v-row>
      <!-- <v-row justify="center" class="my-2" v-if="MobileSize">
        <h2 style="font-weight: 700; font-size: 16px; line-height: 24px;">แนะนำการขายของบน Nexgen Commerce</h2>
      </v-row>
      <v-row justify="start" class="my-4" v-if="MobileSize">
        <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span>
      </v-row>
      <v-row justify="start" class="my-4" v-if="MobileSize">
        <v-col cols="12" class="px-0">
          <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
            <v-card-text>
              <v-row dense>
                <v-col cols="3">
                  <v-btn fab color="#F2F2F2" disabled>
                    <v-avatar size="32" tile>
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group756.png" width="30" height="30" contain></v-img>
                    </v-avatar>
                  </v-btn>
                </v-col>
                <v-col cols="9">
                  <span style="font-weight: 400; font-size: 14px; line-height: 22px;">1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="12" class="px-0">
          <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
            <v-card-text>
              <v-row dense>
                <v-col cols="3">
                  <v-btn fab color="#F2F2F2" disabled>
                    <v-avatar size="32" tile>
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group.png" width="30" height="30" contain></v-img>
                    </v-avatar>
                  </v-btn>
                </v-col>
                <v-col cols="9">
                  <span style="font-weight: 400; font-size: 14px; line-height: 22px;">2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <img src="@/assets/ImageINET-Marketplace/ICONRegister/6.png" contain height="20" width="20" @click="OpenModal" style="cursor: pointer;"/><v-chip color="#E6F5F3" class="pl-1 pr-1 ml-1" style="cursor: pointer;" @click="OpenModal" small><span style="color: #27AB9C; font-weight: 400; font-size: 14px;">เอกสารแนบ</span></v-chip> มาทาง <span style="color: #1B5DD6">email <EMAIL></span> ของ <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="12" class="px-0">
          <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
            <v-card-text>
              <v-row dense>
                <v-col cols="3">
                  <v-btn fab color="#F2F2F2" disabled>
                    <v-avatar size="32" tile>
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/3.png" width="30" height="30"  contain></v-img>
                    </v-avatar>
                  </v-btn>
                </v-col>
                <v-col cols="9">
                  <span style="font-weight: 400; font-size: 14px; line-height: 22px;">3. หลังจากที่ทาง <span style="font-weight: bold;">Thai Payment Gateway</span> ตรวจสอบเสร็จ จะส่งอีเมลกลับมาให้ผ่านอีเมลที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="12" class="px-0">
          <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
            <v-card-text>
              <v-row dense>
                <v-col cols="3">
                  <v-btn fab color="#F2F2F2" disabled>
                    <v-avatar size="32" tile>
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/4.png" width="30" height="30" contain></v-img>
                    </v-avatar>
                  </v-btn>
                </v-col>
                <v-col cols="9">
                  <span style="font-weight: 400; font-size: 14px; line-height: 22px;">4. ระหว่างที่รอ คุณสามารถอัปโหลดสินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ</span>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
        <v-col cols="12" class="px-0">
          <v-card elevation="0" style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 4px;" width="100%" height="100%">
            <v-card-text>
              <v-row dense>
                <v-col cols="3">
                  <v-btn fab color="#F2F2F2" disabled>
                    <v-avatar size="32" tile>
                      <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/5.png" width="31" height="30" contain></v-img>
                    </v-avatar>
                  </v-btn>
                </v-col>
                <v-col cols="9">
                  <span style="font-weight: 400; font-size: 14px; line-height: 22px;">5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row> -->
      <v-card :class="!MobileSize ? '' : 'px-0 mx-0'">
        <v-container>
          <!-- <ModalImage /> -->
            <!-- <v-row no-gutters justify="center" v-if="!MobileSize">
              <v-col cols="12" class="ma-8">
                <v-row justify="center">
                  <v-col cols="12" align="center" class="py-0">
                    <h2 style="font-weight: 700; font-size: 20px;">แนะนำการขายของบน Nexgen Commerce</h2>
                  </v-col>
                </v-row>
                <v-row justify="start">
                  <v-col cols="12" class="mt-4 ml-4">
                    <span style="font-weight: 400; font-size: 16; color: #333333;">ในการขายของบน INET Market กรุณาอ่านขั้นตอนดังต่อไปนี้</span>
                    <v-timeline
                      dense
                    >
                      <v-timeline-item fill-dot color="#F2F2F2" large>
                        <template v-slot:icon>
                            <img src="@/assets/ImageINET-Marketplace/ICONRegister/Group756.png">
                        </template>
                        <v-col cols="12" md="11">
                          <span>1. กรอกข้อมูลให้ครบถ้วนในหน้าร้านค้าของคุณ (หน้านี้)</span>
                        </v-col>
                      </v-timeline-item>
                      <v-timeline-item fill-dot color="#F2F2F2" large>
                        <template v-slot:icon>
                          <img src="@/assets/ImageINET-Marketplace/ICONRegister/Group.png">
                        </template>
                        <v-col cols="12" md="11">
                          <span>2. พอกรอกเสร็จแล้ว ให้ส่งเอกสารต่างๆ ตามที่ระบุใน <img src="@/assets/ImageINET-Marketplace/ICONRegister/6.png" contain height="20" width="20" @click="OpenModal" style="cursor: pointer;"/><v-chip color="#E6F5F3" class="pl-1 pr-1 ml-1" style="cursor: pointer;" @click="OpenModal" small><span style="color: #27AB9C; font-weight: 400; font-size: 14px;">เอกสารแนบ</span></v-chip> มาทาง <span style="color: #1B5DD6">email <EMAIL></span> ของ <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งจะทำการตรวจสอบข้อมูลทางธุรกิจ และสร้างบัญชีให้ ภายใน 1  วันทำการ</span>
                        </v-col>
                      </v-timeline-item>
                      <v-timeline-item fill-dot color="#F2F2F2" large>
                        <template v-slot:icon>
                          <img src="@/assets/ImageINET-Marketplace/ICONRegister/3.png">
                        </template>
                        <v-col cols="12" md="11">
                          <span>3. หลังจากที่ทาง <span style="font-weight: bold;">Thai Payment Gateway</span> ตรวจสอบเสร็จ จะส่งอีเมลกลับมาให้ผ่านอีเมลที่คุณใช้ติดต่อ พร้อมกับ ข้อมูลวิธีการเข้าถึงบัญชี <span style="font-weight: bold;">Thai Payment Gateway</span> ซึ่งใช้เป็นที่รับเงิน เมื่อมีการสั่งซื้อ</span><br>
                        </v-col>
                      </v-timeline-item>
                      <v-timeline-item fill-dot color="#F2F2F2" large>
                        <template v-slot:icon>
                          <img src="@/assets/ImageINET-Marketplace/ICONRegister/4.png">
                        </template>
                        <v-col cols="12" md="11">
                          <span>4. ระหว่างที่รอ คุณสามารถอัปโหลดสินค้าของคุณเข้ามาในระบบไว้ก่อนได้ที่หน้าร้านค้าของคุณ</span>
                        </v-col>
                      </v-timeline-item>
                      <v-timeline-item fill-dot color="#F2F2F2" large>
                        <template v-slot:icon>
                          <img src="@/assets/ImageINET-Marketplace/ICONRegister/5.png">
                        </template>
                        <v-col cols="12" md="11">
                          <span>5. หลังจากที่มีบัญชี Thai Payment Gateway แล้ว สินค้าและร้านค้าของคุณจะถูกแสดงออกสู่สาธารณะ และ สามารถถูกทำการซื้อขายได้</span><br>
                        </v-col>
                      </v-timeline-item>
                    </v-timeline>
                  </v-col>
                </v-row>
              </v-col>
            </v-row> -->
            <!-- Select Business Type -->
            <v-row dense justify="center" class="mb-4">
              <v-col cols="12" md="6">
                <v-card class="d-flex justify-center" outlined style="border: 2px solid #27AB9C;" width="100%" height="100%">
                  <v-tabs
                    v-model="tab"
                    centered
                    icons-and-text
                    color="#4b8bad"
                    class="mt-4"
                  >
                    <v-tabs-slider color="#4b8bad"></v-tabs-slider>
                    <!-- บัญชีนิติบุคคล -->
                    <v-tab href="#tab-1" class="pb-4">
                      {{ $t('Business.BusinessID') }}
                      <v-icon large>mdi-office-building</v-icon>
                    </v-tab>
                    <!-- บัญชีนิติบุคคลอื่นๆ -->
                    <v-tab href="#tab-2" class="pb-4" @click="checkeKYC()">
                      {{ $t('Business.OtherBusinessID') }}
                      <v-icon large>mdi-storefront-outline</v-icon>
                    </v-tab>
                  </v-tabs>
                </v-card>
              </v-col>
            </v-row>
            <v-tabs-items v-model="tab" class="mb-4 mt-8">
              <v-tab-item :value="'tab-1'">
                <!-- select type tax -->
                <v-card elevation="0" width="98%" height="98%" class="ml-4 mr-4">
                  <v-col cols="12" class="ml-2 mb-0 pb-0">
                    <v-row dense>
                      <v-col cols="12" md="12" sm="12">
                        <h2 style="font-weight: 700; font-size: 20px;">{{ $t('Business.TextBusinessID') }}</h2>
                      </v-col>
                      <v-container>
                        <v-radio-group
                          v-model="selectType"
                          row
                          hide-details
                        >
                          <!-- <v-radio
                            label="ลงทะเบียนโดยการกรอกข้อมูลใหม่"
                            color="#27AB9C"
                            value="none"
                          ></v-radio> -->
                          <!-- <v-radio
                            label="ลงทะเบียนโดยการดึงข้อมูลจากกรมสรรพากร"
                            color="#27AB9C"
                            value="TaxByRevenue"
                          ></v-radio> -->
                          <v-radio
                            :label="$t('Business.GetBusinessOneID')"
                            color="#27AB9C"
                            value="TaxByOneID"
                          ></v-radio>
                        </v-radio-group>
                      </v-container>
                      <!-- <v-row dense v-if="selectType">
                        <v-col cols="12" class="mt-0 pt-0 ml-8">
                          <v-radio-group
                            v-model="selectTypeOne"
                            row
                          >
                            <v-radio
                              label="ลงทะเบียนนิติบุคคล"
                              color="#27AB9C"
                              value="none"
                            ></v-radio>
                            <v-radio
                              label="ลงทะเบียนนิติบุคคลอื่นๆ"
                              color="#27AB9C"
                              value="TaxByOneID"
                            ></v-radio>
                          </v-radio-group>
                        </v-col>
                      </v-row> -->
                    </v-row>
                  </v-col>
                </v-card>
              </v-tab-item>
              <!-- v-if="checkeKYCUser" -->
              <v-tab-item :value="'tab-2'" v-if="checkeKYCUser === true">
                <!-- select type tax -->
                <v-card elevation="0" width="98%" height="98%" class="ml-4 mr-4">
                  <v-col cols="12" class="ml-2 mb-0 pb-0">
                    <v-row dense>
                      <v-col cols="12" md="12" sm="12">
                        <h2 style="font-weight: 700; font-size: 20px;">{{ $t('Business.PleaseSelectOtherBusinessID') }}</h2>
                      </v-col>
                      <v-container>
                        <v-radio-group
                          v-model="selectTypeOther"
                          row
                          hide-details
                        >
                          <v-radio
                            :label="$t('Business.RegisOtherNew')"
                            color="#27AB9C"
                            value="noneOther"
                          ></v-radio>
                          <!-- <v-radio
                            label="ลงทะเบียนโดยการดึงข้อมูลจากกรมสรรพากร"
                            color="#27AB9C"
                            value="TaxByRevenue"
                          ></v-radio> -->
                          <!-- <v-radio
                            label="ลงทะเบียนโดยการดึงข้อมูลจาก One ID"
                            color="#27AB9C"
                            value="TaxByOneIDOther"
                          ></v-radio> -->
                        </v-radio-group>
                      </v-container>
                      <!-- <v-row dense v-if="selectType">
                        <v-col cols="12" class="mt-0 pt-0 ml-8">
                          <v-radio-group
                            v-model="selectTypeOne"
                            row
                          >
                            <v-radio
                              label="ลงทะเบียนนิติบุคคล"
                              color="#27AB9C"
                              value="none"
                            ></v-radio>
                            <v-radio
                              label="ลงทะเบียนนิติบุคคลอื่นๆ"
                              color="#27AB9C"
                              value="TaxByOneID"
                            ></v-radio>
                          </v-radio-group>
                        </v-col>
                      </v-row> -->
                    </v-row>
                  </v-col>
                </v-card>
              </v-tab-item>
            </v-tabs-items>
            <!-- form input tax_ID -->
            <!-- <v-row dense> -->
            <v-col cols="12" class="ml-6 mt-0 mb-0 pb-0">
              <v-form ref="FormTaxID" :lazy-validation="lazy1" :style="showtaxSearch === true ? 'dispaly: block' : 'display: none'">
                <v-col cols="12" class="mt-0 pt-0">
                  <v-row dense>
                    <v-col cols="12" md="2" sm="12" class="pt-4">
                      <span>{{ $t('CheckOut.TaxNumber') }} :</span>
                    </v-col>
                    <v-col cols="12" md="3" sm="12">
                      <v-text-field  v-model="taxIDSearch" @input="validateTaxID($event)" :placeholder="$t('Business.PleaseTaxID')" outlined dense :rules="Rules.thaiID" :maxLength="13" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="CheckSpacebar($event)"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="1" sm="12" class="mt-1">
                      <v-btn small color="#27AB9C" @click="searchTaxID()" dark>{{ $t('Business.SearchTax') }}</v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-form>
            </v-col>
            <!-- form input tax_ID Other -->
            <!-- <v-row dense> -->
            <v-col cols="12" class="ml-6 mt-0 mb-0 pb-0">
              <v-form ref="FormTaxIDOther" :lazy-validation="lazy2" :style="showtaxSearchOther === true ? 'dispaly: block' : 'display: none'">
                <v-col cols="12" class="mt-0 pt-0">
                  <v-row dense>
                    <v-col cols="12" md="2" sm="12" class="pt-4">
                      <span>{{ $t('CheckOut.TaxNumber') }} :</span>
                    </v-col>
                    <v-col cols="12" md="3" sm="12">
                      <v-text-field  v-model="taxIDSearchOther" :placeholder="$t('Business.PleaseTaxID')" outlined dense :rules="Rules.empty" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="1" sm="12" class="mt-1">
                      <v-btn small color="#27AB9C" @click="searchTaxIDOther()" dark>{{ $t('Business.SearchTax') }}</v-btn>
                    </v-col>
                  </v-row>
                </v-col>
              </v-form>
            </v-col>
          <!-- </v-row> -->
          <!-- นิติบุคคล -->
          <v-form ref="Formcreate" :lazy-validation="lazy">
            <!-- :style="showForm === true ? 'dispaly: block' : 'display: none'" -->
            <div :style="showForm === true ? 'dispaly: block' : 'display: none'">
              <v-card outlined class="mt-0 mx-4 mb-4" style="border: 1px solid #27AB9C;">
                <v-card-title>
                  <h4 style="font-weight: 700; font-size: 18px;">{{ $t('Business.BusinessInfo') }}</h4>
                </v-card-title>
                <v-card-text>
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.TypeBusiness') }}</span><span style="color: red;"> *</span>
                      <v-select
                        v-model="businessType"
                        :items="businessTypeTHItem"
                        item-text="nameTH"
                        item-value="value"
                        :placeholder="$t('Business.TypeBusiness')"
                        outlined
                        :readonly="typeTaxID === 'TaxByOneID' ? true : false"
                        dense
                        :rules="Rules.empty"
                      >
                      </v-select>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.CompanyName') }}</span><span style="color: red;"> *</span>
                      <v-text-field v-model="businessNameTH" :placeholder="$t('Business.CompanyName')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessNameTHRules" @keypress="isLetterThai($event)">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4" :class="IpadSize ? 'pr-2' : ''">
                      <span>{{ $t('Business.NameDoc') }}</span><span style="color: red;"> *</span>
                      <v-text-field v-model="businessDocNameTH" :placeholder="$t('Business.NameDoc')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessDocNameTHRules" @keypress="isLetterThai($event)">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- ชื่ออังกฤษ -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.TypeBusinessEng') }}</span><span style="color: red;"> *</span>
                        <v-select
                        v-model="businessType"
                        :items="businessTypeTHItem"
                        item-text="nameEN"
                        item-value="value"
                        :placeholder="$t('Business.TypeBusinessEng')"
                        outlined
                        :readonly="typeTaxID === 'TaxByOneID' ? true : false"
                        dense
                        :rules="Rules.empty"
                      >
                        </v-select>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.CompanyNameEng') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="businessNameEN" :placeholder="$t('Business.CompanyNameEng')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessNameENRules" @keypress="isLetterEng($event)">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4" :class="IpadSize ? 'pr-2' : ''">
                      <span>{{ $t('Business.NameDocEng') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="businessDocNameEN" :placeholder="$t('Business.NameDocEng')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.businessDocNameENRules" @keypress="isLetterEng($event)">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- เลขประจำตัวผู้เสียภาษี -->
                  <v-row>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('CheckOut.TaxNumber') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="taxId" :placeholder="$t('CheckOut.TaxNumber')" @input="validateTaxID($event)" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.thaiID" :maxlength="max" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.Email') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="mail" :placeholder="$t('Business.Email')" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.emailRules" @keypress="isLetterEngEmail($event)">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- สาขา -->
                  <v-row>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.BranchName') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="branchName" :placeholder="$t('Business.BranchName')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense :rules="Rules.empty">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.BranchCode') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="branchCode" :placeholder="$t('Business.BranchInput')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :disabled='disabledBranchCode' :maxlength='5'  :rules="Rules.branchno">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- เบอร์โทรศัพท์ -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.TelephoneNum') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="tel" :placeholder="$t('Business.TelephoneNum')" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :maxlength="maxTel" :rules="Rules.tel" v-mask="'##-###-####'">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.MobileNumber') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="phone" :placeholder="$t('Business.MobileNumber')" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.tel" :maxlength="maxPhone" v-mask="'###-###-####'">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.FaxNumber') }}</span>
                      <v-text-field  v-model="fax" :placeholder="$t('Business.FaxNumber')" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :maxlength="maxTel" v-mask="'##-###-####'">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- ที่อยู่ตามใบกำกับภาษีของร้าน -->
                  <v-card-title class="pl-0">
                    <h4 style="font-weight: 700; font-size: 18px;">{{ $t('Business.TitleTaxStore') }}</h4>
                  </v-card-title>
                  <!-- ที่อยู่ -->
                  <v-row>
                    <v-col cols="12" md="6" sm="4">
                      <span>{{ $t('Business.AddressCode') }}</span>
                      <v-text-field  v-model="addressCode" :placeholder="$t('Business.AddressCode')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense >
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="8">
                      <span>{{ $t('Business.Address') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="houseNo" :placeholder="$t('Business.Address')" outlined dense :readonly="typeTaxID === 'TaxByOneID' ? true : false" :rules="Rules.empty">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- เลขที่ห้อง  -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.HouseNumber') }}</span>
                      <v-text-field  v-model="roomNo" :placeholder="$t('Business.HouseNumber')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense >
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.FloorNo') }}</span>
                      <v-text-field  v-model="floor" :placeholder="$t('Business.FloorNo')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Building') }}</span>
                      <v-text-field  v-model="buildingName" :placeholder="$t('Business.Building')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- หมู่บ้าน -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Village') }} </span>
                      <v-text-field  v-model="houseName" :placeholder="$t('Business.Village')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.VillageNo') }} </span>
                      <v-text-field  v-model="houseGroup" :placeholder="$t('Business.VillageNo')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Alley') }}</span>
                      <v-text-field  v-model="alley" :placeholder="$t('Business.Alley')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แยก -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Junction') }} </span>
                    <v-text-field v-model="cross" :placeholder="$t('Business.Junction')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Road') }} </span>
                      <v-text-field v-model="road" :placeholder="$t('Business.Road')" :readonly="typeTaxID === 'TaxByOneID' ? true : false" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Subdistrict') }}<span style="color: red;"> *</span></span>
                      <addressinput-subdistrict :rules="Rules.empty" label="" v-model="subdistrict" v-if="typeTaxID === 'none'" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.Subdistrict')"/>
                      <div v-if="typeTaxID === 'none' && checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <!-- <v-autocomplete outlined dense :items="itemProvince" v-model="province" item-text="name_th" item-value="id" placeholder="จังหวัด" :rules="Rules.empty"  v-if="typeTaxID === 'none' && "></v-autocomplete> -->
                      <v-text-field outlined dense v-model="subdistricttext" :placeholder="$t('Business.Subdistrict')" :rules="Rules.empty" v-if="typeTaxID !== 'none'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- ตำบล -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.District') }}</span><span style="color: red;"> *</span>
                      <addressinput-district label="" v-if="typeTaxID === 'none'" v-model="district" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.District')" />
                      <div v-if="typeTaxID === 'none' && checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <!-- <v-autocomplete outlined dense :items="itemDistrict" v-model="district" item-text="name_th" item-value="id" placeholder="อำเภอ" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                      <v-text-field outlined dense v-model="districttext" :placeholder="$t('Business.District')" :rules="Rules.empty" v-if="typeTaxID !== 'none'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Province') }}</span><span style="color: red;"> *</span>
                      <addressinput-province label="" v-if="typeTaxID === 'none'" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.Province')" />
                      <div v-if="typeTaxID === 'none' && checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <!-- <v-autocomplete outlined dense :items="itemSubdistrict" v-model="subdistrict" item-text="name_th" item-value="id" placeholder="ตำบล" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                      <v-text-field outlined dense v-model="provincetext" :placeholder="$t('Business.Province')" :rules="Rules.empty" v-if="typeTaxID !== 'none'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.PostCode') }}</span><span style="color: red;"> *</span>
                      <addressinput-zipcode label="" v-if="typeTaxID === 'none'" numbered v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.PostCode')" />
                      <div v-else-if="typeTaxID === 'none' && checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <v-text-field disabled v-if="typeTaxID !== 'none'" label="" v-model="zipcode" :placeholder="$t('Business.PostCode')" required outlined dense/>
                    </v-col>
                  </v-row>
                  <!-- อัพโหลดหนังสือรองรับนิติบุคคล -->
                  <v-card-title class="pl-0" v-if="typeTaxID === 'none'">
                    <h4 style="font-weight: 700; font-size: 18px;">เอกสารแนบ</h4>
                  </v-card-title>
                  <v-row v-if="typeTaxID === 'none'">
                    <v-col cols="12" md="12" class="mt-0">
                      <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                        <v-card-title>อัปโหลดหนังสือรับรองนิติบุคคล</v-card-title>
                        <v-card-text>
                          <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFile()">
                            <v-card-text>
                              <v-row no-gutters justify="center" style="cursor: pointer;">
                                <v-file-input
                                  v-model="DataImage"
                                  :items="DataImage"
                                  accept=".pdf"
                                  @change="UploadImage()"
                                  id="file_input"
                                  multiple
                                  :clearable="false"
                                  style="display:none">
                                </v-file-input>
                                <v-col cols="12" md="12" class="mb-6">
                                  <v-row justify="center" class="pt-10">
                                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="280.34" height="154.87" contain></v-img>
                                  </v-row>
                                </v-col>
                                <v-col cols="12" md="12" class="mt-6">
                                  <v-row justify="center">
                                    <v-col cols="12" md="4" style="text-align: center;">
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุ ไฟล์ควรมีขนาดไม่เกิน 10MB</span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <div v-if="file.length !== 0" class="mt-4">
                            <draggable v-model="file"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                              <v-col v-for="(item, index) in file" :key="index" cols="12" md="2" sm="3">
                                <v-card outlined class="pa-1" width="146" height="100%">
                                  <v-card-text>
                                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="130" height="130" contain>
                                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                        <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                      </v-btn>
                                    </v-img>
                                    <p style="text-align: center;">{{ item.name }}</p>
                                  </v-card-text>
                                </v-card>
                              </v-col>
                            </draggable>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                  <v-row justify="right" class="my-4 ml-4" v-if="typeTaxID === 'none'">
                    <h3 style="font-weight: bold; color: color: #333333; font-size: 24px; line-height: 32px;">ดาวน์โหลดเอกสาร</h3>
                  </v-row>
                  <v-row justify="right" dense class="mb-6 ml-4" v-if="typeTaxID === 'none'">
                    <v-col cols="12" md="12">
                      <v-row>
                        <v-card outlined :width="!MobileSize ? '386px' : '100%'" :height="!MobileSize ? '100px' : '100%'" style="border-radius: 8px; border: 1px solid #F2F2F2;" :class="!MobileSize ? 'mr-4' : 'mb-2'" @click="DownloadForm('POA')" dense>
                          <!-- <v-card-text> -->
                            <v-row dense>
                              <v-col cols="2" md="3" sm="3" :class="!MobileSize ? 'mt-2 ml-2' : 'mt-2 mb-2'">
                                <v-avatar
                                :width="!MobileSize ? '84px' : '40px'"
                                :height="!MobileSize ? '84px' : '40px'"
                                color="#EBEEF6"
                                style="border-radius: 8px;"
                                >
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/word.png" :width="!MobileSize ? '60px' : '20px'" :height="!MobileSize ? '60px' : '20px'" contain></v-img>
                                </v-avatar>
                              </v-col>
                              <v-col cols="10" md="8" sm="8" :class="!MobileSize ? 'mt-6' : 'mt-2 pl-6'">
                                <span style="font-weight: bold; line-height: 24px; color: #333333;" :style="!MobileSize ? 'font-size: 16px; ' : 'font-size: 14px; '">หนังสือมอบอำนาจ.docx</span><br/>
                                <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 18 KB</span>
                              </v-col>
                            </v-row>
                          <!-- </v-card-text> -->
                        </v-card>
                        <v-card outlined :width="!MobileSize ? '386px' : '100%'" :height="!MobileSize ? '100px' : '100%'" style="border-radius: 8px; border: 1px solid #F2F2F2;" @click="DownloadForm('POAS')" dense>
                          <!-- <v-card-text> -->
                            <v-row dense>
                              <v-col cols="2" md="3" sm="3" :class="!MobileSize ? 'mt-2 ml-2' : 'mt-5 mb-2'">
                                <v-avatar
                                :width="!MobileSize ? '84px' : '40px'"
                                :height="!MobileSize ? '84px' : '40px'"
                                color="#EBEEF6"
                                style="border-radius: 8px;"
                                >
                                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/word.png" :width="!MobileSize ? '60px' : '20px'" :height="!MobileSize ? '60px' : '20px'" contain></v-img>
                                </v-avatar>
                              </v-col>
                              <v-col cols="10" md="8" sm="8" :class="!MobileSize ? 'mt-6' : 'mt-2 pl-6'">
                                <span style="font-weight: bold; font-size: 16px; line-height: 24px; color: #333333;" :style="!MobileSize ? 'font-size: 16px; ' : 'font-size: 14px; '">หนังสือมอบอำนาจช่วง.docx</span><br/>
                                <span style="font-size: 12px; line-height: 16px; color: #989898;">ขนาดไฟล์ 17 KB</span>
                              </v-col>
                            </v-row>
                          <!-- </v-card-text> -->
                        </v-card>
                      </v-row>
                    </v-col>
                  </v-row>
                  <!-- อัพโหลดหนังสือมอบอำนาจ -->
                  <v-row v-if="typeTaxID === 'none'">
                    <v-col cols="12" md="12" class="mt-0">
                      <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                        <v-card-title>อัปโหลดหนังสือมอบอำนาจ</v-card-title>
                        <v-card-text>
                          <v-card elevation="0" style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;" @click="onPickFile1()">
                            <v-card-text>
                              <v-row no-gutters align="center" justify="center" style="cursor: pointer;">
                                <v-file-input
                                  v-model="DataImage1"
                                  :items="DataImage1"
                                  accept=".pdf"
                                  @change="UploadImage1()"
                                  id="file_input1"
                                  multiple
                                  :clearable="false"
                                  style="display:none">
                                </v-file-input>
                                <v-col cols="12" md="12" class="mb-6">
                                  <v-row justify="center" class="pt-10">
                                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/Group_736.png" width="280.34" height="154.87" contain></v-img>
                                  </v-row>
                                </v-col>
                                <v-col cols="12" md="12" class="mt-6">
                                  <v-row justify="center" align="center">
                                    <v-col cols="12" md="4" style="text-align: center;">
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 400;">เพิ่มไฟล์ของคุณที่นี่</span><br/>
                                      <span style="font-size: 16px; line-height: 24px; font-weight: 400;">หรือเลือกไฟล์จากคอมพิวเตอร์ของคุณ</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;">(ไฟล์นามสกุล .pdf)</span><br/>
                                      <span style="font-size: 12px; line-height: 16px; font-weight: 400;"><span style="color: red;">***</span> หมายเหตุ ไฟล์ควรมีขนาดไม่เกิน 10MB</span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                              </v-row>
                            </v-card-text>
                          </v-card>
                          <div v-if="file1.length !== 0" class="mt-4">
                            <draggable v-model="file1"  :move="onMove1" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                              <v-col v-for="(item, index) in file1" :key="index" cols="12" md="2" sm="3">
                                <v-card outlined class="pa-1" width="146" height="100%">
                                  <v-card-text>
                                    <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="130" height="130" contain>
                                      <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                        <v-icon x-small color="white" dark @click="RemoveImage1(index, item)">mdi-close</v-icon>
                                      </v-btn>
                                    </v-img>
                                    <p style="text-align: center;">{{ item.name }}</p>
                                  </v-card-text>
                                </v-card>
                              </v-col>
                            </draggable>
                          </div>
                        </v-card-text>
                      </v-card>
                    </v-col>
                  </v-row>
                </v-card-text>
                <!-- ปุ่ม -->
                <v-card-actions class="mb-4">
                  <v-spacer></v-spacer>
                  <v-btn outlined color="#27AB9C" class="pl-8 pr-8" @click="cancelPage()">{{ $t('Business.Cancel') }}</v-btn>
                  <v-btn color="#27AB9C" dark @click="createbiz()" class="pl-4 pr-4">{{ $t('Business.Register') }}</v-btn>
                </v-card-actions>
              </v-card>
            </div>
          </v-form>
          <!-- นิติบุคคลอื่นๆ -->
          <v-form ref="FormcreateOther" :lazy-validation="lazy3">
            <!-- :style="showFormOther === true ? 'dispaly: block' : 'display: none'" -->
            <div :style="showFormOther === true ? 'dispaly: block' : 'display: none'">
              <v-card outlined class="mt-0 mx-4 mb-4" style="border: 1px solid #27AB9C;">
                <v-card-title>
                  <h4 style="font-weight: 700; font-size: 18px;">{{ $t('Business.OtherBusinessID') }}</h4>
                </v-card-title>
                <v-card-text>
                  <v-row>
                    <!-- ประเภทกิจการ -->
                    <v-col cols="12" md="6" sm="12">
                      <span>{{ $t('Business.Businesstype') }}</span><span style="color: red;"> *</span>
                      <v-select
                        v-model="businessTypeOther"
                        :items="businessTypeTHItemOther"
                        item-text="nameTH"
                        item-value="value"
                        :placeholder="$t('Business.Businesstype')"
                        outlined
                        dense
                        :rules="Rules.empty"
                      >
                      </v-select>
                    </v-col>
                  </v-row>
                  <!-- ชื่อกิจการ -->
                  <v-row>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.BusinessName') }}</span><span style="color: red;"> *</span>
                      <v-text-field v-model="businessNameTHOther" :placeholder="$t('Business.BusinessName')" outlined dense :rules="Rules.businessNameTHOtherRules" @keypress="isLetterThai($event)">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.BusinessNameEng') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="businessNameENOther" :placeholder="$t('Business.BusinessNameEng')" outlined dense :rules="Rules.businessNameENOtherRules" @keypress="isLetterEng($event)">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- เลขประจำตัวผู้เสียภาษี -->
                  <v-row>
                    <v-col cols="12" md="12" sm="12">
                      <span>{{ $t('Business.TaxNumberOther') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="taxIdOther" :placeholder="$t('Business.TaxNumberOther')" outlined dense :rules="Rules.empty" :maxlength="max" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')">
                      </v-text-field>
                    </v-col>
                    <!-- <v-col cols="12" md="3">
                      <v-btn color="#3cc474" dark rounded class="mt-5 px-4">ตรวจสอบ ONE ID</v-btn>
                    </v-col> -->
                  </v-row>
                  <!-- เบอร์โทรศัพท์ -->
                  <v-row>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.Email') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="mailOther" :placeholder="$t('Business.Email')" outlined dense :rules="Rules.emailRules" @keypress="isLetterEngEmail($event)">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.TelephoneNum') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="telOther" :placeholder="$t('Business.TelephoneNum')" outlined dense :maxlength="12" :rules="Rules.tel" v-mask="'###-###-####'">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- สาขา -->
                  <v-row>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.BranchName') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="branchNameOther" :placeholder="$t('Business.BranchName')" outlined dense :rules="Rules.empty">
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="6">
                      <span>{{ $t('Business.BranchCode') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="branchCodeOther" :placeholder="$t('Business.BranchCode')" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" outlined dense :disabled='disabledBranchCodeOther' :maxlength='5'  :rules="Rules.branchno">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- ที่อยู่กิจการ -->
                  <v-card-title class="pl-0">
                    <h4 style="font-weight: 700; font-size: 18px;">{{ $t('Business.BusinessAddress') }}</h4>
                  </v-card-title>
                  <!-- ที่อยู่ -->
                  <v-row>
                    <v-col cols="12" md="6" sm="4">
                      <span>{{ $t('Business.AddressCode') }} </span>
                      <v-text-field  v-model="addressCodeOther" :placeholder="$t('Business.AddressCode')" outlined dense >
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="6" sm="8">
                      <span>{{ $t('Business.Address') }}</span><span style="color: red;"> *</span>
                      <v-text-field  v-model="houseNoOther" :placeholder="$t('Business.Address')" outlined dense :rules="Rules.empty">
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- เลขที่ห้อง  -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.HouseNumber') }} </span>
                      <v-text-field  v-model="roomNoOther" :placeholder="$t('Business.HouseNumber')" outlined dense >
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.FloorNo') }} </span>
                      <v-text-field  v-model="floorOther" :placeholder="$t('Business.FloorNo')" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Building') }} </span>
                      <v-text-field  v-model="buildingNameOther" :placeholder="$t('Business.Building')" outlined dense>
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- หมู่บ้าน -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Village') }} </span>
                      <v-text-field  v-model="houseNameOther" :placeholder="$t('Business.Village')" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.VillageNo') }} </span>
                      <v-text-field  v-model="houseGroupOther" :placeholder="$t('Business.VillageNo')" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Alley') }} </span>
                      <v-text-field  v-model="alleyOther" :placeholder="$t('Business.Alley')" outlined dense>
                      </v-text-field>
                    </v-col>
                  </v-row>
                  <!-- แยก -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Junction') }} </span>
                    <v-text-field v-model="crossOther" :placeholder="$t('Business.Junction')" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Road') }} </span>
                      <v-text-field v-model="roadOther" :placeholder="$t('Business.Road')" outlined dense>
                      </v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Subdistrict') }}<span style="color: red;"> *</span></span>
                      <addressinput-subdistrict :rules="Rules.empty" label="" v-model="subdistrictOther" v-if="typeTaxIDOther === 'noneOther'" :class="checkSubDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.Subdistrict')"/>
                      <div v-if="typeTaxIDOther === 'noneOther' && checkSubDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <!-- <v-autocomplete outlined dense :items="itemProvince" v-model="province" item-text="name_th" item-value="id" placeholder="จังหวัด" :rules="Rules.empty"  v-if="typeTaxID === 'none' && "></v-autocomplete> -->
                      <v-text-field outlined dense v-model="subdistricttext" :placeholder="$t('Business.Subdistrict')" :rules="Rules.empty" v-if="typeTaxIDOther !== 'noneOther'" :readonly="typeTaxIDOther === 'TaxByOneID' ? true : false"></v-text-field>
                    </v-col>
                  </v-row>
                  <!-- ตำบล -->
                  <v-row>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.District') }}</span><span style="color: red;"> *</span>
                      <addressinput-district label="" v-if="typeTaxIDOther === 'noneOther'" v-model="districtOther" :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.District')" />
                      <div v-if="typeTaxIDOther === 'noneOther' && checkDistrictError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <!-- <v-autocomplete outlined dense :items="itemDistrict" v-model="district" item-text="name_th" item-value="id" placeholder="อำเภอ" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                      <v-text-field outlined dense v-model="districttext" :placeholder="$t('Business.District')" :rules="Rules.empty" v-if="typeTaxIDOther !== 'noneOther'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.Province') }}</span><span style="color: red;"> *</span>
                      <addressinput-province label="" v-if="typeTaxIDOther === 'noneOther'" v-model="provinceOther" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.Province')" />
                      <div v-if="typeTaxIDOther === 'noneOther' && checkProvinceError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <!-- <v-autocomplete outlined dense :items="itemSubdistrict" v-model="subdistrict" item-text="name_th" item-value="id" placeholder="ตำบล" :rules="Rules.empty"  v-if="typeTaxID === 'none'"></v-autocomplete> -->
                      <v-text-field outlined dense v-model="provincetext" :placeholder="$t('Business.Province')" :rules="Rules.empty" v-if="typeTaxIDOther !== 'noneOther'" :readonly="typeTaxID === 'TaxByOneID' ? true : false"></v-text-field>
                    </v-col>
                    <v-col cols="12" md="4" sm="4">
                      <span>{{ $t('Business.PostCode') }}</span><span style="color: red;"> *</span>
                      <addressinput-zipcode label="" v-if="typeTaxIDOther === 'noneOther'" numbered v-model="zipcodeOther" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" :placeholder="$t('Business.PostCode')" />
                      <div v-else-if="typeTaxIDOther === 'noneOther' && checkZipcodeError" class="text-error">{{ $t('AddressProfilePage.ShippingAddressModal.error1') }}</div>
                      <v-text-field disabled v-if="typeTaxIDOther !== 'noneOther'" label="" v-model="zipcode" :placeholder="$t('Business.PostCode')" required outlined dense/>
                    </v-col>
                  </v-row>
                </v-card-text>
                <!-- ปุ่ม -->
                <v-card-actions class="mb-4">
                  <v-spacer></v-spacer>
                  <v-btn outlined color="#27AB9C" class="pl-8 pr-8" @click="cancelPage()">{{ $t('Business.Cancel') }}</v-btn>
                  <v-btn color="#27AB9C" dark @click="createbizOther()" class="pl-4 pr-4">{{ $t('Business.Register') }}</v-btn>
                </v-card-actions>
              </v-card>
            </div>
          </v-form>
        </v-container>
      </v-card>
    </v-container>
  </div>
</template>

<script>
import Vue from 'vue'
// import Province from '@/Thailand_Address/province'
// import District from '@/Thailand_Address/district'
// import Subdistrict from '@/Thailand_Address/subdistrict'ฃ
import draggable from 'vuedraggable'
// import ModalImage from '@/components/Modal/OPS'
import Address2021 from '@/Thailand_Address/address2021'
import VueMask from 'v-mask'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
Vue.use(VueThailandAddress)
Vue.use(VueMask)
export default {
  components: {
    // ModalImage,
    draggable
  },
  data () {
    return {
      taxIdBiz: null,
      DataImage: [],
      DataImage1: [],
      file: [],
      file1: [],
      tab: null,
      taxIDSearchOther: '',
      approveFileSuccess: '',
      resultDocumentSuccess: '',
      subdistricttext: '',
      subdistrictValue: '',
      provincetext: '',
      provinceValue: '',
      districttext: '',
      districtValue: '',
      lazy: false,
      lazy1: false,
      lazy2: false,
      lazy3: false,
      checkeKYCUser: false,
      max: 13,
      maxTel: 11,
      maxPhone: 12,
      taxIDSearch: '',
      disabledBranchCode: true,
      disabledBranchCodeOther: true,
      showtaxSearchOther: false,
      businessType: '',
      businessTypeTHItem: [
        { nameTH: 'ห้างหุ้นส่วนสามัญ', nameEN: 'Ordinary Partnership', value: 1 },
        { nameTH: 'ห้างหุ้นส่วนจำกัด', nameEN: 'Limited Partnership', value: 2 },
        { nameTH: 'บริษัทจำกัด', nameEN: 'Company Limited', value: 3 },
        { nameTH: 'บริษัทมหาชนจำกัด', nameEN: 'Public Limited Company', value: 4 },
        { nameTH: 'นิติบุคคลอื่นๆ ภายใต้กฎหมายเฉพาะ', nameEN: 'Other', value: 5 }
      ],
      businessNameTH: '',
      businessDocNameTH: '',
      businessNameEN: '',
      businessDocNameEN: '',
      taxId: '',
      branchName: '',
      branchCode: '',
      mail: '',
      tel: '',
      phone: '',
      fax: '',
      addressCode: '',
      address: '',
      roomNo: '',
      floor: '',
      buildingName: '',
      houseName: '',
      houseNo: '',
      houseGroup: '',
      alley: '',
      cross: '',
      road: '',
      province: '',
      district: '',
      subdistrict: '',
      zipcode: '',
      provinceOther: '',
      districtOther: '',
      subdistrictOther: '',
      zipcodeOther: '',
      account_title_th: '',
      account_title_eng: '',
      items: [
        {
          text: this.$t('Headers.Home'),
          disabled: false,
          color: '#636363',
          href: '/'
        },
        {
          text: this.$t('Business.BusinessInfo'),
          disabled: true,
          color: '#3EC6B6',
          href: ''
        }
      ],
      Data: require('thai-data'),
      Rules: {
        required: [{ required: true, message: this.$t('Business.PleaseInputData') }],
        empty: [v => !!v || this.$t('Business.PleaseInputData')],
        thaiID: [
          v => !!v || this.$t('Business.PleaseInputData'),
          v => this.validNationalID(v) || this.$t('Business.PleaseTaxIDCorrect')
        ],
        branchno: [
          v => !!v || this.$t('Business.PleaseInputBranchID'),
          v => (v.length >= 5 && v !== null) || this.$t('Business.PleaseBranchCorrect')
        ],
        emailRules: [
          v => !!v || this.$t('Business.PleaseInputEmail'),
          // v => /^[A-Za-z0-9_@.,/#&+-\s]+$/.test(v) || 'กรุณากรอกเฉพาะภาษาอังกฤษ',
          v => /.+@.+\..+/.test(v) || this.$t('Business.PleaseEmailCorrect'),
          v => /^\S*$/.test(v) || this.$t('Business.PleaseEmailNotNull')
        ],
        businessNameTHRules: [
          v => !!v || this.$t('Business.PleaseInputCompanyName'),
          v => /^[ก-๏0-9๐-๙():.,-\s]+$/.test(v) || this.$t('Business.PleaseInputThai')
        ],
        businessNameTHOtherRules: [
          v => !!v || this.$t('Business.PleaseInputBusinessName'),
          v => /^[ก-๏0-9๐-๙():.,-\s]+$/.test(v) || this.$t('Business.PleaseInputThai')
        ],
        businessDocNameTHRules: [
          v => !!v || this.$t('Business.PleaseInputNameDoc'),
          v => /^[ก-๏0-9๐-๙():.,-\s]+$/.test(v) || this.$t('Business.PleaseInputThai')
        ],
        businessNameENRules: [
          v => !!v || this.$t('Business.PleaseInputCompanyNameEng'),
          v => /^[A-Za-z0-9():&.,-\s]+$/.test(v) || this.$t('Business.PleaseInputEng')
        ],
        businessNameENOtherRules: [
          v => !!v || this.$t('Business.PleaseInputBusinessNameEng'),
          v => /^[A-Za-z0-9():&.,-\s]+$/.test(v) || this.$t('Business.PleaseInputEng')
        ],
        businessDocNameENRules: [
          v => !!v || this.$t('Business.PleaseInputNameDocEng'),
          v => /^[A-Za-z0-9():&.,-\s]+$/.test(v) || this.$t('Business.PleaseInputEng')
        ],
        shopNameTH: [
          v => !!v || this.$t('Business.PleaseInputShop')
        ],
        shopNameEN: [
          v => !!v || this.$t('Business.PleaseInputShopEng')
        ],
        tel: [
          v => !!v || this.$t('Business.PleaseInputTel')
        ],
        address: [
          v => !!v || this.$t('Business.PleaseInputAddress')
        ]
      },
      fileRecords: [],
      fileRecords2: [],
      uploadUrl: 'https://www.mocky.io/v2/5d4fb20b3000005c111099e3',
      uploadHeaders: { 'X-Test-Header': 'vue-file-agent' },
      fileRecordsForUpload: [], // maintain an upload queue
      showForm: false,
      showFormOther: false,
      selectType: null,
      selectTypeOne: null,
      showtaxSearch: false,
      typeTaxID: '',
      checkTypeSelect: false,
      checkSubDistrictError: false,
      checkDistrictError: false,
      checkProvinceError: false,
      checkZipcodeError: false,
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      selectTypeOther: '',
      businessTypeOther: '',
      businessTypeTHItemOther: [
        { nameTH: 'การค้าเร่ การค้าแผงลอย', value: '0' },
        { nameTH: 'กิจการเพื่อการบำรุงศาสนาหรือเพื่อการกุศล', value: '1' },
        { nameTH: 'กิจการของนิติบุคคลซึ่งได้มีพระราชบัญญัติ หรือพระราชกฤษฎีกาจัดตั้งขึ้น', value: '2' },
        { nameTH: 'กิจการของกระทรวง ทบวง กรม', value: '3' },
        { nameTH: 'กิจการของมูลนิธิ สมาคม สหกรณ์', value: '4' },
        { nameTH: 'พาณิชยกิจซึ่งรัฐมนตรีได้ประกาศในราชกิจจานุเบกษา', value: '5' },
        { nameTH: 'อื่น ๆ', value: '6' }
      ],
      businessNameTHOther: '',
      businessNameENOther: '',
      taxIdOther: '',
      mailOther: '',
      telOther: '',
      branchNameOther: '',
      branchCodeOther: '',
      addressCodeOther: '',
      houseNoOther: '',
      roomNoOther: '',
      floorOther: '',
      buildingNameOther: '',
      houseNameOther: '',
      houseGroupOther: '',
      alleyOther: '',
      crossOther: '',
      roadOther: '',
      typeTaxIDOther: ''
    }
  },
  created () {
    // this.itemProvince = Province.provinces
    // const GetallData = this.Data.allData().map((item) => {
    //   return item.zipCode
    // })
    // this.zipCodeData = GetallData
    if (localStorage.getItem('oneData') === null) {
      this.$router.push({ path: '/' }).catch(() => {})
    } else {
      this.checkBusinessUser()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  watch: {
    tab (val) {
      // console.log(val)
      if (val === 'tab-1') {
        this.selectType = ''
        this.selectTypeOther = ''
        this.typeTaxID = ''
        this.showForm = false
        this.showFormOther = false
        this.showtaxSearch = false
        this.showtaxSearchOther = false
        this.$refs.FormcreateOther.reset()
        this.$refs.Formcreate.reset()
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
        this.province = ''
        this.district = ''
        this.subdistrict = ''
        this.zipcode = ''
        this.provinceOther = ''
        this.districtOther = ''
        this.subdistrictOther = ''
        this.zipcodeOther = ''
      } else {
        this.selectType = ''
        this.selectTypeOther = ''
        this.typeTaxID = ''
        this.showForm = false
        this.showFormOther = false
        this.showtaxSearch = false
        this.showtaxSearchOther = false
        this.$refs.FormcreateOther.reset()
        this.$refs.Formcreate.reset()
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
        this.province = ''
        this.district = ''
        this.subdistrict = ''
        this.zipcode = ''
        this.provinceOther = ''
        this.districtOther = ''
        this.subdistrictOther = ''
        this.zipcodeOther = ''
      }
    },
    selectTypeOther (val) {
      // console.log(val)
      if (val === 'TaxByOneIDOther') {
        this.typeTaxIDOther = val
        this.$refs.FormTaxIDOther.resetValidation()
        this.showtaxSearchOther = true
        this.taxIDSearchOther = ''
        this.$refs.FormcreateOther.reset()
        this.showFormOther = false
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else if (val === 'noneOther') {
        this.typeTaxIDOther = val
        this.$refs.FormTaxIDOther.resetValidation()
        this.showtaxSearchOther = false
        this.taxIDSearchOther = ''
        this.showFormOther = true
        this.$refs.FormcreateOther.reset()
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      }
    },
    selectType (val) {
      // console.log(val)
      if (val === 'TaxByRevenue') {
        this.typeTaxID = val
        this.$refs.Formcreate.reset()
        this.showForm = false
        this.showtaxSearch = true
        this.taxIDSearch = ''
        this.$refs.FormTaxID.resetValidation()
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else if (val === 'TaxByOneID') {
        this.typeTaxID = val
        this.$refs.Formcreate.reset()
        this.showForm = false
        this.showtaxSearch = true
        this.taxIDSearch = ''
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
        this.$refs.FormTaxID.resetValidation()
      } else if (val === 'none') {
        this.$refs.Formcreate.reset()
        this.typeTaxID = val
        this.showForm = true
        this.showtaxSearch = false
        this.showFormOther = false
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      }
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      // this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '' && val !== null) {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    },
    subdistrictOther (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
        }
      } else {
        this.zipcodeOther = ''
        this.districtOther = ''
        this.provinceOther = ''
      }
    },
    districtOther (val) {
      this.checkDistrictError = false
      // this.statusError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
        }
      } else {
        this.zipcodeOther = ''
        this.subdistrictOther = ''
        this.provinceOther = ''
      }
    },
    provinceOther (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
        }
      } else {
        this.zipcodeOther = ''
        this.subdistrictOther = ''
        this.districtOther = ''
      }
    },
    zipcodeOther (val) {
      this.checkZipcodeError = false
      // console.log(val)
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
        }
      } else {
        this.subdistrictOther = ''
        this.districtOther = ''
        this.provinceOther = ''
      }
    },
    // province (val) {
    //   console.log('pv', val)
    //   const result = District.districts.filter((data) => {
    //     return data.province_id === val
    //   })
    //   this.itemDistrict = result
    //   const dataValue = Province.provinces.filter((data) => {
    //     return data.id === val
    //   })
    //   this.provinceValue = dataValue[0].name_th
    //   console.log('provinceValue', this.provinceValue)
    //   this.subdistrict = ''
    //   this.zipcode = ''
    // },
    // district (val) {
    //   console.log('pv', val)
    //   const result = Subdistrict.subdistricts.filter((data) => {
    //     return data.amphure_id === val
    //   })
    //   this.itemSubdistrict = result
    //   const dataValue = District.districts.filter((data) => {
    //     return data.id === val
    //   })
    //   this.districtValue = dataValue[0].name_th
    //   console.log('districtValue', this.districtValue)
    //   this.zipcode = ''
    // },
    // subdistrict (val) {
    //   console.log('pv', val)
    //   const result = Subdistrict.subdistricts.filter((data) => {
    //     return data.id === val
    //   })
    //   this.subdistrictValue = result[0].name_th
    //   console.log('subdistrictValue', this.subdistrictValue)
    //   this.zipcode = result[0].zip_code
    //   console.log('zipcode', this.zipcode)
    // },
    businessType (val) {
      // console.log('val', val)
      if (val === 1) {
        this.account_title_th = 'ห้างหุ้นส่วนสามัญ'
        this.account_title_eng = 'Ordinary Partnership'
      } else if (val === 2) {
        this.account_title_th = 'ห้างหุ้นส่วนจำกัด'
        this.account_title_eng = 'Limited Partnership'
      } else if (val === 3) {
        this.account_title_th = 'บริษัทจำกัด'
        this.account_title_eng = 'Company Limited'
      } else if (val === 4) {
        this.account_title_th = 'บริษัทมหาชนจำกัด'
        this.account_title_eng = 'Public Limited Company'
      } else {
        this.account_title_th = 'นิติบุคคลอื่นๆ ภายใต้กฎหมายเฉพาะ'
        this.account_title_eng = 'Other'
      }
      // console.log(this.account_title_eng, this.account_title_th)
    },
    taxId (val) {
      if (val !== '') {
        this.disabledBranchCode = false
      } else {
        this.disabledBranchCode = true
      }
    },
    taxIdOther (val) {
      if (val !== '') {
        this.disabledBranchCodeOther = false
      } else {
        this.disabledBranchCodeOther = true
      }
    }
  },
  methods: {
    async checkBusinessUser () {
      // await this.$store.dispatch('actionsAuthorityUser')
      // var response = await this.$store.state.ModuleUser.stateAuthorityUser
      var response = []
      if (this.$store.getters.getDataAuthorityUser.length !== 0) {
        response = await this.$store.getters.getDataAuthorityUser
      } else {
        await this.$store.dispatch('actionsAuthorityUser')
        response = await this.$store.state.ModuleUser.stateAuthorityUser
      }
      if (response.data.list_business.length !== 0) {
        if (response.data.list_business[0].status === 'verified') {
          if (this.MobileSize) {
            this.$swal.fire({ icon: 'warning', text: this.$t('Business.MessageAlready'), showConfirmButton: false, timer: 1500 })
            this.getTaxId()
            this.$router.push({ path: '/detailbusinesssidMobile' }).catch(() => {})
          } else if (!this.MobileSize) {
            this.$swal.fire({ icon: 'warning', text: this.$t('Business.MessageAlready'), showConfirmButton: false, timer: 1500 })
            this.getTaxId()
            this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
          }
        } else if (response.data.list_business[0].status === 'not_verified') {
          this.$swal.fire({ icon: 'warning', text: this.$t('Business.MessageWaiting'), showConfirmButton: false })
          // this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
        } else {
          this.$swal.fire({ icon: 'warning', text: this.$t('Business.OfficeCall'), showConfirmButton: false, timer: 1500 })
          // this.$router.push({ path: '/businessregister' }).catch(() => {})
        }
      }
    },
    validateTaxID (val) {
      if (this.validNationalID(val)) {
        return true
      } else {
        return false
      }
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    async checkeKYC () {
      // alert('*********')
      // this.checkeKYCUser = false
      await this.$store.dispatch('actionsCheckeKYC')
      var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      // console.log(response)
      if (response.result === 'SUCCESS') {
        this.checkeKYCUser = response.data.eKYC_approve === 'yes'
        if (response.data.eKYC_approve === 'no') {
          this.checkeKYCUser = false
          if (this.MobileSize) {
            this.$swal.fire({ icon: 'error', title: '<h4><b>' + this.$t('Business.ModalEKYC.YoureKYC') + '</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">' + this.$t('Business.ModalEKYC.NotVerified') + '</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">' + this.$t('Business.ModalEKYC.TextFirst') + ' </p><span style="line-height: normal;">' + this.$t('Business.ModalEKYC.TextTwo') + '</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">' + this.$t('Business.ModalEKYC.ClickVerified') + '</a>', showConfirmButton: false })
          } else {
            this.$swal.fire({ icon: 'error', title: '<h4><b>' + this.$t('Business.ModalEKYC.YoureKYC') + '</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">' + this.$t('Business.ModalEKYC.NotVerified') + '</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">' + this.$t('Business.ModalEKYC.TextFirst') + ' </p><span style="line-height: normal;">' + this.$t('Business.ModalEKYC.TextTwo') + '</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">' + this.$t('Business.ModalEKYC.ClickVerified') + '</a>', showConfirmButton: false })
          }
        } else {
          if (response.data.eKYC_expire === 'yes') {
            if (this.MobileSize) {
              this.$swal.fire({ icon: 'error', title: '<h4><b>' + this.$t('Business.ModalEKYC.YoureKYC') + '</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">' + this.$t('Business.ModalEKYC.Expire') + '</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">' + this.$t('Business.ModalEKYC.ReneweKYC') + ' </p><span style="line-height: normal;">' + this.$t('Business.ModalEKYC.ExpireFirst') + '</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">' + this.$t('Business.ModalEKYC.ClickRenew') + '</a>', showConfirmButton: false })
            } else {
              this.$swal.fire({ icon: 'error', title: '<h4><b>' + this.$t('Business.ModalEKYC.YoureKYC') + '</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">' + this.$t('Business.ModalEKYC.Expire') + '</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">' + this.$t('Business.ModalEKYC.ReneweKYC') + ' </p><span style="line-height: normal;">' + this.$t('Business.ModalEKYC.ExpireFirst') + '</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">' + this.$t('Business.ModalEKYC.ClickRenew') + '</a>', showConfirmButton: false })
            }
          }
        }
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    isLetterEng (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z0-9():&.,-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    isLetterEngEmail (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[A-Za-z0-9_@.,/#&+-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    isLetterThai (e) {
      const char = String.fromCharCode(e.keyCode)
      if (/^[ก-๏0-9๐-๙():.,-\s]+$/.test(char)) {
        return true
      } else {
        e.preventDefault()
      }
    },
    DownloadForm (val) {
      if (val === 'POA') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88.docx')
      } else if (val === 'POAS') {
        window.open('https://one.th/form/%E0%B8%9F%E0%B8%AD%E0%B8%A3%E0%B9%8C%E0%B8%A1%E0%B8%AB%E0%B8%99%E0%B8%B1%E0%B8%87%E0%B8%AA%E0%B8%B7%E0%B8%AD%E0%B8%A1%E0%B8%AD%E0%B8%9A%E0%B8%AD%E0%B8%B3%E0%B8%99%E0%B8%B2%E0%B8%88%E0%B8%8A%E0%B9%88%E0%B8%A7%E0%B8%87.docx')
      }
    },
    OpenModal () {
      this.$EventBus.$emit('openOPS')
    },
    cancelPage () {
      this.selectType = ''
      this.file = []
      this.file1 = []
      this.approveFileSuccess = ''
      this.resultDocumentSuccess = ''
      this.showForm = false
      window.scrollTo(0, 0)
    },
    async createbiz () {
      this.$store.commit('openLoader')
      if (this.$refs.Formcreate.validate(true)) {
        // console.log(this.tel, this.fax)
        if (this.typeTaxID === 'none') {
          if ((this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode))) {
            if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
              const check = this.checkSendAddress()
              if (check.length !== 0) {
                if (this.approveFileSuccess === '' && this.resultDocumentSuccess === '') {
                  this.$swal.fire({ icon: 'warning', text: 'กรุณาอัปโหลดหนังสือรองรับนิติบุคคลและหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
                } else if (this.approveFileSuccess !== '' && this.resultDocumentSuccess === '') {
                  this.$swal.fire({ icon: 'warning', text: 'กรุณาอัปโหลดหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
                } else if (this.approveFileSuccess === '' && this.resultDocumentSuccess !== '') {
                  this.$swal.fire({ icon: 'warning', text: 'กรุณาอัปโหลดหนังสือรองรับนิติบุคคล', showConfirmButton: false, timer: 1500 })
                } else {
                  var formDataBusiness = {
                    account_title_th: this.account_title_th,
                    first_name_th: this.businessNameTH,
                    account_title_eng: this.account_title_eng,
                    first_name_eng: this.businessNameEN,
                    id_card_num: this.taxId,
                    email: this.mail,
                    tel_no: this.tel !== null ? this.tel.replace(/-/g, '') : '',
                    mobile_no: this.phone !== null ? this.phone.replace(/-/g, '') : '',
                    approve_document: this.approveFileSuccess,
                    result_meeting: this.resultDocumentSuccess,
                    thai_email: this.mail,
                    department: 'ฝ่ายบริหาร',
                    position: 'CEO',
                    house_code: this.addressCode,
                    house_no: this.houseNo,
                    room_no: this.roomNo,
                    moo_ban: this.houseName,
                    moo_no: this.houseGroup,
                    building_name: this.buildingName,
                    floor: this.floor,
                    yaek: this.cross,
                    street: this.road,
                    fax_number: this.fax !== null ? this.fax.replace(/-/g, '') : '',
                    soi: this.alley,
                    province: this.checkTypeSelect === true ? this.provincetext : this.province,
                    tambon: this.checkTypeSelect === true ? this.subdistricttext : this.subdistrict,
                    amphoe: this.checkTypeSelect === true ? this.districttext : this.district,
                    zipcode: this.zipcode,
                    country: 'ประเทศไทย',
                    branch_no: this.branchCode,
                    branch_name: this.branchName,
                    type_verified: ''
                  }
                  // console.log('form data======>', formDataBusiness)
                  await this.$store.dispatch('actionCreateBusiness', formDataBusiness)
                  var response = await this.$store.state.ModuleBusiness.stateCreateBusiness
                  // console.log(response)
                  if (response.result === 'SUCCESS') {
                    await this.getTaxId()
                    this.$swal.fire({ icon: 'success', text: this.$t('Business.MessageSuccess'), showConfirmButton: false, timer: 1500 })
                    this.$EventBus.$emit('getUserDetail')
                    await this.$EventBus.$emit('AuthorityUser')
                    await this.$EventBus.$emit('getBusinessMP')
                    this.$store.commit('closeLoader')
                    this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
                  } else {
                    this.$store.commit('closeLoader')
                    this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
                  }
                }
              } else {
                this.$store.commit('closeLoader')
                this.$swal.fire({ icon: 'warning', title: '<h5>' + this.$t('Business.PleaseCheckInformation') + '</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({ icon: 'warning', title: '<h5>' + this.$t('Business.PleaseCheckInformation') + '</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: this.$t('Business.FillInInformation'), showConfirmButton: false, timer: 2500 })
          }
        } else {
          // if (this.approveFileSuccess === '' && this.resultDocumentSuccess === '') {
          //   this.$swal.fire({ icon: 'warning', title: 'กรุณาอัพโหลดหนังสือรองรับนิติบุคคลและหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
          // } else if (this.approveFileSuccess !== '' && this.resultDocumentSuccess === '') {
          //   this.$swal.fire({ icon: 'warning', title: 'กรุณาอัพโหลดหนังสือมอบอำนาจ', showConfirmButton: false, timer: 1500 })
          // } else if (this.approveFileSuccess === '' && this.resultDocumentSuccess !== '') {
          //   this.$swal.fire({ icon: 'warning', title: 'กรุณาอัพโหลดหนังสือรองรับนิติบุคคล', showConfirmButton: false, timer: 1500 })
          // } else {
          var formDataBusiness1 = {
            account_title_th: this.account_title_th,
            first_name_th: this.businessNameTH,
            account_title_eng: this.account_title_eng,
            first_name_eng: this.businessNameEN,
            id_card_num: this.taxId,
            email: this.mail,
            tel_no: this.tel !== null ? this.tel.replace(/-/g, '') : '',
            mobile_no: this.phone !== null ? this.phone.replace(/-/g, '') : '',
            approve_document: '',
            result_meeting: '',
            thai_email: this.mail,
            department: 'ฝ่ายบริหาร',
            position: 'CEO',
            house_code: this.addressCode,
            house_no: this.houseNo,
            room_no: this.roomNo,
            moo_ban: this.houseName,
            moo_no: this.houseGroup,
            building_name: this.buildingName,
            floor: this.floor,
            yaek: this.cross,
            street: this.road,
            fax_number: this.fax !== null ? this.fax.replace(/-/g, '') : '',
            soi: this.alley,
            province: this.checkTypeSelect === true ? this.provincetext : this.province,
            tambon: this.checkTypeSelect === true ? this.subdistricttext : this.subdistrict,
            amphoe: this.checkTypeSelect === true ? this.districttext : this.district,
            zipcode: this.zipcode,
            country: 'ประเทศไทย',
            branch_no: this.branchCode,
            branch_name: this.branchName,
            type_verified: 'verified'
          }
          // console.log('form data======>', formDataBusiness)
          await this.$store.dispatch('actionCreateBusiness', formDataBusiness1)
          var response1 = await this.$store.state.ModuleBusiness.stateCreateBusiness
          // console.log(response1)
          if (response1.result === 'SUCCESS') {
            await this.getTaxId()
            this.$swal.fire({ icon: 'success', text: this.$t('Business.MessageSuccess'), showConfirmButton: false, timer: 1500 })
            this.$EventBus.$emit('getUserDetail')
            await this.$EventBus.$emit('AuthorityUser')
            await this.$EventBus.$emit('getBusinessMP')
            this.$store.commit('closeLoader')
            this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'error', text: response1.message, showConfirmButton: false, timer: 1500 })
          }
        }
        // }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: this.$t('Business.FillInInformation'), showConfirmButton: false, timer: 1500 })
      }
    },
    getErrorMsg (msg) {
      if (msg === 'This user is unauthorized.') {
        return this.$t('Business.Unauthorized')
      } else if (msg === 'Token not found.') {
        return this.$t('Business.TokenNotFound')
      } else if (msg === 'This user has not been verified Ekyc By ID Card') {
        return this.$t('Business.NotVerifiedID')
      } else if (msg === 'This TAX_ID is already exist.') {
        return this.$t('Business.TaxIDDupli')
      } else if (msg === 'The user was not found in the company.') {
        return this.$t('Business.NotFoundCom')
      } else if (msg === 'Invalid format for business_type_other.') {
        return this.$t('Business.NotValidBusinessType')
      } else if (msg === 'Create business other failed.') {
        return this.$t('Business.CreateOtherFail')
      } else if (msg === 'id_card_num not approved eKYC.') {
        return this.$t('Business.TaxNotEKYC')
      } else {
        return this.$t('Business.ContractStaff')
      }
    },
    async createbizOther () {
      this.$store.commit('openLoader')
      if (this.$refs.FormcreateOther.validate(true)) {
        if (this.typeTaxIDOther === 'noneOther') {
          if ((this.checksubdistrictConfirm(this.subdistrictOther) || this.checkdistrictConfirm(this.districtOther) || this.checkprovinceConfirm(this.provinceOther) || this.checkzipcodeConfirm(this.zipcodeOther))) {
            if (this.subdistrictOther === this.checkSubdistrict && this.districtOther === this.checkDistrict && this.provinceOther === this.checkProvince && this.zipcodeOther === this.checkZipcode) {
              const check = this.checkSendAddress()
              if (check.length !== 0) {
                var formdataOther = {
                  business_type_other: this.businessTypeOther,
                  first_name_th: this.businessNameTHOther,
                  first_name_eng: this.businessNameENOther,
                  id_card_num: this.taxIdOther,
                  email: this.mailOther,
                  mobile_no: this.telOther !== null ? this.telOther.replace(/-/g, '') : '',
                  house_code: this.addressCodeOther !== '' ? this.addressCodeOther : '-',
                  house_no: this.houseNoOther !== '' ? this.houseNoOther : '-',
                  room_no: this.roomNoOther !== '' ? this.roomNoOther : '-',
                  moo_ban: this.houseNameOther !== '' ? this.houseNameOther : '-',
                  moo_no: this.houseGroupOther !== '' ? this.houseGroupOther : '-',
                  building_name: this.buildingNameOther !== '' ? this.buildingNameOther : '-',
                  floor: this.floorOther !== '' ? this.floorOther : '-',
                  yaek: this.crossOther !== '' ? this.crossOther : '-',
                  street: this.roadOther !== '' ? this.roadOther : '-',
                  soi: this.alleyOther !== '' ? this.alleyOther : '-',
                  province: this.provinceOther,
                  tambon: this.subdistrictOther,
                  amphoe: this.districtOther,
                  zipcode: this.zipcodeOther,
                  branch_no: this.branchCodeOther,
                  branch_name: this.branchNameOther,
                  type_verified: 'not_verified'
                }
                // console.log('data formdata other', formdataOther)
                await this.$store.dispatch('actionsCreateBusinessOther', formdataOther)
                var response = await this.$store.state.ModuleBusiness.stateCreateBusinessOther
                // console.log(response)
                if (response.result === 'SUCCESS') {
                  await this.getTaxId()
                  this.$swal.fire({ icon: 'success', text: response.message, showConfirmButton: false, timer: 1500 })
                  this.$EventBus.$emit('getUserDetail')
                  await this.$EventBus.$emit('AuthorityUser')
                  await this.$EventBus.$emit('getBusinessMP')
                  this.$store.commit('closeLoader')
                  this.$router.push({ path: '/detailbusinesssid' }).catch(() => {})
                } else {
                  this.$store.commit('closeLoader')
                  var msg = this.getErrorMsg(response.message)
                  this.$swal.fire({
                    icon: 'error',
                    text: msg,
                    showConfirmButton: false,
                    timer: 1500
                  })
                }
              } else {
                this.$store.commit('closeLoader')
                this.$swal.fire({ icon: 'warning', title: '<h5>' + this.$t('Business.PleaseCheckInformation') + '</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
              }
            } else {
              this.$store.commit('closeLoader')
              this.$swal.fire({ icon: 'warning', title: '<h5>' + this.$t('Business.PleaseCheckInformation') + '</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ icon: 'warning', text: this.$t('Business.FillInInformation'), showConfirmButton: false, timer: 2500 })
          }
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: this.$t('Business.FillInInformation'), showConfirmButton: false, timer: 1500 })
      }
    },
    // Upload File หนังสือรองรับนิติบุคคล
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      this.file = []
      if (this.DataImage.length <= 1) {
        this.DataImage.forEach((file) => {
          this.file.push({
            name: file.name,
            type: file.type
          })
          this.uploadInServe(file)
        })
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาอัปโหลดได้แค่ 1 ไฟล์', showConfirmButton: false, timer: 1500 })
      }
    },
    async uploadInServe (val) {
      const formData = new FormData()
      formData.append('approveDocument', val)
      await this.$store.dispatch('actionUploadApprover', formData)
      var response = await this.$store.state.ModuleBusiness.stateUploadApprover
      // console.log('response upload =====>', response.data)
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', text: 'อัปโหลดหนังสือรับรองนิติบุคคลสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.approveFileSuccess = response.data.response_step_1
        // console.log(this.approveFileSuccess)
      } else {
        this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage (index, val) {
      this.DataImage = []
      this.file.splice(index, 1)
    },
    // Upload File หนังสือมอบอำนาจ
    onPickFile1 () {
      document.getElementById('file_input1').click()
    },
    UploadImage1 () {
      // console.log(this.DataImage1)
      this.file1 = []
      if (this.DataImage1.length <= 1) {
        this.DataImage1.forEach((file) => {
          this.file1.push({
            name: file.name,
            type: file.type
          })
          this.uploadInServe1(file)
        })
      } else {
        this.$swal.fire({ icon: 'warning', text: 'กรุณาอัปโหลดได้แค่ 1 ไฟล์', showConfirmButton: false, timer: 1500 })
      }
    },
    async uploadInServe1 (val) {
      const formData = new FormData()
      formData.append('resultDocument', val)
      await this.$store.dispatch('actionUploadResultDocument', formData)
      var response = await this.$store.state.ModuleBusiness.stateUploadResultDocument
      // console.log('response upload =====>', response.data)
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ icon: 'success', text: 'อัปโหลดหนังสือมอบอำนาจสำเร็จ', showConfirmButton: false, timer: 1500 })
        this.resultDocumentSuccess = response.data.response_step_2
        // console.log(this.resultDocumentSuccess)
      } else {
        this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 1500 })
      }
    },
    onMove1 ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    RemoveImage1 (index, val) {
      this.DataImage1 = []
      this.file1.splice(index, 1)
    },
    async searchTaxID () {
      this.checkTypeSelect = true
      if (this.$refs.FormTaxID.validate(true)) {
        var response
        var data = {
          tax_id: this.taxIDSearch
        }
        if (this.typeTaxID === 'TaxByRevenue') {
          // console.log('TaxByRevenue', this.taxIDSearch)
          await this.$store.dispatch('actionFindTaxIDByRevenue', data)
          response = await this.$store.state.ModuleBusiness.stateFindTaxIDByRevenue
          // console.log('response TaxByRevenue', response)
          if (response.result === 'SUCCESS') {
            var dataTax = response.data
            // console.log('response', response.data)
            this.$swal.fire({ text: this.$t('Business.DataRetrieval'), icon: 'success', timer: 1500, showConfirmButton: false })
            this.showtaxSearch = false
            this.account_title_th = dataTax.account_title_th
            this.businessNameTH = dataTax.first_name_th
            this.mail = dataTax.email
            this.branchCode = dataTax.branch_no
            this.branchName = dataTax.branch_name
            this.fax = dataTax.fax_number === null ? '-' : dataTax.fax_number
            this.tel = dataTax.tel_no === null ? '-' : dataTax.tel_no
            this.phone = dataTax.mobile_no === null ? '-' : dataTax.mobile_no
            this.buildingName = dataTax.address.building_name
            this.floor = dataTax.address.floor
            this.road = dataTax.address.street
            this.cross = dataTax.address.yaek
            this.houseNo = dataTax.address.house_no
            this.roomNo = dataTax.address.room_no
            this.houseName = dataTax.address.moo_ban
            this.alley = dataTax.address.soi
            this.provincetext = dataTax.address.province
            this.districttext = dataTax.address.amphoe
            this.subdistricttext = dataTax.address.tambon
            this.zipcode = dataTax.address.zipcode
            this.showForm = true
          } else {
            if (response.message === 'Not Found Business Account') {
              this.$swal.fire({ text: this.$t('Business.NotFoundBusinessAccount'), icon: 'error', timer: 2500, showConfirmButton: false })
            } else if (response.message === 'This TAX_ID is already exist.') {
              this.$swal.fire({ text: this.$t('Business.TaxIDDupli'), icon: 'error', timer: 2500, showConfirmButton: false })
            } else {
              this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
            }
          }
        } else if (this.typeTaxID === 'TaxByOneID') {
          // console.log('TaxByRevenue', this.taxIDSearch)
          await this.$store.dispatch('actionFindTaxIDByOneID', data)
          response = await this.$store.state.ModuleBusiness.stateFindTaxIDByOneID
          // console.log('response TaxByOneID', response)
          if (response.result === 'SUCCESS') {
            var dataBusiness = response.data
            // console.log('response', response.data)
            this.$swal.fire({ text: this.$t('Business.DataRetrieval'), icon: 'success', timer: 1500, showConfirmButton: false })
            this.showtaxSearch = false
            if (dataBusiness.account_title_th === 'ห้างหุ้นส่วนสามัญ') {
              this.businessType = 1
            } else if (dataBusiness.account_title_th === 'ห้างหุ้นส่วนจำกัด') {
              this.businessType = 2
            } else if (dataBusiness.account_title_th === 'บริษัทจำกัด') {
              this.businessType = 3
            } else if (dataBusiness.account_title_th === 'บริษัทมหาชนจำกัด') {
              this.businessType = 4
            } else {
              this.businessType = 5
            }
            this.businessNameTH = dataBusiness.first_name_th
            this.businessNameEN = dataBusiness.first_name_eng
            this.businessDocNameTH = dataBusiness.name_on_document_th
            this.businessDocNameEN = dataBusiness.name_on_document_eng !== null ? dataBusiness.name_on_document_eng : '-'
            this.taxId = dataBusiness.tax_id
            this.mail = dataBusiness.email
            this.branchCode = dataBusiness.branch_no
            this.branchName = dataBusiness.branch_name
            this.fax = dataBusiness.fax_number === null ? '-' : dataBusiness.fax_number
            this.tel = dataBusiness.tel_no === null ? '-' : dataBusiness.tel_no
            this.phone = dataBusiness.mobile_no === null ? '-' : dataBusiness.mobile_no
            this.buildingName = dataBusiness.address.building_name
            this.floor = dataBusiness.address.floor
            this.road = dataBusiness.address.street
            this.cross = dataBusiness.address.yaek !== null ? dataBusiness.address.yaek : '-'
            var dataDetail = dataBusiness.address.data_detail === null ? '' : dataBusiness.address.data_detail
            this.houseNo = dataBusiness.address.house_no + ' ' + dataDetail
            this.roomNo = dataBusiness.address.room_no !== null ? dataBusiness.address.room_no : '-'
            this.houseName = dataBusiness.address.moo_ban !== null ? dataBusiness.address.moo_ban : '-'
            this.alley = dataBusiness.address.soi !== null ? dataBusiness.address.soi : '-'
            this.provincetext = dataBusiness.address.province
            this.districttext = dataBusiness.address.amphoe
            this.subdistricttext = dataBusiness.address.tambon
            this.zipcode = dataBusiness.address.zipcode
            this.showForm = true
          } else {
            if (response.message === 'Not Found Business Account') {
              this.$swal.fire({ text: this.$t('Business.NotFoundBusinessAccount'), icon: 'error', timer: 2500, showConfirmButton: false })
            } else if (response.message === 'This TAX_ID is already exist.') {
              this.$swal.fire({ text: this.$t('Business.TaxIDDupli'), icon: 'error', timer: 2500, showConfirmButton: false })
            } else {
              this.$swal.fire({ text: `${response.message}`, icon: 'error', timer: 2500, showConfirmButton: false })
            }
          }
        }
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        if (this.tab === 'tab-1') {
          return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
        } else {
          return data.district === this.subdistrictOther && data.amphoe === this.districtOther && data.province === this.provinceOther && data.zipcode === Number(this.zipcodeOther)
        }
      })
      return check
    },
    checkAdressError (key) {
      // console.log('key===', key)
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    async getTaxId () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      this.taxIdBiz = response.data.list_business[0].id
      localStorage.removeItem('business_id')
      localStorage.setItem('business_id', this.taxIdBiz)
    }
  }
}
</script>

<style scoped>
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 12px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
  .v-breadcrumbs {
    align-items: center;
    display: flex;
    flex-wrap: wrap;
    flex: 0 1 auto;
    white-space: nowrap;
    list-style-type: none;
    margin-bottom: 24px;
    /* padding: 8px 0px 8px 75px !important; */
  }
}
.container {
  max-width: 1400px !important;
  padding: 0px 12px 12px 12px;
}
.v-timeline-item__body {
  position: relative;
  height: 100%;
  padding-top: 8px !important;
  flex: 1 1 auto;
}
.v-timeline-item {
  display: flex;
  padding-bottom: 12px;
}
</style>
<style>
input.th-address-input {
  opacity: 0.6;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobil {
  font-size: 18px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 14px;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
