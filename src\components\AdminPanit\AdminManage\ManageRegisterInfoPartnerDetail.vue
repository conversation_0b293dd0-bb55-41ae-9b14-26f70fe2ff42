<template>
  <v-container :style="MobileSize ? 'background-color: #ffffff' : ''" :class="MobileSize ? 'pa-3' : 'mb-3'">
    <v-row dense class="pt-2">
      <v-col cols="12" style="align-content: center;">
        <v-icon color="#27AB9C" class="mr-2" @click="backtoMenu()">mdi-chevron-left</v-icon>
        <span style="font-weight: bold; align-content: center;" :style="MobileSize || IpadSize ? 'font-size: large;' : 'font-size: x-large;'">รายละเอียดการลงทะเบียนคู่ค้า</span>
      </v-col>
    </v-row>
    <v-row>
      <v-card :class="MobileSize ? 'pa-2 mb-4' : 'pa-5 mb-5'" elevation="0">
        <v-form ref="form" v-model="formRegis" lazy-validation>
          <span style="font-size: large;"><b>ข้อมูลบัญชีนิติบุคคล</b></span>
          <v-row :class="MobileSize ? 'px-2' : 'px-3'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">ประเภทธุรกิจ</span>
              <div class="d-flex flex-wrap" v-if="!MobileSize">
                <v-radio-group
                  v-model="radiosTypeBiz"
                  row
                  class="d-flex"
                  readonly
                >
                  <v-radio
                    v-for="(item, index) in filteredItemRadioTypeBusiness"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
              <div v-else>
                <v-select
                  v-model="radiosTypeBiz"
                  :items="itemRadioTypeBusiness"
                  item-text="label"
                  item-value="value"
                  placeholder="ระบุประเภทนิติบุคคล"
                  outlined
                  dense
                  style="border-radius: 8px;"
                  readonly
                >
                </v-select>
              </div>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-2' : 'px-3'" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
            <v-col cols="12" class="d-flex flex-column">
              <span style="font-size: large;">ข้อมูลธุรกิจ</span>
            </v-col>
            <v-col :cols="(radiosTypeBiz === 'typeBiz3' || radiosTypeBiz === 'typeBiz5') ? (MobileSize ? 12 : 8) : 12" :style="MobileSize ? 'margin-top: -5vw;' : 'margin-top: -1vw;'">
              <span>เลขทะเบียนนิติบุคคล :</span><br>
              <div class="d-flex">
                 <v-text-field
                  v-model="bizNumber"
                  outlined
                  dense
                  placeholder="ระบุเลขทะเบียนนิติบุคคล"
                  style="border-radius: 8px;"
                  :maxLength="13"
                  class="mr-1"
                  readonly
                >
                </v-text-field>
              </div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 3" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ประเภทนิติบุคคล (ไทย) :</span><br>
              <v-select
                v-model="businessTypeTH"
                :items="businessTypeItem"
                item-text="nameTH"
                item-value="value"
                placeholder="ระบุประเภทนิติบุคคล"
                outlined
                dense
                style="border-radius: 8px;"
                readonly
              >
              </v-select>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อนิติบุคคล (ไทย) :</span><br>
              <v-text-field
                v-model="bizNameTH"
                outlined
                dense
                placeholder="ระบุชื่อนิติบุคคล"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 5" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อสำหรับแสดงบนเอกสาร (ไทย) :</span><br>
              <v-text-field
                v-model="bizNameDocTH"
                outlined
                dense
                placeholder="ระบุชื่อสำหรับแสดงบนเอกสาร"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 3" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ประเภทนิติบุคคล (อังกฤษ) :</span><br>
              <v-select
                v-model="businessTypeEN"
                :items="businessTypeItem"
                item-text="nameEN"
                item-value="value"
                placeholder="ระบุประเภทนิติบุคคล"
                outlined
                dense
                style="border-radius: 8px;"
                readonly
              >
              </v-select>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อนิติบุคคล (อังกฤษ) :</span><br>
              <v-text-field
                v-model="bizNameEN"
                outlined
                dense
                placeholder="ระบุชื่อนิติบุคคล"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : IpadProSize ? 4 : 5" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>ชื่อสำหรับแสดงบนเอกสาร (อังกฤษ) :</span><br>
              <v-text-field
                v-model="bizNameDocEN"
                outlined
                dense
                placeholder="ระบุชื่อสำหรับแสดงบนเอกสาร"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>เบอร์โทรศัพท์ :</span><br>
              <v-text-field
                v-model="tel"
                outlined
                dense
                placeholder="ระบุเบอร์โทรศัพท์"
                style="border-radius: 8px;"
                :maxLength="9"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>เบอร์มือถือ :</span><br>
              <v-text-field
                v-model="phone"
                outlined
                dense
                placeholder="ระบุเบอร์มือถือ"
                style="border-radius: 8px;"
                :maxLength="10"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
              <span>อีเมล :</span><br>
              <v-text-field
                v-model="email"
                outlined
                dense
                placeholder="ระบุอีเมล"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-2' : 'px-3'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
            <v-col cols="12" class="d-flex flex-column">
              <span style="font-size: large;">ที่อยู่</span>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -3vw;' : 'margin-top: -1vw;'">
              <span>เลขที่ :</span><br>
              <v-text-field
                v-model="houseNumber"
                outlined
                dense
                placeholder="ระบุเลขที่"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ห้องเลขที่ :</span><br>
              <v-text-field
                v-model="roomNumber"
                outlined
                dense
                placeholder="ระบุห้องเลขที่"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ชั้นที่ :</span><br>
              <v-text-field
                v-model="floorNumber"
                outlined
                dense
                placeholder="ระบุชั้น"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>อาคาร :</span><br>
              <v-text-field
                v-model="building"
                outlined
                dense
                placeholder="ระบุอาคาร"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>หมู่บ้าน :</span><br>
              <v-text-field
                v-model="village"
                outlined
                dense
                placeholder="ระบุหมู่บ้าน"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>หมู่ที่ :</span><br>
              <v-text-field
                v-model="moo"
                outlined
                dense
                placeholder="ระบุหมู่ที่"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ตรอก/ซอย :</span><br>
              <v-text-field
                v-model="soi"
                outlined
                dense
                placeholder="ระบุตรอก/ซอย"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>แยก :</span><br>
              <v-text-field
                v-model="yaek"
                outlined
                dense
                placeholder="ระบุแยก"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : 4" :style="MobileSize ? 'margin-top: -8vw;' : 'margin-top: -1vw;'">
              <span>ถนน :</span><br>
              <v-text-field
                v-model="road"
                outlined
                dense
                placeholder="ระบุถนน"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? 'margin-top: -6vw;' : 'margin-top: -1vw;'">
              <!-- <span>จังหวัด<span style="color: red;"> *</span></span> -->
              <addressinput-province readonly v-model="provinceText" placeholder="ระบุจังหวัด"/>
              <div v-if="checkProvinceError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? '' : 'margin-top: -1vw;'">
              <!-- <span>อำเภอ/เขต<span style="color: red;"> *</span></span> -->
              <addressinput-district readonly v-model="districtText" placeholder="ระบุเขต/อำเภอ"/>
              <div v-if="checkDistrictError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :style="MobileSize ? '' : 'margin-top: -1vw;'">
              <!-- <span>ตำบล/แขวง<span style="color: red;"> *</span></span> -->
              <addressinput-subdistrict readonly v-model="subdistricttext" placeholder="ระบุแขวง/ตำบล" />
              <div v-if="checkSubDistrictError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
            <v-col :cols="MobileSize || IpadSize ? 12 : 4" :class="MobileSize || IpadSize ? '' : 'mt-2'">
              <addressinput-zipcode readonly v-model="zipcodeText" placeholder="ระบุรหัสไปรษณีย์"/>
              <div v-if="checkZipcodeError" class="text-error" style="color: red;">ข้อมูลไม่ถูกต้อง</div>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-2' : 'px-3'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">รายละเอียดสาขา</span>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -5vw;' : IpadSize ? 'margin-top: -2.5vw;' : 'margin-top: -1vw;'">
              <span>รหัสสาขา : <span style="font-size: 10px; color: red;">(ตัวอย่างรหัสสาขา 00000)</span></span><br>
              <v-text-field
                v-model="branchNo"
                outlined
                dense
                placeholder="ระบุรหัสสาขา"
                style="border-radius: 8px;"
                readonly
                :maxLength="5"
              ></v-text-field>
            </v-col>
            <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -2.5vw;' : 'margin-top: -1vw;'">
              <span>ชื่อสาขา :</span><br>
              <v-text-field
                v-model="branchName"
                outlined
                dense
                placeholder="ระบุสาขา"
                style="border-radius: 8px;"
                readonly
              ></v-text-field>
            </v-col>
          </v-row>
          <v-row :class="MobileSize ? 'px-2' : 'px-3'" :style="MobileSize || IpadSize ? 'margin-top: -6vw;' : 'margin-top: -1.5vw;'">
            <v-col cols="12" class="d-flex flex-column mt-3">
              <span style="font-size: large;">ใบเสร็จรับรองอิเล็กทรอนิกส์</span>
              <div class="d-flex flex-wrap">
                <v-radio-group
                  v-model="radiosEtax"
                  row
                  class="d-flex"
                  v-for="(item, index) in itemRadioEtax" :key="index"
                  readonly
                >
                  <v-radio
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
            </v-col>
            <v-col cols="12" class="d-flex flex-column">
              <span style="font-size: large;">ผู้ประสานงาน</span>
              <div class="d-flex">
                <v-radio-group
                  v-model="radiosCoordinator"
                  row
                  class="d-flex"
                  v-for="(item, index) in itemRadioCoordinator" :key="index"
                  readonly
                >
                  <v-radio
                    :label="item.label"
                    :value="item.value"
                  ></v-radio>
                </v-radio-group>
              </div>
            </v-col>
            <v-col cols="12" :style="MobileSize ? '' : 'margin-top: -1vw;'">
              <div v-if="radiosCoordinator === 'typeCoor1'" class="d-flex flex-wrap">
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>ชื่อ (ไทย) :</span><br>
                  <v-text-field
                    v-model="firstNameTH"
                    outlined
                    dense
                    placeholder="ระบุชื่อ"
                    style="border-radius: 8px;"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>นามสกุล (ไทย) :</span><br>
                  <v-text-field
                    v-model="lastNameTH"
                    outlined
                    dense
                    placeholder="ระบุนามสกุล"
                    style="border-radius: 8px;"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>เลขประจำตัวประชาชน :</span><br>
                  <v-text-field
                    v-model="taxId"
                    outlined
                    dense
                    placeholder="ระบุเลขประจำตัวประชาชน"
                    style="border-radius: 8px;"
                    :maxLength="13"
                    readonly
                    @input="validateTaxID(taxId)"
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>เบอร์โทรศัพท์มือถือ :</span><br>
                  <v-text-field
                    v-model="telNo"
                    outlined
                    dense
                    placeholder="ระบุเบอร์โทรศัพท์มือถือ"
                    style="border-radius: 8px;"
                    :maxLength="10"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>อีเมล (ใช้ในการกดรับใบรับรอง) :</span><br>
                  <v-text-field
                    v-model="email_eTax"
                    outlined
                    dense
                    placeholder="ระบุอีเมล"
                    style="border-radius: 8px;"
                    readonly
                  ></v-text-field>
                </v-col>
              </div>
              <div v-if="radiosCoordinator === 'typeCoor2'" class="d-flex flex-wrap">
                <v-col :class="IpadSize ? 'pl-0' : MobileSize ? 'px-0' : 'pl-0'" :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>ชื่อผู้รับมอบอำนาจ (ไทย) :</span><br>
                  <v-text-field
                    v-model="firstNameTH"
                    outlined
                    dense
                    placeholder="ระบุชื่อผู้รับมอบอำนาจ"
                    style="border-radius: 8px;"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col :class="IpadSize ? 'pr-0' : MobileSize ? 'px-0' : ''" :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>นามสกุลผู้รับมอบอำนาจ (ไทย) :</span><br>
                  <v-text-field
                    v-model="lastNameTH"
                    outlined
                    dense
                    placeholder="ระบุนามสกุลผู้รับมอบอำนาจ"
                    style="border-radius: 8px;"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col :class="IpadSize ? 'pl-0' : MobileSize ? 'px-0' : 'pr-0'" :cols="MobileSize ? 12 : IpadSize ? 6 : 4" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span style="white-space: nowrap;">เลขประจำตัวประชาชนผู้รับมอบอำนาจ :</span><br>
                  <v-text-field
                    v-model="taxId"
                    outlined
                    dense
                    placeholder="ระบุเลขประจำตัวประชาชนผู้รับมอบอำนาจ"
                    style="border-radius: 8px;"
                    :maxLength="13"
                    readonly
                    @input="validateTaxID(taxId)"
                  ></v-text-field>
                </v-col>
                <v-col :class="IpadSize ? 'pr-0' : MobileSize ? 'px-0' : 'pl-0'" :cols="MobileSize ? 12 : IpadSize ? 6 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span>เบอร์โทรศัพท์มือถือ :</span><br>
                  <v-text-field
                    v-model="telNo"
                    outlined
                    dense
                    placeholder="ระบุเบอร์โทรศัพท์มือถือ"
                    style="border-radius: 8px;"
                    :maxLength="10"
                    readonly
                  ></v-text-field>
                </v-col>
                <v-col :class="IpadSize ? 'px-0' : MobileSize ? 'px-0' : ''" :cols="MobileSize ? 12 : IpadSize ? 12 : 4" v-if="radiosEtax === 'typeEtax1'" :style="MobileSize ? 'margin-top: -8vw;' : IpadSize ? 'margin-top: -4vw;' : 'margin-top: -1vw;'">
                  <span :style="MobileSize ? '' : 'white-space: nowrap'">อีเมลผู้ประสานงานดูแลใบรับรอง (ใช้ในการกดรับใบรับรอง) :</span><br>
                  <v-text-field
                    v-model="email_eTax"
                    outlined
                    dense
                    placeholder="ระบุอีเมลผู้ประสาน"
                    style="border-radius: 8px;"
                    readonly
                  ></v-text-field>
                </v-col>
              </div>
            </v-col>
            <v-col cols="12" class="d-flex flex-column" :style="MobileSize || IpadSize ? 'margin-top: -5vw; margin-bottom: -1vw;' : 'margin-top: -2vw;'">
              <span style="font-size: large;">เอกสาร</span>
            </v-col>
            <!-- test ข้อมูล -->
            <!-- <span>{{ radiosTypeBiz }}</span><br>
            <span>{{ radiosEtax }}</span><br>
            <span>{{ currentFormOption }}</span><br> -->
            <!-- เลือกนิติบุคคล -->
            <v-col cols="12" v-if="radiosTypeBiz === 'typeBiz1'" :style="MobileSize ? '' : IpadSize ? '' : 'margin-top: -1vw;'">
              <!-- กำหนดเอง -->
              <div class="d-flex flex-column" style="font-size: 16px; gap: 3px;" v-if="currentFormOption === 'showFormOption1'">
                <span @click="gotoLink(certificateCompanyPath)" v-if="certificateCompanyPath && certificateCompanyPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารสำเนาหนังสือรับรองบริษัท</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (สำเนาหนังสือรับรองบริษัท) แนบ</span>
                <span @click="gotoLink(vatCompanyPath)" v-if="vatCompanyPath && vatCompanyPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารภพ.20 ของบริษัท</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (ภพ.20 ของบริษัท) แนบ</span>
                <span @click="gotoLink(ownerIdCardPath)" v-if="ownerIdCardPath && ownerIdCardPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารสำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (สำเนาบัตรประชาชนของกรรมการผู้มีอำนาจ) แนบ</span>
              </div>
              <!-- มอบอำนาจ -->
              <div class="d-flex flex-column" style="font-size: 16px; gap: 3px;" v-if="currentFormOption === 'showFormOption2'">
                <span @click="gotoLink(certificateCompanyPath)" v-if="certificateCompanyPath && certificateCompanyPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารสำเนาหนังสือรับรองบริษัท</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (สำเนาหนังสือรับรองบริษัท) แนบ</span>
                <span @click="gotoLink(vatCompanyPath)" v-if="vatCompanyPath && vatCompanyPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารภพ.20 ของบริษัท</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (ภพ.20 ของบริษัท) แนบ</span>
                <span @click="gotoLink(powerOfAttorneyPath)" v-if="powerOfAttorneyPath && powerOfAttorneyPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารหนังสือมอบอำนาจ</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (หนังสือมอบอำนาจ) แนบ</span>
                <span @click="gotoLink(ownerIdCardPath)" v-if="ownerIdCardPath && ownerIdCardPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารสำเนาบัตรประชาชนผู้มีอำนาจ</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (สำเนาบัตรประชาชนผู้มีอำนาจ) แนบ</span>
                <span @click="gotoLink(proxyIdCardPath)" v-if="proxyIdCardPath && proxyIdCardPath !== '-'" style="color: #1976D2; text-decoration: underline; cursor: pointer; width: fit-content;">เอกสารสำเนาบัตรประชาชนของผู้รับมอบอำนาจ</span>
                <span v-else class="text--disabled">ไม่มีเอกสาร (สำเนาบัตรประชาชนของผู้รับมอบอำนาจ) แนบ</span>
              </div>
            </v-col>
          </v-row>
        </v-form>
      </v-card>
    </v-row>
  </v-container>
</template>

<script>
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import { isArray } from 'lodash'
Vue.use(VueThailandAddress)
export default {
  data () {
    return {
      itemRadioTypeBusiness: [
        { label: 'นิติบุคคล', value: 'typeBiz1' },
        { label: 'ทะเบียนพาณิชย์', value: 'typeBiz2' },
        { label: 'OTOP', value: 'typeBiz5' },
        { label: 'วิสาหกิจชุมชน', value: 'typeBiz3' },
        { label: 'วิสาหกิจเพื่อสังคม', value: 'typeBiz4' },
        { label: 'ประชารัฐรักสามัคคี', value: 'typeBiz6' }
      ],
      businessTypeItem: [
        { nameTH: 'ห้างหุ้นส่วนสามัญ', nameEN: 'Ordinary Partnership', value: 1 },
        { nameTH: 'ห้างหุ้นส่วนจำกัด', nameEN: 'Limited Partnership', value: 2 },
        { nameTH: 'บริษัทจำกัด', nameEN: 'Company Limited', value: 3 },
        { nameTH: 'บริษัทมหาชนจำกัด', nameEN: 'Public Limited Company', value: 4 },
        { nameTH: 'นิติบุคคลอื่นๆ ภายใต้กฎหมายเฉพาะ', nameEN: 'Other', value: 5 }
      ],
      itemRadioEtax: [
        { label: 'ใช้บริการ e-Tax Invoice & e-Receipt', value: 'typeEtax1' },
        { label: 'ไม่ใช้บริการ e-Tax Invoice & e-Receipt', value: 'typeEtax2' }
      ],
      itemRadioCoordinator: [
        { label: 'กรรมการดำเนินการเอง', value: 'typeCoor1' },
        { label: 'มอบอำนาจ', value: 'typeCoor2' }
      ],
      radiosTypeBiz: 'typeBiz1',
      radiosEtax: 'typeEtax2',
      radiosCoordinator: 'typeCoor1',
      bizNumber: '',
      businessTypeTH: '',
      businessTypeEN: '',
      bizNameTH: '',
      bizNameDocTH: '',
      bizNameEN: '',
      bizNameDocEN: '',
      phone: '',
      tel: '',
      email: '',
      houseNumber: '',
      roomNumber: '',
      floorNumber: '',
      building: '',
      village: '',
      moo: '',
      soi: '',
      yaek: '',
      road: '',
      checkProvinceError: '',
      checkDistrictError: '',
      checkSubDistrictError: '',
      checkZipcodeError: '',
      provinceText: '',
      districtText: '',
      subdistricttext: '',
      zipcodeText: '',
      formRegis: true,
      firstNameTH: '',
      lastNameTH: '',
      taxId: '',
      telNo: '',
      email_eTax: '',
      branchNo: '',
      branchName: 'สำนักงานใหญ่',
      certificateCompany: null,
      vatCompany: null,
      ownerIdCardCopy: null,
      proxyIdCardCopy: null,
      powerOfAttorneyFile: null,
      certificateCompanyPath: '',
      vatCompanyPath: '',
      ownerIdCardPath: '',
      powerOfAttorneyPath: '',
      proxyIdCardPath: '',
      pageSelect: 'partner',
      businessId: '',
      items: []
    }
  },
  created () {
    // this.pageSelect = this.$route.query.page
    this.businessId = this.$route.query.businessId
    this.getDetailInfoPartner()
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: `/ManageRegisterInfoPartnerDetailMobile?businessId=${this.businessId}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/ManageRegisterInfoPartnerDetail?businessId=${this.businessId}` }).catch(() => {})
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    isValidEtax () {
      return this.radiosEtax === 'typeEtax1' || this.radiosEtax === 'typeEtax2'
    },
    currentFormOption () {
      if (!this.isValidEtax) return null
      const typeBiz = this.radiosTypeBiz.replace('typeBiz', '')
      const coor = this.radiosCoordinator.replace('typeCoor', '')
      return typeBiz && coor ? `showFormOption${(parseInt(typeBiz) - 1) * 2 + parseInt(coor)}` : null
    },
    filteredItemRadioTypeBusiness () {
      if (this.pageSelect === 'partner') {
        return [
          { label: 'นิติบุคคล', value: 'typeBiz1' }
        ]
      }
      return this.itemRadioTypeBusiness
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  methods: {
    async backtoMenu () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/ManageRegisterInfoPartner' }).catch(() => {})
      } else {
        this.$router.push({ path: '/ManageRegisterInfoPartnerMobile' }).catch(() => {})
      }
    },
    mapTitleTHToType (title) {
      return title === 'ห้างหุ้นส่วนสามัญ' ? 1 : title === 'ห้างหุ้นส่วนจำกัด' ? 2 : title === 'บริษัทจำกัด' ? 3 : title === 'บริษัทมหาชนจำกัด' ? 4 : 5
    },
    mapTitleENToType (title) {
      return title === 'Ordinary Partnership' ? 1 : title === 'Limited Partnership' ? 2 : title === 'Company Limited' ? 3 : title === 'Public Limited Company' ? 4 : 5
    },
    mapTypeBiz (val) {
      return val === 'corporation' ? 'typeBiz1' : val === 'BusinessRegis' ? 'typeBiz2' : val === 'Community' ? 'typeBiz3' : val === 'Social' ? 'typeBiz4' : val === 'OTOP' ? 'typeBiz5' : 'typeBiz6'
    },
    async getDetailInfoPartner () {
      this.$store.commit('openLoader')
      var data = {
        business_id: this.businessId
      }
      localStorage.setItem('businessIdParam', this.businessId)
      await this.$store.dispatch('actionsDetailRegisterInfoPartner', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailRegisterInfoPartner
      // console.log(response, 'response')
      if (response.code === 200) {
        this.items = response.data[0]
        // console.log('this.items', this.items)
        // ตั้งค่าฟอร์มให้ตรงกับข้อมูลจาก backend
        this.businessTypeTH = this.mapTitleTHToType(this.items.account_title_th)
        this.bizNameTH = this.items.first_name_th
        this.businessTypeEN = this.mapTitleENToType(this.items.account_title_eng)
        this.bizNameEN = this.items.first_name_eng
        this.bizNameDocTH = this.items.name_on_document_th
        this.bizNameDocEN = this.items.name_on_document_eng
        this.bizNumber = this.items.id_card_num
        this.accessCode = this.items.accessCode || ''
        this.ownerIdCard = this.items.ownerIdCard || ''
        this.email = this.items.email
        this.tel = this.items.tel_no
        this.phone = this.items.mobile_no

        // เอกสาร
        this.certificateCompanyPath = this.items.certificateCompany
        this.vatCompanyPath = this.items.vatCompany
        this.ownerIdCardPath = this.items.ownerIdCardCopy
        this.powerOfAttorneyPath = this.items.powerOfAttorneyFile
        this.proxyIdCardPath = this.items.proxyIdCardCopy
        // console.log('this.ownerIdCardPath', this.ownerIdCardPath)

        // ที่อยู่
        // console.log('this.items.address', isArray(this.items.address))
        const address = isArray(this.items.address) ? this.items.address[0] : this.items.address
        this.houseNumber = address.house_no
        this.roomNumber = address.room_no
        this.village = address.moo_ban
        this.moo = address.moo_no
        this.building = address.building_name
        this.floorNumber = address.floor
        this.yaek = address.yaek
        this.road = address.street
        this.soi = address.soi
        this.provinceText = address.province
        this.subdistricttext = address.tambon
        this.districtText = address.amphoe
        this.zipcodeText = address.zipcode

        // ประเภท
        this.radiosTypeBiz = this.mapTypeBiz(this.items.type_biz)
        this.radiosEtax = this.items.type_eTaxUse === 'Y' ? 'typeEtax1' : 'typeEtax2'
        this.radiosCoordinator = this.items.type_coordinator === 'Director' ? 'typeCoor1' : 'typeCoor2'

        // ผู้ประสานงาน
        // console.log('this.items.coordinator_info', isArray(this.items.coordinator_info))
        const coor = isArray(this.items.coordinator_info) ? this.items.coordinator_info[0] : this.items.coordinator_info
        this.firstNameTH = coor.firstNameTH
        this.lastNameTH = coor.lastNameTH
        this.taxId = coor.taxId
        this.telNo = coor.telNo
        this.email_eTax = coor.email_eTax

        this.branchNo = this.items.branch_no
        this.branchName = this.items.branch_name
      } else {
        this.items = []
        this.$swal.fire({ icon: 'warning', text: 'ระบบขัดข้อง กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timer: 1500 })
      }
      this.$store.commit('closeLoader')
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkConfirmAddress () {
      // เช็คกรณีที่พิมพ์ อำเภอ ตำบล จังหวัด รหัสไปรษณี ผิดและไม่ได้กรอกข้อมูลข้างบน
      const checkA = Address2021.filter((data) => {
        return data.district === this.subdistricttext
      })
      const checkB = Address2021.filter((data) => {
        return data.amphoe === this.districtText
      })
      const checkC = Address2021.filter((data) => {
        return data.province === this.provinceText
      })
      const checkD = Address2021.filter((data) => {
        return data.zipcode === Number(this.zipcodeText)
      })
      if (checkA.length === 0) {
        this.checkSubDistrictError = true
      }
      if (checkB.length === 0) {
        this.checkDistrictError = true
      }
      if (checkC.length === 0) {
        this.checkProvinceError = true
      }
      if (checkD.length === 0) {
        this.checkZipcodeError = true
      }
    },
    checkAdressError (key) {
      if (this.checkSendAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    checkSendAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode === Number(this.zipcode)
      })
      return check
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    gotoLink (href) {
      window.open(href, '_blank')
    }
  }
}
</script>

<style>

</style>
<style scoped>
::v-deep .th-address-input {
  border-radius: 8px;
}
::v-deep input.th-address-input {
  color: black !important;
}
::v-deep .classCheck .v-input__slot,
::v-deep .classCheck .th-address-input {
  background-color: #ebebeb !important;
}
</style>
