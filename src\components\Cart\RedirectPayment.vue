<template lang="html">
  <div v-if="data">
    <div v-html="data"></div>
  </div>
</template>
<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      data: ''
    }
  },
  async created () {
    this.data = await JSON.parse(Decode.decode(localStorage.getItem('PaymentData')))
  },
  mounted () {
    setTimeout(() => {
      document.getElementById('__PostForm').submit()
    }, 100)
  }
}
</script>
