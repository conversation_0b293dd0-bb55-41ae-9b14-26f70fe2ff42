<template>
  <div>
     <a-row type="flex" :gutter="[0,20]">
      <a-col :span="24">
        <a-row type="flex" justify="end">
          <a-button @click="CreateShop"><a-icon type="plus" />เพิ่มร้านค้าของฉัน</a-button>
        </a-row>
      </a-col>
    </a-row>
    <a-row type="flex">
      <a-col :span="24">
        <v-card outlined class="mt-5">
          <v-data-table
            :headers="headers"
            :items="props"
            @page-count="pageCount = $event"
            :page.sync="page"
            :items-per-page="itemsPerPage"
            class="elevation-0, rounded-lg"
            :search="search"
            hide-default-footer
            no-data-text="ไม่มร้านค้า"
          >
            <template v-slot:[`item.address`]="{ item }">
              <p style="margin: 0; padding: 0;">{{ item.house_no }} {{ item.detail }} {{ item.sub_district }} {{ item.district }} {{ item.province }} {{ item.zipcode }}</p>
              <!-- <p style="margin: 0; padding: 0;">{{ item.sub_district }} {{ item.district }}</p>
              <p style="margin: 0; padding: 0;">{{ item.province }}</p> -->
              <!-- <p>{{ item.zipcode }}</p> -->
            </template>
            <template v-slot:[`item.detail`]="{ item }">
              <a-button @click="DetailShop(item)">ดูร้านค้านี้ !</a-button>
            </template>
          </v-data-table>
        </v-card>
        <div class="text-center pt-2">
          <v-pagination light v-model="page" :total-visible="7" :length="pageCount"></v-pagination>
        </div>
      </a-col>
    </a-row>
  </div>
</template>
<script>
export default {
  props: ['props'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      pageCount: 5,
      page: 1,
      itemsPerPage: 4,
      search: '',
      headers: [
        { text: 'ชื่อร้านค้า', value: 'name_th', sortable: false, align: 'left' },
        { text: 'ที่อยู่', value: 'address', sortable: false, width: '250' },
        { text: 'รหัสประจำตัวผู้เสียภาษี', value: 'seller_tax_id', sortable: false, align: 'center', width: '300' },
        { text: 'รายละเอียด', value: 'detail', sortable: false, align: 'center', width: '80' }
      ]
    }
  },
  methods: {
    CreateShop () {
      this.$router.push({ path: '/ManageShop' })
    },
    DetailShop (val) {
      this.$EventBus.$emit('send_shop', val)
    }
  }
}
</script>
