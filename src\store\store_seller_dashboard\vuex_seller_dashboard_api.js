import AxiosShop from './axios_seller_dashboard_api'

const ModuleDashBoard = {
  state: {
    stateRevenuGraphSummary: [],
    stateOrderList: [],
    stateTopProducts: [],
    stateTopBuyers: [],
    stateTopSeller: [],
    stateExportDashboard: []
  },
  mutations: {
    mutationsRevenuGraphSummary (state, data) {
      state.stateRevenuGraphSummary = data
    },
    mutationsOrderList (state, data) {
      state.stateOrderList = data
    },
    mutationsTopProducts (state, data) {
      state.stateTopProducts = data
    },
    mutationsTopBuyers (state, data) {
      state.stateTopBuyers = data
    },
    mutationsTopSeller (state, data) {
      state.stateTopSeller = data
    },
    mutationsExportDashboard (state, data) {
      state.stateExportDashboard = data
    }
  },
  actions: {
    async actionRevenuGraphSummary (context, access) {
      const responseData = await AxiosShop.RevenuGraphSummary(access)
      await context.commit('mutationsRevenuGraphSummary', responseData)
    },
    async actionOrderList (context, access) {
      const responseData = await AxiosShop.OrderList(access)
      await context.commit('mutationsOrderList', responseData)
    },
    async actionTopProducts (context, access) {
      const responseData = await AxiosShop.TopProducts(access)
      await context.commit('mutationsTopProducts', responseData)
    },
    async actionTopBuyers (context, access) {
      const responseData = await AxiosShop.TopBuyers(access)
      await context.commit('mutationsTopBuyers', responseData)
    },
    async actionTopSeller (context, access) {
      const responseData = await AxiosShop.TopSeller(access)
      await context.commit('mutationsTopSeller', responseData)
    },
    async actionExportDashboard (context, access) {
      const responseData = await AxiosShop.ExportDashboard(access)
      await context.commit('mutationsExportDashboard', responseData)
    }
  }
}
export default ModuleDashBoard
