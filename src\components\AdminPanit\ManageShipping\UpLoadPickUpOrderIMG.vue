<template>
  <v-container :class="MobileSize ? 'background_color_Mobile' : 'background_color'" grid-list-xs rounded>
    <v-row dense :class="MobileSize ? 'pt-3 pb-3' : 'pt-3 pb-3'">
      <v-col cols="12">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 18px; line-height: 24px;' : 'font-size: 24px; line-height: 32px; font-weight: bold;'">
          <v-icon @click="backtoPage()" color="#27AB9C" size="30">mdi-chevron-left</v-icon>
          แนบรูปยืนยันเข้ารับพัสดุ
        </span>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'pt-1 pb-3' : 'pt-1 pb-3'">
      <v-col cols="12">
        <span :style="MobileSize ? 'font-weight: 700; font-size: 16px; line-height: 24px;' : 'font-size: 20px; line-height: 30px; font-weight: bold;'">คำสั่งซื้อที่ยังไม่ดำเนินการ</span>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'pt-1 pb-3' : 'pt-1 pb-3'">
      <v-col cols="12" md="6" sm="12" align="start">
        <v-text-field hide-details v-model="search" placeholder="ค้นหา" outlined dense @change="allFilter()" style="max-height: 36px;">
          <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
        </v-text-field>
      </v-col>
      <v-col cols="12" md="6" sm="12" align="start" :class="MobileSize ? 'my-2' : ''" style="display: flex; column-gap: 1rem;">
        <v-row dense>
          <v-col cols="6">
            <div style="height: 40px;">
              <v-dialog
                ref="dialogStartDate"
                v-model="dialogStartDate"
                :return-value.sync="date"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="searchStartDate"
                    placeholder="DD/MM/YYYY"
                    dense
                    outlined
                    readonly
                    hide-details
                    label="วันที่เริ่มต้น"
                    style="max-width: 100%"
                    v-bind="attrs"
                    v-on="on"
                  >
                    <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date"
                  scrollable
                  reactive
                  no-title
                  locale="Th-th"
                  @change="setValueStartDate(date)"
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModalStartDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.dialogStartDate.save(date); GetOrderToUploadIMG()"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </div>
          </v-col>
          <v-col cols="6">
            <div style="height: 40px;">
              <v-dialog
                ref="dialogEndDate"
                v-model="dialogEndDate"
                :return-value.sync="date1"
                persistent
                width="290px"
              >
              <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    v-model="searchEndDate"
                    placeholder="DD/MM/YYYY"
                    dense
                    outlined
                    readonly
                    hide-details
                    label="วันที่สิ้นสุด"
                    style="max-width: 100%;"
                    v-bind="attrs"
                    v-on="on"
                    :disabled="searchStartDate === '' ? true : false"
                  >
                    <v-icon slot="append" color="#27AB9C">mdi-calendar</v-icon>
                  </v-text-field>
                </template>
                <v-date-picker
                  v-model="date1"
                  scrollable
                  reactive
                  no-title
                  locale="Th-th"
                  @change="setValueEndDate(date1)"
                  :min="date"
                  :max="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModalEndDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.dialogEndDate.save(date1), GetOrderToUploadIMG()"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </div>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="6" sm="6" :align="IpadSize ? 'start': 'end'">
        <v-select v-model="selectShipping" :items="listCourier" item-text="courier_name" item-value="courier_code"  hide-details outlined dense label="ขนส่ง" no-data-text="ไม่มีรายชื่อขนส่ง" @change="GetOrderToUploadIMG()"></v-select>
      </v-col>
      <v-col cols="12" md="3" sm="6" :align="IpadSize ? 'start': 'end'">
        <v-select v-model="pageSize" :items="itemsPerPageList" hide-details outlined dense label="จำนวนข้อมูลต่อหน้า" @change="GetOrderToUploadIMG()"></v-select>
      </v-col>
      <v-col cols="12" md="3" sm="12" :align="IpadSize ? 'center': 'end'">
        <v-btn @click="clearFilter()" block class="white--text" color="#27AB9C">ล้างค่า</v-btn>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? 'py-1' : 'py-1 pl-3'" v-if="dataOrder.length !== 0">
      <v-col cols="12" md="6" sm="6">
        <v-checkbox v-model="checkAllOrder" label="เลือกรายการทั้งหมดในหน้านี้" color="#27AB9C" @click="SelectAll(dataOrder, checkAllOrder)" hide-details></v-checkbox>
      </v-col>
      <v-col cols="12" md="6" sm="6" :align="IpadSize ? 'end': 'end'" class="mt-2">
        <v-btn :block="MobileSize ? true : false" @click="ShowDialog()" :height="!MobileSize && !IpadProSize && !IpadSize ? '40' : '40'" rounded color="#27AB9C" class="white--text" :disabled="isChecked">แนบรูปยืนยันเข้ารับพัสดุ</v-btn>
      </v-col>
    </v-row>
    <v-row>
      <v-col>
        <a-tabs v-model="tabs" @change="SelectStatusOrder($event); clearSelectAll()">
          <a-tab-pane :key="0"><span slot="tab" value="all">ทั้งหมด <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="1"><span slot="tab" value="no_images">แนบรูปแล้ว <a-tag color="#6EC4D6" style="border-radius: 8px;">{{ countNoIMG }}</a-tag></span></a-tab-pane>
          <a-tab-pane :key="2"><span slot="tab" value="images">ยังไม่แนบรูป <a-tag color="#D1392B" style="border-radius: 8px;">{{ countIMG }}</a-tag></span></a-tab-pane>
        </a-tabs>
      </v-col>
    </v-row>
    <v-row dense :class="MobileSize ? '' : 'pl-3'" v-if="dataOrder.length !== 0">
      <v-col cols="12" v-for="(items, index) in dataOrder" :key="index" class="mb-4" :class="MobileSize ? 'px-0' : ''">
        <v-card width="100%" height="100%" outlined style="border-radius: 8px;">
          <v-card-text :class="MobileSize ? 'px-2' : ''">
            <v-col cols="12" align="end" v-if="MobileSize">
              <v-row justify="end">
                <v-col cols="12" class="pa-1 mt-2">
                  <v-btn block outlined color="#27AB9C" height="36" rounded class="fontres" :disabled="!(items.isSelect && items.media_details.length === 0 ? false : true) || (!items.isSelect && items.media_details.length === 0 ? false : true)" @click="ShowDialog(items)">
                    <v-icon small class="mr-2">mdi-plus-circle-outline</v-icon>แนบรูปยืนยันเข้ารับพัสดุ
                  </v-btn>
                </v-col>
              </v-row>
            </v-col>
            <v-row dense>
              <v-col :cols="!MobileSize && !IpadProSize && !IpadSize ? '6' : MobileSize ? '12' : '6'" align="start" class="pt-3 my-checkbox">
                <v-checkbox v-model="items.isSelect" class="mt-0" :label="'รายการสั่งซื้อสินค้า' + ' ' + `${ items.product_list.length }` + ' ' + 'รายการ'" color="#27AB9C" hide-details :disabled="items.media_details.length === 0 ? false : true" @change="checkIsChecked()"></v-checkbox>
              </v-col>
              <v-col cols="6" align="end" v-if="!MobileSize && !IpadProSize && !IpadSize" style="display: inline-block; margin: auto;">
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="!(items.isSelect && items.media_details.length === 0 ? false : true) || (!items.isSelect && items.media_details.length === 0 ? false : true)" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">แนบรูปยืนยันเข้ารับพัสดุ</div>
                </v-btn>
              </v-col>
              <v-col cols="6" align="end" v-if="IpadProSize && !IpadSize && !MobileSize" style="display: inline-block; margin: auto;">
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="!(items.isSelect && items.media_details.length === 0 ? false : true) || (!items.isSelect && items.media_details.length === 0 ? false : true)" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">แนบรูปยืนยันเข้ารับพัสดุ</div>
                </v-btn>
              </v-col>
              <v-col cols="6" align="end" v-if="!IpadProSize && IpadSize && !MobileSize" style="display: inline-block; margin: auto;">
                <v-btn outlined color="#27AB9C" height="40" rounded class="fontres" :disabled="!(items.isSelect && items.media_details.length === 0 ? false : true) || (!items.isSelect && items.media_details.length === 0 ? false : true)" @click="ShowDialog(items)">
                  <v-icon small class="mr-2 ">mdi-plus-circle-outline</v-icon><div class="stepAside">แนบรูปยืนยันเข้ารับพัสดุ</div>
                </v-btn>
              </v-col>
            </v-row>
            <v-row dense class="pt-2">
              <v-col cols="12">
                <!-- <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">หมายเหตุจากขนส่ง : <b style="color: #FAAD14;">{{ items.shipping_remark === '' ? '-' : items.shipping_remark }}</b></p> -->
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">รหัสคำสั่งซื้อ : <b>{{ items.order_number }}</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;">วันที่สั่งซื้อ : <b>{{ new Date(items.paid_datetime).toLocaleDateString("th-TH", { year: "numeric", month: "long", day: "numeric", hour: "numeric", minute: "numeric" }) }} น.</b></p>
                <p style="font-weight: 700; font-size: 18px; line-height: 24px; color: #000000;"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                <p style="font-weight: 400; font-size: 16px; line-height: 24px; color: #000000;" v-if="items.shipping_data !== null"><b>{{ items.shipping_data.length === 0 ? '' : items.shipping_data.dst_data.dst_name }}</b> <br v-if="MobileSize"/> {{items.shipping_data.length === 0 || items.shipping_data === null ? '' : items.shipping_data.dst_data.dst_address }}</p><br>
                <ul>
                  <li>
                    <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #333333;" ><b>Standard Delivery</b> : <br v-if="MobileSize"/><b v-if="items.shipping_data !== null">{{ items.shipping_data.tpl_name }}</b></span><br>
                  </li>
                </ul>
              </v-col>
            </v-row>
            <v-row v-if="items.media_details.length !== 0">
              <v-col cols="12" :class="MobileSize ? '' : 'd-flex'">
                <div v-for="(items2, index2) in items.media_details" :key="index2" :class="MobileSize ? 'd-flex justify-center mb-2' : 'mr-2'">
                  <v-card outlined class="pa-1" width="146" height="146" @click="openDialogShowUploadPhoto(items, index2)">
                    <v-img :src="items2.media_path" width="130" height="130" contain>
                    </v-img>
                  </v-card>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row dense v-else>
      <v-col cols="12">
        <v-card width="100%" :height="MobileSize ? '20vh' : '10vh'" outlined style="border-radius: 8px; border-color: #27AB9C;">
          <v-card-text class="text-center">
            <v-row justify="center" style="text-align: center; margin: auto;">
              <span style="font-size: 1rem; font-weight: 700;" :class="MobileSize ? 'pt-6' : IpadSize ? 'pt-6' : 'pt-2'">ไม่มีคำสั่งซื้อที่ยังไม่ดำเนินการ</span>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    <v-row no-gutters justify="center" class="my-6" v-if="current > -1">
      <v-col>
        <v-pagination color="#27AB9C" v-model="current" :length="checkPagination()" :total-visible="10"></v-pagination>
      </v-col>
    </v-row>
    <v-dialog v-model="dialogUploadPhotoShipping" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            แนบรูปยืนยันเข้ารับพัสดุ
          </span>
           <v-btn icon dark @click="closeDialogUploadPhotoShipping()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text>
          <!-- อัปโหลดรูปภาพ -->
          <v-row>
            <v-col cols="12" class="d-flex mt-5">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">รหัสคำสั่งซื้อ:
                <span>
                  <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">{{ TrancateOrderNumber(fullOrderNumbers) }}</span>
                    </template>
                    <div style="width: 300px;">
                      <span>{{ fullOrderNumbers }}</span>
                    </div>
                  </v-tooltip>
                  <!-- <v-tooltip top>
                    <template v-slot:activator="{ on, attrs }">
                      <span v-bind="attrs" v-on="on">{{item.order_number | truncate(80, '...')}}</span><br/>
                    </template>
                    <span>{{ item.order_number }}</span>
                  </v-tooltip> -->
                </span>
              </span>
            </v-col>
            <v-col cols="12" class="d-flex">
              <!-- <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">เลขพัสดุ:
                <span v-if="orderSelect.every(e => e.tracking_number === '-')">-</span>
                <span v-for="(item, index) in orderSelect" :key="index"> {{item.tracking_number === '-' ? '' : item.tracking_number }} <span v-if="index !== orderSelect.length - 1 && item.tracking_number !== '-'">,</span></span>
              </span> -->
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">เลขพัสดุ:
                <span v-if="orderSelect.every(e => e.tracking_number === '-')">-</span>
                <span v-for="(item, index) in orderSelect" :key="index"> {{item.tracking_number === '-' ? '' : item.tracking_number }} <span v-if="index !== orderSelect.length - 1 && item.tracking_number !== '-'">,</span></span>
              </span>
            </v-col>
            <v-col cols="12" class="d-flex" v-if="orderSelect.length > 0">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">วันที่สั่งซื้อ:
                <span>{{dateRange}}</span>
              </span>
            </v-col>
            <!-- <v-col v-for="(item, index) in orderSelect" :key="index" cols="12">
              <span>{{ item.order_number }}</span>
            </v-col> -->
          </v-row>
          <v-row>
            <v-col>
              <v-card
              class="mt-3"
              elevation="0"
              @click="onPickFileByOrder()"
              @drop.prevent="DropImageByOrder($event)"
              :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px; overflow: hidden;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px; overflow: hidden;'"
              >
              <v-file-input
                  v-model="DataImage"
                  :items="DataImage"
                  accept="image/jpeg, image/jpg, image/png"
                  @change="onFileSelectedByOrder(DataImage)"
                  id="file_input"
                  multiple
                  :clearable="false"
                  style="display:none"
              ></v-file-input>
              <v-col cols="12" md="12">
                  <v-row justify="center" align="center">
                  <v-col cols="12" md="12" align="center">
                      <v-img
                      src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                      width="280.34"
                      height="154.87"
                      contain
                      v-if="orderImagePath.length === 0"
                      ></v-img>
                      <v-row v-if="orderImagePath.length !== 0">
                      <v-col v-for="(items, index2) in orderImagePath" :key="index2" cols="12" md="4" sm="4">
                          <v-card outlined class="pa-1" width="146" height="146">
                          <v-img :src="items" width="130" height="130" contain>
                              <v-btn icon x-small style="float: right; background-color: #ff5252;">
                              <v-icon x-small color="white" dark @click.prevent.stop="RemoveImageMultiByOrder(index2)">mdi-close</v-icon>
                              </v-btn>
                          </v-img>
                          </v-card>
                      </v-col>
                      </v-row>
                  </v-col>
                  <v-col cols="12" md="12" style="text-align: center;" v-if="orderImagePath.length === 0">
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                      <span style="line-height: 24px; font-weight: 400;" :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                  </v-col>
                  </v-row>
              </v-col>
              </v-card>
              <v-row v-if="DataImage.length !== 0">
                <v-col v-for="(item, index) in DataImage" :key="index">
                  <canvas :id="`canvas${index}`" :ref="`canvas${index}`" style="display: none;"></canvas>
                </v-col>
              </v-row>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" outlined rounded color="#27AB9C" @click="closeDialogUploadPhotoShipping()" class="mr-2">ยกเลิก</v-btn>
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="confirmUploadIMG()">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogShowUploadPhoto" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 12px;" width="100%" height="100%">
        <v-toolbar align="center" color="#DAF1E9" dark dense elevation="0">
          <span class="flex text-center" :style="MobileSize ? 'font-weight: 600; font-size: 16px; line-height: 24px; color: #27AB9C;' : 'font-weight: 600; font-size: 18px; line-height: 26px; color: #27AB9C;'">
            รูปยืนยันเข้ารับพัสดุ
          </span>
           <v-btn icon dark @click="closeDialogShowUploadPhoto()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text v-if="currentIMG.length !== 0">
          <v-img :src="currentIMG[currentIMGIndex].media_path" contain style="position: relative; height: 400px;">
            <div class="d-flex" style="position: absolute; top: 50%; left: 0; right: 0; transform: translateY(-50%); display: flex; justify-content: space-between; padding: 0 16px; pointer-events: none;">
              <v-icon @click="prevIMG()" color="#27AB9C" size="30" style="pointer-events: auto; background: white; border-radius: 50%;" :disabled="currentIMGIndex === 0">mdi-chevron-left</v-icon>
              <v-spacer></v-spacer>
              <v-icon @click="nextIMG()" color="#27AB9C" size="30" style="pointer-events: auto; background: white; border-radius: 50%;" :disabled="currentIMGIndex === currentIMG.length - 1">mdi-chevron-right</v-icon>
            </div>
          </v-img>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="pb-4">
            <v-btn width="110" height="40" rounded color="#27AB9C" class="white--text" @click="closeDialogShowUploadPhoto()">ตกลง</v-btn>
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  filters: {
    truncate: function (value, limit) {
      if (value !== null) {
        if (value.length > limit) {
          value = value.substring(0, (limit - 4)) + '...'
        }
        return value
      }
    }
  },
  data () {
    return {
      theRedI: true,
      dataOrder: [],
      listShow: [],
      DataImage: [],
      orderImagePath: [],
      base64ImageList: [],
      shopID: -3,
      checkAllOrder: false,
      orderSelectAll: [],
      dialogUploadPhotoShipping: false,
      orderSelect: [],
      search: '',
      dialogStartDate: false,
      dialogEndDate: false,
      searchStartDate: '',
      searchEndDate: '',
      date: '',
      date1: '',
      selectShipping: '',
      listCourier: [],
      countOrderAll: 0,
      countNoIMG: 0,
      countIMG: 0,
      status: 'all',
      tabs: 0,
      isChecked: true,
      current: 1,
      pageSize: 5,
      itemsPerPageList: [
        { text: '5', value: 5 },
        { text: '10', value: 10 },
        { text: '20', value: 20 },
        { text: '50', value: 50 },
        { text: 'ทั้งหมด', value: -1 }
      ],
      maxItem: 0,
      currentIMG: [],
      currentIMGIndex: 0,
      dialogShowUploadPhoto: false,
      trackingList: [],
      dateRange: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        this.current = newPage
      }
    },
    paginated () {
      if (this.search === '') {
        return this.detailCoupon.slice(this.indexStart, this.indexEnd)
      } else {
        var items = this.detailCoupon.filter(e => {
          if (this.search !== '') {
            return e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false)
          }
        })
        return items.slice(this.indexStart, this.indexEnd)
      }
    },
    fullOrderNumbers () {
      return this.orderSelect.map(o => o.order_number).join(', ')
    },
    truncatedOrderNumbers () {
      const str = this.fullOrderNumbers
      const limit = 20
      return str.substring(0, limit) + '...'
    },
    truncateTrackingNumbers () {
      const str = this.fullTrackingNumbers
      const limit = 20
      return str.substring(0, limit) + '...'
    },
    fullTrackingNumbers () {
      return this.orderSelect.map(o => o.tracking_number).join(', ')
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/UpLoadPickUpOrderIMGAdminMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/UpLoadPickUpOrderIMGAdmin' }).catch(() => {})
      }
    },
    dataOrder (val) {
      this.checkIsChecked()
    },
    async current (val) {
      // await this.clearSelectAll()
      await this.GetOrderToUploadIMG()
    }
  },
  async created () {
    // this.$EventBus.$emit('SelectPath')
    // this.$EventBus.$emit('checkAuthUser')
    await this.getCourierType()
    await this.GetOrderToUploadIMG()
    // await this.getOrderCount()
  },
  methods: {
    onPickFileByOrder () {
      document.getElementById('file_input').click()
    },
    async onFileSelectedByOrder (files) {
      // console.log('Ready to upload:', files, files.length)
      this.base64ImageList = []
      this.DataImage = files
      // await this.EditWaterMark()
      var files2 = await this.EditWaterMark(files)
      const fileLength = files2.length
      if (fileLength > 3) {
        this.$swal.fire({
          icon: 'warning',
          text: 'ใส่ไม่ได้เกิน 3 ภาพ',
          showConfirmButton: false,
          timer: 1500
        })
        return
      }
      // console.log(this.base64ImageList, this.base64ImageList.length, 'this.base64ImageList')
      if (files2 && files2.length > 0) {
        const file = files2
        // console.log(file, 'file[i]')
        var base64ImageList = []
        for (let i = 0; i < file.length; i++) {
          const element = file[i]
          if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png' || element.type === 'image/webp') {
            const base64 = await new Promise((resolve, reject) => {
              const reader = new FileReader()
              reader.readAsDataURL(element)
              reader.onload = async () => {
                resolve(reader.result)
              }
              reader.onerror = reject
            })

            base64ImageList.push(base64)
          }
        }
        this.orderImagePath = base64ImageList
        // const Image = {
        //   image: base64ImageList,
        //   type: 'shipment',
        //   seller_shop_id: this.shopID
        // }

        // this.$store.commit('openLoader')

        // await this.$store.dispatch('actionsUploadToS3', Image)
        // const response = this.$store.state.ModuleShop.stateUploadToS3
        // if (response.message === 'List Success.') {
        //   this.$store.commit('closeLoader')
        //   var imagePath = response.data.list_path.map(e => e.path)
        //   for (var i = 0; i < imagePath.length; i++) {
        //     if (this.orderImagePath.length < 3) {
        //       this.orderImagePath.push(imagePath[i])
        //     } else {
        //       this.$swal.fire({
        //         icon: 'warning',
        //         text: 'ใส่ไม่ได้เกิน 3 ภาพ',
        //         showConfirmButton: false,
        //         timer: 1500
        //       })
        //     }
        //   }
        //   this.theRedI = true
        // }
      }
    },
    RemoveImageMultiByOrder (index2) {
      this.orderImagePath.splice(index2, 1)
    },
    async EditWaterMark (files) {
      // console.log(files, 'files')

      const processedFiles = await Promise.all(
        Array.from(files).map((file, index) => {
          return new Promise((resolve, reject) => {
            const reader = new FileReader()

            reader.onload = (e) => {
              const img = new Image()

              img.onload = () => {
                const canvas = this.$refs[`canvas${index}`][0]
                if (!canvas) {
                  console.error(`ไม่พบ canvas${index}`)
                  return reject(new Error(`Canvas ${index} not found`))
                }

                const ctx = canvas.getContext('2d')
                canvas.width = img.width
                canvas.height = img.height

                ctx.drawImage(img, 0, 0)

                // Add timestamp
                const timestamp = new Date().toLocaleString()
                const fontSize = Math.floor(canvas.width * 0.035)
                ctx.font = `${fontSize}px Arial`
                ctx.fillStyle = 'white'
                ctx.textAlign = 'right'
                ctx.textBaseline = 'bottom'

                const padding = 10
                ctx.strokeStyle = 'black'
                ctx.lineWidth = 3
                ctx.strokeText(timestamp, canvas.width - padding, canvas.height - padding)
                ctx.fillText(timestamp, canvas.width - padding, canvas.height - padding)

                canvas.toBlob((blob) => {
                  if (!blob) return reject(new Error('Blob is null'))

                  const newFile = new File([blob], 'timestamped_' + file.name, { type: blob.type })
                  resolve(newFile)
                }, file.type)
              }

              img.onerror = (e) => reject(new Error('Image load failed'))
              img.src = e.target.result
            }

            reader.onerror = (e) => reject(new Error('FileReader failed'))
            reader.readAsDataURL(file)
          })
        })
      )

      this.base64ImageList = processedFiles
      return processedFiles
    },
    async GetOrderToUploadIMG () {
      // console.log('come')
      this.$store.commit('openLoader')
      this.dataOrder = []
      var data = {
        seller_shop_id: this.shopID,
        count: this.pageSize,
        courier_code: this.selectShipping,
        start_date: this.date,
        end_date: this.date1,
        search_keyword: this.search,
        page: this.current,
        status: this.status
      }
      await this.$store.dispatch('actionsGetWithoutImages', data)
      var res = await this.$store.state.ModuleDashboardTransport.stateGetWithoutImages
      // console.log(res, 'res')
      if (res.message === 'Get orders list success') {
        this.$store.commit('closeLoader')
        this.dataOrder = res.data.data.map(e => {
          return {
            ...e,
            isSelect: false
          }
        })
        this.maxItem = res.data.max_items
        this.countOrderAll = res.data.selected_status.all
        this.countNoIMG = res.data.selected_status.has_media
        this.countIMG = res.data.selected_status.no_media
      } else {
        this.$store.commit('closeLoader')
      }
    },
    async getCourierType () {
      var data = {
        seller_shop_id: this.shopID
      }
      await this.$store.dispatch('ActionsGetCourierTypeII', data)
      const response = await this.$store.state.ModuleManageShop.GetCourierTypeII
      const dataListCourier = [{ courier_name: 'ขนส่งทั้งหมด', courier_code: '' }]
      var listCourier = response.data.data
      for (let i = 0; i < listCourier.length; i++) {
        dataListCourier.push({
          courier_name: listCourier[i].name === '' ? 'ขนส่งทั้งหมด' : listCourier[i].name,
          courier_code: listCourier[i].type_id
        })
      }
      this.listCourier = await dataListCourier
    },
    SelectAll (data, check) {
      // console.log(data, check)
      if (check === true) {
        this.dataOrder = []
        for (var i = 0; i < data.length; i++) {
          if (data[i].media_details.length === 0) {
            data[i].isSelect = true
          }
        }
        this.dataOrder = data
      } else {
        this.dataOrder = []
        for (var j = 0; j < data.length; j++) {
          data[j].isSelect = false
        }
        this.dataOrder = data
      }
    },
    ShowDialog (items) {
      this.orderImagePath = []
      // console.log([items], 45)
      if (items) {
        this.orderSelect.push({ order_number: items.order_number })
        this.dateRange = this.formatDateRangeMinToMax([items])
      } else {
        this.orderSelect = this.dataOrder.filter(e => e.isSelect === true && e.media_details.length === 0)
        this.dateRange = this.formatDateRangeMinToMax(this.orderSelect)
      }
      // console.log(items, 'items')
      this.dialogUploadPhotoShipping = true
    },
    closeDialogUploadPhotoShipping () {
      this.dialogUploadPhotoShipping = false
      this.orderImagePath = []
      this.DataImage = []
      this.orderSelect = []
    },
    async confirmUploadIMG () {
      // this.orderSelect = this.dataOrder.filter(e => e.isSelect === true)
      // console.log(this.orderImagePath, 'this.orderImagePath')
      var base64ImageList = this.orderImagePath.map(e => e.split(',')[1])
      // console.log(base64ImageList, 'base64ImageList')
      const Image = {
        image: base64ImageList,
        type: 'shipment',
        seller_shop_id: this.shopID
      }

      this.$store.commit('openLoader')

      await this.$store.dispatch('actionsUploadToS3', Image)
      const response2 = this.$store.state.ModuleShop.stateUploadToS3
      if (response2.message === 'List Success.') {
        this.$store.commit('closeLoader')
        this.orderImagePath = []
        var imagePath = response2.data.list_path.map(e => e.path)
        // console.log(imagePath, 'imagePath')
        for (var i = 0; i < imagePath.length; i++) {
          if (this.orderImagePath.length < 3) {
            this.orderImagePath.push(imagePath[i])
          }
        }
        var data = {
          seller_shop_id: this.shopID,
          mappings: [
            {
              order_numbers: this.orderSelect.map(e => e.order_number),
              images: this.orderImagePath
            }
          ]
        }
        await this.$store.dispatch('actionsUploadProofOfDelivery', data)
        const response = this.$store.state.ModuleDashboardTransport.stateUploadProofOfDelivery
        if (response.message === 'Save images success.') {
          this.$swal.fire({
            icon: 'success',
            text: 'อัปโหลดรูปภาพสำเร็จ',
            showConfirmButton: false,
            timer: 1500
          })
        } else {
          this.$swal.fire({
            icon: 'error',
            text: response.message,
            showConfirmButton: false,
            timer: 1500
          })
        }
        this.orderSelect = []
        this.dialogUploadPhotoShipping = false
        await this.GetOrderToUploadIMG()
        this.theRedI = true
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: response2.message,
          showConfirmButton: false,
          timer: 1500
        })
        this.$store.commit('closeLoader')
      }
    },
    closeModalStartDate () {
      this.dialogStartDate = false
      this.searchStartDate = ''
      this.date = ''
    },
    closeModalEndDate () {
      this.dialogEndDate = false
      this.searchEndDate = ''
      this.date1 = ''
      this.GetOrderToUploadIMG()
    },
    setValueStartDate (val) {
      this.searchEndDate = ''
      this.searchStartDateNotFormat = val
      this.searchStartDate = this.formatDate(val)
    },
    setValueEndDate (val) {
      this.searchEndDate = this.formatDate(val)
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    SelectStatusOrder (val) {
      if (val === 0) {
        this.status = 'all'
      } else if (val === 1) {
        this.status = 'images'
      } else if (val === 2) {
        this.status = 'no-images'
      }
      this.GetOrderToUploadIMG()
      // this.allFilter()
    },
    getOrderCount () {
      // this.countOrderAll = this.dataOrder.length
      // this.countNoIMG = this.dataOrder.filter(e => e.media_details.length > 0).length
      // this.countIMG = this.dataOrder.filter(e => e.media_details.length === 0).length
      this.pageSize = 5
      this.GetOrderToUploadIMG()
    },
    checkIsChecked () {
      this.isChecked = this.dataOrder.every(x => x.isSelect === false)
    },
    backtoPage () {
      if (!this.MobileSize) {
        this.$router.push({ path: '/manageOrderShipping' }).catch(() => {})
      } else {
        this.$router.push({ path: '/manageOrderShippingMobile' }).catch(() => {})
      }
    },
    checkPagination () {
      // var max = null
      // var items = this.detailCoupon.filter(e => {
      //   if (this.search !== '' && this.selectType === 'all') {
      //     return e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false)
      //   } else if (this.search === '' && this.selectType !== 'all') {
      //     return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
      //   } else if (this.search !== '' && this.selectType !== 'all') {
      //     return e.coupon_type.toLowerCase() === (this.selectType.toLowerCase()) && (e.coupon_name.toLowerCase().includes(this.search.toLowerCase()) || (e.coupon_code !== null ? e.coupon_code.toLowerCase().includes(this.search.toLowerCase()) : false))
      //   } else {
      //     return this.detailCoupon
      //   }
      // })
      // this.countPromotion = items.length
      var max = Math.ceil(this.maxItem / this.pageSize)
      // this.pageNumber = this.pageNumber > max ? max : this.pageNumber
      return max
    },
    async allFilter () {
      if (this.search === '' && this.date === '' && this.date1 === '' && this.selectShipping === '' && this.status === 'all') {
        this.pageSize = -1
      } else {
        this.pageSize = 5
      }
      this.current = 1
      await this.GetOrderToUploadIMG()
      await this.checkPagination()
      await this.clearSelectAll()
      // await this.getOrderCount()
    },
    clearSelectAll () {
      this.checkAllOrder = false
      for (var i = 0; i < this.dataOrder.length; i++) {
        this.dataOrder[i].isSelect = false
      }
      this.current = 1
      // this.GetOrderToUploadIMG()
    },
    openDialogShowUploadPhoto (val, index) {
      this.currentIMG = val.media_details
      this.currentIMGIndex = index
      this.dialogShowUploadPhoto = true
    },
    closeDialogShowUploadPhoto () {
      this.dialogShowUploadPhoto = false
    },
    nextIMG () {
      if (this.currentIMGIndex < this.currentIMG.length - 1) {
        this.currentIMGIndex++
      }
    },
    prevIMG () {
      if (this.currentIMGIndex > 0) {
        this.currentIMGIndex--
      }
    },
    clearFilter () {
      this.search = ''
      this.searchStartDate = ''
      this.searchEndDate = ''
      this.date = ''
      this.date1 = ''
      this.selectShipping = ''
      this.status = 'all'
      this.current = 1
      this.pageSize = 5
      this.GetOrderToUploadIMG()
    },
    formatDateRangeMinToMax (val) {
      var minDate = val[0].paid_datetime.substr(0, 10)
      var maxDate = val[0].paid_datetime.substr(0, 10)
      for (var i = 0; i < val.length; i++) {
        if (val[i].paid_datetime.substr(0, 10) < minDate) {
          minDate = val[i].paid_datetime.substr(0, 10)
        }
        if (val[i].paid_datetime.substr(0, 10) > maxDate) {
          maxDate = val[i].paid_datetime.substr(0, 10)
        }
      }
      if (minDate === maxDate) {
        var dateRange = this.formatDate(minDate)
        return dateRange
      } else {
        dateRange = this.formatDate(minDate) + ' ถึง ' + this.formatDate(maxDate)
        return dateRange
      }
    },
    TrancateOrderNumber (val) {
      if (val.length <= 20) {
        return val
      }
      return val.substring(0, 30) + '...'
    }
  }
}
</script>

<style>
.background_color_Mobile{
  background-color: #FFFFFF;
  border: 1px solid #DBECFA;
  border-radius: 8px;
}
</style>
