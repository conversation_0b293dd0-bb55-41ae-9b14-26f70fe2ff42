import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  async CreateFlashSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/create`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetFlashSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async GetListProductDetail (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/list`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteFlashSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/delete`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async UpdateFlashSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/update`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async ListAllShopProductFlashSale (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END}api/flash_sale/list_product_in_shop_falsh_sale`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
