<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize">รายการการสั่งซื้อสินค้า JV</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>รายการการสั่งซื้อสินค้า JV</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาเลขสั่งซื้อ/ผู้สั่งซื้อ/รหัสร้านค้า" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="adminShopListeTax.length !== 0 && (!MobileSize && !IpadSize)">รายการสั่งซื้อของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="adminShopListeTax.length !== 0 && (MobileSize || IpadSize)">รายการสั่งซื้อของระบบทั้งหมด {{ showCountRequest }} รายการ</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="showOrder"
                :search="search"
                style="width:100%; text-align: center;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบรายการการสั่งสินค้าของระบบ"
                no-data-text="ไม่พบรายการการสั่งสินค้าของระบบ"
                :update:items-per-page="itemsPerPage"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.buyer_name`]="{ item }">
                    <span class="nameBuyer" style="white-space: nowrap;">{{ item.buyer_name }}</span>
                  </template>
                  <template v-slot:[`item.order_number`]="{ item }">
                    <span style="white-space: nowrap;">{{ item.order_number }}</span>
                  </template>
                  <template v-slot:[`item.data_response_pr`]="{ item }">
                    <v-btn v-if="item.data_response_pr" color="#27AB9C" outlined @click="OpenDialogShowData('data response pr', item.data_response_pr)">ดู response pr</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.send_pr_timestamp`]="{ item }">
                    <span v-if="item.send_pr_timestamp" color="#27AB9C" > {{new Date(item.send_pr_timestamp).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.data_send_pr`]="{ item }">
                    <v-btn v-if="item.data_send_pr" color="#27AB9C" outlined @click="OpenDialogShowData('data send pr', item.data_send_pr)">ดู data send pr</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.data_send_pdf_pr`]="{ item }">
                    <v-btn v-if="item.data_send_pdf_pr" color="#27AB9C" outlined @click="OpenDialogShowData('data send pdf pr', item.data_send_pdf_pr)">ดู data send pdf pr</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.data_response_pdf_pr`]="{ item }">
                    <span v-if="item.data_response_pdf_pr" color="#27AB9C" > {{ item.data_response_pdf_pr }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.send_pdf_pr_timestamp`]="{ item }">
                    <span v-if="item.send_pdf_pr_timestamp" color="#27AB9C" > {{new Date(item.send_pdf_pr_timestamp).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.data_send_so`]="{ item }">
                    <v-btn v-if="item.data_send_so" color="#27AB9C" outlined @click="OpenDialogShowData('data send so', item.data_send_so)">ดู data send so</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.data_response_so`]="{ item }">
                    <span v-if="item.data_response_so" color="#27AB9C" > {{ item.data_response_so }}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.send_so_timestamp`]="{ item }">
                    <span v-if="item.send_so_timestamp" color="#27AB9C" > {{new Date(item.send_so_timestamp).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_personal`]="{ item }">
                    <v-btn v-if="item.json_personal !== '[]' " color="#27AB9C" outlined @click="OpenDialogShowData('json personal', item.json_personal)">ดู json personal</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_buyer`]="{ item }">
                    <v-btn v-if="item.json_buyer !== '[]'" color="#27AB9C" outlined @click="OpenDialogShowData('json buyer', item.json_buyer)">ดู json buyer</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_mobilyst_req`]="{ item }">
                    <v-btn v-if="item.json_mobilyst_req" color="#27AB9C" outlined @click="OpenDialogShowData('mobilyst req', item.json_mobilyst_req)">ดู mobilyst req</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_mobilyst_res`]="{ item }">
                    <v-btn v-if="item.json_mobilyst_res" color="#27AB9C" outlined @click="OpenDialogShowData('mobilyst res', item.json_mobilyst_res)">ดู mobilyst res</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_refshare_req`]="{ item }">
                    <v-btn v-if="item.json_refshare_req" color="#27AB9C" outlined @click="OpenDialogShowData('refshare req', item.json_refshare_req)">ดู refshare req</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_refshare_res`]="{ item }">
                    <v-btn v-if="item.json_refshare_res" color="#27AB9C" outlined @click="OpenDialogShowData('json refshare res', item.json_refshare_res)">ดู refshare res</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_etax_req`]="{ item }">
                    <v-btn v-if="item.json_etax_req" color="#27AB9C" outlined @click="OpenDialogShowData('json etax req', item.json_etax_req)">ดู etax req</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.json_etax_res`]="{ item }">
                    <v-btn v-if="item.json_etax_res" color="#27AB9C" outlined @click="OpenDialogShowData('json etax res', item.json_etax_res)">ดู etax res</v-btn>
                    <span v-else>-</span>
                  </template>
                  <template v-slot:[`item.created_at`]="{ item }">
                    <span style="white-space: nowrap;"> {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}</span>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
      <v-dialog v-model="modalShowData" :width="MobileSize ? '100%' : '50%'" persistent>
        <v-card>
            <v-card-title class="text-h5 grey lighten-2">
              {{ dataHeader }}
              <v-btn text color="#b1afae" style="margin-left: auto" @click="handleClick()"><v-icon>mdi-content-copy</v-icon>คัดลอก</v-btn>

              <v-tooltip
                v-model="showText"
                top
              >
                <template v-slot:activator="{ on, attrs }">
                  <p
                    icon
                    v-bind="attrs"
                    v-on="on"
                  >
                  </p>
                </template>
                <span>คัดลอกสำเร็จ</span>
              </v-tooltip>
            </v-card-title>

            <v-card-text class="pt-4">
              <pre style="white-space: wrap;" v-html="dataToShow"></pre>
            </v-card-text>

            <v-divider></v-divider>

            <v-card-actions>
              <v-spacer></v-spacer>
              <v-btn
                color="primary"
                text
                @click="modalShowData = false"
              >
                ปิด
              </v-btn>
            </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
  </v-container>
</template>

<script>
// import { Encode } from '@/services'
export default {
  data () {
    return {
      showOrder: [],
      search: '',
      modalShowData: false,
      adminShopListeTax: [],
      dataToShow: '',
      dataHeader: '',
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      isSuperAdmin: null,
      showText: false,
      headers: [
        { text: 'order_number', value: 'order_number', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'buyer_name', value: 'buyer_name', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'seller_shop_id', value: 'seller_shop_id', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_send_pr', value: 'data_send_pr', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_response_pr', value: 'data_response_pr', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'send_pr_timestamp', value: 'send_pr_timestamp', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_send_pdf_pr', value: 'data_send_pdf_pr', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_response_pdf_pr', value: 'data_response_pdf_pr', width: '250', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'send_pdf_pr_timestamp', value: 'send_pdf_pr_timestamp', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_send_so', value: 'data_send_so', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'data_response_so', value: 'data_response_so', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'send_so_timestamp', value: 'send_so_timestamp', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_personal', value: 'json_personal', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_buyer', value: 'json_buyer', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_mobilyst_req', value: 'json_mobilyst_req', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_mobilyst_res', value: 'json_mobilyst_res', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_refshare_req', value: 'json_refshare_req', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_refshare_res', value: 'json_refshare_res', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_etax_req', value: 'json_etax_req', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'json_etax_res', value: 'json_etax_res', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'created_by', value: 'created_by', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'created_at', value: 'created_at', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/orderJVMobile' }).catch(() => {})
      } else {
        // await localStorage.setItem('pathAdmin', 'orderJV')
        this.$router.push({ path: '/orderJV' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    if (localStorage.getItem('oneData') !== null) {
      this.getOrderList()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    OpenDialogShowData (type, data) {
      this.dataToShow = ''
      this.dataHeader = ''
      if (type === 'data send pr') {
        this.dataHeader = 'Data Send PR'
        this.dataToShow = data
      } else if (type === 'data response pr') {
        this.dataHeader = 'Data Response PR'
        this.dataToShow = data
      } else if (type === 'data send pdf pr') {
        this.dataHeader = 'Data Send PDF PR'
        this.dataToShow = data
      } else if (type === 'json personal') {
        this.dataHeader = 'Json Personal'
        this.dataToShow = data
      } else if (type === 'json buyer') {
        this.dataHeader = 'Json Buyer'
        this.dataToShow = data
      } else if (type === 'data send so') {
        this.dataHeader = 'Data Send So'
        this.dataToShow = data
      } else if (type === 'mobilyst req') {
        this.dataHeader = 'Mobilyst Req'
        this.dataToShow = data
      } else if (type === 'mobilyst res') {
        this.dataHeader = 'Mobilyst Res'
        this.dataToShow = data
      } else if (type === 'refshare req') {
        this.dataHeader = 'Refshare Req'
        this.dataToShow = data
      } else if (type === 'json refshare res') {
        this.dataHeader = 'Json Refshare Res'
        this.dataToShow = data
      } else if (type === 'json etax req') {
        this.dataHeader = 'Json Etax Req'
        this.dataToShow = data
      } else {
        this.dataHeader = 'Json Etax Res'
        this.dataToShow = data
      }
      this.modalShowData = true
    },
    handleClick () {
      this.copyText()
      this.showText = !this.showText
    },
    copyText () {
      const text = this.dataToShow
      if (text) {
        navigator.clipboard.writeText(text).then(() => {
          // console.log('Copy Text Success:', text)
        }).catch(err => {
          console.error('Copy Text Fail:', err)
        })
      }
    },
    backtoPage () {
      this.$router.push({ path: '/AdminPanitMobile' }).catch(() => {})
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getOrderList () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsOrderList')
      var response = this.$store.state.ModuleOrderList.stateOrderList
      this.showOrder = response.data
      this.$store.commit('closeLoader')
      // console.log('showOrder---->', this.showOrder)
    }

  }
}
</script>

<style scoped>
.theme--light.v-data-table > .v-data-table__wrapper > table > thead > tr:last-child > th {
  white-space: nowrap !important;
  text-align: center !important;
}
.v-data-table > .v-data-table__wrapper > table > tbody > tr > td {
  text-align: center !important;
}
</style>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
