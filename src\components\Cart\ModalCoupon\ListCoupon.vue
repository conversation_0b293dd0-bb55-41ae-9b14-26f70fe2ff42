<template>
  <div class="text-center">
    <v-dialog v-model="ModalCoupon" width="684" persistent :style="MobileSize ? 'z-index: 16000103;' : ''">
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 8px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 8px;">
                  <div :class="MobileSize ? 'py-6 px-0' : 'pt-7 px-2'">
                    <v-card-text v-if="!MobileSize" class="pa-0">
                      <v-col class="py-0">
                        <v-row dense class="align-center">
                          <v-col class="py-0">
                            <v-row class="">
                              <v-col cols="12" class="py-0">
                                <v-row dense class="pt-2 pb-6 align-center">
                                  <v-img src="@/assets/couponShop.png" max-width="26" max-height="26">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 18px; font-weight: 600;">
                                    {{ $t('ListCoupon.Header') }}
                                  </span>
                                </v-row>
                                <v-text-field v-model="search" clearable clear-icon="mdi-close-circle" @click:clear="clearMessage" hide-details height="40" :placeholder="$t('ListCoupon.PlaceHolder')" oninput="this.value = this.value.replace(/[^A-Za-z0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="isLetterEng($event)" @keydown.enter="submit" outlined dense>
                                  <template slot="append">
                                    <v-img src="@/assets/Magnifer.png" max-width="24" max-height="24">
                                    </v-img>
                                  </template>
                                </v-text-field>
                              </v-col>
                              <!-- <v-col class="pl-0 pb-0 pt-0" cols="2">
                                <v-btn width="100%" height="42" color="#27AB9C" @click="submit2()">
                                  <v-icon color="#FFFFFF">mdi-magnify</v-icon>
                                </v-btn>
                              </v-col> -->
                            </v-row>
                          </v-col>
                          <v-col cols="12" class="pt-2 pb-2">
                            <!-- <span style="font-size: 16px; font-weight: 700;">รายชื่อพนักงาน {{CodePlatform.length}} รายชื่อ</span> -->
                          </v-col>
                        </v-row>
                        <v-row no-gutters v-if="searchData.length !== 0" class="rounded-lg mt-6" style="max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="searchData.length !== 0 " class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <div v-for="(item, index) in searchData" :key="index">
                              <v-card-text v-if="index < showProductSearch" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%" height="120">
                                  <v-row dense class="px-4 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else-if="item.coupon_image === null && item.coupon_type === 'discount'" style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else-if="item.coupon_image === null && item.coupon_type === 'free_shipping'" style="border-radius: 8px;" src="@/assets/freeShippingCoupon.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/coupon_image/xgifty.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-col class="pl-4" style="min-width: 330px; max-width: 330px;">
                                      <span class="d-inline-block text-truncate" style="color: #269AFD; font-size: 18px; font-weight: 700; min-width: 300px; max-width: 320px;">{{item.coupon_name}}</span>
                                      <!-- <span class="d-inline-block text-truncate" style="color: #333333; font-size: 16px; font-weight: 400; min-width: 300px; max-width: 320px;">สำหรับร้านค้า {{item.shop_name}} เท่านั้น</span> -->
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.id"></v-radio>
                                        </v-radio-group>
                                        <span class="" style="color: #333333; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.SelectCoupon') }}</span>
                                    </v-col>
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllSearch && searchData.length > 3">
                              <v-btn text @click="ShowProductSearch()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                        </v-row>
                        <v-row no-gutters v-else-if="(Cards.length || (Shipping.length !== 0 && PickUp === false) || Free.length !== 0)"  class="rounded-lg mt-6" style="max-height: 400px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="Cards.length !== 0 " class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 18px; font-weight: 600;">{{ $t('ListCoupon.GeneralDiscount') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Cards" :key="index">
                              <v-card-text v-if="index < showProductPriceDiscount" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%" height="120">
                                  <v-row dense class="px-4 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-col class="pl-4" style="min-width: 330px; max-width: 330px;">
                                      <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 18px; font-weight: 700; min-width: 300px; max-width: 320px;">{{item.coupon_name}}</span>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 16px; font-weight: 400; min-width: 300px; max-width: 320px;">{{ $t('ListCoupon.ForStore') }} {{item.shop_name}} {{ $t('ListCoupon.Only') }}</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                        <span class="" style="color: #333333; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.SelectCoupon') }}</span>
                                    </v-col>
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllCards && Cards.length > 3">
                              <v-btn text @click="ShowProductPriceDiscount()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                          <v-card v-if="Shipping.length !== 0 && PickUp === false" class="rounded-lg mt-6 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 18px; font-weight: 600;">{{ $t('ListCoupon.ShippingDiscount') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Shipping" :key="index">
                              <v-card-text v-if="index < showShippingDiscount" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%" height="120">
                                  <v-row dense class="px-4 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/freeShippingCoupon.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-col class="pl-4" style="min-width: 330px; max-width: 330px;">
                                      <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 18px; font-weight: 700; min-width: 300px; max-width: 320px;">{{item.coupon_name}}</span>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 16px; font-weight: 400; min-width: 300px; max-width: 320px;">{{ $t('ListCoupon.ForStore') }} {{item.shop_name}} {{ $t('ListCoupon.Only') }}</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                        <span class="" style="color: #333333; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.SelectCoupon') }}</span>
                                    </v-col>
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllShipping && Shipping.length > 3">
                              <v-btn text @click="ShowShippingDiscount()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                          <v-card v-if="Free.length !== 0" class="rounded-lg mt-6 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 18px; font-weight: 600;">{{ $t('ListCoupon.FreeGiftCoupon') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Free" :key="index">
                              <v-card-text v-if="index < showFreeGiftCoupon" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%" height="120">
                                  <v-row dense class="px-4 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="100" max-height="100">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/coupon_image/xgifty.png" max-width="100" max-height="100">
                                    </v-img>
                                    <v-col class="pl-4" style="min-width: 330px; max-width: 330px;">
                                      <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 18px; font-weight: 700; min-width: 300px; max-width: 320px;">{{item.coupon_name}}</span>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 16px; font-weight: 400; min-width: 300px; max-width: 320px;">{{ $t('ListCoupon.ForStore') }} {{item.shop_name}} {{ $t('ListCoupon.Only') }}</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.Validfrom') }} {{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                        <span class="" style="color: #333333; font-size: 14px; font-weight: 400;">{{ $t('ListCoupon.SelectCoupon') }}</span>
                                    </v-col>
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllFree && Free.length > 3">
                              <v-btn text @click="ShowFreeGiftCoupon()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                        </v-row>
                        <v-card v-else class="rounded-lg mt-6 py-6 px-4 "  elevation="0" style=" background-color: #FFFFFF;" >
                          <v-card-text>
                            <v-col cols="12" align="center">
                              <div class="mb-5">
                                <v-img src="@/assets/nocoupon.png" width="120" height="120" contain></v-img>
                              </div>
                              <div>
                                <span style="font-size: 16px; font-weight: 400; color: #9A9A9A;">{{ $t('ListCoupon.NoCoupon') }}</span>
                              </div>
                            </v-col>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-card-text>
                    <v-card-text v-if="MobileSize" class="pa-0">
                      <v-col class="py-0">
                        <v-row dense class="align-center">
                          <v-col class="py-0">
                            <v-row class="">
                              <v-col cols="12" class="py-0">
                                <v-row dense class="pt-2 pb-6 align-center">
                                  <v-img src="@/assets/couponShop.png" max-width="22" max-height="22">
                                  </v-img>
                                  <span class="pl-2" style="font-size: 14px; font-weight: 600;">
                                    {{ $t('ListCoupon.Header') }}
                                  </span>
                                </v-row>
                                <v-text-field v-model="search" clearable clear-icon="mdi-close-circle" @click:clear="clearMessage" hide-details height="40" :placeholder="$t('ListCoupon.PlaceHolder')" oninput="this.value = this.value.replace(/[^A-Za-z0-9\s]/g, '').replace(/(\..*)\./g, '$1')" @keypress="isLetterEng($event)" @keydown.enter="submit" outlined dense>
                                  <template slot="append">
                                    <v-img src="@/assets/Magnifer.png" max-width="24" max-height="24">
                                    </v-img>
                                  </template>
                                </v-text-field>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                        <v-row no-gutters v-if="searchData.length !== 0" class="rounded-lg mt-6" style="max-height: 285px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="searchData.length !== 0" class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <div v-for="(item, index) in searchData" :key="index">
                              <v-card-text v-if="index < showProductSearch" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%">
                                  <v-row dense class="px-2 py-2 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else-if="item.coupon_image === null && item.coupon_type === 'discount'" style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else-if="item.coupon_image === null && item.coupon_type === 'free_shipping'" style="border-radius: 8px;" src="@/assets/freeShippingCoupon.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/coupon_image/xgifty.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-col class="pl-1" style="line-height: 1;">
                                      <v-row dense class="pa-0 align-baseline">
                                        <v-col class="pa-0">
                                          <span class="d-inline-block text-truncate" style="color: #269AFD; font-size: 14px; font-weight: 700; min-width: 140px; max-width: 160px;">{{item.coupon_name}}</span>
                                        </v-col>
                                        <v-col align="end" class="pa-0" style="">
                                          <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                            <v-radio class="custom-radio-checkout" style="display: flex !important; justify-content: flex-end !important; padding-right: 0px !important;" color="#3EC6B6" :value="item.id"></v-radio>
                                          </v-radio-group>
                                        </v-col>
                                      </v-row>
                                      <!-- <span class="d-inline-block text-truncate" style="color: #333333; font-size: 12px; font-weight: 400; min-width: 140px; max-width: 160px;">สำหรับร้านค้า {{item.shop_name}} เท่านั้น</span> -->
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <!-- <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                    </v-col> -->
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllSearch && searchData.length > 3">
                              <v-btn text @click="ShowProductSearch()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                        </v-row>
                        <v-row no-gutters v-else-if="(Cards.length || (Shipping.length !== 0 && PickUp === false) || Free.length !== 0)"  class="rounded-lg mt-6" style="max-height: 285px; overflow-y: auto; overflow-x: hidden;">
                          <v-card v-if="Cards.length !== 0" class="rounded-lg mt-0 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 14px; font-weight: 600;">{{ $t('ListCoupon.GeneralDiscount') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Cards" :key="index">
                              <v-card-text v-if="index < showProductPriceDiscount" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%">
                                  <v-row dense class="px-2 py-2 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/discountCoupon.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-col class="pl-1" style="line-height: 1;">
                                      <v-row dense class="pa-0 align-baseline">
                                        <v-col class="pa-0">
                                          <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 14px; font-weight: 700; min-width: 140px; max-width: 160px;">{{item.coupon_name}}</span>
                                        </v-col>
                                        <v-col align="end" class="pa-0" style="">
                                          <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                            <v-radio class="custom-radio-checkout" style="display: flex !important; justify-content: flex-end !important; padding-right: 0px !important;" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                          </v-radio-group>
                                        </v-col>
                                      </v-row>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 12px; font-weight: 400; min-width: 140px; max-width: 160px;">{{ $t('ListCoupon.ForStore') }} {{item.shop_name}} {{ $t('ListCoupon.Only') }}</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <!-- <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                    </v-col> -->
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllCards && Cards.length > 3">
                              <v-btn text @click="ShowProductPriceDiscount()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                          <v-card v-if="Shipping.length !== 0" class="rounded-lg mt-4 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 14px; font-weight: 600;">{{ $t('ListCoupon.ShippingDiscount') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Shipping" :key="index">
                              <v-card-text v-if="index < showShippingDiscount" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%">
                                  <v-row dense class="px-2 py-2 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/freeShippingCoupon.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-col class="pl-1" style="line-height: 1;">
                                      <v-row dense class="pa-0 align-baseline">
                                        <v-col class="pa-0">
                                          <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 14px; font-weight: 700; min-width: 140px; max-width: 160px;">{{item.coupon_name}}</span>
                                        </v-col>
                                        <v-col align="end" class="pa-0" style="">
                                          <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                            <v-radio class="custom-radio-checkout" style="display: flex !important; justify-content: flex-end !important; padding-right: 0px !important;" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                          </v-radio-group>
                                        </v-col>
                                      </v-row>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 12px; font-weight: 400; min-width: 140px; max-width: 160px;">{{ $t('ListCoupon.ForStore') }} {{item.shop_name}} {{ $t('ListCoupon.Only') }}</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <!-- <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                    </v-col> -->
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllShipping && Shipping.length > 3">
                              <v-btn text @click="ShowShippingDiscount()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                          <v-card v-if="Free.length !== 0" class="rounded-lg mt-4 py-6 px-4 " width="100%" elevation="0" style="border: 1px solid #F5F5F5">
                            <v-row>
                              <v-col>
                                <!-- <v-img class="float-left mt-n1 mr-2" src="@/assets/ConponNGC/shopConpon/discount1.png" width="24" height="24"></v-img> -->
                                <span style="font-size: 14px; font-weight: 600;">{{ $t('ListCoupon.FreeGiftCoupon') }}</span>
                              </v-col>
                            </v-row>
                            <div v-for="(item, index) in Free" :key="index">
                              <v-card-text v-if="index < showFreeGiftCoupon" class="px-0 justify-center">
                                <v-card class="align-content-center" style="border-radius: 8px; border-width: 1px; background: #FAFAFA; border: 1px solid #F7F7F7; box-shadow: 0px 0.5px 2px 0px #60617029; box-shadow: 0px 0px 1px 0px #28293D14;" width="100%">
                                  <v-row dense class="px-2 py-2 align-center">
                                    <v-img v-if="item.coupon_image !== null" style="border-radius: 8px;" :src="item.coupon_image" max-width="50" max-height="50">
                                    </v-img>
                                    <v-img v-else style="border-radius: 8px;" src="@/assets/coupon_image/xgifty.png" max-width="50" max-height="50">
                                    </v-img>
                                    <v-col class="pl-1" style="line-height: 1;">
                                      <v-row dense class="pa-0 align-baseline">
                                        <v-col class="pa-0">
                                          <span class="d-inline-block text-truncate" style="color: #F15A24; font-size: 14px; font-weight: 700; min-width: 140px; max-width: 160px;">{{item.coupon_name}}</span>
                                        </v-col>
                                        <v-col align="end" class="pa-0" style="">
                                          <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                            <v-radio class="custom-radio-checkout" style="display: flex !important; justify-content: flex-end !important; padding-right: 0px !important;" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                          </v-radio-group>
                                        </v-col>
                                      </v-row>
                                      <span class="d-inline-block text-truncate" style="color: #333333; font-size: 12px; font-weight: 400; min-width: 140px; max-width: 160px;">{{ $t('ListCoupon.ForStore') }} {{item.shop_name}} {{ $t('ListCoupon.Only') }}</span>
                                      <v-row dense no-gutters class="align-center">
                                        <v-col class="pa-0" cols="6">
                                          <span style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Min') }} {{Number(item.spend_minimum).toLocaleString()}}.-</span>
                                        </v-col>
                                        <v-col class="pa-0" cols="6">
                                          <span v-if="item.discount_maximum !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{ $t('ListCoupon.Max') }} {{Number(item.discount_maximum).toLocaleString()}}.-</span>
                                        </v-col>
                                      </v-row>
                                      <span v-if="item.use_enddate !== null" style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} - {{formatDateToShow(item.use_enddate)}}</span>
                                      <span v-else style="color: #636363; font-size: 10px; font-weight: 400;">{{formatDateToShow(item.use_startdate)}} {{ $t('ListCoupon.Onwards') }}</span>
                                    </v-col>
                                    <!-- <v-col align="end" class="" style="">
                                        <v-radio-group class="pb-2" hide-details v-model="selectCoupon" >
                                          <v-radio class="custom-radio-checkout" color="#3EC6B6" :value="item.coupon_id"></v-radio>
                                        </v-radio-group>
                                    </v-col> -->
                                  </v-row>
                                </v-card>
                              </v-card-text>
                            </div>
                            <v-col style="text-align: center;" class="pb-0" v-if="!showAllFree && Free.length > 3">
                              <v-btn text @click="ShowFreeGiftCoupon()" style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">{{ $t('ListCoupon.ShowMoreCoupons') }}</v-btn>
                            </v-col>
                          </v-card>
                        </v-row>
                        <v-card v-else class="rounded-lg mt-6 py-6 px-4 "  elevation="0" style=" background-color: #FFFFFF;" >
                          <v-card-text>
                            <v-col cols="12" align="center">
                              <div class="mb-5">
                                <v-img src="@/assets/nocoupon.png" width="120" height="120" contain></v-img>
                              </div>
                              <div>
                                <span style="font-size: 16px; font-weight: 400; color: #9A9A9A;">{{ $t('ListCoupon.NoCoupon') }}</span>
                              </div>
                            </v-col>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
        <v-card-actions v-if="!MobileSize" class="px-12 justify-center" style="height: 100px; background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="Close()" >{{ $t('register.Cancel') }}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="!selectCoupon" class="px-10 white--text " style="border-radius: 40px; width: 150px;" color="#27AB9C" @click="confirm()" >{{ $t('register.Confirm') }}</v-btn>
        </v-card-actions>
        <v-card-actions v-if="MobileSize" class="px-4 justify-center" style="background-color: #FFFFFF;">
        <v-btn outlined class="px-10" style="border-radius: 40px; width: 130px;" color="#27AB9C" @click="Close()" >{{ $t('register.Cancel') }}</v-btn>
        <div class="mx-2"></div>
        <v-btn :disabled="!selectCoupon" class="px-10 white--text" style="border-radius: 40px; width: 130px;" color="#27AB9C" @click="confirm()" >{{ $t('register.Confirm') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog v-model="ModalDetailCoupon" width="760" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 py-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 760px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
                <v-col style="text-align: center;" :class="MobileSize ? 'pt-6 ml-8' : 'pt-6 ml-12'">
                  <span :style="MobileSize ? 'font-size: 16px; color: #FFFFFF' : 'font-size: 24px; color: #FFFFFF'"><b>เงื่อนไขโค้ดส่วนลด</b></span>
                </v-col>
                <v-btn  fab small @click="backstep()" icon :class="MobileSize ? 'mt-3 mr-n5' : 'mt-3'"><v-icon color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '760px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-container class="pa-0">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <div :class="MobileSize ? 'py-6 px-2' : 'py-10 px-10'">
                    <v-col class="pb-0">
                      <v-btn small text @click="backstep()"><v-icon color="#27AB9C">mdi-chevron-left</v-icon><span style="font-size: 16px; color: #27AB9C;" class="text-decoration-underline">ย้อนกลับ</span></v-btn>
                    </v-col>
                    <v-card-text v-if="!MobileSize" class="pt-0">
                      <div>
                        <v-card-text class="justify-center">
                          <v-img src="@/assets/ConponNGC/shopConpon/Coupons.png" style="filter: drop-shadow(rgb(51, 51, 51) 2px 2px 2px);" width="100%" height="169">
                            <v-row>
                              <v-col class="pr-0">
                                <v-col class="ml-12 pb-0 pr-0" style="min-width: 200px; line-height: 32px;">
                                  <span class="d-inline-block text-truncate" style="color: #27AB9C; font-size: 18px; font-weight: 700; max-width: 280px;">{{CouponName}}</span>
                                </v-col>
                                <v-col class="ml-12 pr-0 pt-0" style="min-width: 200px;">
                                  <v-row>
                                    <v-col v-if="CouponImage !== null" style="max-width: 100px;" class="pr-0">
                                      <v-img :src="CouponImage" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="100" height="50"></v-img>
                                    </v-col>
                                    <v-col v-else style="max-width: 100px;" class="pr-0">
                                      <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" max-width="100" height="50"></v-img>
                                    </v-col>
                                    <v-col class="pb-0">
                                      <span v-if="CouponType === 'discount'" style="font-size: 14px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: red;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                        <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                      </span>
                                      <span v-else-if="CouponType === 'free_shipping'" style="font-size: 14px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: #FF9800;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                        <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                      </span>
                                      <span v-else style="font-size: 14px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br></span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <!-- <v-col v-if="UseEnddate !== null" class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <v-spacer style="margin-top: 24px;"></v-spacer>
                                </v-col> -->
                                <v-col v-if="UseEnddate !== null" class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-12 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 12px;">ไม่มีวันหมดอายุ</span>
                                </v-col>
                                <v-row class="ml-14 py-0 py-1" style="min-width: 300px;">
                                  <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 130px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)">
                                    <template #progress="{ value }">
                                      <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                    </template>
                                  </v-progress-linear>
                                  <span class="ml-2" style="font-size: 12px; color: #27AB9C;">ใช้แล้ว {{parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)}}%</span>
                                  <!-- <v-btn text x-small class="px-0 ml-8" @click="OpenDetail()"><span class="text-decoration-underline" style="font-size: 14px; color: blue;">ดูเงื่อนไข</span></v-btn> -->
                                </v-row>
                              </v-col>
                              <v-col class="pl-0 pt-6" style="min-width: 200px;">
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: red;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: #FF9800;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 16px; color: #FFC107;" >คูปอง</span>
                                </v-col>
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: red;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: #FF9800;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 200px;">
                                  <span style="font-size: 48px; font-weight: 700; color: #FFC107;">แถมฟรี</span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-img>
                        </v-card-text>
                      </div>
                      <!-- wtf is this -->
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          ชื่อคูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ CouponName }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          ประเภทโปรโมชัน
                        </span>
                      </v-col>
                      <v-col v-if="CouponType === 'discount'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          ส่วนลดราคาสินค้า
                        </span>
                      </v-col>
                      <v-col v-else-if="CouponType === 'free_shipping'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          ส่วนลดค่าจัดส่งสินค้า
                        </span>
                      </v-col>
                      <v-col v-else class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ $t('ListCoupon.FreeGiftCoupon') }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 18px; font-weight: 600;">
                          สิทธิ์การใช้คูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          สามารถใช้ได้ {{Number(Quota).toLocaleString(undefined)}} สิทธิ์
                        </span>
                      </v-col>
                      <v-divider v-if="CouponType !== 'free_product'" class="mt-1"></v-divider>
                      <v-col v-if="CouponType !== 'free_product'">
                        <span style="font-size: 18px; font-weight: 600;">
                          ส่วนลดสูงสุด
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'percent' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{DiscountMaximum !== null ? `ลดสูงสุด ${Number(DiscountMaximum).toLocaleString(undefined)} บาท` : 'ไม่จำกัด'}}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'baht' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 16px;">
                          {{ `ลดสูงสุด ${Number(DiscountAmount).toLocaleString(undefined)} บาท` }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="CouponDescription !== null"></v-divider>
                      <v-col v-if="CouponDescription !== null">
                        <span style="font-size: 18px; font-weight: 600;">
                          รายละเอียดโค้ดส่วนลด
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="CouponDescription !== null">
                        <span style="font-size: 14px;" v-html="CouponDescription">
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="Object.keys(CouponProductFree).length !== 0"></v-divider>
                      <v-col v-if="Object.keys(CouponProductFree).length !== 0">
                        <span style="font-size: 18px; font-weight: 600;">
                          รายละเอียดของแถม
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="Object.keys(CouponProductFree).length !== 0">
                        <v-row class="pa-0" v-for="(buyItem, index) in CouponProductFree.product_details_buy" :key="'pair-' + index">
                          <template v-if="CouponProductFree.product_details_free[index]">
                            <!-- รายการสั่งซื้อ -->
                            <v-col cols="12" class="px-0">
                              <span style="font-weight: 600; font-size: 16px;" class="pl-2">รายการสั่งซื้อ</span>
                              <v-row no-gutters class="pa-0">
                                <v-col cols="4" class="pa-0pl-2" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="buyItem.image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 16px;"><b>ชื่อสินค้า: </b>{{ buyItem.name }}</span><br>
                                  <span style="font-size: 16px;"><b>ตัวเลือก: </b>{{ buyItem.attribute_priority_1 }}{{ buyItem.attribute_priority_2 === '-' ? '' : `, ${buyItem.attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 16px;"><b>จำนวน: </b>{{ buyItem.quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- ของแถม -->
                            <v-col cols="12" class="px-0">
                              <v-col class="text-center rounded-b-0" style="background-color: #E9F6F5; border-top-right-radius: 8px; border-top-left-radius: 8px;">
                                <span style="font-weight: 600; font-size: 16px; color: #27AB9C;" class="">ของแถม</span>
                              </v-col>
                              <v-row no-gutters class="pa-0 py-2" style="background-color: #F5F5F5; border: 2px solid #E9F6F5; border-top-width: 0px;">
                                <v-col cols="4" class="pa-0" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="CouponProductFree.product_details_free[index].image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 16px;"><b>ชื่อสินค้า: </b>{{ CouponProductFree.product_details_free[index].name }}</span><br>
                                  <span style="font-size: 16px;"><b>ตัวเลือก: </b>{{ CouponProductFree.product_details_free[index].attribute_priority_1 }}{{ CouponProductFree.product_details_free[index].attribute_priority_2 === '-' ? '' : `, ${CouponProductFree.product_details_free[index].attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 16px;"><b>จำนวน: </b>{{ CouponProductFree.product_details_free[index].quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- Divider -->
                            <v-col cols="12" v-if="index < CouponProductFree.product_details_buy.length - 1" class="py-4">
                              <v-divider></v-divider>
                            </v-col>
                          </template>
                        </v-row>
                      </v-col>
                    </v-card-text>
                    <v-card-text v-if="MobileSize" class="pt-0 px-0">
                      <div>
                        <v-card-text class="justify-center">
                          <v-img src="@/assets/ConponNGC/shopConpon/Coupons.png" style="filter: drop-shadow(rgb(51, 51, 51) 2px 2px 2px);" width="100%" height="150">
                            <v-row>
                              <v-col class="pr-0" style="max-width: 200px;">
                                <v-col class="ml-1 pb-0 pr-0" style="min-width: 150px;">
                                  <span class="d-inline-block text-truncate" style="color: #27AB9C; font-size: 14px; font-weight: 700; max-width: 280px;">{{CouponName}}</span>
                                </v-col>
                                <v-col class="ml-0 py-0 pr-0" style="min-width: 60px;">
                                  <v-row>
                                    <v-col v-if="CouponImage !== null" style="max-width: 60px;" class="pr-0">
                                      <v-img :src="CouponImage" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" width="50" height="50"></v-img>
                                    </v-col>
                                    <v-col v-else style="max-width: 60px;" class="pr-0">
                                      <v-img src="@/assets/ConponNGC/couponSty01.png" style="box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);" class="rounded-lg" width="50" height="50"></v-img>
                                    </v-col>
                                    <v-col class="pb-0" style="max-width: 150px;">
                                      <span v-if="CouponType === 'discount'" style="font-size: 9px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: red;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                      <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                    </span>
                                    <span v-else-if="CouponType === 'free_shipping'" style="font-size: 9px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br><span style="color: #FF9800;">ซื้อสินค้าขั้นต่ำ {{Number(SpendMinimum).toLocaleString(undefined)}} บาท</span><br>
                                      <span v-if="DiscountMaximum !== null">ลดสูงสุด {{Number(DiscountMaximum).toLocaleString(undefined)}} บาท</span>
                                    </span>
                                      <span v-else style="font-size: 9px;">ใช้ร่วมกับโค้ดส่วนลดอื่นๆไม่ได้<br></span>
                                    </v-col>
                                  </v-row>
                                </v-col>
                                <!-- <v-col v-if="UseEnddate !== null" class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <v-spacer style="margin-top: 24px;"></v-spacer>
                                </v-col> -->
                                <v-col v-if="UseEnddate !== null" class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">ใช้ได้ถึง {{formatDateToShow(UseEnddate)}}</span>
                                </v-col>
                                <v-col v-else class="ml-1 pr-0 py-1" style="min-width: 200px;">
                                  <span style="font-size: 8px;">ไม่มีวันหมดอายุ</span>
                                </v-col>
                                <v-row class="ml-2 py-0 py-1" style="min-width: 15p0x;">
                                  <v-progress-linear color="transparent" background-color="#CCCCCC" class="mt-2" style="max-width: 65px; height: 5px; border-radius: 48px;" :value="parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)">
                                    <template #progress="{ value }">
                                      <div class="progress-gradient" :style="{background: `linear-gradient(270deg, #FB8700 ${value}%, #C04F36 ${value}%)`}"></div>
                                    </template>
                                  </v-progress-linear>
                                  <span class="ml-2" style="font-size: 12px; color: #27AB9C;">ใช้แล้ว {{parseInt(parseFloat(parseInt(UseCount) / parseInt(Quota)) * 100)}}%</span>
                                  <!-- <v-btn text x-small class="px-0 ml-8" @click="OpenDetail()"><span class="text-decoration-underline" style="font-size: 14px; color: blue;">ดูเงื่อนไข</span></v-btn> -->
                                </v-row>
                              </v-col>
                              <v-col class="pl-0 pt-6 ml-n3" style="min-width: 100px;">
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: red;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: #FF9800;" >ส่วนลด</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 14px; color: #FFC107;" >คูปอง</span>
                                </v-col>
                                <v-col v-if="CouponType === 'discount'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 20px; font-weight: 700; color: red;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else-if="CouponType === 'free_shipping'" class="text-center" style="min-width: 60px;">
                                  <span style="font-size: 20px; font-weight: 700; color: #FF9800;">{{DiscountType === 'baht' ? `฿${DiscountAmount}` : `${DiscountAmount}%`}}</span>
                                </v-col>
                                <v-col v-else class="text-center" style="min-width: 100px;">
                                  <span style="font-size: 20px; font-weight: 700; color: #FFC107;">แถมฟรี</span>
                                </v-col>
                              </v-col>
                            </v-row>
                          </v-img>
                        </v-card-text>
                      </div>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          ชื่อคูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ CouponName }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          ประเภทโปรโมชัน
                        </span>
                      </v-col>
                      <v-col v-if="CouponType === 'discount'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          ส่วนลดราคาสินค้า
                        </span>
                      </v-col>
                      <v-col v-else-if="CouponType === 'free_shipping'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          ส่วนลดค่าจัดส่งสินค้า
                        </span>
                      </v-col>
                      <v-col v-else class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ $t('ListCoupon.FreeGiftCoupon') }}
                        </span>
                      </v-col>
                      <v-divider class="mt-1"></v-divider>
                      <v-col>
                        <span style="font-size: 16px; font-weight: 600;">
                          สิทธิ์การใช้คูปอง
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          สามารถใช้ได้ {{Number(Quota).toLocaleString(undefined)}} สิทธิ์
                        </span>
                      </v-col>
                      <v-divider v-if="CouponType !== 'free_product'" class="mt-1"></v-divider>
                      <v-col v-if="CouponType !== 'free_product'">
                        <span style="font-size: 16px; font-weight: 600;">
                          ส่วนลดสูงสุด
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'percent' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{DiscountMaximum !== null ? `ลดสูงสุด ${Number(DiscountMaximum).toLocaleString(undefined)} บาท` : 'ไม่จำกัด'}}
                        </span>
                      </v-col>
                      <v-col v-if="DiscountType === 'baht' && CouponType !== 'free_product'" class="ml-4 pt-0">
                        <span style="font-size: 14px;">
                          {{ `ลดสูงสุด ${Number(DiscountAmount).toLocaleString(undefined)} บาท` }}
                        </span>
                      </v-col>
                      <v-divider v-if="CouponDescription !== null" class="mt-1"></v-divider>
                      <v-col v-if="CouponDescription !== null">
                        <span style="font-size: 16px; font-weight: 600;">
                          รายละเอียดโค้ดส่วนลด
                        </span>
                      </v-col>
                      <v-col class="ml-4 pt-0" v-if="CouponDescription !== null">
                        <span style="font-size: 12px;" v-html="CouponDescription">
                        </span>
                      </v-col>
                      <v-divider class="mt-1" v-if="Object.keys(CouponProductFree).length !== 0"></v-divider>
                      <v-col v-if="Object.keys(CouponProductFree).length !== 0">
                        <span style="font-size: 16px; font-weight: 600;">
                          รายละเอียดของแถม
                        </span>
                      </v-col>
                      <v-col class="pt-0" v-if="Object.keys(CouponProductFree).length !== 0">
                        <v-row class="pa-0" v-for="(buyItem, index) in CouponProductFree.product_details_buy" :key="'pair-' + index">
                          <template v-if="CouponProductFree.product_details_free[index]">
                            <!-- รายการสั่งซื้อ -->
                            <v-col cols="12" class="px-0">
                              <span style="font-weight: 600; font-size: 14px;" class="pl-2">รายการสั่งซื้อ</span>
                              <v-row no-gutters class="pa-0">
                                <v-col cols="4" class="pa-0"  style="justify-items: center;">
                                  <v-img height="100" width="100" :src="buyItem.image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 14px;"><b>ชื่อสินค้า: </b>{{ buyItem.name }}</span><br>
                                  <span style="font-size: 14px;"><b>ตัวเลือก: </b>{{ buyItem.attribute_priority_1 }}{{ buyItem.attribute_priority_2 === '-' ? '' : `, ${buyItem.attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 14px;"><b>จำนวน: </b>{{ buyItem.quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- ของแถม -->
                            <v-col cols="12" class="px-0">
                              <v-col class="text-center rounded-b-0" style="background-color: #E9F6F5; border-top-right-radius: 8px; border-top-left-radius: 8px;">
                                <span style="font-weight: 600; font-size: 14px; color: #27AB9C;" class="">ของแถม</span>
                              </v-col>
                              <v-row no-gutters class="pa-0 py-2" style="background-color: #F5F5F5; border: 2px solid #E9F6F5; border-top-width: 0px;">
                                <v-col cols="4" class="pa-0" style="justify-items: center;">
                                  <v-img height="100" width="100" :src="CouponProductFree.product_details_free[index].image"></v-img>
                                </v-col>
                                <v-col cols="8" class="pa-0">
                                  <span style="font-size: 14px;"><b>ชื่อสินค้า: </b>{{ CouponProductFree.product_details_free[index].name }}</span><br>
                                  <span style="font-size: 14px;"><b>ตัวเลือก: </b>{{ CouponProductFree.product_details_free[index].attribute_priority_1 }}{{ CouponProductFree.product_details_free[index].attribute_priority_2 === '-' ? '' : `, ${CouponProductFree.product_details_free[index].attribute_priority_2}` }}</span><br>
                                  <span style="font-size: 14px;"><b>จำนวน: </b>{{ CouponProductFree.product_details_free[index].quantity }}</span>
                                </v-col>
                              </v-row>
                            </v-col>
                            <!-- Divider -->
                            <v-col cols="12" v-if="index < CouponProductFree.product_details_buy.length - 1" class="py-4">
                              <v-divider></v-divider>
                            </v-col>
                          </template>
                        </v-row>
                      </v-col>
                    </v-card-text>
                  </div>
                </v-card>
              </v-container>
            </div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  data () {
    return {
      textSearch: '',
      searchData: [],
      DataShop: [],
      CouponProductFree: {},
      PickUp: false,
      pricePoint: 0,
      MaxPoint: 0,
      checkradio: false,
      PointData: [],
      selectPoint: '',
      inputValue: '',
      show: true,
      UserPoint: 0,
      ShopPointDetail: [],
      ShopID: '',
      dataCouponList: [],
      search: '',
      checkSelect: false,
      CouponID: '',
      CollectID: '',
      CouponImage: '',
      CouponName: '',
      CouponCode: '',
      CouponDescription: '',
      CollectStartdate: '',
      CollectEnddate: '',
      UseStartdate: '',
      UseEnddate: '',
      CouponType: '',
      Quota: '',
      UseCount: '',
      UserCap: '',
      SpendMinimum: '',
      DiscountAmount: '',
      ProductList: '',
      DiscountType: '',
      SellerShopID: '',
      Status: '',
      Cards: [],
      Shipping: [],
      Free: [],
      ModalDetailCoupon: false,
      selectCoupon: [],
      showAllCards: false,
      showAllSearch: false,
      showAllShipping: false,
      showAllFree: false,
      ModalCoupon: false,
      showProductPriceDiscount: '3',
      showProductSearch: '3',
      showShippingDiscount: '3',
      showFreeGiftCoupon: '3',
      page: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  mounted () {
    this.$EventBus.$on('getListCoupon', this.getListCoupon)
    this.$EventBus.$on('clearCoupon', (sellerShopIds) => this.clearCoupon(sellerShopIds))
    this.$EventBus.$on('clearPoint', this.clearPoint)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getListCoupon')
      this.$EventBus.$off('clearCoupon')
      this.$EventBus.$off('clearPoint')
    })
  },
  created () {
    // this.getListCoupon()
    // console.log('%c Hello Bug ', 'background: red; color: #000; padding: 4px; border-radius: 2px; margin-left: 1ch;', this.Test)
  },
  beforeDestroy () {
    // this.$EventBus.$off('createAdminPanitSuccess')
    // this.$EventBus.$off('deleteAdminPanitSuccess')
    // this.$EventBus.$off('editAdminPanitSuccess')
  },
  watch: {
    // selectPoint (item) {
    //   console.log('object', item)
    // }
  },
  methods: {
    clearMessage () {
      this.search = ''
      if (this.searchData.length !== 0) {
        this.selectCoupon = ''
        this.searchData = []
        this.getListCoupon()
      }
    },
    async submit (event) {
      if (this.search === null) {
        this.search = ''
      }
      if (this.search !== '') {
        event.preventDefault()
        this.$store.commit('openLoader')
        var SearchArray = []
        SearchArray = this.search
        this.searchData = []
        this.selectCoupon = ''
        this.results = SearchArray
        var data = {
          code: this.results,
          type: '',
          role_user: 'ext_buyer',
          seller_shop_id: this.ShopID
        }
        await this.$store.dispatch('actionsSearchCouponPlatform', data)
        var res = await this.$store.state.ModuleCart.stateSearchCouponPlatform
        if (res.message === 'Success.') {
          this.$store.commit('closeLoader')
          this.textSearch = this.results
          this.searchData = res.data
          // await this.getCouponsCode(this.searchData)
          this.getListCoupon()
        } else if (res.code === 400 && (res.message === 'Code Not Found.' || res.message === 'ไม่พบคูปอง หรือไม่อยู่ในช่วงเวลาใช้คูปอง')) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 4500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('ListCoupon.CouponNotFound')}</h3>`
          })
          this.searchData = []
          this.getListCoupon()
          // this.ModalCoupon = false
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else if (res.message === 'ไม่พบคูปอง') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('ListCoupon.errorMessage1')
            })
          } else if (res.message === 'คูปองใช้เกินกำหนด') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('ListCoupon.errorMessage2')
            })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: res.message
            })
          }
          // this.ModalCoupon = false
        }
      } else {
        this.clearMessage()
      }
    },
    async submit2 () {
      if (this.search === null) {
        this.search = ''
      }
      if (this.search !== '') {
        this.$store.commit('openLoader')
        var SearchArray = []
        this.searchData = []
        this.selectCoupon = ''
        SearchArray = this.search
        this.results = SearchArray
        var data = {
          code: this.results,
          type: '',
          role_user: 'ext_buyer',
          seller_shop_id: this.ShopID
        }
        await this.$store.dispatch('actionsSearchCouponPlatform', data)
        var res = await this.$store.state.ModuleCart.stateSearchCouponPlatform
        if (res.message === 'Success.') {
          this.$store.commit('closeLoader')
          this.textSearch = this.results
          this.searchData = res.data
          // await this.getCouponsCode(this.searchData)
          this.getListCoupon()
        } else if (res.code === 400 && (res.message === 'Code Not Found.' || res.message === 'ไม่พบคูปอง หรือไม่อยู่ในช่วงเวลาใช้คูปอง')) {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 4500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('ListCoupon.CouponNotFound')}</h3>`
          })
          this.searchData = []
          this.getListCoupon()
          // this.ModalCoupon = false
        } else {
          if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else if (res.message === 'ไม่พบคูปอง') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('ListCoupon.errorMessage1')
            })
          } else if (res.message === 'คูปองใช้เกินกำหนด') {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: this.$t('ListCoupon.errorMessage2')
            })
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({
              showConfirmButton: false,
              timer: 2500,
              timerProgressBar: true,
              icon: 'error',
              text: res.message
            })
          }
          // this.ModalCoupon = false
        }
      } else {
        this.clearMessage()
      }
    },
    checkInput () {
      if (parseInt(this.inputValue) > parseInt(this.MaxPoint)) {
        this.inputValue = parseInt(this.MaxPoint)
      } else if (parseInt(this.inputValue) < 0) {
        this.inputValue = 0
      }
    },
    // async getListUserPointBySellerShopID () {
    //   var UserDetail = JSON.parse(Decode.decode(localStorage.getItem('UserDetail')))
    //   var data = {
    //     seller_shop_id: this.ShopID
    //   }
    //   await this.$store.dispatch('actionsgetListUserPointBySellerShopID', data)
    //   var res = await this.$store.state.ModuleManagePoint.stategetListUserPointBySellerShopID
    //   if (res.result === 'SUCCESS') {
    //     if (res.data.length !== 0) {
    //       res.data.forEach(element => {
    //         if (element.user_id === UserDetail.data[0].id) {
    //           this.UserPoint.all_point = element.all_point
    //         }
    //       })
    //     } else {
    //       this.UserPoint = 0
    //     }
    //   }
    //   // console.log(this.UserPoint)
    // },
    // async getSellerShopPointDetail () {
    //   var data = {
    //     seller_shop_id: this.ShopID
    //   }
    //   await this.$store.dispatch('actionsgetSellerShopPointDetail', data)
    //   var res = await this.$store.state.ModuleManagePoint.stategetSellerShopPointDetail
    //   // console.log('object', res)
    //   if (res.result === 'SUCCESS') {
    //     this.ShopPointDetail = res.data
    //   }
    // },
    async getListUserPointByUser () {
      this.PointData = []
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      var RoleUser = JSON.parse(localStorage.getItem('roleUser'))
      var PartnerID = JSON.parse(localStorage.getItem('partner_id'))
      var data = {
        role_user: RoleUser.role,
        customer_id: RoleUser.role === 'sale_order_no_JV' ? PartnerID : -1,
        seller_shop_id: this.ShopID,
        company_id: onedata.cartData.company_id,
        com_perm_id: onedata.cartData.com_perm_id
      }
      await this.$store.dispatch('actionsgetDetailUserPointByUser', data)
      var res = await this.$store.state.ModuleManagePoint.stategetDetailUserPointByUser
      if (res.result === 'SUCCESS') {
        this.PointData = res.data[0]
        // console.log('this.PointData', this.PointData)
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('ListCoupon.PointError')}</h3>`
        })
      }
    },
    formatDateToShow (data) {
      if (!data) return null
      const datePart = data.split(/T| /)[0]
      const [year, month, day] = datePart.split('-')
      if (this.$i18n.locale === 'th') {
        return `${day}/${month}/${parseInt(year) + 543}`
      } else {
        return `${day}/${month}/${year}`
      }
    },
    async getListCoupon () {
      this.Cards = []
      this.Shipping = []
      this.Free = []
      // console.log('DataShop', this.DataShop)
      var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      // console.log('couponData', couponData)
      const normalLength = []
      const onetimeLength = []
      const recurringLength = []
      const ProductListLength = []
      if (this.DataShop.product_list === undefined) {
        if (this.DataShop.product_general !== undefined) {
          this.DataShop.product_general.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              normalLength.push(e)
            }
          })
        }
        if (this.DataShop.product_recurring !== undefined) {
          this.DataShop.product_recurring.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              recurringLength.push(e)
            }
          })
        }
        if (this.DataShop.product_onetime !== undefined) {
          this.DataShop.product_onetime.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              onetimeLength.push(e)
            }
          })
        }
      } else {
        if (this.DataShop.product_list !== undefined) {
          this.DataShop.product_list.forEach(e => {
            if (e.change !== 'yes' && e.stock_status !== 'out of stock') {
              ProductListLength.push(e)
            }
          })
        }
      }
      const matchedProducts = couponData.product.filter((calItem) => {
        const isMatched = normalLength.some(normalItem => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === normalItem.product_id
          } else {
            const productAttributeId = parseInt(normalItem.product_attribute_detail.product_attribute_id.split(' ')[0], 10)
            return calItem.product_attribute_id === productAttributeId
          }
        }) || recurringLength.some(recurringItem => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === recurringItem.product_id
          } else {
            const productAttributeId = parseInt(recurringItem.product_attribute_detail.product_attribute_id.split(' ')[0], 10)
            return calItem.product_attribute_id === productAttributeId
          }
        }) || onetimeLength.some(onetimeItem => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === onetimeItem.product_id
          } else {
            const productAttributeId = parseInt(onetimeItem.product_attribute_detail.product_attribute_id.split(' ')[0], 10)
            return calItem.product_attribute_id === productAttributeId
          }
        }) || ProductListLength.some(ProductList => {
          if (calItem.product_attribute_id === '-1') {
            return calItem.product_id === ProductList.product_id
          } else {
            const productAttributeId = parseInt(ProductList.product_attribute_detail.product_attribute_id)
            return calItem.product_attribute_id === productAttributeId
          }
        })
        return isMatched
      })
      couponData.net_price = this.DataShop.total_price_no_vat
      couponData.price_inc_vat = this.DataShop.total_price_vat
      couponData.total_price_general = this.DataShop.total_price_no_vat_general
      couponData.product = matchedProducts
      couponData.shop_id = this.DataShop.seller_shop_id
      this.ShopID = this.DataShop.seller_shop_id
      await this.getListUserPointByUser()
      // this.getListUserPointBySellerShopID()
      // this.getSellerShopPointDetail()
      await this.$store.dispatch('actionsListCoupon', couponData)
      var res = await this.$store.state.ModuleCart.stateListCoupon
      if (res.message === 'เรียกดูข้อมูลสำเร็จ') {
        this.dataCouponList = res.data.coupon
        var CouponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
        var pricePoint = parseFloat(this.PointData.x_baht) / parseFloat(this.PointData.x_point)
        // console.log('CouponData', CouponData)
        this.pricePoint = pricePoint
        this.MaxPoint = parseInt(((0.25 * CouponData.net_price) / parseFloat(pricePoint)) - 1)
        res.data.coupon.forEach(element => {
          const existingCardIndex = this.Cards.findIndex(card => card.coupon_id === element.coupon_id)
          const existingShippingIndex = this.Shipping.findIndex(shipping => shipping.coupon_id === element.coupon_id)
          const existingFreeIndex = this.Free.findIndex(free => free.coupon_id === element.coupon_id)
          if (existingCardIndex === -1 && existingShippingIndex === -1 && existingFreeIndex === -1) {
            if (element.coupon_type === 'discount') {
              this.Cards.push({ ...element })
            } else if (element.coupon_type === 'free_shipping') {
              this.Shipping.push({ ...element })
            } else {
              this.Free.push({ ...element })
            }
          }
        })
      } else if (res.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h3>${this.$t('Login.LoginFail3')}</h3>`
          })
        }
      }
    },
    backstep () {
      this.ModalDetailCoupon = false
    },
    OpenDetail (item) {
      this.ModalDetailCoupon = true
      this.CouponID = item.coupon_id
      this.CollectID = item.collect_id
      this.CouponImage = item.coupon_image
      this.CouponName = item.coupon_name
      this.CouponCode = item.coupon_code
      this.CouponDescription = item.coupon_description
      this.CollectStartdate = item.collect_startdate
      this.CollectEnddate = item.collect_enddate
      this.UseStartdate = item.use_startdate
      this.UseEnddate = item.use_enddate
      this.CouponType = item.coupon_type
      this.Quota = item.quota
      this.UseCount = item.use_count
      this.UserCap = item.user_cap
      this.SpendMinimum = item.spend_minimum
      this.DiscountAmount = item.discount_amount
      this.DiscountMaximum = item.discount_maximum
      this.CouponProductFree = item.coupon_product_free || []
      this.ProductList = item.product_list
      this.DiscountType = item.discount_type
      this.SellerShopID = item.seller_shop_id
      this.Status = item.status
    },
    Close () {
      if (this.selectCoupon !== '' && this.checkSelect === false) {
        this.selectCoupon = ''
      }
      if (this.selectPoint !== '' && this.checkSelect === false) {
        this.selectPoint = ''
        this.inputValue = ''
      }
      this.search = ''
      this.searchData = []
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showProductSearch = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllSearch = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
    },
    clearCoupon (item) {
      // console.log('Seller Shop IDs:', item)
      this.selectCoupon = ''
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showProductSearch = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllSearch = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
      this.checkSelect = false
      var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
      onedata.cartData.coupon = onedata.cartData.coupon.filter(
        coupon => coupon.seller_shop_id !== item.seller_shop_id
      )
      // console.log('onedata', onedata)
      localStorage.setItem('oneData', Encode.encode(onedata))
    },
    clearPoint () {
      this.selectPoint = false
      this.inputValue = 0
      this.ModalDetailCoupon = false
      this.showProductPriceDiscount = 3
      this.showProductSearch = 3
      this.showShippingDiscount = 3
      this.showFreeGiftCoupon = 3
      this.showAllCards = false
      this.showAllSearch = false
      this.showAllShipping = false
      this.showAllFree = false
      this.ModalCoupon = false
      this.checkSelect = false
    },
    ShowProductPriceDiscount () {
      this.showAllCards = true
      this.showProductPriceDiscount = this.Cards.length
    },
    ShowProductSearch () {
      this.showAllSearch = true
      this.showProductSearch = this.searchData.length
    },
    ShowShippingDiscount () {
      this.showAllShipping = true
      this.showShippingDiscount = this.Shipping.length
    },
    ShowFreeGiftCoupon () {
      this.showAllFree = true
      this.showFreeGiftCoupon = this.Free.length
    },
    async open (page, item, point, baht, pickup, data) {
      this.DataShop = data
      if (item !== '') {
        this.selectCoupon = item
      }
      if (pickup === 'radio-1') {
        this.PickUp = true
      } else {
        this.PickUp = false
      }
      // if (point !== null && point !== '') {
      //   this.inputValue = await point / parseFloat(baht)
      //   // console.log('this.inputValue', this.inputValue)
      //   // console.log('point', point, parseInt(baht))
      // } else {
      //   point = 0
      //   this.inputValue = point
      // }
      // if (this.inputValue === 0) {
      //   // console.log(1)
      //   this.selectPoint = false
      //   this.checkradio = false
      // } else {
      //   // console.log(2)
      //   this.selectPoint = true
      //   this.checkradio = true
      // }
      this.page = page
      this.ModalCoupon = true
      this.$EventBus.$emit('getListCoupon')
    },
    confirm () {
      // var couponData = JSON.parse(Decode.decode(localStorage.getItem('couponData')))
      if (this.selectCoupon.length === 0 && this.selectPoint === false) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('ListCoupon.PleaseSelect')}</h3>`
        })
      } else if (parseInt(this.inputValue) > this.MaxPoint) {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          html: `<h3>${this.$t('ListCoupon.NotExceed1')}<br>${this.$t('ListCoupon.NotExceed2')}</h3>`
        })
      } else {
        // this.selectPoint = this.inputValue
        // console.log('this.pricePoint', this.pricePoint)
        // if (this.selectPoint !== false) {
        //   var Point = this.inputValue * this.pricePoint
        // }
        var Coupon = [].concat(this.Cards, this.Shipping, this.Free).filter(element => {
          return element.coupon_id === this.selectCoupon
        })
        if (Coupon.length !== 0) {
          if (this.page === 'checkoutSaleOrder' || this.page === 'checkout') {
            this.$EventBus.$emit('SelectCouponCheckout', Coupon, [])
          } else if (this.page === 'QTCheckout') {
            this.$EventBus.$emit('SelectCouponQT', Coupon, [])
          } else {
            this.$EventBus.$emit('SelectCoupon', Coupon, [])
          }
          var onedata = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
          onedata.cartData.coupon = onedata.cartData.coupon || []
          Coupon.forEach(newCoupon => {
            const isDuplicate = onedata.cartData.coupon.some(existingCoupon => existingCoupon.coupon_id === newCoupon.coupon_id)
            if (!isDuplicate) {
              onedata.cartData.coupon.push(newCoupon)
            }
          })
          // console.log(onedata)
          localStorage.setItem('oneData', Encode.encode(onedata))
          this.selectCoupon = ''
          this.search = ''
          this.searchData = []
          this.checkSelect = true
          this.ModalCoupon = false
          this.ModalDetailCoupon = false
          this.showProductPriceDiscount = 3
          this.showProductSearch = 3
          this.showShippingDiscount = 3
          this.showFreeGiftCoupon = 3
          this.showAllCards = false
          this.showAllSearch = false
          this.showAllShipping = false
          this.showAllFree = false
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 2500,
            timerProgressBar: true,
            icon: 'warning',
            html: `<h4>${this.$t('ListCoupon.CannotUse')}</h4>`
          })
        }
      }
    }
  }
}
</script>

<style>
.progress-gradient {
  width: 100%;
  height: 100%;
  border-radius: 48px;
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-progress-linear {
  background: linear-gradient(270deg, #FB8700 0%, #C04F36 100%);
}
.v-input--selection-controls {
  margin-top: 0px;
}
</style>
<style scoped>
.dynamic-font-card {
  font-size: calc(1vw + 1vh + .5vmin); /* Adjust this value as needed */
  color: red;
}
.dynamic-font {
  display: block; /* Ensures the span takes up the full width */
}
</style>
<style scoped>
::v-deep .v-radio {
  display: flex !important;
  justify-content: flex-end !important;
  padding-right: 12px !important;
}
::v-deep .custom-radio-checkout .v-input--selection-controls__input .v-icon {
  color: #3EC6B6 !important;
}
</style>
