<template>
  <div>
    <v-breadcrumbs :items="items" class="breadcrumbsPadding" v-bind:style="{ 'background-color': '#FFFFFF' }">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          {{ item.text }}
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-container grid-list-xs>
      <v-overlay :value="overlay2">
        <v-progress-circular color="#27AB9C" indeterminate size="64"></v-progress-circular>
      </v-overlay>
      <div v-if="listOfProduct.length !== 0" class="mt-4">
        <v-row dense align-content="center" justify="center">
          <v-col cols="12" md="12" xs="12">
            <v-row class="mt-2">
              <v-spacer style="border-top: 1px solid #bbb; margin-top: 24px;"></v-spacer>
              <v-chip
              class="ma-2"
              color="#BDE7D9"
              label
              >
                <h2 style="color: #27AB9C; font-weight: bold; font-size: 1.25rem;" class="pt-3">สั่งซื้ออีกครั้ง</h2>
              </v-chip>
              <v-spacer style="border-top: 1px solid #bbb; margin-top: 24px;"></v-spacer>
            </v-row>
          </v-col>
        </v-row>
        <v-row justify="start" class="pt-12"  v-if="listOfProduct !== 0 && !MobileSize && !IpadSize">
          <v-col cols="12" md="4" sm="4" xs="4" v-for="(item, index) in paginated" :key="index">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <!-- <v-row justify="start" class="pt-12"  v-if="listOfProduct !== 'ยังไม่มีรายการสินค้า' && !MobileSize && IpadSize">
          <v-col cols="12" md="2" sm="3" xs="6" v-for="(item, index) in paginated" :key="index">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="pt-4"  v-if="listOfProduct !== 'ยังไม่มีรายการสินค้า' && MobileSize && !IpadSize">
          <v-col cols="6" md="6" sm="6" xs="6" v-for="(item, index) in paginated" :key="index">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row> -->
        <v-row justify="center" class="my-6">
          <v-pagination
          color="#27AB9C"
          v-model="pageNumber"
          :length="pageMax"
          :total-visible="7"
          ></v-pagination>
        </v-row>
      </div>
    </v-container>
  </div>
</template>

<script>
import { Decode } from '@/services'
// const productResult = []
// for (let i = 0; i < 48; i++) {
//   productResult.push({
//     product_id: i,
//     name: `Data Title product ${i}`,
//     price: ` ${(i + 1) * 100}`,
//     stock: `${i}`,
//     image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
//   })
// }
export default {
  // metaInfo () {
  //   return {
  //     title: this.textSearch + ' ราคาพิเศษ | INET-Marketplace Platform',
  //     titleTemplate: '%s',
  //     htmlAttrs: {
  //       lang: 'th-TH'
  //     },
  //     meta: [
  //       { vmid: 'description', name: 'description', content: this.textSearch + 'สินค้าราคาพิเศษ | INET-Marketplace Platform' },
  //       { property: 'og:site_name', vmid: 'og:site_name', content: 'https://testinetmarket.one.th' },
  //       { property: 'og:title', vmid: 'og:title', content: this.textSearch + 'สินค้าราคาพิเศษ | INET-Marketplace Platform' },
  //       { property: 'og:description', vmid: 'og:description', content: this.textSearch + 'สินค้าราคาพิเศษ | INET-Marketplace Platform' },
  //       { property: 'og:type', vmid: 'og:type', content: 'website' },
  //       { property: 'og:url', vmid: 'og:url', content: 'https://testinetmarket.one.th' }
  //     ]
  //   }
  // },
  components: {
    CardProducts: () => import('@/components/Card/ProductCardBuyAgainPage')
    // CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive')
  },
  data () {
    return {
      textSearch: '',
      shopSearch: [],
      overlay2: true,
      pageMax: null,
      current: 1,
      pageSize: 48,
      productCount: null,
      ProductCount: null,
      PathImage: process.env.VUE_APP_IMAGE,
      BuyProductAgain: [],
      listOfProduct: [],
      oneData: [],
      selectPrice: '',
      shopID: '',
      tokenstatus: '',
      ADC_DESC: '',
      NoDataInSearch: false,
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'สั่งซื้ออีกครั้ง',
          disabled: true,
          href: ''
        }
      ],
      itemPrice: [
        { text: 'ทั้งหมด', value: '-' },
        { text: 'ราคา: จากน้อยไปมาก', value: 'lowToHigh' },
        { text: 'ราคา: จากมากไปน้อย', value: 'HighToLow' }
      ],
      toggle_exclusive: 0
    }
  },
  created () {
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    // this.$EventBus.$emit('searchdata')
    // this.$EventBus.$emit('getPath')
    this.getBuyProductAgain()
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.listOfProduct.slice(this.indexStart, this.indexEnd)
    },
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  methods: {
    async getBuyProductAgain () {
      var companyID = ''
      this.listOfProduct = []
      if (localStorage.getItem('roleUser') !== null) {
        this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
        if (localStorage.getItem('oneData') !== null) {
          this.tokenstatus = this.oneData.user.access_token
        } else {
          this.tokenstatus = ''
        }
      } else {
        this.dataRole = ''
        this.tokenstatus = ''
      }
      if (localStorage.getItem('SetRowCompany') !== null) {
        var companyDataSet = JSON.parse(
          Decode.decode(localStorage.getItem('SetRowCompany'))
        )
        companyID = companyDataSet.company.company_id
      } else {
        companyID = '-1'
      }
      var data
      data = {
        role_user: this.dataRole !== '' ? this.dataRole.role : 'ext_buyer',
        company_id: companyID,
        type: 'all',
        limit_item: ''
      }
      // console.log('data------>', data)
      await this.$store.dispatch('actionOrderBuyAgain', data)
      var response = await this.$store.state.ModuleOrder.stateOrderBuyAgain
      // console.log('responseOrderBuyAgain ======>', response)
      if (response.result === 'success') {
        this.overlay2 = false
        if (response.data.orders.length !== 0) {
          this.listOfProduct = await [...response.data.orders]
          // console.log('BuyProductAgain ===========>', this.BuyProductAgain)
          this.status = true
          this.check = true
          this.StatusHomeNewProduct = true
        } else {
          this.overlay2 = false
          this.status = true
          this.check = true
          this.StatusHomeNewProduct = false
        }
      } else {
        this.listOfProduct = []
        this.status = true
        this.check = true
        this.StatusHomeNewProduct = false
      }
    }
    // gotoShopDetail (val) {
    //   const shopCleaned = val.shop_name.replace(/\s/g, '-')
    //   this.$router.push(`/shoppage/${shopCleaned}-${val.shop_id}`).catch(() => {})
    // },
    // async pageChange () {
    //   var dataAllShop
    //   if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
    //     var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    //     var companyId = ''
    //     if (localStorage.getItem('SetRowCompany') !== null) {
    //       companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
    //     }
    //     dataAllShop = {
    //       role_user: dataRole.role,
    //       company_id: companyId !== '' ? companyId.company.company_id : '-1',
    //       page: this.pageNumber
    //     }
    //   } else {
    //     dataAllShop = {
    //       role_user: 'ext_buyer',
    //       company_id: '-1',
    //       page: this.pageNumber
    //     }
    //   }
    //   await this.$store.dispatch('actionListAllShop', dataAllShop)
    //   var response = await this.$store.state.ModuleShop.stateListAllShop
    //   console.log('Result Search ======>', response)
    //   if (response.code === 200) {
    //     this.overlay2 = false
    //     this.productSearch = response.data.list_shop
    //     this.pageMax = parseInt(response.data.total_page)
    //     this.shopSearchShow = response.data.list_shop
    //     this.shopCount = response.data.total_shop
    //     window.scrollTo(0, 0)
    //   } else {
    //     this.overlay2 = false
    //     this.productSearch = []
    //     this.shopSearchShow = []
    //     this.NoDataInSearch = true
    //   }
    // }
  }
}
</script>

<style scoped>
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  white-space: nowrap;
  list-style-type: none;
  /* margin-bottom: 24px; */
  /* padding: 8px 0px 8px 75px !important; */
}
.v-application a {
  color: #636363 !important;
}
.v-breadcrumbs__item  {
  color: #27AB9C !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
</style>
