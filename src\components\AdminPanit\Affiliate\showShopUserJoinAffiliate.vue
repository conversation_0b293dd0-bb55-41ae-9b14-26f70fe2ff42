<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card width="100%" height="100%" style="background: #FFFFFF;" elevation="0" class="mx-0 my-0">
      <v-card-title style="font-weight: 700; font-size: 24px; line-height: 22px; color: #333333;" v-if="!MobileSize"><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon>ร้านค้าที่ผู้สมัครได้เข้าร่วม</v-card-title>
      <v-card-title style="font-weight: 700;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backtoPage()">mdi-chevron-left</v-icon> ร้านค้าที่ผู้สมัครได้เข้าร่วม</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6" sm="12" :class="!MobileSize ? 'pl-0 pr-3 mb-4 pt-3' : 'pl-2 pr-2 mb-4 pt-3'">
            <v-text-field v-model="search" placeholder="ค้นหาชื่อร้านค้า" outlined rounded dense hide-details>
              <v-icon slot="append" color="#27AB9C">mdi-magnify</v-icon>
            </v-text-field>
          </v-col>
          <v-col cols="12" md="12">
            <v-row>
              <v-col :cols="MobileSize ? 12 : IpadSize ? 8 : 6" :class="IpadSize ? 'pt-0' : ''">
                <span style="font-size: 18px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-if="detailShop.length !== 0 && (!MobileSize && !IpadSize)">ร้านค้าที่ผู้สมัครได้เข้าร่วมทั้งหมด {{ showCountRequest }} ร้านค้า</span>
                <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600;" v-else-if="detailShop.length !== 0 && (MobileSize || IpadSize)">ร้านค้าที่ผู้สมัครได้เข้าร่วมทั้งหมด {{ showCountRequest }} ร้านค้า</span>
              </v-col>
            </v-row>
            <v-row dense>
              <v-col cols="12">
                <v-data-table
                :headers="headers"
                :items="detailShop"
                :search="search"
                style="width:100%;"
                height="100%"
                :page.sync="page"
                @pagination="countRequest"
                no-results-text="ไม่พบชื่อร้านค้าที่ผู้สมัครได้เข้าร่วม"
                no-data-text="ไม่มีร้านค้าที่ผู้สมัครได้เข้าร่วม"
                :items-per-page="10"
                class="elevation-1 mt-4"
                :footer-props="{'items-per-page-text':'จำนวนแถว'}"
                >
                  <template v-slot:[`item.indexOfUser`]="{ index }">
                    {{ index + 1 }}
                  </template>
                  <template v-slot:[`item.name_th`]="{ item }">
                    <v-row align="center">
                      <v-col cols="auto">
                        <v-img width="50" height="70" src="@/assets/NoImage.png" v-if="item.path_logo === ''" contain></v-img>
                        <v-img width="50" height="70" :src="`${item.path_logo}`" v-else contain></v-img>
                      </v-col>
                      <v-col style="text-align: start;">
                        <span>{{ item.name_th }}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template v-slot:[`item.created`]="{ item }">
                      <v-col style="text-align: center;">
                        <span>วันที่เริ่มต้น : {{ formatDate(item.created) }}</span><br>
                        <span>วันที่สิ้นสุด : ไม่จำกัด</span>
                      </v-col>
                  </template>
                  <template v-slot:[`item.register`]="{ item }">
                    <span v-if="item.register === 'yes'">
                      <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">เปิดการใช้งาน</v-chip>
                    </span>
                    <span v-if="item.register === 'no'">
                      <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ปิดการใช้งาน</v-chip>
                    </span>
                  </template>
                </v-data-table>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  data () {
    return {
      search: '',
      userDataListJoinShop: [],
      detailShop: [],
      showCountRequest: 0,
      itemsPerPage: 10,
      page: 1,
      headers: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '20', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้าน', value: 'name_th', width: '200', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ระยะเวลาข้อเสนอ', value: 'created', width: '210', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'สถานะร้านค้า', value: 'register', width: '170', align: 'center', sortable: false, class: 'backgroundTable fontTable--text' }
      ]
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    async MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/showShopUserJoinAffiliateMobile' }).catch(() => {})
      } else {
        await localStorage.setItem('pathAdmin', 'showShopUserJoinAffiliate')
        this.$router.push({ path: '/showShopUserJoinAffiliate' }).catch(() => {})
      }
    }
  },
  created () {
    this.$EventBus.$emit('changeNavAdmin')
    this.userID = Number(this.$route.query.userID)
    if (localStorage.getItem('oneData') !== null) {
      this.getUserJoinAffiliate()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  methods: {
    backtoPage () {
      if (this.MobileSize) {
        this.$router.push({ path: '/userJoinAffiliateMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/userJoinAffiliate' }).catch(() => {})
      }
    },
    countRequest (pagination) {
      this.showCountRequest = pagination.itemsLength
    },
    async getUserJoinAffiliate () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsGetUserJoinAffiliate')
      var response = await this.$store.state.ModuleAdminManage.stateGetUserJoinAffiliate
      if (response.message === 'Get user with affiliate') {
        this.$store.commit('closeLoader')
        this.userDataListJoinShop = [...response.data.users_with_affiliate.filter(user => user.id === this.userID)]
        // console.log('data', this.userDataListJoinShop)
        if (this.userDataListJoinShop.length > 0) {
          const shops = this.userDataListJoinShop[0].shops
          this.detailShop = shops
          // console.log(this.detailShop)
        }
      } else if (response.message === 'This user didn\'t have permissions.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'ผู้ใช้รายนี้ไม่มีสิทธิ์ใช้งานแอดมิน', showConfirmButton: false, timerProgressBar: true, timer: 1500 })
      } else if (response.message === 'Error message.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'warning', text: 'กรุณาติดต่อเจ้าหน้าที่', showConfirmButton: false, timerProgressBar: true, timer: 2500 })
      } else if (response.message === 'This user is unauthorized.') {
        this.$store.commit('closeLoader')
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({ icon: 'warning', text: 'กรุณาเข้าสู่ระบบใหม่อีกครั้ง', showConfirmButton: false, timerProgressBar: true, timer: 1500 })
        // window.location.assign('/')
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
      }
    },
    formatDate (value) {
      const text = value
      const result = text.substring(0, 10)
      return result
    }
  }
}
</script>

<style scoped>
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
