<template>
  <div>
  </div>
</template>
<script>
import { Encode } from '@/services'
export default {
  async created () {
    const auth = {
      headers: { Authorization: `Bearer ${this.$router.currentRoute.query.data}` }
    }
    const response = await this.axios.post(`${process.env.VUE_APP_BACK_END}api/user_detail_mp_v2`, '', auth)
    if (response.data.code !== 500) {
      var onedata = {}
      onedata.user = response.data.data
      localStorage.setItem('oneData', Encode.encode(onedata))
      var PathRedirect = localStorage.getItem('CurrentPath')
      if (PathRedirect === '/Register' || PathRedirect === '/Login' || PathRedirect === undefined || PathRedirect === null) {
        PathRedirect = '/'
      }
      var dataRole = {
        role: 'ext_buyer'
      }
      localStorage.setItem('roleUser', JSON.stringify(dataRole))
      localStorage.setItem('PermissionUser', JSON.stringify(response.data.data.current_role_user))
      if (response.data.data.current_role_user.seller_admin === 'Y') {
        if (response.data.data.permission.dashboard === 'Y') {
          window.location.assign('/dashboard')
        } else {
          var permission = localStorage.getItem('permission')
          if (permission === 'super_admin' || permission === 'admin' || permission === 'employee') {
            this.$router.push({ path: '/userInfo' }).catch(() => { })
          }
        }
      } else {
        this.$router.push({ path: `${PathRedirect}` }).catch(() => { })
      }
    } else {
      window.location.assign(`${process.env.VUE_APP_REDIRECT}`)
    }
  }
}
</script>
