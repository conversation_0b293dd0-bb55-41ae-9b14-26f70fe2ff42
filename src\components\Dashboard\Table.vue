<template>
<div>
  <div class="ml-4 mt-3">
    <span style="font-size: 16px; color: #333333; font-weight: 600;">ตารางแสดงข้อมูลจากการระบุเงื่อนไข สำหรับแสดงกราฟแดชบอร์ดร้านค้า</span>
  </div>
<!-- <v-container class="py-0 my-0"> -->
 <v-row class="mobileTable ma-0 pb-0">
  <v-col cols="7" class="pb-0 mb-0">
   <v-text-field
            v-model="search"
            label=""
            outlined
            dense
            class="pt-2 mt-0 "
            placeholder="ค้นหา"
             clearable
             append-icon="mdi-magnify"
             @click:append="onTest()"
          >
          </v-text-field>
  </v-col>
  <v-col cols="5">
    <div >
    <v-tooltip bottom color="rgba(255, 255, 255, .1)">
        <template v-slot:activator="{ on, attrs }">
        <v-icon
          color="success"
          dark
          v-bind="attrs"
          v-on="on"
          class="pt-4"
          style="margin-left: -18px;"
        >
          mdi-information-outline
        </v-icon>
      </template>
      <img
         src="../../assets/icons/tooltip.png"
         alt="John"
         width="241px"
         height="360px"
        >
    </v-tooltip>
  </div>
</v-col>
</v-row>
 <v-row v-resize="onResize" >
  <v-col cols="12">
  <v-data-table
          :headers="dataMain['headers-3']"
          :items="desserts"
          :search="search"
          color="#D4F1E4"
          class="mt-0 pt-0"
          :header-props="headerProps"
    >
     <template v-slot:top v-if="isMobile">
          <v-text-field
            v-model="search"
            label="ค้นหา"
            class="mx-4"
          ></v-text-field>
        </template>
           <template
            v-slot:[`item.buyer_ID`]="{ item: { buyer_ID } = {} }" @click="setKey({buyer_ID})"><div class="pointer">{{ buyer_ID }}</div>
          </template>
            <template
            v-slot:[`item.buyer_name`]="{ item: {  buyer_name } = {} }"><div class="pointer" @click="setKey({buyer_name: buyer_name})"><span class="dot2">{{ buyer_name }}</span></div>
          </template>
           <template
            v-slot:[`item.buyer_org_ID`]="{ item: {  buyer_org_ID } = {} }">{{ buyer_org_ID !== -1 ? buyer_org_ID : '-' }}
          </template>
          <template
            v-slot:[`item.product_id`]="{ item: {  product_id } = {} }">{{ product_id }}
          </template>
          <template
            v-slot:[`item.shop_ID`]="{ item: {  shop_ID } = {} }">{{ shop_ID }}
          </template>
          <template
            v-slot:[`item.sku`]="{ item: {  sku } = {} }">{{ sku }}
          </template>
          <template
            v-slot:[`item.unit`]="{ item: {  unit } = {} }">{{ unit }}
          </template>
          <template
            v-slot:[`item.value`]="{ item: {  value } = {} }">{{ value }}
          </template>
            <v-alert slot="no-results" :value="true" color="error" icon="warning">
                การค้นหาของคุณ "{{ search }}" ไม่พบผลลัพธ์
              </v-alert>
    </v-data-table>
    </v-col>
    </v-row>
  </div>
</template>
<script>
// import dataTest from '../library/dataTest.json'
import dataMap from '../library/TestTable.json'
// import eventBus from '@/components/eventBus'
export default {
  data () {
    return {
      dataMain: dataMap,
      desserts: [1],
      pageMax: null,
      current: 1,
      pageSize: 10,
      dataTemp: [],
      pageCount: null,
      toDay: new Date().toISOString().slice(0, 10),
      Day: `${new Date().toISOString().slice(0, 7)}-01`,
      search: '',
      headerProps: {
        sortByText: 'เรียงตาม'
      },
      isMobile: false
    }
  },
  created () {
    this.init()
    // console.log(this.$store.state.ModuleShop.stateDashboard)
    this.$EventBus.$on('selectDataFilter', this.selectDataFilter)
    this.$EventBus.$on('ResetTable', this.ResetTable)
    this.$EventBus.$on('filterBy', this.filterBy)
  },
  destroyed () {
    this.$EventBus.$off('selectDataFilter')
    this.$EventBus.$off('ResetTable')
    this.$EventBus.$off('filterBy')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    jsonCopy: {
      get () {
        if (Object.values(this.$store.getters.jsonCopy).length !== 0) {
          return this.$store.getters.jsonCopy
        } else {
          return ''
        }
      }
    },
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.desserts.slice(this.indexStart, this.indexEnd)
    }
  },
  watch: {
    '$store.getters.jsonCopy' (e) {
      // console.log('watch-->', e)
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/dashboardMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/dashboard' }).catch(() => {})
      }
    }
  },
  mounted () {
    this.series = []
    this.$store.state.ModuleShop.stateSeries = []
  },
  methods: {
    // succesStatus () {
    //   this.$EventBus.$emit('appendData')
    // },
    async init () {
      const data = {
        start_date: '',
        end_date: '',
        search_type: 'all'
      }
      await this.$store.dispatch('actionsDashboard', data)
      var response = await this.$store.state.ModuleShop.stateDashboard
      // var response = await dataTest
      // console.log('init-->', response)
      this.desserts = []
      this.desserts = response.data
      // this.pageMax = parseInt(this.desserts.length / 10) === 0 ? 1 : Math.ceil(this.desserts.length / 10)
      // this.pageCount = response.data.length
    },
    async setKey (args) {
      // console.log('TEST+1', args)
      // this.$store.state.ModuleShop.stateDashboard = await []
      await this.$store.dispatch('actionsSetChart', args)
      await this.$EventBus.$emit('appendData')
      const num = await {
        number: '0'
      }
      await this.$EventBus.$emit('FuntionChange', num)
      await this.$EventBus.$emit('numChangeData')
      await this.filterBy()
    },
    async ResetTable () {
      // const data = {
      //   start_date: '',
      //   end_date: '',
      //   search_type: 'all'
      // }
      // await this.$store.dispatch('actionsDashboard', data)
      const response = await this.$store.state.ModuleShop.stateDashboard
      this.desserts = await []
      this.desserts = await response.data
    },
    async copyJson () {
      const testingCodeToCopy = document.querySelector('#CopyJson')
      testingCodeToCopy.select()
    },
    onCopy (e) {
      alert('You just copied the following text to the clipboard: ' + e.text)
    },
    onError (e) {
      alert('Failed to copy the text to the clipboard')
      // console.log(e)
    },
    async onTest () {
      // console.log('onTest')
    },
    async selectDataFilter () {
      // this.pageMax = null
      // this.current = 1
      // this.pageSize = 10
      // this.pageCount = null
      const { data = {} } = await this.$store.state.ModuleShop.stateDashboard
      this.desserts = await data
      // console.log('response', this.desserts)
      // this.pageMax = parseInt(this.desserts.length / 10) === 0 ? 1 : Math.ceil(this.desserts.length / 10)
      // console.log('pageMax ===>', this.pageMax, this.desserts.length)
      // this.pageCount = response.data.length
    },
    async filterBy () {
      // console.log('testfilterBy', this.$store.state.ModuleShop.stateDashboard)
      var response = await this.$store.state.ModuleShop.stateDashboard
      this.desserts = await []
      this.desserts = await response.data
    },
    onResize () {
      if (window.innerWidth < 650) {
        this.isMobile = true
      } else {
        this.isMobile = false
      }
    }
  }
}
</script>

<style>
@media only screen and (max-width: 650px) {
  .mobileTable {
    display: none;
  }
}
.pointer { cursor: pointer; }
.main {
  display:flex;
  justify-content:space-around;
}
.dot {
display: inline-block;
    width: 180px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
::v-deep .v-text-field--filled > .v-input__control > .v-input__slot, .v-text-field--full-width > .v-input__control > .v-input__slot, .v-text-field--outlined > .v-input__control > .v-input__slot {
  height: 40px;
}
::v-deep sd {
  padding-bottom: 120px;
}
.dot2 {
    display: inline-block;
    width: 120px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
.hr {
    display: block;
    height: 1px;
    border: 0;
    border-top: 1px dotted #ccc;
    margin: 1em 0;
    padding: 0;
}
</style>
