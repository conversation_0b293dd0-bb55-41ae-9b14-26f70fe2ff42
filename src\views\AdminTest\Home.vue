<template>
  <v-app id="inspire" class="backgroundPage">
    <v-navigation-drawer
      v-model="drawer"
      absolute
      height="100%"
      app
      :permanent="PCSize === true ? true : false"
    >
      <v-list nav>
        <!-- <v-list> -->
          <v-list-item class="px-2">
            <v-list-item-content>
              <v-list-item-title class="text-h6" style="white-space: normal; font-weight: bold; line-height: 30px;">
                การจัดการเว็บไซต์
              </v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        <!-- </v-list> -->

        <v-list-item-group
          mandatory
          v-model="selectedItem"
        >

          <v-list-item @click="changeTitle('บริษัท')" style="line-height: 26px;">
            <v-list-item-icon>
              <v-icon color="#27AB9C">mdi-office-building</v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title @click="changePage('/adminTest')" style="white-space: normal; font-weight: bold; line-height: 30px;">บริษัท</v-list-item-title>
            </v-list-item-content>
          </v-list-item>

          <v-list-item
            v-for="item in listMenu"
            :key="item.title"
            v-show="titleList !== 'บริษัท' ? true : false"
            style="line-height: 26px;"
          >
            <v-list-item-icon>
              <v-icon v-text="item.icon" color="#27AB9C"></v-icon>
            </v-list-item-icon>
            <v-list-item-content>
              <v-list-item-title v-if="item.title === 'รายละเอียดของ'" @click="changeTitle(item.title); changePage(item.path)" style="white-space: normal; font-weight: bold; line-height: 30px;">รายละเอียดของ {{ CompanyName }}</v-list-item-title>
              <v-list-item-title v-text="item.title" v-else @click="changeTitle(item.title); changePage(item.path)" style="font-weight: bold; line-height: 30px; white-space: normal;"></v-list-item-title>
            </v-list-item-content>
          </v-list-item>
        </v-list-item-group>
      </v-list>
    </v-navigation-drawer>

    <v-app-bar
      absolute
      app
      dark
      color="#27AB9C"
      elevate-on-scroll
      scroll-target="#scrolling-techniques-7"
    >
      <v-app-bar-nav-icon @click="drawer = !drawer" class="hidden-md-and-up"></v-app-bar-nav-icon>

      <v-toolbar-title :style="{ 'padding-left': MobileSize ? '0px': '' }">
        <v-img :src="require('@/assets/MarketLogo.png')" contain  class="mr-5"  height="65" width="150" v-if="!MobileSize"/>
        <v-img :src="require('@/assets/MarketLogo.png')" contain  height="65" width="100" v-else/>
      </v-toolbar-title>
      <v-spacer></v-spacer>
      <v-icon size="34" color="white" large class="pr-2">mdi-account-circle-outline</v-icon>
      <span>{{ username }}</span>
    </v-app-bar>

    <v-main>
      <v-container fluid>
        <router-view></router-view>
      </v-container>
    </v-main>
  </v-app>
</template>

<script>
import { Decode } from '@/services'
export default {
  data: () => ({
    drawer: true,
    selectedItem: 1,
    titleList: 'บริษัท',
    listMenu: [
      { key: 0, title: 'รายละเอียดของ', icon: 'mdi-information', path: '/detailCompany' },
      { key: 1, title: 'แผนก', icon: 'mdi-file-tree', path: '/departmentsCompany' },
      { key: 2, title: 'ผู้ใช้งาน', icon: 'mdi-account-circle', path: '/usersCompany' },
      { key: 3, title: 'รายชื่อคู่ค้าองค์กร', icon: 'mdi-account-group', path: '/partnerCompany' },
      { key: 4, title: 'รายการสั่งซื้อทั้งหมด', icon: 'mdi-file-document-multiple', path: '/orderCompany' }
    ],
    CompanyName: '',
    userdetail: '',
    username: '',
    fullname: ''
  }),
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    PCSize () {
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  watch: {
    drawer (val) {
      // console.log(val)
      if (this.MobileSize === false) {
        this.drawer = val
      } else if (this.IpadProSize === false) {
        this.drawer = val
      } else if (this.IpadSize === false) {
        this.drawer = val
      } else {
        this.drawer = true
      }
    }
  },
  async created () {
    this.$EventBus.$on('changeTitle', this.changeTitle)
    this.$EventBus.$on('changeNavAdminManage', this.changeNavAdminManage)
    this.$EventBus.$on('getCompanyName', this.getCompanyName)
    var dataRole = JSON.parse(localStorage.getItem('roleUser'))
    var data = {
      role_user: dataRole.role
    }
    await this.$store.dispatch('actionsUserDetailPage', data)
    const userdetail = await this.$store.state.ModuleUser.stateUserDetailPage
    // console.log('userdetail data', userdetail)
    this.userdetail = userdetail.data[0]
    // Username
    if (this.userdetail.username !== null && this.userdetail.username_oneid === null) {
      this.username = this.userdetail.username
    } else if (this.userdetail.username === null && this.userdetail.username_oneid !== null) {
      this.username = this.userdetail.username_oneid
    } else if (this.userdetail.username !== null && this.userdetail.username_oneid !== null) {
      this.username = this.userdetail.username
    } else {
      this.username = '-'
    }
    // Full name
    if (this.userdetail.first_name_th === '' && this.userdetail.last_name_th === '') {
      this.fullname = '-'
    } else {
      this.fullname = this.userdetail.first_name_th + ' ' + this.userdetail.last_name_th
    }
  },
  mounted () {
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$on('changeTitle')
      this.$EventBus.$on('changeNavAdminManage')
      this.$EventBus.$on('getCompanyName')
    })
  },
  methods: {
    getCompanyName () {
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('companyData')))
      this.CompanyName = companyData.name_th
    },
    changeTitle (val) {
      // console.log(val)
      this.titleList = val
    },
    changePage (val) {
      this.$router.push({ path: `${val}` }).catch(() => {})
    },
    changeNavAdminManage () {
      if (this.$router.currentRoute.name === 'admimManage') {
        this.selectedItem = 0
      } else if (this.$router.currentRoute.name === 'manageCompany' && this.$route.query.Status === 'Create') {
        this.selectedItem = 0
      } else if (this.$router.currentRoute.name === 'detailCompany') {
        this.selectedItem = 1
      } else if (this.$router.currentRoute.name === 'manageCompany' && this.$route.query.Status === 'Edit') {
        this.selectedItem = 1
      } else if (this.$router.currentRoute.name === 'departmentsCompany') {
        this.selectedItem = 2
      } else if (this.$router.currentRoute.name === 'usersCompany') {
        this.selectedItem = 3
      } else if (this.$router.currentRoute.name === 'partnerCompany' || this.$router.currentRoute.name === 'partnerDetail') {
        this.selectedItem = 4
      } else if (this.$router.currentRoute.name === 'orderCompany') {
        this.selectedItem = 5
      }
    }
  }
}
</script>

<style scoped>
.theme--dark .v-bar--underline.theme--light, .theme--light .v-bar--underline.theme--light {
  border-bottom-color: rgba(0,0,0,.12)!important;
}
.backgroundPage{
  background-color: #F6F6F6;
}
</style>
