<template>
  <v-container>
    <v-row >
      <v-col cols="7" md="6">
       <h1 v-if="!MobileSize"><B>ข้อมูลแผนกในบริษัท</B></h1>
       <h2 v-if="MobileSize"><B>ข้อมูลแผนกในบริษัท</B></h2>
      </v-col>
      <v-col cols="5" md="6" align="end">
        <v-btn @click="AddDepartment()"  color="#27AB9C" class="ml-4 pl-4 pr-4 white--text" >
          <v-icon left> mdi-plus</v-icon><B>เพิ่มฝ่ายและแผนก</B>
        </v-btn>
        <!-- <v-icon style="margin-left:3%">mdi-dots-horizontal</v-icon> -->
      </v-col>
    </v-row>
    <v-card class="rounded-lg" >
      <v-container>
        <v-row style="margin-Top:1%;margin:8px">
         <h2>รายการแผนกในบริษัท</h2>
         <v-col cols="12" md="12"  dense>
          <v-card-title>
            แสดง <v-select :style="MobileSize ? 'width:70px': IpadProSize ? 'width:50px': IpadSize ? 'width:40px':'width:5px'" v-model="itemsPerPage" :items="number" solo class="pa-2 ma-0 pt-6"   dense @input="itemsPerPage = parseInt($event, 10)"></v-select>รายการ<v-spacer>
            </v-spacer><v-spacer></v-spacer><v-spacer></v-spacer><v-spacer></v-spacer><v-spacer></v-spacer><v-spacer></v-spacer>
            <v-text-field
              v-model="search"
              append-icon="mdi-magnify"
              label="ค้นหา"
              outlined
              dense
              rounded
              hide-details
            ></v-text-field>
          </v-card-title>
          <v-data-table
            :headers="this.level === 2? headerslevel2 : headerslevel2"
            :items="dataDepartment"
            :page.sync="page"
            :items-per-page="itemsPerPage"
            :search="search"
            hide-default-footer
            class="elevation-1"
            height="400px"
            @page-count="pageCount = $event"
          >
          <template  v-slot:[`item.number`]="{ index}">
            <span>{{ index + 1 }}</span>
          </template>
          <template  v-slot:[`item.datadetail`]="{ item }">
            <v-btn
              class="ma-2"
              outlined
              color="#27AB9C"
              @click="GoToDetail(item)"
            >
              รายละเอียด
            </v-btn>
          </template>
          <template v-slot:[`item.EditAndDelect`]="{ item }">
            <v-icon
              class="mr-2"
              @click="editItem(item)"
            >
              mdi-pencil
            </v-icon>
            <v-icon
              @click="dialogDelete(item)"
            >
              mdi-delete
            </v-icon>
          </template>
          </v-data-table>
          <div class="text pt-2">แสดง 1 ถึง  {{this.countitem}} ของ {{itemsPerPage}} รายการ</div>
          <div class="text-center pt-2">
            <v-pagination
              v-model="page"
              :length="pageCount"
            ></v-pagination>
          </div>
         </v-col>
        </v-row>
      </v-container>
    </v-card>
    <v-dialog  v-model="dialog_Delete" width="600" persistent>
     <v-card>
      <v-card-title >
       <span>กรุณากดยืนยันเพื่อทำการลบ</span>
      </v-card-title><br/>
      <v-card-text>
        <span>กรุณาตรวจสอบก่อนที่จะลบฝ่ายและแผนก</span>
      </v-card-text>
      <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn dense rounded dark outlined color="#27AB9C" class="pl-7 pr-7" @click="Close()">ยกเลิก</v-btn>
        <v-btn dense rounded color="#27AB9C" class="ml-4 pl-8 pr-8 white--text" @click="deleteItem()">ตกลง</v-btn>
      </v-card-actions>
     </v-card>
    </v-dialog>
  </v-container>
</template>

<script>
import { Decode } from '@/services'
export default {
  data () {
    return {
      dataDepartment: [],
      company_id: '',
      level: '',
      page: 1,
      pageCount: 0,
      itemsPerPage: 10,
      search: null,
      countitem: null,
      dialog_Delete: false,
      item_Dialog: null,
      headerslevel3: [
        {
          text: 'ลำดับที่',
          align: 'start',
          sortable: true,
          value: 'number'
        },
        { text: 'รหัสบริษัท', value: 'company_code' },
        { text: 'รหัสฝ่าย', value: 'division_code' },
        { text: 'ชื่อฝ่าย', value: 'divison_name_th' },
        { text: 'รหัสแผนก', value: 'department_code' },
        { text: 'ชื่อแผนก', value: 'department_name_th' },
        { text: 'ข้อมูล', value: 'datadetail' },
        { text: 'แก้ไข/ลบ', value: 'EditAndDelect' }
      ],
      headerslevel2: [
        {
          text: 'ลำดับที่',
          align: 'start',
          sortable: false,
          value: 'number',
          class: 'backgroundTable fontTable--text'
        },
        { text: 'รหัสบริษัท', value: 'company_code', class: 'backgroundTable fontTable--text' },
        { text: 'รหัสแผนก', value: 'department_code', class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อแผนก', value: 'department_name_th', class: 'backgroundTable fontTable--text' },
        { text: 'ข้อมูล', value: 'datadetail', class: 'backgroundTable fontTable--text' },
        { text: 'แก้ไข/ลบ', value: 'EditAndDelect', class: 'backgroundTable fontTable--text' }
      ],
      number: [10, 50, 100]
    }
  },
  created () {
    this.$EventBus.$emit('changeTitle', 'แผนก')
    this.$EventBus.$emit('changeNavAdminManage')
    this.company_id = JSON.parse(Decode.decode(localStorage.getItem('companyData'))).id
    this.level = JSON.parse(Decode.decode(localStorage.getItem('companyData'))).type_level
    // console.log('company', (JSON.parse(Decode.decode(localStorage.getItem('companyData')))))
    this.GetListDepartment()
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    GoToDetail (item) {
      localStorage.setItem('department_id', item.id)
      this.$router.push({ path: 'departmentsCompany/detaildepartmentsCompany', name: 'detaildepartmentsCompany', params: { id: item.id } })
    },
    AddDepartment () {
      this.$router.push({ path: 'createDepartment' })
    },
    editItem (item) {
      localStorage.setItem('department_id', item.id)
      // this.$router.push({ path: 'editDepartment' })
      this.$router.push({ path: 'editDepartment', name: 'EditDepartment', params: { id: item.id } })
    },
    Close () {
      this.dialog_Delete = false
      this.item_Dialog = null
    },
    dialogDelete (item) {
      this.dialog_Delete = true
      this.item_Dialog = item.id
    },
    async deleteItem () {
      var ID = this.item_Dialog
      var data = {
        company_id: this.company_id.toString(),
        department_id: ID.toString()
      }
      await this.$store.dispatch('actionsDeleteDepartment', data)
      var response = await this.$store.state.ModuleDepartment.stateDeleteDepartment
      // console.log('DeleteDepartment', response)
      this.Close()
      if (response.result === 'SUCCESS') {
        this.$swal.fire({ text: 'ลบฝ่ายและแผนกสำเร็จ', icon: 'success', timer: 2500, showConfirmButton: false })
        this.GetListDepartment()
      } else {
        this.$swal.fire({ text: 'ไม่สามารถลบฝ่ายและแผนก', icon: 'error', timer: 2500, showConfirmButton: false })
      }
    },
    async GetListDepartment () {
      var data = {
        company_id: this.company_id
      }
      await this.$store.dispatch('actionsListDepartment', data)
      var response = await this.$store.state.ModuleDepartment.stateListDepartment
      this.dataDepartment = response.data
      this.countitem = this.dataDepartment.length
      // console.log('ListDepartment', response)
    }
  }
}
</script>

<style scoped>
.v-text-field.v-text-field--solo .v-input__control{
    max-width: 75px;
    width: 75px;
}
</style>
