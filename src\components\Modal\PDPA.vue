<template>
  <v-row v-if="pathname !== '/registerLineOA'" justify="center">
    <v-dialog
      v-model="dialog"
      scrollable
      max-width="800px"
      :style="MobileSize ? 'z-index: 16000004' : ''"
      persistent
    >
      <v-card style="overflow-x: hidden;">
        <v-row class="py-8" dense no-gutters>
          <v-img src="@/assets/inetlogo.png" contain height="58" width="121" position="center" class="mr-4"></v-img>
        </v-row>
        <!-- <v-card-text style="font-size: 18px; text-align:center; overflow: hidden;">{{this.data.policy_title}} และ {{this.data.consent_form_title}}</v-card-text> -->
        <v-card-text style="font-size: 18px; text-align:center; overflow: hidden;">{{title}}</v-card-text>
        <v-divider></v-divider>
        <v-card-text style="height: 300px;">
          <!-- <div v-html="this.data.policy_title" style="font-size: 16pt !important; font-weight: bold; padding: 15px 0; text-decoration-line: underline;"></div> -->
          <div v-html="this.detail" style="padding: 15px 0;"></div>
          <!-- <div v-html="this.data.consent_form_title" style="font-size: 16pt !important; font-weight: bold; padding: 15px 0; text-decoration-line: underline;"></div>
          <div v-html="this.data.consent_form_detail" style="font-size: 14pt !important;"></div> -->
        </v-card-text>
        <v-divider></v-divider>
        <v-card-text style="overflow: hidden;">
          <v-checkbox v-model="checkbox">
            <template v-slot:label>
              <div>
                รับทราบ
                <!-- <span style="text-decoration: underline; color:#27AB9C;">ความยินยอมในการเก็บรวบรวม ใช้ หรือเปิดเผยข้อมูลส่วนบุคคล ระบบ Panit</span>
                <span> และ </span><br> -->
                <!-- <span style="text-decoration: underline; color:#27AB9C;">นโยบายความคุ้มครองข้อมูลส่วนบุคคล</span> -->
                <span style="text-decoration: underline; color:#27AB9C;">ข้อกำหนดดังกล่าว</span>
              </div>
            </template>
          </v-checkbox>
        </v-card-text>
        <v-row justify="center" class="mb-4">
        <v-card-actions>
          <!-- <v-btn dense dark outlined color="#27AB9C" class="pl-7 pr-7 mt-2" @click="Consent('reject')">
              ไม่ยินยอม
          </v-btn> -->
          <v-btn :disabled="!checkbox" dense color="#27AB9C" class="ml-4 mt-2 pl-8 pr-8 white--text" @click="confirmConsent()">
              รับทราบ
          </v-btn>
        </v-card-actions>
        </v-row>
      </v-card>
    </v-dialog>
    <ModalCheckName ref="ModalCheckName"/>
  </v-row>
</template>

<script>
export default {
  components: {
    ModalCheckName: () => import('@/components/Modal/CheckName')
  },
  data () {
    return {
      pathname: '',
      one_id: '',
      dialog: false,
      data: '',
      checkbox: false,
      title: '',
      detail: ''
    }
  },
  created () {
    this.pathname = window.location.pathname.replace(/\/$/, '')
    // this.getText()
  },
  computed: {
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  watch: {
    // checkbox (val) {
    //   console.log('checkbox', val)
    // }
  },
  methods: {
    async getText () {
      await this.$store.dispatch('actionsGetTextConsent')
      var res = await this.$store.state.ModuleUser.stateGetTextConsent
      this.title = res.data.title
      this.detail = res.data.consent_text
      // console.log('getText ===>', res)
    },
    open () {
      // this.one_id = id
      this.dialog = true
      this.getText()
    },
    async confirmConsent () {
      var data = {
        consent_agreement: this.checkbox,
        seller_shop_id: -1
      }
      await this.$store.dispatch('actionsAcceptConsent', data)
      var res = await this.$store.state.ModuleUser.stateAcceptConsent
      if (res.message === 'Consent created successfully') {
        this.dialog = false
        var CheckName = this.$store.state.ModuleUser.stateAuthorityUser
        if (CheckName.data.first_name_th === '' || CheckName.data.last_name_th === '') {
          this.$refs.ModalCheckName.open(CheckName.data.phone, CheckName.data.email, CheckName.data.username)
        }
      }
      // console.log('getConfirm ===>', res)
    }
    // actionsAcceptConsent
    // async getConsent () {
    //   if (this.one_id !== '') {
    //     var data = { user_id: this.one_id }
    //     var res = await this.axios.post(`${process.env.VUE_APP_BACK_END2}consent/create`, data)
    //     if (res.data.data.consent_form.status === 'pending' || res.data.data.consent_form.status === 'rejected') {
    //       this.data = res.data.data.consent_form
    //       // console.log('data', this.data)
    //     }
    //   }
    // },
    // async Consent (status) {
    //   var data = { user_id: this.one_id }
    //   if (status === 'reject') {
    //     var res = await this.axios.post(`${process.env.VUE_APP_BACK_END2}consent/rejected`, data)
    //     if (res.data.message === 'rejected consent success' || res.data.message === 'consent was already rejected') {
    //       this.$EventBus.$emit('Logout')
    //     }
    //   } else if (status === 'accept') {
    //     res = await this.axios.post(`${process.env.VUE_APP_BACK_END2}consent/approved`, data)
    //     if (res.data.message === 'approved consent success') {
    //       this.dialog = false
    //     }
    //   }
    // }
  }
}
</script>
