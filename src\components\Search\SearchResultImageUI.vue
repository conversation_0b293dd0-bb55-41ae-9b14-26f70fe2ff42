<template>
  <v-container>
    <v-breadcrumbs :items="RoleUser == 'sale_order' ? itemsSale : items" class="breadcrumbsPadding">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          {{ item.text }}
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <!-- <v-overlay :value="overlay2">
      <v-progress-circular indeterminate size="64"></v-progress-circular>
    </v-overlay> -->
    <div v-if="shopSearchShow.length !== 0" class="mt-4">
      <v-row v-if="!MobileSize">
        <!-- <v-col cols="12" md="12"> -->
        <span style="font-weight: 700; font-size: 18px; color: #333333;" class="mt-2 ml-3">ร้านค้าที่เกี่ยวข้องกับ
          "<span style="color: #27AB9C;">{{ textSearch }}</span>" ทั้งหมด {{ shopCount }} ร้าน</span>
        <v-spacer></v-spacer>
        <v-btn text color="#27AB9C" @click="gotoShowAllShop()" v-if="shopSearchShow.length !== 1">ร้านค้าอื่นๆ<v-icon
          small class="pt-1">mdi-chevron-right</v-icon>
        </v-btn>
        <!-- </v-col> -->
      </v-row>
      <v-row v-if="MobileSize">
        <v-col cols="8" md="6">
          <span style="font-weight: 700; font-size: 14px; color: #333333;" class="pa-0 mt-2 ml-3">
            ร้านค้าที่เกี่ยวข้องกับ"<span style="color: #27AB9C;">{{ textSearch }}</span>"
          </span><br />
          <span style="font-weight: 700; font-size: 14px; color: #333333;" class="pa-0 mt-2 ml-3">
            ทั้งหมด {{ shopCount }} ร้าน
          </span>
        </v-col>
        <v-col cols="4" md="6" align="end">
          <v-btn text color="#27AB9C" @click="gotoShowAllShop()" v-if="shopSearchShow.length !== 1">ร้านค้าอื่นๆ<v-icon
              small class="pt-1">mdi-chevron-right</v-icon>
          </v-btn>
        </v-col>
      </v-row>
      <!-- <v-row>
        <v-col cols="12" md="12" v-if="showSkeletonLoader">
          <v-skeleton-loader
            v-bind="attrs"
            type="image"
          ></v-skeleton-loader>
        </v-col>
        <v-col cols="12" md="12" v-else>
          <v-card :class="MobileSize ? 'pa-0' : 'mt-5 mb-5 ml-0 mr-0'" max-width="100%" outlined hover @click="gotoShopDetail(shopSearchShow)">
            <v-card-text style="padding: 1.5625rem;">
              <v-row no-gutters justify="start">
                <v-col cols="4" md="1" sm="2" xs="12" :align="IpadSize ? 'center' : ''">
                  <v-avatar size="60" @click="gotoShopDetail(shopSearchShow)"
                    v-if="shopSearchShow[0].seller_shop_logo !== ''"
                    style="cursor: pointer;">
                    <v-img alt="user" contain :src="`${shopSearchShow[0].seller_shop_logo}?=${new Date().getTime()}`" style="border: 1px solid #EBEBEB"></v-img>
                  </v-avatar>
                  <v-avatar size="60" v-else style="cursor: pointer;" @click="gotoShopDetail(shopSearchShow)">
                    <v-img src="@/assets/ImageINET-Marketplace/Shop/Store.png"></v-img>
                  </v-avatar>
                </v-col>
                <v-col cols="8" md="8" sm="10" xs="8">
                  <v-row dense justify="start">
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <p style="font-weight: bold; font-size: 15px;">{{ shopSearchShow[0].name_th }}</p>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12">
                      <v-row dense>
                        <v-btn class="pa-0" small color="#27AB9C" @click="gotoShopDetail(shopSearchShow)" text>
                          <v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า
                        </v-btn>
                      </v-row>
                    </v-col>
                  </v-row>
                </v-col>
              </v-row>
            </v-card-text>
          </v-card>
        </v-col>
      </v-row> -->
      <!-- <v-row no-gutters> -->
        <div v-if="shopSearchShow.length !== 0">
          <section>
          <vue-horizontal-list
            active-class="success"
            show-arrows
            center-active
            :items="shopToShow"
            :options="optionsCard"
          >
            <template v-slot:nav-prev>
              <v-icon color="#27AB9C" large>mdi-chevron-left</v-icon>
            </template>

            <template v-slot:nav-next>
              <v-icon color="#27AB9C" large>mdi-chevron-right</v-icon>
            </template>
            <template v-slot:default="{ item }">
              <v-col cols="12" class="pa-0 pl-2">
                <v-col cols="12" md="12" v-if="showSkeletonLoader">
                  <v-skeleton-loader
                    v-bind="attrs"
                    type="image"
                  ></v-skeleton-loader>
                </v-col>
                <v-col v-else cols="12" md="12" class="pa-0">
                  <v-card :href="item.pathShop" style="box-shadow: none;" rounded="8px" class="cardShop" :class="MobileSize ? 'pa-0' : ''" width="50vw" max-width="100%" height="100%" max-height="100%" outlined hover @click.prevent="gotoShopDetail(item)">
                    <!-- <v-card height="105px" max-height="150px" max-width="100%" width="100%" style="position: absolute;border-radius: 4px"> -->
                      <v-img v-if="item.seller_shop_banner !== null" :src="item.seller_shop_banner" height="105px" max-height="150px" max-width="100%" width="100%" style="position: absolute;border-radius: 4px"></v-img>
                      <v-img v-else src="@/assets/ImageINET-Marketplace/Shop/NoImgStore.png" height="105px" max-height="150px" max-width="100%" width="100%" style="position: absolute;border-radius: 4px"></v-img>
                    <!-- </v-card> -->
                    <v-card-text class="pa-0" style="position: relative; z-index:1">
                      <v-row no-gutters justify="start" style="padding-top: 55px !important;">
                        <v-col cols="12" align="center" class="pa-0">
                          <!-- <v-img height="15vh" max-height="25vh" max-width="16vw" width="11.230vw" cols="12" style="position: absolute;border-radius: 4px" class="backIMG">
                          </v-img> -->
                          <v-col cols="12" >
                            <v-avatar size="70" @click.prevent="gotoShopDetail(item)"
                              v-if="item.seller_shop_logo !== ''"
                              style="cursor: pointer; background-color: white; border:1px solid #27AB9C;">
                              <v-img alt="user" contain :src="`${item.seller_shop_logo}?=${new Date().getTime()}`"></v-img>
                            </v-avatar>
                            <v-avatar size="70" v-else style="cursor: pointer; background-color: white; border:1px solid #27AB9C;" @click.prevent="gotoShopDetail(item)">
                              <v-img src="@/assets/ImageINET-Marketplace/Shop/Store.png"></v-img>
                            </v-avatar>
                          </v-col>
                        </v-col>
                        <v-col cols="12" align="center">
                          <v-row dense no-gutters>
                            <v-col cols="12" md="12" sm="12" xs="12">
                              <p class="text-truncate px-1" style="font-weight: bold; font-size: 15px;">{{ item.name_th}}</p>
                            </v-col>
                          </v-row>
                        </v-col>
                      </v-row>
                    </v-card-text>
                    <v-card-actions>
                      <v-col cols="12" md="12" sm="12" xs="12" align="center" :class="MobileSize? 'pa-1' : ''">
                        <!-- <v-row dense no-gutters justify="center"> -->
                          <!-- <v-btn outlined small color="#27AB9C" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-chat</v-icon> แชท</v-btn>
                          <v-divider vertical class="ml-2 mr-2" ></v-divider> -->
                          <v-btn block style="border: 1px solid #BDE7D9 !important;" :style="MobileSize ? 'font-size: 8px;' : ''" outlined small color="#27AB9C" @click.prevent="gotoShopDetail(item)" text>
                            <v-icon small class="pr-1" >mdi-storefront</v-icon> ดูร้านค้า
                          </v-btn>
                          <!-- <v-divider vertical class="ml-2 mr-2" ></v-divider>
                          <v-btn outlined small color="#27AB9C" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-account-plus</v-icon> ติดตาม</v-btn> -->
                        <!-- </v-row> -->
                      </v-col>
                    </v-card-actions>
                  </v-card>
                </v-col>
              </v-col>
            </template>
          </vue-horizontal-list>
          </section>
        </div>
      <!-- </v-row> -->
    </div>
    <div v-if="productSearch.length !== 0">
      <v-row class="mt-4">
        <v-col cols="5" md="5" class="pa-0" v-if="!MobileSize && !IpadSize">
          <!-- <span style="font-weight: 700; font-size: 18px; color: #333333;" class="mt-2 ml-3">ค้นหาคำว่า "<span
            style="color: #27AB9C;">{{ textSearch|truncate(30, '...') }}</span>"</span> -->
        </v-col>
        <v-col cols="5" md="5" class="pa-0 pt-2" v-else-if="!MobileSize && IpadSize">
          <!-- <span style="font-weight: 700; font-size: 16px; color: #333333;" class="mt-2 ml-3">ค้นหาคำว่า "<span
            style="color: #27AB9C;">{{ textSearch|truncate(18, '...') }}</span>"</span> -->
        </v-col>
        <v-col cols="5" md="5" class="pa-0" v-else-if="MobileSize && !IpadSize">
          <!-- <span style="font-weight: 700; font-size: 14px; color: #333333;" class="mt-2 ml-3">ค้นหาคำว่า "
            <span style="color: #27AB9C;">{{ textSearch|truncate(10, '...') }}</span>"<br /> -->
            <!-- <span style="font-weight: 700; font-size: 14px; color: #333333;" class="mt-2 ml-3">ทั้งหมด {{ productSearch.length }} รายการ</span> -->
          <!-- </span> -->
        </v-col>
        <v-spacer v-if="!MobileSize"></v-spacer>
        <v-col cols="4" md="3" class="pa-0">
          <v-select class="setCustomSelect" v-model="selectPrice" :items="itemPrice" @change="ADCDESC()" item-text="text" item-value="value"
            placeholder="ราคา" dense outlined></v-select>
        </v-col>
        <v-col cols="2" md="1" :class="MobileSize ? 'pa-0 mb-4' : IpadSize ? 'pa-0 pl-4' : 'pt-0 mb-4 mr-4'">
          <v-btn-toggle v-model="toggle_exclusive" mandatory text group dense>
            <v-btn>
              <v-icon v-if="toggle_exclusive !== 0" color="#A1A1A1">mdi-view-grid-outline</v-icon>
              <v-icon v-else color="#27AB9C">mdi-view-grid</v-icon>
            </v-btn>
            <v-btn>
              <v-icon v-if="toggle_exclusive !== 1" color="#A1A1A1">mdi-view-agenda-outline</v-icon>
              <v-icon v-else color="#27AB9C">mdi-view-agenda</v-icon>
            </v-btn>
          </v-btn-toggle>
        </v-col>
      </v-row>
      <div v-if="showSkeletonLoader">
        <v-skeleton-loader
            v-bind="attrs"
            type="image, list-item-two-line"
          ></v-skeleton-loader>
      </div>
      <div class=""  v-else>
        <v-row justify="start" class="px-0" v-if="toggle_exclusive === 0 && !MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="(item, index) in productSearch" :key="index" class="px-2">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-if="toggle_exclusive === 0 && !MobileSize && !IpadSize && IpadProSize">
          <v-col cols="6" md="2" v-for="(item, index) in productSearch" :key="index" class="px-2">
            <CardProducts :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-else-if="toggle_exclusive === 0 && !MobileSize && IpadSize && !IpadProSize">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="(item, index) in productSearch" :key="index" class="px-2">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="start" class="px-0" v-else-if="toggle_exclusive === 0 && MobileSize && !IpadSize && !IpadProSize">
          <v-col cols="6" md="2" sm="3" xs="4" v-for="(item, index) in productSearch" :key="index" class="px-2">
            <CardProductsResponsive :itemProduct='item' />
          </v-col>
        </v-row>
        <v-row justify="center" class="px-0" v-if="toggle_exclusive === 1">
          <v-col cols="12" md="12" sm="12" xs="12" v-for="(item, index) in productSearch" :key="index" class="px-2">
            <CardProductsList :itemProduct='item' />
          </v-col>
        </v-row>
        <div v-if="isLoading" class="text-center my-4">
          <v-progress-circular indeterminate color="primary"></v-progress-circular>
        </div>
        <!-- <v-row justify="center" class="my-6">
          <v-pagination color="#27AB9C" v-model="pageNumber" :length="pageMax" :total-visible="7" @change="pageChange()">
          </v-pagination>
        </v-row> -->
        <div ref="targetSection" class="scroll-target"></div>
      </div>
    </div>
    <!-- <a-row type="flex" v-if="shopSearch.length !== 0">
      <a-col :span="24">
        <a-row type="flex">
          <a-col :span="12">
            <span style="font-weight: bold;">ร้านค้าที่เกี่ยวข้องกับ "{{ textSearch }}"</span>
          </a-col>
          <a-col :span="12">
            <a-row type="flex" justify="end">
              <v-btn text color="success" @click="gotoShowAllShop()">ร้านค้าอื่นๆ<v-icon small class="pt-1">mdi-chevron-right</v-icon></v-btn>
            </a-row>
          </a-col>
        </a-row>
      </a-col>
      <a-col :span="24" style="padding: 0; margin-top: -20px; margin-bottom: -20px;">
        <a-divider></a-divider>
      </a-col>
      <a-col :span="24" style="margin-top: 0px;">
        <v-card class="mx-auto mt-5 mb-5" max-width="100%" outlined hover @click="gotoShopDetail(shopSearchShow)">
          <v-card-text style="padding: 1.5625rem;">
            <v-row no-gutters justify="start">
                <v-col cols="1" md="1" sm="12" xs="12">
                <v-avatar size="60" @click="gotoShopDetail(shopSearchShow)" v-if="shopSearchShow.path_logo !== ''" style="cursor: pointer;">
                  <img
                    alt="user"
                    :src="`${shopSearchShow.path_logo}?=${new Date().getTime()}`"
                  >
                </v-avatar>
                <v-avatar size="60" v-else style="cursor: pointer;" @click="gotoShopDetail(shopSearchShow)">
                  <v-icon>
                    mdi-storefront
                  </v-icon>
                </v-avatar>
                </v-col>
                <v-col cols="2" md="2" sm="12" xs="12">
                <v-row dense no-gutters justify="start">
                    <v-col cols="12" md="12" sm="12" xs="12">
                        <p style="font-weight: bold; font-size: 15px;">{{ shopSearchShow.name_th }}</p>
                    </v-col>
                    <v-col cols="12" md="12" sm="12" xs="12">
                        <v-btn outlined small color="orange" @click="gotoShopDetail(shopSearchShow)"><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
                    </v-col>
                </v-row>
                </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </a-col>
    </a-row> -->
    <!-- ********************* ของเก่า ********************* -->
    <!-- <a-row type="flex" :gutter="[16, 8]" v-if="productSearch.length !== 0">
      <a-col :span="24">
        <a-row type="flex">
          <a-col :span="24">
            <span style="font-weight: bold;">ค้นพบสินค้า {{ productCount }} ชิ้น ในการค้นหาคำว่า "{{ textSearch }}"</span>
          </a-col>
          <a-col :span="24" style="padding: 0; margin-top: -20px;">
            <a-divider></a-divider>
          </a-col>
          <a-col :xs="24" :sm="12" :md="4" v-for="(item, index) in productSearch" :key="index">
            <CardProducts :itemProduct="item" />
          </a-col>
        </a-row>
      </a-col>
    </a-row> -->
    <a-row v-show="NoDataInSearch" type="flex" justify="center" style="margin-top: 10%; margin-bottom: 10%;"
      v-else-if="shopSearchShow.length === 0 && productSearch.length === 0">
      <template>
        <a-empty :image="require('@/assets/Not_Result.png')" :image-style="{height: '200px', marginBottom: '40px'}">
          <h1 slot="description" style="font-weight: bold;">ไม่พบผลการค้นหา</h1>
          <h3 slot="description" style="font-weight: bold;">โปรดตรวจสอบตัวสะกดว่าถูกต้องหรือไม่
            หรือค้นหาโดยใช้คำที่ใกล้เคียง</h3>
          <h3 slot="description" style="font-weight: bold;">กรุณาลองค้นหาใหม่อีกครั้ง</h3>
        </a-empty>
      </template>
    </a-row>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import VueHorizontalList from 'vue-horizontal-list'
import { Row, Empty } from 'ant-design-vue'
const productResult = []
for (let i = 0; i < 48; i++) {
  productResult.push({
    product_id: i,
    name: `Data Title product ${i}`,
    price: ` ${(i + 1) * 100}`,
    stock: `${i}`,
    image: `https://picsum.photos/500/300?image=${i * 5 + 10}`
  })
}
export default {
  metaInfo () {
    return {
      title: this.textSearch + ' | Nex Gen Commerce',
      titleTemplate: '%s',
      htmlAttrs: {
        lang: 'th-TH'
      },
      meta: [
        { vmid: 'description', name: 'description', content: this.textSearch + ' | Nex Gen Commerce' },
        { property: 'og:site_name', vmid: 'og:site_name', content: 'https://nexgencommerce.one.th/' },
        { property: 'og:title', vmid: 'og:title', content: this.textSearch + ' | Nex Gen Commerce' },
        { property: 'og:description', vmid: 'og:description', content: this.textSearch + '| Nex Gen Commerce' },
        { property: 'og:type', vmid: 'og:type', content: 'website' },
        { property: 'og:url', vmid: 'og:url', content: 'https://nexgencommerce.one.th/' }
        // { property: 'og:image', name: 'image', content: this.primaryImage },
        // { property: 'og:image:width', content: '640' },
        // { property: 'og:image:height', content: '480' }
      ]
    }
  },
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  components: {
    'a-row': Row,
    'a-empty': Empty,
    VueHorizontalList,
    // CardShop: () => import('@/components/Card/CardShop'),
    CardProducts: () => import('@/components/Card/ProductCardUI'),
    CardProductsResponsive: () => import('@/components/Card/ProductCardResponsive'),
    CardProductsList: () => import('@/components/Card/ProductCardListUI')
  },
  data () {
    return {
      FileImage: [],
      SearchImage: false,
      previousData: null,
      stopLazyLoad: false,
      page: 1,
      isLoading: false, // ตรวจสอบสถานะการโหลด
      hasMore: true, // ตรวจสอบว่ายังมีข้อมูลเพิ่มเติมหรือไม่
      shopToShow: [],
      check: false,
      options: {
        responsive: [
          { end: 576, size: 6 },
          { start: 576, end: 768, size: 6 },
          { start: 768, end: 992, size: 6 },
          { start: 992, end: 1200, size: 6 },
          { size: 6 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1300,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 16
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      optionsCard: {
        responsive: [
          { end: 576, size: 3 },
          { start: 576, end: 768, size: 4 },
          { start: 768, end: 992, size: 4 },
          { start: 992, end: 1200, size: 5 },
          { start: 1200, end: 1300, size: 7 },
          { size: 7 }
        ],
        list: {
          // 1200 because @media (min-width: 1200px) and therefore I want to switch to windowed mode
          windowed: 1200,

          // Because: #app {padding: 80px 24px;}
          padding: 0
        },
        item: {
          // css class to inject into each individual item
          class: '',
          // padding between each item
          padding: 0
        },
        position: {
          // Start from '1' on mounted.
          start: 0
        }
      },
      RoleUser: '',
      pathShop: '',
      productResult,
      textSearch: '',
      productSearch: [],
      shopSearch: [],
      overlay2: true,
      pageMax: null,
      current: 1,
      pageSize: 48,
      productCount: null,
      shopCount: null,
      PathImage: process.env.VUE_APP_IMAGE,
      path: process.env.VUE_APP_DOMAIN,
      shopSearchShow: [],
      selectPrice: '',
      search_ADC_DESC: '',
      shopID: '',
      ADC_DESC: '',
      NoDataInSearch: false,
      items: [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: '/'
        },
        {
          text: 'ผลการค้นหา',
          disabled: true,
          href: ''
        }
      ],
      itemPrice: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ราคา: จากน้อยไปมาก', value: 'lowToHigh' },
        { text: 'ราคา: จากมากไปน้อย', value: 'HighToLow' }
      ],
      toggle_exclusive: 0,
      // showSkeletonLoader: false,
      idCategory: ''
    }
  },
  created () {
    if (localStorage.getItem('roleUser') !== null) {
      this.RoleUser = JSON.parse(localStorage.getItem('roleUser')).role
    } else {
      this.RoleUser = 'ext_buyer'
    }
    if (this.RoleUser === 'sale_order') {
      this.pathshop = JSON.parse(Decode.decode(localStorage.getItem('pathShopSale'))).path
      this.itemsSale = [
        {
          text: 'หน้าแรก',
          disabled: false,
          href: this.pathshop
        },
        {
          text: 'ผลการค้นหา',
          disabled: true,
          href: '/shoppingcart'
        }
      ]
    }
    this.oneData = []
    if (Object.prototype.hasOwnProperty.call(localStorage, 'oneData')) {
      this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    }
    // this.$EventBus.$emit('searchdata')
    // const encodedFile = JSON.parse(localStorage.getItem('FileSearchImage'))
    // function base64ToFile (base64String, fileName) {
    //   const byteString = atob(base64String.split(',')[1]) // Decode Base64 data
    //   const mimeType = base64String.split(',')[0].split(':')[1].split(';')[0]
    //   const ab = new ArrayBuffer(byteString.length)
    //   const ia = new Uint8Array(ab)
    //   for (let i = 0; i < byteString.length; i++) {
    //     ia[i] = byteString.charCodeAt(i)
    //   }
    //   return new File([ab], fileName, { type: mimeType })
    // }
    // // Convert Base64 back to File
    // const decodedFile = base64ToFile(encodedFile, 'uploaded-file.jpg')
    // console.log(decodedFile)
    this.$EventBus.$emit('getPath')
    this.getResultSearchImage()
  },
  computed: {
    pageNumber: {
      get () {
        return this.current || 1
      },
      set (newPage) {
        // You could alternatively call your API here if you have serverside pagination
        this.current = newPage
        window.scrollTo(0, 0)
      }
    },
    indexStart () {
      return (this.current - 1) * this.pageSize
    },
    indexEnd () {
      return this.indexStart + this.pageSize
    },
    paginated () {
      return this.productSearch.slice(this.indexStart, this.indexEnd)
    },
    MobileSize () {
      // console.log('mobile', this.$vuetify.breakpoint)
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      // console.log('ipad pro w:1024', this.$vuetify.breakpoint)
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    desktopSize () {
      // console.log('ipad w:768', this.$vuetify.breakpoint)
      const { xl } = this.$vuetify.breakpoint
      return !!xl
    }
  },
  mounted () {
    window.scrollTo(0, 0)
    this.$EventBus.$on('getResultSearchImage', this.getResultSearchImage)
    this.$EventBus.$on('rePage', this.rePage)
    this.$on('hook:beforeDestroy', () => {
      this.$EventBus.$off('getResultSearchImage')
      this.$EventBus.$off('rePage')
    })
    // this.loadData()
    window.addEventListener('scroll', this.scrollToTargetSection)
    // เรียก method เพื่อตรวจสอบการเปลี่ยนแปลงเมื่อ component ถูกสร้าง
    this.previousData = this.$route.params.data
    this.checkDataChange()

    // สามารถตั้งค่าให้ตรวจสอบในทุกครั้งที่ route มีการเปลี่ยนแปลง
    this.$router.afterEach((to, from) => {
      this.checkDataChange()
    })
  },
  beforeDestroy () {
    window.removeEventListener('scroll', this.scrollToTargetSection)
    this.$EventBus.$off('getResultSearchImage')
    // window.removeEventListener('scroll', this.handleScroll)
  },
  watch: {
    // '$route.params.data': function (newVal, oldVal) {
    //   if (newVal !== oldVal) {
    //     this.page = 1
    //   }
    // }
  },
  methods: {
    rePage () {
      this.page = 1
    },
    async getResultSearchImage (file) {
      const encodedFile = JSON.parse(localStorage.getItem('FileSearchImage'))
      function base64ToFile (base64String, fileName) {
        const byteString = atob(base64String.split(',')[1]) // Decode Base64 data
        const mimeType = base64String.split(',')[0].split(':')[1].split(';')[0]
        const ab = new ArrayBuffer(byteString.length)
        const ia = new Uint8Array(ab)
        for (let i = 0; i < byteString.length; i++) {
          ia[i] = byteString.charCodeAt(i)
        }
        return new File([ab], fileName, { type: mimeType })
      }
      // Convert Base64 back to File
      const decodedFile = base64ToFile(encodedFile, 'uploaded-file.jpg')
      // console.log(decodedFile)
      file = decodedFile
      // console.log('page search ไฟล์ที่อัปโหลด:', file)
      this.FileImage = file
      this.SearchImage = true
      this.stopLazyLoad = true
      this.isLoading = true
      var refCode = this.$route.query.ref_code
      if (this.$route.query.idcat !== '') {
        this.idCategory = this.$route.query.idcat
      } else {
        this.idCategory = ''
      }
      const formData = new FormData()
      if (localStorage.getItem('roleUser') !== null && localStorage.getItem('oneData') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var companyId = ''
        var sellerShop
        if (localStorage.getItem('SetRowCompany') !== null) {
          companyId = JSON.parse(Decode.decode(localStorage.getItem('SetRowCompany')))
        } else if (this.RoleUser === 'sale_order') {
          companyId = JSON.parse(localStorage.getItem('PartnerID'))
          sellerShop = JSON.parse(localStorage.getItem('ShopID'))
        } else if (this.RoleUser === 'sale_order_no_JV') {
          // companyId = JSON.parse(localStorage.getItem('partner_id'))
          sellerShop = JSON.parse(localStorage.getItem('ShopID'))
        }
        formData.append('role_user', dataRole.role)
        formData.append('company_id', dataRole.role === 'sale_order' ? companyId : companyId !== '' ? companyId.company.company_id : '-1')
        formData.append('seller_shop_id', dataRole.role === 'sale_order' || dataRole.role === 'sale_order_no_JV' ? sellerShop : '-1')
        formData.append('order_by_price', this.search_ADC_DESC !== '' ? this.search_ADC_DESC : '')
        formData.append('category', this.idCategory === '' || this.idCategory === undefined ? '' : this.idCategory)
        formData.append('status_product', '')
        formData.append('limit', 48)
        formData.append('page', this.page)
        formData.append('image', file)
      } else {
        formData.append('role_user', 'ext_buyer')
        formData.append('company_id', '-1')
        formData.append('seller_shop_id', '-1')
        formData.append('order_by_price', this.search_ADC_DESC !== '' ? this.search_ADC_DESC : '')
        formData.append('category', this.idCategory === '' || this.idCategory === undefined ? '' : this.idCategory)
        formData.append('status_product', '')
        formData.append('limit', 48)
        formData.append('page', this.page)
        formData.append('image', file)
      }
      await this.$store.dispatch('actionsSearchImage', formData)
      var response = await this.$store.state.ModuleHompage.stateSearchImage
      if (response.ok === 'y') {
        // this.overlay2 = false
        if (this.page === 1) {
        // ถ้าเป็นการค้นหาจากหน้าแรกให้เขียนทับข้อมูลใน productSearch
          this.$store.commit('openLoader')
          this.productSearch = []
          setTimeout(() => {
            this.productSearch = response.query_result.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
            if (this.search_ADC_DESC === 'ASC') {
              this.productSearch.sort((a, b) => a.net_price - b.net_price)
            } else if (this.search_ADC_DESC === 'DESC') {
              this.productSearch.sort((a, b) => b.net_price - a.net_price)
            }
            window.scrollTo(0, 0)
            this.$store.commit('closeLoader')
          }, 1000)
          // this.shopSearchShow = response.query_result_shop
        } else {
          // ถ้าเป็นหน้าถัดไปให้เพิ่มข้อมูลเข้าไปใน productSearch โดยใช้ spread operator
          this.productSearch = [
            ...this.productSearch, // ข้อมูลเดิม
            ...response.query_result.map((item, index) => ({
              ...item,
              link: this.path + 'DetailProduct/' + encodeURIComponent(item.name.replace(/\s/g, '-') + '-' + item.id),
              ref_code: refCode
            }))
          ]
          if (this.search_ADC_DESC === 'ASC') {
            this.productSearch.sort((a, b) => a.net_price - b.net_price)
          } else if (this.search_ADC_DESC === 'DESC') {
            this.productSearch.sort((a, b) => b.net_price - a.net_price)
          }
          this.isLoading = false
        }
        this.shopSearchShow = []
        this.shopToShow = []
        this.shopCount = this.shopSearchShow.length
        this.showSkeletonLoader = false
        if (response.query_result.length === 0 && this.page === 1) {
          this.productSearch = []
          this.shopSearchShow = []
          this.NoDataInSearch = true
          this.showSkeletonLoader = false
          this.isLoading = false
        }
        this.page++
        this.stopLazyLoad = false
        if (response.query_result.length === 0) {
          this.page = 1
          this.stopLazyLoad = true
          this.isLoading = false
        }
      }
    },
    checkDataChange () {
      const currentData = this.$route.params.data

      if (this.previousData !== currentData) {
        // console.log('ค่าของ params.data เปลี่ยนแปลง')
        this.page = 1
        // ทำสิ่งที่ต้องการเมื่อค่ามีการเปลี่ยนแปลง
        this.previousData = currentData // อัปเดตค่าก่อนหน้า
      }
    },
    loadData () {
      // if (this.isLoading || !this.hasMore) return
      // this.isLoading = true
      this.getResultSearchImage(this.FileImage)
    },
    scrollToTargetSection () {
      const target = this.$refs.targetSection // หา div ที่อยู่ล่างสุด
      if (target) {
        const rect = target.getBoundingClientRect() // ตรวจสอบตำแหน่งของ div
        const isVisible = rect.top <= window.innerHeight && rect.bottom >= 0 // ตรวจสอบว่า div อยู่ใน viewport หรือไม่

        if (isVisible && this.stopLazyLoad !== true) {
          this.loadData() // โหลดข้อมูลเพิ่มเติมเมื่อเลื่อนมาถึง div ล่างสุด
        }
      }
    },
    async ADCDESC () {
      if (this.selectPrice === 'lowToHigh') {
        this.search_SKU = ''
        this.search_ADC_DESC = 'ASC'
      } else if (this.selectPrice === 'HighToLow') {
        this.search_ADC_DESC = 'DESC'
        this.search_SKU = ''
      } else if (this.selectPrice === 'SKU: จากน้อยไปมาก') {
        this.search_ADC_DESC = ''
        this.search_SKU = 'ADC'
      } else if (this.selectPrice === 'SKU: จากมากไปน้อย') {
        this.search_ADC_DESC = ''
        this.search_SKU = 'DESC'
      } else {
        this.search_ADC_DESC = ''
      }
      this.$EventBus.$emit('searchdata')
    },
    gotoShopDetail (val) {
      // console.log('val', val)
      // const shopCleaned = val[0].name_th.replace(/\s/g, '-')
      // const shopCleaned = []
      // val.forEach((e, i) => {
      //   shopCleaned.push(val[i].name_th.replace(/\s/g, '-'))
      //   this.$router.push(`/shoppage/${shopCleaned[i]}-${val[i].id}`).catch(() => {})
      // })
      var shopName = encodeURIComponent(val.name_th.replace(/\s/g, '-'))
      this.$router.push(`/shoppage/${shopName}-${val.id}?page=1`).catch(() => {})
      // console.log('shopCleaned', `/shoppage/${val.name_th.replace(/\s/g, '-')}-${val.id}?page=1`)
    },
    gotoShowAllShop () {
      localStorage.setItem('AllShopSearch', Encode.encode(this.shopSearchShow))
      this.$router.push(`/search-shop/${this.textSearch}`).catch(() => {})
    }
  }
}
</script>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
@media screen and (min-width: 768px) {
  .breadcrumbsPadding {
    padding-left: 2.5%;
    padding-top: 1%;
    padding-bottom: 1%;
  }
}
@media screen and (min-width: 900px) and (max-width: 1280px) {
  .breadcrumbsPadding {
    padding-left: 4.2% !important;
    padding-top: 1.5% !important;
    padding-bottom: 1.5% !important;
  }
}
@media screen and (min-width: 1280px) and (max-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 5% !important;
    padding-top: 1% !important;
    padding-bottom: 1% !important;
  }
}
@media screen and (min-width: 1600px) {
  .breadcrumbsPadding {
    padding-left: 8.75% !important;
    padding-top: 12px !important;
    padding-bottom: 12px !important;
  }
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 0px 0px 0px 0px !important;
}
.container {
  max-width: 1250px !important;
}
.cardShop {
  background-color:white;
}
.cardShop:hover {
  transform: scale(1.02) !important;
  background-color:white;
  border:1px solid #BDE7D9;
  box-shadow: 0 4px 8px rgba(167, 230, 213, 0.711) !important;
}
.v-btn-toggle--group > .v-btn.v-btn {
  background-color: transparent !important;
  border-color: transparent;
  margin: 0px !important;
  min-width: auto;
}
</style>
