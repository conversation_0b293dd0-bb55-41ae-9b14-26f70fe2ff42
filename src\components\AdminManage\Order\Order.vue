<template>
  <v-container :class="MobileSize ? 'mt-3' : 'mx-0'">
    <!-- <v-container grid-list-xs> -->
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title
        v-if="!MobileSize"
        style="
          font-weight: bold;
          font-size: 24px;
          line-height: 32px;
          color: #333333;
        "
        >รายการสั่งซื้อ</v-card-title
      >
      <v-card-title
        v-else
        class="px-0"
        style="
          font-weight: bold;
          font-size: 18px;
          line-height: 32px;
          color: #333333;
        "
        ><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()"
          >mdi-chevron-left</v-icon
        >
        รายการสั่งซื้อ</v-card-title
      >
      <!-- <v-overlay :value="overlay">
        <v-progress-circular indeterminate size="64"></v-progress-circular>
      </v-overlay> -->
      <v-row dense class="px-1">
        <!-- <v-col cols="12" class="px-2 py-0">
          <a-tabs @change="SelectDetailOrder">
            <a-tab-pane key="ทั้งหมด"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countOrderAll }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ยังไม่ชำระเงิน"><span slot="tab">ยังไม่ชำระเงิน <a-tag color="#E9A016" style="border-radius: 8px;">{{ countOrderNotpaid }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ชำระเงินสำเร็จ"><span slot="tab">ชำระเงินสำเร็จ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countOrderSuccess }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ชำระเงินไม่สำเร็จ"><span slot="tab">ชำระเงินไม่สำเร็จ <a-tag color="#D1392B" style="border-radius: 8px;">{{ countOrderFail }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ชำระเงินแบบเครดิตเทอม"><span slot="tab">ชำระเงินแบบเครดิตเทอม <a-tag color="#1B5DD6" style="border-radius: 8px;">{{ countOrderCreditTerm }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ยกเลิก"><span slot="tab">ยกเลิก <a-tag color="#f50" style="border-radius: 8px;">{{ countOrderCancel }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col> -->
        <v-col
          v-if="true"
          cols="12"
          md="8"
          sm="12"
          class=""
          :class="!MobileSize ? 'pl-2 pt-0 pt-6' : 'pl-2 pr-2 mb-3'"
        >
          <v-text-field
            v-model="search"
            dense
            hide-details
            outlined
            style="border-radius: 8px;"
            placeholder="ค้นหาจากรหัสการสั่งซื้อ"
            @keyup.enter="ListDataTable(); option.page = 1"
          >
            <v-icon slot="append" color="#CCCCCC">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col
          cols="12"
          md="4"
          sm="12"
          class=""
          :class="!MobileSize ? 'pl-2 pt-0' : ''"
        >
          <v-row dense :class="MobileSize ? 'pt-1' : 'pt-6'">
            <v-col cols="4" class="pt-3">
              <!-- <span style="font-size: 16px; line-height: 24px; color: #333333"
                >Pay Type</span
              > -->
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >สถานะรายการ :</span
              >
            </v-col>
            <v-col cols="8">
              <v-select
                v-model="statusSelect"
                :items="statusItem"
                item-text="text"
                item-value="value"
                outlined
                style="border-radius: 8px;"
                class="setCustomSelect"
                dense
                append-icon="mdi-chevron-down"
                hide-details
              ></v-select>
              <!-- <v-select
                v-model="PayTypeSelect"
                :items="payTypeItem"
                item-text="text"
                item-value="value"
                @change="ListDataTable()"
                outlined
                dense
              ></v-select> -->
            </v-col>
            <!-- <v-col cols="6">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >ใบกำกับภาษี</span
              >
              <v-select
                disabled
                v-model="InvoiceSelect"
                :items="invoiceItem"
                item-text="text"
                item-value="value"
                @change="ListDataTable()"
                outlined
                dense
              ></v-select>
            </v-col> -->
          </v-row>
        </v-col>
      </v-row>
      <v-row dense class="px-1">
        <!-- <v-col
          cols="12"
          md="12"
          sm="12"
          class=""
          :class="!MobileSize ? 'pl-2 pt-0' : 'pl-2 pr-2 mb-3'"
        > -->
          <!-- <v-row dense> -->
          <v-col cols="12" md="4" sm="12">
            <v-row dense class="pt-5">
              <v-col cols="3" class="pt-3">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                >Pay Type : </span>
              </v-col>
              <v-col cols="9">
                <v-select
                  v-model="PayTypeSelect"
                  :items="payTypeItem"
                  item-text="text"
                  item-value="value"
                  @change="ListDataTable(); option.page = 1;"
                  class="setCustomSelect"
                  outlined
                  style="border-radius: 8px;"
                  dense
                  append-icon="mdi-chevron-down"
                ></v-select>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="12">
            <v-row dense :class="MobileSize ? '' : 'pt-5'">
              <v-col cols="3" class="pt-3">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >วันที่สั่งซื้อ :</span
                >
              </v-col>
              <v-col cols="9">
                <v-dialog
                  ref="dialogBuyDate"
                  v-model="modalBuyDate"
                  :return-value.sync="date"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="buyDate"
                      v-bind="attrs"
                      v-on="on"
                      outlined
                      style="border-radius: 8px;"
                      dense
                      @change="ListDataTable()"
                      placeholder="วว/ดด/ปปปป"
                      ><v-icon slot="append" color="#CCCCCC"
                        >mdi-calendar-multiselect</v-icon
                      ></v-text-field
                    >
                  </template>
                  <v-date-picker
                    color="#27AB9C"
                    v-model="date"
                    scrollable
                    reactive
                    locale="Th-th"
                    @change="setValueBuyDate(date)"
                    :max="
                      new Date(
                        Date.now() - new Date().getTimezoneOffset() * 60000
                      )
                        .toISOString()
                        .substr(0, 10)
                    "
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="primary" @click="closeModalBuyDate(); option.page = 1;">
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      :disabled="buyDate === ''"
                      @click="$refs.dialogBuyDate.save(date); ListDataTable(); option.page = 1;"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4" sm="12">
            <v-row dense :class="MobileSize ? '' : 'pt-5'">
              <v-col cols="3" class="pt-3">
                <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >วันที่อนุมัติ : </span
                >
              </v-col>
              <v-col cols="9">
                <v-dialog
                  ref="dialogAcceptDate"
                  v-model="modalAcceptDate"
                  :return-value.sync="date1"
                  persistent
                  width="290px"
                >
                  <template v-slot:activator="{ on, attrs }">
                    <v-text-field
                      readonly
                      v-model="acceptDate"
                      v-bind="attrs"
                      v-on="on"
                      style="border-radius: 8px;"
                      outlined
                      @change="ListDataTable(); option.page = 1;"
                      dense
                      placeholder="วว/ดด/ปปปป"
                      ><v-icon slot="append" color="#CCCCCC"
                        >mdi-calendar-multiselect</v-icon
                      ></v-text-field
                    >
                  </template>
                  <v-date-picker
                    color="#27AB9C"
                    v-model="date1"
                    scrollable
                    reactive
                    locale="Th-th"
                    @change="setValueAcceptDate(date1);"
                    :max="
                      new Date(
                        Date.now() - new Date().getTimezoneOffset() * 60000
                      )
                        .toISOString()
                        .substr(0, 10)
                    "
                  >
                    <v-spacer></v-spacer>
                    <v-btn text color="primary" @click="closeModalAcceptDate()">
                      ยกเลิก
                    </v-btn>
                    <v-btn
                      text
                      color="primary"
                      :disabled="acceptDate === ''"
                      @click="$refs.dialogAcceptDate.save(date1); ListDataTable(); option.page = 1;"
                    >
                      ตกลง
                    </v-btn>
                  </v-date-picker>
                </v-dialog>
              </v-col>
            </v-row>
          </v-col>
          <!-- <v-col cols="6" md="3" sm="6">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่เริ่มสัญญา</span
              >
              <v-dialog
                ref="dialogContractStartDate"
                v-model="modalContractStartDate"
                :return-value.sync="date2"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="contractStartDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    @change="ListDataTable()"
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#27AB9C"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="date2"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueContractStartDate(date2)"
                  :max="
                    new Date(
                      Date.now() - new Date().getTimezoneOffset() * 60000
                    )
                      .toISOString()
                      .substr(0, 10)
                  "
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModalContractStartDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.dialogContractStartDate.save(date2);ListDataTable()"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="6" md="3" sm="6">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่สิ้นสุดสัญญา</span
              >
              <v-dialog
                ref="dialogContractEndDate"
                v-model="modalContractEndDate"
                :return-value.sync="date3"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    :disabled="searchContractStartDate !== '' ? false : true"
                    readonly
                    v-model="contractEndDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    @change="ListDataTable()"
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#27AB9C"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="date3"
                  scrollable
                  reactive
                  locale="Th-th"
                  @change="setValueContractEndDate(date3)"
                  :min="searchContractStartDate"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModalContractEndDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="$refs.dialogContractEndDate.save(date3);ListDataTable()"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col> -->
          <!-- </v-row> -->
        <!-- </v-col> -->
      </v-row>
      <v-row dense class="px-2">
        <v-col cols="12" md="8" sm="12">
          <v-row dense>
            <v-col cols="4" md="2" sm="3" class="pt-3 pl-0">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                  >วันที่รอบบริการ : </span
                >
            </v-col>
            <v-col cols="8" md="10" sm="9" :class="MobileSize? 'px-0':''">
              <v-dialog
                ref="modalRangeDate"
                v-model="modalRangeDate"
                :return-value.sync="dateRange"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="RangeDate1"
                    v-bind="attrs"
                    v-on="on"
                    style="border-radius: 8px;"
                    outlined
                    dense
                    placeholder="วว/ดด/ปปปป - วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#CCCCCC"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <!-- :allowed-dates='(d) => new Date(d) >= new Date(dateRange[0])' -->
                <v-date-picker
                  color="#27AB9C"
                  v-model="dateRange"
                  scrollable
                  range
                  reactive
                  locale="Th-th"
                >
                  <v-spacer></v-spacer>
                  <v-btn text color="primary" @click="CloseModalRangeDate(); option.page = 1;">
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    :disabled="RangeDate1.length === 0 && dateRange.length === 0"
                    @click="setValueRangeDate(dateRange); option.page = 1;"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="4" sm="12" class="pr-0">
          <v-row dense>
            <v-col cols="2" class="pt-3" :class="MobileSize? 'px-0':''">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
              >WHT : </span>
            </v-col>
            <v-col cols="10">
              <v-select
                v-model="PayTypeSelect"
                :items="payTypeItem"
                item-text="text"
                item-value="value"
                @change="ListDataTable(); option.page = 1;"
                outlined
                class="setCustomSelect"
                style="border-radius: 8px;"
                dense
                append-icon="mdi-chevron-down"
              ></v-select>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row dense class="px-2" >
        <v-col cols="12" md="6" sm="12" class="py-3">
          <v-row dense :class="MobileSize ? 'pb-4' : 'pt-0 pb-4'">
            <v-col :cols="MobileSize? '5': '4'" :class="MobileSize? 'px-0':'pt-3'">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >สถานะการชำระเงิน :</span
              >
            </v-col>
            <v-col :cols="MobileSize? '7': '8'" :class="MobileSize? 'pr-0':''">
              <v-select
                v-model="selectPaymentStatus"
                :items="statusPaymentStatusItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                dense
                class="setCustomSelect"
                @change="ListDataTable(); option.page = 1;"
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="6" sm="12" class="py-3">
          <v-row dense :class="MobileSize ? 'pb-4' : 'pt-0 pb-4'">
            <v-col :cols="MobileSize? '5': '4'" :class="MobileSize? 'px-0':'pt-3'">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  color: #333333;
                  font-weight: 400;
                "
                >สถานะการอนุมัติใบเสนอราคา :</span
              >
            </v-col>
            <v-col :cols="MobileSize? '7': '8'" :class="MobileSize? 'pr-0':''">
              <v-select
                v-model="selectApproveStatus"
                :items="statusApproveStatusItem"
                item-text="text"
                item-value="value"
                append-icon="mdi-chevron-down"
                outlined
                dense
                class="setCustomSelect"
                @change="ListDataTable(); option.page = 1;"
                style="border-radius: 8px;"
                hide-details
              ></v-select>
            </v-col>
          </v-row>
        </v-col>
      </v-row>
      <v-row dense>
        <v-col
          v-if="true"
          cols="12"
          md="12"
          sm="12"
          class=""
          :class="!MobileSize ? 'pl-3 pr-3 mb-0' : 'pl-2'"
        >
          <v-row dense>
            <v-col cols="12" md="4" sm="12" align="start">
              <span
                style="
                  font-size: 16px;
                  line-height: 24px;
                  align-items: center;
                  color: #333333;
                  font-weight: 400;
                "
                class="pt-2"
                >รายการสั่งซื้อสินค้า {{ showCountOrder }} รายการ</span
              >
              <!-- <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ยังไม่ชำระเงิน'">รายการสั่งซื้อที่ยังไม่ชำระเงินทั้งหมด {{ showCountOrder }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ชำระเงินสำเร็จ'">รายการสั่งซื้อที่ชำระเงินสำเร็จทั้งหมด {{ showCountOrder }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ชำระเงินไม่สำเร็จ'">รายการสั่งซื้อที่ชำระเงินไม่สำเร็จทั้งหมด {{ showCountOrder }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ชำระเงินแบบเครดิตเทอม'">รายการสั่งซื้อที่ชำระเงินแบบเครดิตเทอมทั้งหมด {{ showCountOrder }} รายการ</span>
              <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ยกเลิก'">รายการสั่งซื้อที่ยกเลิกทั้งหมด {{ showCountOrder }} รายการ</span> -->
            </v-col>
            <v-col cols="12" md="8" sm="12" align="end" class="pt-3">
            <v-btn @click="reSetSearch()" :class="MobileSize ? 'white--text mr-2 mb-3' : 'white--text mr-2'" :block="MobileSize" rounded color="#27AB9C" width="100" height="40"
              ><v-icon small class="mr-1">mdi-restart</v-icon
              >ล้างค่า</v-btn
            >
            <v-btn @click="downloadExcel()" :class="MobileSize ? 'mb-3' : ''" rounded width="153" height="40" outlined color="#27AB9C" :block="MobileSize" :disabled="showCountOrder === 0">
              <v-img :src="require('@/assets/ImageINET-Marketplace/ICONShop/file-export-solid.png')" max-width="24" max-height="24"></v-img>Export Excel</v-btn>
            <v-btn elevation="0" @click="CheckStockBeforeOpenModalPayment(selected[0].order_number)" :block="MobileSize" :disabled="selected.length === 0" rounded color="#27AB9C" height="40" :class="MobileSize ? 'white--text' : 'white--text ml-2'">
              <v-icon>mdi-cash-multiple</v-icon>
              <span>ชำระเงิน</span>
            </v-btn>
          </v-col>
            <!-- <v-col cols="12" md="6" sm="6" align="end">
              <v-row dense justify="end">
                <span
                  style="
                    font-size: 16px;
                    line-height: 24px;
                    color: #333333;
                    font-weight: 400;
                  "
                  class="pt-2 pr-2"
                  >สถานะรายการ :</span
                >
                <v-select
                  v-model="statusSelect"
                  :items="statusItem"
                  item-text="text"
                  item-value="value"
                  outlined
                  dense
                  style="max-width: 217px"
                  hide-details
                ></v-select>
              </v-row>
            </v-col> -->
          </v-row>
        </v-col>
        <v-col cols="12">
          <v-card
            v-if="disableTable === true"
            outlined
            class="small-card mx-2 my-5"
            min-height="436"
          >
            <v-data-table
              :headers="headersAll"
              :items="newsListShow"
              :search="search"
              :options.sync="option"
              style="width: 100%"
              height="100%"
              @pagination="countOrdar"
              :items-per-page="option.itemsPerPage"
              class="shop-table"
              no-results-text="ไม่พบรหัสการสั่งซื้อที่ค้นหา"
              :footer-props="{ 'items-per-page-options': [5, 10, 15, 50, 100], 'items-per-page-text': 'จำนวนแถว' }"
              show-select
              v-model="selected"
              item-key="payment_id_value"
              :server-items-length="maxItem"
              @update:options="updateOptions"
            >
              <template v-slot:[`header.data-table-select`]></template>
              <template v-slot:[`item.data-table-select`]="{isSelected, item, select}">
                <!-- <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-if="(selected.length === 0 && item.transaction_status === 'Not Paid') && (item.sale_order === 'yes' || item.shop_approve === 'approve') && (item.transaction_status === 'Not Paid' || item.transaction_status === 'Cancel' || item.transaction_status === 'Fail') || isSelected" color="#27AB9C"></v-simple-checkbox> -->
                <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status_term !== 'Success' ? isSelected : null" :ripple="false" v-if="item.payment_transaction_status_term !== 'Success' && (item.sale_order === 'yes' || (item.status_approve === 'Approve')) && (item.pay_type === 'recurring') && item.payment_credit_term_number !== '-' || isSelected" color="#27AB9C" :disabled="isSelectionRestricted(item, isSelected)"></v-simple-checkbox>
                <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-else-if="item.transaction_status !== 'Success' && (item.sale_order === 'yes' || (item.status_approve === 'Approve')) && (item.pay_type !== 'recurring') && item.payment_credit_term_number !== '-' || isSelected" color="#27AB9C" :disabled="isSelectionRestricted(item, isSelected)"></v-simple-checkbox>
                <!-- <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-if="(item.transaction_status === 'Not Paid') && (item.payment_transaction_status_term === '-' ? item.transaction_status === 'Not Paid' : item.payment_transaction_status_term === 'Not Paid') && (item.sale_order === 'yes' || item.shop_approve === 'approve') && (item.transaction_status === 'Not Paid' || item.transaction_status === 'Cancel' || item.transaction_status === 'Fail') || isSelected" color="#27AB9C" :disabled="isSelectionRestricted(item, isSelected)"></v-simple-checkbox> -->
                <!-- <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-if="(selected.length === 0 && item.transaction_status === 'Not Paid') && (selected.length === 0 && item.payment_transaction_status_term === '-' ? item.transaction_status === 'Not Paid' : item.payment_transaction_status_term === 'Not Paid') && (item.sale_order === 'yes' || item.shop_approve === 'approve') && (item.transaction_status === 'Not Paid' || item.transaction_status === 'Cancel' || item.transaction_status === 'Fail') || isSelected" color="#27AB9C"></v-simple-checkbox> -->
                <!-- <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-if="(selected.length === 0 && item.transaction_status === 'Not Paid') || isSelected" color="#27AB9C"></v-simple-checkbox> -->
                <!-- <v-simple-checkbox @input="select($event)" :value="item.payment_transaction_status !== 'ชำระเงินสำเร็จ' ? isSelected : null" :ripple="false" v-if="(selected.length === 0 && item.transaction_status === 'Not Paid') || isSelected" color="#27AB9C" :disabled="item.transaction_status !== 'Not Paid'"></v-simple-checkbox> -->
              </template>
              <template v-slot:[`item.created_at`]="{ item }">
                {{
                  new Date(item.created_at).toLocaleDateString('th-TH', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })
                }}
              </template>
              <template v-slot:[`item.paid_datetime`]="{ item }">
                {{
                  item.paid_datetime === '-'
                    ? item.paid_datetime
                    : new Date(item.paid_datetime).toLocaleDateString('th-TH', {
                        year: 'numeric',
                        month: 'long',
                        day: 'numeric',
                        hour: 'numeric',
                        minute: 'numeric'
                      })
                }}
              </template>
              <template v-slot:[`item.payment_transaction_number`]="{ item }">
                <div v-if="item.QT_order !== '-' && item.transaction_status === 'Success'">
                  <a style="border-bottom: solid 1px #1B5DD6; color:#1B5DD6;" @click="orderDetail(item, 'QT')">{{item.payment_transaction_number}}</a>
                </div>
                <div v-else>
                  {{ item.payment_transaction_number }}
                </div>
              </template>
              <template v-slot:[`item.receipt_number`]="{ item }">
                <!-- <div v-if="item.receipt_number !== '-'">
                  <a @click="orderDetail(item)">{{
                    item.receipt_number
                  }}</a>
                </div> -->
                <div>
                  {{ item.receipt_number !== null && item.receipt_number !== '-' ? item.receipt_number: '-' }}
                </div>
              </template>
              <template
                v-slot:[`item.payment_transaction_number_icon`]="{ item }"
              >
                <div v-if="item.QT_order !== '-' && item.transaction_status === 'Success'">
                  <a @click="orderDetail(item, 'QT')">
                    <v-btn
                      x-small
                      style="
                        border: 1px solid #f2f2f2;
                        box-sizing: border-box;
                        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                      "
                      :style="
                        IpadProSize
                          ? 'max-width: 24px; max-height: 24px;'
                          : IpadSize
                          ? 'max-width: 16px; max-height: 16px;'
                          : 'max-width: 32px; max-height: 32px;'
                      "
                      class="pt-4 pb-4"
                    >
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn>
                  </a>
                </div>
                <div v-else>
                  <span>{{ '-' }}</span>
                </div>
              </template>
              <template
                v-slot:[`item.pr_document_id`]="{ item }"
              >
                <div v-if="item.pr_document_id !== '-' && item.PR_External !== '-'">
                  <a @click="orderDetail(item, 'PR')">
                    <!-- <v-btn
                      x-small
                      style="
                        border: 1px solid #f2f2f2;
                        box-sizing: border-box;
                        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                      "
                      :style="
                        IpadProSize
                          ? 'max-width: 24px; max-height: 24px;'
                          : IpadSize
                          ? 'max-width: 16px; max-height: 16px;'
                          : 'max-width: 32px; max-height: 32px;'
                      "
                      class="pt-4 pb-4"
                    >
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn> -->
                  <span style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{item.pr_document_id}}</span>
                  </a>
                </div>
                <div v-else-if="item.pr_document_id !== '-' && item.PR_External === '-'">
                  <span >{{item.PR_External}}</span>
                </div>
                <div v-else>
                  <span>{{ '-' }}</span>
                </div>
              </template>
              <template v-slot:[`item.po_document_id`]="{ item }" >
                <div v-if="item.po_document_id !== '-' && item.PO_External !== '-'">
                  <a @click="orderDetail(item, 'PO')">
                    <!-- <v-btn
                      x-small
                      style="
                        border: 1px solid #f2f2f2;
                        box-sizing: border-box;
                        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                      "
                      :style="
                        IpadProSize
                          ? 'max-width: 24px; max-height: 24px;'
                          : IpadSize
                          ? 'max-width: 16px; max-height: 16px;'
                          : 'max-width: 32px; max-height: 32px;'
                      "
                      class="pt-4 pb-4"
                    >
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn> -->
                    <span style="font-size: 16px; font-weight: 400; color: #1B5DD6; text-decoration: underline; cursor: pointer;">{{item.po_document_id}}</span>
                  </a>
                </div>
                <div v-else-if="item.po_document_id !== '-' && item.PO_External === '-'">
                  <span>{{item.po_document_id}}</span>
                </div>
                <div v-else>
                  <span>{{ '-' }}</span>
                </div>
              </template>
              <template v-slot:[`item.order_mobilyst_no`]="{ item }">
                <div v-if="item.order_mobilyst_no  !== '-' && (item.url_tracking  !== '-' && item.url_tracking  !== '')">
                  <a target="_blank" :href="item.url_tracking"> <span style="border-bottom: solid 1px #1B5DD6; color:#1B5DD6;">{{item.order_mobilyst_no}}</span></a>
                </div>
                <div v-else-if="item.order_mobilyst_no !== '-' && item.order_mobilyst_no !== '' && (item.url_tracking === '-' || item.url_tracking === '')">
                  <span ref="trackNumber" >
                    {{item.order_mobilyst_no}}
                    <v-tooltip :color="changeCol? '#27AB9C73':'#33333373'" bottom>
                      <template v-slot:activator="{ on, attrs }">
                        <v-icon
                          class="copySTY"
                          :color="changeCol? '#27AB9C' :''"
                          v-bind="attrs"
                          v-on="on"
                          @click="copyText(item.order_mobilyst_no)"
                        >mdi-content-copy</v-icon>
                      </template>
                      <!-- <v-icon v-if="changeCol" color="#27AB9C">mdi-check</v-icon> -->
                      <span>copy</span>
                    </v-tooltip>
                  </span>
                </div>
                <div v-else>
                  <span>{{(item.url_tracking  === '-' || item.url_tracking  === '') && item.order_mobilyst_no !== '-'? item.order_mobilyst_no: '-'}}</span>
                </div>
              </template>
              <template v-slot:[`item.pv_no`]="{ item }">
                <v-chip v-if="item.pv_no !== '-'" outlined color="blue" @click="openModaleWHT(item.pv_no)">ดูรายละเอียด</v-chip>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.transportation_status`]="{ item }">
                <div v-if="item.transportation_status !== '-' && item.detail_status === '-'">
                <v-chip v-if="item.transportation_status === 'การจัดส่งสำเร็จ'" color="#F0F9EE" text-color="#1AB759" >ยอมรับสินค้าแล้ว</v-chip>
                <v-chip v-else-if="item.transportation_status === 'ส่งคืนสินค้า'" color="#F5222D1A" text-color="#F5222D" >{{item.transportation_status}}</v-chip>
                <v-chip v-else-if="item.transportation_status === 'อยู่ระหว่างการขนส่ง'" text-color="#2A70C3" color="#DBECFA">{{item.transportation_status}}</v-chip>
                <!-- <v-chip v-else-if="item.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'" color="#FAAD14">{{item.transportation_status}}</v-chip> -->
                <!-- <v-chip v-else-if="item.transportation_status === 'ส่งคืนสินค้า'" color="#F5222D1A">{{item.transportation_status}}</v-chip> -->
                <span v-else-if="item.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'" rounded small class="white--text" color="#27AB9C"><b class="buttonFontSize">-</b></span>
                <!-- <v-btn v-else-if="item.transportation_status === 'อยู่ระหว่างการตรวจสอบและยอมรับสินค้า'" @click="CheckAcceptProductData(item)" rounded small class="white--text" color="#27AB9C"><b class="buttonFontSize">ได้รับสินค้าแล้ว</b></v-btn> -->
                </div>
                <!-- <v-chip v-if="item.transportation_status !== '-' && item.detail_status === '-'" :color="item.transportation_status === 'การจัดส่งสำเร็จ'? '#F0FEE8':item.transportation_status === 'ส่งคืนสินค้า'? '#F5222D1A':item.transportation_status === 'อยู่ระหว่างการขนส่ง'? '#DBECFA': '#FEF6E6'" :text-color="item.transportation_status === 'การจัดส่งสำเร็จ'?'#1AB759':item.transportation_status === 'ส่งคืนสินค้า'? '#F5222D':item.transportation_status === 'อยู่ระหว่างการขนส่ง'? '#2A70C3':'#FAAD14'">{{item.transportation_status}}</v-chip> -->
                <v-chip v-else-if="item.transportation_status !== '-' && item.detail_status !== '-'" :color="item.detail_status === 'ยกเลิกคำสั่งซื้อ'? '#F7D9D9':''" :text-color="item.detail_status === 'ยกเลิกคำสั่งซื้อ'? '#D1392B':''">{{item.detail_status}}</v-chip>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.order_type`]="{ item }">
                <div v-if="item.pay_type === 'general'">
                  <v-chip v-if="item.payment_transaction_status_term === '-' ? item.transaction_status !== 'Success' : item.payment_transaction_status_term !== 'Success'" color="#F0F9EE" text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                  <v-chip v-else-if="item.sale_order === 'no' && item.shop_approve ==='waiting_approve'" color="#FCF0DA" text-color="#E9A016">รอร้านค้าอนุมัติคำสั่งซื้อ</v-chip>
                  <v-chip v-else-if="item.sale_order === 'no' && item.shop_approve ==='reject'" color="#F7D9D9" text-color="#D1392B">ร้านค้ายกเลิกคำสั่งซื้อ</v-chip>
                  <v-chip v-else-if="(item.sale_order === 'yes' || item.shop_approve === 'approve') && item.transaction_status !== 'Success' && item.detail_status === '-'" color="#FCF0DA" text-color="#E9A016">ยังไม่ชำระเงิน</v-chip>
                  <!-- <v-chip v-else-if="item.transaction_status !== 'Success' && item.detail_status === '-'" color="#FCF0DA" text-color="#E9A016">ยังไม่ชำระเงิน</v-chip> -->
                  <v-chip v-else-if="item.detail_status !== '-'" color="#F7D9D9" text-color="#D1392B">{{item.detail_status}}</v-chip>
                </div>
                <div v-else>
                  <v-chip v-if="item.payment_transaction_status_term === '-' ? item.transaction_status !== 'Success' : item.payment_transaction_status_term !== 'Success' && item.detail_status === '-'" color="#FCF0DA" text-color="#E9A016">ยังไม่ชำระเงิน</v-chip>
                  <v-chip v-else-if="item.detail_status !== '-'" color="#F7D9D9" text-color="#D1392B">{{item.detail_status}}</v-chip>
                  <v-chip v-else color="#F0F9EE"  text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
                </div>
              </template>
              <template v-slot:[`item.payment_transaction_status`]="{ item }">
                <v-chip v-if="item.payment_transaction_status_term === '-' ? item.transaction_status !== 'Success' : item.payment_transaction_status_term !== 'Success'" color="#FCF0DA" text-color="#E9A016">ยังไม่ชำระเงิน</v-chip>
                <!-- <v-chip v-else-if="item.transaction_status !== 'Success' && item.detail_status !== '-'" color="#F7D9D9" text-color="#D1392B">{{item.detail_status}}</v-chip> -->
                <v-chip v-else color="#F0F9EE"  text-color="#1AB759">ชำระเงินสำเร็จ</v-chip>
              </template>
              <template v-slot:[`item.pay_type`]="{ item }">
                <v-chip v-if="item.pay_type === 'onetime'" text-color="#1B5DD6" color="rgba(27, 93, 214, 0.10)">One Time</v-chip>
                <v-chip v-else-if="item.pay_type === 'recurring'" text-color="#FF710B" color="rgba(255, 113, 11, 0.10)">Recurring</v-chip>
                <v-chip v-else-if="item.pay_type === 'general'" text-color="#808B96" color="#F4F6F6">General</v-chip>
                <span v-else>-</span>
              </template>
              <template v-slot:[`item.days_overdue`]="{ item }">
                <span>{{item.pay_type === 'recurring' ? item.days_overdue : '-'}}</span>
              </template>
              <template v-slot:[`item.transaction_code_icon`]="{ item }">
                <div
                  v-if="
                    item.transaction_code !== '-' &&
                    item.required_invoice !== '-'
                  "
                >
                  <!-- <a :disabled="checkETax" > -->
                  <v-btn
                    @click="GetETaxPDF(item)"
                    x-small
                    style="
                      border: 1px solid #f2f2f2;
                      box-sizing: border-box;
                      box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                      border-radius: 4px;
                    "
                    :style="
                      IpadProSize
                        ? 'max-width: 24px; max-height: 24px;'
                        : IpadSize
                        ? 'max-width: 16px; max-height: 16px;'
                        : 'max-width: 32px; max-height: 32px;'
                    "
                    class="pt-4 pb-4"
                  >
                    <v-icon color="#27AB9C">mdi-file-document</v-icon>
                  </v-btn>
                  <!-- </a> -->
                </div>
                <div
                  v-else-if="
                    item.transaction_code === '-' &&
                    item.required_invoice !== '-'
                  "
                >
                  <span>{{ item.required_invoice }}</span>
                </div>
                <div v-else>
                  <span>{{ '-' }}</span>
                </div>
              </template>
              <template
                v-slot:[`item.QT_order_invoice`]="{ item }"
              >
                <div v-if="item.QT_order_invoice !== '-'">
                  <a @click="orderDetail(item, 'QT_order')">
                    <v-btn
                      x-small
                      style="
                        border: 1px solid #f2f2f2;
                        box-sizing: border-box;
                        box-shadow: 0px 0px 4px rgba(0, 0, 0, 0.04);
                        border-radius: 4px;
                      "
                      :style="
                        IpadProSize
                          ? 'max-width: 24px; max-height: 24px;'
                          : IpadSize
                          ? 'max-width: 16px; max-height: 16px;'
                          : 'max-width: 32px; max-height: 32px;'
                      "
                      class="pt-4 pb-4"
                    >
                      <v-icon color="#27AB9C">mdi-file-document</v-icon>
                    </v-btn>
                  </a>
                </div>
                <div v-else>
                  <span>-</span>
                </div>
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                {{
                  Number(item.total_amount).toLocaleString(undefined, {
                    minimumFractionDigits: 2
                  })
                }}
              </template>
              <template v-slot:[`item.payment`]="{ item }">
                <v-row justify="center">
                  <v-btn
                    v-if="item.seller_sent_status === 'Success'"
                    text
                    disabled
                    rounded
                    color="#1AB759"
                    small
                    @click="GoToPayment(item)"
                  >
                    <b>จ่ายเงิน</b>
                    <v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                  <v-btn
                    v-else-if="item.seller_sent_status === 'cancel'"
                    text
                    disabled
                    rounded
                    color="#1AB759"
                    small
                    @click="GoToPayment(item)"
                  >
                    <b>จ่ายเงิน</b>
                    <v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                  <v-btn
                    v-else
                    text
                    rounded
                    color="#1AB759"
                    small
                    @click="GoToPayment(item)"
                  >
                    <b>จ่ายเงิน</b>
                    <v-icon small>mdi-chevron-right</v-icon>
                  </v-btn>
                </v-row>
              </template>
              <template v-slot:[`item.transaction_status`]="{ item }">
                <span
                  v-if="
                    item.transaction_status === 'Success' &&
                    item.seller_sent_status !== 'cancel'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F0F9EE"
                    text-color="#1AB759"
                    >ชำระเงินสำเร็จ</v-chip
                  >
                </span>
                <span
                  v-else-if="
                    item.seller_sent_status === 'cancel' ||
                    item.transaction_status === 'Cancel'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#f7c5ad"
                    text-color="#f50"
                    >ยกเลิกสินค้า</v-chip
                  >
                </span>
                <span v-else-if="item.transaction_status === 'Pending'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#E9A016"
                    >รออนุมัติ</v-chip
                  >
                </span>
                <span v-else-if="item.transaction_status === 'Approve'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F0F9EE"
                    text-color="#1AB759"
                    >อนุมัติ</v-chip
                  >
                </span>
                <span v-else-if="item.transaction_status === 'Credit'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#E5EFFF"
                    text-color="#1B5DD6"
                    >ชำระเงินแบบเครดิตเทอม</v-chip
                  >
                </span>
                <span v-else-if="item.transaction_status === 'Fail'">
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F7D9D9"
                    text-color="#D1392B"
                    >ชำระเงินไม่สำเร็จ</v-chip
                  >
                </span>
                <span v-else>
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#E9A016"
                    >ยังไม่ชำระเงิน</v-chip
                  >
                </span>
              </template>
              <template v-slot:[`item.buyer_received_status`]="{ item }">
                <v-row class="pt-5">
                  <v-select
                    v-model="item.buyer_received_status"
                    :items="receive_items"
                    item-text="text"
                    item-value="value"
                    @change="UpdateStatusBuyer(item)"
                    outlined
                    dense
                  ></v-select>
                </v-row>
              </template>
              <template v-slot:[`item.contract`]="{ item }">
                <v-row>
                  <v-col>
                    {{item.contract === '' ? '-' : '-'}}
                  </v-col>
                </v-row>
              </template>
              <template v-slot:[`item.detail`]="{ item }">
                <v-menu offset-y>
                  <template v-slot:activator="{ on, attrs }">
                    <v-btn
                      v-bind="attrs"
                      v-on="on"
                      class="pt-4 pb-4"
                      x-small
                      outlined
                      style="
                        max-width: 32px;
                        max-height: 32px;
                        border-radius: 4px;
                        border: 1px solid var(--neutral-f-2-f-2-f-2, #f2f2f2);
                        background: var(--neutral-ffffff, #fff);
                        box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.04);
                      "
                    >
                      <!-- <b>รายละเอียด</b> -->
                      <v-icon color="#27AB9C">mdi-dots-vertical</v-icon>
                    </v-btn>
                  </template>
                  <v-list>
                    <v-list-item
                      v-for="(items, index) in item.is_order_JV === 'yes' ? actionsItem : actionsItemNoJV"
                      :key="index"
                      link
                    >
                      <v-list-item-content
                        @click="gotoActions(item, items.value)"
                      >
                        <v-list-item-title>{{ items.text }}</v-list-item-title>
                      </v-list-item-content>
                    </v-list-item>
                  </v-list>
                </v-menu>
              </template>
              <template v-slot:[`item.date_qt_approve`]="{ item }">
                <span>{{item.date_qt_approve !== '-' ? new Date(item.date_qt_approve.substring(0, 10)).toLocaleDateString('th-TH', { calendar: 'gregory' }) : item.date_qt_approve}}</span>
              </template>
              <!-- <template v-slot:[`item.status_qt`]="{ item }">

                <span
                  v-if="
                    item.status_qt === null
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#E9A016"
                    >รออนุมัติ</v-chip
                  >
                </span>
                <span
                  v-if="
                    item.status_qt === 'reject'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F7D9D9"
                    text-color="#D1392B"
                    >ไม่อนุมัติ</v-chip
                  >
                </span>
                <span
                  v-if="
                    item.status_qt === 'approve'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F0F9EE"
                    text-color="#1AB759"
                    >อนุมัติ</v-chip
                  >
                </span>
              </template> -->
              <template v-slot:[`item.company_approve`]="{ item }">

                <span
                  v-if="
                    item.company_approve === 'waiting_approve' || item.company_approve === 'check_doc'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#E9A016"
                    >รออนุมัติ</v-chip
                  >
                </span>
                <span
                  v-if="
                    item.company_approve === 'waiting_company_approve'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#E9A016"
                    >รอผู้ซื้ออนุมัติ</v-chip
                  >
                </span>
                <span
                  v-if="
                    item.company_approve === 'waiting_shop_approve'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#FCF0DA"
                    text-color="#E9A016"
                    >รอร้านค้าอนุมัติ</v-chip
                  >
                </span>
                <span
                  v-if="
                    item.company_approve === 'reject'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F7D9D9"
                    text-color="#D1392B"
                    >ไม่อนุมัติ</v-chip
                  >
                </span>
                <span
                  v-if="
                    item.company_approve === 'approve'
                  "
                >
                  <v-chip
                    :class="!MobileSize ? 'ma-2' : 'ma-0'"
                    color="#F0F9EE"
                    text-color="#1AB759"
                    >อนุมัติ</v-chip
                  >
                </span>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img
              src="@/assets/emptypo.png"
              max-height="500px"
              max-width="500px"
              height="100%"
              width="100%"
              contain
              aspect-ratio="2"
            ></v-img>
          </div>
          <h2
            v-if="StateStatus === 'ทั้งหมด'"
            style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c"
          >
            <b>คุณยังไม่มีรายการสั่งซื้อสินค้า</b>
          </h2>
          <h2
            v-else
            style="padding-top: 20px; padding-bottom: 50px; color: #27ab9c"
          >
            <b>คุณยังไม่มีรายการสั่งซื้อสินค้าที่{{ StateStatus }}</b>
          </h2>
        </v-col>
      </v-row>
    </v-card>
    <v-dialog v-model="terminateModal" persistent width="564px">
      <v-card class="rounded-lg">
        <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">ยกเลิกการต่อสัญญา</font>
          </span>
          <v-btn icon dark @click="closeModalterminate()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-text class="pt-4">
          <v-row dense>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >วันที่มีผล Terminate</span
              >
              <v-dialog
                ref="dialogTerminate"
                v-model="modalTerminate"
                :return-value.sync="date4"
                persistent
                width="290px"
              >
                <template v-slot:activator="{ on, attrs }">
                  <v-text-field
                    readonly
                    v-model="TerminateDate"
                    v-bind="attrs"
                    v-on="on"
                    outlined
                    dense
                    placeholder="วว/ดด/ปปปป"
                    ><v-icon slot="append" color="#27AB9C"
                      >mdi-calendar-multiselect</v-icon
                    ></v-text-field
                  >
                </template>
                <v-date-picker
                  color="#27AB9C"
                  v-model="date4"
                  scrollable
                  reactive
                  locale="Th-th"
                  :min="minTerminate"
                >
                  <v-spacer></v-spacer>
                  <v-btn
                    text
                    color="primary"
                    @click="closeModalTerminateDate()"
                  >
                    ยกเลิก
                  </v-btn>
                  <v-btn
                    text
                    color="primary"
                    @click="setValueTerminateDate(date4)"
                  >
                    ตกลง
                  </v-btn>
                </v-date-picker>
              </v-dialog>
            </v-col>
            <v-col cols="12">
              <span style="font-size: 16px; line-height: 24px; color: #333333"
                >เหตุผล Terminate</span
              >
              <v-select
                v-model="reasonSelect"
                :menu-props="{ offsetY: true, overflowY: true }"
                placeholder="กรุณาเลือกเหตุผล Terminate"
                :items="reasonItems"
                item-text="text"
                item-value="value"
                dense
                outlined
              ></v-select>
            </v-col>
            <v-col cols="12" v-if="reasonSelect === '11'">
              <v-textarea
                v-model="reasonTerminate"
                auto-grow
                placeholder="ระบุเหตุผล Terminate"
                outlined
              ></v-textarea>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-actions>
          <v-row dense justify="center" class="mb-4">
            <v-btn
              outlined
              color="#27AB9C"
              height="40px"
              width="110px"
              @click="closeModalterminate()"
              >ยกเลิก</v-btn
            >
            <v-btn
              v-if="reasonSelect !== '11'"
              color="#27AB9C"
              class="ml-2 white--text"
              height="40px"
              width="110px"
              @click="confirmTerminate()"
              :disabled="
                reasonSelect !== '' && TerminateDate !== '' ? false : true
              "
              >บันทึก</v-btn
            >
            <v-btn
              v-if="reasonSelect === '11'"
              color="#27AB9C"
              class="ml-2 white--text"
              height="40px"
              width="110px"
              @click="confirmTerminate()"
              :disabled="
                reasonSelect !== '' && reasonTerminate !== '' ? false : true
              "
              >บันทึก</v-btn
            >
          </v-row>
        </v-card-actions>
      </v-card>
    </v-dialog>
    <v-dialog width="472px" v-model="dialogSuccess" persistent>
      <v-card class="rounded-lg">
         <v-toolbar align="center" color="#BDE7D9" dark dense elevation="0">
          <span class="flex text-center ml-5" style="font-size: 20px; font-weight: bold;">
            <font color="#27AB9C">ยกเลิกการต่อสัญญา</font>
          </span>
          <v-btn icon dark @click="closeModalterminate()">
            <v-icon color="#27AB9C">mdi-close</v-icon>
          </v-btn>
        </v-toolbar>
        <v-card-title>
          <v-row no-gutters class="mt-2 mb-4">
          <v-col cols="12" align="center">
            <svg class="checkmark"  viewBox="0 0 52 52">
              <circle class="checkmark__circle" cx="26" cy="26" r="25" fill="none"/>
              <path class="checkmark__check" fill="none" d="M14.1 27.2l7.1 7.2 16.7-16.8"/>
            </svg>
          </v-col>
          <v-col cols="12" align="center" class="mt-1">
              <span style="font-weight: 600;font-size: 18px;line-height: 26px;color: #27AB9C;">สำเร็จ</span>
          </v-col>
          </v-row>
        </v-card-title>
      </v-card>
    </v-dialog>
    <!-- Dialog e-WHT -->
    <v-dialog v-model="dialogeWHT" width="850px" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
      <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
        <v-card-text class="px-0 pb-0">
          <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 850px'" class="backgroundHead" style="position: absolute; height: 120px;">
            <v-row style="height: 120px;">
              <v-col style="text-align: center;" class="pt-4">
                <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>รายละเอียดข้อมูลรายการ</b></span>
              </v-col>
              <v-btn fab small @click="dialogeWHT = !dialogeWHT" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
            </v-row>
          </div>
          <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
            <v-row :width="MobileSize ? '100%' : '850px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
              <v-col style="text-align: center;">
              </v-col>
            </v-row>
          </div>
          <div class="backgroundContent" style="position: relative;">
            <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px 20px 0px 0px;" :style="MobileSize ? 'padding: 24px 10px 20px 10px;' : 'padding: 40px 48px 20px 48px;'">
              <v-card-text class="pa-0">
                <v-row dense style="color: #333333; font-size: 15px;">
                  <v-col cols="12" md="6" sm="6">
                    <span>เลขที่เอกสารใบสำคัญจ่าย : {{ dataeWHT.pv_no }}</span>
                  </v-col>
                  <v-col cols="12" md="6" sm="6">
                    <span>วันที่จ่ายเงิน : {{ new Date(pvDate).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric' }) }}</span>
                  </v-col>
                  <!-- รายละเอียดการชำระเงิน -->
                  <v-col cols="12" md="12" sm="12">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-size: 18px;"><b><v-icon color="#27AB9C">mdi-circle-medium</v-icon> รายละเอียดการชำระเงิน</b></span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>สถานะการชำระเงิน : {{ dataeWHTPayment.payment_status }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันที่/เวลาการชำระเงินเข้าสู่ระบบ e-WHT: {{ dataeWHTPayment.payment_date !== null ? new Date(dataeWHTPayment.payment_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- สถานะการโอนเงินไปยังปลายทาง -->
                  <v-col cols="12" md="12" sm="12">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-size: 18px;"><b><v-icon color="#27AB9C">mdi-circle-medium</v-icon> รายละเอียดสถานะการโอนเงิน</b></span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>สถานะการโอนเงินไปยังปลายทาง : {{ dataeWHTTransfer.transfer_status }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันที่/เวลาโอนเงินไปยังปลายทาง: {{ dataeWHTTransfer.transfer_date !== null ? new Date(dataeWHTTransfer.transfer_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!--  สถานะการส่งข้อมูลภาษีหัก ณ ที่จ่าย -->
                  <v-col cols="12" md="12" sm="12">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-size: 18px;"><b><v-icon color="#27AB9C">mdi-circle-medium</v-icon> สถานะการส่งข้อมูลภาษีหัก ณ ที่จ่าย</b></span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>สถานะการส่งข้อมูลภาษีหัก ณ ที่จ่าย : {{ dataeWHTCheck.wht_data }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันที่/เวลาการส่งข้อมูลภาษีหัก ณ ที่จ่าย : {{ dataeWHTCheck.wht_data_date !== null ? new Date(dataeWHTCheck.wht_data_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>สถานะการโอนเงิน wht ไปยังกรมสรรพากร : {{ dataeWHTCheck.transfer_rd }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันที่/เวลาการโอนเงิน wht ไปยังกรมสรรพากร : {{ dataeWHTCheck.transfer_rd_date !== null ? new Date(dataeWHTCheck.transfer_rd_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>สถานะการกระทบยอด : {{ dataeWHTCheck.reconcile }}</span>
                      </v-col>
                      <v-col cols="12" md="6" sm="6">
                        <span>วันที่/เวลาการกระทบยอด : {{ dataeWHTCheck.reconcile_date !== null ? new Date(dataeWHTCheck.reconcile_date).toLocaleDateString('th-TH', { year: 'numeric', month: 'numeric', day: 'numeric', hour: 'numeric', minute: 'numeric' }) : '-' }}</span>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogChooesPayType" persistent width="550">
      <v-card :height=" MobileSize || IpadSize ? '230':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="55px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/backgroundHead.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn color="#CCCCCC" icon
              @click="openDialogPayment()">
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-card-title style="place-content: center;" class="px-0">
          <span style="font-size: 20px;font-weight: 700; color: #FAAD14;">วิธีการชำระเงิน</span>
        </v-card-title>
        <v-card-text style="place-content: center;">
          <v-row justify="center" v-if="selected.length !== 0">
            <!-- {{selected[0].payment_method[2]}} -->
            <v-radio-group v-model="radioPayment" row class="ma-0 pa-0" v-if="selected.length !== 0">
              <v-radio value="radio-qr" v-if="selected[0].payment_method[0] === 'qrcode' || selected[0].payment_method[1] === 'qrcode' || selected[0].payment_method[2] === 'qrcode'"><template v-slot:label>
                  <span style="font-size: 16px;">QR Code</span>
                </template>
              </v-radio>
              <v-radio value="radio-credit" v-if="selected[0].payment_method[0] === 'creditcard' || selected[0].payment_method[1] === 'creditcard' || selected[0].payment_method[2] === 'creditcard'"><template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card / Debit Card</span>
                </template>
              </v-radio>
              <v-radio value="radio-installment" v-if="selected[0].payment_method[0] === 'installment' || selected[0].payment_method[1] === 'installment' || selected[0].payment_method[2] === 'installment'" >
                <template v-slot:label>
                  <span tyle="font-size: 16px;">Credit Card แบบผ่อนชำระ</span>
                </template>
              </v-radio>
              <v-radio value="radio-e-WHT" :disabled="!checkEWHT">
                <template v-slot:label>
                  <span :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">Transfer</span>
                </template>
              </v-radio>
            </v-radio-group>
          </v-row>
          <v-row no-gutters justify="center" class="mt-5" v-if="radioPayment === 'radio-installment'">
            <v-col :cols="MobileSize ? 6 : 4" class="pl-8">
              <span style="font-size: 16px;">ระยะเวลาผ่อนชำระ</span>
            </v-col>
            <v-col :cols="MobileSize ? 6 : 8" class="ma-0 pa-0" md="8" sm="6">
              <v-select style="border-radius: 8px;" outlined dense label="เลือกระยะเวลาผ่อนชำระ" v-model="radioCreditTerm" :items="filteredCreditTerms" item-text="displayText" item-value="value" >
                <template v-slot:append>
                  <v-icon>mdi-chevron-down</v-icon>
                </template>
                <template v-slot:no-data>
                  <v-list-item>
                    <v-list-item-content class="text-center">
                      <v-list-item-title>ไม่สามารถผ่อนชำระได้ เนื่องจากไม่ถึง <span style="color: #27AB9C;">'ขั้นต่ำ'</span> ที่กำหนดไว้</v-list-item-title>
                    </v-list-item-content>
                  </v-list-item>
                </template>
              </v-select>
            </v-col>
          </v-row>
        </v-card-text>
        <v-card-text >
          <v-row dense justify="center">
            <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4"
              @click="openDialogPayment()">ยกเลิก</v-btn>
            <v-btn :disabled="radioPayment === 'no' || radioPayment === 'radio-installment' && radioCreditTerm === ''" :width="MobileSize? '100':'156'" height="38" class="white--text " rounded color="#27AB9C"
              @click="confirmPayment()">ตกลง</v-btn>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-dialog v-model="DialogQR" persistent :width="MobileSize ? '100%' : '640'">
        <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden; ">
          <v-card-text class="px-0">
            <div :style="MobileSize ? 'width: 100%' : 'width: 640px'" class="backgroundHead"
              style="position: absolute; height: 120px; ">
              <v-row style="height: 120px; ">
                <v-col style="text-align: center;" class="pt-4">
                  <span style="margin-left: 47px"
                    :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>สแกน QR Code
                      ชำระเงิน</b></span>
                </v-col>
                <v-btn fab small @click="closeDialogQR()" icon class="mt-3"><v-icon
                    color="white">mdi-close</v-icon></v-btn>
              </v-row>
            </div>
            <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
              <v-row :width="MobileSize ? '100%' : '640px'"
                style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                <v-col style="text-align: center;">
                </v-col>
              </v-row>
            </div>
            <div class="backgroundContent" style="position: relative;">
              <v-card class="d-flex justify-center mt-10 mb-9" elevation="0" width="100%" height="100%"
                style="background: #FFFFFF; border-radius: 20px;">
                <div style="text-align: center;">
                  <!-- <v-img height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"></v-img>
                  <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn> -->
                  <v-col class="py-0">
                    <img id="qrcode" height="280" width="280" style="margin-inline: auto;" :src="`${ImageQR}`" v-if="ImageQR !== ''"/>
                  </v-col>
                  <v-col class="py-0">
                    <v-btn v-if="TypeOS !== 'iOS'" color="#27AB9C" rounded width="125" height="40" class="white--text my-8" @click="saveQRCode()">บันทึกรูปภาพ</v-btn>
                  </v-col>
                  <div>
                    <v-col>
                      <span style="font-size: 20px; font-weight: 700;">ยอดชำระเงินจำนวน : {{ Number(netPrice).toLocaleString(undefined, { minimumFractionDigits: 2 })}}
                        บาท</span>
                    </v-col>
                    <v-col>
                      <span style="font-size: 14px; font-weight: 400;">รหัสอ้างอิง {{Ref1}}</span>
                    </v-col>
                    <v-col class="py-0">
                      <span style="font-size: 14px; font-weight: 600; color: #A1A1A1;">สามารถชำระเงินได้ตามขั้นตอนนี้
                        (กรณีชำระเงินผ่านมือถือ)</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">1. กดปุ่ม "บันทึกรูปภาพ" หรือแคปหน้าจอ<br><span style="color: red; font-weight: 700;">* กรณี iOS</span> ให้แคปหน้าจอ หรือกดค้างที่รูปภาพและกดปุ่ม <b>"บันทึกไปยังแอปรูปภาพ"</b></span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">2. เปิดแอพธนาคารของท่าน
                        และเลือกเมนูสแกน QR Code</span>
                    </v-col>
                    <v-col class="text-left py-0">
                      <span style="font-size: 14px; font-weight: 400; color: #A1A1A1;">3. เลือกภาพหน้าจอเพื่อทำการสแกน QR
                        Code</span>
                    </v-col>
                  </div>
                </div>
              </v-card>
            </div>
          </v-card-text>
        </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirm" width="424" persistent>
      <v-card :height=" MobileSize || IpadSize ? '400':''" style="background: #FFFFFF; border-radius: 24px;">
        <v-img height="240px" :src="require('@/assets/ImageINET-Marketplace/ICONShop/waiting.png')">
          <v-app-bar flat color="rgba(0, 0, 0, 0)">
            <v-toolbar-title></v-toolbar-title>
            <v-spacer></v-spacer>
            <v-btn @click="dialogConfirm = false" color="#CCCCCC" icon>
              <v-icon>mdi-close</v-icon>
            </v-btn>
          </v-app-bar>
        </v-img>
        <v-container>
          <v-card-text style="text-align: center;">
            <p style="font-weight: 700; font-size: 24px; line-height: 24px; color: #333333;" class="my-4"><b></b></p>
            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #9A9A9A;">คุณต้องการทำรายการนี้ ใช่
                  หรือ ไม่</span>
              </v-card-text>
              <v-card-text>
                <v-row dense justify="center">
                  <v-btn :width="MobileSize? '100':'156'" height="38" outlined rounded color="#27AB9C" class="mr-4" @click="dialogConfirm = false">ยกเลิก</v-btn>
                  <v-btn :width="MobileSize? '100':'156'" height="38" class="white--text" rounded color="#27AB9C" @click="radioPayment === 'radio-e-WHT' ? GeteWHT() : radioPayment === 'radio-qr' ? GetQRCode('cashPayment'): GetCC('cashPayment') ">ตกลง</v-btn>
                </v-row>
              </v-card-text>
            </v-container>
          </v-card>
        </v-dialog>
    <!-- </v-container> -->
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import axios from 'axios'
export default {
  data () {
    return {
      changeCol: false,
      selectPaymentStatus: '',
      modalRangeDate: false,
      RangeDate1: [],
      modalBuyDate: false,
      modalAcceptDate: false,
      modalContractStartDate: false,
      modalContractEndDate: false,
      modalTerminate: false,
      dialogSuccess: false,
      dialogeWHT: false,
      dataeWHT: '',
      dataeWHTPayment: '',
      dataeWHTTransfer: '',
      dataeWHTCheck: '',
      date: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      date1: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      date2: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      date3: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      date4: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      dateRange: new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10),
      searchBuyDate: '',
      buyDate: '',
      searchAcceptDate: '',
      acceptDate: '',
      searchContractStartDate: '',
      contractStartDate: '',
      searchContractEndDate: '',
      contractEndDate: '',
      searchTerminateDate: '',
      TerminateDate: '',
      reasonTerminate: '',
      PayTypeSelect: '',
      InvoiceSelect: '',
      statusSelect: '',
      orderList: [],
      StateStatus: 'ทั้งหมด',
      terminateModal: false,
      showCountOrder: 0,
      disableTable: false,
      dataRole: '',
      page: 1,
      reasonSelect: '',
      payTypeItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'General', value: 'General' },
        { text: 'Onetime', value: 'Onetime' },
        { text: 'Recurring', value: 'Recurring' }
      ],
      invoiceItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ขอใบกำกับภาษี', value: 'y' },
        { text: 'ไม่ขอใบกำกับภาษี', value: 'n' }
      ],
      statusItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'New Service', value: 'New service' },
        { text: 'Change', value: 'Change' },
        { text: 'Renew', value: 'Renew' },
        { text: 'Change&Renew', value: 'Change & renew' },
        { text: 'Terminate', value: 'Terminate' }
      ],
      statusPaymentStatusItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'ชำระเงินสำเร็จ', value: 'Success' },
        { text: 'ยังไม่ชำระเงิน', value: 'Not Paid' }
      ],
      actionsItem: [
        { text: 'รายละเอียด', value: 'detail', disable: false },
        { text: 'Change', value: 'change', disable: false },
        { text: 'Renew', value: 'renew', disable: false },
        { text: 'Change&Renew', value: 'both', disable: false },
        { text: 'Terminate', value: 'terminate', disable: false }
      ],
      actionsItemNoJV: [
        { text: 'รายละเอียด', value: 'detail', disable: false }
      ],
      OrderNamePurchaser: [
        // { key: 0, name: 'ข้อมูลไม่ครบ' },
        { key: 0, name: 'ทั้งหมด' },
        { key: 2, name: 'ยังไม่ชำระเงิน' },
        { key: 1, name: 'รออนุมัติ' },
        { key: 6, name: 'อนุมัติ' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' },
        { key: 4, name: 'ยกเลิก' }
      ],
      OrderNameExtBuyer: [
        { key: 0, name: 'ทั้งหมด' },
        { key: 2, name: 'ยังไม่ชำระเงิน' },
        { key: 3, name: 'ชำระเงินสำเร็จ' },
        { key: 5, name: 'ชำระเงินไม่สำเร็จ' },
        { key: 4, name: 'ยกเลิก' }
      ],
      reasonItems: [
        { text: 'เปลี่ยนหัวบิลซื้อตรงกับ JV', value: '1' },
        { text: 'สิ้นสุดความต้องการใช้งาน - จบโปรเจคจริง', value: '2' },
        {
          text: 'สิ้นสุดความต้องการใช้งาน -ยกเลิกโปรเจคชั่วคราวถ้า Solution ลงตัว',
          value: '3'
        },
        {
          text: 'สิ้นสุดความต้องการใช้งาน - End User ยกเลิกบริการกับ SI (ลูกค้าซื้อไปให้บริการต่อ)',
          value: '4'
        },
        { text: 'ยกเลิกบริการเพราะต้องการลดต้นทุนในองค์กร', value: '5' },
        { text: 'ราคาสูงเกินงบ/ลูกค้าขอลดราคา แต่ไม่สามารถลดได้', value: '6' },
        { text: 'ลูกค้าติด Policy(นโยบาย)', value: '7' },
        {
          text: 'ระบบมีปัญหาจนลูกค้าจนขอ Terminate / ปัญหาจากการSupport',
          value: '8'
        },
        { text: 'Service ไม่พร้อมให้บริการ', value: '9' },
        { text: 'ยกเลิก Service บางส่วน', value: '10' },
        { text: 'อื่นๆ', value: '11' }
      ],
      keyCheckHead: 0,
      // headers: [
      //   { text: 'วันที่', value: 'created_at', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบเสนอราคา', value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบกำกับภาษี', value: 'transaction_code_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'วันที่สั่งซื้อ', value: 'paid_datetime', filterable: false, sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'จ่ายเงิน', value: 'payment', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      // headersSuccess: [
      //   { text: 'วันที่', value: 'created_at', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบเสนอราคา', value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบกำกับภาษี', value: 'transaction_code_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', filterable: false, sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      // headersPending: [
      //   { text: 'วันที่', value: 'created_at', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบเสนอราคา', value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบกำกับภาษี', value: 'transaction_code_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: ' ', value: 'detail', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      // headersApprove: [
      //   { text: 'วันที่', value: 'created_at', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบเสนอราคา', value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบกำกับภาษี', value: 'transaction_code_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: ' ', value: 'detail', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      // headersNoPaid: [
      //   { text: 'วันที่', value: 'created_at', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบเสนอราคา', value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบกำกับภาษี', value: 'transaction_code_icon', filterable: false, sortable: false, align: 'center', width: '125px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'จ่ายเงิน', value: 'payment', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      // headersCancel: [
      //   { text: 'วันที่', value: 'created_at', align: 'center', filterable: false, sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'รหัสการสั่งซื้อ', value: 'payment_transaction_number', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ราคา', value: 'total_amount', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบเสนอราคา', value: 'payment_transaction_number_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'ใบกำกับภาษี', value: 'transaction_code_icon', filterable: false, sortable: false, width: '125px', align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'วัน/เวลาทำธุรกรรม', value: 'paid_datetime', filterable: false, sortable: false, align: 'center', width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
      //   { text: 'สถานะการจ่ายเงิน', value: 'transaction_status', filterable: false, sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' }
      // ],
      headersAll: [
        {
          text: 'วันที่ทำรายการ',
          value: 'created_at',
          align: 'start',
          filterable: false,
          sortable: false,
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'รหัสการสั่งซื้อ',
          value: 'payment_transaction_number',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'งวดที่ชำระเงิน',
          value: 'num_of_credit_term',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะการชำระเงิน',
          value: 'payment_transaction_status',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะรายการ',
          value: 'order_type',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะการอนุมิติใบเสนอราคา',
          value: 'company_approve',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Pay Type',
          value: 'pay_type',
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ผู้ซื้อ',
          value: 'buyer_name',
          sortable: false,
          width: '180px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่อนุมัติ',
          value: 'date_qt_approve',
          align: 'start',
          filterable: false,
          width: '180px',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'สถานะขนส่ง',
          value: 'transportation_status',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ใบเสร็จ',
          value: 'receipt_number',
          align: 'receipt_number',
          filterable: false,
          width: '180px',
          sortable: false,
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบเสนอราคา',
          value: 'payment_transaction_number_icon',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบกำกับภาษี',
          value: 'transaction_code_icon',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบสัญญา',
          value: 'contract',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'ใบแจ้งหนี้',
          value: 'QT_order_invoice',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่ใบแจ้งหนี้',
          value: 'service_cycle_date',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'วันที่กำหนดชำระ',
          value: 'invoice_due_date',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จำนวนวันที่เกินกำหนดชำระ',
          value: 'days_overdue',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'e-WHT',
          value: 'pv_no',
          filterable: false,
          sortable: false,
          width: '125px',
          align: 'start',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ PR',
          value: 'pr_document_id',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'เลขที่ PO',
          value: 'po_document_id',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'Tracking Number',
          value: 'order_mobilyst_no',
          filterable: false,
          sortable: false,
          align: 'start',
          width: '180px',
          class: 'backgroundTable fontTable--text fontSizeDetail'
        },
        {
          text: 'จัดการ',
          value: 'detail',
          align: 'start',
          filterable: false,
          sortable: false,
          class: 'backgroundTable fontTable--text'
        }
      ],
      receive_items: [
        { text: 'ยังไม่รับ', value: 'not_received' },
        { text: 'รับของแล้ว', value: 'received' }
      ],
      statusSend: { text: 'ยังไม่รับ', value: 'not_received' },
      DataTable: [],
      customClick: record => ({
        on: {
          click: () => {
            this.pendingData(record)
          }
        }
      }),
      overlay: false,
      ProcurementData: '',
      responseData: '',
      checkbox: true,
      search: '',
      countOrderAll: 0,
      countOrderPending: 0,
      countOrderNotpaid: 0,
      countOrderSuccess: 0,
      countOrderFail: 0,
      countOrderCancel: 0,
      countOrderApprove: 0,
      countOrderCreditTerm: 0,
      companyData: [],
      pvDate: '',
      selected: [],
      dialogChooesPayType: false,
      radioPayment: 'no',
      radioCreditTerm: '',
      dialogConfirm: false,
      items: [],
      DialogQR: false,
      netPrice: '',
      ImageQR: '',
      TypeOS: '',
      Ref1: '',
      test: [],
      limit: 10,
      maxItem: 0,
      option: {
        page: 1,
        itemsPerPage: 10
      },
      Image: '',
      CloseDialog: false,
      OrderB2B: '',
      statusApproveStatusItem: [
        { text: 'ทั้งหมด', value: '' },
        { text: 'อนุมัติแล้ว', value: 'approve' },
        { text: 'รออนุมิติ', value: 'waiting_approve' },
        { text: 'รอร้านค้าอนุมิติ', value: 'waiting_shop_approve' },
        { text: 'รอผู้ซื้ออนุมิติ', value: 'waiting_company_approve' },
        { text: 'ไม่อนุมิติ', value: 'reject' }
      ],
      selectApproveStatus: '',
      checkEWHT: false,
      taxID: '',
      username: ''
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    newsListShow () {
      if (this.statusSelect === '') {
        return this.DataTable
      } else {
        return this.DataTable.filter(element => {
          return element.order_type.toLowerCase().includes(this.statusSelect.toLowerCase())
        })
      }
    }
  },
  watch: {
    dateRange (val) {
      // console.log('dateRange', val)
      this.contractStartDate = val[0] !== undefined ? this.formatDateToShow(val[0]) : ''
      this.contractEndDate = val[1] !== undefined ? this.formatDateToShow(val[1]) : ''
      if (this.contractStartDate !== '' && this.contractEndDate !== '') {
        this.RangeDate1 = this.contractStartDate + ' - ' + this.contractEndDate
      } else {
        this.RangeDate1 = ''
      }
    },
    dialogSuccess (val) {
      if (!val) return
      setTimeout(() => (this.dialogSuccess = false), 2000)
    },
    MobileSize (val) {
      // console.log('val', val)
      if (val === true) {
        this.$router.push({ path: '/orderCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/orderCompany' }).catch(() => {})
      }
    },
    DataTable (val) {
      // console.log('DataTable', val)
    },
    overlay (val) {
      val &&
        setTimeout(() => {
          this.overlay = false
        }, 500)
    },
    StateStatus (val) {
      // console.log('val', val)
      if (val === 'ทั้งหมด') {
        this.DataTable = this.orderList.all
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ยังไม่ชำระเงิน') {
        this.DataTable = this.orderList.not_paid
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ชำระเงินสำเร็จ') {
        this.DataTable = this.orderList.success
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ชำระเงินไม่สำเร็จ') {
        this.DataTable = this.orderList.fail
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ชำระเงินแบบเครดิตเทอม') {
        this.DataTable = this.orderList.credit_term
        this.keyCheckHead = 4
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ยกเลิก') {
        this.DataTable = this.orderList.cancel
        this.keyCheckHead = 5
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    selected (val) {
      console.log(val, 5554)
      if (val.length === 1) {
        this.items = val[0]
      } else {
        this.items = {}
      }
      // console.log(this.items, 6666)
    }
  },
  async created () {
    // console.log('StateStatus', this.StateStatus)
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    this.$EventBus.$on('getPOBuyer', this.SwitchRole)
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (
      localStorage.getItem('oneData') !== null &&
      localStorage.getItem('CompanyData') !== null
    ) {
      this.companyData = JSON.parse(
        Decode.decode(localStorage.getItem('CompanyData'))
      )
      if (this.$route.query.searchOrder) {
        this.searchOrder(this.$route.query.searchOrder)
        // ลบ searchOrder query ออก
        const query = { ...this.$route.query }
        delete query.searchOrder
        this.$router.replace({ query }).catch(() => {})
      } else {
        await this.GetTaxID()
        await this.userDetailMpV2()
        await this.CheckPaymentEWHT()
        this.ListDataTable()
      }
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
    this.minTerminate = this.checkDateToShow((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
    this.date4 = this.checkDateToShow((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
    // this.TerminateDate = this.formatDateToShow(this.checkDateToShow((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)))
    this.dateRange = []
    // this.RangeDate1 = []
    // this.checkDisableMenu()
  },
  methods: {
    searchOrder (val) {
      this.search = val
      this.ListDataTable()
    },
    changeFormat (val) {
      var pvDate = val
      let dateToReturn = ''

      var year = pvDate.substring(0, 4)
      var month = pvDate.substring(4, 6)
      var day = pvDate.substring(6, 8)

      dateToReturn = year + '-' + month + '-' + day

      return dateToReturn
    },
    async openModaleWHT (pvNo) {
      this.$store.commit('openLoader')
      this.dataeWHT = ''
      this.pvDate = ''
      this.dataeWHTPayment = ''
      this.dataeWHTTransfer = ''
      this.dataeWHTCheck = ''
      const data = {
        pv_no: pvNo
      }
      await this.$store.dispatch('actionsStatusPvNo', data)
      const response = await this.$store.state.ModuleAdminManage.stateStatusPvNo
      if (response.message === 'Get Inquiry Status Success') {
        this.dataeWHT = await response.data
        this.pvDate = await this.changeFormat(this.dataeWHT.pv_date)
        this.dataeWHTPayment = await response.data.payment
        this.dataeWHTTransfer = await response.data.transfer
        this.dataeWHTCheck = await response.data.ewht
        this.$store.commit('closeLoader')
        this.dialogeWHT = true
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: `${response.message}`
          })
        }
      }
    },
    copyText (text) {
      navigator.clipboard.writeText(text)
      this.changeCol = true
      setTimeout(() => {
        this.changeCol = false
      }, 1000)
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        icon: 'success',
        html: '<span style="padding-left: 10px;">คัดลอกสำเร็จ</span>'
      })
    },
    async acceptProduct (order) {
      // อาจจะมีการเพิ่มค่าที่ส่งไป api ถ้าหากมีแยกหลาย order
      var data = {
        payment_transaction_number: order.order_number,
        order_number: order.order_number,
        status: 'accepted',
        role_user: 'purchaser'
      }
      await this.$store.dispatch('actionAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateAcceptProduct
      // console.log('resacceptProduct')
      // console.log('data', data)
      if (res.message === 'Update status success.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'ยืนยันการตรวจสอบและได้รับสินค้าแล้ว'
        })
        this.ListDataTable()
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR'
          })
        }
      }
      this.checkAccept = 'ยอมรับสินค้าแล้ว'
    },
    async CheckAcceptProductData (item) {
      this.$store.commit('openLoader')
      var data = {
        payment_transaction_number: item.order_number
      }
      await this.$store.dispatch('actionCheckAcceptProduct', data)
      var res = this.$store.state.ModuleOrder.stateCheckAcceptProduct
      if (res.result === 'SUCCESS') {
        this.checkAcceptProduct = res.data
        // console.log('checkAcceptProduct', this.checkAcceptProduct)
        this.acceptProduct(item)
        // this.$store.commit('closeLoader')
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ showConfirmButton: false, timer: 3000, timerProgressBar: true, icon: 'warning', text: res.message })
          this.checkAcceptProduct = []
          // this.$router.push('/pobuyerProfile')
        }
      }
    },
    async downloadExcel () {
      this.$store.commit('openLoader')
      var search = this.search === '' ? false : this.search.replace(/\s/gi, '')
      var payTypeSelect = this.PayTypeSelect === '' ? false : this.PayTypeSelect
      var requiredInvoice = false
      var buyDate = this.buyDate === '' ? false : this.formatDate(this.buyDate)
      var acceptDate = this.acceptDate === '' ? false : this.formatDate(this.acceptDate)
      var contractStartDate = this.contractStartDate === '' ? false : this.formatDate(this.contractStartDate)
      var contractEndDate = this.contractEndDate === '' ? false : this.formatDate(this.contractEndDate)
      var orderStatus = false
      var conditionSendCs = false
      var selectPaymentStatus = this.selectPaymentStatus === '' ? false : this.selectPaymentStatus.replace(/\s/gi, '&nbsp')
      await axios({
        url: `${process.env.VUE_APP_BACK_END}api/export_order_report/${this.dataRole.role}/${this.companyData.id}/${search}/${payTypeSelect}/${requiredInvoice}/${buyDate}/${acceptDate}/${contractStartDate}/${contractEndDate}/${orderStatus}/${conditionSendCs}/${selectPaymentStatus}`,
        method: 'GET',
        responseType: 'blob'
      }).then((res) => {
        var fileURL = window.URL.createObjectURL(new Blob([res.data]))
        var fileLink = document.createElement('a')
        fileLink.href = fileURL
        fileLink.setAttribute('download', 'result.xlsx')
        document.body.appendChild(fileLink)
        fileLink.click()
      })
      this.$store.commit('closeLoader')
      // var URL = 'https://devinet-eprocurement.one.th/api/backend/api/export_order_report/'
      // window.open('/' + `${URL}` + `${this.dataRole.role}` + '/' + `${this.companyData.id}` + '/' + `${search}` + '/' + `${payTypeSelect}` + '/' + `${requiredInvoice}` + '/' + `${buyDate}` + '/' + `${acceptDate}` + '/' + `${contractStartDate}` + '/' + `${contractEndDate}` + '/' + `${orderStatus}` + '/' + `${conditionSendCs}` + '/' + `${selectPaymentStatus}`)
    },
    async trackingMobilyst (val) {
      window.open(val)
    },
    async setValueRangeDate (val) {
      this.$refs.modalRangeDate.save(val)
      var Range = await val.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
      this.dateRange = Range
      await this.ListDataTable()
    },
    SortDate (RangeDate) {
      return RangeDate.sort((a, b) => {
        var dateA = new Date(a)
        var dateB = new Date(b)
        return dateA - dateB
      })
    },
    checkDisableMenu () {
      var dateCurrent = new Date().getDate()
      if (dateCurrent > 14) {
        for (var i = 1; i < this.actionsItem.length; i++) {
          if (this.actionsItem[i].value === 'change' || this.actionsItem[i].value === 'both' || this.actionsItem[i].value === 'terminate') {
            this.actionsItem[i].disable = true
          }
        }
      }
    },
    async confirmTerminate () {
      var reasonSelectCheck = ''
      for (let i = 0; i < this.reasonItems.length; i++) {
        if (this.reasonItems[i].value === this.reasonSelect) {
          reasonSelectCheck = this.reasonItems[i].text
        }
      }
      var data = {
        order_number: this.orderNumberTerminate,
        terminate_subject: reasonSelectCheck,
        terminate_reason: this.reasonTerminate,
        terminate_date: this.TerminateDate
      }
      await this.$store.dispatch('actionsTerminate', data)
      var res = await this.$store.state.ModuleAdminManage.stateTerminate
      if (res.code === 200) {
        this.closeModalterminate()
        this.dialogSuccess = true
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'info',
            iconColor: '#E9A016',
            title: 'ไม่สามารถดำเนินการได้'
          })
        }
      }
    },
    closeModalterminate () {
      this.terminateModal = false
      this.searchTerminateDate = ''
      this.reasonSelect = ''
      this.TerminateDate = ''
      this.reasonTerminate = ''
    },
    gotoActions (item, select) {
      // console.log('select===>', select)
      var orderNumber = item.payment_transaction_number
      this.orderNumberTerminate = item.payment_transaction_number
      var termId = item.id_of_credit_term !== null ? item.id_of_credit_term : '-'
      if (select === 'detail') {
        if (this.MobileSize === false) {
          this.$router
            .push({ path: `/orderDetailCompany?orderNumber=${orderNumber}&termId=${termId}` })
            .catch(() => {})
        } else {
          this.$router
            .push({
              path: `/orderDetailCompanyMobile?orderNumber=${orderNumber}&termId=${termId}`
            })
            .catch(() => {})
        }
      } else if (select === 'change') {
        this.$router.push({ path: `/Change?orderNumber=${orderNumber}` }).catch(() => {})
      } else if (select === 'renew') {
        this.$router.push({ path: `/RenewOrder?orderNumber=${orderNumber}` }).catch(() => {})
      } else if (select === 'both') {
        this.$router.push({ path: `/ChangeAndRenewOrder?orderNumber=${orderNumber}` }).catch(() => {})
      } else if (select === 'terminate') {
        this.terminateModal = true
      }
    },
    setValueBuyDate (val) {
      this.searchBuyDate = val
      // console.log('val', val)
      this.buyDate = this.formatDateToShow(val)
    },
    setValueAcceptDate (val) {
      this.searchAcceptDate = val
      // console.log(this.searchDateNotFormat)
      this.acceptDate = this.formatDateToShow(val)
    },
    setValueContractStartDate (val) {
      this.searchContractStartDate = val
      // console.log(this.searchDateNotFormat)
      this.contractStartDate = this.formatDateToShow(val)
      this.contractEndDate = ''
    },
    setValueContractEndDate (val) {
      this.searchContractEndDate = val
      // console.log(this.searchDateNotFormat)
      this.contractEndDate = this.formatDateToShow(val)
    },
    setValueTerminateDate (val) {
      this.$refs.dialogTerminate.save(val)
      this.searchTerminateDate = val
      // console.log(this.searchDateNotFormat)
      this.TerminateDate = this.formatDateToShow(val)
    },
    closeModalBuyDate () {
      this.modalBuyDate = false
      this.date = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchBuyDate = ''
      this.buyDate = ''
    },
    closeModalAcceptDate () {
      this.modalAcceptDate = false
      this.date1 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchAcceptDate = ''
      this.acceptDate = ''
    },
    async CloseModalRangeDate () {
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.dateRange = []
      this.RangeDate1 = []
      await this.ListDataTable()
    },
    closeModalContractStartDate () {
      this.modalContractStartDate = false
      this.date2 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchContractStartDate = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
    },
    closeModalContractEndDate () {
      this.modalContractEndDate = false
      this.date3 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchContractEndDate = ''
      this.contractEndDate = ''
    },
    closeModalTerminateDate () {
      this.modalTerminate = false
      this.date4 = new Date(Date.now() - new Date().getTimezoneOffset() * 60000)
        .toISOString()
        .substr(0, 10)
      this.searchTerminateDate = ''
      this.TerminateDate = ''
    },
    formatDateToShow (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      const yearChange = parseInt(year) + 543
      return `${day}/${month}/${yearChange}`
    },
    formatDate (date) {
      // console.log('date', date)
      if (!date) return null
      const [day, month, year] = date.split('/')
      const yearChange = parseInt(year) - 543
      // console.log('year', `${day}-${month}-${yearChange}`)
      return `${yearChange}-${month}-${day}`
    },
    replaceSpacbar (data) {
      if (!data) return null
      var data1 = data.replace(/\s/gi, '&nbsp')
      return `${data1}`
    },
    async GetETaxPDF (val) {
      var data = {
        transactionCode: val.transaction_code
      }
      const timeoutId = setTimeout(async () => {
        await this.$store.dispatch('ActionsGetETaxPDF', data)
        var response = await this.$store.state.ModuleCart.stateGetETaxPDF
        // console.log('response', response)
        if (response.result === 'OK') {
          if (response.etaxResponse.status === 'OK') {
            if (response.etaxResponse.urlPdf !== undefined) {
              window.open(`${response.etaxResponse.urlPdf}`, '_blank')
            } else {
              window.open(`${response.etaxResponse.pdfURL}`, '_blank')
            }
            // console.log('response', response.etaxResponse.urlPdf)
          }
        } else {
          if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
            this.$store.commit('closeLoader')
            this.$EventBus.$emit('refreshToken')
          } else {
            this.$swal.fire({
              toast: true,
              showConfirmButton: false,
              timer: 1000,
              timerProgressBar: true,
              icon: 'error',
              title: 'ไม่พบเอกสารใบกำกับภาษี'
            })
          }
        }
      }, 1000)
      // console.log('4', timeoutId)
      if (timeoutId > 1000) {
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่พบเอกสารใบกำกับภาษี'
        })
      }
    },
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    async ListDataTable () {
      this.$store.commit('openLoader')
      this.countOrderAll = 0
      this.countOrderNotpaid = 0
      this.countOrderSuccess = 0
      this.countOrderFail = 0
      this.countOrderCancel = 0
      this.countOrderCreditTerm = 0
      var data = {
        role_user: 'purchaser',
        company_id: this.companyData.id,
        search_keyword: this.search,
        pay_type: this.PayTypeSelect,
        required_invoice: this.InvoiceSelect,
        create_date: this.buyDate,
        approved_at: this.acceptDate,
        start_date_contract: this.contractStartDate,
        end_date_contract: this.contractEndDate,
        order_status: '',
        payment_transaction_status: this.selectPaymentStatus,
        status_qt: this.selectApproveStatus,
        page: this.option.page,
        limit: this.option.itemsPerPage
      }
      await this.$store.dispatch('actionsListOrderPurchaser', data)
      var res = await this.$store.state.ModuleAdminManage.stateListOrderPurchaser
      if (res.message === 'Show list order purchaser success') {
        this.$store.commit('closeLoader')
        this.orderList = res.data
        this.countOrderAll = this.orderList.all.length
        this.countOrderNotpaid = this.orderList.not_paid !== undefined ? this.orderList.not_paid.length : 0
        this.countOrderSuccess = this.orderList.success !== undefined ? this.orderList.success.length : 0
        this.countOrderFail = this.orderList.fail !== undefined ? this.orderList.fail.length : 0
        this.countOrderCancel = this.orderList.cancel !== undefined ? this.orderList.cancel.length : 0
        this.countOrderCreditTerm = this.orderList.credit_term !== undefined ? this.orderList.credit_term.length : 0
        this.maxItem = this.orderList.total_all
        // this.option.page = 1
        // console.log(this.countOrderAll, this.countOrderPending, this.countOrderNotpaid, this.countOrderFail, this.countOrderSuccess, this.countOrderCancel)
        if (this.StateStatus === 'ทั้งหมด') {
          this.DataTable = this.orderList.all.map(item => {
            return {
              ...item,
              payment_id_value: item.payment_credit_term_number !== '-' ? item.payment_credit_term_number : item.payment_transaction_number
            }
          })
          // console.log('data!!!-------------->', this.DataTable)
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 'ยังไม่ชำระเงิน') {
          this.DataTable = this.orderList.not_paid
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 'ชำระเงินสำเร็จ') {
          this.DataTable = this.orderList.success
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 'ชำระเงินไม่สำเร็จ') {
          this.DataTable = this.orderList.fail
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 'ชำระเงินแบบเครดิตเทอม') {
          this.DataTable = this.orderList.credit_term
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        } else if (this.StateStatus === 'ยกเลิก') {
          this.DataTable = this.orderList.cancel
          if (this.DataTable.length === 0) {
            this.disableTable = false
          } else {
            this.disableTable = true
          }
        }
        // console.log('DataTable===>', this.DataTable)
      } else if (
        res.message ===
        'An error has occurred. Please try again in an hour or two.'
      ) {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'error',
          title: 'SERVER ERROR'
        })
      } else {
        if (res.message === 'This user is Unauthorized' || res.message === 'This user is unauthorized.' || res.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            text: 'ผู้ใช้งานนี้ถูกใช้งานอยู่'
          })
          localStorage.removeItem('oneData')
        }
      }
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async UpdateStatusBuyer (item) {
      // console.log(item)
      if (item.seller_sent_status === 'not_sent') {
        this.$swal.fire({
          icon: 'warning',
          text: 'สินค้ากำลังจัดส่ง',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      } else {
        const update = {
          order_number: item.order_number,
          buyer_received_status: item.buyer_received_status
        }
        // console.log('update', update)
        await this.$store.dispatch('actionUpdateStatusBuyer', update)
        this.$swal.fire({
          icon: 'success',
          text: 'บันทึกข้อมูลสำเร็จ',
          showConfirmButton: false,
          timer: 1500
        })
        await this.ListDataTable()
      }
    },
    async GoToPayment (item) {
      const PaymentID = {
        payment_transaction_number: item.payment_transaction_number
      }
      // console.log('payment_transaction_number', PaymentID)
      await this.$store.dispatch('ActionGetPaymentPage', PaymentID)
      var response = this.$store.state.ModuleCart.stateGetPaymentPage
      // console.log('respose paymenttttttttt', response)
      await localStorage.setItem('PaymentData', Encode.encode(response))
      this.$router.push('/RedirectPaymentPage')
    },
    async orderDetail (val, typeDoc) {
      // console.log('path to not tell =====>', typeDoc)
      // console.log('val', val)
      if (typeDoc === 'QT') {
        window.open(`${val.QT_order}`, '_blank')
      } else if (typeDoc === 'PR') {
        window.open(`${val.PR_External}`, '_blank')
      } else if (typeDoc === 'PO') {
        window.open(`${val.PO_External}`, '_blank')
      } else if (typeDoc === 'QT_order') {
        window.open(`${val.QT_order_invoice}`, '_blank')
      }
      // var data = {
      //   payment_transaction_number: val.payment_transaction_number
      // }
      //   console.log('data naja', data)
      // await this.$store.dispatch('actionOrderDetail', data)
      // await this.$router.push({ path: '/quotation1shop' }).catch(() => {})
      // var response = await this.$store.state.ModuleOrder.stateOrderDetailData
      // console.log('order detail na', response)
      // if (response.result === 'SUCCESS') {
      //   this.OrderDetailProp = response.data
      //   console.log('กี่ร้านนะะะะะะะะะ', response.data[0].order_number[0])
      // }
    },
    SelectDetailOrder (item) {
      // console.log('SelectDetailOrder', item)
      this.StateStatus = item
      this.page = 1
    },
    SwitchRole () {
      this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
      this.ListDataTable()
    },
    // async getDataTable () {
    //   this.overlay = true
    //   const data = {
    //     procurement_org_id: this.ProcurementData.procurement_org_id
    //   }
    //   await this.$store.dispatch('actionListOrderProcurement', data)
    //   var response = await this.$store.state.ModuleCart.stateListOrderProcurement
    //   // console.log('response List Order Procurement', response)
    //   if (response.result === 'SUCCESS') {
    //     this.responseData = response.data
    //     this.overlay = false
    //   }
    // },
    async pendingData (item) {
      this.overlay = true
      const data = {
        order_id: item.order_id
      }
      await this.$store.dispatch('actionOrderDetail', data)
      var response = await this.$store.state.ModuleCart.stateOrderDetailData
      // console.log('response detail order', response)
      if (response.result === 'SUCCESS') {
        this.overlay = false
        localStorage.setItem(
          'MyOrderDetail',
          Encode.encode(JSON.stringify(response.data))
        )
        this.$router.push('/myorderdetail')
      }
    },
    goDetailPO (item) {
      var orderNumber = item.payment_transaction_number
      if (this.MobileSize === false) {
        this.$router
          .push({ path: `/orderDetailCompany?orderNumber=${orderNumber}` })
          .catch(() => {})
      } else {
        this.$router
          .push({
            path: `/orderDetailCompanyMobile?orderNumber=${orderNumber}`
          })
          .catch(() => {})
      }
      // if (item.transaction_status === 'Pending' || item.transaction_status === 'Approve') {
      //   if (this.MobileSize === false) {
      //     this.$router.push({ path: '/pobuyerdetailapprove' }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: '/pobuyerdetailapproveMobile' }).catch(() => {})
      //   }
      // } else {
      //   var orderNumber = item.payment_transaction_number
      //   if (this.MobileSize === false) {
      //     this.$router.push({ path: `/orderDetailCompany?orderNumber=${orderNumber}` }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: `/orderDetailCompany?orderNumber=${orderNumber}` }).catch(() => {})
      //   }
      // }
    },
    reSetSearch () {
      this.searchContractStartDate = ''
      this.selectPaymentStatus = ''
      this.date = ''
      this.date1 = ''
      this.date2 = ''
      this.date3 = ''
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.PayTypeSelect = ''
      this.InvoiceSelect = ''
      this.buyDate = ''
      this.acceptDate = ''
      this.statusImportantSelect = ''
      this.statusSelect = ''
      this.search = ''
      this.$refs.modalRangeDate.save([])
      this.modalRangeDate = false
      this.contractStartDate = ''
      this.contractEndDate = ''
      this.dateRange = []
      this.RangeDate1 = []
      this.selected = []
      this.selectApproveStatus = ''
      this.ListDataTable()
    },
    checkDateToShow (date) {
      if (!date) return null
      const oldDate = date.split('-')
      var newDate = ` ${oldDate[0]}-${oldDate[1]}-${oldDate[2]}`
      if (parseInt(oldDate[2]) <= 15) {
        var d1 = '01'
        var current
        if (new Date(newDate).getMonth() === 11) {
          current = new Date(new Date(newDate).getFullYear() + 1, 0, 1)
        } else {
          current = new Date(new Date(newDate).getFullYear(), new Date(newDate).getMonth() + 1, 1)
        }
        var endyear = current.getFullYear()
        var endMonth = ('0' + (current.getMonth() + 1)).slice(-2)
        return `${endyear}-${endMonth}-${d1}`
      } else {
        var d2 = '01'
        var current2
        if (new Date(newDate).getMonth() + 1 === 11) {
          current2 = new Date(new Date(newDate).getFullYear() + 1, 0, 1)
        } else {
          current2 = new Date(new Date(newDate).getFullYear(), new Date(newDate).getMonth() + 2, 1)
        }
        var endyear2 = current2.getFullYear()
        var endMonth2 = ('0' + (current2.getMonth() + 1)).slice(-2)
        return `${endyear2}-${endMonth2}-${d2}`
      }
    },
    async CheckStockBeforeOpenModalPayment (orderNumber) {
      var messageCheckError = ''
      var i
      var data = {
        payment_transaction_number: orderNumber
      }
      await this.$store.dispatch('actionsCheckStockBeforePayment', data)
      const response = await this.$store.state.ModuleCart.stateCheckStockBeforePayment
      if (response.message === 'สามารถซื้อสินค้าทั้งหมดได้') {
        this.dialogChooesPayType = true
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม') {
        for (i = 0; i < response.data.product_free.length; i++) {
          messageCheckError = response.data.product_free[i].product_name + `${i === response.data.product_free.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการแถม ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else if (response.message === 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว') {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่มีข้อมูลอยู่ในระบบแล้ว ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      } else {
        for (i = 0; i < response.data.product_list.length; i++) {
          messageCheckError = response.data.product_list[i].product_name + `${i === response.data.product_list.length - 1 ? '' : ' ,'}`
        }
        this.$swal.fire({
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'มีสินค้าบางตัวที่ไม่เพียงพอต่อการซื้อ ได้แก่ ' + messageCheckError + ' กรุณาติดต่อเจ้าหน้าที่'
        })
      }
    },
    confirmPayment () {
      var dataRole = JSON.parse((localStorage.getItem('roleUser'))).role
      // console.log('dataRole', dataRole)
      this.dialogConfirm = true
      this.dialogChooesPayType = false
      if (dataRole === 'purchaser') {
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่สามารถชำระเงินได้',
          text: 'สำหรับผู้ซื้อองค์กรเท่านั้น'
        })
      }
    },
    openDialogPayment () {
      this.dialogChooesPayType = false
      this.radioPayment = 'no'
    },
    async GetCC (paymentTypeData) {
      // console.log('GetCC', this.radioCreditTerm)
      this.$store.commit('openLoader')
      this.dialogConfirm = false
      var data
      var resCC
      var goLocalValue = window.location.href.startsWith('http://localhost:8080/') ? 'local' : ''
      if (this.radioPayment !== 'radio-installment') {
        this.radioCreditTerm = ''
      }
      var productIdToChange = []
      this.selected.forEach(item => {
        productIdToChange.push(item.payment_credit_term_number)
      })
      data = {
        go_local: goLocalValue,
        payment_transaction_number: productIdToChange,
        // payment_transaction_number: this.items.payment_credit_term_number !== null && this.items.payment_credit_term_number !== '-' ? this.items.payment_credit_term_number : this.items.payment_transaction_number,
        term: this.radioCreditTerm ? this.radioCreditTerm : ''
      }
      // console.log('data', data)
      // await this.$store.dispatch('actionsGetCC', data)
      // resCC = await this.$store.state.ModuleCart.stateGetCC
      await this.$store.dispatch('actionsGetCCB2B', data)
      resCC = await this.$store.state.ModuleCart.stateGetCCB2B
      if (resCC.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        localStorage.setItem('PaymentData', Encode.encode(resCC.data))
        this.$router.push('/RedirectPaymentPage').catch(() => {})
      } else if (resCC.message === 'ERROR ระบบ Payment มีปัญหาไม่สามารถส่งหรือรับข้อมูลได้') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ระบบ Payment มีปัญหา',
          text: 'ไม่สามารถชำระเงินได้'
        })
      } else {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          title: 'ไม่สามารถชำระเงินได้'
        })
      }
      this.$store.commit('closeLoader')
    },
    async GetQRCode (paymentTypeData) {
      // console.log('GetQRCode', paymentTypeData)
      const paymentType = paymentTypeData
      if (paymentType === 'cashPayment') {
        await this.openDialogQR()
      }
    },
    async openDialogQR () {
      // console.log('openDialogQR')
      var data
      var resQR = ''
      var productIdToChange = []
      this.selected.forEach(item => {
        // console.log(item.payment_credit_term_number)
        productIdToChange.push(item.payment_credit_term_number)
      })
      // console.log(productIdToChange, 'payment_credit_term_number')
      data = {
        payment_transaction_number: productIdToChange
        // payment_transaction_number: this.items.payment_credit_term_number !== null && this.items.payment_credit_term_number !== '-' ? this.items.payment_credit_term_number : this.items.payment_transaction_number
      }
      // console.log('data', productIdToChange)
      // await this.$store.dispatch('actionsGetQRCode', data)
      // resQR = await this.$store.state.ModuleCart.stateGetQRCode
      await this.$store.dispatch('actionsGetQRCodeB2B', data)
      resQR = await this.$store.state.ModuleCart.stateGetQRCodeB2B
      if (resQR.result === 'SUCCESS') {
        localStorage.removeItem('sale_order')
        this.netPrice = resQR.data.net_price
        this.Ref1 = resQR.data.ref1
        this.Ref2 = resQR.data.ref2
        this.imageBase64 = 'data:image/png;base64,' + resQR.data.img_base
        this.ImageQR = ''
        this.ImageQR = await resQR.data.img_base64
        this.OrderB2B = resQR.data.order_b2b
        setTimeout(() => {
          this.showIMG(this.ImageQR)
        }, 1000)
        // this.DialogQR = true
      } else if (resQR.result === 'FAILED') {
        this.$swal.fire({
          showConfirmButton: false,
          timer: 5000,
          timerProgressBar: true,
          icon: 'error',
          text: 'ERROR ไม่สามารถชำระเงินได้'
        })
        this.DialogQR = false
        this.dialogConfirm = false
      } else {
        if (resQR.message === 'This user is Unauthorized' || resQR.message === 'This user is unauthorized.' || resQR.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'ระบบขัดข้องกรุณาติดต่อเจ้าหน้าที่'
          })
          this.DialogQR = false
          this.dialogConfirm = false
        }
        // }
        // this.$store.commit('closeLoader')
      }
    },
    async checkOrderResult (orderNumber) {
      var productIdToChange = []
      this.selected.forEach(item => {
        productIdToChange.push(item.payment_credit_term_number)
      })
      const data = {
        // payment_transaction_number: productIdToChange
        payment_transaction_number: this.items.payment_credit_term_number !== null && this.items.payment_credit_term_number !== '-' ? this.items.payment_credit_term_number : this.items.payment_transaction_number
      }
      var value = orderNumber
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'การชำระเงินไม่เสร็จสมบูรณ์'
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    async updateOptions (options) {
      this.options = options
      await this.ListDataTable()
    },
    async showIMG (ImageQR) {
      // console.log('ImageQR', ImageQR)
      this.Image = ImageQR
      // console.log('ImageQR', ImageQR)
      this.DialogQR = true
      this.$store.commit('closeLoader')
      var data
      // var productIdToChange = []
      // if (this.items.payment_credit_term_number === null) {
      //   this.selected.forEach(item => {
      //     productIdToChange.push(item.payment_transaction_number)
      //   })
      // } else {
      //   this.selected.forEach(item => {
      //     productIdToChange.push(item.payment_credit_term_number)
      //   })
      // }

      data = {
        payment_transaction_number: this.OrderB2B
        // payment_transaction_number: productIdToChange
        // payment_transaction_number: this.items.payment_credit_term_number !== null && this.items.payment_credit_term_number !== '-' ? this.items.payment_credit_term_number : this.items.payment_transaction_number
      }
      var value = data.payment_transaction_number
      const maxAttempts = 15
      let currentAttempt = 1
      while (currentAttempt <= maxAttempts) {
        // await this.$store.dispatch('actionsCheckResultB2B', data)
        // const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultB2B
        await this.$store.dispatch('actionsCheckResultQRCodeV2', data)
        const resCheckQR = await this.$store.state.ModuleCart.stateCheckResultQRCodeV2
        if (this.CloseDialog === true) {
          break
        }
        if (resCheckQR.result === 'SUCCESS') {
          localStorage.setItem('orderNumberB2B', JSON.stringify(value))
          this.$router.push({ path: `/yourorder?id=${value}` }).catch(() => {})
          break
        }
        await new Promise(resolve => setTimeout(resolve, 10000))
        currentAttempt++
        if (currentAttempt === 15 && resCheckQR.result !== 'SUCCESS') {
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: 'การชำระเงินไม่เสร็จสมบูรณ์'
          })
          // this.$router.push({ path: '/' }).catch(() => {})
        }
      }
    },
    async closeDialogQR () {
      // this.$store.commit('openLoader')
      this.DialogQR = false
      this.CloseDialog = true
      this.dialogConfirm = false
      this.$store.commit('closeLoader')
    },
    saveQRCode () {
      const image = document.getElementById('qrcode')
      // console.log(image)
      const link = document.createElement('a')
      link.href = image.src
      link.download = 'image.png'

      // Simulate a click on the link
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    },
    isSelectionRestricted (item, isSelected) {
      if (this.selected.length === 0) {
        return false
      }
      const selectedPartner = this.selected[0].order_number
      return this.selected.length > 0 && !isSelected && item.order_number !== selectedPartner
    },
    async GeteWHT () {
      this.$store.commit('openLoader')
      // var data
      var productIdToChange = []
      this.selected.forEach(item => {
        productIdToChange.push(item.payment_credit_term_number)
      })
      const data = {
        payment_transaction_number: productIdToChange
        // payment_transaction_number: this.items.payment_credit_term_number !== null && this.items.payment_credit_term_number !== '-' ? this.items.payment_credit_term_number : this.items.payment_transaction_number
      }
      // data = {
      //   order_number: this.items.payment_transaction
      //   // order_number: this.items.detail_credit_term.payment_credit_term_number !== '-' && this.items.pay_type === 'recurring' ? this.items.detail_credit_term.payment_credit_term_number : this.items.payment_transaction
      // }
      await this.$store.dispatch('actionSendEWHT', data)
      const response = await this.$store.state.ModuleCart.stateSendEWHT
      if (response.message === 'PaymentVoucher Import Successfully') {
        this.dialogConfirm = false
        this.$store.commit('closeLoader')
        await this.$store.dispatch('actionsRedirectWHT')
        const response = await this.$store.state.ModuleCart.stateRedirectWHT
        const newTab = window.open('', '_blank')
        await this.$swal.fire({
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'success',
          text: 'ชำระเงินสำเร็จ'
        })
        if (newTab) {
          newTab.location.href = response.url
        } else {
          window.location.href = response.url
        }
        // if (this.MobileSize) {
        //   this.$router.push({ path: '/orderCompanyMobile' }).catch(() => {})
        // } else {
        //   this.$router.push({ path: '/orderCompany' }).catch(() => {})
        // }
        // this.dialogConfirm = false
        // await this.getFranchise()
        // await this.getItemProduct()
      } else {
        if (response.message === 'This user is Unauthorized' || response.message === 'This user is unauthorized.' || response.message === 'กรุณากรอก token ให้ถูกต้อง') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({
            showConfirmButton: false,
            timer: 5000,
            timerProgressBar: true,
            icon: 'error',
            text: response.message
          })
        }
      }
    },
    async CheckPaymentEWHT () {
      this.$store.commit('openLoader')
      var data
      data = {
        tax_id: this.taxId,
        username: this.username
      }
      await this.$store.dispatch('actionPaymenyCheckewht', data)
      const response = await this.$store.state.ModuleOrder.statePaymenyCheckewht
      if (response.code === 200) {
        this.checkEWHT = true
      } else {
        this.checkEWHT = false
      }
      this.$store.commit('closeLoader')
    },
    async GetTaxID () {
      var companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      var data = {
        company_id: companyData.id
      }
      await this.$store.dispatch('actionsDetailCompany', data)
      var response = await this.$store.state.ModuleAdminManage.stateDetailCompany
      if (response.result === 'SUCCESS') {
        this.taxId = response.data.tax_id
        // console.log('taxid', this.taxId)
      }
    },
    async userDetailMpV2 () {
      await this.$store.dispatch('actionsAuthorityUser')
      var response = await this.$store.state.ModuleUser.stateAuthorityUser
      if (response.code === 200) {
        this.username = response.data.username
        // console.log('username', this.username)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-last-child(1) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }

.copySTY:hover {
  transform: scale(1.3);
}
</style>

<style scoped>
.setCustomSelect /deep/ .v-select__selections {
  align-items: center;
  display: flex;
  flex: 1 1;
  flex-wrap: wrap;
  line-height: 24px;
  max-width: 100%;
  min-width: 0;
  height: 40px;
  color: #333333 !important;
}
.fontSizeDetail {
  font-weight: 700 !important;
  font-size: 14px !important;
  line-height: 22px !important;
  color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
<style >
.checkmark__circle {
  stroke-dasharray: 166;
  stroke-dashoffset: 166;
  stroke-width: 2;
  stroke-miterlimit: 10;
  stroke: #27AB9C;
  fill: none;
  animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
}

.checkmark {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  display: block;
  stroke-width: 4;
  stroke: #fff;
  stroke-miterlimit: 10;
  margin: 5% auto;
  box-shadow: inset 0px 0px 0px #27AB9C;
  animation: fill .4s ease-in-out .4s forwards, scale .3s ease-in-out .9s both;
}

.checkmark__check {
  transform-origin: 50% 50%;
  stroke-dasharray: 48;
  stroke-dashoffset: 48;
  animation: stroke 0.3s cubic-bezier(0.65, 0, 0.45, 1) 0.8s forwards;
}
@keyframes stroke {
  100% {
    stroke-dashoffset: 0;
  }
}

@keyframes scale {

  0%,
  100% {
    transform: none;
  }

  50% {
    transform: scale3d(1.1, 1.1, 1);
  }
}

@keyframes fill {
  100% {
    box-shadow: inset 0px 0px 0px 40px #27AB9C;
  }
}
</style>

<style scoped lang="scss">
::v-deep .shop-table table {
  thead {
    tr th:nth-child(1) {
      background: #E6F5F3 !important;
      border-style: none !important;
    }
  }
  tbody {
    tr {
      td:nth-child(24) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
      }
    }
  }
  thead {
    tr {
        th {
          white-space: nowrap;
        }
        th:nth-child(1) {
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        z-index: 10;
        background: white;
        }
    }
  }
  thead {
    tr {
        th:nth-child(24) {
        z-index: 11;
        background: white;
        position: sticky !important;
        position: -webkit-sticky !important;
        right: 0;
        }
    }
  }
}
::v-deep .product-table table {
  thead {
  tr {
      th {
        white-space: nowrap;
      }
  }
  }
}
</style>
