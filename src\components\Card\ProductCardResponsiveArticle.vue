<template>
  <v-hover
    v-slot="{ hover }"
  >
    <!-- <v-card outlined class="rounded-lg px-2 custom-card pt-2"  :href="itemProduct.link ? itemProduct.link : pathProductDetail" height="350px" width="210px" :elevation="hover ? 8 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;"> -->
    <!-- <v-card outlined class="rounded-lg px-2 custom-card pt-2"  :href="itemProduct.link ? itemProduct.link : pathProductDetail" height="100%" width="210px" :elevation="hover ? 8 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;"> -->
    <v-card outlined v-if="itemProduct !== undefined" class="rounded-lg px-2 custom-card card"  :href="itemProduct.link ? itemProduct.link : pathProductDetail" :elevation="hover ? 8 : 0" :class="{ 'on-hover': hover }" style="cursor: pointer;" onclick="return false;">
      <v-row dense class="d-flex pb-auto" style="padding-bottom: 10px; padding-top: 10px;">
        <v-col cols="12" style="display: flex; justify-content: flex-end;">
          <div>
            <v-btn
              v-if="itemProduct.txt === false"
              @click="addRelated(itemProduct)"
              class="mx-2"
              fab
              dark
              x-small
              height="22"
              width="22"
              color="#62B3F9"
            >
              <v-icon dark small>mdi-plus</v-icon>
            </v-btn>
            <v-btn
              v-else
              @click="RemoveRelated(itemProduct)"
              class="mx-2"
              fab
              dark
              x-small
              height="22"
              width="22"
              color="#f60000fc"
            >
              <v-icon dark small>mdi-minus</v-icon>
            </v-btn>
          </div>
        </v-col>
      </v-row>
      <v-col cols="12" md="12" class="pa-0">
        <v-img
        v-lazyload
        :gradient="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock') ? '#33333373, #33333373' : ''"
        :src="!Array.isArray(itemProduct.images_URL) ? itemProduct.images_URL : itemProduct.images_URL[0]"
        loading='lazy'
        height="115"
        width="100%"
        contain
        style="border-radius: 8px;"
        v-if="itemProduct.images_URL.length !== 0"
        class="align-start"
        >
        <v-chip v-if="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock')" color="#33333380" text-color="white" style="position:absolute; z-index:3; top:38%; left:25.5%">สินค้าหมด</v-chip>
          <!-- <v-chip
          v-if="itemProduct.stock_count === 0"
          class="ma-2"
          text-color="#D1392B"
          color="rgba(255, 255, 255)"
          small
          >
            <v-avatar
              left
              color="#D1392B"
              size="10"
            >
              <v-icon small color="white">mdi-close</v-icon>
            </v-avatar>
            สินค้าหมด
          </v-chip> -->
          <!-- <v-row dense>
            <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
              <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
              <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
              <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
              <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
              <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
              <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
              <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
              <v-img src="@/assets/Tag/Event1.png" height="70" width="55" contain style="margin-left: 10px; margin-top: -20px;" v-else-if="itemProduct.message_status === 'e-receipt'"></v-img>
            </v-col> -->
            <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && !IpadSize && MobileSize">
              <v-img src="@/assets/icons/Discount.png" height="45" width="45" contain style="margin-left: 30%;">
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
              </v-img>
            </v-col>
            <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && IpadSize && !MobileSize">
              <v-img src="@/assets/icons/Discount.png" height="45" width="45" contain style="margin-left: 30%;">
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
              </v-img>
            </v-col>
            <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize">
              <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span><br/>
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span><br/>
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;">ลด</span>
              </v-img>
            </v-col> -->
          <!-- </v-row> -->
        </v-img>
        <v-img v-lazyload
        :gradient="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock') ? '#33333373, #33333373' : ''"
        src="@/assets/NoImage.png"
        height="115"
        width="100%"
        style="border-radius: 8px;"
        v-else>
        <v-chip v-if="(itemProduct.normalOutStock === true && itemProduct.normalOutStock !== undefined) || (itemProduct.product_type === 'general' && itemProduct.stock_status === 'out of stock')" color="#33333380" text-color="white" style="position:absolute; z-index:3; top:38%; left:25.5%">สินค้าหมด</v-chip>
          <!-- <v-chip
          v-if="itemProduct.stock_count === 0 || itemProduct.stock_status === 'out of stock'"
          class="ma-2"
          text-color="#D1392B"
          color="rgba(255, 255, 255)"
          small
          >
            <v-avatar
              left
              color="#D1392B"
              size="10"
            >
              <v-icon small color="white">mdi-close</v-icon>
            </v-avatar>
            สินค้าหมด
          </v-chip> -->
          <!-- <v-row  dense>
            <v-col cols="6" md="6" sm="6" xs="6" class="pt-4">
              <v-img src="@/assets/Tag/Sale.svg" height="33" width="70" contain style="margin-left: -8px; margin-top: -2px;" v-if="itemProduct.message_status === 'sale'"></v-img>
              <v-img src="@/assets/Tag/New.svg" height="33" width="61" contain style="margin-left: 0px; margin-top: -14px;" v-else-if="itemProduct.message_status === 'new'"></v-img>
              <v-img src="@/assets/Tag/Hot.svg" height="55" width="70" contain style="margin-left: -10px; margin-top: -10px;" v-else-if="itemProduct.message_status === 'hot'"></v-img>
              <v-img src="@/assets/Tag/Cool.svg" height="45" width="70" contain style="margin-left: -6px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'cool'"></v-img>
              <v-img src="@/assets/Tag/Recommend.svg" height="50" width="85" contain style="margin-left: 0px; margin-top: -12px;" v-else-if="itemProduct.message_status === 'recommend'"></v-img>
              <v-img src="@/assets/Tag/Pre-order.svg" height="40" width="75" contain style="margin-left: -5px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'pre-order'"></v-img>
              <v-img src="@/assets/Tag/BestSeller.svg" height="50" width="70" contain style="margin-left: -11px; margin-top: -8px;" v-else-if="itemProduct.message_status === 'best-seller'"></v-img>
              <v-img src="@/assets/Tag/Event1.png" height="70" width="55" contain style="margin-left: 10px; margin-top: -20px;" v-else-if="itemProduct.message_status === 'e-receipt'"></v-img>
            </v-col> -->
            <!-- <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && !IpadSize && MobileSize">
              <v-img src="@/assets/icons/Discount.png" height="45" width="45" contain style="margin-left: 30%;">
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
              </v-img>
            </v-col>
            <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && !IpadProSize && IpadSize && !MobileSize">
              <v-img src="@/assets/icons/Discount.png" height="45" width="45" contain style="margin-left: 30%;">
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 16px;" class="pt-1" v-else-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span>
                <span style="color: #FF0000; font-size: 12px; font-weight: bold; padding-left: 18px; display: block; line-height: 18px;">ลด</span>
              </v-img>
            </v-col>
            <v-col cols="6" md="6" sm="6" xs="6" style="display: flex; align-self: start;" v-else-if="(itemProduct.discount_percent !== '0%' && itemProduct.discount_percent !== '0.00' && itemProduct.discount_percent !== 0) && IpadProSize">
              <v-img src="@/assets/icons/Discount.png" height="55" width="55" contain style="margin-left: 25%;">
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) !== '%'">{{ itemProduct.discount_percent }}%</span><br/>
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;" v-if="itemProduct.discount_percent.charAt(itemProduct.discount_percent.length - 1) === '%'">{{ itemProduct.discount_percent }}</span><br/>
                <span style="color: #FF0000; font-size: 18px; font-weight: bold; padding-left: 10px;">ลด</span>
              </v-img>
            </v-col> -->
          <!-- </v-row> -->
        </v-img>
      </v-col>
      <!-- <p class="mb-0 mt-1 px-0 textSKUCardMobile">{{ itemProduct.sku }}</p> -->
      <v-tooltip bottom>
        <template v-slot:activator="{ on, attrs }">
          <!-- <v-card-text v-bind="attrs" v-on="on">{{ itemProduct.product_name|truncate(26, '...') }}</v-card-text> -->
          <v-col cols="12" md="12" class="px-0 pb-0">
            <v-row no-gutters>
              <v-col cols="12" md="10">
                <h1 v-bind="attrs" v-on="on" class="mb-0" style="max-width: 240px; font-size: 14px; font-weight: 700; -webkit-line-clamp: 2; -webkit-box-orient: vertical; display: -webkit-box;overflow: hidden; text-overflow: ellipsis;"
                 :class="MobileSize? 'fontMobile' : IpadSize? 'fontIpad' : 'font'">{{ itemProduct.name }}</h1>
              </v-col>
            </v-row>
          </v-col>
        </template>
        <span>{{ itemProduct.name }}</span>
      </v-tooltip>
       <v-row no-gutters :class="itemProduct.short_description === null || itemProduct.short_description === ''? 'mb-0': 'mb-0'">
        <p v-if="itemProduct.short_description === null || itemProduct.short_description === ''" class="text-truncate mb-0" :style="IpadProSize? 'font-size: 10px; font-weight: 400; max-width: 132px; color:transparent':'font-size: 12px; font-weight: 400; max-width: 132px; color:transparent'"></p><br v-if="itemProduct.short_description === null || itemProduct.short_description === ''"/>
        <p v-else class="text-truncate mb-0 mt-2" style="font-size: 10px; font-weight: 400; color: #9A9A9A; max-width: 100vw;">{{ itemProduct.short_description }}</p>
      </v-row>
      <v-col  cols="12" class="pa-0" :class="MobileSize || IpadSize? 'pb-1' : 'pb-4'">
        <v-chip x-small v-if="itemProduct.fda_number !== null && itemProduct.fda_number !== ''" color="#F3F5F7" class="square-chip pa-0" text-color="#636363"><v-img style="border-radius: 999px;" max-height="14px" max-width="14px" src="@/assets/FDA.jpg"></v-img> <span class="pl-1">เครื่องหมาย อย.</span></v-chip>
        <p v-else class="text-truncate mb-0" style="font-size: 10px; font-weight: 400; max-width: 132px; color:transparent">-</p>
      </v-col>
      <v-card-text class="pt-2 pb-0 px-0">
        <v-row dense>
          <v-rating
            v-model="itemProduct.stars"
            color="#FB9300"
            background-color="#C4C4C4"
            empty-icon="$ratingFull"
            half-increments
            hover
            small
            dense
            readonly
          ></v-rating>
          <v-spacer></v-spacer>
          <p v-if="itemProduct.sold !== undefined" class="pr-1 mb-0 mr-auto" style="font-size: 10px; font-weight: 400; margin-top: 1px;">ขายแล้ว {{itemProduct.sold | formatNumber}} ชิ้น</p>
          <p v-else class="pr-1 mr-auto" style="font-size: 10px; font-weight: 400; margin-top: 1px;"></p>
        </v-row>
      </v-card-text>
      <v-card-text class="pt-6 px-0" v-if="!MobileSize && !IpadSize && !IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="margin-top:13px">
          <span style="font-size: 24px; font-weight: 700;" >฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 24px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
        <!-- <span>{{ Number(itemProduct.revenue_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
        <!-- <span v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPrice">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPrice">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span> -->
      </v-card-text>
      <v-card-text class="pt-0 px-0" v-if="!MobileSize && !IpadSize && IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="margin-top:13px">
          <span style="font-size: 18px; font-weight: 700;" >฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <v-chip class="mx-0 ml-1" color="#FEE7E8" text-color="#F5222D" style="font-size: 8px; font-weight: 400;" x-small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <v-chip class="mx-0 ml-1" color="#FEE7E8" text-color="#F5222D" style="font-size: 8px; font-weight: 400;" x-small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 10px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
        <!-- <span>{{ Number(itemProduct.revenue_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
        <!-- <span v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPriceIPADPro">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecrese" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPriceIPADPro">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span> -->
      </v-card-text>
      <v-card-text class="pt-2 px-0" v-else-if="!MobileSize && IpadSize && !IpadProSize">
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="margin-top:13px">
          <span style="font-size: 18px; font-weight: 700;" >฿ {{ Number( itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <v-col cols="12" class="pa-0">
            <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          </v-col>
          <v-col cols="12" class="pa-0">
            <!-- <v-row no-gutters>
              <v-col cols="6"> -->
                <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              <!-- </v-col>
              <v-col cols="6"> -->
                <v-chip class="mx-0 ml-2" color="#FEE7E8" text-color="#F5222D" style="font-size: 8px; font-weight: 400;" x-small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
              <!-- </v-col>
            </v-row> -->
          </v-col>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <v-col cols="12" md="12">
            <span class="specialPrice" style="font-size: 18px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          </v-col>
          <v-col cols="12" md="12">
            <!-- <v-row no-gutters>
              <v-col cols="6" md="6"> -->
                <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              <!-- </v-col> -->
              <!-- <v-col cols="6" md="6"> -->
                <v-chip class="mx-0 ml-2" color="#FEE7E8" text-color="#F5222D" style="font-size: 8px; font-weight: 400;" x-small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
              <!-- </v-col>
            </v-row> -->
          </v-col>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 10px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
        <!-- <span>{{ Number(itemProduct.revenue_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
        <!-- <span v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecreseIPAD" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPriceIPAD">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecreseIPAD" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPriceIPAD">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span> -->
      </v-card-text>
      <v-card-text class="pt-0 px-0" v-else-if="MobileSize && !IpadSize && !IpadProSize">
        <!-- <span>{{ Number(itemProduct.revenue_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> -->
        <!-- <span v-if="itemProduct.real_price === itemProduct.fake_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2})  }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecreseIPAD" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPriceIPAD">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 16px;">฿ {{ Number(itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="priceDecreseIPAD" >฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="specialPriceIPAD">฿ {{ Number(itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span> -->
        <div v-if="(parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include) === itemProduct.fake_price || itemProduct.real_price === itemProduct.fake_price) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price === '' || itemProduct.special_price === undefined)" style="margin-top:13px">
          <span style="font-size: 20px; font-weight: 700;" >฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        </div>
        <div v-else-if="itemProduct.real_price !== itemProduct.fake_price && (itemProduct.special_price === '' || itemProduct.special_price === undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0" style="margin-top:8px">
          <span class="specialPrice" style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === itemProduct.special_price && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0 && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined)" style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.real_price) + parseFloat(itemProduct.vat_include):itemProduct.real_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <div v-else-if="itemProduct.real_price !== itemProduct.special_price && (itemProduct.special_price !== '' || itemProduct.special_price !== undefined) && itemProduct.real_price !== 0 && itemProduct.fake_price !== 0">
          <span class="specialPrice" style="font-size: 20px; font-weight: 700;">฿ {{ Number(itemProduct.vat_default === 'yes'? parseFloat(itemProduct.special_price) + parseFloat(itemProduct.vat_include) : itemProduct.special_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span><br>
          <span class="priceDecrese" style="font-size: 12px; font-weight: 600;">฿ {{ Number(itemProduct.fake_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
          <v-chip class="ml-2 " color="#FEE7E8" text-color="#F5222D" style="font-size: 10px; font-weight: 400;" small v-if="itemProduct.discount_percent !== '0%'">ส่วนลด -{{itemProduct.discount_percent}}</v-chip>
        </div>
        <span v-else-if="itemProduct.real_price === 0 && itemProduct.fake_price === 0" style="font-size: 16px;">
          ติดต่อสอบถามเจ้าหน้าที่
        </span>
      </v-card-text>
      <!-- <span class="ml-4" v-if="itemProduct.give_tier !== 'y'">฿ {{ Number(itemProduct.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
      <div v-else>
        <span style="font-weight: 300" class="priceDecrese ml-4" >฿ {{ Number(itemProduct.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
        <span class="specialPrice">฿ {{ Number(itemProduct.product_tier_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
      </div><br/> -->
    </v-card>
  </v-hover>
</template>

<script>
// import { Decode } from '@/services'
import Vue from 'vue'
export default {
  props: ['itemProduct'],
  filters: {
    truncate: function (value, limit) {
      if (value.length > limit) {
        value = value.substring(0, (limit - 4)) + '...'
      }
      return value
    }
  },
  data () {
    return {
      discription: 'หมวกนิรภัยป้องกันอุบัติหมวกนิรภัยป้องกันอุบัติ',
      rating: 5,
      favorite: false,
      priceSame: false,
      oneData: [],
      pathProductDetail: '',
      path: process.env.VUE_APP_DOMAIN,
      productID: '',
      namesPath: '',
      roleUser: '',
      productImage: ''
    }
  },
  created () {
    this.$EventBus.$on('checkRoleCardRes', this.checkRoleCardRes)
    if (localStorage.getItem('roleUser') !== null) {
      this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
    } else {
      this.roleUser = {
        role: 'ext_buyer'
      }
    }
    this.productImage = this.itemProduct.images_URL[0]
    this.formatSold()
    if (this.itemProduct !== undefined) {
      if (this.itemProduct.id !== undefined && this.itemProduct.id !== '') {
        if (this.itemProduct.link) {
          // console.log('tt1')
          this.pathProductDetail = this.itemProduct.link
        } else {
          // console.log('els')
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.id)
        }
      } else if (this.itemProduct.product_id !== undefined && this.itemProduct.product_id !== '') {
        if (this.itemProduct.link) {
          this.pathProductDetail = this.itemProduct.link
        } else {
          this.pathProductDetail = this.path + 'DetailProduct/' + encodeURIComponent(this.itemProduct.name.replace(/\s/g, '-') + '-' + this.itemProduct.product_id)
        }
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  methods: {
    formatSold () {
      Vue.filter('formatNumber', function (value) {
        if (!value) return 0
        if (value >= 1000) {
          return (value / 1000).toFixed(1) + 'พัน'
        }
        return value.toString()
      })
    },
    checkRoleCardRes () {
      this.roleUser = ''
      if (localStorage.getItem('roleUser') !== null) {
        this.roleUser = JSON.parse(localStorage.getItem('roleUser'))
      } else {
        this.roleUser = {
          role: 'ext_buyer'
        }
      }
    },
    DetailProduct (val) {
      if (val !== 'no') {
        // console.log(val)
        // console.log('DetailProduct2', val)
        localStorage.removeItem('an_id')
        const nameCleaned = val.name.replace(/\s/g, '-')
        if (val.id !== undefined && val.id !== '') {
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } }).catch(() => {})
        } else {
          this.$router.push({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.product_id}` } }).catch(() => {})
        }
        // const routeData = this.$router.resolve({ name: 'DetailProduct', params: { data: `${nameCleaned}-${val.id}` } })
        // window.location.assign(routeData.href, '_blank')
        // this.$router.push({ path: `${routeData.href}` }).catch(() => {})
      }
    },
    addRelated (e) {
      this.$EventBus.$emit('addRelated', e)
      this.$EventBus.$emit('addItemLanding', e)
    },
    RemoveRelated (e) {
      this.$EventBus.$emit('RemoveRelated', e)
      this.$EventBus.$emit('RemoveItemLanding', e)
    }
  }
}
</script>

<style scoped>
.fontMobile{
  line-height: 18px;
  height: 35px;
  max-height: 35px;
}
.fontIpad{
  line-height: 18px;
  height: 38.5px;
  max-height: 40px;
}
.font{
 line-height: 20px;
 height: 40px;
 max-height: 40px;
}
.custom-card {
  border: 1px solid #BDE7D9 !important; /* สีขอบของการ์ด */
  border-color: #BDE7D9 !important; /* สีขอบของการ์ดเมื่อไม่ได้โฮเวอร์ */
}
.card {
  height: 350px;
  max-height: 100%;
  /* max-height: 370px; */
  width: 100% !important;
  max-width: 250px;
}
.square-chip {
  padding: 0px;
  width: 53%; /* กำหนดความกว้าง */
  height: 14px; /* กำหนดความสูง */
  font-size: 10px; /* ขนาดตัวอักษร */
}
@media (max-width: 1366px) and (min-width: 1250px) {
  /* Media Query สำหรับ iPad Pro (1024px) */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 52%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
  .card {
    max-width: 19.5vw;
  }
}
@media (max-width: 1180px) and (min-width: 1025px) {
  /* Media Query สำหรับ iPad air แนวนอน */
  .square-chip {
    /* padding: 0px !important; */
    width: 52%;
    /* height: 14px;
    font-size: 10px; */
  }
  .card {
    max-width: 18vw;
  }
}
@media (max-width: 1250px) and (min-width: 1181px) {
  /* Media Query สำหรับ โน๊ตบุ๊คหน้าจอขนาดเล็ก */
  .square-chip {
    /* padding: 1px 0px 0px 1px; */
    width: 52%;
    /* height: 14px; */
    /* font-size: 10px; */
  }
  .card {
    max-width: 16.5vw;
  }
}
</style>
