import axios from 'axios'
import { Decode } from '@/services'

const GetToken = () => {
  if (localStorage.getItem('oneData') !== null) {
    const oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
    const auth = {
      headers: { Authorization: `Bearer ${oneData.user.access_token}` }
    }
    return auth
  } else {
    const auth = ''
    return auth
  }
}

export default {
  // ListSignature
  async ListSignature () {
    const auth = await GetToken()
    try {
      // console.log('=====>', auth)
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}digitalSignature/listSignature`, '', auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เพ่ิมSignature
  async SetSignature (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}digitalSignature/setSignature`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  // เพ่ิมSignature
  async EditSignature (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}digitalSignature/editSignature`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async DeleteSignature (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}digitalSignature/deleteSignature`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SetDefaultSignature (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}digitalSignature/setDefaultSignature`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  },
  async SignDocuments (data) {
    const auth = await GetToken()
    try {
      var response = await axios.post(`${process.env.VUE_APP_BACK_END2}digitalSignature/signDocuments`, data, auth)
      return response.data
    } catch (error) {
      return error.response.data
    }
  }
}
