<template>
  <v-dialog v-model="modalShowDetailPosition" persistent max-width="1000px">
    <v-card>
      <v-card-title style="display: flex; justify-content: center;">
        <b>รายละเอียดตำแหน่ง {{ this.positionName }}</b>
      </v-card-title>
      <v-card-text>
        <v-data-table
          v-if="selectedPositionDetails"
          :headers="tableHeaders"
          :items="[selectedPositionDetails]"
          :items-per-page="10"
          class="elevation-1 mt-4 data-table-spacing"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          :update:items-per-page="itemsPerPage"
          no-results-text="ไม่พบรายละเอียดตำแหน่ง"
          no-data-text="ไม่มีรายละเอียดตำแหน่ง"
        >
          <template v-slot:[`item.indexOfUser`]="{ index }">
            <span>{{ index + 1 }}</span>
          </template>
          <template v-slot:[`item.manage_position`]="{ item }">
            <span v-if="item.manage_position === '0'">
              <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ไม่มีสิทธิ์</v-chip>
            </span>
            <span v-else-if="item.manage_position === '1'">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">มีสิทธิ์</v-chip>
            </span>
          </template>
          <template v-slot:[`item.companies`]="{ item }">
            <span>
              <v-btn v-if="item.companies.length" @click="viewCompaniesDetails(item.companies)" outlined color="#27AB9C" small>ดูรายละเอียด</v-btn>
              <span v-else>ไม่มีข้อมูล</span>
            </span>
          </template>
          <template v-slot:[`item.shops`]="{ item }">
            <span>
              <v-btn v-if="item.shops.length" @click="viewShopsDetails(item.shops)" outlined color="#27AB9C" small>ดูรายละเอียด</v-btn>
              <span v-else>ไม่มีข้อมูล</span>
            </span>
          </template>
        </v-data-table>

        <v-text-field
          v-if="selectedCompanyDetails.length"
          v-model="companySearch"
          label="ค้นหาชื่อบริษัท"
          prepend-icon="mdi-magnify"
          single-line
          hide-details
        ></v-text-field>

        <v-data-table
          v-if="selectedCompanyDetails.length"
          :headers="companyTableHeaders"
          :items="selectedCompanyDetails"
          :search="companySearch"
          :items-per-page="10"
          class="elevation-1 mt-4 data-table-spacing"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          :update:items-per-page="itemsPerPage"
          no-results-text="ไม่มีรายละเอียดบริษัทที่ได้ค้นหา"
          no-data-text="ไม่มีรายละเอียดบริษัท"
        >
          <template v-slot:[`item.indexOfUser`]="{ index }">
            <span>{{ index + 1 }}</span>
          </template>
          <template v-slot:[`item.name_th`]="{ item }">
            <v-row align="center">
              <v-col cols="auto">
                <v-img width="50" height="70" src="@/assets/NoImage.png" v-if="item.img_path === null" contain></v-img>
                <v-img width="50" height="70" :src="`${item.img_path}`" v-else contain></v-img>
              </v-col>
              <v-col style="text-align: start;">
                <span>{{ item.name_th }}</span>
              </v-col>
            </v-row>
          </template>
          <template v-slot:[`item.status`]="{ item }">
            <span v-if="item.status === 'active'">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ใช้งานได้</v-chip>
            </span>
            <span v-else-if="item.status === 'inactive'">
              <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
            </span>
          </template>
          <template v-slot:[`item.access_company_member`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.access_company_member === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.access_company_member === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
          <template v-slot:[`item.access_company_order`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.access_company_order === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.access_company_order === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
          <template v-slot:[`item.assign_company_permission`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.assign_company_permission === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.assign_company_permission === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
        </v-data-table>

        <v-text-field
          v-if="selectedShopDetails.length"
          v-model="shopSearch"
          label="ค้นหาชื่อร้านค้า"
          prepend-icon="mdi-magnify"
          single-line
          hide-details
        ></v-text-field>

        <v-data-table
          v-if="selectedShopDetails.length"
          :headers="shopTableHeaders"
          :items="selectedShopDetails"
          :search="shopSearch"
          :items-per-page="10"
          class="elevation-1 mt-4 data-table-spacing"
          :footer-props="{'items-per-page-text':'จำนวนแถว'}"
          :update:items-per-page="itemsPerPage"
          no-results-text="ไม่มีรายละเอียดร้านค้าที่ได้ค้นหา"
          no-data-text="ไม่มีรายละเอียดร้านค้า"
        >
          <template v-slot:[`item.indexOfUser`]="{ index }">
            <span>{{ index + 1 }}</span>
          </template>
          <template v-slot:[`item.name_th`]="{ item }">
            <v-row align="center">
              <v-col cols="auto">
                <v-img width="50" height="70" src="@/assets/NoImage.png" v-if="item.path_logo === null" contain></v-img>
                <v-img width="50" height="70" :src="`${item.path_logo}`" v-else contain></v-img>
              </v-col>
              <v-col style="text-align: start;">
                <span>{{ item.name_th }}</span>
              </v-col>
            </v-row>
          </template>
          <template v-slot:[`item.shop_status`]="{ item }">
            <span v-if="item.shop_status === 'active'">
              <v-chip class="ma-2" color="#F0F9EE" text-color="#1AB759">ใช้งานได้</v-chip>
            </span>
            <span v-else-if="item.shop_status === 'inactive'">
              <v-chip class="ma-2" color="#F7D9D9" text-color="#D1392B">ยกเลิก</v-chip>
            </span>
          </template>
          <template v-slot:[`item.access_shop_product`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.access_shop_product === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.access_shop_product === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
          <template v-slot:[`item.access_shop_order`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.access_shop_order === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.access_shop_order === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
          <template v-slot:[`item.access_shop_user`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.access_shop_user === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.access_shop_user === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
          <template v-slot:[`item.assign_shop_permission`]="{ item }">
            <div class="d-flex justify-center align-center">
              <v-img v-if="item.assign_shop_permission === '0'" width="40" height="40" src="@/assets/icons/incorrect.png" contain></v-img>
              <v-img v-else-if="item.assign_shop_permission === '1'" width="40" height="40" src="@/assets/icons/correct.png" contain></v-img>
            </div>
          </template>
        </v-data-table>

      </v-card-text>
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn color="#27AB9C" class="white--text rounded-button" @click="modalShowDetailPosition = false">ปิด</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script>
export default {
  data () {
    return {
      itemsPerPage: 10,
      modalShowDetailPosition: false,
      selectedPositionDetails: null,
      selectedCompanyDetails: [],
      selectedShopDetails: [],
      positions: [],
      companySearch: '',
      shopSearch: '',
      positionName: '',
      tableHeaders: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'จัดการตำแหน่ง', value: 'manage_position', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'บริษัท', value: 'companies', align: 'center', filterable: false, sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ร้านค้า', value: 'shops', align: 'center', filterable: false, sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      companyTableHeaders: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อบริษัท', value: 'name_th', align: 'center', sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'status', align: 'center', sortable: false, width: '50px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบบุคลากรบริษัท', value: 'access_company_member', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบรายการสั่งซื้อของบริษัท', value: 'access_company_order', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'แก้ไขสิทธิภายในบริษัท', value: 'assign_company_permission', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      shopTableHeaders: [
        { text: 'ลำดับ', value: 'indexOfUser', width: '50', align: 'center', filterable: false, sortable: false, class: 'backgroundTable fontTable--text' },
        { text: 'ชื่อร้านค้า', value: 'name_th', align: 'center', sortable: false, width: '180px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะ', value: 'shop_status', align: 'center', sortable: false, width: '50px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        // { text: 'ตรวจสอบรายการสินค้าของร้าน', value: 'access_shop_product', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบรายการสั่งซื้อของร้าน', value: 'access_shop_order', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ตรวจสอบสมาชิกภายในร้าน', value: 'access_shop_user', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'แก้ไขสิทธิภายในร้านค้า', value: 'assign_shop_permission', align: 'center', sortable: false, width: '100px', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ]
    }
  },
  computed: {
    filteredCompanyDetails () {
      return this.selectedCompanyDetails.filter(company =>
        company.name_th.toLowerCase().includes(this.companySearch.toLowerCase())
      )
    },
    filteredShopDetails () {
      return this.selectedShopDetails.filter(shop =>
        shop.name_th.toLowerCase().includes(this.shopSearch.toLowerCase())
      )
    }
  },
  methods: {
    open (data, position) {
      this.resetDetails()
      this.sendData = data
      this.positionID = position.position_id
      this.positionName = position.position_name
      this.modalShowDetailPosition = true
      this.selectedPositionDetails = this.positions.find(pos => pos.position_id === this.positionID) || position
    },
    resetDetails () {
      // reset ทุกครั้ง ที่เลือกดูรายละเอียด
      this.selectedCompanyDetails = []
      this.selectedShopDetails = []
    },
    viewCompaniesDetails (companies) {
      if (this.selectedCompanyDetails.length && this.selectedCompanyDetails[0].name_th === companies[0].name_th) {
        // ถ้ารายละเอียดบริษัทเปิดอยู่แล้วให้ปิด
        this.selectedCompanyDetails = []
      } else {
        this.selectedCompanyDetails = companies.map(company => ({
          img_path: company.img_path,
          name_th: company.name_th,
          status: company.status,
          access_company_member: company.access_company_member,
          access_company_order: company.access_company_order,
          assign_company_permission: company.assign_company_permission
        }))
      }
    },
    viewShopsDetails (shops) {
      if (this.selectedShopDetails.length && this.selectedShopDetails[0].name_th === shops[0].name_th) {
        // ถ้ารายละเอียดร้านค้าเปิดอยู่แล้วให้ปิด
        this.selectedShopDetails = []
      } else {
        this.selectedShopDetails = shops.map(shop => ({
          path_logo: shop.path_logo,
          name_th: shop.name_th,
          shop_status: shop.shop_status,
          access_shop_product: shop.access_shop_product,
          access_shop_order: shop.access_shop_order,
          access_shop_user: shop.access_shop_user,
          assign_shop_permission: shop.assign_shop_permission
        }))
      }
    }
  }
}
</script>

<style lang="css" scoped>
::-webkit-scrollbar {
  width: 6px;
}/* Track */
::-webkit-scrollbar-track {
  background: #f1f1f1;
}
::-webkit-scrollbar-thumb {
  background: #E6E6E6;
  border-radius: 8px;
}
::-webkit-scrollbar-thumb:hover {
  background: #888;
  border-radius: 8px;
}
</style>

<style>
.checkbox-admin .v-input--selection-controls__input {
  margin-right: 0px !important;
}
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.doc-detail {
  font-size: 14px;
  text-align: center;
  font-weight: 600;
}
.blod-detail {
  font-size: 16px;
  font-weight: 600;
}
.title-detail {
  font-size: 14px;
  font-weight: 400;
}
.data-table-spacing {
  margin-bottom: 10px;
}
</style>
