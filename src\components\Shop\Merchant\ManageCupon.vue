<template>
  <div class="mb-2">
    <v-row no-gutters>
      <v-col cols="12" class="mt-6">
        <div v-if="!MobileSize">
          <span style="font-weight: 700; font-size: 24px; line-height: 32px;" class="ml-4" v-if="statusPage === 'Edit'">แก้ไขคูปอง</span>
          <span style="font-weight: 700; font-size: 24px; line-height: 32px;" class="ml-4" v-else>สร้างคูปอง</span>
        </div>
        <div v-else>
          <span style="font-weight: bold; font-size: 18px; line-height: 40px;" class="ml-2" v-if="statusPage === 'Edit'"><v-icon @click="backToMerChant()" color="#27AB9C" >mdi-chevron-left</v-icon> แก้ไขคูปอง</span>
          <span style="font-weight: bold; font-size: 18px; line-height: 40px;" class="ml-2" v-else><v-icon @click="backToMerChant()" color="#27AB9C" >mdi-chevron-left</v-icon> สร้างคูปอง</span>
        </div>
      </v-col>
      <v-col cols="12" class="mt-6">
        <v-row no-gutters class="ml-4 mr-4">
          <v-form ref="formCupon" :lazy-validation="lazy">
            <!-- การ์ดหนึ่ง -->
            <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;">
              <v-card-text>
                <v-row no-gutters v-if="statusPage !== 'Edit'">
                  <!-- ข้อมูลทั่วไป -->
                  <v-col cols="12">
                    <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ข้อมูลทั่วไป</span>
                  </v-col>
                  <!-- เพิ่มรูปภาพ -->
                  <v-col cols="12" md="12" class="mt-6 px-0">
                    <v-card
                      elevation="0"
                      width="100%"
                      height="100%"
                      style="background: #FFFFFF; border-radius: 8px;"
                    >
                      <v-card-text>
                        <v-card
                          elevation="0"
                          style="border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;"
                          @click="onPickFile()"
                          :disabled="statusPage === 'Edit' ? true : false"
                        >
                          <v-card-text>
                            <v-row
                              no-gutters
                              align="center"
                              justify="center"
                              style="cursor: pointer;"
                            >
                              <v-file-input
                                v-model="DataImage"
                                :items="DataImage"
                                accept="image/jpeg, image/jpg, image/png, video/*"
                                @change="UploadImage()"
                                id="file_input"
                                multiple
                                :clearable="false"
                                style="display:none"
                              >
                              </v-file-input>
                              <v-col cols="12" md="12" class="mb-6">
                                <v-row justify="center" class="pt-10">
                                  <v-img
                                    src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                    width="280.34"
                                    height="154.87"
                                    contain
                                  ></v-img>
                                </v-row>
                              </v-col>
                              <v-col cols="12" md="12" class="mt-6">
                                <v-row justify="center" align="center">
                                  <v-col cols="12" md="4" style="text-align: center;">
                                    <span
                                      style="font-size: 16px; line-height: 24px; font-weight: 400;"
                                      >เพิ่มรูปภาพของคุณที่นี่</span
                                    ><br />
                                    <span
                                      style="font-size: 16px; line-height: 24px; font-weight: 400;"
                                      >หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span
                                    ><br />
                                    <span
                                      style="font-size: 12px; line-height: 16px; font-weight: 400;"
                                      >(ไฟล์นามสกุล .JPEG, .PNG เพิ่มได้สูงสุด 1 รูปภาพ)</span
                                    ><br />
                                    <!-- <span
                                      style="font-size: 12px; line-height: 16px; font-weight: 400;"
                                      ><span style="color: red;">***</span> หมายเหตุ
                                      ไฟล์รูปควรมีขนาดไม่เกิน 6 MB</span
                                    ><br/> -->
                                    <!-- <span
                                      style="font-size: 12px; line-height: 16px; font-weight: 400;"
                                      ><span style="color: red;">***</span> รูปแรกที่อัปโหลดจะเป็นรูปโปรไฟล์ของร้านค้าและรูปที่เหลือเป็นรูปแบนเนอร์ของร้านค้า</span
                                    > -->
                                  </v-col>
                                </v-row>
                              </v-col>
                            </v-row>
                          </v-card-text>
                        </v-card>
                        <div v-if="cupon_images.length !== 0" class="mt-4">
                          <draggable v-model="cupon_images"  :move="onMove" @start="drag=true" @end="drag=false" class="pl-5 pr-5 row  fill-height align-center sortable-list">
                            <v-col v-for="(item, index) in cupon_images" :key="index" cols="12" md="2" sm="4" xs="6">
                              <v-card  outlined class="pa-1" width="146" height="146" :disabled="statusPage === 'Edit' ? true : false" v-if="item.path !== ''">
                                <v-img  :src="item.path" :lazy-src="item.url" width="130" height="130" contain>
                                  <v-btn icon x-small style="float: right; background-color: #ff5252;">
                                    <v-icon x-small color="white" dark @click="RemoveImage(index, item)">mdi-close</v-icon>
                                  </v-btn>
                                </v-img>
                              </v-card>
                            </v-col>
                          </draggable>
                        </div>
                      </v-card-text>
                    </v-card>
                  </v-col>
                </v-row>
                <v-row no-gutters :class="statusPage === 'Edit' ? '' : 'mt-6'">
                  <!-- ข้อมูลคูปอง -->
                  <v-col cols="12" md="12" class="">
                    <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">ข้อมูลคูปอง</span>
                  </v-col>
                  <v-col cols="12" md="12" class="mt-4">
                    <v-row dense no-gutters>
                      <!-- ชื่อคูปอง -->
                      <v-col cols="12" md="6">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ชื่อคูปอง <span style="color: red;">*</span></span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-text-field
                              v-model="cuponName"
                              outlined
                              dense
                              :rules="Rules.cupon_name"
                              :maxLength="30"
                              placeholder="ระบุชื่อคูปอง"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- โค้ดส่วนลด -->
                      <v-col cols="12" md="6">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">โค้ดส่วนลด</span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-text-field
                              v-model="cuponCode"
                              outlined
                              dense
                              :maxLength="6"
                              :rules="Rules.cupon_code"
                              oninput="this.value = this.value.replace(/[^a-zA-Z0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                              placeholder="โค้ดรหัสส่วนลด 4-6 หลัก"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- รายละเอียดคูปอง -->
                      <v-col cols="12" md="12">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">รายละเอียดคูปอง <span style="color: red;">*</span></span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-textarea
                             v-model="detailCupon"
                             outlined
                             hide-details
                             :rules="Rules.cupon_detail"
                             placeholder="รายละเอียดคูปอง"
                            ></v-textarea>
                          </v-col>
                          <span style="color: #F5222D;font-weight: 400; font-size: 12px; line-height: 16px;" class="mt-2 ml-1">* รายละเอียดคูปอง วิธีใช้งานและสิทธิประโยชน์สำหรับสมาชิกอย่างละเอียด</span>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            <!-- การ์ดสอง -->
            <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;" class="mt-6">
              <v-card-text>
                <v-row no-gutters>
                  <!-- กำหนดระยะเวลาคูปอง -->
                  <v-col cols="12">
                    <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">กำหนดระยะเวลาคูปอง</span>
                  </v-col>
                  <!-- ระยะเวลาเก็บคูปอง -->
                  <v-col cols="12" md="12" class="mt-4">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ระยะเวลาเก็บคูปอง <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                        <v-row dense no-gutters class="mt-2">
                          <v-col cols="12" md="2" class="pt-2">
                            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #636363;">วันที่เริ่ม - สิ้นสุด</span>
                          </v-col>
                          <v-col cols="12" md="10" class="mb-0 pb-0">
                            <v-row dense no-gutters>
                              <v-col cols="5" md="3" sm="5">
                                <!-- วันเริ่มต้น -->
                                <v-dialog
                                  ref="dialogStartDateKeepCupon"
                                  v-model="modalStartDateKeepCupon"
                                  persistent
                                  width="290px"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="StartDateKeepCupon"
                                      readonly
                                      outlined
                                      dense
                                      v-bind="attrs"
                                      v-on="on"
                                      placeholder="DD/MM/YYYY"
                                      :rules="Rules.date"
                                    ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="KeepCuponStartDate"
                                    scrollable
                                    flat
                                    :max="maxSelectEndDateToKeep"
                                    :min="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                                  >
                                    <v-row dense>
                                      <v-col cols="12" class="pt-0">
                                        <v-checkbox v-model="disableTimeKeepStart" label="เวลา" color="#27AB9C" hide-details></v-checkbox>
                                        <v-menu
                                          ref="menu"
                                          v-model="menuStartTimeKeepCupon"
                                          :close-on-content-click="false"
                                          :nudge-right="40"
                                          transition="scale-transition"
                                          offset-y
                                          max-width="290px"
                                          min-width="290px"
                                          v-if="disableTimeKeepStart"
                                        >
                                          <template v-slot:activator="{ on, attrs }">
                                            <v-text-field
                                              v-model="StarttimeKeepCupon"
                                              readonly
                                              outlined
                                              dense
                                              v-bind="attrs"
                                              v-on="on"
                                              class="mt-2"
                                              placeholder="ระบุเวลา"
                                              v-if="disableTimeKeepStart"
                                            ><v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon></v-text-field>
                                          </template>
                                          <v-time-picker
                                            v-if="menuStartTimeKeepCupon"
                                            v-model="StarttimeKeepCupon"
                                            full-width
                                            format="24hr"
                                            :min="minTimeStartKeep"
                                            @click:minute="$refs.menu.save(StarttimeKeepCupon)"
                                          ></v-time-picker>
                                        </v-menu>
                                      </v-col>
                                      <v-col cols="12" align="end">
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="modalStartDateKeepCupon = false"
                                        >
                                          Cancel
                                        </v-btn>
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="SaveStartDateKeepCupon(KeepCuponStartDate, StarttimeKeepCupon)"
                                        >
                                          OK
                                        </v-btn>
                                      </v-col>
                                    </v-row>
                                  </v-date-picker>
                                </v-dialog>
                              </v-col>
                              <v-col cols="2" md="1" sm="2" :class="IpadSize ? 'pl-7 pt-2' : 'pl-7 pt-2'" v-if="disableTimeKeepCupon === false"> - </v-col>
                              <!-- วันสิ้นสุด -->
                              <v-col cols="5" md="3" sm="5" v-if="disableTimeKeepCupon === false">
                                <v-dialog
                                  ref="dialogEndDateKeepCupon"
                                  v-model="modalEndDateKeepCupon"
                                  persistent
                                  width="290px"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="EndDateKeepCupon"
                                      readonly
                                      outlined
                                      dense
                                      v-bind="attrs"
                                      v-on="on"
                                      placeholder="DD/MM/YYYY"
                                      :rules="Rules.date"
                                    ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="KeepCuponEndDate"
                                    scrollable
                                    :min="KeepCuponStartDate"
                                    @click:date="selectDate($event)"
                                  >
                                    <v-row dense>
                                      <v-col cols="12" class="pt-0">
                                        <v-checkbox v-model="disableTimeKeepEnd" label="เวลา" color="#27AB9C" hide-details></v-checkbox>
                                        <v-menu
                                          ref="menu1"
                                          v-model="menuEndTimeKeepCupon"
                                          :close-on-content-click="false"
                                          :nudge-right="40"
                                          transition="scale-transition"
                                          offset-y
                                          max-width="290px"
                                          min-width="290px"
                                          v-if="disableTimeKeepEnd"
                                        >
                                          <template v-slot:activator="{ on, attrs }">
                                            <v-text-field
                                              v-model="EndtimeKeepCupon"
                                              readonly
                                              outlined
                                              dense
                                              v-bind="attrs"
                                              v-on="on"
                                              class="mt-2"
                                              placeholder="ระบุเวลา"
                                              v-if="disableTimeKeepEnd"
                                            ><v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon></v-text-field>
                                          </template>
                                          <v-time-picker
                                            v-if="menuEndTimeKeepCupon"
                                            v-model="EndtimeKeepCupon"
                                            full-width
                                            format="24hr"
                                            :max="maxSelectTimeEndKeep"
                                            :min="minTimeEndKeep"
                                            @click:minute="$refs.menu1.save(EndtimeKeepCupon)"
                                          ></v-time-picker>
                                        </v-menu>
                                      </v-col>
                                      <v-col cols="12" align="end">
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="modalEndDateKeepCupon = false"
                                        >
                                          Cancel
                                        </v-btn>
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="SaveEndDateKeepCupon(UseCuponEndDate, EndtimeKeepCupon)"
                                        >
                                          OK
                                        </v-btn>
                                      </v-col>
                                    </v-row>
                                  </v-date-picker>
                                </v-dialog>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="mt-0">
                        <v-checkbox v-model="disableTimeKeepCupon" label="ไม่ระบุวันสิ้นสุด" color="#27AB9C" hide-details class="mt-0"></v-checkbox>
                      </v-col>
                    </v-row>
                  </v-col>
                  <!-- ระยะเวลาใช้โปรโมชัน -->
                  <v-col cols="12" md="12" class="mt-4">
                    <v-row dense>
                      <v-col cols="12">
                        <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ระยะเวลาใช้โปรโมชัน <span style="color: red;">*</span></span>
                      </v-col>
                      <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                        <v-row dense no-gutters class="mt-2">
                          <v-col cols="12" md="2" class="pt-2">
                            <span style="font-weight: 400; font-size: 16px; line-height: 24px; color: #636363;">วันที่เริ่ม - สิ้นสุด</span>
                          </v-col>
                          <v-col cols="12" md="10">
                            <v-row dense no-gutters>
                              <v-col cols="5" md="3" sm="5">
                                <!-- วันเริ่มต้น -->
                                <v-dialog
                                  ref="dialogStartDateUseCupon"
                                  v-model="modalStartDateUseCupon"
                                  persistent
                                  width="290px"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="StartDateUseCupon"
                                      readonly
                                      outlined
                                      dense
                                      v-bind="attrs"
                                      v-on="on"
                                      placeholder="DD/MM/YYYY"
                                      :rules="Rules.date"
                                    ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="UseCuponStartDate"
                                    scrollable
                                    :max="maxSelectEndDateToUse"
                                    :min="(new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)"
                                  >
                                    <v-row dense>
                                      <v-col cols="12" class="pt-0">
                                        <v-checkbox v-model="disableTimeUseStart" label="เวลา" color="#27AB9C" hide-details></v-checkbox>
                                        <v-menu
                                          ref="menu2"
                                          v-model="menuStartTimeUseCupon"
                                          :close-on-content-click="false"
                                          :nudge-right="40"
                                          transition="scale-transition"
                                          offset-y
                                          max-width="290px"
                                          min-width="290px"
                                          v-if="disableTimeUseStart"
                                        >
                                          <template v-slot:activator="{ on, attrs }">
                                            <v-text-field
                                              v-model="StartTimeUseCupon"
                                              readonly
                                              outlined
                                              dense
                                              v-bind="attrs"
                                              v-on="on"
                                              class="mt-2"
                                              placeholder="ระบุเวลา"
                                              v-if="disableTimeUseStart"
                                            ><v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon></v-text-field>
                                          </template>
                                          <v-time-picker
                                            v-if="menuStartTimeUseCupon"
                                            v-model="StartTimeUseCupon"
                                            full-width
                                            :max="maxSelectTimeEndUse"
                                            format="24hr"
                                            :min="minTimeStartUse"
                                            @click:minute="$refs.menu2.save(StartTimeUseCupon)"
                                          ></v-time-picker>
                                        </v-menu>
                                      </v-col>
                                      <v-col cols="12" align="end">
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="modalStartDateUseCupon = false"
                                        >
                                          Cancel
                                        </v-btn>
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="SaveStartDateUseCupon(UseCuponStartDate, StartTimeUseCupon)"
                                        >
                                          OK
                                        </v-btn>
                                      </v-col>
                                    </v-row>
                                  </v-date-picker>
                                </v-dialog>
                              </v-col>
                              <v-col cols="2" md="1" sm="2" class="pl-7 pt-2" v-if="disableTimeUseCupon === false"> - </v-col>
                              <!-- วันสิ้นสุด -->
                              <v-col cols="5" md="3" sm="5" v-if="disableTimeUseCupon === false">
                                <v-dialog
                                  ref="dialogEndDateUseCupon"
                                  v-model="modalEndDateUseCupon"
                                  persistent
                                  width="290px"
                                >
                                  <template v-slot:activator="{ on, attrs }">
                                    <v-text-field
                                      v-model="EndDateUseCupon"
                                      readonly
                                      outlined
                                      dense
                                      v-bind="attrs"
                                      v-on="on"
                                      placeholder="DD/MM/YYYY"
                                      :rules="Rules.date"
                                    ><v-icon slot="append" color="#27AB9C">mdi-calendar </v-icon></v-text-field>
                                  </template>
                                  <v-date-picker
                                    v-model="UseCuponEndDate"
                                    scrollable
                                    @click:date="selectDateUse($event)"
                                    :min="UseCuponStartDate"
                                  >
                                    <v-row dense>
                                      <v-col cols="12" class="pt-0">
                                        <v-checkbox v-model="disableTimeUseEnd" label="เวลา" color="#27AB9C" hide-details></v-checkbox>
                                        <v-menu
                                          ref="menu2"
                                          v-model="menuEndTimeUseCupon"
                                          :close-on-content-click="false"
                                          :nudge-right="40"
                                          transition="scale-transition"
                                          offset-y
                                          max-width="290px"
                                          min-width="290px"
                                          v-if="disableTimeUseEnd"
                                        >
                                          <template v-slot:activator="{ on, attrs }">
                                            <v-text-field
                                              v-model="EndTimeUseCupon"
                                              readonly
                                              outlined
                                              dense
                                              v-bind="attrs"
                                              v-on="on"
                                              class="mt-2"
                                              placeholder="ระบุเวลา"
                                              v-if="disableTimeUseEnd"
                                            ><v-icon slot="append" color="#27AB9C">mdi-clock-time-four-outline </v-icon></v-text-field>
                                          </template>
                                          <v-time-picker
                                            v-if="menuEndTimeUseCupon"
                                            v-model="EndTimeUseCupon"
                                            full-width
                                            format="24hr"
                                            :max="maxSelectTimeEndUse"
                                            :min="minTimeEndUse"
                                            @click:minute="$refs.menu2.save(EndTimeUseCupon)"
                                          ></v-time-picker>
                                        </v-menu>
                                      </v-col>
                                      <v-col cols="12" align="end">
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="modalEndDateUseCupon = false"
                                        >
                                          Cancel
                                        </v-btn>
                                        <v-btn
                                          text
                                          color="primary"
                                          @click="SaveEndDateUseCupon(UseCuponEndDate, EndTimeUseCupon)"
                                        >
                                          OK
                                        </v-btn>
                                      </v-col>
                                    </v-row>
                                  </v-date-picker>
                                </v-dialog>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                      <v-col cols="12" class="mt-0">
                        <v-checkbox v-model="disableTimeUseCupon" label="ไม่ระบุวันสิ้นสุด" color="#27AB9C" hide-details class="mt-0"></v-checkbox>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            <!-- การ์ดสาม -->
            <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;" class="mt-6">
              <v-card-text>
                <v-row no-gutters>
                  <!-- กำหนดจำนวนคูปอง และสิทธิ์คูปอง -->
                  <v-col cols="12">
                    <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">กำหนดจำนวนคูปอง และสิทธิ์คูปอง</span>
                  </v-col>
                  <v-col cols="12" md="12" class="mt-4">
                    <v-row dense no-gutters>
                      <!-- จำนวนคูปอง -->
                      <v-col cols="12" md="6">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">จำนวนคูปอง <span style="color: red;">*</span></span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-text-field
                              v-model="cuponCount"
                              outlined
                              dense
                              :rules="[checkMaxValueOfCoupon(cuponCount)]"
                              oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                              placeholder="ระบุจำนวนคูปอง"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- จำนวนคูปองที่ใช้ได้จริง -->
                      <v-col cols="12" md="6">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">จำนวนคูปองที่ใช้ได้จริง <span style="color: red;">*</span></span>
                          </v-col>
                          <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-text-field
                              v-model="cuponCountReal"
                              outlined
                              dense
                              :rules="[Rules.loanCkeckCoupon(cuponCountReal, cuponCount), checkMaxValueOfCouponReal(cuponCountReal)]"
                              oninput="this.value = this.value.replace(/[^a-zA-Z0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                              placeholder="ระบุจำนวนคูปองที่ใช้ได้จริง"
                            ></v-text-field>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- สิทธิ์การใช้คูปอง -->
                      <v-col cols="12" md="12">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">สิทธิ์การใช้คูปอง <span style="color: red;">* </span></span>
                          </v-col>
                          <v-col cols="12"  class="mt-0 pb-0" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-radio-group v-model="authorityCupon" :rules="Rules.authority_cupon">
                              <v-radio
                                v-for="(item, index) in dataAuthorityCupon"
                                :key="index"
                                :label="`${item.lable}`"
                                :value="item.value"
                              ></v-radio>
                            </v-radio-group>
                          </v-col>
                          <v-col cols="12" class="pt-0 mt-0" v-if="authorityCupon === 3">
                            <v-row dense class="pr-2 pl-2 pt-0 mt-0">
                              <v-col cols="4" md="2" sm="4">
                                <v-text-field v-model="showText" outlined dense readonly></v-text-field>
                              </v-col>
                              <v-col cols="8" md="10" sm="8">
                                <v-text-field v-model="amountAuthorityCupon" :rules="[Rules.amount_Authority_Cupon(cuponCountReal, amountAuthorityCupon)]" outlined dense oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" suffix="สิทธิ์" placeholder="ระบุจำนวนสิทธิ์"></v-text-field>
                              </v-col>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- ประเภทส่วนลด -->
                      <v-col cols="12" md="12">
                        <v-row dense>
                          <v-col cols="12">
                            <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ประเภทส่วนลด <span style="color: red;">* (เลือกได้ 1 รูปแบบ)</span></span>
                          </v-col>
                          <v-col cols="12"  class="mt-0" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                            <v-radio-group v-model="DiscountCupon" :rules="Rules.discount_cupon">
                              <v-radio
                                v-for="(item, index) in dataDiscountCupon"
                                :key="index"
                                :label="`${item.lable}`"
                                :value="item.value"
                              ></v-radio>
                            </v-radio-group>
                            <v-row dense no-gutters class="mt-0" v-if="DiscountCupon === 'money'">
                              <v-col cols="12">
                                <v-text-field v-model="amountDiscountMoney" suffix="บาท" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" :rules="Rules.moneyDiscount" dense outlined placeholder="ระบุส่วนลดที่ต้องการ"></v-text-field>
                              </v-col>
                            </v-row>
                            <v-row dense no-gutters class="mt-0" v-if="DiscountCupon === 'percent'">
                              <v-col cols="12">
                                <v-text-field v-model="amountDiscountMoneyPercent" suffix="%" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" :rules="Rules.percentDiscount" dense outlined placeholder="ระบุส่วนลดที่ต้องการ"></v-text-field>
                              </v-col>
                            </v-row>
                            <v-row dense no-gutters class="ml-4 mt-0" v-if="DiscountCupon === 'percent'">
                              <v-radio-group
                                v-model="DiscountCuponSelect"
                                class="mt-0"
                              >
                                <!-- จำกัดจำนวนเงิน -->
                                <v-col cols="12" class="my-0 pb-0 pt-0">
                                  <v-row dense no-gutters>
                                    <v-radio
                                      label="จำกัดจำนวนเงิน"
                                      value="1"
                                    ></v-radio>
                                    <v-text-field v-model="amountDiscount" suffix="บาท" :rules="Rules.amountDiscount" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" dense outlined placeholder="ระบุส่วนลดที่ต้องการ" class="ml-4" v-if="DiscountCuponSelect === '1'"></v-text-field>
                                  </v-row>
                                </v-col>
                                <!-- ไม่จำกัดจำนวนเงิน -->
                                <v-col cols="12" class="my-0 pb-0">
                                  <v-radio
                                    label="ไม่จำกัดจำนวนเงิน"
                                    value="2"
                                  ></v-radio>
                                </v-col>
                              </v-radio-group>
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                      <!-- ค่าใช้จ่ายขั้นต่ำ -->
                      <v-col cols="12" md="12">
                        <v-row dense no-gutters>
                          <!-- ค่าใช้จ่ายขั้นต่ำ -->
                          <v-col cols="12" md="12">
                            <v-row dense>
                              <v-col cols="12">
                                <span style="font-weight: 500; font-size: 16px; line-height: 24px; color: #333333;">ค่าใช้จ่ายขั้นต่ำ <span style="color: red;">*</span></span>
                              </v-col>
                              <v-col cols="12" :class="MobileSize ? '' : IpadSize ? '' : 'pr-4'">
                                <v-text-field
                                  v-model="UseCuponLimit"
                                  outlined
                                  dense
                                  :rules="[Rules.UseCupon_Limit(UseCuponLimit, amountDiscountMoney)]"
                                  suffix="บาท"
                                  oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"
                                  placeholder="ระบุค่าใช้จ่ายขั้นต่ำในการใช้คูปอง (* ยอดใช้จ่ายขั้นต่ำของการใช้สิทธิ์คูปอง)"
                                ></v-text-field>
                              </v-col>
                              <!-- <span style="color: #F5222D;font-weight: 400; font-size: 12px; line-height: 16px;" class="mt-1 ml-1">* ยอดใช้จ่ายขั้นต่ำของการใช้สิทธิ์คูปอง</span> -->
                            </v-row>
                          </v-col>
                        </v-row>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            <!-- การ์ดสี่ -->
            <v-card elevation="0" width="100%" style="background: #FAFAFA; box-shadow: 0px 0px 2px rgba(0, 0, 0, 0.15); border-radius: 8px;" class="mt-6">
              <v-card-text>
                <v-row no-gutters>
                  <!-- รูปแบบที่สามารถใช้คูปองได้ -->
                  <v-col cols="12">
                    <span style="font-weight: 600; font-size: 16px; line-height: 24px; color: #333333;">รูปแบบที่สามารถใช้คูปองได้ <span style="color: red;">*</span></span>
                  </v-col>
                  <v-col cols="12" class="mb-4">
                    <v-row dense no-gutters>
                      <v-col cols="12">
                        <v-checkbox
                          v-model="extBuyer"
                          label="ผู้ซื้อทั่วไป"
                          color="#27AB9C"
                          value="ext_buyer"
                          hide-details
                        ></v-checkbox>
                        <v-checkbox
                          v-model="Purchaser"
                          label="ผู้ซื้อองค์กร"
                          color="#27AB9C"
                          value="purchaser"
                          hide-details
                        ></v-checkbox>
                        <v-checkbox
                          v-model="SpacialPrice"
                          label="ร้องขอราคาพิเศษ"
                          color="#27AB9C"
                          value="spacial_price"
                          hide-details
                        ></v-checkbox>
                        <v-checkbox
                          v-model="Quatation"
                          label="ใบเสนอราคา"
                          color="#27AB9C"
                          value="quatation"
                          hide-details
                        ></v-checkbox>
                      </v-col>
                    </v-row>
                  </v-col>
                </v-row>
              </v-card-text>
            </v-card>
            <v-row dense class="mt-8 mb-4">
              <v-spacer></v-spacer>
              <v-btn color="#27AB9C" class="px-8 mr-2" @click="backToMerChant()" outlined dark >ยกเลิก</v-btn>
              <v-btn color="#27AB9C" class="px-4" dark @click="Confirm(statusPage)">
                <span v-if="statusPage !== 'Edit'">สร้างคูปอง</span>
                <span v-else>แก้ไขคูปอง</span>
              </v-btn>
            </v-row>
          </v-form>
        </v-row>
      </v-col>
    </v-row>
    <v-dialog v-model="dialogConfirmEditCoupon" persistent width="464">
      <v-card style="background: #FFFFFF; border-radius: 4px;">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''"><b>แก้ไขคูปอง</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogConfirmEditCoupon = !dialogConfirmEditCoupon" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row justify="center" no-gutters dense>
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;">คุณได้ทำการแก้ไขคูปอง</p>
              </v-col>
              <v-col cols="12" align="center">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">คุณยืนยันการแก้ไขคูปอง ใช่ หรือ ไม่</p>
              </v-col>
            </v-row>
          </v-card-text>
          <v-card-actions>
            <v-row justify="center" class="mb-4">
              <v-btn class="mr-4" color="#27AB9C" dark outlined style="border: 1px solid #27AB9C; border-radius: 8px;" @click="dialogConfirmEditCoupon = !dialogConfirmEditCoupon">ยกเลิก</v-btn>
              <v-btn color="#27AB9C" dark style="border: 1px solid #27AB9C; border-radius: 8px;" @click="confirmEditCoupon()">ตกลง</v-btn>
            </v-row>
          </v-card-actions>
        </v-container>
      </v-card>
    </v-dialog>
    <v-dialog v-model="dialogConfirmCoupon" persistent width="373">
      <v-card style="background: #FFFFFF; border-radius: 4px;">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title>
                <span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''" v-if="statusPage === 'Edit'"><b>แก้ไขคูปอง</b></span>
                <span style="color: #27AB9C;" :class="MobileSize ? 'title-mobile' : ''" v-else><b>สร้างคูปอง</b></span>
              </v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="closeDialogConfirmCoupon()" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-container>
          <v-card-text>
            <v-row justify="center" no-gutters dense>
              <v-col cols="12" align="center">
                <v-img src="@/assets/ImageINET-Marketplace/ICONShop/successIcon.png" contain max-height="70" max-width="70"></v-img>
              </v-col>
              <v-col cols="12" align="center" class="mt-6">
                <p style="font-weight: 500; font-size: 14px; line-height: 22px; color: color: #333333;" class="pt-0 mt-0">เสร็จสิ้น</p>
              </v-col>
            </v-row>
          </v-card-text>
        </v-container>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
export default {
  components: {
    draggable
  },
  data () {
    return {
      statusPage: '',
      lazy: false,
      msg: '',
      showmsg: false,
      Rules: {
        cupon_name: [
          v => !!v || 'กรุณากรอกชื่อคูปอง'
        ],
        cupon_code: [
          // v => !!v || 'กรุณากรอกรหัสโค้ดส่วนลด',
          v => v.length > 3 || v.length < 1 || 'กรุณากรอกรหัสโค้ดส่วนลดอย่างน้อย 4 หลัก'
        ],
        cupon_detail: [
          v => !!v || 'กรุณากรอกรายละเอียดคูปอง'
        ],
        cupon_count: [
          v => !isNaN(parseInt(v)) || 'กรุณากรอกจำนวนคูปอง'
        ],
        authority_cupon: [
          v => !!v || 'กรุณาเลือกสิทธิ์การใช้คูปอง'
        ],
        discount_cupon: [
          v => !!v || 'กรุณาเลือกประเภทส่วนลด'
        ],
        UseCupon_Limit (priceLow, discountMoney) {
          if (parseFloat(priceLow) < parseFloat(discountMoney)) {
            return 'ค่าใช้จ่ายขั้นต่ำต้องมากกว่าส่วนลดแบบจำนวนเงิน'
          } else if (isNaN(parseFloat(priceLow))) {
            return 'กรุณากรอกค่าใช้จ่ายขั้นต่ำในการใช้คูปอง'
          }
        },
        date: [
          v => !!v || 'กรุณาเลือกวันที่'
        ],
        amount_Authority_Cupon (couponToUse, authCount) {
          if (parseInt(authCount) > parseInt(couponToUse)) {
            return 'ห้ามกรอกจำนวนสิทธิ์เกินจำนวนคูปองที่ใช้ได้จริง'
          } else if (isNaN(parseInt(authCount))) {
            return 'กรุณากรอกจำนวนสิทธิ์'
          } else if (parseInt(authCount) < 2) {
            return 'กรุณากรอกจำนวนสิทธิตั้งแต่ 2 สิทธิ์ขึ้นไป'
          }
        },
        moneyDiscount: [
          v => !isNaN(parseInt(v)) || 'กรุณาระบุส่วนลด',
          v => parseInt(v) > 0 || 'ห้ามกรอกส่วนลด 0 บาท'
        ],
        percentDiscount: [
          v => !isNaN(parseInt(v)) || 'กรุณาระบุส่วนลด',
          v => parseInt(v) > 0 & parseInt(v) < 100 || 'ต้องกรอกส่วนลดมากกว่า 0% และไม่เกิน 100 %'
        ],
        loanCkeckCoupon (value, CountCoupon) {
          if (parseInt(value) > parseInt(CountCoupon)) {
            return 'จำนวนคูปองที่ใช้ได้จริงต้องน้อยกว่าหรือเท่ากับจำนวนคูปอง'
          } else if (isNaN(parseInt(value))) {
            return 'กรุณากรอกจำนวนคูปอง'
          } else if (parseInt(value) <= 0) {
            return 'กรุณากรอกจำนวนคูปองมากกว่า 0'
          }
        },
        amountDiscount: [
          v => !isNaN(parseInt(v)) || 'กรุณาระบุส่วนลด',
          v => parseInt(v) > 0 || 'ห้ามกรอกส่วนลด 0 บาท'
        ]
      },
      showText: '1 คน ต่อ',
      Detail: {
        seller_shop_id: '',
        couponImagePath: '',
        couponName: '',
        couponCode: '',
        couponDescription: '',
        collectStartDate: '',
        collectEndDate: '',
        useStartDate: '',
        useEndDate: '',
        discountType: '',
        discountAmount: '',
        discountMaximum: '',
        collectQuota: '',
        quota: '',
        spendMinimum: '',
        userCap: '',
        couponType: '',
        ext_buyer: '',
        purchaser: '',
        special_price: '',
        qu: ''
      },
      dataAuthorityCupon: [
        { key: 1, lable: '1 คนต่อ 1 สิทธิ์การใช้', value: 1 },
        { key: 2, lable: 'ไม่จำกัดสิทธิ์การใช้', value: 2 },
        { key: 3, lable: 'กำหนดเอง', value: 3 }
      ],
      dataDiscountCupon: [
        { key: 1, lable: 'ส่วนลดเป็นจำนวนเงิน', value: 'money' },
        { key: 2, lable: 'ส่วนลดเป็นเปอร์เซ็นต์ (%)', value: 'percent' }
      ],
      DataImage: [],
      cupon_images: [],
      cupon_images_sendData: [],
      cuponName: '',
      cuponCode: '',
      detailCupon: '',
      disableTimeUseCupon: false,
      disableTimeKeepCupon: false,
      modalStartDateUseCupon: false,
      modalStartDateKeepCupon: false,
      modalEndDateKeepCupon: false,
      StartDateKeepCupon: '',
      EndDateKeepCupon: '',
      KeepCuponStartDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      KeepCuponEndDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      StartDateUseCupon: '',
      modalEndDateUseCupon: false,
      EndDateUseCupon: '',
      UseCuponStartDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      UseCuponEndDate: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10),
      disableTimeKeepStart: false,
      menuStartTimeKeepCupon: false,
      StarttimeKeepCupon: null,
      menuEndTimeKeepCupon: false,
      EndtimeKeepCupon: null,
      disableTimeKeepEnd: false,
      disableTimeUseStart: false,
      menuStartTimeUseCupon: false,
      StartTimeUseCupon: null,
      disableTimeUseEnd: false,
      menuEndTimeUseCupon: false,
      EndTimeUseCupon: null,
      cuponCount: '',
      cuponCountReal: '',
      authorityCupon: null,
      DiscountCupon: '',
      DiscountCuponSelect: null,
      amountDiscount: '',
      UseCuponLimit: '',
      extBuyer: '',
      Purchaser: '',
      SpacialPrice: '',
      Quatation: '',
      maxSelectTimeEndKeep: '',
      maxSelectTimeEndUse: '',
      amountAuthorityCupon: '',
      shopID: '',
      keepStartDateToSend: '',
      keepEndDateToSend: '',
      useStartDateToSend: '',
      useEndDateToSend: '',
      amountDiscountMoney: '',
      amountDiscountMoneyPercent: '',
      dialogConfirmEditCoupon: false,
      dialogConfirmCoupon: false,
      detailCoupon: [],
      typeUse: '',
      couponID: '',
      maxcollectQuota: '',
      maxQuota: '',
      checkStartDateKeepForTime: '',
      checkStartDateUseForTime: '',
      maxSelectEndDateToKeep: '',
      maxSelectEndDateToUse: '',
      minTimeStartKeep: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5),
      minTimeStartUse: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5),
      minTimeEndKeep: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5),
      minTimeEndUse: (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
    }
  },
  created () {
    this.shopID = localStorage.getItem('shopSellerID')
    this.statusPage = this.$route.query.Status
    if (this.statusPage === 'Edit') {
      this.couponID = this.$route.query.id
      this.getDetailCoupon()
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        // console.log('this.statusPage', this.statusPage)
        if (this.statusPage === 'Create') {
          this.$router.push({ path: `/ManagaCuponMobile?Status=${this.statusPage}` }).catch(() => {})
        } else if (this.statusPage === 'Edit') {
          this.$router.push({ path: `/ManagaCuponMobile?Status=${this.statusPage}&id=${this.couponID}` }).catch(() => {})
        }
      } else {
        if (this.statusPage === 'Create') {
          this.$router.push({ path: `/ManagaCupon?Status=${this.statusPage}` }).catch(() => {})
        } else if (this.statusPage === 'Edit') {
          this.$router.push({ path: `/ManagaCupon?Status=${this.statusPage}&id=${this.couponID}` }).catch(() => {})
        }
      }
    },
    modalStartDateKeepCupon (val) {
      if (val) {
        this.EndDateKeepCupon = ''
        this.KeepCuponEndDate = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.maxSelectEndDateToKeep = ''
        this.keepEndDateToSend = ''
      }
    },
    modalStartDateUseCupon (val) {
      if (val) {
        this.EndDateUseCupon = ''
        this.UseCuponEndDate = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.maxSelectEndDateToUse = ''
        this.useEndDateToSend = ''
      }
    },
    KeepCuponStartDate (val) {
      if (val === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
        this.minTimeStartKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
      } else {
        this.minTimeStartKeep = ''
      }
    },
    KeepCuponEndDate (val) {
      if (val === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
        this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
      } else {
        this.minTimeEndKeep = ''
      }
    },
    UseCuponStartDate (val) {
      if (val === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
        this.minTimeStartUse = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
      } else {
        this.minTimeStartUse = ''
      }
    },
    UseCuponEndDate (val) {
      if (val === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
        this.minTimeEndUse = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
      } else {
        this.minTimeEndUse = ''
      }
    },
    StarttimeKeepCupon (val) {
      this.maxSelectTimeEndKeep = ''
      this.minTimeEndKeep = ''
      // console.log(this.formatDate(this.KeepCuponEndDate), this.formatDate(this.KeepCuponStartDate))
      if (this.formatDate(this.KeepCuponEndDate) === this.formatDate(this.KeepCuponStartDate)) {
        this.maxSelectTimeEndKeep = '23:59'
        this.minTimeEndKeep = val
        if (this.minTimeEndKeep === this.val) {
          this.minTimeEndKeep = '23:59'
        } else {
          this.minTimeEndKeep = val
        }
      } else {
        this.maxSelectTimeEndKeep = ''
        if (this.formatDate(this.KeepCuponEndDate) === this.formatDate(this.KeepCuponStartDate)) {
          this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
        } else {
          this.minTimeEndKeep = ''
        }
      }
      // console.log(this.minTimeEndKeep)
    },
    StartTimeUseCupon (val) {
      this.maxSelectEndDateToUse = ''
      this.minTimeEndUse = ''
      if (this.formatDate(this.UseCuponEndDate) === this.formatDate(this.UseCuponStartDate)) {
        this.maxSelectEndDateToUse = '23:59'
        this.minTimeEndUse = val
        if (this.minTimeEndUse === this.val) {
          this.minTimeEndKeep = '23:59'
        } else {
          this.minTimeEndKeep = val
        }
      } else {
        this.maxSelectEndDateToUse = ''
        if (this.formatDate(this.UseCuponEndDate) === this.formatDate(this.UseCuponStartDate)) {
          this.minTimeEndUse = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
        } else {
          this.minTimeEndUse = ''
        }
      }
    },
    authorityCupon (val) {
      if (val !== 3) {
        this.amountAuthorityCupon = ''
      }
    },
    DiscountCupon (val) {
      if (val === 'money') {
        this.DiscountCuponSelect = null
        this.amountDiscount = ''
        this.amountDiscountMoneyPercent = ''
      } else {
        this.amountDiscountMoney = ''
      }
    },
    disableTimeKeepStart (val) {
      if (val === false) {
        this.StarttimeKeepCupon = ''
      }
    },
    disableTimeKeepEnd (val) {
      if (val === false) {
        this.EndtimeKeepCupon = ''
      }
    },
    disableTimeUseStart (val) {
      if (val === false) {
        this.StartTimeUseCupon = ''
      }
    },
    disableTimeUseEnd (val) {
      if (val === false) {
        this.EndTimeUseCupon = ''
      }
    },
    disableTimeKeepCupon (val) {
      if (val) {
        this.EndDateKeepCupon = ''
        this.KeepCuponEndDate = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.disableTimeKeepEnd = false
        this.maxSelectEndDateToKeep = ''
        this.keepEndDateToSend = ''
      }
    },
    disableTimeUseCupon (val) {
      if (val) {
        this.EndDateUseCupon = ''
        this.UseCuponEndDate = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)
        this.disableTimeUseEnd = false
        this.maxSelectEndDateToUse = ''
        this.useEndDateToSend = ''
      }
    }
  },
  methods: {
    selectDate (val) {
      this.maxSelectEndDateToKeep = val
      this.maxSelectTimeEndKeep = ''
      this.minTimeEndKeep = ''
      // console.log(this.formatDate(val), this.KeepCuponStartDate)
      if (this.formatDate(val) === this.formatDate(this.KeepCuponStartDate)) {
        this.maxSelectTimeEndKeep = '23:59'
        // this.minTimeEndKeep = this.StarttimeKeepCupon
        if (this.minTimeEndKeep === this.StarttimeKeepCupon) {
          this.minTimeEndKeep = '23:59'
        } else {
          this.minTimeEndKeep = this.StarttimeKeepCupon
        }
        // console.log(typeof this.maxSelectTimeEndKeep)
      } else {
        this.maxSelectTimeEndKeep = ''
        this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
      }
      // console.log(this.maxSelectTimeEndKeep)
    },
    selectDateUse (val) {
      this.maxSelectEndDateToUse = val
      this.maxSelectTimeEndUse = ''
      this.minTimeEndUse = ''
      if (this.formatDate(val) === this.formatDate(this.UseCuponStartDate)) {
        this.maxSelectTimeEndUse = '23:59'
        // this.minTimeEndUse = this.StartTimeUseCupon
        if (this.minTimeEndUse === this.StartTimeUseCupon) {
          this.minTimeEndUse = '23:59'
        } else {
          this.minTimeEndUse = this.StartTimeUseCupon
        }
        // console.log(this.maxSelectTimeEndUse)
      } else {
        this.maxSelectTimeEndUse = ''
        this.minTimeEndUse = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
      }
    },
    backToMerChant () {
      this.$refs.formCupon.resetValidation()
      if (this.MobileSize) {
        this.$router.push({ path: '/MerchantShopMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/MerchantShop' }).catch(() => {})
      }
    },
    onPickFile () {
      document.getElementById('file_input').click()
    },
    UploadImage () {
      // console.log(this.DataImage, 'this.DataImage')
      // console.log(this.Detail.cupon_images.length, 'this.Detail.shop_image')
      // console.log(this.Detail.shop_image.length < 6)
      // var mediaType = ''
      // var showImage = []
      // this.shop_media = []
      // this.Detail.shop_image = []
      if (this.cupon_images.length <= 1) {
        if (this.DataImage !== undefined && this.DataImage.length <= 1 && this.cupon_images.length <= 0) {
          for (let i = 0; i < this.DataImage.length; i++) {
            const element = this.DataImage[i]
            this.cupon_images_sendData = this.DataImage[i]
            const imageSize = element.size / 1024 / 1024
            if (imageSize < 5) {
              if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
                const reader = new FileReader()
                reader.readAsDataURL(element)
                reader.onload = () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  if (this.cupon_images.length <= 0) {
                    this.cupon_images.push({
                      image_data: resultReader,
                      path: url,
                      name: this.DataImage[i].name
                    })
                  } else {
                    this.$swal.fire({
                      icon: 'warning',
                      text: 'เพิ่มรูปภาพได้ไม่เกิน 1 รูป',
                      showConfirmButton: false,
                      timer: 1500
                    })
                  }
                  // console.log(this.Detail.shop_image, 'this.Detail.shop_image')
                  // mediaType = this.DataImage[i].type
                  // var checkType = mediaType.split('/', 1)
                  // if (checkType.toString() === 'video') {
                  //   checkType = 'vdo'
                  // } else {
                  //   checkType = 'image'
                  // }
                  // this.shop_media.push({
                  //   media: element,
                  //   media_type: checkType
                  // })
                  // console.log(this.shop_media, 'this.shop_media')
                }
              } else {
                this.$swal.fire({
                  icon: 'warning',
                  text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ',
                  showConfirmButton: false,
                  timer: 1500
                })
              }
            } else {
              this.$swal.fire({
                icon: 'warning',
                text: 'กรุณาใส่รูปที่มีขนาดน้อยกว่า 5 MB',
                showConfirmButton: false,
                timer: 1500
              })
            }
          }
        } else {
          this.$swal.fire({
            icon: 'warning',
            text: 'เพิ่มรูปภาพได้ไม่เกิน 1 รูป',
            showConfirmButton: false,
            timer: 1500
          })
        }
      } else {
        this.$swal.fire({
          icon: 'warning',
          text: 'เพิ่มรูปภาพได้ไม่เกิน 1 รูป',
          showConfirmButton: false,
          timer: 1500
        })
      }
    },
    RemoveImage (index, val) {
      if (this.$route.query.Status === 'Edit') {
        // if (val.id !== undefined) {
        //   this.Detail.remove_img.push({
        //     id: val.id
        //   })
        // }
        this.cupon_images.splice(index, 1)
      } else {
        this.DataImage.splice(index, 1)
        this.cupon_images.splice(index, 1)
      }
    },
    onMove ({ relatedContext, draggedContext }) {
      const relatedElement = relatedContext
      const draggedElement = draggedContext
      return (
        (!relatedElement || !relatedElement.fixed) && !draggedElement.fixed
      )
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${day}/${month}/${year}`
    },
    checkMaxValueOfCoupon (MaxValue) {
      if (this.statusPage === 'Edit') {
        if (parseInt(MaxValue) < parseInt(this.maxcollectQuota)) {
          // console.log('เข้าเงื่อนไข')
          this.msg = 'กรุณาระบุจำนวนคูปองให้มากกว่าหรือเท่ากับ ' + this.maxcollectQuota
          return this.msg
        } else if (isNaN(parseInt(MaxValue))) {
          return 'กรุณากรอกจำนวนคูปอง'
        } else if (parseInt(MaxValue) <= 0) {
          return 'กรุณากรอกจำนวนคูปองมากกว่า 0'
        } else {
          this.showmsg = false
        }
      } else {
        if (isNaN(parseInt(MaxValue))) {
          return 'กรุณากรอกจำนวนคูปอง'
        } else if (parseInt(MaxValue) <= 0) {
          return 'กรุณากรอกจำนวนคูปองมากกว่า 0'
        }
      }
    },
    checkMaxValueOfCouponReal (MaxValue) {
      if (this.statusPage === 'Edit') {
        if (parseInt(MaxValue) < parseInt(this.maxQuota)) {
          // console.log('เข้าเงื่อนไข')
          this.msg = 'กรุณาระบุจำนวนคูปองให้มากกว่าหรือเท่ากับ ' + this.maxQuota
          return this.msg
        } else {
          this.showmsg = false
        }
      }
    },
    SaveStartDateKeepCupon (startDate, StartTime) {
      this.$refs.dialogStartDateKeepCupon.save(startDate)
      var selectDate = ''
      var StartDate = ''
      this.keepStartDateToSend = ''
      // this.keepEndDateToSend = ''
      if (StartTime === null || StartTime === '') {
        StartTime = '00:00'
        selectDate = new Date(this.KeepCuponStartDate)
        StartDate = this.formatDate(new Date(selectDate.setDate(selectDate.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        this.checkStartDateKeepForTime = StartDate
        this.StartDateKeepCupon = StartDate + ' ' + StartTime
        var keepStartDateToSend = new Date(selectDate.setDate(selectDate.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.keepStartDateToSend = keepStartDateToSend + ' ' + StartTime
      } else {
        selectDate = new Date(this.KeepCuponStartDate)
        StartDate = this.formatDate(new Date(selectDate.setDate(selectDate.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        this.checkStartDateKeepForTime = StartDate
        this.StartDateKeepCupon = StartDate + ' ' + StartTime
        var keepDataStartDate = new Date(selectDate.setDate(selectDate.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.keepStartDateToSend = keepDataStartDate + ' ' + StartTime
      }
      // console.log(this.keepStartDateToSend)
    },
    SaveEndDateKeepCupon (endDate, EndTime) {
      this.$refs.dialogEndDateKeepCupon.save(endDate)
      var selectDateEnd = ''
      var EndDate = ''
      this.keepEndDateToSend = ''
      if (EndTime === null || EndTime === '') {
        selectDateEnd = new Date(this.KeepCuponEndDate)
        EndDate = this.formatDate(new Date(selectDateEnd.setDate(selectDateEnd.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        // console.log(this.checkStartDateKeepForTime)
        if (this.checkStartDateKeepForTime === EndDate) {
          EndTime = '23:59'
        } else {
          EndTime = '23:59'
        }
        // console.log(EndTime)
        this.EndDateKeepCupon = EndDate + ' ' + EndTime
        var keepEndDateToSend = new Date(selectDateEnd.setDate(selectDateEnd.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.keepEndDateToSend = keepEndDateToSend + ' ' + EndTime
      } else {
        selectDateEnd = new Date(this.KeepCuponEndDate)
        EndDate = this.formatDate(new Date(selectDateEnd.setDate(selectDateEnd.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        if (EndTime !== null) {
          // EndTime = '23:59'
        } else {
          EndTime = '23:59'
        }
        this.EndDateKeepCupon = EndDate + ' ' + EndTime
        var keepDataEndDate = new Date(selectDateEnd.setDate(selectDateEnd.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.keepEndDateToSend = keepDataEndDate + ' ' + EndTime
      }
    },
    SaveStartDateUseCupon (StartDateUse, StartTimeuse) {
      this.$refs.dialogStartDateUseCupon.save(StartDateUse)
      var selectDateStartUse = ''
      var StartDateUse1 = ''
      this.useStartDateToSend = ''
      // this.useEndDateToSend = ''
      if (StartTimeuse === null || StartTimeuse === '') {
        StartTimeuse = '00:00'
        selectDateStartUse = new Date(this.UseCuponStartDate)
        StartDateUse1 = this.formatDate(new Date(selectDateStartUse.setDate(selectDateStartUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        this.checkStartDateUseForTime = StartDateUse1
        this.StartDateUseCupon = StartDateUse1 + ' ' + StartTimeuse
        var useStartDateToSend = new Date(selectDateStartUse.setDate(selectDateStartUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.useStartDateToSend = useStartDateToSend + ' ' + StartTimeuse
      } else {
        selectDateStartUse = new Date(this.UseCuponStartDate)
        StartDateUse1 = this.formatDate(new Date(selectDateStartUse.setDate(selectDateStartUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        this.checkStartDateUseForTime = StartDateUse1
        this.StartDateUseCupon = StartDateUse1 + ' ' + StartTimeuse
        var useDataStartDate = new Date(selectDateStartUse.setDate(selectDateStartUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.useStartDateToSend = useDataStartDate + ' ' + StartTimeuse
      }
    },
    SaveEndDateUseCupon (EndDateUse, EndTimeuse) {
      this.$refs.dialogEndDateUseCupon.save(EndDateUse)
      // console.log(EndTimeuse)
      var selectDateEndUse = ''
      var EndDateUse1 = ''
      this.useEndDateToSend = ''
      if (EndTimeuse === null || EndTimeuse === '') {
        selectDateEndUse = new Date(this.UseCuponEndDate)
        EndDateUse1 = this.formatDate(new Date(selectDateEndUse.setDate(selectDateEndUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        if (this.checkStartDateUseForTime === EndDateUse1) {
          EndTimeuse = '23:59'
        } else {
          EndTimeuse = '23:59'
        }
        this.EndDateUseCupon = EndDateUse1 + ' ' + EndTimeuse
        var useEndDateToSend = new Date(selectDateEndUse.setDate(selectDateEndUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.useEndDateToSend = useEndDateToSend + ' ' + EndTimeuse
      } else {
        selectDateEndUse = new Date(this.UseCuponEndDate)
        EndDateUse1 = this.formatDate(new Date(selectDateEndUse.setDate(selectDateEndUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10))
        if (EndTimeuse !== null) {
          // EndTimeuse = '23:59'
        } else {
          EndTimeuse = '23:59'
        }
        this.EndDateUseCupon = EndDateUse1 + ' ' + EndTimeuse
        var useDataEndDate = new Date(selectDateEndUse.setDate(selectDateEndUse.getDate()) - (new Date()).getTimezoneOffset() * 60000).toISOString().substr(0, 10)
        this.useEndDateToSend = useDataEndDate + ' ' + EndTimeuse
      }
    },
    Confirm (val) {
      if (val === 'Create') {
        this.createCupon()
      } else if (val === 'Edit') {
        this.dialogConfirmEditCoupon = true
      }
    },
    closeDialogConfirmCoupon () {
      this.dialogConfirmCoupon = !this.dialogConfirmCoupon
      this.$EventBus.$emit('GetCouponList')
      if (!this.MobileSize) {
        this.$router.push({ path: '/MerchantShop' }).catch(() => {})
      } else {
        this.$router.push({ path: '/MerchantShopMobile' }).catch(() => {})
      }
    },
    async createCupon () {
      if (this.$refs.formCupon.validate(true)) {
        if (this.extBuyer === 'ext_buyer' || this.Purchaser === 'purchaser' || this.SpacialPrice === 'spacial_price' || this.Quatation === 'quatation') {
          // this.Detail.seller_shop_id = this.shopID
          // this.Detail.couponImagePath = this.cupon_images.length !== 0 ? this.cupon_images[0].image_data : ''
          // this.Detail.couponName = this.cuponName
          // this.Detail.couponCode = this.cuponCode.toUpperCase()
          // this.Detail.couponDescription = this.detailCupon
          // this.Detail.collectStartDate = this.keepStartDateToSend
          // this.Detail.collectEndDate = this.keepEndDateToSend
          // this.Detail.useStartDate = this.useStartDateToSend
          // this.Detail.useEndDate = this.useEndDateToSend
          // this.Detail.discountType = this.DiscountCupon
          // if (this.DiscountCupon === 'money') {
          //   this.Detail.discountAmount = this.amountDiscountMoney
          //   this.Detail.discountMaximum = null
          // } else if (this.DiscountCupon === 'percent') {
          //   if (this.DiscountCuponSelect === '1') {
          //     this.Detail.discountAmount = this.amountDiscountMoneyPercent
          //     this.Detail.discountMaximum = this.amountDiscount
          //   } else {
          //     this.Detail.discountAmount = this.amountDiscountMoneyPercent
          //     this.Detail.discountMaximum = 0
          //   }
          // }
          // this.Detail.spendMinimum = this.UseCuponLimit
          // this.Detail.collectQuota = this.cuponCount
          // this.Detail.quota = this.cuponCountReal
          // this.Detail.couponType = 'normal'
          // if (this.authorityCupon === 1) {
          //   this.Detail.userCap = '1'
          // } else if (this.authorityCupon === 2) {
          //   this.Detail.userCap = '0'
          // } else if (this.authorityCupon === 3) {
          //   this.Detail.userCap = this.amountAuthorityCupon
          // }
          // this.Detail.ext_buyer = this.extBuyer === 'ext_buyer' ? 'y' : 'n'
          // this.Detail.purchaser = this.Purchaser === 'purchaser' ? 'y' : 'n'
          // this.Detail.special_price = this.SpacialPrice === 'spacial_price' ? 'y' : 'n'
          // this.Detail.qu = this.Quatation === 'quatation' ? 'y' : 'n'
          const formData = new FormData()
          formData.append('seller_shop_id', this.shopID)
          formData.append('couponImagePath', this.cupon_images_sendData)
          formData.append('couponName', this.cuponName)
          formData.append('couponCode', this.cuponCode !== '' ? this.cuponCode.toLowerCase() : this.cuponCode)
          formData.append('couponDescription', this.detailCupon)
          formData.append('collectStartDate', this.keepStartDateToSend)
          formData.append('collectEndDate', this.keepEndDateToSend)
          formData.append('useStartDate', this.useStartDateToSend)
          formData.append('useEndDate', this.useEndDateToSend)
          formData.append('discountType', this.DiscountCupon)
          if (this.DiscountCupon === 'money') {
            formData.append('discountAmount', this.amountDiscountMoney)
            formData.append('discountMaximum', null)
          } else if (this.DiscountCupon === 'percent') {
            if (this.DiscountCuponSelect === '1') {
              formData.append('discountAmount', this.amountDiscountMoneyPercent)
              formData.append('discountMaximum', this.amountDiscount)
            } else {
              formData.append('discountAmount', this.amountDiscountMoneyPercent)
              formData.append('discountMaximum', 0)
            }
          }
          formData.append('spendMinimum', this.UseCuponLimit)
          formData.append('collectQuota', this.cuponCount)
          formData.append('quota', this.cuponCountReal)
          formData.append('couponType', 'normal')
          if (this.authorityCupon === 1) {
            formData.append('userCap', '1')
          } else if (this.authorityCupon === 2) {
            formData.append('userCap', '0')
          } else if (this.authorityCupon === 3) {
            formData.append('userCap', parseInt(this.amountAuthorityCupon))
          }
          formData.append('ext_buyer', this.extBuyer === 'ext_buyer' ? 'y' : 'n')
          formData.append('purchaser', this.Purchaser === 'purchaser' ? 'y' : 'n')
          formData.append('special_price', this.SpacialPrice === 'spacial_price' ? 'y' : 'n')
          formData.append('qu', this.Quatation === 'quatation' ? 'y' : 'n')
          // console.log(formData)
          this.$store.commit('openLoader')
          await this.$store.dispatch('actionCreateShopCoupon', formData)
          var response = await this.$store.state.ModuleShop.stateCreateShopCoupon
          if (response.result === 'OK') {
            this.$store.commit('closeLoader')
            this.dialogConfirmCoupon = true
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือกรูปแบบที่สามารถใช้คูปองได้อย่างน้อย 1 รูปแบบ', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async confirmEditCoupon () {
      this.dialogConfirmEditCoupon = false
      if (this.$refs.formCupon.validate(true)) {
        if (this.extBuyer === 'ext_buyer' || this.Purchaser === 'purchaser' || this.SpacialPrice === 'spacial_price' || this.Quatation === 'quatation') {
          this.Detail.seller_shop_id = this.shopID
          this.Detail.couponId = this.couponID
          this.Detail.couponName = this.cuponName
          this.Detail.couponCode = this.cuponCode !== '' ? this.cuponCode.toLowerCase() : this.cuponCode
          this.Detail.couponDescription = this.detailCupon
          this.Detail.collectStartDate = this.keepStartDateToSend
          this.Detail.collectEndDate = this.keepEndDateToSend
          this.Detail.useStartDate = this.useStartDateToSend
          this.Detail.useEndDate = this.useEndDateToSend
          this.Detail.discountType = this.DiscountCupon
          if (this.DiscountCupon === 'money') {
            this.Detail.discountAmount = this.amountDiscountMoney
            this.Detail.discountMaximum = null
          } else if (this.DiscountCupon === 'percent') {
            if (this.DiscountCuponSelect === '1') {
              this.Detail.discountAmount = this.amountDiscountMoneyPercent
              this.Detail.discountMaximum = this.amountDiscount
            } else {
              this.Detail.discountAmount = this.amountDiscountMoneyPercent
              this.Detail.discountMaximum = 0
            }
          }
          this.Detail.spendMinimum = this.UseCuponLimit
          this.Detail.collectQuota = this.cuponCount
          this.Detail.quota = this.cuponCountReal
          this.Detail.couponType = 'normal'
          if (this.authorityCupon === 1) {
            this.Detail.userCap = '1'
          } else if (this.authorityCupon === 2) {
            this.Detail.userCap = '0'
          } else if (this.authorityCupon === 3) {
            this.Detail.userCap = this.amountAuthorityCupon
          }
          this.Detail.ext_buyer = this.extBuyer === 'ext_buyer' ? 'y' : 'n'
          this.Detail.purchaser = this.Purchaser === 'purchaser' ? 'y' : 'n'
          this.Detail.special_price = this.SpacialPrice === 'spacial_price' ? 'y' : 'n'
          this.Detail.qu = this.Quatation === 'quatation' ? 'y' : 'n'
          // console.log(this.Detail)
          this.$store.commit('openLoader')
          await this.$store.dispatch('actionEditShopCoupon', this.Detail)
          var response = await this.$store.state.ModuleShop.stateEditShopCoupon
          if (response.result === 'OK') {
            this.$store.commit('closeLoader')
            this.dialogConfirmCoupon = true
          } else {
            this.$store.commit('closeLoader')
            this.$swal.fire({ showConfirmButton: false, timer: 2500, timerProgressBar: true, icon: 'error', text: `${response.message}` })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือกรูปแบบที่สามารถใช้คูปองได้อย่างน้อย 1 รูปแบบ', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$nextTick(() => {
          const el = document.getElementsByClassName('error--text')
          if (el) {
            document
              .getElementsByClassName('error--text')[0]
              .scrollIntoView({ behavior: 'smooth', block: 'end' })
          }
        })
      }
    },
    async getDetailCoupon () {
      var data = {
        seller_shop_id: this.shopID,
        couponId: this.couponID
      }
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionDetailShopCoupon', data)
      var response = await this.$store.state.ModuleShop.stateDetailShopCoupon
      // console.log('Detail Coupon', response.data)
      if (response.result === 'OK') {
        this.$store.commit('closeLoader')
        this.detailCoupon = response.data.coupon
        this.typeUse = response.data.typeUse
        // console.log(this.detailCoupon, this.typeUse)
        // กำหนดค่าลง field
        this.cupon_images.push({
          image_data: this.detailCoupon.couponImagePath !== null ? this.detailCoupon.couponImagePath : '',
          path: this.detailCoupon.couponImagePath !== null ? this.detailCoupon.couponImagePath : ''
        })
        this.cuponName = this.detailCoupon.couponDetail.couponName
        this.cuponCode = this.detailCoupon.couponDetail.couponCode !== null ? this.detailCoupon.couponDetail.couponCode.toLowerCase() : ''
        this.detailCupon = this.detailCoupon.couponDetail.couponDescription
        this.cuponCount = this.detailCoupon.couponRule.collectQuota.toString()
        this.cuponCountReal = this.detailCoupon.couponRule.quota.toString()
        this.maxcollectQuota = this.detailCoupon.couponRule.collectQuota.toString()
        this.maxQuota = this.detailCoupon.couponRule.quota.toString()
        this.UseCuponLimit = this.detailCoupon.couponRule.spendMinimum
        // เช็ควันเริ่มต้น ระยะเวลาเก็บคูปอง
        var checkcollectStartDate = this.detailCoupon.couponDate.collectStartDate.substr(0, 10)
        var checkcollectStartDateTime = this.detailCoupon.couponDate.collectStartDate.substr(11, 5)
        if (checkcollectStartDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
          this.minTimeStartKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
        } else {
          this.minTimeStartKeep = ''
        }
        if (checkcollectStartDateTime !== '00:00') {
          this.KeepCuponStartDate = checkcollectStartDate
          this.disableTimeKeepStart = true
          this.StarttimeKeepCupon = checkcollectStartDateTime
          this.StartDateKeepCupon = this.formatDate(checkcollectStartDate) + ' ' + checkcollectStartDateTime
          this.keepStartDateToSend = checkcollectStartDate + ' ' + checkcollectStartDateTime
        } else {
          this.KeepCuponStartDate = checkcollectStartDate
          this.disableTimeKeepStart = true
          this.StarttimeKeepCupon = checkcollectStartDateTime
          this.StartDateKeepCupon = this.formatDate(checkcollectStartDate) + ' ' + checkcollectStartDateTime
          this.keepStartDateToSend = checkcollectStartDate + ' ' + checkcollectStartDateTime
        }
        // เช็ควันสิ้นสุด ระยะเวลาเก็บคูปอง
        if (this.detailCoupon.couponDate.collectEndDate !== null) {
          this.disableTimeKeepCupon = false
          var checkcollectEndDate = this.detailCoupon.couponDate.collectEndDate.substr(0, 10)
          var checkcollectEndDateTime = this.detailCoupon.couponDate.collectEndDate.substr(11, 5)
          // console.log(checkcollectEndDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
          // console.log(checkcollectEndDate, (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
          if (checkcollectEndDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
            this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
          } else {
            this.minTimeEndKeep = ''
          }
          if (checkcollectEndDateTime !== '00:00') {
            this.KeepCuponEndDate = checkcollectEndDate
            this.disableTimeKeepEnd = true
            this.EndtimeKeepCupon = checkcollectEndDateTime
            this.EndDateKeepCupon = this.formatDate(checkcollectEndDate) + ' ' + checkcollectEndDateTime
            this.keepEndDateToSend = checkcollectEndDate + ' ' + checkcollectEndDateTime
          } else {
            this.KeepCuponEndDate = checkcollectEndDate
            this.disableTimeKeepEnd = true
            this.EndtimeKeepCupon = checkcollectEndDateTime
            this.EndDateKeepCupon = this.formatDate(checkcollectEndDate) + ' ' + checkcollectEndDateTime
            this.keepEndDateToSend = checkcollectEndDate + ' ' + checkcollectEndDateTime
          }
        } else {
          this.disableTimeKeepCupon = true
          if (this.detailCoupon.couponDate.collectEndDate !== null) {
            var checkcollectEndDate1 = this.detailCoupon.couponDate.collectEndDate.substr(0, 10)
            if (checkcollectEndDate1 === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
              this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
            } else {
              this.minTimeEndKeep = ''
            }
          } else {
            this.KeepCuponEndDate = this.detailCoupon.couponDate.collectStartDate.substr(0, 10)
            if (this.KeepCuponEndDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
              this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
            } else {
              this.minTimeEndKeep = this.detailCoupon.couponDate.collectStartDate.substr(11, 5)
            }
          }
        }
        // เช็ควันเริ่มต้น ระยะเวลาใช้คูปอง
        var checkuseStartDate = this.detailCoupon.couponDate.useStartDate.substr(0, 10)
        var checkuseStartDateTime = this.detailCoupon.couponDate.useStartDate.substr(11, 5)
        // console.log(checkuseStartDate, (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10))
        if (checkuseStartDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
          this.minTimeStartUse = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
        } else {
          this.minTimeStartUse = ''
        }
        if (checkuseStartDateTime !== '00:00') {
          this.UseCuponStartDate = checkuseStartDate
          this.disableTimeUseStart = true
          this.StartTimeUseCupon = checkuseStartDateTime
          this.StartDateUseCupon = this.formatDate(checkuseStartDate) + ' ' + checkuseStartDateTime
          this.useStartDateToSend = checkuseStartDate + ' ' + checkuseStartDateTime
        } else {
          this.KeepCuponStartDate = checkcollectStartDate
          this.disableTimeUseStart = true
          this.StartTimeUseCupon = checkuseStartDateTime
          this.StartDateUseCupon = this.formatDate(checkcollectStartDate) + ' ' + checkuseStartDateTime
          this.useStartDateToSend = checkuseStartDate
        }
        // เช็ควันสิ้นสุด ระยะเวลาใช้คูปอง
        if (this.detailCoupon.couponDate.useEndDate !== null) {
          this.disableTimeUseCupon = false
          var checkuseEndDate = this.detailCoupon.couponDate.useEndDate.substr(0, 10)
          var checkuseEndDateTime = this.detailCoupon.couponDate.useEndDate.substr(11, 5)
          if (checkuseEndDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
            this.minTimeEndUse = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
          } else {
            this.minTimeEndUse = ''
          }
          if (checkcollectEndDateTime !== '00:00') {
            this.UseCuponEndDate = checkuseEndDate
            this.disableTimeUseEnd = true
            this.EndTimeUseCupon = checkuseEndDateTime
            this.EndDateUseCupon = this.formatDate(checkuseEndDate) + ' ' + checkuseEndDateTime
            this.useEndDateToSend = checkuseEndDate + ' ' + checkuseEndDateTime
          } else {
            this.UseCuponEndDate = checkuseEndDate
            this.disableTimeUseEnd = true
            this.EndTimeUseCupon = checkuseEndDateTime
            this.EndDateUseCupon = this.formatDate(checkuseEndDate) + ' ' + checkuseEndDateTime
            this.useEndDateToSend = checkuseEndDate + ' ' + checkuseEndDateTime
          }
        } else {
          this.disableTimeUseCupon = true
          if (this.detailCoupon.couponDate.useEndDate !== null) {
            var checkuseEndDate1 = this.detailCoupon.couponDate.useEndDate.substr(0, 10)
            if (checkuseEndDate1 === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
              this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
            } else {
              this.minTimeEndKeep = ''
            }
          } else {
            this.UseCuponEndDate = this.detailCoupon.couponDate.useStartDate.substr(0, 10)
            if (this.UseCuponEndDate === (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)) {
              this.minTimeEndKeep = (new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(11, 5)
            } else {
              this.minTimeEndKeep = this.detailCoupon.couponDate.useStartDate.substr(11, 5)
            }
          }
        }
        // สิทธิ์การใช้คูปอง
        if (this.detailCoupon.couponRule.userCap === 1) {
          this.authorityCupon = 1
        } else if (this.detailCoupon.couponRule.userCap === 0) {
          this.authorityCupon = 2
        } else {
          this.authorityCupon = 3
          this.amountAuthorityCupon = this.detailCoupon.couponRule.userCap
        }
        // รูปแบบที่สามารถใช้คูปองได้
        this.extBuyer = this.typeUse.ext_buyer === 'y' ? 'ext_buyer' : ''
        this.Purchaser = this.typeUse.purchaser === 'y' ? 'purchaser' : ''
        this.SpacialPrice = this.typeUse.special_price === 'y' ? 'spacial_price' : ''
        this.Quatation = this.typeUse.qu === 'y' ? 'quatation' : ''
        if (this.detailCoupon.couponRule.discountType === 'money') {
          this.DiscountCupon = this.detailCoupon.couponRule.discountType
          this.amountDiscountMoney = this.detailCoupon.couponRule.discountAmount.toString()
          this.amountDiscount = this.detailCoupon.couponRule.discountMaximum.toString()
        } else {
          this.DiscountCupon = this.detailCoupon.couponRule.discountType
          if (this.detailCoupon.couponRule.discountMaximum !== 0) {
            this.DiscountCuponSelect = '1'
            this.amountDiscount = this.detailCoupon.couponRule.discountMaximum.toString()
            this.amountDiscountMoneyPercent = this.detailCoupon.couponRule.discountAmount.toString()
          } else {
            this.DiscountCuponSelect = '2'
            this.amountDiscount = this.detailCoupon.couponRule.discountMaximum.toString()
            this.amountDiscountMoneyPercent = this.detailCoupon.couponRule.discountAmount.toString()
          }
        }
      } else {
        this.$store.commit('closeLoader')
        if (response.message === 'This user is Unauthorized') {
          this.$EventBus.$emit('refreshToken')
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h4>ไม่มีคูปองนี้ในร้านค้า</h4>', text: 'โปรดตรวจสอบอีกครั้ง', showConfirmButton: false, timer: 2500 })
          this.$router.push({ path: '/MerchantShop' }).catch(() => {})
        }
      }
    }
  }
}
</script>

<style>
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
</style>
