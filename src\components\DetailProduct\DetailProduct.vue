<template>
  <div>
    <v-card outlined height="100%">
      <v-container>
      <v-row class="fill-height" align="start" justify="start" dense no-gutters>
        <v-col cols="12" md="5" sm="12" xs="12">
          <v-row dense>
            <v-col cols="12" md="12" class="pl-5 pr-5">
              <v-img :src="primaryImage" height="400" width="100%" contain v-if="primaryImage !== ''" aspect-ratio="2" @click="openModal()"></v-img>
              <v-img src="@/assets/NoImage.png" max-height="2000px" max-width="2000px" height="100%" width="100%" contain aspect-ratio="2" v-else></v-img>
            </v-col>
            <v-col cols="12" md="12">
              <v-slide-group active-class="success" center-active multiple show-arrows>
                <v-slide-item v-for="(n, index) in productDetail.product_image" :key="index">
                  <v-card color="white" class="ma-1" max-height="100" max-width="100" @click="changeImage(n)">
                    <v-img :src="n" contain width="100" height="100"/>
                  </v-card>
                </v-slide-item>
              </v-slide-group>
            </v-col>
          </v-row>
        </v-col>
        <v-col cols="12" md="7" sm="12" xs="12">
          <v-row dense>
            <v-col cols="12" md="12" sm="12" class="mr-0">
              <p class="productName">{{ productDetail.name }}</p>
              <span class="mt-2 text-left textproduct">รหัสสินค้า : {{ productDetail.sku }}</span><br>
              <span class="mt-2 text-left textproduct">แบรนด์ : {{ productDetail.manufacturer_name }}</span><br>
              <span class="mt-2 text-left textproduct">จำหน่ายโดย : {{ productDetail.supplier_name }}</span>
            </v-col>
            <v-col cols="12" md="12" sm="12">
              <v-card outlined color="#fafafa">
                <p class="priceProduct pt-2 px-4 pb-0" v-if="productDetail.give_tier !== 'y'">฿ {{ Number(productDetail.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                <p class="priceProduct" v-else><span class="priceDecrese">฿ {{ Number(productDetail.product_float_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span> <span class="specialPrice">฿ {{ Number(productDetail.product_tier_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span></p>
              </v-card>
            </v-col>
            <span class="mt-2 text-left">การจัดส่ง : ฟรีค่าจัดส่ง</span><br>
            <v-col cols="12"  md="12" sm="12" class="mr-0 mt-5" v-if="productDetail.stock_count !== 0 && productDetail.stock_count >= 0">
              <span class="mr-0">จำนวน : </span>
              <v-btn elevation="1" x-small fab icon outlined class="mx-1 mb-1" @click="quantity--" :disabled="disabledinput_minus">
                <v-icon small>mdi-minus</v-icon>
              </v-btn>
              <input v-model="quantity" @change="inputQuantity" class="AddNumberProduct" size="4" :maxLength="productDetail.stock_count" :max="productDetail.stock_count" type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"/>
              <v-btn elevation="1" x-small fab icon outlined class="mx-1 mb-1" @click="quantity++" :disabled="disabledinput_plus">
                <v-icon small>mdi-plus</v-icon>
              </v-btn>
              <span class="ml-2" v-if="productDetail.stock_count !== 0 && productDetail.stock_count !== -1">มีสินค้าทั้งหมด {{ Number(productDetail.stock_count).toLocaleString(undefined) }} ชิ้น</span>
              <span class="ml-2" style="color: red;" v-else>ไม่มีสินค้าในสต็อก</span>
              <!-- <span class="ml-2">มีสินค้าทั้งหมด {{ productDetail.stock_count }} ชิ้น</span> -->
            </v-col>
            <v-col cols="12"  md="12" sm="12" class="mr-0 mt-5" v-else>
              <span class="mr-0">จำนวน : </span>
              <v-btn elevation="1" x-small fab icon outlined class="mx-1 mb-1" @click="quantity--" disabled>
                <v-icon small>mdi-minus</v-icon>
              </v-btn>
              <input v-model="quantity" @change="inputQuantity" class="AddNumberProduct" size="4" :maxLength="productDetail.stock_count" :max="productDetail.stock_count" type="text" oninput="this.value = this.value.replace(/[^0-9]/g, '').replace(/(\..*)\./g, '$1')"/>
              <v-btn elevation="1" x-small fab icon outlined class="mx-1 mb-1" @click="quantity++" disabled>
                <v-icon small>mdi-plus</v-icon>
              </v-btn>
              <span class="ml-2" v-if="productDetail.stock_count !== 0 && productDetail.stock_count !== -1">มีสินค้าทั้งหมด {{ Number(productDetail.stock_count).toLocaleString(undefined) }} ชิ้น</span>
              <span class="ml-2" style="color: red;" v-else>ไม่มีสินค้าในสต็อก</span>
            </v-col>
            <v-row class="mt-2" align="center" justify="center">
              <v-col cols="12" md="12" v-if="productDetail.stock_count !== 0  && productDetail.stock_count >= 0">
                <v-btn outlined large color="success" style="min-width: 78; height: 48px;" @click="AddToCart()"><v-icon color="#388E3C">mdi-cart-arrow-down</v-icon> เพิ่มไปยังรถเข็น</v-btn>
                <!-- <v-btn color="success" large class="ml-4 px-12" style="min-width: 78; height: 48px;">ซื้อสินค้าชิ้นนี้</v-btn> -->
              </v-col>
              <v-col cols="12" md="12" v-else>
                <v-btn outlined large color="success" style="min-width: 78; height: 48px;" @click="AddToCart()" disabled><v-icon color="#388E3C">mdi-cart-arrow-down</v-icon> เพิ่มไปยังรถเข็น</v-btn>
                <!-- <v-btn color="success" large class="ml-4 px-12" style="min-width: 78; height: 48px;">ซื้อสินค้าชิ้นนี้</v-btn> -->
              </v-col>
            </v-row>
          </v-row>
        </v-col>
      </v-row>
      </v-container>
      <v-card-actions></v-card-actions>
      <!-- <v-card-actions>
        <v-row dense justify="center">
          <v-col cols="12" md="12" class="pl-10">
            <span style="font-size: 16px;">แชร์ : </span>
            <v-btn icon @click="overlay = !overlay"><v-icon>mdi-qrcode-scan</v-icon></v-btn>
            <v-overlay
             :value="overlay"
            >
              <v-row justify="end" align="center" class="mb-4">
                <v-btn icon @click="overlay = false"><v-icon>mdi-close</v-icon></v-btn>
              </v-row>
              <v-row justify="center" align="center" class="mb-6">
                <vue-qrcode :value="`${Path}${pathRoute}`"></vue-qrcode>
              </v-row>
              <v-row justify="center" align="center">
                <v-text-field v-model="message" outlined dense></v-text-field>
                <v-btn
                  icon
                  v-clipboard:copy="message"
                  v-clipboard:success="onCopy"
                  v-clipboard:error="onError"
                  class="mb-6"
                  >
                  <v-icon>mdi-content-copy</v-icon>
                </v-btn>
              </v-row>
            </v-overlay>
            <v-icon color="rgb(66,103,178)" large>mdi-facebook</v-icon>
            <v-icon color="rgb(0,120,255)" large>mdi-facebook-messenger</v-icon>
            <v-icon color="#FF5722" large>mdi-google-plus</v-icon>
          </v-col>
        </v-row>
      </v-card-actions> -->
    </v-card>
    <v-card class="mx-auto mt-5" max-width="100%" outlined>
      <v-card-text style="padding: 1.5625rem;">
        <v-row no-gutters justify="start">
          <v-col cols="1" md="1" sm="12" xs="12">
            <v-avatar size="60" @click="gotoShopDetail()" v-if="productDetail.path_logo !== ''" style="cursor: pointer;">
              <img
                alt="user"
                :src="`${productDetail.path_logo}?=${new Date().getTime()}`"
              >
            </v-avatar>
            <v-avatar size="60" v-else style="cursor: pointer;" @click="gotoShopDetail()">
              <v-icon>
                mdi-storefront
              </v-icon>
            </v-avatar>
          </v-col>
          <v-col cols="2" md="2" sm="12" xs="12">
            <v-row dense no-gutters justify="start">
              <v-col cols="12" md="12" sm="12" xs="12">
                <p style="font-weight: bold; font-size: 15px;">{{ productDetail.seller_name_th }}</p>
              </v-col>
              <v-col cols="12" md="12" sm="12" xs="12">
                <v-btn outlined small color="orange" @click="gotoShopDetail()"><v-icon small class="pr-1">mdi-storefront</v-icon> ดูร้านค้า</v-btn>
              </v-col>
            </v-row>
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>
    <v-card class="mx-auto mt-5" max-width="100%" outlined>
      <v-container>
        <div style="background-color: rgba(0,0,0,.02); width: 100%; height: 50px; display: block;">
          <h2 style="padding-top: 0.5em; padding-left: 10px;">รายละเอียดสินค้า</h2>
        </div>
        <div class="pt-5 pl-5">
          <span v-html="productDetail.description"></span>
        </div>
      </v-container>
    </v-card>
    <v-dialog
     v-model="dialog"
     width="50%"
     height="100%"
    >
      <v-card>
        <v-img :src="primaryImage" height="100%" width="100%" contain aspect-ratio="2"></v-img>
      </v-card>
    </v-dialog>
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
export default {
  metaInfo () {
    return {
      meta: [
        { property: 'og:title', content: this.productname },
        { property: 'og:description', content: this.productdescription },
        { property: 'og:type', content: 'website' },
        { property: 'og:url', content: this.message },
        { property: 'og:image', content: this.primaryImage }
      ]
    }
  },
  data () {
    return {
      dialog: false,
      product_img_path: [],
      primaryImage: '',
      quantity: 1,
      stockcount: 0,
      disabledinput_minus: true,
      disabledinput_plus: false,
      shopImage: '',
      shopname: 'ร้านขายของ 1',
      shopID: 1,
      PathImage: process.env.VUE_APP_IMAGE,
      productDetail: [],
      pathRoute: '',
      Path: process.env.VUE_APP_WEB_FILE,
      overlay: false,
      zIndex: 0,
      absolute: true,
      message: '',
      productname: '',
      productdescription: ''
      // permission: ''
    }
  },
  serverPrefetch () {
    // return the Promise from the action
    // so that the component waits before rendering
    return this.getProductDetail()
  },
  watch: {
    // permission (val) {
    //   var per = JSON.parse(localStorage.getItem('roleUser'))
    //   // console.log('val', val.role)
    //   if (val.role !== per) {
    //     console.log('1234')
    //   }
    // },
    quantity (val) {
      var quantity = parseInt(val)
      if (parseInt(val) === 0) {
        this.quantity = 1
      } else if (parseInt(val) > 0) {
        if (quantity === 1) {
          this.disabledinput_minus = true
        } else {
          this.disabledinput_minus = false
        }
      } else if (parseInt(val) < 0) {
        this.quantity = 1
      }
      if (quantity >= this.productDetail.stock_count) {
        this.disabledinput_plus = true
        // const Toast = this.$swal.mixin({
        //   toast: true,
        //   showConfirmButton: false,
        //   timer: 2000,
        //   timerProgressBar: true
        // })
        // Toast.fire({
        //   icon: 'warning',
        //   text: 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว'
        // })
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 2000,
          timerProgressBar: true,
          icon: 'warning',
          text: 'ไม่สามารถเพิ่มจำนวนสินค้าได้ เนื่องจากคุณเพิ่มสินค้าถึงจำนวนที่กำหนดแล้ว'
        })
      } else {
        this.disabledinput_plus = false
      }
    }
  },
  created () {
    this.$EventBus.$on('getProductDetail', this.getProductDetail)
    // this.permission = JSON.parse(localStorage.getItem('roleUser'))
    // console.log(this.$router.currentRoute.path)
    this.pathRoute = this.$router.currentRoute.path
    this.message = this.Path + this.pathRoute
    this.getProductDetail()
  },
  mounted () {
    this.$EventBus.$emit('CheckPermission')
  },
  methods: {
    changeImage (image) {
      this.primaryImage = image
    },
    inputQuantity () {
      var quantity = parseInt(this.quantity)
      this.stockcount = this.productDetail.stock_count
      if (quantity > this.stockcount) {
        this.quantity = this.productDetail.stock_count
      }
    },
    gotoShopDetail () {
      const shopCleaned = encodeURIComponent(this.productDetail.seller_name_th.replace(/\s/g, '-'))
      this.$router.push({ path: `/shoppage/${shopCleaned}-${this.productDetail.seller_shop_id}` }).cache(() => {})
    },
    // GetProductDetail
    async getProductDetail () {
      var data
      var path = this.$router.currentRoute.params.data
      var cleanPath = path.split('-')
      if (localStorage.getItem('roleUser') !== null) {
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        data = {
          product_id: cleanPath[cleanPath.length - 1],
          role: dataRole.role
        }
      } else {
        data = {
          product_id: cleanPath[cleanPath.length - 1],
          role: 'ext_buyer'
        }
      }
      await this.$store.dispatch('actionsProductDetail', data)
      var res = await this.$store.state.ModuleProduct.stateProductDetailData
      // console.log('res product detail =====>', res)
      if (res.result === 'SUCCESS') {
        this.productDetail = res.data
        this.productname = res.data.name
        this.productdescription = res.data.description
        if (res.data.product_image.length !== 0) {
          this.primaryImage = res.data.product_image[0]
        } else {
          this.primaryImage = ''
        }
      } else {
        this.productDetail = ''
      }
    },
    async AddToCart () {
      if (this.quantity === '') {
        this.quantity = 1
      }
      if (localStorage.getItem('oneData') === null) {
        // console.log('in no login')
        var productImage = {}
        if (this.productDetail.product_image.length === 0) {
          productImage.url = ''
        } else {
          productImage.url = this.productDetail.product_image[0]
        }
        var netPrice = parseInt(this.quantity) * parseInt(this.productDetail.product_float_price)
        if (localStorage.getItem('_cartData') === null) {
          const setData = {
            product_to_cal: [],
            shop_to_cal: [],
            address_data: {},
            shop_list: [
              {
                shop_name: this.productDetail.seller_name_en,
                shop_id: this.productDetail.seller_shop_id,
                selectData: [],
                product_list: [
                  {
                    product_id: this.productDetail.product_id,
                    product_name: this.productDetail.name,
                    product_image: productImage,
                    shop_name: this.productDetail.seller_name_en,
                    sku: this.productDetail.sku,
                    quantity: this.quantity,
                    stock: this.productDetail.stock_count,
                    price: this.productDetail.product_float_price,
                    net_price: netPrice
                  }
                ]
              }
            ]
          }
          await localStorage.setItem('_cartData', Encode.encode(setData))
          this.quantity = 1
          this.$EventBus.$emit('getCartPopOver')
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          // Toast.fire({
          //   icon: 'success',
          //   title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
          })
        } else {
          var Cartdata = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
          var shoplist = {}
          var cartShopIndexList = {}
          var productlist = {}
          for (let shopIndex = 0; shopIndex < Cartdata.shop_list.length; shopIndex++) {
            const shop = Cartdata.shop_list[shopIndex]
            cartShopIndexList[shop.shop_id] = shopIndex
            shoplist[shop.shop_id] = 0
            for (let prodIndex = 0; prodIndex < shop.product_list.length; prodIndex++) {
              const productList = shop.product_list[prodIndex]
              productlist[productList.product_id] = 0
            }
          }
          // console.log('shoplist ===', shoplist)
          // console.log('productlist ===', productlist)
          // console.log('this.productDetail.seller_shop_id', this.productDetail.seller_shop_id)
          if (this.productDetail.product_id in productlist) {
            // console.log('duplicate product')
            this.$swal.fire({
              toast: true,
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'error',
              title: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว'
            })
            return true
          } else {
            // const Toast = this.$swal.mixin({
            //   toast: true,
            //   showConfirmButton: false,
            //   timer: 1500,
            //   timerProgressBar: true
            // })
            // Toast.fire({
            //   icon: 'success',
            //   title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
            // })
            this.$swal.fire({
              toast: true,
              showConfirmButton: false,
              timer: 1500,
              timerProgressBar: true,
              icon: 'success',
              title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
            })
            if (this.productDetail.seller_shop_id in shoplist) {
              // console.log('add new product in shop')
              var newProduct = {
                product_id: this.productDetail.product_id,
                product_name: this.productDetail.name,
                product_image: productImage,
                shop_name: this.productDetail.seller_name_en,
                sku: this.productDetail.sku,
                quantity: this.quantity,
                stock: this.productDetail.stock_count,
                price: this.productDetail.product_float_price,
                net_price: netPrice
              }
              Cartdata.shop_list[cartShopIndexList[this.productDetail.seller_shop_id]].product_list.push(newProduct)
              await localStorage.setItem('_cartData', Encode.encode(Cartdata))
              this.quantity = 1
              this.$EventBus.$emit('getCartPopOver')
            } else {
              // console.log('add new shop')
              const setData = {
                shop_name: this.productDetail.seller_name_en,
                shop_id: this.productDetail.seller_shop_id,
                selectData: [],
                product_list: [
                  {
                    product_id: this.productDetail.product_id,
                    product_name: this.productDetail.name,
                    product_image: productImage,
                    shop_name: this.productDetail.seller_name_en,
                    sku: this.productDetail.sku,
                    quantity: this.quantity,
                    stock: this.productDetail.stock_count,
                    price: this.productDetail.product_float_price,
                    net_price: netPrice
                  }
                ]
              }
              Cartdata.shop_list.push(setData)
              await localStorage.setItem('_cartData', Encode.encode(Cartdata))
              this.quantity = 1
              this.$EventBus.$emit('getCartPopOver')
            }
          }
        }
      } else {
        // Add product to cart with login
        var dataRole = JSON.parse(localStorage.getItem('roleUser'))
        var data = {
          seller_shop_id: this.productDetail.seller_shop_id,
          sku: this.productDetail.sku,
          quantity: this.quantity,
          role_user: dataRole.role
        }
        await this.$store.dispatch('ActionAddToCart', data)
        const res = await this.$store.state.ModuleCart.stateAddToCart
        // console.log('response add to cart in product detail', res)
        if (res.message === 'Add to Cart Success') {
          this.$EventBus.$emit('getCartPopOver')
          this.quantity = 1
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          // Toast.fire({
          //   icon: 'success',
          //   title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'success',
            title: 'เพิ่มสินค้าลงในรถเข็นเรียบร้อย'
          })
        } else if (res.message === 'This product is already in cart') {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          // Toast.fire({
          //   icon: 'warning',
          //   title: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว'
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'มีสินค้ารายการนี้อยู่ในรถเข็นแล้ว'
          })
        } else if (res.message === 'Some parameter missing. [seller_shop_id, sku, quantity]') {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          // Toast.fire({
          //   icon: 'error',
          //   title: 'ใส่ข้อมูลไม่ครบ'
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'ใส่ข้อมูลไม่ครบ'
          })
        } else if (res.message === 'An error has occurred. Please try again in an hour or two.') {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          // Toast.fire({
          //   icon: 'error',
          //   title: 'SERVER ERROR detail '
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'SERVER ERROR detail'
          })
        } else if (res.message === 'This user is unauthorized.') {
          this.$store.commit('closeLoader')
          this.$EventBus.$emit('refreshToken')
        } else if (res.message === 'Please insert quantity > 0') {
          // const Toast = this.$swal.mixin({
          //   toast: true,
          //   showConfirmButton: false,
          //   timer: 1500,
          //   timerProgressBar: true
          // })
          // Toast.fire({
          //   icon: 'error',
          //   title: 'SERVER ERROR detail '
          // })
          this.$swal.fire({
            toast: true,
            showConfirmButton: false,
            timer: 1500,
            timerProgressBar: true,
            icon: 'error',
            title: 'กรุณาใส่จำนวนสินค้าอย่างน้อย 1 ชิ้น'
          })
        }
      }
    },
    onCopy: function (e) {
      // const Toast = this.$swal.mixin({
      //   toast: true,
      //   showConfirmButton: false,
      //   timer: 1500,
      //   timerProgressBar: true,
      //   position: 'top'
      // })
      // Toast.fire({
      //   icon: 'success',
      //   title: 'คัดลอกลิงก์สำเร็จ!'
      // })
      this.$swal.fire({
        toast: true,
        showConfirmButton: false,
        timer: 1500,
        timerProgressBar: true,
        position: 'top',
        icon: 'success',
        title: 'คัดลอกลิงก์สำเร็จ!'
      })
    },
    onError: function (e) {
      alert('Failed to copy texts')
    },
    openModal () {
      this.dialog = true
    }
  }
}
</script>

<style scoped>
.v-text-field{
  width: 350px;
}
.productName {
  font-weight: 500;
  margin: 0;
  vertical-align: sub;
  max-height: 3rem;
  line-height: 1.5rem;
  overflow: hidden;
  max-width: 41.5625rem;
  font-size: 1.25rem;
}
.textproduct {
  color: #333;
  font-size: 12px;
  font-weight: 300;
  line-height: 1.375;
}
.priceProduct {
  font-size: 1.875rem;
  font-weight: 500;
  color: #ee4d2d;
  padding-bottom: 0;
}
.AddNumberProduct {
  height: 30px;
  width: 60px;
  box-shadow: inset 0 1px 3px 0 rgba(232, 232, 232, 0.5);
  background-color: #ffffff;
  text-align: center;
}
.priceDecrese {
  font-size: 1.55rem;
  text-decoration: line-through;
  color: #929292;
  margin-right: 10px;
}
.specialPrice {
  font-size: 1.875rem;
  font-weight: 500;
  color: red;
}
</style>
