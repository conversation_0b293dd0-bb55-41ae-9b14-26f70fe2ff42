<template lang="html">
  <div>
    <v-breadcrumbs :items="items">
      <template v-slot:divider>
        <v-icon>mdi-chevron-right</v-icon>
      </template>
      <template v-slot:item="{ item }">
        <v-breadcrumbs-item :href="item.href" :disabled="item.disabled">
          <span :style="{color: item.disabled === true ? '#27AB9C' : '#636363','font-size': '16px'}">{{ item.text }}</span>
        </v-breadcrumbs-item>
      </template>
    </v-breadcrumbs>
    <v-divider class="mt-1"></v-divider>
    <v-row class="mt-1">
      <v-col cols="12" md="8">
        <v-row no-gutters>
          <v-col cols="12" class="mb-5">
            <v-card class="mt-3">
              <v-row no-gutters>
                <v-col cols="8" md="6" class="pl-5 pt-5 pb-0">
                  <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>ที่อยู่ในการจัดส่งสินค้า</b></p>
                </v-col>
                <!-- start แก้ไขที่อยู่ -->
                <v-col cols="4" md="6" class="pt-5 text-right" v-if="Address !== 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                  <v-btn text rounded color="#333333" :class="MobileSize || IpadSize || IpadProSize ? 'mr-2' : 'mr-5'" small elevation="0" @click="editAddress(AddressData)"><v-icon class="pr-1" small color="#A1A1A1">mdi-pencil</v-icon>แก้ไขที่อยู่</v-btn>
                </v-col>
                 <!-- end แก้ไขที่อยู่ -->
              </v-row>
              <v-col cols="12" align="left" class="pt-0 pl-5" v-if="Address === 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่'">
                <v-btn outlined icon small color="#A1A1A1" @click="openAddAddress()"><v-icon small color="#A1A1A1">mdi-plus</v-icon></v-btn>
                <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งสินค้า</b></span>
              </v-col>
              <v-col cols="12" align="left" class="pt-0 pl-5" v-else>
                {{ Address }}
              </v-col>
            </v-card>
          </v-col>
          <!--start เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
          <!-- <v-col cols="12" class="mb-5">
            <v-card>
              <v-col cols="12" class="pl-5 pt-5 pb-0">
                <h2><b>ที่อยู่ในการจัดส่งใบกำกับภาษี</b></h2>
              </v-col>
              <v-col v-if="taxAddress === ''" cols="12" align="left" class="pt-0 pl-5">
                <v-btn outlined icon small color="#A1A1A1" @click="openTaxInvoiceAddressLocal()"><v-icon small color="#A1A1A1">mdi-plus</v-icon></v-btn>
                <span class="pl-2"><b>เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี</b></span>
              </v-col>
              <v-col v-else cols="12" align="left" class="pt-0 pl-5">
                {{ companyName }}  {{ companyTaxID }} {{ taxAddress }}
              </v-col>
            </v-card>
          </v-col> -->
          <!--end เพิ่มที่อยู่ในการจัดส่งใบกำกับภาษี -->
          <!-- Desktop, ipadPro -->
          <v-col cols="12" md="12" v-if="!MobileSize">
            <v-card>
              <v-col cols="12" class="pl-5 pt-5">
                <p style="font-size: 24px;"><b>รายการสั่งซื้อสินค้า</b></p>
              </v-col>
              <!-- start ขอใบเสนอราคา Desktop, ipadPro -->
              <!-- <v-col cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain></v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false" class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ขอใบเสนอราคา</v-btn>
              </v-col> -->
              <!-- end ขอใบเสนอราคา Desktop, ipadPro -->
              <v-container grid-list-xs>
                <a-table bordered v-for="(item,index) in itemsCart" :key="index" :data-source="item.product_list" :rowKey="(record, index) => checkRowKey(record)" :columns="headers">
                  <template slot="title">
                    <v-row class="text-left">
                      <v-col justify="center">
                        <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
                        <b class="ml-3" style="line-height: 30px;">{{item.shop_name}}</b>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="12" md="4" class="pr-0">
                        <v-img :src="`${record.product_image}`" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"/>
                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else/>
                      </v-col>
                      <v-col cols="12" md="8">
                        <p class="mb-0 captionSku">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                        <span v-if="record.attribute_option_1" class="mb-0 captionSku">{{record.key_1_value}}: {{record.attribute_option_1}}</span>
                        <span v-if="record.attribute_option_2" class="pl-2 mb-0 captionSku">{{record.key_2_value}}: {{record.attribute_option_2}}</span>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="price" slot-scope="text, record">
                    <v-col cols="12">
                      <span>{{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                    </v-col>
                  </template>
                  <template slot="net_price" slot-scope="text, record">
                    <span>{{ Number(record.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
                  </template>
                </a-table>
              </v-container>
            </v-card>
          </v-col>
          <!-- Mobile Ipad -->
          <v-col cols="12" md="12" v-else>
            <v-card>
              <v-col cols="12" class="pl-5 pt-5">
                <p style="font-size: 20px;"><b>รายการสั่งซื้อสินค้า</b></p>
              </v-col>
              <!-- start ขอใบเสนอราคา mobile, ipad -->
              <!-- <v-col cols="12" md="12" sm="12">
                <v-avatar min-width="32" width="32" height="32" color="#FFEFEE" style="border-radius: 4px;">
                  <v-img src="@/assets/ImageINET-Marketplace/ICONRegister/pdf.png" width="22.86" height="22.86" contain></v-img>
                </v-avatar>
                <v-btn text style="color: #27AB9C; font-weight: bold;" :ripple="false" class="pl-0 pr-0 ml-2 hide-background-hover" @click="openQuotation()">ขอใบเสนอราคา</v-btn>
              </v-col> -->
              <!-- end ขอใบเสนอราคา mobile, ipad -->
              <v-container grid-list-xs>
                <a-table bordered v-for="(item,index) in itemsCart" :key="index" :data-source="item.product_list" :rowKey="(record, index) => checkRowKey(record)" :columns="headersMobile" :showHeader="false">
                  <template slot="title">
                    <v-row class="text-left">
                      <v-col justify="center">
                        <v-img class="float-left" src="@/assets/ImageINET-Marketplace/Shop/store-icon.png" width="30" height="30"></v-img>
                        <b class="ml-3" style="line-height: 30px;">{{item.shop_name}}</b>
                      </v-col>
                    </v-row>
                  </template>
                  <template slot="productdetails" slot-scope="text, record">
                    <v-row>
                      <v-col cols="3" md="4" class="pr-0 py-1">
                        <v-img :src="`${record.product_image}`" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-if="record.product_image !== ''"/>
                        <v-img src="@/assets/NoImage.png" :class="IpadProSize ? 'imageshowIpadPro' : 'imageshow'" v-else/>
                      </v-col>
                      <v-col cols="9" md="8">
                        <p class="mb-0 captionSku">รหัสสินค้า: {{record.sku}}<br/>{{record.product_name}}</p>
                        <!-- start detail product attribute mobile -->
                        <span v-if="record.attribute_option_1" class="mb-0 captionSku">{{record.key_1_value}}: {{record.attribute_option_1}}</span>
                        <span v-if="record.attribute_option_2" class="pl-2 mb-0 captionSku">{{record.key_2_value}}: {{record.attribute_option_2}}</span>
                        <!-- end detail product attribute mobile -->
                        <p class="mb-0 captionSku">ราคา: {{ Number(record.price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</p>
                      </v-col>
                    </v-row>
                  </template>
                </a-table>
              </v-container>
            </v-card>
          </v-col>
        </v-row>
      </v-col>
      <v-col cols="12" md="4">
        <v-card class="mt-3">
          <v-container grid-list-xs>
            <v-row>
              <v-col cols="12">
                <p :style="MobileSize ? 'font-size: 20px;' : 'font-size: 24px;'"><b>สรุปรายการสั่งซื้อสินค้า</b></p>
              </v-col>
              <v-col cols="8" class="py-0">
                <span>ราคาไม่รวมภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="4" align="right" class="py-0">
                <span>{{ Number(TotalPriceNoVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="12">
                <v-divider></v-divider>
              </v-col>
              <v-col cols="8">
                <span>ส่วนลด</span>
              </v-col>
              <v-col cols="4" align="right">
                <span>{{ Number(TotalPriceDiscount).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <!-- <v-col cols="12">
                <span>ใช้โค้ดส่วนลด</span>
              </v-col> -->
              <v-col cols="8">
                <span>ภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="4" align="right">
                <span>{{ Number(TotalVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="8">
                <span>ราคารวมภาษีมูลค่าเพิ่ม</span>
              </v-col>
              <v-col cols="4" align="right">
                <span>{{ Number(TotalPriceVat).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</span>
              </v-col>
              <v-col cols="8" class="py-0">
                <span>ค่าจัดส่ง</span>
              </v-col>
              <v-col cols="4" class="py-0" align="right">
                <span><b>{{ Number(TotalShipping).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></span>
              </v-col>
              <v-col cols="8" class="py-0">
                <!-- <span style="font-size: 10px" v-if="Number(itemsCart.total_shipping) === 0 &&
                (address_data.province !== 'กรุงเทพมหานคร' && address_data.province !== 'นครปฐม' && address_data.province !== 'นนทบุรี' && address_data.province !== 'ปทุมธานี' && address_data.province !== 'สมุทรปราการ' && address_data.province !== 'สมุทรสาคร')">ค่าจัดส่งขึ้นอยู่เงื่อนที่ทางบริษัทเป็นผู้กำหนด</span>
                <span style="font-size: 10px" v-else-if="Number(itemsCart.total_shipping) === 0 &&
                (address_data.province === 'กรุงเทพมหานคร' || address_data.province === 'นครปฐม' || address_data.province === 'นนทบุรี' || address_data.province === 'ปทุมธานี' || address_data.province === 'สมุทรปราการ' || address_data.province === 'สมุทรสาคร')">ฟรีค่าส่งในกรุงเทพฯ และปริมณฑล</span> -->
                <span style="font-size: 10px; color: #8C8C8C;" class="mb-0">ราคานี้เป็นมาตรฐาน - ราคาอาจแตกต่างกันไปขึ้นอยู่กับสินค้า / ปลายทาง เจ้าหน้าที่ จัดส่งจะติดต่อคุณ</span>
              </v-col>
              <v-col cols="8">
                <span class="totalPriceFont"><b>ราคารวมทั้งหมด</b></span>
              </v-col>
              <v-col cols="4" align="right">
                <span class="totalPriceFont"><b>{{ Number(TotalNetPrice).toLocaleString(undefined, {minimumFractionDigits: 2}) }}</b></span>
              </v-col>
              <v-col cols="12" align="right" class="mt-2 mb-2">
                <v-btn class="white--text" block color="#27AB9C" @click="confirmCreateOrderMobile()">
                  <span style="font-size: 16px; font-style: normal; font-weight: bold;">ชำระเงิน</span>
                </v-btn>
                <!-- <v-btn rounded block color="#00B500" dark @click="confirmCreateOrderMobile()"><b>ชำระเงิน</b></v-btn> -->
              </v-col>
            </v-row>
          </v-container>
        </v-card>
      </v-col>
    </v-row>
    <EditModalAddress />
    <!-- <TaxInvoiceAddressLocal ref="TaxInvoiceAddressLocal" /> -->
    <!-- <ModalQuotation /> -->
    <ModalAddressLocal />
  </div>
</template>

<script>
import { Decode, Encode } from '@/services'
import { Table } from 'ant-design-vue'
export default {
  components: {
    'a-table': Table,
    ModalAddressLocal: () => import(/* webpackPrefetch: true */ '@/components/Modal/AddressLocalUI'),
    EditModalAddress: () => import(/* webpackPrefetch: true */ '@/components/Modal/EditAddressLocalUI.vue')
    // TaxInvoiceAddressLocal: () => import('@/components/Cart/ModalAddress/TaxInvoiceAddressLocal')
    // ModalQuotation: () => import('@/components/Cart/ModalQuotation/QuotationModal')
  },
  data: () => ({
    overlay: false,
    items: [
      {
        text: 'หน้าแรก',
        disabled: false,
        href: '/'
      },
      {
        text: 'รถเข็น',
        disabled: false,
        href: '/shoppingcart'
      },
      {
        text: 'รายการสั่งซื้อสินค้า',
        disabled: true,
        href: '/checkout'
      }
    ],
    cartData: '',
    itemsCart: [],
    DataGetCart: {},
    Address: 'คุณยังไม่ได้เพิ่มข้อมูลที่อยู่',
    AddressData: {},
    address_data: '',
    taxAddress: '',
    companyName: '',
    companyTaxID: '',
    checkAdminQU: true,
    Fullname: '',
    TotalPriceNoVat: 0,
    TotalPriceDiscount: 0,
    TotalVat: 0,
    TotalPriceVat: 0,
    TotalShipping: 0,
    TotalNetPrice: 0,
    dataeditAddressLocal: '',
    check: false,
    taxInvoiceID: ''
  }),
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    headers () {
      const headers = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '40%'
        },
        {
          title: 'จำนวน',
          dataIndex: 'quantity',
          key: 'quantity',
          scopedSlots: { customRender: 'quantity' },
          width: '20%'
        },
        {
          title: 'ราคาต่อชิ้น',
          dataIndex: 'price',
          key: 'price',
          scopedSlots: { customRender: 'price' },
          width: '20%'
        },
        {
          title: 'ราคารวม',
          dataIndex: 'net_price',
          key: 'net_price',
          scopedSlots: { customRender: 'net_price' },
          width: '20%'
        }
      ]
      return headers
    },
    headersMobile () {
      const headersMobile = [
        {
          title: 'รายละเอียดสินค้า',
          dataIndex: 'productdetails',
          key: 'productdetails',
          scopedSlots: { customRender: 'productdetails' },
          width: '30%'
        }
      ]
      return headersMobile
    }
  },
  watch: {
  },
  // mounted () {
  //   this.$on('hook:beforeDestroy', () => {
  //     this.$EventBus.$on('SentGetAddressLocal')
  //     this.$EventBus.$on('GetTaxAddressLocal')
  //     this.$EventBus.$on('SentGetCartLocal')
  //   })
  // },
  mounted () {
    this.$EventBus.$emit('openAddresslocal')
    // this.$EventBus.$on('closeAddresslocal', this.backstep())
    this.$EventBus.$on('SentGetCartLocal', this.getCart())
    this.$EventBus.$on('SentGetAddressLocal', (data) => { this.getAddress(data) })
    this.$EventBus.$on('GetTaxAddressLocal', (data) => { this.checkAddressTaxinvoiceData(data) })
    var cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
    // console.log('cartdata created', cartData)
    if (cartData !== null) {
      if (cartData.product_to_cal.length === 0 || cartData.shop_to_cal.length === 0) {
        this.$router.push('/shoppingcart')
      } else {
        this.getCart()
      }
    }
  },
  beforeDestroy () {
    this.$EventBus.$off('SentGetCartLocal')
    this.$EventBus.$off('SentGetAddressLocal')
    this.$EventBus.$off('GetTaxAddressLocal')
    // this.$EventBus.$off('closeAddresslocal')
  },
  methods: {
    checkRowKey (record) {
      // set key กรณีที่ สินค้ามี หนึ่ง attribute
      if (record.key_2_value === '') {
        if (record.status_data_change === 'yes') {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null) {
              return record.attribute_option_1
            }
          } else {
            return record.sku
          }
        } else {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null) {
              return record.attribute_option_1
            }
          } else {
            return record.sku
          }
        }
      } else {
        if (record.status_data_change === 'yes') {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null && record.key_2_value !== null) {
              var newKey = record.attribute_option_1.concat(record.attribute_option_2.toString())
              return newKey
            }
          } else {
            return record.sku
          }
        } else {
          if (record.have_attribute === 'yes') {
            if (record.key_1_value !== null && record.key_2_value !== null) {
              var newKey2 = record.attribute_option_1.concat(record.attribute_option_2.toString())
              return newKey2
            }
          } else {
            return record.sku
          }
        }
      }
    },
    async getCart () {
      // this.overlay = true
      this.$store.commit('openLoader')
      const cartData = JSON.parse(Decode.decode(localStorage.getItem('_cartData')))
      // console.log('Checkout Get Cart', cartData)
      var data = {
        shop_to_cal: cartData.shop_to_cal,
        product_to_calculate: cartData.product_to_cal,
        shop_list: cartData.shop_list,
        address_data: [this.AddressData],
        invoice_id: this.taxInvoiceID
      }
      await this.$store.dispatch('ActionLocalstorageGetCart', data)
      var res = await this.$store.state.ModuleCart.stateLocalstorageGetCart
      // console.log('res get cart', res.data)
      if (res.message === 'Get cart success') {
        this.$store.commit('closeLoader')
        this.DataGetCart = res.data
        this.overlay = false
        this.itemsCart = res.data.choose_list
        this.TotalPriceNoVat = res.data.total_price_no_vat
        this.TotalVat = res.data.total_vat
        this.TotalPriceVat = res.data.total_price_vat
        this.TotalShipping = res.data.total_shipping
        this.TotalNetPrice = res.data.total_net_price
        this.TotalPriceDiscount = res.data.total_price_discount
      } else if (res.message === 'มีความผิดพลาดจากการคำนวนค่าขนส่ง Flash จากที่อยู่ของผู้ใช้งาน') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'warning',
          title: 'กรุณาทำรายการยืนยันคำสั่งซื้ออีกครั้ง'
        })
        this.backstep()
      } else if (res.message === 'น้ำหนักของสินค้าในตระกร้ารวมกันแล้วเกิน 50 kg ที่กำหนดขนส่งของ flash') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'warning',
          title: 'สินค้าของคุณมีน้ำหนักรวมของสินค้าเกิน 50 กิโลกรัม หากต้องการสั่งซื้อกรุณาติดต่อเจ้าหน้าที่ ขอบคุณครับ'
        })
        this.backstep()
      } else if (res.message === 'Get cart faild.Some products have weight equal 0.') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 2500,
          timerProgressBar: true,
          icon: 'error',
          title: `${'รหัสสินค้า' + ' ' + res.data[0].sku + ' ' + 'มีปัญหาเรื่องน้ำหนัก กรุณาติดต่อเจ้าหน้าที่'}`
        })
        this.backstep()
      } else if (res.message === 'ขออภัยเนื่องจากที่อยู่จัดส่งอยู่นอกเขตพื้นที่บริการ หรือ ขนาดและน้ำหนักของสินค้าเกินมามาตรฐานที่จะจัดส่งได้') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: `${res.message}`
        })
        this.backstep()
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 7000,
          timerProgressBar: true,
          icon: 'error',
          title: `${res.message}`
        })
        this.backstep()
      }
    },
    async getCartUpdate (cartData) {
      this.$store.commit('openLoader')
      var data = {
        shop_to_cal: cartData.shop_to_cal,
        product_to_cal: cartData.product_to_cal,
        shop_list: cartData.shop_list,
        address_data: [this.AddressData],
        invoice_id: this.taxInvoiceID
      }
      await this.$store.dispatch('ActionLocalstorageGetCart', data)
      var res = await this.$store.state.ModuleCart.stateLocalstorageGetCart
      // console.log('res get cart', res.data)
      if (res.message === 'Get cart success') {
        this.$store.commit('closeLoader')
        this.DataGetCart = res.data
        this.overlay = false
        this.itemsCart = res.data.choose_list
        this.TotalPriceNoVat = res.data.total_price_no_vat
        this.TotalVat = res.data.total_vat
        this.TotalPriceVat = res.data.total_price_vat
        this.TotalShipping = res.data.total_shipping
        this.TotalNetPrice = res.data.total_net_price
        this.TotalPriceDiscount = res.data.total_price_discount
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ title: 'ดำเนินการไม่สำเร็จ', text: res.message, icon: 'error', timer: 1500, timerProgressBar: true, showConfirmButton: false })
      }
    },
    // googleSentData () {
    //   window.dataLayer = window.dataLayer || []
    //   window.dataLayer.push({
    //     event: 'begin_checkout',
    //     currency: 'THB',
    //     value: this.TotalNetPrice,
    //     items: this.itemsCart[0].product_list
    //   })
    //   this.$analytics.fbq.event('AddToCart', {
    //     productList: this.itemsCart[0].product_list,
    //     productTotalPrice: this.TotalNetPrice
    //   })
    // },
    backstep () {
      // this.$router.go(-1)
      this.$router.replace({ path: '/shoppingcart' }).catch(() => { this.$router.go(-1) })
    },
    backstepCart () {
      // this.$router.go(-1)
      this.$router.push({ path: '/shoppingcart' }).catch(() => {})
    },
    getAddress (val) {
      this.AddressData = val
      this.Address = val.first_name + ' ' + val.last_name + ' ' + val.address_detail + ' ' + 'แขวง/ตำบล' + ' ' + val.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + val.district + ' ' + 'จังหวัด' + ' ' + val.province + ' ' + val.zipcode + ' ' + 'เบอร์' + ' ' + val.phone
      this.getCart()
    },
    confirmCreateOrder () {
      // const ToastDelete = this.$swal.mixin({
      //   toast: true,
      //   showCancelButton: true,
      //   confirmButtonText: 'ยืนยัน',
      //   cancelButtonText: 'ยกเลิก',
      //   cancelButtonColor: '#d33'
      // })
      this.$swal.fire({
        toast: true,
        showCancelButton: true,
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        cancelButtonColor: '#d33',
        icon: 'warning',
        title: 'ยืนยันการสั่งซื้อหรือไม่'
      }).then((result) => {
        if (result.isConfirmed) {
          this.CreateOrder()
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    confirmCreateOrderMobile () {
      this.$swal.fire({
        icon: 'warning',
        showCancelButton: true,
        html: '<h3>ยืนยันการสั่งซื้อหรือไม่</h3>',
        confirmButtonText: 'ยืนยัน',
        cancelButtonText: 'ยกเลิก',
        confirmButtonColor: '#27AB9C',
        reverseButtons: true
        // cancelButtonColor: '#aaa'
      }).then((result) => {
        if (result.isConfirmed) {
          this.CreateOrder()
          // this.googleSentData()
        } else if (result.isDismissed) {
        }
      }).catch(() => {
      })
    },
    async CreateOrder () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('ActionLocalstorageCreateOrder', this.DataGetCart)
      var res = await this.$store.state.ModuleCart.stateLocalstorageCreateOrder
      // console.log('response create order', res)
      this.overlay = false
      if (res.message === 'Create Order success') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'success',
          title: 'สั่งสินค้าเรียบร้อย'
        })
        var setData = {
          product_to_cal: res.data.product_to_cal,
          shop_to_cal: res.data.shop_to_cal,
          address_data: {},
          shop_list: res.data.shop_list
        }
        await localStorage.setItem('_cartData', Encode.encode(setData))
        var dataPayment = {
          payment_transaction_number: res.data.payment_transaction_number
        }
        // console.log('BeforePayment', res.data)
        await this.$store.dispatch('ActionGetPaymentPage', dataPayment)
        var resRedirect = this.$store.state.ModuleCart.stateGetPaymentPage
        this.overlay = false
        this.$EventBus.$emit('getCartPopOver')
        localStorage.setItem('PaymentData', Encode.encode(resRedirect))
        this.$router.push('/RedirectPaymentPage')
      } else if (res.message === 'Not enough product in stock') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: `สินค้า ${res.data[0].product} สามารถซื้อได้ ${res.data[0].quantity} ชิ้นเท่านั้น`
        })
        this.backstep()
      } else if (res.message === 'Update New Price') {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 1500,
          timerProgressBar: true,
          icon: 'warning',
          title: 'ระบบมีการอัปเดตสินค้าใหม่'
        })
        this.getCartUpdate(res.data)
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({
          toast: true,
          showConfirmButton: false,
          timer: 3000,
          timerProgressBar: true,
          icon: 'error',
          title: `${res.message}`
        })
        this.backstep()
      }
    },
    checkAddressTaxinvoiceData (data) {
      // console.log('checkAddressTaxinvoiceData', data)
      if (data.role === 'non-login') {
        this.companyName = data.name
        this.companyTaxID = 'เลขประจำตัวผู้เสียภาษี :' + ' ' + data.tax_id
        this.taxAddress = data.address + ' ' + 'แขวง/ตำบล' + ' ' + data.sub_district + ' ' + 'เขต/อำเภอ' + ' ' + data.district + ' ' + 'จังหวัด' + ' ' + data.province + ' ' + data.postal_code
        this.taxInvoiceID = data.invoice_id
        this.getCart()
      } else {
        this.companyName = 'ไม่มีข้อมูล'
        this.companyTaxID = 'ไม่มีข้อมูล'
        this.taxAddress = 'ไม่มีข้อมูล'
      }
    },
    editAddress (val) {
      // console.log('OpenDialog Edit ====>', val)
      localStorage.setItem('AddressUserDetail', Encode.encode(val))
      // this.EditaddressDialog = val
      this.$EventBus.$emit('EditModalAddress')
    },
    openTaxInvoiceAddressLocal () {
      this.$refs.TaxInvoiceAddressLocal.open()
      // มีการยิง api get ข้อมูลใบกำกับภาษี
      // localStorage.setItem('AddressData', Encode.encode(this.propsAddress[0].address_data))
    },
    openQuotation () {
      var dataList = []
      // console.log('DataGetCart===>', this.DataGetCart)
      var totalShipping = this.DataGetCart.total_shipping
      var discount = this.DataGetCart.total_price_discount
      var vat = this.DataGetCart.total_vat
      this.itemsCart.forEach(element => {
        element.product_list.forEach(product => {
          const productList = {
            product_id: product.product_id,
            // attribute_option_1: product.attribute_option_1 ? product.attribute_option_1 : '',
            // attribute_option_2: product.attribute_option_2 ? product.attribute_option_2 : '',
            quantity: product.quantity,
            price: product.price,
            status: product.product_status
          }
          dataList.push(productList)
        })
      })
      // ข้อมูลที่ขาด product_status
      const data = {
        list_data: dataList,
        companyName: '',
        fullname: this.AddressData.first_name + ' ' + this.AddressData.last_name,
        phone: this.AddressData.phone,
        email: this.AddressData.email,
        total_shipping: totalShipping,
        discount: discount,
        vat: vat,
        isAdmin: false
      }
      // console.log('openModalQuotation==>', data)
      this.$EventBus.$emit('openModalQuotation', data)
    }
  }
}
</script>

<style lang="css" scoped>
.captionSku {
  font-size: 13px;
  font-style: normal;
  font-family: 'Prompt' !important;
  /* font-weight: 500; */
}
.captionSkuMobile {
  font-size: 12px;
  font-style: normal;
  font-family: 'Prompt' !important;
  /* font-weight: 500; */
}
.v-breadcrumbs {
  align-items: center;
  display: flex;
  flex-wrap: wrap;
  flex: 0 1 auto;
  list-style-type: none;
  margin: 0;
  padding: 4px 0px 4px 0px !important;
}
.v-breadcrumbs li .v-icon {
  color: #27AB9C;
}
.imageshow {
  max-width: 75px !important;
  width: 75px;
  height: 75px;
  cursor: pointer;
}
.imageshowIpadPro {
  max-width: 60px !important;
  width: 60px;
  height: 60px;
  cursor: pointer;
}
.totalPriceFont {
  font-size: 18px;
}
::v-deep .ant-table-pagination {
  display: none;
}
::v-deep .ant-table-header-column .ant-table-selection {
  display: none;
}
::v-deep .ant-table-thead > tr > th.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-tbody > tr > td.ant-table-selection-column {
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-thead > tr > th {
  border-top: 0px solid #e8e8e8 !important;
  border-right: 0px solid #e8e8e8 !important;
  border-left: 0px solid #e8e8e8 !important;
}
::v-deep .ant-table-bordered .ant-table-body > table {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table-bordered .ant-table-tbody > tr > td {
  border: 0px solid #e8e8e8;
}
::v-deep .ant-table.ant-table-bordered .ant-table-title {
  border: 1px solid #e8e8e8;
  margin-bottom: 6px;
  border-radius: 8px;
}
</style>
