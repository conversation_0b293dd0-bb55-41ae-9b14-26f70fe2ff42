<template>
  <div>
    <v-dialog v-model="dialogUserTopTen" persistent width="500">
      <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>สินค้าขายดี Top 10</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogUserTopTen = !dialogUserTopTen" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="4" md="2">
              <v-avatar size="60" style="background: #F3F5F9; border-radius: 999px;">
                <v-img src="../../assets/icons/nullproduct.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="7" md="6" class="mt-3">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">สินค้า</span>
            </v-col>
          </v-row>
          <v-row dense class="my-4">
            <v-img src="@/assets/LineDash.png" contain width="100%"></v-img>
          </v-row>
          <v-row dense>
            <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
              <v-card-text class="py-0 px-0">
                <v-list style="background: #FAFAFA;" disabled>
                  <v-list-item-group v-for="(item, i) in itemUserExtbuyer" :key="i">
                    <v-list-item>
                      <div style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333;" class="pr-4">{{ item.number }}</div>
                      <v-list-item-avatar :size="MobileSize ? '35' : '64'">
                        <!-- <v-img v-if="item.image !== null" :src="item.image" contain></v-img> -->
                        <img v-if="item.product_image"
                          :src="item.product_image"
                          alt="Product"
                          >
                         <img v-else
                         src="../../assets/icons/nullproduct.png"
                         alt="Product"
                         >
                      </v-list-item-avatar>
                      <v-list-item-content class="mx-1 ml-4">
                        <v-list-item-title>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <span v-bind="attrs" v-on="on" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.product_name }}</span><br/>
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"></span>
                            </template>
                            <span>{{ item.product_name }}</span>
                          </v-tooltip>
                        </v-list-item-title>
                      </v-list-item-content>
                      <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ item.total_sold }}&nbsp;&nbsp;ชิ้น</div>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-card-text>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <!-- Top Ten Purchaser -->
    <v-dialog v-model="dialogPurchaserTopTen" persistent width="500">
      <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
        <v-toolbar flat color="#BDE7D9">
          <v-row>
            <v-col class="d-flex justify-space-around">
              <v-toolbar-title><span style="color: #27AB9C;"><b>ข้อมูลผู้ซื้อยอดเยี่ยม Top 10</b></span></v-toolbar-title>
            </v-col>
          </v-row>
          <v-btn fab small @click="dialogPurchaserTopTen = !dialogPurchaserTopTen" icon><v-icon color="#27AB9C">mdi-close</v-icon></v-btn>
        </v-toolbar>
        <v-card-text>
          <v-row dense class="mt-2">
            <v-col cols="4" md="2">
              <v-avatar size="60" style="background: #F3F5F9; border-radius: 999px;">
                <v-img src="../../assets/icons/checklist 1.png" contain  max-height="40px" max-width="40px" style="border-radius: 8px;"></v-img>
              </v-avatar>
            </v-col>
            <v-col cols="7" md="6" class="mt-3">
              <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">ผู้ซื้อ</span>
            </v-col>
          </v-row>
         <v-row dense class="my-4">
              <v-img src="@/assets/LineDash.png" contain :style="MobileSize ? 'width: 12vw;' : 'width: 100%;'"></v-img>
            </v-row>
          <v-row dense>
            <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
              <v-card-text class="py-0 px-0">
                <v-list style="background: #FAFAFA;" disabled>
                  <v-list-item-group v-for="(item, i) in itemUserPurchaser" :key="i">
                    <v-list-item>
                      <div style="font-weight: 700; font-size: 18px; line-height: 26px; color: #333333;" class="pr-4">{{ item.number }}</div>
                      <v-list-item-avatar :size="MobileSize ? '35' : '60'">
                        <v-img v-if="item.user_image !== ''" :src="item.user_image" contain></v-img>
                          <v-img src="../../assets/icons/checklist 1.png" contain :max-height="MobileSize ? '30px' : '40px'" :max-width="MobileSize ? '30px' : '40px'" v-else></v-img>
                      </v-list-item-avatar>
                      <v-list-item-content class="mx-1 ml-4">
                        <v-list-item-title>
                          <v-tooltip bottom>
                            <template v-slot:activator="{ on, attrs }">
                              <span v-bind="attrs" v-on="on" style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">{{ item.buyer_name }}</span><br/>
                              <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;"></span>
                            </template>
                            <span>{{ item.buyer_name }}</span>
                          </v-tooltip>
                        </v-list-item-title>
                      </v-list-item-content>
                      <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6 mobile-l">{{ Number(item.total_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                    </v-list-item>
                  </v-list-item-group>
                </v-list>
              </v-card-text>
            </v-card>
          </v-row>
        </v-card-text>
      </v-card>
    </v-dialog>
    <v-row dense>
      <v-col cols="12" align="start" class="pl-1 mb-2">
        <span style="font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;">สินค้าขายดียอดเยี่ยม Top 10 และข้อมูลผู้ซื้อยอดเยี่ยม Top 10</span>
      </v-col>
    </v-row>
    <v-row dense>
      <v-col cols="12" md="6">
        <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="2">
                <v-avatar :size="MobileSize ? '40' : '60'" style="background: #F3F5F9; border-radius: 999px;">
                  <v-img src="../../assets/icons/nullproduct.png" contain  :max-height="MobileSize ? '20px' : '40px'" :max-width="MobileSize ? '20px' : '40px'" style="border-radius: 8px;"></v-img>
                </v-avatar>
              </v-col>
              <v-col :cols="MobileSize ? '8' : '6'" :class="MobileSize ? 'mt-1' : 'mt-3'">
                <span :style="MobileSize ? 'font-weight: 600; font-size: 14px; line-height: 24px; color: #333333;' : 'font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;'" :class="MobileSize ? 'ml-' : 'ml-2'">สินค้าขายดียอดเยี่ยม Top 10</span>
              </v-col>
              <v-col :cols="MobileSize ? '1' : '2'" :class="MobileSize ? 'ml-2 mt-1' : 'ml-4 mt-3'" v-if="itemUserExtbuyer.length !== 0">
                <v-btn v-if="MobileSize" fab x-small outlined color="#27AB9C" @click="openAllTopTen('ExtBuyer')"><v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
                <v-btn v-else outlined color="#27AB9C" @click="openAllTopTen('ExtBuyer')" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">ดูทั้งหมด <v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
              </v-col>
            </v-row>
            <v-row dense class="my-4">
              <v-img src="@/assets/LineDash.png" contain :style="MobileSize ? 'width: 50vw;' : 'width: 100%;'"></v-img>
            </v-row>
            <v-row dense>
              <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text class="py-0 px-0" v-if="itemUserExtbuyer.length !== 0">
                  <v-list style="background: #FAFAFA;" disabled>
                    <v-list-item-group v-for="(item, i) in itemUserExtbuyer" :key="i">
                      <v-list-item v-if="item.number < 6">
                        <v-list-item-avatar :size="MobileSize ? '32' : '50'">
                          <v-img contain v-if="item.number === 1" src="@/assets/icons/level_1.png"></v-img>
                          <v-img contain v-else-if="item.number === 2" src="@/assets/icons/level_2.png"></v-img>
                          <v-img contain v-else-if="item.number === 3" src="@/assets/icons/level_3.png"></v-img>
                          <v-img contain v-else-if="item.number === 4" src="@/assets/icons/level_4.png"></v-img>
                          <v-img contain v-else-if="item.number === 5" src="@/assets/icons/level_5.png"></v-img>
                        </v-list-item-avatar>
                        <v-list-item-avatar :size="MobileSize ? '40' : '64'">
                          <!-- <v-img v-if="item.image !== null" :src="item.image" contain></v-img> -->
                          <img v-if="item.product_image"
                          :src="item.product_image"
                          alt="Product"
                          >
                         <img v-else
                         src="../../assets/icons/nullproduct.png"
                         alt="Product"
                         >
                        </v-list-item-avatar>
                        <v-list-item-content class="mx-1 ml-4">
                          <v-list-item-title>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" :class="MobileSize ? 'mobileRes' : 'dot2'">{{ item.product_name }}</span><br/>
                            <!-- <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span> -->
                          </v-list-item-title>
                        </v-list-item-content>
                        <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ item.total_sold }}&nbsp;&nbsp;ชิ้น</div>
                      </v-list-item>
                    </v-list-item-group>
                  </v-list>
                </v-card-text>
                <v-card-text v-else style="height: 200px;">
                  <v-row justify="center" align="center">
                    <p style="font-size: 18px;" class="pt-10">ไม่มีข้อมูลสินค้าขายดียอดเยี่ยม Top 10</p>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
      <v-col cols="12" md="6">
        <v-card outlined style="background: #FFFFFF; border: 1px solid #EBEBEB; border-radius: 8px;" width="100%" height="100%">
          <v-card-text>
            <v-row dense>
              <v-col cols="2">
                <v-avatar :size="MobileSize ? '40' : '60'" style="background: #F3F5F9; border-radius: 999px;">
                  <v-img src="../../assets/icons/checklist 1.png" contain :max-height="MobileSize ? '20px' : '40px'" :max-width="MobileSize ? '20px' : '40px'" style="border-radius: 8px;"></v-img>
                </v-avatar>
              </v-col>
              <v-col :cols="MobileSize ? '8' : '6'" :class="MobileSize ? 'mt-1' : 'mt-3'">
                <span :style="MobileSize ? 'font-weight: 600; font-size: 14px; line-height: 24px; color: #333333;' : 'font-weight: 700; font-size: 16px; line-height: 24px; color: #333333;'" :class="MobileSize ? 'ml-2' : 'ml-2'">ข้อมูลผู้ซื้อยอดเยี่ยม Top 10</span>
              </v-col>
              <v-col :cols="MobileSize ? '1' : '2'" :class="MobileSize ? 'ml-2 mt-1' : 'ml-4 mt-3'" v-if="itemUserPurchaser.length !== 0">
                <v-btn v-if="MobileSize" fab x-small outlined color="#27AB9C" @click="openAllTopTen('Purchaser')"><v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
                <v-btn v-else outlined color="#27AB9C" @click="openAllTopTen('Purchaser')" :style="MobileSize ? 'font-size: 14px;' : 'font-size: 16px;'">ดูทั้งหมด <v-icon color="#27AB9C">mdi-chevron-right</v-icon></v-btn>
              </v-col>
            </v-row>
            <v-row dense class="my-4">
              <v-img src="@/assets/LineDash.png" contain :style="MobileSize ? 'width: 50vw;' : 'width: 100%;'"></v-img>
            </v-row>
            <v-row dense>
              <v-card width="100%" height="100%" elevation="0" style="background: #FAFAFA; border-radius: 8px;">
                <v-card-text class="py-0 px-0" v-if="itemUserPurchaser.length !== 0">
                  <v-list style="background: #FAFAFA;" disabled>
                    <v-list-item-group v-for="(item, i) in itemUserPurchaser" :key="i">
                      <v-list-item v-if="item.number < 6">
                        <v-list-item-avatar :size="MobileSize ? '32' : '50'">
                          <v-img contain v-if="item.number === 1" src="@/assets/icons/level_1.png"></v-img>
                          <v-img contain v-else-if="item.number === 2" src="@/assets/icons/level_2.png"></v-img>
                          <v-img contain v-else-if="item.number === 3" src="@/assets/icons/level_3.png"></v-img>
                          <v-img contain v-else-if="item.number === 4" src="@/assets/icons/level_4.png"></v-img>
                          <v-img contain v-else-if="item.number === 5" src="@/assets/icons/level_5.png"></v-img>
                        </v-list-item-avatar>
                        <v-list-item-avatar :size="MobileSize ? '40' : '64'">
                          <v-img v-if="item.user_image !== ''" :src="item.user_image" contain></v-img>
                          <v-img src="../../assets/icons/checklist 1.png" contain :max-height="MobileSize ? '30px' : '40px'" :max-width="MobileSize ? '30px' : '40px'" v-else></v-img>
                        </v-list-item-avatar>
                        <v-list-item-content class="mx-1 ml-4">
                          <v-list-item-title>
                            <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;" :class="MobileSize ? 'mobileRes' : 'dot3'">{{ item.buyer_name }}</span><br/>
                            <!-- <span style="font-weight: 400; font-size: 14px; line-height: 22px; color: #333333;">(Partner)</span> -->
                          </v-list-item-title>
                        </v-list-item-content>
                        <div style="font-weight: 700; font-size: 16px; line-height: 24px; color: #1AB759;" class="pl-6">{{ Number(item.total_price).toLocaleString(undefined, { minimumFractionDigits: 2}) }}&nbsp;&nbsp;บาท</div>
                      </v-list-item>
                    </v-list-item-group>
                  </v-list>
                </v-card-text>
                <v-card-text v-else style="height: 200px;">
                  <v-row justify="center">
                    <p style="font-size: 18px;" class="pt-10">ไม่มีข้อมูลผู้ซื้อยอดเยี่ยม Top 10</p>
                  </v-row>
                </v-card-text>
              </v-card>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </div>
</template>

<script>
export default {
  data () {
    return {
      dateStart: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dateEnd: this.formatDate((new Date(Date.now() - (new Date()).getTimezoneOffset() * 60000)).toISOString().substr(0, 10)),
      dialogUserTopTen: false,
      dialogPurchaserTopTen: false,
      itemUserExtbuyer: [],
      itemUserPurchaser: []
    }
  },
  async created () {
    this.$EventBus.$on('getDataTop10buyers', this.getDataTop10buyers)
    this.$EventBus.$on('getDataTop10purchasers', this.getDataTop10purchasers)
    // await this.getDataTop10buyers(this.dateStart, this.dateEnd, '', '')
    // await this.getDataTop10purchasers(this.dateStart, this.dateEnd, '', '')
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
    // itemUserPurchaser () {
    //   return this.itemUserPurchaser2
    // },
    // itemUserExtbuyer () {
    //   return this.itemUserExtbuyer2
    // }
  },
  methods: {
    openAllTopTen (val) {
      if (val === 'ExtBuyer') {
        this.dialogUserTopTen = !this.dialogUserTopTen
      } else if (val === 'Purchaser') {
        this.dialogPurchaserTopTen = !this.dialogPurchaserTopTen
      }
    },
    async getDataTop10buyers () {
      this.itemUserExtbuyer = []
      // console.log(data)
      // await this.$store.dispatch('actionsTop10Buyer', data)
      // var response = await this.$store.state.ModuleAdminPanit.stateTop10Buyer
      // console.log('getDataTop10buyers', response)
      if (this.$store.getters.topProduct) {
        this.itemUserExtbuyer = this.$store.getters.topProduct
        // console.log('itemUserExtbuyer', this.itemUserExtbuyer)
      }
    },
    async getDataTop10purchasers () {
      this.itemUserPurchaser = []
      // console.log(data)
      // await this.$store.dispatch('actionsTop10Purchaser', data)
      // var response = await this.$store.state.ModuleAdminPanit.stateTop10Purchaser
      // console.log('getDataTop10purchasers', response)
      if (this.$store.getters.topBuyer) {
        this.itemUserPurchaser = this.$store.getters.topBuyer
      }
    },
    formatDate (date) {
      if (!date) return null
      const [year, month, day] = date.split('-')
      return `${year}-${month}-${day}`
    }
  }
}
</script>

<style>
.mobileRes {
  display: inline-block;
  width: 120px;
  white-space: nowrap;
  overflow: hidden !important;
  text-overflow: ellipsis;
}
.dot2 {
    display: inline-block;
    width: 190px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
.dot3 {
    display: inline-block;
    width: 180px;
    white-space: nowrap;
    overflow: hidden !important;
    text-overflow: ellipsis;
}
@media only screen and (max-width: 600px) {
  .dot2 {
    display: none;
  }
  .dot3 {
    display: none;
  }
  .mobile-l {
    font-size: 14px !important;
  }
}
</style>
