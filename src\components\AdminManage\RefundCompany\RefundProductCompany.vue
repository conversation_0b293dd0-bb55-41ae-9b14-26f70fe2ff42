<template>
  <v-container :class="MobileSize ? 'mt-3' : ''">
    <v-card elevation="0" width="100%" height="100%">
      <v-card-title style="font-weight: bold; font-size: 24px; line-height: 32px; color: #333333;" v-if="!MobileSize">ติดตามการคืนสินค้า</v-card-title>
      <v-card-title class="px-0" style="font-weight: bold; font-size: 18px; line-height: 32px; color: #333333;" v-else><v-icon color="#27AB9C" class="mr-2" @click="backToMenu()">mdi-chevron-left</v-icon> ติดตามการคืนสินค้า</v-card-title>
      <v-row no-gutters>
        <v-col cols="12" class="px-2 py-0">
          <a-tabs @change="SelectDetailOrderRefund">
            <!-- <a-tab-pane v-for="item in OrderNamePurchaser" :key="item.key" :tab="item.name"></a-tab-pane> -->
            <a-tab-pane key="ทั้งหมด"><span slot="tab">ทั้งหมด <a-tag color="#27AB9C" style="border-radius: 8px;">{{ countRefundAll }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="รออนุมัติ"><span slot="tab">รออนุมัติ <a-tag color="#E9A016" style="border-radius: 8px;">{{ countRefundPending }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="อนุมัติ"><span slot="tab">อนุมัติ <a-tag color="#1AB759" style="border-radius: 8px;">{{ countRefundApproved }}</a-tag></span></a-tab-pane>
            <a-tab-pane key="ไม่อนุมัติ"><span slot="tab">ไม่อนุมัติ <a-tag color="#D1392B" style="border-radius: 8px;">{{ countRefundNotAllowed }}</a-tag></span></a-tab-pane>
          </a-tabs>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="6" sm="12" class="" :class="!MobileSize ? 'pl-0 pr-3 mb-3 ' : 'pl-2 pr-2 mb-3'">
          <v-text-field v-model="search" dense hide-details outlined rounded placeholder="ค้นหาจากรหัสการสั่งซื้อ">
            <v-icon slot="append">mdi-magnify</v-icon>
          </v-text-field>
        </v-col>
        <v-col v-if="disableTable === true" cols="12" md="12" sm="12" class="" :class="!MobileSize ? 'pl-3 pr-3 mb-2 mt-0 ' : 'pl-2 pr-2 mb-3 mt-3'">
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-if="StateStatus === 'ทั้งหมด'">รายการติดตามการคืนสินค้าทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'รออนุมัติ'">รายการติดตามการคืนสินค้าที่รออนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'อนุมัติ'">รายการติดตามการคืนสินค้าที่อนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
          <span style="font-size: 16px; line-height: 24px; align-items: center; color: #333333; font-weight: 600" v-else-if="StateStatus === 'ไม่อนุมัติ'">รายการติดตามการคืนสินค้าที่ไม่อนุมัติทั้งหมด {{ showCountOrder }} รายการ</span>
        </v-col>
        <v-col cols="12">
          <v-card v-if="disableTable === true" outlined class="small-card mx-4 my-5" min-height="512">
            <v-data-table
              :headers="keyCheckHead == 0 ? headersAll : keyCheckHead == 1 ? headersPending : keyCheckHead == 2 ? headersApproved : keyCheckHead == 3 ? headersNotAllowed : headersAll"
              :items="DataTable"
              :search="search"
              style="width:100%;"
              height="100%"
              :page.sync="page"
              @pagination="countOrdar"
              no-results-text="ไม่พบรายการคืนสินค้าที่ค้นหา"
              no-data-text="ไม่มีรายการคืนสินค้าในตาราง"
              :update:items-per-page="getItemPerPage"
              class=""
              :items-per-page="10"
              :footer-props="{'items-per-page-text':'จำนวนแถว'}"
            >
              <template v-slot:[`item.created_at`]="{ item }">
                {{new Date(item.created_at).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric' })}}
              </template>
              <template v-slot:[`item.total_amount`]="{ item }">
                {{ Number(item.net_price).toLocaleString(undefined, {minimumFractionDigits: 2}) }}
              </template>
              <template v-slot:[`item.paid_datetime`]="{ item }">
                <span v-if="item.paid_datetime !== null">
                  {{ item.paid_datetime === 'ชำระเงินแบบเครดิตเทอม' ? item.paid_datetime : new Date(item.paid_datetime).toLocaleDateString('th-TH', { year: 'numeric', month: 'long', day: 'numeric', hour: 'numeric', minute: 'numeric', second: 'numeric' })}}
                </span>
                <span v-else>
                  -
                </span>
              </template>
              <template v-slot:[`item.status_refund`]="{ item }">
                <span v-if="item.status_refund === 'approve'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F0F9EE" text-color="#1AB759">อนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status_refund === 'waiting'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#FCF0DA" text-color="#E9A016">รออนุมัติ</v-chip>
                </span>
                <span v-else-if="item.status_refund === 'reject'">
                  <v-chip :class="!MobileSize ? 'ma-2' : 'ma-0'" color="#F7D9D9" text-color="#D1392B">ไม่อนุมัติ</v-chip>
                </span>
              </template>
              <template v-slot:[`item.actions`]="{ item }">
                <v-btn text rounded color="#27AB9C" small @click="goDetailRefund(item)">
                  <b>รายละเอียด</b>
                  <v-icon small>mdi-chevron-right</v-icon>
                </v-btn>
              </template>
            </v-data-table>
          </v-card>
        </v-col>
        <v-col cols="12" v-if="disableTable === false" align="center">
          <div class="my-5">
            <v-img src="@/assets/emptypo.png" max-height="500px" max-width="500px" height="100%" width="100%" contain aspect-ratio="2"></v-img>
          </div>
          <h2 v-if="StateStatus === 'ทั้งหมด'" style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการติดตามการคืนสินค้า</b></h2>
          <h2 v-else style="padding-top: 20px; padding-bottom: 50px; color: #27AB9C;"><b>คุณยังไม่มีรายการติดตามการคืนสินค้าที่{{ StateStatus }}</b></h2>
        </v-col>
      </v-row>
    </v-card>
  </v-container>
</template>

<script>
import { Encode, Decode } from '@/services'
import { Tabs, Tag } from 'ant-design-vue'
export default {
  components: {
    'a-tabs': Tabs,
    'a-tab-pane': Tabs.TabPane,
    'a-tag': Tag
  },
  data () {
    return {
      disableTable: false,
      search: '',
      // orderRefund: {
      //   all: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000442',
      //       price: '20000',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'Approved'
      //     },
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000440',
      //       price: '1800',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'pending'
      //     },
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000434',
      //       price: '400',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'notApproved'
      //     }
      //   ],
      //   pending: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000440',
      //       price: '1800',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'pending'
      //     }
      //   ],
      //   approved: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000442',
      //       price: '20000',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'Approved'
      //     }
      //   ],
      //   notApproved: [
      //     {
      //       created_at: '2022-03-19T04:46:36.000000Z',
      //       payment_transaction_number: '220321000000000434',
      //       price: '400',
      //       paid_date: '2022-03-21T04:46:36.000000Z',
      //       status: 'notApproved'
      //     }
      //   ]
      // },
      orderRefund: [],
      StateStatus: 'ทั้งหมด',
      keyCheckHead: 0,
      headersAll: [
        { text: 'วันที่', value: 'created_at', filterable: false, width: '180', align: 'start', sortable: false, class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'total_amount', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่ชำระเงิน', value: 'paid_datetime', filterable: false, width: '200', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', value: 'status_refund', filterable: false, width: '150', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'actions', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersPending: [
        { text: 'วันที่', value: 'created_at', width: '180', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'total_amount', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่ชำระเงิน', value: 'paid_datetime', filterable: false, width: '200', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', value: 'status_refund', filterable: false, width: '150', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'actions', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersApproved: [
        { text: 'วันที่', value: 'created_at', width: '180', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', filterable: false, sortable: false, lign: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'total_amount', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่ชำระเงิน', value: 'paid_datetime', filterable: false, width: '200', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', value: 'status_refund', filterable: false, width: '150', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'actions', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      headersNotAllowed: [
        { text: 'วันที่', value: 'created_at', width: '180', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'รหัสการสั่งซื้อ', value: 'order_number', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'ราคา', value: 'total_amount', filterable: false, sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'วันที่ชำระเงิน', value: 'paid_datetime', filterable: false, width: '200', sortable: false, align: 'center', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: 'สถานะการคืนสินค้า', value: 'status_refund', filterable: false, width: '150', sortable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' },
        { text: ' ', value: 'actions', sortable: false, filterable: false, align: 'start', class: 'backgroundTable fontTable--text fontSizeDetail' }
      ],
      countRefundAll: 0,
      countRefundPending: 0,
      countRefundApproved: 0,
      countRefundNotAllowed: 0,
      showCountOrder: 0,
      pageCount: 5,
      page: 1,
      itemsPerPage: 10,
      DataTable: [],
      dataRole: '',
      companyData: []
    }
  },
  mounted () {
    window.scrollTo(0, 0)
  },
  created () {
    this.$EventBus.$emit('changeNavCompany')
    this.$EventBus.$emit('checkPathCompany')
    this.dataRole = JSON.parse(localStorage.getItem('roleUser'))
    if (localStorage.getItem('oneData') !== null && localStorage.getItem('CompanyData') !== null) {
      this.companyData = JSON.parse(Decode.decode(localStorage.getItem('CompanyData')))
      this.ListRefundDataTable()
    } else {
      this.$router.push({ path: '/' }).catch(() => {})
    }
  },
  computed: {
    DataListOrder () {
      return this.responseData
    },
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    }
  },
  watch: {
    StateStatus (val) {
      if (val === 'ทั้งหมด') {
        this.DataTable = this.orderRefund.all !== undefined ? this.orderRefund.all : []
        this.keyCheckHead = 0
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'รออนุมัติ') {
        this.DataTable = this.orderRefund.waiting !== undefined ? this.orderRefund.waiting : []
        this.keyCheckHead = 1
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'อนุมัติ') {
        this.DataTable = this.orderRefund.approve !== undefined ? this.orderRefund.approve : []
        this.keyCheckHead = 2
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      } else if (val === 'ไม่อนุมัติ') {
        this.DataTable = this.orderRefund.reject !== undefined ? this.orderRefund.reject : []
        this.keyCheckHead = 3
        if (this.DataTable.length === 0) {
          this.disableTable = false
        } else {
          this.disableTable = true
        }
      }
    },
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/refundCompanyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/refundCompany' }).catch(() => {})
      }
      // var data
      // var OrderNumber
      // if (localStorage.getItem('orderRefundNumber') !== null) {
      //   data = JSON.parse(Decode.decode(localStorage.getItem('orderRefundNumber')))
      //   OrderNumber = data.reference_id
      //   if (val === true) {
      //     this.$router.push({ path: `/refundDetailCompanyMobile?orderNumber=${OrderNumber}` }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: `/refundDetailCompany?orderNumber=${OrderNumber}` }).catch(() => {})
      //   }
      // } else {
      //   if (val === true) {
      //     this.$router.push({ path: '/refundCompanyMobile' }).catch(() => {})
      //   } else {
      //     this.$router.push({ path: '/refundCompany' }).catch(() => {})
      //   }
      // }
    }
  },
  methods: {
    backToMenu () {
      if (this.MobileSize) {
        this.$router.push({ path: '/companyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/detailCompany' }).catch(() => {})
      }
    },
    getItemPerPage (val) {
      this.itemsPerPage = val
      // console.log('val ======', typeof this.itemsPerPage)
    },
    SelectDetailOrderRefund (item) {
      // console.log('test', item)
      this.StateStatus = item
      this.page = 1
    },
    countOrdar (pagination) {
      this.showCountOrder = pagination.itemsLength
    },
    async ListRefundDataTable () {
      this.countRefundAll = 0
      this.countRefundPending = 0
      this.countRefundApproved = 0
      this.countRefundNotAllowed = 0
      var data = {
        // role_user: this.dataRole.role
        company_id: this.companyData.id,
        role_user: 'purchaser'
      }
      await this.$store.dispatch('actionsListRefundPurchaser', data)
      var response = await this.$store.state.ModuleAdminManage.stateListRefundPurchaser
      this.orderRefund = response.data
      if (response.result === 'SUCCESS') {
        if (response.message !== 'Not found data') {
          if (this.orderRefund.length !== 0) {
            this.countRefundAll = this.orderRefund.count_all
            this.countRefundPending = this.orderRefund.count_waiting
            this.countRefundApproved = this.orderRefund.count_approve
            this.countRefundNotAllowed = this.orderRefund.count_reject
            if (this.StateStatus === 'ทั้งหมด') {
              this.DataTable = this.orderRefund.all
              if (this.DataTable.length === 0) {
                this.disableTable = false
              } else {
                this.disableTable = true
              }
            } else if (this.StateStatus === 'รออนุมัติ') {
              this.DataTable = this.orderRefund.waiting
              if (this.DataTable.length === 0) {
                this.disableTable = false
              } else {
                this.disableTable = true
              }
            } else if (this.StateStatus === 'อนุมัติ') {
              this.DataTable = this.orderRefund.approve
              if (this.DataTable.length === 0) {
                this.disableTable = false
              } else {
                this.disableTable = true
              }
            } else if (this.StateStatus === 'ไม่อนุมัติ') {
              this.DataTable = this.orderRefund.reject
              if (this.DataTable.length === 0) {
                this.disableTable = false
              } else {
                this.disableTable = true
              }
            }
          } else {
            this.countRefundAll = 0
            this.countRefundPending = 0
            this.countRefundApproved = 0
            this.countRefundNotAllowed = 0
            this.DataTable = []
          }
        } else {
          this.countRefundAll = 0
          this.countRefundPending = 0
          this.countRefundApproved = 0
          this.countRefundNotAllowed = 0
          this.DataTable = []
        }
      } else if (response.message === 'This user is unauthorized.') {
        this.$EventBus.$emit('refreshToken')
        // this.$swal.fire({
        //   icon: 'error',
        //   text: 'ผู้ใช้งานนี้ถูกใช้งานอยู่',
        //   showConfirmButton: false,
        //   timer: 1500
        // })
        // localStorage.removeItem('oneData')
      }
    },
    goDetailRefund (item) {
      var data = {
        reference_id: item.order_number,
        role_user: this.dataRole.role
      }
      localStorage.setItem('orderRefundNumber', Encode.encode(data))
      var OrderNumber = item.order_number
      if (this.MobileSize === false) {
        this.$router.push({ path: `/refundDetailCompany?orderNumber=${OrderNumber}` }).catch(() => {})
      } else {
        this.$router.push({ path: `/refundDetailCompanyMobile?orderNumber=${OrderNumber}` }).catch(() => {})
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  ::v-deep table {
    tbody {
      tr {
        td:nth-child(6) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(1) {
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
          z-index: 10;
          background: white;
        }
      }
    }
    thead {
      tr {
        th:nth-child(6) {
          z-index: 11;
          background: white;
          position: sticky !important;
          position: -webkit-sticky !important;
          right: 0;
        }
      }
    }
  }
</style>

<style scoped>
.fontSizeDetail {
  font-weight: 700 !important; font-size: 14px !important; line-height: 22px !important; color: #333333 !important;
}
.v-data-table /deep/ .v-data-footer {
  font-size: 0.62rem;
}
</style>
