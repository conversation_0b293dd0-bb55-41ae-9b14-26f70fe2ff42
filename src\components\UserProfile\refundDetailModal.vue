<template>
  <v-dialog
    max-width="800"
    v-model="modal"
  >
    <template v-slot:activator="{ on, attrs }">
        <v-btn
        rounded
        small
        color="primary"
        v-bind="attrs"
        v-on="on"
        >คืนเงิน/คืนสินค้า</v-btn>
    </template>
    <template v-slot:default="modal">
      <v-card v-if="modal">
        <v-toolbar
            color="primary"
            dark
        >
        <v-col cols="11" class="d-flex justify-center">
          <span style="font-size: 24px">การคืนเงิน/คืนสินค้า</span>
        </v-col>
        <v-btn @click="cancelRefund" icon>
            <v-icon color="white">mdi-close</v-icon>
        </v-btn>
        </v-toolbar>
        <v-card-text>
            <v-row class="mt-5">
                <v-col>
                    <span style="font-size: 20px; color: black; font-weight: bold;">เหตุผลในการคืนสินค้าเงิน/คืนสินค้า</span>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-select
                      v-model="selectedReason"
                      :items="itemsReason"
                      placeholder="กรุณาเลือกเหตุผลการส่งคืนสินค้า"
                      dense
                      outlined
                    ></v-select>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <span style="font-size: 20px; color: black; font-weight: bold;">รายละเอียด</span>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <v-textarea
                        label="กรอกรายละเอียดปัญกาที่พบ เพื่อให้เจ้าหน้าที่ หรือผู้ขาย ใช้ในการพิจารณาเพิ่มเติม"
                        value=""
                        v-model="discription"
                        outlined
                    ></v-textarea>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                    <span style="font-size: 20px; color: black; font-weight: bold;">กรุณาแนบรูปภาพ หรือวิดีโอ สามารถเห็นรายละเอียด ดังนี้</span>
                </v-col>
            </v-row>
            <v-row no-gutters>
                <v-col class="align-center mt-3" cols="12">
                    <v-icon x-small class="mx-2">mdi-circle</v-icon><span style="font-size: 18px;">สินค้าที่ได้รับทั้งหมด</span>
                </v-col>
                <v-col class="align-center mt-3" cols="12">
                    <v-icon x-small class="mx-2">mdi-circle</v-icon><span style="font-size: 18px;">ใบแปะหน้าพัสดุ</span>
                </v-col>
                <v-col class="align-center mt-3" cols="12">
                    <v-icon x-small class="mx-2">mdi-circle</v-icon><span style="font-size: 18px;">ระบุรายการสินค้าที่ไม่ได้รับ</span>
                </v-col>
            </v-row>
            <v-row>
                <v-col>
                  <v-card
                    v-if="FlashsTest.length === 0"
                    class="mt-3"
                    elevation="0"
                    :style="theRedI ? 'border: 2px dashed #27AB9C; box-sizing: border-box; border-radius: 8px;' : 'border: 2px dashed red; box-sizing: border-box; border-radius: 8px;'"
                    height="400px%"
                    @click="onPickFile()"
                    >
                    <v-card-text >
                        <v-row dense align="center" justify="center" style="cursor: pointer;">
                        <v-file-input
                            v-model="DataImage"
                            :items="DataImage"
                            accept="image/jpeg, image/jpg, image/png"
                            @change="UploadImage()"
                            id="file_input"
                            multiple :clearable="false"
                            style="display:none"
                        >
                        </v-file-input>
                        <v-col cols="12" md="12">
                            <v-row justify="center" align="center">
                            <v-col cols="12" md="12" align="center">
                                <v-img
                                src="@/assets/ImageINET-Marketplace/ICONShop/uploadImg.png"
                                width="280.34"
                                height="154.87"
                                contain
                                ></v-img>
                            </v-col>
                            <v-col cols="12" md="12" style="text-align: center;">
                                <span style="line-height: 24px; font-weight: 400;"
                                :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">เพิ่มรูปภาพของคุณที่นี่</span><br />
                                <span style="line-height: 24px; font-weight: 400;"
                                :style="IpadSize ? 'font-size: 14px;' : 'font-size: 16px;'">หรือเลือกรูปภาพจากคอมพิวเตอร์ของคุณ</span><br />
                                <span style="line-height: 16px; font-weight: 400;"
                                :style="IpadSize ? 'font-size: 9px;' : 'font-size: 12px;'">(ขนาดรูปภาพ 1480x620 px  ไฟล์นามสกุล .JPEG,PNG)</span><br />
                            </v-col>
                            </v-row>
                        </v-col>
                        </v-row>
                    </v-card-text>
                    </v-card>
                </v-col>
            </v-row>
        </v-card-text>
        <v-card-actions class="justify-end">
            <v-col class="d-flex justify-end">
                <v-btn
                    class="mr-3"
                    @click="cancelRefund"
                >ยกเลิก</v-btn>
                <v-btn color="#27ab9c" class="white--text" @click="confirmRefund">บันทึก</v-btn>
            </v-col>
        </v-card-actions>
      </v-card>
    </template>
  </v-dialog>
</template>

<script>
export default {
  props: ['itemProduct'],
  data () {
    return {
      itemsReason: [
        'ฉันไม่ได้รับพัสดุของคำสั่งซื้อนี้',
        'ได้รับสินค้าไม่ครบ / ชิ้นส่วนไม่ครบ',
        'ได้รับกล่องเปล่า',
        'สินค้าที่ได้รับมาผิด ไม่ใช่สินค้าที่สั่ง',
        'สินค้าสภาพไม่ดีหรือมีความเสียหาย',
        'การทำงานของสินค้าไม่สมบูรณ์',
        'ต้องการคืนสินค้าในสภาพสมบูรณ์',
        'สินค้าผิดลิขสิทธิ์',
        'สินค้าไม่ตรงตามรายละเอียดที่ระบุไว้'
      ],
      selectedReason: '',
      DataImage: [],
      FlashsTest: [],
      theRedI: true,
      modal: false,
      productData: {},
      discription: '',
      presentProductData: [],
      productIndex: ''
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    }
  },
  watch: {
    itemProduct (val) {
      console.log(val)
    }
  },
  created () {
    this.$EventBus.$on('openModal', this.openModal)
  },
  methods: {
    onPickFile () {
      document.getElementById('file_input').click()
    },
    async UploadImage () {
      for (let i = 0; i < this.DataImage.length; i++) {
        const element = this.DataImage[i]
        if (element.type === 'image/jpeg' || element.type === 'image/jpg' || element.type === 'image/png') {
          var url = URL.createObjectURL(element)
          const image = new Image()
          const imageDimensions = await new Promise((resolve) => {
            image.onload = () => {
              const dimensions = {
                height: image.height,
                width: image.width
              }
              resolve(dimensions)
            }
            image.src = url
          })
          if (this.FlashsTest.length < 2) {
            if (i < 1) {
              if (imageDimensions.height <= 620 && imageDimensions.width <= 1480) {
                const reader = new FileReader()
                reader.readAsDataURL(element)
                reader.onload = async () => {
                  var resultReader = reader.result
                  var url = URL.createObjectURL(element)
                  this.FlashsTest.push({
                    image_data: resultReader.split(',')[1],
                    url: url,
                    link: ''
                  })

                  const data = {
                    image: [this.FlashsTest[0].image_data],
                    type: 'refund'
                  }
                  this.$store.commit('openLoader')
                  await this.$store.dispatch('actionsUploadToS3', data)
                  const response = await this.$store.state.ModuleShop.stateUploadToS3

                  if (response.message === 'List Success.') {
                    this.FlashsTest[0].image_data = response.data.list_path[0].path
                    this.$store.commit('closeLoader')
                  }
                  this.theRedI = true
                }
              } else {
                this.$swal.fire({ icon: 'warning', text: 'โปรดใช้รูปตามขนาดที่กำหนด', showConfirmButton: false, timer: 1500 })
              }
            } else {
              this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', text: 'กรุณาใส่แบนเนอร์หลักไม่เกิน 1 ภาพ', showConfirmButton: false, timer: 1500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', text: 'กรุณาเพิ่มเฉพาะไฟล์รูปภาพ', showConfirmButton: false, timer: 2500 })
        }
      }
    },
    cancelRefund () {
      this.modal = false
    },
    confirmRefund () {
      this.modal = false
      this.presentProductData = this.productData
      // this.presentProductData.name = this.discription
      var modalData = {
        selectedReason: this.selectedReason,
        discription: this.discription
      }
      this.$EventBus.$emit('confirmRefund', this.presentProductData, this.productIndex, modalData)
    },
    openModal (open, item, index) {
      console.log(item, 'item')
      this.modal = open
      this.productData = item
      this.discription = item.name
      this.productIndex = index
    }
  }
}
</script>

<style>

</style>
