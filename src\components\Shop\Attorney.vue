<template>
  <v-container>
    <v-card width="100%" height="100%" elevation="0" class="mb-4" style="border-radius: 8px;">
      <v-card-text :class="MobileSize ? 'px-2' : ''">
        <!-- ส่วนกรอกข้อมูล -->
        <v-row class="pa-0">
          <v-col cols="12" class="mt-2">
            <div>
              <span style="font-weight: bold; font-size: 24px; line-height: 40px; color: #333333;" class="ml-2"><v-icon color="#27AB9C" class="mr-2" v-if="MobileSize" @click="backtoUserMenu()">mdi-chevron-left</v-icon> ยินยอมรับมอบอำนาจช่วง</span>
            </div>
          </v-col>
          <v-col cols="12" class="mt-1">
            <v-form ref="formAttorney" :lazy-validation="lazy">
              <v-row dense class="px-3">
                <v-icon color="#09B1F2">mdi-card-account-details</v-icon>
                <span class="pl-2" style="font-weight: 700; font-size: 18px; line-height: 40px; color: #333333;">ข้อมูลบัตรประชาชน</span>
              </v-row>
              <v-row dense class="px-2">
                <v-col cols="12" md="4" sm="12">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมายเลขบัตรประชาชน <span style="color: red;"> *</span></span>
                  <v-text-field v-model="taxID" placeholder="หมายเลขบัตรประชาชน" counter="13" dense outlined :maxLength="13" @keypress="CheckSpacebar($event)" @input="validateTaxID()" oninput="this.value = this.value.replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')" :rules="Rules.taxID"></v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชื่อ <span style="color: red;"> *</span></span>
                  <v-text-field v-model="name" placeholder="ชื่อ" dense outlined @keypress="CheckSpacebar($event)" :rules="Rules.name" oninput="this.value = this.value.replace(/[^ก-๏\s]/g, '')"></v-text-field>
                </v-col>
                <v-col cols="12" md="4" sm="6">
                  <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">นามสกุล <span style="color: red;"> *</span></span>
                  <v-text-field v-model="surname" placeholder="นามสกุล" dense outlined @keypress="CheckSpacebarName($event)" :rules="Rules.surname" oninput="this.value = this.value.replace(/[^ก-๏\s]/g, '')"></v-text-field>
                </v-col>
              </v-row>
              <v-row dense class="px-2 pt-2">
                <v-col cols="12">
                  <div class="borderd-content">
                    <span class="title">ที่อยู่ตามบัตรประชาชน</span>
                    <v-col cols="12" class="px-4">
                      <!-- แถวที่ 1 -->
                      <v-row dense>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">เลขที่ <span style="color: red;"> *</span></span>
                          <v-text-field v-model="houseNo" placeholder="ระบุเลขที่อยู่" dense outlined :rules="Rules.house_no" oninput="this.value = this.value.replace(/[^0-9๐-๙,/-]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ห้องเลขที่</span>
                          <v-text-field v-model="roomNo" placeholder="ระบุเลขห้อง" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ชั้นที่</span>
                          <v-text-field v-model="floor" placeholder="ระบุชั้น" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9a-zA-Z/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                      </v-row>
                      <!-- แถวที่ 2 -->
                      <v-row dense>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">อาคาร</span>
                          <v-text-field v-model="buildingName" placeholder="ชื่ออาคาร,อพาร์ทเมนต์,คอนโดมิเนียม" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-.\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมู่บ้าน</span>
                          <v-text-field v-model="mooBan" placeholder="ชื่อหมู่บ้าน" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">หมู่ที่</span>
                          <v-text-field v-model="mooNo" placeholder="ระบุหมู่" dense outlined :rules="Rules.moo_no" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^0-9\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                      </v-row>
                      <!-- แถวที่ 3 -->
                      <v-row dense>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ตรอก/ซอย</span>
                          <v-text-field v-model="soi" placeholder="ระบุตรอก,ซอย" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">แยก</span>
                          <v-text-field v-model="yaek" placeholder="ระบุแยก" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9/-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                        <v-col cols="12" md="4" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">ถนน</span>
                          <v-text-field v-model="street" placeholder="ระบุชื่อถนน" dense outlined :rules="Rules.maxText" oninput="this.value = this.value.replace(/\s+/g, ' ').replace(/^\s/,'').replace(/[^a-zA-Zก-๏0-9-\s]/g, '').replace(/(\..*)\./g, '$1')"></v-text-field>
                        </v-col>
                      </v-row>
                      <!-- แถวที่ 4 -->
                      <v-row dense>
                        <v-col cols="12" md="6" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">แขวง/ตำบล<span style="color: red;"> *</span></span>
                          <addressinput-subdistrict :class="checkSubDistrictError ? 'input_text-thai-address-error setMaxWidth' : 'input_text-thai-address setMaxWidth'" label=""  v-model="subdistrict" placeholder="ระบุแขวง/ตำบล"/>
                          <div v-if="checkSubDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="12" md="6" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">เขต/อำเภอ<span style="color: red;"> *</span></span>
                          <addressinput-district :class="checkDistrictError ? 'input_text-thai-address-error' : 'input_text-thai-address'" label="" v-model="district"  placeholder="ระบุเขต/อำเภอ" />
                          <div v-if="checkDistrictError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                      </v-row>
                      <!-- แถวที่ 5 -->
                      <v-row dense>
                        <v-col cols="12" md="6" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">จังหวัด<span style="color: red;"> *</span></span>
                          <addressinput-province label="" v-model="province" :class="checkProvinceError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุจังหวัด" />
                          <div v-if="checkProvinceError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                        <v-col cols="12" md="6" sm="6">
                          <span style="font-weight: 500; font-size: 16px; line-height: 40px; color: #333333;">รหัสไปรษณีย์<span style="color: red;"> *</span></span>
                          <addressinput-zipcode label="" v-model="zipcode" :class="checkZipcodeError ? 'input_text-thai-address-error' : 'input_text-thai-address'" placeholder="ระบุรหัสไปรษณีย์" />
                          <div v-if="checkZipcodeError" class="text-error">ข้อมูลไม่ถูกต้อง</div>
                        </v-col>
                      </v-row>
                    </v-col>
                  </div>
                </v-col>
              </v-row>
              <v-row dense class="pa-2">
                <v-col cols="12" class="d-flex">
                  <v-btn class="mr-auto" rounded outlined color="#27AB9C" height="40" @click="Cancel()">ยกเลิก</v-btn>
                  <v-btn class="ml-auto white--text" rounded color="#27AB9C" height="40" @click="Accept()">ยินยอมรับมอบอำนาจช่วง</v-btn>
                </v-col>
              </v-row>
            </v-form>
          </v-col>
        </v-row>
        <!-- ส่วนเซ็นลายเซ็น -->
        <v-dialog v-model="ModalSignCA" :width="MobileSize ? '100%' : '579px'" :style="MobileSize ? 'z-index: 16000004' : ''" persistent>
          <v-card elevation="0" style="background: #FFFFFF; border-radius: 24px; overflow-x: hidden;">
            <v-card-text class="px-0 pb-0">
              <div :style="MobileSize ? 'width: 100%' : IpadSize ? 'width: 100%' : 'width: 579px'" class="backgroundHead" style="position: absolute; height: 120px;">
                <v-row style="height: 120px;">
                  <v-col style="text-align: center;" class="pt-4">
                    <span :class="MobileSize ? 'title-mobile white--text' : 'title white--text'"><b>ลายเซ็นยินยอม</b></span>
                  </v-col>
                  <v-btn fab small @click="closeCASign()" icon class="mt-3"><v-icon color="white">mdi-close</v-icon></v-btn>
                </v-row>
              </div>
              <div style="position: relative; padding: 0px 12px 0px; display: flex; padding-top: 60px;">
                <v-row :width="MobileSize ? '100%' : '579px'" style="height: 50px; border-radius: 24px 24px 0px 0px; background: #FFFFFF;">
                  <v-col style="text-align: center;">
                  </v-col>
                </v-row>
              </div>
              <div class="backgroundContent" style="position: relative;">
                <v-card elevation="0" width="100%" height="100%" style="background: #FFFFFF; border-radius: 20px;">
                  <v-card-text style="text-align: center;" :class="MobileSize ? 'px-0' : ''">
                    <v-row justify="center">
                      <v-col cols="12" align="center">
                        <v-card width="579" height="100%" elevation="0">
                          <v-card-text :class="MobileSize ? 'px-2' : ''">
                            <v-col cols="12" md="12">
                              <v-row dense>
                                <v-col cols="12" md="12" align="end" class="d-flex">
                                  <span class="mr-auto pt-2" style="font-weight: 700; font-size: 16px;">ลายเซ็น <span style="color: red;"> *</span></span>
                                  <v-btn class="ml-auto" rounded color="#27AB9C" outlined @click="clearSign()">ล้างค่า</v-btn>
                                </v-col>
                              </v-row>
                              <v-card width="100%" height="100%" class="my-2">
                                <v-card-text class="pa-0">
                                  <div id="app">
                                    <vueSignature
                                    ref="signature"
                                    :sigOption="option"
                                    :w="'100%'"
                                    :h="'100%'"
                                    ></vueSignature>
                                  </div>
                                </v-card-text>
                              </v-card>
                            </v-col>
                          </v-card-text>
                        </v-card>
                      </v-col>
                    </v-row>
                  </v-card-text>
                </v-card>
              </div>
            </v-card-text>
            <v-card-actions style="height: 88px; background-color: #F5FCFB;">
              <v-btn color="#27AB9C" rounded outlined width="125" height="40" @click="closeCASign()">ยกเลิก</v-btn>
              <v-spacer></v-spacer>
              <v-btn color="#27AB9C" rounded width="125" height="40" class="white--text" @click="sendCA()">อัปโหลด</v-btn>
            </v-card-actions>
          </v-card>
        </v-dialog>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
import Vue from 'vue'
import VueThailandAddress from 'vue-thailand-address'
import 'vue-thailand-address/dist/vue-thailand-address.css'
import Address2021 from '@/Thailand_Address/address2021'
import vueSignature from 'vue-signature'
import { Decode } from '@/services'
Vue.use(VueThailandAddress)
export default {
  components: {
    vueSignature
  },
  data () {
    return {
      oneData: [],
      checkeKYCUser: false,
      ModalSignCA: false,
      lazy: false,
      taxID: '',
      name: '',
      surname: '',
      houseNo: '',
      roomNo: '',
      floor: '',
      buildingName: '',
      mooBan: '',
      mooNo: '',
      soi: '',
      yaek: '',
      street: '',
      subdistrict: '',
      district: '',
      province: '',
      zipcode: '',
      checkSubdistrict: '',
      checkDistrict: '',
      checkProvince: '',
      checkZipcode: '',
      checkSubDistrictError: '',
      checkDistrictError: '',
      checkProvinceError: '',
      checkZipcodeError: '',
      Rules: {
        name: [
          v => !!v || 'กรุณากรอกชื่อ'
        ],
        surname: [
          v => !!v || 'กรุณากรอกนามสกุล'
        ],
        taxID: [
          v => !!v || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีผู้รับ',
          v => v.length >= 13 || 'กรุณากรอกเลขประจำตัวผู้เสียภาษีให้ครบ 13 หลัก',
          v => this.validNationalID(v) || 'เลขประจำตัวผู้เสียภาษีไม่ถูกต้อง'
        ],
        house_no: [
          v => !!v || 'กรุณาระบุเลขที่',
          v => v.charAt(0) !== '-' || 'กรุณากรอกข้อมูลให้ถูกต้อง',
          v => (v.split('').filter(char => char === '-').length <= 1) || 'ระบุข้อมูลไม่ถูกต้อง',
          v => (/^[-0-9,-/]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9,-/]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ],
        maxText: [
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร'
        ],
        moo_no: [
          v => (/^[-0-9]+$/.test(v) || v.length === 0) || 'กรุณาระบุตัวเลขเท่านั้น',
          v => v.length <= 120 || 'กรอกได้ไม่เกิน 120 ตัวอักษร',
          v => ((/^[0-9]+$/.test(v) || v.length === 0) || (/^[-]+$/.test(v) && v.length === 1)) || 'ระบุข้อมูลไม่ถูกต้อง'
        ]
      },
      option: {
        penColor: 'rgba(12,38,154,255)',
        backgroundColor: 'rgb(255, 255, 255)'
      },
      dataUrl: '',
      dataToSendCA: '',
      idForSendCA: ''
    }
  },
  created () {
    this.oneData = []
    if (localStorage.getItem('oneData') !== null) {
      this.checkeKYC()
    } else {
      this.$router.push({ path: '/' }).catch(() => { })
    }
  },
  watch: {
    MobileSize (val) {
      if (val === true) {
        this.$router.push({ path: '/AttorneyMobile' }).catch(() => {})
      } else {
        this.$router.push({ path: '/Attorney' }).catch(() => {})
      }
    },
    subdistrict (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.district === val
        })
        if (result.length !== 0) {
          this.checkSubdistrict = result[0].district
          // this.checkAdressError('checkSubDistrictError')
        } else {
          this.checkAdressError('checkSubDistrictError')
          this.checkSubdistrict = ''
          this.zipcode = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.district = ''
        this.province = ''
      }
    },
    district (val) {
      this.checkDistrictError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.amphoe === val
        })
        if (result.length !== 0) {
          this.checkDistrict = result[0].amphoe
          // this.checkAdressError('checkDistrictError')
        } else {
          this.checkAdressError('checkDistrictError')
          this.checkDistrict = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.province = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.province = ''
      }
    },
    province (val) {
      this.checkProvinceError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.province === val
        })
        if (result.length !== 0) {
          this.checkProvince = result[0].province
          // this.checkAdressError('checkProvinceError')
        } else {
          this.checkAdressError('checkProvinceError')
          this.checkProvince = ''
          this.zipcode = ''
          this.subdistrict = ''
          this.district = ''
        }
      } else {
        this.zipcode = ''
        this.subdistrict = ''
        this.district = ''
      }
    },
    zipcode (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        const result = Address2021.filter((data) => {
          return data.zipcode === parseInt(val)
        })
        if (result.length !== 0) {
          this.checkZipcode = result[0].zipcode.toString()
          // this.checkAdressError('checkZipcodeError')
        } else {
          this.checkAdressError('checkZipcodeError')
          this.checkZipcode = ''
          this.subdistrict = ''
          this.district = ''
          this.province = ''
        }
      } else {
        this.subdistrict = ''
        this.district = ''
        this.province = ''
      }
    }
  },
  computed: {
    MobileSize () {
      const { xs } = this.$vuetify.breakpoint
      return !!xs
    },
    IpadProSize () {
      const { md } = this.$vuetify.breakpoint
      return !!md
    },
    IpadSize () {
      const { sm } = this.$vuetify.breakpoint
      return !!sm
    }
  },
  methods: {
    backtoUserMenu () {
      this.$router.push({ path: '/' }).catch(() => {})
    },
    closeCASign () {
      this.ModalSignCA = !this.ModalSignCA
      this.$refs.signature.clear()
    },
    clearSign () {
      this.$refs.signature.clear()
    },
    async checkeKYC () {
      this.$store.commit('openLoader')
      await this.$store.dispatch('actionsCheckeKYC')
      var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      if (response.result === 'SUCCESS') {
        this.checkeKYCUser = response.data.eKYC_approve === 'yes'
        // this.checkeKYCUser = true
        // console.log('this.checkeKYCUser', this.checkeKYCUser)
        if (this.checkeKYCUser) {
          await this.getAttorneyUserDetail()
        } else {
          if (response.data.eKYC_approve === 'no') {
            this.$store.commit('closeLoader')
            this.checkeKYCUser = false
            if (this.MobileSize) {
              this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
            } else {
              this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
            }
          } else {
            if (response.data.eKYC_expire === 'yes') {
              this.$store.commit('closeLoader')
              if (this.MobileSize) {
                this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
              } else {
                this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
              }
            }
          }
        }
      }
    },
    // async checkeKYC () {
    //   this.$store.commit('openLoader')
    //   // await this.$store.dispatch('actionsCheckeKYC')
    //   // var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
    //   // if (response.result === 'SUCCESS') {
    //   // this.checkeKYCUser = response.data.eKYC_approve === 'yes'
    //   this.checkeKYCUser = true
    //   // console.log('this.checkeKYCUser', this.checkeKYCUser)
    //   if (this.checkeKYCUser) {
    //     await this.getAttorneyUserDetail()
    //   } else {
    //     // if (response.data.eKYC_approve === 'no') {
    //     //   this.$store.commit('closeLoader')
    //     //   this.checkeKYCUser = false
    //     //   if (this.MobileSize) {
    //     //     this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //     //   } else {
    //     //     this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //     //   }
    //     // } else {
    //     //   if (response.data.eKYC_expire === 'yes') {
    //     //     this.$store.commit('closeLoader')
    //     //     if (this.MobileSize) {
    //     //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //     //     } else {
    //     //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
    //     //     }
    //     //   }
    //     // }
    //   }
    //   // }
    // },
    async getAttorneyUserDetail () {
      var dataUser = ''
      var listShopAttorney = []
      var shopDetail = JSON.parse(localStorage.getItem('shopDetail'))
      // console.log(shopDetail)
      await this.$store.dispatch('actionListReciver')
      var responseListReciver = await this.$store.state.ModuleShop.stateListReciver
      if (responseListReciver.ok === 'y') {
        for (let j = 0; j < responseListReciver.query_result.length; j++) {
          if (responseListReciver.query_result[j].sell_shop_id === shopDetail.id) {
            listShopAttorney = responseListReciver.query_result[j].receiver
          }
        }
        var IDForNotAttorney = listShopAttorney.filter((item) => item.is_attorney === 'yes' && item.status_approve === 'Waiting')
        this.idForSendCA = IDForNotAttorney[0].id
        var data = {
          id: IDForNotAttorney[0].id
        }
        await this.$store.dispatch('actionGetAttorneyUserDetail', data)
        var response = await this.$store.state.ModuleShop.stateGetAttorneyUserDetail
        if (response.ok === 'y') {
          this.$store.commit('closeLoader')
          if (response.query_result.length !== 0) {
            dataUser = response.query_result[0]
            this.taxID = dataUser.tax_id
            this.name = dataUser.first_name_th
            this.surname = dataUser.last_name_th
            this.houseNo = dataUser.house_no
            this.mooBan = dataUser.moo_ban
            this.buildingName = dataUser.building_name
            this.street = dataUser.street
            this.soi = dataUser.soi
            this.roomNo = dataUser.room_no
            this.floor = dataUser.floor
            this.mooNo = dataUser.moo_no
            this.yaek = dataUser.yaek
            this.subdistrict = dataUser.sub_district
            this.district = dataUser.district
            this.province = dataUser.province
            this.zipcode = dataUser.zip_code
          } else {
            this.$swal.fire({ icon: 'error', text: 'คุณไม่มีสิทธิ์ในการรับมอบอำนาจช่วง', showConfirmButton: false, timer: 2000 })
            this.$router.push({ path: '/' }).catch(() => {})
          }
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', title: response.message, showConfirmButton: false, timer: 2000 })
        }
      }
    },
    CheckSpacebar (e) {
      if (e.keyCode === 32) {
        e.preventDefault()
      }
    },
    CheckSpacebarName (e) {
      if (e.keyCode === 32 && (e.target.value === undefined || e.target.value === '')) {
        e.preventDefault()
      }
    },
    validateTaxID () {
      if (this.validNationalID(this.taxID)) {
        // console.log('Pass')
        return true
      } else {
        // console.log('Fail')
        return false
      }
    },
    validNationalID (id) {
      if (id.length !== 13) return false
      for (var i = 0, sum = 0; i < 12; i++) {
        sum += parseInt(id.charAt(i)) * (13 - i)
      }
      var mod = sum % 11
      var check = (11 - mod) % 10
      return check === parseInt(id.charAt(12))
    },
    checksubdistrictConfirm (val) {
      this.checkSubDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkSubDistrictError')
        return false
      }
    },
    checkdistrictConfirm (val) {
      this.checkDistrictError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkDistrictError')
        return false
      }
    },
    checkprovinceConfirm (val) {
      this.checkProvinceError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkProvinceError')
        return false
      }
    },
    checkzipcodeConfirm (val) {
      this.checkZipcodeError = false
      if (val !== '') {
        return true
      } else {
        this.checkAdressError('checkZipcodeError')
        return false
      }
    },
    checkAddress () {
      const check = Address2021.filter((data) => {
        return data.district === this.subdistrict && data.amphoe === this.district && data.province === this.province && data.zipcode.toString() === this.zipcode
      })
      return check
    },
    checkAdressError (key) {
      if (this.checkAddress().length !== 0) {
        this.checkSubDistrictError = false
        this.checkDistrictError = false
        this.checkProvinceError = false
        this.checkZipcodeError = false
      } else {
        if (key === 'checkSubDistrictError') {
          this.checkSubDistrictError = true
        } else if (key === 'checkDistrictError') {
          this.checkDistrictError = true
        } else if (key === 'checkProvinceError') {
          this.checkProvinceError = true
        } else if (key === 'checkZipcodeError') {
          this.checkZipcodeError = true
        }
      }
    },
    Cancel () {
      this.$refs.formAttorney.resetValidation()
      this.$router.push({ path: '/' }).catch(() => {})
    },
    async Accept () {
      // this.$store.commit('openLoader')
      // await this.$store.dispatch('actionsCheckeKYC')
      // var response = await this.$store.state.ModuleBusiness.stateCheckeKYC
      // if (response.result === 'SUCCESS' && response.data.eKYC_approve === 'yes') {
      if (this.$refs.formAttorney.validate(true)) {
        if (this.checksubdistrictConfirm(this.subdistrict) || this.checkdistrictConfirm(this.district) || this.checkprovinceConfirm(this.province) || this.checkzipcodeConfirm(this.zipcode)) {
          if (this.subdistrict === this.checkSubdistrict && this.district === this.checkDistrict && this.province === this.checkProvince && this.zipcode === this.checkZipcode) {
            const check = this.checkAddress()
            this.oneData = JSON.parse(Decode.decode(localStorage.getItem('oneData')))
            if (check.length !== 0) {
              this.dataToSendCA = {
                id: this.idForSendCA,
                tax_id: this.taxID,
                title_name_th: 'นาย',
                first_name_th: this.name,
                last_name_th: this.surname,
                house_no: this.houseNo,
                moo_ban: this.mooBan !== '' ? this.mooBan : '-',
                building_name: this.buildingName !== '' ? this.buildingName : '-',
                street: this.street !== '' ? this.street : '-',
                soi: this.soi !== '' ? this.soi : '-',
                room_no: this.roomNo !== '' ? this.roomNo : '-',
                floor: this.floor !== '' ? this.floor : '-',
                moo_no: this.mooNo !== '' ? this.mooNo : '-',
                yaek: this.yaek !== '' ? this.yaek : '-',
                sub_district: this.subdistrict,
                district: this.district,
                province: this.province,
                zip_code: this.zipcode
              }
              // console.log('data ====>', this.dataToSendCA)
              this.ModalSignCA = true
            } else {
              this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: 'โปรดเลือก (แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
            }
          } else {
            this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', text: '(แขวง/ตำบล, เขต/อำเภอ, จังหวัด, รหัสไปรษณีย์)', showConfirmButton: false, timer: 2500 })
          }
        } else {
          this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
        }
      } else {
        this.$swal.fire({ icon: 'warning', title: '<h5>กรอกข้อมูลไม่ถูกต้อง กรุณาตรวจสอบข้อมูลใหม่อีกครั้ง</h5>', showConfirmButton: false, timer: 2500 })
      }
      // } else {
      //   if (response.data.eKYC_approve === 'no') {
      //     this.$store.commit('closeLoader')
      //     if (this.MobileSize) {
      //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //     } else {
      //       this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">ยังไม่ได้ยืนยันตัวตน</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">ท่านยังไม่ได้ยืนยันตัวตนผ่าน eKYC </p><span style="line-height: normal;">กรุณายืนยันตัวตนก่อน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อยืนยันตัวตนผ่าน eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //     }
      //   } else {
      //     if (response.data.eKYC_expire === 'yes') {
      //       this.$store.commit('closeLoader')
      //       if (this.MobileSize) {
      //         this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //       } else {
      //         this.$swal.fire({ icon: 'error', title: '<h4><b>สถานะ eKYC ของคุณคือ</b></h4>', html: '<h2 style="background-color: red; color: white; width: 250px; justify-content: center; display: inline-block;">หมดอายุ</h2>' + '<p style="margin-top: 10px; line-height: normal; margin-bottom: 0em;">กรุณาต่ออายุ eKYC </p><span style="line-height: normal;">ก่อนยืนยันตัวตน</span><br/>' + '<br/><a href="https://ekyc.id.th/" style="background-color: #2778c4; color: #fff; font-size: 1em; border: 0; border-radius: 0.25em; margin: 0 1.875em; padding: 8px;">กดที่นี่เพื่อต่ออายุ eKYC</a>', showConfirmButton: false, showCloseButton: true, allowOutsideClick: false, allowEscapeKey: false })
      //       }
      //     }
      //   }
      // }
    },
    async sendCA () {
      this.$store.commit('openLoader')
      var sign = ''
      // isEmpty() = true แปลว่าไม่มี
      if (!this.$refs.signature.isEmpty()) {
        sign = this.$refs.signature.save()
        this.dataToSendCA.receiver_signature = sign.split(',')[1]
        await this.$store.dispatch('actionAttorneyUpdateAndConfrime', this.dataToSendCA)
        var response = await this.$store.state.ModuleShop.stateAttorneyUpdateAndConfrime
        if (response.ok === 'y') {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'success', title: 'ส่งคำขอหนังสือมอบอำนาจช่วงสำเร็จ', showConfirmButton: false, timer: 2000 })
          this.$router.push({ path: '/' }).catch(() => {})
        } else {
          this.$store.commit('closeLoader')
          this.$swal.fire({ icon: 'error', text: response.message, showConfirmButton: false, timer: 2000 })
        }
      } else {
        this.$store.commit('closeLoader')
        this.$swal.fire({ icon: 'error', text: 'กรุณาเซ็นลายเซ็น', showConfirmButton: false, timer: 2000 })
      }
    }
  }
}
</script>

<style scoped>
.borderd-content {
  border: 1px solid #CCCCCC;
  border-radius: 4px;
  height: 100%;
  position: relative;
}

.borderd-content .title {
  margin: -21px 0 0 10px;
  background: #ffffff;
  padding: 4px;
  display: inline-block;
  font-family: 'Sukhumvit Set' !important;
  font-weight: 600;
  font-size: 16px !important;
  position: absolute;
}
</style>

<style>
.v-text-field input {
  font-size: 0.9em;
}
.v-text-field__details {
  margin-bottom: 4px !important;
}
.border_confirm {
  background-color: #27AB9C;
}
.input_text {
  height: 60px;
  opacity: 1;
}
.input_text-thai-address-error {
  height: 45px !important;
}
.input_text-thai-address {
  height: 60px !important;
}
.title-mobile {
  font-size: 18px;
}
input.th-address-input {
  font-size: 16px;
}
.input_text-thai-address-error input.th-address-input {
  opacity: 1;
  font-size: 16px;
  border-radius: 4px;
  padding-left: 10px;
  border: 2px solid #ff5252  !important;
}
.input_text-thai-address input.th-address-input {
  opacity: 1;
  font-size: 16px !important;
  color: black !important;
  border-radius: 4px;
  padding-left: 10px;
  /* border: 1px solid red !important; */
}
.text-error {
  color: #ff5252;
  font-size: 12px;
  line-height: 12px;
  padding: 3px 12px 2px 0px !important;
}
ul.th-address-autocomplete {
  max-height: 180px !important;
}
@media screen and (max-width: 768px) {
  ul.th-address-autocomplete {
    font-size: 12px;
  }
}
/* สไตล์สำหรับ non-mobile (desktop, tablet, etc.) */
@media screen and (min-width: 769px) {
  ul.th-address-autocomplete {
    font-size: small;
  }
}
</style>
